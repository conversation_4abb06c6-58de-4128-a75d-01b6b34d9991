# Telegram API凭证
API_ID=your_api_id_here
API_HASH=your_api_hash_here

# 可选：指定数据库路径（默认为APPDATA目录下）
# DATABASE_PATH=/custom/path/to/database.db

# 日志配置
LOG_LEVEL=INFO
LOG_ROTATION=10 MB
LOG_RETENTION=30 days

# 模块日志级别
LOG_LEVEL_TELEGRAM=DEBUG
LOG_LEVEL_DATABASE=WARNING
LOG_LEVEL_CONTROLLERS=INFO

# 客户端配置
CLIENT_CONNECTION_TIMEOUT=30
INACTIVE_CLIENT_DISCONNECT_TIME=600

# Telegram操作超时配置（秒）
TELEGRAM_PROFILE_UPDATE_TIMEOUT=30
TELEGRAM_AVATAR_UPLOAD_TIMEOUT=90
TELEGRAM_BATCH_OPERATION_TIMEOUT=120

# 代理配置
PROXY_CHECK_TIMEOUT=10
