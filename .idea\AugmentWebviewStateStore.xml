<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AugmentWebviewStateStore">
    <option name="stateMap">
      <map>
        <entry key="CHAT_STATE" value="{&quot;currentConversationId&quot;:&quot;1ffbe6fb-fa4d-4616-b271-0879bf4745e3&quot;,&quot;conversations&quot;:{&quot;1ffbe6fb-fa4d-4616-b271-0879bf4745e3&quot;:{&quot;id&quot;:&quot;1ffbe6fb-fa4d-4616-b271-0879bf4745e3&quot;,&quot;createdAtIso&quot;:&quot;2025-08-01T03:11:26.853Z&quot;,&quot;lastInteractedAtIso&quot;:&quot;2025-08-01T03:58:18.868Z&quot;,&quot;chatHistory&quot;:[{&quot;status&quot;:&quot;success&quot;,&quot;request_id&quot;:&quot;184c6d86-31c2-4a01-a005-0669998b96d1&quot;,&quot;request_message&quot;:&quot;分析项目&quot;,&quot;rich_text_json_repr&quot;:{&quot;type&quot;:&quot;doc&quot;,&quot;content&quot;:[{&quot;type&quot;:&quot;paragraph&quot;,&quot;content&quot;:[{&quot;type&quot;:&quot;text&quot;,&quot;text&quot;:&quot;分析项目&quot;}]}]},&quot;mentioned_items&quot;:[],&quot;structured_output_nodes&quot;:[{&quot;id&quot;:0,&quot;type&quot;:0,&quot;content&quot;:&quot;&quot;},{&quot;id&quot;:1,&quot;type&quot;:2,&quot;content&quot;:&quot;\n\n---\n\n*<NAME_EMAIL> has been suspended. To continue, [purchase a subscription](https://app.augmentcode.com/account).*&quot;}],&quot;seen_state&quot;:&quot;seen&quot;,&quot;structured_request_nodes&quot;:[{&quot;id&quot;:0,&quot;type&quot;:0,&quot;text_node&quot;:{&quot;content&quot;:&quot;分析项目&quot;}},{&quot;id&quot;:1,&quot;type&quot;:4,&quot;ide_state_node&quot;:{&quot;workspaceFolders&quot;:[{&quot;repositoryRoot&quot;:&quot;H:/PyProject/TeleTest-bak&quot;,&quot;folderRoot&quot;:&quot;H:/PyProject/TeleTest-bak&quot;}],&quot;workspaceFoldersUnchanged&quot;:false}}],&quot;timestamp&quot;:&quot;2025-08-01T03:20:40.239Z&quot;,&quot;response_text&quot;:&quot;\n\n---\n\n*<NAME_EMAIL> has been suspended. To continue, [purchase a subscription](https://app.augmentcode.com/account).*&quot;,&quot;workspace_file_chunks&quot;:[]}],&quot;feedbackStates&quot;:{&quot;temp-fe-a6298a28-509f-470a-9d90-188554ea607a&quot;:{&quot;selectedRating&quot;:0,&quot;feedbackNote&quot;:&quot;&quot;}},&quot;toolUseStates&quot;:{},&quot;draftExchange&quot;:{&quot;request_message&quot;:&quot;&quot;,&quot;rich_text_json_repr&quot;:{&quot;type&quot;:&quot;doc&quot;,&quot;content&quot;:[{&quot;type&quot;:&quot;paragraph&quot;}]},&quot;mentioned_items&quot;:[],&quot;status&quot;:&quot;draft&quot;},&quot;requestIds&quot;:[],&quot;isPinned&quot;:false,&quot;isShareable&quot;:true,&quot;extraData&quot;:{&quot;hasDirtyEdits&quot;:false},&quot;personaType&quot;:0,&quot;rootTaskUuid&quot;:&quot;5cfb779a-9e1d-4e62-89be-d28d3a496917&quot;}},&quot;agentExecutionMode&quot;:&quot;manual&quot;,&quot;isPanelCollapsed&quot;:true,&quot;displayedAnnouncements&quot;:[]}" />
      </map>
    </option>
  </component>
</project>