# 全局设置

nserver *******
# 首选 DNS 服务器
nserver *******
# 备用 DNS 服务器

#nserver指定了DNS服务器，nscache用于DNS缓存，提高解析速度。
nscache 65536
# 超时的时间值
timeouts 1 5 30 60 180 1800 15 600

# 认证方式 (为了简单起见，这里禁用认证。生产环境强烈建议启用！)
# auth ip <允许访问的 IP 地址范围>
# auth strong <用户名>:<密码>
auth none

# 在windows上作为服务启动
service
# 为每个公网 IP 配置代理监听器

# IP 1: HTTP 代理
socks -n -************** -p3128 -e**************
proxy -n -************** -p31291 -e**************
# HTTP 代理监听在 ip1:3128
# SOCKS5 代理
socks -n -************** -p1080 -e**************
proxy -n -************** -p1081 -e**************
# SOCKS5 代理监听在 ip1:1080

# IP 2: HTTP 代理
socks -n -************** -p3129 -e**************

  # HTTP 代理监听在 ip2:3129
socks -na -a:user2:pass2  -************** -p31130 -e**************
