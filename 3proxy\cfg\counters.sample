# Scenario:
# You're billed for traffic except internal networks 
# 192.168.0.0 mask 255.255.0.0 and 10.0.0.0 mask 255.0.0.0.
# If you exceed 2Gb limit you will have very high penalty
#
# You want to have daily report about common amount of external traffic.
# You also need to know amount of Web traffic for every user
# You want to limit Web traffic to 100Mb/day to whole campus and
# to 20MB/day to every user

# see explanations in 3proxy.cfg.sample
internal 192.168.1.1
external 10.1.1.1
nserver 10.1.2.1
nserver 10.2.2.2
nscache 65536
dnspr

# no logging will be used, only traffic reports

# use  d:\3proxy\3profy.3cf to store counters data
# generate daily traffic reports in d:\3proxy\traf\
counter "d:\3proxy\3profy.3cf" D "d:\3proxy\traf\traf"

# define users
users "user1:CL:password1" "user2:CL:password2" "user3:CL:password3"
users "user4:CL:password4" "user5:CL:password5" "user6:CL:password6"
# ...

# do not count traffic for 192.168.0.0/16,10.0.0.0/8
nocountin * * 192.168.0.0/16,10.0.0.0/8
# Count external traffic summary for all clients with limit to 100MB/day
countin "1/Test 1" D 100 *
# Count external Web traffic summary for all clients to 1Gb/month
countin "2/Test 2" M 1024 vlad,3APA3A,test 127.0.0.1 * 80,81,8080-8088
# For every user count and limit daily Web traffic to 20 Mb
# There is no way to configure it in a single line, we need a line
# for every user we have
countin "3/User 1" D 20 user1 * * 80,81,8080-8088
countin "4/User 2" D 20 user2 * * 80,81,8080-8088
# ...
countin "202/User 200" D 20 user200 * * 80,81,8080-8088

# enable proxy
auth strong
proxy

# enable administration to user1 from localhost
internal 127.0.0.1
allow user1
admin

