# By <PERSON>
nserver ********
nscache 65536

# we can grab wpad file from provider and feed it to dighosts
# to build list of free networks
# system "c:\3proxy\dighosts.exe -m http://wpad.security.nnov.ru/wpad.dat c:\3proxy\freenetworks.net"

service

internal ***********
external ********

dnspr

log &3proxylog,root
#log c:\3proxy\logs\proxy.log D
#logformat "Linsert into log (timestamp, username, service, clientip, remoteip, remoteport, bytesin, bytesout,request,error) values (
#logformat "%t '%U' '%N' '%C' '%R' %r %I %O '%T' %E"
logformat "-\'+_Linsert into log (time, bytesin, bytesout, username, url, host, port, service) values ('%Y-%m-%d %H:%M:%S', %I, %O, '%U', '%T', '%n', %r, '%N');"
archiver zip c:\3proxy\zip.exe -m -qq %A %F
rotate 50


auth strong
users temp:CL:password root:CL:password

# access free networks directly
allow * * $c:\3proxy\freenetworks.net
# redirect web traffic for non-free networks to provider's proxy
allow * * * 80
parent 1000 http ******** 3128 
# allow rest of traffic
allow *
proxy

flush

auth iponly
allow *
pop3p
tcppm 25 mail.security.nnov.ru 25

flush
# redirect port 80 traffic via SOCKS server to local HTTP proxy to
# have URLs logged
allow  * * * 80
parent 1000 http 0.0.0.0 0
allow *
socks

#daemon

