{\rtf1\adeflang1025\ansi\ansicpg1251\uc1\adeff0\deff0\stshfdbch0\stshfloch0\stshfhich0\stshfbi0\deflang1049\deflangfe1049{\fonttbl{\f0\froman\fcharset204\fprq2{\*\panose 02020603050405020304}Times New Roman;}{\f2\fmodern\fcharset204\fprq1{\*\panose 02070309020205020404}Courier New;}
{\f3\froman\fcharset2\fprq2{\*\panose 05050102010706020507}Symbol;}{\f10\fnil\fcharset2\fprq2{\*\panose 05000000000000000000}Wingdings;}{\f39\froman\fcharset0\fprq2 Times New Roman;}{\f37\froman\fcharset238\fprq2 Times New Roman CE;}
{\f40\froman\fcharset161\fprq2 Times New Roman Greek;}{\f41\froman\fcharset162\fprq2 Times New Roman Tur;}{\f42\fbidi \froman\fcharset177\fprq2 Times New Roman (Hebrew);}{\f43\fbidi \froman\fcharset178\fprq2 Times New Roman (Arabic);}
{\f44\froman\fcharset186\fprq2 Times New Roman Baltic;}{\f45\froman\fcharset163\fprq2 Times New Roman (Vietnamese);}{\f59\fmodern\fcharset0\fprq1 Courier New;}{\f57\fmodern\fcharset238\fprq1 Courier New CE;}
{\f60\fmodern\fcharset161\fprq1 Courier New Greek;}{\f61\fmodern\fcharset162\fprq1 Courier New Tur;}{\f62\fbidi \fmodern\fcharset177\fprq1 Courier New (Hebrew);}{\f63\fbidi \fmodern\fcharset178\fprq1 Courier New (Arabic);}
{\f64\fmodern\fcharset186\fprq1 Courier New Baltic;}{\f65\fmodern\fcharset163\fprq1 Courier New (Vietnamese);}}{\colortbl;\red0\green0\blue0;\red0\green0\blue255;\red0\green255\blue255;\red0\green255\blue0;\red255\green0\blue255;\red255\green0\blue0;
\red255\green255\blue0;\red255\green255\blue255;\red0\green0\blue128;\red0\green128\blue128;\red0\green128\blue0;\red128\green0\blue128;\red128\green0\blue0;\red128\green128\blue0;\red128\green128\blue128;\red192\green192\blue192;}{\stylesheet{
\ql \li0\ri0\widctlpar\wrapdefault\aspalpha\aspnum\faauto\adjustright\rin0\lin0\itap0 \rtlch\fcs1 \af0\afs24\alang1025 \ltrch\fcs0 \fs24\lang1049\langfe1049\cgrid\langnp1049\langfenp1049 \snext0 Normal;}{\*\cs10 \additive \ssemihidden 
Default Paragraph Font;}{\*\ts11\tsrowd\trftsWidthB3\trpaddl108\trpaddr108\trpaddfl3\trpaddft3\trpaddfb3\trpaddfr3\trcbpat1\trcfpat1\tblind0\tblindtype3\tscellwidthfts0\tsvertalt\tsbrdrt\tsbrdrl\tsbrdrb\tsbrdrr\tsbrdrdgl\tsbrdrdgr\tsbrdrh\tsbrdrv 
\ql \li0\ri0\widctlpar\wrapdefault\aspalpha\aspnum\faauto\adjustright\rin0\lin0\itap0 \rtlch\fcs1 \af0\afs20 \ltrch\fcs0 \fs20\lang1024\langfe1024\cgrid\langnp1024\langfenp1024 \snext11 \ssemihidden Normal Table;}}
{\*\latentstyles\lsdstimax156\lsdlockeddef0}{\*\listtable{\list\listtemplateid-1938272566{\listlevel\levelnfc0\levelnfcn0\leveljc0\leveljcn0\levelfollow0\levelstartat1\levelspace0\levelindent0{\leveltext\'01\'00;}{\levelnumbers\'01;}\rtlch\fcs1 \af0 
\ltrch\fcs0 \fbias0 \fi-360\li360\jclisttab\tx360\lin360 }{\listlevel\levelnfc0\levelnfcn0\leveljc0\leveljcn0\levelfollow0\levelstartat1\levelspace0\levelindent0{\leveltext\'03\'00.\'01;}{\levelnumbers\'01\'03;}\rtlch\fcs1 \af0 \ltrch\fcs0 \fbias0 
\fi-360\li360\jclisttab\tx360\lin360 }{\listlevel\levelnfc0\levelnfcn0\leveljc0\leveljcn0\levelfollow0\levelstartat1\levelspace0\levelindent0{\leveltext\'05\'00.\'01.\'02;}{\levelnumbers\'01\'03\'05;}\rtlch\fcs1 \af0 \ltrch\fcs0 \fbias0 \fi-720\li720
\jclisttab\tx720\lin720 }{\listlevel\levelnfc0\levelnfcn0\leveljc0\leveljcn0\levelfollow0\levelstartat1\levelspace0\levelindent0{\leveltext\'07\'00.\'01.\'02.\'03;}{\levelnumbers\'01\'03\'05\'07;}\rtlch\fcs1 \af0 \ltrch\fcs0 \fbias0 \fi-720\li720
\jclisttab\tx720\lin720 }{\listlevel\levelnfc0\levelnfcn0\leveljc0\leveljcn0\levelfollow0\levelstartat1\levelspace0\levelindent0{\leveltext\'09\'00.\'01.\'02.\'03.\'04;}{\levelnumbers\'01\'03\'05\'07\'09;}\rtlch\fcs1 \af0 \ltrch\fcs0 \fbias0 
\fi-1080\li1080\jclisttab\tx1080\lin1080 }{\listlevel\levelnfc0\levelnfcn0\leveljc0\leveljcn0\levelfollow0\levelstartat1\levelspace0\levelindent0{\leveltext\'0b\'00.\'01.\'02.\'03.\'04.\'05;}{\levelnumbers\'01\'03\'05\'07\'09\'0b;}\rtlch\fcs1 \af0 
\ltrch\fcs0 \fbias0 \fi-1080\li1080\jclisttab\tx1080\lin1080 }{\listlevel\levelnfc0\levelnfcn0\leveljc0\leveljcn0\levelfollow0\levelstartat1\levelspace0\levelindent0{\leveltext\'0d\'00.\'01.\'02.\'03.\'04.\'05.\'06;}{\levelnumbers
\'01\'03\'05\'07\'09\'0b\'0d;}\rtlch\fcs1 \af0 \ltrch\fcs0 \fbias0 \fi-1440\li1440\jclisttab\tx1440\lin1440 }{\listlevel\levelnfc0\levelnfcn0\leveljc0\leveljcn0\levelfollow0\levelstartat1\levelspace0\levelindent0{\leveltext
\'0f\'00.\'01.\'02.\'03.\'04.\'05.\'06.\'07;}{\levelnumbers\'01\'03\'05\'07\'09\'0b\'0d\'0f;}\rtlch\fcs1 \af0 \ltrch\fcs0 \fbias0 \fi-1440\li1440\jclisttab\tx1440\lin1440 }{\listlevel\levelnfc0\levelnfcn0\leveljc0\leveljcn0\levelfollow0\levelstartat1
\levelspace0\levelindent0{\leveltext\'11\'00.\'01.\'02.\'03.\'04.\'05.\'06.\'07.\'08;}{\levelnumbers\'01\'03\'05\'07\'09\'0b\'0d\'0f\'11;}\rtlch\fcs1 \af0 \ltrch\fcs0 \fbias0 \fi-1800\li1800\jclisttab\tx1800\lin1800 }{\listname ;}\listid233053280}
{\list\listtemplateid269381388\listhybrid{\listlevel\levelnfc0\levelnfcn0\leveljc0\leveljcn0\levelfollow0\levelstartat1\levelspace0\levelindent0{\leveltext\leveltemplateid1994686728\'02\'00.;}{\levelnumbers\'01;}\rtlch\fcs1 \af0 \ltrch\fcs0 \fbias0 
\fi-360\li720\jclisttab\tx720\lin720 }{\listlevel\levelnfc255\levelnfcn255\leveljc0\leveljcn0\levelfollow0\levelstartat0\levelspace0\levelindent0{\leveltext\leveltemplateid-2036938464\'00;}{\levelnumbers;}\rtlch\fcs1 \af0 \ltrch\fcs0 \jclisttab\tx360 }
{\listlevel\levelnfc255\levelnfcn255\leveljc0\leveljcn0\levelfollow0\levelstartat0\levelspace0\levelindent0{\leveltext\leveltemplateid1578941516\'00;}{\levelnumbers;}\rtlch\fcs1 \af0 \ltrch\fcs0 \jclisttab\tx360 }{\listlevel\levelnfc255\levelnfcn255
\leveljc0\leveljcn0\levelfollow0\levelstartat0\levelspace0\levelindent0{\leveltext\leveltemplateid936021434\'00;}{\levelnumbers;}\rtlch\fcs1 \af0 \ltrch\fcs0 \jclisttab\tx360 }{\listlevel\levelnfc255\levelnfcn255\leveljc0\leveljcn0\levelfollow0
\levelstartat0\levelspace0\levelindent0{\leveltext\leveltemplateid-1323636224\'00;}{\levelnumbers;}\rtlch\fcs1 \af0 \ltrch\fcs0 \jclisttab\tx360 }{\listlevel\levelnfc255\levelnfcn255\leveljc0\leveljcn0\levelfollow0\levelstartat0\levelspace0\levelindent0
{\leveltext\leveltemplateid-1892018578\'00;}{\levelnumbers;}\rtlch\fcs1 \af0 \ltrch\fcs0 \jclisttab\tx360 }{\listlevel\levelnfc255\levelnfcn255\leveljc0\leveljcn0\levelfollow0\levelstartat0\levelspace0\levelindent0{\leveltext\leveltemplateid-108487922
\'00;}{\levelnumbers;}\rtlch\fcs1 \af0 \ltrch\fcs0 \jclisttab\tx360 }{\listlevel\levelnfc255\levelnfcn255\leveljc0\leveljcn0\levelfollow0\levelstartat0\levelspace0\levelindent0{\leveltext\leveltemplateid-4969626\'00;}{\levelnumbers;}\rtlch\fcs1 \af0 
\ltrch\fcs0 \jclisttab\tx360 }{\listlevel\levelnfc255\levelnfcn255\leveljc0\leveljcn0\levelfollow0\levelstartat0\levelspace0\levelindent0{\leveltext\leveltemplateid1135759886\'00;}{\levelnumbers;}\rtlch\fcs1 \af0 \ltrch\fcs0 \jclisttab\tx360 }{\listname 
;}\listid243993609}{\list\listtemplateid1411046654{\listlevel\levelnfc0\levelnfcn0\leveljc0\leveljcn0\levelfollow0\levelstartat2\levelspace0\levelindent0{\leveltext\'01\'00;}{\levelnumbers\'01;}\rtlch\fcs1 \af0 \ltrch\fcs0 \fbias0 \fi-360\li360
\jclisttab\tx360\lin360 }{\listlevel\levelnfc0\levelnfcn0\leveljc0\leveljcn0\levelfollow0\levelstartat2\levelspace0\levelindent0{\leveltext\'03\'00.\'01;}{\levelnumbers\'01\'03;}\rtlch\fcs1 \af0 \ltrch\fcs0 \fbias0 \fi-360\li720\jclisttab\tx720\lin720 }
{\listlevel\levelnfc0\levelnfcn0\leveljc0\leveljcn0\levelfollow0\levelstartat1\levelspace0\levelindent0{\leveltext\'05\'00.\'01.\'02;}{\levelnumbers\'01\'03\'05;}\rtlch\fcs1 \af0 \ltrch\fcs0 \fbias0 \fi-720\li1440\jclisttab\tx1440\lin1440 }{\listlevel
\levelnfc0\levelnfcn0\leveljc0\leveljcn0\levelfollow0\levelstartat1\levelspace0\levelindent0{\leveltext\'07\'00.\'01.\'02.\'03;}{\levelnumbers\'01\'03\'05\'07;}\rtlch\fcs1 \af0 \ltrch\fcs0 \fbias0 \fi-720\li1800\jclisttab\tx1800\lin1800 }{\listlevel
\levelnfc0\levelnfcn0\leveljc0\leveljcn0\levelfollow0\levelstartat1\levelspace0\levelindent0{\leveltext\'09\'00.\'01.\'02.\'03.\'04;}{\levelnumbers\'01\'03\'05\'07\'09;}\rtlch\fcs1 \af0 \ltrch\fcs0 \fbias0 \fi-1080\li2520\jclisttab\tx2520\lin2520 }
{\listlevel\levelnfc0\levelnfcn0\leveljc0\leveljcn0\levelfollow0\levelstartat1\levelspace0\levelindent0{\leveltext\'0b\'00.\'01.\'02.\'03.\'04.\'05;}{\levelnumbers\'01\'03\'05\'07\'09\'0b;}\rtlch\fcs1 \af0 \ltrch\fcs0 \fbias0 \fi-1080\li2880
\jclisttab\tx2880\lin2880 }{\listlevel\levelnfc0\levelnfcn0\leveljc0\leveljcn0\levelfollow0\levelstartat1\levelspace0\levelindent0{\leveltext\'0d\'00.\'01.\'02.\'03.\'04.\'05.\'06;}{\levelnumbers\'01\'03\'05\'07\'09\'0b\'0d;}\rtlch\fcs1 \af0 \ltrch\fcs0 
\fbias0 \fi-1440\li3600\jclisttab\tx3600\lin3600 }{\listlevel\levelnfc0\levelnfcn0\leveljc0\leveljcn0\levelfollow0\levelstartat1\levelspace0\levelindent0{\leveltext\'0f\'00.\'01.\'02.\'03.\'04.\'05.\'06.\'07;}{\levelnumbers
\'01\'03\'05\'07\'09\'0b\'0d\'0f;}\rtlch\fcs1 \af0 \ltrch\fcs0 \fbias0 \fi-1440\li3960\jclisttab\tx3960\lin3960 }{\listlevel\levelnfc0\levelnfcn0\leveljc0\leveljcn0\levelfollow0\levelstartat1\levelspace0\levelindent0{\leveltext
\'11\'00.\'01.\'02.\'03.\'04.\'05.\'06.\'07.\'08;}{\levelnumbers\'01\'03\'05\'07\'09\'0b\'0d\'0f\'11;}\rtlch\fcs1 \af0 \ltrch\fcs0 \fbias0 \fi-1800\li4680\jclisttab\tx4680\lin4680 }{\listname ;}\listid368384829}{\list\listtemplateid1424627480\listhybrid
{\listlevel\levelnfc23\levelnfcn23\leveljc0\leveljcn0\levelfollow0\levelstartat1\levelspace0\levelindent0{\leveltext\leveltemplateid1868871046\'01-;}{\levelnumbers;}\loch\af0\hich\af0\dbch\af0\fbias0 \fi-360\li420\jclisttab\tx420\lin420 }{\listlevel
\levelnfc23\levelnfcn23\leveljc0\leveljcn0\levelfollow0\levelstartat1\lvltentative\levelspace0\levelindent0{\leveltext\leveltemplateid68747267\'01o;}{\levelnumbers;}\f2\fbias0 \fi-360\li1140\jclisttab\tx1140\lin1140 }{\listlevel\levelnfc23\levelnfcn23
\leveljc0\leveljcn0\levelfollow0\levelstartat1\lvltentative\levelspace0\levelindent0{\leveltext\leveltemplateid68747269\'01\u-3929 ?;}{\levelnumbers;}\f10\fbias0 \fi-360\li1860\jclisttab\tx1860\lin1860 }{\listlevel\levelnfc23\levelnfcn23\leveljc0
\leveljcn0\levelfollow0\levelstartat1\lvltentative\levelspace0\levelindent0{\leveltext\leveltemplateid68747265\'01\u-3913 ?;}{\levelnumbers;}\f3\fbias0 \fi-360\li2580\jclisttab\tx2580\lin2580 }{\listlevel\levelnfc23\levelnfcn23\leveljc0\leveljcn0
\levelfollow0\levelstartat1\lvltentative\levelspace0\levelindent0{\leveltext\leveltemplateid68747267\'01o;}{\levelnumbers;}\f2\fbias0 \fi-360\li3300\jclisttab\tx3300\lin3300 }{\listlevel\levelnfc23\levelnfcn23\leveljc0\leveljcn0\levelfollow0\levelstartat1
\lvltentative\levelspace0\levelindent0{\leveltext\leveltemplateid68747269\'01\u-3929 ?;}{\levelnumbers;}\f10\fbias0 \fi-360\li4020\jclisttab\tx4020\lin4020 }{\listlevel\levelnfc23\levelnfcn23\leveljc0\leveljcn0\levelfollow0\levelstartat1\lvltentative
\levelspace0\levelindent0{\leveltext\leveltemplateid68747265\'01\u-3913 ?;}{\levelnumbers;}\f3\fbias0 \fi-360\li4740\jclisttab\tx4740\lin4740 }{\listlevel\levelnfc23\levelnfcn23\leveljc0\leveljcn0\levelfollow0\levelstartat1\lvltentative\levelspace0
\levelindent0{\leveltext\leveltemplateid68747267\'01o;}{\levelnumbers;}\f2\fbias0 \fi-360\li5460\jclisttab\tx5460\lin5460 }{\listlevel\levelnfc23\levelnfcn23\leveljc0\leveljcn0\levelfollow0\levelstartat1\lvltentative\levelspace0\levelindent0{\leveltext
\leveltemplateid68747269\'01\u-3929 ?;}{\levelnumbers;}\f10\fbias0 \fi-360\li6180\jclisttab\tx6180\lin6180 }{\listname ;}\listid634527506}{\list\listtemplateid1914202178\listhybrid{\listlevel\levelnfc0\levelnfcn0\leveljc0\leveljcn0\levelfollow0
\levelstartat1\levelspace0\levelindent0{\leveltext\leveltemplateid745160830\'02\'00.;}{\levelnumbers\'01;}\rtlch\fcs1 \af0 \ltrch\fcs0 \fbias0 \fi-360\li720\jclisttab\tx720\lin720 }{\listlevel\levelnfc255\levelnfcn255\leveljc0\leveljcn0\levelfollow0
\levelstartat0\levelspace0\levelindent0{\leveltext\leveltemplateid-1816475770\'00;}{\levelnumbers;}\rtlch\fcs1 \af0 \ltrch\fcs0 \jclisttab\tx360 }{\listlevel\levelnfc255\levelnfcn255\leveljc0\leveljcn0\levelfollow0\levelstartat0\levelspace0\levelindent0
{\leveltext\leveltemplateid-715330988\'00;}{\levelnumbers;}\rtlch\fcs1 \af0 \ltrch\fcs0 \jclisttab\tx360 }{\listlevel\levelnfc255\levelnfcn255\leveljc0\leveljcn0\levelfollow0\levelstartat0\levelspace0\levelindent0{\leveltext\leveltemplateid1569232270
\'00;}{\levelnumbers;}\rtlch\fcs1 \af0 \ltrch\fcs0 \jclisttab\tx360 }{\listlevel\levelnfc255\levelnfcn255\leveljc0\leveljcn0\levelfollow0\levelstartat0\levelspace0\levelindent0{\leveltext\leveltemplateid-297370836\'00;}{\levelnumbers;}\rtlch\fcs1 \af0 
\ltrch\fcs0 \jclisttab\tx360 }{\listlevel\levelnfc255\levelnfcn255\leveljc0\leveljcn0\levelfollow0\levelstartat0\levelspace0\levelindent0{\leveltext\leveltemplateid-346936770\'00;}{\levelnumbers;}\rtlch\fcs1 \af0 \ltrch\fcs0 \jclisttab\tx360 }{\listlevel
\levelnfc255\levelnfcn255\leveljc0\leveljcn0\levelfollow0\levelstartat0\levelspace0\levelindent0{\leveltext\leveltemplateid416698944\'00;}{\levelnumbers;}\rtlch\fcs1 \af0 \ltrch\fcs0 \jclisttab\tx360 }{\listlevel\levelnfc255\levelnfcn255\leveljc0
\leveljcn0\levelfollow0\levelstartat0\levelspace0\levelindent0{\leveltext\leveltemplateid-596467262\'00;}{\levelnumbers;}\rtlch\fcs1 \af0 \ltrch\fcs0 \jclisttab\tx360 }{\listlevel\levelnfc255\levelnfcn255\leveljc0\leveljcn0\levelfollow0\levelstartat0
\levelspace0\levelindent0{\leveltext\leveltemplateid11278156\'00;}{\levelnumbers;}\rtlch\fcs1 \af0 \ltrch\fcs0 \jclisttab\tx360 }{\listname ;}\listid1261259807}}{\*\listoverridetable{\listoverride\listid1261259807\listoverridecount0\ls1}
{\listoverride\listid634527506\listoverridecount0\ls2}{\listoverride\listid233053280\listoverridecount0\ls3}{\listoverride\listid243993609\listoverridecount0\ls4}{\listoverride\listid368384829\listoverridecount0\ls5}}{\*\rsidtbl \rsid1339617\rsid1586505
\rsid1600374\rsid1771507\rsid1986875\rsid2235853\rsid2296829\rsid2567226\rsid2701137\rsid3560132\rsid3671104\rsid3692728\rsid4980816\rsid5776876\rsid5975359\rsid6257560\rsid6570302\rsid7013593\rsid7235628\rsid7749673\rsid8087560\rsid8326885\rsid8672163
\rsid8675175\rsid9981679\rsid10440144\rsid10497064\rsid10908886\rsid12060716\rsid12261729\rsid12544729\rsid12806430\rsid13111076\rsid13909505\rsid15236432\rsid15609114\rsid15928529\rsid16077654}{\*\generator Microsoft Word 11.0.0000;}{\info
{\title 3proxy developer reference}{\author Vladimir Dubrovin}{\operator Vladimir Dubrovin}{\creatim\yr2007\mo11\dy23\hr19\min28}{\revtim\yr2008\mo1\dy22\hr19\min28}{\version8}{\edmins86}{\nofpages8}{\nofwords2396}{\nofchars13662}{\*\company Sandy}
{\nofcharsws16026}{\vern24611}{\*\password 00000000}}{\*\xmlnstbl {\xmlns1 http://schemas.microsoft.com/office/word/2003/wordml}{\xmlns2 urn:schemas-microsoft-com:office:smarttags}}
\paperw11906\paperh16838\margl1701\margr850\margt1134\margb1134\gutter0\ltrsect 
\deftab708\widowctrl\ftnbj\aenddoc\donotembedsysfont1\donotembedlingdata0\grfdocevents0\validatexml1\showplaceholdtext0\ignoremixedcontent0\saveinvalidxml0\showxmlerrors1\noxlattoyen\expshrtn\noultrlspc\dntblnsbdb\nospaceforul\formshade\horzdoc\dgmargin
\dghspace180\dgvspace180\dghorigin1701\dgvorigin1134\dghshow1\dgvshow1
\jexpand\viewkind1\viewscale130\viewzk2\pgbrdrhead\pgbrdrfoot\splytwnine\ftnlytwnine\htmautsp\nolnhtadjtbl\useltbaln\alntblind\lytcalctblwd\lyttblrtgr\lnbrkrule\nobrkwrptbl\viewnobound1\snaptogridincell
\allowfieldendsel\wrppunct\asianbrkrule\rsidroot10497064\newtblstyruls\nogrowautofit \fet0{\*\wgrffmtfilter 013f}\ilfomacatclnup0\ltrpar \sectd \ltrsect\linex0\headery708\footery708\colsx708\endnhere\sectlinegrid360\sectdefaultcl\sftnbj {\*\pnseclvl1
\pnucrm\pnstart1\pnindent720\pnhang {\pntxta .}}{\*\pnseclvl2\pnucltr\pnstart1\pnindent720\pnhang {\pntxta .}}{\*\pnseclvl3\pndec\pnstart1\pnindent720\pnhang {\pntxta .}}{\*\pnseclvl4\pnlcltr\pnstart1\pnindent720\pnhang {\pntxta )}}{\*\pnseclvl5
\pndec\pnstart1\pnindent720\pnhang {\pntxtb (}{\pntxta )}}{\*\pnseclvl6\pnlcltr\pnstart1\pnindent720\pnhang {\pntxtb (}{\pntxta )}}{\*\pnseclvl7\pnlcrm\pnstart1\pnindent720\pnhang {\pntxtb (}{\pntxta )}}{\*\pnseclvl8\pnlcltr\pnstart1\pnindent720\pnhang 
{\pntxtb (}{\pntxta )}}{\*\pnseclvl9\pnlcrm\pnstart1\pnindent720\pnhang {\pntxtb (}{\pntxta )}}\pard\plain \ltrpar\ql \li0\ri0\widctlpar\wrapdefault\aspalpha\aspnum\faauto\adjustright\rin0\lin0\itap0 \rtlch\fcs1 \af0\afs24\alang1025 \ltrch\fcs0 
\fs24\lang1049\langfe1049\cgrid\langnp1049\langfenp1049 {\rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid13111076 
\par }\pard \ltrpar\qc \li0\ri0\widctlpar\wrapdefault\aspalpha\aspnum\faauto\adjustright\rin0\lin0\itap0\pararsid2235853 {\rtlch\fcs1 \af0\afs32 \ltrch\fcs0 \fs32\lang1033\langfe1049\langnp1033\insrsid10497064\charrsid12261729 3proxy developer reference}{
\rtlch\fcs1 \af0\afs32 \ltrch\fcs0 \fs32\lang1033\langfe1049\langnp1033\insrsid10497064 
\par }{\rtlch\fcs1 \af0\afs20 \ltrch\fcs0 \fs20\lang1033\langfe1049\langnp1033\insrsid2235853\charrsid12261729 $Id: devref.rtf,v 1.4 2008/01/24 08:44:57 vlad Exp $
\par }\pard \ltrpar\ql \li0\ri0\widctlpar\wrapdefault\aspalpha\aspnum\faauto\adjustright\rin0\lin0\itap0 {\rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid10497064 
\par {\listtext\pard\plain\ltrpar \rtlch\fcs1 \af0 \ltrch\fcs0 \b\lang1033\langfe1049\langnp1033\insrsid10497064\charrsid12060716 \hich\af0\dbch\af0\loch\f0 1.\tab}}\pard \ltrpar\ql \fi-360\li720\ri0\widctlpar
\jclisttab\tx720\wrapdefault\aspalpha\aspnum\faauto\ls4\adjustright\rin0\lin720\itap0\pararsid6570302 {\rtlch\fcs1 \af0 \ltrch\fcs0 \b\lang1033\langfe1049\langnp1033\insrsid10497064\charrsid12060716 Understanding Internal 3proxy structure
\par }\pard \ltrpar\ql \li0\ri0\widctlpar\wrapdefault\aspalpha\aspnum\faauto\adjustright\rin0\lin0\itap0\pararsid10497064 {\rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid10497064 
\par 3proxy is implemented as multithread application. Server model is implemented as \'93one connection \endash  one thread\'94. It means, for every client connection new thre
ad is created. This model is effective enough under Windows, because it allows it avoid thread creation on asynchronous operations, yet under most POSIX systems this model can not be considered as most efficient. It\rquote 
s planned for (very far in future) release to implement more efficient model, where single thread can serve few clients.
\par 
\par 
\par {\listtext\pard\plain\ltrpar \rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid10497064 \hich\af0\dbch\af0\loch\f0 1.1\tab}}\pard \ltrpar\ql \fi-360\li360\ri0\widctlpar
\jclisttab\tx360\wrapdefault\aspalpha\aspnum\faauto\ls3\ilvl1\adjustright\rin0\lin360\itap0\pararsid6570302 {\rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid10497064 main thread:
\par }\pard \ltrpar\ql \li0\ri0\widctlpar\wrapdefault\aspalpha\aspnum\faauto\adjustright\rin0\lin0\itap0\pararsid6570302 {\rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid6570302 
\par }\pard \ltrpar\ql \li0\ri0\widctlpar\wrapdefault\aspalpha\aspnum\faauto\adjustright\rin0\lin0\itap0\pararsid10497064 {\rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid10497064 
3proxy begins with main thread. This thread parses configuration}{\rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid4980816  file and starts main loop}{\rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid10497064 . }{
\rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid8672163  During configuration file parsing }{\rtlch\fcs1 \af0 \ltrch\fcs0 \i\lang1033\langfe1049\langnp1033\insrsid8672163\charrsid8672163 struct extaparam conf;}{\rtlch\fcs1 \af0 
\ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid8672163  structure is filled and service threads are started.
\par }{\rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid3560132 
\par }{\rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid6570302 Main loop cycle }{\rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid2701137 takes approximately 1 second and }{\rtlch\fcs1 \af0 \ltrch\fcs0 
\lang1033\langfe1049\langnp1033\insrsid6570302 does these tasks:}{\rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid10497064 
\par {\listtext\pard\plain\ltrpar \rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid6570302 \hich\af0\dbch\af0\loch\f0 -\tab}}\pard \ltrpar\ql \fi-360\li420\ri0\widctlpar
\jclisttab\tx420\wrapdefault\aspalpha\aspnum\faauto\ls2\adjustright\rin0\lin420\itap0\pararsid6570302 {\rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid6570302 re-reads configuration}{\rtlch\fcs1 \af0 \ltrch\fcs0 
\lang1033\langfe1049\langnp1033\insrsid8672163  file}{\rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid6570302 , if necessary
\par {\listtext\pard\plain\ltrpar \rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid6570302 \hich\af0\dbch\af0\loch\f0 -\tab}performs scheduled tasks
\par {\listtext\pard\plain\ltrpar \rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid6570302 \hich\af0\dbch\af0\loch\f0 -\tab}monitors files (\lquote monitor}{\rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid4980816 \rquote 
 command}{\rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid6570302 )}{\rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid2701137 , approx. once in a minute}{\rtlch\fcs1 \af0 \ltrch\fcs0 
\lang1033\langfe1049\langnp1033\insrsid6570302 
\par {\listtext\pard\plain\ltrpar \rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid6570302 \hich\af0\dbch\af0\loch\f0 -\tab}rotates main logfile
\par {\listtext\pard\plain\ltrpar \rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid6570302 \hich\af0\dbch\af0\loch\f0 -\tab}dumps counters to }{\rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid2701137 
file, approx. once in a minute}{\rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid6570302 
\par {\listtext\pard\plain\ltrpar \rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid2701137 \hich\af0\dbch\af0\loch\f0 -\tab}}\pard \ltrpar\ql \fi-360\li420\ri0\widctlpar
\jclisttab\tx420\wrapdefault\aspalpha\aspnum\faauto\ls2\adjustright\rin0\lin420\itap0\pararsid2701137 {\rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid2701137 performs termination, if required
\par }\pard \ltrpar\ql \li60\ri0\widctlpar\wrapdefault\aspalpha\aspnum\faauto\adjustright\rin0\lin60\itap0\pararsid8672163 {\rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid3560132 
\par }{\rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid2701137 I}{\rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid8672163 t\rquote s guaranteed every configuration and schedule command is executed from the same thread.

\par Main thread is implemented in }{\rtlch\fcs1 \af0 \ltrch\fcs0 \b\lang1033\langfe1049\langnp1033\insrsid8672163\charrsid12544729 3proxy.c}{\rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid8672163 
\par }\pard \ltrpar\ql \li60\ri0\widctlpar\wrapdefault\aspalpha\aspnum\faauto\adjustright\rin0\lin60\itap0\pararsid6570302 {\rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid6570302 
\par {\listtext\pard\plain\ltrpar \rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid8672163 \hich\af0\dbch\af0\loch\f0 1.2\tab}}\pard \ltrpar\ql \fi-360\li360\ri0\widctlpar
\jclisttab\tx360\wrapdefault\aspalpha\aspnum\faauto\ls3\ilvl1\adjustright\rin0\lin360\itap0\pararsid8672163 {\rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid8672163 service thread}{\rtlch\fcs1 \af0 \ltrch\fcs0 
\lang1033\langfe1049\langnp1033\insrsid6570302 
\par }\pard \ltrpar\ql \li0\ri0\widctlpar\wrapdefault\aspalpha\aspnum\faauto\adjustright\rin0\lin0\itap0\pararsid8672163 {\rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid8672163 
\par Service threads are started immediately, than service command (e.g. \lquote proxy\rquote  or \lquote socks\rquote ) are found during configuration file parsing. Each command creates new thread. Thread does these tasks:
\par {\listtext\pard\plain\ltrpar \rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid8672163 \hich\af0\dbch\af0\loch\f0 -\tab}}\pard \ltrpar\ql \fi-360\li420\ri0\widctlpar
\jclisttab\tx420\wrapdefault\aspalpha\aspnum\faauto\ls2\adjustright\rin0\lin420\itap0\pararsid8672163 {\rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid8672163 parses service command arguments and fills \lquote }{\rtlch\fcs1 \af0 
\ltrch\fcs0 \i\lang1033\langfe1049\langnp1033\insrsid8672163\charrsid6257560 struct srvparam srv}{\rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid8672163 \rquote  structure with service configuration and \lquote }{\rtlch\fcs1 \af0 
\ltrch\fcs0 \i\lang1033\langfe1049\langnp1033\insrsid8672163\charrsid6257560 struct clientparam defparam}{\rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid8672163 \rquote  structure with default client configuration
\par {\listtext\pard\plain\ltrpar \rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid6257560 \hich\af0\dbch\af0\loch\f0 -\tab}}{\rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid6257560 initializes filters}{\rtlch\fcs1 \af0 
\ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid3560132  (filter_open)}{\rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid6257560\charrsid8672163 
\par {\listtext\pard\plain\ltrpar \rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid6257560 \hich\af0\dbch\af0\loch\f0 -\tab}}\pard \ltrpar\ql \fi-360\li420\ri0\widctlpar
\jclisttab\tx420\wrapdefault\aspalpha\aspnum\faauto\ls2\adjustright\rin0\lin420\itap0\pararsid6257560 {\rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid6257560 creates }{\rtlch\fcs1 \af0 \ltrch\fcs0 
\lang1033\langfe1049\langnp1033\insrsid2701137 and initializes }{\rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid6257560 listening service socket
\par {\listtext\pard\plain\ltrpar \rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid2701137 \hich\af0\dbch\af0\loch\f0 -\tab}}\pard \ltrpar\ql \fi-360\li420\ri0\widctlpar
\jclisttab\tx420\wrapdefault\aspalpha\aspnum\faauto\ls2\adjustright\rin0\lin420\itap0\pararsid3560132 {\rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid2701137 enters into service loop}{\rtlch\fcs1 \af0 \ltrch\fcs0 
\lang1033\langfe1049\langnp1033\insrsid3560132 
\par {\listtext\pard\plain\ltrpar \rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid3560132 \hich\af0\dbch\af0\loch\f0 -\tab}terminates filters (filter_close)
\par }\pard \ltrpar\ql \li60\ri0\widctlpar\wrapdefault\aspalpha\aspnum\faauto\adjustright\rin0\lin60\itap0\pararsid2701137 {\rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid3560132 
\par }{\rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid2701137 service loop:
\par {\listtext\pard\plain\ltrpar \rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid2701137 \hich\af0\dbch\af0\loch\f0 -\tab}}\pard \ltrpar\ql \fi-360\li420\ri0\widctlpar
\jclisttab\tx420\wrapdefault\aspalpha\aspnum\faauto\ls2\adjustright\rin0\lin420\itap0\pararsid2701137 {\rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid2701137 
checks for configuration reload (approximately every second), thread exits if configuration reloaded or 3proxy is in terminating state.
\par {\listtext\pard\plain\ltrpar \rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid2701137 \hich\af0\dbch\af0\loch\f0 -\tab}accepts client connection and creates \lquote }{\rtlch\fcs1 \af0 \ltrch\fcs0 
\i\lang1033\langfe1049\langnp1033\insrsid2701137 struct clientparam new}{\rtlch\fcs1 \af0 \ltrch\fcs0 \i\lang1033\langfe1049\langnp1033\insrsid2701137\charrsid6257560 param}{\rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid2701137 
\rquote  structure with client configuration
\par {\listtext\pard\plain\ltrpar \rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid3560132 \hich\af0\dbch\af0\loch\f0 -\tab}}{\rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid3560132 
creates/checks client filters (filter_client)
\par {\listtext\pard\plain\ltrpar \rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid3560132 \hich\af0\dbch\af0\loch\f0 -\tab}creates client thread with newly created \lquote }{\rtlch\fcs1 \af0 \ltrch\fcs0 
\i\lang1033\langfe1049\langnp1033\insrsid3560132 struct clientparam new}{\rtlch\fcs1 \af0 \ltrch\fcs0 \i\lang1033\langfe1049\langnp1033\insrsid3560132\charrsid6257560 param}{\rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid3560132 
\rquote 
\par }\pard \ltrpar\ql \li0\ri0\widctlpar\wrapdefault\aspalpha\aspnum\faauto\adjustright\rin0\lin0\itap0\pararsid6570302 {\rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid6570302 
\par }{\rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid5975359 service threads are implemented in }{\rtlch\fcs1 \af0 \ltrch\fcs0 \b\lang1033\langfe1049\langnp1033\insrsid7749673\charrsid12544729 proxymain.c}{\rtlch\fcs1 \af0 \ltrch\fcs0 
\lang1033\langfe1049\langnp1033\insrsid5975359\charrsid7749673 
\par }{\rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid5975359 
\par }{\rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid3560132 Please note: struct clientparam is freed and }{\rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid3560132\charrsid3560132 filter_clear}{\rtlch\fcs1 \af0 
\ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid3560132  is executed from different (client) thread.
\par 
\par {\listtext\pard\plain\ltrpar \rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid3560132 \hich\af0\dbch\af0\loch\f0 1.3\tab}}\pard \ltrpar\ql \fi-360\li360\ri0\widctlpar
\jclisttab\tx360\wrapdefault\aspalpha\aspnum\faauto\ls3\ilvl1\adjustright\rin0\lin360\itap0\pararsid3560132 {\rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid3560132 client thread
\par }\pard \ltrpar\ql \li0\ri0\widctlpar\wrapdefault\aspalpha\aspnum\faauto\adjustright\rin0\lin0\itap0\pararsid7749673 {\rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid7749673 
\par Client threads are started from service thread. Client thread:
\par {\listtext\pard\plain\ltrpar \rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid7749673 \hich\af0\dbch\af0\loch\f0 -\tab}}\pard \ltrpar\ql \fi-360\li420\ri0\widctlpar
\jclisttab\tx420\wrapdefault\aspalpha\aspnum\faauto\ls2\adjustright\rin0\lin420\itap0\pararsid7749673 {\rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid7749673 
reads client request (except portmappers) with authentication information and request headers (if any).
\par {\listtext\pard\plain\ltrpar \rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid5776876 \hich\af0\dbch\af0\loch\f0 -\tab}}{\rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid5776876 filters request (if any) with  }{
\rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid5776876\charrsid5776876 filter_request}{\rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid7749673 
\par {\listtext\pard\plain\ltrpar \rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid5776876 \hich\af0\dbch\af0\loch\f0 -\tab}}{\rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid5776876 filters headers (if any) with }{
\rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid5776876\charrsid5776876 filter_header_cli}{\rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid5776876 
\par {\listtext\pard\plain\ltrpar \rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid5776876 \hich\af0\dbch\af0\loch\f0 -\tab}performs authentication and authorization
\par {\listtext\pard\plain\ltrpar \rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid5776876 \hich\af0\dbch\af0\loch\f0 -\tab}established connection with server
\par {\listtext\pard\plain\ltrpar \rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid5776876 \hich\af0\dbch\af0\loch\f0 -\tab}sends request to server
\par {\listtext\pard\plain\ltrpar \rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid5776876 \hich\af0\dbch\af0\loch\f0 -\tab}}\pard \ltrpar\ql \fi-360\li420\ri0\widctlpar
\jclisttab\tx420\wrapdefault\aspalpha\aspnum\faauto\ls2\adjustright\rin0\lin420\itap0\pararsid3560132 {\rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid5776876 filters server headers (if any)}{\rtlch\fcs1 \af0 \ltrch\fcs0 
\lang1033\langfe1049\langnp1033\insrsid3560132 
\par {\listtext\pard\plain\ltrpar \rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid5776876 \hich\af0\dbch\af0\loch\f0 -\tab}}{\rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid5776876 maps client end s
erver sockets to transmit data between client and server
\par {\listtext\pard\plain\ltrpar \rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid5776876 \hich\af0\dbch\af0\loch\f0 -\tab}}\pard \ltrpar\ql \fi-360\li420\ri0\widctlpar
\jclisttab\tx420\wrapdefault\aspalpha\aspnum\faauto\ls2\adjustright\rin0\lin420\itap0\pararsid5776876 {\rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid5776876 logs request. Global counters are also updated on this operation
\par {\listtext\pard\plain\ltrpar \rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid5776876 \hich\af0\dbch\af0\loch\f0 -\tab}}\pard \ltrpar\ql \fi-360\li420\ri0\widctlpar
\jclisttab\tx420\wrapdefault\aspalpha\aspnum\faauto\ls2\adjustright\rin0\lin420\itap0\pararsid3560132 {\rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid5776876 clears client filters (}{\rtlch\fcs1 \af0 \ltrch\fcs0 
\lang1033\langfe1049\langnp1033\insrsid5776876\charrsid5776876 filter_clear}{\rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid5776876 )
\par {\listtext\pard\plain\ltrpar \rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid5776876 \hich\af0\dbch\af0\loch\f0 -\tab}frees }{\rtlch\fcs1 \af0 \ltrch\fcs0 \i\lang1033\langfe1049\langnp1033\insrsid5776876\charrsid5776876 
struct clientparam}{\rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid5776876  data
\par }\pard \ltrpar\ql \li0\ri0\widctlpar\wrapdefault\aspalpha\aspnum\faauto\adjustright\rin0\lin0\itap0\pararsid5776876 {\rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid5776876 
\par }{\rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid10908886 in some point client thread may loop to process few client requests from the same connection (e.g. HTTP \lquote established\rquote  connection in \lquote proxy\rquote ).
\par 
\par }{\rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid5776876 Socket mapping does:
\par {\listtext\pard\plain\ltrpar \rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid5776876 \hich\af0\dbch\af0\loch\f0 -\tab}}\pard \ltrpar\ql \fi-360\li420\ri0\widctlpar
\jclisttab\tx420\wrapdefault\aspalpha\aspnum\faauto\ls2\adjustright\rin0\lin420\itap0\pararsid5776876 {\rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid5776876 caches data in internal client and server buffers
\par {\listtext\pard\plain\ltrpar \rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid5776876 \hich\af0\dbch\af0\loch\f0 -\tab}delays data transmit to limit bandwidth
\par {\listtext\pard\plain\ltrpar \rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid5776876 \hich\af0\dbch\af0\loch\f0 -\tab}performs data filtering (}{\rtlch\fcs1 \af0 \ltrch\fcs0 
\lang1033\langfe1049\langnp1033\insrsid5776876\charrsid5776876 filter_data_cli}{\rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid5776876  / }{\rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid5776876\charrsid5776876 
filter_data_srv}{\rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid5776876 )
\par }\pard \ltrpar\ql \li0\ri0\widctlpar\wrapdefault\aspalpha\aspnum\faauto\adjustright\rin0\lin0\itap0\pararsid3560132 {\rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid3560132 
\par }{\rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid1586505 client threads are implemented in }{\rtlch\fcs1 \af0 \ltrch\fcs0 \b\lang1033\langfe1049\langnp1033\insrsid1586505\charrsid12544729 proxy.c}{\rtlch\fcs1 \af0 \ltrch\fcs0 
\lang1033\langfe1049\langnp1033\insrsid1586505 , }{\rtlch\fcs1 \af0 \ltrch\fcs0 \b\lang1033\langfe1049\langnp1033\insrsid1586505\charrsid12544729 socks.c}{\rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid1586505 , }{\rtlch\fcs1 \af0 
\ltrch\fcs0 \b\lang1033\langfe1049\langnp1033\insrsid1586505\charrsid12544729 pop3p.c}{\rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid1586505  etc.
\par 
\par {\listtext\pard\plain\ltrpar \rtlch\fcs1 \af0 \ltrch\fcs0 \b\lang1033\langfe1049\langnp1033\insrsid1586505\charrsid12060716 \hich\af0\dbch\af0\loch\f0 2.\tab}}\pard \ltrpar\ql \fi-360\li720\ri0\widctlpar
\jclisttab\tx720\wrapdefault\aspalpha\aspnum\faauto\ls4\adjustright\rin0\lin720\itap0\pararsid1586505 {\rtlch\fcs1 \af0 \ltrch\fcs0 \b\lang1033\langfe1049\langnp1033\insrsid1586505\charrsid12060716 Hacking into 3proxy code with plugins
\par }\pard \ltrpar\ql \li0\ri0\widctlpar\wrapdefault\aspalpha\aspnum\faauto\adjustright\rin0\lin0\itap0\pararsid3560132 {\rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid1586505 
\par }\pard \ltrpar\ql \li0\ri0\widctlpar\jclisttab\tx720\wrapdefault\aspalpha\aspnum\faauto\adjustright\rin0\lin0\itap0\pararsid2296829 {\rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid2296829 2.1 }{\rtlch\fcs1 \af0 \ltrch\fcs0 
\lang1033\langfe1049\langnp1033\insrsid1586505 What is 3proxy plugin
\par }\pard \ltrpar\ql \li0\ri0\widctlpar\wrapdefault\aspalpha\aspnum\faauto\adjustright\rin0\lin0\itap0\pararsid1586505 {\rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid1586505 
\par }{\rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid10440144 3proxy plugin is any dynamic/shared library. There is no specific requirement for plugin, actually you can load any dynamic library with \lquote plugin\rquote 
 command. No linking with any libraries are required. However, to interoperate with 3proxy dynamic library must have an export function 3proxy may call to pass the structure with required information.}{\rtlch\fcs1 \af0 \ltrch\fcs0 
\lang1033\langfe1049\langnp1033\insrsid1586505 
\par }{\rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid15928529 
\par }{\rtlch\fcs1 \af0 \ltrch\fcs0 \i\lang1033\langfe1049\langnp1033\insrsid10440144\charrsid10440144 typedef int (*PLUGINFUNC) (struct pluginlink *pluginlink, int argc, char** argv);
\par }{\rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid10440144 
\par }{\rtlch\fcs1 \af0 \ltrch\fcs0 \i\lang1033\langfe1049\langnp1033\insrsid10440144\charrsid10440144 struct pluginlink}{\rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid10440144 
 is a structure with export information, explained later, argc and argv are argument counter and array of arguments of \'93plugin\'94 command. Plugin should report it\rquote 
s status with integer return value. 0 is success, positive value indicates non-recoverable error, 3proxy do not parse rest of configuration and enters into termination state, negative value indicates recoverable value, 3proxy logs warning (if possible).}{
\rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid12544729  In case of C++, all 3proxy functions/structures must be extern \'93C\'94.}{\rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid10440144 
\par }{\rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid15928529 
\par }\pard \ltrpar\ql \li0\ri0\widctlpar\wrapdefault\aspalpha\aspnum\faauto\adjustright\rin0\lin0\itap0\pararsid15928529 {\rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid15928529 All 3proxy structures/functions descriptions are located in 
}{\rtlch\fcs1 \af0 \ltrch\fcs0 \b\lang1033\langfe1049\langnp1033\insrsid15928529\charrsid15928529 structures.h}{\rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid15928529 
\par }\pard \ltrpar\ql \li360\ri0\widctlpar\jclisttab\tx720\wrapdefault\aspalpha\aspnum\faauto\adjustright\rin0\lin360\itap0\pararsid2296829 {\rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid2296829 
\par }{\rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid15928529 
\par }\pard \ltrpar\ql \li0\ri0\widctlpar\jclisttab\tx720\wrapdefault\aspalpha\aspnum\faauto\adjustright\rin0\lin0\itap0\pararsid2296829 {\rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid2296829 2.2 }{\rtlch\fcs1 \af0 \ltrch\fcs0 
\lang1033\langfe1049\langnp1033\insrsid12544729 Understanding pluginlink structure
\par }\pard \ltrpar\ql \li0\ri0\widctlpar\wrapdefault\aspalpha\aspnum\faauto\adjustright\rin0\lin0\itap0\pararsid12544729 {\rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid12544729 
\par Because there is no linking between 3proxy and plugin, all 3proxy functions and structures are passed with pluginlink structure. Pluginlink is actually a collection of pointers to 3proxy internal structures and functions. Because plug
inlink is constantly extending, you should see it\rquote s definitions in }{\rtlch\fcs1 \af0 \ltrch\fcs0 \b\lang1033\langfe1049\langnp1033\insrsid12544729\charrsid12544729 structures.h}{\rtlch\fcs1 \af0 \ltrch\fcs0 
\lang1033\langfe1049\langnp1033\insrsid12544729 .
\par 
\par }{\rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid13909505 most important}{\rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid12544729  are:
\par }{\rtlch\fcs1 \af0 \ltrch\fcs0 \i\lang1033\langfe1049\langnp1033\insrsid13909505 
\par }{\rtlch\fcs1 \af0 \ltrch\fcs0 \i\lang1033\langfe1049\langnp1033\insrsid12544729\charrsid8087560 struct symbol symbols;
\par }{\rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid8087560 \'93symbols\'94
 is a kind of name/value  export table, made as a list. It can be used by plugins to exchange information and functions between plugins, e.g. to export functions from one plugin to another, where pluginlink is useless, because it\rquote s static. It
\rquote s quite simple:}{\rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid12544729 
\par }{\rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid8087560 
\par }\pard \ltrpar\ql \li0\ri0\widctlpar\wrapdefault\aspalpha\aspnum\faauto\adjustright\rin0\lin0\itap0\pararsid8087560 {\rtlch\fcs1 \af0 \ltrch\fcs0 \i\lang1033\langfe1049\langnp1033\insrsid8087560\charrsid8087560 struct symbol \{
\par \tab struct symbol *next;
\par \tab char * name;
\par \tab void * value;
\par \};
\par }{\rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid8087560 
\par }{\rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid13909505 n}{\rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid8087560 ame \endash  is a name of function or structure
\par }{\rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid13909505 v}{\rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid8087560 alue \endash  is it\rquote s value.
\par }{\rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid13909505 
\par }{\rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid8087560 u}{\rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid13909505 se}{\rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid8087560 
 pluginlink->fundbyname function to lookup, e.g.
\par }{\rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid13909505 
\par }{\rtlch\fcs1 \af0 \ltrch\fcs0 \i\lang1033\langfe1049\langnp1033\insrsid8087560\charrsid13909505 anotherplugindata = pluginlink->findbyname(\'93anotherplugindata\'94);
\par }{\rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid13909505 
\par }{\rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid8087560 To export }{\rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid13909505 something from your plugin, add your structure to this list.}{\rtlch\fcs1 \af0 
\ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid8087560\charrsid8087560 
\par }\pard \ltrpar\ql \li0\ri0\widctlpar\wrapdefault\aspalpha\aspnum\faauto\adjustright\rin0\lin0\itap0\pararsid12544729 {\rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid8087560\charrsid12544729 
\par }{\rtlch\fcs1 \af0 \ltrch\fcs0 \i\lang1033\langfe1049\langnp1033\insrsid12544729\charrsid13909505 struct extparam *conf;
\par }{\rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid12544729 pointer to conf structure, it holds all current 3proxy configuration
\par }{\rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid12544729\charrsid12544729 
\par }\pard \ltrpar\ql \li360\ri0\widctlpar\jclisttab\tx720\wrapdefault\aspalpha\aspnum\faauto\adjustright\rin0\lin360\itap0\pararsid2296829 {\rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid2296829 
\par }\pard \ltrpar\ql \li0\ri0\widctlpar\jclisttab\tx720\wrapdefault\aspalpha\aspnum\faauto\adjustright\rin0\lin0\itap0\pararsid2296829 {\rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid2296829 2.3 }{\rtlch\fcs1 \af0 \ltrch\fcs0 
\lang1033\langfe1049\langnp1033\insrsid12544729 How to get control within plugin}{\rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid10440144 
\par }\pard \ltrpar\ql \li0\ri0\widctlpar\wrapdefault\aspalpha\aspnum\faauto\adjustright\rin0\lin0\itap0\pararsid12544729 {\rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid12544729 
\par There are few points you can get control for your plugin, after it\rquote s loaded with \lquote plugin\rquote  command.
\par 
\par {\listtext\tab}}\pard \ltrpar\ql \li0\ri0\widctlpar\jclisttab\tx360\wrapdefault\aspalpha\aspnum\faauto\ls4\ilvl2\adjustright\rin0\lin0\itap0\pararsid13909505 {\rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid2296829 2.3.1 }{\rtlch\fcs1 
\af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid12544729 Adding configuration command processor with struct command structure
\par }\pard \ltrpar\ql \li0\ri0\widctlpar\wrapdefault\aspalpha\aspnum\faauto\adjustright\rin0\lin0\itap0\pararsid13909505 {\rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid13909505 
\par A list of configuration file command, available from 3proxy.cfg is extendable.  Each command is  defined by }{\rtlch\fcs1 \af0 \ltrch\fcs0 \i\lang1033\langfe1049\langnp1033\insrsid13909505\charrsid13909505 struct commands}{\rtlch\fcs1 \af0 \ltrch\fcs0 
\lang1033\langfe1049\langnp1033\insrsid13909505 :
\par 
\par }{\rtlch\fcs1 \af0 \ltrch\fcs0 \i\lang1033\langfe1049\langnp1033\insrsid13909505\charrsid13909505 struct commands \{
\par \tab struct commands *next;
\par \tab char * command;
\par \tab int (* handler)(int argc, unsigned char ** argv);
\par \tab int minargs;
\par \tab int maxargs;\tab 
\par \};
\par }{\rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid13909505 
\par }{\rtlch\fcs1 \af0 \ltrch\fcs0 \i\lang1033\langfe1049\langnp1033\insrsid13909505\charrsid13909505 struct commands *next}{\rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid13909505  - next element in list}{\rtlch\fcs1 \af0 \ltrch\fcs0 
\lang1033\langfe1049\langnp1033\insrsid13909505\charrsid13909505 
\par }{\rtlch\fcs1 \af0 \ltrch\fcs0 \i\lang1033\langfe1049\langnp1033\insrsid13909505\charrsid13909505 char * command}{\rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid13909505  \endash  command name
\par }{\rtlch\fcs1 \af0 \ltrch\fcs0 \i\lang1033\langfe1049\langnp1033\insrsid13909505\charrsid13909505 int (* handler)(i}{\rtlch\fcs1 \af0 \ltrch\fcs0 \i\lang1033\langfe1049\langnp1033\insrsid13909505 nt argc, unsigned char ** argv)}{\rtlch\fcs1 \af0 
\ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid13909505  \endash  command handler. It\rquote s called than \lquote command\rquote  is found in configuration files, argc is a number of arguments, counting command itself, argv is array of arguments.

\par }{\rtlch\fcs1 \af0 \ltrch\fcs0 \i\lang1033\langfe1049\langnp1033\insrsid13909505\charrsid12060716 minargs}{\rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid13909505  \endash  minimum number of arguments command support (>= 1)
\par }{\rtlch\fcs1 \af0 \ltrch\fcs0 \i\lang1033\langfe1049\langnp1033\insrsid12060716\charrsid12060716 maxargs}{\rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid12060716  \endash 
 maximum number of arguments command support, 0 means infinity.}{\rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid13909505\charrsid13909505 
\par }{\rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid13909505 
\par }{\rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid16077654 Handler r}{\rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid3671104 
eturn value of 0 indicates command is successfully processed. Positive return value indicates non-recoverable error, 3proxy enter}{\rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid16077654 s}{\rtlch\fcs1 \af0 \ltrch\fcs0 
\lang1033\langfe1049\langnp1033\insrsid3671104  }{\rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid16077654 termination state. Negative value indicate
s 3proxy to continue to process command list, it makes it possible to set few handlers for the same command.}{\rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid3671104 
\par 
\par }{\rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid12060716 A list of the command is pointed by pluginlinks->}{\rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid12060716\charrsid12060716 commandhandlers}{\rtlch\fcs1 
\af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid12060716 ; you must insert you command after first one (do not replace pluginlink->}{\rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid12060716\charrsid12060716 commandhandlers}{
\rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid12060716 ). It\rquote s guaranteed at least 1 dummy command is always present.}{\rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid12060716\charrsid12060716 
\par }{\rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid12060716 
\par Example:
\par 
\par }{\rtlch\fcs1 \af0 \ltrch\fcs0 \i\lang1033\langfe1049\langnp1033\insrsid12060716\charrsid12060716 int mycommandhandler(int argc, unsigned char **argv);
\par struct commands mycommand;
\par 
\par mycommand.command = \'93mycommand\'94;
\par mycommand.handler = mycommandhandler;
\par mycommand.intargs = 1;
\par }\pard \ltrpar\ql \li0\ri0\widctlpar\wrapdefault\aspalpha\aspnum\faauto\adjustright\rin0\lin0\itap0\pararsid12060716 {\rtlch\fcs1 \af0 \ltrch\fcs0 \i\lang1033\langfe1049\langnp1033\insrsid12060716\charrsid12060716 mycommand.intargs = 2;
\par mycommand.next = pluginslinks->commandhandlers->next;
\par pluginslink->commandhandlers->next = &mycommand;
\par }{\rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid12060716 
\par Adds processor for \'93mycommand\'94 command with zero on one arguments.}{\rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid12060716\charrsid13909505 
\par }\pard \ltrpar\ql \li0\ri0\widctlpar\wrapdefault\aspalpha\aspnum\faauto\adjustright\rin0\lin0\itap0\pararsid13909505 {\rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid13909505 
\par }{\rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid12060716 Adding configuration command is useful, if your plugin expects configuration data.
\par 
\par }\pard \ltrpar\ql \li0\ri0\widctlpar\wrapdefault\aspalpha\aspnum\faauto\adjustright\rin0\lin0\itap0\pararsid3671104 {\rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid3671104 
\par {\listtext\tab}}\pard \ltrpar\ql \li0\ri0\widctlpar\jclisttab\tx360\wrapdefault\aspalpha\aspnum\faauto\ls4\ilvl2\adjustright\rin0\lin0\itap0\pararsid3671104 {\rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid2296829 2.3.2 }{\rtlch\fcs1 
\af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid3671104 Adding authentication method with }{\rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid3671104\charrsid3671104 struct auth}{\rtlch\fcs1 \af0 \ltrch\fcs0 
\lang1033\langfe1049\langnp1033\insrsid3671104 
\par }\pard \ltrpar\ql \li0\ri0\widctlpar\wrapdefault\aspalpha\aspnum\faauto\adjustright\rin0\lin0\itap0\pararsid3671104 {\rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid3671104 
\par 3proxy supports authentication and authorization. Authentication process determines user account (for example by user
name and password), authorization checks, if user account has a right to access given resource and optionally establishes a connection, if required.
\par 
\par \lquote auth\rquote  command combines both authentication and authorization method. It\rquote s extandable with struct auth list:
\par 
\par }{\rtlch\fcs1 \af0 \ltrch\fcs0 \i\lang1033\langfe1049\langnp1033\insrsid3671104\charrsid16077654 struct auth \{
\par \tab struct auth *next;
\par \tab AUTHFUNC authenticate;
\par \tab AUTHFUNC authorize;
\par \tab char * desc;
\par \};
\par }{\rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid3671104 
\par }{\rtlch\fcs1 \af0 \ltrch\fcs0 \i\lang1033\langfe1049\langnp1033\insrsid3671104\charrsid16077654 char * desc}{\rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid3671104  \endash  name of authentication/authorization method}{\rtlch\fcs1 
\af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid3671104\charrsid3671104 
\par }\pard \ltrpar\ql \li0\ri0\widctlpar\wrapdefault\aspalpha\aspnum\faauto\adjustright\rin0\lin0\itap0\pararsid13909505 {\rtlch\fcs1 \af0 \ltrch\fcs0 \i\lang1033\langfe1049\langnp1033\insrsid3671104\charrsid16077654 authenticate}{\rtlch\fcs1 \af0 
\ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid3671104  \endash  name of authentication function}{\rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid12060716 
\par }{\rtlch\fcs1 \af0 \ltrch\fcs0 \i\lang1033\langfe1049\langnp1033\insrsid3671104\charrsid16077654 authorize}{\rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid3671104  \endash  name of authorization function
\par 
\par pluginlink->}{\rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid3671104\charrsid3671104 authfuncs}{\rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid3671104 
 points to list of authenticataction structures. Like above, new structure must be inserted after fiest one (or to the end of the list).
\par }{\rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid16077654 
\par First, authentication is called, if authentication indicates OK status (return value 0), authorization is called. Normally, \lquote }{\rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid16077654\charrsid16077654 checkACL}{\rtlch\fcs1 \af0 
\ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid16077654 \rquote  (pluginlink->checkACL) is called as authorization function to check user\rquote s request matches to standard allow/deny rules. 
If for some reason you need to avoid this check, you should call pluginlink->alwaysauth to do some dirty job, like establishing outgoing connection.
\par 
\par }{\rtlch\fcs1 \af0 \ltrch\fcs0 \i\lang1033\langfe1049\langnp1033\insrsid16077654\charrsid4980816 typedef int (*AUTHFUNC)(struct clientparam * param);
\par }{\rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid16077654 
\par is both authentication and authorization function. \lquote }{\rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid16077654\charrsid16077654 struct clientparam}{\rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid16077654 
\rquote  holds all information about client connection, including username (param->username) and password (param->password).
\par 
\par Return value of 0 indicates successful authentication/authorization, 1}{\rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid1600374  and 3}{\rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid16077654  \endash 
 authorization }{\rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid1600374 failed (access denied), use {\*\xmlopen\xmlns2{\factoidname metricconverter}{\xmlattr\xmlattrns0{\xmlattrname ProductID}{\xmlattrvalue 3 in}}}3 in{\*\xmlclose}
 case you want to indicate access is explicitly denied and {\*\xmlopen\xmlns2{\factoidname metricconverter}{\xmlattr\xmlattrns0{\xmlattrname ProductID}{\xmlattrvalue 3 in}}}3 in{\*\xmlclose} case there is no matching rule. 4,5,6,7,8 \endash 
 authentication failed (e.g. username/password do not match). 4 indicates username }{\rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid12806430 does not present in request and }{\rtlch\fcs1 \af0 \ltrch\fcs0 
\lang1033\langfe1049\langnp1033\insrsid1600374 must be requested, if possible. 5 indicates username }{\rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid12806430 found in request }{\rtlch\fcs1 \af0 \ltrch\fcs0 
\lang1033\langfe1049\langnp1033\insrsid1600374 can not be found in user\rquote s database/list, 6,7,8 \endash  username does not match password for different authentication types. 10 \endash  user exceeded some limits, e.g. tr
affic. You may use some different code to indicate internal problems.}{\rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid16077654 
\par }{\rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid1600374 
\par Example:
\par 
\par }{\rtlch\fcs1 \af0 \ltrch\fcs0 \i\lang1033\langfe1049\langnp1033\insrsid1600374\charrsid1600374 int myauthfunc(struct clientparam *param);
\par struct auth myauth;
\par 
\par myauth.desc = \'93myauth\'94;
\par myauth.authenticate = myauthfunc;
\par myauth.authorize = pluginlink->checkACL;
\par myauth->next = pluginlink->authfuncs->next;
\par pluginlink->authfuncs->next = &myauth;
\par }{\rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid1600374 
\par Installs \'93myauthfunc\'94 as authentication function.}{\rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid7235628  There is no need to add \lquote auth\rquote  command processor for new authentication type, it\rquote 
s processed by standard \lquote auth\rquote  command processor.}{\rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid12806430 
\par }{\rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid7235628 
\par {\listtext\tab}}\pard \ltrpar\ql \li0\ri0\widctlpar\jclisttab\tx360\wrapdefault\aspalpha\aspnum\faauto\ls4\ilvl2\adjustright\rin0\lin0\itap0\pararsid12806430 {\rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid2296829 2.3.3 }{\rtlch\fcs1 
\af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid12806430 Adding scheduled functions
\par }\pard \ltrpar\ql \li0\ri0\widctlpar\wrapdefault\aspalpha\aspnum\faauto\adjustright\rin0\lin0\itap0\pararsid12806430 {\rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid12806430 
\par }{\rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid4980816 Scheduled functions are described by this structure:
\par 
\par }{\rtlch\fcs1 \af0 \ltrch\fcs0 \i\lang1033\langfe1049\langnp1033\insrsid15928529\charrsid15236432 typedef enum \{NONE, MINUTELY, HOURLY, DAILY, WEEKLY, MONTHLY, ANNUALLY, NEVER\} ROTATION;
\par 
\par }\pard \ltrpar\ql \li0\ri0\widctlpar\wrapdefault\aspalpha\aspnum\faauto\adjustright\rin0\lin0\itap0\pararsid4980816 {\rtlch\fcs1 \af0 \ltrch\fcs0 \i\lang1033\langfe1049\langnp1033\insrsid4980816\charrsid15236432 struct schedule \{
\par \tab struct schedule *next;
\par \tab ROTATION type;
\par \tab void *data;
\par \tab int (*function)(void *);
\par \tab time_t start_time;
\par \};
\par }\pard \ltrpar\ql \li0\ri0\widctlpar\wrapdefault\aspalpha\aspnum\faauto\adjustright\rin0\lin0\itap0\pararsid12806430 {\rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid4980816 
\par }\pard \ltrpar\ql \li0\ri0\widctlpar\wrapdefault\aspalpha\aspnum\faauto\adjustright\rin0\lin0\itap0\pararsid15928529 {\rtlch\fcs1 \af0 \ltrch\fcs0 \i\lang1033\langfe1049\langnp1033\insrsid15928529\charrsid15236432 int (*function)(void *)}{\rtlch\fcs1 
\af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid15928529  \endash  scheduled function}{\rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid15928529\charrsid4980816 
\par }{\rtlch\fcs1 \af0 \ltrch\fcs0 \i\lang1033\langfe1049\langnp1033\insrsid15928529\charrsid15236432 void *data}{\rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid15928529  \endash 
 this pointer will be passed as an argument to scheduled functions}{\rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid15928529\charrsid4980816 
\par }{\rtlch\fcs1 \af0 \ltrch\fcs0 \i\lang1033\langfe1049\langnp1033\insrsid15928529\charrsid15236432 ROTATION type}{\rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid15928529  \endash 
 defines how often function is called (once in a minute, hour, etc). 
\par }{\rtlch\fcs1 \af0 \ltrch\fcs0 \i\lang1033\langfe1049\langnp1033\insrsid15928529\charrsid15236432 start_time}{\rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid15928529  \endash  time to begin using of scheduled function
\par 
\par Scheduled functions are called every \lquote type\rquote  interval after start_time and also on reloading configuration and going to termination state.
\par 
\par }{\rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid15236432 Schedule function r}{\rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid15928529 eturn value of 1 means function must be 
removed from the schedule. 3proxy doesn\rquote t free struct schedule.
\par 
\par Schedule list can be empty. Pointer to schedule is pointed by }{\rtlch\fcs1 \af0 \ltrch\fcs0 \i\lang1033\langfe1049\langnp1033\insrsid15928529\charrsid15236432 struct schedule ** schedule;}{\rtlch\fcs1 \af0 \ltrch\fcs0 
\lang1033\langfe1049\langnp1033\insrsid15928529  in pluginlink.
\par 
\par }{\rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid15236432 Example:}{\rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid15928529 
\par }{\rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid15236432 
\par }{\rtlch\fcs1 \af0 \ltrch\fcs0 \i\lang1033\langfe1049\langnp1033\insrsid15236432\charrsid15236432 int myschedfunc(void * data);
\par struct schedule myschedule;
\par 
\par myschedule.data = \'93somethinghere\'94;
\par myschedule.function = myschedfunc}{\rtlch\fcs1 \af0 \ltrch\fcs0 \i\lang1033\langfe1049\langnp1033\insrsid15236432 ;}{\rtlch\fcs1 \af0 \ltrch\fcs0 \i\lang1033\langfe1049\langnp1033\insrsid15236432\charrsid15236432 
\par }\pard \ltrpar\ql \li0\ri0\widctlpar\wrapdefault\aspalpha\aspnum\faauto\adjustright\rin0\lin0\itap0\pararsid12806430 {\rtlch\fcs1 \af0 \ltrch\fcs0 \i\lang1033\langfe1049\langnp1033\insrsid15236432\charrsid15236432 myschedule.type = MINUTELY;}{\rtlch\fcs1 
\af0 \ltrch\fcs0 \i\lang1033\langfe1049\langnp1033\insrsid15928529\charrsid15236432 
\par }{\rtlch\fcs1 \af0 \ltrch\fcs0 \i\lang1033\langfe1049\langnp1033\insrsid15236432\charrsid15236432 myschedule.starttime = 0;
\par myschedule.next = *pluginlink->schedule;
\par *pluginlink->schedule = myschedule;
\par }{\rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid15236432 
\par }{\rtlch\fcs1 \af0 \ltrch\fcs0 \ul\lang1033\langfe1049\langnp1033\insrsid15236432\charrsid15236432 NOTE:}{\rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid15236432  time_t is different for different compilers. 
Make sure to compile plugin and 3proxy with same compiler.
\par }{\rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid15928529 
\par {\listtext\tab}}\pard \ltrpar\ql \li0\ri0\widctlpar\jclisttab\tx360\wrapdefault\aspalpha\aspnum\faauto\ls4\ilvl2\adjustright\rin0\lin0\itap0\pararsid4980816 {\rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid4980816 2.3.4 Filters API

\par }\pard \ltrpar\ql \li0\ri0\widctlpar\wrapdefault\aspalpha\aspnum\faauto\adjustright\rin0\lin0\itap0\pararsid4980816 {\rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid4980816 
\par }{\rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid15236432 3proxy has filters API, you can use, to process client request and data flowing through proxy. It should be noted, that currently 3proxy doesn\rquote 
t provide filters with any useful data conversion, so, it\rquote s filter\rquote s task to find data in data flow. In case filter modifies some data, it\rquote s filter\rquote s task again to assure that everything\rquote s fine.}{\rtlch\fcs1 \af0 
\ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid1771507  If you know some filtering API like MILTER, you will find 3proxy filters very same.}{\rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid15236432 
\par 
\par }\pard \ltrpar\ql \li0\ri0\widctlpar\wrapdefault\aspalpha\aspnum\faauto\adjustright\rin0\lin0\itap0\pararsid15236432 {\rtlch\fcs1 \af0 \ltrch\fcs0 \i\lang1033\langfe1049\langnp1033\insrsid15236432\charrsid1771507 typedef enum \{
\par \tab PASS,
\par \tab CONTINUE,
\par \tab HANDLED,
\par \tab REJECT,
\par \tab REMOVE
\par \} FILTER_ACTION;
\par 
\par typedef\tab void*\tab  \tab FILTER_OPEN(void * idata, struct srvparam * param);
\par typedef\tab FILTER_ACTION \tab FILTER_CLIENT(void *fo, struct clientparam * param, void** fc);
\par typedef\tab FILTER_ACTION\tab FILTER_BUFFER(void *fc, struct clientparam * param, unsigned char ** buf_p, int * bufsize_p, int offset, int * length_p);
\par typedef\tab void\tab \tab FILTER_CLOSE(void *fo);
\par 
\par struct filter \{
\par \tab struct filter * next;
\par \tab char * instance;
\par \tab void * data;
\par \tab FILTER_OPEN *filter_open;
\par \tab FILTER_CLIENT *filter_client;
\par \tab FILTER_BUFFER *filter_request;
\par \tab FILTER_BUFFER *filter_header_cli;
\par \tab FILTER_BUFFER *filter_header_srv;
\par \tab FILTER_BUFFER *filter_data_cli;
\par \tab FILTER_BUFFER *filter_data_srv;
\par \tab FILTER_CLOSE *filter_clear;
\par \tab FILTER_CLOSE *filter_close;
\par \};
\par }{\rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid15236432 
\par }\pard \ltrpar\ql \li0\ri0\widctlpar\wrapdefault\aspalpha\aspnum\faauto\adjustright\rin0\lin0\itap0\pararsid1771507 {\rtlch\fcs1 \af0 \ltrch\fcs0 \i\lang1033\langfe1049\langnp1033\insrsid1771507\charrsid1771507 char * instance}{\rtlch\fcs1 \af0 
\ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid1771507  \endash  is some instance identifier. You can use it to find required filter in the list. 3proxy itself doesn\rquote t use this field.}{\rtlch\fcs1 \af0 \ltrch\fcs0 
\lang1033\langfe1049\langnp1033\insrsid1771507\charrsid15236432 
\par }{\rtlch\fcs1 \af0 \ltrch\fcs0 \i\lang1033\langfe1049\langnp1033\insrsid7013593 
\par }{\rtlch\fcs1 \af0 \ltrch\fcs0 \i\lang1033\langfe1049\langnp1033\insrsid1771507\charrsid1771507 void * data}{\rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid1771507  \endash  this parameter is passed to filter_open function.
\par The rest are filtering functions. Section 1 explains where and then each filter is called.}{\rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid7013593  data should not be NULL.}{\rtlch\fcs1 \af0 \ltrch\fcs0 
\lang1033\langfe1049\langnp1033\insrsid1771507 
\par 
\par }{\rtlch\fcs1 \af0 \ltrch\fcs0 \i\lang1033\langfe1049\langnp1033\insrsid1771507\charrsid1986875 filter_open}{\rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid1771507  must always be defined, if you want filter to be ever used. It
\rquote s called then new service is created and is given \'93data\'94 from struct filter and struct srvparam (parsed service configuration). If filter_op
en for some filter returns NULL, filter will not be used for this service. Non-NULL return value will be used as \'93fo\'94 parameter for every call to filter_client.
\par 
\par }{\rtlch\fcs1 \af0 \ltrch\fcs0 \i\lang1033\langfe1049\langnp1033\insrsid1771507\charrsid1986875 filter_client}{\rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid1771507  }{\rtlch\fcs1 \af0 \ltrch\fcs0 
\lang1033\langfe1049\langnp1033\insrsid1986875 is called upon client connect (before any data is sent/received). It\rquote s good plac
e to filter client by IP (and is not good place to filter it by hostname, because this operation takes a long time, 3proxy will not be able to accept new connection). fo is a data pointer received from filter_open, param \endash 
 newly created clientparam structu
re, fc is return parameter filter_open must initialize, it will be used as an argument to FILTER_BUFFER functions. PASS return value means this filter will be used for this client request. CONTINUE says to install filters. On different values client conne
ction is closed and no client thread is created.}{\rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid1771507\charrsid15236432 
\par }\pard \ltrpar\ql \li0\ri0\widctlpar\wrapdefault\aspalpha\aspnum\faauto\adjustright\rin0\lin0\itap0\pararsid15236432 {\rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid1771507 
\par }{\rtlch\fcs1 \af0 \ltrch\fcs0 \i\lang1033\langfe1049\langnp1033\insrsid1986875 f}{\rtlch\fcs1 \af0 \ltrch\fcs0 \i\lang1033\langfe1049\langnp1033\insrsid1986875\charrsid1771507 ilter_reques}{\rtlch\fcs1 \af0 \ltrch\fcs0 
\i\lang1033\langfe1049\langnp1033\insrsid1986875 t, }{\rtlch\fcs1 \af0 \ltrch\fcs0 \i\lang1033\langfe1049\langnp1033\insrsid1986875\charrsid1771507 filter_header_cli}{\rtlch\fcs1 \af0 \ltrch\fcs0 \i\lang1033\langfe1049\langnp1033\insrsid1986875 , }{
\rtlch\fcs1 \af0 \ltrch\fcs0 \i\lang1033\langfe1049\langnp1033\insrsid1986875\charrsid1771507 filter_header_srv}{\rtlch\fcs1 \af0 \ltrch\fcs0 \i\lang1033\langfe1049\langnp1033\insrsid1986875 , }{\rtlch\fcs1 \af0 \ltrch\fcs0 
\i\lang1033\langfe1049\langnp1033\insrsid1986875\charrsid1771507 filter_data_cli}{\rtlch\fcs1 \af0 \ltrch\fcs0 \i\lang1033\langfe1049\langnp1033\insrsid1986875 , }{\rtlch\fcs1 \af0 \ltrch\fcs0 
\i\lang1033\langfe1049\langnp1033\insrsid1986875\charrsid1771507 filter_data_srv}{\rtlch\fcs1 \af0 \ltrch\fcs0 \i\lang1033\langfe1049\langnp1033\insrsid1986875  }{\rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid1986875 
are used to process request and data r}{\rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid9981679 eceived from client and server.}{\rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid1986875\charrsid1986875 
\par }\pard \ltrpar\ql \li0\ri0\widctlpar\wrapdefault\aspalpha\aspnum\faauto\adjustright\rin0\lin0\itap0\pararsid4980816 {\rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid15236432 
\par }{\rtlch\fcs1 \af0 \ltrch\fcs0 \i\lang1033\langfe1049\langnp1033\insrsid9981679\charrsid1771507 char ** buf_p}{\rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid9981679  is a pointer to current buffer, }{\rtlch\fcs1 \af0 \ltrch\fcs0 
\i\lang1033\langfe1049\langnp1033\insrsid9981679\charrsid1771507 int * bufsize_p}{\rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid9981679  is a pointer to it\rquote s size. In case you change some data and it doesn\rquote 
t fit to current buffer, you may allocate new buffer (with pluginlink->myalloc), copy data from old buffer, free old buffer (with pluginlink->myfree) and set new values for *}{\rtlch\fcs1 \af0 \ltrch\fcs0 
\i\lang1033\langfe1049\langnp1033\insrsid9981679\charrsid1771507 buf_p}{\rtlch\fcs1 \af0 \ltrch\fcs0 \i\lang1033\langfe1049\langnp1033\insrsid9981679  }{\rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid9981679 and }{\rtlch\fcs1 \af0 
\ltrch\fcs0 \i\lang1033\langfe1049\langnp1033\insrsid9981679 *}{\rtlch\fcs1 \af0 \ltrch\fcs0 \i\lang1033\langfe1049\langnp1033\insrsid9981679\charrsid1771507 bufsize_p}{\rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid9981679 .
\par 
\par }{\rtlch\fcs1 \af0 \ltrch\fcs0 \i\lang1033\langfe1049\langnp1033\insrsid9981679 int }{\rtlch\fcs1 \af0 \ltrch\fcs0 \i\lang1033\langfe1049\langnp1033\insrsid9981679\charrsid1771507 * offset_p}{\rtlch\fcs1 \af0 \ltrch\fcs0 
\lang1033\langfe1049\langnp1033\insrsid9981679  offset of the new data in the buffer, }{\rtlch\fcs1 \af0 \ltrch\fcs0 \i\lang1033\langfe1049\langnp1033\insrsid9981679\charrsid1771507 int * length_p}{\rtlch\fcs1 \af0 \ltrch\fcs0 
\lang1033\langfe1049\langnp1033\insrsid9981679  length of all data in the buffer. You should filter only (*length_p - *offset_p) characters starting from }{\rtlch\fcs1 \af0 \ltrch\fcs0 \i\lang1033\langfe1049\langnp1033\insrsid9981679\charrsid9981679 
(*buf_p + *offset_p)}{\rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid9981679 .}{\rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid9981679\charrsid9981679 
\par }{\rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid9981679 
\par }{\rtlch\fcs1 \af0 \ltrch\fcs0 \i\lang1033\langfe1049\langnp1033\insrsid9981679\charrsid1771507 filter_clear}{\rtlch\fcs1 \af0 \ltrch\fcs0 \i\lang1033\langfe1049\langnp1033\insrsid9981679  }{\rtlch\fcs1 \af0 \ltrch\fcs0 
\lang1033\langfe1049\langnp1033\insrsid9981679 is called for each successfule filter_client}{\rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid15609114  and should be used to free allocated resources}{\rtlch\fcs1 \af0 \ltrch\fcs0 
\lang1033\langfe1049\langnp1033\insrsid9981679\charrsid9981679 
\par }{\rtlch\fcs1 \af0 \ltrch\fcs0 \i\lang1033\langfe1049\langnp1033\insrsid9981679\charrsid9981679 filter_close}{\rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid9981679  is called for each successful filter_open}{\rtlch\fcs1 \af0 
\ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid15609114  for the same reason}{\rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid9981679 
\par 
\par An example of filter API usage you can find in PCREPlugin (see plugins/}{\rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid9981679\charrsid9981679  PCREPlugin}{\rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid9981679 /
}{\rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid9981679\charrsid9981679  pcre_plugin.c}{\rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid9981679 ).
\par 
\par }{\rtlch\fcs1 \af0 \ltrch\fcs0 \ul\lang1033\langfe1049\langnp1033\insrsid15609114\charrsid15609114 Note}{\rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid15609114 : if }{\rtlch\fcs1 \af0 \ltrch\fcs0 
\lang1033\langfe1049\langnp1033\insrsid15609114\charrsid15609114 param->nooverwritefilter}{\rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid15609114 
 is set for FILTER_BUFFER functions, filter may change data in the buffer, but must not change data length. This flag may be set, if data size if already known and is sent to the client. }{\rtlch\fcs1 \af0 \ltrch\fcs0 
\lang1033\langfe1049\langnp1033\insrsid15609114\charrsid15609114 
\par }{\rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid15609114\charrsid9981679 
\par {\listtext\tab}}\pard \ltrpar\ql \li0\ri0\widctlpar\jclisttab\tx360\wrapdefault\aspalpha\aspnum\faauto\ls4\ilvl2\adjustright\rin0\lin0\itap0\pararsid4980816 {\rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid4980816 2.3.5 
Replacing log functions, traffic counting functions, bandwidth limitation functions
\par }\pard \ltrpar\ql \li0\ri0\widctlpar\wrapdefault\aspalpha\aspnum\faauto\adjustright\rin0\lin0\itap0\pararsid4980816 {\rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid4980816 
\par }\pard \ltrpar\ql \li0\ri0\widctlpar\wrapdefault\aspalpha\aspnum\faauto\adjustright\rin0\lin0\itap0\pararsid13909505 {\rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid8326885 
Log, traffic and bandwidth function can be directly replaced in any over place. All functions may be replaced in conf (pluginlink->conf->logfunc, pluginlink->conf->bandlim
func, pluginlink->conf->trafcountfunc).  In this case, these functions will be used for services started after the changes are made. }{\rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid2567226 l}{\rtlch\fcs1 \af0 \ltrch\fcs0 
\lang1033\langfe1049\langnp1033\insrsid8326885 ogfunc may also be changed for }{\rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid2567226 struct s}{\rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid8326885 
rvparam (e.g. within filter_open)}{\rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid2567226 , bandlimfunc and trafcountfunc may be changed in struct clientparam for every client individually (e.g. within filter_client).}{\rtlch\fcs1 
\af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid12806430 
\par }{\rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid2567226 
\par }{\rtlch\fcs1 \af0 \ltrch\fcs0 \i\lang1033\langfe1049\langnp1033\insrsid2567226\charrsid2567226 typedef void (*LOGFUNC)(struct clientparam * param, const unsigned char * test);
\par typedef void (*TRAFCOUNTFUNC)(struct clientparam * param); }{\rtlch\fcs1 \af0 \ltrch\fcs0 \i\lang1033\langfe1049\langnp1033\insrsid2567226 
\par }{\rtlch\fcs1 \af0 \ltrch\fcs0 \i\lang1033\langfe1049\langnp1033\insrsid2567226\charrsid2567226 typedef unsigned (*BANDLIMFUNC)(struct clientparam * param, unsigned nbytesin, unsigned nbytesout);
\par }{\rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid2567226 
\par }{\rtlch\fcs1 \af0 \ltrch\fcs0 \i\lang1033\langfe1049\langnp1033\insrsid2567226\charrsid2567226 struct clientparam * param}{\rtlch\fcs1 \af0 \ltrch\fcs0 \i\lang1033\langfe1049\langnp1033\insrsid2567226  \endash }{\rtlch\fcs1 \af0 \ltrch\fcs0 
\lang1033\langfe1049\langnp1033\insrsid2567226  information about client request}{\rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid2567226\charrsid2567226 
\par }{\rtlch\fcs1 \af0 \ltrch\fcs0 \i\lang1033\langfe1049\langnp1033\insrsid2567226\charrsid2567226 char * text}{\rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid2567226  - text string (e.g. request)
\par }{\rtlch\fcs1 \af0 \ltrch\fcs0 \i\lang1033\langfe1049\langnp1033\insrsid2567226\charrsid2567226 nbytesin, nbytesout}{\rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid2567226  \endash 
 number of bytes received from / send to server. bandlimfunc returns delay in milliseconds.
\par }{\rtlch\fcs1 \af0 \ltrch\fcs0 \lang1033\langfe1049\langnp1033\insrsid2567226\charrsid2567226 
\par }}