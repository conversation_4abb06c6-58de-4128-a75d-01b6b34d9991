<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
3APA3A 3proxy tiny proxy server HowTo
<br>В стадии разработки
<ul>
  <li><a href="#COMPILE">Компиляция</a>
  <ul>
    <li><a href="#MSVC">Как скомпилировать 3proxy Visual C++</a>
    <li><a href="#INTL">Как скомпилировать 3proxy Intel C Compiler под Windows</a>
    <li><a href="#GCCWIN">Как скомпилировать 3proxy GCC под Windows</a>
    <li><a href="#GCCUNIX">Как скомпилировать 3proxy GCC под Unix/Linux</a>
    <li><a href="#CCCUNIX">Как скомпилировать 3proxy Compaq C Compiler под Unix/Linux</a>
  </ul>
  <li><a href="#INSTALL">Установка и удаление 3proxy</a>
  <ul>
    <li><a href="#INSTNT">Как установить/удалить 3proxy под Windows 95/98/ME/NT/2000/XP как службу</a>
    <li><a href="#INST95">Как установить/удалить 3proxy под Windows 95/98/ME</a>
    <li><a href="#INSTUNIX">Как установить/удалить 3proxy под Unix/Linux</a>
  </ul>
  <li><a href="#SERVER">Конфигурация сервера</a>
  <ul>
    <li><a href="#NOTHING">Как заставить 3proxy запускаться</a></li>
    <li><a href="#LIMITS">Как заставить ограничения (по ширине канала, трафику, ACL и. т.п.) работать</a></li>
    <li><a href="#SERVICE">Как заставить 3proxy запускаться как службу</a></li>
    <li><a href="#INTEXT">Как разобраться с internal и external</a></li>
    <li><a href="#ODBC">Как починить ведение журналов в ODBC?</a></li>
    <li><a href="#IPV6">Как заставить IPv6 работать</a></li>
    <li><a href="#CRASH">Как сделать чтобы 3proxy не крешился</a></li>
    <li><a href="#SAMPLE">Как посмотреть пример файла конфигурации</a>
    <li><a href="#LOGGING">Как настроить ведение журнала</a>
    <li><a href="#LOGFORMAT">Как настроить формат журнала</a>
    <li><a href="#LOGANALIZERS">Как использовать лог-анализаторы с 3proxy</a>
    <li><a href="#LAUNCH">Как запустить конкретную службу (HTTP, SOCKS и т.д)</a>
    <li><a href="#BIND">Как повесить службу на определенный интерфейс или порт</a>
    <li><a href="#NAMES">Как разрешать имена на родительском прокси?</a></li>
    <li><a href="#ISFTP">Как настроить FTP прокси?</a></li>
    <li><a href="#TLSPR">Как настроить SNI proxy (tlspr)</a></li>
    <li><a href="#AUTH">Как ограничить доступ к службе</a>
    <li><a href="#USERS">Как создать список пользователей</a>
    <li><a href="#ACL">Как ограничить доступ пользователей к ресурсам</a>
    <li><a href="#REDIR">Как управлять перенаправлениями</a>
    <li><a href="#SOCKSREDIR">Как управлять локальными перенаправлениями</a>
    <li><a href="#ROUNDROBIN">Как организовать балансировку между несколькими каналами</a>
    <li><a href="#CHAIN">Как составлять цепочки прокси</a>
    <li><a href="#BANDLIM">Как ограничивать скорости приема</a>
    <li><a href="#TRAFLIM">Как ограничивать объем принимаемого трафика</a>
    <li><a href="#TRAF">Как пофиксить некорректный подсчет трафика</a></li>
    <li><a href="#NSCACHING">Как управлять разрешением имен и кэшированием DNS</a>
    <li><a href="#IPV6">Как использовать IPv6</a>
    <li><a href="#CONNBACK">Как использовать connect back</a>
  </ul>
  <li><a href="#CLIENT">Конфигурация и настройка клиентов</a>
  <ul>
    <li><a href="#IE">Как использовать 3proxy с Internet Explorer или другим браузером</a>
    <li><a href="#FTP">Как настраивать FTP клиент</a>
    <li><a href="#SMTP">Как использовать SMTP через 3proxy</a>
    <li><a href="#POP3">Как использовать службу POP3 Proxy</a>
    <li><a href="#CAP">Как использовать 3proxy с программой, не поддерживающей работу с прокси-сервером</a>
    <li><a href="#GAMES">Как использовать 3proxy с играми</a>
  </ul>
  <li><a href="#ADMIN">Администрирование и анализ информации</a>
  <ul>
    <li><a href="#NEWVERSION">Где взять свежую версию</a>
    <li><a href="#NTSERVICE">Как управлять службой 3proxy в Windows NT/2000/XP</a>
    <li><a href="#ERRORS">Коды ошибок в журнале</a>
  </ul>
  <li><a href="#QUEST">Как задать вопрос, которого нет в HowTo</a>
</ul>
<br>
<hr>
  <li><a name="COMPILE"><b>Компиляция</b></a>
  <p>
  <ul>
    <li><a name="MSVC"><i>Как скомпилировать 3proxy Visual C++</i></a>
    <p>
    Извлеките файлы из архива 3proxy.tgz (например, с помощью WinZip).
    Используйте команду nmake /f Makefile.msvc.
    </p>
    <li><a name="INTL"><i>Как скомпилировать 3proxy Intel C Compiler под Windows</i></a>
    <p>
    См. <a href="#MSVC">Как скомпилировать 3proxy Visual C++</a>. 
    Используйте Makefile.intl вместо Makefile.msvc
    </p>
    <li><a name="GCCWIN"><i>Как скомпилировать 3proxy GCC под Windows</i></a></li>
    <p>
    Извлеките файлы из архива 3proxy.tgz (например, с помощью WinZip или, при наличии
    Cygwin, tar -xzf 3proxy.tgz).
    Используйте команду make -f Makefile.win. Если по каким-то причинам вы хотите использовать
    библиотеку POSIX-эмуляции CygWin - используйте make -f Makefile.unix.
    При использовании CygWin, функции, специфичные для Windows (такие, как запуск в
    качестве службы) будут недоступны.
    </p>
    <li><a name="GCCUNIX"><i>Как скомпилировать 3proxy GCC под Unix/Linux</i></a></li>
    <p>
    Используйте make -f Makefile.unix. Должен использоваться GNU make, на
    некоторых системах необходимо использовать gmake вместо make. Под Linux
    необходимо использовать Makefile.Linux, под Solaris - Makefile.Solaris-* (в
    зависимости от используемого компилятора). Компиляция проверена в FreeBSD/i386,
    OpenBSD/i386, NetBSD/i386, RH Linux/Alpha, Debian/i386, Gentoo/i386, Gentoo/PPC,
    Solaris 10, но должно собираться в любых версиях *BSD/Linux/Solaris.
    В других системах может потребоваться модификация make-файла и/или исходных текстов.
    Для компиляции с поддержкой ODBC необходимо убрать -DNOODBC из флагов
    компиляции и добавить -lodbc (или другую ODBC-библиотеку) к флагам линковщика.
    </p>
	<li><a name="CCCUNIX"><i>Как скомпилировать 3proxy Compaq C Compiler под Unix/Linux</i></a></li>
	<p>
	Используйте make -f Makefile.ccc. Компиляция проверена в RH Linux 7.1/Alpha.
    В других системах может потребоваться модификация файла и/или исходных текстов.
    </p>
  </ul>
<hr>
<li><a name="INSTALL"><b>Установка и удаление 3proxy</b></a>
<p>
<ul>
  <li><a name="INSTNT"><i>Как установить/удалить 3proxy под Windows 95/98/ME/NT/2000/XP/2003 как службу</i></a>
  <p>
  Извлеките файлы из архива 3proxy.zip в любой каталог 
  (например, c:\Program Files\3proxy). Если необходимо, создайте каталог для
  хранения файлов журналов. Создайте файл конфигурации 3proxy.cfg в
  каталоге 3proxy (см. раздел <a href="#SERVER">Конфигурация сервера</a>).
  Если используется версия более ранняя, чем 0.6, добавьте строку
  <pre>
  service</pre>
  в файл 3proxy.cfg. Откройте командную строку (cmd.exe).
  Перейдите в каталог с 3proxy и дайте команду 3proxy.exe --install:
  <pre>
  D:\>C:
  C:\>cd C:\Program Files\3proxy
  C:\Program Files\3proxy>3proxy.exe --install</pre>
  Сервис должен быть установлен и запущен. Если сервис не запускается,
  проверьте содержимое файла журнала,
  попробуйте удалить строку service из 3proxy.cfg, запустить 3proxy.exe вручную
  и проанализировать сообщения об ошибках.
  </p><p>
  Для удаления 3proxy необходимо остановить сервис и дать
  команду 3proxy.exe --remove:
  <pre>
  D:\>C:
  C:\>cd C:\Program Files\3proxy
  C:\Program Files\3proxy>net stop 3proxy
  C:\Program Files\3proxy>3proxy.exe --remove</pre>
  после чего каталог 3proxy можно удалить.
  <p>
  Установка в качестве системной службы под Windows 9x поддерживается с версии 0.5
  </p>
  <li><a name="INST95"><i>Как установить/удалить 3proxy под Windows 95/98/ME</i></a>
  <p>
  Извлеките файлы из архива 3proxy.zip в любой каталог 
  (например, c:\Program Files\3proxy). Если необходимо, создайте каталог для
  хранения файлов журналов. Создайте файл конфигурации 3proxy.cfg в
  каталоге 3proxy (См. раздел <a href="#SERVER">Конфигурация сервера</a>).
  В файле конфигурации удалите строку
  <pre>
  service</pre>
  и добавьте строку
  <pre>
  daemon</pre>
  Создайте ярлык для 3proxy.exe и поместите его в автозагрузку либо с помощью
  редактора реестра regedit.exe добавьте в разделе
  <br>HKLM\Software\Microsoft\Windows\CurrentVersion\Run</br>
  строковый параметр 
  <br>3proxy = "c:\Program Files\3proxy.exe" "C:\Program Files\3proxy.cfg"<br>
  Использование кавычек при наличии в пути пробела обязательно.
  Перезагрузитесь.
  Если сервер не запускается,
  проверьте содержимое файла журнала,
  попробуйте удалить строку daemon из 3proxy.cfg, запустить 3proxy.exe вручную
  и проанализировать сообщения об ошибках.
  </p>
  <li><a name="INSTUNIX"><i>Как установить/удалить 3proxy под Unix/Linux</i></a>
  <p>
  Скомпилируйте 3proxy (см. раздел <a href="#COMPILE">Компиляция</a>). Скопируйте
  исполняемые файлы в подходящий каталог (например, /usr/local/3proxy/sbin для
  серверных приложений или /usr/local/3proxy/bin для клиентских утилит).
  Создайте файл /usr/local/etc/3proxy.cfg. 
  (См. раздел <a href="#SERVER">Конфигурация сервера</a>).
  Изменить расположение файла конфигурации можно, задав параметр при вызове
  3proxy или изменив путь в файле 3proxy.c до компиляции.
  Добавьте вызов 3proxy в скрипты начальной инициализации.
  </p>
</ul>
<hr>
<li><a name="SERVER"><b>Конфигурация сервера</b></a>
<p>
<ul>
  <li><a name="NOTHING">Как заставить прокси работать</a></li>
  <p>
	Для работы требуется корректный файл конфигурации. Если прокси не запускается, значит в конфигурации есть ошибка.
  </p>
  <li><a name="LIMITS">Как заставить работать ограничения (контроль доступа, ограничения ширины канала, счетчики и т.п.)</a></li>
  <p>
  <i>A:</i> Обычные ошибки - использование auth none (для работы любых
  функций, основанных на ACL, требуется auth iponly, nbname или strong),
  нарушение порядка ввода команд (команды выполняются последовательно,
  запуск сервиса proxy, socks, tcppm и т.д. должен осуществляться после
  того, как указана его конфигурация), неправильный порядок записей в ACL
  (записи просматриваются последовательно до первой, удовлетворяющей
  критериям). Если в ACL имеется хотя бы одна запись, то считается, что
  последняя запись в ACL - это неявная deny *.
  </p>
  <li><a name="SERVICE">Как починить запуск 3proxy службой</a></li>
  <p>
  Чаще всего 3proxy не запускается службой (но запускается вручную) по одной из следующих причин:
  <ul>
    <li>Использование относительных (неполных) путей файлов в файле конфигурации
    При использовании файлов журналов, файлов вставок ($filename) используйте
    полные пути, например, $"c:\3proxy\include files\networks.local". Тоже самое
    относится к файлам журналов и любым другим.
    Для отладки лучше запускать 3proxy с ведением журнала на стандартный вывод.
    Не забудьте в таком случае отключить daemon и service в файле конфигурации.
    Для чистоты эксперимента запускать 3proxy из коммандной строки в таком случае
    следует, находясь в другой папке.
    <li>Отсутствие у системной записи прав на доступ к исполняемому файлу, каким-либо файлам конфигурации, журнала и т.п.
    <li>Отсутствие файла конфигурации по стандартному расположению -
    3proxy.cfg в одном каталоге с исполняемым файлом. Если файл расположен по
    другому пути, необходимо использовать команду
    <pre>
    3proxy --install path_to_configuration_file</pre>
    <li>Отсутствие у пользователя прав на установку или запуск службы
    <li>Служба уже установлена или запущена
  </ul>
  </p>
  <li><a name="INTEXT">Как разобраться с internal и external</a></li></li>
  <p>
  Убедитесь, что выправильно понимаете что такое internal и external адреса.
  Оба адреса - это адреса, принадлежищие хосту, на котором установлен 3proxy.
  Эта опция конфигурации необходима в классической ситуации, когда 3proxy
  установлен на граничном компьютере с двумя (или более) подключениями:
  <pre>
       LAN connection +-------------+ Internet connection
  LAN <-------------->| 3proxy host |<-------------------> INTERNET
                     ^+-------------+^
                     |               |
               Internal IP      External IP</pre>
  Если 3proxy работает на хосте с одним интерфейсом, то его адрес будет и
  internal и external.
  <br>Интерфейс с адресом internal должен существовать и быть рабочим на момент
  запуска 3proxy, и не должен отключаться. Если internal интерфейс
  периодически отключается, то не следует его указывать, или можно указать адрес
  0.0.0.0. При этом прокси будет принимать запросы на всех интерфейсах, поэтому
  при наличии нескольких интерфейсов для ограничения доступа следует использовать
  фаервол или хотя бы ACL.
  </p>
  <p>
  Интерфейс с адресом external, если он указан, должен быть рабочим на момент
  получения запроса клиента. При отсутствии external или адресе 0.0.0.0 внешний
  адрес будет выбираться системой при установке соединения. При этом, может быть
  возможность доступа через прокси к ресурсам локальной сети, поэтому для
  предотвращения несанкционированного доступа следует использовать ACL. Кроме
  того, могут быть проблемы с приемом входящих соединений через SOCKSv5
  (SOCKSv5 используется в клиентах исключительно редко).
  В случае, если адрес динамический, можно либо не
  указывать external, либо использовать адрес 0.0.0.0, либо, если необходима
  поддержка входящих соединений в SOCKSv5, использовать скрипт,
  который будет получать текущий адрес и сохранять его в файл, который будет
  отслуживаться через команду monitor.
  </p>
  <li><a name="ODBC">Как починить ведение журналов в ODBC</a></li>
  <p>
  Убедитесь, что используется системный, а не
  пользовательский DSN. Убедитесь, что выполняется правильный SQL запрос. Наиболее
  распространенная проблема связана с отсутствием кавычек или неправильным
  форматом данных. Самый простой способ - сделать ведение журнала в файл или
  на стандартный вывод, просмотреть выдаваемые SQL запросы и попробовать
  дать такой запрос вручную.
  </p> 
  <li><a name="IPv6">Как починить IPv6</a></li>
  <p>
  Прокси не может обращаться напрямую к IPv6 сети если в запросе от клиента
  указан IPv4. В запросе от клиента должен быть IPv6 адрес или имя хоста, чаще
  всего это решается включением опции разрешения имен через прокси-сервер на стороне
  клиента.
  </p> 
  <li><a name="CRASH">Как починить падения 3proxy</a></li>
  <p>
  Возможно, недостаточен размер стека потока по-умолчанию, это может
  быть при использовани каких-либо сторонних плагинов (PAM, ODBC) или на
  некоторых платформах (некоторые версии FreeBSD на amd64). Можно решить
  проблему с помощью опции 'stacksize' или '-S', поддерживаемых в 0.8.4 и выше.
  </p>
  <li><a name="SAMPLE"><i>Как посмотреть пример файла конфигурации</i></a>
  <p>
  Пример файла конфигурации 3proxy.cfg.sample поставляется с любым дистрибутивом
  программы.
  </p>
  <li><a name="LOGGING"><i>Как настроить ведение журнала</i></a>
  <p>
  3proxy поддерживает ведение журнала на экран (stdout), в файл, через ODBC и через службу
  syslog (только для Unix/Linux/CygWin). Можно управлять либо общим файлом
  журнала, задаваемым в файле конфигурации 3proxy.cfg и единым для всех служб,
  либо индивидуальными файлами для отдельных служб (например, команда 
  socks -l/var/log/socks.log запускает SOCKS прокси
  и задает для него индивидуальный журнал). Для общего файла журнала
  поддерживается ротация (т.е. периодическое создание новых файлов журнала
  с предопределенными именами и удаление файлов старше определенного срока)
  и архивация файлов журнала.
  Тип журнала определяется параметром log в файле конфигурации либо ключом
  -l при вызове конкретной службы (например, socks прокси). log или -l без
  параметров соответствуют ведению журнала на экран (stdout).
  <pre>
  log filename</pre>
  и
  <pre>
  -lfilename</pre>
  соответствуют записи журнала в файл filename. Если при указании имени файла
  в log в нем содержится символ %, то имя рассматривается как форматный
  спецификатор (см. logformat). Например,
  log c:\3proxy\logs\%y%m%d.log D создаст файл типа c:\3proxy\logs\060725.log,
  для образования даты будет использовано местное время.
  <pre>
  log @ident</pre>
  и
  <pre>
  -l@ident</pre>
  соответствуют ведению журнала через syslog с идентификатором ident.
  <pre>
  log &connstring</pre>
  соответствует ведению журнала через ODBC, connstring задается в формате
  datasource,username,password (последние два параметра опциональны, если
  datasource не требует или уже содержит сведения для авторизации). При этом
  команда logformat должна задавать SQL запрос, который необходимо выполнить
  для добавления записи в журнал, см <a href="#LOGFORMAT">Как настроить формат журнала</a>.
  </p><p>
  Управление ротацией общего файла журнала происходит с помощью команд файла
  конфигурации log, rotate и archiver.
  <pre>
  log filename LOGTYPE</pre>
  задает тип ротации. LOGTYPE может принимать значения:
  <ul>
    <li>M, ежемесячная ротация
    <li>W, еженедельная ротация
    <li>D, ежедневная ротация
    <li>H, ежечасная ротация
    <li>C, ежеминутная ротация
  </ul>
  <pre>
  rotate NUMBER</pre>
  указывает на число файлов, участвующих в ротации (т.е. сколько последних
  журналов хранить).
  <pre>
  archiver EXT COMMAND PARAMETERS</pre>
  задает параметры архивации журнала. EXT указывает на расширение
  архива (например, zip, gz, Z, rar и т.д.) COMMAND указывает на
  программу и PARAMETERS - на параметры командной строки. Архиватор
  должен сам удалять исходный файл, также ему можно передать имя файла
  с помощью макроса %F и ожидаемое имя архива с помощью макроса %A.
  В качестве архиватора вполне можно задать пакетный файл, который,
  например, будет загружать данные из журнала в базу данных.
  Примеры команды archiver для популярных архиваторов можно найти в
  3proxy.cfg.sample
  <pre>
  logdump OFFSET_BYTES_FROM_SERVER OFFSET_BYTES_FROM_CLIENT</pre>
  Задает смещение в байтах на входящий и исходящий трафик, при достижении
  которого необходимо создавать запись в журнале даже в том случае, если
  соединение еще не завершено. Если logdump не указан или значения
  OFFSET_BYTES_FROM_SERVER OFFSET_BYTES_FROM_CLIENT нулевые - в журнале
  будет создана единственная запись по окончании обработки запроса клиента
  (при разрыве соединения). Пример:
  <pre>
  logdump 1048576 1048576</pre>
  создает в журнале запись на каждый мегабайт входящего или исходящего
  трафика.
  </p>
  <li><a name="LOGFORMAT"><i>Как настроить формат журнала</i></a>
  <p>
  Начиная с версии 0.3, формат журнала может быть настроен с помощью
  команды logformat со строкой формата. Первый символ строки должен
  быть L или G, что указывает на формат, в котором будет указываться
  время и даты, L - текущее локальное время, G - абсолютное время по
  Гринвичу. Строка формата может содержать следующие модификаторы:
  <ul>
    <li>	 %y - Год (последние две цифры)
    <li>	 %Y - Год (четырехзначный)
    <li>	 %m - Номер месяца (01-12)
    <li>	 %o - Трехбуквенная аббревиатура месяца
    <li>	 %d - День (01-31)
    <li>	 %H - Час (00-23)
    <li>	 %M - Минута (00-59)
    <li>	 %S - Секунда (00-59)
    <li>	 %t - Временная метка (число секунд с 00:00:00 1 января 1970 г. по Гринвичу)
    <li>	 %. - Миллисекунды
    <li>	 %z - Временная зона в почтовом формате (от Гринвича, '+' восток, '-' запад ЧЧММ), например, Московское зимнее время +0300.
    <li>	 %U - Имя пользователя ('-', если отсутствует).
    <li>	 %N - Название прокси сервиса (PROXY, SOCKS, POP3P, и т.д.)
    <li>	 %p - Порт прокси сервиса
    <li>	 %E - Код ошибки (см. <a href="#ERRORS">Коды ошибок в журнале</a>)
    <li>	 %C - IP клиента
    <li>	 %c - Порт клиента
    <li>	 %R - IP сервера (исходящего соединения по запросу клиента)
    <li>	 %r - Порт сервера (исходящего соединения по запросу клиента)
    <li>	 %Q – IP, запрошенный клиентом
    <li>	 %q – Порт, запрошенный клиентом
    <li>	 %I - Принято байт от сервера
    <li>	 %O - Отправлено байт на сервер
    <li>	 %n - Имя хоста из запроса
    <li>	 %h - Число звеньев до конечного сервера (при использовании перенаправлений или чейнинга
    см. <a href="#CHAIN">Как составлять цепочки прокси</a>)
    <li>	 %T – Текст, специфичный для прокси сервиса (например, запрошенный URL).
    Можно использовать %X-YT, где X и Y положительные числа, тогда
    отображаются только поля с X по Y текста. Поля считаются разделенными
    пробельным символом.
  </ul>
  Пример:
  <pre>
  logformat "L%t.%. %N.%p %E %U %C:%c %R:%r %O %I %h %T"</pre>
  будет генерировать в журнале записи типа
  <pre>
  1042454727.0296 SOCK4.1080 000 3APA3A 127.0.0.1:4739 195.122.226.28:4739 505 18735 1 GET http://3proxy.ru/ HTTP/1.1</pre>
  (без переноса строк)
  При использовании ODBC, logformat должен задавать формат SQL команды,
  которую необходимо дать для внесения записи в журнал, например:
  <pre>
  logformat "-\'+_GINSERT INTO proxystat VALUES (%t, '%c', '%U', %I)"</pre>
  префикс -\'+_ указывает, что символы \ и ' следует заменить на _ .

  </p>
  <li><a name="LOGANALIZERS"><i>Как использовать лог-анализаторы с 3proxy</i></a>
  <p>
  Просто сделайте формат ведения журнала совместимым с одним из форматов,
  поддерживаемых анализатором. Это позволяет использовать практически любые
  анализаторы. Примеры совместимых форматов:
  <br>
  Формат Squid access.log:
  <pre>
  &quot;- +_G%t.%. %D %C TCP_MISS/200 %I %1-1T %2-2T %U DIRECT/%R application/unknown&quot;</pre>
  Более совместимый формат, но без %D
  <pre>
  &quot;- +_G%t.%. 1 %C TCP_MISS/200 %I %1-1T %2-2T %U
  DIRECT/%R application/unknown&quot;</pre>
  Формат ISA 2000 proxy WEBEXTD.LOG (поля разделены табуляцией):
  <pre>
  &quot;-	+ L%C	%U	Unknown	Y	%Y-%m-%d	%H:%M:%S
  w3proxy	3PROXY	-	%n	%R	%r	%D
  %O	%I	http	TCP	%1-1T	%2-2T	-	-
  %E	-	-	-&quot;</pre>
  Формат ISA 2004 proxy WEB.w3c (поля разделены табуляцией):
  <pre>
  &quot;-	+ L%C	%U	Unknown	%Y-%m-%d	%H:%M:%S
  3PROXY	-	%n	%R	%r	%D	%O
  %I	http	%1-1T	%2-2T	-	%E	-
  -	Internal	External	0x0	Allowed&quot;</pre>
  Формат ISA 2000/2004 firewall FWSEXTD.log (поля разделены табуляцией):
  <pre>
  &quot;-	+ L%C	%U	unnknown:0:0.0	N	%Y-%m-%d
  %H:%M:%S	fwsrv	3PROXY	-	%n	%R	%r
  %D	%O	%I	%r	TCP	Connect	-	-
  -	%E	-	-	-	-	-&quot;</pre>
  Стандартный лог HTTPD (Apache и другие):
  <pre>
  &quot;-&quot;&quot;+_L%C - %U [%d/%o/%Y:%H:%M:%S %z] &quot;&quot;%T&quot;&quot; %E %I&quot;</pre>
  Более совместимый, но без кода ошибки:
  <pre>
  &quot;-&quot;&quot;+_L%C - %U [%d/%o/%Y:%H:%M:%S %z] &quot;&quot;%T&quot;&quot; 200 %I&quot;</pre>
  <li><a name="LAUNCH"><i>Как запустить конкретную службу (HTTP, SOCKS и т.д)</i></a>
  <p>
  3proxy поставляется в двух вариантах: как набор отдельных модулей (proxy,
  socks, pop3p, tcppm, udppm) и как универсальный прокси-сервер (3proxy).
  Универсальный прокси сервер - это законченная программа, которой не требуются
  отдельные модули.
  <br>Отдельный модуль управляется только из командной строки. Поэтому для
  отдельного модуля не поддерживаются многие функции, такие как управление
  доступом и ротация журнала. Запуск модуля осуществляется из командной строки.
  Например,
  <pre>
  $/sbin/socks -l/var/log/socks.log -i127.0.0.1</pre>
  запускает SOCKS на порту 127.0.0.1:1080 с ведением журнала /var/log/socks.log
  Справку по опциям командной строки можно получить запустив модуль с ключом -?.
  </p><p>
  Если используется 3proxy, то запускаемые службы указываются в файле 3proxy.cfg.
  Файл 3proxy.cfg просматривается 3proxy построчно, каждая строка рассматривается
  как управляющая команда. Синтаксис команд описан в 3proxy.cfg.sample. Например,
  <pre>
  log /var/log/3proxy.log D
  rotate 30
  internal 127.0.0.1
  external ***********
  proxy
  socks
  pop3p -l/var/log/pop3proxy</pre>
  запускает 3 службы - PROXY, SOCKS и POP3 Proxy. Каждая слушает на интерфейсе
  127.0.0.1 порт по-умолчанию (3128 для proxy, 1080 для socks и 110 для
  pop3p). Журналы всех служб кроме pop3p ведутся в файле /var/log/3proxy.log,
  который ежедневно меняется. Хранятся 30 последних файлов. Для pop3p ведется
  отдельный журнал /var/log/pop3proxy (см. <a href="#LOGGING">Как настроить ведение журнала</a>).
  </p>
  <li><a name="BIND"><i>Как повесить службу на определенный интерфейс или порт</i></a>
  <p>
  Опция -i позволяет указать внутренний интерфейс, -p - порт (пробелы в
  опциях не допускаются). Например, чтобы служба proxy висела на порту
  8080 интерфейсов *********** и 192.168.2.1 необходимо дать команды
  </p>
  <pre>
  proxy -p8080 -i***********
  proxy -p8080 -i192.168.2.1</pre>
  <li><a name="NAMES"><i>Как разрешать имена на родительском прокси?</i></a></li>
  <p>
  <i>A:</i> Для этого надо использовать тип родительского прокси http,
  connect+, socks4+ и socks5+. Однако, при это надо помнить, что самому 3proxy
  требуется разрешение имени для управления ACL. Поэтому, если с прокси-хоста
  не работают разрешения имени, необходимо в конфигурации дать команду
  <pre>
  fakeresolve</pre>
  которая разрешает любое имя в адрес *********.
  </p>
  <li><a name="ISFTP"><i>Как настроить FTP прокси?</i></a></li>
  <p>
  Есть поддержка как FTP через HTTP (то, что называется FTP прокси в браузерах) так и настоящего FTP прокси (то, что называется
  FTP proxy в командных оболочках и FTP клиентах). В браузерах в качестве FTP прокси следует прописывать порт службы proxy,
  т.е. FTP организован
  через http прокси, дополнительного прокси поднимать не надо. Для FTP-клиентов необходимо поднять ftppr. FTP прокси всегда работает
  с FTP сервером в пассивном режиме.
  </p>
    <li><a name="TLSPR"><i>Как настроить SNI proxy (tlspr)</i></a></li>
  <p>
 
    SNI proxy может быть использовать для транспарентного перенаправления любого TLS трафика (например HTTPS) на внешнем маршрутизаторе
    или локальными правилами. Так же можно использовать его для извлечения имени хоста из TLS хендшейка с целью логгирования или использования в ACL.
    Еще одна задача которую может решать модуль - требование наличия TLS или mTLS (mutual TLS).
    Если tlspr используется как отдельный сервис без исползования плагина Transparent, то необходимо задать порт назначения через опцию -T (по умолчанию 443),
    т.к. TLS хендшейк не содержит информации о порте назначения.
  </p><p>    
    -c контролирует уровень требования к TLS:
</p><pre>
0 (по умолчанию) - пропустить трафик без TLS
1 - требовать TLS, проверять наличие client HELLO
2 - требовать TLS, проверять наличие client и server HELLO
3 - требовать TLS, проверять наличие серверного сертификата (не совместим с TLS 1.3+)
4 - требовать взаимный (mutual) TLS, проверять что сервер запрашивает сертификат и клиент его отправляет (не совместим с TLS 1.3+)    
</pre>
<p>
примеры конфигурации:
1. Порт 1443 можно использовать для перенаправления в него HTTPS трафика по порту 443 (например с внешнего маршрутизатора)
<pre>
tlspr -p1443 -P443 -c1
</pre>
2. tlspr используется как родительский прокси в SOCKS чтобы обнаруживать реальный hostname назначения (даже если запрашивается подклюение по IP адресу)
<pre>
allow * * * 80
parent 1000 http 0.0.0.0 0
allow * * * * CONNECT
parent 1000 tls 0.0.0.0 0
deny * * some.not.allowed.host
allow *
socks
</pre>
  </p>

  <li><a name="AUTH"><i>Как ограничить доступ к службе</i></a>
  <p>
  Во-первых, для ограничения доступа необходимо указать внутренний интерфейс,
  на котором прокси-сервер будет принимать соединения. Внутренний интерфейс
  указывается с помощью команды internal в файле конфигурации или с помощью
  ключа -i конкретного модуля.
  (см. <a href="#LAUNCH">Как запустить конкретную службу (HTTP, SOCKS и т.д)</a>).
  Отсутствие указания внутреннего интерфейса может привести к тому, что ваш
  прокси будет открытым.
  <p> Указание внешнего интерфейса (т.е. IP, с которого сервер будет устанавливать
  внешние соединения) так же является полезным. Для этого служит команда external
  и ключ -e соответственно.
  Для универсального прокси возможна дополнительная авторизация доступа с помощью
  имени/пароля, NetBIOS имени пользователя и по спискам доступа (по IP клиента,
  IP и порту назначения, см. <a href="#ACL">Как ограничить доступ пользователей к ресурсам</a>).
  Тип авторизации устанавливается командой auth в файле конфигурации.
  <pre>
  auth none</pre>
  Отсутствие какой-либо авторизации. Списки доступа не проверяются.
  <pre>
  auth iponly</pre>
  Будет идти проверка по списку доступа с использованием IP клиента, IP и номера
  порта назначения.
  <pre>
  auth nbname</pre>
  Перед проверкой по списком доступа будет произведена попытка получить NetBIOS
  имя клиента. Для этого используется NetBIOS код службы messager (0x03). Если
  имя определить не удалось (служба messager для Windows NT/2000/XP или WinPopUP
  для 95/98/ME не запущена), то имя будет считаться пустым. Далее следует
  проверка по спискам доступа. Данный тип авторизации не зависит от платформы
  сервера (т.е. прокси сервер, запущенный под Unix, сможет определять NetBIOS
  имена). Его рекомендуется использовать в однородных сетях, где у всех клиентов
  установлена Windows NT/2000/XP и пользователи не имеют доступа к
  привелегированным учетным записям. Этот вид авторизации не является надежным.
  <pre>
  auth strong</pre>
  Проверяется имя и пароль, переданные пользователем при подключении к прокси.
  Данный вид авторизации работает только с proxy и socks. Необходимо задание
  списка пользователей (см <a href="#USERS">Как создать список пользователей</a>).
  Соединения от неизвестных пользователей не принимаются. После проверки имени
  пользвоателя и пароля происходит проверка списков доступа.
  </p><p>
  Для разных служб можно установить различные типы авторизации, например,
  <pre>
  auth none
  pop3p
  auth iponly
  proxy
  auth strong
  socks</pre>
  не накладывает ограничений на использование POP3 Proxy, производит проверку
  по спискам доступа для пользователей HTTP Proxy и требует авторизации с именем
  и паролем для SOCKS.
  </p><p>С версии 0.6 возможно использвоать двойную авторизацию, например,
  <pre>
  auth iponly strong
  allow * * ***********/16
  allow user1,user2
  proxy</pre>
  будет использовать авторизацию только в том случае, если не удалось пропустить
  пользователя с авторизаций iponly, т.е. для доступа к ресурсам ***********/16
  авторизация не требуется.
  </p><p>С версии 0.6 так же можно использвоать кэширование авторизации (имени
  пользователя) с целью повышения производительности. Использовать кэширование
  для strong практически не имеет смысла, она полезно для nbname и авторизации
  через внешние плагины, типа WindowsAuthentication. Кэширование настраивается
  командой authcache с двумя параметрами - типом кэширования и временем, на
  которое кэшируется пароль. Возможные типы: ip - после успешной авторизации в
  течение времени кэширования все запросы пришедшие с того же адреса считаются
  запросами от того же пользователя, name - после успешной авторизации от
  пользователя с тем же именем требуют указания имени, но реально аутентификации
  не производится, ip,name - запрос должен придти от того же IP и с тем же
  именем. user,password - имя и пароль пользователя сверяются с кэшированными.
  Возможны и другие сочетания. Для авторизации должен использоваться специальный
  метод авторизации - cache. Пример:
  <pre>
  authcache ip 60
  auth cache strong windows
  proxy -n</pre>
  Кэширование влияет на безопасность доступа. Ни в коем случае не следует
  использовать кэширование для доступа к критичным ресурсам, в частности к
  интерфейсу администрирования.
  </p>
  <p>authcache так же может использоваться для привязки сессий пользователя к ip с
  с помощью опции limit
  <pre>
  autchcache ip,user,pass,limit 120
  auth cache strong</pre>
  запретит пользователю использовать более одного адреса в течении времени кеширования.
  </p>
  <li><a name="USERS"><i>Как создать список пользователей</i></a>
  <p>
  Список пользователей задается с помощью команды users. 
  <pre>
  users USERDESC ...</pre>
  С помощью одной команды можно задать несколько пользователей, можно
  давать несколько команд users. USERDESC - описание пользователя. Описание
  пользователя состоит из трех полей разделенных : (двоеточием) - имени (login)
  типа пароля и пароля. Например:
  <pre>
  users admin:CL:bigsecret test:CL:password test1:CL:password1
  users "test2:CR:$1$lFDGlder$pLRb4cU2D7GAT58YQvY49."
  users test3:NT:BD7DFBF29A93F93C63CB84790DA00E63</pre>
  Обратите внимание на двойные кавычки - они необходимы для второго пользователя,
  т.к. в его пароле встречается знак $, который для файла 3proxy.cfg означает
  включение другого файла. Поддеживается следующие типы паролей:
  <ul>
    <li>тип не указан - использовать системную авторизацию для
    данного пользователя (пока не реализовано).
    <li>CL - пароль в открытом тексте
    <li>CR - пароль в формате crypt() (только MD5)
    <li>NT - пароль в формате NT в шестнадцатеричной кодировке
  </ul>
  NT и crypt пароли могут быть использованы для импорта учетных записей из
  Windows/Samba и Unix соответственно (для Windows можно использовать утилиты
  семейства pwdump).
  Учетные записи удобно хранить в отдельном файле (в таком случае можно хранить
  их построчно в формате, типичном для файлов паролей). Включить файл можно с
  помощью макроса $:
  <pre>
  users $/etc/.3proxypasswd</pre>
  или
  <pre>
  users $"c:\Program Files\3proxy\passwords"</pre>
  Шифрованные NT и crypt пароли можно создавать с помощью утилиты mycrypt.
  <br>Список пользователей един для всех служб. Разграничение доступа по службам
  необходимо производить с помощью списков доступа.
  </p>
  <li><a name="ACL"><i>Как ограничить доступ пользователей к ресурсам</i></a>
  <p>
  Для построения списков доступа используются команды allow, deny и
  flush. Команды имеют следующую структуру:
  <pre>
  allow &lt;userlist&gt; &lt;sourcelist&gt; &lt;targetlist&gt; &lt;targetportlist&gt; &lt;commandlist&gt; &lt;weekdays&gt; &lt;timeperiodslist&gt;
  deny &lt;userlist&gt; &lt;sourcelist&gt; &lt;targetlist&gt; &lt;targetportlist&gt; &lt;commandlist&gt; &lt;weekdays&gt; &lt;timeperiodslist&gt;
  flush</pre>
  Команда flush используется для сброса существующего списка доступа (это
  необходимо для того, чтобы можно было задать различные списки доступа для
  различных служб). allow служит для разрешения соединения, deny - для запрета
  соединения. Команда parent используется в качестве расширения команды
  allow для управления перенаправлениями соединений (о перенаправлении см. <a href="#REDIR">Как управлять перенаправлениями</a>).
  В момент установки исходящего соединения просматривается список доступа и
  находится первая запись, соответствующая запрошенному клиентом соединению.
  Если запись соттветствует allow - соединение разрешается, deny - запрещается.
  Если список пуст, то соединение разрешается. Если список не пуст, но подходящей
  записи нет, то соединение запрещается. При этом:
  <ul>
    <li>&lt;userlist&gt; - список логинов пользователей через запятую
    <li>&lt;sourcelist&gt; - список сетей клиентов через запятую. Сеть
    задается в формате xxx.yyy.zzz.mmm/l, где l - длина маски
    сети (количество ненулевых байт). Например, 192.168.1.0/24
    соответствует сети с маской 255.255.255.0.
    <li>&lt;targetlist&gt; - список сетей назначения через запятую
    <li>&lt;targetportlist&gt; - список портов назначения через запятую.
    можно задать диапазон портов через -, например, 80,1024-65535
    <li>&lt;commandlist&gt; - список команд, через запятую, для которых применяется правило:
    <br>		CONNECT	- установить исходящее TCP соединение (например, SOCKSv4/5, POP3 proxy, и т.д.)
    <br>		BIND - разрешить входящее TCP соединение (SOCKSv5)
    <br>		UDPASSOC - создать UDP-ассоциацию (SOCKSv5)
    <br>		ICMPASSOC - создать ICMP-ассоциацию (не реализовано)
    <br>		HTTP_GET - HTTP GET запрос (HTTP proxy)
    <br>		HTTP_PUT - HTTP PUT запрос (HTTP proxy)
    <br>		HTTP_POST - HTTP POST запрос (HTTP proxy)
    <br>		HTTP_HEAD - HTTP HEAD запрос (HTTP proxy)
    <br>		HTTP_CONNECT - HTTP CONNECT запрос (HTTP proxy)
    <br>		HTTP_OTHER - другой HTTP запрос (HTTP proxy)
    <br>		HTTP - соответствует любому HTTP запросу кроме HTTP_CONNECT (HTTP proxy)
    <br>		HTTPS - тоже, что HTTP_CONNECT (HTTP proxy)
    <br>		FTP_GET - FTP get запрос
    <br>		FTP_PUT - FTP put запрос
    <br>		FTP_LIST - FTP list запрос
    <br>		FTP - соответствует любому FTP запросу
    <br>		ADMIN - доступ к интерфейсу администрирования
    <li>&lt;weekdays&gt; задает список дней недели, 1 соответствует
    понедельнику, 0 или 7 - воскресенье. 1-5 означает с понедельника
    по пятницу (включительно). 1,3,5 задает нечетные дни недели.
    <li>&lt;timeperiodslist&gt; список интервалов дня в формате 
    ЧЧ:ММ:СС-ЧЧ:ММ:СС, например, 00:00:00-08:00:00,17:00:00-24:00:00
    задает нерабочее время.
  </ul>
  Примеры использования листов доступа можно найти в файле 3proxy.cfg.sample.
  </p>
  <li><a name="REDIR"><i>Как управлять перенаправлениями</i></a>
  <p>
  Перенаправления имеет смысл использовать, например, чтобы перенаправить
  обращения определенных клиентов или на определнные сервера на другой сервер
  (например, при попытке доступа на Web сервер с недозволенным материалом
  перенаправить на собственный Web сервер, или для того, чтобы в зависимости
  от IP клиента перенаправлять его соединения на разные сервера (особенно при
  отображении портов через tcppm). Кроме того, перенаправление может быть
  использовано, например, для перенаправления все исходящих HTTP запросов,
  посланных через SOCKS, на HTTP прокси. Поскольку формат запроса к Web серверу
  и Proxy различается, не любой Proxy сервер способен корректно обработать
  перенаправленный запрос (HTTP proxy в комплекте 3proxy нормально обрабатывает
  перенаправленные запросы, что делает возможным его использования в качестве
  "прозрачного" прокси. Кроме того, HTTP прокси обнаруживает перенаправления
  на родительский прокси и генерирует нормальные заголовки. Пример простейшего
  перенаправления:
  <pre>
  auth iponly
  allow *
  parent 1000 http *********** 3128
  proxy</pre>
  перенаправляет весь трафик службы proxy на родительский HTTP-прокси сервер
  *********** порт 3128.
  <br>
  Если в качестве номера порта указан порт 0, то указанный IP адрес используется
  в качестве внешнего адреса для установки соединения (аналог -eIP, но только
  для запросов попадающих под allow).
  <br>
  Специальным случаем перенаправлений являются локальные перенаправления,
  которые, как правило, используются совместно со службой socks. В локальных
  перенаправлениях IP адрес 0.0.0.0 порт 0. Например,
  <pre>
  auth iponly
  allow * * * 80
  parent 1000 http 0.0.0.0 0
  allow * * * 21
  parent 1000 ftp 0.0.0.0 0
  allow * * * 110
  parent 1000 pop3 0.0.0.0 0
  socks</pre>
  перенаправляет все содеинения, проходящие через SOCKS-сервер по 80 порту, в
  локальный HTTP прокси, 21-му - в FTP, и 110 - в POP3 прокси. При этом службы
  proxy, ftppr или pop3pr запускать не требуется. Это может быть полезно для
  того, чтобы видеть в логах записи о посещаемых пользвоателем ресурсах и
  загружаемых файлах даже в том случае, если он подключается через SOCKS.
  </p>
<li><a name="SOCKSREDIR">Как управлять локальными перенаправлениями</a>
<p>
<ul>
  <li><a name="REDIR"><i>Q: Для чего это надо?</i></a></li>
  <p>
  <i>A:</i> Чтобы иметь в логах URL запросов, если пользователь SOCKS пользуется
  Web, FTP или POP3.
  </p>
  <li><a name="REDIRLIMIT"><i>Q: Какие недостатки?</i></a></li>
  <p>
  <i>A:</i> Перенапраление невозможно для web-серверов или FTP, висящих на
  нестандартных портах, для SOCKSv4 не поддрживается авторизация с
  паролем (IE поддерживает только SOCKSv4), но при этом IE передает
  имя пользователя по SOCKSv4 (имя, с которым пользователь вошел в систему).
  Для SOCKSv5 не поддерживается NTLM авторизация, пароли передаются в открытом
  тексте.
  </p>
  <li><a name="REDIRADV"><i>Q: Какие преимущества?</i></a></li>
  <p>
  <i>A:</i> Достаточно в настройках IE только указать адрес SOCKS прокси. В
  больших сетях можно для этого использовать WPAD (автоматическое
  обнаружение прокси). В 3proxy достаточно запускать только одну службу
  (socks). Если используется только Internet Explorer, то можно
  автоматически получать имя пользователя в логах, не запрашивая
  логин/пароль.
  </p>
  <li><a name="REDIRHOW"><i>Q: Как настраивается?</i></a></li>
  <p>
  <i>A:</i> Указывается parent http proxy со специальным адресом 0.0.0.0 и портом
  0. Пример:
  <pre>
  allow * * * 80,8080-8088
  parent 1000 http 0.0.0.0 0
  allow * * * 80,8080-8088
  #перенаправить соединения по портам 80 и 8080-8088 в локальный
  #http прокси. Вторая команда allow необходима, т.к. контроль доступа
  #осуществляется 2 раза - на уровне socks и на уровне HTTP прокси
  allow * * * 21,2121
  parent 1000 ftp 0.0.0.0 0
  allow * * * 21,2121
  #перенаправить соединения по портам 21 и 2121 в локальный
  #ftp прокси
  allow *
  #пустить все соединения напрямую
  socks</pre>
  </p>
  <li><a name="REDIINTER"><i>Q: Как взаимодействует с другими правилами в ACL?</i></a></li>
  <p>
  <i>A:</i> После внутреннего перенаправления правила рассматриваются еще раз за
  исключением самого правила с перенаправлением (т.е. обработка правил не
  прекращается). Это позволяет сделать дальнейшие перенаправления на
  внешний прокси. По этой же причине локальное перенаправление не должно
  быть последним правилом (т.е. должно быть еще хотя бы правило allow,
  чтобы разрешить внешние соединения через HTTP прокси).
  Например,
  <pre>
  allow * * * 80,8080-8088
  parent 1000 http 0.0.0.0 0
  #перенаправить во внутренний прокси
  allow * * $c:\3proxy\local.nets 80,8080-8088
  #разрешить прямой web-доступ к сетям из local.nets
  allow * * * 80,8080-8088
  parent 1000 http proxy.3proxy.ru 3128
  #все остальные веб-запросы перенаправить на внешний прокси-сервер
  allow *
  #разрешить socks-запросы по другим портам</pre>
  </p>
</ul>
  <li><a name="ROUNDROBIN"><i>Как организовать балансировку между несоклькими каналами</i></a>
  <p>
  Сам по себе прокси не может управлять маршрутизацией пакетов сетевого уровня.
  Единственная возможность для управления внешними соединениями - это выбор
  внешнего интерфейса. Сделать выбор внешнего интерфейса случайным начиная
  с версии 0.6 можно с использованием локальных перенаправлений (с номером
  порта равным нулю):
  <pre>
  auth iponly
  allow *
  parent 500 http ********** 0
  parent 500 http ********02 0</pre>
  будет использовать внешний интерфейс ******** или ******** с вероятностью 0.5.
  Внешний интерфейс это то же, что задается командой external или опцией -e.
  Чтобы это работало как ожидается, необходимо, чтобы локальным интерфейсам
  ******** и ******** соответствовали разные маршруты по-умолчанию.
  <p>
  Если оба адреса принадлежат одной сети, например, ********** и ********02 и
  нужно случайным образом выбирать один из шлюзов ******** и ********, то нужно
  управлять роутингом примерно так (при условии что маршрут по-умолчанию не
  задан):
  <pre>
  route add -p ******** **********
  route add -p ******** ********02
  route add -p 0.0.0.0 mask 0.0.0.0 ***********
  route add -p 0.0.0.0 mask 0.0.0.0 ***********</pre>
  Если второго адреса на прокси сервере нет - его надо добавить. Под Linux/Unix
  лучше использовать source routing.
  </p>
  <li><a name="CHAIN"><i>Как составлять цепочки прокси</i></a>
  <p>
  Для составления цепочек прокси так же можно использовать команду parent, которая
  является расширением команды allow (т.е. команде parent должна предшествовать
  команда allow). С помощью этой команды можно строить цепочки из HTTPS
  (HTTP CONNECT), SOCKS4 и SOCKS5 прокси (т.е. последовательно подключаться
  через несколько прокси), при этом возможна авторизация на родительском прокси,
  звено цепочки может выбираться случайным образом из несольких значений
  с вероятностью согласно их весу. Вес (от 1 до 1000) задается для каждого
  прокси. Сумма весов по всем перенаправлениям должна быть кратна 1000.
  Прокси с весами до 1000 группируются, и при построении цепочки один из них
  выбирается случайно согласно весу. Длина цепочки определяется из суммарного
  веса. Например, если суммарный вес цепочки 3000, в цепочке будет 3 звена (хопа).
  Синтаксис команды:
  <pre>
  parent &lt;weight&gt; &lt;type&gt; &lt;ip&gt; &lt;port&gt; &lt;username&gt; &lt;password&gt;</pre>
  weight - вес прокси, type - тип прокси (tcp - перенаправление соединения,
  может быть только последним в цепочке, http - синоним tcp, connect - HTTP
  CONNECT/HTTPS прокси, socks4 - SOCKSv4 прокси, socks5 - SOCKSv5 прокси),
  ip - IP адрес прокси, port - порт прокси, username - имя для авторизации
  на прокси (опционально), password - пароль для авторизации на прокси
  (опционально).
  <br>Пример:
  <pre>
  allow *
  parent 500 socks5 *********** 1080
  parent 500 connect ************ 3128</pre>
  Создает цепочку из одного звена (суммарный вес 1000), в котором один из двух
  прокси выбирается случайно с равной вероятностью (веса равны). В цепочку
  перенаправляются все исходящие соединения (определяется командой allow).
  <pre>
  allow * * * 80
  parent 1000 socks5 ************ 1080
  parent 1000 connect ************ 3128
  parent 300 socks4 ************ 1080
  parent 700 socks5 ************ 1080</pre>
  Создает цепочку из трех звеньев (суммарный вес 3000). Первое звено -
  ************, второе - ************, а третье - либо ************ с
  вероятностью 0.3 либо ************ с вероятностью 0.7
  </p>
  <li><a name="BANDLIM"><i>Как ограничивать скорости приема</i></a>
  <p>
  3proxy позволяет устанавливать фильтры ширины потребляемого канала. Для этого
  служат команды bandlimin/bandlimout и nobandlimin/nobandlimout
  (in в команде означает, что правило применяется к входящему трафику,
  out - к исходящему).
  <pre>
  bandlimin &lt;bitrate&gt; &lt;userlist&gt; &lt;sourcelist&gt; &lt;targetlist&gt; &lt;targetportlist&gt; &lt;commandlist&gt;
  nobandlimin &lt;userlist&gt; &lt;sourcelist&gt; &lt;targetlist&gt; &lt;targetportlist&gt; &lt;commandlist&gt;</pre>
  <p>
  bitrate указывает ширину потока в битах в секунду (именно в битах). В остальном
  команды аналогичны командам allow/deny с тем отличием, что команды bandlim
  не имеют привязки к конкретному сервису, такому как HTTP прокси или SOCKS
  и действуют на все сервисы, трафик по всем соединениям, попавшим под действие
  правила суммируется независимо от того, через какой сервис это соединение
  установлено.
  <pre>
  bandlimin 57600 * ************6
  bandlimin 57600 * ************7
  bandlimin 57600 * ************8
  bandlimin 57600 * ************9</pre>
  устанавалиет канал 57600 для каждого из четырех клиентов,
  <pre>
  bandlimin 57600 * ************6/30</pre>
  устанавалиает суммарный канал 57600 на 4-х клиентов. Если необходимо, чтобы на
  какой-то сервис не было ограничения ширины канала, следует указать nobandlim
  для этого сервиса, например:
  <pre>
  nobandlimin * * * 110
  bandlimin 57600 * ************6/32</pre>
  разрешает клиентам неограниченный по скорости доступ по протоколу POP3.
  </p>
  <li><a name="TRAFLIM"><i>Как ограничивать объем принимаемого трафика</i></a>
  <p>
  <pre>
  counter &lt;filename&gt; &lt;type&gt; &lt;reportpath&gt; 
  countin &lt;number&gt; &lt;type&gt; &lt;amount&gt; &lt;userlist&gt; &lt;sourcelist&gt; &lt;targetlist&gt; &lt;targetportlist&gt; &lt;commandlist&gt;
  nocountin &lt;userlist&gt; &lt;sourcelist&gt; &lt;targetlist&gt; &lt;targetportlist&gt; &lt;commandlist&gt;</pre>
  countout &lt;number&gt; &lt;type&gt; &lt;amount&gt; &lt;userlist&gt; &lt;sourcelist&gt; &lt;targetlist&gt; &lt;targetportlist&gt; &lt;commandlist&gt;
  nocountout &lt;userlist&gt; &lt;sourcelist&gt; &lt;targetlist&gt; &lt;targetportlist&gt; &lt;commandlist&gt;</pre>
  <p>
  Команды позволяют установить лимит трафика на день, неделю или месяц.
  Сведения о трафике постоянно сохраняются в двоичном файле, указываемом
  командой counter, что делает подсчет трафика независимым от
  перезагрузки прокси. Можно управлять двоичным файлом, используя утилиту
  countersutil.
  Действие команд countin/nocountin аналогично действию bandlimin/nobandlimin,
  number - задает последовательный номер счетчика, номер должен быть
  уникальным положительным числом. Значение 0 указывает, что сведения
  для данного счетчика не надо сохранять в файле.
  <br>
  reportpath - путь, по которому будут создаваться текстовые отчеты по
  потребленному трафику.
  <br>
  type - тип ограничения или частота создания файлов отчета.
  D (На день), W (на неделю) или M (на месяц).
  <br>
  amount - объем трафика на указанный период в мегабайтах.
  </p>
  <li><a name="TRAF">Как пофиксить некорректный подсчет трафика</a>
  <p>
  Следует учитывать, что 3proxy считает трафик только на прикладном уровне и
  только проходящий через прокси-сервер. Провайдеры и другие средства учета
  трафика считают трафик на сетевом уровне, что уже дает расхождение порядка 10%
  за счет информации из заголовков пакетов. Кроме того, часть трафика, как
  минимум DNS-разрешения, различный флудовый трафик и т.д. идут мимо прокси.
  Уровень "шумового" трафика в Internet сейчас составляет порядка 50KB/день на
  каждый реальный IP адрес, но может сильно варьироваться в зависимости от сети,
  наличия открытых портов, реакции на ping-запросы и текущего уровня вирусной
  активности. По этим причинам, если 3proxy используется чтобы не "выжрать"
  трафик, выделенный провайдером, всегда следует делать некий запас порядка
  15%.
  </p>
  <p>
  Если на одной с 3proxy машине имеются какие-либо сервисы или
  работает пользователь, то их трафик не проходит через proxy-сервер и так же
  не будет учтен. Если где-то есть NAT, то клиенты, выходящие через NAT мимо
  прокси, так же останутся неучтенными. Если расхождение с провайдером превышает
  10% - нужно искать причину именно в этом.
  </p>
  <li><a name="NSCACHING"><i>Как управлять разрешением имен и кэшированием DNS</i></a>
  <p>
  Для разрешения имен и кэширования применяются команды nserver,
  nscache и nsrecord.
  <pre>
  nserver ***********
  nserver 192.168.1.3:5353/tcp</pre>
  указывает 3proxy какие машины следует использвоать в качестве серверов
  DNS. Сервер 192.168.1.3 будет использоваться по порту TCP/5353 (вместо дефолтного UDP/53) только при недостижимости
  ***********. Можно указать до 5 серверов. Если nserver не указан, будут
  использованы системные функции разрешения имен.
  <pre>
  nscache 65535
  nscache6 65535</pre>
  указывает размер кэша для разрешения имен (обычно достаточно большой) для IPv4 и IPv6 соответственно.
  Кэш исопльзуется только при явном указании nserver.
  <pre>
  nsrecord server.mycompany.example.com ***********
  nsrecord www.porno.com *********
  ...
  deny * * *********</pre>
  добавляет статическую запись в кэш. Статические записи так же влияют на разрешение через dnspr если не указана опция -s. Начиная с версии 0.8 для dnspr могут быть сконфигурированы родительские прокси.</p>
  <li><a name="IPV6"><i>Как использовать IPv6</i></a>
  <p>
  IPv6 поддерживается с версии 0.8. Обратите внимание, что в некоторых видах прокси (например SOCKSv4)
  IPv6 не поддерживает на уровне протокола. В SOCKSv5 есть поддержка IPv6 с помощью отдельного вида
  запроса, который должен быть реализован в клиентском приложении или соксификаторе.
  <br>
  Возможно проксирование из сетей IPv4 и IPv6 в сети IPv4,
  IPv6 и смешанные. Адреса IPv6 могут использоваться в командах
  internal, external, parent, ACL, опциях -i, -e и т.д. Команду external и опцию
  -e для каждого сервиса можно давать два раза - один раз с IPv4 и один раз с IPv6
  адресом. internal и -i может быть указан только один, для биндинга ко всем адресам IPv4 и IPv6
  можно использовать адрес [0:0:0:0:0:0:0:0] or [::]. 
  <br>
  Кроме того, для каждого сервиса могут быть даны опции -4, -46, -64, -6 которые
  задают приоритет разрешения имен в адреса IPv4 и IPv6 (только IPv4, приоритет IPv4, приоритет IPv6, только IPv6).
  </p>	
  <li><a name="CONNBACK"><i>Как использовать connect back</i></a>
  <p>
  Например, пользователю нужен доступ к прокси-серверу, который расположен
  на хосте *********** недоступном из внешней сети, но имеющем доступ во внешнюю
  сеть с внешним адрес *******. Так же у него есть машина с именем host.dyndns.example.org
  с внешним адресом *******. Пользователь запускает 2 экземпляра 3proxy, один на
  хосте *********** с конфигурацией
  <pre>
  users user:CL:password
  auth strong
  allow user
  proxy -rhost.dyndns.example.org:1234</pre>
  второй на хосте host.dyndns.example.org (*******) с конфигурацией
  <pre>
  auth iponly
  allow * * *******
  tcppm -R0.0.0.0:1234 3128 ******* 3128</pre>
  В настройках браузера указывается host.dyndns.example.org:3128.
  </p>	
</ul>
<hr>
<li><a name="CLIENT"><b>Конфигурация клиентов</b></a>
<p>
<ul>
  <li><a name="IE"><i>Как использовать 3proxy с Internet Explorer или другим браузером</i></a>
  <p>Мы будем рассматривать Interenet Explorer, т.к. у него больше особенностей
  настройки, с другими браузерами должно возникать меньше вопросов.
  <p>Есть два основных способа работы с 3proxy - использовать HTTP прокси (сервис
  proxy) или SOCKS прокси (сервис socks). Обычно используется HTTP прокси. Для
  SOCKS прокси можно использовать встроенную поддержку браузера или
  программу-соксификатор (см. 
  <a href="#CAP">Как использовать 3proxy с программой, не поддерживающей работу с прокси-сервером</a>),
  если встроенная поддержка SOCKS чем-то не устраивает или так удобнее. Не все
  соксификаторы поддерживают входящие соединения, поэтому чтобы не было проблем с
  протоколом FTP лучше использовать пассивный режим (в Internet Explorer 5.5SP2
  и выше "Использовать пассивный FTP-протокол").
  <p>Для конфигурации HTTP прокси необходимо указать внутренний адрес и порт,
  установленые для службы proxy в конфигурации 3proxy (либо как "Один
  прокси-сервер для всех протоколов" либо для HTTP, Secure, FTP). Поддержка
  Gopher в 3proxy в настоящий момент не реализована, но этот устаревший протокол
  в Internet практически не используется. Для нормальной работы FTP через HTTP
  прокси необходимо отключить представление для папок FTP (FTP folder view),
  он включен по-умолчанию, т.к. иначе FTP будет работать без использвания HTTP
  прокси. HTTP прокси использует пассивный режим для протокола FTP. В настройках
  3proxy рекомендуется разрешть метод HTTP_CONNECT только для порта назначения 443.
  Если метод HTTP_CONNECT не разрешен, то не будет работать протокол HTTPS, если
  он разрешен для всех портов, то можно обойти журналирование запросов для
  протокола HTTP).
  Для работы с HTTP прокси может использоваться авторизация по паролю (strong). 
  <p>Для использования SOCKS необходимо настроить только SOCKS прокси (иначе для
  протоколов, для которых указан прокси сервер, он будет использоваться как HTTP
  прокси). Internet Explorer (включая IE 6.0SP1)
  поддерживает только SOCKSv4. В качестве имени пользователя передается имя
  пользователя, использованное для входа в систему. Передача пароля в SOCKSv4 не
  поддерживается, поэтому авторизация по паролю (strong) не доступна. Можно
  использовать имя пользователя, переданное браузером в ACL для iponly-авторизации,
  но при этом необходимо иметь ввиду ненадежность такого способа. При работе через
  SOCKS Internet Explorer (и большая часть других браузеров) используют пассивный
  режим работы для FTP. При использовании SOCKS можно получить в журнале (логах)
  URL запрашиваемых страниц, для этого необходимо создать внутреннее
  перенаправление в HTTP прокси для порта 80 (и других портов, используемых для
  HTTP) в локальный HTTP прокси (proxy), порт 21 (и другие порты, используемые
  FTP) в FTP прокси. При перенаправлении соединений в FTP прокси, нужно иметь
  ввиду, что FTP создает вторичные соединения для передачи данных. Для этого,
  во-первых, необходимо разрешить доступ к внутреннему интерфейсу прокси сервера
  через SOCKS (что не очень безопасно), во-вторых, принять меры, чтобы в SOCKS
  такие соединения на внутренний интерфейс не тарифицировались (т.к. иначе они
  будут тарифицироваться дважды - SOCKS и FTP прокси).
  <p>Internet Explorer и другие продвинутые браузеры поддерживают автоматическую
  конфигурацию прокси-сервера в полностью автоматическом или полуавтоматическом
  режимах. Автоматическая конфигурация позволяет задать достаточно сложные
  правила, позволяющие клиентам использовать (или не использвоать) разные
  прокси-серверы для доступа к разным ресурсам. Эта возможность разбирается в
  статьях
  <br>Microsoft: Q296591 A Description of the Automatic Discovery Feature
  <br><a href="https://support.microsoft.com/default.aspx?scid=kb;EN-US;296591">http://support.microsoft.com/default.aspx?scid=kb;EN-US;296591</a>
  <br>Netscape: Navigator Proxy Auto-Config File Format
  <br><a href="https://wp.netscape.com/eng/mozilla/2.0/relnotes/demo/proxy-live.html">http://wp.netscape.com/eng/mozilla/2.0/relnotes/demo/proxy-live.html</a>
  <li><a name="FTP"><i>Как настраивать FTP клиент</i></a>
  <p>
  Настройка FTP клиента для работы через SOCKS прокси не отличается от настройки
  <a href="#IE">браузера</a>.
  <p>
  Для работы с FTP клиентом через FTP прокси (ftppr) есть несколько возможностей.
  Допустим, нам необходимо подключиться ко внешнему FTP серверу со следующими
  параметрами:
  <pre>
  Адрес:		ftp.security.nnov.ru
  Username:	ftpuser
  Password:	********</pre>
  <p>Работа с клиентом, не поддерживающим FTP прокси - в качестве адреса FTP
  сервера указывается адрес прокси (например, proxy.security.nnov.ru), в качестве
  имени пользователя <EMAIL>. Если для доступа к службе
  ftppr требуется авторизация по имени и паролю с именем pruser и паролем prpass,
  то в качестве имени пользователя необходимо указать
  pruser:prpass:<EMAIL>. Если FTP клиент требует указания
  полной URL для подключения к серверу, то она должна выглядеть как
  <p>
  pruser:prpass:<EMAIL>:********@proxy.security.nnov.ru
  <p>
  Не все клиенты обработают это корректно, но в большинстве случаев этого не
  требуется.
  <p>Работа с клиентом, поддерживающим FTP прокси - если 3proxy
  не требует авторизации по паролю, то настройки стандартны для клиента. Если
  требуется доступ по паролю, то в качестве имени пользователя указывается
  pruser:prpass:ftpuser.
  <li><a name="SMTP"><i>Как использовать SMTP через 3proxy</i></a>
  <p>
  В большинстве случаев достаточно установить отображение (tcppm) TCP порта 25 на
  аналогичный порт SMTP сервера провайдера. В конфигурации клиента указать
  внутренний адрес прокси в качестве адреса SMTP-сервера.
  <li><a name="POP3"><i>Как использовать POP3 proxy</i></a>
  <p>
  Предположим, у вас есть следующие настройки для получения почты:
  <pre>
  POP3 server:	pop3.security.nnov.ru
  Login:		user
  Password:	********</pre>
  В настройках почтовой программы следует указать:
  <pre>
  POP3 server:	(адрес прокси-сервера)
  Login:		<EMAIL>
  Password:	********</pre>
  <p>
  Имя пользователя может само по себе содержать знак '@'. Если в примере выше
  имя пользователя <EMAIL>, то имя пользвоателя для доступа к
  прокси-серверу должно быть <EMAIL>@pop3.security.nnov.ru. Если
  pop3pr требует авторизации по имени/паролю (например, pruser и prpass), то их
  необходимо указать в начале имени пользователя, отделив знаком ':', т.е для
  самого тяжелого случая pruser:prpass:<EMAIL>@pop3.security.nnov.ru
  </p>
  <li><a name="CAP"><i>Как использовать 3proxy с программой, не поддерживающей работу с прокси-сервером</i></a>
  <p>
  Можно использовать любую программу-редиректор. 3proxy поддерживает исходящие
  и обратные TCP и UDP соединения, но редиректоры могут иметь свои ограничения,
  кроме того, некоторые плохо написаные приложения не поддаются "соксификации".
  Если программе требуется обращаться к небольшому набору серверов 
  (например, игровых), то проблему можно решить с помощью портмаппинга.
  <li><a name="GAMES"><i>Как использовать 3proxy с играми</i></a>
  <p>
  Если по каким-то причинам соксификатор не работает или недоступен,
  то необходимо использовать отображения портов (обычно игры, 
  кроме mood-подобных, работают по протоколу UDP, надо использовать udppm).
  Нужно иметь ввиду, что для udppm требуется отдельный маппинг для каждого
  серверного порта и каждого клиента. Например, если есть один сервер с портами
  2115 и 2116 и три клиента, то нужно создать 6 разных маппингов, например
  <pre>
	udppm 12115 server 2115
	udppm 12115 server 2115
	udppm 22115 server 2115
	udppm 32115 server 2115
	udppm 12116 server 2116
	udppm 22116 server 2116
	udppm 32116 server 2116</pre>
  В игровом клиенте адрес и порт с маппингом следует указывать вместо адреса
  и порта сервера.
  </p>
</ul>
<hr>
<li><a name="ADMIN"><b>Администрирование и анализ информации</b></a>
<p>
<ul>
  <li><a name="NEWVERSION"><i>Где взять свежую версию</i></a>
  <p>
  Свежую версию всегда можно взять
  <a href="https://3proxy.ru/">здесь</a>. Обратите внимание,
  что в новой версии может измениться порядок лицензирования или команды
  конфигурации, поэтому прежде чем устанавливать новую версии программы
  обязательно ознакомьтесь с документацией.
  </p>
  <li><a name="NTSERVICE"><i>Как управлять службой 3proxy в Windows NT/2000/XP</i></a>
  <p>
  При установке 3proxy в качестве системной службы сервером поддерживаются
  команды запуска, остановки, временной приостановки и продолжения.
  При временной приостановке сервер перестает принимать новые запросы от 
  клиентов, но обработка ранее поступивших запросов продолжается. Сервер не
  подерживает динамическое изменение конфигурации, т.е. после изменения
  конфигурации 3proxy необходимо перезапустить.
  Управлять запуском, остановкой, приостановкой и продолжением можно либо
  через служебную программу "Службы" (Services) либо через команду net:
  <pre>
  net start 3proxy
  net stop 3proxy
  net pause 3proxy
  net continue 3proxy</pre>
  </p>
  <li><a name="ERRORS"><i>Коды ошибок в журнале</i></a>
  <p>
  <ul>
    <li>0 - операция завершена успешно (в случае установленного
    соединения - соединение закрыто корректно одной из
    сторон).
    <li>1-9 - ошибки авторизации
    <li>1 - доступ закрыт ACL (deny)
    <li>2 - перенаправление (не должно быть в журнале)
    <li>3 - нет записи ACL для данного соединения
    <li>4 - не определено имя пользователя для auth strong
    <li>5 - не найдено имя пользователя для auth strong
    <li>6 - неверный пароль (открытый текст)
    <li>7 - неверный пароль (crypt)
    <li>8 - неверный пароль (NT)
    <li>9 - недостаточно данных для перенаправления (не должно быть в журнале)
    <li>10 - превышен лимит трафика
    <li>11-19 - ошибки соединения
    <li>11 - невозможно создать сокет socket()
    <li>12 - невозможно выбрать интерфейс bind()
    <li>13 - сбой подключения connect()
    <li>14 - сбой getpeername()
    <li>20-29 - общие ошибки
    <li>21 - ошибка выделения памяти
    <li>30-39 - ошибки перенаправления CONNECT
    <li>31 - невозможно послать запрос к CONNECT прокси
    <li>32 - превышено ожидание или некорректный ответ CONNECT прокси
    <li>33 - CONNECT прокси не может установить соединение
    <li>34 - превышено ожидание или обрыв соединения при согласовании CONNECT соединения
    <li>40-49 - ошибки перенаправления SOCKS4
    <li>50-69 - ошибки перенаправления SOCKS5
    <li>70-79 ошибки установки родительского соединения, аналогичны 1x
    <li>90-99 - ошибки разрыва соединения
    <li>с версии 0.9
    <li>90 - неожиданная системная ошибка (не должно происходить)
    <li>91 - ошибка poll (не должно происходить)
    <li>92 - соединение прервано по таймауту на сетевую операцию (см. timeouts)
    <li>93 - соединение прервано по таймауту связанному с рейтлимитом или из-за превышения числа ошибок
    <li>94 - клиент или сервер закрыли соединение или произошла сетевая ошибка, остались неотправленные данные
    <li>95 - клиент "грязно" закрыл соединение или сетевая ошибка
    <li>96 - сервер "грязно" закрыл соединение или сетевая ошибка
    <li>97 - клиент и сервер "грязно" закрыли соединение или сетевая ошибка
    <li>98 - исчерпан лимит данных сервера (не должно быть в журнале)
    <li>99 - исчерпан лимит данных клиента (не должно быть в журнале)
    <li>до версии 0.9
    <li>90 - ошибка сокета или соединение неожиданно прервано
    <li>91 - общий сбой стека TCP/IP
    <li>92 - соединение прервано по таймауту
    <li>93 - ошибка получения данных от сервера
    <li>94 - ошибка получения данных от клиента
    <li>95 - таймаут из-за ограничения bandlimin/bandlimout
    <li>96 - ошибка отправки данных клиенту
    <li>97 - ошибка отправки данных серверу
    <li>98 - исчерпан лимит данных сервера (не должно быть в журнале)
    <li>99 - исчерпан лимит данных клиента (не должно быть в журнале)
    <li>100 - не найден IP адрес по запросу клиента
    <li>200-299 - ошибки UDP portmapper
    <li>300-399 - ошибки TCP portmapper
    <li>400-499 - ошибки SOCKS proxy
    <li>500-599 - ошибки HTTP proxy
    <li>600-699 - ошибки POP3 proxy
    <li>999 - функция не реализована
  </ul>
  </p>
</ul>
<hr>
<li><a name="QUEST"><b>Как задать вопрос, которого нет в HowTo</b></a>
<p>
Задайте его на <a href="https://github.com/z3APA3A/3proxy/issues">Github</a>.
Только не пытайтесь задавать какие-либо вопросы, если вы просто не поняли этот
HowTo.
</ul> 
