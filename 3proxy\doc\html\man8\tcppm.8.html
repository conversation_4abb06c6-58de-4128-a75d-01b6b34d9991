<!-- Creator     : groff version 1.22.4 -->
<html>
<head>

</head>
<body>

<h1 align="center">tcppm</h1>

<a href="#NAME">NAME</a><br>
<a href="#SYNOPSIS">SYNOPSIS</a><br>
<a href="#DESCRIPTION">DESCRIPTION</a><br>
<a href="#OPTIONS">OPTIONS</a><br>
<a href="#ARGUMENTS">ARGUMENTS</a><br>
<a href="#CLIENTS">CLIENTS</a><br>
<a href="#BUGS">BUGS</a><br>
<a href="#SEE ALSO">SEE ALSO</a><br>
<a href="#AUTHORS">AUTHORS</a><br>

<hr>


<h2>NAME
<a name="NAME"></a>
</h2>


<p style="margin-left:11%; margin-top: 1em"><b>tcppm</b> -
TCP port mapper</p>

<h2>SYNOPSIS
<a name="SYNOPSIS"></a>
</h2>


<p style="margin-left:11%; margin-top: 1em"><b>tcppm</b>
[<b>-d</b>] [<b>-l</b>[[<i>@</i>]<i>logfile</i>]]
[<b>-i</b><i>internal_ip</i>] [<b>-e</b><i>external_ip</i>]
<i>local_port remote_host remote_port</i></p>

<h2>DESCRIPTION
<a name="DESCRIPTION"></a>
</h2>



<p style="margin-left:11%; margin-top: 1em"><i><b>tcppm</b></i>
forwards connections from local to remote TCP port</p>

<h2>OPTIONS
<a name="OPTIONS"></a>
</h2>


<table width="100%" border="0" rules="none" frame="void"
       cellspacing="0" cellpadding="0">
<tr valign="top" align="left">
<td width="11%"></td>
<td width="3%">


<p style="margin-top: 1em"><b>-I</b></p></td>
<td width="8%"></td>
<td width="78%">


<p style="margin-top: 1em">Inetd mode. Standalone service
only.</p> </td></tr>
<tr valign="top" align="left">
<td width="11%"></td>
<td width="3%">


<p><b>-d</b></p></td>
<td width="8%"></td>
<td width="78%">


<p>Daemonise. Detach service from console and run in the
background.</p> </td></tr>
<tr valign="top" align="left">
<td width="11%"></td>
<td width="3%">


<p><b>-t</b></p></td>
<td width="8%"></td>
<td width="78%">


<p>Be silenT. Do not log start/stop/accept error
records.</p> </td></tr>
<tr valign="top" align="left">
<td width="11%"></td>
<td width="3%">


<p><b>-e</b></p></td>
<td width="8%"></td>
<td width="78%">


<p>External address. IP address of interface proxy should
initiate connections from. By default system will deside
which address to use in accordance with routing table.</p></td></tr>
<tr valign="top" align="left">
<td width="11%"></td>
<td width="3%">


<p><b>-i</b></p></td>
<td width="8%"></td>
<td width="78%">


<p>Internal address. IP address proxy accepts connections
to. By default connection to any interface is accepted.
It&acute;s usually unsafe.</p></td></tr>
<tr valign="top" align="left">
<td width="11%"></td>
<td width="3%">


<p><b>-l</b></p></td>
<td width="8%"></td>
<td width="78%">


<p>Log. By default logging is to stdout. If <i>logfile</i>
is specified logging is to file. Under Unix, if
&acute;<i>@</i>&acute; preceeds <i>logfile</i>, syslog is
used for logging.</p></td></tr>
<tr valign="top" align="left">
<td width="11%"></td>
<td width="3%">


<p><b>-S</b></p></td>
<td width="8%"></td>
<td width="78%">


<p>Increase or decrease stack size. You may want to try
something like -S8192 if you experience 3proxy crashes.</p></td></tr>
</table>

<h2>ARGUMENTS
<a name="ARGUMENTS"></a>
</h2>



<p style="margin-left:11%; margin-top: 1em"><i>local_port</i></p>

<p style="margin-left:22%;">- port tcppm accepts
connection</p>

<p style="margin-left:11%;"><i>remote_host</i></p>

<p style="margin-left:22%;">- IP address of the host
connection is forwarded to</p>

<p style="margin-left:11%;"><i>remote_port</i></p>

<p style="margin-left:22%;">- remote port connection is
forwarded to</p>

<h2>CLIENTS
<a name="CLIENTS"></a>
</h2>


<p style="margin-left:11%; margin-top: 1em">Any TCP based
application can be used as a client. Use <i>internal_ip</i>
and <i>local_port</i> as a destination in client
application. Connection is forwarded to
<i>remote_host</i>:<i>remote_port</i></p>

<h2>BUGS
<a name="BUGS"></a>
</h2>


<p style="margin-left:11%; margin-top: 1em">Report all bugs
to <b><EMAIL></b></p>

<h2>SEE ALSO
<a name="SEE ALSO"></a>
</h2>


<p style="margin-left:11%; margin-top: 1em">3proxy(8),
proxy(8), ftppr(8), socks(8), pop3p(8), udppm(8),
syslogd(8), <br>
https://3proxy.org/</p>

<h2>AUTHORS
<a name="AUTHORS"></a>
</h2>


<p style="margin-left:11%; margin-top: 1em">3proxy is
designed by Vladimir 3APA3A Dubrovin
(<i><EMAIL></i>)</p>
<hr>
</body>
</html>
