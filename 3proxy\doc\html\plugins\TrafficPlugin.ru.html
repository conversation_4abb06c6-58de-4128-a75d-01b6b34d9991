<h3>Плагин коррекции траффика 3proxy</h3>
Как известно, 3proxy считает траффик не сетевой, а прикладной.
Обычно прикладной траффик немного меньше (примерно на 10%) чем сетевой,
однако в некоторых случаях, например когда пользователи сети играют в
игры, сетевой траффик может превысить прикладной в 4-5 раз. Это довольно неприятно,
так как получается, что они за это не платят.
<p>
Происходит это потому, что в каждом посланом пакете есть заголовок+данные. Заголовок
весит порядка 50-60 байт а количество данных может меняться от 15-20 байт (что характерно для игр)
до 800-900 байт (у IE). Также количество данных в пакете зависит от загрузки сети,
удалённости сервера и прочих причин.
<p>
Данный плагин может исправить такую ситуацию. Он может умножать счётчик траффика
при окончании соединения на некоторый коэффициент либо добавлять к данным размеры заголовков пакетов,
которые прошли по сети.
<h4>Использование</h4>
<ol>
 <li>Извлечь TrafficPlugin.dll в каталог с 3proxy.exe
 <li>Стартовать плагин в 3proxy.cfg
<pre>
plugin TrafficPlugin.dll start
</pre>
 <li>Добавить правила:
<br>
ДЛЯ РЕЖИМА ДОМНОЖЕНИЯ ТРАФФИКА НА ЧИСЛО:
<pre>
trafcorrect m &lt;сервис&gt; &lt;исходящий порт&gt; &lt;коэффициент&gt;
</pre>
где: &lt;сервис&gt; может быть proxy, socks4, socks45, socks5, tcppm, udppm, pop3p
           если сервис указан неверно то считается, что это может быть любой сервис.
	   можно использовать *, тогда правило будет считаться для любого сервиса.
<br>     &lt;исходящий порт&gt; - порт, к которому подключается прокси сервер. * - любой
	 &lt;коэффициент&gt; - число на каоторое домнажается траффик. Обязательный параметр.
	 Должен быть от больше 0 и меньше 100
<br>
ДЛЯ РЕЖИМА С УЧЁТОМ РАЗМЕРА ЗАГОЛОВКОВ ПАКЕТОВ:
<pre>
trafcorrect p &lt;сервис&gt; &lt;tcp/udp&gt; &lt;исходящий порт&gt; [размер пустого пакета]
</pre>
	 &lt;tcp/udp&gt; - протокол, по которому осуществляется соединение.
	 [размер пакета] - средний размер пустого пакета. Можно определить захватив
данные при помощи такой утилиты как Ethereal. Параметр необязателен. Если отсутствует,
то размер пакета будет считаться равным 66 байт.
<br>Режимы можно смешивать.
Следует учитывать, что плугин создаёт список всех правил изменения траффика.
Когда происходит окончание соединения выполняется первое подходящее правило.
</ol>
Подсчет трафика в любом режиме не является точным, это некоторая аппроксимация
позволяющаяподсчитать трафик с точностью до нескольких процентов.

<h4>Пример:</h4>
<pre>
plugin "TrafficPlugin.dll" start
trafcorrect m socks5 6112 4.5
trafcorrect m socks5 * 1.1
</pre>
следующее неверно:
<pre>
plugin "TrafficPlugin.dll" start
trafcorrect m socks5 * 1.1
trafcorrect m socks5 6112 4.5
</pre>
Вторая строчка выполнена никогда не будет, т.к. правило 1 содержит *.


<h4>Загрузить:</h4>
<ul>
 <li>Плагин включен в дистрибутив 3proxy 0.6
</ul>
