<h3>Плагин TransparentPlugin 3proxy (только для Linux/BSD)</h3>

Плагин превращает 3proxy в транспарентный прокси для практически любых TCP-соединений
и позволяет прозрачно для клиентов использовать весь фунционал прокси - редиректоры,
родительские прокси, ACLи, ограничения трафика. TransparentPlugin получает IP:port
назначения от Linux  и использует эту информацию в качестве конечного адреса назначения.
<br>
Пример использования:

<pre>
plugin /path/to/TransparentPlugin.ld.so transparent_plugin
log /path/to/log
auth iponly
allow * * * 80
parent 1000 http 0.0.0.0 0
allow *
parent 1000 socks5 SOCKS5_IP SOCKS5_PORT USER PASSWORD
transparent
tcppm -iLOCAL_IP 12345 127.0.0.1 11111
notransparent
proxy
</pre>
Теперь любые TCP-соединения транспарентно перенаправленные в локальный порт 12345 
будут прологгированы и перенаправлены в родительский SOCKSv5 proxy, при этом для
HTTP-запросов по порту TCP/80 будут видны параметры HTTP-запроса.
Параметры '127.0.0.1 11111' в данном случае не оказывают влияния, т.к.
будут перезаписываться IP и портом назначения для каждого TCP-соединения соответственно.
<h4>Загрузить:</h4>
<ul>
 <li>Плагин включен в дистрибутив 3proxy 0.8
</ul>

&copy; Vladimir Dubrovin, License: BSD style
