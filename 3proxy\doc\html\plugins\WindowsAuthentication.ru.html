<h3>Плагин аутентификации Windows для 3proxy</h3>
Поддерживается только аутентификация открытым текстом в домене или на локальной машине Windows.
<h4>Использование</h4>
<ol>
 <li>Извлечь WindowsAuthentication.dll в каталог с 3proxy.exe
 <li>Создать 3ProxyAllowedGroup - системная группа Windows, которой разрешено использование прокси. Ее необходимо создать (можно
использовать группу с другим именем, см. ниже). Учетные записи пользователей, которым разрешен доступ к прокси
должны быть включены в группу непосредственно или посредством включения их групп. Группа может быть как локальной, так и в
ActiveDirectory.
 <li>В файле конфигурации загрузить dll с помощью команды plugin:
<br>plugin &quot;WindowsAuthentication.dll&quot; WindowsAuthentication &quot;3ProxyAllowedGroup&quot;
<br>Если DLL находится в другом каталоге, то вместо &quot;WindowsAuthentication.dll&quot;
необходимо указать полный путь к DLL. 3ProxyAllowedGroup - название системной группы,
которой разрешен доступ к прокси.
 <li>Плагин добавляет новый тип аутентификации - windows. Т.е. для использования
 Windows-аутентификации надо дать команду
<pre>
auth windows
</pre>
 <li>Не рекомендуется использовать данный плагин без кэширования
 аутентификации (authcache), т.к. это приведет к увеличению нагрузки на
 сервер/контроллер домена. Пример:
<pre>
 authcache user,pass 900
 auth cache windows
</pre>

 <li>В настоящее время не поддерживается NTLM-аутентификация для плагинов,
поэтому необходимо запускать proxy с ключиком -n.
</ol>
<h4>Загрузить:</h4>
<ul>
 <li>Плагин включен в дистрибутив 3proxy 0.6
</ul>

