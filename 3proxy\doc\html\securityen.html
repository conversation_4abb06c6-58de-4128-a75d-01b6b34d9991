<h3>3proxy security considirations</h3>
</ul>
<ul>
<li>Never install 3proxy suid. If you need it to run suid write some
wrapper with fixed configuration file.
<li>Make configuration file only available to account 3proxy starts with.
<li>Under Windows if 3proxy is used as service create new
unprivileged local account without "logon locally" right. Assign this account
to 3proxy service.
<li>Under unix use chroot to jail 3proxy (make sure files included in
configuration file after 'chroot' command, if any, are available from jail)
<li>Under Unix, either start 3proxy with unprivileged account or, if you need
some privileged ports to be used by 3proxy, use setgid/setuid commands inside
3proxy.cfg immediately after last occurance of service binded to
privileged port in configuration file (setgid must preceed setuid).
<li>Allways use full paths in configuration file
<li>Try to avoid 'strong' authentication, because only cleartext
authentication method is currently available.
<li>Always specify internal and external interfaces.
<li>Always limit connections to internal network and localhost (to 127.0.0.1 and
all interfaces) with ACLs. Be carefull, because BIND command in SOCKS requies
BIND method with external interface IP address to be allowed.
<li> Before 3proxy 0.8 always use nserver and nscache under Unix, overwise DoS attack is possible
with unreachable DNS server (because gethostbyname will block over threads).
<li>Keep logs in secure location, because some confidential information from
user's request can be logged.
<li>Use -xyz+A character filtering sequences for 'logformat', especially with
ODBC logging to prevent SQL and log record injections. 
<li>Immediately report all service crashes to developers
<li>Participate in code audit :)
</ol>
 
</ul>
<p>

