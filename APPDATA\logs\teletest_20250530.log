2025-05-30 02:41:48.392 | INFO     | utils.logger:init_logger:98 - 日志系统初始化完成，默认级别: DEBUG，日志目录: h:\PyProject\TeleTest\APPDATA\logs
2025-05-30 02:41:50.307 | INFO     | data.database:init_database:31 - 初始化数据库连接: h:\PyProject\TeleTest\APPDATA\database\telegram_manager.db
2025-05-30 02:41:50.381 | DEBUG    | data.database:init_database:58 - 创建数据库表
2025-05-30 02:41:50.392 | INFO     | data.database:init_database:61 - 数据库初始化完成
2025-05-30 02:41:50.992 | DEBUG    | app.controllers.auth_controller:initialize_software:43 - 控制器: 初始化软件 app_id=1000, version=1.0
2025-05-30 02:41:50.993 | DEBUG    | utils.client_http:get:41 - 发送GET请求: https://server.xile188.com/api/user/1000/windowns10/1.0/ini
2025-05-30 02:41:51.400 | DEBUG    | core.auth.api_service:init_software:66 - 初始化数据解密成功: {'new_content': '1.多账户管理\n2.ip池管理\n3.消息监听\n4.消息群发\n5.拉人入群', 'new_url': None, 'notice': {'id': '2', 'content': '公告内容测试3', 'time': '**********'}}
2025-05-30 02:41:51.407 | INFO     | app.controllers.auth_controller:login:61 - 控制器: 用户登录 account=zhangsan03
2025-05-30 02:41:54.282 | INFO     | core.auth.api_service:login:127 - 用户登录: account=zhangsan03
2025-05-30 02:41:54.283 | DEBUG    | utils.client_http:post:68 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/logon
2025-05-30 02:41:54.456 | INFO     | core.auth.api_service:get_user_info:347 - 获取用户信息
2025-05-30 02:41:54.457 | DEBUG    | utils.client_http:post:68 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/info
2025-05-30 02:41:54.660 | INFO     | core.auth.api_service:get_user_info:375 - 获取用户信息成功
2025-05-30 02:41:54.666 | INFO     | __main__:on_auth_completed:93 - 用户登录成功，正在启动主窗口...
2025-05-30 02:41:54.682 | INFO     | core.telegram.client_worker:__init__:63 - Telegram客户端工作线程初始化
2025-05-30 02:41:54.688 | INFO     | core.telegram.client_worker:run:67 - Telegram客户端工作线程开始运行
2025-05-30 02:41:54.691 | INFO     | core.telegram.client_manager:__init__:66 - Telegram客户端管理器初始化
2025-05-30 02:41:54.692 | INFO     | core.telegram.user_manager:__init__:31 - Telegram用户管理器初始化
2025-05-30 02:41:54.692 | INFO     | core.telegram.group_manager:__init__:36 - Telegram群组管理器初始化
2025-05-30 02:41:54.693 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-05-30 02:41:54.693 | INFO     | core.telegram.monitor_manager:__init__:26 - Telegram监控管理器初始化
2025-05-30 02:41:54.694 | INFO     | core.telegram.telegram_service:__init__:39 - Telegram服务初始化完成
2025-05-30 02:41:54.695 | INFO     | ui.main_window:_initialize_core_components:79 - MainWindow: 初始化核心组件...
2025-05-30 02:41:54.697 | INFO     | app.services.account_service:__init__:42 - 账户服务初始化
2025-05-30 02:41:54.697 | INFO     | core.proxy.proxy_task_manager:run:284 - 代理任务管理器线程启动
2025-05-30 02:41:54.697 | INFO     | app.controllers.account_controller:__init__:81 - 账户控制器初始化
2025-05-30 02:41:54.699 | INFO     | app.services.monitor_service:__init__:39 - 初始化监控任务服务
2025-05-30 02:41:54.699 | INFO     | core.monitor.filter:__init__:26 - 初始化消息过滤器
2025-05-30 02:41:54.699 | INFO     | core.monitor.extractor:__init__:24 - 初始化关键词提取器
2025-05-30 02:41:54.700 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-05-30 02:41:54.700 | INFO     | core.monitor.notifier:__init__:90 - 初始化通知管理器
2025-05-30 02:41:54.701 | INFO     | app.controllers.telegram_monitor_controller:__init__:64 - 初始化 Telegram 监控控制器
2025-05-30 02:41:54.702 | DEBUG    | app.controllers.telegram_monitor_controller:__init__:82 - 控制器初始化完成
2025-05-30 02:41:54.872 | INFO     | ui.views.monitor_view:__init__:48 - 初始化监控视图
2025-05-30 02:41:54.873 | DEBUG    | ui.views.monitor_view:_connect_signals:80 - 连接控制器信号
2025-05-30 02:41:55.031 | INFO     | ui.views.proxy_view:__init__:96 - 初始化视图，加载第一页代理数据
2025-05-30 02:41:55.213 | INFO     | __main__:on_auth_completed:97 - 主窗口已启动
2025-05-30 02:41:55.258 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:88 - 尝试连接监听日志信号...
2025-05-30 02:41:55.259 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:90 - 监听日志信号连接成功
2025-05-30 02:41:55.260 | INFO     | core.auth.api_service:verify_vip:425 - 会员验证接口调用
2025-05-30 02:41:55.261 | DEBUG    | utils.client_http:post:68 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/vip
2025-05-30 02:41:55.262 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-05-30 02:41:55.265 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-05-30 02:41:55.270 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:145 - 加载监控任务列表 (搜索关键词: '')
2025-05-30 02:41:55.271 | DEBUG    | ui.views.monitor_view:_on_loading_started:620 - 加载中: 正在加载任务列表...
2025-05-30 02:41:55.271 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-05-30 02:41:55.279 | INFO     | app.services.proxy_service:refresh_proxies:712 - 刷新代理列表: offset=0, limit=20
2025-05-30 02:41:55.279 | INFO     | core.proxy.proxy_task_manager:submit_task:172 - 提交耗时任务到子线程: refresh_proxies_0_20
2025-05-30 02:41:55.280 | DEBUG    | app.services.proxy_service:get_proxies:500 - 获取代理列表: offset=0, limit=20
2025-05-30 02:41:55.280 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-05-30 02:41:55.281 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-05-30 02:41:55.286 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-05-30 02:41:55.311 | INFO     | app.controllers.invite_controller:get_invite_tasks:38 - 获取邀请任务列表, status=None
2025-05-30 02:41:55.313 | INFO     | app.services.invite_service:get_invite_tasks:121 - 获取邀请任务列表, status=None
2025-05-30 02:41:55.314 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-05-30 02:41:55.314 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-05-30 02:41:55.315 | INFO     | app.services.invite_service:task_initialize:39 - 初始化邀请服务，重置运行中的任务为暂停状态
2025-05-30 02:41:55.316 | INFO     | core.proxy.proxy_task_manager:task_wrapper:199 - 任务 refresh_proxies_0_20 完成
2025-05-30 02:41:55.316 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-05-30 02:41:55.518 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-05-30 02:41:55.522 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-05-30 02:41:55.525 | INFO     | app.services.invite_service:task_initialize:48 - 成功重置 0 个任务状态
2025-05-30 02:41:55.525 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-05-30 02:41:55.526 | DEBUG    | ui.views.monitor_view:_refresh_task_list:175 - 刷新任务列表，共2个任务
2025-05-30 02:41:55.531 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-05-30 02:41:55.536 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-05-30 02:41:55.537 | INFO     | app.services.account_service:get_all_groups:96 - 获取所有账户分组成功, 共 1 个
2025-05-30 02:41:55.538 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-05-30 02:41:55.543 | DEBUG    | ui.views.account_view:_on_groups_loaded:442 - 分组加载完成: 1个分组
2025-05-30 02:41:55.551 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-05-30 02:41:55.553 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-05-30 02:41:55.559 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-05-30 02:41:55.616 | INFO     | app.services.account_service:get_all_accounts:289 - 获取所有账户成功, 共 2 个
2025-05-30 02:41:55.617 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-05-30 02:41:55.621 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-05-30 02:41:55.622 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-05-30 02:41:55.623 | DEBUG    | ui.views.account_view:_on_accounts_loaded:575 - 账户加载完成: 2个账户
2025-05-30 02:41:55.643 | DEBUG    | ui.views.account_view:_update_group_counts:184 - 分组计数已更新: {1: 1}
2025-05-30 02:41:55.647 | INFO     | ui.views.account_view:_auto_login_accounts:630 - 开始调用控制器进行 2 个账户的自动登录。
2025-05-30 02:41:55.647 | INFO     | app.services.account_service:batch_auto_login:1153 - 服务层：开始批量自动登录 2 个账户, 最大并发: 5
2025-05-30 02:41:55.647 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-05-30 02:41:55.649 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-05-30 02:41:55.650 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-05-30 02:41:55.664 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-05-30 02:41:55.670 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-05-30 02:41:55.677 | INFO     | app.controllers.telegram_monitor_controller:_load_total_users_count_internal:191 - 成功加载总体统计数据: 总用户数 958, 今日采集 0, 日均采集 106, 运行天数 9
2025-05-30 02:41:55.678 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:156 - 成功加载 2 个任务
2025-05-30 02:41:55.678 | DEBUG    | ui.views.monitor_view:_on_loading_finished:625 - 加载完成
2025-05-30 02:41:55.686 | INFO     | app.services.account_service:get_all_accounts:289 - 获取所有账户成功, 共 2 个
2025-05-30 02:41:55.686 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-05-30 02:41:55.689 | INFO     | app.services.account_service:batch_auto_login:1192 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-05-30 02:41:55.689 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-05-30 02:41:55.692 | DEBUG    | ui.views.account_view:_update_group_counts:184 - 分组计数已更新: {1: 1}
2025-05-30 02:41:55.692 | DEBUG    | ui.views.account_view:_init_cache:151 - 账户缓存初始化完成，共 2 个账户
2025-05-30 02:41:55.697 | DEBUG    | ui.views.account_view:_on_accounts_loaded:575 - 账户加载完成: 2个账户
2025-05-30 02:41:55.719 | DEBUG    | ui.views.account_view:_update_group_counts:184 - 分组计数已更新: {1: 1}
2025-05-30 02:41:55.726 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-05-30 02:41:55.728 | WARNING  | core.auth.api_service:verify_vip:441 - 会员验证失败: 验证失败
2025-05-30 02:41:55.973 | INFO     | app.services.account_service:batch_auto_login:1192 - 服务层：账户 *********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-05-30 02:41:55.973 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-05-30 02:41:55.975 | INFO     | app.services.account_service:batch_auto_login:1262 - 服务层：准备调用核心层处理 2 个账户的自动登录。
2025-05-30 02:41:55.976 | INFO     | app.services.account_service:batch_auto_login:1272 - 服务层：设置核心层任务超时为 120 秒。
2025-05-30 02:41:55.976 | INFO     | core.telegram.client_manager:batch_auto_login:1220 - 开始批量自动登录, 共 2 个账户, 最大并发 5
2025-05-30 02:41:55.977 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:256 - 创建客户端: h:\PyProject\TeleTest\APPDATA\sessions\+***********.session, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-05-30 02:41:55.978 | DEBUG    | core.telegram.client_manager:get_proxy:93 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-05-30 02:41:55.978 | DEBUG    | core.telegram.client_manager:get_proxy:140 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-05-30 02:41:55.979 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-05-30 02:41:55.982 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:256 - 创建客户端: h:\PyProject\TeleTest\APPDATA\sessions\***********.session, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-05-30 02:41:55.983 | DEBUG    | core.telegram.client_manager:get_proxy:93 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-05-30 02:41:55.983 | DEBUG    | core.telegram.client_manager:get_proxy:140 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-05-30 02:41:56.086 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-05-30 02:41:56.088 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-05-30 02:41:56.090 | INFO     | ui.views.proxy_view:_load_initial_data:1044 - 初始数据加载完成，共 4 个代理
2025-05-30 02:41:59.877 | INFO     | core.telegram.client_manager:_connect_client:204 - 连接成功!
2025-05-30 02:41:59.976 | INFO     | core.telegram.client_manager:_connect_client:204 - 连接成功!
2025-05-30 02:42:01.085 | INFO     | core.telegram.client_manager:_verify_client_authorization:1195 - 账户已授权: ***********
2025-05-30 02:42:01.441 | INFO     | core.telegram.client_manager:_verify_client_authorization:1195 - 账户已授权: +***********
2025-05-30 02:42:03.457 | INFO     | core.telegram.client_manager:batch_auto_login:1369 - 批量登录完成: 总计 2, 成功 2, 失败 0
2025-05-30 02:42:03.992 | INFO     | app.services.account_service:batch_auto_login:1293 - 服务层：核心层登录完成: 成功 2, 失败 0 / 2
2025-05-30 02:42:06.257 | INFO     | app.services.proxy_service:delete_proxy:375 - 删除代理ID: 2
2025-05-30 02:42:06.258 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-05-30 02:42:06.395 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-05-30 02:42:06.396 | INFO     | app.services.proxy_service:refresh_proxies:712 - 刷新代理列表: offset=0, limit=20
2025-05-30 02:42:06.396 | INFO     | core.proxy.proxy_task_manager:submit_task:172 - 提交耗时任务到子线程: refresh_proxies_0_20
2025-05-30 02:42:06.397 | DEBUG    | app.services.proxy_service:get_proxies:500 - 获取代理列表: offset=0, limit=20
2025-05-30 02:42:06.397 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-05-30 02:42:06.397 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-05-30 02:42:06.400 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-05-30 02:42:06.403 | INFO     | core.proxy.proxy_task_manager:task_wrapper:199 - 任务 refresh_proxies_0_20 完成
2025-05-30 02:42:06.569 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-05-30 02:42:06.571 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-05-30 02:42:06.574 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-05-30 02:42:06.576 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-05-30 02:42:06.584 | INFO     | ui.views.proxy_view:load_proxies_by_page:525 - 加载代理页面: 1
2025-05-30 02:42:06.585 | INFO     | app.services.proxy_service:refresh_proxies:712 - 刷新代理列表: offset=0, limit=20
2025-05-30 02:42:06.586 | INFO     | core.proxy.proxy_task_manager:submit_task:172 - 提交耗时任务到子线程: refresh_proxies_0_20
2025-05-30 02:42:06.587 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-05-30 02:42:06.587 | DEBUG    | app.services.proxy_service:get_proxies:500 - 获取代理列表: offset=0, limit=20
2025-05-30 02:42:06.588 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-05-30 02:42:06.597 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-05-30 02:42:06.601 | INFO     | core.proxy.proxy_task_manager:task_wrapper:199 - 任务 refresh_proxies_0_20 完成
2025-05-30 02:42:06.626 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-05-30 02:42:06.627 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-05-30 02:42:06.632 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-05-30 02:42:06.634 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-05-30 02:42:06.639 | INFO     | ui.views.proxy_view:load_proxies_by_page:538 - 页面 1 加载完成，显示 3 条记录，共 3 条
2025-05-30 02:42:08.615 | INFO     | app.services.proxy_service:delete_proxy:375 - 删除代理ID: 5
2025-05-30 02:42:08.616 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-05-30 02:42:08.742 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-05-30 02:42:08.743 | INFO     | app.services.proxy_service:refresh_proxies:712 - 刷新代理列表: offset=0, limit=20
2025-05-30 02:42:08.743 | INFO     | core.proxy.proxy_task_manager:submit_task:172 - 提交耗时任务到子线程: refresh_proxies_0_20
2025-05-30 02:42:08.744 | DEBUG    | app.services.proxy_service:get_proxies:500 - 获取代理列表: offset=0, limit=20
2025-05-30 02:42:08.744 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-05-30 02:42:08.744 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-05-30 02:42:08.748 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-05-30 02:42:08.750 | INFO     | core.proxy.proxy_task_manager:task_wrapper:199 - 任务 refresh_proxies_0_20 完成
2025-05-30 02:42:08.851 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-05-30 02:42:08.911 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-05-30 02:42:08.915 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-05-30 02:42:08.917 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-05-30 02:42:08.927 | INFO     | ui.views.proxy_view:load_proxies_by_page:525 - 加载代理页面: 1
2025-05-30 02:42:08.928 | INFO     | app.services.proxy_service:refresh_proxies:712 - 刷新代理列表: offset=0, limit=20
2025-05-30 02:42:08.928 | INFO     | core.proxy.proxy_task_manager:submit_task:172 - 提交耗时任务到子线程: refresh_proxies_0_20
2025-05-30 02:42:08.928 | DEBUG    | app.services.proxy_service:get_proxies:500 - 获取代理列表: offset=0, limit=20
2025-05-30 02:42:08.928 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-05-30 02:42:08.928 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-05-30 02:42:08.940 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-05-30 02:42:08.942 | INFO     | core.proxy.proxy_task_manager:task_wrapper:199 - 任务 refresh_proxies_0_20 完成
2025-05-30 02:42:08.969 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-05-30 02:42:08.971 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-05-30 02:42:08.977 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-05-30 02:42:08.979 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-05-30 02:42:08.983 | INFO     | ui.views.proxy_view:load_proxies_by_page:538 - 页面 1 加载完成，显示 2 条记录，共 2 条
2025-05-30 02:42:11.456 | INFO     | app.services.proxy_service:delete_proxy:375 - 删除代理ID: 3
2025-05-30 02:42:11.457 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-05-30 02:42:11.575 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-05-30 02:42:11.575 | INFO     | app.services.proxy_service:refresh_proxies:712 - 刷新代理列表: offset=0, limit=20
2025-05-30 02:42:11.576 | INFO     | core.proxy.proxy_task_manager:submit_task:172 - 提交耗时任务到子线程: refresh_proxies_0_20
2025-05-30 02:42:11.576 | DEBUG    | app.services.proxy_service:get_proxies:500 - 获取代理列表: offset=0, limit=20
2025-05-30 02:42:11.576 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-05-30 02:42:11.576 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-05-30 02:42:11.580 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-05-30 02:42:11.582 | INFO     | core.proxy.proxy_task_manager:task_wrapper:199 - 任务 refresh_proxies_0_20 完成
2025-05-30 02:42:11.754 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-05-30 02:42:11.755 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-05-30 02:42:11.758 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-05-30 02:42:11.759 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-05-30 02:42:11.768 | INFO     | ui.views.proxy_view:load_proxies_by_page:525 - 加载代理页面: 1
2025-05-30 02:42:11.769 | INFO     | app.services.proxy_service:refresh_proxies:712 - 刷新代理列表: offset=0, limit=20
2025-05-30 02:42:11.770 | INFO     | core.proxy.proxy_task_manager:submit_task:172 - 提交耗时任务到子线程: refresh_proxies_0_20
2025-05-30 02:42:11.770 | DEBUG    | app.services.proxy_service:get_proxies:500 - 获取代理列表: offset=0, limit=20
2025-05-30 02:42:11.771 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-05-30 02:42:11.771 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-05-30 02:42:11.779 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-05-30 02:42:11.780 | INFO     | core.proxy.proxy_task_manager:task_wrapper:199 - 任务 refresh_proxies_0_20 完成
2025-05-30 02:42:11.789 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-05-30 02:42:11.790 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-05-30 02:42:11.793 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-05-30 02:42:11.795 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-05-30 02:42:11.798 | INFO     | ui.views.proxy_view:load_proxies_by_page:538 - 页面 1 加载完成，显示 1 条记录，共 1 条
2025-05-30 02:42:31.120 | INFO     | ui.views.proxy_view:_on_add_proxy:451 - 用户开始添加代理: IP类型=socks5(远程), IP文本='**************'
2025-05-30 02:42:31.121 | DEBUG    | ui.views.proxy_view:_on_add_proxy:459 - 固定端口: 10061
2025-05-30 02:42:31.121 | DEBUG    | ui.views.proxy_view:_on_add_proxy:473 - 用户凭据: username=, password=None
2025-05-30 02:42:31.121 | INFO     | app.services.proxy_service:add_proxies_from_text:879 - 从文本添加代理: IP文本='**************', 端口范围=10061-10061
2025-05-30 02:42:31.122 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-05-30 02:42:31.136 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-05-30 02:42:31.137 | INFO     | app.services.proxy_service:refresh_proxies:712 - 刷新代理列表: offset=0, limit=20
2025-05-30 02:42:31.137 | INFO     | core.proxy.proxy_task_manager:submit_task:172 - 提交耗时任务到子线程: refresh_proxies_0_20
2025-05-30 02:42:31.138 | DEBUG    | app.services.proxy_service:get_proxies:500 - 获取代理列表: offset=0, limit=20
2025-05-30 02:42:31.138 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-05-30 02:42:31.138 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-05-30 02:42:31.145 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-05-30 02:42:31.147 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-05-30 02:42:31.147 | INFO     | core.proxy.proxy_task_manager:task_wrapper:199 - 任务 refresh_proxies_0_20 完成
2025-05-30 02:42:31.149 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-05-30 02:42:31.153 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-05-30 02:42:31.155 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-05-30 02:42:31.161 | INFO     | app.services.proxy_service:validate_proxies:756 - 开始验证指定代理，共 1 个
2025-05-30 02:42:31.161 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-05-30 02:42:31.193 | INFO     | core.proxy.proxy_core_service:validate_proxies_stream:167 - 批量验证代理(流式): 1个
2025-05-30 02:42:31.194 | INFO     | core.proxy.proxy_validator:validate_proxy:43 - 正在验证代理 socks5://**************:10061
2025-05-30 02:42:33.021 | INFO     | core.proxy.proxy_validator:validate_proxy:68 - 代理验证成功: socks5://**************:10061, 耗时: 1826.00ms
2025-05-30 02:42:33.032 | INFO     | core.proxy.proxy_core_service:validate_proxies_stream:232 - 批量代理验证完成，结果: 1/1 个有效
2025-05-30 02:42:33.032 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-05-30 02:42:33.033 | INFO     | app.services.proxy_service:refresh_proxies:712 - 刷新代理列表: offset=0, limit=20
2025-05-30 02:42:33.033 | INFO     | core.proxy.proxy_task_manager:submit_task:172 - 提交耗时任务到子线程: refresh_proxies_0_20
2025-05-30 02:42:33.034 | DEBUG    | app.services.proxy_service:get_proxies:500 - 获取代理列表: offset=0, limit=20
2025-05-30 02:42:33.034 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-05-30 02:42:33.034 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-05-30 02:42:33.044 | INFO     | ui.views.proxy_view:load_proxies_by_page:525 - 加载代理页面: 1
2025-05-30 02:42:33.045 | INFO     | app.services.proxy_service:refresh_proxies:712 - 刷新代理列表: offset=0, limit=20
2025-05-30 02:42:33.046 | INFO     | app.services.proxy_service:refresh_proxies:719 - 刷新任务已在运行，跳过
2025-05-30 02:42:33.047 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-05-30 02:42:33.047 | DEBUG    | app.services.proxy_service:get_proxies:500 - 获取代理列表: offset=0, limit=20
2025-05-30 02:42:33.048 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-05-30 02:42:33.049 | INFO     | core.proxy.proxy_task_manager:task_wrapper:199 - 任务 refresh_proxies_0_20 完成
2025-05-30 02:42:33.049 | INFO     | ui.views.proxy_view:load_proxies_by_page:525 - 加载代理页面: 1
2025-05-30 02:42:33.049 | INFO     | app.services.proxy_service:refresh_proxies:712 - 刷新代理列表: offset=0, limit=20
2025-05-30 02:42:33.050 | INFO     | core.proxy.proxy_task_manager:submit_task:172 - 提交耗时任务到子线程: refresh_proxies_0_20
2025-05-30 02:42:33.050 | DEBUG    | app.services.proxy_service:get_proxies:500 - 获取代理列表: offset=0, limit=20
2025-05-30 02:42:33.051 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-05-30 02:42:33.051 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-05-30 02:42:33.059 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-05-30 02:42:33.063 | INFO     | core.proxy.proxy_task_manager:task_wrapper:199 - 任务 refresh_proxies_0_20 完成
2025-05-30 02:42:33.076 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-05-30 02:42:33.077 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-05-30 02:42:33.078 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-05-30 02:42:33.079 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-05-30 02:42:33.080 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-05-30 02:42:33.081 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-05-30 02:42:33.091 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-05-30 02:42:33.092 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-05-30 02:42:33.093 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-05-30 02:42:33.095 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-05-30 02:42:33.098 | INFO     | ui.views.proxy_view:load_proxies_by_page:538 - 页面 1 加载完成，显示 2 条记录，共 2 条
2025-05-30 02:42:33.120 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-05-30 02:42:33.125 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-05-30 02:42:33.129 | INFO     | ui.views.proxy_view:load_proxies_by_page:538 - 页面 1 加载完成，显示 2 条记录，共 2 条
2025-05-30 02:42:33.130 | INFO     | ui.views.proxy_view:load_proxies_by_page:525 - 加载代理页面: 1
2025-05-30 02:42:33.130 | INFO     | app.services.proxy_service:refresh_proxies:712 - 刷新代理列表: offset=0, limit=20
2025-05-30 02:42:33.131 | INFO     | core.proxy.proxy_task_manager:submit_task:172 - 提交耗时任务到子线程: refresh_proxies_0_20
2025-05-30 02:42:33.131 | DEBUG    | app.services.proxy_service:get_proxies:500 - 获取代理列表: offset=0, limit=20
2025-05-30 02:42:33.132 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-05-30 02:42:33.132 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-05-30 02:42:33.141 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-05-30 02:42:33.145 | INFO     | core.proxy.proxy_task_manager:task_wrapper:199 - 任务 refresh_proxies_0_20 完成
2025-05-30 02:42:33.174 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-05-30 02:42:33.175 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-05-30 02:42:33.179 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-05-30 02:42:33.180 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-05-30 02:42:33.184 | INFO     | ui.views.proxy_view:load_proxies_by_page:538 - 页面 1 加载完成，显示 2 条记录，共 2 条
2025-05-30 02:42:33.185 | INFO     | ui.views.proxy_view:_on_add_proxy:509 - 已清空输入框，代理添加和验证流程完成
2025-05-30 02:42:54.646 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-05-30 02:42:54.647 | DEBUG    | utils.client_http:post:68 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-05-30 02:43:54.650 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-05-30 02:43:54.650 | DEBUG    | utils.client_http:post:68 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-05-30 02:44:54.653 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-05-30 02:44:54.654 | DEBUG    | utils.client_http:post:68 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-05-30 02:45:41.834 | INFO     | ui.views.proxy_view:_on_add_proxy:451 - 用户开始添加代理: IP类型=socks5(远程), IP文本='************'
2025-05-30 02:45:41.835 | DEBUG    | ui.views.proxy_view:_on_add_proxy:459 - 固定端口: 10000
2025-05-30 02:45:41.835 | DEBUG    | ui.views.proxy_view:_on_add_proxy:473 - 用户凭据: username=, password=None
2025-05-30 02:45:41.836 | INFO     | app.services.proxy_service:add_proxies_from_text:879 - 从文本添加代理: IP文本='************', 端口范围=10000-10000
2025-05-30 02:45:41.836 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-05-30 02:45:41.847 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-05-30 02:45:41.848 | INFO     | app.services.proxy_service:refresh_proxies:712 - 刷新代理列表: offset=0, limit=20
2025-05-30 02:45:41.848 | INFO     | core.proxy.proxy_task_manager:submit_task:172 - 提交耗时任务到子线程: refresh_proxies_0_20
2025-05-30 02:45:41.849 | DEBUG    | app.services.proxy_service:get_proxies:500 - 获取代理列表: offset=0, limit=20
2025-05-30 02:45:41.849 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-05-30 02:45:41.850 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-05-30 02:45:41.855 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-05-30 02:45:41.857 | INFO     | core.proxy.proxy_task_manager:task_wrapper:199 - 任务 refresh_proxies_0_20 完成
2025-05-30 02:45:41.858 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-05-30 02:45:41.859 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-05-30 02:45:41.864 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-05-30 02:45:41.865 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-05-30 02:45:41.870 | INFO     | app.services.proxy_service:validate_proxies:756 - 开始验证指定代理，共 1 个
2025-05-30 02:45:41.871 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-05-30 02:45:41.902 | INFO     | core.proxy.proxy_core_service:validate_proxies_stream:167 - 批量验证代理(流式): 1个
2025-05-30 02:45:41.903 | INFO     | core.proxy.proxy_validator:validate_proxy:43 - 正在验证代理 socks5://************:10000
2025-05-30 02:45:42.832 | INFO     | core.proxy.proxy_validator:validate_proxy:68 - 代理验证成功: socks5://************:10000, 耗时: 928.36ms
2025-05-30 02:45:42.842 | INFO     | core.proxy.proxy_core_service:validate_proxies_stream:232 - 批量代理验证完成，结果: 1/1 个有效
2025-05-30 02:45:42.842 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-05-30 02:45:42.843 | INFO     | app.services.proxy_service:refresh_proxies:712 - 刷新代理列表: offset=0, limit=20
2025-05-30 02:45:42.843 | INFO     | core.proxy.proxy_task_manager:submit_task:172 - 提交耗时任务到子线程: refresh_proxies_0_20
2025-05-30 02:45:42.844 | DEBUG    | app.services.proxy_service:get_proxies:500 - 获取代理列表: offset=0, limit=20
2025-05-30 02:45:42.844 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-05-30 02:45:42.844 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-05-30 02:45:42.854 | INFO     | ui.views.proxy_view:load_proxies_by_page:525 - 加载代理页面: 1
2025-05-30 02:45:42.855 | INFO     | app.services.proxy_service:refresh_proxies:712 - 刷新代理列表: offset=0, limit=20
2025-05-30 02:45:42.856 | INFO     | app.services.proxy_service:refresh_proxies:719 - 刷新任务已在运行，跳过
2025-05-30 02:45:42.856 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-05-30 02:45:42.856 | DEBUG    | app.services.proxy_service:get_proxies:500 - 获取代理列表: offset=0, limit=20
2025-05-30 02:45:42.857 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-05-30 02:45:42.857 | INFO     | core.proxy.proxy_task_manager:task_wrapper:199 - 任务 refresh_proxies_0_20 完成
2025-05-30 02:45:42.858 | INFO     | ui.views.proxy_view:load_proxies_by_page:525 - 加载代理页面: 1
2025-05-30 02:45:42.858 | INFO     | app.services.proxy_service:refresh_proxies:712 - 刷新代理列表: offset=0, limit=20
2025-05-30 02:45:42.859 | INFO     | core.proxy.proxy_task_manager:submit_task:172 - 提交耗时任务到子线程: refresh_proxies_0_20
2025-05-30 02:45:42.859 | DEBUG    | app.services.proxy_service:get_proxies:500 - 获取代理列表: offset=0, limit=20
2025-05-30 02:45:42.860 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-05-30 02:45:42.860 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-05-30 02:45:42.866 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-05-30 02:45:42.869 | INFO     | core.proxy.proxy_task_manager:task_wrapper:199 - 任务 refresh_proxies_0_20 完成
2025-05-30 02:45:42.887 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-05-30 02:45:42.888 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-05-30 02:45:42.889 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-05-30 02:45:42.890 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-05-30 02:45:42.891 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-05-30 02:45:42.892 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-05-30 02:45:42.899 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-05-30 02:45:42.899 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-05-30 02:45:42.900 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-05-30 02:45:42.901 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-05-30 02:45:42.906 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-05-30 02:45:42.910 | INFO     | ui.views.proxy_view:load_proxies_by_page:538 - 页面 1 加载完成，显示 3 条记录，共 3 条
2025-05-30 02:45:42.911 | INFO     | ui.views.proxy_view:load_proxies_by_page:525 - 加载代理页面: 1
2025-05-30 02:45:42.911 | INFO     | app.services.proxy_service:refresh_proxies:712 - 刷新代理列表: offset=0, limit=20
2025-05-30 02:45:42.912 | INFO     | core.proxy.proxy_task_manager:submit_task:172 - 提交耗时任务到子线程: refresh_proxies_0_20
2025-05-30 02:45:42.912 | DEBUG    | app.services.proxy_service:get_proxies:500 - 获取代理列表: offset=0, limit=20
2025-05-30 02:45:42.912 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-05-30 02:45:42.913 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-05-30 02:45:42.925 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-05-30 02:45:42.929 | INFO     | core.proxy.proxy_task_manager:task_wrapper:199 - 任务 refresh_proxies_0_20 完成
2025-05-30 02:45:42.950 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-05-30 02:45:42.953 | INFO     | ui.views.proxy_view:load_proxies_by_page:538 - 页面 1 加载完成，显示 3 条记录，共 3 条
2025-05-30 02:45:42.982 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-05-30 02:45:42.983 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-05-30 02:45:42.987 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-05-30 02:45:42.991 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-05-30 02:45:42.995 | INFO     | ui.views.proxy_view:load_proxies_by_page:538 - 页面 1 加载完成，显示 3 条记录，共 3 条
2025-05-30 02:45:43.012 | INFO     | ui.views.proxy_view:_on_add_proxy:509 - 已清空输入框，代理添加和验证流程完成
2025-05-30 02:45:54.649 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-05-30 02:45:54.649 | DEBUG    | utils.client_http:post:68 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-05-30 02:46:00.272 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-05-30 02:46:00.277 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-05-30 02:46:00.278 | INFO     | app.services.proxy_service:validate_all_proxies:332 - 开始验证所有代理
2025-05-30 02:46:00.279 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-05-30 02:46:00.291 | INFO     | core.proxy.proxy_core_service:validate_proxies_stream:167 - 批量验证代理(流式): 3个
2025-05-30 02:46:00.292 | INFO     | core.proxy.proxy_validator:validate_proxy:43 - 正在验证代理 socks5://************:10000
2025-05-30 02:46:00.293 | INFO     | core.proxy.proxy_validator:validate_proxy:43 - 正在验证代理 socks5://**************:10061
2025-05-30 02:46:00.294 | INFO     | core.proxy.proxy_validator:validate_proxy:43 - 正在验证代理 socks5://127.0.0.1:1080
2025-05-30 02:46:01.191 | WARNING  | core.proxy.proxy_validator:validate_proxy:71 - 使用https://web.telegram.org验证代理失败: socks5://**************:10061, 错误: [WinError 64] 指定的网络名不再可用。
2025-05-30 02:46:01.279 | INFO     | core.proxy.proxy_validator:validate_proxy:68 - 代理验证成功: socks5://************:10000, 耗时: 986.00ms
2025-05-30 02:46:02.942 | INFO     | core.proxy.proxy_validator:validate_proxy:68 - 代理验证成功: socks5://127.0.0.1:1080, 耗时: 2648.12ms
2025-05-30 02:46:03.093 | INFO     | core.proxy.proxy_validator:validate_proxy:68 - 代理验证成功: socks5://**************:10061, 耗时: 1818.37ms
2025-05-30 02:46:03.104 | INFO     | core.proxy.proxy_core_service:validate_proxies_stream:232 - 批量代理验证完成，结果: 3/3 个有效
2025-05-30 02:46:03.105 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-05-30 02:46:03.105 | INFO     | app.services.proxy_service:refresh_proxies:712 - 刷新代理列表: offset=0, limit=20
2025-05-30 02:46:03.106 | INFO     | core.proxy.proxy_task_manager:submit_task:172 - 提交耗时任务到子线程: refresh_proxies_0_20
2025-05-30 02:46:03.106 | DEBUG    | app.services.proxy_service:get_proxies:500 - 获取代理列表: offset=0, limit=20
2025-05-30 02:46:03.107 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-05-30 02:46:03.107 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-05-30 02:46:03.116 | INFO     | ui.views.proxy_view:load_proxies_by_page:525 - 加载代理页面: 1
2025-05-30 02:46:03.116 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-05-30 02:46:03.117 | INFO     | app.services.proxy_service:refresh_proxies:712 - 刷新代理列表: offset=0, limit=20
2025-05-30 02:46:03.118 | INFO     | app.services.proxy_service:refresh_proxies:719 - 刷新任务已在运行，跳过
2025-05-30 02:46:03.118 | INFO     | core.proxy.proxy_task_manager:task_wrapper:199 - 任务 refresh_proxies_0_20 完成
2025-05-30 02:46:03.118 | DEBUG    | app.services.proxy_service:get_proxies:500 - 获取代理列表: offset=0, limit=20
2025-05-30 02:46:03.119 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-05-30 02:46:03.120 | INFO     | ui.views.proxy_view:load_proxies_by_page:525 - 加载代理页面: 1
2025-05-30 02:46:03.121 | INFO     | app.services.proxy_service:refresh_proxies:712 - 刷新代理列表: offset=0, limit=20
2025-05-30 02:46:03.122 | INFO     | core.proxy.proxy_task_manager:submit_task:172 - 提交耗时任务到子线程: refresh_proxies_0_20
2025-05-30 02:46:03.122 | DEBUG    | app.services.proxy_service:get_proxies:500 - 获取代理列表: offset=0, limit=20
2025-05-30 02:46:03.123 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-05-30 02:46:03.123 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-05-30 02:46:03.132 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-05-30 02:46:03.135 | INFO     | core.proxy.proxy_task_manager:task_wrapper:199 - 任务 refresh_proxies_0_20 完成
2025-05-30 02:46:03.154 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-05-30 02:46:03.156 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-05-30 02:46:03.157 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-05-30 02:46:03.158 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-05-30 02:46:03.159 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-05-30 02:46:03.160 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-05-30 02:46:03.168 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-05-30 02:46:03.169 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-05-30 02:46:03.169 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-05-30 02:46:03.172 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-05-30 02:46:03.175 | INFO     | ui.views.proxy_view:load_proxies_by_page:538 - 页面 1 加载完成，显示 3 条记录，共 3 条
2025-05-30 02:46:03.195 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-05-30 02:46:03.199 | INFO     | ui.views.proxy_view:load_proxies_by_page:538 - 页面 1 加载完成，显示 3 条记录，共 3 条
2025-05-30 02:46:03.205 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-05-30 02:46:13.513 | INFO     | ui.main_window:closeEvent:335 - MainWindow: 接收到关闭事件
2025-05-30 02:46:13.526 | INFO     | ui.main_window:_cleanup_before_quit:228 - MainWindow: 执行清理资源...
2025-05-30 02:46:13.527 | INFO     | core.telegram.client_worker:stop:110 - 正在停止Telegram客户端工作线程
2025-05-30 02:46:13.535 | INFO     | core.telegram.client_manager:cleanup_async:1140 - 开始清理资源并断开所有连接
2025-05-30 02:46:13.535 | INFO     | core.telegram.client_manager:disconnect_all_clients:725 - 正在断开所有客户端连接
2025-05-30 02:46:13.536 | INFO     | core.telegram.client_manager:_safe_disconnect:746 - 正在断开客户端连接: ***********
2025-05-30 02:46:13.536 | INFO     | core.telegram.client_manager:_safe_disconnect:746 - 正在断开客户端连接: +***********
2025-05-30 02:46:13.549 | INFO     | core.telegram.client_manager:_safe_disconnect:750 - 断开客户端连接成功: ***********
2025-05-30 02:46:13.549 | INFO     | core.telegram.client_manager:_safe_disconnect:750 - 断开客户端连接成功: +***********
2025-05-30 02:46:13.550 | INFO     | core.telegram.client_manager:cleanup_async:1159 - 资源清理完成
2025-05-30 02:46:14.047 | INFO     | core.telegram.client_manager:cleanup_async:1140 - 开始清理资源并断开所有连接
2025-05-30 02:46:14.048 | INFO     | core.telegram.client_manager:disconnect_all_clients:725 - 正在断开所有客户端连接
2025-05-30 02:46:14.049 | INFO     | core.telegram.client_manager:cleanup_async:1159 - 资源清理完成
2025-05-30 02:46:14.562 | INFO     | core.telegram.client_worker:run:99 - Telegram客户端工作线程已退出
2025-05-30 02:46:14.563 | INFO     | core.telegram.client_worker:stop:124 - Telegram客户端工作线程已停止
2025-05-30 02:46:14.564 | INFO     | ui.main_window:_cleanup_before_quit:237 - TelegramClientWorker 已停止。
2025-05-30 02:46:14.564 | INFO     | ui.main_window:_cleanup_before_quit:241 - MainWindow 清理完成
2025-05-30 02:46:14.580 | INFO     | __main__:main:111 - 应用程序已正常退出
2025-05-30 03:07:14.708 | INFO     | utils.logger:init_logger:98 - 日志系统初始化完成，默认级别: DEBUG，日志目录: h:\PyProject\TeleTest\APPDATA\logs
2025-05-30 03:07:16.134 | INFO     | data.database:init_database:31 - 初始化数据库连接: h:\PyProject\TeleTest\APPDATA\database\telegram_manager.db
2025-05-30 03:07:16.150 | DEBUG    | data.database:init_database:58 - 创建数据库表
2025-05-30 03:07:16.162 | INFO     | data.database:init_database:61 - 数据库初始化完成
2025-05-30 03:07:16.749 | DEBUG    | app.controllers.auth_controller:initialize_software:43 - 控制器: 初始化软件 app_id=1000, version=1.0
2025-05-30 03:07:16.750 | DEBUG    | utils.client_http:get:41 - 发送GET请求: https://server.xile188.com/api/user/1000/windowns10/1.0/ini
2025-05-30 03:07:17.173 | DEBUG    | core.auth.api_service:init_software:66 - 初始化数据解密成功: {'new_content': '1.多账户管理\n2.ip池管理\n3.消息监听\n4.消息群发\n5.拉人入群', 'new_url': None, 'notice': {'id': '2', 'content': '公告内容测试3', 'time': '**********'}}
2025-05-30 03:07:17.179 | INFO     | app.controllers.auth_controller:login:61 - 控制器: 用户登录 account=zhangsan03
2025-05-30 03:07:20.024 | INFO     | core.auth.api_service:login:127 - 用户登录: account=zhangsan03
2025-05-30 03:07:20.024 | DEBUG    | utils.client_http:post:68 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/logon
2025-05-30 03:07:20.245 | INFO     | core.auth.api_service:get_user_info:347 - 获取用户信息
2025-05-30 03:07:20.246 | DEBUG    | utils.client_http:post:68 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/info
2025-05-30 03:07:20.405 | INFO     | core.auth.api_service:get_user_info:375 - 获取用户信息成功
2025-05-30 03:07:20.412 | INFO     | __main__:on_auth_completed:93 - 用户登录成功，正在启动主窗口...
2025-05-30 03:07:20.428 | INFO     | core.telegram.client_worker:__init__:63 - Telegram客户端工作线程初始化
2025-05-30 03:07:20.431 | INFO     | core.telegram.client_worker:run:67 - Telegram客户端工作线程开始运行
2025-05-30 03:07:20.434 | INFO     | core.telegram.client_manager:__init__:66 - Telegram客户端管理器初始化
2025-05-30 03:07:20.434 | INFO     | core.telegram.user_manager:__init__:31 - Telegram用户管理器初始化
2025-05-30 03:07:20.435 | INFO     | core.telegram.group_manager:__init__:36 - Telegram群组管理器初始化
2025-05-30 03:07:20.435 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-05-30 03:07:20.436 | INFO     | core.telegram.monitor_manager:__init__:26 - Telegram监控管理器初始化
2025-05-30 03:07:20.436 | INFO     | core.telegram.telegram_service:__init__:39 - Telegram服务初始化完成
2025-05-30 03:07:20.438 | INFO     | ui.main_window:_initialize_core_components:79 - MainWindow: 初始化核心组件...
2025-05-30 03:07:20.439 | INFO     | app.services.account_service:__init__:42 - 账户服务初始化
2025-05-30 03:07:20.439 | INFO     | core.proxy.proxy_task_manager:run:284 - 代理任务管理器线程启动
2025-05-30 03:07:20.439 | INFO     | app.controllers.account_controller:__init__:81 - 账户控制器初始化
2025-05-30 03:07:20.440 | INFO     | app.services.monitor_service:__init__:39 - 初始化监控任务服务
2025-05-30 03:07:20.441 | INFO     | core.monitor.filter:__init__:26 - 初始化消息过滤器
2025-05-30 03:07:20.441 | INFO     | core.monitor.extractor:__init__:24 - 初始化关键词提取器
2025-05-30 03:07:20.441 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-05-30 03:07:20.442 | INFO     | core.monitor.notifier:__init__:90 - 初始化通知管理器
2025-05-30 03:07:20.442 | INFO     | app.controllers.telegram_monitor_controller:__init__:64 - 初始化 Telegram 监控控制器
2025-05-30 03:07:20.443 | DEBUG    | app.controllers.telegram_monitor_controller:__init__:82 - 控制器初始化完成
2025-05-30 03:07:20.608 | INFO     | ui.views.monitor_view:__init__:48 - 初始化监控视图
2025-05-30 03:07:20.608 | DEBUG    | ui.views.monitor_view:_connect_signals:80 - 连接控制器信号
2025-05-30 03:07:20.769 | INFO     | ui.views.proxy_view:__init__:96 - 初始化视图，加载第一页代理数据
2025-05-30 03:07:20.953 | INFO     | __main__:on_auth_completed:97 - 主窗口已启动
2025-05-30 03:07:20.997 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:88 - 尝试连接监听日志信号...
2025-05-30 03:07:20.998 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:90 - 监听日志信号连接成功
2025-05-30 03:07:20.998 | INFO     | core.auth.api_service:verify_vip:425 - 会员验证接口调用
2025-05-30 03:07:20.999 | DEBUG    | utils.client_http:post:68 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/vip
2025-05-30 03:07:20.999 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-05-30 03:07:21.004 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-05-30 03:07:21.008 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:145 - 加载监控任务列表 (搜索关键词: '')
2025-05-30 03:07:21.008 | DEBUG    | ui.views.monitor_view:_on_loading_started:620 - 加载中: 正在加载任务列表...
2025-05-30 03:07:21.009 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-05-30 03:07:21.015 | INFO     | app.services.proxy_service:refresh_proxies:712 - 刷新代理列表: offset=0, limit=20
2025-05-30 03:07:21.016 | INFO     | core.proxy.proxy_task_manager:submit_task:172 - 提交耗时任务到子线程: refresh_proxies_0_20
2025-05-30 03:07:21.016 | DEBUG    | app.services.proxy_service:get_proxies:500 - 获取代理列表: offset=0, limit=20
2025-05-30 03:07:21.017 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-05-30 03:07:21.017 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-05-30 03:07:21.032 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-05-30 03:07:21.047 | INFO     | app.controllers.invite_controller:get_invite_tasks:38 - 获取邀请任务列表, status=None
2025-05-30 03:07:21.049 | INFO     | app.services.invite_service:get_invite_tasks:121 - 获取邀请任务列表, status=None
2025-05-30 03:07:21.049 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-05-30 03:07:21.051 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-05-30 03:07:21.051 | INFO     | app.services.invite_service:task_initialize:39 - 初始化邀请服务，重置运行中的任务为暂停状态
2025-05-30 03:07:21.052 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-05-30 03:07:21.053 | INFO     | core.proxy.proxy_task_manager:task_wrapper:199 - 任务 refresh_proxies_0_20 完成
2025-05-30 03:07:21.267 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-05-30 03:07:21.271 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-05-30 03:07:21.274 | INFO     | app.services.invite_service:task_initialize:48 - 成功重置 0 个任务状态
2025-05-30 03:07:21.274 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-05-30 03:07:21.275 | DEBUG    | ui.views.monitor_view:_refresh_task_list:175 - 刷新任务列表，共2个任务
2025-05-30 03:07:21.280 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-05-30 03:07:21.287 | INFO     | app.services.account_service:get_all_groups:96 - 获取所有账户分组成功, 共 1 个
2025-05-30 03:07:21.288 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-05-30 03:07:21.291 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-05-30 03:07:21.292 | DEBUG    | ui.views.account_view:_on_groups_loaded:442 - 分组加载完成: 1个分组
2025-05-30 03:07:21.299 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-05-30 03:07:21.301 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-05-30 03:07:21.304 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-05-30 03:07:21.361 | INFO     | app.services.account_service:get_all_accounts:289 - 获取所有账户成功, 共 2 个
2025-05-30 03:07:21.362 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-05-30 03:07:21.364 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-05-30 03:07:21.365 | DEBUG    | ui.views.account_view:_on_accounts_loaded:575 - 账户加载完成: 2个账户
2025-05-30 03:07:21.385 | DEBUG    | ui.views.account_view:_update_group_counts:184 - 分组计数已更新: {1: 1}
2025-05-30 03:07:21.389 | INFO     | ui.views.account_view:_auto_login_accounts:630 - 开始调用控制器进行 2 个账户的自动登录。
2025-05-30 03:07:21.390 | INFO     | app.services.account_service:batch_auto_login:1153 - 服务层：开始批量自动登录 2 个账户, 最大并发: 5
2025-05-30 03:07:21.390 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-05-30 03:07:21.396 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-05-30 03:07:21.399 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-05-30 03:07:21.400 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-05-30 03:07:21.401 | INFO     | app.controllers.telegram_monitor_controller:_load_total_users_count_internal:191 - 成功加载总体统计数据: 总用户数 958, 今日采集 0, 日均采集 106, 运行天数 9
2025-05-30 03:07:21.402 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:156 - 成功加载 2 个任务
2025-05-30 03:07:21.403 | DEBUG    | ui.views.monitor_view:_on_loading_finished:625 - 加载完成
2025-05-30 03:07:21.405 | INFO     | app.services.account_service:batch_auto_login:1192 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-05-30 03:07:21.405 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-05-30 03:07:21.406 | INFO     | app.services.account_service:get_all_accounts:289 - 获取所有账户成功, 共 2 个
2025-05-30 03:07:21.406 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-05-30 03:07:21.420 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-05-30 03:07:21.422 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-05-30 03:07:21.436 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-05-30 03:07:21.438 | DEBUG    | ui.views.account_view:_update_group_counts:184 - 分组计数已更新: {1: 1}
2025-05-30 03:07:21.439 | DEBUG    | ui.views.account_view:_init_cache:151 - 账户缓存初始化完成，共 2 个账户
2025-05-30 03:07:21.443 | DEBUG    | ui.views.account_view:_on_accounts_loaded:575 - 账户加载完成: 2个账户
2025-05-30 03:07:21.468 | DEBUG    | ui.views.account_view:_update_group_counts:184 - 分组计数已更新: {1: 1}
2025-05-30 03:07:21.481 | INFO     | app.services.account_service:batch_auto_login:1192 - 服务层：账户 *********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-05-30 03:07:21.481 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-05-30 03:07:21.483 | INFO     | app.services.account_service:batch_auto_login:1262 - 服务层：准备调用核心层处理 2 个账户的自动登录。
2025-05-30 03:07:21.484 | INFO     | app.services.account_service:batch_auto_login:1272 - 服务层：设置核心层任务超时为 120 秒。
2025-05-30 03:07:21.484 | INFO     | core.telegram.client_manager:batch_auto_login:1220 - 开始批量自动登录, 共 2 个账户, 最大并发 5
2025-05-30 03:07:21.484 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:256 - 创建客户端: h:\PyProject\TeleTest\APPDATA\sessions\+***********.session, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-05-30 03:07:21.486 | DEBUG    | core.telegram.client_manager:get_proxy:93 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-05-30 03:07:21.487 | DEBUG    | core.telegram.client_manager:get_proxy:140 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-05-30 03:07:21.492 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-05-30 03:07:21.492 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:256 - 创建客户端: h:\PyProject\TeleTest\APPDATA\sessions\***********.session, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-05-30 03:07:21.492 | DEBUG    | core.telegram.client_manager:get_proxy:93 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-05-30 03:07:21.493 | DEBUG    | core.telegram.client_manager:get_proxy:140 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-05-30 03:07:21.498 | WARNING  | core.auth.api_service:verify_vip:441 - 会员验证失败: 验证失败
2025-05-30 03:07:21.812 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-05-30 03:07:21.814 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-05-30 03:07:21.816 | INFO     | ui.views.proxy_view:_load_initial_data:1044 - 初始数据加载完成，共 3 个代理
2025-05-30 03:07:25.193 | INFO     | core.telegram.client_manager:_connect_client:204 - 连接成功!
2025-05-30 03:07:25.344 | INFO     | core.telegram.client_manager:_connect_client:204 - 连接成功!
2025-05-30 03:07:26.311 | INFO     | core.telegram.client_manager:_verify_client_authorization:1195 - 账户已授权: +***********
2025-05-30 03:07:26.489 | INFO     | core.telegram.client_manager:_verify_client_authorization:1195 - 账户已授权: ***********
2025-05-30 03:07:28.496 | INFO     | core.telegram.client_manager:batch_auto_login:1369 - 批量登录完成: 总计 2, 成功 2, 失败 0
2025-05-30 03:07:28.523 | INFO     | app.services.account_service:batch_auto_login:1293 - 服务层：核心层登录完成: 成功 2, 失败 0 / 2
2025-05-30 03:07:31.344 | INFO     | app.services.proxy_service:delete_proxy:375 - 删除代理ID: 3
2025-05-30 03:07:31.345 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-05-30 03:07:31.473 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-05-30 03:07:31.473 | INFO     | app.services.proxy_service:refresh_proxies:712 - 刷新代理列表: offset=0, limit=20
2025-05-30 03:07:31.474 | INFO     | core.proxy.proxy_task_manager:submit_task:172 - 提交耗时任务到子线程: refresh_proxies_0_20
2025-05-30 03:07:31.474 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-05-30 03:07:31.474 | DEBUG    | app.services.proxy_service:get_proxies:500 - 获取代理列表: offset=0, limit=20
2025-05-30 03:07:31.475 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-05-30 03:07:31.481 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-05-30 03:07:31.481 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-05-30 03:07:31.482 | INFO     | core.proxy.proxy_task_manager:task_wrapper:199 - 任务 refresh_proxies_0_20 完成
2025-05-30 03:07:31.666 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-05-30 03:07:31.676 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-05-30 03:07:31.680 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-05-30 03:07:31.690 | INFO     | ui.views.proxy_view:load_proxies_by_page:525 - 加载代理页面: 1
2025-05-30 03:07:31.691 | INFO     | app.services.proxy_service:refresh_proxies:712 - 刷新代理列表: offset=0, limit=20
2025-05-30 03:07:31.691 | INFO     | core.proxy.proxy_task_manager:submit_task:172 - 提交耗时任务到子线程: refresh_proxies_0_20
2025-05-30 03:07:31.692 | DEBUG    | app.services.proxy_service:get_proxies:500 - 获取代理列表: offset=0, limit=20
2025-05-30 03:07:31.692 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-05-30 03:07:31.692 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-05-30 03:07:31.699 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-05-30 03:07:31.702 | INFO     | core.proxy.proxy_task_manager:task_wrapper:199 - 任务 refresh_proxies_0_20 完成
2025-05-30 03:07:31.715 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-05-30 03:07:31.716 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-05-30 03:07:31.725 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-05-30 03:07:31.728 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-05-30 03:07:31.731 | INFO     | ui.views.proxy_view:load_proxies_by_page:538 - 页面 1 加载完成，显示 2 条记录，共 2 条
2025-05-30 03:07:34.576 | INFO     | ui.views.proxy_view:_on_add_proxy:451 - 用户开始添加代理: IP类型=socks5(本地), IP文本='**************'
2025-05-30 03:07:34.576 | DEBUG    | ui.views.proxy_view:_on_add_proxy:459 - 固定端口: 10000
2025-05-30 03:07:34.577 | DEBUG    | ui.views.proxy_view:_on_add_proxy:473 - 用户凭据: username=, password=None
2025-05-30 03:07:34.577 | INFO     | app.services.proxy_service:add_proxies_from_text:879 - 从文本添加代理: IP文本='**************', 端口范围=10000-10000
2025-05-30 03:07:34.577 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-05-30 03:07:34.583 | WARNING  | app.services.proxy_service:add_proxies_from_text:904 - 代理IP ************** 已存在，跳过添加
2025-05-30 03:07:34.584 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-05-30 03:07:40.808 | INFO     | ui.views.proxy_view:_on_add_proxy:451 - 用户开始添加代理: IP类型=socks5(本地), IP文本='**************'
2025-05-30 03:07:40.808 | DEBUG    | ui.views.proxy_view:_on_add_proxy:459 - 固定端口: 10000
2025-05-30 03:07:40.809 | DEBUG    | ui.views.proxy_view:_on_add_proxy:473 - 用户凭据: username=, password=None
2025-05-30 03:07:40.809 | INFO     | app.services.proxy_service:add_proxies_from_text:879 - 从文本添加代理: IP文本='**************', 端口范围=10000-10000
2025-05-30 03:07:40.810 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-05-30 03:07:40.815 | WARNING  | app.services.proxy_service:add_proxies_from_text:904 - 代理IP ************** 已存在，跳过添加
2025-05-30 03:07:40.816 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-05-30 03:08:01.314 | INFO     | ui.main_window:closeEvent:335 - MainWindow: 接收到关闭事件
2025-05-30 03:08:01.326 | INFO     | ui.main_window:_cleanup_before_quit:228 - MainWindow: 执行清理资源...
2025-05-30 03:08:01.327 | INFO     | core.telegram.client_worker:stop:110 - 正在停止Telegram客户端工作线程
2025-05-30 03:08:01.333 | INFO     | core.telegram.client_manager:cleanup_async:1140 - 开始清理资源并断开所有连接
2025-05-30 03:08:01.334 | INFO     | core.telegram.client_manager:disconnect_all_clients:725 - 正在断开所有客户端连接
2025-05-30 03:08:01.334 | INFO     | core.telegram.client_manager:_safe_disconnect:746 - 正在断开客户端连接: +***********
2025-05-30 03:08:01.335 | INFO     | core.telegram.client_manager:_safe_disconnect:746 - 正在断开客户端连接: ***********
2025-05-30 03:08:01.344 | INFO     | core.telegram.client_manager:_safe_disconnect:750 - 断开客户端连接成功: +***********
2025-05-30 03:08:01.345 | INFO     | core.telegram.client_manager:_safe_disconnect:750 - 断开客户端连接成功: ***********
2025-05-30 03:08:01.346 | INFO     | core.telegram.client_manager:cleanup_async:1159 - 资源清理完成
2025-05-30 03:08:01.850 | INFO     | core.telegram.client_manager:cleanup_async:1140 - 开始清理资源并断开所有连接
2025-05-30 03:08:01.850 | INFO     | core.telegram.client_manager:disconnect_all_clients:725 - 正在断开所有客户端连接
2025-05-30 03:08:01.851 | INFO     | core.telegram.client_manager:cleanup_async:1159 - 资源清理完成
2025-05-30 03:08:02.352 | INFO     | core.telegram.client_worker:run:99 - Telegram客户端工作线程已退出
2025-05-30 03:08:02.353 | INFO     | core.telegram.client_worker:stop:124 - Telegram客户端工作线程已停止
2025-05-30 03:08:02.354 | INFO     | ui.main_window:_cleanup_before_quit:237 - TelegramClientWorker 已停止。
2025-05-30 03:08:02.354 | INFO     | ui.main_window:_cleanup_before_quit:241 - MainWindow 清理完成
2025-05-30 03:08:02.372 | INFO     | __main__:main:111 - 应用程序已正常退出
2025-05-30 03:08:08.777 | INFO     | utils.logger:init_logger:98 - 日志系统初始化完成，默认级别: DEBUG，日志目录: h:\PyProject\TeleTest\APPDATA\logs
2025-05-30 03:08:10.224 | INFO     | data.database:init_database:31 - 初始化数据库连接: h:\PyProject\TeleTest\APPDATA\database\telegram_manager.db
2025-05-30 03:08:10.241 | DEBUG    | data.database:init_database:58 - 创建数据库表
2025-05-30 03:08:10.254 | INFO     | data.database:init_database:61 - 数据库初始化完成
2025-05-30 03:08:10.870 | DEBUG    | app.controllers.auth_controller:initialize_software:43 - 控制器: 初始化软件 app_id=1000, version=1.0
2025-05-30 03:08:10.871 | DEBUG    | utils.client_http:get:41 - 发送GET请求: https://server.xile188.com/api/user/1000/windowns10/1.0/ini
2025-05-30 03:08:11.241 | DEBUG    | core.auth.api_service:init_software:66 - 初始化数据解密成功: {'new_content': '1.多账户管理\n2.ip池管理\n3.消息监听\n4.消息群发\n5.拉人入群', 'new_url': None, 'notice': {'id': '2', 'content': '公告内容测试3', 'time': '**********'}}
2025-05-30 03:08:11.247 | INFO     | app.controllers.auth_controller:login:61 - 控制器: 用户登录 account=zhangsan03
2025-05-30 03:08:14.138 | INFO     | core.auth.api_service:login:127 - 用户登录: account=zhangsan03
2025-05-30 03:08:14.139 | DEBUG    | utils.client_http:post:68 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/logon
2025-05-30 03:08:14.307 | INFO     | core.auth.api_service:get_user_info:347 - 获取用户信息
2025-05-30 03:08:14.308 | DEBUG    | utils.client_http:post:68 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/info
2025-05-30 03:08:14.465 | INFO     | core.auth.api_service:get_user_info:375 - 获取用户信息成功
2025-05-30 03:08:14.472 | INFO     | __main__:on_auth_completed:93 - 用户登录成功，正在启动主窗口...
2025-05-30 03:08:14.488 | INFO     | core.telegram.client_worker:__init__:63 - Telegram客户端工作线程初始化
2025-05-30 03:08:14.492 | INFO     | core.telegram.client_worker:run:67 - Telegram客户端工作线程开始运行
2025-05-30 03:08:14.495 | INFO     | core.telegram.client_manager:__init__:66 - Telegram客户端管理器初始化
2025-05-30 03:08:14.495 | INFO     | core.telegram.user_manager:__init__:31 - Telegram用户管理器初始化
2025-05-30 03:08:14.496 | INFO     | core.telegram.group_manager:__init__:36 - Telegram群组管理器初始化
2025-05-30 03:08:14.496 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-05-30 03:08:14.497 | INFO     | core.telegram.monitor_manager:__init__:26 - Telegram监控管理器初始化
2025-05-30 03:08:14.497 | INFO     | core.telegram.telegram_service:__init__:39 - Telegram服务初始化完成
2025-05-30 03:08:14.499 | INFO     | ui.main_window:_initialize_core_components:79 - MainWindow: 初始化核心组件...
2025-05-30 03:08:14.500 | INFO     | app.services.account_service:__init__:42 - 账户服务初始化
2025-05-30 03:08:14.500 | INFO     | app.controllers.account_controller:__init__:81 - 账户控制器初始化
2025-05-30 03:08:14.500 | INFO     | core.proxy.proxy_task_manager:run:284 - 代理任务管理器线程启动
2025-05-30 03:08:14.500 | INFO     | app.services.monitor_service:__init__:39 - 初始化监控任务服务
2025-05-30 03:08:14.501 | INFO     | core.monitor.filter:__init__:26 - 初始化消息过滤器
2025-05-30 03:08:14.501 | INFO     | core.monitor.extractor:__init__:24 - 初始化关键词提取器
2025-05-30 03:08:14.502 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-05-30 03:08:14.502 | INFO     | core.monitor.notifier:__init__:90 - 初始化通知管理器
2025-05-30 03:08:14.502 | INFO     | app.controllers.telegram_monitor_controller:__init__:64 - 初始化 Telegram 监控控制器
2025-05-30 03:08:14.503 | DEBUG    | app.controllers.telegram_monitor_controller:__init__:82 - 控制器初始化完成
2025-05-30 03:08:14.673 | INFO     | ui.views.monitor_view:__init__:48 - 初始化监控视图
2025-05-30 03:08:14.674 | DEBUG    | ui.views.monitor_view:_connect_signals:80 - 连接控制器信号
2025-05-30 03:08:14.835 | INFO     | ui.views.proxy_view:__init__:96 - 初始化视图，加载第一页代理数据
2025-05-30 03:08:15.022 | INFO     | __main__:on_auth_completed:97 - 主窗口已启动
2025-05-30 03:08:15.068 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:88 - 尝试连接监听日志信号...
2025-05-30 03:08:15.069 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:90 - 监听日志信号连接成功
2025-05-30 03:08:15.070 | INFO     | core.auth.api_service:verify_vip:425 - 会员验证接口调用
2025-05-30 03:08:15.071 | DEBUG    | utils.client_http:post:68 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/vip
2025-05-30 03:08:15.072 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-05-30 03:08:15.075 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-05-30 03:08:15.079 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:145 - 加载监控任务列表 (搜索关键词: '')
2025-05-30 03:08:15.079 | DEBUG    | ui.views.monitor_view:_on_loading_started:620 - 加载中: 正在加载任务列表...
2025-05-30 03:08:15.080 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-05-30 03:08:15.086 | INFO     | app.services.proxy_service:refresh_proxies:712 - 刷新代理列表: offset=0, limit=20
2025-05-30 03:08:15.087 | INFO     | core.proxy.proxy_task_manager:submit_task:172 - 提交耗时任务到子线程: refresh_proxies_0_20
2025-05-30 03:08:15.087 | DEBUG    | app.services.proxy_service:get_proxies:500 - 获取代理列表: offset=0, limit=20
2025-05-30 03:08:15.088 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-05-30 03:08:15.088 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-05-30 03:08:15.092 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-05-30 03:08:15.114 | INFO     | app.controllers.invite_controller:get_invite_tasks:38 - 获取邀请任务列表, status=None
2025-05-30 03:08:15.115 | INFO     | app.services.invite_service:get_invite_tasks:121 - 获取邀请任务列表, status=None
2025-05-30 03:08:15.117 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-05-30 03:08:15.118 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-05-30 03:08:15.118 | INFO     | app.services.invite_service:task_initialize:39 - 初始化邀请服务，重置运行中的任务为暂停状态
2025-05-30 03:08:15.119 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-05-30 03:08:15.120 | INFO     | core.proxy.proxy_task_manager:task_wrapper:199 - 任务 refresh_proxies_0_20 完成
2025-05-30 03:08:15.325 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-05-30 03:08:15.330 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-05-30 03:08:15.331 | INFO     | app.services.invite_service:task_initialize:48 - 成功重置 0 个任务状态
2025-05-30 03:08:15.332 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-05-30 03:08:15.334 | INFO     | app.services.account_service:get_all_groups:96 - 获取所有账户分组成功, 共 1 个
2025-05-30 03:08:15.334 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-05-30 03:08:15.336 | DEBUG    | ui.views.monitor_view:_refresh_task_list:175 - 刷新任务列表，共2个任务
2025-05-30 03:08:15.341 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-05-30 03:08:15.347 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-05-30 03:08:15.350 | DEBUG    | ui.views.account_view:_on_groups_loaded:442 - 分组加载完成: 1个分组
2025-05-30 03:08:15.358 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-05-30 03:08:15.363 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-05-30 03:08:15.367 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-05-30 03:08:15.423 | INFO     | app.services.account_service:get_all_accounts:289 - 获取所有账户成功, 共 2 个
2025-05-30 03:08:15.423 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-05-30 03:08:15.429 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-05-30 03:08:15.430 | DEBUG    | ui.views.account_view:_on_accounts_loaded:575 - 账户加载完成: 2个账户
2025-05-30 03:08:15.452 | DEBUG    | ui.views.account_view:_update_group_counts:184 - 分组计数已更新: {1: 1}
2025-05-30 03:08:15.457 | INFO     | ui.views.account_view:_auto_login_accounts:630 - 开始调用控制器进行 2 个账户的自动登录。
2025-05-30 03:08:15.457 | INFO     | app.services.account_service:batch_auto_login:1153 - 服务层：开始批量自动登录 2 个账户, 最大并发: 5
2025-05-30 03:08:15.457 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-05-30 03:08:15.464 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-05-30 03:08:15.466 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-05-30 03:08:15.472 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-05-30 03:08:15.480 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-05-30 03:08:15.482 | INFO     | app.controllers.telegram_monitor_controller:_load_total_users_count_internal:191 - 成功加载总体统计数据: 总用户数 958, 今日采集 0, 日均采集 106, 运行天数 9
2025-05-30 03:08:15.483 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:156 - 成功加载 2 个任务
2025-05-30 03:08:15.483 | DEBUG    | ui.views.monitor_view:_on_loading_finished:625 - 加载完成
2025-05-30 03:08:15.489 | INFO     | app.services.account_service:batch_auto_login:1192 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-05-30 03:08:15.489 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-05-30 03:08:15.490 | INFO     | app.services.account_service:get_all_accounts:289 - 获取所有账户成功, 共 2 个
2025-05-30 03:08:15.491 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-05-30 03:08:15.492 | WARNING  | core.auth.api_service:verify_vip:441 - 会员验证失败: 验证失败
2025-05-30 03:08:15.588 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-05-30 03:08:15.725 | DEBUG    | ui.views.account_view:_update_group_counts:184 - 分组计数已更新: {1: 1}
2025-05-30 03:08:15.725 | DEBUG    | ui.views.account_view:_init_cache:151 - 账户缓存初始化完成，共 2 个账户
2025-05-30 03:08:15.729 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-05-30 03:08:15.732 | DEBUG    | ui.views.account_view:_on_accounts_loaded:575 - 账户加载完成: 2个账户
2025-05-30 03:08:15.754 | DEBUG    | ui.views.account_view:_update_group_counts:184 - 分组计数已更新: {1: 1}
2025-05-30 03:08:15.802 | INFO     | app.services.account_service:batch_auto_login:1192 - 服务层：账户 *********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-05-30 03:08:15.802 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-05-30 03:08:15.806 | INFO     | app.services.account_service:batch_auto_login:1262 - 服务层：准备调用核心层处理 2 个账户的自动登录。
2025-05-30 03:08:15.807 | INFO     | app.services.account_service:batch_auto_login:1272 - 服务层：设置核心层任务超时为 120 秒。
2025-05-30 03:08:15.807 | INFO     | core.telegram.client_manager:batch_auto_login:1220 - 开始批量自动登录, 共 2 个账户, 最大并发 5
2025-05-30 03:08:15.808 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:256 - 创建客户端: h:\PyProject\TeleTest\APPDATA\sessions\+***********.session, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-05-30 03:08:15.808 | DEBUG    | core.telegram.client_manager:get_proxy:93 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-05-30 03:08:15.809 | DEBUG    | core.telegram.client_manager:get_proxy:140 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-05-30 03:08:15.813 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:256 - 创建客户端: h:\PyProject\TeleTest\APPDATA\sessions\***********.session, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-05-30 03:08:15.815 | DEBUG    | core.telegram.client_manager:get_proxy:93 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-05-30 03:08:15.815 | DEBUG    | core.telegram.client_manager:get_proxy:140 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-05-30 03:08:15.822 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-05-30 03:08:15.979 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-05-30 03:08:15.981 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-05-30 03:08:15.983 | INFO     | ui.views.proxy_view:_load_initial_data:1044 - 初始数据加载完成，共 2 个代理
2025-05-30 03:08:19.470 | INFO     | core.telegram.client_manager:_connect_client:204 - 连接成功!
2025-05-30 03:08:20.471 | INFO     | core.telegram.client_manager:_connect_client:204 - 连接成功!
2025-05-30 03:08:20.613 | INFO     | core.telegram.client_manager:_verify_client_authorization:1195 - 账户已授权: ***********
2025-05-30 03:08:21.610 | INFO     | core.telegram.client_manager:_verify_client_authorization:1195 - 账户已授权: +***********
2025-05-30 03:08:23.613 | INFO     | core.telegram.client_manager:batch_auto_login:1369 - 批量登录完成: 总计 2, 成功 2, 失败 0
2025-05-30 03:08:23.818 | INFO     | app.services.account_service:batch_auto_login:1293 - 服务层：核心层登录完成: 成功 2, 失败 0 / 2
2025-05-30 03:08:25.749 | INFO     | ui.views.proxy_view:_on_add_proxy:451 - 用户开始添加代理: IP类型=socks5(本地), IP文本='**************'
2025-05-30 03:08:25.750 | DEBUG    | ui.views.proxy_view:_on_add_proxy:459 - 固定端口: 10000
2025-05-30 03:08:25.751 | DEBUG    | ui.views.proxy_view:_on_add_proxy:473 - 用户凭据: username=, password=None
2025-05-30 03:08:25.751 | INFO     | app.services.proxy_service:add_proxies_from_text:879 - 从文本添加代理: IP文本='**************', 端口范围=10000-10000
2025-05-30 03:08:25.751 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-05-30 03:08:25.757 | WARNING  | app.services.proxy_service:add_proxies_from_text:904 - 代理IP ************** 已存在，跳过添加
2025-05-30 03:08:25.759 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-05-30 03:08:59.092 | INFO     | ui.views.proxy_view:_on_add_proxy:451 - 用户开始添加代理: IP类型=socks5(远程), IP文本='**************'
2025-05-30 03:08:59.092 | DEBUG    | ui.views.proxy_view:_on_add_proxy:459 - 固定端口: 10000
2025-05-30 03:08:59.092 | DEBUG    | ui.views.proxy_view:_on_add_proxy:473 - 用户凭据: username=, password=None
2025-05-30 03:08:59.093 | INFO     | app.services.proxy_service:add_proxies_from_text:879 - 从文本添加代理: IP文本='**************', 端口范围=10000-10000
2025-05-30 03:08:59.093 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-05-30 03:08:59.096 | WARNING  | app.services.proxy_service:add_proxies_from_text:904 - 代理IP ************** 已存在，跳过添加
2025-05-30 03:08:59.097 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-05-30 03:09:11.639 | INFO     | ui.views.proxy_view:_on_add_proxy:451 - 用户开始添加代理: IP类型=socks5(远程), IP文本='**************'
2025-05-30 03:09:11.639 | DEBUG    | ui.views.proxy_view:_on_add_proxy:459 - 固定端口: 10001
2025-05-30 03:09:11.640 | DEBUG    | ui.views.proxy_view:_on_add_proxy:473 - 用户凭据: username=, password=None
2025-05-30 03:09:11.640 | INFO     | app.services.proxy_service:add_proxies_from_text:879 - 从文本添加代理: IP文本='**************', 端口范围=10001-10001
2025-05-30 03:09:11.640 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-05-30 03:09:11.643 | WARNING  | app.services.proxy_service:add_proxies_from_text:904 - 代理IP ************** 已存在，跳过添加
2025-05-30 03:09:11.644 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-05-30 03:09:14.458 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-05-30 03:09:14.459 | DEBUG    | utils.client_http:post:68 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-05-30 03:10:11.613 | INFO     | ui.views.proxy_view:_on_add_proxy:451 - 用户开始添加代理: IP类型=socks5(远程), IP文本='**************'
2025-05-30 03:10:11.613 | DEBUG    | ui.views.proxy_view:_on_add_proxy:459 - 固定端口: 10001
2025-05-30 03:10:11.613 | DEBUG    | ui.views.proxy_view:_on_add_proxy:473 - 用户凭据: username=, password=None
2025-05-30 03:10:11.613 | INFO     | app.services.proxy_service:add_proxies_from_text:879 - 从文本添加代理: IP文本='**************', 端口范围=10001-10001
2025-05-30 03:10:11.614 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-05-30 03:10:11.627 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-05-30 03:10:11.628 | INFO     | app.services.proxy_service:refresh_proxies:712 - 刷新代理列表: offset=0, limit=20
2025-05-30 03:10:11.628 | INFO     | core.proxy.proxy_task_manager:submit_task:172 - 提交耗时任务到子线程: refresh_proxies_0_20
2025-05-30 03:10:11.629 | DEBUG    | app.services.proxy_service:get_proxies:500 - 获取代理列表: offset=0, limit=20
2025-05-30 03:10:11.629 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-05-30 03:10:11.630 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-05-30 03:10:11.635 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-05-30 03:10:11.636 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-05-30 03:10:11.636 | INFO     | core.proxy.proxy_task_manager:task_wrapper:199 - 任务 refresh_proxies_0_20 完成
2025-05-30 03:10:11.637 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-05-30 03:10:11.641 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-05-30 03:10:11.642 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-05-30 03:10:11.648 | INFO     | app.services.proxy_service:validate_proxies:756 - 开始验证指定代理，共 1 个
2025-05-30 03:10:11.648 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-05-30 03:10:11.681 | INFO     | core.proxy.proxy_core_service:validate_proxies_stream:167 - 批量验证代理(流式): 1个
2025-05-30 03:10:11.681 | INFO     | core.proxy.proxy_validator:validate_proxy:43 - 正在验证代理 socks5://**************:10001
2025-05-30 03:10:13.498 | INFO     | core.proxy.proxy_validator:validate_proxy:68 - 代理验证成功: socks5://**************:10001, 耗时: 1815.31ms
2025-05-30 03:10:13.507 | INFO     | core.proxy.proxy_core_service:validate_proxies_stream:232 - 批量代理验证完成，结果: 1/1 个有效
2025-05-30 03:10:13.508 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-05-30 03:10:13.509 | INFO     | app.services.proxy_service:refresh_proxies:712 - 刷新代理列表: offset=0, limit=20
2025-05-30 03:10:13.509 | INFO     | core.proxy.proxy_task_manager:submit_task:172 - 提交耗时任务到子线程: refresh_proxies_0_20
2025-05-30 03:10:13.509 | DEBUG    | app.services.proxy_service:get_proxies:500 - 获取代理列表: offset=0, limit=20
2025-05-30 03:10:13.509 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-05-30 03:10:13.510 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-05-30 03:10:13.517 | INFO     | ui.views.proxy_view:load_proxies_by_page:525 - 加载代理页面: 1
2025-05-30 03:10:13.518 | INFO     | app.services.proxy_service:refresh_proxies:712 - 刷新代理列表: offset=0, limit=20
2025-05-30 03:10:13.519 | INFO     | app.services.proxy_service:refresh_proxies:719 - 刷新任务已在运行，跳过
2025-05-30 03:10:13.519 | DEBUG    | app.services.proxy_service:get_proxies:500 - 获取代理列表: offset=0, limit=20
2025-05-30 03:10:13.520 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-05-30 03:10:13.520 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-05-30 03:10:13.522 | INFO     | ui.views.proxy_view:load_proxies_by_page:525 - 加载代理页面: 1
2025-05-30 03:10:13.522 | INFO     | core.proxy.proxy_task_manager:task_wrapper:199 - 任务 refresh_proxies_0_20 完成
2025-05-30 03:10:13.522 | INFO     | app.services.proxy_service:refresh_proxies:712 - 刷新代理列表: offset=0, limit=20
2025-05-30 03:10:13.523 | INFO     | core.proxy.proxy_task_manager:submit_task:172 - 提交耗时任务到子线程: refresh_proxies_0_20
2025-05-30 03:10:13.523 | DEBUG    | app.services.proxy_service:get_proxies:500 - 获取代理列表: offset=0, limit=20
2025-05-30 03:10:13.523 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-05-30 03:10:13.524 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-05-30 03:10:13.530 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-05-30 03:10:13.535 | INFO     | core.proxy.proxy_task_manager:task_wrapper:199 - 任务 refresh_proxies_0_20 完成
2025-05-30 03:10:13.554 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-05-30 03:10:13.555 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-05-30 03:10:13.556 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-05-30 03:10:13.558 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-05-30 03:10:13.559 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-05-30 03:10:13.560 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-05-30 03:10:13.567 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-05-30 03:10:13.567 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-05-30 03:10:13.568 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-05-30 03:10:13.570 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-05-30 03:10:13.574 | INFO     | ui.views.proxy_view:load_proxies_by_page:525 - 加载代理页面: 1
2025-05-30 03:10:13.575 | INFO     | app.services.proxy_service:refresh_proxies:712 - 刷新代理列表: offset=0, limit=20
2025-05-30 03:10:13.575 | INFO     | core.proxy.proxy_task_manager:submit_task:172 - 提交耗时任务到子线程: refresh_proxies_0_20
2025-05-30 03:10:13.576 | DEBUG    | app.services.proxy_service:get_proxies:500 - 获取代理列表: offset=0, limit=20
2025-05-30 03:10:13.576 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-05-30 03:10:13.576 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-05-30 03:10:13.585 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-05-30 03:10:13.592 | INFO     | core.proxy.proxy_task_manager:task_wrapper:199 - 任务 refresh_proxies_0_20 完成
2025-05-30 03:10:13.596 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-05-30 03:10:13.599 | INFO     | ui.views.proxy_view:load_proxies_by_page:538 - 页面 1 加载完成，显示 3 条记录，共 3 条
2025-05-30 03:10:13.620 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-05-30 03:10:13.624 | INFO     | ui.views.proxy_view:load_proxies_by_page:538 - 页面 1 加载完成，显示 3 条记录，共 3 条
2025-05-30 03:10:13.665 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-05-30 03:10:13.666 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-05-30 03:10:13.671 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-05-30 03:10:13.673 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-05-30 03:10:13.676 | INFO     | ui.views.proxy_view:load_proxies_by_page:538 - 页面 1 加载完成，显示 3 条记录，共 3 条
2025-05-30 03:10:13.677 | INFO     | ui.views.proxy_view:_on_add_proxy:509 - 已清空输入框，代理添加和验证流程完成
2025-05-30 03:10:14.460 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-05-30 03:10:14.460 | DEBUG    | utils.client_http:post:68 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-05-30 03:11:14.465 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-05-30 03:11:14.466 | DEBUG    | utils.client_http:post:68 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-05-30 03:12:14.458 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-05-30 03:12:14.459 | DEBUG    | utils.client_http:post:68 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-05-30 03:13:14.458 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-05-30 03:13:14.459 | DEBUG    | utils.client_http:post:68 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-05-30 03:14:14.459 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-05-30 03:14:14.460 | DEBUG    | utils.client_http:post:68 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-05-30 03:15:14.462 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-05-30 03:15:14.462 | DEBUG    | utils.client_http:post:68 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-05-30 03:16:14.460 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-05-30 03:16:14.461 | DEBUG    | utils.client_http:post:68 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-05-30 03:17:14.464 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-05-30 03:17:14.464 | DEBUG    | utils.client_http:post:68 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-05-30 03:18:14.463 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-05-30 03:18:14.464 | DEBUG    | utils.client_http:post:68 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-05-30 03:19:14.463 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-05-30 03:19:14.464 | DEBUG    | utils.client_http:post:68 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-05-30 03:20:14.462 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-05-30 03:20:14.463 | DEBUG    | utils.client_http:post:68 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-05-30 03:21:14.459 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-05-30 03:21:14.460 | DEBUG    | utils.client_http:post:68 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-05-30 03:22:14.465 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-05-30 03:22:14.465 | DEBUG    | utils.client_http:post:68 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-05-30 03:23:14.458 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-05-30 03:23:14.458 | DEBUG    | utils.client_http:post:68 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-05-30 03:24:14.461 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-05-30 03:24:14.461 | DEBUG    | utils.client_http:post:68 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-05-30 03:25:14.460 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-05-30 03:25:14.461 | DEBUG    | utils.client_http:post:68 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-05-30 03:26:14.464 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-05-30 03:26:14.465 | DEBUG    | utils.client_http:post:68 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-05-30 03:27:14.461 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-05-30 03:27:14.462 | DEBUG    | utils.client_http:post:68 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-05-30 03:28:14.460 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-05-30 03:28:14.461 | DEBUG    | utils.client_http:post:68 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-05-30 03:29:14.459 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-05-30 03:29:14.460 | DEBUG    | utils.client_http:post:68 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-05-30 03:30:14.461 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-05-30 03:30:14.461 | DEBUG    | utils.client_http:post:68 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-05-30 03:30:40.397 | INFO     | ui.views.proxy_view:_on_export_proxies_clicked:1217 - 用户点击导出代理按钮
2025-05-30 03:30:41.998 | INFO     | ui.views.proxy_view:_on_table_double_clicked:1172 - 已复制代理配置: socks5://**************:10001
2025-05-30 03:31:04.433 | INFO     | ui.main_window:closeEvent:335 - MainWindow: 接收到关闭事件
2025-05-30 03:31:04.449 | INFO     | ui.main_window:_cleanup_before_quit:228 - MainWindow: 执行清理资源...
2025-05-30 03:31:04.450 | INFO     | core.telegram.client_worker:stop:110 - 正在停止Telegram客户端工作线程
2025-05-30 03:31:04.457 | INFO     | core.telegram.client_manager:cleanup_async:1140 - 开始清理资源并断开所有连接
2025-05-30 03:31:04.458 | INFO     | core.telegram.client_manager:disconnect_all_clients:725 - 正在断开所有客户端连接
2025-05-30 03:31:04.459 | INFO     | core.telegram.client_manager:_safe_disconnect:746 - 正在断开客户端连接: ***********
2025-05-30 03:31:04.459 | INFO     | core.telegram.client_manager:_safe_disconnect:752 - 客户端已断开连接: ***********
2025-05-30 03:31:04.459 | INFO     | core.telegram.client_manager:_safe_disconnect:746 - 正在断开客户端连接: +***********
2025-05-30 03:31:04.460 | INFO     | core.telegram.client_manager:_safe_disconnect:752 - 客户端已断开连接: +***********
2025-05-30 03:31:04.460 | INFO     | core.telegram.client_manager:cleanup_async:1159 - 资源清理完成
2025-05-30 03:31:04.960 | INFO     | core.telegram.client_manager:cleanup_async:1140 - 开始清理资源并断开所有连接
2025-05-30 03:31:04.960 | INFO     | core.telegram.client_manager:disconnect_all_clients:725 - 正在断开所有客户端连接
2025-05-30 03:31:04.961 | INFO     | core.telegram.client_manager:cleanup_async:1159 - 资源清理完成
2025-05-30 03:31:05.463 | INFO     | core.telegram.client_worker:run:99 - Telegram客户端工作线程已退出
2025-05-30 03:31:05.464 | INFO     | core.telegram.client_worker:stop:124 - Telegram客户端工作线程已停止
2025-05-30 03:31:05.464 | INFO     | ui.main_window:_cleanup_before_quit:237 - TelegramClientWorker 已停止。
2025-05-30 03:31:05.464 | INFO     | ui.main_window:_cleanup_before_quit:241 - MainWindow 清理完成
2025-05-30 03:31:05.482 | INFO     | __main__:main:111 - 应用程序已正常退出
2025-05-30 12:17:18.201 | INFO     | utils.logger:init_logger:98 - 日志系统初始化完成，默认级别: DEBUG，日志目录: h:\PyProject\TeleTest\APPDATA\logs
2025-05-30 12:17:21.702 | INFO     | data.database:init_database:31 - 初始化数据库连接: h:\PyProject\TeleTest\APPDATA\database\telegram_manager.db
2025-05-30 12:17:21.729 | DEBUG    | data.database:init_database:58 - 创建数据库表
2025-05-30 12:17:21.744 | INFO     | data.database:init_database:61 - 数据库初始化完成
2025-05-30 12:17:22.984 | DEBUG    | app.controllers.auth_controller:initialize_software:43 - 控制器: 初始化软件 app_id=1000, version=1.0
2025-05-30 12:17:22.984 | DEBUG    | utils.client_http:get:41 - 发送GET请求: https://server.xile188.com/api/user/1000/windowns10/1.0/ini
2025-05-30 12:17:23.422 | DEBUG    | core.auth.api_service:init_software:66 - 初始化数据解密成功: {'new_content': '1.多账户管理\n2.ip池管理\n3.消息监听\n4.消息群发\n5.拉人入群', 'new_url': None, 'notice': {'id': '2', 'content': '公告内容测试3', 'time': '**********'}}
2025-05-30 12:17:23.430 | INFO     | app.controllers.auth_controller:login:61 - 控制器: 用户登录 account=zhangsan03
2025-05-30 12:17:26.413 | INFO     | core.auth.api_service:login:127 - 用户登录: account=zhangsan03
2025-05-30 12:17:26.414 | DEBUG    | utils.client_http:post:68 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/logon
2025-05-30 12:17:26.584 | INFO     | core.auth.api_service:get_user_info:347 - 获取用户信息
2025-05-30 12:17:26.584 | DEBUG    | utils.client_http:post:68 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/info
2025-05-30 12:17:26.771 | INFO     | core.auth.api_service:get_user_info:375 - 获取用户信息成功
2025-05-30 12:17:26.778 | INFO     | __main__:on_auth_completed:93 - 用户登录成功，正在启动主窗口...
2025-05-30 12:17:26.794 | INFO     | core.telegram.client_worker:__init__:63 - Telegram客户端工作线程初始化
2025-05-30 12:17:26.796 | INFO     | core.telegram.client_worker:run:67 - Telegram客户端工作线程开始运行
2025-05-30 12:17:26.801 | INFO     | core.telegram.client_manager:__init__:66 - Telegram客户端管理器初始化
2025-05-30 12:17:26.801 | INFO     | core.telegram.user_manager:__init__:31 - Telegram用户管理器初始化
2025-05-30 12:17:26.802 | INFO     | core.telegram.group_manager:__init__:36 - Telegram群组管理器初始化
2025-05-30 12:17:26.802 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-05-30 12:17:26.802 | INFO     | core.telegram.monitor_manager:__init__:26 - Telegram监控管理器初始化
2025-05-30 12:17:26.803 | INFO     | core.telegram.telegram_service:__init__:39 - Telegram服务初始化完成
2025-05-30 12:17:26.804 | INFO     | ui.main_window:_initialize_core_components:79 - MainWindow: 初始化核心组件...
2025-05-30 12:17:26.805 | INFO     | app.services.account_service:__init__:42 - 账户服务初始化
2025-05-30 12:17:26.805 | INFO     | core.proxy.proxy_task_manager:run:284 - 代理任务管理器线程启动
2025-05-30 12:17:26.806 | INFO     | app.controllers.account_controller:__init__:81 - 账户控制器初始化
2025-05-30 12:17:26.806 | INFO     | app.services.monitor_service:__init__:39 - 初始化监控任务服务
2025-05-30 12:17:26.806 | INFO     | core.monitor.filter:__init__:26 - 初始化消息过滤器
2025-05-30 12:17:26.807 | INFO     | core.monitor.extractor:__init__:24 - 初始化关键词提取器
2025-05-30 12:17:26.807 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-05-30 12:17:26.807 | INFO     | core.monitor.notifier:__init__:90 - 初始化通知管理器
2025-05-30 12:17:26.808 | INFO     | app.controllers.telegram_monitor_controller:__init__:64 - 初始化 Telegram 监控控制器
2025-05-30 12:17:26.808 | DEBUG    | app.controllers.telegram_monitor_controller:__init__:82 - 控制器初始化完成
2025-05-30 12:17:27.018 | INFO     | ui.views.monitor_view:__init__:48 - 初始化监控视图
2025-05-30 12:17:27.019 | DEBUG    | ui.views.monitor_view:_connect_signals:80 - 连接控制器信号
2025-05-30 12:17:27.208 | INFO     | ui.views.proxy_view:__init__:96 - 初始化视图，加载第一页代理数据
2025-05-30 12:17:27.404 | INFO     | __main__:on_auth_completed:97 - 主窗口已启动
2025-05-30 12:17:27.468 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:88 - 尝试连接监听日志信号...
2025-05-30 12:17:27.468 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:90 - 监听日志信号连接成功
2025-05-30 12:17:27.469 | INFO     | core.auth.api_service:verify_vip:425 - 会员验证接口调用
2025-05-30 12:17:27.470 | DEBUG    | utils.client_http:post:68 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/vip
2025-05-30 12:17:27.471 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-05-30 12:17:27.474 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-05-30 12:17:27.477 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:145 - 加载监控任务列表 (搜索关键词: '')
2025-05-30 12:17:27.478 | DEBUG    | ui.views.monitor_view:_on_loading_started:620 - 加载中: 正在加载任务列表...
2025-05-30 12:17:27.478 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-05-30 12:17:27.485 | INFO     | app.services.proxy_service:refresh_proxies:712 - 刷新代理列表: offset=0, limit=20
2025-05-30 12:17:27.486 | INFO     | core.proxy.proxy_task_manager:submit_task:172 - 提交耗时任务到子线程: refresh_proxies_0_20
2025-05-30 12:17:27.487 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-05-30 12:17:27.487 | DEBUG    | app.services.proxy_service:get_proxies:500 - 获取代理列表: offset=0, limit=20
2025-05-30 12:17:27.488 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-05-30 12:17:27.516 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-05-30 12:17:27.520 | INFO     | app.controllers.invite_controller:get_invite_tasks:38 - 获取邀请任务列表, status=None
2025-05-30 12:17:27.522 | INFO     | app.services.invite_service:get_invite_tasks:121 - 获取邀请任务列表, status=None
2025-05-30 12:17:27.522 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-05-30 12:17:27.524 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-05-30 12:17:27.524 | INFO     | app.services.invite_service:task_initialize:39 - 初始化邀请服务，重置运行中的任务为暂停状态
2025-05-30 12:17:27.525 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-05-30 12:17:27.526 | INFO     | core.proxy.proxy_task_manager:task_wrapper:199 - 任务 refresh_proxies_0_20 完成
2025-05-30 12:17:27.632 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-05-30 12:17:27.632 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-05-30 12:17:27.636 | INFO     | app.services.invite_service:task_initialize:48 - 成功重置 0 个任务状态
2025-05-30 12:17:27.637 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-05-30 12:17:27.637 | DEBUG    | ui.views.monitor_view:_refresh_task_list:175 - 刷新任务列表，共2个任务
2025-05-30 12:17:27.643 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-05-30 12:17:27.651 | INFO     | app.services.account_service:get_all_groups:96 - 获取所有账户分组成功, 共 1 个
2025-05-30 12:17:27.651 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-05-30 12:17:27.654 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-05-30 12:17:27.656 | DEBUG    | ui.views.account_view:_on_groups_loaded:442 - 分组加载完成: 1个分组
2025-05-30 12:17:27.664 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-05-30 12:17:27.668 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-05-30 12:17:27.671 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-05-30 12:17:27.741 | INFO     | app.services.account_service:get_all_accounts:289 - 获取所有账户成功, 共 2 个
2025-05-30 12:17:27.742 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-05-30 12:17:27.744 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-05-30 12:17:27.744 | DEBUG    | ui.views.account_view:_on_accounts_loaded:575 - 账户加载完成: 2个账户
2025-05-30 12:17:27.764 | DEBUG    | ui.views.account_view:_update_group_counts:184 - 分组计数已更新: {1: 1}
2025-05-30 12:17:27.769 | INFO     | ui.views.account_view:_auto_login_accounts:630 - 开始调用控制器进行 2 个账户的自动登录。
2025-05-30 12:17:27.769 | INFO     | app.services.account_service:batch_auto_login:1153 - 服务层：开始批量自动登录 2 个账户, 最大并发: 5
2025-05-30 12:17:27.770 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-05-30 12:17:27.774 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-05-30 12:17:27.775 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-05-30 12:17:27.777 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-05-30 12:17:27.781 | INFO     | app.controllers.telegram_monitor_controller:_load_total_users_count_internal:191 - 成功加载总体统计数据: 总用户数 958, 今日采集 0, 日均采集 106, 运行天数 9
2025-05-30 12:17:27.781 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:156 - 成功加载 2 个任务
2025-05-30 12:17:27.782 | DEBUG    | ui.views.monitor_view:_on_loading_finished:625 - 加载完成
2025-05-30 12:17:27.785 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-05-30 12:17:27.814 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-05-30 12:17:27.819 | WARNING  | core.auth.api_service:verify_vip:441 - 会员验证失败: 验证失败
2025-05-30 12:17:27.905 | INFO     | app.services.account_service:get_all_accounts:289 - 获取所有账户成功, 共 2 个
2025-05-30 12:17:27.906 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-05-30 12:17:27.954 | INFO     | app.services.account_service:batch_auto_login:1192 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-05-30 12:17:27.955 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-05-30 12:17:28.083 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=True
2025-05-30 12:17:28.085 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=True
2025-05-30 12:17:28.087 | INFO     | ui.views.proxy_view:_load_initial_data:1044 - 初始数据加载完成，共 3 个代理
2025-05-30 12:17:28.088 | DEBUG    | ui.views.account_view:_update_group_counts:184 - 分组计数已更新: {1: 1}
2025-05-30 12:17:28.088 | DEBUG    | ui.views.account_view:_init_cache:151 - 账户缓存初始化完成，共 2 个账户
2025-05-30 12:17:28.092 | DEBUG    | ui.views.account_view:_on_accounts_loaded:575 - 账户加载完成: 2个账户
2025-05-30 12:17:28.113 | DEBUG    | ui.views.account_view:_update_group_counts:184 - 分组计数已更新: {1: 1}
2025-05-30 12:17:28.120 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-05-30 12:17:28.125 | INFO     | app.services.account_service:batch_auto_login:1192 - 服务层：账户 *********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-05-30 12:17:28.126 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-05-30 12:17:28.127 | INFO     | app.services.account_service:batch_auto_login:1262 - 服务层：准备调用核心层处理 2 个账户的自动登录。
2025-05-30 12:17:28.128 | INFO     | app.services.account_service:batch_auto_login:1272 - 服务层：设置核心层任务超时为 120 秒。
2025-05-30 12:17:28.128 | INFO     | core.telegram.client_manager:batch_auto_login:1220 - 开始批量自动登录, 共 2 个账户, 最大并发 5
2025-05-30 12:17:28.129 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:256 - 创建客户端: h:\PyProject\TeleTest\APPDATA\sessions\+***********.session, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-05-30 12:17:28.129 | DEBUG    | core.telegram.client_manager:get_proxy:93 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-05-30 12:17:28.130 | DEBUG    | core.telegram.client_manager:get_proxy:140 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-05-30 12:17:28.134 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-05-30 12:17:28.135 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:256 - 创建客户端: h:\PyProject\TeleTest\APPDATA\sessions\***********.session, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-05-30 12:17:28.135 | DEBUG    | core.telegram.client_manager:get_proxy:93 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-05-30 12:17:28.136 | DEBUG    | core.telegram.client_manager:get_proxy:140 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-05-30 12:17:29.671 | ERROR    | core.telegram.client_manager:_connect_client:217 - 认证密钥错误 (AuthKeyError): The authorization key (session file) was used under two different IP addresses simultaneously, and can no longer be used. Use the same session exclusively, or use different sessions (caused by InvokeWithLayerRequest(InitConnectionRequest(GetConfigRequest)))
2025-05-30 12:17:29.841 | ERROR    | core.telegram.client_manager:_connect_client:217 - 认证密钥错误 (AuthKeyError): The authorization key (session file) was used under two different IP addresses simultaneously, and can no longer be used. Use the same session exclusively, or use different sessions (caused by InvokeWithLayerRequest(InitConnectionRequest(GetConfigRequest)))
2025-05-30 12:17:31.852 | INFO     | core.telegram.client_manager:batch_auto_login:1369 - 批量登录完成: 总计 2, 成功 0, 失败 2
2025-05-30 12:17:32.132 | INFO     | app.services.account_service:batch_auto_login:1293 - 服务层：核心层登录完成: 成功 0, 失败 2 / 2
2025-05-30 12:17:47.522 | INFO     | ui.views.add_msg_task_view:_load_initial_data:136 - 加载初始数据
2025-05-30 12:17:47.565 | INFO     | ui.views.add_msg_task_view:_load_account_groups:146 - 加载账户分组
2025-05-30 12:17:47.566 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-05-30 12:17:47.574 | INFO     | app.services.account_service:get_all_groups:96 - 获取所有账户分组成功, 共 1 个
2025-05-30 12:17:47.575 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-05-30 12:17:47.576 | DEBUG    | ui.views.account_view:_on_groups_loaded:442 - 分组加载完成: 1个分组
2025-05-30 12:17:47.577 | INFO     | ui.views.add_msg_task_view:_load_account_groups:162 - 加载账户分组数据: 1个分组
2025-05-30 12:17:47.578 | INFO     | ui.views.add_msg_task_view:_load_accounts_by_group:177 - 根据分组ID加载账户: -1
2025-05-30 12:17:47.578 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-05-30 12:17:47.701 | INFO     | app.services.account_service:get_all_accounts:289 - 获取所有账户成功, 共 2 个
2025-05-30 12:17:47.701 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-05-30 12:17:47.726 | INFO     | ui.views.add_msg_task_view:_load_accounts_by_group:213 - 已加载 2 个账户到列表
2025-05-30 12:17:47.726 | DEBUG    | ui.views.account_view:_on_accounts_loaded:575 - 账户加载完成: 2个账户
2025-05-30 12:17:47.748 | DEBUG    | ui.views.account_view:_update_group_counts:184 - 分组计数已更新: {1: 1}
2025-05-30 12:17:54.656 | INFO     | ui.views.add_msg_task_view:_toggleSelectAllGroups:362 - 全选账户
2025-05-30 12:17:56.481 | INFO     | app.controllers.send_msg_controller:create_task:174 - 创建任务: 测试消息3
2025-05-30 12:17:56.482 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-05-30 12:17:56.487 | INFO     | data.repositories.message_repo:add_task:36 - 消息任务已添加, ID: 2, 名称: 测试消息3, 账户切换时间: 10-30秒, 消息间隔: 60-180秒
2025-05-30 12:17:56.500 | INFO     | app.controllers.send_msg_controller:_on_task_created:49 - 收到任务创建事件: 2
2025-05-30 12:17:56.501 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-05-30 12:17:56.501 | INFO     | app.controllers.send_msg_controller:create_task:180 - 任务创建成功: ID=2, 名称=测试消息3
2025-05-30 12:17:56.522 | INFO     | ui.views.send_msg_view:_on_task_created:724 - 收到任务创建事件: 2
2025-05-30 12:17:56.569 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-05-30 12:17:56.727 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-05-30 12:17:56.754 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-05-30 12:17:56.787 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-05-30 12:17:56.789 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-05-30 12:17:56.804 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-05-30 12:18:19.609 | INFO     | ui.views.invite_view:_on_create_task_clicked:164 - 点击创建新任务按钮
2025-05-30 12:18:19.719 | INFO     | ui.views.add_invite_task_view:_load_initial_data:65 - 加载初始数据
2025-05-30 12:18:19.759 | INFO     | ui.views.add_invite_task_view:_load_account_groups:75 - 加载账户分组
2025-05-30 12:18:19.759 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-05-30 12:18:19.823 | INFO     | app.services.account_service:get_all_groups:96 - 获取所有账户分组成功, 共 1 个
2025-05-30 12:18:19.824 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-05-30 12:18:19.837 | DEBUG    | ui.views.account_view:_on_groups_loaded:442 - 分组加载完成: 1个分组
2025-05-30 12:18:19.838 | INFO     | ui.views.add_invite_task_view:_load_account_groups:91 - 加载账户分组数据: 1个分组
2025-05-30 12:18:19.839 | INFO     | ui.views.add_invite_task_view:_load_accounts_by_group:106 - 根据分组ID加载账户: -1
2025-05-30 12:18:19.839 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-05-30 12:18:19.915 | INFO     | app.services.account_service:get_all_accounts:289 - 获取所有账户成功, 共 2 个
2025-05-30 12:18:19.915 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-05-30 12:18:19.917 | INFO     | ui.views.add_invite_task_view:_load_accounts_by_group:142 - 已加载 2 个账户到列表
2025-05-30 12:18:19.918 | DEBUG    | ui.views.account_view:_on_accounts_loaded:575 - 账户加载完成: 2个账户
2025-05-30 12:18:19.938 | DEBUG    | ui.views.account_view:_update_group_counts:184 - 分组计数已更新: {1: 1}
2025-05-30 12:18:26.773 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-05-30 12:18:26.773 | DEBUG    | utils.client_http:post:68 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-05-30 12:18:56.697 | INFO     | ui.views.proxy_view:_on_export_proxies_clicked:1217 - 用户点击导出代理按钮
2025-05-30 12:19:04.863 | INFO     | ui.views.proxy_view:_on_table_double_clicked:1172 - 已复制代理配置: socks5://127.0.0.1:1080
2025-05-30 12:19:26.772 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-05-30 12:19:26.772 | DEBUG    | utils.client_http:post:68 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-05-30 12:20:26.771 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-05-30 12:20:26.771 | DEBUG    | utils.client_http:post:68 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-05-30 12:21:26.771 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-05-30 12:21:26.772 | DEBUG    | utils.client_http:post:68 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-05-30 12:22:26.774 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-05-30 12:22:26.774 | DEBUG    | utils.client_http:post:68 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-05-30 12:23:26.776 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-05-30 12:23:26.776 | DEBUG    | utils.client_http:post:68 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-05-30 12:24:26.775 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-05-30 12:24:26.776 | DEBUG    | utils.client_http:post:68 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-05-30 12:25:26.771 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-05-30 12:25:26.771 | DEBUG    | utils.client_http:post:68 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-05-30 12:25:34.498 | INFO     | ui.views.proxy_view:_on_toggle_service:255 - 用户点击切换代理服务状态
2025-05-30 12:25:34.521 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=True
2025-05-30 12:25:34.525 | INFO     | app.services.proxy_service:stop_service:578 - 停止代理服务
2025-05-30 12:25:34.526 | INFO     | core.proxy.proxy_task_manager:submit_task:172 - 提交耗时任务到子线程: stop_service
2025-05-30 12:25:34.527 | INFO     | core.proxy.proxy_core_service:stop_proxy_service:316 - 停止代理服务
2025-05-30 12:25:36.798 | INFO     | core.proxy.proxy_core_service:stop_proxy_service:324 - 代理服务停止成功: 服务已停止
2025-05-30 12:25:36.798 | INFO     | core.proxy.proxy_task_manager:task_wrapper:199 - 任务 stop_service 完成
2025-05-30 12:25:36.799 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-05-30 12:26:26.772 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-05-30 12:26:26.773 | DEBUG    | utils.client_http:post:68 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-05-30 12:27:26.772 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-05-30 12:27:26.772 | DEBUG    | utils.client_http:post:68 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-05-30 12:28:26.771 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-05-30 12:28:26.772 | DEBUG    | utils.client_http:post:68 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-05-30 12:29:26.776 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-05-30 12:29:26.776 | DEBUG    | utils.client_http:post:68 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-05-30 12:30:26.774 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-05-30 12:30:26.774 | DEBUG    | utils.client_http:post:68 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-05-30 12:31:26.771 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-05-30 12:31:26.771 | DEBUG    | utils.client_http:post:68 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-05-30 12:32:26.774 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-05-30 12:32:26.775 | DEBUG    | utils.client_http:post:68 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-05-30 12:33:26.776 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-05-30 12:33:26.776 | DEBUG    | utils.client_http:post:68 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-05-30 12:34:26.772 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-05-30 12:34:26.772 | DEBUG    | utils.client_http:post:68 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-05-30 12:34:57.905 | INFO     | ui.main_window:closeEvent:335 - MainWindow: 接收到关闭事件
2025-05-30 12:34:57.922 | INFO     | ui.main_window:_cleanup_before_quit:228 - MainWindow: 执行清理资源...
2025-05-30 12:34:57.923 | INFO     | core.telegram.client_worker:stop:110 - 正在停止Telegram客户端工作线程
2025-05-30 12:34:57.932 | INFO     | core.telegram.client_manager:cleanup_async:1140 - 开始清理资源并断开所有连接
2025-05-30 12:34:57.933 | INFO     | core.telegram.client_manager:disconnect_all_clients:725 - 正在断开所有客户端连接
2025-05-30 12:34:57.934 | INFO     | core.telegram.client_manager:cleanup_async:1159 - 资源清理完成
2025-05-30 12:34:58.435 | INFO     | core.telegram.client_manager:cleanup_async:1140 - 开始清理资源并断开所有连接
2025-05-30 12:34:58.435 | INFO     | core.telegram.client_manager:disconnect_all_clients:725 - 正在断开所有客户端连接
2025-05-30 12:34:58.436 | INFO     | core.telegram.client_manager:cleanup_async:1159 - 资源清理完成
2025-05-30 12:34:58.937 | INFO     | core.telegram.client_worker:run:99 - Telegram客户端工作线程已退出
2025-05-30 12:34:58.938 | INFO     | core.telegram.client_worker:stop:124 - Telegram客户端工作线程已停止
2025-05-30 12:34:58.938 | INFO     | ui.main_window:_cleanup_before_quit:237 - TelegramClientWorker 已停止。
2025-05-30 12:34:58.939 | INFO     | ui.main_window:_cleanup_before_quit:241 - MainWindow 清理完成
2025-05-30 12:34:58.956 | INFO     | __main__:main:111 - 应用程序已正常退出
2025-05-30 14:17:50.552 | INFO     | utils.logger:init_logger:98 - 日志系统初始化完成，默认级别: DEBUG，日志目录: h:\PyProject\TeleTest\APPDATA\logs
2025-05-30 14:17:52.040 | INFO     | data.database:init_database:31 - 初始化数据库连接: h:\PyProject\TeleTest\APPDATA\database\telegram_manager.db
2025-05-30 14:17:52.058 | DEBUG    | data.database:init_database:58 - 创建数据库表
2025-05-30 14:17:52.069 | INFO     | data.database:init_database:61 - 数据库初始化完成
2025-05-30 14:17:53.660 | DEBUG    | app.controllers.auth_controller:initialize_software:43 - 控制器: 初始化软件 app_id=1000, version=1.0
2025-05-30 14:17:53.661 | DEBUG    | utils.client_http:get:41 - 发送GET请求: https://server.xile188.com/api/user/1000/windowns10/1.0/ini
2025-05-30 14:17:54.082 | DEBUG    | core.auth.api_service:init_software:66 - 初始化数据解密成功: {'new_content': '1.多账户管理\n2.ip池管理\n3.消息监听\n4.消息群发\n5.拉人入群', 'new_url': None, 'notice': {'id': '2', 'content': '公告内容测试3', 'time': '**********'}}
2025-05-30 14:17:54.090 | INFO     | app.controllers.auth_controller:login:61 - 控制器: 用户登录 account=zhangsan03
2025-05-30 14:17:57.128 | INFO     | core.auth.api_service:login:127 - 用户登录: account=zhangsan03
2025-05-30 14:17:57.129 | DEBUG    | utils.client_http:post:68 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/logon
2025-05-30 14:17:57.354 | INFO     | core.auth.api_service:get_user_info:347 - 获取用户信息
2025-05-30 14:17:57.355 | DEBUG    | utils.client_http:post:68 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/info
2025-05-30 14:17:57.560 | INFO     | core.auth.api_service:get_user_info:375 - 获取用户信息成功
2025-05-30 14:17:57.567 | INFO     | __main__:on_auth_completed:93 - 用户登录成功，正在启动主窗口...
2025-05-30 14:17:57.582 | INFO     | core.telegram.client_worker:__init__:63 - Telegram客户端工作线程初始化
2025-05-30 14:17:57.586 | INFO     | core.telegram.client_worker:run:67 - Telegram客户端工作线程开始运行
2025-05-30 14:17:57.588 | INFO     | core.telegram.client_manager:__init__:66 - Telegram客户端管理器初始化
2025-05-30 14:17:57.589 | INFO     | core.telegram.user_manager:__init__:31 - Telegram用户管理器初始化
2025-05-30 14:17:57.589 | INFO     | core.telegram.group_manager:__init__:36 - Telegram群组管理器初始化
2025-05-30 14:17:57.590 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-05-30 14:17:57.590 | INFO     | core.telegram.monitor_manager:__init__:26 - Telegram监控管理器初始化
2025-05-30 14:17:57.590 | INFO     | core.telegram.telegram_service:__init__:39 - Telegram服务初始化完成
2025-05-30 14:17:57.592 | INFO     | ui.main_window:_initialize_core_components:79 - MainWindow: 初始化核心组件...
2025-05-30 14:17:57.593 | INFO     | app.services.account_service:__init__:42 - 账户服务初始化
2025-05-30 14:17:57.594 | INFO     | core.proxy.proxy_task_manager:run:284 - 代理任务管理器线程启动
2025-05-30 14:17:57.594 | INFO     | app.controllers.account_controller:__init__:81 - 账户控制器初始化
2025-05-30 14:17:57.595 | INFO     | app.services.monitor_service:__init__:39 - 初始化监控任务服务
2025-05-30 14:17:57.595 | INFO     | core.monitor.filter:__init__:26 - 初始化消息过滤器
2025-05-30 14:17:57.595 | INFO     | core.monitor.extractor:__init__:24 - 初始化关键词提取器
2025-05-30 14:17:57.595 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-05-30 14:17:57.596 | INFO     | core.monitor.notifier:__init__:90 - 初始化通知管理器
2025-05-30 14:17:57.596 | INFO     | app.controllers.telegram_monitor_controller:__init__:64 - 初始化 Telegram 监控控制器
2025-05-30 14:17:57.597 | DEBUG    | app.controllers.telegram_monitor_controller:__init__:82 - 控制器初始化完成
2025-05-30 14:17:57.770 | INFO     | ui.views.monitor_view:__init__:48 - 初始化监控视图
2025-05-30 14:17:57.770 | DEBUG    | ui.views.monitor_view:_connect_signals:80 - 连接控制器信号
2025-05-30 14:17:57.941 | INFO     | ui.views.proxy_view:__init__:96 - 初始化视图，加载第一页代理数据
2025-05-30 14:17:58.137 | INFO     | __main__:on_auth_completed:97 - 主窗口已启动
2025-05-30 14:17:58.180 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:88 - 尝试连接监听日志信号...
2025-05-30 14:17:58.181 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:90 - 监听日志信号连接成功
2025-05-30 14:17:58.182 | INFO     | core.auth.api_service:verify_vip:425 - 会员验证接口调用
2025-05-30 14:17:58.183 | DEBUG    | utils.client_http:post:68 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/vip
2025-05-30 14:17:58.184 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-05-30 14:17:58.187 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-05-30 14:17:58.192 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:145 - 加载监控任务列表 (搜索关键词: '')
2025-05-30 14:17:58.193 | DEBUG    | ui.views.monitor_view:_on_loading_started:620 - 加载中: 正在加载任务列表...
2025-05-30 14:17:58.193 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-05-30 14:17:58.198 | INFO     | app.services.proxy_service:refresh_proxies:712 - 刷新代理列表: offset=0, limit=20
2025-05-30 14:17:58.198 | INFO     | core.proxy.proxy_task_manager:submit_task:172 - 提交耗时任务到子线程: refresh_proxies_0_20
2025-05-30 14:17:58.199 | DEBUG    | app.services.proxy_service:get_proxies:500 - 获取代理列表: offset=0, limit=20
2025-05-30 14:17:58.199 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-05-30 14:17:58.200 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-05-30 14:17:58.204 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-05-30 14:17:58.227 | INFO     | app.controllers.invite_controller:get_invite_tasks:38 - 获取邀请任务列表, status=None
2025-05-30 14:17:58.228 | INFO     | app.services.invite_service:get_invite_tasks:121 - 获取邀请任务列表, status=None
2025-05-30 14:17:58.230 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-05-30 14:17:58.231 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-05-30 14:17:58.231 | INFO     | app.services.invite_service:task_initialize:39 - 初始化邀请服务，重置运行中的任务为暂停状态
2025-05-30 14:17:58.232 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-05-30 14:17:58.232 | INFO     | core.proxy.proxy_task_manager:task_wrapper:199 - 任务 refresh_proxies_0_20 完成
2025-05-30 14:17:58.360 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-05-30 14:17:58.364 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-05-30 14:17:58.370 | INFO     | app.services.invite_service:task_initialize:48 - 成功重置 0 个任务状态
2025-05-30 14:17:58.370 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-05-30 14:17:58.371 | DEBUG    | ui.views.monitor_view:_refresh_task_list:175 - 刷新任务列表，共2个任务
2025-05-30 14:17:58.376 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-05-30 14:17:58.382 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-05-30 14:17:58.385 | INFO     | app.services.account_service:get_all_groups:96 - 获取所有账户分组成功, 共 1 个
2025-05-30 14:17:58.385 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-05-30 14:17:58.388 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-05-30 14:17:58.391 | DEBUG    | ui.views.account_view:_on_groups_loaded:442 - 分组加载完成: 1个分组
2025-05-30 14:17:58.398 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-05-30 14:17:58.400 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-05-30 14:17:58.460 | INFO     | app.services.account_service:get_all_accounts:289 - 获取所有账户成功, 共 2 个
2025-05-30 14:17:58.461 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-05-30 14:17:58.464 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-05-30 14:17:58.465 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-05-30 14:17:58.466 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-05-30 14:17:58.467 | DEBUG    | ui.views.account_view:_on_accounts_loaded:575 - 账户加载完成: 2个账户
2025-05-30 14:17:58.486 | DEBUG    | ui.views.account_view:_update_group_counts:184 - 分组计数已更新: {1: 1}
2025-05-30 14:17:58.491 | INFO     | ui.views.account_view:_auto_login_accounts:630 - 开始调用控制器进行 2 个账户的自动登录。
2025-05-30 14:17:58.492 | INFO     | app.services.account_service:batch_auto_login:1153 - 服务层：开始批量自动登录 2 个账户, 最大并发: 5
2025-05-30 14:17:58.492 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-05-30 14:17:58.498 | INFO     | app.controllers.telegram_monitor_controller:_load_total_users_count_internal:191 - 成功加载总体统计数据: 总用户数 958, 今日采集 0, 日均采集 106, 运行天数 9
2025-05-30 14:17:58.498 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:156 - 成功加载 2 个任务
2025-05-30 14:17:58.499 | DEBUG    | ui.views.monitor_view:_on_loading_finished:625 - 加载完成
2025-05-30 14:17:58.501 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-05-30 14:17:58.514 | INFO     | app.services.account_service:batch_auto_login:1192 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-05-30 14:17:58.515 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-05-30 14:17:58.516 | INFO     | app.services.account_service:get_all_accounts:289 - 获取所有账户成功, 共 2 个
2025-05-30 14:17:58.517 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-05-30 14:17:58.519 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-05-30 14:17:58.520 | DEBUG    | ui.views.account_view:_update_group_counts:184 - 分组计数已更新: {1: 1}
2025-05-30 14:17:58.521 | DEBUG    | ui.views.account_view:_init_cache:151 - 账户缓存初始化完成，共 2 个账户
2025-05-30 14:17:58.526 | DEBUG    | ui.views.account_view:_on_accounts_loaded:575 - 账户加载完成: 2个账户
2025-05-30 14:17:58.546 | DEBUG    | ui.views.account_view:_update_group_counts:184 - 分组计数已更新: {1: 1}
2025-05-30 14:17:58.554 | WARNING  | core.auth.api_service:verify_vip:441 - 会员验证失败: 验证失败
2025-05-30 14:17:58.815 | INFO     | app.services.account_service:batch_auto_login:1192 - 服务层：账户 *********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-05-30 14:17:58.815 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-05-30 14:17:58.817 | INFO     | app.services.account_service:batch_auto_login:1262 - 服务层：准备调用核心层处理 2 个账户的自动登录。
2025-05-30 14:17:58.818 | INFO     | app.services.account_service:batch_auto_login:1272 - 服务层：设置核心层任务超时为 120 秒。
2025-05-30 14:17:58.818 | INFO     | core.telegram.client_manager:batch_auto_login:1220 - 开始批量自动登录, 共 2 个账户, 最大并发 5
2025-05-30 14:17:58.819 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:256 - 创建客户端: h:\PyProject\TeleTest\APPDATA\sessions\+***********.session, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-05-30 14:17:58.820 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-05-30 14:17:58.820 | DEBUG    | core.telegram.client_manager:get_proxy:93 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-05-30 14:17:58.820 | DEBUG    | core.telegram.client_manager:get_proxy:140 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-05-30 14:17:58.825 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:256 - 创建客户端: h:\PyProject\TeleTest\APPDATA\sessions\***********.session, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-05-30 14:17:58.825 | DEBUG    | core.telegram.client_manager:get_proxy:93 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-05-30 14:17:58.827 | DEBUG    | core.telegram.client_manager:get_proxy:140 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-05-30 14:17:58.854 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-05-30 14:17:58.872 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-05-30 14:17:58.942 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-05-30 14:17:58.944 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-05-30 14:17:58.946 | INFO     | ui.views.proxy_view:_load_initial_data:1044 - 初始数据加载完成，共 3 个代理
2025-05-30 14:18:01.602 | ERROR    | core.telegram.client_manager:_connect_client:217 - 认证密钥错误 (AuthKeyError): The authorization key (session file) was used under two different IP addresses simultaneously, and can no longer be used. Use the same session exclusively, or use different sessions (caused by InvokeWithLayerRequest(InitConnectionRequest(GetConfigRequest)))
2025-05-30 14:18:04.664 | ERROR    | core.telegram.client_manager:_connect_client:217 - 认证密钥错误 (AuthKeyError): The authorization key (session file) was used under two different IP addresses simultaneously, and can no longer be used. Use the same session exclusively, or use different sessions (caused by InvokeWithLayerRequest(InitConnectionRequest(GetConfigRequest)))
2025-05-30 14:18:06.676 | INFO     | core.telegram.client_manager:batch_auto_login:1369 - 批量登录完成: 总计 2, 成功 0, 失败 2
2025-05-30 14:18:06.849 | INFO     | app.services.account_service:batch_auto_login:1293 - 服务层：核心层登录完成: 成功 0, 失败 2 / 2
2025-05-30 14:18:07.609 | INFO     | ui.views.monitor_view:open_add_task_dialog:135 - 打开编辑任务对话框 2
2025-05-30 14:18:07.670 | INFO     | ui.views.monitor_view:open_add_task_dialog:158 - 编辑监控任务 ID: 2
2025-05-30 14:18:07.735 | INFO     | ui.views.add_monitor_view:_load_initial_data:111 - 加载初始数据
2025-05-30 14:18:07.736 | INFO     | ui.views.add_monitor_view:set_task_data:191 - 设置任务数据: 2
2025-05-30 14:18:07.736 | INFO     | app.controllers.telegram_monitor_controller:get_task_data:306 - 获取任务数据: ID 2
2025-05-30 14:18:07.736 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-05-30 14:18:07.781 | INFO     | ui.views.add_monitor_view:_load_account_groups:400 - 加载账户分组
2025-05-30 14:18:07.782 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-05-30 14:18:07.783 | INFO     | ui.views.add_monitor_view:_load_accounts_by_group:417 - 根据分组ID加载账户: -1
2025-05-30 14:18:07.784 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-05-30 14:18:07.798 | INFO     | app.services.account_service:get_all_accounts:289 - 获取所有账户成功, 共 2 个
2025-05-30 14:18:07.798 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-05-30 14:18:07.802 | INFO     | ui.views.add_monitor_view:_load_accounts_by_group:442 - 已加载 2 个账户到列表
2025-05-30 14:18:07.803 | DEBUG    | ui.views.account_view:_on_accounts_loaded:575 - 账户加载完成: 2个账户
2025-05-30 14:18:07.824 | DEBUG    | ui.views.account_view:_update_group_counts:184 - 分组计数已更新: {1: 1}
2025-05-30 14:18:07.830 | INFO     | app.services.account_service:get_all_groups:96 - 获取所有账户分组成功, 共 1 个
2025-05-30 14:18:07.830 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-05-30 14:18:07.951 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-05-30 14:18:07.952 | DEBUG    | ui.views.account_view:_on_groups_loaded:442 - 分组加载完成: 1个分组
2025-05-30 14:18:07.953 | INFO     | ui.views.add_monitor_view:_on_group_index_changed:460 - 当前分组id:-1
2025-05-30 14:18:07.953 | INFO     | ui.views.add_monitor_view:_load_account_groups:409 - 加载账户分组数据: [{'id': 1, 'name': '营销', 'description': '', 'account_count': 1}]
2025-05-30 14:18:07.954 | INFO     | ui.views.add_monitor_view:_load_accounts_by_group:417 - 根据分组ID加载账户: -1
2025-05-30 14:18:07.954 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-05-30 14:18:07.957 | INFO     | app.controllers.telegram_monitor_controller:get_task_data:317 - 成功获取任务数据: 2
2025-05-30 14:18:07.958 | INFO     | ui.views.add_monitor_view:_load_task_data:126 - 加载任务数据到界面: 关键词监控
2025-05-30 14:18:07.960 | INFO     | ui.views.add_monitor_view:_load_task_data:182 - 任务数据加载成功: 关键词监控
2025-05-30 14:18:07.978 | INFO     | app.services.account_service:get_all_accounts:289 - 获取所有账户成功, 共 2 个
2025-05-30 14:18:07.978 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-05-30 14:18:07.981 | INFO     | ui.views.add_monitor_view:_load_accounts_by_group:442 - 已加载 2 个账户到列表
2025-05-30 14:18:07.981 | DEBUG    | ui.views.account_view:_on_accounts_loaded:575 - 账户加载完成: 2个账户
2025-05-30 14:18:08.001 | DEBUG    | ui.views.account_view:_update_group_counts:184 - 分组计数已更新: {1: 1}
2025-05-30 14:18:57.552 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-05-30 14:18:57.553 | DEBUG    | utils.client_http:post:68 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-05-30 14:19:57.553 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-05-30 14:19:57.553 | DEBUG    | utils.client_http:post:68 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-05-30 14:20:57.557 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-05-30 14:20:57.558 | DEBUG    | utils.client_http:post:68 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-05-30 14:21:25.486 | DEBUG    | ui.views.monitor_view:_on_task_item_clicked:258 - 任务项点击: 2
2025-05-30 14:21:25.489 | INFO     | app.controllers.telegram_monitor_controller:select_task:237 - 选择任务: 2
2025-05-30 14:21:25.490 | DEBUG    | ui.views.monitor_view:_on_task_selection_changed:281 - 任务选择变更: 2
2025-05-30 14:21:25.490 | DEBUG    | ui.views.monitor_view:_on_loading_started:620 - 加载中: 正在加载任务详情...
2025-05-30 14:21:25.490 | INFO     | app.controllers.telegram_monitor_controller:load_task_details:261 - 加载任务详情: ID 2
2025-05-30 14:21:25.490 | INFO     | app.services.monitor_service:get_task_detail:1018 - 获取任务详情: ID 2
2025-05-30 14:21:25.491 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-05-30 14:21:25.499 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-05-30 14:21:25.500 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-05-30 14:21:25.507 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-05-30 14:21:25.509 | DEBUG    | app.controllers.telegram_monitor_controller:load_task_details:271 - 获取到任务 2 的统计数据: {'total_users': 4, 'today_users': 0, 'avg_daily': 1, 'running_days': 3}
2025-05-30 14:21:25.510 | DEBUG    | ui.views.monitor_view:_on_task_details_loaded:309 - 任务详情加载完成: 2
2025-05-30 14:21:25.511 | DEBUG    | ui.views.monitor_view:_on_task_details_loaded:312 - 任务详情数据: {'created_at': '2025-05-26 18:23:23', 'description': '', 'id': '2', 'ignore_keywords': [], 'ignore_nicknames_rules': [], 'is_active': True, 'is_running': False, 'keywords': ['开户', '比特币', '注册地址', '登录地址', '娱乐', '在线', '时时彩', 'pk10', '分分彩'], 'last_error': "所有账户监控设置均失败: object tuple can't be used in 'await' expression, object tuple can't be used in 'await' expression", 'monitor_messages': True, 'monitor_new_users': False, 'monitored_chats': [{'account_phone': '***********', 'chat_id': '**********', 'chat_title': '泰国华人交友聊天总群', 'id': 100}, {'account_phone': '***********', 'chat_id': '**********', 'chat_title': '赚钱项目灰产社区', 'id': 101}, {'account_phone': '***********', 'chat_id': '**********', 'chat_title': '深圳集团 - 藏楼1.0', 'id': 102}, {'account_phone': '***********', 'chat_id': '**********', 'chat_title': '干到你没钱16', 'id': 103}, {'account_phone': '***********', 'chat_id': '**********', 'chat_title': '东南亚博彩代理聊天群', 'id': 104}, {'account_phone': '***********', 'chat_id': '**********', 'chat_title': '柬埔寨泰国电子体育群', 'id': 105}, {'account_phone': '***********', 'chat_id': '**********', 'chat_title': '索引群聊❤️搜索王', 'id': 106}, {'account_phone': '***********', 'chat_id': '**********', 'chat_title': 'wiegnwi', 'id': 107}, {'account_phone': '***********', 'chat_id': '**********', 'chat_title': '区块节点NodeMining-Pool', 'id': 108}, {'account_phone': '***********', 'chat_id': '**********', 'chat_title': '区块节点NodeMining-Pool', 'id': 109}, {'account_phone': '***********', 'chat_id': '**********', 'chat_title': '🔥入金话术馆', 'id': 110}, {'account_phone': '***********', 'chat_id': '**********', 'chat_title': '广州会所资源_(部分预览)', 'id': 111}, {'account_phone': '***********', 'chat_id': '**********', 'chat_title': '内涵段子🔥搞笑视频', 'id': 112}, {'account_phone': '***********', 'chat_id': '**********', 'chat_title': '国外 话术 素材总部', 'id': 113}, {'account_phone': '***********', 'chat_id': '**********', 'chat_title': '精聊话术-人设套图大全', 'id': 114}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '🅰️中文搜片搜索👙自慰🩸抠逼🔍乱伦🗣口交', 'id': 115}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '软件开发🪸搭建网站🪸网站开发', 'id': 116}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '迷🦊 吃瓜 完整版破案‼️', 'id': 117}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '【官方】币安矿池置换中文群🇨🇳👤', 'id': 118}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '🈲 高中 学生资源库（加密）🎀', 'id': 119}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '🍒 萝莉屋18岁 资源群🦄', 'id': 120}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '【官方】币安矿池置换中文群🇨🇳', 'id': 121}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '【官方】币安BNB挖矿中文总社区🇨🇳❤️', 'id': 122}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '免费情色视频搜索🚀', 'id': 123}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '香港服务器交流群', 'id': 124}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': 'wiegnwi', 'id': 125}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '💡站长群 HostCLi.com 宝塔纯净版', 'id': 126}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': 'Fragment Universal Drop', 'id': 127}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '柯基币-礦池-官方社区', 'id': 128}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '中文算力MinerDog pool', 'id': 129}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '云泽社区', 'id': 130}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '国际联盟—海外实卡接码/邮寄公开群【支持担保】', 'id': 131}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '仙楼集团·资源发布站 @xianlou', 'id': 132}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '路易频道引流推广工作群', 'id': 133}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '泡妞笔记', 'id': 134}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '神奇宝贝 @so588', 'id': 135}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': 'TGbox助手|群发|拉人|引流|群发助手', 'id': 136}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '比特币购买，比特币行情，比特币怎么买', 'id': 137}], 'name': '关键词监控', 'stats': {'avg_daily': 1, 'running_days': 3, 'today_users': 0, 'total_users': 4}, 'updated_at': '2025-05-30 01:31:23'}
2025-05-30 14:21:25.514 | DEBUG    | ui.views.monitor_view:_on_task_details_loaded:316 - 统计数据: {'avg_daily': 1, 'running_days': 3, 'today_users': 0, 'total_users': 4}
2025-05-30 14:21:25.515 | DEBUG    | ui.views.monitor_view:_on_loading_started:620 - 加载中: 正在加载第 1 页数据...
2025-05-30 14:21:25.515 | INFO     | app.controllers.telegram_monitor_controller:load_task_users:396 - 加载任务用户数据: ID 2, 页码 1, 每页 10 条
2025-05-30 14:21:25.516 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-05-30 14:21:25.527 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-05-30 14:21:25.529 | INFO     | ui.views.monitor_view:_update_users_table:342 - 开始更新用户表格，收到4条数据
2025-05-30 14:21:25.529 | DEBUG    | ui.views.monitor_view:_update_users_table:343 - 分页信息：{'current_page': 1, 'page_size': 10, 'total_items': 4, 'total_pages': 1}
2025-05-30 14:21:25.530 | INFO     | ui.views.monitor_view:_update_users_table:360 - 示例数据[0]: {'join_date': '2025-05-29 17:23:09', 'keyword': '比特币', 'nickname': '共赢2 联盟', 'task_type': 'text', 'uid': '7942265654', 'username': 'bangqiwang'}
2025-05-30 14:21:25.530 | INFO     | ui.views.monitor_view:_update_users_table:360 - 示例数据[1]: {'join_date': '2025-05-26 18:48:01', 'keyword': '娱乐', 'nickname': 'SOSO搜搜 🤖 中文频道群组搜索 None', 'task_type': 'text', 'uid': '6549939461', 'username': ''}
2025-05-30 14:21:25.531 | INFO     | ui.views.monitor_view:_update_users_table:360 - 示例数据[2]: {'join_date': '2025-05-26 18:47:58', 'keyword': '开户, 时时彩', 'nickname': '极搜🔍中文搜索@JISO None', 'task_type': 'text', 'uid': '6920818412', 'username': ''}
2025-05-30 14:21:25.532 | INFO     | ui.views.monitor_view:_update_users_table:364 - 设置表格行数: 4
2025-05-30 14:21:25.538 | INFO     | ui.views.monitor_view:_update_users_table:388 - 用户表格更新完成
2025-05-30 14:21:25.539 | INFO     | app.controllers.telegram_monitor_controller:load_task_users:423 - 成功加载第 1 页用户数据，共 4 条
2025-05-30 14:21:25.539 | DEBUG    | ui.views.monitor_view:_on_loading_finished:625 - 加载完成
2025-05-30 14:21:25.540 | INFO     | app.controllers.telegram_monitor_controller:load_task_details:287 - 成功加载任务详情: 关键词监控
2025-05-30 14:21:25.540 | DEBUG    | ui.views.monitor_view:_on_loading_finished:625 - 加载完成
2025-05-30 14:21:26.157 | DEBUG    | ui.views.monitor_view:_on_task_item_clicked:258 - 任务项点击: 1
2025-05-30 14:21:26.161 | INFO     | app.controllers.telegram_monitor_controller:select_task:237 - 选择任务: 1
2025-05-30 14:21:26.161 | DEBUG    | ui.views.monitor_view:_on_task_selection_changed:281 - 任务选择变更: 1
2025-05-30 14:21:26.162 | DEBUG    | ui.views.monitor_view:_on_loading_started:620 - 加载中: 正在加载任务详情...
2025-05-30 14:21:26.162 | INFO     | app.controllers.telegram_monitor_controller:load_task_details:261 - 加载任务详情: ID 1
2025-05-30 14:21:26.163 | INFO     | app.services.monitor_service:get_task_detail:1018 - 获取任务详情: ID 1
2025-05-30 14:21:26.163 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-05-30 14:21:26.170 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-05-30 14:21:26.171 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-05-30 14:21:26.179 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-05-30 14:21:26.180 | DEBUG    | app.controllers.telegram_monitor_controller:load_task_details:271 - 获取到任务 1 的统计数据: {'total_users': 952, 'today_users': 0, 'avg_daily': 106, 'running_days': 9}
2025-05-30 14:21:26.180 | DEBUG    | ui.views.monitor_view:_on_task_details_loaded:309 - 任务详情加载完成: 1
2025-05-30 14:21:26.181 | DEBUG    | ui.views.monitor_view:_on_task_details_loaded:312 - 任务详情数据: {'created_at': '2025-05-21 01:05:08', 'description': '', 'id': '1', 'ignore_keywords': [], 'ignore_nicknames_rules': [], 'is_active': True, 'is_running': False, 'keywords': [], 'last_error': "所有账户监控设置均失败: object tuple can't be used in 'await' expression, object tuple can't be used in 'await' expression", 'monitor_messages': True, 'monitor_new_users': False, 'monitored_chats': [{'account_phone': '***********', 'chat_id': '**********', 'chat_title': '泰国华人交友聊天总群', 'id': 32}, {'account_phone': '***********', 'chat_id': '**********', 'chat_title': '东南亚博彩代理聊天群', 'id': 33}, {'account_phone': '***********', 'chat_id': '**********', 'chat_title': '柬埔寨泰国华人聊天群', 'id': 34}, {'account_phone': '***********', 'chat_id': '**********', 'chat_title': '赚钱项目灰产社区', 'id': 35}, {'account_phone': '***********', 'chat_id': '**********', 'chat_title': '区块节点NodeMining-Pool', 'id': 36}, {'account_phone': '***********', 'chat_id': '**********', 'chat_title': '区块节点NodeMining-Pool', 'id': 37}, {'account_phone': '***********', 'chat_id': '**********', 'chat_title': '深圳集团 - 藏楼（聊天', 'id': 38}, {'account_phone': '***********', 'chat_id': '**********', 'chat_title': '广州会所资源_(部分预览)', 'id': 39}, {'account_phone': '***********', 'chat_id': '**********', 'chat_title': '🔥入金话术馆', 'id': 40}, {'account_phone': '***********', 'chat_id': '**********', 'chat_title': 'wiegnwi', 'id': 41}, {'account_phone': '***********', 'chat_id': '**********', 'chat_title': '国外 话术 素材总部', 'id': 42}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '💡站长群 HostCLi.com 宝塔纯净版', 'id': 43}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '云泽社区', 'id': 44}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '🍒 萝莉屋18岁 资源群🦄', 'id': 45}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '柯基币-礦池-官方社区', 'id': 46}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '🈲 高中 学生资源库（加密）🎀', 'id': 47}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '🅰️中文搜片搜索👙自慰🩸抠逼🔍乱伦🗣口交', 'id': 48}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '中文算力MinerDog pool', 'id': 49}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '【官方】币安矿池置换中文群🇨🇳', 'id': 50}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '香港服务器交流群', 'id': 51}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '免费情色视频搜索🚀', 'id': 52}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '迷🦊 吃瓜 完整版破案‼️', 'id': 53}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '软件开发🪸搭建网站🪸网站开发', 'id': 54}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '国际联盟—海外实卡接码/邮寄公开群【支持担保】', 'id': 55}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': 'wiegnwi', 'id': 56}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '嘿嘿嘿', 'id': 57}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '泡妞笔记', 'id': 58}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '仙楼集团·资源发布站 @xianlou', 'id': 59}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '路易频道引流推广工作群', 'id': 60}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '神奇宝贝 @so588', 'id': 61}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': 'TGbox助手|群发|拉人|引流|群发助手', 'id': 62}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '比特币购买，比特币行情，比特币怎么买', 'id': 63}], 'name': '全部群组', 'stats': {'avg_daily': 106, 'running_days': 9, 'today_users': 0, 'total_users': 952}, 'updated_at': '2025-05-26 16:29:16'}
2025-05-30 14:21:26.184 | DEBUG    | ui.views.monitor_view:_on_task_details_loaded:316 - 统计数据: {'avg_daily': 106, 'running_days': 9, 'today_users': 0, 'total_users': 952}
2025-05-30 14:21:26.185 | DEBUG    | ui.views.monitor_view:_on_loading_started:620 - 加载中: 正在加载第 1 页数据...
2025-05-30 14:21:26.185 | INFO     | app.controllers.telegram_monitor_controller:load_task_users:396 - 加载任务用户数据: ID 1, 页码 1, 每页 10 条
2025-05-30 14:21:26.186 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-05-30 14:21:26.196 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-05-30 14:21:26.197 | INFO     | ui.views.monitor_view:_update_users_table:342 - 开始更新用户表格，收到10条数据
2025-05-30 14:21:26.198 | DEBUG    | ui.views.monitor_view:_update_users_table:343 - 分页信息：{'current_page': 1, 'page_size': 10, 'total_items': 967, 'total_pages': 97}
2025-05-30 14:21:26.198 | INFO     | ui.views.monitor_view:_update_users_table:360 - 示例数据[0]: {'join_date': '2025-05-29 17:15:39', 'keyword': '', 'nickname': 'san hejiu', 'task_type': 'text', 'uid': '7690383982', 'username': ''}
2025-05-30 14:21:26.199 | INFO     | ui.views.monitor_view:_update_users_table:360 - 示例数据[1]: {'join_date': '2025-05-29 17:15:26', 'keyword': '', 'nickname': '西港外送佳佳/打炮按摩 None', 'task_type': 'text', 'uid': '7991181298', 'username': ''}
2025-05-30 14:21:26.199 | INFO     | ui.views.monitor_view:_update_users_table:360 - 示例数据[2]: {'join_date': '2025-05-29 17:15:07', 'keyword': '', 'nickname': '黑子 None', 'task_type': 'text', 'uid': '6654528704', 'username': 'BqZ8D3cFkOy7p4'}
2025-05-30 14:21:26.200 | INFO     | ui.views.monitor_view:_update_users_table:364 - 设置表格行数: 10
2025-05-30 14:21:26.205 | INFO     | ui.views.monitor_view:_update_users_table:388 - 用户表格更新完成
2025-05-30 14:21:26.205 | INFO     | app.controllers.telegram_monitor_controller:load_task_users:423 - 成功加载第 1 页用户数据，共 10 条
2025-05-30 14:21:26.206 | DEBUG    | ui.views.monitor_view:_on_loading_finished:625 - 加载完成
2025-05-30 14:21:26.206 | INFO     | app.controllers.telegram_monitor_controller:load_task_details:287 - 成功加载任务详情: 全部群组
2025-05-30 14:21:26.207 | DEBUG    | ui.views.monitor_view:_on_loading_finished:625 - 加载完成
2025-05-30 14:21:28.663 | INFO     | ui.views.monitor_view:open_add_task_dialog:135 - 打开添加任务对话框 
2025-05-30 14:21:28.763 | INFO     | ui.views.add_monitor_view:_load_initial_data:111 - 加载初始数据
2025-05-30 14:21:28.827 | INFO     | ui.views.add_monitor_view:_load_account_groups:400 - 加载账户分组
2025-05-30 14:21:28.828 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-05-30 14:21:28.829 | INFO     | ui.views.add_monitor_view:_load_accounts_by_group:417 - 根据分组ID加载账户: -1
2025-05-30 14:21:28.830 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-05-30 14:21:29.020 | INFO     | app.services.account_service:get_all_accounts:289 - 获取所有账户成功, 共 2 个
2025-05-30 14:21:29.021 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-05-30 14:21:29.024 | INFO     | ui.views.add_monitor_view:_load_accounts_by_group:442 - 已加载 2 个账户到列表
2025-05-30 14:21:29.024 | DEBUG    | ui.views.account_view:_on_accounts_loaded:575 - 账户加载完成: 2个账户
2025-05-30 14:21:29.044 | DEBUG    | ui.views.account_view:_update_group_counts:184 - 分组计数已更新: {1: 1}
2025-05-30 14:21:29.050 | INFO     | app.services.account_service:get_all_groups:96 - 获取所有账户分组成功, 共 1 个
2025-05-30 14:21:29.051 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-05-30 14:21:29.052 | DEBUG    | ui.views.account_view:_on_groups_loaded:442 - 分组加载完成: 1个分组
2025-05-30 14:21:29.053 | INFO     | ui.views.add_monitor_view:_on_group_index_changed:460 - 当前分组id:-1
2025-05-30 14:21:29.054 | INFO     | ui.views.add_monitor_view:_load_account_groups:409 - 加载账户分组数据: [{'id': 1, 'name': '营销', 'description': '', 'account_count': 1}]
2025-05-30 14:21:29.054 | INFO     | ui.views.add_monitor_view:_load_accounts_by_group:417 - 根据分组ID加载账户: -1
2025-05-30 14:21:29.055 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-05-30 14:21:29.060 | INFO     | app.services.account_service:get_all_accounts:289 - 获取所有账户成功, 共 2 个
2025-05-30 14:21:29.060 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-05-30 14:21:29.063 | INFO     | ui.views.add_monitor_view:_load_accounts_by_group:442 - 已加载 2 个账户到列表
2025-05-30 14:21:29.064 | DEBUG    | ui.views.account_view:_on_accounts_loaded:575 - 账户加载完成: 2个账户
2025-05-30 14:21:29.082 | DEBUG    | ui.views.account_view:_update_group_counts:184 - 分组计数已更新: {1: 1}
2025-05-30 14:21:34.383 | INFO     | ui.views.add_msg_task_view:_load_initial_data:136 - 加载初始数据
2025-05-30 14:21:34.436 | INFO     | ui.views.add_msg_task_view:_load_account_groups:146 - 加载账户分组
2025-05-30 14:21:34.436 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-05-30 14:21:34.624 | INFO     | app.services.account_service:get_all_groups:96 - 获取所有账户分组成功, 共 1 个
2025-05-30 14:21:34.624 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-05-30 14:21:34.627 | DEBUG    | ui.views.account_view:_on_groups_loaded:442 - 分组加载完成: 1个分组
2025-05-30 14:21:34.628 | INFO     | ui.views.add_msg_task_view:_load_account_groups:162 - 加载账户分组数据: 1个分组
2025-05-30 14:21:34.629 | INFO     | ui.views.add_msg_task_view:_load_accounts_by_group:177 - 根据分组ID加载账户: -1
2025-05-30 14:21:34.630 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-05-30 14:21:34.640 | INFO     | app.services.account_service:get_all_accounts:289 - 获取所有账户成功, 共 2 个
2025-05-30 14:21:34.641 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-05-30 14:21:34.644 | INFO     | ui.views.add_msg_task_view:_load_accounts_by_group:213 - 已加载 2 个账户到列表
2025-05-30 14:21:34.645 | DEBUG    | ui.views.account_view:_on_accounts_loaded:575 - 账户加载完成: 2个账户
2025-05-30 14:21:34.665 | DEBUG    | ui.views.account_view:_update_group_counts:184 - 分组计数已更新: {1: 1}
2025-05-30 14:21:37.132 | INFO     | ui.views.invite_view:_on_create_task_clicked:164 - 点击创建新任务按钮
2025-05-30 14:21:37.241 | INFO     | ui.views.add_invite_task_view:_load_initial_data:65 - 加载初始数据
2025-05-30 14:21:37.278 | INFO     | ui.views.add_invite_task_view:_load_account_groups:75 - 加载账户分组
2025-05-30 14:21:37.278 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-05-30 14:21:37.338 | INFO     | app.services.account_service:get_all_groups:96 - 获取所有账户分组成功, 共 1 个
2025-05-30 14:21:37.339 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-05-30 14:21:37.340 | DEBUG    | ui.views.account_view:_on_groups_loaded:442 - 分组加载完成: 1个分组
2025-05-30 14:21:37.341 | INFO     | ui.views.add_invite_task_view:_load_account_groups:91 - 加载账户分组数据: 1个分组
2025-05-30 14:21:37.342 | INFO     | ui.views.add_invite_task_view:_load_accounts_by_group:106 - 根据分组ID加载账户: -1
2025-05-30 14:21:37.343 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-05-30 14:21:37.388 | INFO     | app.services.account_service:get_all_accounts:289 - 获取所有账户成功, 共 2 个
2025-05-30 14:21:37.389 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-05-30 14:21:37.390 | INFO     | ui.views.add_invite_task_view:_load_accounts_by_group:142 - 已加载 2 个账户到列表
2025-05-30 14:21:37.391 | DEBUG    | ui.views.account_view:_on_accounts_loaded:575 - 账户加载完成: 2个账户
2025-05-30 14:21:37.412 | DEBUG    | ui.views.account_view:_update_group_counts:184 - 分组计数已更新: {1: 1}
2025-05-30 14:21:46.780 | INFO     | ui.views.proxy_view:_on_table_double_clicked:1172 - 已复制代理配置: socks5://127.0.0.1:1080
2025-05-30 14:21:48.397 | INFO     | ui.views.proxy_view:_on_table_double_clicked:1172 - 已复制代理配置: socks5://**************:10001
2025-05-30 14:21:57.554 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-05-30 14:21:57.555 | DEBUG    | utils.client_http:post:68 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-05-30 14:22:57.560 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-05-30 14:22:57.561 | DEBUG    | utils.client_http:post:68 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-05-30 14:23:57.554 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-05-30 14:23:57.554 | DEBUG    | utils.client_http:post:68 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-05-30 14:24:57.551 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-05-30 14:24:57.552 | DEBUG    | utils.client_http:post:68 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-05-30 14:25:57.556 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-05-30 14:25:57.556 | DEBUG    | utils.client_http:post:68 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-05-30 14:26:57.555 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-05-30 14:26:57.556 | DEBUG    | utils.client_http:post:68 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-05-30 14:27:57.554 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-05-30 14:27:57.555 | DEBUG    | utils.client_http:post:68 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-05-30 14:28:57.551 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-05-30 14:28:57.552 | DEBUG    | utils.client_http:post:68 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-05-30 14:29:57.552 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-05-30 14:29:57.553 | DEBUG    | utils.client_http:post:68 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-05-30 14:30:57.558 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-05-30 14:30:57.558 | DEBUG    | utils.client_http:post:68 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-05-30 14:31:57.552 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-05-30 14:31:57.553 | DEBUG    | utils.client_http:post:68 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-05-30 14:32:57.555 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-05-30 14:32:57.556 | DEBUG    | utils.client_http:post:68 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-05-30 14:33:57.554 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-05-30 14:33:57.555 | DEBUG    | utils.client_http:post:68 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-05-30 14:34:57.557 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-05-30 14:34:57.558 | DEBUG    | utils.client_http:post:68 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-05-30 14:35:57.553 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-05-30 14:35:57.554 | DEBUG    | utils.client_http:post:68 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-05-30 14:36:21.281 | DEBUG    | ui.views.monitor_view:_on_task_item_clicked:258 - 任务项点击: 2
2025-05-30 14:36:21.285 | INFO     | app.controllers.telegram_monitor_controller:select_task:237 - 选择任务: 2
2025-05-30 14:36:21.285 | DEBUG    | ui.views.monitor_view:_on_task_selection_changed:281 - 任务选择变更: 2
2025-05-30 14:36:21.286 | DEBUG    | ui.views.monitor_view:_on_loading_started:620 - 加载中: 正在加载任务详情...
2025-05-30 14:36:21.286 | INFO     | app.controllers.telegram_monitor_controller:load_task_details:261 - 加载任务详情: ID 2
2025-05-30 14:36:21.287 | INFO     | app.services.monitor_service:get_task_detail:1018 - 获取任务详情: ID 2
2025-05-30 14:36:21.287 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-05-30 14:36:21.293 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-05-30 14:36:21.295 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-05-30 14:36:21.303 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-05-30 14:36:21.304 | DEBUG    | app.controllers.telegram_monitor_controller:load_task_details:271 - 获取到任务 2 的统计数据: {'total_users': 4, 'today_users': 0, 'avg_daily': 1, 'running_days': 3}
2025-05-30 14:36:21.305 | DEBUG    | ui.views.monitor_view:_on_task_details_loaded:309 - 任务详情加载完成: 2
2025-05-30 14:36:21.305 | DEBUG    | ui.views.monitor_view:_on_task_details_loaded:312 - 任务详情数据: {'created_at': '2025-05-26 18:23:23', 'description': '', 'id': '2', 'ignore_keywords': [], 'ignore_nicknames_rules': [], 'is_active': True, 'is_running': False, 'keywords': ['开户', '比特币', '注册地址', '登录地址', '娱乐', '在线', '时时彩', 'pk10', '分分彩'], 'last_error': "所有账户监控设置均失败: object tuple can't be used in 'await' expression, object tuple can't be used in 'await' expression", 'monitor_messages': True, 'monitor_new_users': False, 'monitored_chats': [{'account_phone': '***********', 'chat_id': '**********', 'chat_title': '泰国华人交友聊天总群', 'id': 100}, {'account_phone': '***********', 'chat_id': '**********', 'chat_title': '赚钱项目灰产社区', 'id': 101}, {'account_phone': '***********', 'chat_id': '**********', 'chat_title': '深圳集团 - 藏楼1.0', 'id': 102}, {'account_phone': '***********', 'chat_id': '**********', 'chat_title': '干到你没钱16', 'id': 103}, {'account_phone': '***********', 'chat_id': '**********', 'chat_title': '东南亚博彩代理聊天群', 'id': 104}, {'account_phone': '***********', 'chat_id': '**********', 'chat_title': '柬埔寨泰国电子体育群', 'id': 105}, {'account_phone': '***********', 'chat_id': '**********', 'chat_title': '索引群聊❤️搜索王', 'id': 106}, {'account_phone': '***********', 'chat_id': '**********', 'chat_title': 'wiegnwi', 'id': 107}, {'account_phone': '***********', 'chat_id': '**********', 'chat_title': '区块节点NodeMining-Pool', 'id': 108}, {'account_phone': '***********', 'chat_id': '**********', 'chat_title': '区块节点NodeMining-Pool', 'id': 109}, {'account_phone': '***********', 'chat_id': '**********', 'chat_title': '🔥入金话术馆', 'id': 110}, {'account_phone': '***********', 'chat_id': '**********', 'chat_title': '广州会所资源_(部分预览)', 'id': 111}, {'account_phone': '***********', 'chat_id': '**********', 'chat_title': '内涵段子🔥搞笑视频', 'id': 112}, {'account_phone': '***********', 'chat_id': '**********', 'chat_title': '国外 话术 素材总部', 'id': 113}, {'account_phone': '***********', 'chat_id': '**********', 'chat_title': '精聊话术-人设套图大全', 'id': 114}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '🅰️中文搜片搜索👙自慰🩸抠逼🔍乱伦🗣口交', 'id': 115}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '软件开发🪸搭建网站🪸网站开发', 'id': 116}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '迷🦊 吃瓜 完整版破案‼️', 'id': 117}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '【官方】币安矿池置换中文群🇨🇳👤', 'id': 118}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '🈲 高中 学生资源库（加密）🎀', 'id': 119}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '🍒 萝莉屋18岁 资源群🦄', 'id': 120}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '【官方】币安矿池置换中文群🇨🇳', 'id': 121}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '【官方】币安BNB挖矿中文总社区🇨🇳❤️', 'id': 122}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '免费情色视频搜索🚀', 'id': 123}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '香港服务器交流群', 'id': 124}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': 'wiegnwi', 'id': 125}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '💡站长群 HostCLi.com 宝塔纯净版', 'id': 126}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': 'Fragment Universal Drop', 'id': 127}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '柯基币-礦池-官方社区', 'id': 128}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '中文算力MinerDog pool', 'id': 129}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '云泽社区', 'id': 130}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '国际联盟—海外实卡接码/邮寄公开群【支持担保】', 'id': 131}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '仙楼集团·资源发布站 @xianlou', 'id': 132}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '路易频道引流推广工作群', 'id': 133}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '泡妞笔记', 'id': 134}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '神奇宝贝 @so588', 'id': 135}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': 'TGbox助手|群发|拉人|引流|群发助手', 'id': 136}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '比特币购买，比特币行情，比特币怎么买', 'id': 137}], 'name': '关键词监控', 'stats': {'avg_daily': 1, 'running_days': 3, 'today_users': 0, 'total_users': 4}, 'updated_at': '2025-05-30 01:31:23'}
2025-05-30 14:36:21.308 | DEBUG    | ui.views.monitor_view:_on_task_details_loaded:316 - 统计数据: {'avg_daily': 1, 'running_days': 3, 'today_users': 0, 'total_users': 4}
2025-05-30 14:36:21.310 | DEBUG    | ui.views.monitor_view:_on_loading_started:620 - 加载中: 正在加载第 1 页数据...
2025-05-30 14:36:21.310 | INFO     | app.controllers.telegram_monitor_controller:load_task_users:396 - 加载任务用户数据: ID 2, 页码 1, 每页 10 条
2025-05-30 14:36:21.310 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-05-30 14:36:21.316 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-05-30 14:36:21.319 | INFO     | ui.views.monitor_view:_update_users_table:342 - 开始更新用户表格，收到4条数据
2025-05-30 14:36:21.319 | DEBUG    | ui.views.monitor_view:_update_users_table:343 - 分页信息：{'current_page': 1, 'page_size': 10, 'total_items': 4, 'total_pages': 1}
2025-05-30 14:36:21.320 | INFO     | ui.views.monitor_view:_update_users_table:360 - 示例数据[0]: {'join_date': '2025-05-29 17:23:09', 'keyword': '比特币', 'nickname': '共赢2 联盟', 'task_type': 'text', 'uid': '7942265654', 'username': 'bangqiwang'}
2025-05-30 14:36:21.320 | INFO     | ui.views.monitor_view:_update_users_table:360 - 示例数据[1]: {'join_date': '2025-05-26 18:48:01', 'keyword': '娱乐', 'nickname': 'SOSO搜搜 🤖 中文频道群组搜索 None', 'task_type': 'text', 'uid': '6549939461', 'username': ''}
2025-05-30 14:36:21.320 | INFO     | ui.views.monitor_view:_update_users_table:360 - 示例数据[2]: {'join_date': '2025-05-26 18:47:58', 'keyword': '开户, 时时彩', 'nickname': '极搜🔍中文搜索@JISO None', 'task_type': 'text', 'uid': '6920818412', 'username': ''}
2025-05-30 14:36:21.321 | INFO     | ui.views.monitor_view:_update_users_table:364 - 设置表格行数: 4
2025-05-30 14:36:21.323 | INFO     | ui.views.monitor_view:_update_users_table:388 - 用户表格更新完成
2025-05-30 14:36:21.324 | INFO     | app.controllers.telegram_monitor_controller:load_task_users:423 - 成功加载第 1 页用户数据，共 4 条
2025-05-30 14:36:21.324 | DEBUG    | ui.views.monitor_view:_on_loading_finished:625 - 加载完成
2025-05-30 14:36:21.324 | INFO     | app.controllers.telegram_monitor_controller:load_task_details:287 - 成功加载任务详情: 关键词监控
2025-05-30 14:36:21.325 | DEBUG    | ui.views.monitor_view:_on_loading_finished:625 - 加载完成
2025-05-30 14:36:44.180 | DEBUG    | ui.views.monitor_view:_on_task_item_clicked:258 - 任务项点击: 1
2025-05-30 14:36:44.183 | INFO     | app.controllers.telegram_monitor_controller:select_task:237 - 选择任务: 1
2025-05-30 14:36:44.184 | DEBUG    | ui.views.monitor_view:_on_task_selection_changed:281 - 任务选择变更: 1
2025-05-30 14:36:44.185 | DEBUG    | ui.views.monitor_view:_on_loading_started:620 - 加载中: 正在加载任务详情...
2025-05-30 14:36:44.185 | INFO     | app.controllers.telegram_monitor_controller:load_task_details:261 - 加载任务详情: ID 1
2025-05-30 14:36:44.186 | INFO     | app.services.monitor_service:get_task_detail:1018 - 获取任务详情: ID 1
2025-05-30 14:36:44.186 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-05-30 14:36:44.199 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-05-30 14:36:44.200 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-05-30 14:36:44.212 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-05-30 14:36:44.212 | DEBUG    | app.controllers.telegram_monitor_controller:load_task_details:271 - 获取到任务 1 的统计数据: {'total_users': 952, 'today_users': 0, 'avg_daily': 106, 'running_days': 9}
2025-05-30 14:36:44.213 | DEBUG    | ui.views.monitor_view:_on_task_details_loaded:309 - 任务详情加载完成: 1
2025-05-30 14:36:44.214 | DEBUG    | ui.views.monitor_view:_on_task_details_loaded:312 - 任务详情数据: {'created_at': '2025-05-21 01:05:08', 'description': '', 'id': '1', 'ignore_keywords': [], 'ignore_nicknames_rules': [], 'is_active': True, 'is_running': False, 'keywords': [], 'last_error': "所有账户监控设置均失败: object tuple can't be used in 'await' expression, object tuple can't be used in 'await' expression", 'monitor_messages': True, 'monitor_new_users': False, 'monitored_chats': [{'account_phone': '***********', 'chat_id': '**********', 'chat_title': '泰国华人交友聊天总群', 'id': 32}, {'account_phone': '***********', 'chat_id': '**********', 'chat_title': '东南亚博彩代理聊天群', 'id': 33}, {'account_phone': '***********', 'chat_id': '**********', 'chat_title': '柬埔寨泰国华人聊天群', 'id': 34}, {'account_phone': '***********', 'chat_id': '**********', 'chat_title': '赚钱项目灰产社区', 'id': 35}, {'account_phone': '***********', 'chat_id': '**********', 'chat_title': '区块节点NodeMining-Pool', 'id': 36}, {'account_phone': '***********', 'chat_id': '**********', 'chat_title': '区块节点NodeMining-Pool', 'id': 37}, {'account_phone': '***********', 'chat_id': '**********', 'chat_title': '深圳集团 - 藏楼（聊天', 'id': 38}, {'account_phone': '***********', 'chat_id': '**********', 'chat_title': '广州会所资源_(部分预览)', 'id': 39}, {'account_phone': '***********', 'chat_id': '**********', 'chat_title': '🔥入金话术馆', 'id': 40}, {'account_phone': '***********', 'chat_id': '**********', 'chat_title': 'wiegnwi', 'id': 41}, {'account_phone': '***********', 'chat_id': '**********', 'chat_title': '国外 话术 素材总部', 'id': 42}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '💡站长群 HostCLi.com 宝塔纯净版', 'id': 43}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '云泽社区', 'id': 44}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '🍒 萝莉屋18岁 资源群🦄', 'id': 45}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '柯基币-礦池-官方社区', 'id': 46}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '🈲 高中 学生资源库（加密）🎀', 'id': 47}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '🅰️中文搜片搜索👙自慰🩸抠逼🔍乱伦🗣口交', 'id': 48}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '中文算力MinerDog pool', 'id': 49}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '【官方】币安矿池置换中文群🇨🇳', 'id': 50}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '香港服务器交流群', 'id': 51}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '免费情色视频搜索🚀', 'id': 52}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '迷🦊 吃瓜 完整版破案‼️', 'id': 53}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '软件开发🪸搭建网站🪸网站开发', 'id': 54}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '国际联盟—海外实卡接码/邮寄公开群【支持担保】', 'id': 55}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': 'wiegnwi', 'id': 56}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '嘿嘿嘿', 'id': 57}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '泡妞笔记', 'id': 58}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '仙楼集团·资源发布站 @xianlou', 'id': 59}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '路易频道引流推广工作群', 'id': 60}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '神奇宝贝 @so588', 'id': 61}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': 'TGbox助手|群发|拉人|引流|群发助手', 'id': 62}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '比特币购买，比特币行情，比特币怎么买', 'id': 63}], 'name': '全部群组', 'stats': {'avg_daily': 106, 'running_days': 9, 'today_users': 0, 'total_users': 952}, 'updated_at': '2025-05-26 16:29:16'}
2025-05-30 14:36:44.216 | DEBUG    | ui.views.monitor_view:_on_task_details_loaded:316 - 统计数据: {'avg_daily': 106, 'running_days': 9, 'today_users': 0, 'total_users': 952}
2025-05-30 14:36:44.218 | DEBUG    | ui.views.monitor_view:_on_loading_started:620 - 加载中: 正在加载第 1 页数据...
2025-05-30 14:36:44.219 | INFO     | app.controllers.telegram_monitor_controller:load_task_users:396 - 加载任务用户数据: ID 1, 页码 1, 每页 10 条
2025-05-30 14:36:44.220 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-05-30 14:36:44.229 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-05-30 14:36:44.231 | INFO     | ui.views.monitor_view:_update_users_table:342 - 开始更新用户表格，收到10条数据
2025-05-30 14:36:44.231 | DEBUG    | ui.views.monitor_view:_update_users_table:343 - 分页信息：{'current_page': 1, 'page_size': 10, 'total_items': 967, 'total_pages': 97}
2025-05-30 14:36:44.232 | INFO     | ui.views.monitor_view:_update_users_table:360 - 示例数据[0]: {'join_date': '2025-05-29 17:15:39', 'keyword': '', 'nickname': 'san hejiu', 'task_type': 'text', 'uid': '7690383982', 'username': ''}
2025-05-30 14:36:44.232 | INFO     | ui.views.monitor_view:_update_users_table:360 - 示例数据[1]: {'join_date': '2025-05-29 17:15:26', 'keyword': '', 'nickname': '西港外送佳佳/打炮按摩 None', 'task_type': 'text', 'uid': '7991181298', 'username': ''}
2025-05-30 14:36:44.233 | INFO     | ui.views.monitor_view:_update_users_table:360 - 示例数据[2]: {'join_date': '2025-05-29 17:15:07', 'keyword': '', 'nickname': '黑子 None', 'task_type': 'text', 'uid': '6654528704', 'username': 'BqZ8D3cFkOy7p4'}
2025-05-30 14:36:44.233 | INFO     | ui.views.monitor_view:_update_users_table:364 - 设置表格行数: 10
2025-05-30 14:36:44.240 | INFO     | ui.views.monitor_view:_update_users_table:388 - 用户表格更新完成
2025-05-30 14:36:44.240 | INFO     | app.controllers.telegram_monitor_controller:load_task_users:423 - 成功加载第 1 页用户数据，共 10 条
2025-05-30 14:36:44.241 | DEBUG    | ui.views.monitor_view:_on_loading_finished:625 - 加载完成
2025-05-30 14:36:44.241 | INFO     | app.controllers.telegram_monitor_controller:load_task_details:287 - 成功加载任务详情: 全部群组
2025-05-30 14:36:44.242 | DEBUG    | ui.views.monitor_view:_on_loading_finished:625 - 加载完成
2025-05-30 14:36:57.554 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-05-30 14:36:57.554 | DEBUG    | utils.client_http:post:68 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-05-30 14:37:57.560 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-05-30 14:37:57.561 | DEBUG    | utils.client_http:post:68 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-05-30 14:38:57.553 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-05-30 14:38:57.554 | DEBUG    | utils.client_http:post:68 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-05-30 14:39:57.552 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-05-30 14:39:57.553 | DEBUG    | utils.client_http:post:68 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-05-30 14:40:57.553 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-05-30 14:40:57.553 | DEBUG    | utils.client_http:post:68 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-05-30 14:41:57.552 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-05-30 14:41:57.553 | DEBUG    | utils.client_http:post:68 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-05-30 14:42:57.556 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-05-30 14:42:57.557 | DEBUG    | utils.client_http:post:68 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-05-30 14:43:57.553 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-05-30 14:43:57.554 | DEBUG    | utils.client_http:post:68 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-05-30 14:44:57.553 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-05-30 14:44:57.553 | DEBUG    | utils.client_http:post:68 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-05-30 14:45:30.924 | INFO     | ui.main_window:closeEvent:335 - MainWindow: 接收到关闭事件
2025-05-30 14:45:30.937 | INFO     | ui.main_window:_cleanup_before_quit:228 - MainWindow: 执行清理资源...
2025-05-30 14:45:30.945 | INFO     | core.telegram.client_worker:stop:110 - 正在停止Telegram客户端工作线程
2025-05-30 14:45:30.953 | INFO     | core.telegram.client_manager:cleanup_async:1140 - 开始清理资源并断开所有连接
2025-05-30 14:45:30.954 | INFO     | core.telegram.client_manager:disconnect_all_clients:725 - 正在断开所有客户端连接
2025-05-30 14:45:30.954 | INFO     | core.telegram.client_manager:cleanup_async:1159 - 资源清理完成
2025-05-30 14:45:31.464 | INFO     | core.telegram.client_manager:cleanup_async:1140 - 开始清理资源并断开所有连接
2025-05-30 14:45:31.464 | INFO     | core.telegram.client_manager:disconnect_all_clients:725 - 正在断开所有客户端连接
2025-05-30 14:45:31.465 | INFO     | core.telegram.client_manager:cleanup_async:1159 - 资源清理完成
2025-05-30 14:45:31.980 | INFO     | core.telegram.client_worker:run:99 - Telegram客户端工作线程已退出
2025-05-30 14:45:31.981 | INFO     | core.telegram.client_worker:stop:124 - Telegram客户端工作线程已停止
2025-05-30 14:45:31.981 | INFO     | ui.main_window:_cleanup_before_quit:237 - TelegramClientWorker 已停止。
2025-05-30 14:45:31.981 | INFO     | ui.main_window:_cleanup_before_quit:241 - MainWindow 清理完成
2025-05-30 14:45:31.997 | INFO     | __main__:main:111 - 应用程序已正常退出
