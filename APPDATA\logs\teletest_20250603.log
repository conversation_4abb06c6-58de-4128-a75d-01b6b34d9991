2025-06-03 16:17:12.167 | INFO     | utils.logger:init_logger:98 - 日志系统初始化完成，默认级别: DEBUG，日志目录: h:\PyProject\TeleTest\APPDATA\logs
2025-06-03 16:17:14.198 | INFO     | data.database:init_database:31 - 初始化数据库连接: h:\PyProject\TeleTest\APPDATA\database\telegram_manager.db
2025-06-03 16:17:14.291 | DEBUG    | data.database:init_database:58 - 创建数据库表
2025-06-03 16:17:14.302 | INFO     | data.database:init_database:61 - 数据库初始化完成
2025-06-03 16:17:15.079 | DEBUG    | app.controllers.auth_controller:initialize_software:43 - 控制器: 初始化软件 app_id=1000, version=1.0
2025-06-03 16:17:15.080 | DEBUG    | utils.client_http:get:41 - 发送GET请求: https://server.xile188.com/api/user/1000/windowns10/1.0/ini
2025-06-03 16:17:15.564 | DEBUG    | core.auth.api_service:init_software:66 - 初始化数据解密成功: {'new_content': '1.多账户管理\n2.ip池管理\n3.消息监听\n4.消息群发\n5.拉人入群', 'new_url': None, 'notice': {'id': '2', 'content': '公告内容测试3', 'time': '**********'}}
2025-06-03 16:17:15.579 | INFO     | app.controllers.auth_controller:login:61 - 控制器: 用户登录 account=zhangsan03
2025-06-03 16:17:18.826 | INFO     | core.auth.api_service:login:127 - 用户登录: account=zhangsan03
2025-06-03 16:17:18.827 | DEBUG    | utils.client_http:post:68 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/logon
2025-06-03 16:17:19.073 | INFO     | core.auth.api_service:get_user_info:347 - 获取用户信息
2025-06-03 16:17:19.073 | DEBUG    | utils.client_http:post:68 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/info
2025-06-03 16:17:19.282 | INFO     | core.auth.api_service:get_user_info:375 - 获取用户信息成功
2025-06-03 16:17:19.291 | INFO     | __main__:on_auth_completed:93 - 用户登录成功，正在启动主窗口...
2025-06-03 16:17:19.313 | INFO     | core.telegram.client_worker:__init__:63 - Telegram客户端工作线程初始化
2025-06-03 16:17:19.319 | INFO     | core.telegram.client_worker:run:67 - Telegram客户端工作线程开始运行
2025-06-03 16:17:19.324 | INFO     | core.telegram.client_manager:__init__:66 - Telegram客户端管理器初始化
2025-06-03 16:17:19.325 | INFO     | core.telegram.user_manager:__init__:31 - Telegram用户管理器初始化
2025-06-03 16:17:19.326 | INFO     | core.telegram.group_manager:__init__:36 - Telegram群组管理器初始化
2025-06-03 16:17:19.327 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-06-03 16:17:19.328 | INFO     | core.telegram.monitor_manager:__init__:26 - Telegram监控管理器初始化
2025-06-03 16:17:19.329 | INFO     | core.telegram.telegram_service:__init__:39 - Telegram服务初始化完成
2025-06-03 16:17:19.330 | INFO     | ui.main_window:_initialize_core_components:79 - MainWindow: 初始化核心组件...
2025-06-03 16:17:19.333 | INFO     | app.services.account_service:__init__:42 - 账户服务初始化
2025-06-03 16:17:19.334 | INFO     | app.controllers.account_controller:__init__:81 - 账户控制器初始化
2025-06-03 16:17:19.334 | INFO     | app.services.monitor_service:__init__:39 - 初始化监控任务服务
2025-06-03 16:17:19.335 | INFO     | core.monitor.filter:__init__:26 - 初始化消息过滤器
2025-06-03 16:17:19.335 | INFO     | core.monitor.extractor:__init__:24 - 初始化关键词提取器
2025-06-03 16:17:19.335 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-06-03 16:17:19.336 | INFO     | core.monitor.notifier:__init__:90 - 初始化通知管理器
2025-06-03 16:17:19.336 | INFO     | app.controllers.telegram_monitor_controller:__init__:64 - 初始化 Telegram 监控控制器
2025-06-03 16:17:19.337 | DEBUG    | app.controllers.telegram_monitor_controller:__init__:82 - 控制器初始化完成
2025-06-03 16:17:19.364 | INFO     | core.proxy.proxy_task_manager:run:284 - 代理任务管理器线程启动
2025-06-03 16:17:19.585 | INFO     | ui.views.monitor_view:__init__:48 - 初始化监控视图
2025-06-03 16:17:19.585 | DEBUG    | ui.views.monitor_view:_connect_signals:80 - 连接控制器信号
2025-06-03 16:17:19.835 | INFO     | ui.views.proxy_view:__init__:96 - 初始化视图，加载第一页代理数据
2025-06-03 16:17:20.222 | INFO     | __main__:on_auth_completed:97 - 主窗口已启动
2025-06-03 16:17:20.435 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:88 - 尝试连接监听日志信号...
2025-06-03 16:17:20.942 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:90 - 监听日志信号连接成功
2025-06-03 16:17:20.943 | INFO     | core.auth.api_service:verify_vip:425 - 会员验证接口调用
2025-06-03 16:17:20.944 | DEBUG    | utils.client_http:post:68 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/vip
2025-06-03 16:17:20.945 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-03 16:17:20.950 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-03 16:17:20.955 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:145 - 加载监控任务列表 (搜索关键词: '')
2025-06-03 16:17:20.956 | DEBUG    | ui.views.monitor_view:_on_loading_started:620 - 加载中: 正在加载任务列表...
2025-06-03 16:17:20.956 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-03 16:17:20.974 | INFO     | app.services.proxy_service:refresh_proxies:712 - 刷新代理列表: offset=0, limit=20
2025-06-03 16:17:20.974 | INFO     | core.proxy.proxy_task_manager:submit_task:172 - 提交耗时任务到子线程: refresh_proxies_0_20
2025-06-03 16:17:20.975 | DEBUG    | app.services.proxy_service:get_proxies:500 - 获取代理列表: offset=0, limit=20
2025-06-03 16:17:20.975 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-03 16:17:20.976 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-03 16:17:20.979 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-03 16:17:20.981 | INFO     | app.controllers.invite_controller:get_invite_tasks:38 - 获取邀请任务列表, status=None
2025-06-03 16:17:20.982 | INFO     | app.services.invite_service:get_invite_tasks:121 - 获取邀请任务列表, status=None
2025-06-03 16:17:20.982 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-03 16:17:20.991 | INFO     | app.services.invite_service:task_initialize:39 - 初始化邀请服务，重置运行中的任务为暂停状态
2025-06-03 16:17:20.992 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-03 16:17:21.039 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-03 16:17:21.041 | INFO     | core.proxy.proxy_task_manager:task_wrapper:199 - 任务 refresh_proxies_0_20 完成
2025-06-03 16:17:21.318 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-03 16:17:21.324 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-03 16:17:21.330 | DEBUG    | ui.views.monitor_view:_refresh_task_list:175 - 刷新任务列表，共2个任务
2025-06-03 16:17:21.338 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-03 16:17:21.340 | INFO     | app.services.invite_service:task_initialize:48 - 成功重置 0 个任务状态
2025-06-03 16:17:21.340 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-03 16:17:21.349 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-03 16:17:21.350 | INFO     | app.services.account_service:get_all_groups:96 - 获取所有账户分组成功, 共 1 个
2025-06-03 16:17:21.351 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-03 16:17:21.354 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-03 16:17:21.356 | DEBUG    | ui.views.account_view:_on_groups_loaded:442 - 分组加载完成: 1个分组
2025-06-03 16:17:21.368 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-03 16:17:21.372 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-03 16:17:21.449 | WARNING  | core.auth.api_service:verify_vip:441 - 会员验证失败: 验证失败
2025-06-03 16:17:21.604 | INFO     | app.services.account_service:get_all_accounts:289 - 获取所有账户成功, 共 2 个
2025-06-03 16:17:21.605 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-03 16:17:21.714 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-03 16:17:21.716 | DEBUG    | ui.views.account_view:_on_accounts_loaded:575 - 账户加载完成: 2个账户
2025-06-03 16:17:21.743 | DEBUG    | ui.views.account_view:_update_group_counts:184 - 分组计数已更新: {1: 1}
2025-06-03 16:17:21.749 | INFO     | ui.views.account_view:_auto_login_accounts:630 - 开始调用控制器进行 2 个账户的自动登录。
2025-06-03 16:17:21.749 | INFO     | app.services.account_service:batch_auto_login:1153 - 服务层：开始批量自动登录 2 个账户, 最大并发: 5
2025-06-03 16:17:21.750 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-03 16:17:21.752 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-03 16:17:21.752 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-03 16:17:21.758 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-06-03 16:17:21.780 | INFO     | app.controllers.telegram_monitor_controller:_load_total_users_count_internal:191 - 成功加载总体统计数据: 总用户数 958, 今日采集 0, 日均采集 74, 运行天数 13
2025-06-03 16:17:21.781 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:156 - 成功加载 2 个任务
2025-06-03 16:17:21.782 | DEBUG    | ui.views.monitor_view:_on_loading_finished:625 - 加载完成
2025-06-03 16:17:21.786 | INFO     | app.services.account_service:get_all_accounts:289 - 获取所有账户成功, 共 2 个
2025-06-03 16:17:21.787 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-03 16:17:21.787 | INFO     | app.services.account_service:batch_auto_login:1192 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-06-03 16:17:21.788 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-03 16:17:21.793 | DEBUG    | ui.views.account_view:_update_group_counts:184 - 分组计数已更新: {1: 1}
2025-06-03 16:17:21.793 | DEBUG    | ui.views.account_view:_init_cache:151 - 账户缓存初始化完成，共 2 个账户
2025-06-03 16:17:21.801 | DEBUG    | ui.views.account_view:_on_accounts_loaded:575 - 账户加载完成: 2个账户
2025-06-03 16:17:21.833 | DEBUG    | ui.views.account_view:_update_group_counts:184 - 分组计数已更新: {1: 1}
2025-06-03 16:17:21.841 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-03 16:17:21.845 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=True
2025-06-03 16:17:21.847 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=True
2025-06-03 16:17:21.849 | INFO     | ui.views.proxy_view:_load_initial_data:1044 - 初始数据加载完成，共 3 个代理
2025-06-03 16:17:21.852 | INFO     | app.services.account_service:batch_auto_login:1192 - 服务层：账户 *********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-06-03 16:17:21.852 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-03 16:17:21.854 | INFO     | app.services.account_service:batch_auto_login:1262 - 服务层：准备调用核心层处理 2 个账户的自动登录。
2025-06-03 16:17:21.855 | INFO     | app.services.account_service:batch_auto_login:1272 - 服务层：设置核心层任务超时为 120 秒。
2025-06-03 16:17:21.855 | INFO     | core.telegram.client_manager:batch_auto_login:1220 - 开始批量自动登录, 共 2 个账户, 最大并发 5
2025-06-03 16:17:21.857 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-03 16:17:21.858 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:256 - 创建客户端: h:\PyProject\TeleTest\APPDATA\sessions\+***********.session, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-06-03 16:17:21.859 | DEBUG    | core.telegram.client_manager:get_proxy:93 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-06-03 16:17:21.860 | DEBUG    | core.telegram.client_manager:get_proxy:140 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-06-03 16:17:21.868 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:256 - 创建客户端: h:\PyProject\TeleTest\APPDATA\sessions\***********.session, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-06-03 16:17:21.868 | DEBUG    | core.telegram.client_manager:get_proxy:93 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-06-03 16:17:21.869 | DEBUG    | core.telegram.client_manager:get_proxy:140 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-06-03 16:17:21.900 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-03 16:17:21.919 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-03 16:17:23.826 | ERROR    | core.telegram.client_manager:_connect_client:217 - 认证密钥错误 (AuthKeyError): The authorization key (session file) was used under two different IP addresses simultaneously, and can no longer be used. Use the same session exclusively, or use different sessions (caused by InvokeWithLayerRequest(InitConnectionRequest(GetConfigRequest)))
2025-06-03 16:17:23.943 | ERROR    | core.telegram.client_manager:_connect_client:217 - 认证密钥错误 (AuthKeyError): The authorization key (session file) was used under two different IP addresses simultaneously, and can no longer be used. Use the same session exclusively, or use different sessions (caused by InvokeWithLayerRequest(InitConnectionRequest(GetConfigRequest)))
2025-06-03 16:17:25.958 | INFO     | core.telegram.client_manager:batch_auto_login:1369 - 批量登录完成: 总计 2, 成功 0, 失败 2
2025-06-03 16:17:26.866 | INFO     | app.services.account_service:batch_auto_login:1293 - 服务层：核心层登录完成: 成功 0, 失败 2 / 2
2025-06-03 16:17:34.204 | INFO     | ui.views.account_view:_on_delete_account:365 - 请求删除账号: ***********
2025-06-03 16:17:34.204 | INFO     | ui.views.account_view:_on_delete_account:369 - 查找账号ID: ***********
2025-06-03 16:17:35.157 | INFO     | app.services.account_service:delete_account:359 - 删除账户: 2, 删除session: True
2025-06-03 16:17:35.174 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-03 16:17:35.408 | INFO     | core.telegram.client_manager:disconnect_client:707 - 正在断开客户端连接: ***********
2025-06-03 16:17:35.549 | WARNING  | core.telegram.client_manager:disconnect_client:710 - 找不到客户端: ***********
2025-06-03 16:17:35.792 | INFO     | data.repositories.account_repo:delete_account:450 - 删除账户成功: ID=2
2025-06-03 16:17:35.804 | INFO     | app.services.account_service:delete_account:388 - 删除session文件: H:\PyProject\TeleTest\APPDATA\sessions\***********.session
2025-06-03 16:17:35.804 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-03 16:17:35.805 | INFO     | ui.views.account_view:_on_account_deleted:642 - 账户删除成功: ID=2
2025-06-03 16:17:35.806 | DEBUG    | ui.views.account_view:_update_group_counts:184 - 分组计数已更新: {}
2025-06-03 16:17:35.814 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-03 16:17:35.828 | INFO     | app.services.account_service:get_all_accounts:289 - 获取所有账户成功, 共 1 个
2025-06-03 16:17:35.829 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-03 16:17:35.831 | DEBUG    | ui.views.account_view:_on_accounts_loaded:575 - 账户加载完成: 1个账户
2025-06-03 16:17:35.849 | DEBUG    | ui.views.account_view:_update_group_counts:184 - 分组计数已更新: {}
2025-06-03 16:17:36.844 | INFO     | ui.views.account_view:_on_delete_account:365 - 请求删除账号: +***********
2025-06-03 16:17:36.844 | INFO     | ui.views.account_view:_on_delete_account:369 - 查找账号ID: +***********
2025-06-03 16:17:37.855 | INFO     | app.services.account_service:delete_account:359 - 删除账户: 1, 删除session: True
2025-06-03 16:17:37.856 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-03 16:17:38.041 | INFO     | core.telegram.client_manager:disconnect_client:707 - 正在断开客户端连接: +***********
2025-06-03 16:17:38.041 | WARNING  | core.telegram.client_manager:disconnect_client:710 - 找不到客户端: +***********
2025-06-03 16:17:38.198 | INFO     | data.repositories.account_repo:delete_account:450 - 删除账户成功: ID=1
2025-06-03 16:17:38.208 | INFO     | app.services.account_service:delete_account:388 - 删除session文件: H:\PyProject\TeleTest\APPDATA\sessions\+***********.session
2025-06-03 16:17:38.208 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-03 16:17:38.209 | INFO     | ui.views.account_view:_on_account_deleted:642 - 账户删除成功: ID=1
2025-06-03 16:17:38.209 | DEBUG    | ui.views.account_view:_update_group_counts:184 - 分组计数已更新: {}
2025-06-03 16:17:38.216 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-03 16:17:38.230 | INFO     | app.services.account_service:get_all_accounts:289 - 获取所有账户成功, 共 0 个
2025-06-03 16:17:38.230 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-03 16:17:38.232 | DEBUG    | ui.views.account_view:_on_accounts_loaded:575 - 账户加载完成: 0个账户
2025-06-03 16:17:38.232 | DEBUG    | ui.views.account_view:_update_group_counts:184 - 分组计数已更新: {}
2025-06-03 16:17:40.373 | INFO     | ui.views.import_sessions_view:refresh_proxy_list_async:422 - 开始刷新代理列表...
2025-06-03 16:17:40.373 | INFO     | app.services.account_service:get_proxy_ips:1032 - 获取有效代理IP列表（包含绑定计数）
2025-06-03 16:17:40.374 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-03 16:17:40.447 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-03 16:17:40.448 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-03 16:17:40.452 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-03 16:17:40.453 | INFO     | app.services.account_service:get_proxy_ips:1084 - 成功获取 3 个有效代理（已附带绑定计数）
2025-06-03 16:17:40.454 | INFO     | app.controllers.account_controller:get_proxy_ips:537 - 获取到3个有效代理IP
2025-06-03 16:17:40.463 | INFO     | ui.views.import_sessions_view:refresh_groups_list_async:469 - 开始刷新分组列表...
2025-06-03 16:17:40.464 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-03 16:17:40.468 | INFO     | app.services.account_service:get_all_groups:96 - 获取所有账户分组成功, 共 1 个
2025-06-03 16:17:40.468 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-03 16:18:03.541 | INFO     | ui.views.import_sessions_view:login_selected_sessions_async:545 - 开始导入并登录 2 个会话，并发数: 5，文件: [{'session_file': 'H:\\PyProject\\TeleTest\\APPDATA\\+***********.session', 'proxy_config': {'id': 3, 'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '**************', 'port': 10001, 'username': None, 'password': None}, 'group_id': 1, 'row_index': 0}, {'session_file': 'H:\\PyProject\\TeleTest\\APPDATA\\+***********.session', 'proxy_config': {'id': 2, 'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '**************', 'port': 10061, 'username': None, 'password': None}, 'group_id': -1, 'row_index': 1}]
2025-06-03 16:18:03.542 | INFO     | app.services.account_service:batch_import_sessions:855 - 服务层(重构)：开始批量导入 2 个session文件，最大并发: 5
2025-06-03 16:18:03.543 | INFO     | app.services.account_service:batch_import_sessions:906 - 服务层：准备使用 IP 池代理 **************:10001 (ID: 3) for session H:\PyProject\TeleTest\APPDATA\+***********.session
2025-06-03 16:18:03.543 | INFO     | app.services.account_service:batch_import_sessions:906 - 服务层：准备使用 IP 池代理 **************:10061 (ID: 2) for session H:\PyProject\TeleTest\APPDATA\+***********.session
2025-06-03 16:18:03.544 | INFO     | app.services.account_service:batch_import_sessions:938 - 服务层：准备调用核心层处理 2 个session。
2025-06-03 16:18:03.544 | INFO     | core.telegram.client_manager:batch_import_sessions:549 - 核心层：开始批量导入 2 个session文件，最大并发 5
2025-06-03 16:18:03.545 | INFO     | core.telegram.client_manager:import_session:457 - 正在导入session: H:\PyProject\TeleTest\APPDATA\+***********.session，代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '**************', 'port': 10001}
2025-06-03 16:18:03.546 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:256 - 创建客户端: H:\PyProject\TeleTest\APPDATA\+***********.session, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '**************', 'port': 10001}
2025-06-03 16:18:03.549 | DEBUG    | core.telegram.client_manager:get_proxy:93 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '**************', 'port': 10001}
2025-06-03 16:18:03.551 | DEBUG    | core.telegram.client_manager:get_proxy:140 - get_proxy: 使用 ip_pool 代理 **************:10001，转换结果: (<ProxyType.SOCKS5: 2>, '**************', 10001)
2025-06-03 16:18:03.556 | INFO     | core.telegram.client_manager:import_session:457 - 正在导入session: H:\PyProject\TeleTest\APPDATA\+***********.session，代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '**************', 'port': 10061}
2025-06-03 16:18:03.557 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:256 - 创建客户端: H:\PyProject\TeleTest\APPDATA\+***********.session, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '**************', 'port': 10061}
2025-06-03 16:18:03.559 | DEBUG    | core.telegram.client_manager:get_proxy:93 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '**************', 'port': 10061}
2025-06-03 16:18:03.561 | DEBUG    | core.telegram.client_manager:get_proxy:140 - get_proxy: 使用 ip_pool 代理 **************:10061，转换结果: (<ProxyType.SOCKS5: 2>, '**************', 10061)
2025-06-03 16:18:08.760 | ERROR    | core.telegram.client_manager:_connect_client:217 - 认证密钥错误 (AuthKeyError): The authorization key (session file) was used under two different IP addresses simultaneously, and can no longer be used. Use the same session exclusively, or use different sessions (caused by InvokeWithLayerRequest(InitConnectionRequest(GetConfigRequest)))
2025-06-03 16:18:08.768 | WARNING  | core.telegram.client_manager:import_session:467 - 导入session失败, 连接错误: H:\PyProject\TeleTest\APPDATA\+***********.session
2025-06-03 16:18:12.352 | INFO     | core.telegram.client_manager:_connect_client:204 - 连接成功!
2025-06-03 16:18:16.039 | INFO     | core.telegram.client_manager:import_session:491 - 导入的用户phone: ***********，abcegwe6g4, 长, 三
2025-06-03 16:18:16.041 | INFO     | core.telegram.client_manager:import_session:517 - Session文件已从 H:\PyProject\TeleTest\APPDATA\+***********.session 复制到: h:\PyProject\TeleTest\APPDATA\sessions\***********.session
2025-06-03 16:18:16.042 | INFO     | core.telegram.client_manager:import_session:529 - 导入session成功: H:\PyProject\TeleTest\APPDATA\+***********.session -> ***********
2025-06-03 16:18:16.042 | INFO     | core.telegram.client_manager:batch_import_sessions:618 - 核心层：批量导入session完成: 总计 2, 成功 1, 失败 1
2025-06-03 16:18:16.558 | INFO     | app.services.account_service:batch_import_sessions:953 - 服务层：核心层返回结果: {'total': 2, 'success': 1, 'failed': 1, 'details': [{'path': 'H:\\PyProject\\TeleTest\\APPDATA\\+***********.session', 'success': True, 'result': {'id': **********, 'first_name': '长', 'last_name': '三', 'username': 'abcegwe6g4', 'phone': '***********', 'bot': False, 'verified': False, 'restricted': False, 'photo': False, 'status': <telethon.tl.types.UserStatusOnline object at 0x0000017BD2A4D110>, 'status_type': 'online', 'session_file': 'h:\\PyProject\\TeleTest\\APPDATA\\sessions\\***********.session'}}, {'path': 'H:\\PyProject\\TeleTest\\APPDATA\\+***********.session', 'success': False, 'result': '认证密钥错误 (AuthKeyError): The authorization key (session file) was used under two different IP addresses simultaneously, and can no longer be used. Use the same session exclusively, or use different sessions (caused by InvokeWithLayerRequest(InitConnectionRequest(GetConfigRequest)))'}]}
2025-06-03 16:18:16.559 | INFO     | app.services.account_service:add_account:190 - 准备添加账户: ***********, 传入代理类型: ip_pool, 传入代理ID: 3
2025-06-03 16:18:16.559 | INFO     | app.services.account_service:add_account:218 - 规范化后添加账户: ***********, 数据库代理类型: ip_pool, 数据库代理ID: 3
2025-06-03 16:18:16.560 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-03 16:18:16.563 | INFO     | data.repositories.account_repo:create_account:287 - 创建账户成功: ***********, 代理类型: ip_pool
2025-06-03 16:18:16.578 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-03 16:18:16.579 | INFO     | ui.views.account_view:_on_notify:661 - 收到通知: 导入通知_0
2025-06-03 16:18:16.586 | DEBUG    | ui.views.import_sessions_view:_handle_controller_notification:588 - ImportSessionsView 收到通知: event='导入通知_0', type='success', data='{'message': '成功', 'success': True, 'account_data': {'id': 1, 'phone': '***********', 'session_file': 'h:\\PyProject\\TeleTest\\APPDATA\\sessions\\***********.session', 'first_name': '长', 'last_name': '三', 'username': 'abcegwe6g4', 'bio': '', 'profile_photo': '', 'is_active': True, 'is_connected': True, 'has_2fa': False, 'last_connected': '2025-06-03T16:18:16.560759', 'last_active': None, 'created_at': '2025-06-03T16:18:16.563750', 'updated_at': '2025-06-03T16:18:16.563750', 'proxy_id': 3, 'proxy_type': 'ip_pool', 'group_id': 1}}'
2025-06-03 16:18:16.595 | WARNING  | app.services.account_service:batch_import_sessions:1009 - 服务层：核心层导入 H:\PyProject\TeleTest\APPDATA\+***********.session 失败: 认证密钥错误 (AuthKeyError): The authorization key (session file) was used under two different IP addresses simultaneously, and can no longer be used. Use the same session exclusively, or use different sessions (caused by InvokeWithLayerRequest(InitConnectionRequest(GetConfigRequest)))
2025-06-03 16:18:16.596 | INFO     | ui.views.account_view:_on_notify:661 - 收到通知: 导入通知_1
2025-06-03 16:18:16.602 | DEBUG    | ui.views.import_sessions_view:_handle_controller_notification:588 - ImportSessionsView 收到通知: event='导入通知_1', type='error', data='{'message': '导入失败: 认证密钥错误 (AuthKeyError): The authorization key (session file) was used under two different IP addresses simultaneously, and can no longer be used. Use the same session exclusively, or use different sessions (caused by InvokeWithLayerRequest(InitConnectionRequest(GetConfigRequest)))', 'success': False}'
2025-06-03 16:18:16.618 | INFO     | app.controllers.account_controller:_on_service_batch_accounts_added:471 - 控制器：接收到 1 个新添加账户的数据，准备通知UI更新主列表。
2025-06-03 16:18:16.619 | INFO     | app.services.account_service:batch_import_sessions:1022 - 服务层：批量导入完成: 1 个成功添加至数据库, 1 个失败。
2025-06-03 16:18:16.620 | INFO     | ui.views.import_sessions_view:_handle_batch_import_summary:575 - 批量导入摘要: 批量导入完成: 1 个成功添加至数据库, 1 个失败。
2025-06-03 16:18:16.631 | INFO     | ui.views.account_view:_on_new_accounts_added:675 - 收到 1 个新导入的账户数据
2025-06-03 16:18:16.642 | DEBUG    | ui.views.account_view:_update_group_counts:184 - 分组计数已更新: {1: 1}
2025-06-03 16:18:16.648 | DEBUG    | ui.views.account_view:_update_group_counts:184 - 分组计数已更新: {1: 1}
2025-06-03 16:18:19.274 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-03 16:18:19.274 | DEBUG    | utils.client_http:post:68 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-03 16:18:29.626 | INFO     | app.services.account_service:refresh_account_info:595 - 刷新账户信息: 1
2025-06-03 16:18:29.627 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-03 16:18:29.637 | INFO     | core.telegram.user_manager:get_user_info:42 - 正在获取用户信息: ***********
2025-06-03 16:18:33.280 | INFO     | core.telegram.user_manager:get_user_info:67 - 获取用户信息成功: ***********
2025-06-03 16:18:33.662 | INFO     | data.repositories.account_repo:update_account:378 - 更新账户成功: 1
2025-06-03 16:18:33.671 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-03 16:18:35.828 | INFO     | app.controllers.send_msg_controller:delete_task:128 - 删除任务: 1
2025-06-03 16:18:35.829 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-03 16:18:36.168 | INFO     | data.repositories.message_repo:delete_target_logs_by_task:136 - 删除任务1的所有目标日志，共314条
2025-06-03 16:18:36.175 | INFO     | data.repositories.message_repo:delete_task:73 - 消息任务已删除, ID: 1
2025-06-03 16:18:36.183 | INFO     | app.controllers.send_msg_controller:_on_task_deleted:61 - 收到任务删除事件: 1
2025-06-03 16:18:36.184 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-03 16:18:36.194 | DEBUG    | app.controllers.send_msg_controller:get_all_tasks:88 - 获取所有任务: 页码=1, 每页=50
2025-06-03 16:18:36.194 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-03 16:18:36.196 | INFO     | ui.views.send_msg_view:_on_task_deleted:739 - 收到任务删除事件: 1
2025-06-03 16:18:36.204 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-03 16:18:36.209 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-03 16:18:36.262 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-03 16:18:36.278 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-03 16:18:36.302 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-03 16:18:36.306 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-03 16:18:36.317 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-03 16:18:37.798 | INFO     | app.controllers.send_msg_controller:delete_task:128 - 删除任务: 2
2025-06-03 16:18:37.799 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-03 16:18:37.879 | INFO     | data.repositories.message_repo:delete_target_logs_by_task:136 - 删除任务2的所有目标日志，共1条
2025-06-03 16:18:37.881 | INFO     | data.repositories.message_repo:delete_task:73 - 消息任务已删除, ID: 2
2025-06-03 16:18:37.886 | INFO     | app.controllers.send_msg_controller:_on_task_deleted:61 - 收到任务删除事件: 2
2025-06-03 16:18:37.887 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-03 16:18:37.895 | DEBUG    | app.controllers.send_msg_controller:get_all_tasks:88 - 获取所有任务: 页码=1, 每页=50
2025-06-03 16:18:37.895 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-03 16:18:37.897 | INFO     | ui.views.send_msg_view:_on_task_deleted:739 - 收到任务删除事件: 2
2025-06-03 16:18:37.924 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-03 16:18:38.029 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-03 16:18:38.030 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-03 16:18:38.032 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-03 16:18:38.051 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-03 16:18:38.053 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-03 16:18:38.057 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-03 16:18:39.404 | INFO     | ui.views.add_msg_task_view:_load_initial_data:136 - 加载初始数据
2025-06-03 16:18:39.459 | INFO     | ui.views.add_msg_task_view:_load_account_groups:146 - 加载账户分组
2025-06-03 16:18:39.460 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-03 16:18:39.621 | INFO     | app.services.account_service:get_all_groups:96 - 获取所有账户分组成功, 共 1 个
2025-06-03 16:18:39.621 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-03 16:18:39.622 | DEBUG    | ui.views.account_view:_on_groups_loaded:442 - 分组加载完成: 1个分组
2025-06-03 16:18:39.623 | INFO     | ui.views.add_msg_task_view:_load_account_groups:162 - 加载账户分组数据: 1个分组
2025-06-03 16:18:39.624 | INFO     | ui.views.add_msg_task_view:_load_accounts_by_group:177 - 根据分组ID加载账户: -1
2025-06-03 16:18:39.625 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-03 16:18:39.632 | INFO     | app.services.account_service:get_all_accounts:289 - 获取所有账户成功, 共 1 个
2025-06-03 16:18:39.632 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-03 16:18:39.634 | INFO     | ui.views.add_msg_task_view:_load_accounts_by_group:213 - 已加载 1 个账户到列表
2025-06-03 16:18:39.634 | DEBUG    | ui.views.account_view:_on_accounts_loaded:575 - 账户加载完成: 1个账户
2025-06-03 16:18:39.645 | DEBUG    | ui.views.account_view:_update_group_counts:184 - 分组计数已更新: {1: 1}
2025-06-03 16:19:05.593 | INFO     | ui.views.add_msg_task_view:_toggleSelectAllGroups:362 - 全选账户
2025-06-03 16:19:08.091 | INFO     | app.controllers.send_msg_controller:create_task:174 - 创建任务: ddv6_com
2025-06-03 16:19:08.093 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-03 16:19:08.098 | INFO     | data.repositories.message_repo:add_task:36 - 消息任务已添加, ID: 1, 名称: ddv6_com, 账户切换时间: 10-30秒, 消息间隔: 60-180秒
2025-06-03 16:19:08.106 | INFO     | app.controllers.send_msg_controller:_on_task_created:49 - 收到任务创建事件: 1
2025-06-03 16:19:08.107 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-03 16:19:08.108 | INFO     | app.controllers.send_msg_controller:create_task:180 - 任务创建成功: ID=1, 名称=ddv6_com
2025-06-03 16:19:08.132 | INFO     | ui.views.send_msg_view:_on_task_created:724 - 收到任务创建事件: 1
2025-06-03 16:19:08.172 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-03 16:19:08.318 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-03 16:19:08.332 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-03 16:19:08.352 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-03 16:19:08.353 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-03 16:19:08.363 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-03 16:19:10.081 | INFO     | app.controllers.send_msg_controller:start_task:148 - 启动任务: 1
2025-06-03 16:19:10.082 | INFO     | app.services.message_sending_service:start_task:212 - 启动群发任务 [Task ID: 1]
2025-06-03 16:19:10.082 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-03 16:19:10.097 | INFO     | data.repositories.message_repo:update_task:64 - 消息任务已更新, ID: 1, 账户切换时间: 10-30秒, 消息间隔: 60-180秒
2025-06-03 16:19:10.103 | INFO     | app.controllers.send_msg_controller:_on_task_status_changed:67 - 收到任务状态变更事件: 1 -> running
2025-06-03 16:19:10.105 | INFO     | ui.views.send_msg_view:_on_task_status_changed:748 - 收到任务状态变更事件: 1 -> running
2025-06-03 16:19:10.109 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-03 16:19:10.124 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-03 16:19:10.127 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-03 16:19:10.129 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-03 16:19:10.135 | DEBUG    | app.services.message_sending_service:start_task:269 - 处理任务批次 [Task ID: 1], 目标数量: 1
2025-06-03 16:19:10.136 | DEBUG    | app.services.message_sending_service:start_task:281 - 处理目标 [Task ID: 1] [1/1] Target: ddv6_com
2025-06-03 16:19:10.136 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-03 16:19:10.139 | DEBUG    | app.services.message_sending_service:start_task:294 - 选择账户 [Task ID: 1] Account: ***********
2025-06-03 16:19:10.140 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-03 16:19:10.142 | DEBUG    | app.services.message_sending_service:start_task:313 - 发送消息 [Task ID: 1] Account: *********** -> Target: ddv6_com
2025-06-03 16:19:10.143 | INFO     | core.telegram.message_manager:send_text_message:43 - 账户 *********** 准备向 ddv6_com 发送文本消息
2025-06-03 16:19:12.520 | INFO     | core.telegram.message_manager:send_text_message:56 - 账户 *********** 成功向 ddv6_com 发送消息 ID: 115，解析模式: html
2025-06-03 16:19:13.147 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-03 16:19:13.153 | INFO     | data.repositories.message_repo:update_target_log_status:119 - 更新目标日志状态: 任务1 目标ddv6_com 状态success
2025-06-03 16:19:13.157 | DEBUG    | app.services.message_sending_service:start_task:323 - 发送成功 [Task ID: 1] Target: ddv6_com
2025-06-03 16:19:13.164 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-03 16:19:13.164 | DEBUG    | app.controllers.send_msg_controller:_on_task_progress_updated:73 - 收到任务进度更新事件: 1, 已处理: 2, 成功: 1, 失败: 0
2025-06-03 16:19:13.165 | DEBUG    | ui.views.send_msg_view:_on_task_progress_updated:879 - 收到任务进度更新: 1, 已处理: 2, 成功: 1, 失败: 0
2025-06-03 16:19:13.169 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-03 16:19:13.183 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-03 16:19:13.185 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-03 16:19:13.191 | INFO     | app.services.message_sending_service:start_task:355 - 任务完成 [Task ID: 1] 总数: 2, 成功: 1, 失败: 0
2025-06-03 16:19:13.197 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-03 16:19:13.198 | INFO     | data.repositories.message_repo:update_task:64 - 消息任务已更新, ID: 1, 账户切换时间: 10-30秒, 消息间隔: 60-180秒
2025-06-03 16:19:13.203 | INFO     | app.controllers.send_msg_controller:_on_task_status_changed:67 - 收到任务状态变更事件: 1 -> completed
2025-06-03 16:19:13.204 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-03 16:19:13.210 | INFO     | ui.views.send_msg_view:_on_task_status_changed:748 - 收到任务状态变更事件: 1 -> completed
2025-06-03 16:19:13.215 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-03 16:19:13.234 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-03 16:19:19.274 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-03 16:19:19.275 | DEBUG    | utils.client_http:post:68 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-03 16:19:39.853 | INFO     | ui.main_window:closeEvent:335 - MainWindow: 接收到关闭事件
2025-06-03 16:19:39.870 | INFO     | ui.main_window:_cleanup_before_quit:228 - MainWindow: 执行清理资源...
2025-06-03 16:19:39.871 | INFO     | core.telegram.client_worker:stop:110 - 正在停止Telegram客户端工作线程
2025-06-03 16:19:39.882 | INFO     | core.telegram.client_manager:cleanup_async:1140 - 开始清理资源并断开所有连接
2025-06-03 16:19:39.882 | INFO     | core.telegram.client_manager:disconnect_all_clients:725 - 正在断开所有客户端连接
2025-06-03 16:19:39.883 | INFO     | core.telegram.client_manager:_safe_disconnect:746 - 正在断开客户端连接: ***********
2025-06-03 16:19:39.889 | INFO     | core.telegram.client_manager:_safe_disconnect:750 - 断开客户端连接成功: ***********
2025-06-03 16:19:39.890 | INFO     | core.telegram.client_manager:cleanup_async:1159 - 资源清理完成
2025-06-03 16:19:40.393 | INFO     | core.telegram.client_manager:cleanup_async:1140 - 开始清理资源并断开所有连接
2025-06-03 16:19:40.393 | INFO     | core.telegram.client_manager:disconnect_all_clients:725 - 正在断开所有客户端连接
2025-06-03 16:19:40.394 | INFO     | core.telegram.client_manager:cleanup_async:1159 - 资源清理完成
2025-06-03 16:19:40.894 | INFO     | core.telegram.client_worker:run:99 - Telegram客户端工作线程已退出
2025-06-03 16:19:40.895 | INFO     | core.telegram.client_worker:stop:124 - Telegram客户端工作线程已停止
2025-06-03 16:19:40.895 | INFO     | ui.main_window:_cleanup_before_quit:237 - TelegramClientWorker 已停止。
2025-06-03 16:19:40.896 | INFO     | ui.main_window:_cleanup_before_quit:241 - MainWindow 清理完成
2025-06-03 16:19:40.931 | INFO     | __main__:main:111 - 应用程序已正常退出
