2025-06-04 09:11:01.064 | INFO     | utils.logger:init_logger:98 - 日志系统初始化完成，默认级别: DEBUG，日志目录: H:\PyProject\TeleTest\APPDATA\logs
2025-06-04 09:23:15.366 | INFO     | utils.logger:init_logger:98 - 日志系统初始化完成，默认级别: DEBUG，日志目录: H:\PyProject\TeleTest\APPDATA\logs
2025-06-04 09:27:04.843 | INFO     | utils.logger:init_logger:98 - 日志系统初始化完成，默认级别: DEBUG，日志目录: H:\PyProject\TeleTest\APPDATA\logs
2025-06-04 09:34:36.116 | INFO     | utils.logger:init_logger:98 - 日志系统初始化完成，默认级别: DEBUG，日志目录: H:\PyProject\TeleTest\APPDATA\logs
2025-06-04 09:46:19.424 | INFO     | utils.logger:init_logger:98 - 日志系统初始化完成，默认级别: DEBUG，日志目录: H:\PyProject\TeleTest\APPDATA\logs
2025-06-04 09:53:01.666 | INFO     | utils.logger:init_logger:98 - 日志系统初始化完成，默认级别: DEBUG，日志目录: h:\PyProject\TeleTest\APPDATA\logs
2025-06-04 09:53:53.235 | INFO     | utils.logger:init_logger:98 - 日志系统初始化完成，默认级别: DEBUG，日志目录: h:\PyProject\TeleTest\APPDATA\logs
2025-06-04 09:53:54.871 | CRITICAL | __main__:main:114 - 程序启动失败: 'NoneType' object has no attribute 'get'
2025-06-04 09:54:19.251 | INFO     | utils.logger:init_logger:98 - 日志系统初始化完成，默认级别: DEBUG，日志目录: h:\PyProject\TeleTest\APPDATA\logs
2025-06-04 09:54:20.709 | INFO     | data.database:init_database:31 - 初始化数据库连接: h:\PyProject\TeleTest\APPDATA\database\telegram_manager.db
2025-06-04 09:54:20.783 | DEBUG    | data.database:init_database:58 - 创建数据库表
2025-06-04 09:54:20.792 | INFO     | data.database:init_database:61 - 数据库初始化完成
2025-06-04 09:54:21.948 | DEBUG    | app.controllers.auth_controller:initialize_software:43 - 控制器: 初始化软件 app_id=1000, version=1.0
2025-06-04 09:54:21.949 | DEBUG    | utils.client_http:get:41 - 发送GET请求: https://server.xile188.com/api/user/1000/windowns10/1.0/ini
2025-06-04 09:54:22.365 | DEBUG    | core.auth.api_service:init_software:66 - 初始化数据解密成功: {'new_content': '1.多账户管理\n2.ip池管理\n3.消息监听\n4.消息群发\n5.拉人入群', 'new_url': None, 'notice': {'id': '2', 'content': '公告内容测试3', 'time': '**********'}}
2025-06-04 09:54:22.372 | INFO     | app.controllers.auth_controller:login:61 - 控制器: 用户登录 account=zhangsan03
2025-06-04 09:54:25.334 | INFO     | core.auth.api_service:login:127 - 用户登录: account=zhangsan03
2025-06-04 09:54:25.335 | DEBUG    | utils.client_http:post:68 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/logon
2025-06-04 09:54:25.559 | INFO     | core.auth.api_service:get_user_info:347 - 获取用户信息
2025-06-04 09:54:25.560 | DEBUG    | utils.client_http:post:68 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/info
2025-06-04 09:54:25.765 | INFO     | core.auth.api_service:get_user_info:375 - 获取用户信息成功
2025-06-04 09:54:25.773 | INFO     | __main__:on_auth_completed:93 - 用户登录成功，正在启动主窗口...
2025-06-04 09:54:25.790 | INFO     | core.telegram.client_worker:__init__:63 - Telegram客户端工作线程初始化
2025-06-04 09:54:25.794 | INFO     | core.telegram.client_worker:run:67 - Telegram客户端工作线程开始运行
2025-06-04 09:54:25.797 | INFO     | core.telegram.client_manager:__init__:66 - Telegram客户端管理器初始化
2025-06-04 09:54:25.797 | INFO     | core.telegram.user_manager:__init__:31 - Telegram用户管理器初始化
2025-06-04 09:54:25.798 | INFO     | core.telegram.group_manager:__init__:36 - Telegram群组管理器初始化
2025-06-04 09:54:25.799 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-06-04 09:54:25.799 | INFO     | core.telegram.monitor_manager:__init__:26 - Telegram监控管理器初始化
2025-06-04 09:54:25.799 | INFO     | core.telegram.telegram_service:__init__:39 - Telegram服务初始化完成
2025-06-04 09:54:25.801 | INFO     | ui.main_window:_initialize_core_components:79 - MainWindow: 初始化核心组件...
2025-06-04 09:54:25.802 | INFO     | app.services.account_service:__init__:42 - 账户服务初始化
2025-06-04 09:54:25.802 | INFO     | core.proxy.proxy_task_manager:run:284 - 代理任务管理器线程启动
2025-06-04 09:54:25.803 | INFO     | app.controllers.account_controller:__init__:81 - 账户控制器初始化
2025-06-04 09:54:25.803 | INFO     | app.services.monitor_service:__init__:39 - 初始化监控任务服务
2025-06-04 09:54:25.804 | INFO     | core.monitor.filter:__init__:26 - 初始化消息过滤器
2025-06-04 09:54:25.804 | INFO     | core.monitor.extractor:__init__:24 - 初始化关键词提取器
2025-06-04 09:54:25.805 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-06-04 09:54:25.805 | INFO     | core.monitor.notifier:__init__:90 - 初始化通知管理器
2025-06-04 09:54:25.806 | INFO     | app.controllers.telegram_monitor_controller:__init__:64 - 初始化 Telegram 监控控制器
2025-06-04 09:54:25.806 | DEBUG    | app.controllers.telegram_monitor_controller:__init__:82 - 控制器初始化完成
2025-06-04 09:54:25.982 | INFO     | ui.views.monitor_view:__init__:48 - 初始化监控视图
2025-06-04 09:54:25.983 | DEBUG    | ui.views.monitor_view:_connect_signals:80 - 连接控制器信号
2025-06-04 09:54:26.151 | INFO     | ui.views.proxy_view:__init__:96 - 初始化视图，加载第一页代理数据
2025-06-04 09:54:26.344 | INFO     | __main__:on_auth_completed:97 - 主窗口已启动
2025-06-04 09:54:26.410 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:88 - 尝试连接监听日志信号...
2025-06-04 09:54:26.410 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:90 - 监听日志信号连接成功
2025-06-04 09:54:26.411 | INFO     | core.auth.api_service:verify_vip:425 - 会员验证接口调用
2025-06-04 09:54:26.411 | DEBUG    | utils.client_http:post:68 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/vip
2025-06-04 09:54:26.413 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-04 09:54:26.417 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-04 09:54:26.421 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:145 - 加载监控任务列表 (搜索关键词: '')
2025-06-04 09:54:26.421 | DEBUG    | ui.views.monitor_view:_on_loading_started:620 - 加载中: 正在加载任务列表...
2025-06-04 09:54:26.422 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-04 09:54:26.427 | INFO     | app.services.proxy_service:refresh_proxies:712 - 刷新代理列表: offset=0, limit=20
2025-06-04 09:54:26.428 | INFO     | core.proxy.proxy_task_manager:submit_task:172 - 提交耗时任务到子线程: refresh_proxies_0_20
2025-06-04 09:54:26.429 | DEBUG    | app.services.proxy_service:get_proxies:500 - 获取代理列表: offset=0, limit=20
2025-06-04 09:54:26.429 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-04 09:54:26.429 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-04 09:54:26.445 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-04 09:54:26.459 | INFO     | app.controllers.invite_controller:get_invite_tasks:38 - 获取邀请任务列表, status=None
2025-06-04 09:54:26.461 | INFO     | app.services.invite_service:get_invite_tasks:121 - 获取邀请任务列表, status=None
2025-06-04 09:54:26.461 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-04 09:54:26.462 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-04 09:54:26.463 | INFO     | app.services.invite_service:task_initialize:39 - 初始化邀请服务，重置运行中的任务为暂停状态
2025-06-04 09:54:26.463 | INFO     | core.proxy.proxy_task_manager:task_wrapper:199 - 任务 refresh_proxies_0_20 完成
2025-06-04 09:54:26.464 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-04 09:54:26.666 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-04 09:54:26.667 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-04 09:54:26.669 | INFO     | app.services.invite_service:task_initialize:48 - 成功重置 0 个任务状态
2025-06-04 09:54:26.670 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-04 09:54:26.671 | INFO     | app.services.account_service:get_all_groups:96 - 获取所有账户分组成功, 共 1 个
2025-06-04 09:54:26.671 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-04 09:54:26.672 | DEBUG    | ui.views.monitor_view:_refresh_task_list:175 - 刷新任务列表，共2个任务
2025-06-04 09:54:26.677 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-04 09:54:26.685 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-04 09:54:26.687 | DEBUG    | ui.views.account_view:_on_groups_loaded:442 - 分组加载完成: 1个分组
2025-06-04 09:54:26.693 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-04 09:54:26.697 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-04 09:54:26.699 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-04 09:54:26.760 | INFO     | app.services.account_service:get_all_accounts:289 - 获取所有账户成功, 共 1 个
2025-06-04 09:54:26.761 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-04 09:54:26.767 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-04 09:54:26.768 | DEBUG    | ui.views.account_view:_on_accounts_loaded:575 - 账户加载完成: 1个账户
2025-06-04 09:54:26.778 | DEBUG    | ui.views.account_view:_update_group_counts:184 - 分组计数已更新: {1: 1}
2025-06-04 09:54:26.782 | INFO     | ui.views.account_view:_auto_login_accounts:630 - 开始调用控制器进行 1 个账户的自动登录。
2025-06-04 09:54:26.783 | INFO     | app.services.account_service:batch_auto_login:1153 - 服务层：开始批量自动登录 1 个账户, 最大并发: 5
2025-06-04 09:54:26.783 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-04 09:54:26.788 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-04 09:54:26.789 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-04 09:54:26.790 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-04 09:54:26.792 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-06-04 09:54:26.806 | INFO     | app.controllers.telegram_monitor_controller:_load_total_users_count_internal:191 - 成功加载总体统计数据: 总用户数 958, 今日采集 0, 日均采集 68, 运行天数 14
2025-06-04 09:54:26.806 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:156 - 成功加载 2 个任务
2025-06-04 09:54:26.807 | DEBUG    | ui.views.monitor_view:_on_loading_finished:625 - 加载完成
2025-06-04 09:54:26.824 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-04 09:54:26.832 | INFO     | app.services.account_service:get_all_accounts:289 - 获取所有账户成功, 共 1 个
2025-06-04 09:54:26.832 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-04 09:54:26.833 | INFO     | app.services.account_service:batch_auto_login:1192 - 服务层：账户 *********** 使用IP池代理ID 3, 详情: socks5 **************:10001
2025-06-04 09:54:26.834 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-04 09:54:26.836 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=True
2025-06-04 09:54:26.839 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=True
2025-06-04 09:54:26.841 | INFO     | ui.views.proxy_view:_load_initial_data:1044 - 初始数据加载完成，共 3 个代理
2025-06-04 09:54:26.842 | INFO     | app.services.account_service:batch_auto_login:1262 - 服务层：准备调用核心层处理 1 个账户的自动登录。
2025-06-04 09:54:26.843 | INFO     | app.services.account_service:batch_auto_login:1272 - 服务层：设置核心层任务超时为 90 秒。
2025-06-04 09:54:26.843 | INFO     | core.telegram.client_manager:batch_auto_login:1220 - 开始批量自动登录, 共 1 个账户, 最大并发 5
2025-06-04 09:54:26.844 | DEBUG    | ui.views.account_view:_update_group_counts:184 - 分组计数已更新: {1: 1}
2025-06-04 09:54:26.844 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:256 - 创建客户端: h:\PyProject\TeleTest\APPDATA\sessions\***********.session, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '**************', 'port': 10001}
2025-06-04 09:54:26.845 | DEBUG    | ui.views.account_view:_init_cache:151 - 账户缓存初始化完成，共 1 个账户
2025-06-04 09:54:26.845 | DEBUG    | core.telegram.client_manager:get_proxy:93 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '**************', 'port': 10001}
2025-06-04 09:54:26.851 | DEBUG    | core.telegram.client_manager:get_proxy:140 - get_proxy: 使用 ip_pool 代理 **************:10001，转换结果: (<ProxyType.SOCKS5: 2>, '**************', 10001)
2025-06-04 09:54:26.851 | DEBUG    | ui.views.account_view:_on_accounts_loaded:575 - 账户加载完成: 1个账户
2025-06-04 09:54:26.862 | DEBUG    | ui.views.account_view:_update_group_counts:184 - 分组计数已更新: {1: 1}
2025-06-04 09:54:26.875 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-04 09:54:26.895 | WARNING  | core.auth.api_service:verify_vip:441 - 会员验证失败: 验证失败
2025-06-04 09:54:34.001 | ERROR    | core.telegram.client_manager:_connect_client:217 - 认证密钥错误 (AuthKeyError): The authorization key (session file) was used under two different IP addresses simultaneously, and can no longer be used. Use the same session exclusively, or use different sessions (caused by InvokeWithLayerRequest(InitConnectionRequest(GetConfigRequest)))
2025-06-04 09:54:36.015 | INFO     | core.telegram.client_manager:batch_auto_login:1369 - 批量登录完成: 总计 1, 成功 0, 失败 1
2025-06-04 09:54:36.850 | INFO     | app.services.account_service:batch_auto_login:1293 - 服务层：核心层登录完成: 成功 0, 失败 1 / 1
2025-06-04 09:55:03.792 | INFO     | ui.main_window:closeEvent:335 - MainWindow: 接收到关闭事件
2025-06-04 09:55:03.804 | INFO     | ui.main_window:_cleanup_before_quit:228 - MainWindow: 执行清理资源...
2025-06-04 09:55:03.805 | INFO     | core.telegram.client_worker:stop:110 - 正在停止Telegram客户端工作线程
2025-06-04 09:55:03.817 | INFO     | core.telegram.client_manager:cleanup_async:1140 - 开始清理资源并断开所有连接
2025-06-04 09:55:03.819 | INFO     | core.telegram.client_manager:disconnect_all_clients:725 - 正在断开所有客户端连接
2025-06-04 09:55:03.820 | INFO     | core.telegram.client_manager:cleanup_async:1159 - 资源清理完成
2025-06-04 09:55:04.328 | INFO     | core.telegram.client_manager:cleanup_async:1140 - 开始清理资源并断开所有连接
2025-06-04 09:55:04.328 | INFO     | core.telegram.client_manager:disconnect_all_clients:725 - 正在断开所有客户端连接
2025-06-04 09:55:04.329 | INFO     | core.telegram.client_manager:cleanup_async:1159 - 资源清理完成
2025-06-04 09:55:04.842 | INFO     | core.telegram.client_worker:run:99 - Telegram客户端工作线程已退出
2025-06-04 09:55:04.843 | INFO     | core.telegram.client_worker:stop:124 - Telegram客户端工作线程已停止
2025-06-04 09:55:04.844 | INFO     | ui.main_window:_cleanup_before_quit:237 - TelegramClientWorker 已停止。
2025-06-04 09:55:04.844 | INFO     | ui.main_window:_cleanup_before_quit:241 - MainWindow 清理完成
2025-06-04 09:55:04.860 | INFO     | __main__:main:111 - 应用程序已正常退出
2025-06-04 09:56:14.598 | INFO     | utils.logger:init_logger:98 - 日志系统初始化完成，默认级别: DEBUG，日志目录: h:\PyProject\TeleTest\APPDATA\logs
2025-06-04 09:56:16.027 | INFO     | data.database:init_database:31 - 初始化数据库连接: h:\PyProject\TeleTest\APPDATA\database\telegram_manager.db
2025-06-04 09:56:16.095 | DEBUG    | data.database:init_database:58 - 创建数据库表
2025-06-04 09:56:16.107 | INFO     | data.database:init_database:61 - 数据库初始化完成
2025-06-04 09:56:16.752 | DEBUG    | app.controllers.auth_controller:initialize_software:43 - 控制器: 初始化软件 app_id=1000, version=1.0
2025-06-04 09:56:16.752 | DEBUG    | utils.client_http:get:41 - 发送GET请求: https://server.xile188.com/api/user/1000/windowns10/1.0/ini
2025-06-04 09:56:17.043 | DEBUG    | core.auth.api_service:init_software:66 - 初始化数据解密成功: {'new_content': '1.多账户管理\n2.ip池管理\n3.消息监听\n4.消息群发\n5.拉人入群', 'new_url': None, 'notice': {'id': '2', 'content': '公告内容测试3', 'time': '**********'}}
2025-06-04 09:56:17.051 | INFO     | app.controllers.auth_controller:login:61 - 控制器: 用户登录 account=zhangsan03
2025-06-04 09:56:19.974 | INFO     | core.auth.api_service:login:127 - 用户登录: account=zhangsan03
2025-06-04 09:56:19.975 | DEBUG    | utils.client_http:post:68 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/logon
2025-06-04 09:56:20.201 | INFO     | core.auth.api_service:get_user_info:347 - 获取用户信息
2025-06-04 09:56:20.201 | DEBUG    | utils.client_http:post:68 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/info
2025-06-04 09:56:20.411 | INFO     | core.auth.api_service:get_user_info:375 - 获取用户信息成功
2025-06-04 09:56:20.419 | INFO     | __main__:on_auth_completed:93 - 用户登录成功，正在启动主窗口...
2025-06-04 09:56:20.435 | INFO     | core.telegram.client_worker:__init__:63 - Telegram客户端工作线程初始化
2025-06-04 09:56:20.439 | INFO     | core.telegram.client_worker:run:67 - Telegram客户端工作线程开始运行
2025-06-04 09:56:20.441 | INFO     | core.telegram.client_manager:__init__:66 - Telegram客户端管理器初始化
2025-06-04 09:56:20.442 | INFO     | core.telegram.user_manager:__init__:31 - Telegram用户管理器初始化
2025-06-04 09:56:20.442 | INFO     | core.telegram.group_manager:__init__:36 - Telegram群组管理器初始化
2025-06-04 09:56:20.443 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-06-04 09:56:20.443 | INFO     | core.telegram.monitor_manager:__init__:26 - Telegram监控管理器初始化
2025-06-04 09:56:20.443 | INFO     | core.telegram.telegram_service:__init__:39 - Telegram服务初始化完成
2025-06-04 09:56:20.445 | INFO     | ui.main_window:_initialize_core_components:79 - MainWindow: 初始化核心组件...
2025-06-04 09:56:20.446 | INFO     | app.services.account_service:__init__:42 - 账户服务初始化
2025-06-04 09:56:20.446 | INFO     | core.proxy.proxy_task_manager:run:284 - 代理任务管理器线程启动
2025-06-04 09:56:20.446 | INFO     | app.controllers.account_controller:__init__:81 - 账户控制器初始化
2025-06-04 09:56:20.447 | INFO     | app.services.monitor_service:__init__:39 - 初始化监控任务服务
2025-06-04 09:56:20.447 | INFO     | core.monitor.filter:__init__:26 - 初始化消息过滤器
2025-06-04 09:56:20.447 | INFO     | core.monitor.extractor:__init__:24 - 初始化关键词提取器
2025-06-04 09:56:20.448 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-06-04 09:56:20.448 | INFO     | core.monitor.notifier:__init__:90 - 初始化通知管理器
2025-06-04 09:56:20.449 | INFO     | app.controllers.telegram_monitor_controller:__init__:64 - 初始化 Telegram 监控控制器
2025-06-04 09:56:20.449 | DEBUG    | app.controllers.telegram_monitor_controller:__init__:82 - 控制器初始化完成
2025-06-04 09:56:20.636 | INFO     | ui.views.monitor_view:__init__:48 - 初始化监控视图
2025-06-04 09:56:20.636 | DEBUG    | ui.views.monitor_view:_connect_signals:80 - 连接控制器信号
2025-06-04 09:56:20.810 | INFO     | ui.views.proxy_view:__init__:96 - 初始化视图，加载第一页代理数据
2025-06-04 09:56:21.036 | INFO     | __main__:on_auth_completed:97 - 主窗口已启动
2025-06-04 09:56:21.086 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:88 - 尝试连接监听日志信号...
2025-06-04 09:56:21.087 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:90 - 监听日志信号连接成功
2025-06-04 09:56:21.088 | INFO     | core.auth.api_service:verify_vip:425 - 会员验证接口调用
2025-06-04 09:56:21.088 | DEBUG    | utils.client_http:post:68 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/vip
2025-06-04 09:56:21.089 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-04 09:56:21.093 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-04 09:56:21.096 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:145 - 加载监控任务列表 (搜索关键词: '')
2025-06-04 09:56:21.096 | DEBUG    | ui.views.monitor_view:_on_loading_started:620 - 加载中: 正在加载任务列表...
2025-06-04 09:56:21.096 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-04 09:56:21.103 | INFO     | app.services.proxy_service:refresh_proxies:712 - 刷新代理列表: offset=0, limit=20
2025-06-04 09:56:21.104 | INFO     | core.proxy.proxy_task_manager:submit_task:172 - 提交耗时任务到子线程: refresh_proxies_0_20
2025-06-04 09:56:21.105 | DEBUG    | app.services.proxy_service:get_proxies:500 - 获取代理列表: offset=0, limit=20
2025-06-04 09:56:21.105 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-04 09:56:21.105 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-04 09:56:21.109 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-04 09:56:21.132 | INFO     | app.controllers.invite_controller:get_invite_tasks:38 - 获取邀请任务列表, status=None
2025-06-04 09:56:21.133 | INFO     | app.services.invite_service:get_invite_tasks:121 - 获取邀请任务列表, status=None
2025-06-04 09:56:21.135 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-04 09:56:21.136 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-04 09:56:21.136 | INFO     | app.services.invite_service:task_initialize:39 - 初始化邀请服务，重置运行中的任务为暂停状态
2025-06-04 09:56:21.137 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-04 09:56:21.138 | INFO     | core.proxy.proxy_task_manager:task_wrapper:199 - 任务 refresh_proxies_0_20 完成
2025-06-04 09:56:21.260 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-04 09:56:21.264 | DEBUG    | ui.views.monitor_view:_refresh_task_list:175 - 刷新任务列表，共2个任务
2025-06-04 09:56:21.270 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-04 09:56:21.272 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-04 09:56:21.281 | INFO     | app.services.account_service:get_all_groups:96 - 获取所有账户分组成功, 共 1 个
2025-06-04 09:56:21.282 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-04 09:56:21.288 | DEBUG    | ui.views.account_view:_on_groups_loaded:442 - 分组加载完成: 1个分组
2025-06-04 09:56:21.296 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-04 09:56:21.297 | INFO     | app.services.invite_service:task_initialize:48 - 成功重置 0 个任务状态
2025-06-04 09:56:21.297 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-04 09:56:21.308 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-04 09:56:21.310 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-04 09:56:21.311 | INFO     | app.services.account_service:get_all_accounts:289 - 获取所有账户成功, 共 1 个
2025-06-04 09:56:21.311 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-04 09:56:21.397 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-04 09:56:21.427 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-04 09:56:21.457 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-04 09:56:21.460 | DEBUG    | ui.views.account_view:_on_accounts_loaded:575 - 账户加载完成: 1个账户
2025-06-04 09:56:21.475 | DEBUG    | ui.views.account_view:_update_group_counts:184 - 分组计数已更新: {1: 1}
2025-06-04 09:56:21.482 | INFO     | ui.views.account_view:_auto_login_accounts:630 - 开始调用控制器进行 1 个账户的自动登录。
2025-06-04 09:56:21.483 | INFO     | app.services.account_service:batch_auto_login:1153 - 服务层：开始批量自动登录 1 个账户, 最大并发: 5
2025-06-04 09:56:21.483 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-04 09:56:21.554 | INFO     | app.controllers.telegram_monitor_controller:_load_total_users_count_internal:191 - 成功加载总体统计数据: 总用户数 958, 今日采集 0, 日均采集 68, 运行天数 14
2025-06-04 09:56:21.554 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:156 - 成功加载 2 个任务
2025-06-04 09:56:21.554 | DEBUG    | ui.views.monitor_view:_on_loading_finished:625 - 加载完成
2025-06-04 09:56:21.557 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-04 09:56:21.559 | INFO     | app.services.account_service:batch_auto_login:1192 - 服务层：账户 *********** 使用IP池代理ID 3, 详情: socks5 **************:10001
2025-06-04 09:56:21.560 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-04 09:56:21.573 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-04 09:56:21.577 | INFO     | app.services.account_service:get_all_accounts:289 - 获取所有账户成功, 共 1 个
2025-06-04 09:56:21.577 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-04 09:56:21.580 | INFO     | app.services.account_service:batch_auto_login:1262 - 服务层：准备调用核心层处理 1 个账户的自动登录。
2025-06-04 09:56:21.580 | INFO     | app.services.account_service:batch_auto_login:1272 - 服务层：设置核心层任务超时为 90 秒。
2025-06-04 09:56:21.580 | INFO     | core.telegram.client_manager:batch_auto_login:1220 - 开始批量自动登录, 共 1 个账户, 最大并发 5
2025-06-04 09:56:21.581 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:256 - 创建客户端: h:\PyProject\TeleTest\APPDATA\sessions\***********.session, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '**************', 'port': 10001}
2025-06-04 09:56:21.582 | DEBUG    | ui.views.account_view:_update_group_counts:184 - 分组计数已更新: {1: 1}
2025-06-04 09:56:21.583 | DEBUG    | core.telegram.client_manager:get_proxy:93 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '**************', 'port': 10001}
2025-06-04 09:56:21.583 | DEBUG    | ui.views.account_view:_init_cache:151 - 账户缓存初始化完成，共 1 个账户
2025-06-04 09:56:21.583 | DEBUG    | core.telegram.client_manager:get_proxy:140 - get_proxy: 使用 ip_pool 代理 **************:10001，转换结果: (<ProxyType.SOCKS5: 2>, '**************', 10001)
2025-06-04 09:56:21.589 | DEBUG    | ui.views.account_view:_on_accounts_loaded:575 - 账户加载完成: 1个账户
2025-06-04 09:56:21.601 | DEBUG    | ui.views.account_view:_update_group_counts:184 - 分组计数已更新: {1: 1}
2025-06-04 09:56:21.609 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-04 09:56:21.614 | WARNING  | core.auth.api_service:verify_vip:441 - 会员验证失败: 验证失败
2025-06-04 09:56:21.702 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-06-04 09:56:21.874 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=True
2025-06-04 09:56:21.876 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=True
2025-06-04 09:56:21.877 | INFO     | ui.views.proxy_view:_load_initial_data:1044 - 初始数据加载完成，共 3 个代理
2025-06-04 09:56:21.882 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-04 09:56:26.135 | ERROR    | core.telegram.client_manager:_connect_client:217 - 认证密钥错误 (AuthKeyError): The authorization key (session file) was used under two different IP addresses simultaneously, and can no longer be used. Use the same session exclusively, or use different sessions (caused by InvokeWithLayerRequest(InitConnectionRequest(GetConfigRequest)))
2025-06-04 09:56:28.145 | INFO     | core.telegram.client_manager:batch_auto_login:1369 - 批量登录完成: 总计 1, 成功 0, 失败 1
2025-06-04 09:56:28.598 | INFO     | app.services.account_service:batch_auto_login:1293 - 服务层：核心层登录完成: 成功 0, 失败 1 / 1
2025-06-04 09:56:43.827 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-04 09:56:43.838 | INFO     | app.services.account_service:get_accounts_by_group:311 - 获取分组 1 的账户成功, 共 1 个
2025-06-04 09:56:43.838 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-04 09:56:43.840 | DEBUG    | ui.views.account_view:_on_accounts_loaded:575 - 账户加载完成: 1个账户
2025-06-04 09:56:44.369 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-04 09:56:44.375 | INFO     | app.services.account_service:get_all_accounts:289 - 获取所有账户成功, 共 1 个
2025-06-04 09:56:44.375 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-04 09:56:44.377 | DEBUG    | ui.views.account_view:_on_accounts_loaded:575 - 账户加载完成: 1个账户
2025-06-04 09:56:44.390 | DEBUG    | ui.views.account_view:_update_group_counts:184 - 分组计数已更新: {1: 1}
2025-06-04 09:57:08.607 | INFO     | ui.main_window:closeEvent:335 - MainWindow: 接收到关闭事件
2025-06-04 09:57:08.623 | INFO     | ui.main_window:_cleanup_before_quit:228 - MainWindow: 执行清理资源...
2025-06-04 09:57:08.624 | INFO     | core.telegram.client_worker:stop:110 - 正在停止Telegram客户端工作线程
2025-06-04 09:57:08.631 | INFO     | core.telegram.client_manager:cleanup_async:1140 - 开始清理资源并断开所有连接
2025-06-04 09:57:08.631 | INFO     | core.telegram.client_manager:disconnect_all_clients:725 - 正在断开所有客户端连接
2025-06-04 09:57:08.632 | INFO     | core.telegram.client_manager:cleanup_async:1159 - 资源清理完成
2025-06-04 09:57:09.140 | INFO     | core.telegram.client_manager:cleanup_async:1140 - 开始清理资源并断开所有连接
2025-06-04 09:57:09.140 | INFO     | core.telegram.client_manager:disconnect_all_clients:725 - 正在断开所有客户端连接
2025-06-04 09:57:09.141 | INFO     | core.telegram.client_manager:cleanup_async:1159 - 资源清理完成
2025-06-04 09:57:09.642 | INFO     | core.telegram.client_worker:run:99 - Telegram客户端工作线程已退出
2025-06-04 09:57:09.644 | INFO     | core.telegram.client_worker:stop:124 - Telegram客户端工作线程已停止
2025-06-04 09:57:09.644 | INFO     | ui.main_window:_cleanup_before_quit:237 - TelegramClientWorker 已停止。
2025-06-04 09:57:09.645 | INFO     | ui.main_window:_cleanup_before_quit:241 - MainWindow 清理完成
2025-06-04 09:57:09.662 | INFO     | __main__:main:111 - 应用程序已正常退出
2025-06-04 09:59:14.989 | INFO     | utils.logger:init_logger:98 - 日志系统初始化完成，默认级别: DEBUG，日志目录: h:\PyProject\TeleTest\APPDATA\logs
2025-06-04 09:59:16.377 | INFO     | data.database:init_database:31 - 初始化数据库连接: h:\PyProject\TeleTest\APPDATA\database\telegram_manager.db
2025-06-04 09:59:16.443 | DEBUG    | data.database:init_database:58 - 创建数据库表
2025-06-04 09:59:16.455 | INFO     | data.database:init_database:61 - 数据库初始化完成
2025-06-04 09:59:17.022 | DEBUG    | app.controllers.auth_controller:initialize_software:43 - 控制器: 初始化软件 app_id=1000, version=1.0
2025-06-04 09:59:17.023 | DEBUG    | utils.client_http:get:41 - 发送GET请求: https://server.xile188.com/api/user/1000/windowns10/1.0/ini
2025-06-04 09:59:17.325 | DEBUG    | core.auth.api_service:init_software:66 - 初始化数据解密成功: {'new_content': '1.多账户管理\n2.ip池管理\n3.消息监听\n4.消息群发\n5.拉人入群', 'new_url': None, 'notice': {'id': '2', 'content': '公告内容测试3', 'time': '**********'}}
2025-06-04 09:59:17.331 | INFO     | app.controllers.auth_controller:login:61 - 控制器: 用户登录 account=zhangsan03
2025-06-04 09:59:20.240 | INFO     | core.auth.api_service:login:127 - 用户登录: account=zhangsan03
2025-06-04 09:59:20.241 | DEBUG    | utils.client_http:post:68 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/logon
2025-06-04 09:59:20.504 | INFO     | core.auth.api_service:get_user_info:347 - 获取用户信息
2025-06-04 09:59:20.504 | DEBUG    | utils.client_http:post:68 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/info
2025-06-04 09:59:20.710 | INFO     | core.auth.api_service:get_user_info:375 - 获取用户信息成功
2025-06-04 09:59:20.716 | INFO     | __main__:on_auth_completed:93 - 用户登录成功，正在启动主窗口...
2025-06-04 09:59:20.732 | INFO     | core.telegram.client_worker:__init__:63 - Telegram客户端工作线程初始化
2025-06-04 09:59:20.738 | INFO     | core.telegram.client_worker:run:67 - Telegram客户端工作线程开始运行
2025-06-04 09:59:20.740 | INFO     | core.telegram.client_manager:__init__:66 - Telegram客户端管理器初始化
2025-06-04 09:59:20.741 | INFO     | core.telegram.user_manager:__init__:31 - Telegram用户管理器初始化
2025-06-04 09:59:20.741 | INFO     | core.telegram.group_manager:__init__:36 - Telegram群组管理器初始化
2025-06-04 09:59:20.742 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-06-04 09:59:20.742 | INFO     | core.telegram.monitor_manager:__init__:26 - Telegram监控管理器初始化
2025-06-04 09:59:20.742 | INFO     | core.telegram.telegram_service:__init__:39 - Telegram服务初始化完成
2025-06-04 09:59:20.743 | INFO     | ui.main_window:_initialize_core_components:79 - MainWindow: 初始化核心组件...
2025-06-04 09:59:20.744 | INFO     | app.services.account_service:__init__:42 - 账户服务初始化
2025-06-04 09:59:20.744 | INFO     | core.proxy.proxy_task_manager:run:284 - 代理任务管理器线程启动
2025-06-04 09:59:20.745 | INFO     | app.controllers.account_controller:__init__:81 - 账户控制器初始化
2025-06-04 09:59:20.745 | INFO     | app.services.monitor_service:__init__:39 - 初始化监控任务服务
2025-06-04 09:59:20.746 | INFO     | core.monitor.filter:__init__:26 - 初始化消息过滤器
2025-06-04 09:59:20.746 | INFO     | core.monitor.extractor:__init__:24 - 初始化关键词提取器
2025-06-04 09:59:20.746 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-06-04 09:59:20.747 | INFO     | core.monitor.notifier:__init__:90 - 初始化通知管理器
2025-06-04 09:59:20.747 | INFO     | app.controllers.telegram_monitor_controller:__init__:64 - 初始化 Telegram 监控控制器
2025-06-04 09:59:20.747 | DEBUG    | app.controllers.telegram_monitor_controller:__init__:82 - 控制器初始化完成
2025-06-04 09:59:20.922 | INFO     | ui.views.monitor_view:__init__:48 - 初始化监控视图
2025-06-04 09:59:20.923 | DEBUG    | ui.views.monitor_view:_connect_signals:80 - 连接控制器信号
2025-06-04 09:59:21.091 | INFO     | ui.views.proxy_view:__init__:96 - 初始化视图，加载第一页代理数据
2025-06-04 09:59:21.282 | INFO     | __main__:on_auth_completed:97 - 主窗口已启动
2025-06-04 09:59:21.326 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:88 - 尝试连接监听日志信号...
2025-06-04 09:59:21.326 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:90 - 监听日志信号连接成功
2025-06-04 09:59:21.327 | INFO     | core.auth.api_service:verify_vip:425 - 会员验证接口调用
2025-06-04 09:59:21.328 | DEBUG    | utils.client_http:post:68 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/vip
2025-06-04 09:59:21.329 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-04 09:59:21.332 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-04 09:59:21.337 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:145 - 加载监控任务列表 (搜索关键词: '')
2025-06-04 09:59:21.338 | DEBUG    | ui.views.monitor_view:_on_loading_started:620 - 加载中: 正在加载任务列表...
2025-06-04 09:59:21.338 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-04 09:59:21.344 | INFO     | app.services.proxy_service:refresh_proxies:712 - 刷新代理列表: offset=0, limit=20
2025-06-04 09:59:21.344 | INFO     | core.proxy.proxy_task_manager:submit_task:172 - 提交耗时任务到子线程: refresh_proxies_0_20
2025-06-04 09:59:21.345 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-04 09:59:21.345 | DEBUG    | app.services.proxy_service:get_proxies:500 - 获取代理列表: offset=0, limit=20
2025-06-04 09:59:21.346 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-04 09:59:21.355 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-04 09:59:21.372 | INFO     | app.controllers.invite_controller:get_invite_tasks:38 - 获取邀请任务列表, status=None
2025-06-04 09:59:21.373 | INFO     | app.services.invite_service:get_invite_tasks:121 - 获取邀请任务列表, status=None
2025-06-04 09:59:21.375 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-04 09:59:21.376 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-04 09:59:21.376 | INFO     | app.services.invite_service:task_initialize:39 - 初始化邀请服务，重置运行中的任务为暂停状态
2025-06-04 09:59:21.377 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-04 09:59:21.378 | INFO     | core.proxy.proxy_task_manager:task_wrapper:199 - 任务 refresh_proxies_0_20 完成
2025-06-04 09:59:21.502 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-04 09:59:21.504 | INFO     | app.services.account_service:get_all_groups:96 - 获取所有账户分组成功, 共 1 个
2025-06-04 09:59:21.504 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-04 09:59:21.505 | INFO     | app.services.invite_service:task_initialize:48 - 成功重置 0 个任务状态
2025-06-04 09:59:21.505 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-04 09:59:21.506 | DEBUG    | ui.views.monitor_view:_refresh_task_list:175 - 刷新任务列表，共2个任务
2025-06-04 09:59:21.511 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-04 09:59:21.519 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-04 09:59:21.520 | DEBUG    | ui.views.account_view:_on_groups_loaded:442 - 分组加载完成: 1个分组
2025-06-04 09:59:21.527 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-04 09:59:21.529 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-04 09:59:21.532 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-04 09:59:21.534 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-04 09:59:21.593 | INFO     | app.services.account_service:get_all_accounts:289 - 获取所有账户成功, 共 1 个
2025-06-04 09:59:21.594 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-04 09:59:21.596 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-04 09:59:21.597 | DEBUG    | ui.views.account_view:_on_accounts_loaded:575 - 账户加载完成: 1个账户
2025-06-04 09:59:21.608 | DEBUG    | ui.views.account_view:_update_group_counts:184 - 分组计数已更新: {1: 1}
2025-06-04 09:59:21.612 | INFO     | ui.views.account_view:_auto_login_accounts:630 - 开始调用控制器进行 1 个账户的自动登录。
2025-06-04 09:59:21.613 | INFO     | app.services.account_service:batch_auto_login:1153 - 服务层：开始批量自动登录 1 个账户, 最大并发: 5
2025-06-04 09:59:21.613 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-04 09:59:21.622 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-04 09:59:21.623 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-04 09:59:21.623 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-04 09:59:21.625 | INFO     | app.controllers.telegram_monitor_controller:_load_total_users_count_internal:191 - 成功加载总体统计数据: 总用户数 958, 今日采集 0, 日均采集 68, 运行天数 14
2025-06-04 09:59:21.626 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:156 - 成功加载 2 个任务
2025-06-04 09:59:21.626 | DEBUG    | ui.views.monitor_view:_on_loading_finished:625 - 加载完成
2025-06-04 09:59:21.628 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-06-04 09:59:21.638 | INFO     | app.services.account_service:batch_auto_login:1192 - 服务层：账户 *********** 使用IP池代理ID 3, 详情: socks5 **************:10001
2025-06-04 09:59:21.639 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-04 09:59:21.653 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-04 09:59:21.655 | INFO     | app.services.account_service:get_all_accounts:289 - 获取所有账户成功, 共 1 个
2025-06-04 09:59:21.656 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-04 09:59:21.661 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=True
2025-06-04 09:59:21.663 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=True
2025-06-04 09:59:21.665 | INFO     | ui.views.proxy_view:_load_initial_data:1044 - 初始数据加载完成，共 3 个代理
2025-06-04 09:59:21.666 | INFO     | app.services.account_service:batch_auto_login:1262 - 服务层：准备调用核心层处理 1 个账户的自动登录。
2025-06-04 09:59:21.666 | INFO     | app.services.account_service:batch_auto_login:1272 - 服务层：设置核心层任务超时为 90 秒。
2025-06-04 09:59:21.666 | INFO     | core.telegram.client_manager:batch_auto_login:1220 - 开始批量自动登录, 共 1 个账户, 最大并发 5
2025-06-04 09:59:21.668 | DEBUG    | ui.views.account_view:_update_group_counts:184 - 分组计数已更新: {1: 1}
2025-06-04 09:59:21.668 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:256 - 创建客户端: h:\PyProject\TeleTest\APPDATA\sessions\***********.session, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '**************', 'port': 10001}
2025-06-04 09:59:21.668 | DEBUG    | ui.views.account_view:_init_cache:151 - 账户缓存初始化完成，共 1 个账户
2025-06-04 09:59:21.669 | DEBUG    | core.telegram.client_manager:get_proxy:93 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '**************', 'port': 10001}
2025-06-04 09:59:21.670 | DEBUG    | core.telegram.client_manager:get_proxy:140 - get_proxy: 使用 ip_pool 代理 **************:10001，转换结果: (<ProxyType.SOCKS5: 2>, '**************', 10001)
2025-06-04 09:59:21.674 | DEBUG    | ui.views.account_view:_on_accounts_loaded:575 - 账户加载完成: 1个账户
2025-06-04 09:59:21.685 | DEBUG    | ui.views.account_view:_update_group_counts:184 - 分组计数已更新: {1: 1}
2025-06-04 09:59:21.696 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-04 09:59:22.022 | WARNING  | core.auth.api_service:verify_vip:441 - 会员验证失败: 验证失败
2025-06-04 09:59:26.265 | ERROR    | core.telegram.client_manager:_connect_client:217 - 认证密钥错误 (AuthKeyError): The authorization key (session file) was used under two different IP addresses simultaneously, and can no longer be used. Use the same session exclusively, or use different sessions (caused by InvokeWithLayerRequest(InitConnectionRequest(GetConfigRequest)))
2025-06-04 09:59:28.275 | INFO     | core.telegram.client_manager:batch_auto_login:1369 - 批量登录完成: 总计 1, 成功 0, 失败 1
2025-06-04 09:59:28.693 | INFO     | app.services.account_service:batch_auto_login:1293 - 服务层：核心层登录完成: 成功 0, 失败 1 / 1
2025-06-04 09:59:38.604 | INFO     | ui.main_window:closeEvent:335 - MainWindow: 接收到关闭事件
2025-06-04 09:59:38.616 | INFO     | ui.main_window:_cleanup_before_quit:228 - MainWindow: 执行清理资源...
2025-06-04 09:59:38.617 | INFO     | core.telegram.client_worker:stop:110 - 正在停止Telegram客户端工作线程
2025-06-04 09:59:38.626 | INFO     | core.telegram.client_manager:cleanup_async:1140 - 开始清理资源并断开所有连接
2025-06-04 09:59:38.626 | INFO     | core.telegram.client_manager:disconnect_all_clients:725 - 正在断开所有客户端连接
2025-06-04 09:59:38.627 | INFO     | core.telegram.client_manager:cleanup_async:1159 - 资源清理完成
2025-06-04 09:59:39.144 | INFO     | core.telegram.client_manager:cleanup_async:1140 - 开始清理资源并断开所有连接
2025-06-04 09:59:39.144 | INFO     | core.telegram.client_manager:disconnect_all_clients:725 - 正在断开所有客户端连接
2025-06-04 09:59:39.144 | INFO     | core.telegram.client_manager:cleanup_async:1159 - 资源清理完成
2025-06-04 09:59:39.659 | INFO     | core.telegram.client_worker:run:99 - Telegram客户端工作线程已退出
2025-06-04 09:59:39.659 | INFO     | core.telegram.client_worker:stop:124 - Telegram客户端工作线程已停止
2025-06-04 09:59:39.660 | INFO     | ui.main_window:_cleanup_before_quit:237 - TelegramClientWorker 已停止。
2025-06-04 09:59:39.660 | INFO     | ui.main_window:_cleanup_before_quit:241 - MainWindow 清理完成
2025-06-04 09:59:39.677 | INFO     | __main__:main:111 - 应用程序已正常退出
2025-06-04 10:07:00.743 | INFO     | utils.logger:init_logger:98 - 日志系统初始化完成，默认级别: DEBUG，日志目录: h:\PyProject\TeleTest\APPDATA\logs
2025-06-04 10:07:02.170 | INFO     | data.database:init_database:31 - 初始化数据库连接: h:\PyProject\TeleTest\APPDATA\database\telegram_manager.db
2025-06-04 10:07:02.238 | DEBUG    | data.database:init_database:58 - 创建数据库表
2025-06-04 10:07:02.249 | INFO     | data.database:init_database:61 - 数据库初始化完成
2025-06-04 10:07:02.777 | DEBUG    | app.controllers.auth_controller:initialize_software:43 - 控制器: 初始化软件 app_id=1000, version=1.0
2025-06-04 10:07:02.778 | DEBUG    | utils.client_http:get:41 - 发送GET请求: https://server.xile188.com/api/user/1000/windowns10/1.0/ini
2025-06-04 10:07:03.245 | DEBUG    | core.auth.api_service:init_software:66 - 初始化数据解密成功: {'new_content': '1.多账户管理\n2.ip池管理\n3.消息监听\n4.消息群发\n5.拉人入群', 'new_url': None, 'notice': {'id': '2', 'content': '公告内容测试3', 'time': '**********'}}
2025-06-04 10:07:03.251 | INFO     | app.controllers.auth_controller:login:61 - 控制器: 用户登录 account=zhangsan03
2025-06-04 10:07:06.164 | INFO     | core.auth.api_service:login:127 - 用户登录: account=zhangsan03
2025-06-04 10:07:06.164 | DEBUG    | utils.client_http:post:68 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/logon
2025-06-04 10:07:06.385 | INFO     | core.auth.api_service:get_user_info:347 - 获取用户信息
2025-06-04 10:07:06.386 | DEBUG    | utils.client_http:post:68 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/info
2025-06-04 10:07:06.755 | INFO     | core.auth.api_service:get_user_info:375 - 获取用户信息成功
2025-06-04 10:07:06.762 | INFO     | __main__:on_auth_completed:93 - 用户登录成功，正在启动主窗口...
2025-06-04 10:07:06.778 | INFO     | core.telegram.client_worker:__init__:63 - Telegram客户端工作线程初始化
2025-06-04 10:07:06.782 | INFO     | core.telegram.client_worker:run:67 - Telegram客户端工作线程开始运行
2025-06-04 10:07:06.785 | INFO     | core.telegram.client_manager:__init__:66 - Telegram客户端管理器初始化
2025-06-04 10:07:06.785 | INFO     | core.telegram.user_manager:__init__:31 - Telegram用户管理器初始化
2025-06-04 10:07:06.785 | INFO     | core.telegram.group_manager:__init__:36 - Telegram群组管理器初始化
2025-06-04 10:07:06.786 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-06-04 10:07:06.786 | INFO     | core.telegram.monitor_manager:__init__:26 - Telegram监控管理器初始化
2025-06-04 10:07:06.787 | INFO     | core.telegram.telegram_service:__init__:39 - Telegram服务初始化完成
2025-06-04 10:07:06.788 | INFO     | ui.main_window:_initialize_core_components:79 - MainWindow: 初始化核心组件...
2025-06-04 10:07:06.789 | INFO     | app.services.account_service:__init__:42 - 账户服务初始化
2025-06-04 10:07:06.789 | INFO     | core.proxy.proxy_task_manager:run:284 - 代理任务管理器线程启动
2025-06-04 10:07:06.790 | INFO     | app.controllers.account_controller:__init__:81 - 账户控制器初始化
2025-06-04 10:07:06.791 | INFO     | app.services.monitor_service:__init__:39 - 初始化监控任务服务
2025-06-04 10:07:06.791 | INFO     | core.monitor.filter:__init__:26 - 初始化消息过滤器
2025-06-04 10:07:06.791 | INFO     | core.monitor.extractor:__init__:24 - 初始化关键词提取器
2025-06-04 10:07:06.792 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-06-04 10:07:06.792 | INFO     | core.monitor.notifier:__init__:90 - 初始化通知管理器
2025-06-04 10:07:06.793 | INFO     | app.controllers.telegram_monitor_controller:__init__:64 - 初始化 Telegram 监控控制器
2025-06-04 10:07:06.793 | DEBUG    | app.controllers.telegram_monitor_controller:__init__:82 - 控制器初始化完成
2025-06-04 10:07:06.966 | INFO     | ui.views.monitor_view:__init__:48 - 初始化监控视图
2025-06-04 10:07:06.966 | DEBUG    | ui.views.monitor_view:_connect_signals:80 - 连接控制器信号
2025-06-04 10:07:06.994 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:88 - 尝试连接监听日志信号...
2025-06-04 10:07:06.995 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:90 - 监听日志信号连接成功
2025-06-04 10:07:07.000 | INFO     | core.auth.api_service:verify_vip:425 - 会员验证接口调用
2025-06-04 10:07:07.004 | DEBUG    | utils.client_http:post:68 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/vip
2025-06-04 10:07:07.012 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-04 10:07:07.044 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-04 10:07:07.069 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:145 - 加载监控任务列表 (搜索关键词: '')
2025-06-04 10:07:07.069 | DEBUG    | ui.views.monitor_view:_on_loading_started:620 - 加载中: 正在加载任务列表...
2025-06-04 10:07:07.070 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-04 10:07:07.101 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-04 10:07:07.102 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-04 10:07:07.104 | DEBUG    | ui.views.monitor_view:_refresh_task_list:175 - 刷新任务列表，共2个任务
2025-06-04 10:07:07.110 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-04 10:07:07.115 | INFO     | app.services.account_service:get_all_groups:96 - 获取所有账户分组成功, 共 1 个
2025-06-04 10:07:07.116 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-04 10:07:07.118 | DEBUG    | ui.views.account_view:_on_groups_loaded:442 - 分组加载完成: 1个分组
2025-06-04 10:07:07.126 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-04 10:07:07.134 | INFO     | app.services.account_service:get_all_accounts:289 - 获取所有账户成功, 共 1 个
2025-06-04 10:07:07.134 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-04 10:07:07.136 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-04 10:07:07.137 | DEBUG    | ui.views.account_view:_on_accounts_loaded:575 - 账户加载完成: 1个账户
2025-06-04 10:07:07.147 | DEBUG    | ui.views.account_view:_update_group_counts:184 - 分组计数已更新: {1: 1}
2025-06-04 10:07:07.152 | INFO     | ui.views.account_view:_auto_login_accounts:630 - 开始调用控制器进行 1 个账户的自动登录。
2025-06-04 10:07:07.152 | INFO     | app.services.account_service:batch_auto_login:1153 - 服务层：开始批量自动登录 1 个账户, 最大并发: 5
2025-06-04 10:07:07.152 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-04 10:07:07.156 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-04 10:07:07.161 | INFO     | app.controllers.telegram_monitor_controller:_load_total_users_count_internal:191 - 成功加载总体统计数据: 总用户数 958, 今日采集 0, 日均采集 68, 运行天数 14
2025-06-04 10:07:07.161 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:156 - 成功加载 2 个任务
2025-06-04 10:07:07.162 | DEBUG    | ui.views.monitor_view:_on_loading_finished:625 - 加载完成
2025-06-04 10:07:07.163 | INFO     | app.services.account_service:get_all_accounts:289 - 获取所有账户成功, 共 1 个
2025-06-04 10:07:07.163 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-04 10:07:07.164 | INFO     | app.services.account_service:batch_auto_login:1192 - 服务层：账户 *********** 使用IP池代理ID 3, 详情: socks5 **************:10001
2025-06-04 10:07:07.164 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-04 10:07:07.165 | DEBUG    | ui.views.account_view:_update_group_counts:184 - 分组计数已更新: {1: 1}
2025-06-04 10:07:07.166 | DEBUG    | ui.views.account_view:_init_cache:151 - 账户缓存初始化完成，共 1 个账户
2025-06-04 10:07:07.170 | DEBUG    | ui.views.account_view:_on_accounts_loaded:575 - 账户加载完成: 1个账户
2025-06-04 10:07:07.181 | DEBUG    | ui.views.account_view:_update_group_counts:184 - 分组计数已更新: {1: 1}
2025-06-04 10:07:07.187 | INFO     | app.services.account_service:batch_auto_login:1262 - 服务层：准备调用核心层处理 1 个账户的自动登录。
2025-06-04 10:07:07.187 | INFO     | app.services.account_service:batch_auto_login:1272 - 服务层：设置核心层任务超时为 90 秒。
2025-06-04 10:07:07.187 | INFO     | core.telegram.client_manager:batch_auto_login:1220 - 开始批量自动登录, 共 1 个账户, 最大并发 5
2025-06-04 10:07:07.188 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:256 - 创建客户端: h:\PyProject\TeleTest\APPDATA\sessions\***********.session, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '**************', 'port': 10001}
2025-06-04 10:07:07.189 | DEBUG    | core.telegram.client_manager:get_proxy:93 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '**************', 'port': 10001}
2025-06-04 10:07:07.190 | DEBUG    | core.telegram.client_manager:get_proxy:140 - get_proxy: 使用 ip_pool 代理 **************:10001，转换结果: (<ProxyType.SOCKS5: 2>, '**************', 10001)
2025-06-04 10:07:07.289 | WARNING  | core.auth.api_service:verify_vip:441 - 会员验证失败: 验证失败
2025-06-04 10:07:10.513 | ERROR    | core.telegram.client_manager:_connect_client:217 - 认证密钥错误 (AuthKeyError): The authorization key (session file) was used under two different IP addresses simultaneously, and can no longer be used. Use the same session exclusively, or use different sessions (caused by InvokeWithLayerRequest(InitConnectionRequest(GetConfigRequest)))
2025-06-04 10:07:12.529 | INFO     | core.telegram.client_manager:batch_auto_login:1369 - 批量登录完成: 总计 1, 成功 0, 失败 1
2025-06-04 10:07:13.194 | INFO     | app.services.account_service:batch_auto_login:1293 - 服务层：核心层登录完成: 成功 0, 失败 1 / 1
2025-06-04 10:07:48.411 | INFO     | utils.logger:init_logger:98 - 日志系统初始化完成，默认级别: DEBUG，日志目录: h:\PyProject\TeleTest\APPDATA\logs
2025-06-04 10:07:49.884 | INFO     | data.database:init_database:31 - 初始化数据库连接: h:\PyProject\TeleTest\APPDATA\database\telegram_manager.db
2025-06-04 10:07:49.952 | DEBUG    | data.database:init_database:58 - 创建数据库表
2025-06-04 10:07:49.964 | INFO     | data.database:init_database:61 - 数据库初始化完成
2025-06-04 10:07:50.552 | DEBUG    | app.controllers.auth_controller:initialize_software:43 - 控制器: 初始化软件 app_id=1000, version=1.0
2025-06-04 10:07:50.552 | DEBUG    | utils.client_http:get:41 - 发送GET请求: https://server.xile188.com/api/user/1000/windowns10/1.0/ini
2025-06-04 10:07:50.945 | DEBUG    | core.auth.api_service:init_software:66 - 初始化数据解密成功: {'new_content': '1.多账户管理\n2.ip池管理\n3.消息监听\n4.消息群发\n5.拉人入群', 'new_url': None, 'notice': {'id': '2', 'content': '公告内容测试3', 'time': '**********'}}
2025-06-04 10:07:50.951 | INFO     | app.controllers.auth_controller:login:61 - 控制器: 用户登录 account=zhangsan03
2025-06-04 10:07:53.881 | INFO     | core.auth.api_service:login:127 - 用户登录: account=zhangsan03
2025-06-04 10:07:53.882 | DEBUG    | utils.client_http:post:68 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/logon
2025-06-04 10:07:54.153 | INFO     | core.auth.api_service:get_user_info:347 - 获取用户信息
2025-06-04 10:07:54.154 | DEBUG    | utils.client_http:post:68 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/info
2025-06-04 10:07:54.360 | INFO     | core.auth.api_service:get_user_info:375 - 获取用户信息成功
2025-06-04 10:07:54.365 | INFO     | __main__:on_auth_completed:93 - 用户登录成功，正在启动主窗口...
2025-06-04 10:07:54.381 | INFO     | core.telegram.client_worker:__init__:63 - Telegram客户端工作线程初始化
2025-06-04 10:07:54.385 | INFO     | core.telegram.client_worker:run:67 - Telegram客户端工作线程开始运行
2025-06-04 10:07:54.388 | INFO     | core.telegram.client_manager:__init__:66 - Telegram客户端管理器初始化
2025-06-04 10:07:54.388 | INFO     | core.telegram.user_manager:__init__:31 - Telegram用户管理器初始化
2025-06-04 10:07:54.389 | INFO     | core.telegram.group_manager:__init__:36 - Telegram群组管理器初始化
2025-06-04 10:07:54.389 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-06-04 10:07:54.389 | INFO     | core.telegram.monitor_manager:__init__:26 - Telegram监控管理器初始化
2025-06-04 10:07:54.390 | INFO     | core.telegram.telegram_service:__init__:39 - Telegram服务初始化完成
2025-06-04 10:07:54.393 | INFO     | ui.main_window:_initialize_core_components:79 - MainWindow: 初始化核心组件...
2025-06-04 10:07:54.394 | INFO     | app.services.account_service:__init__:42 - 账户服务初始化
2025-06-04 10:07:54.394 | INFO     | core.proxy.proxy_task_manager:run:284 - 代理任务管理器线程启动
2025-06-04 10:07:54.395 | INFO     | app.controllers.account_controller:__init__:81 - 账户控制器初始化
2025-06-04 10:07:54.395 | INFO     | app.services.monitor_service:__init__:39 - 初始化监控任务服务
2025-06-04 10:07:54.396 | INFO     | core.monitor.filter:__init__:26 - 初始化消息过滤器
2025-06-04 10:07:54.396 | INFO     | core.monitor.extractor:__init__:24 - 初始化关键词提取器
2025-06-04 10:07:54.396 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-06-04 10:07:54.397 | INFO     | core.monitor.notifier:__init__:90 - 初始化通知管理器
2025-06-04 10:07:54.397 | INFO     | app.controllers.telegram_monitor_controller:__init__:64 - 初始化 Telegram 监控控制器
2025-06-04 10:07:54.398 | DEBUG    | app.controllers.telegram_monitor_controller:__init__:82 - 控制器初始化完成
2025-06-04 10:07:54.576 | INFO     | ui.views.monitor_view:__init__:48 - 初始化监控视图
2025-06-04 10:07:54.576 | DEBUG    | ui.views.monitor_view:_connect_signals:80 - 连接控制器信号
2025-06-04 10:07:54.747 | INFO     | ui.views.proxy_view:__init__:96 - 初始化视图，加载第一页代理数据
2025-06-04 10:07:54.939 | INFO     | __main__:on_auth_completed:97 - 主窗口已启动
2025-06-04 10:07:54.988 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:88 - 尝试连接监听日志信号...
2025-06-04 10:07:54.989 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:90 - 监听日志信号连接成功
2025-06-04 10:07:54.989 | INFO     | core.auth.api_service:verify_vip:425 - 会员验证接口调用
2025-06-04 10:07:54.990 | DEBUG    | utils.client_http:post:68 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/vip
2025-06-04 10:07:54.991 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-04 10:07:54.994 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-04 10:07:54.998 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:145 - 加载监控任务列表 (搜索关键词: '')
2025-06-04 10:07:54.999 | DEBUG    | ui.views.monitor_view:_on_loading_started:620 - 加载中: 正在加载任务列表...
2025-06-04 10:07:54.999 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-04 10:07:55.005 | INFO     | app.services.proxy_service:refresh_proxies:712 - 刷新代理列表: offset=0, limit=20
2025-06-04 10:07:55.006 | INFO     | core.proxy.proxy_task_manager:submit_task:172 - 提交耗时任务到子线程: refresh_proxies_0_20
2025-06-04 10:07:55.007 | DEBUG    | app.services.proxy_service:get_proxies:500 - 获取代理列表: offset=0, limit=20
2025-06-04 10:07:55.007 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-04 10:07:55.007 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-04 10:07:55.038 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-04 10:07:55.042 | INFO     | app.controllers.invite_controller:get_invite_tasks:38 - 获取邀请任务列表, status=None
2025-06-04 10:07:55.042 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-04 10:07:55.042 | INFO     | app.services.invite_service:get_invite_tasks:121 - 获取邀请任务列表, status=None
2025-06-04 10:07:55.043 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-04 10:07:55.044 | INFO     | core.proxy.proxy_task_manager:task_wrapper:199 - 任务 refresh_proxies_0_20 完成
2025-06-04 10:07:55.045 | INFO     | app.services.invite_service:task_initialize:39 - 初始化邀请服务，重置运行中的任务为暂停状态
2025-06-04 10:07:55.045 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-04 10:07:55.235 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-04 10:07:55.242 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-04 10:07:55.247 | DEBUG    | ui.views.monitor_view:_refresh_task_list:175 - 刷新任务列表，共2个任务
2025-06-04 10:07:55.252 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-04 10:07:55.254 | INFO     | app.services.invite_service:task_initialize:48 - 成功重置 0 个任务状态
2025-06-04 10:07:55.254 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-04 10:07:55.259 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-04 10:07:55.262 | INFO     | app.services.account_service:get_all_groups:96 - 获取所有账户分组成功, 共 1 个
2025-06-04 10:07:55.263 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-04 10:07:55.265 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-04 10:07:55.267 | DEBUG    | ui.views.account_view:_on_groups_loaded:442 - 分组加载完成: 1个分组
2025-06-04 10:07:55.275 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-04 10:07:55.277 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-04 10:07:55.339 | INFO     | app.services.account_service:get_all_accounts:289 - 获取所有账户成功, 共 1 个
2025-06-04 10:07:55.340 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-04 10:07:55.344 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-04 10:07:55.345 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-04 10:07:55.347 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-04 10:07:55.347 | DEBUG    | ui.views.account_view:_on_accounts_loaded:575 - 账户加载完成: 1个账户
2025-06-04 10:07:55.358 | DEBUG    | ui.views.account_view:_update_group_counts:184 - 分组计数已更新: {1: 1}
2025-06-04 10:07:55.363 | INFO     | ui.views.account_view:_auto_login_accounts:630 - 开始调用控制器进行 1 个账户的自动登录。
2025-06-04 10:07:55.364 | INFO     | app.services.account_service:batch_auto_login:1153 - 服务层：开始批量自动登录 1 个账户, 最大并发: 5
2025-06-04 10:07:55.364 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-04 10:07:55.367 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-04 10:07:55.369 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-06-04 10:07:55.381 | INFO     | app.controllers.telegram_monitor_controller:_load_total_users_count_internal:191 - 成功加载总体统计数据: 总用户数 958, 今日采集 0, 日均采集 68, 运行天数 14
2025-06-04 10:07:55.382 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:156 - 成功加载 2 个任务
2025-06-04 10:07:55.382 | DEBUG    | ui.views.monitor_view:_on_loading_finished:625 - 加载完成
2025-06-04 10:07:55.400 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-04 10:07:55.408 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=True
2025-06-04 10:07:55.411 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=True
2025-06-04 10:07:55.414 | INFO     | ui.views.proxy_view:_load_initial_data:1044 - 初始数据加载完成，共 3 个代理
2025-06-04 10:07:55.415 | INFO     | app.services.account_service:get_all_accounts:289 - 获取所有账户成功, 共 1 个
2025-06-04 10:07:55.415 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-04 10:07:55.416 | INFO     | app.services.account_service:batch_auto_login:1192 - 服务层：账户 *********** 使用IP池代理ID 3, 详情: socks5 **************:10001
2025-06-04 10:07:55.417 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-04 10:07:55.420 | DEBUG    | ui.views.account_view:_update_group_counts:184 - 分组计数已更新: {1: 1}
2025-06-04 10:07:55.420 | DEBUG    | ui.views.account_view:_init_cache:151 - 账户缓存初始化完成，共 1 个账户
2025-06-04 10:07:55.425 | DEBUG    | ui.views.account_view:_on_accounts_loaded:575 - 账户加载完成: 1个账户
2025-06-04 10:07:55.437 | DEBUG    | ui.views.account_view:_update_group_counts:184 - 分组计数已更新: {1: 1}
2025-06-04 10:07:55.443 | INFO     | app.services.account_service:batch_auto_login:1262 - 服务层：准备调用核心层处理 1 个账户的自动登录。
2025-06-04 10:07:55.444 | INFO     | app.services.account_service:batch_auto_login:1272 - 服务层：设置核心层任务超时为 90 秒。
2025-06-04 10:07:55.444 | INFO     | core.telegram.client_manager:batch_auto_login:1220 - 开始批量自动登录, 共 1 个账户, 最大并发 5
2025-06-04 10:07:55.444 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:256 - 创建客户端: h:\PyProject\TeleTest\APPDATA\sessions\***********.session, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '**************', 'port': 10001}
2025-06-04 10:07:55.445 | DEBUG    | core.telegram.client_manager:get_proxy:93 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '**************', 'port': 10001}
2025-06-04 10:07:55.446 | DEBUG    | core.telegram.client_manager:get_proxy:140 - get_proxy: 使用 ip_pool 代理 **************:10001，转换结果: (<ProxyType.SOCKS5: 2>, '**************', 10001)
2025-06-04 10:07:55.452 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-04 10:07:55.464 | WARNING  | core.auth.api_service:verify_vip:441 - 会员验证失败: 验证失败
2025-06-04 10:07:59.888 | ERROR    | core.telegram.client_manager:_connect_client:217 - 认证密钥错误 (AuthKeyError): The authorization key (session file) was used under two different IP addresses simultaneously, and can no longer be used. Use the same session exclusively, or use different sessions (caused by InvokeWithLayerRequest(InitConnectionRequest(GetConfigRequest)))
2025-06-04 10:08:01.897 | INFO     | core.telegram.client_manager:batch_auto_login:1369 - 批量登录完成: 总计 1, 成功 0, 失败 1
2025-06-04 10:08:02.492 | INFO     | app.services.account_service:batch_auto_login:1293 - 服务层：核心层登录完成: 成功 0, 失败 1 / 1
2025-06-04 10:08:03.865 | INFO     | ui.main_window:closeEvent:335 - MainWindow: 接收到关闭事件
2025-06-04 10:08:03.879 | INFO     | ui.main_window:_cleanup_before_quit:228 - MainWindow: 执行清理资源...
2025-06-04 10:08:03.880 | INFO     | core.telegram.client_worker:stop:110 - 正在停止Telegram客户端工作线程
2025-06-04 10:08:03.886 | INFO     | core.telegram.client_manager:cleanup_async:1140 - 开始清理资源并断开所有连接
2025-06-04 10:08:03.887 | INFO     | core.telegram.client_manager:disconnect_all_clients:725 - 正在断开所有客户端连接
2025-06-04 10:08:03.888 | INFO     | core.telegram.client_manager:cleanup_async:1159 - 资源清理完成
2025-06-04 10:08:04.401 | INFO     | core.telegram.client_manager:cleanup_async:1140 - 开始清理资源并断开所有连接
2025-06-04 10:08:04.401 | INFO     | core.telegram.client_manager:disconnect_all_clients:725 - 正在断开所有客户端连接
2025-06-04 10:08:04.402 | INFO     | core.telegram.client_manager:cleanup_async:1159 - 资源清理完成
2025-06-04 10:08:04.917 | INFO     | core.telegram.client_worker:run:99 - Telegram客户端工作线程已退出
2025-06-04 10:08:04.918 | INFO     | core.telegram.client_worker:stop:124 - Telegram客户端工作线程已停止
2025-06-04 10:08:04.919 | INFO     | ui.main_window:_cleanup_before_quit:237 - TelegramClientWorker 已停止。
2025-06-04 10:08:04.919 | INFO     | ui.main_window:_cleanup_before_quit:241 - MainWindow 清理完成
2025-06-04 10:08:04.936 | INFO     | __main__:main:111 - 应用程序已正常退出
2025-06-04 12:02:05.236 | INFO     | utils.logger:init_logger:98 - 日志系统初始化完成，默认级别: DEBUG，日志目录: h:\PyProject\TeleTest\APPDATA\logs
2025-06-04 12:02:08.204 | INFO     | data.database:init_database:31 - 初始化数据库连接: h:\PyProject\TeleTest\APPDATA\database\telegram_manager.db
2025-06-04 12:02:08.323 | DEBUG    | data.database:init_database:58 - 创建数据库表
2025-06-04 12:02:08.341 | INFO     | data.database:init_database:61 - 数据库初始化完成
2025-06-04 12:02:09.656 | DEBUG    | app.controllers.auth_controller:initialize_software:43 - 控制器: 初始化软件 app_id=1000, version=1.0
2025-06-04 12:02:09.656 | DEBUG    | utils.client_http:get:41 - 发送GET请求: https://server.xile188.com/api/user/1000/windowns10/1.0/ini
2025-06-04 12:02:10.122 | DEBUG    | core.auth.api_service:init_software:66 - 初始化数据解密成功: {'new_content': '1.多账户管理\n2.ip池管理\n3.消息监听\n4.消息群发\n5.拉人入群', 'new_url': None, 'notice': {'id': '2', 'content': '公告内容测试3', 'time': '**********'}}
2025-06-04 12:02:10.143 | INFO     | app.controllers.auth_controller:login:61 - 控制器: 用户登录 account=zhangsan03
2025-06-04 12:02:13.208 | INFO     | core.auth.api_service:login:127 - 用户登录: account=zhangsan03
2025-06-04 12:02:13.208 | DEBUG    | utils.client_http:post:68 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/logon
2025-06-04 12:02:13.522 | INFO     | core.auth.api_service:get_user_info:347 - 获取用户信息
2025-06-04 12:02:13.522 | DEBUG    | utils.client_http:post:68 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/info
2025-06-04 12:02:13.747 | INFO     | core.auth.api_service:get_user_info:375 - 获取用户信息成功
2025-06-04 12:02:13.754 | INFO     | __main__:on_auth_completed:93 - 用户登录成功，正在启动主窗口...
2025-06-04 12:02:13.772 | INFO     | core.telegram.client_worker:__init__:63 - Telegram客户端工作线程初始化
2025-06-04 12:02:13.779 | INFO     | core.telegram.client_worker:run:67 - Telegram客户端工作线程开始运行
2025-06-04 12:02:13.782 | INFO     | core.telegram.client_manager:__init__:66 - Telegram客户端管理器初始化
2025-06-04 12:02:13.783 | INFO     | core.telegram.user_manager:__init__:31 - Telegram用户管理器初始化
2025-06-04 12:02:13.783 | INFO     | core.telegram.group_manager:__init__:36 - Telegram群组管理器初始化
2025-06-04 12:02:13.784 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-06-04 12:02:13.784 | INFO     | core.telegram.monitor_manager:__init__:26 - Telegram监控管理器初始化
2025-06-04 12:02:13.784 | INFO     | core.telegram.telegram_service:__init__:39 - Telegram服务初始化完成
2025-06-04 12:02:13.786 | INFO     | ui.main_window:_initialize_core_components:79 - MainWindow: 初始化核心组件...
2025-06-04 12:02:13.788 | INFO     | app.services.account_service:__init__:42 - 账户服务初始化
2025-06-04 12:02:13.788 | INFO     | core.proxy.proxy_task_manager:run:284 - 代理任务管理器线程启动
2025-06-04 12:02:13.788 | INFO     | app.controllers.account_controller:__init__:81 - 账户控制器初始化
2025-06-04 12:02:13.789 | INFO     | app.services.monitor_service:__init__:39 - 初始化监控任务服务
2025-06-04 12:02:13.790 | INFO     | core.monitor.filter:__init__:26 - 初始化消息过滤器
2025-06-04 12:02:13.790 | INFO     | core.monitor.extractor:__init__:24 - 初始化关键词提取器
2025-06-04 12:02:13.790 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-06-04 12:02:13.791 | INFO     | core.monitor.notifier:__init__:90 - 初始化通知管理器
2025-06-04 12:02:13.791 | INFO     | app.controllers.telegram_monitor_controller:__init__:64 - 初始化 Telegram 监控控制器
2025-06-04 12:02:13.792 | DEBUG    | app.controllers.telegram_monitor_controller:__init__:82 - 控制器初始化完成
2025-06-04 12:02:14.003 | INFO     | ui.views.monitor_view:__init__:48 - 初始化监控视图
2025-06-04 12:02:14.004 | DEBUG    | ui.views.monitor_view:_connect_signals:80 - 连接控制器信号
2025-06-04 12:02:14.205 | INFO     | ui.views.proxy_view:__init__:96 - 初始化视图，加载第一页代理数据
2025-06-04 12:02:14.410 | INFO     | __main__:on_auth_completed:97 - 主窗口已启动
2025-06-04 12:02:14.463 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:88 - 尝试连接监听日志信号...
2025-06-04 12:02:14.464 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:90 - 监听日志信号连接成功
2025-06-04 12:02:14.465 | INFO     | core.auth.api_service:verify_vip:425 - 会员验证接口调用
2025-06-04 12:02:14.465 | DEBUG    | utils.client_http:post:68 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/vip
2025-06-04 12:02:14.466 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-04 12:02:14.470 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-04 12:02:14.474 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:145 - 加载监控任务列表 (搜索关键词: '')
2025-06-04 12:02:14.475 | DEBUG    | ui.views.monitor_view:_on_loading_started:620 - 加载中: 正在加载任务列表...
2025-06-04 12:02:14.475 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-04 12:02:14.482 | INFO     | app.services.proxy_service:refresh_proxies:712 - 刷新代理列表: offset=0, limit=20
2025-06-04 12:02:14.482 | INFO     | core.proxy.proxy_task_manager:submit_task:172 - 提交耗时任务到子线程: refresh_proxies_0_20
2025-06-04 12:02:14.483 | DEBUG    | app.services.proxy_service:get_proxies:500 - 获取代理列表: offset=0, limit=20
2025-06-04 12:02:14.483 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-04 12:02:14.483 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-04 12:02:14.487 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-04 12:02:14.514 | INFO     | app.controllers.invite_controller:get_invite_tasks:38 - 获取邀请任务列表, status=None
2025-06-04 12:02:14.516 | INFO     | app.services.invite_service:get_invite_tasks:121 - 获取邀请任务列表, status=None
2025-06-04 12:02:14.517 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-04 12:02:14.517 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-04 12:02:14.518 | INFO     | app.services.invite_service:task_initialize:39 - 初始化邀请服务，重置运行中的任务为暂停状态
2025-06-04 12:02:14.519 | INFO     | core.proxy.proxy_task_manager:task_wrapper:199 - 任务 refresh_proxies_0_20 完成
2025-06-04 12:02:14.519 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-04 12:02:14.645 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-04 12:02:14.648 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-04 12:02:14.652 | INFO     | app.services.account_service:get_all_groups:96 - 获取所有账户分组成功, 共 1 个
2025-06-04 12:02:14.652 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-04 12:02:14.653 | INFO     | app.services.invite_service:task_initialize:48 - 成功重置 0 个任务状态
2025-06-04 12:02:14.653 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-04 12:02:14.654 | DEBUG    | ui.views.monitor_view:_refresh_task_list:175 - 刷新任务列表，共2个任务
2025-06-04 12:02:14.661 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-04 12:02:14.668 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-04 12:02:14.671 | DEBUG    | ui.views.account_view:_on_groups_loaded:442 - 分组加载完成: 1个分组
2025-06-04 12:02:14.678 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-04 12:02:14.683 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-04 12:02:14.686 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-04 12:02:14.806 | INFO     | app.services.account_service:get_all_accounts:289 - 获取所有账户成功, 共 1 个
2025-06-04 12:02:14.807 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-04 12:02:14.931 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-04 12:02:14.933 | DEBUG    | ui.views.account_view:_on_accounts_loaded:575 - 账户加载完成: 1个账户
2025-06-04 12:02:14.943 | DEBUG    | ui.views.account_view:_update_group_counts:184 - 分组计数已更新: {1: 1}
2025-06-04 12:02:14.948 | INFO     | ui.views.account_view:_auto_login_accounts:630 - 开始调用控制器进行 1 个账户的自动登录。
2025-06-04 12:02:14.948 | INFO     | app.services.account_service:batch_auto_login:1153 - 服务层：开始批量自动登录 1 个账户, 最大并发: 5
2025-06-04 12:02:14.949 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-04 12:02:14.959 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-04 12:02:14.964 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-04 12:02:14.967 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-04 12:02:14.969 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-06-04 12:02:14.990 | INFO     | app.controllers.telegram_monitor_controller:_load_total_users_count_internal:191 - 成功加载总体统计数据: 总用户数 958, 今日采集 0, 日均采集 68, 运行天数 14
2025-06-04 12:02:14.991 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:156 - 成功加载 2 个任务
2025-06-04 12:02:14.991 | DEBUG    | ui.views.monitor_view:_on_loading_finished:625 - 加载完成
2025-06-04 12:02:14.997 | INFO     | app.services.account_service:get_all_accounts:289 - 获取所有账户成功, 共 1 个
2025-06-04 12:02:14.998 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-04 12:02:14.999 | INFO     | app.services.account_service:batch_auto_login:1192 - 服务层：账户 *********** 使用IP池代理ID 3, 详情: socks5 **************:10001
2025-06-04 12:02:15.000 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-04 12:02:15.017 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-04 12:02:15.021 | WARNING  | core.auth.api_service:verify_vip:441 - 会员验证失败: 验证失败
2025-06-04 12:02:15.214 | DEBUG    | ui.views.account_view:_update_group_counts:184 - 分组计数已更新: {1: 1}
2025-06-04 12:02:15.215 | DEBUG    | ui.views.account_view:_init_cache:151 - 账户缓存初始化完成，共 1 个账户
2025-06-04 12:02:15.219 | INFO     | app.services.account_service:batch_auto_login:1262 - 服务层：准备调用核心层处理 1 个账户的自动登录。
2025-06-04 12:02:15.220 | INFO     | app.services.account_service:batch_auto_login:1272 - 服务层：设置核心层任务超时为 90 秒。
2025-06-04 12:02:15.220 | INFO     | core.telegram.client_manager:batch_auto_login:1220 - 开始批量自动登录, 共 1 个账户, 最大并发 5
2025-06-04 12:02:15.221 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:256 - 创建客户端: h:\PyProject\TeleTest\APPDATA\sessions\***********.session, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '**************', 'port': 10001}
2025-06-04 12:02:15.221 | DEBUG    | core.telegram.client_manager:get_proxy:93 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '**************', 'port': 10001}
2025-06-04 12:02:15.222 | DEBUG    | ui.views.account_view:_on_accounts_loaded:575 - 账户加载完成: 1个账户
2025-06-04 12:02:15.222 | DEBUG    | core.telegram.client_manager:get_proxy:140 - get_proxy: 使用 ip_pool 代理 **************:10001，转换结果: (<ProxyType.SOCKS5: 2>, '**************', 10001)
2025-06-04 12:02:15.238 | DEBUG    | ui.views.account_view:_update_group_counts:184 - 分组计数已更新: {1: 1}
2025-06-04 12:02:15.345 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-04 12:02:15.476 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-06-04 12:02:15.478 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-06-04 12:02:15.481 | INFO     | ui.views.proxy_view:_load_initial_data:1044 - 初始数据加载完成，共 3 个代理
2025-06-04 12:02:20.937 | ERROR    | core.telegram.client_manager:_connect_client:217 - 认证密钥错误 (AuthKeyError): The authorization key (session file) was used under two different IP addresses simultaneously, and can no longer be used. Use the same session exclusively, or use different sessions (caused by InvokeWithLayerRequest(InitConnectionRequest(GetConfigRequest)))
2025-06-04 12:02:22.945 | INFO     | core.telegram.client_manager:batch_auto_login:1369 - 批量登录完成: 总计 1, 成功 0, 失败 1
2025-06-04 12:02:25.953 | INFO     | ui.main_window:closeEvent:335 - MainWindow: 接收到关闭事件
2025-06-04 12:02:25.969 | INFO     | ui.main_window:_cleanup_before_quit:228 - MainWindow: 执行清理资源...
2025-06-04 12:02:25.973 | INFO     | core.telegram.client_worker:stop:110 - 正在停止Telegram客户端工作线程
2025-06-04 12:02:25.999 | INFO     | core.telegram.client_manager:cleanup_async:1140 - 开始清理资源并断开所有连接
2025-06-04 12:02:25.999 | INFO     | core.telegram.client_manager:disconnect_all_clients:725 - 正在断开所有客户端连接
2025-06-04 12:02:26.000 | INFO     | core.telegram.client_manager:cleanup_async:1159 - 资源清理完成
2025-06-04 12:02:26.508 | INFO     | core.telegram.client_manager:cleanup_async:1140 - 开始清理资源并断开所有连接
2025-06-04 12:02:26.508 | INFO     | core.telegram.client_manager:disconnect_all_clients:725 - 正在断开所有客户端连接
2025-06-04 12:02:26.509 | INFO     | core.telegram.client_manager:cleanup_async:1159 - 资源清理完成
2025-06-04 12:02:27.011 | INFO     | core.telegram.client_worker:run:99 - Telegram客户端工作线程已退出
2025-06-04 12:02:27.012 | INFO     | core.telegram.client_worker:stop:124 - Telegram客户端工作线程已停止
2025-06-04 12:02:27.012 | INFO     | ui.main_window:_cleanup_before_quit:237 - TelegramClientWorker 已停止。
2025-06-04 12:02:27.013 | INFO     | ui.main_window:_cleanup_before_quit:241 - MainWindow 清理完成
2025-06-04 12:02:27.030 | INFO     | __main__:main:111 - 应用程序已正常退出
2025-06-04 12:15:37.696 | INFO     | utils.logger:init_logger:98 - 日志系统初始化完成，默认级别: DEBUG，日志目录: h:\PyProject\TeleTest\APPDATA\logs
2025-06-04 12:15:39.232 | INFO     | data.database:init_database:31 - 初始化数据库连接: h:\PyProject\TeleTest\APPDATA\database\telegram_manager.db
2025-06-04 12:15:39.309 | DEBUG    | data.database:init_database:58 - 创建数据库表
2025-06-04 12:15:39.318 | INFO     | data.database:init_database:61 - 数据库初始化完成
2025-06-04 12:15:40.393 | DEBUG    | app.controllers.auth_controller:initialize_software:43 - 控制器: 初始化软件 app_id=1000, version=1.0
2025-06-04 12:15:40.394 | DEBUG    | utils.client_http:get:41 - 发送GET请求: https://server.xile188.com/api/user/1000/windowns10/1.0/ini
2025-06-04 12:15:40.898 | DEBUG    | core.auth.api_service:init_software:66 - 初始化数据解密成功: {'new_content': '1.多账户管理\n2.ip池管理\n3.消息监听\n4.消息群发\n5.拉人入群', 'new_url': None, 'notice': {'id': '2', 'content': '公告内容测试3', 'time': '**********'}}
2025-06-04 12:15:40.905 | INFO     | app.controllers.auth_controller:login:61 - 控制器: 用户登录 account=zhangsan03
2025-06-04 12:15:43.914 | INFO     | core.auth.api_service:login:127 - 用户登录: account=zhangsan03
2025-06-04 12:15:43.914 | DEBUG    | utils.client_http:post:68 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/logon
2025-06-04 12:15:44.166 | INFO     | core.auth.api_service:get_user_info:347 - 获取用户信息
2025-06-04 12:15:44.167 | DEBUG    | utils.client_http:post:68 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/info
2025-06-04 12:15:44.437 | INFO     | core.auth.api_service:get_user_info:375 - 获取用户信息成功
2025-06-04 12:15:44.445 | INFO     | __main__:on_auth_completed:93 - 用户登录成功，正在启动主窗口...
2025-06-04 12:15:44.460 | INFO     | core.telegram.client_worker:__init__:63 - Telegram客户端工作线程初始化
2025-06-04 12:15:44.464 | INFO     | core.telegram.client_worker:run:67 - Telegram客户端工作线程开始运行
2025-06-04 12:15:44.466 | INFO     | core.telegram.client_manager:__init__:66 - Telegram客户端管理器初始化
2025-06-04 12:15:44.467 | INFO     | core.telegram.user_manager:__init__:31 - Telegram用户管理器初始化
2025-06-04 12:15:44.467 | INFO     | core.telegram.group_manager:__init__:36 - Telegram群组管理器初始化
2025-06-04 12:15:44.468 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-06-04 12:15:44.468 | INFO     | core.telegram.monitor_manager:__init__:26 - Telegram监控管理器初始化
2025-06-04 12:15:44.469 | INFO     | core.telegram.telegram_service:__init__:39 - Telegram服务初始化完成
2025-06-04 12:15:44.470 | INFO     | ui.main_window:_initialize_core_components:79 - MainWindow: 初始化核心组件...
2025-06-04 12:15:44.471 | INFO     | app.services.account_service:__init__:42 - 账户服务初始化
2025-06-04 12:15:44.471 | INFO     | core.proxy.proxy_task_manager:run:284 - 代理任务管理器线程启动
2025-06-04 12:15:44.472 | INFO     | app.controllers.account_controller:__init__:81 - 账户控制器初始化
2025-06-04 12:15:44.472 | INFO     | app.services.monitor_service:__init__:39 - 初始化监控任务服务
2025-06-04 12:15:44.472 | INFO     | core.monitor.filter:__init__:26 - 初始化消息过滤器
2025-06-04 12:15:44.473 | INFO     | core.monitor.extractor:__init__:24 - 初始化关键词提取器
2025-06-04 12:15:44.473 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-06-04 12:15:44.473 | INFO     | core.monitor.notifier:__init__:90 - 初始化通知管理器
2025-06-04 12:15:44.474 | INFO     | app.controllers.telegram_monitor_controller:__init__:64 - 初始化 Telegram 监控控制器
2025-06-04 12:15:44.474 | DEBUG    | app.controllers.telegram_monitor_controller:__init__:82 - 控制器初始化完成
2025-06-04 12:15:44.656 | INFO     | ui.views.monitor_view:__init__:48 - 初始化监控视图
2025-06-04 12:15:44.657 | DEBUG    | ui.views.monitor_view:_connect_signals:80 - 连接控制器信号
2025-06-04 12:15:44.830 | INFO     | ui.views.proxy_view:__init__:96 - 初始化视图，加载第一页代理数据
2025-06-04 12:15:45.050 | INFO     | __main__:on_auth_completed:97 - 主窗口已启动
2025-06-04 12:15:45.111 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:88 - 尝试连接监听日志信号...
2025-06-04 12:15:45.112 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:90 - 监听日志信号连接成功
2025-06-04 12:15:45.113 | INFO     | core.auth.api_service:verify_vip:425 - 会员验证接口调用
2025-06-04 12:15:45.114 | DEBUG    | utils.client_http:post:68 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/vip
2025-06-04 12:15:45.115 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-04 12:15:45.120 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-04 12:15:45.125 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:145 - 加载监控任务列表 (搜索关键词: '')
2025-06-04 12:15:45.126 | DEBUG    | ui.views.monitor_view:_on_loading_started:620 - 加载中: 正在加载任务列表...
2025-06-04 12:15:45.127 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-04 12:15:45.134 | INFO     | app.services.proxy_service:refresh_proxies:712 - 刷新代理列表: offset=0, limit=20
2025-06-04 12:15:45.135 | INFO     | core.proxy.proxy_task_manager:submit_task:172 - 提交耗时任务到子线程: refresh_proxies_0_20
2025-06-04 12:15:45.136 | DEBUG    | app.services.proxy_service:get_proxies:500 - 获取代理列表: offset=0, limit=20
2025-06-04 12:15:45.136 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-04 12:15:45.136 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-04 12:15:45.143 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-04 12:15:45.175 | INFO     | app.controllers.invite_controller:get_invite_tasks:38 - 获取邀请任务列表, status=None
2025-06-04 12:15:45.176 | INFO     | app.services.invite_service:get_invite_tasks:121 - 获取邀请任务列表, status=None
2025-06-04 12:15:45.180 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-04 12:15:45.180 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-04 12:15:45.182 | INFO     | app.services.invite_service:task_initialize:39 - 初始化邀请服务，重置运行中的任务为暂停状态
2025-06-04 12:15:45.182 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-04 12:15:45.183 | INFO     | core.proxy.proxy_task_manager:task_wrapper:199 - 任务 refresh_proxies_0_20 完成
2025-06-04 12:15:45.304 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-04 12:15:45.311 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-04 12:15:45.314 | INFO     | app.services.invite_service:task_initialize:48 - 成功重置 0 个任务状态
2025-06-04 12:15:45.314 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-04 12:15:45.315 | INFO     | app.services.account_service:get_all_groups:96 - 获取所有账户分组成功, 共 1 个
2025-06-04 12:15:45.316 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-04 12:15:45.317 | DEBUG    | ui.views.monitor_view:_refresh_task_list:175 - 刷新任务列表，共2个任务
2025-06-04 12:15:45.322 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-04 12:15:45.330 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-04 12:15:45.333 | DEBUG    | ui.views.account_view:_on_groups_loaded:442 - 分组加载完成: 1个分组
2025-06-04 12:15:45.339 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-04 12:15:45.344 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-04 12:15:45.347 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-04 12:15:45.416 | INFO     | app.services.account_service:get_all_accounts:289 - 获取所有账户成功, 共 1 个
2025-06-04 12:15:45.417 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-04 12:15:45.421 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-04 12:15:45.423 | DEBUG    | ui.views.account_view:_on_accounts_loaded:575 - 账户加载完成: 1个账户
2025-06-04 12:15:45.476 | DEBUG    | ui.views.account_view:_update_group_counts:184 - 分组计数已更新: {1: 1}
2025-06-04 12:15:45.485 | INFO     | ui.views.account_view:_auto_login_accounts:630 - 开始调用控制器进行 1 个账户的自动登录。
2025-06-04 12:15:45.486 | INFO     | app.services.account_service:batch_auto_login:1153 - 服务层：开始批量自动登录 1 个账户, 最大并发: 5
2025-06-04 12:15:45.487 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-04 12:15:45.526 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-04 12:15:45.529 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-04 12:15:45.530 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-04 12:15:45.536 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-06-04 12:15:45.592 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-04 12:15:45.596 | INFO     | app.controllers.telegram_monitor_controller:_load_total_users_count_internal:191 - 成功加载总体统计数据: 总用户数 958, 今日采集 0, 日均采集 68, 运行天数 14
2025-06-04 12:15:45.597 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:156 - 成功加载 2 个任务
2025-06-04 12:15:45.598 | DEBUG    | ui.views.monitor_view:_on_loading_finished:625 - 加载完成
2025-06-04 12:15:45.602 | INFO     | app.services.account_service:get_all_accounts:289 - 获取所有账户成功, 共 1 个
2025-06-04 12:15:45.603 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-04 12:15:45.604 | INFO     | app.services.account_service:batch_auto_login:1192 - 服务层：账户 *********** 使用IP池代理ID 3, 详情: socks5 **************:10001
2025-06-04 12:15:45.604 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-04 12:15:45.605 | WARNING  | core.auth.api_service:verify_vip:441 - 会员验证失败: 验证失败
2025-06-04 12:15:45.832 | INFO     | app.services.account_service:batch_auto_login:1262 - 服务层：准备调用核心层处理 1 个账户的自动登录。
2025-06-04 12:15:45.833 | INFO     | app.services.account_service:batch_auto_login:1272 - 服务层：设置核心层任务超时为 90 秒。
2025-06-04 12:15:45.833 | INFO     | core.telegram.client_manager:batch_auto_login:1220 - 开始批量自动登录, 共 1 个账户, 最大并发 5
2025-06-04 12:15:45.834 | DEBUG    | ui.views.account_view:_update_group_counts:184 - 分组计数已更新: {1: 1}
2025-06-04 12:15:45.834 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:256 - 创建客户端: h:\PyProject\TeleTest\APPDATA\sessions\***********.session, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '**************', 'port': 10001}
2025-06-04 12:15:45.834 | DEBUG    | ui.views.account_view:_init_cache:151 - 账户缓存初始化完成，共 1 个账户
2025-06-04 12:15:45.835 | DEBUG    | core.telegram.client_manager:get_proxy:93 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '**************', 'port': 10001}
2025-06-04 12:15:45.836 | DEBUG    | core.telegram.client_manager:get_proxy:140 - get_proxy: 使用 ip_pool 代理 **************:10001，转换结果: (<ProxyType.SOCKS5: 2>, '**************', 10001)
2025-06-04 12:15:45.844 | DEBUG    | ui.views.account_view:_on_accounts_loaded:575 - 账户加载完成: 1个账户
2025-06-04 12:15:45.861 | DEBUG    | ui.views.account_view:_update_group_counts:184 - 分组计数已更新: {1: 1}
2025-06-04 12:15:45.924 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-04 12:15:45.933 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-06-04 12:15:45.935 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-06-04 12:15:45.937 | INFO     | ui.views.proxy_view:_load_initial_data:1044 - 初始数据加载完成，共 3 个代理
2025-06-04 12:15:52.044 | ERROR    | core.telegram.client_manager:_connect_client:217 - 认证密钥错误 (AuthKeyError): The authorization key (session file) was used under two different IP addresses simultaneously, and can no longer be used. Use the same session exclusively, or use different sessions (caused by InvokeWithLayerRequest(InitConnectionRequest(GetConfigRequest)))
2025-06-04 12:15:54.062 | INFO     | core.telegram.client_manager:batch_auto_login:1369 - 批量登录完成: 总计 1, 成功 0, 失败 1
2025-06-04 12:15:54.864 | INFO     | app.services.account_service:batch_auto_login:1293 - 服务层：核心层登录完成: 成功 0, 失败 1 / 1
2025-06-04 12:16:31.883 | INFO     | app.services.account_service:refresh_account_info:595 - 刷新账户信息: 1
2025-06-04 12:16:31.883 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-04 12:16:31.892 | INFO     | core.telegram.user_manager:get_user_info:42 - 正在获取用户信息: ***********
2025-06-04 12:16:31.893 | WARNING  | core.telegram.user_manager:get_user_info:48 - 获取用户信息失败, 找不到客户端: ***********
2025-06-04 12:16:32.039 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-04 12:16:44.428 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-04 12:16:44.428 | DEBUG    | utils.client_http:post:68 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-04 12:16:45.842 | INFO     | ui.views.account_view:_on_delete_account:365 - 请求删除账号: ***********
2025-06-04 12:16:45.842 | INFO     | ui.views.account_view:_on_delete_account:369 - 查找账号ID: ***********
2025-06-04 12:16:46.801 | INFO     | app.services.account_service:delete_account:359 - 删除账户: 1, 删除session: True
2025-06-04 12:16:46.802 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-04 12:16:46.982 | INFO     | core.telegram.client_manager:disconnect_client:707 - 正在断开客户端连接: ***********
2025-06-04 12:16:46.983 | WARNING  | core.telegram.client_manager:disconnect_client:710 - 找不到客户端: ***********
2025-06-04 12:16:47.132 | INFO     | data.repositories.account_repo:delete_account:450 - 删除账户成功: ID=1
2025-06-04 12:16:47.142 | INFO     | app.services.account_service:delete_account:388 - 删除session文件: h:\PyProject\TeleTest\APPDATA\sessions\***********.session
2025-06-04 12:16:47.142 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-04 12:16:47.143 | INFO     | ui.views.account_view:_on_account_deleted:642 - 账户删除成功: ID=1
2025-06-04 12:16:47.144 | DEBUG    | ui.views.account_view:_update_group_counts:184 - 分组计数已更新: {}
2025-06-04 12:16:47.149 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-04 12:16:47.159 | INFO     | app.services.account_service:get_all_accounts:289 - 获取所有账户成功, 共 0 个
2025-06-04 12:16:47.159 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-04 12:16:47.163 | DEBUG    | ui.views.account_view:_on_accounts_loaded:575 - 账户加载完成: 0个账户
2025-06-04 12:16:47.164 | DEBUG    | ui.views.account_view:_update_group_counts:184 - 分组计数已更新: {}
2025-06-04 12:16:51.058 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-04 12:16:51.062 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-04 12:16:51.064 | INFO     | app.services.proxy_service:validate_all_proxies:332 - 开始验证所有代理
2025-06-04 12:16:51.064 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-04 12:16:51.077 | INFO     | core.proxy.proxy_core_service:validate_proxies_stream:167 - 批量验证代理(流式): 3个
2025-06-04 12:16:51.078 | INFO     | core.proxy.proxy_validator:validate_proxy:43 - 正在验证代理 socks5://**************:10001
2025-06-04 12:16:56.271 | WARNING  | core.proxy.proxy_validator:validate_proxy:71 - 使用https://web.telegram.org验证代理失败: socks5://**************:10001, 错误: 
2025-06-04 12:17:01.271 | WARNING  | core.proxy.proxy_validator:validate_proxy:71 - 使用https://core.telegram.org验证代理失败: socks5://**************:10001, 错误: 
2025-06-04 12:17:01.272 | WARNING  | core.proxy.proxy_validator:validate_proxy:75 - 代理验证失败: socks5://**************:10001
2025-06-04 12:17:01.273 | INFO     | core.proxy.proxy_validator:validate_proxy:43 - 正在验证代理 socks5://156.240.16.190:10061
2025-06-04 12:17:06.274 | WARNING  | core.proxy.proxy_validator:validate_proxy:71 - 使用https://web.telegram.org验证代理失败: socks5://156.240.16.190:10061, 错误: 
2025-06-04 12:17:11.272 | WARNING  | core.proxy.proxy_validator:validate_proxy:71 - 使用https://core.telegram.org验证代理失败: socks5://156.240.16.190:10061, 错误: 
2025-06-04 12:17:11.273 | WARNING  | core.proxy.proxy_validator:validate_proxy:75 - 代理验证失败: socks5://156.240.16.190:10061
2025-06-04 12:17:11.273 | INFO     | core.proxy.proxy_validator:validate_proxy:43 - 正在验证代理 socks5://127.0.0.1:1080
2025-06-04 12:17:13.645 | INFO     | core.proxy.proxy_validator:validate_proxy:68 - 代理验证成功: socks5://127.0.0.1:1080, 耗时: 2370.95ms
2025-06-04 12:17:14.486 | INFO     | core.proxy.proxy_core_service:validate_proxies_stream:232 - 批量代理验证完成，结果: 1/3 个有效
2025-06-04 12:17:14.487 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-04 12:17:14.488 | INFO     | app.services.proxy_service:refresh_proxies:712 - 刷新代理列表: offset=0, limit=20
2025-06-04 12:17:14.489 | INFO     | core.proxy.proxy_task_manager:submit_task:172 - 提交耗时任务到子线程: refresh_proxies_0_20
2025-06-04 12:17:14.489 | DEBUG    | app.services.proxy_service:get_proxies:500 - 获取代理列表: offset=0, limit=20
2025-06-04 12:17:14.490 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-04 12:17:14.490 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-04 12:17:14.504 | INFO     | ui.views.proxy_view:load_proxies_by_page:525 - 加载代理页面: 1
2025-06-04 12:17:14.505 | INFO     | app.services.proxy_service:refresh_proxies:712 - 刷新代理列表: offset=0, limit=20
2025-06-04 12:17:14.505 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-04 12:17:14.506 | INFO     | app.services.proxy_service:refresh_proxies:719 - 刷新任务已在运行，跳过
2025-06-04 12:17:14.506 | DEBUG    | app.services.proxy_service:get_proxies:500 - 获取代理列表: offset=0, limit=20
2025-06-04 12:17:14.507 | INFO     | core.proxy.proxy_task_manager:task_wrapper:199 - 任务 refresh_proxies_0_20 完成
2025-06-04 12:17:14.507 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-04 12:17:14.509 | INFO     | ui.views.proxy_view:load_proxies_by_page:525 - 加载代理页面: 1
2025-06-04 12:17:14.510 | INFO     | app.services.proxy_service:refresh_proxies:712 - 刷新代理列表: offset=0, limit=20
2025-06-04 12:17:14.511 | INFO     | core.proxy.proxy_task_manager:submit_task:172 - 提交耗时任务到子线程: refresh_proxies_0_20
2025-06-04 12:17:14.511 | DEBUG    | app.services.proxy_service:get_proxies:500 - 获取代理列表: offset=0, limit=20
2025-06-04 12:17:14.511 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-04 12:17:14.512 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-04 12:17:14.520 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-04 12:17:14.523 | INFO     | core.proxy.proxy_task_manager:task_wrapper:199 - 任务 refresh_proxies_0_20 完成
2025-06-04 12:17:14.529 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-04 12:17:14.531 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-04 12:17:14.532 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-04 12:17:14.534 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-04 12:17:14.536 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-04 12:17:14.538 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-04 12:17:14.547 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-04 12:17:14.549 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-04 12:17:14.551 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-04 12:17:14.552 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-06-04 12:17:14.559 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-06-04 12:17:14.566 | INFO     | ui.views.proxy_view:load_proxies_by_page:538 - 页面 1 加载完成，显示 3 条记录，共 3 条
2025-06-04 12:17:14.579 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-06-04 12:17:14.585 | INFO     | ui.views.proxy_view:load_proxies_by_page:538 - 页面 1 加载完成，显示 3 条记录，共 3 条
2025-06-04 12:17:44.435 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-04 12:17:44.436 | DEBUG    | utils.client_http:post:68 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-04 12:18:07.406 | INFO     | ui.main_window:closeEvent:335 - MainWindow: 接收到关闭事件
2025-06-04 12:18:07.418 | INFO     | ui.main_window:_cleanup_before_quit:228 - MainWindow: 执行清理资源...
2025-06-04 12:18:07.419 | INFO     | core.telegram.client_worker:stop:110 - 正在停止Telegram客户端工作线程
2025-06-04 12:18:07.427 | INFO     | core.telegram.client_manager:cleanup_async:1140 - 开始清理资源并断开所有连接
2025-06-04 12:18:07.428 | INFO     | core.telegram.client_manager:disconnect_all_clients:725 - 正在断开所有客户端连接
2025-06-04 12:18:07.428 | INFO     | core.telegram.client_manager:cleanup_async:1159 - 资源清理完成
2025-06-04 12:18:07.936 | INFO     | core.telegram.client_manager:cleanup_async:1140 - 开始清理资源并断开所有连接
2025-06-04 12:18:07.936 | INFO     | core.telegram.client_manager:disconnect_all_clients:725 - 正在断开所有客户端连接
2025-06-04 12:18:07.937 | INFO     | core.telegram.client_manager:cleanup_async:1159 - 资源清理完成
2025-06-04 12:18:08.450 | INFO     | core.telegram.client_worker:run:99 - Telegram客户端工作线程已退出
2025-06-04 12:18:08.451 | INFO     | core.telegram.client_worker:stop:124 - Telegram客户端工作线程已停止
2025-06-04 12:18:08.451 | INFO     | ui.main_window:_cleanup_before_quit:237 - TelegramClientWorker 已停止。
2025-06-04 12:18:08.451 | INFO     | ui.main_window:_cleanup_before_quit:241 - MainWindow 清理完成
2025-06-04 12:18:08.470 | INFO     | __main__:main:111 - 应用程序已正常退出
2025-06-04 14:32:57.280 | INFO     | utils.logger:init_logger:98 - 日志系统初始化完成，默认级别: DEBUG，日志目录: h:\PyProject\TeleTest\APPDATA\logs
2025-06-04 14:32:59.984 | INFO     | data.database:init_database:31 - 初始化数据库连接: h:\PyProject\TeleTest\APPDATA\database\telegram_manager.db
2025-06-04 14:33:00.067 | DEBUG    | data.database:init_database:58 - 创建数据库表
2025-06-04 14:33:00.084 | INFO     | data.database:init_database:61 - 数据库初始化完成
2025-06-04 14:33:01.807 | DEBUG    | app.controllers.auth_controller:initialize_software:43 - 控制器: 初始化软件 app_id=1000, version=1.0
2025-06-04 14:33:01.807 | DEBUG    | utils.client_http:get:54 - 发送GET请求: https://server.xile188.com/api/user/1000/windowns10/1.0/ini
2025-06-04 14:33:02.306 | DEBUG    | core.auth.api_service:init_software:66 - 初始化数据解密成功: {'new_content': '1.多账户管理\n2.ip池管理\n3.消息监听\n4.消息群发\n5.拉人入群', 'new_url': None, 'notice': {'id': '2', 'content': '公告内容测试3', 'time': '**********'}}
2025-06-04 14:33:02.319 | INFO     | app.controllers.auth_controller:login:61 - 控制器: 用户登录 account=zhangsan03
2025-06-04 14:33:05.226 | INFO     | core.auth.api_service:login:127 - 用户登录: account=zhangsan03
2025-06-04 14:33:05.227 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/logon
2025-06-04 14:33:05.454 | INFO     | core.auth.api_service:get_user_info:347 - 获取用户信息
2025-06-04 14:33:05.454 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/info
2025-06-04 14:33:05.720 | INFO     | core.auth.api_service:get_user_info:375 - 获取用户信息成功
2025-06-04 14:33:05.728 | INFO     | __main__:on_auth_completed:93 - 用户登录成功，正在启动主窗口...
2025-06-04 14:33:05.750 | INFO     | core.telegram.client_worker:__init__:63 - Telegram客户端工作线程初始化
2025-06-04 14:33:05.757 | INFO     | core.telegram.client_worker:run:67 - Telegram客户端工作线程开始运行
2025-06-04 14:33:05.761 | INFO     | core.telegram.client_manager:__init__:66 - Telegram客户端管理器初始化
2025-06-04 14:33:05.762 | INFO     | core.telegram.user_manager:__init__:31 - Telegram用户管理器初始化
2025-06-04 14:33:05.762 | INFO     | core.telegram.group_manager:__init__:36 - Telegram群组管理器初始化
2025-06-04 14:33:05.763 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-06-04 14:33:05.763 | INFO     | core.telegram.monitor_manager:__init__:26 - Telegram监控管理器初始化
2025-06-04 14:33:05.764 | INFO     | core.telegram.telegram_service:__init__:39 - Telegram服务初始化完成
2025-06-04 14:33:05.767 | INFO     | ui.main_window:_initialize_core_components:79 - MainWindow: 初始化核心组件...
2025-06-04 14:33:05.768 | INFO     | app.services.account_service:__init__:42 - 账户服务初始化
2025-06-04 14:33:05.768 | INFO     | core.proxy.proxy_task_manager:run:284 - 代理任务管理器线程启动
2025-06-04 14:33:05.768 | INFO     | app.controllers.account_controller:__init__:81 - 账户控制器初始化
2025-06-04 14:33:05.769 | INFO     | app.services.monitor_service:__init__:39 - 初始化监控任务服务
2025-06-04 14:33:05.769 | INFO     | core.monitor.filter:__init__:26 - 初始化消息过滤器
2025-06-04 14:33:05.769 | INFO     | core.monitor.extractor:__init__:24 - 初始化关键词提取器
2025-06-04 14:33:05.770 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-06-04 14:33:05.770 | INFO     | core.monitor.notifier:__init__:90 - 初始化通知管理器
2025-06-04 14:33:05.770 | INFO     | app.controllers.telegram_monitor_controller:__init__:64 - 初始化 Telegram 监控控制器
2025-06-04 14:33:05.771 | DEBUG    | app.controllers.telegram_monitor_controller:__init__:82 - 控制器初始化完成
2025-06-04 14:33:05.967 | INFO     | ui.views.monitor_view:__init__:48 - 初始化监控视图
2025-06-04 14:33:05.967 | DEBUG    | ui.views.monitor_view:_connect_signals:80 - 连接控制器信号
2025-06-04 14:33:06.140 | INFO     | ui.views.proxy_view:__init__:96 - 初始化视图，加载第一页代理数据
2025-06-04 14:33:06.340 | INFO     | __main__:on_auth_completed:97 - 主窗口已启动
2025-06-04 14:33:06.390 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:88 - 尝试连接监听日志信号...
2025-06-04 14:33:06.391 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:90 - 监听日志信号连接成功
2025-06-04 14:33:06.392 | INFO     | core.auth.api_service:verify_vip:425 - 会员验证接口调用
2025-06-04 14:33:06.392 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/vip
2025-06-04 14:33:06.393 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-04 14:33:06.397 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-04 14:33:06.401 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:145 - 加载监控任务列表 (搜索关键词: '')
2025-06-04 14:33:06.402 | DEBUG    | ui.views.monitor_view:_on_loading_started:620 - 加载中: 正在加载任务列表...
2025-06-04 14:33:06.402 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-04 14:33:06.409 | INFO     | app.services.proxy_service:refresh_proxies:712 - 刷新代理列表: offset=0, limit=20
2025-06-04 14:33:06.409 | INFO     | core.proxy.proxy_task_manager:submit_task:172 - 提交耗时任务到子线程: refresh_proxies_0_20
2025-06-04 14:33:06.410 | DEBUG    | app.services.proxy_service:get_proxies:500 - 获取代理列表: offset=0, limit=20
2025-06-04 14:33:06.410 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-04 14:33:06.410 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-04 14:33:06.413 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-04 14:33:06.434 | INFO     | app.controllers.invite_controller:get_invite_tasks:38 - 获取邀请任务列表, status=None
2025-06-04 14:33:06.439 | INFO     | app.services.invite_service:get_invite_tasks:121 - 获取邀请任务列表, status=None
2025-06-04 14:33:06.440 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-04 14:33:06.441 | INFO     | app.services.invite_service:task_initialize:39 - 初始化邀请服务，重置运行中的任务为暂停状态
2025-06-04 14:33:06.444 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-04 14:33:06.445 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-04 14:33:06.447 | INFO     | core.proxy.proxy_task_manager:task_wrapper:199 - 任务 refresh_proxies_0_20 完成
2025-06-04 14:33:06.655 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-04 14:33:06.655 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-04 14:33:06.658 | DEBUG    | ui.views.monitor_view:_refresh_task_list:175 - 刷新任务列表，共2个任务
2025-06-04 14:33:06.663 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-04 14:33:06.665 | INFO     | app.services.invite_service:task_initialize:48 - 成功重置 0 个任务状态
2025-06-04 14:33:06.666 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-04 14:33:06.671 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-04 14:33:06.672 | INFO     | app.services.account_service:get_all_groups:96 - 获取所有账户分组成功, 共 1 个
2025-06-04 14:33:06.673 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-04 14:33:06.676 | DEBUG    | ui.views.account_view:_on_groups_loaded:442 - 分组加载完成: 1个分组
2025-06-04 14:33:06.683 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-04 14:33:06.686 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-04 14:33:06.687 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-04 14:33:06.750 | INFO     | app.services.account_service:get_all_accounts:289 - 获取所有账户成功, 共 0 个
2025-06-04 14:33:06.751 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-04 14:33:06.754 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-04 14:33:06.755 | DEBUG    | ui.views.account_view:_on_accounts_loaded:575 - 账户加载完成: 0个账户
2025-06-04 14:33:06.755 | DEBUG    | ui.views.account_view:_update_group_counts:184 - 分组计数已更新: {}
2025-06-04 14:33:06.756 | INFO     | ui.views.account_view:_auto_login_accounts:595 - 自动登录条件不满足：无控制器或无账户。
2025-06-04 14:33:06.760 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-04 14:33:06.762 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-04 14:33:06.764 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-04 14:33:06.785 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-04 14:33:06.791 | INFO     | app.controllers.telegram_monitor_controller:_load_total_users_count_internal:191 - 成功加载总体统计数据: 总用户数 958, 今日采集 0, 日均采集 68, 运行天数 14
2025-06-04 14:33:06.792 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:156 - 成功加载 2 个任务
2025-06-04 14:33:06.792 | DEBUG    | ui.views.monitor_view:_on_loading_finished:625 - 加载完成
2025-06-04 14:33:06.794 | INFO     | app.services.account_service:get_all_accounts:289 - 获取所有账户成功, 共 0 个
2025-06-04 14:33:06.794 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-04 14:33:06.796 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-06-04 14:33:06.813 | DEBUG    | ui.views.account_view:_update_group_counts:184 - 分组计数已更新: {}
2025-06-04 14:33:06.813 | DEBUG    | ui.views.account_view:_init_cache:151 - 账户缓存初始化完成，共 0 个账户
2025-06-04 14:33:06.819 | DEBUG    | ui.views.account_view:_on_accounts_loaded:575 - 账户加载完成: 0个账户
2025-06-04 14:33:06.820 | DEBUG    | ui.views.account_view:_update_group_counts:184 - 分组计数已更新: {}
2025-06-04 14:33:06.820 | INFO     | ui.views.account_view:_auto_login_accounts:595 - 自动登录条件不满足：无控制器或无账户。
2025-06-04 14:33:06.839 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-04 14:33:06.860 | WARNING  | core.auth.api_service:verify_vip:441 - 会员验证失败: 验证失败
2025-06-04 14:33:07.131 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-06-04 14:33:07.134 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-06-04 14:33:07.136 | INFO     | ui.views.proxy_view:_load_initial_data:1044 - 初始数据加载完成，共 3 个代理
2025-06-04 14:33:09.806 | INFO     | ui.main_window:closeEvent:335 - MainWindow: 接收到关闭事件
2025-06-04 14:33:09.821 | INFO     | ui.main_window:_cleanup_before_quit:228 - MainWindow: 执行清理资源...
2025-06-04 14:33:09.824 | INFO     | core.telegram.client_worker:stop:110 - 正在停止Telegram客户端工作线程
2025-06-04 14:33:09.833 | INFO     | core.telegram.client_manager:cleanup_async:1140 - 开始清理资源并断开所有连接
2025-06-04 14:33:09.833 | INFO     | core.telegram.client_manager:disconnect_all_clients:725 - 正在断开所有客户端连接
2025-06-04 14:33:09.834 | INFO     | core.telegram.client_manager:cleanup_async:1159 - 资源清理完成
2025-06-04 14:33:10.342 | INFO     | core.telegram.client_manager:cleanup_async:1140 - 开始清理资源并断开所有连接
2025-06-04 14:33:10.342 | INFO     | core.telegram.client_manager:disconnect_all_clients:725 - 正在断开所有客户端连接
2025-06-04 14:33:10.342 | INFO     | core.telegram.client_manager:cleanup_async:1159 - 资源清理完成
2025-06-04 14:33:10.854 | INFO     | core.telegram.client_worker:run:99 - Telegram客户端工作线程已退出
2025-06-04 14:33:10.855 | INFO     | core.telegram.client_worker:stop:124 - Telegram客户端工作线程已停止
2025-06-04 14:33:10.855 | INFO     | ui.main_window:_cleanup_before_quit:237 - TelegramClientWorker 已停止。
2025-06-04 14:33:10.856 | INFO     | ui.main_window:_cleanup_before_quit:241 - MainWindow 清理完成
2025-06-04 14:33:10.872 | INFO     | __main__:main:111 - 应用程序已正常退出
