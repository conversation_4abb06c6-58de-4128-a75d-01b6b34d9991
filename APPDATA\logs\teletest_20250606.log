2025-06-06 19:48:23.541 | INFO     | utils.logger:init_logger:98 - 日志系统初始化完成，默认级别: DEBUG，日志目录: h:\PyProject\TeleTest\APPDATA\logs
2025-06-06 19:48:27.101 | INFO     | data.database:init_database:31 - 初始化数据库连接: h:\PyProject\TeleTest\APPDATA\database\telegram_manager.db
2025-06-06 19:48:27.179 | DEBUG    | data.database:init_database:58 - 创建数据库表
2025-06-06 19:48:27.191 | INFO     | data.database:init_database:61 - 数据库初始化完成
2025-06-06 19:48:28.845 | DEBUG    | app.controllers.auth_controller:initialize_software:43 - 控制器: 初始化软件 app_id=1000, version=1.0
2025-06-06 19:48:28.846 | DEBUG    | utils.client_http:get:54 - 发送GET请求: https://server.xile188.com/api/user/1000/windowns10/1.0/ini
2025-06-06 19:48:29.404 | DEBUG    | core.auth.api_service:init_software:66 - 初始化数据解密成功: {'new_content': '1.多账户管理\n2.ip池管理\n3.消息监听\n4.消息群发\n5.拉人入群', 'new_url': None, 'notice': {'id': '2', 'content': '公告内容测试3', 'time': '**********'}}
2025-06-06 20:08:00.898 | INFO     | app.controllers.auth_controller:register:104 - 控制器: 用户注册 account=demo01
2025-06-06 20:08:04.001 | INFO     | core.auth.api_service:register:220 - 用户注册: account=demo01, invid=, code=
2025-06-06 20:08:04.001 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/reg
2025-06-06 20:08:04.545 | INFO     | core.auth.api_service:register:255 - 注册结果: 注册成功
2025-06-06 20:09:34.226 | INFO     | app.controllers.auth_controller:login:61 - 控制器: 用户登录 account=demo01
2025-06-06 20:09:34.226 | INFO     | core.auth.api_service:login:127 - 用户登录: account=demo01
2025-06-06 20:09:34.227 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/logon
2025-06-06 20:09:34.529 | INFO     | core.auth.api_service:get_user_info:347 - 获取用户信息
2025-06-06 20:09:34.529 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/info
2025-06-06 20:09:34.734 | INFO     | core.auth.api_service:get_user_info:375 - 获取用户信息成功
2025-06-06 20:09:34.739 | INFO     | __main__:on_auth_completed:93 - 用户登录成功，正在启动主窗口...
2025-06-06 20:09:34.761 | INFO     | core.telegram.client_worker:__init__:63 - Telegram客户端工作线程初始化
2025-06-06 20:09:34.763 | INFO     | core.telegram.client_worker:run:67 - Telegram客户端工作线程开始运行
2025-06-06 20:09:34.769 | INFO     | core.telegram.client_manager:__init__:66 - Telegram客户端管理器初始化
2025-06-06 20:09:34.769 | INFO     | core.telegram.user_manager:__init__:31 - Telegram用户管理器初始化
2025-06-06 20:09:34.770 | INFO     | core.telegram.group_manager:__init__:36 - Telegram群组管理器初始化
2025-06-06 20:09:34.771 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-06-06 20:09:34.771 | INFO     | core.telegram.monitor_manager:__init__:26 - Telegram监控管理器初始化
2025-06-06 20:09:34.771 | INFO     | core.telegram.telegram_service:__init__:39 - Telegram服务初始化完成
2025-06-06 20:09:34.773 | INFO     | ui.main_window:_initialize_core_components:79 - MainWindow: 初始化核心组件...
2025-06-06 20:09:34.774 | INFO     | app.services.account_service:__init__:42 - 账户服务初始化
2025-06-06 20:09:34.774 | INFO     | core.proxy.proxy_task_manager:run:284 - 代理任务管理器线程启动
2025-06-06 20:09:34.774 | INFO     | app.controllers.account_controller:__init__:81 - 账户控制器初始化
2025-06-06 20:09:34.775 | INFO     | app.services.monitor_service:__init__:39 - 初始化监控任务服务
2025-06-06 20:09:34.775 | INFO     | core.monitor.filter:__init__:26 - 初始化消息过滤器
2025-06-06 20:09:34.776 | INFO     | core.monitor.extractor:__init__:24 - 初始化关键词提取器
2025-06-06 20:09:34.776 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-06-06 20:09:34.777 | INFO     | core.monitor.notifier:__init__:90 - 初始化通知管理器
2025-06-06 20:09:34.777 | INFO     | app.controllers.telegram_monitor_controller:__init__:64 - 初始化 Telegram 监控控制器
2025-06-06 20:09:34.777 | DEBUG    | app.controllers.telegram_monitor_controller:__init__:82 - 控制器初始化完成
2025-06-06 20:09:34.977 | INFO     | ui.views.monitor_view:__init__:48 - 初始化监控视图
2025-06-06 20:09:34.977 | DEBUG    | ui.views.monitor_view:_connect_signals:80 - 连接控制器信号
2025-06-06 20:09:35.154 | INFO     | ui.views.proxy_view:__init__:96 - 初始化视图，加载第一页代理数据
2025-06-06 20:09:35.362 | INFO     | __main__:on_auth_completed:97 - 主窗口已启动
2025-06-06 20:09:35.433 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:88 - 尝试连接监听日志信号...
2025-06-06 20:09:35.434 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:90 - 监听日志信号连接成功
2025-06-06 20:09:35.435 | INFO     | core.auth.api_service:verify_vip:425 - 会员验证接口调用
2025-06-06 20:09:35.435 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/vip
2025-06-06 20:09:35.436 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-06 20:09:35.441 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-06 20:09:35.445 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:145 - 加载监控任务列表 (搜索关键词: '')
2025-06-06 20:09:35.445 | DEBUG    | ui.views.monitor_view:_on_loading_started:620 - 加载中: 正在加载任务列表...
2025-06-06 20:09:35.446 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-06 20:09:35.455 | INFO     | app.services.proxy_service:refresh_proxies:712 - 刷新代理列表: offset=0, limit=20
2025-06-06 20:09:35.455 | INFO     | core.proxy.proxy_task_manager:submit_task:172 - 提交耗时任务到子线程: refresh_proxies_0_20
2025-06-06 20:09:35.456 | DEBUG    | app.services.proxy_service:get_proxies:500 - 获取代理列表: offset=0, limit=20
2025-06-06 20:09:35.456 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-06 20:09:35.457 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-06 20:09:35.468 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-06 20:09:35.489 | INFO     | app.controllers.invite_controller:get_invite_tasks:38 - 获取邀请任务列表, status=None
2025-06-06 20:09:35.491 | INFO     | app.services.invite_service:get_invite_tasks:121 - 获取邀请任务列表, status=None
2025-06-06 20:09:35.491 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-06 20:09:35.492 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-06 20:09:35.493 | INFO     | app.services.invite_service:task_initialize:39 - 初始化邀请服务，重置运行中的任务为暂停状态
2025-06-06 20:09:35.493 | INFO     | core.proxy.proxy_task_manager:task_wrapper:199 - 任务 refresh_proxies_0_20 完成
2025-06-06 20:09:35.493 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-06 20:09:35.618 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-06 20:09:35.621 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-06 20:09:35.628 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-06 20:09:35.628 | INFO     | app.services.invite_service:task_initialize:48 - 成功重置 0 个任务状态
2025-06-06 20:09:35.629 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-06 20:09:35.630 | DEBUG    | ui.views.monitor_view:_refresh_task_list:175 - 刷新任务列表，共2个任务
2025-06-06 20:09:35.636 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-06 20:09:35.644 | INFO     | app.services.account_service:get_all_groups:96 - 获取所有账户分组成功, 共 1 个
2025-06-06 20:09:35.645 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-06 20:09:35.645 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-06 20:09:35.650 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-06 20:09:35.651 | DEBUG    | ui.views.account_view:_on_groups_loaded:442 - 分组加载完成: 1个分组
2025-06-06 20:09:35.658 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-06 20:09:35.725 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-06 20:09:35.726 | INFO     | app.services.account_service:get_all_accounts:289 - 获取所有账户成功, 共 0 个
2025-06-06 20:09:35.726 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-06 20:09:35.731 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-06 20:09:35.733 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-06-06 20:09:35.738 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-06 20:09:35.749 | DEBUG    | ui.views.account_view:_on_accounts_loaded:575 - 账户加载完成: 0个账户
2025-06-06 20:09:35.750 | DEBUG    | ui.views.account_view:_update_group_counts:184 - 分组计数已更新: {}
2025-06-06 20:09:35.750 | INFO     | ui.views.account_view:_auto_login_accounts:595 - 自动登录条件不满足：无控制器或无账户。
2025-06-06 20:09:35.758 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-06 20:09:35.760 | INFO     | app.controllers.telegram_monitor_controller:_load_total_users_count_internal:191 - 成功加载总体统计数据: 总用户数 958, 今日采集 0, 日均采集 60, 运行天数 16
2025-06-06 20:09:35.760 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:156 - 成功加载 2 个任务
2025-06-06 20:09:35.760 | DEBUG    | ui.views.monitor_view:_on_loading_finished:625 - 加载完成
2025-06-06 20:09:35.775 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-06 20:09:35.779 | INFO     | app.services.account_service:get_all_accounts:289 - 获取所有账户成功, 共 0 个
2025-06-06 20:09:35.780 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-06 20:09:35.783 | DEBUG    | ui.views.account_view:_update_group_counts:184 - 分组计数已更新: {}
2025-06-06 20:09:35.784 | DEBUG    | ui.views.account_view:_init_cache:151 - 账户缓存初始化完成，共 0 个账户
2025-06-06 20:09:35.789 | DEBUG    | ui.views.account_view:_on_accounts_loaded:575 - 账户加载完成: 0个账户
2025-06-06 20:09:35.790 | DEBUG    | ui.views.account_view:_update_group_counts:184 - 分组计数已更新: {}
2025-06-06 20:09:35.790 | INFO     | ui.views.account_view:_auto_login_accounts:595 - 自动登录条件不满足：无控制器或无账户。
2025-06-06 20:09:35.796 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=True
2025-06-06 20:09:35.799 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=True
2025-06-06 20:09:35.802 | INFO     | ui.views.proxy_view:_load_initial_data:1044 - 初始数据加载完成，共 3 个代理
2025-06-06 20:09:35.808 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-06 20:09:35.855 | INFO     | core.auth.api_service:verify_vip:439 - 会员验证成功
2025-06-06 20:10:34.726 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-06 20:10:34.726 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-06 20:11:34.724 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-06 20:11:34.724 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-06 20:12:34.727 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-06 20:12:34.728 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-06 20:13:34.724 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-06 20:13:34.725 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-06 20:14:34.727 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-06 20:14:34.727 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-06 20:15:34.726 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-06 20:15:34.726 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-06 20:16:34.728 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-06 20:16:34.729 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-06 20:17:34.731 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-06 20:17:34.731 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-06 20:18:34.727 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-06 20:18:34.727 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-06 20:19:34.725 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-06 20:19:34.725 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-06 20:20:34.733 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-06 20:20:34.734 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-06 20:21:34.726 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-06 20:21:34.727 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-06 20:22:34.733 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-06 20:22:34.733 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-06 20:23:34.726 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-06 20:23:34.727 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-06 20:24:34.733 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-06 20:24:34.734 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-06 20:25:34.728 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-06 20:25:34.728 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-06 20:26:34.726 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-06 20:26:34.727 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-06 20:27:34.725 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-06 20:27:34.725 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-06 20:28:34.728 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-06 20:28:34.728 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-06 20:29:34.725 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-06 20:29:34.726 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-06 20:30:34.729 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-06 20:30:34.729 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-06 20:31:34.728 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-06 20:31:34.729 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-06 20:32:34.725 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-06 20:32:34.726 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-06 20:33:34.726 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-06 20:33:34.726 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-06 20:34:34.729 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-06 20:34:34.730 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-06 20:35:34.724 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-06 20:35:34.724 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-06 20:36:34.725 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-06 20:36:34.725 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-06 20:37:34.728 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-06 20:37:34.729 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-06 20:38:34.724 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-06 20:38:34.725 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-06 20:39:34.726 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-06 20:39:34.726 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-06 20:40:34.737 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-06 20:40:34.737 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-06 20:41:34.726 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-06 20:41:34.726 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-06 20:42:34.724 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-06 20:42:34.725 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-06 20:43:34.728 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-06 20:43:34.728 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-06 20:44:34.726 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-06 20:44:34.726 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-06 20:45:34.726 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-06 20:45:34.726 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-06 20:46:34.727 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-06 20:46:34.728 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-06 20:47:34.732 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-06 20:47:34.733 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-06 20:48:34.733 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-06 20:48:34.734 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-06 20:49:34.732 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-06 20:49:34.733 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-06 20:50:34.725 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-06 20:50:34.726 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-06 20:51:34.725 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-06 20:51:34.726 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-06 20:52:34.727 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-06 20:52:34.727 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-06 20:53:34.730 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-06 20:53:34.731 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-06 20:54:34.729 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-06 20:54:34.730 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-06 20:55:34.728 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-06 20:55:34.729 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-06 20:56:34.727 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-06 20:56:34.727 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-06 20:57:34.729 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-06 20:57:34.730 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-06 20:58:34.731 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-06 20:58:34.731 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-06 20:59:34.730 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-06 20:59:34.731 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-06 21:00:34.725 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-06 21:00:34.725 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-06 21:01:34.731 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-06 21:01:34.731 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-06 21:02:34.731 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-06 21:02:34.731 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-06 21:03:34.732 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-06 21:03:34.733 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-06 21:04:34.730 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-06 21:04:34.730 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-06 21:05:34.730 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-06 21:05:34.730 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-06 21:06:34.728 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-06 21:06:34.728 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-06 21:07:34.730 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-06 21:07:34.731 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-06 21:08:34.726 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-06 21:08:34.726 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-06 21:09:34.734 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-06 21:09:34.734 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-06 21:10:34.729 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-06 21:10:34.730 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-06 21:11:34.729 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-06 21:11:34.729 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-06 21:12:34.724 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-06 21:12:34.725 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-06 21:13:34.731 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-06 21:13:34.732 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-06 21:14:34.729 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-06 21:14:34.730 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-06 21:15:34.726 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-06 21:15:34.727 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-06 21:16:34.725 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-06 21:16:34.726 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-06 21:17:34.733 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-06 21:17:34.733 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-06 21:18:34.729 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-06 21:18:34.730 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-06 21:19:34.724 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-06 21:19:34.725 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-06 21:20:34.726 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-06 21:20:34.727 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-06 21:21:34.730 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-06 21:21:34.730 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-06 21:22:34.725 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-06 21:22:34.725 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-06 21:23:34.725 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-06 21:23:34.726 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-06 21:24:34.727 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-06 21:24:34.728 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-06 21:25:34.728 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-06 21:25:34.729 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-06 21:26:34.724 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-06 21:26:34.727 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-06 21:27:34.727 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-06 21:27:34.727 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-06 21:28:34.725 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-06 21:28:34.725 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-06 21:29:34.724 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-06 21:29:34.725 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-06 21:30:34.724 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-06 21:30:34.724 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-06 21:31:34.727 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-06 21:31:34.728 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-06 21:32:34.725 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-06 21:32:34.726 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-06 21:33:34.726 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-06 21:33:34.726 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-06 21:34:34.729 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-06 21:34:34.729 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-06 21:35:34.735 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-06 21:35:34.735 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-06 21:36:34.730 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-06 21:36:34.731 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-06 21:37:34.728 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-06 21:37:34.729 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-06 21:38:34.726 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-06 21:38:34.727 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-06 21:39:34.728 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-06 21:39:34.729 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-06 21:40:34.728 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-06 21:40:34.728 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-06 21:41:34.732 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-06 21:41:34.732 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-06 21:42:34.726 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-06 21:42:34.726 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-06 21:43:34.726 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-06 21:43:34.727 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-06 21:44:34.731 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-06 21:44:34.731 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-06 21:45:34.730 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-06 21:45:34.731 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-06 21:46:34.727 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-06 21:46:34.728 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-06 21:47:34.731 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-06 21:47:34.732 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-06 21:48:34.726 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-06 21:48:34.727 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-06 21:49:34.733 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-06 21:49:34.733 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-06 21:50:34.728 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-06 21:50:34.728 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-06 21:51:34.725 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-06 21:51:34.725 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-06 21:52:34.725 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-06 21:52:34.726 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-06 21:53:34.724 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-06 21:53:34.725 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-06 21:54:34.725 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-06 21:54:34.725 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-06 21:55:34.725 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-06 21:55:34.726 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-06 21:56:34.728 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-06 21:56:34.728 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-06 21:57:34.725 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-06 21:57:34.725 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-06 21:58:03.857 | INFO     | ui.views.proxy_view:_on_add_proxy:451 - 用户开始添加代理: IP类型=socks5(本地), IP文本='***********
*********-10
*********-*********3'
2025-06-06 21:58:03.858 | DEBUG    | ui.views.proxy_view:_on_add_proxy:463 - 端口范围: 10000-20000
2025-06-06 21:58:03.858 | DEBUG    | ui.views.proxy_view:_on_add_proxy:473 - 用户凭据: username=, password=None
2025-06-06 21:58:03.858 | INFO     | app.services.proxy_service:add_proxies_from_text:879 - 从文本添加代理: IP文本='***********
*********-10
*********-*********3', 端口范围=10000-20000
2025-06-06 21:58:03.859 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-06 21:58:03.979 | INFO     | core.proxy.proxy_core_service:generate_proxy_config:249 - 生成代理配置文件
2025-06-06 21:58:03.979 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-06 21:58:03.984 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-06 21:58:03.986 | INFO     | app.services.proxy_service:_handle_local_proxy_service:820 - 服务正在运行，重启服务以应用新配置
2025-06-06 21:58:03.986 | INFO     | app.services.proxy_service:restart_service:615 - 重启代理服务
2025-06-06 21:58:03.987 | INFO     | core.proxy.proxy_task_manager:submit_task:172 - 提交耗时任务到子线程: restart_service
2025-06-06 21:58:03.987 | INFO     | core.proxy.proxy_core_service:generate_proxy_config:249 - 生成代理配置文件
2025-06-06 21:58:03.987 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-06 21:58:03.991 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-06 21:58:03.993 | INFO     | core.proxy.proxy_core_service:restart_proxy_service:341 - 重启代理服务
2025-06-06 21:58:03.993 | INFO     | core.proxy.proxy_core_service:generate_proxy_config:249 - 生成代理配置文件
2025-06-06 21:58:03.993 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-06 21:58:03.997 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-06 21:58:07.789 | INFO     | core.proxy.proxy_core_service:restart_proxy_service:352 - 代理服务重启成功: 服务配置热重载成功
2025-06-06 21:58:07.789 | INFO     | core.proxy.proxy_task_manager:task_wrapper:199 - 任务 restart_service 完成
2025-06-06 21:58:07.790 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-06 21:58:07.790 | INFO     | app.services.proxy_service:refresh_proxies:712 - 刷新代理列表: offset=0, limit=20
2025-06-06 21:58:07.791 | INFO     | core.proxy.proxy_task_manager:submit_task:172 - 提交耗时任务到子线程: refresh_proxies_0_20
2025-06-06 21:58:07.791 | DEBUG    | app.services.proxy_service:get_proxies:500 - 获取代理列表: offset=0, limit=20
2025-06-06 21:58:07.791 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-06 21:58:07.791 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-06 21:58:07.796 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-06 21:58:07.798 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-06 21:58:07.798 | INFO     | core.proxy.proxy_task_manager:task_wrapper:199 - 任务 refresh_proxies_0_20 完成
2025-06-06 21:58:07.799 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-06 21:58:07.801 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-06 21:58:07.804 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=True
2025-06-06 21:58:07.809 | INFO     | app.services.proxy_service:validate_proxies:756 - 开始验证指定代理，共 24 个
2025-06-06 21:58:07.810 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-06 21:58:07.914 | INFO     | core.proxy.proxy_core_service:validate_proxies_stream:167 - 批量验证代理(流式): 24个
2025-06-06 21:58:07.915 | INFO     | core.proxy.proxy_validator:validate_proxy:43 - 正在验证代理 socks5://***********:10000
2025-06-06 21:58:13.276 | WARNING  | core.proxy.proxy_validator:validate_proxy:71 - 使用https://web.telegram.org验证代理失败: socks5://***********:10000, 错误: 
2025-06-06 21:58:18.275 | WARNING  | core.proxy.proxy_validator:validate_proxy:71 - 使用https://core.telegram.org验证代理失败: socks5://***********:10000, 错误: 
2025-06-06 21:58:18.276 | WARNING  | core.proxy.proxy_validator:validate_proxy:75 - 代理验证失败: socks5://***********:10000
2025-06-06 21:58:18.277 | INFO     | core.proxy.proxy_validator:validate_proxy:43 - 正在验证代理 socks5://*********:10001
2025-06-06 21:58:19.328 | INFO     | ui.views.proxy_view:load_proxies_by_page:525 - 加载代理页面: 2
2025-06-06 21:58:19.328 | INFO     | app.services.proxy_service:refresh_proxies:712 - 刷新代理列表: offset=20, limit=20
2025-06-06 21:58:19.328 | INFO     | core.proxy.proxy_task_manager:submit_task:172 - 提交耗时任务到子线程: refresh_proxies_20_20
2025-06-06 21:58:19.329 | DEBUG    | app.services.proxy_service:get_proxies:500 - 获取代理列表: offset=20, limit=20
2025-06-06 21:58:19.329 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-06 21:58:19.329 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-06 21:58:19.334 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-06 21:58:19.336 | INFO     | core.proxy.proxy_task_manager:task_wrapper:199 - 任务 refresh_proxies_20_20 完成
2025-06-06 21:58:19.336 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-06 21:58:19.337 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-06 21:58:19.340 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-06 21:58:19.343 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=True
2025-06-06 21:58:19.347 | INFO     | ui.views.proxy_view:load_proxies_by_page:538 - 页面 2 加载完成，显示 7 条记录，共 27 条
2025-06-06 21:58:23.272 | WARNING  | core.proxy.proxy_validator:validate_proxy:71 - 使用https://web.telegram.org验证代理失败: socks5://*********:10001, 错误: 
2025-06-06 21:58:28.271 | WARNING  | core.proxy.proxy_validator:validate_proxy:71 - 使用https://core.telegram.org验证代理失败: socks5://*********:10001, 错误: 
2025-06-06 21:58:28.272 | WARNING  | core.proxy.proxy_validator:validate_proxy:75 - 代理验证失败: socks5://*********:10001
2025-06-06 21:58:28.272 | INFO     | core.proxy.proxy_validator:validate_proxy:43 - 正在验证代理 socks5://172.1.1.2:10002
2025-06-06 21:58:33.275 | WARNING  | core.proxy.proxy_validator:validate_proxy:71 - 使用https://web.telegram.org验证代理失败: socks5://172.1.1.2:10002, 错误: 
2025-06-06 21:58:34.724 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-06 21:58:34.725 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-06 21:58:38.275 | WARNING  | core.proxy.proxy_validator:validate_proxy:71 - 使用https://core.telegram.org验证代理失败: socks5://172.1.1.2:10002, 错误: 
2025-06-06 21:58:38.276 | WARNING  | core.proxy.proxy_validator:validate_proxy:75 - 代理验证失败: socks5://172.1.1.2:10002
2025-06-06 21:58:38.277 | INFO     | core.proxy.proxy_validator:validate_proxy:43 - 正在验证代理 socks5://172.1.1.3:10003
2025-06-06 21:58:43.273 | WARNING  | core.proxy.proxy_validator:validate_proxy:71 - 使用https://web.telegram.org验证代理失败: socks5://172.1.1.3:10003, 错误: 
2025-06-06 21:58:48.273 | WARNING  | core.proxy.proxy_validator:validate_proxy:71 - 使用https://core.telegram.org验证代理失败: socks5://172.1.1.3:10003, 错误: 
2025-06-06 21:58:48.273 | WARNING  | core.proxy.proxy_validator:validate_proxy:75 - 代理验证失败: socks5://172.1.1.3:10003
2025-06-06 21:58:48.274 | INFO     | core.proxy.proxy_validator:validate_proxy:43 - 正在验证代理 socks5://172.1.1.4:10004
2025-06-06 21:58:53.271 | WARNING  | core.proxy.proxy_validator:validate_proxy:71 - 使用https://web.telegram.org验证代理失败: socks5://172.1.1.4:10004, 错误: 
2025-06-06 21:58:58.272 | WARNING  | core.proxy.proxy_validator:validate_proxy:71 - 使用https://core.telegram.org验证代理失败: socks5://172.1.1.4:10004, 错误: 
2025-06-06 21:58:58.272 | WARNING  | core.proxy.proxy_validator:validate_proxy:75 - 代理验证失败: socks5://172.1.1.4:10004
2025-06-06 21:58:58.273 | INFO     | core.proxy.proxy_validator:validate_proxy:43 - 正在验证代理 socks5://172.1.1.5:10005
2025-06-06 21:59:03.274 | WARNING  | core.proxy.proxy_validator:validate_proxy:71 - 使用https://web.telegram.org验证代理失败: socks5://172.1.1.5:10005, 错误: 
2025-06-06 21:59:08.274 | WARNING  | core.proxy.proxy_validator:validate_proxy:71 - 使用https://core.telegram.org验证代理失败: socks5://172.1.1.5:10005, 错误: 
2025-06-06 21:59:08.275 | WARNING  | core.proxy.proxy_validator:validate_proxy:75 - 代理验证失败: socks5://172.1.1.5:10005
2025-06-06 21:59:08.275 | INFO     | core.proxy.proxy_validator:validate_proxy:43 - 正在验证代理 socks5://172.1.1.6:10006
2025-06-06 21:59:13.272 | WARNING  | core.proxy.proxy_validator:validate_proxy:71 - 使用https://web.telegram.org验证代理失败: socks5://172.1.1.6:10006, 错误: 
2025-06-06 21:59:18.271 | WARNING  | core.proxy.proxy_validator:validate_proxy:71 - 使用https://core.telegram.org验证代理失败: socks5://172.1.1.6:10006, 错误: 
2025-06-06 21:59:18.271 | WARNING  | core.proxy.proxy_validator:validate_proxy:75 - 代理验证失败: socks5://172.1.1.6:10006
2025-06-06 21:59:18.272 | INFO     | core.proxy.proxy_validator:validate_proxy:43 - 正在验证代理 socks5://172.1.1.7:10007
2025-06-06 21:59:23.272 | WARNING  | core.proxy.proxy_validator:validate_proxy:71 - 使用https://web.telegram.org验证代理失败: socks5://172.1.1.7:10007, 错误: 
2025-06-06 21:59:28.271 | WARNING  | core.proxy.proxy_validator:validate_proxy:71 - 使用https://core.telegram.org验证代理失败: socks5://172.1.1.7:10007, 错误: 
2025-06-06 21:59:28.272 | WARNING  | core.proxy.proxy_validator:validate_proxy:75 - 代理验证失败: socks5://172.1.1.7:10007
2025-06-06 21:59:28.273 | INFO     | core.proxy.proxy_validator:validate_proxy:43 - 正在验证代理 socks5://172.1.1.8:10008
2025-06-06 21:59:33.272 | WARNING  | core.proxy.proxy_validator:validate_proxy:71 - 使用https://web.telegram.org验证代理失败: socks5://172.1.1.8:10008, 错误: 
2025-06-06 21:59:34.728 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-06 21:59:34.728 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-06 21:59:38.273 | WARNING  | core.proxy.proxy_validator:validate_proxy:71 - 使用https://core.telegram.org验证代理失败: socks5://172.1.1.8:10008, 错误: 
2025-06-06 21:59:38.274 | WARNING  | core.proxy.proxy_validator:validate_proxy:75 - 代理验证失败: socks5://172.1.1.8:10008
2025-06-06 21:59:38.275 | INFO     | core.proxy.proxy_validator:validate_proxy:43 - 正在验证代理 socks5://172.1.1.9:10009
2025-06-06 21:59:43.271 | WARNING  | core.proxy.proxy_validator:validate_proxy:71 - 使用https://web.telegram.org验证代理失败: socks5://172.1.1.9:10009, 错误: 
2025-06-06 21:59:48.276 | WARNING  | core.proxy.proxy_validator:validate_proxy:71 - 使用https://core.telegram.org验证代理失败: socks5://172.1.1.9:10009, 错误: 
2025-06-06 21:59:48.277 | WARNING  | core.proxy.proxy_validator:validate_proxy:75 - 代理验证失败: socks5://172.1.1.9:10009
2025-06-06 21:59:48.787 | INFO     | core.proxy.proxy_validator:validate_proxy:43 - 正在验证代理 socks5://*********0:10010
2025-06-06 21:59:54.272 | WARNING  | core.proxy.proxy_validator:validate_proxy:71 - 使用https://web.telegram.org验证代理失败: socks5://*********0:10010, 错误: 
2025-06-06 21:59:59.275 | WARNING  | core.proxy.proxy_validator:validate_proxy:71 - 使用https://core.telegram.org验证代理失败: socks5://*********0:10010, 错误: 
2025-06-06 21:59:59.276 | WARNING  | core.proxy.proxy_validator:validate_proxy:75 - 代理验证失败: socks5://*********0:10010
2025-06-06 21:59:59.276 | INFO     | core.proxy.proxy_validator:validate_proxy:43 - 正在验证代理 socks5://*********:10011
2025-06-06 22:00:04.272 | WARNING  | core.proxy.proxy_validator:validate_proxy:71 - 使用https://web.telegram.org验证代理失败: socks5://*********:10011, 错误: 
2025-06-06 22:00:09.273 | WARNING  | core.proxy.proxy_validator:validate_proxy:71 - 使用https://core.telegram.org验证代理失败: socks5://*********:10011, 错误: 
2025-06-06 22:00:09.274 | WARNING  | core.proxy.proxy_validator:validate_proxy:75 - 代理验证失败: socks5://*********:10011
2025-06-06 22:00:09.274 | INFO     | core.proxy.proxy_validator:validate_proxy:43 - 正在验证代理 socks5://193.1.1.2:10012
2025-06-06 22:00:14.276 | WARNING  | core.proxy.proxy_validator:validate_proxy:71 - 使用https://web.telegram.org验证代理失败: socks5://193.1.1.2:10012, 错误: 
2025-06-06 22:00:19.278 | WARNING  | core.proxy.proxy_validator:validate_proxy:71 - 使用https://core.telegram.org验证代理失败: socks5://193.1.1.2:10012, 错误: 
2025-06-06 22:00:19.279 | WARNING  | core.proxy.proxy_validator:validate_proxy:75 - 代理验证失败: socks5://193.1.1.2:10012
2025-06-06 22:00:19.279 | INFO     | core.proxy.proxy_validator:validate_proxy:43 - 正在验证代理 socks5://193.1.1.3:10013
2025-06-06 22:00:24.275 | WARNING  | core.proxy.proxy_validator:validate_proxy:71 - 使用https://web.telegram.org验证代理失败: socks5://193.1.1.3:10013, 错误: 
2025-06-06 22:00:29.275 | WARNING  | core.proxy.proxy_validator:validate_proxy:71 - 使用https://core.telegram.org验证代理失败: socks5://193.1.1.3:10013, 错误: 
2025-06-06 22:00:29.276 | WARNING  | core.proxy.proxy_validator:validate_proxy:75 - 代理验证失败: socks5://193.1.1.3:10013
2025-06-06 22:00:29.276 | INFO     | core.proxy.proxy_validator:validate_proxy:43 - 正在验证代理 socks5://193.1.1.4:10014
2025-06-06 22:00:34.278 | WARNING  | core.proxy.proxy_validator:validate_proxy:71 - 使用https://web.telegram.org验证代理失败: socks5://193.1.1.4:10014, 错误: 
2025-06-06 22:00:34.725 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-06 22:00:34.725 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-06 22:00:39.272 | WARNING  | core.proxy.proxy_validator:validate_proxy:71 - 使用https://core.telegram.org验证代理失败: socks5://193.1.1.4:10014, 错误: 
2025-06-06 22:00:39.272 | WARNING  | core.proxy.proxy_validator:validate_proxy:75 - 代理验证失败: socks5://193.1.1.4:10014
2025-06-06 22:00:39.273 | INFO     | core.proxy.proxy_validator:validate_proxy:43 - 正在验证代理 socks5://193.1.1.5:10015
2025-06-06 22:00:44.275 | WARNING  | core.proxy.proxy_validator:validate_proxy:71 - 使用https://web.telegram.org验证代理失败: socks5://193.1.1.5:10015, 错误: 
2025-06-06 22:00:49.273 | WARNING  | core.proxy.proxy_validator:validate_proxy:71 - 使用https://core.telegram.org验证代理失败: socks5://193.1.1.5:10015, 错误: 
2025-06-06 22:00:49.273 | WARNING  | core.proxy.proxy_validator:validate_proxy:75 - 代理验证失败: socks5://193.1.1.5:10015
2025-06-06 22:00:49.274 | INFO     | core.proxy.proxy_validator:validate_proxy:43 - 正在验证代理 socks5://193.1.1.6:10016
2025-06-06 22:00:51.330 | INFO     | ui.views.proxy_view:load_proxies_by_page:525 - 加载代理页面: 1
2025-06-06 22:00:51.331 | INFO     | app.services.proxy_service:refresh_proxies:712 - 刷新代理列表: offset=0, limit=20
2025-06-06 22:00:51.331 | INFO     | core.proxy.proxy_task_manager:submit_task:172 - 提交耗时任务到子线程: refresh_proxies_0_20
2025-06-06 22:00:51.332 | DEBUG    | app.services.proxy_service:get_proxies:500 - 获取代理列表: offset=0, limit=20
2025-06-06 22:00:51.332 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-06 22:00:51.332 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-06 22:00:51.335 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-06 22:00:51.337 | INFO     | core.proxy.proxy_task_manager:task_wrapper:199 - 任务 refresh_proxies_0_20 完成
2025-06-06 22:00:51.337 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-06 22:00:51.338 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-06 22:00:51.341 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-06 22:00:51.344 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=True
2025-06-06 22:00:51.348 | INFO     | ui.views.proxy_view:load_proxies_by_page:538 - 页面 1 加载完成，显示 20 条记录，共 27 条
2025-06-06 22:00:54.271 | WARNING  | core.proxy.proxy_validator:validate_proxy:71 - 使用https://web.telegram.org验证代理失败: socks5://193.1.1.6:10016, 错误: 
2025-06-06 22:00:59.271 | WARNING  | core.proxy.proxy_validator:validate_proxy:71 - 使用https://core.telegram.org验证代理失败: socks5://193.1.1.6:10016, 错误: 
2025-06-06 22:00:59.272 | WARNING  | core.proxy.proxy_validator:validate_proxy:75 - 代理验证失败: socks5://193.1.1.6:10016
2025-06-06 22:00:59.272 | INFO     | core.proxy.proxy_validator:validate_proxy:43 - 正在验证代理 socks5://193.1.1.7:10017
2025-06-06 22:01:04.280 | WARNING  | core.proxy.proxy_validator:validate_proxy:71 - 使用https://web.telegram.org验证代理失败: socks5://193.1.1.7:10017, 错误: 
2025-06-06 22:01:09.275 | WARNING  | core.proxy.proxy_validator:validate_proxy:71 - 使用https://core.telegram.org验证代理失败: socks5://193.1.1.7:10017, 错误: 
2025-06-06 22:01:09.276 | WARNING  | core.proxy.proxy_validator:validate_proxy:75 - 代理验证失败: socks5://193.1.1.7:10017
2025-06-06 22:01:09.276 | INFO     | core.proxy.proxy_validator:validate_proxy:43 - 正在验证代理 socks5://193.1.1.8:10018
2025-06-06 22:01:14.276 | WARNING  | core.proxy.proxy_validator:validate_proxy:71 - 使用https://web.telegram.org验证代理失败: socks5://193.1.1.8:10018, 错误: 
2025-06-06 22:01:19.277 | WARNING  | core.proxy.proxy_validator:validate_proxy:71 - 使用https://core.telegram.org验证代理失败: socks5://193.1.1.8:10018, 错误: 
2025-06-06 22:01:19.278 | WARNING  | core.proxy.proxy_validator:validate_proxy:75 - 代理验证失败: socks5://193.1.1.8:10018
2025-06-06 22:01:19.278 | INFO     | core.proxy.proxy_validator:validate_proxy:43 - 正在验证代理 socks5://193.1.1.9:10019
2025-06-06 22:01:24.272 | WARNING  | core.proxy.proxy_validator:validate_proxy:71 - 使用https://web.telegram.org验证代理失败: socks5://193.1.1.9:10019, 错误: 
2025-06-06 22:01:29.275 | WARNING  | core.proxy.proxy_validator:validate_proxy:71 - 使用https://core.telegram.org验证代理失败: socks5://193.1.1.9:10019, 错误: 
2025-06-06 22:01:29.275 | WARNING  | core.proxy.proxy_validator:validate_proxy:75 - 代理验证失败: socks5://193.1.1.9:10019
2025-06-06 22:01:29.773 | INFO     | core.proxy.proxy_validator:validate_proxy:43 - 正在验证代理 socks5://*********0:10020
2025-06-06 22:01:34.725 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-06 22:01:34.725 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-06 22:01:35.272 | WARNING  | core.proxy.proxy_validator:validate_proxy:71 - 使用https://web.telegram.org验证代理失败: socks5://*********0:10020, 错误: 
2025-06-06 22:01:40.275 | WARNING  | core.proxy.proxy_validator:validate_proxy:71 - 使用https://core.telegram.org验证代理失败: socks5://*********0:10020, 错误: 
2025-06-06 22:01:40.276 | WARNING  | core.proxy.proxy_validator:validate_proxy:75 - 代理验证失败: socks5://*********0:10020
2025-06-06 22:01:40.276 | INFO     | core.proxy.proxy_validator:validate_proxy:43 - 正在验证代理 socks5://*********1:10021
2025-06-06 22:01:45.272 | WARNING  | core.proxy.proxy_validator:validate_proxy:71 - 使用https://web.telegram.org验证代理失败: socks5://*********1:10021, 错误: 
2025-06-06 22:01:50.273 | WARNING  | core.proxy.proxy_validator:validate_proxy:71 - 使用https://core.telegram.org验证代理失败: socks5://*********1:10021, 错误: 
2025-06-06 22:01:50.274 | WARNING  | core.proxy.proxy_validator:validate_proxy:75 - 代理验证失败: socks5://*********1:10021
2025-06-06 22:01:50.274 | INFO     | core.proxy.proxy_validator:validate_proxy:43 - 正在验证代理 socks5://*********2:10022
2025-06-06 22:01:55.274 | WARNING  | core.proxy.proxy_validator:validate_proxy:71 - 使用https://web.telegram.org验证代理失败: socks5://*********2:10022, 错误: 
2025-06-06 22:02:00.271 | WARNING  | core.proxy.proxy_validator:validate_proxy:71 - 使用https://core.telegram.org验证代理失败: socks5://*********2:10022, 错误: 
2025-06-06 22:02:00.272 | WARNING  | core.proxy.proxy_validator:validate_proxy:75 - 代理验证失败: socks5://*********2:10022
2025-06-06 22:02:00.273 | INFO     | core.proxy.proxy_validator:validate_proxy:43 - 正在验证代理 socks5://*********3:10023
2025-06-06 22:02:05.274 | WARNING  | core.proxy.proxy_validator:validate_proxy:71 - 使用https://web.telegram.org验证代理失败: socks5://*********3:10023, 错误: 
2025-06-06 22:02:10.272 | WARNING  | core.proxy.proxy_validator:validate_proxy:71 - 使用https://core.telegram.org验证代理失败: socks5://*********3:10023, 错误: 
2025-06-06 22:02:10.272 | WARNING  | core.proxy.proxy_validator:validate_proxy:75 - 代理验证失败: socks5://*********3:10023
2025-06-06 22:02:10.282 | INFO     | core.proxy.proxy_core_service:validate_proxies_stream:232 - 批量代理验证完成，结果: 0/24 个有效
2025-06-06 22:02:10.282 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-06 22:02:10.283 | INFO     | app.services.proxy_service:refresh_proxies:712 - 刷新代理列表: offset=0, limit=20
2025-06-06 22:02:10.283 | INFO     | core.proxy.proxy_task_manager:submit_task:172 - 提交耗时任务到子线程: refresh_proxies_0_20
2025-06-06 22:02:10.284 | DEBUG    | app.services.proxy_service:get_proxies:500 - 获取代理列表: offset=0, limit=20
2025-06-06 22:02:10.284 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-06 22:02:10.284 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-06 22:02:10.296 | INFO     | ui.views.proxy_view:load_proxies_by_page:525 - 加载代理页面: 1
2025-06-06 22:02:10.298 | INFO     | app.services.proxy_service:refresh_proxies:712 - 刷新代理列表: offset=0, limit=20
2025-06-06 22:02:10.299 | INFO     | app.services.proxy_service:refresh_proxies:719 - 刷新任务已在运行，跳过
2025-06-06 22:02:10.299 | DEBUG    | app.services.proxy_service:get_proxies:500 - 获取代理列表: offset=0, limit=20
2025-06-06 22:02:10.299 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-06 22:02:10.299 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-06 22:02:10.301 | INFO     | core.proxy.proxy_task_manager:task_wrapper:199 - 任务 refresh_proxies_0_20 完成
2025-06-06 22:02:10.301 | INFO     | ui.views.proxy_view:load_proxies_by_page:525 - 加载代理页面: 1
2025-06-06 22:02:10.302 | INFO     | app.services.proxy_service:refresh_proxies:712 - 刷新代理列表: offset=0, limit=20
2025-06-06 22:02:10.302 | INFO     | core.proxy.proxy_task_manager:submit_task:172 - 提交耗时任务到子线程: refresh_proxies_0_20
2025-06-06 22:02:10.302 | DEBUG    | app.services.proxy_service:get_proxies:500 - 获取代理列表: offset=0, limit=20
2025-06-06 22:02:10.302 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-06 22:02:10.303 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-06 22:02:10.310 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-06 22:02:10.315 | INFO     | core.proxy.proxy_task_manager:task_wrapper:199 - 任务 refresh_proxies_0_20 完成
2025-06-06 22:02:10.349 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-06 22:02:10.350 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-06 22:02:10.351 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-06 22:02:10.351 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-06 22:02:10.352 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-06 22:02:10.354 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-06 22:02:10.361 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-06 22:02:10.362 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-06 22:02:10.364 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-06 22:02:10.367 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=True
2025-06-06 22:02:10.370 | INFO     | ui.views.proxy_view:load_proxies_by_page:538 - 页面 1 加载完成，显示 20 条记录，共 27 条
2025-06-06 22:02:10.411 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=True
2025-06-06 22:02:10.416 | INFO     | ui.views.proxy_view:load_proxies_by_page:538 - 页面 1 加载完成，显示 20 条记录，共 27 条
2025-06-06 22:02:10.490 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=True
2025-06-06 22:02:10.495 | INFO     | ui.views.proxy_view:load_proxies_by_page:525 - 加载代理页面: 1
2025-06-06 22:02:10.495 | INFO     | app.services.proxy_service:refresh_proxies:712 - 刷新代理列表: offset=0, limit=20
2025-06-06 22:02:10.496 | INFO     | core.proxy.proxy_task_manager:submit_task:172 - 提交耗时任务到子线程: refresh_proxies_0_20
2025-06-06 22:02:10.496 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-06 22:02:10.496 | DEBUG    | app.services.proxy_service:get_proxies:500 - 获取代理列表: offset=0, limit=20
2025-06-06 22:02:10.498 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-06 22:02:10.508 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-06 22:02:10.512 | INFO     | core.proxy.proxy_task_manager:task_wrapper:199 - 任务 refresh_proxies_0_20 完成
2025-06-06 22:02:10.541 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-06 22:02:10.542 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-06 22:02:10.547 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-06 22:02:10.555 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=True
2025-06-06 22:02:10.559 | INFO     | ui.views.proxy_view:load_proxies_by_page:538 - 页面 1 加载完成，显示 20 条记录，共 27 条
2025-06-06 22:02:10.560 | INFO     | ui.views.proxy_view:_on_add_proxy:509 - 已清空输入框，代理添加和验证流程完成
2025-06-06 22:02:34.726 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-06 22:02:34.726 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-06 22:03:34.725 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-06 22:03:34.725 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-06 22:04:34.725 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-06 22:04:34.726 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-06 22:05:34.725 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-06 22:05:34.725 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-06 22:06:34.728 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-06 22:06:34.729 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-06 22:06:56.308 | INFO     | ui.views.proxy_view:load_proxies_by_page:525 - 加载代理页面: 2
2025-06-06 22:06:56.308 | INFO     | app.services.proxy_service:refresh_proxies:712 - 刷新代理列表: offset=20, limit=20
2025-06-06 22:06:56.308 | INFO     | core.proxy.proxy_task_manager:submit_task:172 - 提交耗时任务到子线程: refresh_proxies_20_20
2025-06-06 22:06:56.309 | DEBUG    | app.services.proxy_service:get_proxies:500 - 获取代理列表: offset=20, limit=20
2025-06-06 22:06:56.309 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-06 22:06:56.309 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-06 22:06:56.312 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-06 22:06:56.314 | INFO     | core.proxy.proxy_task_manager:task_wrapper:199 - 任务 refresh_proxies_20_20 完成
2025-06-06 22:06:56.315 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-06 22:06:56.316 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-06 22:06:56.320 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-06 22:06:56.323 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=True
2025-06-06 22:06:56.327 | INFO     | ui.views.proxy_view:load_proxies_by_page:538 - 页面 2 加载完成，显示 7 条记录，共 27 条
2025-06-06 22:07:27.996 | INFO     | ui.views.proxy_view:_on_table_double_clicked:1172 - 已复制代理配置: socks5://127.0.0.1:1080
2025-06-06 22:07:34.725 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-06 22:07:34.725 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-06 22:08:34.725 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-06 22:08:34.725 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-06 22:09:34.724 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-06 22:09:34.725 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-06 22:10:34.725 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-06 22:10:34.726 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-06 22:11:34.726 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-06 22:11:34.726 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-06 22:12:34.726 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-06 22:12:34.727 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-06 22:13:34.725 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-06 22:13:34.725 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-06 22:14:34.727 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-06 22:14:34.728 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-06 22:15:34.727 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-06 22:15:34.727 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-06 22:16:34.726 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-06 22:16:34.727 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-06 22:17:34.726 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-06 22:17:34.726 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-06 22:18:34.740 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-06 22:18:34.741 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-06 22:19:34.730 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-06 22:19:34.731 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-06 22:20:34.725 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-06 22:20:34.726 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-06 22:21:34.725 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-06 22:21:34.726 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-06 22:22:34.725 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-06 22:22:34.726 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-06 22:22:48.355 | INFO     | ui.views.account_view:_on_add_group:209 - 请求添加分组: 主账户
2025-06-06 22:22:48.356 | INFO     | app.controllers.account_controller:create_group:128 - 请求创建分组: 主账户
2025-06-06 22:22:48.356 | INFO     | app.services.account_service:create_group:56 - 创建账户分组: 主账户
2025-06-06 22:22:48.357 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-06 22:22:48.417 | INFO     | data.repositories.account_repo:create_group:55 - 创建账户分组成功: 主账户
2025-06-06 22:22:48.429 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-06 22:22:48.431 | INFO     | app.controllers.account_controller:create_group:131 - 创建分组结果: success=True, result=<AccountGroup(id=2, name=主账户)>
2025-06-06 22:22:48.432 | INFO     | ui.views.account_view:_on_group_created:457 - 分组创建成功: 主账户
2025-06-06 22:22:48.436 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-06 22:22:48.566 | INFO     | app.services.account_service:get_all_groups:96 - 获取所有账户分组成功, 共 2 个
2025-06-06 22:22:48.566 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-06 22:22:48.567 | DEBUG    | ui.views.account_view:_on_groups_loaded:442 - 分组加载完成: 2个分组
2025-06-06 22:22:49.853 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-06 22:22:49.863 | INFO     | app.services.account_service:get_accounts_by_group:311 - 获取分组 2 的账户成功, 共 0 个
2025-06-06 22:22:49.863 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-06 22:22:49.864 | DEBUG    | ui.views.account_view:_on_accounts_loaded:575 - 账户加载完成: 0个账户
2025-06-06 22:22:49.865 | INFO     | ui.views.account_view:_auto_login_accounts:595 - 自动登录条件不满足：无控制器或无账户。
2025-06-06 22:22:50.621 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-06 22:22:50.633 | INFO     | app.services.account_service:get_accounts_by_group:311 - 获取分组 1 的账户成功, 共 0 个
2025-06-06 22:22:50.634 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-06 22:22:50.634 | DEBUG    | ui.views.account_view:_on_accounts_loaded:575 - 账户加载完成: 0个账户
2025-06-06 22:22:50.635 | INFO     | ui.views.account_view:_auto_login_accounts:595 - 自动登录条件不满足：无控制器或无账户。
2025-06-06 22:22:51.971 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-06 22:22:52.099 | INFO     | app.services.account_service:get_all_groups:96 - 获取所有账户分组成功, 共 2 个
2025-06-06 22:22:52.099 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-06 22:22:52.100 | INFO     | ui.views.add_account_view:load_account_groups:94 - 成功加载 2 个分组
2025-06-06 22:22:53.635 | INFO     | ui.views.add_account_view:get_proxy_settings:114 - selected_items: []
2025-06-06 22:22:53.638 | INFO     | ui.views.add_account_view:get_proxy_settings:114 - selected_items: []
2025-06-06 22:22:53.653 | INFO     | app.services.account_service:get_proxy_ips:1032 - 获取有效代理IP列表（包含绑定计数）
2025-06-06 22:22:53.653 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-06 22:22:53.664 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-06 22:22:53.666 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-06 22:22:53.670 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-06 22:22:53.670 | INFO     | app.services.account_service:get_proxy_ips:1084 - 成功获取 1 个有效代理（已附带绑定计数）
2025-06-06 22:22:53.672 | INFO     | app.controllers.account_controller:get_proxy_ips:537 - 获取到1个有效代理IP
2025-06-06 22:22:54.260 | INFO     | ui.views.add_account_view:get_proxy_settings:107 - 使用系统代理
2025-06-06 22:22:54.260 | INFO     | ui.views.add_account_view:get_proxy_settings:107 - 使用系统代理
2025-06-06 22:23:34.728 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-06 22:23:34.729 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-06 22:24:13.196 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-06 22:24:13.256 | INFO     | app.services.account_service:get_accounts_by_group:311 - 获取分组 1 的账户成功, 共 0 个
2025-06-06 22:24:13.257 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-06 22:24:13.260 | DEBUG    | ui.views.account_view:_on_accounts_loaded:575 - 账户加载完成: 0个账户
2025-06-06 22:24:13.260 | INFO     | ui.views.account_view:_auto_login_accounts:595 - 自动登录条件不满足：无控制器或无账户。
2025-06-06 22:24:14.279 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-06 22:24:14.431 | INFO     | app.services.account_service:get_all_groups:96 - 获取所有账户分组成功, 共 2 个
2025-06-06 22:24:14.432 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-06 22:24:14.432 | INFO     | ui.views.add_account_view:load_account_groups:94 - 成功加载 2 个分组
2025-06-06 22:24:17.279 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-06 22:24:17.336 | INFO     | app.services.account_service:get_accounts_by_group:311 - 获取分组 1 的账户成功, 共 0 个
2025-06-06 22:24:17.337 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-06 22:24:17.339 | DEBUG    | ui.views.account_view:_on_accounts_loaded:575 - 账户加载完成: 0个账户
2025-06-06 22:24:17.339 | INFO     | ui.views.account_view:_auto_login_accounts:595 - 自动登录条件不满足：无控制器或无账户。
2025-06-06 22:24:18.508 | INFO     | ui.views.import_sessions_view:refresh_proxy_list_async:422 - 开始刷新代理列表...
2025-06-06 22:24:18.509 | INFO     | app.services.account_service:get_proxy_ips:1032 - 获取有效代理IP列表（包含绑定计数）
2025-06-06 22:24:18.510 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-06 22:24:18.628 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-06 22:24:18.629 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-06 22:24:18.632 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-06 22:24:18.633 | INFO     | app.services.account_service:get_proxy_ips:1084 - 成功获取 1 个有效代理（已附带绑定计数）
2025-06-06 22:24:18.634 | INFO     | app.controllers.account_controller:get_proxy_ips:537 - 获取到1个有效代理IP
2025-06-06 22:24:18.645 | INFO     | ui.views.import_sessions_view:refresh_groups_list_async:469 - 开始刷新分组列表...
2025-06-06 22:24:18.646 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-06 22:24:18.652 | INFO     | app.services.account_service:get_all_groups:96 - 获取所有账户分组成功, 共 2 个
2025-06-06 22:24:18.653 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-06 22:24:34.724 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-06 22:24:34.725 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-06 22:25:34.724 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-06 22:25:34.725 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-06 22:26:34.728 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-06 22:26:34.728 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-06 22:27:01.205 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-06 22:27:01.357 | INFO     | app.services.account_service:get_all_groups:96 - 获取所有账户分组成功, 共 2 个
2025-06-06 22:27:01.358 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-06 22:27:01.360 | INFO     | ui.views.add_account_view:load_account_groups:94 - 成功加载 2 个分组
2025-06-06 22:27:03.291 | INFO     | ui.views.add_account_view:get_proxy_settings:102 - 禁用代理
2025-06-06 22:27:03.292 | INFO     | ui.views.add_account_view:get_proxy_settings:102 - 禁用代理
2025-06-06 22:27:03.761 | INFO     | ui.views.add_account_view:get_proxy_settings:107 - 使用系统代理
2025-06-06 22:27:03.761 | INFO     | ui.views.add_account_view:get_proxy_settings:107 - 使用系统代理
2025-06-06 22:27:04.163 | INFO     | ui.views.add_account_view:get_proxy_settings:114 - selected_items: []
2025-06-06 22:27:04.167 | INFO     | ui.views.add_account_view:get_proxy_settings:114 - selected_items: []
2025-06-06 22:27:04.179 | INFO     | app.services.account_service:get_proxy_ips:1032 - 获取有效代理IP列表（包含绑定计数）
2025-06-06 22:27:04.180 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-06 22:27:04.189 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-06 22:27:04.190 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-06 22:27:04.195 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-06 22:27:04.197 | INFO     | app.services.account_service:get_proxy_ips:1084 - 成功获取 1 个有效代理（已附带绑定计数）
2025-06-06 22:27:04.197 | INFO     | app.controllers.account_controller:get_proxy_ips:537 - 获取到1个有效代理IP
2025-06-06 22:27:05.035 | INFO     | ui.views.add_account_view:get_proxy_settings:107 - 使用系统代理
2025-06-06 22:27:05.036 | INFO     | ui.views.add_account_view:get_proxy_settings:107 - 使用系统代理
2025-06-06 22:27:05.674 | INFO     | ui.views.add_account_view:get_proxy_settings:102 - 禁用代理
2025-06-06 22:27:05.674 | INFO     | ui.views.add_account_view:get_proxy_settings:102 - 禁用代理
2025-06-06 22:27:06.183 | INFO     | ui.views.add_account_view:get_proxy_settings:107 - 使用系统代理
2025-06-06 22:27:06.183 | INFO     | ui.views.add_account_view:get_proxy_settings:107 - 使用系统代理
2025-06-06 22:27:34.727 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-06 22:27:34.727 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-06 22:28:34.727 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-06 22:28:34.728 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-06 22:29:34.727 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-06 22:29:34.728 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-06 22:30:34.730 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-06 22:30:34.731 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-06 22:31:34.730 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-06 22:31:34.730 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-06 22:32:34.726 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-06 22:32:34.726 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-06 22:33:34.727 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-06 22:33:34.727 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-06 22:34:34.729 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-06 22:34:34.729 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-06 22:35:34.725 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-06 22:35:34.726 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-06 22:36:34.725 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-06 22:36:34.726 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-06 22:37:34.727 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-06 22:37:34.728 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-06 22:38:34.733 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-06 22:38:34.733 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-06 22:39:34.728 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-06 22:39:34.729 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-06 22:40:34.725 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-06 22:40:34.726 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-06 22:41:34.728 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-06 22:41:34.728 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-06 22:42:34.725 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-06 22:42:34.726 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-06 22:43:34.724 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-06 22:43:34.725 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-06 22:44:34.728 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-06 22:44:34.729 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-06 22:45:34.725 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-06 22:45:34.726 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-06 22:46:34.728 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-06 22:46:34.728 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-06 22:47:34.726 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-06 22:47:34.726 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-06 22:48:34.725 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-06 22:48:34.726 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-06 22:49:34.724 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-06 22:49:34.725 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-06 22:50:34.727 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-06 22:50:34.728 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-06 22:51:34.726 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-06 22:51:34.726 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-06 22:52:34.726 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-06 22:52:34.727 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-06 22:53:34.728 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-06 22:53:34.729 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-06 22:54:34.724 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-06 22:54:34.724 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-06 22:55:34.725 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-06 22:55:34.725 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-06 22:56:34.736 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-06 22:56:34.737 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-06 22:57:34.729 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-06 22:57:34.729 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-06 22:58:34.724 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-06 22:58:34.725 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-06 22:59:34.725 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-06 22:59:34.725 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-06 23:00:34.724 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-06 23:00:34.725 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-06 23:01:34.727 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-06 23:01:34.728 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-06 23:02:34.728 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-06 23:02:34.728 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-06 23:02:51.640 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-06 23:02:51.714 | INFO     | app.services.account_service:get_accounts_by_group:311 - 获取分组 1 的账户成功, 共 0 个
2025-06-06 23:02:51.714 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-06 23:02:51.718 | DEBUG    | ui.views.account_view:_on_accounts_loaded:575 - 账户加载完成: 0个账户
2025-06-06 23:02:51.718 | INFO     | ui.views.account_view:_auto_login_accounts:595 - 自动登录条件不满足：无控制器或无账户。
2025-06-06 23:02:52.723 | INFO     | ui.main_window:closeEvent:335 - MainWindow: 接收到关闭事件
2025-06-06 23:02:52.754 | INFO     | ui.main_window:_cleanup_before_quit:228 - MainWindow: 执行清理资源...
2025-06-06 23:02:52.757 | INFO     | core.telegram.client_worker:stop:110 - 正在停止Telegram客户端工作线程
2025-06-06 23:02:52.770 | INFO     | core.telegram.client_manager:cleanup_async:1140 - 开始清理资源并断开所有连接
2025-06-06 23:02:52.770 | INFO     | core.telegram.client_manager:disconnect_all_clients:725 - 正在断开所有客户端连接
2025-06-06 23:02:52.771 | INFO     | core.telegram.client_manager:cleanup_async:1159 - 资源清理完成
2025-06-06 23:02:53.256 | INFO     | core.telegram.client_manager:cleanup_async:1140 - 开始清理资源并断开所有连接
2025-06-06 23:02:53.256 | INFO     | core.telegram.client_manager:disconnect_all_clients:725 - 正在断开所有客户端连接
2025-06-06 23:02:53.257 | INFO     | core.telegram.client_manager:cleanup_async:1159 - 资源清理完成
2025-06-06 23:02:53.758 | INFO     | core.telegram.client_worker:run:99 - Telegram客户端工作线程已退出
2025-06-06 23:02:53.759 | INFO     | core.telegram.client_worker:stop:124 - Telegram客户端工作线程已停止
2025-06-06 23:02:53.759 | INFO     | ui.main_window:_cleanup_before_quit:237 - TelegramClientWorker 已停止。
2025-06-06 23:02:53.760 | INFO     | ui.main_window:_cleanup_before_quit:241 - MainWindow 清理完成
2025-06-06 23:02:53.773 | INFO     | __main__:main:111 - 应用程序已正常退出
2025-06-06 23:15:01.758 | INFO     | utils.logger:init_logger:98 - 日志系统初始化完成，默认级别: DEBUG，日志目录: h:\PyProject\TeleTest\APPDATA\logs
2025-06-06 23:15:03.993 | INFO     | data.database:init_database:31 - 初始化数据库连接: h:\PyProject\TeleTest\APPDATA\database\telegram_manager.db
2025-06-06 23:15:04.068 | DEBUG    | data.database:init_database:58 - 创建数据库表
2025-06-06 23:15:04.080 | INFO     | data.database:init_database:61 - 数据库初始化完成
2025-06-06 23:15:05.461 | DEBUG    | app.controllers.auth_controller:initialize_software:43 - 控制器: 初始化软件 app_id=1000, version=1.0
2025-06-06 23:15:05.462 | DEBUG    | utils.client_http:get:54 - 发送GET请求: https://server.xile188.com/api/user/1000/windowns10/1.0/ini
2025-06-06 23:15:05.892 | DEBUG    | core.auth.api_service:init_software:66 - 初始化数据解密成功: {'new_content': '1.多账户管理\n2.ip池管理\n3.消息监听\n4.消息群发\n5.拉人入群', 'new_url': None, 'notice': {'id': '2', 'content': '公告内容测试3', 'time': '**********'}}
2025-06-06 23:15:05.899 | INFO     | app.controllers.auth_controller:login:61 - 控制器: 用户登录 account=demo01
2025-06-06 23:15:09.060 | INFO     | core.auth.api_service:login:127 - 用户登录: account=demo01
2025-06-06 23:15:09.061 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/logon
2025-06-06 23:15:09.411 | INFO     | core.auth.api_service:get_user_info:347 - 获取用户信息
2025-06-06 23:15:09.412 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/info
2025-06-06 23:15:09.657 | INFO     | core.auth.api_service:get_user_info:375 - 获取用户信息成功
2025-06-06 23:15:09.662 | INFO     | __main__:on_auth_completed:93 - 用户登录成功，正在启动主窗口...
2025-06-06 23:15:09.681 | INFO     | core.telegram.client_worker:__init__:63 - Telegram客户端工作线程初始化
2025-06-06 23:15:09.686 | INFO     | core.telegram.client_worker:run:67 - Telegram客户端工作线程开始运行
2025-06-06 23:15:09.691 | INFO     | core.telegram.client_manager:__init__:66 - Telegram客户端管理器初始化
2025-06-06 23:15:09.691 | INFO     | core.telegram.user_manager:__init__:31 - Telegram用户管理器初始化
2025-06-06 23:15:09.692 | INFO     | core.telegram.group_manager:__init__:36 - Telegram群组管理器初始化
2025-06-06 23:15:09.693 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-06-06 23:15:09.693 | INFO     | core.telegram.monitor_manager:__init__:26 - Telegram监控管理器初始化
2025-06-06 23:15:09.694 | INFO     | core.telegram.telegram_service:__init__:39 - Telegram服务初始化完成
2025-06-06 23:15:09.696 | INFO     | ui.main_window:_initialize_core_components:79 - MainWindow: 初始化核心组件...
2025-06-06 23:15:09.698 | INFO     | app.services.account_service:__init__:42 - 账户服务初始化
2025-06-06 23:15:09.699 | INFO     | app.controllers.account_controller:__init__:81 - 账户控制器初始化
2025-06-06 23:15:09.699 | INFO     | core.proxy.proxy_task_manager:run:284 - 代理任务管理器线程启动
2025-06-06 23:15:09.699 | INFO     | app.services.monitor_service:__init__:39 - 初始化监控任务服务
2025-06-06 23:15:09.700 | INFO     | core.monitor.filter:__init__:26 - 初始化消息过滤器
2025-06-06 23:15:09.700 | INFO     | core.monitor.extractor:__init__:24 - 初始化关键词提取器
2025-06-06 23:15:09.701 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-06-06 23:15:09.701 | INFO     | core.monitor.notifier:__init__:90 - 初始化通知管理器
2025-06-06 23:15:09.702 | INFO     | app.controllers.telegram_monitor_controller:__init__:64 - 初始化 Telegram 监控控制器
2025-06-06 23:15:09.702 | DEBUG    | app.controllers.telegram_monitor_controller:__init__:82 - 控制器初始化完成
2025-06-06 23:15:09.940 | INFO     | ui.views.monitor_view:__init__:48 - 初始化监控视图
2025-06-06 23:15:09.941 | DEBUG    | ui.views.monitor_view:_connect_signals:80 - 连接控制器信号
2025-06-06 23:15:10.126 | INFO     | ui.views.proxy_view:__init__:96 - 初始化视图，加载第一页代理数据
2025-06-06 23:15:10.332 | INFO     | __main__:on_auth_completed:97 - 主窗口已启动
2025-06-06 23:15:10.381 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:88 - 尝试连接监听日志信号...
2025-06-06 23:15:10.382 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:90 - 监听日志信号连接成功
2025-06-06 23:15:10.382 | INFO     | core.auth.api_service:verify_vip:425 - 会员验证接口调用
2025-06-06 23:15:10.383 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/vip
2025-06-06 23:15:10.384 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-06 23:15:10.388 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-06 23:15:10.392 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:145 - 加载监控任务列表 (搜索关键词: '')
2025-06-06 23:15:10.392 | DEBUG    | ui.views.monitor_view:_on_loading_started:620 - 加载中: 正在加载任务列表...
2025-06-06 23:15:10.393 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-06 23:15:10.399 | INFO     | app.services.proxy_service:refresh_proxies:712 - 刷新代理列表: offset=0, limit=20
2025-06-06 23:15:10.399 | INFO     | core.proxy.proxy_task_manager:submit_task:172 - 提交耗时任务到子线程: refresh_proxies_0_20
2025-06-06 23:15:10.400 | DEBUG    | app.services.proxy_service:get_proxies:500 - 获取代理列表: offset=0, limit=20
2025-06-06 23:15:10.400 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-06 23:15:10.400 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-06 23:15:10.423 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-06 23:15:10.432 | INFO     | app.controllers.invite_controller:get_invite_tasks:38 - 获取邀请任务列表, status=None
2025-06-06 23:15:10.434 | INFO     | app.services.invite_service:get_invite_tasks:121 - 获取邀请任务列表, status=None
2025-06-06 23:15:10.435 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-06 23:15:10.435 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-06 23:15:10.436 | INFO     | app.services.invite_service:task_initialize:39 - 初始化邀请服务，重置运行中的任务为暂停状态
2025-06-06 23:15:10.437 | INFO     | core.proxy.proxy_task_manager:task_wrapper:199 - 任务 refresh_proxies_0_20 完成
2025-06-06 23:15:10.438 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-06 23:15:10.661 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-06 23:15:10.663 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-06 23:15:10.663 | INFO     | app.services.invite_service:task_initialize:48 - 成功重置 0 个任务状态
2025-06-06 23:15:10.663 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-06 23:15:10.664 | INFO     | ui.main_window:start_proxy_service:134 - 检测到本地代理，自动启动代理服务...
2025-06-06 23:15:10.674 | DEBUG    | ui.views.monitor_view:_refresh_task_list:175 - 刷新任务列表，共2个任务
2025-06-06 23:15:10.680 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-06 23:15:10.689 | INFO     | app.services.account_service:get_all_groups:96 - 获取所有账户分组成功, 共 2 个
2025-06-06 23:15:10.689 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-06 23:15:10.690 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-06 23:15:10.693 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-06 23:15:10.694 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-06 23:15:10.696 | DEBUG    | ui.views.account_view:_on_groups_loaded:442 - 分组加载完成: 2个分组
2025-06-06 23:15:10.713 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-06 23:15:10.777 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=True
2025-06-06 23:15:10.791 | INFO     | app.services.account_service:get_all_accounts:289 - 获取所有账户成功, 共 0 个
2025-06-06 23:15:10.791 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-06 23:15:10.794 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-06 23:15:10.795 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-06 23:15:10.797 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-06 23:15:10.799 | DEBUG    | ui.views.account_view:_on_accounts_loaded:575 - 账户加载完成: 0个账户
2025-06-06 23:15:10.799 | DEBUG    | ui.views.account_view:_update_group_counts:184 - 分组计数已更新: {}
2025-06-06 23:15:10.800 | INFO     | ui.views.account_view:_auto_login_accounts:595 - 自动登录条件不满足：无控制器或无账户。
2025-06-06 23:15:10.819 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-06 23:15:10.824 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-06 23:15:10.826 | INFO     | app.controllers.telegram_monitor_controller:_load_total_users_count_internal:191 - 成功加载总体统计数据: 总用户数 958, 今日采集 0, 日均采集 60, 运行天数 16
2025-06-06 23:15:10.826 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:156 - 成功加载 2 个任务
2025-06-06 23:15:10.827 | DEBUG    | ui.views.monitor_view:_on_loading_finished:625 - 加载完成
2025-06-06 23:15:10.829 | INFO     | core.auth.api_service:verify_vip:439 - 会员验证成功
2025-06-06 23:15:10.833 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=True
2025-06-06 23:15:10.846 | INFO     | app.services.account_service:get_all_accounts:289 - 获取所有账户成功, 共 0 个
2025-06-06 23:15:10.846 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-06 23:15:10.850 | DEBUG    | ui.views.account_view:_update_group_counts:184 - 分组计数已更新: {}
2025-06-06 23:15:10.850 | DEBUG    | ui.views.account_view:_init_cache:151 - 账户缓存初始化完成，共 0 个账户
2025-06-06 23:15:10.856 | DEBUG    | ui.views.account_view:_on_accounts_loaded:575 - 账户加载完成: 0个账户
2025-06-06 23:15:10.856 | DEBUG    | ui.views.account_view:_update_group_counts:184 - 分组计数已更新: {}
2025-06-06 23:15:10.857 | INFO     | ui.views.account_view:_auto_login_accounts:595 - 自动登录条件不满足：无控制器或无账户。
2025-06-06 23:15:10.866 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=True
2025-06-06 23:15:10.868 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=True
2025-06-06 23:15:10.870 | INFO     | ui.views.proxy_view:_load_initial_data:1044 - 初始数据加载完成，共 27 个代理
2025-06-06 23:15:10.874 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-06 23:16:09.650 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-06 23:16:09.651 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-06 23:17:09.647 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-06 23:17:09.647 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-06 23:18:09.649 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-06 23:18:09.649 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-06 23:18:41.419 | INFO     | ui.views.import_sessions_view:refresh_proxy_list_async:422 - 开始刷新代理列表...
2025-06-06 23:18:41.420 | INFO     | app.services.account_service:get_proxy_ips:1032 - 获取有效代理IP列表（包含绑定计数）
2025-06-06 23:18:41.420 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-06 23:18:41.545 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-06 23:18:41.559 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-06 23:18:41.564 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-06 23:18:41.565 | INFO     | app.services.account_service:get_proxy_ips:1084 - 成功获取 1 个有效代理（已附带绑定计数）
2025-06-06 23:18:41.565 | INFO     | app.controllers.account_controller:get_proxy_ips:537 - 获取到1个有效代理IP
2025-06-06 23:18:41.578 | INFO     | ui.views.import_sessions_view:refresh_groups_list_async:469 - 开始刷新分组列表...
2025-06-06 23:18:41.578 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-06 23:18:41.583 | INFO     | app.services.account_service:get_all_groups:96 - 获取所有账户分组成功, 共 2 个
2025-06-06 23:18:41.584 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-06 23:19:09.649 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-06 23:19:09.650 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-06 23:20:09.649 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-06 23:20:09.650 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-06 23:21:09.648 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-06 23:21:09.648 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-06 23:22:09.647 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-06 23:22:09.648 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-06 23:23:09.647 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-06 23:23:09.648 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-06 23:24:09.648 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-06 23:24:09.648 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-06 23:25:09.646 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-06 23:25:09.647 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-06 23:26:09.650 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-06 23:26:09.651 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-06 23:27:09.647 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-06 23:27:09.647 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-06 23:28:09.647 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-06 23:28:09.647 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-06 23:29:09.647 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-06 23:29:09.647 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-06 23:30:09.646 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-06 23:30:09.647 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-06 23:31:09.648 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-06 23:31:09.648 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-06 23:32:09.649 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-06 23:32:09.649 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-06 23:33:09.649 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-06 23:33:09.650 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-06 23:34:09.649 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-06 23:34:09.650 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-06 23:35:09.646 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-06 23:35:09.646 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-06 23:36:09.647 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-06 23:36:09.648 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-06 23:37:09.649 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-06 23:37:09.650 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-06 23:37:34.547 | INFO     | ui.views.import_sessions_view:refresh_proxy_list_async:422 - 开始刷新代理列表...
2025-06-06 23:37:34.547 | INFO     | app.services.account_service:get_proxy_ips:1032 - 获取有效代理IP列表（包含绑定计数）
2025-06-06 23:37:34.548 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-06 23:37:34.664 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-06 23:37:34.665 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-06 23:37:34.669 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-06 23:37:34.671 | INFO     | app.services.account_service:get_proxy_ips:1084 - 成功获取 1 个有效代理（已附带绑定计数）
2025-06-06 23:37:34.671 | INFO     | app.controllers.account_controller:get_proxy_ips:537 - 获取到1个有效代理IP
2025-06-06 23:37:34.679 | INFO     | ui.views.import_sessions_view:refresh_groups_list_async:469 - 开始刷新分组列表...
2025-06-06 23:37:34.680 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-06 23:37:34.693 | INFO     | app.services.account_service:get_all_groups:96 - 获取所有账户分组成功, 共 2 个
2025-06-06 23:37:34.693 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-06 23:37:47.071 | INFO     | ui.views.import_sessions_view:login_selected_sessions_async:545 - 开始导入并登录 2 个会话，并发数: 5，文件: [{'session_file': 'H:\\PyProject\\TeleTest\\APPDATA\\+***********.session', 'proxy_config': {'id': 1, 'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080, 'username': None, 'password': None}, 'group_id': 2, 'row_index': 0}, {'session_file': 'H:\\PyProject\\TeleTest\\APPDATA\\+***********.session', 'proxy_config': {'type': 'system'}, 'group_id': 1, 'row_index': 1}]
2025-06-06 23:37:47.072 | INFO     | app.services.account_service:batch_import_sessions:855 - 服务层(重构)：开始批量导入 2 个session文件，最大并发: 5
2025-06-06 23:37:47.073 | INFO     | app.services.account_service:batch_import_sessions:906 - 服务层：准备使用 IP 池代理 127.0.0.1:1080 (ID: 1) for session H:\PyProject\TeleTest\APPDATA\+***********.session
2025-06-06 23:37:47.073 | INFO     | app.services.account_service:batch_import_sessions:916 - 服务层：准备使用系统代理 for session H:\PyProject\TeleTest\APPDATA\+***********.session
2025-06-06 23:37:47.073 | INFO     | app.services.account_service:batch_import_sessions:938 - 服务层：准备调用核心层处理 2 个session。
2025-06-06 23:37:47.074 | INFO     | core.telegram.client_manager:batch_import_sessions:549 - 核心层：开始批量导入 2 个session文件，最大并发 5
2025-06-06 23:37:47.075 | INFO     | core.telegram.client_manager:import_session:457 - 正在导入session: H:\PyProject\TeleTest\APPDATA\+***********.session，代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-06-06 23:37:47.077 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:256 - 创建客户端: H:\PyProject\TeleTest\APPDATA\+***********.session, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-06-06 23:37:47.080 | DEBUG    | core.telegram.client_manager:get_proxy:93 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-06-06 23:37:47.082 | DEBUG    | core.telegram.client_manager:get_proxy:140 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-06-06 23:37:47.091 | INFO     | core.telegram.client_manager:import_session:457 - 正在导入session: H:\PyProject\TeleTest\APPDATA\+***********.session，代理: {'type': 'system'}
2025-06-06 23:37:47.091 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:256 - 创建客户端: H:\PyProject\TeleTest\APPDATA\+***********.session, 代理: {'type': 'system'}
2025-06-06 23:37:47.092 | DEBUG    | core.telegram.client_manager:get_proxy:93 - get_proxy 收到代理配置字典: {'type': 'system'}
2025-06-06 23:37:47.097 | DEBUG    | core.telegram.client_manager:get_proxy:113 - get_proxy: 使用系统代理，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-06-06 23:37:49.095 | ERROR    | core.telegram.client_manager:_connect_client:217 - 认证密钥错误 (AuthKeyError): The authorization key (session file) was used under two different IP addresses simultaneously, and can no longer be used. Use the same session exclusively, or use different sessions (caused by InvokeWithLayerRequest(InitConnectionRequest(GetConfigRequest)))
2025-06-06 23:37:49.102 | WARNING  | core.telegram.client_manager:import_session:467 - 导入session失败, 连接错误: H:\PyProject\TeleTest\APPDATA\+***********.session
2025-06-06 23:37:50.856 | ERROR    | core.telegram.client_manager:_connect_client:217 - 认证密钥错误 (AuthKeyError): The authorization key (session file) was used under two different IP addresses simultaneously, and can no longer be used. Use the same session exclusively, or use different sessions (caused by InvokeWithLayerRequest(InitConnectionRequest(GetConfigRequest)))
2025-06-06 23:37:50.860 | WARNING  | core.telegram.client_manager:import_session:467 - 导入session失败, 连接错误: H:\PyProject\TeleTest\APPDATA\+***********.session
2025-06-06 23:37:50.861 | INFO     | core.telegram.client_manager:batch_import_sessions:618 - 核心层：批量导入session完成: 总计 2, 成功 0, 失败 2
2025-06-06 23:37:51.086 | INFO     | app.services.account_service:batch_import_sessions:953 - 服务层：核心层返回结果: {'total': 2, 'success': 0, 'failed': 2, 'details': [{'path': 'H:\\PyProject\\TeleTest\\APPDATA\\+***********.session', 'success': False, 'result': '认证密钥错误 (AuthKeyError): The authorization key (session file) was used under two different IP addresses simultaneously, and can no longer be used. Use the same session exclusively, or use different sessions (caused by InvokeWithLayerRequest(InitConnectionRequest(GetConfigRequest)))'}, {'path': 'H:\\PyProject\\TeleTest\\APPDATA\\+***********.session', 'success': False, 'result': '认证密钥错误 (AuthKeyError): The authorization key (session file) was used under two different IP addresses simultaneously, and can no longer be used. Use the same session exclusively, or use different sessions (caused by InvokeWithLayerRequest(InitConnectionRequest(GetConfigRequest)))'}]}
2025-06-06 23:37:51.087 | WARNING  | app.services.account_service:batch_import_sessions:1009 - 服务层：核心层导入 H:\PyProject\TeleTest\APPDATA\+***********.session 失败: 认证密钥错误 (AuthKeyError): The authorization key (session file) was used under two different IP addresses simultaneously, and can no longer be used. Use the same session exclusively, or use different sessions (caused by InvokeWithLayerRequest(InitConnectionRequest(GetConfigRequest)))
2025-06-06 23:37:51.087 | INFO     | ui.views.account_view:_on_notify:661 - 收到通知: 导入通知_0
2025-06-06 23:37:51.093 | DEBUG    | ui.views.import_sessions_view:_handle_controller_notification:588 - ImportSessionsView 收到通知: event='导入通知_0', type='error', data='{'message': '导入失败: 认证密钥错误 (AuthKeyError): The authorization key (session file) was used under two different IP addresses simultaneously, and can no longer be used. Use the same session exclusively, or use different sessions (caused by InvokeWithLayerRequest(InitConnectionRequest(GetConfigRequest)))', 'success': False}'
2025-06-06 23:37:51.099 | DEBUG    | ui.views.import_sessions_view:_handle_controller_notification:588 - ImportSessionsView 收到通知: event='导入通知_0', type='error', data='{'message': '导入失败: 认证密钥错误 (AuthKeyError): The authorization key (session file) was used under two different IP addresses simultaneously, and can no longer be used. Use the same session exclusively, or use different sessions (caused by InvokeWithLayerRequest(InitConnectionRequest(GetConfigRequest)))', 'success': False}'
2025-06-06 23:37:51.113 | WARNING  | app.services.account_service:batch_import_sessions:1009 - 服务层：核心层导入 H:\PyProject\TeleTest\APPDATA\+***********.session 失败: 认证密钥错误 (AuthKeyError): The authorization key (session file) was used under two different IP addresses simultaneously, and can no longer be used. Use the same session exclusively, or use different sessions (caused by InvokeWithLayerRequest(InitConnectionRequest(GetConfigRequest)))
2025-06-06 23:37:51.114 | INFO     | ui.views.account_view:_on_notify:661 - 收到通知: 导入通知_1
2025-06-06 23:37:51.121 | DEBUG    | ui.views.import_sessions_view:_handle_controller_notification:588 - ImportSessionsView 收到通知: event='导入通知_1', type='error', data='{'message': '导入失败: 认证密钥错误 (AuthKeyError): The authorization key (session file) was used under two different IP addresses simultaneously, and can no longer be used. Use the same session exclusively, or use different sessions (caused by InvokeWithLayerRequest(InitConnectionRequest(GetConfigRequest)))', 'success': False}'
2025-06-06 23:37:51.133 | DEBUG    | ui.views.import_sessions_view:_handle_controller_notification:588 - ImportSessionsView 收到通知: event='导入通知_1', type='error', data='{'message': '导入失败: 认证密钥错误 (AuthKeyError): The authorization key (session file) was used under two different IP addresses simultaneously, and can no longer be used. Use the same session exclusively, or use different sessions (caused by InvokeWithLayerRequest(InitConnectionRequest(GetConfigRequest)))', 'success': False}'
2025-06-06 23:37:51.144 | INFO     | app.services.account_service:batch_import_sessions:1022 - 服务层：批量导入完成: 0 个成功添加至数据库, 2 个失败。
2025-06-06 23:37:51.144 | INFO     | ui.views.import_sessions_view:_handle_batch_import_summary:575 - 批量导入摘要: 批量导入完成: 0 个成功添加至数据库, 2 个失败。
2025-06-06 23:37:51.148 | INFO     | ui.views.import_sessions_view:_handle_batch_import_summary:575 - 批量导入摘要: 批量导入完成: 0 个成功添加至数据库, 2 个失败。
2025-06-06 23:38:09.647 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-06 23:38:09.648 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-06 23:39:09.650 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-06 23:39:09.651 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-06 23:40:09.649 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-06 23:40:09.650 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-06 23:41:09.648 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-06 23:41:09.648 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-06 23:42:09.649 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-06 23:42:09.650 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-06 23:43:09.648 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-06 23:43:09.649 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-06 23:44:09.646 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-06 23:44:09.647 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-06 23:45:09.650 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-06 23:45:09.650 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-06 23:46:09.649 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-06 23:46:09.649 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-06 23:46:28.487 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-06 23:46:28.646 | INFO     | app.services.account_service:get_all_groups:96 - 获取所有账户分组成功, 共 2 个
2025-06-06 23:46:28.646 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-06 23:46:28.647 | INFO     | ui.views.add_account_view:load_account_groups:94 - 成功加载 2 个分组
2025-06-06 23:46:30.158 | INFO     | ui.views.add_account_view:get_proxy_settings:114 - selected_items: []
2025-06-06 23:46:30.161 | INFO     | ui.views.add_account_view:get_proxy_settings:114 - selected_items: []
2025-06-06 23:46:30.170 | INFO     | app.services.account_service:get_proxy_ips:1032 - 获取有效代理IP列表（包含绑定计数）
2025-06-06 23:46:30.171 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-06 23:46:30.179 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-06 23:46:30.180 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-06 23:46:30.183 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-06 23:46:30.184 | INFO     | app.services.account_service:get_proxy_ips:1084 - 成功获取 1 个有效代理（已附带绑定计数）
2025-06-06 23:46:30.185 | INFO     | app.controllers.account_controller:get_proxy_ips:537 - 获取到1个有效代理IP
2025-06-06 23:47:09.644 | INFO     | app.services.account_service:get_proxy_ips:1032 - 获取有效代理IP列表（包含绑定计数）
2025-06-06 23:47:09.644 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-06 23:47:09.646 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-06 23:47:09.647 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-06 23:47:09.650 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-06 23:47:09.651 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-06 23:47:09.655 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-06 23:47:09.657 | INFO     | app.services.account_service:get_proxy_ips:1084 - 成功获取 1 个有效代理（已附带绑定计数）
2025-06-06 23:47:09.657 | INFO     | app.controllers.account_controller:get_proxy_ips:537 - 获取到1个有效代理IP
2025-06-06 23:48:09.647 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-06 23:48:09.648 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-06 23:49:09.653 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-06 23:49:09.655 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-06 23:50:09.646 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-06 23:50:09.646 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-06 23:50:35.858 | INFO     | ui.views.add_account_view:get_proxy_settings:114 - selected_items: [<PySide6.QtWidgets.QListWidgetItem object at 0x0000025AE2876A00>]
2025-06-06 23:50:35.859 | INFO     | ui.views.add_account_view:get_proxy_settings:118 - 使用代理IP: {'account_count': 0, 'id': 1, 'ip': '127.0.0.1', 'password': None, 'port': 1080, 'proxy_type': 'socks5', 'username': None}
2025-06-06 23:50:35.860 | INFO     | ui.views.add_account_view:send_verification_code:165 - 发送验证码: +***********, 代理设置: {'id': 1, 'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080, 'username': None, 'password': None}
2025-06-06 23:50:35.861 | INFO     | app.services.account_service:start_login:691 - 开始登录过程: +***********, 代理: {'id': 1, 'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080, 'username': None, 'password': None}
2025-06-06 23:50:35.861 | INFO     | core.telegram.client_manager:start_login:286 - 开始登录账户: +***********
2025-06-06 23:50:35.863 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:256 - 创建客户端: h:\PyProject\TeleTest\APPDATA\sessions\+***********.session, 代理: {'id': 1, 'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080, 'username': None, 'password': None}
2025-06-06 23:50:35.863 | DEBUG    | core.telegram.client_manager:get_proxy:93 - get_proxy 收到代理配置字典: {'id': 1, 'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080, 'username': None, 'password': None}
2025-06-06 23:50:35.864 | DEBUG    | core.telegram.client_manager:get_proxy:140 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-06-06 23:50:39.675 | INFO     | core.telegram.client_manager:_connect_client:204 - 连接成功!
2025-06-06 23:50:40.896 | INFO     | core.telegram.client_manager:_verify_client_authorization:1195 - 账户已授权: +***********
2025-06-06 23:51:09.648 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-06 23:51:09.649 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-06 23:52:09.648 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-06 23:52:09.649 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-06 23:52:40.119 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-06 23:52:40.179 | INFO     | app.services.account_service:get_all_accounts:289 - 获取所有账户成功, 共 0 个
2025-06-06 23:52:40.179 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-06 23:52:40.180 | DEBUG    | ui.views.account_view:_on_accounts_loaded:575 - 账户加载完成: 0个账户
2025-06-06 23:52:40.181 | DEBUG    | ui.views.account_view:_update_group_counts:184 - 分组计数已更新: {}
2025-06-06 23:52:40.181 | INFO     | ui.views.account_view:_auto_login_accounts:595 - 自动登录条件不满足：无控制器或无账户。
2025-06-06 23:52:41.801 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-06 23:52:41.954 | INFO     | app.services.account_service:get_all_groups:96 - 获取所有账户分组成功, 共 2 个
2025-06-06 23:52:41.954 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-06 23:52:41.955 | INFO     | ui.views.add_account_view:load_account_groups:94 - 成功加载 2 个分组
2025-06-06 23:52:43.316 | INFO     | ui.views.add_account_view:get_proxy_settings:114 - selected_items: []
2025-06-06 23:52:43.318 | INFO     | ui.views.add_account_view:get_proxy_settings:114 - selected_items: []
2025-06-06 23:52:43.328 | INFO     | app.services.account_service:get_proxy_ips:1032 - 获取有效代理IP列表（包含绑定计数）
2025-06-06 23:52:43.328 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-06 23:52:43.336 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-06 23:52:43.336 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-06 23:52:43.339 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-06 23:52:43.340 | INFO     | app.services.account_service:get_proxy_ips:1084 - 成功获取 1 个有效代理（已附带绑定计数）
2025-06-06 23:52:43.341 | INFO     | app.controllers.account_controller:get_proxy_ips:537 - 获取到1个有效代理IP
2025-06-06 23:52:46.531 | INFO     | ui.views.add_account_view:get_proxy_settings:114 - selected_items: [<PySide6.QtWidgets.QListWidgetItem object at 0x0000025AE27971C0>]
2025-06-06 23:52:46.532 | INFO     | ui.views.add_account_view:get_proxy_settings:118 - 使用代理IP: {'account_count': 0, 'id': 1, 'ip': '127.0.0.1', 'password': None, 'port': 1080, 'proxy_type': 'socks5', 'username': None}
2025-06-06 23:52:46.533 | INFO     | ui.views.add_account_view:send_verification_code:165 - 发送验证码: +***********, 代理设置: {'id': 1, 'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080, 'username': None, 'password': None}
2025-06-06 23:52:46.533 | INFO     | app.services.account_service:start_login:691 - 开始登录过程: +***********, 代理: {'id': 1, 'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080, 'username': None, 'password': None}
2025-06-06 23:52:46.534 | INFO     | core.telegram.client_manager:start_login:286 - 开始登录账户: +***********
2025-06-06 23:52:46.535 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:256 - 创建客户端: h:\PyProject\TeleTest\APPDATA\sessions\+***********.session, 代理: {'id': 1, 'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080, 'username': None, 'password': None}
2025-06-06 23:52:46.536 | DEBUG    | core.telegram.client_manager:get_proxy:93 - get_proxy 收到代理配置字典: {'id': 1, 'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080, 'username': None, 'password': None}
2025-06-06 23:52:46.536 | DEBUG    | core.telegram.client_manager:get_proxy:140 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-06-06 23:53:00.756 | INFO     | core.telegram.client_manager:_connect_client:204 - 连接成功!
2025-06-06 23:53:01.504 | WARNING  | core.telegram.client_manager:_verify_client_authorization:1201 - 账户未授权: +***********
2025-06-06 23:53:09.647 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-06 23:53:09.648 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-06 23:53:44.981 | INFO     | app.services.account_service:submit_code:718 - 提交验证码: +***********
2025-06-06 23:53:48.014 | INFO     | core.telegram.client_manager:submit_code:369 - 登录成功: +***********
2025-06-06 23:53:48.992 | INFO     | app.controllers.account_controller:complete_login:494 - 正在完成登录: +***********, 代理ID: 1, 代理类型: ip_pool, 分组ID: 2
2025-06-06 23:53:48.992 | INFO     | app.services.account_service:get_user_info:1128 - 获取用户信息: +***********
2025-06-06 23:53:48.993 | INFO     | core.telegram.user_manager:get_user_info:42 - 正在获取用户信息: +***********
2025-06-06 23:53:48.994 | INFO     | app.controllers.account_controller:complete_login:494 - 正在完成登录: +***********, 代理ID: 1, 代理类型: ip_pool, 分组ID: 2
2025-06-06 23:53:48.995 | INFO     | app.services.account_service:get_user_info:1128 - 获取用户信息: +***********
2025-06-06 23:53:48.995 | INFO     | core.telegram.user_manager:get_user_info:42 - 正在获取用户信息: +***********
2025-06-06 23:53:49.899 | INFO     | core.telegram.user_manager:get_user_info:67 - 获取用户信息成功: +***********
2025-06-06 23:53:50.243 | INFO     | app.controllers.account_controller:complete_login:502 - 自动保存h:\PyProject\TeleTest\APPDATA\sessions\+***********.session
2025-06-06 23:53:50.244 | INFO     | app.services.account_service:add_account:190 - 准备添加账户: +***********, 传入代理类型: ip_pool, 传入代理ID: 1
2025-06-06 23:53:50.244 | INFO     | app.services.account_service:add_account:218 - 规范化后添加账户: +***********, 数据库代理类型: ip_pool, 数据库代理ID: 1
2025-06-06 23:53:50.245 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-06 23:53:50.248 | INFO     | data.repositories.account_repo:create_account:287 - 创建账户成功: +***********, 代理类型: ip_pool
2025-06-06 23:53:50.264 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-06 23:53:50.266 | INFO     | app.controllers.account_controller:complete_login:516 - 账户添加成功: +***********
2025-06-06 23:53:50.266 | INFO     | ui.views.add_account_view:on_login_code_verified:299 - 保存成功
2025-06-06 23:53:50.270 | INFO     | ui.views.account_view:_on_account_added:256 - 账户添加成功: +***********
2025-06-06 23:53:50.275 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-06 23:53:50.283 | INFO     | app.services.account_service:get_all_accounts:289 - 获取所有账户成功, 共 1 个
2025-06-06 23:53:50.283 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-06 23:53:50.284 | DEBUG    | ui.views.account_view:_on_accounts_loaded:575 - 账户加载完成: 1个账户
2025-06-06 23:53:50.288 | INFO     | core.telegram.user_manager:get_user_info:67 - 获取用户信息成功: +***********
2025-06-06 23:53:50.298 | DEBUG    | ui.views.account_view:_update_group_counts:184 - 分组计数已更新: {2: 1}
2025-06-06 23:53:50.303 | INFO     | ui.views.account_view:_auto_login_accounts:630 - 开始调用控制器进行 1 个账户的自动登录。
2025-06-06 23:53:50.303 | INFO     | app.services.account_service:batch_auto_login:1153 - 服务层：开始批量自动登录 1 个账户, 最大并发: 5
2025-06-06 23:53:50.304 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-06 23:53:50.339 | INFO     | app.services.account_service:batch_auto_login:1192 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-06-06 23:53:50.339 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-06 23:53:50.340 | INFO     | app.services.account_service:batch_auto_login:1262 - 服务层：准备调用核心层处理 1 个账户的自动登录。
2025-06-06 23:53:50.341 | INFO     | core.telegram.client_manager:batch_auto_login:1220 - 开始批量自动登录, 共 1 个账户, 最大并发 5
2025-06-06 23:53:50.341 | INFO     | app.services.account_service:batch_auto_login:1272 - 服务层：设置核心层任务超时为 90 秒。
2025-06-06 23:53:51.006 | INFO     | app.controllers.account_controller:complete_login:502 - 自动保存h:\PyProject\TeleTest\APPDATA\sessions\+***********.session
2025-06-06 23:53:51.007 | INFO     | app.services.account_service:add_account:190 - 准备添加账户: +***********, 传入代理类型: ip_pool, 传入代理ID: 1
2025-06-06 23:53:51.007 | INFO     | app.services.account_service:add_account:218 - 规范化后添加账户: +***********, 数据库代理类型: ip_pool, 数据库代理ID: 1
2025-06-06 23:53:51.008 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-06 23:53:51.011 | WARNING  | data.repositories.account_repo:create_account:262 - 手机号已存在: +***********
2025-06-06 23:53:51.012 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-06 23:53:51.013 | ERROR    | app.controllers.account_controller:complete_login:519 - 账户添加失败: +***********, 原因: 手机号已存在或创建失败
2025-06-06 23:53:51.018 | ERROR    | ui.views.account_view:_on_account_added:262 - 账户添加失败: 手机号已存在或创建失败
2025-06-06 23:53:51.311 | INFO     | core.telegram.client_manager:_verify_client_authorization:1195 - 账户已授权: +***********
2025-06-06 23:53:51.311 | INFO     | core.telegram.client_manager:login_worker:1335 - 登录工作协程被取消
2025-06-06 23:53:51.312 | INFO     | core.telegram.client_manager:batch_auto_login:1369 - 批量登录完成: 总计 1, 成功 1, 失败 0
2025-06-06 23:53:51.585 | INFO     | app.services.account_service:batch_auto_login:1293 - 服务层：核心层登录完成: 成功 1, 失败 0 / 1
2025-06-06 23:54:09.646 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-06 23:54:09.647 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-06 23:55:09.647 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-06 23:55:09.647 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-06 23:55:21.418 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-06 23:55:21.466 | INFO     | app.services.account_service:get_all_accounts:289 - 获取所有账户成功, 共 1 个
2025-06-06 23:55:21.467 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-06 23:55:21.469 | DEBUG    | ui.views.account_view:_on_accounts_loaded:575 - 账户加载完成: 1个账户
2025-06-06 23:55:21.482 | DEBUG    | ui.views.account_view:_update_group_counts:184 - 分组计数已更新: {2: 1}
2025-06-06 23:55:23.131 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-06 23:55:23.289 | INFO     | app.services.account_service:get_all_groups:96 - 获取所有账户分组成功, 共 2 个
2025-06-06 23:55:23.290 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-06 23:55:23.291 | INFO     | ui.views.add_account_view:load_account_groups:94 - 成功加载 2 个分组
2025-06-06 23:55:24.185 | INFO     | ui.views.add_account_view:get_proxy_settings:114 - selected_items: []
2025-06-06 23:55:24.188 | INFO     | ui.views.add_account_view:get_proxy_settings:114 - selected_items: []
2025-06-06 23:55:24.198 | INFO     | app.services.account_service:get_proxy_ips:1032 - 获取有效代理IP列表（包含绑定计数）
2025-06-06 23:55:24.198 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-06 23:55:24.206 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-06 23:55:24.207 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-06 23:55:24.212 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-06 23:55:24.213 | INFO     | app.services.account_service:get_proxy_ips:1084 - 成功获取 1 个有效代理（已附带绑定计数）
2025-06-06 23:55:24.214 | INFO     | app.controllers.account_controller:get_proxy_ips:537 - 获取到1个有效代理IP
2025-06-06 23:55:26.568 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-06 23:55:26.635 | INFO     | app.services.account_service:get_all_accounts:289 - 获取所有账户成功, 共 1 个
2025-06-06 23:55:26.635 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-06 23:55:26.637 | DEBUG    | ui.views.account_view:_on_accounts_loaded:575 - 账户加载完成: 1个账户
2025-06-06 23:55:26.649 | DEBUG    | ui.views.account_view:_update_group_counts:184 - 分组计数已更新: {2: 1}
2025-06-06 23:55:31.611 | DEBUG    | ui.views.monitor_view:_on_task_item_clicked:258 - 任务项点击: 1
2025-06-06 23:55:31.615 | INFO     | app.controllers.telegram_monitor_controller:select_task:237 - 选择任务: 1
2025-06-06 23:55:31.615 | DEBUG    | ui.views.monitor_view:_on_task_selection_changed:281 - 任务选择变更: 1
2025-06-06 23:55:31.616 | DEBUG    | ui.views.monitor_view:_on_loading_started:620 - 加载中: 正在加载任务详情...
2025-06-06 23:55:31.616 | INFO     | app.controllers.telegram_monitor_controller:load_task_details:261 - 加载任务详情: ID 1
2025-06-06 23:55:31.616 | INFO     | app.services.monitor_service:get_task_detail:1018 - 获取任务详情: ID 1
2025-06-06 23:55:31.617 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-06 23:55:31.631 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-06 23:55:31.632 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-06 23:55:31.640 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-06 23:55:31.641 | DEBUG    | app.controllers.telegram_monitor_controller:load_task_details:271 - 获取到任务 1 的统计数据: {'total_users': 952, 'today_users': 0, 'avg_daily': 60, 'running_days': 16}
2025-06-06 23:55:31.643 | DEBUG    | ui.views.monitor_view:_on_task_details_loaded:309 - 任务详情加载完成: 1
2025-06-06 23:55:31.645 | DEBUG    | ui.views.monitor_view:_on_task_details_loaded:312 - 任务详情数据: {'created_at': '2025-05-21 01:05:08', 'description': '', 'id': '1', 'ignore_keywords': [], 'ignore_nicknames_rules': [], 'is_active': True, 'is_running': False, 'keywords': [], 'last_error': "所有账户监控设置均失败: object tuple can't be used in 'await' expression, object tuple can't be used in 'await' expression", 'monitor_messages': True, 'monitor_new_users': False, 'monitored_chats': [{'account_phone': '***********', 'chat_id': '**********', 'chat_title': '泰国华人交友聊天总群', 'id': 32}, {'account_phone': '***********', 'chat_id': '**********', 'chat_title': '东南亚博彩代理聊天群', 'id': 33}, {'account_phone': '***********', 'chat_id': '**********', 'chat_title': '柬埔寨泰国华人聊天群', 'id': 34}, {'account_phone': '***********', 'chat_id': '**********', 'chat_title': '赚钱项目灰产社区', 'id': 35}, {'account_phone': '***********', 'chat_id': '**********', 'chat_title': '区块节点NodeMining-Pool', 'id': 36}, {'account_phone': '***********', 'chat_id': '**********', 'chat_title': '区块节点NodeMining-Pool', 'id': 37}, {'account_phone': '***********', 'chat_id': '**********', 'chat_title': '深圳集团 - 藏楼（聊天', 'id': 38}, {'account_phone': '***********', 'chat_id': '**********', 'chat_title': '广州会所资源_(部分预览)', 'id': 39}, {'account_phone': '***********', 'chat_id': '**********', 'chat_title': '🔥入金话术馆', 'id': 40}, {'account_phone': '***********', 'chat_id': '**********', 'chat_title': 'wiegnwi', 'id': 41}, {'account_phone': '***********', 'chat_id': '**********', 'chat_title': '国外 话术 素材总部', 'id': 42}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '💡站长群 HostCLi.com 宝塔纯净版', 'id': 43}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '云泽社区', 'id': 44}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '🍒 萝莉屋18岁 资源群🦄', 'id': 45}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '柯基币-礦池-官方社区', 'id': 46}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '🈲 高中 学生资源库（加密）🎀', 'id': 47}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '🅰️中文搜片搜索👙自慰🩸抠逼🔍乱伦🗣口交', 'id': 48}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '中文算力MinerDog pool', 'id': 49}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '【官方】币安矿池置换中文群🇨🇳', 'id': 50}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '香港服务器交流群', 'id': 51}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '免费情色视频搜索🚀', 'id': 52}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '迷🦊 吃瓜 完整版破案‼️', 'id': 53}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '软件开发🪸搭建网站🪸网站开发', 'id': 54}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '国际联盟—海外实卡接码/邮寄公开群【支持担保】', 'id': 55}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': 'wiegnwi', 'id': 56}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '嘿嘿嘿', 'id': 57}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '泡妞笔记', 'id': 58}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '仙楼集团·资源发布站 @xianlou', 'id': 59}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '路易频道引流推广工作群', 'id': 60}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '神奇宝贝 @so588', 'id': 61}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': 'TGbox助手|群发|拉人|引流|群发助手', 'id': 62}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '比特币购买，比特币行情，比特币怎么买', 'id': 63}], 'name': '全部群组', 'stats': {'avg_daily': 60, 'running_days': 16, 'today_users': 0, 'total_users': 952}, 'updated_at': '2025-05-26 16:29:16'}
2025-06-06 23:55:31.649 | DEBUG    | ui.views.monitor_view:_on_task_details_loaded:316 - 统计数据: {'avg_daily': 60, 'running_days': 16, 'today_users': 0, 'total_users': 952}
2025-06-06 23:55:31.651 | DEBUG    | ui.views.monitor_view:_on_loading_started:620 - 加载中: 正在加载第 1 页数据...
2025-06-06 23:55:31.651 | INFO     | app.controllers.telegram_monitor_controller:load_task_users:396 - 加载任务用户数据: ID 1, 页码 1, 每页 10 条
2025-06-06 23:55:31.652 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-06 23:55:31.663 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-06 23:55:31.664 | INFO     | ui.views.monitor_view:_update_users_table:342 - 开始更新用户表格，收到10条数据
2025-06-06 23:55:31.665 | DEBUG    | ui.views.monitor_view:_update_users_table:343 - 分页信息：{'current_page': 1, 'page_size': 10, 'total_items': 967, 'total_pages': 97}
2025-06-06 23:55:31.665 | INFO     | ui.views.monitor_view:_update_users_table:360 - 示例数据[0]: {'join_date': '2025-05-29 17:15:39', 'keyword': '', 'nickname': 'san hejiu', 'task_type': 'text', 'uid': '7690383982', 'username': ''}
2025-06-06 23:55:31.666 | INFO     | ui.views.monitor_view:_update_users_table:360 - 示例数据[1]: {'join_date': '2025-05-29 17:15:26', 'keyword': '', 'nickname': '西港外送佳佳/打炮按摩 None', 'task_type': 'text', 'uid': '7991181298', 'username': ''}
2025-06-06 23:55:31.666 | INFO     | ui.views.monitor_view:_update_users_table:360 - 示例数据[2]: {'join_date': '2025-05-29 17:15:07', 'keyword': '', 'nickname': '黑子 None', 'task_type': 'text', 'uid': '6654528704', 'username': 'BqZ8D3cFkOy7p4'}
2025-06-06 23:55:31.666 | INFO     | ui.views.monitor_view:_update_users_table:364 - 设置表格行数: 10
2025-06-06 23:55:31.676 | INFO     | ui.views.monitor_view:_update_users_table:388 - 用户表格更新完成
2025-06-06 23:55:31.677 | INFO     | app.controllers.telegram_monitor_controller:load_task_users:423 - 成功加载第 1 页用户数据，共 10 条
2025-06-06 23:55:31.679 | DEBUG    | ui.views.monitor_view:_on_loading_finished:625 - 加载完成
2025-06-06 23:55:31.679 | INFO     | app.controllers.telegram_monitor_controller:load_task_details:287 - 成功加载任务详情: 全部群组
2025-06-06 23:55:31.680 | DEBUG    | ui.views.monitor_view:_on_loading_finished:625 - 加载完成
2025-06-06 23:56:09.647 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-06 23:56:09.647 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-06 23:57:09.647 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-06 23:57:09.647 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-06 23:58:09.648 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-06 23:58:09.649 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-06 23:58:39.335 | INFO     | ui.views.monitor_view:open_add_task_dialog:135 - 打开添加任务对话框 
2025-06-06 23:58:39.440 | INFO     | ui.views.add_monitor_view:_load_initial_data:111 - 加载初始数据
2025-06-06 23:58:39.509 | INFO     | ui.views.add_monitor_view:_load_account_groups:400 - 加载账户分组
2025-06-06 23:58:39.509 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-06 23:58:39.510 | INFO     | ui.views.add_monitor_view:_load_accounts_by_group:417 - 根据分组ID加载账户: -1
2025-06-06 23:58:39.510 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-06 23:58:39.701 | INFO     | app.services.account_service:get_all_accounts:289 - 获取所有账户成功, 共 1 个
2025-06-06 23:58:39.702 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-06 23:58:39.706 | INFO     | ui.views.add_monitor_view:_load_accounts_by_group:442 - 已加载 1 个账户到列表
2025-06-06 23:58:39.707 | DEBUG    | ui.views.account_view:_on_accounts_loaded:575 - 账户加载完成: 1个账户
2025-06-06 23:58:39.719 | DEBUG    | ui.views.account_view:_update_group_counts:184 - 分组计数已更新: {2: 1}
2025-06-06 23:58:39.734 | INFO     | app.services.account_service:get_all_groups:96 - 获取所有账户分组成功, 共 2 个
2025-06-06 23:58:39.735 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-06 23:58:39.738 | DEBUG    | ui.views.account_view:_on_groups_loaded:442 - 分组加载完成: 2个分组
2025-06-06 23:58:39.739 | INFO     | ui.views.add_monitor_view:_on_group_index_changed:460 - 当前分组id:-1
2025-06-06 23:58:39.739 | INFO     | ui.views.add_monitor_view:_load_account_groups:409 - 加载账户分组数据: [{'id': 2, 'name': '主账户', 'description': '', 'account_count': 1}, {'id': 1, 'name': '营销', 'description': '', 'account_count': 0}]
2025-06-06 23:58:39.741 | INFO     | ui.views.add_monitor_view:_load_accounts_by_group:417 - 根据分组ID加载账户: -1
2025-06-06 23:58:39.741 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-06 23:58:39.747 | INFO     | app.services.account_service:get_all_accounts:289 - 获取所有账户成功, 共 1 个
2025-06-06 23:58:39.747 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-06 23:58:39.749 | INFO     | ui.views.add_monitor_view:_load_accounts_by_group:442 - 已加载 1 个账户到列表
2025-06-06 23:58:39.750 | DEBUG    | ui.views.account_view:_on_accounts_loaded:575 - 账户加载完成: 1个账户
2025-06-06 23:58:39.761 | DEBUG    | ui.views.account_view:_update_group_counts:184 - 分组计数已更新: {2: 1}
2025-06-06 23:59:09.648 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-06 23:59:09.648 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-07 00:00:09.648 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-07 00:00:09.648 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-07 00:01:09.646 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-07 00:01:09.646 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-07 00:02:09.651 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-07 00:02:09.651 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-07 00:03:09.646 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-07 00:03:09.646 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-07 00:04:09.648 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-07 00:04:09.649 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-07 00:05:09.648 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-07 00:05:09.649 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-07 00:06:09.646 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-07 00:06:09.647 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-07 00:07:09.651 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-07 00:07:09.651 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-07 00:08:09.647 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-07 00:08:09.648 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-07 00:09:09.648 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-07 00:09:09.648 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-07 00:10:09.650 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-07 00:10:09.651 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-07 00:11:09.652 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-07 00:11:09.652 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-07 00:12:09.652 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-07 00:12:09.652 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-07 00:13:09.650 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-07 00:13:09.650 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-07 00:14:09.647 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-07 00:14:09.647 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-07 00:15:09.653 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-07 00:15:09.653 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-07 00:16:09.646 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-07 00:16:09.647 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-07 00:17:09.646 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-07 00:17:09.647 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-07 00:18:09.648 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-07 00:18:09.648 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-07 00:19:09.646 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-07 00:19:09.646 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-07 00:20:09.652 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-07 00:20:09.652 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-07 00:21:09.647 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-07 00:21:09.648 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-07 00:22:09.650 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-07 00:22:09.650 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-07 00:23:09.646 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-07 00:23:09.646 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-07 00:24:09.648 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-07 00:24:09.648 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-07 00:25:09.646 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-07 00:25:09.647 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-07 00:26:09.646 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-07 00:26:09.647 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-07 00:27:09.648 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-07 00:27:09.648 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-07 00:28:09.650 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-07 00:28:09.650 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-07 00:29:09.649 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-07 00:29:09.649 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-07 00:30:09.648 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-07 00:30:09.648 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-07 00:30:52.478 | INFO     | ui.main_window:closeEvent:335 - MainWindow: 接收到关闭事件
2025-06-07 00:30:52.493 | INFO     | ui.main_window:_cleanup_before_quit:228 - MainWindow: 执行清理资源...
2025-06-07 00:30:52.493 | INFO     | core.telegram.client_worker:stop:110 - 正在停止Telegram客户端工作线程
2025-06-07 00:30:52.532 | INFO     | core.telegram.client_manager:cleanup_async:1140 - 开始清理资源并断开所有连接
2025-06-07 00:30:52.532 | INFO     | core.telegram.client_manager:disconnect_all_clients:725 - 正在断开所有客户端连接
2025-06-07 00:30:52.532 | INFO     | core.telegram.client_manager:_safe_disconnect:746 - 正在断开客户端连接: +***********
2025-06-07 00:30:52.533 | INFO     | core.telegram.client_manager:_safe_disconnect:752 - 客户端已断开连接: +***********
2025-06-07 00:30:52.533 | INFO     | core.telegram.client_manager:_safe_disconnect:746 - 正在断开客户端连接: +***********
2025-06-07 00:30:52.538 | INFO     | core.telegram.client_manager:_safe_disconnect:750 - 断开客户端连接成功: +***********
2025-06-07 00:30:52.539 | INFO     | core.telegram.client_manager:cleanup_async:1159 - 资源清理完成
2025-06-07 00:30:53.026 | INFO     | core.telegram.client_manager:cleanup_async:1140 - 开始清理资源并断开所有连接
2025-06-07 00:30:53.026 | INFO     | core.telegram.client_manager:disconnect_all_clients:725 - 正在断开所有客户端连接
2025-06-07 00:30:53.027 | INFO     | core.telegram.client_manager:cleanup_async:1159 - 资源清理完成
2025-06-07 00:30:53.527 | INFO     | core.telegram.client_worker:run:99 - Telegram客户端工作线程已退出
2025-06-07 00:30:53.528 | INFO     | core.telegram.client_worker:stop:124 - Telegram客户端工作线程已停止
2025-06-07 00:30:53.529 | INFO     | ui.main_window:_cleanup_before_quit:237 - TelegramClientWorker 已停止。
2025-06-07 00:30:53.529 | INFO     | ui.main_window:_cleanup_before_quit:241 - MainWindow 清理完成
2025-06-07 00:30:53.541 | INFO     | __main__:main:111 - 应用程序已正常退出
