2025-06-09 16:21:11.177 | INFO     | utils.logger:init_logger:98 - 日志系统初始化完成，默认级别: DEBUG，日志目录: h:\PyProject\TeleTest\APPDATA\logs
2025-06-09 16:21:14.752 | INFO     | data.database:init_database:31 - 初始化数据库连接: h:\PyProject\TeleTest\APPDATA\database\telegram_manager.db
2025-06-09 16:21:14.842 | DEBUG    | data.database:init_database:58 - 创建数据库表
2025-06-09 16:21:14.854 | INFO     | data.database:init_database:61 - 数据库初始化完成
2025-06-09 16:21:16.001 | DEBUG    | app.controllers.auth_controller:initialize_software:43 - 控制器: 初始化软件 app_id=1000, version=1.0
2025-06-09 16:21:16.002 | DEBUG    | utils.client_http:get:54 - 发送GET请求: https://server.xile188.com/api/user/1000/windowns10/1.0/ini
2025-06-09 16:21:16.360 | DEBUG    | core.auth.api_service:init_software:66 - 初始化数据解密成功: {'new_content': '1.多账户管理\n2.ip池管理\n3.消息监听\n4.消息群发\n5.拉人入群', 'new_url': None, 'notice': {'id': '2', 'content': '公告内容测试3', 'time': '**********'}}
2025-06-09 16:21:16.367 | INFO     | app.controllers.auth_controller:login:61 - 控制器: 用户登录 account=demo01
2025-06-09 16:21:19.469 | INFO     | core.auth.api_service:login:127 - 用户登录: account=demo01
2025-06-09 16:21:19.470 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/logon
2025-06-09 16:21:19.764 | INFO     | core.auth.api_service:get_user_info:347 - 获取用户信息
2025-06-09 16:21:19.765 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/info
2025-06-09 16:21:20.023 | INFO     | core.auth.api_service:get_user_info:375 - 获取用户信息成功
2025-06-09 16:21:20.030 | INFO     | __main__:on_auth_completed:93 - 用户登录成功，正在启动主窗口...
2025-06-09 16:21:20.050 | INFO     | core.telegram.client_worker:__init__:63 - Telegram客户端工作线程初始化
2025-06-09 16:21:20.055 | INFO     | core.telegram.client_worker:run:67 - Telegram客户端工作线程开始运行
2025-06-09 16:21:20.057 | INFO     | core.telegram.client_manager:__init__:66 - Telegram客户端管理器初始化
2025-06-09 16:21:20.058 | INFO     | core.telegram.user_manager:__init__:31 - Telegram用户管理器初始化
2025-06-09 16:21:20.059 | INFO     | core.telegram.group_manager:__init__:36 - Telegram群组管理器初始化
2025-06-09 16:21:20.059 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-06-09 16:21:20.059 | INFO     | core.telegram.monitor_manager:__init__:26 - Telegram监控管理器初始化
2025-06-09 16:21:20.060 | INFO     | core.telegram.telegram_service:__init__:39 - Telegram服务初始化完成
2025-06-09 16:21:20.062 | INFO     | ui.main_window:_initialize_core_components:79 - MainWindow: 初始化核心组件...
2025-06-09 16:21:20.063 | INFO     | app.services.account_service:__init__:42 - 账户服务初始化
2025-06-09 16:21:20.063 | INFO     | core.proxy.proxy_task_manager:run:284 - 代理任务管理器线程启动
2025-06-09 16:21:20.064 | INFO     | app.controllers.account_controller:__init__:81 - 账户控制器初始化
2025-06-09 16:21:20.064 | INFO     | app.services.monitor_service:__init__:39 - 初始化监控任务服务
2025-06-09 16:21:20.065 | INFO     | core.monitor.filter:__init__:26 - 初始化消息过滤器
2025-06-09 16:21:20.065 | INFO     | core.monitor.extractor:__init__:24 - 初始化关键词提取器
2025-06-09 16:21:20.066 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-06-09 16:21:20.066 | INFO     | core.monitor.notifier:__init__:90 - 初始化通知管理器
2025-06-09 16:21:20.067 | INFO     | app.controllers.telegram_monitor_controller:__init__:64 - 初始化 Telegram 监控控制器
2025-06-09 16:21:20.067 | DEBUG    | app.controllers.telegram_monitor_controller:__init__:82 - 控制器初始化完成
2025-06-09 16:21:20.282 | INFO     | ui.views.monitor_view:__init__:48 - 初始化监控视图
2025-06-09 16:21:20.282 | DEBUG    | ui.views.monitor_view:_connect_signals:80 - 连接控制器信号
2025-06-09 16:21:20.478 | INFO     | ui.views.proxy_view:__init__:96 - 初始化视图，加载第一页代理数据
2025-06-09 16:21:20.729 | INFO     | __main__:on_auth_completed:97 - 主窗口已启动
2025-06-09 16:21:20.801 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:88 - 尝试连接监听日志信号...
2025-06-09 16:21:20.802 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:90 - 监听日志信号连接成功
2025-06-09 16:21:20.802 | INFO     | core.auth.api_service:verify_vip:425 - 会员验证接口调用
2025-06-09 16:21:20.803 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/vip
2025-06-09 16:21:20.804 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-09 16:21:20.808 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-09 16:21:20.811 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:145 - 加载监控任务列表 (搜索关键词: '')
2025-06-09 16:21:20.811 | DEBUG    | ui.views.monitor_view:_on_loading_started:620 - 加载中: 正在加载任务列表...
2025-06-09 16:21:20.812 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-09 16:21:20.818 | INFO     | app.services.proxy_service:refresh_proxies:712 - 刷新代理列表: offset=0, limit=20
2025-06-09 16:21:20.818 | INFO     | core.proxy.proxy_task_manager:submit_task:172 - 提交耗时任务到子线程: refresh_proxies_0_20
2025-06-09 16:21:20.819 | DEBUG    | app.services.proxy_service:get_proxies:500 - 获取代理列表: offset=0, limit=20
2025-06-09 16:21:20.819 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-09 16:21:20.820 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-09 16:21:20.825 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-09 16:21:20.856 | INFO     | app.controllers.invite_controller:get_invite_tasks:38 - 获取邀请任务列表, status=None
2025-06-09 16:21:20.857 | INFO     | app.services.invite_service:get_invite_tasks:121 - 获取邀请任务列表, status=None
2025-06-09 16:21:20.860 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-09 16:21:20.862 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-09 16:21:20.862 | INFO     | app.services.invite_service:task_initialize:39 - 初始化邀请服务，重置运行中的任务为暂停状态
2025-06-09 16:21:20.863 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-09 16:21:20.864 | INFO     | core.proxy.proxy_task_manager:task_wrapper:199 - 任务 refresh_proxies_0_20 完成
2025-06-09 16:21:20.968 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-09 16:21:20.970 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-09 16:21:20.977 | INFO     | ui.main_window:start_proxy_service:134 - 检测到本地代理，自动启动代理服务...
2025-06-09 16:21:20.984 | DEBUG    | ui.views.monitor_view:_refresh_task_list:175 - 刷新任务列表，共2个任务
2025-06-09 16:21:20.991 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-09 16:21:20.993 | INFO     | app.services.invite_service:task_initialize:48 - 成功重置 0 个任务状态
2025-06-09 16:21:20.993 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-09 16:21:21.002 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-09 16:21:21.008 | INFO     | app.services.account_service:get_all_groups:96 - 获取所有账户分组成功, 共 2 个
2025-06-09 16:21:21.009 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-09 16:21:21.011 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-09 16:21:21.017 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=True
2025-06-09 16:21:21.020 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-09 16:21:21.021 | DEBUG    | ui.views.account_view:_on_groups_loaded:442 - 分组加载完成: 2个分组
2025-06-09 16:21:21.040 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-09 16:21:21.112 | INFO     | app.services.account_service:get_all_accounts:289 - 获取所有账户成功, 共 1 个
2025-06-09 16:21:21.113 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-09 16:21:21.114 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-09 16:21:21.116 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-09 16:21:21.117 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-09 16:21:21.118 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-09 16:21:21.120 | INFO     | app.controllers.telegram_monitor_controller:_load_total_users_count_internal:191 - 成功加载总体统计数据: 总用户数 958, 今日采集 0, 日均采集 50, 运行天数 19
2025-06-09 16:21:21.121 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:156 - 成功加载 2 个任务
2025-06-09 16:21:21.122 | DEBUG    | ui.views.monitor_view:_on_loading_finished:625 - 加载完成
2025-06-09 16:21:21.122 | DEBUG    | ui.views.account_view:_on_accounts_loaded:575 - 账户加载完成: 1个账户
2025-06-09 16:21:21.136 | DEBUG    | ui.views.account_view:_update_group_counts:184 - 分组计数已更新: {2: 1}
2025-06-09 16:21:21.141 | INFO     | ui.views.account_view:_auto_login_accounts:630 - 开始调用控制器进行 1 个账户的自动登录。
2025-06-09 16:21:21.141 | INFO     | app.services.account_service:batch_auto_login:1153 - 服务层：开始批量自动登录 1 个账户, 最大并发: 5
2025-06-09 16:21:21.142 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-09 16:21:21.147 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=True
2025-06-09 16:21:21.177 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-09 16:21:21.188 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=True
2025-06-09 16:21:21.191 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=True
2025-06-09 16:21:21.194 | INFO     | ui.views.proxy_view:_load_initial_data:1044 - 初始数据加载完成，共 27 个代理
2025-06-09 16:21:21.196 | INFO     | app.services.account_service:get_all_accounts:289 - 获取所有账户成功, 共 1 个
2025-06-09 16:21:21.196 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-09 16:21:21.198 | INFO     | app.services.account_service:batch_auto_login:1192 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-06-09 16:21:21.198 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-09 16:21:21.200 | DEBUG    | ui.views.account_view:_update_group_counts:184 - 分组计数已更新: {2: 1}
2025-06-09 16:21:21.201 | DEBUG    | ui.views.account_view:_init_cache:151 - 账户缓存初始化完成，共 1 个账户
2025-06-09 16:21:21.206 | DEBUG    | ui.views.account_view:_on_accounts_loaded:575 - 账户加载完成: 1个账户
2025-06-09 16:21:21.221 | DEBUG    | ui.views.account_view:_update_group_counts:184 - 分组计数已更新: {2: 1}
2025-06-09 16:21:21.229 | INFO     | app.services.account_service:batch_auto_login:1262 - 服务层：准备调用核心层处理 1 个账户的自动登录。
2025-06-09 16:21:21.229 | INFO     | app.services.account_service:batch_auto_login:1272 - 服务层：设置核心层任务超时为 90 秒。
2025-06-09 16:21:21.230 | INFO     | core.telegram.client_manager:batch_auto_login:1220 - 开始批量自动登录, 共 1 个账户, 最大并发 5
2025-06-09 16:21:21.230 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:256 - 创建客户端: h:\PyProject\TeleTest\APPDATA\sessions\+***********.session, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-06-09 16:21:21.232 | DEBUG    | core.telegram.client_manager:get_proxy:93 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-06-09 16:21:21.232 | DEBUG    | core.telegram.client_manager:get_proxy:140 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-06-09 16:21:21.243 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-09 16:21:21.251 | WARNING  | core.auth.api_service:verify_vip:441 - 会员验证失败: 验证失败
2025-06-09 16:21:25.804 | INFO     | core.telegram.client_manager:_connect_client:204 - 连接成功!
2025-06-09 16:21:26.751 | INFO     | core.telegram.client_manager:_verify_client_authorization:1195 - 账户已授权: +***********
2025-06-09 16:21:28.754 | INFO     | core.telegram.client_manager:batch_auto_login:1369 - 批量登录完成: 总计 1, 成功 1, 失败 0
2025-06-09 16:21:29.270 | INFO     | app.services.account_service:batch_auto_login:1293 - 服务层：核心层登录完成: 成功 1, 失败 0 / 1
2025-06-09 16:22:20.026 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-09 16:22:20.027 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-09 16:23:20.024 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-09 16:23:20.024 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-09 16:24:20.025 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-09 16:24:20.026 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-09 16:25:20.022 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-09 16:25:20.023 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-09 16:26:20.024 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-09 16:26:20.024 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-09 16:27:20.024 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-09 16:27:20.024 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-09 16:28:20.025 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-09 16:28:20.026 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-09 16:29:20.024 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-09 16:29:20.025 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-09 16:30:20.028 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-09 16:30:20.029 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-09 16:31:20.030 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-09 16:31:20.031 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-09 16:32:20.030 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-09 16:32:20.031 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-09 16:33:20.030 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-09 16:33:20.030 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-09 16:34:20.030 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-09 16:34:20.031 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-09 16:35:20.028 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-09 16:35:20.029 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-09 16:36:20.030 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-09 16:36:20.030 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-09 16:37:20.029 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-09 16:37:20.029 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-09 16:38:20.023 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-09 16:38:20.024 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-09 16:39:20.026 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-09 16:39:20.026 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-09 16:40:20.022 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-09 16:40:20.022 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-09 16:41:20.025 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-09 16:41:20.025 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-09 16:42:20.022 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-09 16:42:20.022 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-09 16:43:20.024 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-09 16:43:20.024 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-09 16:44:20.022 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-09 16:44:20.023 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-09 16:45:20.025 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-09 16:45:20.026 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-09 16:46:20.024 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-09 16:46:20.025 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-09 16:47:20.026 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-09 16:47:20.026 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-09 16:48:20.024 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-09 16:48:20.025 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-09 16:49:20.022 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-09 16:49:20.022 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-09 16:50:20.023 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-09 16:50:20.023 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-09 16:51:20.025 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-09 16:51:20.026 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-09 16:52:20.022 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-09 16:52:20.022 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-09 16:53:20.024 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-09 16:53:20.025 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-09 16:54:20.024 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-09 16:54:20.025 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-09 16:55:20.023 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-09 16:55:20.023 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-09 16:56:20.023 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-09 16:56:20.023 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-09 16:57:20.026 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-09 16:57:20.026 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-09 16:58:20.031 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-09 16:58:20.031 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-09 16:59:20.027 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-09 16:59:20.028 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-09 17:00:20.030 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-09 17:00:20.030 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-09 17:01:20.023 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-09 17:01:20.023 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-09 17:02:20.030 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-09 17:02:20.031 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-09 17:03:20.028 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-09 17:03:20.029 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-09 17:04:20.021 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-09 17:04:20.022 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-09 17:05:20.031 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-09 17:05:20.031 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-09 17:06:20.023 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-09 17:06:20.023 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-09 17:07:20.029 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-09 17:07:20.029 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-09 17:08:20.031 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-09 17:08:20.032 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-09 17:09:20.026 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-09 17:09:20.026 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-09 17:10:20.030 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-09 17:10:20.030 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-09 17:11:20.028 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-09 17:11:20.028 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-09 17:12:20.021 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-09 17:12:20.022 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-09 17:13:20.029 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-09 17:13:20.029 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-09 17:14:20.027 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-09 17:14:20.027 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-09 17:15:20.022 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-09 17:15:20.023 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-09 17:16:20.027 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-09 17:16:20.027 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-09 17:17:20.027 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-09 17:17:20.027 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-09 17:18:20.022 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-09 17:18:20.023 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-09 17:19:20.030 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-09 17:19:20.031 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-09 17:20:20.027 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-09 17:20:20.027 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-09 17:21:20.028 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-09 17:21:20.029 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-09 17:22:20.026 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-09 17:22:20.026 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-09 17:23:20.023 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-09 17:23:20.023 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-09 17:24:20.024 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-09 17:24:20.024 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-09 17:25:20.023 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-09 17:25:20.023 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-09 17:26:20.023 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-09 17:26:20.023 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-09 17:27:20.025 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-09 17:27:20.025 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-09 17:28:20.022 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-09 17:28:20.022 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-09 17:29:20.026 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-09 17:29:20.026 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-09 17:30:20.024 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-09 17:30:20.024 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-09 17:31:20.023 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-09 17:31:20.023 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-09 17:32:20.027 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-09 17:32:20.027 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-09 17:33:20.025 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-09 17:33:20.025 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-09 17:34:20.025 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-09 17:34:20.025 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-09 17:35:20.025 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-09 17:35:20.025 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-09 17:36:20.026 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-09 17:36:20.026 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-09 17:37:20.023 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-09 17:37:20.023 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-09 17:38:20.029 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-09 17:38:20.029 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-09 17:39:20.021 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-09 17:39:20.022 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-09 17:39:35.386 | INFO     | ui.main_window:closeEvent:335 - MainWindow: 接收到关闭事件
2025-06-09 17:39:35.397 | INFO     | ui.main_window:_cleanup_before_quit:228 - MainWindow: 执行清理资源...
2025-06-09 17:39:35.398 | INFO     | core.telegram.client_worker:stop:110 - 正在停止Telegram客户端工作线程
2025-06-09 17:39:35.406 | INFO     | core.telegram.client_manager:cleanup_async:1140 - 开始清理资源并断开所有连接
2025-06-09 17:39:35.407 | INFO     | core.telegram.client_manager:disconnect_all_clients:725 - 正在断开所有客户端连接
2025-06-09 17:39:35.407 | INFO     | core.telegram.client_manager:_safe_disconnect:746 - 正在断开客户端连接: +***********
2025-06-09 17:39:35.415 | INFO     | core.telegram.client_manager:_safe_disconnect:750 - 断开客户端连接成功: +***********
2025-06-09 17:39:35.415 | INFO     | core.telegram.client_manager:cleanup_async:1159 - 资源清理完成
2025-06-09 17:39:35.901 | INFO     | core.telegram.client_manager:cleanup_async:1140 - 开始清理资源并断开所有连接
2025-06-09 17:39:35.901 | INFO     | core.telegram.client_manager:disconnect_all_clients:725 - 正在断开所有客户端连接
2025-06-09 17:39:35.902 | INFO     | core.telegram.client_manager:cleanup_async:1159 - 资源清理完成
2025-06-09 17:39:36.403 | INFO     | core.telegram.client_worker:run:99 - Telegram客户端工作线程已退出
2025-06-09 17:39:36.404 | INFO     | core.telegram.client_worker:stop:124 - Telegram客户端工作线程已停止
2025-06-09 17:39:36.405 | INFO     | ui.main_window:_cleanup_before_quit:237 - TelegramClientWorker 已停止。
2025-06-09 17:39:36.405 | INFO     | ui.main_window:_cleanup_before_quit:241 - MainWindow 清理完成
2025-06-09 17:39:36.416 | INFO     | __main__:main:111 - 应用程序已正常退出
