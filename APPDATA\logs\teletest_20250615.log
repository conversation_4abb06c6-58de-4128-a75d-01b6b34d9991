2025-06-15 12:25:54.617 | INFO     | utils.logger:init_logger:98 - 日志系统初始化完成，默认级别: DEBUG，日志目录: h:\PyProject\TeleTest\APPDATA\logs
2025-06-15 12:25:58.003 | INFO     | data.database:init_database:31 - 初始化数据库连接: h:\PyProject\TeleTest\APPDATA\database\telegram_manager.db
2025-06-15 12:25:58.033 | DEBUG    | data.database:init_database:58 - 创建数据库表
2025-06-15 12:25:58.049 | INFO     | data.database:init_database:61 - 数据库初始化完成
2025-06-15 12:25:59.940 | DEBUG    | app.controllers.auth_controller:initialize_software:43 - 控制器: 初始化软件 app_id=1000, version=1.0
2025-06-15 12:25:59.940 | DEBUG    | utils.client_http:get:54 - 发送GET请求: https://server.xile188.com/api/user/1000/windowns10/1.0/ini
2025-06-15 12:26:00.700 | DEBUG    | core.auth.api_service:init_software:66 - 初始化数据解密成功: {'new_content': '1.多账户管理\n2.ip池管理\n3.消息监听\n4.消息群发\n5.拉人入群', 'new_url': None, 'notice': {'id': '2', 'content': '公告内容测试3', 'time': '**********'}}
2025-06-15 12:26:00.721 | INFO     | app.controllers.auth_controller:login:61 - 控制器: 用户登录 account=demo01
2025-06-15 12:26:04.286 | INFO     | core.auth.api_service:login:127 - 用户登录: account=demo01
2025-06-15 12:26:04.286 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/logon
2025-06-15 12:26:04.586 | INFO     | core.auth.api_service:get_user_info:347 - 获取用户信息
2025-06-15 12:26:04.587 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/info
2025-06-15 12:26:04.796 | INFO     | core.auth.api_service:get_user_info:375 - 获取用户信息成功
2025-06-15 12:26:04.804 | INFO     | __main__:on_auth_completed:93 - 用户登录成功，正在启动主窗口...
2025-06-15 12:26:04.838 | INFO     | core.telegram.client_worker:__init__:63 - Telegram客户端工作线程初始化
2025-06-15 12:26:04.846 | INFO     | core.telegram.client_worker:run:67 - Telegram客户端工作线程开始运行
2025-06-15 12:26:04.851 | INFO     | core.telegram.client_manager:__init__:66 - Telegram客户端管理器初始化
2025-06-15 12:26:04.853 | INFO     | core.telegram.user_manager:__init__:31 - Telegram用户管理器初始化
2025-06-15 12:26:04.856 | INFO     | core.telegram.group_manager:__init__:36 - Telegram群组管理器初始化
2025-06-15 12:26:04.858 | INFO     | ui.main_window:_initialize_core_components:80 - MainWindow: 初始化核心组件...
2025-06-15 12:26:04.859 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-06-15 12:26:04.861 | INFO     | core.telegram.monitor_manager:__init__:26 - Telegram监控管理器初始化
2025-06-15 12:26:04.862 | INFO     | app.services.account_service:__init__:42 - 账户服务初始化
2025-06-15 12:26:04.862 | INFO     | core.telegram.telegram_service:__init__:39 - Telegram服务初始化完成
2025-06-15 12:26:04.863 | INFO     | app.controllers.account_controller:__init__:81 - 账户控制器初始化
2025-06-15 12:26:04.863 | INFO     | core.proxy.proxy_task_manager:run:284 - 代理任务管理器线程启动
2025-06-15 12:26:04.864 | INFO     | app.services.monitor_service:__init__:39 - 初始化监控任务服务
2025-06-15 12:26:04.865 | INFO     | core.monitor.filter:__init__:26 - 初始化消息过滤器
2025-06-15 12:26:04.866 | INFO     | core.monitor.extractor:__init__:24 - 初始化关键词提取器
2025-06-15 12:26:04.866 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-06-15 12:26:04.867 | INFO     | core.monitor.notifier:__init__:90 - 初始化通知管理器
2025-06-15 12:26:04.867 | INFO     | app.controllers.telegram_monitor_controller:__init__:64 - 初始化 Telegram 监控控制器
2025-06-15 12:26:04.868 | DEBUG    | app.controllers.telegram_monitor_controller:__init__:82 - 控制器初始化完成
2025-06-15 12:26:05.183 | INFO     | ui.views.monitor_view:__init__:48 - 初始化监控视图
2025-06-15 12:26:05.184 | DEBUG    | ui.views.monitor_view:_connect_signals:80 - 连接控制器信号
2025-06-15 12:26:05.434 | INFO     | ui.views.proxy_view:__init__:96 - 初始化视图，加载第一页代理数据
2025-06-15 12:26:05.718 | INFO     | __main__:on_auth_completed:97 - 主窗口已启动
2025-06-15 12:26:05.812 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:88 - 尝试连接监听日志信号...
2025-06-15 12:26:05.812 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:90 - 监听日志信号连接成功
2025-06-15 12:26:05.813 | INFO     | core.auth.api_service:verify_vip:425 - 会员验证接口调用
2025-06-15 12:26:05.813 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/vip
2025-06-15 12:26:05.814 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-15 12:26:05.832 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-15 12:26:05.838 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:145 - 加载监控任务列表 (搜索关键词: '')
2025-06-15 12:26:05.839 | DEBUG    | ui.views.monitor_view:_on_loading_started:620 - 加载中: 正在加载任务列表...
2025-06-15 12:26:05.842 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-15 12:26:05.850 | INFO     | app.services.proxy_service:refresh_proxies:712 - 刷新代理列表: offset=0, limit=20
2025-06-15 12:26:05.850 | INFO     | core.proxy.proxy_task_manager:submit_task:172 - 提交耗时任务到子线程: refresh_proxies_0_20
2025-06-15 12:26:05.851 | DEBUG    | app.services.proxy_service:get_proxies:500 - 获取代理列表: offset=0, limit=20
2025-06-15 12:26:05.851 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-15 12:26:05.851 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-15 12:26:05.883 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-15 12:26:05.898 | INFO     | app.controllers.invite_controller:get_invite_tasks:38 - 获取邀请任务列表, status=None
2025-06-15 12:26:05.900 | INFO     | app.services.invite_service:get_invite_tasks:121 - 获取邀请任务列表, status=None
2025-06-15 12:26:05.901 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-15 12:26:05.902 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-15 12:26:05.909 | INFO     | core.proxy.proxy_task_manager:task_wrapper:199 - 任务 refresh_proxies_0_20 完成
2025-06-15 12:26:05.945 | INFO     | app.services.invite_service:task_initialize:39 - 初始化邀请服务，重置运行中的任务为暂停状态
2025-06-15 12:26:05.949 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-15 12:26:06.093 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-15 12:26:06.093 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-15 12:26:06.096 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-15 12:26:06.098 | INFO     | app.services.invite_service:task_initialize:48 - 成功重置 0 个任务状态
2025-06-15 12:26:06.098 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-15 12:26:06.099 | DEBUG    | ui.views.monitor_view:_refresh_task_list:175 - 刷新任务列表，共2个任务
2025-06-15 12:26:06.109 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-15 12:26:06.123 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-15 12:26:06.129 | INFO     | app.services.account_service:get_all_groups:96 - 获取所有账户分组成功, 共 2 个
2025-06-15 12:26:06.130 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-15 12:26:06.132 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-15 12:26:06.135 | DEBUG    | ui.views.account_view:_on_groups_loaded:442 - 分组加载完成: 2个分组
2025-06-15 12:26:06.155 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-15 12:26:06.233 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-15 12:26:06.234 | INFO     | app.services.account_service:get_all_accounts:289 - 获取所有账户成功, 共 2 个
2025-06-15 12:26:06.235 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-15 12:26:06.242 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-15 12:26:06.243 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-06-15 12:26:06.257 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-15 12:26:06.260 | DEBUG    | ui.views.account_view:_on_accounts_loaded:575 - 账户加载完成: 2个账户
2025-06-15 12:26:06.293 | DEBUG    | ui.views.account_view:_update_group_counts:184 - 分组计数已更新: {2: 1, 1: 1}
2025-06-15 12:26:06.298 | INFO     | ui.views.account_view:_auto_login_accounts:630 - 开始调用控制器进行 2 个账户的自动登录。
2025-06-15 12:26:06.298 | INFO     | app.services.account_service:batch_auto_login:1153 - 服务层：开始批量自动登录 2 个账户, 最大并发: 5
2025-06-15 12:26:06.299 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-15 12:26:06.307 | INFO     | app.controllers.telegram_monitor_controller:_load_total_users_count_internal:191 - 成功加载总体统计数据: 总用户数 1845, 今日采集 0, 日均采集 461, 运行天数 4
2025-06-15 12:26:06.308 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:156 - 成功加载 2 个任务
2025-06-15 12:26:06.309 | DEBUG    | ui.views.monitor_view:_on_loading_finished:625 - 加载完成
2025-06-15 12:26:06.311 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=True
2025-06-15 12:26:06.314 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=True
2025-06-15 12:26:06.316 | INFO     | ui.views.proxy_view:_load_initial_data:1044 - 初始数据加载完成，共 1 个代理
2025-06-15 12:26:06.321 | INFO     | app.services.account_service:batch_auto_login:1192 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-06-15 12:26:06.322 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-15 12:26:06.327 | INFO     | app.services.account_service:get_all_accounts:289 - 获取所有账户成功, 共 2 个
2025-06-15 12:26:06.327 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-15 12:26:06.329 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-15 12:26:06.340 | DEBUG    | ui.views.account_view:_update_group_counts:184 - 分组计数已更新: {2: 1, 1: 1}
2025-06-15 12:26:06.341 | DEBUG    | ui.views.account_view:_init_cache:151 - 账户缓存初始化完成，共 2 个账户
2025-06-15 12:26:06.350 | DEBUG    | ui.views.account_view:_on_accounts_loaded:575 - 账户加载完成: 2个账户
2025-06-15 12:26:06.391 | DEBUG    | ui.views.account_view:_update_group_counts:184 - 分组计数已更新: {2: 1, 1: 1}
2025-06-15 12:26:06.399 | WARNING  | core.auth.api_service:verify_vip:441 - 会员验证失败: 验证失败
2025-06-15 12:26:06.462 | INFO     | app.services.account_service:batch_auto_login:1192 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-06-15 12:26:06.463 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-15 12:26:06.465 | INFO     | app.services.account_service:batch_auto_login:1262 - 服务层：准备调用核心层处理 2 个账户的自动登录。
2025-06-15 12:26:06.466 | INFO     | app.services.account_service:batch_auto_login:1272 - 服务层：设置核心层任务超时为 120 秒。
2025-06-15 12:26:06.466 | INFO     | core.telegram.client_manager:batch_auto_login:1220 - 开始批量自动登录, 共 2 个账户, 最大并发 5
2025-06-15 12:26:06.467 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-15 12:26:06.467 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:256 - 创建客户端: h:\PyProject\TeleTest\APPDATA\sessions\+***********.session, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-06-15 12:26:06.467 | DEBUG    | core.telegram.client_manager:get_proxy:93 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-06-15 12:26:06.468 | DEBUG    | core.telegram.client_manager:get_proxy:140 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-06-15 12:26:06.478 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:256 - 创建客户端: h:\PyProject\TeleTest\APPDATA\sessions\+***********.session, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-06-15 12:26:06.479 | DEBUG    | core.telegram.client_manager:get_proxy:93 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-06-15 12:26:06.480 | DEBUG    | core.telegram.client_manager:get_proxy:140 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-06-15 12:26:06.511 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-15 12:26:06.729 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-15 12:26:08.603 | INFO     | ui.main_window:closeEvent:343 - MainWindow: 接收到关闭事件
2025-06-15 12:26:08.619 | INFO     | ui.main_window:_cleanup_before_quit:236 - MainWindow: 执行清理资源...
2025-06-15 12:26:08.621 | INFO     | core.telegram.client_worker:stop:110 - 正在停止Telegram客户端工作线程
2025-06-15 12:26:08.622 | INFO     | core.telegram.client_worker:_graceful_shutdown:451 - 等待 1 个耗时任务完成...
2025-06-15 12:27:23.749 | INFO     | core.telegram.client_manager:_connect_client:204 - 连接成功!
2025-06-15 12:27:24.708 | INFO     | core.telegram.client_manager:_verify_client_authorization:1195 - 账户已授权: +***********
2025-06-15 12:27:31.987 | INFO     | core.telegram.client_manager:_connect_client:204 - 连接成功!
2025-06-15 12:27:32.987 | INFO     | core.telegram.client_manager:_verify_client_authorization:1195 - 账户已授权: +***********
2025-06-15 12:27:34.988 | INFO     | core.telegram.client_manager:batch_auto_login:1369 - 批量登录完成: 总计 2, 成功 2, 失败 0
2025-06-15 12:27:34.999 | INFO     | core.telegram.client_manager:cleanup_async:1140 - 开始清理资源并断开所有连接
2025-06-15 12:27:35.000 | INFO     | core.telegram.client_manager:disconnect_all_clients:725 - 正在断开所有客户端连接
2025-06-15 12:27:35.001 | INFO     | core.telegram.client_manager:_safe_disconnect:746 - 正在断开客户端连接: +***********
2025-06-15 12:27:35.001 | INFO     | core.telegram.client_manager:_safe_disconnect:746 - 正在断开客户端连接: +***********
2025-06-15 12:27:35.009 | INFO     | core.telegram.client_manager:_safe_disconnect:750 - 断开客户端连接成功: +***********
2025-06-15 12:27:35.010 | INFO     | core.telegram.client_manager:_safe_disconnect:750 - 断开客户端连接成功: +***********
2025-06-15 12:27:35.010 | INFO     | core.telegram.client_manager:cleanup_async:1159 - 资源清理完成
2025-06-15 12:27:35.496 | INFO     | core.telegram.client_manager:cleanup_async:1140 - 开始清理资源并断开所有连接
2025-06-15 12:27:35.496 | INFO     | core.telegram.client_manager:disconnect_all_clients:725 - 正在断开所有客户端连接
2025-06-15 12:27:35.497 | INFO     | core.telegram.client_manager:cleanup_async:1159 - 资源清理完成
2025-06-15 12:27:35.998 | INFO     | core.telegram.client_worker:run:99 - Telegram客户端工作线程已退出
2025-06-15 12:27:36.015 | INFO     | __main__:main:111 - 应用程序已正常退出
