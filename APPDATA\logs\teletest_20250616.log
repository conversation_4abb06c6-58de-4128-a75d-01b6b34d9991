2025-06-16 08:40:35.172 | INFO     | utils.logger:init_logger:98 - 日志系统初始化完成，默认级别: DEBUG，日志目录: h:\PyProject\TeleTest\APPDATA\logs
2025-06-16 08:40:38.776 | INFO     | data.database:init_database:31 - 初始化数据库连接: h:\PyProject\TeleTest\APPDATA\database\telegram_manager.db
2025-06-16 08:40:38.804 | DEBUG    | data.database:init_database:58 - 创建数据库表
2025-06-16 08:40:38.819 | INFO     | data.database:init_database:61 - 数据库初始化完成
2025-06-16 08:40:40.156 | DEBUG    | app.controllers.auth_controller:initialize_software:43 - 控制器: 初始化软件 app_id=1000, version=1.0
2025-06-16 08:40:40.157 | DEBUG    | utils.client_http:get:54 - 发送GET请求: https://server.xile188.com/api/user/1000/windowns10/1.0/ini
2025-06-16 08:40:40.730 | DEBUG    | core.auth.api_service:init_software:66 - 初始化数据解密成功: {'new_content': '1.多账户管理\n2.ip池管理\n3.消息监听\n4.消息群发\n5.拉人入群', 'new_url': None, 'notice': {'id': '2', 'content': '公告内容测试3', 'time': '**********'}}
2025-06-16 08:40:40.740 | INFO     | app.controllers.auth_controller:login:61 - 控制器: 用户登录 account=demo01
2025-06-16 08:40:43.954 | INFO     | core.auth.api_service:login:127 - 用户登录: account=demo01
2025-06-16 08:40:43.954 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/logon
2025-06-16 08:40:44.179 | INFO     | core.auth.api_service:get_user_info:347 - 获取用户信息
2025-06-16 08:40:44.180 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/info
2025-06-16 08:40:44.429 | INFO     | core.auth.api_service:get_user_info:375 - 获取用户信息成功
2025-06-16 08:40:44.435 | INFO     | __main__:on_auth_completed:93 - 用户登录成功，正在启动主窗口...
2025-06-16 08:40:44.452 | INFO     | core.telegram.client_worker:__init__:63 - Telegram客户端工作线程初始化
2025-06-16 08:40:44.455 | INFO     | core.telegram.client_worker:run:67 - Telegram客户端工作线程开始运行
2025-06-16 08:40:44.461 | INFO     | core.telegram.client_manager:__init__:66 - Telegram客户端管理器初始化
2025-06-16 08:40:44.461 | INFO     | core.telegram.user_manager:__init__:31 - Telegram用户管理器初始化
2025-06-16 08:40:44.462 | INFO     | core.telegram.group_manager:__init__:36 - Telegram群组管理器初始化
2025-06-16 08:40:44.462 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-06-16 08:40:44.462 | INFO     | core.telegram.monitor_manager:__init__:26 - Telegram监控管理器初始化
2025-06-16 08:40:44.463 | INFO     | core.telegram.telegram_service:__init__:39 - Telegram服务初始化完成
2025-06-16 08:40:44.465 | INFO     | ui.main_window:_initialize_core_components:80 - MainWindow: 初始化核心组件...
2025-06-16 08:40:44.466 | INFO     | app.services.account_service:__init__:42 - 账户服务初始化
2025-06-16 08:40:44.467 | INFO     | core.proxy.proxy_task_manager:run:284 - 代理任务管理器线程启动
2025-06-16 08:40:44.467 | INFO     | app.controllers.account_controller:__init__:81 - 账户控制器初始化
2025-06-16 08:40:44.468 | INFO     | app.services.monitor_service:__init__:39 - 初始化监控任务服务
2025-06-16 08:40:44.468 | INFO     | core.monitor.filter:__init__:26 - 初始化消息过滤器
2025-06-16 08:40:44.469 | INFO     | core.monitor.extractor:__init__:24 - 初始化关键词提取器
2025-06-16 08:40:44.469 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-06-16 08:40:44.470 | INFO     | core.monitor.notifier:__init__:90 - 初始化通知管理器
2025-06-16 08:40:44.470 | INFO     | app.controllers.telegram_monitor_controller:__init__:64 - 初始化 Telegram 监控控制器
2025-06-16 08:40:44.471 | DEBUG    | app.controllers.telegram_monitor_controller:__init__:82 - 控制器初始化完成
2025-06-16 08:40:44.674 | INFO     | ui.views.monitor_view:__init__:48 - 初始化监控视图
2025-06-16 08:40:44.675 | DEBUG    | ui.views.monitor_view:_connect_signals:80 - 连接控制器信号
2025-06-16 08:40:44.863 | INFO     | ui.views.proxy_view:__init__:96 - 初始化视图，加载第一页代理数据
2025-06-16 08:40:45.080 | INFO     | __main__:on_auth_completed:97 - 主窗口已启动
2025-06-16 08:40:45.156 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:88 - 尝试连接监听日志信号...
2025-06-16 08:40:45.156 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:90 - 监听日志信号连接成功
2025-06-16 08:40:45.156 | INFO     | core.auth.api_service:verify_vip:425 - 会员验证接口调用
2025-06-16 08:40:45.157 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/vip
2025-06-16 08:40:45.158 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-16 08:40:45.163 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-16 08:40:45.167 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:145 - 加载监控任务列表 (搜索关键词: '')
2025-06-16 08:40:45.167 | DEBUG    | ui.views.monitor_view:_on_loading_started:620 - 加载中: 正在加载任务列表...
2025-06-16 08:40:45.168 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-16 08:40:45.174 | INFO     | app.services.proxy_service:refresh_proxies:712 - 刷新代理列表: offset=0, limit=20
2025-06-16 08:40:45.175 | INFO     | core.proxy.proxy_task_manager:submit_task:172 - 提交耗时任务到子线程: refresh_proxies_0_20
2025-06-16 08:40:45.175 | DEBUG    | app.services.proxy_service:get_proxies:500 - 获取代理列表: offset=0, limit=20
2025-06-16 08:40:45.175 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-16 08:40:45.176 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-16 08:40:45.180 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-16 08:40:45.212 | INFO     | app.controllers.invite_controller:get_invite_tasks:38 - 获取邀请任务列表, status=None
2025-06-16 08:40:45.216 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-16 08:40:45.220 | INFO     | app.services.invite_service:get_invite_tasks:121 - 获取邀请任务列表, status=None
2025-06-16 08:40:45.221 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-16 08:40:45.221 | INFO     | core.proxy.proxy_task_manager:task_wrapper:199 - 任务 refresh_proxies_0_20 完成
2025-06-16 08:40:45.223 | INFO     | app.services.invite_service:task_initialize:39 - 初始化邀请服务，重置运行中的任务为暂停状态
2025-06-16 08:40:45.223 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-16 08:40:45.434 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-16 08:40:45.439 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-16 08:40:45.442 | INFO     | app.services.invite_service:task_initialize:48 - 成功重置 0 个任务状态
2025-06-16 08:40:45.443 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-16 08:40:45.444 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-16 08:40:45.445 | DEBUG    | ui.views.monitor_view:_refresh_task_list:175 - 刷新任务列表，共2个任务
2025-06-16 08:40:45.451 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-16 08:40:45.463 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-16 08:40:45.465 | INFO     | app.services.account_service:get_all_groups:96 - 获取所有账户分组成功, 共 2 个
2025-06-16 08:40:45.465 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-16 08:40:45.467 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-16 08:40:45.468 | DEBUG    | ui.views.account_view:_on_groups_loaded:442 - 分组加载完成: 2个分组
2025-06-16 08:40:45.484 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-16 08:40:45.556 | INFO     | app.services.account_service:get_all_accounts:289 - 获取所有账户成功, 共 2 个
2025-06-16 08:40:45.557 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-16 08:40:45.558 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-16 08:40:45.559 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-16 08:40:45.560 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-16 08:40:45.562 | DEBUG    | ui.views.account_view:_on_accounts_loaded:575 - 账户加载完成: 2个账户
2025-06-16 08:40:45.585 | DEBUG    | ui.views.account_view:_update_group_counts:184 - 分组计数已更新: {2: 1, 1: 1}
2025-06-16 08:40:45.590 | INFO     | ui.views.account_view:_auto_login_accounts:630 - 开始调用控制器进行 2 个账户的自动登录。
2025-06-16 08:40:45.590 | INFO     | app.services.account_service:batch_auto_login:1153 - 服务层：开始批量自动登录 2 个账户, 最大并发: 5
2025-06-16 08:40:45.591 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-16 08:40:45.595 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-06-16 08:40:45.613 | INFO     | app.controllers.telegram_monitor_controller:_load_total_users_count_internal:191 - 成功加载总体统计数据: 总用户数 1845, 今日采集 0, 日均采集 461, 运行天数 4
2025-06-16 08:40:45.613 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:156 - 成功加载 2 个任务
2025-06-16 08:40:45.614 | DEBUG    | ui.views.monitor_view:_on_loading_finished:625 - 加载完成
2025-06-16 08:40:45.622 | INFO     | app.services.account_service:batch_auto_login:1192 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-06-16 08:40:45.623 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-16 08:40:45.624 | INFO     | app.services.account_service:get_all_accounts:289 - 获取所有账户成功, 共 2 个
2025-06-16 08:40:45.624 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-16 08:40:45.627 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-16 08:40:45.629 | DEBUG    | ui.views.account_view:_update_group_counts:184 - 分组计数已更新: {2: 1, 1: 1}
2025-06-16 08:40:45.629 | DEBUG    | ui.views.account_view:_init_cache:151 - 账户缓存初始化完成，共 2 个账户
2025-06-16 08:40:45.635 | DEBUG    | ui.views.account_view:_on_accounts_loaded:575 - 账户加载完成: 2个账户
2025-06-16 08:40:45.660 | DEBUG    | ui.views.account_view:_update_group_counts:184 - 分组计数已更新: {2: 1, 1: 1}
2025-06-16 08:40:45.668 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=True
2025-06-16 08:40:45.672 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=True
2025-06-16 08:40:45.674 | INFO     | ui.views.proxy_view:_load_initial_data:1044 - 初始数据加载完成，共 1 个代理
2025-06-16 08:40:45.675 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-16 08:40:45.677 | INFO     | app.services.account_service:batch_auto_login:1192 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-06-16 08:40:45.677 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-16 08:40:45.703 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-16 08:40:45.708 | INFO     | app.services.account_service:batch_auto_login:1262 - 服务层：准备调用核心层处理 2 个账户的自动登录。
2025-06-16 08:40:45.709 | INFO     | app.services.account_service:batch_auto_login:1272 - 服务层：设置核心层任务超时为 120 秒。
2025-06-16 08:40:45.709 | INFO     | core.telegram.client_manager:batch_auto_login:1220 - 开始批量自动登录, 共 2 个账户, 最大并发 5
2025-06-16 08:40:45.710 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:256 - 创建客户端: h:\PyProject\TeleTest\APPDATA\sessions\+***********.session, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-06-16 08:40:45.711 | DEBUG    | core.telegram.client_manager:get_proxy:93 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-06-16 08:40:45.712 | DEBUG    | core.telegram.client_manager:get_proxy:140 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-06-16 08:40:45.717 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:256 - 创建客户端: h:\PyProject\TeleTest\APPDATA\sessions\+***********.session, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-06-16 08:40:45.717 | DEBUG    | core.telegram.client_manager:get_proxy:93 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-06-16 08:40:45.718 | DEBUG    | core.telegram.client_manager:get_proxy:140 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-06-16 08:40:45.731 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-16 08:40:45.775 | WARNING  | core.auth.api_service:verify_vip:441 - 会员验证失败: 验证失败
2025-06-16 08:40:48.676 | INFO     | core.telegram.client_manager:_connect_client:204 - 连接成功!
2025-06-16 08:40:48.700 | INFO     | core.telegram.client_manager:_connect_client:204 - 连接成功!
2025-06-16 08:40:49.604 | INFO     | core.telegram.client_manager:_verify_client_authorization:1195 - 账户已授权: +***********
2025-06-16 08:40:49.660 | INFO     | core.telegram.client_manager:_verify_client_authorization:1195 - 账户已授权: +***********
2025-06-16 08:40:51.872 | INFO     | core.telegram.client_manager:batch_auto_login:1369 - 批量登录完成: 总计 2, 成功 2, 失败 0
2025-06-16 08:40:51.885 | INFO     | app.services.account_service:batch_auto_login:1293 - 服务层：核心层登录完成: 成功 2, 失败 0 / 2
2025-06-16 08:41:44.427 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-16 08:41:44.428 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-16 08:42:44.428 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-16 08:42:44.429 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-16 08:43:44.430 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-16 08:43:44.431 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-16 08:44:44.432 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-16 08:44:44.432 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-16 08:45:44.429 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-16 08:45:44.430 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-16 08:46:05.444 | INFO     | ui.main_window:closeEvent:343 - MainWindow: 接收到关闭事件
2025-06-16 08:46:05.456 | INFO     | ui.main_window:_cleanup_before_quit:236 - MainWindow: 执行清理资源...
2025-06-16 08:46:05.457 | INFO     | core.telegram.client_worker:stop:110 - 正在停止Telegram客户端工作线程
2025-06-16 08:46:05.468 | INFO     | core.telegram.client_manager:cleanup_async:1140 - 开始清理资源并断开所有连接
2025-06-16 08:46:05.468 | INFO     | core.telegram.client_manager:disconnect_all_clients:725 - 正在断开所有客户端连接
2025-06-16 08:46:05.469 | INFO     | core.telegram.client_manager:_safe_disconnect:746 - 正在断开客户端连接: +***********
2025-06-16 08:46:05.470 | INFO     | core.telegram.client_manager:_safe_disconnect:746 - 正在断开客户端连接: +***********
2025-06-16 08:46:05.487 | INFO     | core.telegram.client_manager:_safe_disconnect:750 - 断开客户端连接成功: +***********
2025-06-16 08:46:05.487 | INFO     | core.telegram.client_manager:_safe_disconnect:750 - 断开客户端连接成功: +***********
2025-06-16 08:46:05.488 | INFO     | core.telegram.client_manager:cleanup_async:1159 - 资源清理完成
2025-06-16 08:46:05.973 | INFO     | core.telegram.client_manager:cleanup_async:1140 - 开始清理资源并断开所有连接
2025-06-16 08:46:05.974 | INFO     | core.telegram.client_manager:disconnect_all_clients:725 - 正在断开所有客户端连接
2025-06-16 08:46:05.974 | INFO     | core.telegram.client_manager:cleanup_async:1159 - 资源清理完成
2025-06-16 08:46:06.460 | INFO     | core.telegram.client_worker:run:99 - Telegram客户端工作线程已退出
2025-06-16 08:46:06.461 | INFO     | core.telegram.client_worker:stop:124 - Telegram客户端工作线程已停止
2025-06-16 08:46:06.461 | INFO     | ui.main_window:_cleanup_before_quit:245 - TelegramClientWorker 已停止。
2025-06-16 08:46:06.461 | INFO     | ui.main_window:_cleanup_before_quit:249 - MainWindow 清理完成
2025-06-16 08:46:06.473 | INFO     | __main__:main:111 - 应用程序已正常退出
2025-06-16 08:46:13.917 | INFO     | utils.logger:init_logger:98 - 日志系统初始化完成，默认级别: DEBUG，日志目录: h:\PyProject\TeleTest\APPDATA\logs
2025-06-16 08:46:15.351 | INFO     | data.database:init_database:31 - 初始化数据库连接: h:\PyProject\TeleTest\APPDATA\database\telegram_manager.db
2025-06-16 08:46:15.371 | DEBUG    | data.database:init_database:58 - 创建数据库表
2025-06-16 08:46:15.382 | INFO     | data.database:init_database:61 - 数据库初始化完成
2025-06-16 08:46:16.459 | DEBUG    | app.controllers.auth_controller:initialize_software:43 - 控制器: 初始化软件 app_id=1000, version=1.0
2025-06-16 08:46:16.459 | DEBUG    | utils.client_http:get:54 - 发送GET请求: https://server.xile188.com/api/user/1000/windowns10/1.0/ini
2025-06-16 08:46:16.827 | DEBUG    | core.auth.api_service:init_software:66 - 初始化数据解密成功: {'new_content': '1.多账户管理\n2.ip池管理\n3.消息监听\n4.消息群发\n5.拉人入群', 'new_url': None, 'notice': {'id': '2', 'content': '公告内容测试3', 'time': '**********'}}
2025-06-16 08:46:16.835 | INFO     | app.controllers.auth_controller:login:61 - 控制器: 用户登录 account=demo01
2025-06-16 08:46:20.831 | INFO     | core.auth.api_service:login:127 - 用户登录: account=demo01
2025-06-16 08:46:20.832 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/logon
2025-06-16 08:46:21.099 | INFO     | core.auth.api_service:get_user_info:347 - 获取用户信息
2025-06-16 08:46:21.100 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/info
2025-06-16 08:46:21.306 | INFO     | core.auth.api_service:get_user_info:375 - 获取用户信息成功
2025-06-16 08:46:21.312 | INFO     | __main__:on_auth_completed:93 - 用户登录成功，正在启动主窗口...
2025-06-16 08:46:21.330 | INFO     | core.telegram.client_worker:__init__:63 - Telegram客户端工作线程初始化
2025-06-16 08:46:21.334 | INFO     | core.telegram.client_worker:run:67 - Telegram客户端工作线程开始运行
2025-06-16 08:46:21.337 | INFO     | core.telegram.client_manager:__init__:66 - Telegram客户端管理器初始化
2025-06-16 08:46:21.338 | INFO     | core.telegram.user_manager:__init__:31 - Telegram用户管理器初始化
2025-06-16 08:46:21.338 | INFO     | core.telegram.group_manager:__init__:36 - Telegram群组管理器初始化
2025-06-16 08:46:21.339 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-06-16 08:46:21.339 | INFO     | core.telegram.monitor_manager:__init__:26 - Telegram监控管理器初始化
2025-06-16 08:46:21.340 | INFO     | core.telegram.telegram_service:__init__:39 - Telegram服务初始化完成
2025-06-16 08:46:21.342 | INFO     | ui.main_window:_initialize_core_components:80 - MainWindow: 初始化核心组件...
2025-06-16 08:46:21.343 | INFO     | app.services.account_service:__init__:42 - 账户服务初始化
2025-06-16 08:46:21.344 | INFO     | core.proxy.proxy_task_manager:run:284 - 代理任务管理器线程启动
2025-06-16 08:46:21.344 | INFO     | app.controllers.account_controller:__init__:81 - 账户控制器初始化
2025-06-16 08:46:21.345 | INFO     | app.services.monitor_service:__init__:39 - 初始化监控任务服务
2025-06-16 08:46:21.346 | INFO     | core.monitor.filter:__init__:26 - 初始化消息过滤器
2025-06-16 08:46:21.346 | INFO     | core.monitor.extractor:__init__:24 - 初始化关键词提取器
2025-06-16 08:46:21.346 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-06-16 08:46:21.347 | INFO     | core.monitor.notifier:__init__:90 - 初始化通知管理器
2025-06-16 08:46:21.347 | INFO     | app.controllers.telegram_monitor_controller:__init__:64 - 初始化 Telegram 监控控制器
2025-06-16 08:46:21.348 | DEBUG    | app.controllers.telegram_monitor_controller:__init__:82 - 控制器初始化完成
2025-06-16 08:46:21.538 | INFO     | ui.views.monitor_view:__init__:48 - 初始化监控视图
2025-06-16 08:46:21.538 | DEBUG    | ui.views.monitor_view:_connect_signals:80 - 连接控制器信号
2025-06-16 08:46:21.718 | INFO     | ui.views.proxy_view:__init__:96 - 初始化视图，加载第一页代理数据
2025-06-16 08:46:21.926 | INFO     | __main__:on_auth_completed:97 - 主窗口已启动
2025-06-16 08:46:21.976 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:88 - 尝试连接监听日志信号...
2025-06-16 08:46:21.977 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:90 - 监听日志信号连接成功
2025-06-16 08:46:21.978 | INFO     | core.auth.api_service:verify_vip:425 - 会员验证接口调用
2025-06-16 08:46:21.978 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/vip
2025-06-16 08:46:21.979 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-16 08:46:21.984 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-16 08:46:21.988 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:145 - 加载监控任务列表 (搜索关键词: '')
2025-06-16 08:46:21.988 | DEBUG    | ui.views.monitor_view:_on_loading_started:620 - 加载中: 正在加载任务列表...
2025-06-16 08:46:21.989 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-16 08:46:21.998 | INFO     | app.services.proxy_service:refresh_proxies:712 - 刷新代理列表: offset=0, limit=20
2025-06-16 08:46:21.999 | INFO     | core.proxy.proxy_task_manager:submit_task:172 - 提交耗时任务到子线程: refresh_proxies_0_20
2025-06-16 08:46:21.999 | DEBUG    | app.services.proxy_service:get_proxies:500 - 获取代理列表: offset=0, limit=20
2025-06-16 08:46:22.000 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-16 08:46:22.000 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-16 08:46:22.003 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-16 08:46:22.028 | INFO     | app.controllers.invite_controller:get_invite_tasks:38 - 获取邀请任务列表, status=None
2025-06-16 08:46:22.028 | INFO     | app.services.invite_service:get_invite_tasks:121 - 获取邀请任务列表, status=None
2025-06-16 08:46:22.029 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-16 08:46:22.031 | INFO     | app.services.invite_service:task_initialize:39 - 初始化邀请服务，重置运行中的任务为暂停状态
2025-06-16 08:46:22.032 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-16 08:46:22.032 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-16 08:46:22.033 | INFO     | core.proxy.proxy_task_manager:task_wrapper:199 - 任务 refresh_proxies_0_20 完成
2025-06-16 08:46:22.239 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-16 08:46:22.243 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-16 08:46:22.246 | INFO     | app.services.invite_service:task_initialize:48 - 成功重置 0 个任务状态
2025-06-16 08:46:22.247 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-16 08:46:22.248 | DEBUG    | ui.views.monitor_view:_refresh_task_list:175 - 刷新任务列表，共2个任务
2025-06-16 08:46:22.253 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-16 08:46:22.260 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-16 08:46:22.264 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-16 08:46:22.265 | INFO     | app.services.account_service:get_all_groups:96 - 获取所有账户分组成功, 共 2 个
2025-06-16 08:46:22.266 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-16 08:46:22.267 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-16 08:46:22.269 | DEBUG    | ui.views.account_view:_on_groups_loaded:442 - 分组加载完成: 2个分组
2025-06-16 08:46:22.284 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-16 08:46:22.349 | INFO     | app.services.account_service:get_all_accounts:289 - 获取所有账户成功, 共 2 个
2025-06-16 08:46:22.350 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-16 08:46:22.350 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-16 08:46:22.351 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-16 08:46:22.353 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-16 08:46:22.354 | DEBUG    | ui.views.account_view:_on_accounts_loaded:575 - 账户加载完成: 2个账户
2025-06-16 08:46:22.375 | DEBUG    | ui.views.account_view:_update_group_counts:184 - 分组计数已更新: {2: 1, 1: 1}
2025-06-16 08:46:22.380 | INFO     | ui.views.account_view:_auto_login_accounts:630 - 开始调用控制器进行 2 个账户的自动登录。
2025-06-16 08:46:22.380 | INFO     | app.services.account_service:batch_auto_login:1153 - 服务层：开始批量自动登录 2 个账户, 最大并发: 5
2025-06-16 08:46:22.381 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-16 08:46:22.385 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-06-16 08:46:22.390 | INFO     | app.controllers.telegram_monitor_controller:_load_total_users_count_internal:191 - 成功加载总体统计数据: 总用户数 1845, 今日采集 0, 日均采集 461, 运行天数 4
2025-06-16 08:46:22.391 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:156 - 成功加载 2 个任务
2025-06-16 08:46:22.392 | DEBUG    | ui.views.monitor_view:_on_loading_finished:625 - 加载完成
2025-06-16 08:46:22.415 | INFO     | app.services.account_service:batch_auto_login:1192 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-06-16 08:46:22.416 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-16 08:46:22.417 | INFO     | app.services.account_service:get_all_accounts:289 - 获取所有账户成功, 共 2 个
2025-06-16 08:46:22.417 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-16 08:46:22.422 | DEBUG    | ui.views.account_view:_update_group_counts:184 - 分组计数已更新: {2: 1, 1: 1}
2025-06-16 08:46:22.423 | DEBUG    | ui.views.account_view:_init_cache:151 - 账户缓存初始化完成，共 2 个账户
2025-06-16 08:46:22.429 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-16 08:46:22.431 | DEBUG    | ui.views.account_view:_on_accounts_loaded:575 - 账户加载完成: 2个账户
2025-06-16 08:46:22.453 | DEBUG    | ui.views.account_view:_update_group_counts:184 - 分组计数已更新: {2: 1, 1: 1}
2025-06-16 08:46:22.467 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=True
2025-06-16 08:46:22.469 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=True
2025-06-16 08:46:22.471 | INFO     | ui.views.proxy_view:_load_initial_data:1044 - 初始数据加载完成，共 1 个代理
2025-06-16 08:46:22.483 | WARNING  | core.auth.api_service:verify_vip:441 - 会员验证失败: 验证失败
2025-06-16 08:46:22.590 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-16 08:46:22.592 | INFO     | app.services.account_service:batch_auto_login:1192 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-06-16 08:46:22.593 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-16 08:46:22.782 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-16 08:46:22.783 | INFO     | app.services.account_service:batch_auto_login:1262 - 服务层：准备调用核心层处理 2 个账户的自动登录。
2025-06-16 08:46:22.784 | INFO     | app.services.account_service:batch_auto_login:1272 - 服务层：设置核心层任务超时为 120 秒。
2025-06-16 08:46:22.784 | INFO     | core.telegram.client_manager:batch_auto_login:1220 - 开始批量自动登录, 共 2 个账户, 最大并发 5
2025-06-16 08:46:22.785 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:256 - 创建客户端: h:\PyProject\TeleTest\APPDATA\sessions\+***********.session, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-06-16 08:46:22.786 | DEBUG    | core.telegram.client_manager:get_proxy:93 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-06-16 08:46:22.787 | DEBUG    | core.telegram.client_manager:get_proxy:140 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-06-16 08:46:22.790 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:256 - 创建客户端: h:\PyProject\TeleTest\APPDATA\sessions\+***********.session, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-06-16 08:46:22.791 | DEBUG    | core.telegram.client_manager:get_proxy:93 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-06-16 08:46:22.791 | DEBUG    | core.telegram.client_manager:get_proxy:140 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-06-16 08:46:22.810 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-16 08:46:25.711 | INFO     | core.telegram.client_manager:_connect_client:204 - 连接成功!
2025-06-16 08:46:26.126 | INFO     | core.telegram.client_manager:_connect_client:204 - 连接成功!
2025-06-16 08:46:26.636 | INFO     | core.telegram.client_manager:_verify_client_authorization:1195 - 账户已授权: +***********
2025-06-16 08:46:27.663 | INFO     | core.telegram.client_manager:_verify_client_authorization:1195 - 账户已授权: +***********
2025-06-16 08:46:29.667 | INFO     | core.telegram.client_manager:batch_auto_login:1369 - 批量登录完成: 总计 2, 成功 2, 失败 0
2025-06-16 08:46:29.803 | INFO     | app.services.account_service:batch_auto_login:1293 - 服务层：核心层登录完成: 成功 2, 失败 0 / 2
2025-06-16 08:46:57.135 | INFO     | ui.main_window:closeEvent:344 - MainWindow: 接收到关闭事件
2025-06-16 08:46:57.150 | INFO     | ui.main_window:_cleanup_before_quit:236 - MainWindow: 执行清理资源...
2025-06-16 08:46:57.151 | INFO     | core.telegram.client_worker:stop:110 - 正在停止Telegram客户端工作线程
2025-06-16 08:46:57.159 | INFO     | core.telegram.client_manager:cleanup_async:1140 - 开始清理资源并断开所有连接
2025-06-16 08:46:57.160 | INFO     | core.telegram.client_manager:disconnect_all_clients:725 - 正在断开所有客户端连接
2025-06-16 08:46:57.161 | INFO     | core.telegram.client_manager:_safe_disconnect:746 - 正在断开客户端连接: +***********
2025-06-16 08:46:57.162 | INFO     | core.telegram.client_manager:_safe_disconnect:746 - 正在断开客户端连接: +***********
2025-06-16 08:46:57.173 | INFO     | core.telegram.client_manager:_safe_disconnect:750 - 断开客户端连接成功: +***********
2025-06-16 08:46:57.174 | INFO     | core.telegram.client_manager:_safe_disconnect:750 - 断开客户端连接成功: +***********
2025-06-16 08:46:57.175 | INFO     | core.telegram.client_manager:cleanup_async:1159 - 资源清理完成
2025-06-16 08:46:57.661 | INFO     | core.telegram.client_manager:cleanup_async:1140 - 开始清理资源并断开所有连接
2025-06-16 08:46:57.661 | INFO     | core.telegram.client_manager:disconnect_all_clients:725 - 正在断开所有客户端连接
2025-06-16 08:46:57.662 | INFO     | core.telegram.client_manager:cleanup_async:1159 - 资源清理完成
2025-06-16 08:46:58.163 | INFO     | core.telegram.client_worker:run:99 - Telegram客户端工作线程已退出
2025-06-16 08:46:58.164 | INFO     | core.telegram.client_worker:stop:124 - Telegram客户端工作线程已停止
2025-06-16 08:46:58.164 | INFO     | ui.main_window:_cleanup_before_quit:245 - TelegramClientWorker 已停止。
2025-06-16 08:46:58.165 | INFO     | ui.main_window:_cleanup_before_quit:249 - MainWindow 清理完成
2025-06-16 08:46:58.177 | INFO     | __main__:main:111 - 应用程序已正常退出
2025-06-16 08:47:03.622 | INFO     | utils.logger:init_logger:98 - 日志系统初始化完成，默认级别: DEBUG，日志目录: h:\PyProject\TeleTest\APPDATA\logs
2025-06-16 08:47:06.199 | INFO     | data.database:init_database:31 - 初始化数据库连接: h:\PyProject\TeleTest\APPDATA\database\telegram_manager.db
2025-06-16 08:47:06.214 | DEBUG    | data.database:init_database:58 - 创建数据库表
2025-06-16 08:47:06.226 | INFO     | data.database:init_database:61 - 数据库初始化完成
2025-06-16 08:47:06.951 | DEBUG    | app.controllers.auth_controller:initialize_software:43 - 控制器: 初始化软件 app_id=1000, version=1.0
2025-06-16 08:47:06.952 | DEBUG    | utils.client_http:get:54 - 发送GET请求: https://server.xile188.com/api/user/1000/windowns10/1.0/ini
2025-06-16 08:47:07.246 | DEBUG    | core.auth.api_service:init_software:66 - 初始化数据解密成功: {'new_content': '1.多账户管理\n2.ip池管理\n3.消息监听\n4.消息群发\n5.拉人入群', 'new_url': None, 'notice': {'id': '2', 'content': '公告内容测试3', 'time': '**********'}}
2025-06-16 08:47:07.253 | INFO     | app.controllers.auth_controller:login:61 - 控制器: 用户登录 account=demo01
2025-06-16 08:47:10.309 | INFO     | core.auth.api_service:login:127 - 用户登录: account=demo01
2025-06-16 08:47:10.310 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/logon
2025-06-16 08:47:10.604 | INFO     | core.auth.api_service:get_user_info:347 - 获取用户信息
2025-06-16 08:47:10.605 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/info
2025-06-16 08:47:10.804 | INFO     | core.auth.api_service:get_user_info:375 - 获取用户信息成功
2025-06-16 08:47:10.812 | INFO     | __main__:on_auth_completed:93 - 用户登录成功，正在启动主窗口...
2025-06-16 08:47:10.861 | INFO     | core.telegram.client_worker:__init__:63 - Telegram客户端工作线程初始化
2025-06-16 08:47:10.869 | INFO     | core.telegram.client_worker:run:67 - Telegram客户端工作线程开始运行
2025-06-16 08:47:10.874 | INFO     | core.telegram.client_manager:__init__:66 - Telegram客户端管理器初始化
2025-06-16 08:47:10.875 | INFO     | core.telegram.user_manager:__init__:31 - Telegram用户管理器初始化
2025-06-16 08:47:10.876 | INFO     | core.telegram.group_manager:__init__:36 - Telegram群组管理器初始化
2025-06-16 08:47:10.876 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-06-16 08:47:10.877 | INFO     | core.telegram.monitor_manager:__init__:26 - Telegram监控管理器初始化
2025-06-16 08:47:10.878 | INFO     | core.telegram.telegram_service:__init__:39 - Telegram服务初始化完成
2025-06-16 08:47:10.882 | INFO     | ui.main_window:_initialize_core_components:80 - MainWindow: 初始化核心组件...
2025-06-16 08:47:10.883 | INFO     | app.services.account_service:__init__:42 - 账户服务初始化
2025-06-16 08:47:10.884 | INFO     | app.controllers.account_controller:__init__:81 - 账户控制器初始化
2025-06-16 08:47:10.884 | INFO     | core.proxy.proxy_task_manager:run:284 - 代理任务管理器线程启动
2025-06-16 08:47:10.885 | INFO     | app.services.monitor_service:__init__:39 - 初始化监控任务服务
2025-06-16 08:47:10.885 | INFO     | core.monitor.filter:__init__:26 - 初始化消息过滤器
2025-06-16 08:47:10.886 | INFO     | core.monitor.extractor:__init__:24 - 初始化关键词提取器
2025-06-16 08:47:10.886 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-06-16 08:47:10.887 | INFO     | core.monitor.notifier:__init__:90 - 初始化通知管理器
2025-06-16 08:47:10.887 | INFO     | app.controllers.telegram_monitor_controller:__init__:64 - 初始化 Telegram 监控控制器
2025-06-16 08:47:10.888 | DEBUG    | app.controllers.telegram_monitor_controller:__init__:82 - 控制器初始化完成
2025-06-16 08:47:11.148 | INFO     | ui.views.monitor_view:__init__:48 - 初始化监控视图
2025-06-16 08:47:11.148 | DEBUG    | ui.views.monitor_view:_connect_signals:80 - 连接控制器信号
2025-06-16 08:47:11.340 | INFO     | ui.views.proxy_view:__init__:96 - 初始化视图，加载第一页代理数据
2025-06-16 08:47:11.602 | INFO     | __main__:on_auth_completed:97 - 主窗口已启动
2025-06-16 08:47:11.649 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:88 - 尝试连接监听日志信号...
2025-06-16 08:47:11.650 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:90 - 监听日志信号连接成功
2025-06-16 08:47:11.650 | INFO     | core.auth.api_service:verify_vip:425 - 会员验证接口调用
2025-06-16 08:47:11.651 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/vip
2025-06-16 08:47:11.652 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-16 08:47:11.660 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-16 08:47:11.666 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:145 - 加载监控任务列表 (搜索关键词: '')
2025-06-16 08:47:11.666 | DEBUG    | ui.views.monitor_view:_on_loading_started:620 - 加载中: 正在加载任务列表...
2025-06-16 08:47:11.667 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-16 08:47:11.676 | INFO     | app.services.proxy_service:refresh_proxies:712 - 刷新代理列表: offset=0, limit=20
2025-06-16 08:47:11.677 | INFO     | core.proxy.proxy_task_manager:submit_task:172 - 提交耗时任务到子线程: refresh_proxies_0_20
2025-06-16 08:47:11.678 | DEBUG    | app.services.proxy_service:get_proxies:500 - 获取代理列表: offset=0, limit=20
2025-06-16 08:47:11.678 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-16 08:47:11.678 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-16 08:47:11.684 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-16 08:47:11.717 | INFO     | app.controllers.invite_controller:get_invite_tasks:38 - 获取邀请任务列表, status=None
2025-06-16 08:47:11.720 | INFO     | app.services.invite_service:get_invite_tasks:121 - 获取邀请任务列表, status=None
2025-06-16 08:47:11.720 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-16 08:47:11.721 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-16 08:47:11.722 | INFO     | app.services.invite_service:task_initialize:39 - 初始化邀请服务，重置运行中的任务为暂停状态
2025-06-16 08:47:11.722 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-16 08:47:11.723 | INFO     | core.proxy.proxy_task_manager:task_wrapper:199 - 任务 refresh_proxies_0_20 完成
2025-06-16 08:47:11.845 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-16 08:47:11.852 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-16 08:47:11.853 | INFO     | app.services.invite_service:task_initialize:48 - 成功重置 0 个任务状态
2025-06-16 08:47:11.854 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-16 08:47:11.857 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-16 08:47:11.858 | DEBUG    | ui.views.monitor_view:_refresh_task_list:175 - 刷新任务列表，共2个任务
2025-06-16 08:47:11.865 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-16 08:47:11.873 | INFO     | app.services.account_service:get_all_groups:96 - 获取所有账户分组成功, 共 2 个
2025-06-16 08:47:11.873 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-16 08:47:11.875 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-16 08:47:11.876 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-16 08:47:11.880 | DEBUG    | ui.views.account_view:_on_groups_loaded:442 - 分组加载完成: 2个分组
2025-06-16 08:47:11.893 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-16 08:47:11.965 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-16 08:47:11.966 | INFO     | app.services.account_service:get_all_accounts:289 - 获取所有账户成功, 共 2 个
2025-06-16 08:47:11.966 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-16 08:47:11.968 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-16 08:47:11.969 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-06-16 08:47:11.975 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-16 08:47:11.983 | DEBUG    | ui.views.account_view:_on_accounts_loaded:575 - 账户加载完成: 2个账户
2025-06-16 08:47:12.012 | DEBUG    | ui.views.account_view:_update_group_counts:184 - 分组计数已更新: {2: 1, 1: 1}
2025-06-16 08:47:12.017 | INFO     | ui.views.account_view:_auto_login_accounts:630 - 开始调用控制器进行 2 个账户的自动登录。
2025-06-16 08:47:12.018 | INFO     | app.services.account_service:batch_auto_login:1153 - 服务层：开始批量自动登录 2 个账户, 最大并发: 5
2025-06-16 08:47:12.018 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-16 08:47:12.028 | INFO     | app.controllers.telegram_monitor_controller:_load_total_users_count_internal:191 - 成功加载总体统计数据: 总用户数 1845, 今日采集 0, 日均采集 461, 运行天数 4
2025-06-16 08:47:12.028 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:156 - 成功加载 2 个任务
2025-06-16 08:47:12.029 | DEBUG    | ui.views.monitor_view:_on_loading_finished:625 - 加载完成
2025-06-16 08:47:12.035 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=True
2025-06-16 08:47:12.037 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=True
2025-06-16 08:47:12.040 | INFO     | ui.views.proxy_view:_load_initial_data:1044 - 初始数据加载完成，共 1 个代理
2025-06-16 08:47:12.046 | INFO     | app.services.account_service:get_all_accounts:289 - 获取所有账户成功, 共 2 个
2025-06-16 08:47:12.047 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-16 08:47:12.049 | INFO     | app.services.account_service:batch_auto_login:1192 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-06-16 08:47:12.049 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-16 08:47:12.050 | DEBUG    | ui.views.account_view:_update_group_counts:184 - 分组计数已更新: {2: 1, 1: 1}
2025-06-16 08:47:12.051 | DEBUG    | ui.views.account_view:_init_cache:151 - 账户缓存初始化完成，共 2 个账户
2025-06-16 08:47:12.057 | DEBUG    | ui.views.account_view:_on_accounts_loaded:575 - 账户加载完成: 2个账户
2025-06-16 08:47:12.088 | DEBUG    | ui.views.account_view:_update_group_counts:184 - 分组计数已更新: {2: 1, 1: 1}
2025-06-16 08:47:12.097 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-16 08:47:12.098 | WARNING  | core.auth.api_service:verify_vip:441 - 会员验证失败: 验证失败
2025-06-16 08:47:12.393 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-16 08:47:12.423 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-16 08:47:12.430 | INFO     | app.services.account_service:batch_auto_login:1192 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-06-16 08:47:12.431 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-16 08:47:12.432 | INFO     | app.services.account_service:batch_auto_login:1262 - 服务层：准备调用核心层处理 2 个账户的自动登录。
2025-06-16 08:47:12.433 | INFO     | app.services.account_service:batch_auto_login:1272 - 服务层：设置核心层任务超时为 120 秒。
2025-06-16 08:47:12.433 | INFO     | core.telegram.client_manager:batch_auto_login:1220 - 开始批量自动登录, 共 2 个账户, 最大并发 5
2025-06-16 08:47:12.434 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:256 - 创建客户端: h:\PyProject\TeleTest\APPDATA\sessions\+***********.session, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-06-16 08:47:12.435 | DEBUG    | core.telegram.client_manager:get_proxy:93 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-06-16 08:47:12.436 | DEBUG    | core.telegram.client_manager:get_proxy:140 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-06-16 08:47:12.441 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:256 - 创建客户端: h:\PyProject\TeleTest\APPDATA\sessions\+***********.session, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-06-16 08:47:12.442 | DEBUG    | core.telegram.client_manager:get_proxy:93 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-06-16 08:47:12.442 | DEBUG    | core.telegram.client_manager:get_proxy:140 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-06-16 08:47:12.511 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-16 08:47:15.336 | INFO     | core.telegram.client_manager:_connect_client:204 - 连接成功!
2025-06-16 08:47:15.742 | INFO     | core.telegram.client_manager:_connect_client:204 - 连接成功!
2025-06-16 08:47:16.249 | INFO     | core.telegram.client_manager:_verify_client_authorization:1195 - 账户已授权: +***********
2025-06-16 08:47:16.793 | INFO     | core.telegram.client_manager:_verify_client_authorization:1195 - 账户已授权: +***********
2025-06-16 08:47:18.796 | INFO     | core.telegram.client_manager:batch_auto_login:1369 - 批量登录完成: 总计 2, 成功 2, 失败 0
2025-06-16 08:47:19.490 | INFO     | app.services.account_service:batch_auto_login:1293 - 服务层：核心层登录完成: 成功 2, 失败 0 / 2
2025-06-16 08:47:19.850 | INFO     | ui.main_window:closeEvent:344 - MainWindow: 接收到关闭事件
2025-06-16 08:47:20.168 | INFO     | ui.main_window:_cleanup_before_quit:236 - MainWindow: 执行清理资源...
2025-06-16 08:47:20.721 | INFO     | core.telegram.client_worker:stop:110 - 正在停止Telegram客户端工作线程
2025-06-16 08:47:20.765 | INFO     | core.telegram.client_manager:cleanup_async:1140 - 开始清理资源并断开所有连接
2025-06-16 08:47:20.765 | INFO     | core.telegram.client_manager:disconnect_all_clients:725 - 正在断开所有客户端连接
2025-06-16 08:47:20.766 | INFO     | core.telegram.client_manager:_safe_disconnect:746 - 正在断开客户端连接: +***********
2025-06-16 08:47:20.767 | INFO     | core.telegram.client_manager:_safe_disconnect:746 - 正在断开客户端连接: +***********
2025-06-16 08:47:20.779 | INFO     | core.telegram.client_manager:_safe_disconnect:750 - 断开客户端连接成功: +***********
2025-06-16 08:47:20.780 | INFO     | core.telegram.client_manager:_safe_disconnect:750 - 断开客户端连接成功: +***********
2025-06-16 08:47:20.781 | INFO     | core.telegram.client_manager:cleanup_async:1159 - 资源清理完成
2025-06-16 08:47:21.269 | INFO     | core.telegram.client_manager:cleanup_async:1140 - 开始清理资源并断开所有连接
2025-06-16 08:47:21.269 | INFO     | core.telegram.client_manager:disconnect_all_clients:725 - 正在断开所有客户端连接
2025-06-16 08:47:21.270 | INFO     | core.telegram.client_manager:cleanup_async:1159 - 资源清理完成
2025-06-16 08:47:21.771 | INFO     | core.telegram.client_worker:run:99 - Telegram客户端工作线程已退出
2025-06-16 08:47:21.772 | INFO     | core.telegram.client_worker:stop:124 - Telegram客户端工作线程已停止
2025-06-16 08:47:21.772 | INFO     | ui.main_window:_cleanup_before_quit:245 - TelegramClientWorker 已停止。
2025-06-16 08:47:21.773 | INFO     | ui.main_window:_cleanup_before_quit:249 - MainWindow 清理完成
2025-06-16 08:47:21.784 | INFO     | __main__:main:111 - 应用程序已正常退出
2025-06-16 08:47:44.523 | INFO     | utils.logger:init_logger:98 - 日志系统初始化完成，默认级别: DEBUG，日志目录: h:\PyProject\TeleTest\APPDATA\logs
2025-06-16 08:47:46.047 | INFO     | data.database:init_database:31 - 初始化数据库连接: h:\PyProject\TeleTest\APPDATA\database\telegram_manager.db
2025-06-16 08:47:46.066 | DEBUG    | data.database:init_database:58 - 创建数据库表
2025-06-16 08:47:46.080 | INFO     | data.database:init_database:61 - 数据库初始化完成
2025-06-16 08:47:46.697 | DEBUG    | app.controllers.auth_controller:initialize_software:43 - 控制器: 初始化软件 app_id=1000, version=1.0
2025-06-16 08:47:46.697 | DEBUG    | utils.client_http:get:54 - 发送GET请求: https://server.xile188.com/api/user/1000/windowns10/1.0/ini
2025-06-16 08:47:47.042 | DEBUG    | core.auth.api_service:init_software:66 - 初始化数据解密成功: {'new_content': '1.多账户管理\n2.ip池管理\n3.消息监听\n4.消息群发\n5.拉人入群', 'new_url': None, 'notice': {'id': '2', 'content': '公告内容测试3', 'time': '**********'}}
2025-06-16 08:47:47.048 | INFO     | app.controllers.auth_controller:login:61 - 控制器: 用户登录 account=demo01
2025-06-16 08:47:51.138 | INFO     | core.auth.api_service:login:127 - 用户登录: account=demo01
2025-06-16 08:47:51.138 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/logon
2025-06-16 08:47:51.373 | INFO     | core.auth.api_service:get_user_info:347 - 获取用户信息
2025-06-16 08:47:51.373 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/info
2025-06-16 08:47:51.652 | INFO     | core.auth.api_service:get_user_info:375 - 获取用户信息成功
2025-06-16 08:47:51.659 | INFO     | __main__:on_auth_completed:93 - 用户登录成功，正在启动主窗口...
2025-06-16 08:47:51.677 | INFO     | core.telegram.client_worker:__init__:63 - Telegram客户端工作线程初始化
2025-06-16 08:47:51.683 | INFO     | core.telegram.client_worker:run:67 - Telegram客户端工作线程开始运行
2025-06-16 08:47:51.685 | INFO     | core.telegram.client_manager:__init__:66 - Telegram客户端管理器初始化
2025-06-16 08:47:51.686 | INFO     | core.telegram.user_manager:__init__:31 - Telegram用户管理器初始化
2025-06-16 08:47:51.686 | INFO     | core.telegram.group_manager:__init__:36 - Telegram群组管理器初始化
2025-06-16 08:47:51.686 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-06-16 08:47:51.687 | INFO     | core.telegram.monitor_manager:__init__:26 - Telegram监控管理器初始化
2025-06-16 08:47:51.687 | INFO     | core.telegram.telegram_service:__init__:39 - Telegram服务初始化完成
2025-06-16 08:47:51.688 | INFO     | ui.main_window:_initialize_core_components:80 - MainWindow: 初始化核心组件...
2025-06-16 08:47:51.689 | INFO     | app.services.account_service:__init__:42 - 账户服务初始化
2025-06-16 08:47:51.689 | INFO     | core.proxy.proxy_task_manager:run:284 - 代理任务管理器线程启动
2025-06-16 08:47:51.689 | INFO     | app.controllers.account_controller:__init__:81 - 账户控制器初始化
2025-06-16 08:47:51.690 | INFO     | app.services.monitor_service:__init__:39 - 初始化监控任务服务
2025-06-16 08:47:51.690 | INFO     | core.monitor.filter:__init__:26 - 初始化消息过滤器
2025-06-16 08:47:51.691 | INFO     | core.monitor.extractor:__init__:24 - 初始化关键词提取器
2025-06-16 08:47:51.691 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-06-16 08:47:51.692 | INFO     | core.monitor.notifier:__init__:90 - 初始化通知管理器
2025-06-16 08:47:51.692 | INFO     | app.controllers.telegram_monitor_controller:__init__:64 - 初始化 Telegram 监控控制器
2025-06-16 08:47:51.693 | DEBUG    | app.controllers.telegram_monitor_controller:__init__:82 - 控制器初始化完成
2025-06-16 08:47:51.903 | INFO     | ui.views.monitor_view:__init__:48 - 初始化监控视图
2025-06-16 08:47:51.903 | DEBUG    | ui.views.monitor_view:_connect_signals:80 - 连接控制器信号
2025-06-16 08:47:52.110 | INFO     | ui.views.proxy_view:__init__:96 - 初始化视图，加载第一页代理数据
2025-06-16 08:47:52.351 | INFO     | __main__:on_auth_completed:97 - 主窗口已启动
2025-06-16 08:47:52.396 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:88 - 尝试连接监听日志信号...
2025-06-16 08:47:52.396 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:90 - 监听日志信号连接成功
2025-06-16 08:47:52.397 | INFO     | core.auth.api_service:verify_vip:425 - 会员验证接口调用
2025-06-16 08:47:52.397 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/vip
2025-06-16 08:47:52.398 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-16 08:47:52.403 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-16 08:47:52.405 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:145 - 加载监控任务列表 (搜索关键词: '')
2025-06-16 08:47:52.406 | DEBUG    | ui.views.monitor_view:_on_loading_started:620 - 加载中: 正在加载任务列表...
2025-06-16 08:47:52.406 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-16 08:47:52.413 | INFO     | app.services.proxy_service:refresh_proxies:712 - 刷新代理列表: offset=0, limit=20
2025-06-16 08:47:52.413 | INFO     | core.proxy.proxy_task_manager:submit_task:172 - 提交耗时任务到子线程: refresh_proxies_0_20
2025-06-16 08:47:52.414 | DEBUG    | app.services.proxy_service:get_proxies:500 - 获取代理列表: offset=0, limit=20
2025-06-16 08:47:52.414 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-16 08:47:52.414 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-16 08:47:52.418 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-16 08:47:52.448 | INFO     | app.controllers.invite_controller:get_invite_tasks:38 - 获取邀请任务列表, status=None
2025-06-16 08:47:52.450 | INFO     | app.services.invite_service:get_invite_tasks:121 - 获取邀请任务列表, status=None
2025-06-16 08:47:52.451 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-16 08:47:52.451 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-16 08:47:52.452 | INFO     | app.services.invite_service:task_initialize:39 - 初始化邀请服务，重置运行中的任务为暂停状态
2025-06-16 08:47:52.453 | INFO     | core.proxy.proxy_task_manager:task_wrapper:199 - 任务 refresh_proxies_0_20 完成
2025-06-16 08:47:52.453 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-16 08:47:52.580 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-16 08:47:52.581 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-16 08:47:52.586 | INFO     | app.services.invite_service:task_initialize:48 - 成功重置 0 个任务状态
2025-06-16 08:47:52.586 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-16 08:47:52.587 | DEBUG    | ui.views.monitor_view:_refresh_task_list:175 - 刷新任务列表，共2个任务
2025-06-16 08:47:52.594 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-16 08:47:52.600 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-16 08:47:52.606 | INFO     | app.services.account_service:get_all_groups:96 - 获取所有账户分组成功, 共 2 个
2025-06-16 08:47:52.606 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-16 08:47:52.607 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-16 08:47:52.612 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-16 08:47:52.613 | DEBUG    | ui.views.account_view:_on_groups_loaded:442 - 分组加载完成: 2个分组
2025-06-16 08:47:52.629 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-16 08:47:52.705 | INFO     | app.services.account_service:get_all_accounts:289 - 获取所有账户成功, 共 2 个
2025-06-16 08:47:52.706 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-16 08:47:52.709 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-16 08:47:52.710 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-16 08:47:52.711 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-16 08:47:52.712 | DEBUG    | ui.views.account_view:_on_accounts_loaded:575 - 账户加载完成: 2个账户
2025-06-16 08:47:52.735 | DEBUG    | ui.views.account_view:_update_group_counts:184 - 分组计数已更新: {2: 1, 1: 1}
2025-06-16 08:47:52.740 | INFO     | ui.views.account_view:_auto_login_accounts:630 - 开始调用控制器进行 2 个账户的自动登录。
2025-06-16 08:47:52.740 | INFO     | app.services.account_service:batch_auto_login:1153 - 服务层：开始批量自动登录 2 个账户, 最大并发: 5
2025-06-16 08:47:52.741 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-16 08:47:52.747 | INFO     | app.controllers.telegram_monitor_controller:_load_total_users_count_internal:191 - 成功加载总体统计数据: 总用户数 1845, 今日采集 0, 日均采集 461, 运行天数 4
2025-06-16 08:47:52.748 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:156 - 成功加载 2 个任务
2025-06-16 08:47:52.748 | DEBUG    | ui.views.monitor_view:_on_loading_finished:625 - 加载完成
2025-06-16 08:47:52.749 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-06-16 08:47:52.768 | INFO     | app.services.account_service:batch_auto_login:1192 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-06-16 08:47:52.768 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-16 08:47:52.770 | INFO     | app.services.account_service:get_all_accounts:289 - 获取所有账户成功, 共 2 个
2025-06-16 08:47:52.770 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-16 08:47:52.772 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-16 08:47:52.773 | DEBUG    | ui.views.account_view:_update_group_counts:184 - 分组计数已更新: {2: 1, 1: 1}
2025-06-16 08:47:52.774 | DEBUG    | ui.views.account_view:_init_cache:151 - 账户缓存初始化完成，共 2 个账户
2025-06-16 08:47:52.781 | DEBUG    | ui.views.account_view:_on_accounts_loaded:575 - 账户加载完成: 2个账户
2025-06-16 08:47:52.805 | DEBUG    | ui.views.account_view:_update_group_counts:184 - 分组计数已更新: {2: 1, 1: 1}
2025-06-16 08:47:52.822 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=True
2025-06-16 08:47:52.828 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=True
2025-06-16 08:47:52.831 | INFO     | ui.views.proxy_view:_load_initial_data:1044 - 初始数据加载完成，共 1 个代理
2025-06-16 08:47:52.834 | WARNING  | core.auth.api_service:verify_vip:441 - 会员验证失败: 验证失败
2025-06-16 08:47:53.049 | INFO     | app.services.account_service:batch_auto_login:1192 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-06-16 08:47:53.050 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-16 08:47:53.088 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-16 08:47:53.089 | INFO     | app.services.account_service:batch_auto_login:1262 - 服务层：准备调用核心层处理 2 个账户的自动登录。
2025-06-16 08:47:53.090 | INFO     | app.services.account_service:batch_auto_login:1272 - 服务层：设置核心层任务超时为 120 秒。
2025-06-16 08:47:53.090 | INFO     | core.telegram.client_manager:batch_auto_login:1220 - 开始批量自动登录, 共 2 个账户, 最大并发 5
2025-06-16 08:47:53.092 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:256 - 创建客户端: h:\PyProject\TeleTest\APPDATA\sessions\+***********.session, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-06-16 08:47:53.093 | DEBUG    | core.telegram.client_manager:get_proxy:93 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-06-16 08:47:53.094 | DEBUG    | core.telegram.client_manager:get_proxy:140 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-06-16 08:47:53.101 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:256 - 创建客户端: h:\PyProject\TeleTest\APPDATA\sessions\+***********.session, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-06-16 08:47:53.102 | DEBUG    | core.telegram.client_manager:get_proxy:93 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-06-16 08:47:53.103 | DEBUG    | core.telegram.client_manager:get_proxy:140 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-06-16 08:47:53.129 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-16 08:47:53.155 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-16 08:47:56.133 | INFO     | core.telegram.client_manager:_connect_client:204 - 连接成功!
2025-06-16 08:47:56.250 | INFO     | core.telegram.client_manager:_connect_client:204 - 连接成功!
2025-06-16 08:47:57.060 | INFO     | core.telegram.client_manager:_verify_client_authorization:1195 - 账户已授权: +***********
2025-06-16 08:47:57.161 | INFO     | core.telegram.client_manager:_verify_client_authorization:1195 - 账户已授权: +***********
2025-06-16 08:47:59.172 | INFO     | core.telegram.client_manager:batch_auto_login:1369 - 批量登录完成: 总计 2, 成功 2, 失败 0
2025-06-16 08:48:00.108 | INFO     | app.services.account_service:batch_auto_login:1293 - 服务层：核心层登录完成: 成功 2, 失败 0 / 2
2025-06-16 08:48:51.649 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-16 08:48:51.649 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-16 08:49:51.647 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-16 08:49:51.648 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-16 08:50:51.648 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-16 08:50:51.648 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-16 08:51:07.910 | INFO     | ui.main_window:closeEvent:344 - MainWindow: 接收到关闭事件
2025-06-16 08:51:07.923 | INFO     | ui.main_window:_cleanup_before_quit:236 - MainWindow: 执行清理资源...
2025-06-16 08:51:07.924 | INFO     | core.telegram.client_worker:stop:110 - 正在停止Telegram客户端工作线程
2025-06-16 08:51:07.931 | INFO     | core.telegram.client_manager:cleanup_async:1140 - 开始清理资源并断开所有连接
2025-06-16 08:51:07.932 | INFO     | core.telegram.client_manager:disconnect_all_clients:725 - 正在断开所有客户端连接
2025-06-16 08:51:07.932 | INFO     | core.telegram.client_manager:_safe_disconnect:746 - 正在断开客户端连接: +***********
2025-06-16 08:51:07.933 | INFO     | core.telegram.client_manager:_safe_disconnect:746 - 正在断开客户端连接: +***********
2025-06-16 08:51:07.945 | INFO     | core.telegram.client_manager:_safe_disconnect:750 - 断开客户端连接成功: +***********
2025-06-16 08:51:07.945 | INFO     | core.telegram.client_manager:_safe_disconnect:750 - 断开客户端连接成功: +***********
2025-06-16 08:51:07.946 | INFO     | core.telegram.client_manager:cleanup_async:1159 - 资源清理完成
2025-06-16 08:51:08.431 | INFO     | core.telegram.client_manager:cleanup_async:1140 - 开始清理资源并断开所有连接
2025-06-16 08:51:08.431 | INFO     | core.telegram.client_manager:disconnect_all_clients:725 - 正在断开所有客户端连接
2025-06-16 08:51:08.432 | INFO     | core.telegram.client_manager:cleanup_async:1159 - 资源清理完成
2025-06-16 08:51:08.933 | INFO     | core.telegram.client_worker:run:99 - Telegram客户端工作线程已退出
2025-06-16 08:51:08.933 | INFO     | core.telegram.client_worker:stop:124 - Telegram客户端工作线程已停止
2025-06-16 08:51:08.934 | INFO     | ui.main_window:_cleanup_before_quit:245 - TelegramClientWorker 已停止。
2025-06-16 08:51:08.935 | INFO     | ui.main_window:_cleanup_before_quit:249 - MainWindow 清理完成
2025-06-16 08:51:08.937 | INFO     | __main__:main:111 - 应用程序已正常退出
2025-06-16 08:52:27.105 | INFO     | utils.logger:init_logger:98 - 日志系统初始化完成，默认级别: DEBUG，日志目录: h:\PyProject\TeleTest\APPDATA\logs
2025-06-16 08:52:28.700 | INFO     | data.database:init_database:31 - 初始化数据库连接: h:\PyProject\TeleTest\APPDATA\database\telegram_manager.db
2025-06-16 08:52:28.719 | DEBUG    | data.database:init_database:58 - 创建数据库表
2025-06-16 08:52:28.732 | INFO     | data.database:init_database:61 - 数据库初始化完成
2025-06-16 08:52:28.752 | CRITICAL | __main__:main:114 - 程序启动失败: 'AuthView' object has no attribute 'setCustomBackgroundColor'
2025-06-16 08:52:46.835 | INFO     | utils.logger:init_logger:98 - 日志系统初始化完成，默认级别: DEBUG，日志目录: h:\PyProject\TeleTest\APPDATA\logs
2025-06-16 08:52:48.311 | INFO     | data.database:init_database:31 - 初始化数据库连接: h:\PyProject\TeleTest\APPDATA\database\telegram_manager.db
2025-06-16 08:52:48.331 | DEBUG    | data.database:init_database:58 - 创建数据库表
2025-06-16 08:52:48.342 | INFO     | data.database:init_database:61 - 数据库初始化完成
2025-06-16 08:52:49.197 | DEBUG    | app.controllers.auth_controller:initialize_software:43 - 控制器: 初始化软件 app_id=1000, version=1.0
2025-06-16 08:52:49.197 | DEBUG    | utils.client_http:get:54 - 发送GET请求: https://server.xile188.com/api/user/1000/windowns10/1.0/ini
2025-06-16 08:52:49.544 | DEBUG    | core.auth.api_service:init_software:66 - 初始化数据解密成功: {'new_content': '1.多账户管理\n2.ip池管理\n3.消息监听\n4.消息群发\n5.拉人入群', 'new_url': None, 'notice': {'id': '2', 'content': '公告内容测试3', 'time': '**********'}}
2025-06-16 08:52:49.551 | INFO     | app.controllers.auth_controller:login:61 - 控制器: 用户登录 account=demo01
2025-06-16 08:52:52.554 | INFO     | core.auth.api_service:login:127 - 用户登录: account=demo01
2025-06-16 08:52:52.555 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/logon
2025-06-16 08:52:52.862 | INFO     | core.auth.api_service:get_user_info:347 - 获取用户信息
2025-06-16 08:52:52.862 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/info
2025-06-16 08:52:53.139 | INFO     | core.auth.api_service:get_user_info:375 - 获取用户信息成功
2025-06-16 08:52:53.146 | INFO     | __main__:on_auth_completed:93 - 用户登录成功，正在启动主窗口...
2025-06-16 08:52:53.171 | INFO     | core.telegram.client_worker:__init__:63 - Telegram客户端工作线程初始化
2025-06-16 08:52:53.172 | INFO     | ui.main_window:_initialize_core_components:82 - MainWindow: 初始化核心组件...
2025-06-16 08:52:53.172 | INFO     | core.telegram.client_worker:run:67 - Telegram客户端工作线程开始运行
2025-06-16 08:52:53.174 | INFO     | app.services.account_service:__init__:42 - 账户服务初始化
2025-06-16 08:52:53.174 | INFO     | core.proxy.proxy_task_manager:run:284 - 代理任务管理器线程启动
2025-06-16 08:52:53.174 | INFO     | app.controllers.account_controller:__init__:81 - 账户控制器初始化
2025-06-16 08:52:53.175 | INFO     | app.services.monitor_service:__init__:39 - 初始化监控任务服务
2025-06-16 08:52:53.175 | INFO     | core.telegram.client_manager:__init__:66 - Telegram客户端管理器初始化
2025-06-16 08:52:53.175 | INFO     | core.monitor.filter:__init__:26 - 初始化消息过滤器
2025-06-16 08:52:53.175 | INFO     | core.telegram.user_manager:__init__:31 - Telegram用户管理器初始化
2025-06-16 08:52:53.176 | INFO     | core.monitor.extractor:__init__:24 - 初始化关键词提取器
2025-06-16 08:52:53.176 | INFO     | core.telegram.group_manager:__init__:36 - Telegram群组管理器初始化
2025-06-16 08:52:53.176 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-06-16 08:52:53.176 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-06-16 08:52:53.176 | INFO     | core.monitor.notifier:__init__:90 - 初始化通知管理器
2025-06-16 08:52:53.176 | INFO     | core.telegram.monitor_manager:__init__:26 - Telegram监控管理器初始化
2025-06-16 08:52:53.177 | INFO     | app.controllers.telegram_monitor_controller:__init__:64 - 初始化 Telegram 监控控制器
2025-06-16 08:52:53.177 | INFO     | core.telegram.telegram_service:__init__:39 - Telegram服务初始化完成
2025-06-16 08:52:53.177 | DEBUG    | app.controllers.telegram_monitor_controller:__init__:82 - 控制器初始化完成
2025-06-16 08:52:53.363 | INFO     | ui.views.monitor_view:__init__:48 - 初始化监控视图
2025-06-16 08:52:53.364 | DEBUG    | ui.views.monitor_view:_connect_signals:80 - 连接控制器信号
2025-06-16 08:52:53.548 | INFO     | ui.views.proxy_view:__init__:96 - 初始化视图，加载第一页代理数据
2025-06-16 08:52:53.759 | INFO     | __main__:on_auth_completed:97 - 主窗口已启动
2025-06-16 08:52:53.812 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:88 - 尝试连接监听日志信号...
2025-06-16 08:52:53.812 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:90 - 监听日志信号连接成功
2025-06-16 08:52:53.813 | INFO     | core.auth.api_service:verify_vip:425 - 会员验证接口调用
2025-06-16 08:52:53.814 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/vip
2025-06-16 08:52:53.814 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-16 08:52:53.819 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-16 08:52:53.822 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:145 - 加载监控任务列表 (搜索关键词: '')
2025-06-16 08:52:53.823 | DEBUG    | ui.views.monitor_view:_on_loading_started:620 - 加载中: 正在加载任务列表...
2025-06-16 08:52:53.823 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-16 08:52:53.832 | INFO     | app.services.proxy_service:refresh_proxies:712 - 刷新代理列表: offset=0, limit=20
2025-06-16 08:52:53.832 | INFO     | core.proxy.proxy_task_manager:submit_task:172 - 提交耗时任务到子线程: refresh_proxies_0_20
2025-06-16 08:52:53.833 | DEBUG    | app.services.proxy_service:get_proxies:500 - 获取代理列表: offset=0, limit=20
2025-06-16 08:52:53.833 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-16 08:52:53.833 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-16 08:52:53.837 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-16 08:52:53.865 | INFO     | app.controllers.invite_controller:get_invite_tasks:38 - 获取邀请任务列表, status=None
2025-06-16 08:52:53.866 | INFO     | app.services.invite_service:get_invite_tasks:121 - 获取邀请任务列表, status=None
2025-06-16 08:52:53.868 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-16 08:52:53.869 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-16 08:52:53.869 | INFO     | app.services.invite_service:task_initialize:39 - 初始化邀请服务，重置运行中的任务为暂停状态
2025-06-16 08:52:53.870 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-16 08:52:53.870 | INFO     | core.proxy.proxy_task_manager:task_wrapper:199 - 任务 refresh_proxies_0_20 完成
2025-06-16 08:52:54.069 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-16 08:52:54.074 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-16 08:52:54.075 | INFO     | app.services.invite_service:task_initialize:48 - 成功重置 0 个任务状态
2025-06-16 08:52:54.076 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-16 08:52:54.077 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-16 08:52:54.080 | DEBUG    | ui.views.monitor_view:_refresh_task_list:175 - 刷新任务列表，共2个任务
2025-06-16 08:52:54.085 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-16 08:52:54.096 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-16 08:52:54.099 | INFO     | app.services.account_service:get_all_groups:96 - 获取所有账户分组成功, 共 2 个
2025-06-16 08:52:54.099 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-16 08:52:54.100 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-16 08:52:54.103 | DEBUG    | ui.views.account_view:_on_groups_loaded:442 - 分组加载完成: 2个分组
2025-06-16 08:52:54.117 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-16 08:52:54.184 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-16 08:52:54.185 | INFO     | app.services.account_service:get_all_accounts:289 - 获取所有账户成功, 共 2 个
2025-06-16 08:52:54.185 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-16 08:52:54.186 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-16 08:52:54.188 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-16 08:52:54.189 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-06-16 08:52:54.194 | DEBUG    | ui.views.account_view:_on_accounts_loaded:575 - 账户加载完成: 2个账户
2025-06-16 08:52:54.217 | DEBUG    | ui.views.account_view:_update_group_counts:184 - 分组计数已更新: {2: 1, 1: 1}
2025-06-16 08:52:54.221 | INFO     | ui.views.account_view:_auto_login_accounts:630 - 开始调用控制器进行 2 个账户的自动登录。
2025-06-16 08:52:54.221 | INFO     | app.services.account_service:batch_auto_login:1153 - 服务层：开始批量自动登录 2 个账户, 最大并发: 5
2025-06-16 08:52:54.222 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-16 08:52:54.232 | INFO     | app.controllers.telegram_monitor_controller:_load_total_users_count_internal:191 - 成功加载总体统计数据: 总用户数 1845, 今日采集 0, 日均采集 461, 运行天数 4
2025-06-16 08:52:54.232 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:156 - 成功加载 2 个任务
2025-06-16 08:52:54.233 | DEBUG    | ui.views.monitor_view:_on_loading_finished:625 - 加载完成
2025-06-16 08:52:54.239 | INFO     | app.services.account_service:get_all_accounts:289 - 获取所有账户成功, 共 2 个
2025-06-16 08:52:54.240 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-16 08:52:54.241 | INFO     | app.services.account_service:batch_auto_login:1192 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-06-16 08:52:54.241 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-16 08:52:54.244 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-16 08:52:54.247 | DEBUG    | ui.views.account_view:_update_group_counts:184 - 分组计数已更新: {2: 1, 1: 1}
2025-06-16 08:52:54.247 | DEBUG    | ui.views.account_view:_init_cache:151 - 账户缓存初始化完成，共 2 个账户
2025-06-16 08:52:54.253 | DEBUG    | ui.views.account_view:_on_accounts_loaded:575 - 账户加载完成: 2个账户
2025-06-16 08:52:54.280 | DEBUG    | ui.views.account_view:_update_group_counts:184 - 分组计数已更新: {2: 1, 1: 1}
2025-06-16 08:52:54.294 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=True
2025-06-16 08:52:54.298 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=True
2025-06-16 08:52:54.300 | INFO     | ui.views.proxy_view:_load_initial_data:1044 - 初始数据加载完成，共 1 个代理
2025-06-16 08:52:54.302 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-16 08:52:54.304 | WARNING  | core.auth.api_service:verify_vip:441 - 会员验证失败: 验证失败
2025-06-16 08:52:54.320 | INFO     | app.services.account_service:batch_auto_login:1192 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-06-16 08:52:54.321 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-16 08:52:54.408 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-16 08:52:54.572 | INFO     | app.services.account_service:batch_auto_login:1262 - 服务层：准备调用核心层处理 2 个账户的自动登录。
2025-06-16 08:52:54.573 | INFO     | app.services.account_service:batch_auto_login:1272 - 服务层：设置核心层任务超时为 120 秒。
2025-06-16 08:52:54.573 | INFO     | core.telegram.client_manager:batch_auto_login:1220 - 开始批量自动登录, 共 2 个账户, 最大并发 5
2025-06-16 08:52:54.574 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:256 - 创建客户端: h:\PyProject\TeleTest\APPDATA\sessions\+***********.session, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-06-16 08:52:54.575 | DEBUG    | core.telegram.client_manager:get_proxy:93 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-06-16 08:52:54.575 | DEBUG    | core.telegram.client_manager:get_proxy:140 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-06-16 08:52:54.580 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:256 - 创建客户端: h:\PyProject\TeleTest\APPDATA\sessions\+***********.session, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-06-16 08:52:54.581 | DEBUG    | core.telegram.client_manager:get_proxy:93 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-06-16 08:52:54.582 | DEBUG    | core.telegram.client_manager:get_proxy:140 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-06-16 08:52:54.592 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-16 08:52:57.835 | INFO     | core.telegram.client_manager:_connect_client:204 - 连接成功!
2025-06-16 08:52:58.046 | INFO     | core.telegram.client_manager:_connect_client:204 - 连接成功!
2025-06-16 08:52:58.831 | INFO     | core.telegram.client_manager:_verify_client_authorization:1195 - 账户已授权: +***********
2025-06-16 08:52:59.090 | INFO     | core.telegram.client_manager:_verify_client_authorization:1195 - 账户已授权: +***********
2025-06-16 08:53:01.088 | INFO     | core.telegram.client_manager:batch_auto_login:1369 - 批量登录完成: 总计 2, 成功 2, 失败 0
2025-06-16 08:53:01.586 | INFO     | app.services.account_service:batch_auto_login:1293 - 服务层：核心层登录完成: 成功 2, 失败 0 / 2
2025-06-16 08:53:53.132 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-16 08:53:53.133 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-16 08:53:54.175 | INFO     | ui.main_window:closeEvent:346 - MainWindow: 接收到关闭事件
2025-06-16 08:53:54.188 | INFO     | ui.main_window:_cleanup_before_quit:238 - MainWindow: 执行清理资源...
2025-06-16 08:53:54.189 | INFO     | core.telegram.client_worker:stop:110 - 正在停止Telegram客户端工作线程
2025-06-16 08:53:54.193 | INFO     | core.telegram.client_manager:cleanup_async:1140 - 开始清理资源并断开所有连接
2025-06-16 08:53:54.194 | INFO     | core.telegram.client_manager:disconnect_all_clients:725 - 正在断开所有客户端连接
2025-06-16 08:53:54.195 | INFO     | core.telegram.client_manager:_safe_disconnect:746 - 正在断开客户端连接: +***********
2025-06-16 08:53:54.196 | INFO     | core.telegram.client_manager:_safe_disconnect:746 - 正在断开客户端连接: +***********
2025-06-16 08:53:54.207 | INFO     | core.telegram.client_manager:_safe_disconnect:750 - 断开客户端连接成功: +***********
2025-06-16 08:53:54.207 | INFO     | core.telegram.client_manager:_safe_disconnect:750 - 断开客户端连接成功: +***********
2025-06-16 08:53:54.208 | INFO     | core.telegram.client_manager:cleanup_async:1159 - 资源清理完成
2025-06-16 08:53:54.694 | INFO     | core.telegram.client_manager:cleanup_async:1140 - 开始清理资源并断开所有连接
2025-06-16 08:53:54.695 | INFO     | core.telegram.client_manager:disconnect_all_clients:725 - 正在断开所有客户端连接
2025-06-16 08:53:54.695 | INFO     | core.telegram.client_manager:cleanup_async:1159 - 资源清理完成
2025-06-16 08:53:55.197 | INFO     | core.telegram.client_worker:run:99 - Telegram客户端工作线程已退出
2025-06-16 08:53:55.198 | INFO     | core.telegram.client_worker:stop:124 - Telegram客户端工作线程已停止
2025-06-16 08:53:55.198 | INFO     | ui.main_window:_cleanup_before_quit:247 - TelegramClientWorker 已停止。
2025-06-16 08:53:55.198 | INFO     | ui.main_window:_cleanup_before_quit:251 - MainWindow 清理完成
2025-06-16 08:53:55.202 | INFO     | __main__:main:111 - 应用程序已正常退出
2025-06-16 09:05:20.152 | INFO     | utils.logger:init_logger:98 - 日志系统初始化完成，默认级别: DEBUG，日志目录: h:\PyProject\TeleTest\APPDATA\logs
2025-06-16 09:05:21.610 | INFO     | data.database:init_database:31 - 初始化数据库连接: h:\PyProject\TeleTest\APPDATA\database\telegram_manager.db
2025-06-16 09:05:21.628 | DEBUG    | data.database:init_database:58 - 创建数据库表
2025-06-16 09:05:21.639 | INFO     | data.database:init_database:61 - 数据库初始化完成
2025-06-16 09:05:22.815 | DEBUG    | app.controllers.auth_controller:initialize_software:43 - 控制器: 初始化软件 app_id=1000, version=1.0
2025-06-16 09:05:22.816 | DEBUG    | utils.client_http:get:54 - 发送GET请求: https://server.xile188.com/api/user/1000/windowns10/1.0/ini
2025-06-16 09:05:23.195 | DEBUG    | core.auth.api_service:init_software:66 - 初始化数据解密成功: {'new_content': '1.多账户管理\n2.ip池管理\n3.消息监听\n4.消息群发\n5.拉人入群', 'new_url': None, 'notice': {'id': '2', 'content': '公告内容测试3', 'time': '**********'}}
2025-06-16 09:05:23.208 | INFO     | app.controllers.auth_controller:login:61 - 控制器: 用户登录 account=demo01
2025-06-16 09:05:26.179 | INFO     | core.auth.api_service:login:127 - 用户登录: account=demo01
2025-06-16 09:05:26.180 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/logon
2025-06-16 09:05:26.447 | INFO     | core.auth.api_service:get_user_info:347 - 获取用户信息
2025-06-16 09:05:26.447 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/info
2025-06-16 09:05:26.737 | INFO     | core.auth.api_service:get_user_info:375 - 获取用户信息成功
2025-06-16 09:05:26.744 | INFO     | __main__:on_auth_completed:93 - 用户登录成功，正在启动主窗口...
2025-06-16 09:05:26.768 | INFO     | core.telegram.client_worker:__init__:63 - Telegram客户端工作线程初始化
2025-06-16 09:05:26.768 | INFO     | ui.main_window:_initialize_core_components:82 - MainWindow: 初始化核心组件...
2025-06-16 09:05:26.769 | INFO     | core.telegram.client_worker:run:67 - Telegram客户端工作线程开始运行
2025-06-16 09:05:26.770 | INFO     | core.proxy.proxy_task_manager:run:284 - 代理任务管理器线程启动
2025-06-16 09:05:26.770 | INFO     | app.services.account_service:__init__:42 - 账户服务初始化
2025-06-16 09:05:26.771 | INFO     | app.controllers.account_controller:__init__:81 - 账户控制器初始化
2025-06-16 09:05:26.772 | INFO     | app.services.monitor_service:__init__:39 - 初始化监控任务服务
2025-06-16 09:05:26.772 | INFO     | core.telegram.client_manager:__init__:66 - Telegram客户端管理器初始化
2025-06-16 09:05:26.772 | INFO     | core.monitor.filter:__init__:26 - 初始化消息过滤器
2025-06-16 09:05:26.773 | INFO     | core.telegram.user_manager:__init__:31 - Telegram用户管理器初始化
2025-06-16 09:05:26.773 | INFO     | core.monitor.extractor:__init__:24 - 初始化关键词提取器
2025-06-16 09:05:26.773 | INFO     | core.telegram.group_manager:__init__:36 - Telegram群组管理器初始化
2025-06-16 09:05:26.774 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-06-16 09:05:26.774 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-06-16 09:05:26.774 | INFO     | core.monitor.notifier:__init__:90 - 初始化通知管理器
2025-06-16 09:05:26.774 | INFO     | core.telegram.monitor_manager:__init__:26 - Telegram监控管理器初始化
2025-06-16 09:05:26.775 | INFO     | app.controllers.telegram_monitor_controller:__init__:64 - 初始化 Telegram 监控控制器
2025-06-16 09:05:26.775 | INFO     | core.telegram.telegram_service:__init__:39 - Telegram服务初始化完成
2025-06-16 09:05:26.775 | DEBUG    | app.controllers.telegram_monitor_controller:__init__:82 - 控制器初始化完成
2025-06-16 09:05:26.956 | INFO     | ui.views.monitor_view:__init__:48 - 初始化监控视图
2025-06-16 09:05:26.957 | DEBUG    | ui.views.monitor_view:_connect_signals:80 - 连接控制器信号
2025-06-16 09:05:27.128 | INFO     | ui.views.proxy_view:__init__:96 - 初始化视图，加载第一页代理数据
2025-06-16 09:05:27.342 | INFO     | __main__:on_auth_completed:97 - 主窗口已启动
2025-06-16 09:05:27.392 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:88 - 尝试连接监听日志信号...
2025-06-16 09:05:27.392 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:90 - 监听日志信号连接成功
2025-06-16 09:05:27.393 | INFO     | core.auth.api_service:verify_vip:425 - 会员验证接口调用
2025-06-16 09:05:27.394 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/vip
2025-06-16 09:05:27.395 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-16 09:05:27.401 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-16 09:05:27.406 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:145 - 加载监控任务列表 (搜索关键词: '')
2025-06-16 09:05:27.406 | DEBUG    | ui.views.monitor_view:_on_loading_started:620 - 加载中: 正在加载任务列表...
2025-06-16 09:05:27.407 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-16 09:05:27.414 | INFO     | app.services.proxy_service:refresh_proxies:712 - 刷新代理列表: offset=0, limit=20
2025-06-16 09:05:27.414 | INFO     | core.proxy.proxy_task_manager:submit_task:172 - 提交耗时任务到子线程: refresh_proxies_0_20
2025-06-16 09:05:27.415 | DEBUG    | app.services.proxy_service:get_proxies:500 - 获取代理列表: offset=0, limit=20
2025-06-16 09:05:27.415 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-16 09:05:27.415 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-16 09:05:27.420 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-16 09:05:27.447 | INFO     | app.controllers.invite_controller:get_invite_tasks:38 - 获取邀请任务列表, status=None
2025-06-16 09:05:27.448 | INFO     | app.services.invite_service:get_invite_tasks:121 - 获取邀请任务列表, status=None
2025-06-16 09:05:27.449 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-16 09:05:27.451 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-16 09:05:27.451 | INFO     | app.services.invite_service:task_initialize:39 - 初始化邀请服务，重置运行中的任务为暂停状态
2025-06-16 09:05:27.451 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-16 09:05:27.452 | INFO     | core.proxy.proxy_task_manager:task_wrapper:199 - 任务 refresh_proxies_0_20 完成
2025-06-16 09:05:27.562 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-16 09:05:27.566 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-16 09:05:27.566 | INFO     | app.services.invite_service:task_initialize:48 - 成功重置 0 个任务状态
2025-06-16 09:05:27.566 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-16 09:05:27.568 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-16 09:05:27.570 | DEBUG    | ui.views.monitor_view:_refresh_task_list:175 - 刷新任务列表，共2个任务
2025-06-16 09:05:27.577 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-16 09:05:27.586 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-16 09:05:27.593 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-16 09:05:27.594 | INFO     | app.services.account_service:get_all_groups:96 - 获取所有账户分组成功, 共 2 个
2025-06-16 09:05:27.594 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-16 09:05:27.656 | DEBUG    | ui.views.account_view:_on_groups_loaded:442 - 分组加载完成: 2个分组
2025-06-16 09:05:27.671 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-16 09:05:27.839 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-16 09:05:27.842 | INFO     | app.services.account_service:get_all_accounts:289 - 获取所有账户成功, 共 2 个
2025-06-16 09:05:27.842 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-16 09:05:27.843 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-16 09:05:27.845 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-06-16 09:05:27.863 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-16 09:05:27.865 | INFO     | app.controllers.telegram_monitor_controller:_load_total_users_count_internal:191 - 成功加载总体统计数据: 总用户数 1845, 今日采集 0, 日均采集 461, 运行天数 4
2025-06-16 09:05:27.866 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:156 - 成功加载 2 个任务
2025-06-16 09:05:27.866 | DEBUG    | ui.views.monitor_view:_on_loading_finished:625 - 加载完成
2025-06-16 09:05:27.867 | DEBUG    | ui.views.account_view:_on_accounts_loaded:575 - 账户加载完成: 2个账户
2025-06-16 09:05:27.891 | DEBUG    | ui.views.account_view:_update_group_counts:184 - 分组计数已更新: {2: 1, 1: 1}
2025-06-16 09:05:27.896 | INFO     | ui.views.account_view:_auto_login_accounts:630 - 开始调用控制器进行 2 个账户的自动登录。
2025-06-16 09:05:27.896 | INFO     | app.services.account_service:batch_auto_login:1153 - 服务层：开始批量自动登录 2 个账户, 最大并发: 5
2025-06-16 09:05:27.897 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-16 09:05:27.910 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=True
2025-06-16 09:05:27.912 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=True
2025-06-16 09:05:27.914 | INFO     | ui.views.proxy_view:_load_initial_data:1044 - 初始数据加载完成，共 1 个代理
2025-06-16 09:05:27.922 | INFO     | app.services.account_service:get_all_accounts:289 - 获取所有账户成功, 共 2 个
2025-06-16 09:05:27.922 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-16 09:05:27.923 | INFO     | app.services.account_service:batch_auto_login:1192 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-06-16 09:05:27.924 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-16 09:05:27.928 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-16 09:05:27.929 | DEBUG    | ui.views.account_view:_update_group_counts:184 - 分组计数已更新: {2: 1, 1: 1}
2025-06-16 09:05:27.929 | DEBUG    | ui.views.account_view:_init_cache:151 - 账户缓存初始化完成，共 2 个账户
2025-06-16 09:05:27.933 | DEBUG    | ui.views.account_view:_on_accounts_loaded:575 - 账户加载完成: 2个账户
2025-06-16 09:05:27.957 | DEBUG    | ui.views.account_view:_update_group_counts:184 - 分组计数已更新: {2: 1, 1: 1}
2025-06-16 09:05:27.970 | WARNING  | core.auth.api_service:verify_vip:441 - 会员验证失败: 验证失败
2025-06-16 09:05:28.180 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-16 09:05:28.211 | INFO     | app.services.account_service:batch_auto_login:1192 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-06-16 09:05:28.211 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-16 09:05:28.239 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-16 09:05:28.243 | INFO     | app.services.account_service:batch_auto_login:1262 - 服务层：准备调用核心层处理 2 个账户的自动登录。
2025-06-16 09:05:28.244 | INFO     | app.services.account_service:batch_auto_login:1272 - 服务层：设置核心层任务超时为 120 秒。
2025-06-16 09:05:28.244 | INFO     | core.telegram.client_manager:batch_auto_login:1220 - 开始批量自动登录, 共 2 个账户, 最大并发 5
2025-06-16 09:05:28.245 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:256 - 创建客户端: h:\PyProject\TeleTest\APPDATA\sessions\+***********.session, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-06-16 09:05:28.245 | DEBUG    | core.telegram.client_manager:get_proxy:93 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-06-16 09:05:28.246 | DEBUG    | core.telegram.client_manager:get_proxy:140 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-06-16 09:05:28.249 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:256 - 创建客户端: h:\PyProject\TeleTest\APPDATA\sessions\+***********.session, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-06-16 09:05:28.250 | DEBUG    | core.telegram.client_manager:get_proxy:93 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-06-16 09:05:28.251 | DEBUG    | core.telegram.client_manager:get_proxy:140 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-06-16 09:05:28.265 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-16 09:05:31.628 | INFO     | core.telegram.client_manager:_connect_client:204 - 连接成功!
2025-06-16 09:05:31.642 | INFO     | core.telegram.client_manager:_connect_client:204 - 连接成功!
2025-06-16 09:05:32.676 | INFO     | core.telegram.client_manager:_verify_client_authorization:1195 - 账户已授权: +***********
2025-06-16 09:05:32.686 | INFO     | core.telegram.client_manager:_verify_client_authorization:1195 - 账户已授权: +***********
2025-06-16 09:05:34.687 | INFO     | core.telegram.client_manager:batch_auto_login:1369 - 批量登录完成: 总计 2, 成功 2, 失败 0
2025-06-16 09:05:35.271 | INFO     | app.services.account_service:batch_auto_login:1293 - 服务层：核心层登录完成: 成功 2, 失败 0 / 2
2025-06-16 09:06:26.723 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-16 09:06:26.724 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-16 09:07:26.729 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-16 09:07:26.729 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-16 09:08:26.726 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-16 09:08:26.727 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-16 09:09:26.726 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-16 09:09:26.727 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-16 09:10:03.801 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-16 09:10:03.812 | INFO     | app.services.account_service:get_accounts_by_group:311 - 获取分组 2 的账户成功, 共 1 个
2025-06-16 09:10:03.812 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-16 09:10:03.813 | DEBUG    | ui.views.account_view:_on_accounts_loaded:575 - 账户加载完成: 1个账户
2025-06-16 09:10:04.663 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-16 09:10:04.671 | INFO     | app.services.account_service:get_accounts_by_group:311 - 获取分组 1 的账户成功, 共 1 个
2025-06-16 09:10:04.672 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-16 09:10:04.673 | DEBUG    | ui.views.account_view:_on_accounts_loaded:575 - 账户加载完成: 1个账户
2025-06-16 09:10:05.658 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-16 09:10:05.666 | INFO     | app.services.account_service:get_all_accounts:289 - 获取所有账户成功, 共 2 个
2025-06-16 09:10:05.666 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-16 09:10:05.668 | DEBUG    | ui.views.account_view:_on_accounts_loaded:575 - 账户加载完成: 2个账户
2025-06-16 09:10:05.691 | DEBUG    | ui.views.account_view:_update_group_counts:184 - 分组计数已更新: {2: 1, 1: 1}
2025-06-16 09:10:06.208 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-16 09:10:06.219 | INFO     | app.services.account_service:get_accounts_by_group:311 - 获取分组 2 的账户成功, 共 1 个
2025-06-16 09:10:06.220 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-16 09:10:06.222 | DEBUG    | ui.views.account_view:_on_accounts_loaded:575 - 账户加载完成: 1个账户
2025-06-16 09:10:06.622 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-16 09:10:06.628 | INFO     | app.services.account_service:get_accounts_by_group:311 - 获取分组 1 的账户成功, 共 1 个
2025-06-16 09:10:06.628 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-16 09:10:06.630 | DEBUG    | ui.views.account_view:_on_accounts_loaded:575 - 账户加载完成: 1个账户
2025-06-16 09:10:08.631 | INFO     | ui.main_window:closeEvent:346 - MainWindow: 接收到关闭事件
2025-06-16 09:10:08.888 | INFO     | ui.main_window:_cleanup_before_quit:238 - MainWindow: 执行清理资源...
2025-06-16 09:10:08.942 | INFO     | core.telegram.client_worker:stop:110 - 正在停止Telegram客户端工作线程
2025-06-16 09:10:09.107 | INFO     | core.telegram.client_manager:cleanup_async:1140 - 开始清理资源并断开所有连接
2025-06-16 09:10:09.107 | INFO     | core.telegram.client_manager:disconnect_all_clients:725 - 正在断开所有客户端连接
2025-06-16 09:10:09.109 | INFO     | core.telegram.client_manager:_safe_disconnect:746 - 正在断开客户端连接: +***********
2025-06-16 09:10:09.116 | INFO     | core.telegram.client_manager:_safe_disconnect:746 - 正在断开客户端连接: +***********
2025-06-16 09:10:09.145 | INFO     | core.telegram.client_manager:_safe_disconnect:750 - 断开客户端连接成功: +***********
2025-06-16 09:10:09.148 | INFO     | core.telegram.client_manager:_safe_disconnect:750 - 断开客户端连接成功: +***********
2025-06-16 09:10:09.161 | INFO     | core.telegram.client_manager:cleanup_async:1159 - 资源清理完成
2025-06-16 09:10:09.607 | INFO     | core.telegram.client_manager:cleanup_async:1140 - 开始清理资源并断开所有连接
2025-06-16 09:10:09.607 | INFO     | core.telegram.client_manager:disconnect_all_clients:725 - 正在断开所有客户端连接
2025-06-16 09:10:09.608 | INFO     | core.telegram.client_manager:cleanup_async:1159 - 资源清理完成
2025-06-16 09:10:10.108 | INFO     | core.telegram.client_worker:run:99 - Telegram客户端工作线程已退出
2025-06-16 09:10:10.109 | INFO     | core.telegram.client_worker:stop:124 - Telegram客户端工作线程已停止
2025-06-16 09:10:10.109 | INFO     | ui.main_window:_cleanup_before_quit:247 - TelegramClientWorker 已停止。
2025-06-16 09:10:10.109 | INFO     | ui.main_window:_cleanup_before_quit:251 - MainWindow 清理完成
2025-06-16 09:10:10.115 | INFO     | __main__:main:111 - 应用程序已正常退出
2025-06-16 17:14:17.651 | INFO     | utils.logger:init_logger:98 - 日志系统初始化完成，默认级别: DEBUG，日志目录: h:\PyProject\TeleTest\APPDATA\logs
2025-06-16 17:14:21.166 | INFO     | data.database:init_database:31 - 初始化数据库连接: h:\PyProject\TeleTest\APPDATA\database\telegram_manager.db
2025-06-16 17:14:21.198 | DEBUG    | data.database:init_database:58 - 创建数据库表
2025-06-16 17:14:21.215 | INFO     | data.database:init_database:61 - 数据库初始化完成
2025-06-16 17:14:22.735 | DEBUG    | app.controllers.auth_controller:initialize_software:43 - 控制器: 初始化软件 app_id=1000, version=1.0
2025-06-16 17:14:22.736 | DEBUG    | utils.client_http:get:54 - 发送GET请求: https://server.xile188.com/api/user/1000/windowns10/1.0/ini
2025-06-16 17:14:23.303 | DEBUG    | core.auth.api_service:init_software:66 - 初始化数据解密成功: {'new_content': '1.多账户管理\n2.ip池管理\n3.消息监听\n4.消息群发\n5.拉人入群', 'new_url': None, 'notice': {'id': '2', 'content': '公告内容测试3', 'time': '**********'}}
2025-06-16 17:14:23.314 | INFO     | app.controllers.auth_controller:login:61 - 控制器: 用户登录 account=demo01
2025-06-16 17:14:26.384 | INFO     | core.auth.api_service:login:127 - 用户登录: account=demo01
2025-06-16 17:14:26.385 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/logon
2025-06-16 17:14:26.635 | INFO     | core.auth.api_service:get_user_info:347 - 获取用户信息
2025-06-16 17:14:26.635 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/info
2025-06-16 17:14:26.890 | INFO     | core.auth.api_service:get_user_info:375 - 获取用户信息成功
2025-06-16 17:14:26.897 | INFO     | __main__:on_auth_completed:93 - 用户登录成功，正在启动主窗口...
2025-06-16 17:14:26.929 | INFO     | core.telegram.client_worker:__init__:61 - Telegram客户端工作线程初始化
2025-06-16 17:14:26.930 | INFO     | ui.main_window:_initialize_core_components:82 - MainWindow: 初始化核心组件...
2025-06-16 17:14:26.930 | INFO     | core.telegram.client_worker:run:65 - Telegram客户端工作线程开始运行
2025-06-16 17:14:26.930 | INFO     | core.proxy.proxy_task_manager:run:284 - 代理任务管理器线程启动
2025-06-16 17:14:26.931 | INFO     | app.services.account_service:__init__:42 - 账户服务初始化
2025-06-16 17:14:26.932 | INFO     | app.controllers.account_controller:__init__:81 - 账户控制器初始化
2025-06-16 17:14:26.932 | INFO     | core.telegram.client_manager:__init__:66 - Telegram客户端管理器初始化
2025-06-16 17:14:26.933 | INFO     | app.services.monitor_service:__init__:39 - 初始化监控任务服务
2025-06-16 17:14:26.933 | INFO     | core.telegram.user_manager:__init__:30 - Telegram用户管理器初始化
2025-06-16 17:14:26.933 | INFO     | core.monitor.filter:__init__:26 - 初始化消息过滤器
2025-06-16 17:14:26.933 | INFO     | core.telegram.group_manager:__init__:36 - Telegram群组管理器初始化
2025-06-16 17:14:26.934 | INFO     | core.monitor.extractor:__init__:24 - 初始化关键词提取器
2025-06-16 17:14:26.934 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-06-16 17:14:26.934 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-06-16 17:14:26.934 | INFO     | core.telegram.monitor_manager:__init__:26 - Telegram监控管理器初始化
2025-06-16 17:14:26.934 | INFO     | core.monitor.notifier:__init__:90 - 初始化通知管理器
2025-06-16 17:14:26.934 | INFO     | core.telegram.telegram_service:__init__:39 - Telegram服务初始化完成
2025-06-16 17:14:26.935 | INFO     | app.controllers.telegram_monitor_controller:__init__:64 - 初始化 Telegram 监控控制器
2025-06-16 17:14:26.935 | DEBUG    | app.controllers.telegram_monitor_controller:__init__:82 - 控制器初始化完成
2025-06-16 17:14:27.157 | INFO     | ui.views.monitor_view:__init__:48 - 初始化监控视图
2025-06-16 17:14:27.158 | DEBUG    | ui.views.monitor_view:_connect_signals:80 - 连接控制器信号
2025-06-16 17:14:27.340 | INFO     | ui.views.proxy_view:__init__:96 - 初始化视图，加载第一页代理数据
2025-06-16 17:14:27.536 | INFO     | __main__:on_auth_completed:97 - 主窗口已启动
2025-06-16 17:14:27.604 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:88 - 尝试连接监听日志信号...
2025-06-16 17:14:27.605 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:90 - 监听日志信号连接成功
2025-06-16 17:14:27.606 | INFO     | core.auth.api_service:verify_vip:425 - 会员验证接口调用
2025-06-16 17:14:27.606 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/vip
2025-06-16 17:14:27.607 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-16 17:14:27.612 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-16 17:14:27.615 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:145 - 加载监控任务列表 (搜索关键词: '')
2025-06-16 17:14:27.616 | DEBUG    | ui.views.monitor_view:_on_loading_started:620 - 加载中: 正在加载任务列表...
2025-06-16 17:14:27.616 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-16 17:14:27.623 | INFO     | app.services.proxy_service:refresh_proxies:712 - 刷新代理列表: offset=0, limit=20
2025-06-16 17:14:27.624 | INFO     | core.proxy.proxy_task_manager:submit_task:172 - 提交耗时任务到子线程: refresh_proxies_0_20
2025-06-16 17:14:27.624 | DEBUG    | app.services.proxy_service:get_proxies:500 - 获取代理列表: offset=0, limit=20
2025-06-16 17:14:27.624 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-16 17:14:27.625 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-16 17:14:27.630 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-16 17:14:27.653 | INFO     | app.controllers.invite_controller:get_invite_tasks:38 - 获取邀请任务列表, status=None
2025-06-16 17:14:27.655 | INFO     | app.services.invite_service:get_invite_tasks:121 - 获取邀请任务列表, status=None
2025-06-16 17:14:27.656 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-16 17:14:27.657 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-16 17:14:27.658 | INFO     | app.services.invite_service:task_initialize:39 - 初始化邀请服务，重置运行中的任务为暂停状态
2025-06-16 17:14:27.658 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-16 17:14:27.659 | INFO     | core.proxy.proxy_task_manager:task_wrapper:199 - 任务 refresh_proxies_0_20 完成
2025-06-16 17:14:27.864 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-16 17:14:27.866 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-16 17:14:27.872 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-16 17:14:27.875 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-16 17:14:27.877 | INFO     | app.services.invite_service:task_initialize:48 - 成功重置 0 个任务状态
2025-06-16 17:14:27.878 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-16 17:14:27.879 | DEBUG    | ui.views.monitor_view:_refresh_task_list:175 - 刷新任务列表，共2个任务
2025-06-16 17:14:27.885 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-16 17:14:27.895 | INFO     | app.services.account_service:get_all_groups:96 - 获取所有账户分组成功, 共 2 个
2025-06-16 17:14:27.895 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-16 17:14:27.899 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-16 17:14:27.900 | DEBUG    | ui.views.account_view:_on_groups_loaded:442 - 分组加载完成: 2个分组
2025-06-16 17:14:27.914 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-16 17:14:27.917 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-16 17:14:27.976 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-06-16 17:14:27.995 | INFO     | app.services.account_service:get_all_accounts:289 - 获取所有账户成功, 共 2 个
2025-06-16 17:14:27.996 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-16 17:14:28.002 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-16 17:14:28.003 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-16 17:14:28.004 | DEBUG    | ui.views.account_view:_on_accounts_loaded:575 - 账户加载完成: 2个账户
2025-06-16 17:14:28.027 | DEBUG    | ui.views.account_view:_update_group_counts:184 - 分组计数已更新: {2: 1, 1: 1}
2025-06-16 17:14:28.031 | INFO     | ui.views.account_view:_auto_login_accounts:630 - 开始调用控制器进行 2 个账户的自动登录。
2025-06-16 17:14:28.032 | INFO     | app.services.account_service:batch_auto_login:1153 - 服务层：开始批量自动登录 2 个账户, 最大并发: 5
2025-06-16 17:14:28.032 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-16 17:14:28.037 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=True
2025-06-16 17:14:28.040 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=True
2025-06-16 17:14:28.043 | INFO     | ui.views.proxy_view:_load_initial_data:1044 - 初始数据加载完成，共 1 个代理
2025-06-16 17:14:28.046 | INFO     | app.controllers.telegram_monitor_controller:_load_total_users_count_internal:191 - 成功加载总体统计数据: 总用户数 1845, 今日采集 0, 日均采集 369, 运行天数 5
2025-06-16 17:14:28.046 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:156 - 成功加载 2 个任务
2025-06-16 17:14:28.047 | DEBUG    | ui.views.monitor_view:_on_loading_finished:625 - 加载完成
2025-06-16 17:14:28.051 | INFO     | app.services.account_service:batch_auto_login:1192 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-06-16 17:14:28.051 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-16 17:14:28.054 | INFO     | app.services.account_service:get_all_accounts:289 - 获取所有账户成功, 共 2 个
2025-06-16 17:14:28.055 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-16 17:14:28.057 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-16 17:14:28.061 | DEBUG    | ui.views.account_view:_update_group_counts:184 - 分组计数已更新: {2: 1, 1: 1}
2025-06-16 17:14:28.062 | DEBUG    | ui.views.account_view:_init_cache:151 - 账户缓存初始化完成，共 2 个账户
2025-06-16 17:14:28.067 | DEBUG    | ui.views.account_view:_on_accounts_loaded:575 - 账户加载完成: 2个账户
2025-06-16 17:14:28.090 | DEBUG    | ui.views.account_view:_update_group_counts:184 - 分组计数已更新: {2: 1, 1: 1}
2025-06-16 17:14:28.098 | INFO     | app.services.account_service:batch_auto_login:1192 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-06-16 17:14:28.098 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-16 17:14:28.099 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-16 17:14:28.100 | INFO     | app.services.account_service:batch_auto_login:1262 - 服务层：准备调用核心层处理 2 个账户的自动登录。
2025-06-16 17:14:28.100 | INFO     | app.services.account_service:batch_auto_login:1272 - 服务层：设置核心层任务超时为 120 秒。
2025-06-16 17:14:28.100 | INFO     | core.telegram.client_manager:batch_auto_login:1220 - 开始批量自动登录, 共 2 个账户, 最大并发 5
2025-06-16 17:14:28.101 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:256 - 创建客户端: h:\PyProject\TeleTest\APPDATA\sessions\+***********.session, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-06-16 17:14:28.102 | DEBUG    | core.telegram.client_manager:get_proxy:93 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-06-16 17:14:28.102 | DEBUG    | core.telegram.client_manager:get_proxy:140 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-06-16 17:14:28.110 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:256 - 创建客户端: h:\PyProject\TeleTest\APPDATA\sessions\+***********.session, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-06-16 17:14:28.111 | DEBUG    | core.telegram.client_manager:get_proxy:93 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-06-16 17:14:28.111 | DEBUG    | core.telegram.client_manager:get_proxy:140 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-06-16 17:14:28.135 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-16 17:14:28.140 | WARNING  | core.auth.api_service:verify_vip:441 - 会员验证失败: 验证失败
2025-06-16 17:14:28.436 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-16 17:14:31.880 | INFO     | core.telegram.client_manager:_connect_client:204 - 连接成功!
2025-06-16 17:14:32.921 | INFO     | core.telegram.client_manager:_verify_client_authorization:1195 - 账户已授权: +***********
2025-06-16 17:14:37.185 | INFO     | core.telegram.client_manager:_connect_client:204 - 连接成功!
2025-06-16 17:14:38.350 | INFO     | core.telegram.client_manager:_verify_client_authorization:1195 - 账户已授权: +***********
2025-06-16 17:14:40.363 | INFO     | core.telegram.client_manager:batch_auto_login:1369 - 批量登录完成: 总计 2, 成功 2, 失败 0
2025-06-16 17:14:41.137 | INFO     | app.services.account_service:batch_auto_login:1293 - 服务层：核心层登录完成: 成功 2, 失败 0 / 2
2025-06-16 17:14:43.748 | INFO     | app.services.account_service:refresh_account_info:595 - 刷新账户信息: 1
2025-06-16 17:14:43.749 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-16 17:14:43.759 | INFO     | core.telegram.user_manager:get_user_info:41 - 正在获取用户信息: +***********
2025-06-16 17:14:44.617 | INFO     | core.telegram.user_manager:get_user_info:66 - 获取用户信息成功: +***********
2025-06-16 17:14:45.007 | INFO     | data.repositories.account_repo:update_account:378 - 更新账户成功: 1
2025-06-16 17:14:45.015 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-16 17:14:48.258 | INFO     | app.services.account_service:refresh_account_info:595 - 刷新账户信息: 2
2025-06-16 17:14:48.259 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-16 17:14:48.267 | INFO     | core.telegram.user_manager:get_user_info:41 - 正在获取用户信息: +***********
2025-06-16 17:14:49.300 | INFO     | core.telegram.user_manager:get_user_info:66 - 获取用户信息成功: +***********
2025-06-16 17:14:49.509 | INFO     | data.repositories.account_repo:update_account:378 - 更新账户成功: 2
2025-06-16 17:14:49.515 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-16 17:15:26.883 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-16 17:15:26.883 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-16 17:16:26.885 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-16 17:16:26.885 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-16 17:17:26.882 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-16 17:17:26.883 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-16 17:18:26.883 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-16 17:18:26.884 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-16 17:19:26.881 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-16 17:19:26.882 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-16 17:20:26.883 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-16 17:20:26.884 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-16 17:21:26.885 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-16 17:21:26.886 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-16 17:22:26.881 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-16 17:22:26.882 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-16 17:23:26.887 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-16 17:23:26.887 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-16 17:24:26.888 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-16 17:24:26.888 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-16 17:25:26.890 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-16 17:25:26.891 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-16 17:26:26.884 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-16 17:26:26.884 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-16 17:27:26.881 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-16 17:27:26.882 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-16 17:28:26.881 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-16 17:28:26.882 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-16 17:28:32.419 | INFO     | ui.main_window:closeEvent:346 - MainWindow: 接收到关闭事件
2025-06-16 17:28:32.429 | INFO     | ui.main_window:_cleanup_before_quit:238 - MainWindow: 执行清理资源...
2025-06-16 17:28:32.429 | INFO     | core.telegram.client_worker:stop:108 - 正在停止Telegram客户端工作线程
2025-06-16 17:28:32.438 | INFO     | core.telegram.client_manager:cleanup_async:1140 - 开始清理资源并断开所有连接
2025-06-16 17:28:32.439 | INFO     | core.telegram.client_manager:disconnect_all_clients:725 - 正在断开所有客户端连接
2025-06-16 17:28:32.439 | INFO     | core.telegram.client_manager:_safe_disconnect:746 - 正在断开客户端连接: +***********
2025-06-16 17:28:32.440 | INFO     | core.telegram.client_manager:_safe_disconnect:746 - 正在断开客户端连接: +***********
2025-06-16 17:28:32.454 | INFO     | core.telegram.client_manager:_safe_disconnect:750 - 断开客户端连接成功: +***********
2025-06-16 17:28:32.454 | INFO     | core.telegram.client_manager:_safe_disconnect:750 - 断开客户端连接成功: +***********
2025-06-16 17:28:32.454 | INFO     | core.telegram.client_manager:cleanup_async:1159 - 资源清理完成
2025-06-16 17:28:32.941 | INFO     | core.telegram.client_manager:cleanup_async:1140 - 开始清理资源并断开所有连接
2025-06-16 17:28:32.941 | INFO     | core.telegram.client_manager:disconnect_all_clients:725 - 正在断开所有客户端连接
2025-06-16 17:28:32.942 | INFO     | core.telegram.client_manager:cleanup_async:1159 - 资源清理完成
2025-06-16 17:28:33.443 | INFO     | core.telegram.client_worker:run:97 - Telegram客户端工作线程已退出
2025-06-16 17:28:33.445 | INFO     | core.telegram.client_worker:stop:122 - Telegram客户端工作线程已停止
2025-06-16 17:28:33.445 | INFO     | ui.main_window:_cleanup_before_quit:247 - TelegramClientWorker 已停止。
2025-06-16 17:28:33.446 | INFO     | ui.main_window:_cleanup_before_quit:251 - MainWindow 清理完成
2025-06-16 17:28:33.455 | INFO     | __main__:main:111 - 应用程序已正常退出
2025-06-16 17:39:15.313 | INFO     | utils.logger:init_logger:98 - 日志系统初始化完成，默认级别: DEBUG，日志目录: h:\PyProject\TeleTest\APPDATA\logs
2025-06-16 17:39:16.874 | INFO     | data.database:init_database:31 - 初始化数据库连接: h:\PyProject\TeleTest\APPDATA\database\telegram_manager.db
2025-06-16 17:39:16.899 | DEBUG    | data.database:init_database:58 - 创建数据库表
2025-06-16 17:39:16.938 | INFO     | data.database:init_database:61 - 数据库初始化完成
2025-06-16 17:39:18.330 | DEBUG    | app.controllers.auth_controller:initialize_software:43 - 控制器: 初始化软件 app_id=1000, version=1.0
2025-06-16 17:39:18.331 | DEBUG    | utils.client_http:get:54 - 发送GET请求: https://server.xile188.com/api/user/1000/windowns10/1.0/ini
2025-06-16 17:39:18.710 | DEBUG    | core.auth.api_service:init_software:66 - 初始化数据解密成功: {'new_content': '1.多账户管理\n2.ip池管理\n3.消息监听\n4.消息群发\n5.拉人入群', 'new_url': None, 'notice': {'id': '2', 'content': '公告内容测试3', 'time': '**********'}}
2025-06-16 17:39:18.717 | INFO     | app.controllers.auth_controller:login:61 - 控制器: 用户登录 account=demo01
2025-06-16 17:39:21.672 | INFO     | core.auth.api_service:login:127 - 用户登录: account=demo01
2025-06-16 17:39:21.673 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/logon
2025-06-16 17:39:22.202 | INFO     | core.auth.api_service:get_user_info:347 - 获取用户信息
2025-06-16 17:39:22.202 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/info
2025-06-16 17:39:22.447 | INFO     | core.auth.api_service:get_user_info:375 - 获取用户信息成功
2025-06-16 17:39:22.453 | INFO     | __main__:on_auth_completed:93 - 用户登录成功，正在启动主窗口...
2025-06-16 17:39:22.476 | INFO     | core.telegram.client_worker:__init__:61 - Telegram客户端工作线程初始化
2025-06-16 17:39:22.477 | INFO     | ui.main_window:_initialize_core_components:82 - MainWindow: 初始化核心组件...
2025-06-16 17:39:22.478 | INFO     | core.telegram.client_worker:run:65 - Telegram客户端工作线程开始运行
2025-06-16 17:39:22.478 | INFO     | app.services.account_service:__init__:42 - 账户服务初始化
2025-06-16 17:39:22.478 | INFO     | core.proxy.proxy_task_manager:run:284 - 代理任务管理器线程启动
2025-06-16 17:39:22.480 | INFO     | app.controllers.account_controller:__init__:81 - 账户控制器初始化
2025-06-16 17:39:22.480 | INFO     | core.telegram.client_manager:__init__:66 - Telegram客户端管理器初始化
2025-06-16 17:39:22.480 | INFO     | app.services.monitor_service:__init__:39 - 初始化监控任务服务
2025-06-16 17:39:22.480 | INFO     | core.telegram.user_manager:__init__:30 - Telegram用户管理器初始化
2025-06-16 17:39:22.481 | INFO     | core.monitor.filter:__init__:26 - 初始化消息过滤器
2025-06-16 17:39:22.481 | INFO     | core.telegram.group_manager:__init__:36 - Telegram群组管理器初始化
2025-06-16 17:39:22.481 | INFO     | core.monitor.extractor:__init__:24 - 初始化关键词提取器
2025-06-16 17:39:22.481 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-06-16 17:39:22.482 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-06-16 17:39:22.482 | INFO     | core.telegram.monitor_manager:__init__:26 - Telegram监控管理器初始化
2025-06-16 17:39:22.482 | INFO     | core.monitor.notifier:__init__:90 - 初始化通知管理器
2025-06-16 17:39:22.482 | INFO     | core.telegram.telegram_service:__init__:39 - Telegram服务初始化完成
2025-06-16 17:39:22.483 | INFO     | app.controllers.telegram_monitor_controller:__init__:64 - 初始化 Telegram 监控控制器
2025-06-16 17:39:22.483 | DEBUG    | app.controllers.telegram_monitor_controller:__init__:82 - 控制器初始化完成
2025-06-16 17:39:22.684 | INFO     | ui.views.monitor_view:__init__:48 - 初始化监控视图
2025-06-16 17:39:22.684 | DEBUG    | ui.views.monitor_view:_connect_signals:80 - 连接控制器信号
2025-06-16 17:39:22.861 | INFO     | ui.views.proxy_view:__init__:96 - 初始化视图，加载第一页代理数据
2025-06-16 17:39:23.052 | INFO     | __main__:on_auth_completed:97 - 主窗口已启动
2025-06-16 17:39:23.093 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:88 - 尝试连接监听日志信号...
2025-06-16 17:39:23.094 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:90 - 监听日志信号连接成功
2025-06-16 17:39:23.094 | INFO     | core.auth.api_service:verify_vip:425 - 会员验证接口调用
2025-06-16 17:39:23.095 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/vip
2025-06-16 17:39:23.095 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-16 17:39:23.101 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-16 17:39:23.104 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:145 - 加载监控任务列表 (搜索关键词: '')
2025-06-16 17:39:23.104 | DEBUG    | ui.views.monitor_view:_on_loading_started:620 - 加载中: 正在加载任务列表...
2025-06-16 17:39:23.104 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-16 17:39:23.112 | INFO     | app.services.proxy_service:refresh_proxies:712 - 刷新代理列表: offset=0, limit=20
2025-06-16 17:39:23.113 | INFO     | core.proxy.proxy_task_manager:submit_task:172 - 提交耗时任务到子线程: refresh_proxies_0_20
2025-06-16 17:39:23.113 | DEBUG    | app.services.proxy_service:get_proxies:500 - 获取代理列表: offset=0, limit=20
2025-06-16 17:39:23.114 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-16 17:39:23.114 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-16 17:39:23.117 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-16 17:39:23.140 | INFO     | app.controllers.invite_controller:get_invite_tasks:38 - 获取邀请任务列表, status=None
2025-06-16 17:39:23.141 | INFO     | app.services.invite_service:get_invite_tasks:121 - 获取邀请任务列表, status=None
2025-06-16 17:39:23.141 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-16 17:39:23.144 | INFO     | app.services.invite_service:task_initialize:39 - 初始化邀请服务，重置运行中的任务为暂停状态
2025-06-16 17:39:23.145 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-16 17:39:23.146 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-16 17:39:23.147 | INFO     | core.proxy.proxy_task_manager:task_wrapper:199 - 任务 refresh_proxies_0_20 完成
2025-06-16 17:39:23.277 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-16 17:39:23.279 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-16 17:39:23.291 | INFO     | app.services.invite_service:task_initialize:48 - 成功重置 0 个任务状态
2025-06-16 17:39:23.291 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-16 17:39:23.294 | DEBUG    | ui.views.monitor_view:_refresh_task_list:175 - 刷新任务列表，共2个任务
2025-06-16 17:39:23.303 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-16 17:39:23.310 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-16 17:39:23.315 | INFO     | app.services.account_service:get_all_groups:96 - 获取所有账户分组成功, 共 2 个
2025-06-16 17:39:23.315 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-16 17:39:23.316 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-16 17:39:23.319 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-16 17:39:23.320 | DEBUG    | ui.views.account_view:_on_groups_loaded:442 - 分组加载完成: 2个分组
2025-06-16 17:39:23.337 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-16 17:39:23.399 | INFO     | app.services.account_service:get_all_accounts:289 - 获取所有账户成功, 共 2 个
2025-06-16 17:39:23.399 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-16 17:39:23.400 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-16 17:39:23.401 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-16 17:39:23.402 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-16 17:39:23.403 | DEBUG    | ui.views.account_view:_on_accounts_loaded:575 - 账户加载完成: 2个账户
2025-06-16 17:39:23.424 | DEBUG    | ui.views.account_view:_update_group_counts:184 - 分组计数已更新: {2: 1, 1: 1}
2025-06-16 17:39:23.428 | INFO     | ui.views.account_view:_auto_login_accounts:630 - 开始调用控制器进行 2 个账户的自动登录。
2025-06-16 17:39:23.429 | INFO     | app.services.account_service:batch_auto_login:1153 - 服务层：开始批量自动登录 2 个账户, 最大并发: 5
2025-06-16 17:39:23.430 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-16 17:39:23.435 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-06-16 17:39:23.448 | INFO     | app.controllers.telegram_monitor_controller:_load_total_users_count_internal:191 - 成功加载总体统计数据: 总用户数 1845, 今日采集 0, 日均采集 369, 运行天数 5
2025-06-16 17:39:23.449 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:156 - 成功加载 2 个任务
2025-06-16 17:39:23.449 | DEBUG    | ui.views.monitor_view:_on_loading_finished:625 - 加载完成
2025-06-16 17:39:23.453 | INFO     | app.services.account_service:get_all_accounts:289 - 获取所有账户成功, 共 2 个
2025-06-16 17:39:23.454 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-16 17:39:23.456 | INFO     | app.services.account_service:batch_auto_login:1192 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-06-16 17:39:23.456 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-16 17:39:23.459 | DEBUG    | ui.views.account_view:_update_group_counts:184 - 分组计数已更新: {2: 1, 1: 1}
2025-06-16 17:39:23.460 | DEBUG    | ui.views.account_view:_init_cache:151 - 账户缓存初始化完成，共 2 个账户
2025-06-16 17:39:23.465 | DEBUG    | ui.views.account_view:_on_accounts_loaded:575 - 账户加载完成: 2个账户
2025-06-16 17:39:23.492 | DEBUG    | ui.views.account_view:_update_group_counts:184 - 分组计数已更新: {2: 1, 1: 1}
2025-06-16 17:39:23.501 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-16 17:39:23.503 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=True
2025-06-16 17:39:23.505 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=True
2025-06-16 17:39:23.508 | INFO     | ui.views.proxy_view:_load_initial_data:1044 - 初始数据加载完成，共 1 个代理
2025-06-16 17:39:23.512 | INFO     | app.services.account_service:batch_auto_login:1192 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-06-16 17:39:23.513 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-16 17:39:23.513 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-16 17:39:23.515 | INFO     | app.services.account_service:batch_auto_login:1262 - 服务层：准备调用核心层处理 2 个账户的自动登录。
2025-06-16 17:39:23.516 | INFO     | app.services.account_service:batch_auto_login:1272 - 服务层：设置核心层任务超时为 120 秒。
2025-06-16 17:39:23.516 | INFO     | core.telegram.client_manager:batch_auto_login:1211 - 开始批量自动登录, 共 2 个账户, 最大并发 5
2025-06-16 17:39:23.518 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:254 - 创建客户端: h:\PyProject\TeleTest\APPDATA\sessions\+***********.session, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-06-16 17:39:23.519 | DEBUG    | core.telegram.client_manager:get_proxy:91 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-06-16 17:39:23.520 | DEBUG    | core.telegram.client_manager:get_proxy:138 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-06-16 17:39:23.527 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:254 - 创建客户端: h:\PyProject\TeleTest\APPDATA\sessions\+***********.session, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-06-16 17:39:23.528 | DEBUG    | core.telegram.client_manager:get_proxy:91 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-06-16 17:39:23.529 | DEBUG    | core.telegram.client_manager:get_proxy:138 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-06-16 17:39:23.550 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-16 17:39:23.561 | WARNING  | core.auth.api_service:verify_vip:441 - 会员验证失败: 验证失败
2025-06-16 17:39:23.803 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-16 17:39:26.884 | INFO     | core.telegram.client_manager:_connect_client:202 - 连接成功!
2025-06-16 17:39:27.748 | INFO     | core.telegram.client_manager:_connect_client:202 - 连接成功!
2025-06-16 17:39:28.149 | INFO     | core.telegram.client_manager:_verify_client_authorization:1186 - 账户已授权: +***********
2025-06-16 17:39:28.806 | INFO     | core.telegram.client_manager:_verify_client_authorization:1186 - 账户已授权: +***********
2025-06-16 17:39:30.809 | INFO     | core.telegram.client_manager:batch_auto_login:1360 - 批量登录完成: 总计 2, 成功 2, 失败 0
2025-06-16 17:39:31.552 | INFO     | app.services.account_service:batch_auto_login:1293 - 服务层：核心层登录完成: 成功 2, 失败 0 / 2
2025-06-16 17:39:33.413 | INFO     | ui.views.add_msg_task_view:_load_initial_data:136 - 加载初始数据
2025-06-16 17:39:33.457 | INFO     | ui.views.add_msg_task_view:_load_account_groups:146 - 加载账户分组
2025-06-16 17:39:33.457 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-16 17:39:33.466 | INFO     | app.services.account_service:get_all_groups:96 - 获取所有账户分组成功, 共 2 个
2025-06-16 17:39:33.466 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-16 17:39:33.467 | DEBUG    | ui.views.account_view:_on_groups_loaded:442 - 分组加载完成: 2个分组
2025-06-16 17:39:33.468 | INFO     | ui.views.add_msg_task_view:_load_account_groups:162 - 加载账户分组数据: 2个分组
2025-06-16 17:39:33.468 | INFO     | ui.views.add_msg_task_view:_load_accounts_by_group:177 - 根据分组ID加载账户: -1
2025-06-16 17:39:33.469 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-16 17:39:33.475 | INFO     | app.services.account_service:get_all_accounts:289 - 获取所有账户成功, 共 2 个
2025-06-16 17:39:33.476 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-16 17:39:33.599 | INFO     | ui.views.add_msg_task_view:_load_accounts_by_group:213 - 已加载 2 个账户到列表
2025-06-16 17:39:33.600 | DEBUG    | ui.views.account_view:_on_accounts_loaded:575 - 账户加载完成: 2个账户
2025-06-16 17:39:33.620 | DEBUG    | ui.views.account_view:_update_group_counts:184 - 分组计数已更新: {2: 1, 1: 1}
2025-06-16 17:40:22.449 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-16 17:40:22.450 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-16 17:41:12.770 | INFO     | ui.main_window:closeEvent:346 - MainWindow: 接收到关闭事件
2025-06-16 17:41:12.785 | INFO     | ui.main_window:_cleanup_before_quit:238 - MainWindow: 执行清理资源...
2025-06-16 17:41:12.786 | INFO     | core.telegram.client_worker:stop:108 - 正在停止Telegram客户端工作线程
2025-06-16 17:41:12.791 | INFO     | core.telegram.client_manager:cleanup_async:1131 - 开始清理资源并断开所有连接
2025-06-16 17:41:12.792 | INFO     | core.telegram.client_manager:disconnect_all_clients:716 - 正在断开所有客户端连接
2025-06-16 17:41:12.793 | INFO     | core.telegram.client_manager:_safe_disconnect:737 - 正在断开客户端连接: +***********
2025-06-16 17:41:12.794 | INFO     | core.telegram.client_manager:_safe_disconnect:737 - 正在断开客户端连接: +***********
2025-06-16 17:41:12.808 | INFO     | core.telegram.client_manager:_safe_disconnect:741 - 断开客户端连接成功: +***********
2025-06-16 17:41:12.808 | INFO     | core.telegram.client_manager:_safe_disconnect:741 - 断开客户端连接成功: +***********
2025-06-16 17:41:12.809 | INFO     | core.telegram.client_manager:cleanup_async:1150 - 资源清理完成
2025-06-16 17:41:13.293 | INFO     | core.telegram.client_manager:cleanup_async:1131 - 开始清理资源并断开所有连接
2025-06-16 17:41:13.293 | INFO     | core.telegram.client_manager:disconnect_all_clients:716 - 正在断开所有客户端连接
2025-06-16 17:41:13.294 | INFO     | core.telegram.client_manager:cleanup_async:1150 - 资源清理完成
2025-06-16 17:41:13.795 | INFO     | core.telegram.client_worker:run:97 - Telegram客户端工作线程已退出
2025-06-16 17:41:13.796 | INFO     | core.telegram.client_worker:stop:122 - Telegram客户端工作线程已停止
2025-06-16 17:41:13.797 | INFO     | ui.main_window:_cleanup_before_quit:247 - TelegramClientWorker 已停止。
2025-06-16 17:41:13.797 | INFO     | ui.main_window:_cleanup_before_quit:251 - MainWindow 清理完成
2025-06-16 17:41:13.812 | INFO     | __main__:main:111 - 应用程序已正常退出
2025-06-16 23:07:09.002 | INFO     | utils.logger:init_logger:98 - 日志系统初始化完成，默认级别: DEBUG，日志目录: h:\PyProject\TeleTest\APPDATA\logs
2025-06-16 23:07:11.871 | INFO     | data.database:init_database:31 - 初始化数据库连接: h:\PyProject\TeleTest\APPDATA\database\telegram_manager.db
2025-06-16 23:07:11.902 | DEBUG    | data.database:init_database:58 - 创建数据库表
2025-06-16 23:07:11.919 | INFO     | data.database:init_database:61 - 数据库初始化完成
2025-06-16 23:07:13.475 | DEBUG    | app.controllers.auth_controller:initialize_software:43 - 控制器: 初始化软件 app_id=1000, version=1.0
2025-06-16 23:07:13.476 | DEBUG    | utils.client_http:get:54 - 发送GET请求: https://server.xile188.com/api/user/1000/windowns10/1.0/ini
2025-06-16 23:07:13.995 | DEBUG    | core.auth.api_service:init_software:66 - 初始化数据解密成功: {'new_content': '1.多账户管理\n2.ip池管理\n3.消息监听\n4.消息群发\n5.拉人入群', 'new_url': None, 'notice': {'id': '2', 'content': '公告内容测试3', 'time': '**********'}}
2025-06-16 23:07:14.008 | INFO     | app.controllers.auth_controller:login:61 - 控制器: 用户登录 account=demo01
2025-06-16 23:07:17.155 | INFO     | core.auth.api_service:login:127 - 用户登录: account=demo01
2025-06-16 23:07:17.155 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/logon
2025-06-16 23:07:17.456 | INFO     | core.auth.api_service:get_user_info:347 - 获取用户信息
2025-06-16 23:07:17.456 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/info
2025-06-16 23:07:17.733 | INFO     | core.auth.api_service:get_user_info:375 - 获取用户信息成功
2025-06-16 23:07:17.742 | INFO     | __main__:on_auth_completed:93 - 用户登录成功，正在启动主窗口...
2025-06-16 23:07:17.783 | INFO     | core.telegram.client_worker:__init__:61 - Telegram客户端工作线程初始化
2025-06-16 23:07:17.784 | INFO     | ui.main_window:_initialize_core_components:82 - MainWindow: 初始化核心组件...
2025-06-16 23:07:17.785 | INFO     | core.telegram.client_worker:run:65 - Telegram客户端工作线程开始运行
2025-06-16 23:07:17.786 | INFO     | core.proxy.proxy_task_manager:run:284 - 代理任务管理器线程启动
2025-06-16 23:07:17.786 | INFO     | app.services.account_service:__init__:42 - 账户服务初始化
2025-06-16 23:07:17.787 | INFO     | app.controllers.account_controller:__init__:81 - 账户控制器初始化
2025-06-16 23:07:17.788 | INFO     | app.services.monitor_service:__init__:39 - 初始化监控任务服务
2025-06-16 23:07:17.788 | INFO     | core.telegram.client_manager:__init__:66 - Telegram客户端管理器初始化
2025-06-16 23:07:17.788 | INFO     | core.monitor.filter:__init__:26 - 初始化消息过滤器
2025-06-16 23:07:17.788 | INFO     | core.telegram.user_manager:__init__:30 - Telegram用户管理器初始化
2025-06-16 23:07:17.789 | INFO     | core.monitor.extractor:__init__:24 - 初始化关键词提取器
2025-06-16 23:07:17.789 | INFO     | core.telegram.group_manager:__init__:36 - Telegram群组管理器初始化
2025-06-16 23:07:17.789 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-06-16 23:07:17.789 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-06-16 23:07:17.790 | INFO     | core.monitor.notifier:__init__:90 - 初始化通知管理器
2025-06-16 23:07:17.790 | INFO     | core.telegram.monitor_manager:__init__:26 - Telegram监控管理器初始化
2025-06-16 23:07:17.790 | INFO     | app.controllers.telegram_monitor_controller:__init__:64 - 初始化 Telegram 监控控制器
2025-06-16 23:07:17.791 | INFO     | core.telegram.telegram_service:__init__:39 - Telegram服务初始化完成
2025-06-16 23:07:17.791 | DEBUG    | app.controllers.telegram_monitor_controller:__init__:82 - 控制器初始化完成
2025-06-16 23:07:18.001 | INFO     | ui.views.monitor_view:__init__:48 - 初始化监控视图
2025-06-16 23:07:18.001 | DEBUG    | ui.views.monitor_view:_connect_signals:80 - 连接控制器信号
2025-06-16 23:07:18.178 | INFO     | ui.views.proxy_view:__init__:96 - 初始化视图，加载第一页代理数据
2025-06-16 23:07:18.389 | INFO     | __main__:on_auth_completed:97 - 主窗口已启动
2025-06-16 23:07:18.454 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:88 - 尝试连接监听日志信号...
2025-06-16 23:07:18.455 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:90 - 监听日志信号连接成功
2025-06-16 23:07:18.456 | INFO     | core.auth.api_service:verify_vip:425 - 会员验证接口调用
2025-06-16 23:07:18.456 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/vip
2025-06-16 23:07:18.457 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-16 23:07:18.463 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-16 23:07:18.466 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:145 - 加载监控任务列表 (搜索关键词: '')
2025-06-16 23:07:18.467 | DEBUG    | ui.views.monitor_view:_on_loading_started:620 - 加载中: 正在加载任务列表...
2025-06-16 23:07:18.467 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-16 23:07:18.474 | INFO     | app.services.proxy_service:refresh_proxies:712 - 刷新代理列表: offset=0, limit=20
2025-06-16 23:07:18.475 | INFO     | core.proxy.proxy_task_manager:submit_task:172 - 提交耗时任务到子线程: refresh_proxies_0_20
2025-06-16 23:07:18.476 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-16 23:07:18.477 | DEBUG    | app.services.proxy_service:get_proxies:500 - 获取代理列表: offset=0, limit=20
2025-06-16 23:07:18.478 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-16 23:07:18.480 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-16 23:07:18.529 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-16 23:07:18.534 | INFO     | core.proxy.proxy_task_manager:task_wrapper:199 - 任务 refresh_proxies_0_20 完成
2025-06-16 23:07:18.532 | INFO     | app.controllers.invite_controller:get_invite_tasks:38 - 获取邀请任务列表, status=None
2025-06-16 23:07:18.536 | INFO     | app.services.invite_service:get_invite_tasks:121 - 获取邀请任务列表, status=None
2025-06-16 23:07:18.540 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-16 23:07:18.543 | INFO     | app.services.invite_service:task_initialize:39 - 初始化邀请服务，重置运行中的任务为暂停状态
2025-06-16 23:07:18.544 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-16 23:07:18.620 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-16 23:07:18.623 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-16 23:07:18.628 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-16 23:07:18.629 | INFO     | app.services.invite_service:task_initialize:48 - 成功重置 0 个任务状态
2025-06-16 23:07:18.630 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-16 23:07:18.631 | DEBUG    | ui.views.monitor_view:_refresh_task_list:175 - 刷新任务列表，共2个任务
2025-06-16 23:07:18.636 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-16 23:07:18.643 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-16 23:07:18.650 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-16 23:07:18.651 | INFO     | app.services.account_service:get_all_groups:96 - 获取所有账户分组成功, 共 2 个
2025-06-16 23:07:18.652 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-16 23:07:18.709 | DEBUG    | ui.views.account_view:_on_groups_loaded:442 - 分组加载完成: 2个分组
2025-06-16 23:07:18.723 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-16 23:07:18.733 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-16 23:07:18.734 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-16 23:07:18.736 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-06-16 23:07:18.759 | INFO     | app.services.account_service:get_all_accounts:289 - 获取所有账户成功, 共 2 个
2025-06-16 23:07:18.760 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-16 23:07:18.763 | INFO     | app.controllers.telegram_monitor_controller:_load_total_users_count_internal:191 - 成功加载总体统计数据: 总用户数 1845, 今日采集 0, 日均采集 369, 运行天数 5
2025-06-16 23:07:18.764 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:156 - 成功加载 2 个任务
2025-06-16 23:07:18.764 | DEBUG    | ui.views.monitor_view:_on_loading_finished:625 - 加载完成
2025-06-16 23:07:18.765 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-16 23:07:18.767 | DEBUG    | ui.views.account_view:_on_accounts_loaded:575 - 账户加载完成: 2个账户
2025-06-16 23:07:18.792 | DEBUG    | ui.views.account_view:_update_group_counts:184 - 分组计数已更新: {2: 1, 1: 1}
2025-06-16 23:07:18.797 | INFO     | ui.views.account_view:_auto_login_accounts:630 - 开始调用控制器进行 2 个账户的自动登录。
2025-06-16 23:07:18.798 | INFO     | app.services.account_service:batch_auto_login:1153 - 服务层：开始批量自动登录 2 个账户, 最大并发: 5
2025-06-16 23:07:18.798 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-16 23:07:18.804 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=True
2025-06-16 23:07:18.806 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=True
2025-06-16 23:07:18.809 | INFO     | ui.views.proxy_view:_load_initial_data:1044 - 初始数据加载完成，共 1 个代理
2025-06-16 23:07:18.814 | INFO     | app.services.account_service:get_all_accounts:289 - 获取所有账户成功, 共 2 个
2025-06-16 23:07:18.815 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-16 23:07:18.816 | INFO     | app.services.account_service:batch_auto_login:1192 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-06-16 23:07:18.817 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-16 23:07:18.819 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-16 23:07:18.821 | DEBUG    | ui.views.account_view:_update_group_counts:184 - 分组计数已更新: {2: 1, 1: 1}
2025-06-16 23:07:18.822 | DEBUG    | ui.views.account_view:_init_cache:151 - 账户缓存初始化完成，共 2 个账户
2025-06-16 23:07:18.826 | DEBUG    | ui.views.account_view:_on_accounts_loaded:575 - 账户加载完成: 2个账户
2025-06-16 23:07:18.848 | DEBUG    | ui.views.account_view:_update_group_counts:184 - 分组计数已更新: {2: 1, 1: 1}
2025-06-16 23:07:18.855 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-16 23:07:18.857 | INFO     | app.services.account_service:batch_auto_login:1192 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-06-16 23:07:18.858 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-16 23:07:18.888 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-16 23:07:18.893 | INFO     | app.services.account_service:batch_auto_login:1262 - 服务层：准备调用核心层处理 2 个账户的自动登录。
2025-06-16 23:07:18.894 | INFO     | app.services.account_service:batch_auto_login:1272 - 服务层：设置核心层任务超时为 120 秒。
2025-06-16 23:07:18.894 | INFO     | core.telegram.client_manager:batch_auto_login:1211 - 开始批量自动登录, 共 2 个账户, 最大并发 5
2025-06-16 23:07:18.895 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:254 - 创建客户端: h:\PyProject\TeleTest\APPDATA\sessions\+***********.session, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-06-16 23:07:18.896 | DEBUG    | core.telegram.client_manager:get_proxy:91 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-06-16 23:07:18.897 | DEBUG    | core.telegram.client_manager:get_proxy:138 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-06-16 23:07:18.903 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:254 - 创建客户端: h:\PyProject\TeleTest\APPDATA\sessions\+***********.session, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-06-16 23:07:18.904 | DEBUG    | core.telegram.client_manager:get_proxy:91 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-06-16 23:07:18.904 | DEBUG    | core.telegram.client_manager:get_proxy:138 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-06-16 23:07:18.914 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-16 23:07:18.955 | WARNING  | core.auth.api_service:verify_vip:441 - 会员验证失败: 验证失败
2025-06-16 23:07:22.816 | INFO     | core.telegram.client_manager:_connect_client:202 - 连接成功!
2025-06-16 23:07:23.065 | INFO     | core.telegram.client_manager:_connect_client:202 - 连接成功!
2025-06-16 23:07:23.796 | INFO     | core.telegram.client_manager:_verify_client_authorization:1186 - 账户已授权: +***********
2025-06-16 23:07:24.069 | INFO     | core.telegram.client_manager:_verify_client_authorization:1186 - 账户已授权: +***********
2025-06-16 23:07:26.072 | INFO     | core.telegram.client_manager:batch_auto_login:1360 - 批量登录完成: 总计 2, 成功 2, 失败 0
2025-06-16 23:07:26.903 | INFO     | app.services.account_service:batch_auto_login:1293 - 服务层：核心层登录完成: 成功 2, 失败 0 / 2
2025-06-16 23:08:17.729 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-16 23:08:17.729 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-16 23:09:17.726 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-16 23:09:17.726 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-16 23:10:17.727 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-16 23:10:17.727 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-16 23:11:17.725 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-16 23:11:17.726 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-16 23:12:17.731 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-16 23:12:17.731 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-16 23:13:17.726 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-16 23:13:17.727 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-16 23:14:17.735 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-16 23:14:17.735 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-16 23:15:17.724 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-16 23:15:17.724 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-16 23:16:17.725 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-16 23:16:17.725 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-16 23:17:17.727 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-16 23:17:17.728 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-16 23:18:17.725 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-16 23:18:17.725 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-16 23:19:17.725 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-16 23:19:17.726 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-16 23:20:17.726 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-16 23:20:17.726 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-16 23:21:17.724 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-16 23:21:17.725 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-16 23:22:17.725 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-16 23:22:17.726 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-16 23:23:17.728 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-16 23:23:17.729 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-16 23:24:17.726 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-16 23:24:17.727 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-16 23:25:05.357 | INFO     | ui.main_window:closeEvent:346 - MainWindow: 接收到关闭事件
2025-06-16 23:25:05.372 | INFO     | ui.main_window:_cleanup_before_quit:238 - MainWindow: 执行清理资源...
2025-06-16 23:25:05.374 | INFO     | core.telegram.client_worker:stop:108 - 正在停止Telegram客户端工作线程
2025-06-16 23:25:05.382 | INFO     | core.telegram.client_manager:cleanup_async:1131 - 开始清理资源并断开所有连接
2025-06-16 23:25:05.383 | INFO     | core.telegram.client_manager:disconnect_all_clients:716 - 正在断开所有客户端连接
2025-06-16 23:25:05.383 | INFO     | core.telegram.client_manager:_safe_disconnect:737 - 正在断开客户端连接: +***********
2025-06-16 23:25:05.384 | INFO     | core.telegram.client_manager:_safe_disconnect:737 - 正在断开客户端连接: +***********
2025-06-16 23:25:05.394 | INFO     | core.telegram.client_manager:_safe_disconnect:741 - 断开客户端连接成功: +***********
2025-06-16 23:25:05.395 | INFO     | core.telegram.client_manager:_safe_disconnect:741 - 断开客户端连接成功: +***********
2025-06-16 23:25:05.396 | INFO     | core.telegram.client_manager:cleanup_async:1150 - 资源清理完成
2025-06-16 23:25:05.881 | INFO     | core.telegram.client_manager:cleanup_async:1131 - 开始清理资源并断开所有连接
2025-06-16 23:25:05.881 | INFO     | core.telegram.client_manager:disconnect_all_clients:716 - 正在断开所有客户端连接
2025-06-16 23:25:05.882 | INFO     | core.telegram.client_manager:cleanup_async:1150 - 资源清理完成
2025-06-16 23:25:06.383 | INFO     | core.telegram.client_worker:run:97 - Telegram客户端工作线程已退出
2025-06-16 23:25:06.384 | INFO     | core.telegram.client_worker:stop:122 - Telegram客户端工作线程已停止
2025-06-16 23:25:06.384 | INFO     | ui.main_window:_cleanup_before_quit:247 - TelegramClientWorker 已停止。
2025-06-16 23:25:06.384 | INFO     | ui.main_window:_cleanup_before_quit:251 - MainWindow 清理完成
2025-06-16 23:25:06.394 | INFO     | __main__:main:111 - 应用程序已正常退出
