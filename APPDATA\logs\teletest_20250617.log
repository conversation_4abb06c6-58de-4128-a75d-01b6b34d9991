2025-06-17 21:25:44.587 | INFO     | utils.logger:init_logger:98 - 日志系统初始化完成，默认级别: DEBUG，日志目录: H:\PyProject\TeleTest\APPDATA\logs
2025-06-17 21:25:48.131 | INFO     | data.database:init_database:31 - 初始化数据库连接: H:\PyProject\TeleTest\APPDATA\database\telegram_manager.db
2025-06-17 21:25:48.171 | DEBUG    | data.database:init_database:58 - 创建数据库表
2025-06-17 21:25:48.192 | INFO     | data.database:init_database:61 - 数据库初始化完成
2025-06-17 21:25:52.926 | DEBUG    | app.controllers.auth_controller:initialize_software:43 - 控制器: 初始化软件 app_id=1000, version=1.0
2025-06-17 21:25:52.926 | DEBUG    | utils.client_http:get:54 - 发送GET请求: https://server.xile188.com/api/user/1000/windowns10/1.0/ini
2025-06-17 21:25:53.490 | DEBUG    | core.auth.api_service:init_software:66 - 初始化数据解密成功: {'new_content': '1.多账户管理\n2.ip池管理\n3.消息监听\n4.消息群发\n5.拉人入群', 'new_url': None, 'notice': {'id': '2', 'content': '公告内容测试3', 'time': '**********'}}
2025-06-17 21:25:53.500 | INFO     | app.controllers.auth_controller:login:61 - 控制器: 用户登录 account=demo01
2025-06-17 21:25:56.848 | INFO     | core.auth.api_service:login:127 - 用户登录: account=demo01
2025-06-17 21:25:56.849 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/logon
2025-06-17 21:25:57.085 | INFO     | core.auth.api_service:get_user_info:347 - 获取用户信息
2025-06-17 21:25:57.086 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/info
2025-06-17 21:25:57.369 | INFO     | core.auth.api_service:get_user_info:375 - 获取用户信息成功
2025-06-17 21:25:57.375 | INFO     | __main__:on_auth_completed:93 - 用户登录成功，正在启动主窗口...
2025-06-17 21:25:57.405 | INFO     | core.telegram.client_worker:__init__:61 - Telegram客户端工作线程初始化
2025-06-17 21:25:57.405 | INFO     | ui.main_window:_initialize_core_components:82 - MainWindow: 初始化核心组件...
2025-06-17 21:25:57.405 | INFO     | core.telegram.client_worker:run:65 - Telegram客户端工作线程开始运行
2025-06-17 21:25:57.406 | INFO     | core.proxy.proxy_task_manager:run:284 - 代理任务管理器线程启动
2025-06-17 21:25:57.408 | INFO     | app.services.account_service:__init__:42 - 账户服务初始化
2025-06-17 21:25:57.408 | INFO     | app.controllers.account_controller:__init__:81 - 账户控制器初始化
2025-06-17 21:25:57.408 | INFO     | app.services.monitor_service:__init__:39 - 初始化监控任务服务
2025-06-17 21:25:57.408 | INFO     | core.monitor.filter:__init__:26 - 初始化消息过滤器
2025-06-17 21:25:57.409 | INFO     | core.monitor.extractor:__init__:24 - 初始化关键词提取器
2025-06-17 21:25:57.409 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-06-17 21:25:57.410 | INFO     | core.monitor.notifier:__init__:90 - 初始化通知管理器
2025-06-17 21:25:57.410 | INFO     | core.telegram.client_manager:__init__:66 - Telegram客户端管理器初始化
2025-06-17 21:25:57.411 | INFO     | app.controllers.telegram_monitor_controller:__init__:64 - 初始化 Telegram 监控控制器
2025-06-17 21:25:57.411 | INFO     | core.telegram.user_manager:__init__:30 - Telegram用户管理器初始化
2025-06-17 21:25:57.411 | DEBUG    | app.controllers.telegram_monitor_controller:__init__:82 - 控制器初始化完成
2025-06-17 21:25:57.412 | INFO     | core.telegram.group_manager:__init__:36 - Telegram群组管理器初始化
2025-06-17 21:25:57.412 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-06-17 21:25:57.414 | INFO     | core.telegram.monitor_manager:__init__:26 - Telegram监控管理器初始化
2025-06-17 21:25:57.414 | INFO     | core.telegram.telegram_service:__init__:39 - Telegram服务初始化完成
2025-06-17 21:25:57.654 | INFO     | ui.views.monitor_view:__init__:48 - 初始化监控视图
2025-06-17 21:25:57.655 | DEBUG    | ui.views.monitor_view:_connect_signals:80 - 连接控制器信号
2025-06-17 21:25:57.864 | INFO     | ui.views.proxy_view:__init__:96 - 初始化视图，加载第一页代理数据
2025-06-17 21:25:58.100 | INFO     | __main__:on_auth_completed:97 - 主窗口已启动
2025-06-17 21:25:58.174 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:88 - 尝试连接监听日志信号...
2025-06-17 21:25:58.174 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:90 - 监听日志信号连接成功
2025-06-17 21:25:58.174 | INFO     | core.auth.api_service:verify_vip:425 - 会员验证接口调用
2025-06-17 21:25:58.175 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/vip
2025-06-17 21:25:58.176 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-17 21:25:58.182 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-17 21:25:58.186 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:145 - 加载监控任务列表 (搜索关键词: '')
2025-06-17 21:25:58.187 | DEBUG    | ui.views.monitor_view:_on_loading_started:620 - 加载中: 正在加载任务列表...
2025-06-17 21:25:58.187 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-17 21:25:58.195 | INFO     | app.services.proxy_service:refresh_proxies:712 - 刷新代理列表: offset=0, limit=20
2025-06-17 21:25:58.195 | INFO     | core.proxy.proxy_task_manager:submit_task:172 - 提交耗时任务到子线程: refresh_proxies_0_20
2025-06-17 21:25:58.196 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-17 21:25:58.196 | DEBUG    | app.services.proxy_service:get_proxies:500 - 获取代理列表: offset=0, limit=20
2025-06-17 21:25:58.198 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-17 21:25:58.204 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-17 21:25:58.208 | INFO     | app.controllers.invite_controller:get_invite_tasks:38 - 获取邀请任务列表, status=None
2025-06-17 21:25:58.209 | INFO     | app.services.invite_service:get_invite_tasks:121 - 获取邀请任务列表, status=None
2025-06-17 21:25:58.209 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-17 21:25:58.235 | INFO     | app.services.invite_service:task_initialize:39 - 初始化邀请服务，重置运行中的任务为暂停状态
2025-06-17 21:25:58.246 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-17 21:25:58.270 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-17 21:25:58.272 | INFO     | core.proxy.proxy_task_manager:task_wrapper:199 - 任务 refresh_proxies_0_20 完成
2025-06-17 21:25:58.494 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-17 21:25:58.502 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-17 21:25:58.515 | DEBUG    | ui.views.monitor_view:_refresh_task_list:175 - 刷新任务列表，共2个任务
2025-06-17 21:25:58.521 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-17 21:25:58.529 | INFO     | app.services.invite_service:task_initialize:48 - 成功重置 0 个任务状态
2025-06-17 21:25:58.529 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-17 21:25:58.532 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-17 21:25:58.546 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-17 21:25:58.549 | INFO     | app.services.account_service:get_all_groups:96 - 获取所有账户分组成功, 共 2 个
2025-06-17 21:25:58.549 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-17 21:25:58.551 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-17 21:25:58.562 | DEBUG    | ui.views.account_view:_on_groups_loaded:442 - 分组加载完成: 2个分组
2025-06-17 21:25:58.577 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-17 21:25:58.672 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-17 21:25:58.674 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-17 21:25:58.676 | INFO     | app.services.account_service:get_all_accounts:289 - 获取所有账户成功, 共 2 个
2025-06-17 21:25:58.676 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-17 21:25:58.685 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-06-17 21:25:58.706 | INFO     | app.controllers.telegram_monitor_controller:_load_total_users_count_internal:191 - 成功加载总体统计数据: 总用户数 1845, 今日采集 0, 日均采集 308, 运行天数 6
2025-06-17 21:25:58.706 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:156 - 成功加载 2 个任务
2025-06-17 21:25:58.707 | DEBUG    | ui.views.monitor_view:_on_loading_finished:625 - 加载完成
2025-06-17 21:25:58.710 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-17 21:25:58.716 | DEBUG    | ui.views.account_view:_on_accounts_loaded:575 - 账户加载完成: 2个账户
2025-06-17 21:25:58.739 | DEBUG    | ui.views.account_view:_update_group_counts:184 - 分组计数已更新: {2: 1, 1: 1}
2025-06-17 21:25:58.744 | INFO     | ui.views.account_view:_auto_login_accounts:630 - 开始调用控制器进行 2 个账户的自动登录。
2025-06-17 21:25:58.744 | INFO     | app.services.account_service:batch_auto_login:1153 - 服务层：开始批量自动登录 2 个账户, 最大并发: 5
2025-06-17 21:25:58.744 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-17 21:25:58.760 | WARNING  | core.auth.api_service:verify_vip:441 - 会员验证失败: 验证失败
2025-06-17 21:25:58.810 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=True
2025-06-17 21:25:58.812 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=True
2025-06-17 21:25:58.814 | INFO     | ui.views.proxy_view:_load_initial_data:1044 - 初始数据加载完成，共 1 个代理
2025-06-17 21:25:59.020 | INFO     | app.services.account_service:get_all_accounts:289 - 获取所有账户成功, 共 2 个
2025-06-17 21:25:59.020 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-17 21:25:59.022 | INFO     | app.services.account_service:batch_auto_login:1192 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-06-17 21:25:59.022 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-17 21:25:59.027 | DEBUG    | ui.views.account_view:_update_group_counts:184 - 分组计数已更新: {2: 1, 1: 1}
2025-06-17 21:25:59.027 | DEBUG    | ui.views.account_view:_init_cache:151 - 账户缓存初始化完成，共 2 个账户
2025-06-17 21:25:59.032 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-17 21:25:59.034 | DEBUG    | ui.views.account_view:_on_accounts_loaded:575 - 账户加载完成: 2个账户
2025-06-17 21:25:59.055 | DEBUG    | ui.views.account_view:_update_group_counts:184 - 分组计数已更新: {2: 1, 1: 1}
2025-06-17 21:25:59.072 | INFO     | app.services.account_service:batch_auto_login:1192 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-06-17 21:25:59.072 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-17 21:25:59.074 | INFO     | app.services.account_service:batch_auto_login:1262 - 服务层：准备调用核心层处理 2 个账户的自动登录。
2025-06-17 21:25:59.075 | INFO     | app.services.account_service:batch_auto_login:1272 - 服务层：设置核心层任务超时为 120 秒。
2025-06-17 21:25:59.075 | INFO     | core.telegram.client_manager:batch_auto_login:1211 - 开始批量自动登录, 共 2 个账户, 最大并发 5
2025-06-17 21:25:59.076 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-17 21:25:59.078 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:254 - 创建客户端: H:\PyProject\TeleTest\APPDATA\sessions\+***********.session, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-06-17 21:25:59.078 | DEBUG    | core.telegram.client_manager:get_proxy:91 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-06-17 21:25:59.078 | DEBUG    | core.telegram.client_manager:get_proxy:138 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-06-17 21:25:59.083 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:254 - 创建客户端: H:\PyProject\TeleTest\APPDATA\sessions\+***********.session, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-06-17 21:25:59.084 | DEBUG    | core.telegram.client_manager:get_proxy:91 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-06-17 21:25:59.084 | DEBUG    | core.telegram.client_manager:get_proxy:138 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-06-17 21:25:59.126 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-17 21:25:59.162 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-17 21:26:02.871 | INFO     | core.telegram.client_manager:_connect_client:202 - 连接成功!
2025-06-17 21:26:03.538 | INFO     | core.telegram.client_manager:_connect_client:202 - 连接成功!
2025-06-17 21:26:03.856 | INFO     | core.telegram.client_manager:_verify_client_authorization:1186 - 账户已授权: +***********
2025-06-17 21:26:04.606 | INFO     | core.telegram.client_manager:_verify_client_authorization:1186 - 账户已授权: +***********
2025-06-17 21:26:06.555 | DEBUG    | ui.views.monitor_view:_on_task_item_clicked:258 - 任务项点击: 1
2025-06-17 21:26:06.559 | INFO     | app.controllers.telegram_monitor_controller:select_task:237 - 选择任务: 1
2025-06-17 21:26:06.559 | DEBUG    | ui.views.monitor_view:_on_task_selection_changed:281 - 任务选择变更: 1
2025-06-17 21:26:06.560 | DEBUG    | ui.views.monitor_view:_on_loading_started:620 - 加载中: 正在加载任务详情...
2025-06-17 21:26:06.560 | INFO     | app.controllers.telegram_monitor_controller:load_task_details:261 - 加载任务详情: ID 1
2025-06-17 21:26:06.560 | INFO     | app.services.monitor_service:get_task_detail:1018 - 获取任务详情: ID 1
2025-06-17 21:26:06.560 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-17 21:26:06.580 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-17 21:26:06.582 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-17 21:26:06.597 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-17 21:26:06.598 | DEBUG    | app.controllers.telegram_monitor_controller:load_task_details:271 - 获取到任务 1 的统计数据: {'total_users': 1839, 'today_users': 0, 'avg_daily': 306, 'running_days': 6}
2025-06-17 21:26:06.599 | DEBUG    | ui.views.monitor_view:_on_task_details_loaded:309 - 任务详情加载完成: 1
2025-06-17 21:26:06.600 | DEBUG    | ui.views.monitor_view:_on_task_details_loaded:312 - 任务详情数据: {'created_at': '2025-06-11 10:02:49', 'description': '', 'id': '1', 'ignore_keywords': [], 'ignore_nicknames_rules': [], 'is_active': True, 'is_running': False, 'keywords': [], 'last_error': "所有账户监控设置均失败: object tuple can't be used in 'await' expression, object tuple can't be used in 'await' expression, object tuple can't be used in 'await' expression", 'monitor_messages': True, 'monitor_new_users': False, 'monitored_chats': [{'account_phone': '***********', 'chat_id': '**********', 'chat_title': '泰国华人交友聊天总群', 'id': 32}, {'account_phone': '***********', 'chat_id': '**********', 'chat_title': '东南亚博彩代理聊天群', 'id': 33}, {'account_phone': '***********', 'chat_id': '**********', 'chat_title': '柬埔寨泰国华人聊天群', 'id': 34}, {'account_phone': '***********', 'chat_id': '**********', 'chat_title': '赚钱项目灰产社区', 'id': 35}, {'account_phone': '***********', 'chat_id': '**********', 'chat_title': '区块节点NodeMining-Pool', 'id': 36}, {'account_phone': '***********', 'chat_id': '**********', 'chat_title': '区块节点NodeMining-Pool', 'id': 37}, {'account_phone': '***********', 'chat_id': '**********', 'chat_title': '深圳集团 - 藏楼（聊天', 'id': 38}, {'account_phone': '***********', 'chat_id': '**********', 'chat_title': '广州会所资源_(部分预览)', 'id': 39}, {'account_phone': '***********', 'chat_id': '**********', 'chat_title': '🔥入金话术馆', 'id': 40}, {'account_phone': '***********', 'chat_id': '**********', 'chat_title': 'wiegnwi', 'id': 41}, {'account_phone': '***********', 'chat_id': '**********', 'chat_title': '国外 话术 素材总部', 'id': 42}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '💡站长群 HostCLi.com 宝塔纯净版', 'id': 43}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '云泽社区', 'id': 44}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '🍒 萝莉屋18岁 资源群🦄', 'id': 45}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '柯基币-礦池-官方社区', 'id': 46}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '🈲 高中 学生资源库（加密）🎀', 'id': 47}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '🅰️中文搜片搜索👙自慰🩸抠逼🔍乱伦🗣口交', 'id': 48}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '中文算力MinerDog pool', 'id': 49}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '【官方】币安矿池置换中文群🇨🇳', 'id': 50}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '香港服务器交流群', 'id': 51}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '免费情色视频搜索🚀', 'id': 52}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '迷🦊 吃瓜 完整版破案‼️', 'id': 53}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '软件开发🪸搭建网站🪸网站开发', 'id': 54}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '国际联盟—海外实卡接码/邮寄公开群【支持担保】', 'id': 55}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': 'wiegnwi', 'id': 56}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '嘿嘿嘿', 'id': 57}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '泡妞笔记', 'id': 58}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '仙楼集团·资源发布站 @xianlou', 'id': 59}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '路易频道引流推广工作群', 'id': 60}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '神奇宝贝 @so588', 'id': 61}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': 'TGbox助手|群发|拉人|引流|群发助手', 'id': 62}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '比特币购买，比特币行情，比特币怎么买', 'id': 63}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '泰国华人交友聊天总群', 'id': 138}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '东南亚博彩代理聊天群', 'id': 139}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '柬埔寨泰国电子体育群', 'id': 140}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '赚钱项目灰产社区', 'id': 141}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '干到你没钱16', 'id': 142}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '深圳集团 - 藏楼1.0', 'id': 143}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '索引群聊❤️搜索王', 'id': 144}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': 'Token FRAG Allocation', 'id': 145}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '广州会所资源_(部分预览)', 'id': 146}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '区块节点NodeMining-Pool', 'id': 147}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '区块节点NodeMining-Pool', 'id': 148}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '🔥入金话术馆', 'id': 149}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '国外 话术 素材总部', 'id': 150}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '神州联盟🇨🇳 海外顶尖话术', 'id': 151}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '精聊话术-人设套图大全', 'id': 152}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '狗推瑟瑟👠嗨吹群', 'id': 153}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': 'wiegnwi', 'id': 154}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '干到你没钱20', 'id': 155}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '🟥官方直开跑分🟦日赚过万项目🟨收款码代收🟧', 'id': 156}], 'name': '全部群组', 'stats': {'avg_daily': 306, 'running_days': 6, 'today_users': 0, 'total_users': 1839}, 'updated_at': '2025-06-11 10:02:49'}
2025-06-17 21:26:06.600 | DEBUG    | ui.views.monitor_view:_on_task_details_loaded:316 - 统计数据: {'avg_daily': 306, 'running_days': 6, 'today_users': 0, 'total_users': 1839}
2025-06-17 21:26:06.602 | DEBUG    | ui.views.monitor_view:_on_loading_started:620 - 加载中: 正在加载第 1 页数据...
2025-06-17 21:26:06.602 | INFO     | app.controllers.telegram_monitor_controller:load_task_users:396 - 加载任务用户数据: ID 1, 页码 1, 每页 10 条
2025-06-17 21:26:06.602 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-17 21:26:06.606 | INFO     | core.telegram.client_manager:batch_auto_login:1360 - 批量登录完成: 总计 2, 成功 2, 失败 0
2025-06-17 21:26:06.621 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-17 21:26:06.622 | INFO     | ui.views.monitor_view:_update_users_table:342 - 开始更新用户表格，收到10条数据
2025-06-17 21:26:06.622 | DEBUG    | ui.views.monitor_view:_update_users_table:343 - 分页信息：{'current_page': 1, 'page_size': 10, 'total_items': 1869, 'total_pages': 187}
2025-06-17 21:26:06.623 | INFO     | ui.views.monitor_view:_update_users_table:360 - 示例数据[0]: {'join_date': '2025-06-11 04:18:21', 'keyword': '', 'nickname': 'SL-TN-GV 注册邮箱 卖家 UU（海外）卖家🌈', 'task_type': 'text', 'uid': '7796651829', 'username': ''}
2025-06-17 21:26:06.623 | INFO     | ui.views.monitor_view:_update_users_table:360 - 示例数据[1]: {'join_date': '2025-06-11 04:18:19', 'keyword': '', 'nickname': 'ao wu', 'task_type': 'text', 'uid': '7365791847', 'username': ''}
2025-06-17 21:26:06.623 | INFO     | ui.views.monitor_view:_update_users_table:360 - 示例数据[2]: {'join_date': '2025-06-11 04:18:12', 'keyword': '', 'nickname': 'ST艾伦 None', 'task_type': 'text', 'uid': '7729912032', 'username': ''}
2025-06-17 21:26:06.624 | INFO     | ui.views.monitor_view:_update_users_table:364 - 设置表格行数: 10
2025-06-17 21:26:06.635 | INFO     | ui.views.monitor_view:_update_users_table:388 - 用户表格更新完成
2025-06-17 21:26:06.636 | INFO     | app.controllers.telegram_monitor_controller:load_task_users:423 - 成功加载第 1 页用户数据，共 10 条
2025-06-17 21:26:06.636 | DEBUG    | ui.views.monitor_view:_on_loading_finished:625 - 加载完成
2025-06-17 21:26:06.636 | INFO     | app.controllers.telegram_monitor_controller:load_task_details:287 - 成功加载任务详情: 全部群组
2025-06-17 21:26:06.636 | DEBUG    | ui.views.monitor_view:_on_loading_finished:625 - 加载完成
2025-06-17 21:26:07.085 | INFO     | app.services.account_service:batch_auto_login:1293 - 服务层：核心层登录完成: 成功 2, 失败 0 / 2
2025-06-17 21:26:08.387 | INFO     | ui.views.monitor_view:_confirm_delete_task:547 - 确认删除任务: 1
2025-06-17 21:26:57.367 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-17 21:26:57.368 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-17 21:27:57.368 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-17 21:27:57.368 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-17 21:28:57.367 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-17 21:28:57.368 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-17 21:29:57.369 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-17 21:29:57.369 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-17 21:30:57.368 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-17 21:30:57.369 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-17 21:31:13.292 | DEBUG    | ui.views.monitor_view:_on_task_item_clicked:258 - 任务项点击: 2
2025-06-17 21:31:13.295 | INFO     | app.controllers.telegram_monitor_controller:select_task:237 - 选择任务: 2
2025-06-17 21:31:13.296 | DEBUG    | ui.views.monitor_view:_on_task_selection_changed:281 - 任务选择变更: 2
2025-06-17 21:31:13.296 | DEBUG    | ui.views.monitor_view:_on_loading_started:620 - 加载中: 正在加载任务详情...
2025-06-17 21:31:13.296 | INFO     | app.controllers.telegram_monitor_controller:load_task_details:261 - 加载任务详情: ID 2
2025-06-17 21:31:13.296 | INFO     | app.services.monitor_service:get_task_detail:1018 - 获取任务详情: ID 2
2025-06-17 21:31:13.296 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-17 21:31:13.308 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-17 21:31:13.310 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-17 21:31:13.319 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-17 21:31:13.320 | DEBUG    | app.controllers.telegram_monitor_controller:load_task_details:271 - 获取到任务 2 的统计数据: {'total_users': 24, 'today_users': 0, 'avg_daily': 4, 'running_days': 6}
2025-06-17 21:31:13.321 | DEBUG    | ui.views.monitor_view:_on_task_details_loaded:309 - 任务详情加载完成: 2
2025-06-17 21:31:13.321 | DEBUG    | ui.views.monitor_view:_on_task_details_loaded:312 - 任务详情数据: {'created_at': '2025-06-11 10:02:49', 'description': '', 'id': '2', 'ignore_keywords': [], 'ignore_nicknames_rules': [], 'is_active': True, 'is_running': False, 'keywords': ['开户', '开通', '登录', '注册'], 'last_error': "所有账户监控设置均失败: object tuple can't be used in 'await' expression, object tuple can't be used in 'await' expression", 'monitor_messages': True, 'monitor_new_users': False, 'monitored_chats': [{'account_phone': '***********', 'chat_id': '**********', 'chat_title': '泰国华人交友聊天总群', 'id': 100}, {'account_phone': '***********', 'chat_id': '**********', 'chat_title': '赚钱项目灰产社区', 'id': 101}, {'account_phone': '***********', 'chat_id': '**********', 'chat_title': '深圳集团 - 藏楼1.0', 'id': 102}, {'account_phone': '***********', 'chat_id': '**********', 'chat_title': '干到你没钱16', 'id': 103}, {'account_phone': '***********', 'chat_id': '**********', 'chat_title': '东南亚博彩代理聊天群', 'id': 104}, {'account_phone': '***********', 'chat_id': '**********', 'chat_title': '柬埔寨泰国电子体育群', 'id': 105}, {'account_phone': '***********', 'chat_id': '**********', 'chat_title': '索引群聊❤️搜索王', 'id': 106}, {'account_phone': '***********', 'chat_id': '**********', 'chat_title': 'wiegnwi', 'id': 107}, {'account_phone': '***********', 'chat_id': '**********', 'chat_title': '区块节点NodeMining-Pool', 'id': 108}, {'account_phone': '***********', 'chat_id': '**********', 'chat_title': '区块节点NodeMining-Pool', 'id': 109}, {'account_phone': '***********', 'chat_id': '**********', 'chat_title': '🔥入金话术馆', 'id': 110}, {'account_phone': '***********', 'chat_id': '**********', 'chat_title': '广州会所资源_(部分预览)', 'id': 111}, {'account_phone': '***********', 'chat_id': '**********', 'chat_title': '内涵段子🔥搞笑视频', 'id': 112}, {'account_phone': '***********', 'chat_id': '**********', 'chat_title': '国外 话术 素材总部', 'id': 113}, {'account_phone': '***********', 'chat_id': '**********', 'chat_title': '精聊话术-人设套图大全', 'id': 114}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '🅰️中文搜片搜索👙自慰🩸抠逼🔍乱伦🗣口交', 'id': 115}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '软件开发🪸搭建网站🪸网站开发', 'id': 116}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '迷🦊 吃瓜 完整版破案‼️', 'id': 117}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '【官方】币安矿池置换中文群🇨🇳👤', 'id': 118}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '🈲 高中 学生资源库（加密）🎀', 'id': 119}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '🍒 萝莉屋18岁 资源群🦄', 'id': 120}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '【官方】币安矿池置换中文群🇨🇳', 'id': 121}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '【官方】币安BNB挖矿中文总社区🇨🇳❤️', 'id': 122}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '免费情色视频搜索🚀', 'id': 123}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '香港服务器交流群', 'id': 124}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': 'wiegnwi', 'id': 125}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '💡站长群 HostCLi.com 宝塔纯净版', 'id': 126}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': 'Fragment Universal Drop', 'id': 127}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '柯基币-礦池-官方社区', 'id': 128}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '中文算力MinerDog pool', 'id': 129}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '云泽社区', 'id': 130}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '国际联盟—海外实卡接码/邮寄公开群【支持担保】', 'id': 131}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '仙楼集团·资源发布站 @xianlou', 'id': 132}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '路易频道引流推广工作群', 'id': 133}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '泡妞笔记', 'id': 134}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '神奇宝贝 @so588', 'id': 135}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': 'TGbox助手|群发|拉人|引流|群发助手', 'id': 136}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '比特币购买，比特币行情，比特币怎么买', 'id': 137}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '泰国华人交友聊天总群', 'id': 157}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '索引群聊❤️搜索王', 'id': 158}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '东南亚博彩代理聊天群', 'id': 159}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '深圳集团 - 藏楼1.0', 'id': 160}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '柬埔寨泰国电子体育群', 'id': 161}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '赚钱项目灰产社区', 'id': 162}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '干到你没钱16', 'id': 163}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': 'Token FRAG Allocation', 'id': 164}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '广州会所资源_(部分预览)', 'id': 165}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '区块节点NodeMining-Pool', 'id': 166}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '区块节点NodeMining-Pool', 'id': 167}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '🔥入金话术馆', 'id': 168}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '国外 话术 素材总部', 'id': 169}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '神州联盟🇨🇳 海外顶尖话术', 'id': 170}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '精聊话术-人设套图大全', 'id': 171}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '狗推瑟瑟👠嗨吹群', 'id': 172}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': 'wiegnwi', 'id': 173}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '干到你没钱20', 'id': 174}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '🟥官方直开跑分🟦日赚过万项目🟨收款码代收🟧', 'id': 175}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '💡站长群 HostCLi.com 宝塔纯净版', 'id': 176}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '香港服务器交流群', 'id': 177}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '🈲 高中 学生资源库（加密）🎀', 'id': 178}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '🍒 萝莉屋18岁 资源群🦄', 'id': 179}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '🅰️中文搜片搜索👙自慰🩸抠逼🔍乱伦🗣口交', 'id': 180}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '币安BNB矿池官方社区🅥', 'id': 181}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '【官方】币安矿池置换中文群🇨🇳👤', 'id': 182}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '【官方】币安BNB挖矿中文总社区🇨🇳❤️❤️', 'id': 183}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '免费情色视频搜索🚀', 'id': 184}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '迷🦊 吃瓜 完整版破案‼️', 'id': 185}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '国际联盟—海外实卡接码/邮寄公开群【支持担保】', 'id': 186}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '【官方】币安矿池置换中文群🇨🇳❤️❤️', 'id': 187}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': 'FRAG Rewards', 'id': 188}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '柯基币-礦池-官方社区', 'id': 189}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '中文算力MinerDog pool', 'id': 190}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '云泽社区', 'id': 191}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '仙楼集团·资源发布站 @xianlou', 'id': 192}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '软件开发🪸搭建网站🪸网站开发', 'id': 193}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '路易频道引流推广工作群【结算时间19.00】', 'id': 194}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': 'wiegnwi', 'id': 195}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '泡妞笔记', 'id': 196}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': 'TGbox助手|群发|拉人|引流|群发助手', 'id': 197}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '比特币购买，比特币行情，比特币怎么买', 'id': 198}], 'name': '开户监听', 'stats': {'avg_daily': 4, 'running_days': 6, 'today_users': 0, 'total_users': 24}, 'updated_at': '2025-06-11 10:02:49'}
2025-06-17 21:31:13.322 | DEBUG    | ui.views.monitor_view:_on_task_details_loaded:316 - 统计数据: {'avg_daily': 4, 'running_days': 6, 'today_users': 0, 'total_users': 24}
2025-06-17 21:31:13.323 | DEBUG    | ui.views.monitor_view:_on_loading_started:620 - 加载中: 正在加载第 1 页数据...
2025-06-17 21:31:13.323 | INFO     | app.controllers.telegram_monitor_controller:load_task_users:396 - 加载任务用户数据: ID 2, 页码 1, 每页 10 条
2025-06-17 21:31:13.323 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-17 21:31:13.336 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-17 21:31:13.337 | INFO     | ui.views.monitor_view:_update_users_table:342 - 开始更新用户表格，收到10条数据
2025-06-17 21:31:13.337 | DEBUG    | ui.views.monitor_view:_update_users_table:343 - 分页信息：{'current_page': 1, 'page_size': 10, 'total_items': 24, 'total_pages': 3}
2025-06-17 21:31:13.337 | INFO     | ui.views.monitor_view:_update_users_table:360 - 示例数据[0]: {'join_date': '2025-06-11 04:13:04', 'keyword': '登录', 'nickname': '小高高 日 Phone login Tn web login SL fer', 'task_type': 'text', 'uid': '8010084637', 'username': ''}
2025-06-17 21:31:13.337 | INFO     | ui.views.monitor_view:_update_users_table:360 - 示例数据[1]: {'join_date': '2025-06-11 04:06:39', 'keyword': '注册', 'nickname': '中文索引 None', 'task_type': 'text', 'uid': '5064505565', 'username': ''}
2025-06-17 21:31:13.337 | INFO     | ui.views.monitor_view:_update_users_table:360 - 示例数据[2]: {'join_date': '2025-06-11 03:55:12', 'keyword': '注册', 'nickname': '币安矿池质押中文播报📣 None', 'task_type': 'text', 'uid': '8097600256', 'username': 'Binance_BNBP00L_Bot'}
2025-06-17 21:31:13.338 | INFO     | ui.views.monitor_view:_update_users_table:364 - 设置表格行数: 10
2025-06-17 21:31:13.345 | INFO     | ui.views.monitor_view:_update_users_table:388 - 用户表格更新完成
2025-06-17 21:31:13.346 | INFO     | app.controllers.telegram_monitor_controller:load_task_users:423 - 成功加载第 1 页用户数据，共 10 条
2025-06-17 21:31:13.346 | DEBUG    | ui.views.monitor_view:_on_loading_finished:625 - 加载完成
2025-06-17 21:31:13.346 | INFO     | app.controllers.telegram_monitor_controller:load_task_details:287 - 成功加载任务详情: 开户监听
2025-06-17 21:31:13.346 | DEBUG    | ui.views.monitor_view:_on_loading_finished:625 - 加载完成
2025-06-17 21:31:57.365 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-17 21:31:57.365 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-17 21:32:57.365 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-17 21:32:57.365 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-17 21:33:57.367 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-17 21:33:57.367 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-17 21:34:57.373 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-17 21:34:57.373 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-17 21:35:57.369 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-17 21:35:57.369 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-17 21:36:57.370 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-17 21:36:57.371 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-17 21:37:57.366 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-17 21:37:57.366 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-17 21:38:38.748 | DEBUG    | ui.views.monitor_view:_on_task_item_clicked:258 - 任务项点击: 1
2025-06-17 21:38:38.751 | INFO     | app.controllers.telegram_monitor_controller:select_task:237 - 选择任务: 1
2025-06-17 21:38:38.751 | DEBUG    | ui.views.monitor_view:_on_task_selection_changed:281 - 任务选择变更: 1
2025-06-17 21:38:38.752 | DEBUG    | ui.views.monitor_view:_on_loading_started:620 - 加载中: 正在加载任务详情...
2025-06-17 21:38:38.752 | INFO     | app.controllers.telegram_monitor_controller:load_task_details:261 - 加载任务详情: ID 1
2025-06-17 21:38:38.752 | INFO     | app.services.monitor_service:get_task_detail:1018 - 获取任务详情: ID 1
2025-06-17 21:38:38.752 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-17 21:38:38.762 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-17 21:38:38.764 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-17 21:38:38.774 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-17 21:38:38.775 | DEBUG    | app.controllers.telegram_monitor_controller:load_task_details:271 - 获取到任务 1 的统计数据: {'total_users': 1839, 'today_users': 0, 'avg_daily': 306, 'running_days': 6}
2025-06-17 21:38:38.776 | DEBUG    | ui.views.monitor_view:_on_task_details_loaded:309 - 任务详情加载完成: 1
2025-06-17 21:38:38.776 | DEBUG    | ui.views.monitor_view:_on_task_details_loaded:312 - 任务详情数据: {'created_at': '2025-06-11 10:02:49', 'description': '', 'id': '1', 'ignore_keywords': [], 'ignore_nicknames_rules': [], 'is_active': True, 'is_running': False, 'keywords': [], 'last_error': "所有账户监控设置均失败: object tuple can't be used in 'await' expression, object tuple can't be used in 'await' expression, object tuple can't be used in 'await' expression", 'monitor_messages': True, 'monitor_new_users': False, 'monitored_chats': [{'account_phone': '***********', 'chat_id': '**********', 'chat_title': '泰国华人交友聊天总群', 'id': 32}, {'account_phone': '***********', 'chat_id': '**********', 'chat_title': '东南亚博彩代理聊天群', 'id': 33}, {'account_phone': '***********', 'chat_id': '**********', 'chat_title': '柬埔寨泰国华人聊天群', 'id': 34}, {'account_phone': '***********', 'chat_id': '**********', 'chat_title': '赚钱项目灰产社区', 'id': 35}, {'account_phone': '***********', 'chat_id': '**********', 'chat_title': '区块节点NodeMining-Pool', 'id': 36}, {'account_phone': '***********', 'chat_id': '**********', 'chat_title': '区块节点NodeMining-Pool', 'id': 37}, {'account_phone': '***********', 'chat_id': '**********', 'chat_title': '深圳集团 - 藏楼（聊天', 'id': 38}, {'account_phone': '***********', 'chat_id': '**********', 'chat_title': '广州会所资源_(部分预览)', 'id': 39}, {'account_phone': '***********', 'chat_id': '**********', 'chat_title': '🔥入金话术馆', 'id': 40}, {'account_phone': '***********', 'chat_id': '**********', 'chat_title': 'wiegnwi', 'id': 41}, {'account_phone': '***********', 'chat_id': '**********', 'chat_title': '国外 话术 素材总部', 'id': 42}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '💡站长群 HostCLi.com 宝塔纯净版', 'id': 43}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '云泽社区', 'id': 44}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '🍒 萝莉屋18岁 资源群🦄', 'id': 45}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '柯基币-礦池-官方社区', 'id': 46}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '🈲 高中 学生资源库（加密）🎀', 'id': 47}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '🅰️中文搜片搜索👙自慰🩸抠逼🔍乱伦🗣口交', 'id': 48}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '中文算力MinerDog pool', 'id': 49}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '【官方】币安矿池置换中文群🇨🇳', 'id': 50}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '香港服务器交流群', 'id': 51}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '免费情色视频搜索🚀', 'id': 52}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '迷🦊 吃瓜 完整版破案‼️', 'id': 53}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '软件开发🪸搭建网站🪸网站开发', 'id': 54}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '国际联盟—海外实卡接码/邮寄公开群【支持担保】', 'id': 55}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': 'wiegnwi', 'id': 56}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '嘿嘿嘿', 'id': 57}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '泡妞笔记', 'id': 58}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '仙楼集团·资源发布站 @xianlou', 'id': 59}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '路易频道引流推广工作群', 'id': 60}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '神奇宝贝 @so588', 'id': 61}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': 'TGbox助手|群发|拉人|引流|群发助手', 'id': 62}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '比特币购买，比特币行情，比特币怎么买', 'id': 63}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '泰国华人交友聊天总群', 'id': 138}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '东南亚博彩代理聊天群', 'id': 139}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '柬埔寨泰国电子体育群', 'id': 140}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '赚钱项目灰产社区', 'id': 141}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '干到你没钱16', 'id': 142}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '深圳集团 - 藏楼1.0', 'id': 143}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '索引群聊❤️搜索王', 'id': 144}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': 'Token FRAG Allocation', 'id': 145}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '广州会所资源_(部分预览)', 'id': 146}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '区块节点NodeMining-Pool', 'id': 147}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '区块节点NodeMining-Pool', 'id': 148}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '🔥入金话术馆', 'id': 149}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '国外 话术 素材总部', 'id': 150}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '神州联盟🇨🇳 海外顶尖话术', 'id': 151}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '精聊话术-人设套图大全', 'id': 152}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '狗推瑟瑟👠嗨吹群', 'id': 153}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': 'wiegnwi', 'id': 154}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '干到你没钱20', 'id': 155}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '🟥官方直开跑分🟦日赚过万项目🟨收款码代收🟧', 'id': 156}], 'name': '全部群组', 'stats': {'avg_daily': 306, 'running_days': 6, 'today_users': 0, 'total_users': 1839}, 'updated_at': '2025-06-11 10:02:49'}
2025-06-17 21:38:38.777 | DEBUG    | ui.views.monitor_view:_on_task_details_loaded:316 - 统计数据: {'avg_daily': 306, 'running_days': 6, 'today_users': 0, 'total_users': 1839}
2025-06-17 21:38:38.778 | DEBUG    | ui.views.monitor_view:_on_loading_started:620 - 加载中: 正在加载第 1 页数据...
2025-06-17 21:38:38.778 | INFO     | app.controllers.telegram_monitor_controller:load_task_users:396 - 加载任务用户数据: ID 1, 页码 1, 每页 10 条
2025-06-17 21:38:38.778 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-17 21:38:38.791 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-17 21:38:38.793 | INFO     | ui.views.monitor_view:_update_users_table:342 - 开始更新用户表格，收到10条数据
2025-06-17 21:38:38.793 | DEBUG    | ui.views.monitor_view:_update_users_table:343 - 分页信息：{'current_page': 1, 'page_size': 10, 'total_items': 1869, 'total_pages': 187}
2025-06-17 21:38:38.794 | INFO     | ui.views.monitor_view:_update_users_table:360 - 示例数据[0]: {'join_date': '2025-06-11 04:18:21', 'keyword': '', 'nickname': 'SL-TN-GV 注册邮箱 卖家 UU（海外）卖家🌈', 'task_type': 'text', 'uid': '7796651829', 'username': ''}
2025-06-17 21:38:38.794 | INFO     | ui.views.monitor_view:_update_users_table:360 - 示例数据[1]: {'join_date': '2025-06-11 04:18:19', 'keyword': '', 'nickname': 'ao wu', 'task_type': 'text', 'uid': '7365791847', 'username': ''}
2025-06-17 21:38:38.794 | INFO     | ui.views.monitor_view:_update_users_table:360 - 示例数据[2]: {'join_date': '2025-06-11 04:18:12', 'keyword': '', 'nickname': 'ST艾伦 None', 'task_type': 'text', 'uid': '7729912032', 'username': ''}
2025-06-17 21:38:38.794 | INFO     | ui.views.monitor_view:_update_users_table:364 - 设置表格行数: 10
2025-06-17 21:38:38.801 | INFO     | ui.views.monitor_view:_update_users_table:388 - 用户表格更新完成
2025-06-17 21:38:38.801 | INFO     | app.controllers.telegram_monitor_controller:load_task_users:423 - 成功加载第 1 页用户数据，共 10 条
2025-06-17 21:38:38.801 | DEBUG    | ui.views.monitor_view:_on_loading_finished:625 - 加载完成
2025-06-17 21:38:38.801 | INFO     | app.controllers.telegram_monitor_controller:load_task_details:287 - 成功加载任务详情: 全部群组
2025-06-17 21:38:38.801 | DEBUG    | ui.views.monitor_view:_on_loading_finished:625 - 加载完成
2025-06-17 21:38:39.417 | DEBUG    | ui.views.monitor_view:_on_task_item_clicked:258 - 任务项点击: 2
2025-06-17 21:38:39.420 | INFO     | app.controllers.telegram_monitor_controller:select_task:237 - 选择任务: 2
2025-06-17 21:38:39.420 | DEBUG    | ui.views.monitor_view:_on_task_selection_changed:281 - 任务选择变更: 2
2025-06-17 21:38:39.421 | DEBUG    | ui.views.monitor_view:_on_loading_started:620 - 加载中: 正在加载任务详情...
2025-06-17 21:38:39.421 | INFO     | app.controllers.telegram_monitor_controller:load_task_details:261 - 加载任务详情: ID 2
2025-06-17 21:38:39.421 | INFO     | app.services.monitor_service:get_task_detail:1018 - 获取任务详情: ID 2
2025-06-17 21:38:39.421 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-17 21:38:39.432 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-17 21:38:39.434 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-17 21:38:39.445 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-17 21:38:39.446 | DEBUG    | app.controllers.telegram_monitor_controller:load_task_details:271 - 获取到任务 2 的统计数据: {'total_users': 24, 'today_users': 0, 'avg_daily': 4, 'running_days': 6}
2025-06-17 21:38:39.447 | DEBUG    | ui.views.monitor_view:_on_task_details_loaded:309 - 任务详情加载完成: 2
2025-06-17 21:38:39.448 | DEBUG    | ui.views.monitor_view:_on_task_details_loaded:312 - 任务详情数据: {'created_at': '2025-06-11 10:02:49', 'description': '', 'id': '2', 'ignore_keywords': [], 'ignore_nicknames_rules': [], 'is_active': True, 'is_running': False, 'keywords': ['开户', '开通', '登录', '注册'], 'last_error': "所有账户监控设置均失败: object tuple can't be used in 'await' expression, object tuple can't be used in 'await' expression", 'monitor_messages': True, 'monitor_new_users': False, 'monitored_chats': [{'account_phone': '***********', 'chat_id': '**********', 'chat_title': '泰国华人交友聊天总群', 'id': 100}, {'account_phone': '***********', 'chat_id': '**********', 'chat_title': '赚钱项目灰产社区', 'id': 101}, {'account_phone': '***********', 'chat_id': '**********', 'chat_title': '深圳集团 - 藏楼1.0', 'id': 102}, {'account_phone': '***********', 'chat_id': '**********', 'chat_title': '干到你没钱16', 'id': 103}, {'account_phone': '***********', 'chat_id': '**********', 'chat_title': '东南亚博彩代理聊天群', 'id': 104}, {'account_phone': '***********', 'chat_id': '**********', 'chat_title': '柬埔寨泰国电子体育群', 'id': 105}, {'account_phone': '***********', 'chat_id': '**********', 'chat_title': '索引群聊❤️搜索王', 'id': 106}, {'account_phone': '***********', 'chat_id': '**********', 'chat_title': 'wiegnwi', 'id': 107}, {'account_phone': '***********', 'chat_id': '**********', 'chat_title': '区块节点NodeMining-Pool', 'id': 108}, {'account_phone': '***********', 'chat_id': '**********', 'chat_title': '区块节点NodeMining-Pool', 'id': 109}, {'account_phone': '***********', 'chat_id': '**********', 'chat_title': '🔥入金话术馆', 'id': 110}, {'account_phone': '***********', 'chat_id': '**********', 'chat_title': '广州会所资源_(部分预览)', 'id': 111}, {'account_phone': '***********', 'chat_id': '**********', 'chat_title': '内涵段子🔥搞笑视频', 'id': 112}, {'account_phone': '***********', 'chat_id': '**********', 'chat_title': '国外 话术 素材总部', 'id': 113}, {'account_phone': '***********', 'chat_id': '**********', 'chat_title': '精聊话术-人设套图大全', 'id': 114}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '🅰️中文搜片搜索👙自慰🩸抠逼🔍乱伦🗣口交', 'id': 115}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '软件开发🪸搭建网站🪸网站开发', 'id': 116}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '迷🦊 吃瓜 完整版破案‼️', 'id': 117}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '【官方】币安矿池置换中文群🇨🇳👤', 'id': 118}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '🈲 高中 学生资源库（加密）🎀', 'id': 119}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '🍒 萝莉屋18岁 资源群🦄', 'id': 120}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '【官方】币安矿池置换中文群🇨🇳', 'id': 121}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '【官方】币安BNB挖矿中文总社区🇨🇳❤️', 'id': 122}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '免费情色视频搜索🚀', 'id': 123}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '香港服务器交流群', 'id': 124}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': 'wiegnwi', 'id': 125}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '💡站长群 HostCLi.com 宝塔纯净版', 'id': 126}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': 'Fragment Universal Drop', 'id': 127}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '柯基币-礦池-官方社区', 'id': 128}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '中文算力MinerDog pool', 'id': 129}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '云泽社区', 'id': 130}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '国际联盟—海外实卡接码/邮寄公开群【支持担保】', 'id': 131}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '仙楼集团·资源发布站 @xianlou', 'id': 132}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '路易频道引流推广工作群', 'id': 133}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '泡妞笔记', 'id': 134}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '神奇宝贝 @so588', 'id': 135}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': 'TGbox助手|群发|拉人|引流|群发助手', 'id': 136}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '比特币购买，比特币行情，比特币怎么买', 'id': 137}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '泰国华人交友聊天总群', 'id': 157}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '索引群聊❤️搜索王', 'id': 158}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '东南亚博彩代理聊天群', 'id': 159}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '深圳集团 - 藏楼1.0', 'id': 160}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '柬埔寨泰国电子体育群', 'id': 161}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '赚钱项目灰产社区', 'id': 162}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '干到你没钱16', 'id': 163}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': 'Token FRAG Allocation', 'id': 164}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '广州会所资源_(部分预览)', 'id': 165}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '区块节点NodeMining-Pool', 'id': 166}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '区块节点NodeMining-Pool', 'id': 167}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '🔥入金话术馆', 'id': 168}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '国外 话术 素材总部', 'id': 169}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '神州联盟🇨🇳 海外顶尖话术', 'id': 170}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '精聊话术-人设套图大全', 'id': 171}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '狗推瑟瑟👠嗨吹群', 'id': 172}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': 'wiegnwi', 'id': 173}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '干到你没钱20', 'id': 174}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '🟥官方直开跑分🟦日赚过万项目🟨收款码代收🟧', 'id': 175}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '💡站长群 HostCLi.com 宝塔纯净版', 'id': 176}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '香港服务器交流群', 'id': 177}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '🈲 高中 学生资源库（加密）🎀', 'id': 178}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '🍒 萝莉屋18岁 资源群🦄', 'id': 179}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '🅰️中文搜片搜索👙自慰🩸抠逼🔍乱伦🗣口交', 'id': 180}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '币安BNB矿池官方社区🅥', 'id': 181}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '【官方】币安矿池置换中文群🇨🇳👤', 'id': 182}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '【官方】币安BNB挖矿中文总社区🇨🇳❤️❤️', 'id': 183}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '免费情色视频搜索🚀', 'id': 184}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '迷🦊 吃瓜 完整版破案‼️', 'id': 185}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '国际联盟—海外实卡接码/邮寄公开群【支持担保】', 'id': 186}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '【官方】币安矿池置换中文群🇨🇳❤️❤️', 'id': 187}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': 'FRAG Rewards', 'id': 188}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '柯基币-礦池-官方社区', 'id': 189}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '中文算力MinerDog pool', 'id': 190}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '云泽社区', 'id': 191}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '仙楼集团·资源发布站 @xianlou', 'id': 192}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '软件开发🪸搭建网站🪸网站开发', 'id': 193}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '路易频道引流推广工作群【结算时间19.00】', 'id': 194}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': 'wiegnwi', 'id': 195}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '泡妞笔记', 'id': 196}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': 'TGbox助手|群发|拉人|引流|群发助手', 'id': 197}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '比特币购买，比特币行情，比特币怎么买', 'id': 198}], 'name': '开户监听', 'stats': {'avg_daily': 4, 'running_days': 6, 'today_users': 0, 'total_users': 24}, 'updated_at': '2025-06-11 10:02:49'}
2025-06-17 21:38:39.448 | DEBUG    | ui.views.monitor_view:_on_task_details_loaded:316 - 统计数据: {'avg_daily': 4, 'running_days': 6, 'today_users': 0, 'total_users': 24}
2025-06-17 21:38:39.449 | DEBUG    | ui.views.monitor_view:_on_loading_started:620 - 加载中: 正在加载第 1 页数据...
2025-06-17 21:38:39.449 | INFO     | app.controllers.telegram_monitor_controller:load_task_users:396 - 加载任务用户数据: ID 2, 页码 1, 每页 10 条
2025-06-17 21:38:39.450 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-17 21:38:39.461 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-17 21:38:39.463 | INFO     | ui.views.monitor_view:_update_users_table:342 - 开始更新用户表格，收到10条数据
2025-06-17 21:38:39.464 | DEBUG    | ui.views.monitor_view:_update_users_table:343 - 分页信息：{'current_page': 1, 'page_size': 10, 'total_items': 24, 'total_pages': 3}
2025-06-17 21:38:39.464 | INFO     | ui.views.monitor_view:_update_users_table:360 - 示例数据[0]: {'join_date': '2025-06-11 04:13:04', 'keyword': '登录', 'nickname': '小高高 日 Phone login Tn web login SL fer', 'task_type': 'text', 'uid': '8010084637', 'username': ''}
2025-06-17 21:38:39.464 | INFO     | ui.views.monitor_view:_update_users_table:360 - 示例数据[1]: {'join_date': '2025-06-11 04:06:39', 'keyword': '注册', 'nickname': '中文索引 None', 'task_type': 'text', 'uid': '5064505565', 'username': ''}
2025-06-17 21:38:39.464 | INFO     | ui.views.monitor_view:_update_users_table:360 - 示例数据[2]: {'join_date': '2025-06-11 03:55:12', 'keyword': '注册', 'nickname': '币安矿池质押中文播报📣 None', 'task_type': 'text', 'uid': '8097600256', 'username': 'Binance_BNBP00L_Bot'}
2025-06-17 21:38:39.465 | INFO     | ui.views.monitor_view:_update_users_table:364 - 设置表格行数: 10
2025-06-17 21:38:39.471 | INFO     | ui.views.monitor_view:_update_users_table:388 - 用户表格更新完成
2025-06-17 21:38:39.472 | INFO     | app.controllers.telegram_monitor_controller:load_task_users:423 - 成功加载第 1 页用户数据，共 10 条
2025-06-17 21:38:39.472 | DEBUG    | ui.views.monitor_view:_on_loading_finished:625 - 加载完成
2025-06-17 21:38:39.472 | INFO     | app.controllers.telegram_monitor_controller:load_task_details:287 - 成功加载任务详情: 开户监听
2025-06-17 21:38:39.472 | DEBUG    | ui.views.monitor_view:_on_loading_finished:625 - 加载完成
2025-06-17 21:38:46.641 | INFO     | ui.views.add_msg_task_view:_load_initial_data:136 - 加载初始数据
2025-06-17 21:38:46.699 | INFO     | ui.views.add_msg_task_view:_load_account_groups:146 - 加载账户分组
2025-06-17 21:38:46.699 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-17 21:38:46.874 | INFO     | app.services.account_service:get_all_groups:96 - 获取所有账户分组成功, 共 2 个
2025-06-17 21:38:46.874 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-17 21:38:46.876 | DEBUG    | ui.views.account_view:_on_groups_loaded:442 - 分组加载完成: 2个分组
2025-06-17 21:38:46.877 | INFO     | ui.views.add_msg_task_view:_load_account_groups:162 - 加载账户分组数据: 2个分组
2025-06-17 21:38:46.877 | INFO     | ui.views.add_msg_task_view:_load_accounts_by_group:177 - 根据分组ID加载账户: -1
2025-06-17 21:38:46.878 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-17 21:38:46.891 | INFO     | app.services.account_service:get_all_accounts:289 - 获取所有账户成功, 共 2 个
2025-06-17 21:38:46.892 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-17 21:38:46.897 | INFO     | ui.views.add_msg_task_view:_load_accounts_by_group:213 - 已加载 2 个账户到列表
2025-06-17 21:38:46.899 | DEBUG    | ui.views.account_view:_on_accounts_loaded:575 - 账户加载完成: 2个账户
2025-06-17 21:38:46.919 | DEBUG    | ui.views.account_view:_update_group_counts:184 - 分组计数已更新: {2: 1, 1: 1}
2025-06-17 21:38:57.815 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-17 21:38:57.815 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-17 21:39:28.939 | INFO     | utils.logger:init_logger:98 - 日志系统初始化完成，默认级别: DEBUG，日志目录: H:\PyProject\TeleTest\APPDATA\logs
2025-06-17 21:39:31.649 | INFO     | data.database:init_database:31 - 初始化数据库连接: H:\PyProject\TeleTest\APPDATA\database\telegram_manager.db
2025-06-17 21:39:31.766 | DEBUG    | data.database:init_database:58 - 创建数据库表
2025-06-17 21:39:31.809 | INFO     | data.database:init_database:61 - 数据库初始化完成
2025-06-17 21:39:32.789 | DEBUG    | app.controllers.auth_controller:initialize_software:43 - 控制器: 初始化软件 app_id=1000, version=1.0
2025-06-17 21:39:32.790 | DEBUG    | utils.client_http:get:54 - 发送GET请求: https://server.xile188.com/api/user/1000/windowns10/1.0/ini
2025-06-17 21:39:33.069 | DEBUG    | core.auth.api_service:init_software:66 - 初始化数据解密成功: {'new_content': '1.多账户管理\n2.ip池管理\n3.消息监听\n4.消息群发\n5.拉人入群', 'new_url': None, 'notice': {'id': '2', 'content': '公告内容测试3', 'time': '**********'}}
2025-06-17 21:39:33.077 | INFO     | app.controllers.auth_controller:login:61 - 控制器: 用户登录 account=demo01
2025-06-17 21:39:36.096 | INFO     | core.auth.api_service:login:127 - 用户登录: account=demo01
2025-06-17 21:39:36.097 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/logon
2025-06-17 21:39:36.321 | INFO     | core.auth.api_service:get_user_info:347 - 获取用户信息
2025-06-17 21:39:36.322 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/info
2025-06-17 21:39:36.534 | INFO     | core.auth.api_service:get_user_info:375 - 获取用户信息成功
2025-06-17 21:39:36.543 | INFO     | __main__:on_auth_completed:93 - 用户登录成功，正在启动主窗口...
2025-06-17 21:39:36.589 | INFO     | core.telegram.client_worker:__init__:61 - Telegram客户端工作线程初始化
2025-06-17 21:39:36.590 | INFO     | ui.main_window:_initialize_core_components:82 - MainWindow: 初始化核心组件...
2025-06-17 21:39:36.591 | INFO     | core.telegram.client_worker:run:65 - Telegram客户端工作线程开始运行
2025-06-17 21:39:36.592 | INFO     | app.services.account_service:__init__:42 - 账户服务初始化
2025-06-17 21:39:36.592 | INFO     | core.proxy.proxy_task_manager:run:284 - 代理任务管理器线程启动
2025-06-17 21:39:36.593 | INFO     | app.controllers.account_controller:__init__:81 - 账户控制器初始化
2025-06-17 21:39:36.593 | INFO     | app.services.monitor_service:__init__:39 - 初始化监控任务服务
2025-06-17 21:39:36.593 | INFO     | core.monitor.filter:__init__:26 - 初始化消息过滤器
2025-06-17 21:39:36.594 | INFO     | core.monitor.extractor:__init__:24 - 初始化关键词提取器
2025-06-17 21:39:36.595 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-06-17 21:39:36.595 | INFO     | core.telegram.client_manager:__init__:66 - Telegram客户端管理器初始化
2025-06-17 21:39:36.595 | INFO     | core.monitor.notifier:__init__:90 - 初始化通知管理器
2025-06-17 21:39:36.596 | INFO     | core.telegram.user_manager:__init__:30 - Telegram用户管理器初始化
2025-06-17 21:39:36.596 | INFO     | app.controllers.telegram_monitor_controller:__init__:64 - 初始化 Telegram 监控控制器
2025-06-17 21:39:36.596 | INFO     | core.telegram.group_manager:__init__:36 - Telegram群组管理器初始化
2025-06-17 21:39:36.597 | DEBUG    | app.controllers.telegram_monitor_controller:__init__:82 - 控制器初始化完成
2025-06-17 21:39:36.597 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-06-17 21:39:36.598 | INFO     | core.telegram.monitor_manager:__init__:26 - Telegram监控管理器初始化
2025-06-17 21:39:36.599 | INFO     | core.telegram.telegram_service:__init__:39 - Telegram服务初始化完成
2025-06-17 21:39:36.888 | INFO     | ui.views.monitor_view:__init__:48 - 初始化监控视图
2025-06-17 21:39:36.889 | DEBUG    | ui.views.monitor_view:_connect_signals:80 - 连接控制器信号
2025-06-17 21:39:37.116 | INFO     | ui.views.proxy_view:__init__:96 - 初始化视图，加载第一页代理数据
2025-06-17 21:39:37.345 | INFO     | __main__:on_auth_completed:97 - 主窗口已启动
2025-06-17 21:39:37.390 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:88 - 尝试连接监听日志信号...
2025-06-17 21:39:37.390 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:90 - 监听日志信号连接成功
2025-06-17 21:39:37.390 | INFO     | core.auth.api_service:verify_vip:425 - 会员验证接口调用
2025-06-17 21:39:37.391 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/vip
2025-06-17 21:39:37.392 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-17 21:39:37.397 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-17 21:39:37.401 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:145 - 加载监控任务列表 (搜索关键词: '')
2025-06-17 21:39:37.401 | DEBUG    | ui.views.monitor_view:_on_loading_started:620 - 加载中: 正在加载任务列表...
2025-06-17 21:39:37.401 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-17 21:39:37.412 | INFO     | app.services.proxy_service:refresh_proxies:712 - 刷新代理列表: offset=0, limit=20
2025-06-17 21:39:37.412 | INFO     | core.proxy.proxy_task_manager:submit_task:172 - 提交耗时任务到子线程: refresh_proxies_0_20
2025-06-17 21:39:37.413 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-17 21:39:37.413 | DEBUG    | app.services.proxy_service:get_proxies:500 - 获取代理列表: offset=0, limit=20
2025-06-17 21:39:37.415 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-17 21:39:37.436 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-17 21:39:37.483 | INFO     | app.controllers.invite_controller:get_invite_tasks:38 - 获取邀请任务列表, status=None
2025-06-17 21:39:37.484 | INFO     | app.services.invite_service:get_invite_tasks:121 - 获取邀请任务列表, status=None
2025-06-17 21:39:37.484 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-17 21:39:37.490 | INFO     | app.services.invite_service:task_initialize:39 - 初始化邀请服务，重置运行中的任务为暂停状态
2025-06-17 21:39:37.491 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-17 21:39:37.493 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-17 21:39:37.496 | INFO     | core.proxy.proxy_task_manager:task_wrapper:199 - 任务 refresh_proxies_0_20 完成
2025-06-17 21:39:37.626 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-17 21:39:37.628 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-17 21:39:37.647 | INFO     | app.services.invite_service:task_initialize:48 - 成功重置 0 个任务状态
2025-06-17 21:39:37.647 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-17 21:39:37.649 | DEBUG    | ui.views.monitor_view:_refresh_task_list:175 - 刷新任务列表，共2个任务
2025-06-17 21:39:37.657 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-17 21:39:37.664 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-17 21:39:37.673 | INFO     | app.services.account_service:get_all_groups:96 - 获取所有账户分组成功, 共 2 个
2025-06-17 21:39:37.673 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-17 21:39:37.674 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-17 21:39:37.678 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-17 21:39:37.680 | DEBUG    | ui.views.account_view:_on_groups_loaded:442 - 分组加载完成: 2个分组
2025-06-17 21:39:37.694 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-17 21:39:37.802 | INFO     | app.services.account_service:get_all_accounts:289 - 获取所有账户成功, 共 2 个
2025-06-17 21:39:37.803 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-17 21:39:37.806 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-17 21:39:37.806 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-17 21:39:37.808 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-17 21:39:37.809 | DEBUG    | ui.views.account_view:_on_accounts_loaded:575 - 账户加载完成: 2个账户
2025-06-17 21:39:37.832 | DEBUG    | ui.views.account_view:_update_group_counts:184 - 分组计数已更新: {2: 1, 1: 1}
2025-06-17 21:39:37.839 | INFO     | ui.views.account_view:_auto_login_accounts:630 - 开始调用控制器进行 2 个账户的自动登录。
2025-06-17 21:39:37.839 | INFO     | app.services.account_service:batch_auto_login:1153 - 服务层：开始批量自动登录 2 个账户, 最大并发: 5
2025-06-17 21:39:37.840 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-17 21:39:37.854 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-06-17 21:39:37.872 | INFO     | app.controllers.telegram_monitor_controller:_load_total_users_count_internal:191 - 成功加载总体统计数据: 总用户数 1845, 今日采集 0, 日均采集 308, 运行天数 6
2025-06-17 21:39:37.873 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:156 - 成功加载 2 个任务
2025-06-17 21:39:37.873 | DEBUG    | ui.views.monitor_view:_on_loading_finished:625 - 加载完成
2025-06-17 21:39:37.887 | INFO     | app.services.account_service:get_all_accounts:289 - 获取所有账户成功, 共 2 个
2025-06-17 21:39:37.887 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-17 21:39:37.890 | INFO     | app.services.account_service:batch_auto_login:1192 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-06-17 21:39:37.890 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-17 21:39:37.900 | DEBUG    | ui.views.account_view:_update_group_counts:184 - 分组计数已更新: {2: 1, 1: 1}
2025-06-17 21:39:37.900 | DEBUG    | ui.views.account_view:_init_cache:151 - 账户缓存初始化完成，共 2 个账户
2025-06-17 21:39:37.905 | DEBUG    | ui.views.account_view:_on_accounts_loaded:575 - 账户加载完成: 2个账户
2025-06-17 21:39:37.928 | DEBUG    | ui.views.account_view:_update_group_counts:184 - 分组计数已更新: {2: 1, 1: 1}
2025-06-17 21:39:37.933 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=True
2025-06-17 21:39:37.935 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=True
2025-06-17 21:39:37.938 | INFO     | ui.views.proxy_view:_load_initial_data:1044 - 初始数据加载完成，共 1 个代理
2025-06-17 21:39:37.940 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-17 21:39:37.944 | WARNING  | core.auth.api_service:verify_vip:441 - 会员验证失败: 验证失败
2025-06-17 21:39:37.995 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-17 21:39:37.996 | INFO     | app.services.account_service:batch_auto_login:1192 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-06-17 21:39:37.997 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-17 21:39:38.028 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-17 21:39:38.116 | INFO     | app.services.account_service:batch_auto_login:1262 - 服务层：准备调用核心层处理 2 个账户的自动登录。
2025-06-17 21:39:38.117 | INFO     | core.telegram.client_manager:batch_auto_login:1211 - 开始批量自动登录, 共 2 个账户, 最大并发 5
2025-06-17 21:39:38.117 | INFO     | app.services.account_service:batch_auto_login:1272 - 服务层：设置核心层任务超时为 120 秒。
2025-06-17 21:39:38.118 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:254 - 创建客户端: H:\PyProject\TeleTest\APPDATA\sessions\+***********.session, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-06-17 21:39:38.119 | DEBUG    | core.telegram.client_manager:get_proxy:91 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-06-17 21:39:38.120 | DEBUG    | core.telegram.client_manager:get_proxy:138 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-06-17 21:39:38.124 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:254 - 创建客户端: H:\PyProject\TeleTest\APPDATA\sessions\+***********.session, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-06-17 21:39:38.125 | DEBUG    | core.telegram.client_manager:get_proxy:91 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-06-17 21:39:38.127 | DEBUG    | core.telegram.client_manager:get_proxy:138 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-06-17 21:39:38.249 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-17 21:39:44.102 | INFO     | core.telegram.client_manager:_connect_client:202 - 连接成功!
2025-06-17 21:39:45.433 | INFO     | core.telegram.client_manager:_verify_client_authorization:1186 - 账户已授权: +***********
2025-06-17 21:39:47.064 | INFO     | core.telegram.client_manager:_connect_client:202 - 连接成功!
2025-06-17 21:39:49.900 | INFO     | core.telegram.client_manager:_verify_client_authorization:1186 - 账户已授权: +***********
2025-06-17 21:39:51.903 | INFO     | core.telegram.client_manager:batch_auto_login:1360 - 批量登录完成: 总计 2, 成功 2, 失败 0
2025-06-17 21:39:52.183 | INFO     | app.services.account_service:batch_auto_login:1293 - 服务层：核心层登录完成: 成功 2, 失败 0 / 2
2025-06-17 21:40:36.529 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-17 21:40:36.529 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-17 21:41:36.530 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-17 21:41:36.531 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-17 21:42:36.527 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-17 21:42:36.528 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-17 21:43:36.529 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-17 21:43:36.529 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-17 21:44:36.530 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-17 21:44:36.531 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-17 21:45:36.528 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-17 21:45:36.528 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-17 21:46:36.527 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-17 21:46:36.528 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-17 21:47:36.535 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-17 21:47:36.536 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-17 21:48:36.522 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-17 21:48:36.522 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-17 21:49:36.526 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-17 21:49:36.526 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-17 21:50:36.526 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-17 21:50:36.527 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-17 21:51:36.530 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-17 21:51:36.531 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-17 21:52:36.531 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-17 21:52:36.531 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-17 21:53:36.528 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-17 21:53:36.528 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-17 21:54:36.528 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-17 21:54:36.528 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-17 21:55:36.528 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-17 21:55:36.528 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-17 21:56:36.527 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-17 21:56:36.528 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-17 21:57:36.530 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-17 21:57:36.530 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-17 21:58:36.526 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-17 21:58:36.526 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-17 21:59:36.529 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-17 21:59:36.530 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-17 22:00:36.529 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-17 22:00:36.529 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-17 22:01:36.522 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-17 22:01:36.522 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-17 22:02:36.522 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-17 22:02:36.523 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-17 22:03:36.521 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-17 22:03:36.522 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-17 22:04:36.523 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-17 22:04:36.524 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-17 22:05:36.523 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-17 22:05:36.523 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-17 22:06:36.525 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-17 22:06:36.525 | DEBUG    | utils.client_http:post:81 - 发送POST请求: https://server.xile188.com/api/user/1000/windowns10/1.0/heartbeat
2025-06-17 22:07:26.888 | INFO     | utils.logger:init_logger:98 - 日志系统初始化完成，默认级别: DEBUG，日志目录: H:\PyProject\TeleTest\APPDATA\logs
2025-06-17 22:07:30.448 | INFO     | data.database:init_database:31 - 初始化数据库连接: H:\PyProject\TeleTest\APPDATA\database\telegram_manager.db
2025-06-17 22:07:30.768 | DEBUG    | data.database:init_database:58 - 创建数据库表
2025-06-17 22:07:30.813 | INFO     | data.database:init_database:61 - 数据库初始化完成
2025-06-17 22:07:31.794 | DEBUG    | app.controllers.auth_controller:initialize_software:43 - 控制器: 初始化软件 app_id=1000, version=1.0
2025-06-17 22:07:32.085 | DEBUG    | core.auth.api_service:init_software:66 - 初始化数据解密成功: {'new_content': '1.多账户管理\n2.ip池管理\n3.消息监听\n4.消息群发\n5.拉人入群', 'new_url': None, 'notice': {'id': '2', 'content': '公告内容测试3', 'time': '**********'}}
2025-06-17 22:07:32.094 | INFO     | app.controllers.auth_controller:login:61 - 控制器: 用户登录 account=demo01
2025-06-17 22:07:35.318 | INFO     | core.auth.api_service:login:127 - 用户登录: account=demo01
2025-06-17 22:07:35.639 | INFO     | core.auth.api_service:get_user_info:347 - 获取用户信息
2025-06-17 22:07:35.893 | INFO     | core.auth.api_service:get_user_info:375 - 获取用户信息成功
2025-06-17 22:07:35.900 | INFO     | __main__:on_auth_completed:93 - 用户登录成功，正在启动主窗口...
2025-06-17 22:07:35.933 | INFO     | core.telegram.client_worker:__init__:61 - Telegram客户端工作线程初始化
2025-06-17 22:07:35.934 | INFO     | ui.main_window:_initialize_core_components:82 - MainWindow: 初始化核心组件...
2025-06-17 22:07:35.935 | INFO     | core.telegram.client_worker:run:65 - Telegram客户端工作线程开始运行
2025-06-17 22:07:35.936 | INFO     | app.services.account_service:__init__:42 - 账户服务初始化
2025-06-17 22:07:35.936 | INFO     | core.proxy.proxy_task_manager:run:284 - 代理任务管理器线程启动
2025-06-17 22:07:35.937 | INFO     | app.controllers.account_controller:__init__:81 - 账户控制器初始化
2025-06-17 22:07:35.937 | INFO     | app.services.monitor_service:__init__:39 - 初始化监控任务服务
2025-06-17 22:07:35.938 | INFO     | core.monitor.filter:__init__:26 - 初始化消息过滤器
2025-06-17 22:07:35.938 | INFO     | core.monitor.extractor:__init__:24 - 初始化关键词提取器
2025-06-17 22:07:35.938 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-06-17 22:07:35.939 | INFO     | core.monitor.notifier:__init__:90 - 初始化通知管理器
2025-06-17 22:07:35.940 | INFO     | app.controllers.telegram_monitor_controller:__init__:64 - 初始化 Telegram 监控控制器
2025-06-17 22:07:35.941 | DEBUG    | app.controllers.telegram_monitor_controller:__init__:82 - 控制器初始化完成
2025-06-17 22:07:35.943 | INFO     | core.telegram.client_manager:__init__:66 - Telegram客户端管理器初始化
2025-06-17 22:07:35.944 | INFO     | core.telegram.user_manager:__init__:30 - Telegram用户管理器初始化
2025-06-17 22:07:35.944 | INFO     | core.telegram.group_manager:__init__:36 - Telegram群组管理器初始化
2025-06-17 22:07:35.944 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-06-17 22:07:35.945 | INFO     | core.telegram.monitor_manager:__init__:26 - Telegram监控管理器初始化
2025-06-17 22:07:35.946 | INFO     | core.telegram.telegram_service:__init__:39 - Telegram服务初始化完成
2025-06-17 22:07:36.239 | INFO     | ui.views.monitor_view:__init__:48 - 初始化监控视图
2025-06-17 22:07:36.239 | DEBUG    | ui.views.monitor_view:_connect_signals:80 - 连接控制器信号
2025-06-17 22:07:36.470 | INFO     | ui.views.proxy_view:__init__:96 - 初始化视图，加载第一页代理数据
2025-06-17 22:07:36.713 | INFO     | __main__:on_auth_completed:97 - 主窗口已启动
2025-06-17 22:07:36.762 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:88 - 尝试连接监听日志信号...
2025-06-17 22:07:36.762 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:90 - 监听日志信号连接成功
2025-06-17 22:07:36.763 | INFO     | core.auth.api_service:verify_vip:425 - 会员验证接口调用
2025-06-17 22:07:36.764 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-17 22:07:36.771 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-17 22:07:36.776 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:145 - 加载监控任务列表 (搜索关键词: '')
2025-06-17 22:07:36.776 | DEBUG    | ui.views.monitor_view:_on_loading_started:620 - 加载中: 正在加载任务列表...
2025-06-17 22:07:36.776 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-17 22:07:36.785 | INFO     | app.services.proxy_service:refresh_proxies:712 - 刷新代理列表: offset=0, limit=20
2025-06-17 22:07:36.785 | INFO     | core.proxy.proxy_task_manager:submit_task:172 - 提交耗时任务到子线程: refresh_proxies_0_20
2025-06-17 22:07:36.786 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-17 22:07:36.786 | DEBUG    | app.services.proxy_service:get_proxies:500 - 获取代理列表: offset=0, limit=20
2025-06-17 22:07:36.788 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-17 22:07:36.797 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-17 22:07:36.865 | INFO     | app.controllers.invite_controller:get_invite_tasks:38 - 获取邀请任务列表, status=None
2025-06-17 22:07:36.866 | INFO     | app.services.invite_service:get_invite_tasks:121 - 获取邀请任务列表, status=None
2025-06-17 22:07:36.866 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-17 22:07:36.869 | INFO     | app.services.invite_service:task_initialize:39 - 初始化邀请服务，重置运行中的任务为暂停状态
2025-06-17 22:07:36.870 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-17 22:07:36.878 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-17 22:07:36.881 | INFO     | core.proxy.proxy_task_manager:task_wrapper:199 - 任务 refresh_proxies_0_20 完成
2025-06-17 22:07:37.039 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-17 22:07:37.041 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-17 22:07:37.055 | DEBUG    | ui.views.monitor_view:_refresh_task_list:175 - 刷新任务列表，共2个任务
2025-06-17 22:07:37.062 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-17 22:07:37.070 | INFO     | app.services.invite_service:task_initialize:48 - 成功重置 0 个任务状态
2025-06-17 22:07:37.070 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-17 22:07:37.077 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-17 22:07:37.085 | INFO     | app.services.account_service:get_all_groups:96 - 获取所有账户分组成功, 共 2 个
2025-06-17 22:07:37.085 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-17 22:07:37.085 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-17 22:07:37.090 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-17 22:07:37.095 | DEBUG    | ui.views.account_view:_on_groups_loaded:442 - 分组加载完成: 2个分组
2025-06-17 22:07:37.111 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-17 22:07:37.208 | INFO     | app.services.account_service:get_all_accounts:289 - 获取所有账户成功, 共 2 个
2025-06-17 22:07:37.208 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-17 22:07:37.209 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-17 22:07:37.210 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-17 22:07:37.212 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-17 22:07:37.214 | DEBUG    | ui.views.account_view:_on_accounts_loaded:575 - 账户加载完成: 2个账户
2025-06-17 22:07:37.236 | DEBUG    | ui.views.account_view:_update_group_counts:184 - 分组计数已更新: {2: 1, 1: 1}
2025-06-17 22:07:37.241 | INFO     | ui.views.account_view:_auto_login_accounts:630 - 开始调用控制器进行 2 个账户的自动登录。
2025-06-17 22:07:37.241 | INFO     | app.services.account_service:batch_auto_login:1153 - 服务层：开始批量自动登录 2 个账户, 最大并发: 5
2025-06-17 22:07:37.242 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-17 22:07:37.248 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-06-17 22:07:37.277 | INFO     | app.controllers.telegram_monitor_controller:_load_total_users_count_internal:191 - 成功加载总体统计数据: 总用户数 1845, 今日采集 0, 日均采集 308, 运行天数 6
2025-06-17 22:07:37.278 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:156 - 成功加载 2 个任务
2025-06-17 22:07:37.278 | DEBUG    | ui.views.monitor_view:_on_loading_finished:625 - 加载完成
2025-06-17 22:07:37.296 | INFO     | app.services.account_service:get_all_accounts:289 - 获取所有账户成功, 共 2 个
2025-06-17 22:07:37.297 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-17 22:07:37.298 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=True
2025-06-17 22:07:37.300 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=True
2025-06-17 22:07:37.303 | INFO     | ui.views.proxy_view:_load_initial_data:1044 - 初始数据加载完成，共 1 个代理
2025-06-17 22:07:37.304 | INFO     | app.services.account_service:batch_auto_login:1192 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-06-17 22:07:37.305 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-17 22:07:37.310 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-17 22:07:37.313 | DEBUG    | ui.views.account_view:_update_group_counts:184 - 分组计数已更新: {2: 1, 1: 1}
2025-06-17 22:07:37.313 | DEBUG    | ui.views.account_view:_init_cache:151 - 账户缓存初始化完成，共 2 个账户
2025-06-17 22:07:37.320 | DEBUG    | ui.views.account_view:_on_accounts_loaded:575 - 账户加载完成: 2个账户
2025-06-17 22:07:37.346 | DEBUG    | ui.views.account_view:_update_group_counts:184 - 分组计数已更新: {2: 1, 1: 1}
2025-06-17 22:07:37.359 | WARNING  | core.auth.api_service:verify_vip:441 - 会员验证失败: 验证失败
2025-06-17 22:07:37.420 | INFO     | app.services.account_service:batch_auto_login:1192 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-06-17 22:07:37.420 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-17 22:07:37.421 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-17 22:07:37.424 | INFO     | app.services.account_service:batch_auto_login:1262 - 服务层：准备调用核心层处理 2 个账户的自动登录。
2025-06-17 22:07:37.425 | INFO     | app.services.account_service:batch_auto_login:1272 - 服务层：设置核心层任务超时为 120 秒。
2025-06-17 22:07:37.425 | INFO     | core.telegram.client_manager:batch_auto_login:1211 - 开始批量自动登录, 共 2 个账户, 最大并发 5
2025-06-17 22:07:37.428 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:254 - 创建客户端: H:\PyProject\TeleTest\APPDATA\sessions\+***********.session, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-06-17 22:07:37.428 | DEBUG    | core.telegram.client_manager:get_proxy:91 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-06-17 22:07:37.429 | DEBUG    | core.telegram.client_manager:get_proxy:138 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-06-17 22:07:37.440 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:254 - 创建客户端: H:\PyProject\TeleTest\APPDATA\sessions\+***********.session, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-06-17 22:07:37.440 | DEBUG    | core.telegram.client_manager:get_proxy:91 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-06-17 22:07:37.440 | DEBUG    | core.telegram.client_manager:get_proxy:138 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-06-17 22:07:37.471 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-17 22:07:37.717 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-17 22:07:46.460 | WARNING  | core.telegram.client_manager:_connect_client:219 - 连接失败: 网络错误 Connection to Telegram failed 2 time(s)
2025-06-17 22:07:46.460 | WARNING  | core.telegram.client_manager:_connect_client:219 - 连接失败: 网络错误 Connection to Telegram failed 2 time(s)
2025-06-17 22:07:56.471 | WARNING  | core.telegram.client_manager:_connect_client:219 - 连接失败: 网络错误 Connection to Telegram failed 2 time(s)
2025-06-17 22:07:56.489 | WARNING  | core.telegram.client_manager:_connect_client:219 - 连接失败: 网络错误 Connection to Telegram failed 2 time(s)
2025-06-17 22:08:06.461 | WARNING  | core.telegram.client_manager:_connect_client:219 - 连接失败: 网络错误 Connection to Telegram failed 2 time(s)
2025-06-17 22:08:06.509 | WARNING  | core.telegram.client_manager:_connect_client:219 - 连接失败: 网络错误 Connection to Telegram failed 2 time(s)
2025-06-17 22:08:08.513 | INFO     | core.telegram.client_manager:batch_auto_login:1360 - 批量登录完成: 总计 2, 成功 0, 失败 2
2025-06-17 22:08:09.444 | INFO     | app.services.account_service:batch_auto_login:1293 - 服务层：核心层登录完成: 成功 0, 失败 2 / 2
2025-06-17 22:08:35.883 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-17 22:08:50.066 | INFO     | ui.main_window:closeEvent:346 - MainWindow: 接收到关闭事件
2025-06-17 22:08:50.087 | INFO     | ui.main_window:_cleanup_before_quit:238 - MainWindow: 执行清理资源...
2025-06-17 22:08:50.088 | INFO     | core.telegram.client_worker:stop:108 - 正在停止Telegram客户端工作线程
2025-06-17 22:08:50.099 | INFO     | core.telegram.client_manager:cleanup_async:1131 - 开始清理资源并断开所有连接
2025-06-17 22:08:50.100 | INFO     | core.telegram.client_manager:disconnect_all_clients:716 - 正在断开所有客户端连接
2025-06-17 22:08:50.100 | INFO     | core.telegram.client_manager:cleanup_async:1150 - 资源清理完成
2025-06-17 22:08:50.601 | INFO     | core.telegram.client_manager:cleanup_async:1131 - 开始清理资源并断开所有连接
2025-06-17 22:08:50.601 | INFO     | core.telegram.client_manager:disconnect_all_clients:716 - 正在断开所有客户端连接
2025-06-17 22:08:50.602 | INFO     | core.telegram.client_manager:cleanup_async:1150 - 资源清理完成
2025-06-17 22:08:51.103 | INFO     | core.telegram.client_worker:run:97 - Telegram客户端工作线程已退出
2025-06-17 22:08:51.104 | INFO     | core.telegram.client_worker:stop:122 - Telegram客户端工作线程已停止
2025-06-17 22:08:51.104 | INFO     | ui.main_window:_cleanup_before_quit:247 - TelegramClientWorker 已停止。
2025-06-17 22:08:51.104 | INFO     | ui.main_window:_cleanup_before_quit:251 - MainWindow 清理完成
2025-06-17 22:08:51.116 | INFO     | __main__:main:111 - 应用程序已正常退出
2025-06-17 22:14:38.238 | INFO     | utils.logger:init_logger:98 - 日志系统初始化完成，默认级别: DEBUG，日志目录: H:\PyProject\TeleTest\APPDATA\logs
2025-06-17 22:14:40.474 | INFO     | data.database:init_database:31 - 初始化数据库连接: H:\PyProject\TeleTest\APPDATA\database\telegram_manager.db
2025-06-17 22:14:40.509 | DEBUG    | data.database:init_database:58 - 创建数据库表
2025-06-17 22:14:40.533 | INFO     | data.database:init_database:61 - 数据库初始化完成
2025-06-17 22:14:41.160 | DEBUG    | app.controllers.auth_controller:initialize_software:43 - 控制器: 初始化软件 app_id=1000, version=1.0
2025-06-17 22:14:45.062 | DEBUG    | core.auth.api_service:init_software:57 - {'code': 200, 'msg': '成功', 'time': 1750169682, 'sign': '4979b96b420e1bcc0115b47895065dcb', 'data': '9eefd48e8d0ccb4411b7429cf8b033f82544b065d147bea0ed46c0ec53e96aab51b1d6d04fab2220d26259dd7d02000222bd6a9513acf4cba1c5e6c83c21c362c31f9a5e62d737b41f8ea9c6df9df0d804ccfdf127daba905869dfef00464e33b0f0c12de0e729abd0895d1f1359ba3886c5f557320092dbe6143db57bd33ea2ffd7aa8ca686c4819991f96772ca02f025afb740a8641045b68ebf3bb4c88cec07be2136684e2383144dd41b1e79caa5cd9f764ea55a6c0daba2caa904bcd62b8c76936798a423f4d7cdd73c49b30a30f14442da3e8b5898adf82c1053b5726ed6030846580468990f405a3d0f9495e4b5ea773434107620b1f0f270bdede0bc48c3f330e44d7a54b306d4324a020d25', 'run': {'ms': 11.6, 'ram': '97.75k'}}
2025-06-17 22:14:51.371 | DEBUG    | core.auth.api_service:init_software:66 - 初始化数据解密成功: {'new_content': '1.多账户管理\n2.ip池管理\n3.消息监听\n4.消息群发\n5.拉人入群', 'new_url': None, 'notice': {'id': '2', 'content': '公告内容测试3', 'time': '**********'}}
2025-06-17 22:16:50.032 | INFO     | app.controllers.auth_controller:login:61 - 控制器: 用户登录 account=demo01
2025-06-17 22:16:53.190 | INFO     | core.auth.api_service:login:127 - 用户登录: account=demo01
2025-06-17 22:16:53.495 | INFO     | core.auth.api_service:get_user_info:347 - 获取用户信息
2025-06-17 22:16:53.695 | INFO     | core.auth.api_service:get_user_info:375 - 获取用户信息成功
2025-06-17 22:16:53.703 | INFO     | __main__:on_auth_completed:93 - 用户登录成功，正在启动主窗口...
2025-06-17 22:16:53.745 | INFO     | core.telegram.client_worker:__init__:61 - Telegram客户端工作线程初始化
2025-06-17 22:16:53.746 | INFO     | ui.main_window:_initialize_core_components:82 - MainWindow: 初始化核心组件...
2025-06-17 22:16:53.747 | INFO     | app.services.account_service:__init__:42 - 账户服务初始化
2025-06-17 22:16:53.748 | INFO     | core.telegram.client_worker:run:65 - Telegram客户端工作线程开始运行
2025-06-17 22:16:53.748 | INFO     | core.proxy.proxy_task_manager:run:284 - 代理任务管理器线程启动
2025-06-17 22:16:53.749 | INFO     | app.controllers.account_controller:__init__:81 - 账户控制器初始化
2025-06-17 22:16:53.749 | INFO     | app.services.monitor_service:__init__:39 - 初始化监控任务服务
2025-06-17 22:16:53.750 | INFO     | core.monitor.filter:__init__:26 - 初始化消息过滤器
2025-06-17 22:16:53.750 | INFO     | core.monitor.extractor:__init__:24 - 初始化关键词提取器
2025-06-17 22:16:53.750 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-06-17 22:16:53.751 | INFO     | core.monitor.notifier:__init__:90 - 初始化通知管理器
2025-06-17 22:16:53.751 | INFO     | app.controllers.telegram_monitor_controller:__init__:64 - 初始化 Telegram 监控控制器
2025-06-17 22:16:53.752 | DEBUG    | app.controllers.telegram_monitor_controller:__init__:82 - 控制器初始化完成
2025-06-17 22:16:53.752 | INFO     | core.telegram.client_manager:__init__:66 - Telegram客户端管理器初始化
2025-06-17 22:16:53.754 | INFO     | core.telegram.user_manager:__init__:30 - Telegram用户管理器初始化
2025-06-17 22:16:53.755 | INFO     | core.telegram.group_manager:__init__:36 - Telegram群组管理器初始化
2025-06-17 22:16:53.755 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-06-17 22:16:53.755 | INFO     | core.telegram.monitor_manager:__init__:26 - Telegram监控管理器初始化
2025-06-17 22:16:53.756 | INFO     | core.telegram.telegram_service:__init__:39 - Telegram服务初始化完成
2025-06-17 22:16:54.023 | INFO     | ui.views.monitor_view:__init__:48 - 初始化监控视图
2025-06-17 22:16:54.024 | DEBUG    | ui.views.monitor_view:_connect_signals:80 - 连接控制器信号
2025-06-17 22:16:54.246 | INFO     | ui.views.proxy_view:__init__:96 - 初始化视图，加载第一页代理数据
2025-06-17 22:16:54.484 | INFO     | __main__:on_auth_completed:97 - 主窗口已启动
2025-06-17 22:16:54.538 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:88 - 尝试连接监听日志信号...
2025-06-17 22:16:54.538 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:90 - 监听日志信号连接成功
2025-06-17 22:16:54.539 | INFO     | core.auth.api_service:verify_vip:425 - 会员验证接口调用
2025-06-17 22:16:54.540 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-17 22:16:54.547 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-17 22:16:54.551 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:145 - 加载监控任务列表 (搜索关键词: '')
2025-06-17 22:16:54.552 | DEBUG    | ui.views.monitor_view:_on_loading_started:620 - 加载中: 正在加载任务列表...
2025-06-17 22:16:54.552 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-17 22:16:54.562 | INFO     | app.services.proxy_service:refresh_proxies:712 - 刷新代理列表: offset=0, limit=20
2025-06-17 22:16:54.563 | INFO     | core.proxy.proxy_task_manager:submit_task:172 - 提交耗时任务到子线程: refresh_proxies_0_20
2025-06-17 22:16:54.564 | DEBUG    | app.services.proxy_service:get_proxies:500 - 获取代理列表: offset=0, limit=20
2025-06-17 22:16:54.564 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-17 22:16:54.565 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-17 22:16:54.584 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-17 22:16:54.636 | INFO     | app.controllers.invite_controller:get_invite_tasks:38 - 获取邀请任务列表, status=None
2025-06-17 22:16:54.636 | INFO     | app.services.invite_service:get_invite_tasks:121 - 获取邀请任务列表, status=None
2025-06-17 22:16:54.637 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-17 22:16:54.645 | INFO     | app.services.invite_service:task_initialize:39 - 初始化邀请服务，重置运行中的任务为暂停状态
2025-06-17 22:16:54.645 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-17 22:16:54.648 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-17 22:16:54.651 | INFO     | core.proxy.proxy_task_manager:task_wrapper:199 - 任务 refresh_proxies_0_20 完成
2025-06-17 22:16:54.792 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-17 22:16:54.806 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-17 22:16:54.817 | DEBUG    | ui.views.monitor_view:_refresh_task_list:175 - 刷新任务列表，共2个任务
2025-06-17 22:16:54.823 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-17 22:16:54.830 | INFO     | app.services.invite_service:task_initialize:48 - 成功重置 0 个任务状态
2025-06-17 22:16:54.831 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-17 22:16:54.835 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-17 22:16:54.842 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-17 22:16:54.846 | INFO     | app.services.account_service:get_all_groups:96 - 获取所有账户分组成功, 共 2 个
2025-06-17 22:16:54.846 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-17 22:16:54.850 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-17 22:16:54.855 | DEBUG    | ui.views.account_view:_on_groups_loaded:442 - 分组加载完成: 2个分组
2025-06-17 22:16:54.872 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-17 22:16:54.966 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-17 22:16:54.969 | INFO     | app.services.account_service:get_all_accounts:289 - 获取所有账户成功, 共 2 个
2025-06-17 22:16:54.969 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-17 22:16:54.970 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-17 22:16:54.974 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-06-17 22:16:54.981 | INFO     | app.controllers.telegram_monitor_controller:_load_total_users_count_internal:191 - 成功加载总体统计数据: 总用户数 1845, 今日采集 0, 日均采集 308, 运行天数 6
2025-06-17 22:16:54.981 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:156 - 成功加载 2 个任务
2025-06-17 22:16:54.981 | DEBUG    | ui.views.monitor_view:_on_loading_finished:625 - 加载完成
2025-06-17 22:16:54.992 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-17 22:16:54.995 | DEBUG    | ui.views.account_view:_on_accounts_loaded:575 - 账户加载完成: 2个账户
2025-06-17 22:16:55.023 | DEBUG    | ui.views.account_view:_update_group_counts:184 - 分组计数已更新: {2: 1, 1: 1}
2025-06-17 22:16:55.030 | INFO     | ui.views.account_view:_auto_login_accounts:630 - 开始调用控制器进行 2 个账户的自动登录。
2025-06-17 22:16:55.031 | INFO     | app.services.account_service:batch_auto_login:1153 - 服务层：开始批量自动登录 2 个账户, 最大并发: 5
2025-06-17 22:16:55.031 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-17 22:16:55.050 | WARNING  | core.auth.api_service:verify_vip:441 - 会员验证失败: 验证失败
2025-06-17 22:16:55.249 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=True
2025-06-17 22:16:55.253 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=True
2025-06-17 22:16:55.255 | INFO     | ui.views.proxy_view:_load_initial_data:1044 - 初始数据加载完成，共 1 个代理
2025-06-17 22:16:55.305 | INFO     | app.services.account_service:get_all_accounts:289 - 获取所有账户成功, 共 2 个
2025-06-17 22:16:55.305 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-17 22:16:55.306 | INFO     | app.services.account_service:batch_auto_login:1192 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-06-17 22:16:55.307 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-17 22:16:55.310 | DEBUG    | ui.views.account_view:_update_group_counts:184 - 分组计数已更新: {2: 1, 1: 1}
2025-06-17 22:16:55.310 | DEBUG    | ui.views.account_view:_init_cache:151 - 账户缓存初始化完成，共 2 个账户
2025-06-17 22:16:55.315 | DEBUG    | ui.views.account_view:_on_accounts_loaded:575 - 账户加载完成: 2个账户
2025-06-17 22:16:55.336 | DEBUG    | ui.views.account_view:_update_group_counts:184 - 分组计数已更新: {2: 1, 1: 1}
2025-06-17 22:16:55.342 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-17 22:16:55.349 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-17 22:16:55.351 | INFO     | app.services.account_service:batch_auto_login:1192 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-06-17 22:16:55.351 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-17 22:16:55.379 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-17 22:16:55.386 | INFO     | app.services.account_service:batch_auto_login:1262 - 服务层：准备调用核心层处理 2 个账户的自动登录。
2025-06-17 22:16:55.387 | INFO     | app.services.account_service:batch_auto_login:1272 - 服务层：设置核心层任务超时为 120 秒。
2025-06-17 22:16:55.388 | INFO     | core.telegram.client_manager:batch_auto_login:1211 - 开始批量自动登录, 共 2 个账户, 最大并发 5
2025-06-17 22:16:55.389 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:254 - 创建客户端: H:\PyProject\TeleTest\APPDATA\sessions\+***********.session, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-06-17 22:16:55.389 | DEBUG    | core.telegram.client_manager:get_proxy:91 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-06-17 22:16:55.389 | DEBUG    | core.telegram.client_manager:get_proxy:138 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-06-17 22:16:55.398 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:254 - 创建客户端: H:\PyProject\TeleTest\APPDATA\sessions\+***********.session, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-06-17 22:16:55.399 | DEBUG    | core.telegram.client_manager:get_proxy:91 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-06-17 22:16:55.399 | DEBUG    | core.telegram.client_manager:get_proxy:138 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-06-17 22:16:55.439 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-17 22:16:58.802 | INFO     | core.telegram.client_manager:_connect_client:202 - 连接成功!
2025-06-17 22:16:59.282 | INFO     | core.telegram.client_manager:_connect_client:202 - 连接成功!
2025-06-17 22:17:00.024 | INFO     | core.telegram.client_manager:_verify_client_authorization:1186 - 账户已授权: +***********
2025-06-17 22:17:00.366 | INFO     | core.telegram.client_manager:_verify_client_authorization:1186 - 账户已授权: +***********
2025-06-17 22:17:02.371 | INFO     | core.telegram.client_manager:batch_auto_login:1360 - 批量登录完成: 总计 2, 成功 2, 失败 0
2025-06-17 22:17:02.510 | INFO     | app.services.account_service:batch_auto_login:1293 - 服务层：核心层登录完成: 成功 2, 失败 0 / 2
2025-06-17 22:17:10.480 | INFO     | ui.main_window:closeEvent:346 - MainWindow: 接收到关闭事件
2025-06-17 22:17:10.495 | INFO     | ui.main_window:_cleanup_before_quit:238 - MainWindow: 执行清理资源...
2025-06-17 22:17:10.496 | INFO     | core.telegram.client_worker:stop:108 - 正在停止Telegram客户端工作线程
2025-06-17 22:17:10.507 | INFO     | core.telegram.client_manager:cleanup_async:1131 - 开始清理资源并断开所有连接
2025-06-17 22:17:10.507 | INFO     | core.telegram.client_manager:disconnect_all_clients:716 - 正在断开所有客户端连接
2025-06-17 22:17:10.508 | INFO     | core.telegram.client_manager:_safe_disconnect:737 - 正在断开客户端连接: +***********
2025-06-17 22:17:10.508 | INFO     | core.telegram.client_manager:_safe_disconnect:737 - 正在断开客户端连接: +***********
2025-06-17 22:17:10.528 | INFO     | core.telegram.client_manager:_safe_disconnect:741 - 断开客户端连接成功: +***********
2025-06-17 22:17:10.529 | INFO     | core.telegram.client_manager:_safe_disconnect:741 - 断开客户端连接成功: +***********
2025-06-17 22:17:10.530 | INFO     | core.telegram.client_manager:cleanup_async:1150 - 资源清理完成
2025-06-17 22:17:11.015 | INFO     | core.telegram.client_manager:cleanup_async:1131 - 开始清理资源并断开所有连接
2025-06-17 22:17:11.015 | INFO     | core.telegram.client_manager:disconnect_all_clients:716 - 正在断开所有客户端连接
2025-06-17 22:17:11.015 | INFO     | core.telegram.client_manager:cleanup_async:1150 - 资源清理完成
2025-06-17 22:17:11.516 | INFO     | core.telegram.client_worker:run:97 - Telegram客户端工作线程已退出
2025-06-17 22:17:11.517 | INFO     | core.telegram.client_worker:stop:122 - Telegram客户端工作线程已停止
2025-06-17 22:17:11.520 | INFO     | ui.main_window:_cleanup_before_quit:247 - TelegramClientWorker 已停止。
2025-06-17 22:17:11.521 | INFO     | ui.main_window:_cleanup_before_quit:251 - MainWindow 清理完成
2025-06-17 22:17:11.563 | INFO     | __main__:main:111 - 应用程序已正常退出
2025-06-17 22:46:49.930 | INFO     | utils.logger:init_logger:98 - 日志系统初始化完成，默认级别: DEBUG，日志目录: H:\PyProject\TeleTest\APPDATA\logs
2025-06-17 22:46:53.582 | INFO     | data.database:init_database:31 - 初始化数据库连接: H:\PyProject\TeleTest\APPDATA\database\telegram_manager.db
2025-06-17 22:46:53.634 | DEBUG    | data.database:init_database:58 - 创建数据库表
2025-06-17 22:46:53.660 | INFO     | data.database:init_database:61 - 数据库初始化完成
2025-06-17 22:46:55.385 | DEBUG    | app.controllers.auth_controller:initialize_software:43 - 控制器: 初始化软件 app_id=1000, version=1.0
2025-06-17 22:46:55.386 | DEBUG    | core.auth.api_service:init_software:55 - 发送初始化请求: https://server.xile188.com/api/user/1000/windowns10/1.0/ini, params={}
2025-06-17 22:47:11.748 | DEBUG    | core.auth.api_service:init_software:57 - {'code': 200, 'msg': '成功', 'time': 1750171616, 'sign': 'e7f7c92b311b065adf564a5bd65c57b6', 'data': '9eefd48e8d0ccb4411b7429cf8b033f82544b065d147bea0ed46c0ec53e96aab51b1d6d04fab2220d26259dd7d02000222bd6a9513acf4cba1c5e6c83c21c362c31f9a5e62d737b41f8ea9c6df9df0d804ccfdf127daba905869dfef00464e33b0f0c12de0e729abd0895d1f1359ba3886c5f557320092dbe6143db57bd33ea2ffd7aa8ca686c4819991f96772ca02f025afb740a8641045b68ebf3bb4c88cec07be2136684e2383144dd41b1e79caa5cd9f764ea55a6c0daba2caa904bcd62b8c76936798a423f4d7cdd73c49b30a30f14442da3e8b5898adf82c1053b5726ed6030846580468990f405a3d0f9495e4b5ea773434107620b1f0f270bdede0bc48c3f330e44d7a54b306d4324a020d25', 'run': {'ms': 14.11, 'ram': '97.17k'}}
2025-06-17 22:47:26.343 | DEBUG    | core.auth.api_service:init_software:67 - 初始化数据解密成功: {'new_content': '1.多账户管理\n2.ip池管理\n3.消息监听\n4.消息群发\n5.拉人入群', 'new_url': None, 'notice': {'id': '2', 'content': '公告内容测试3', 'time': '**********'}}
2025-06-17 22:50:01.018 | INFO     | __main__:main:111 - 应用程序已正常退出
2025-06-17 22:50:05.160 | INFO     | utils.logger:init_logger:98 - 日志系统初始化完成，默认级别: DEBUG，日志目录: H:\PyProject\TeleTest\APPDATA\logs
2025-06-17 22:50:08.170 | INFO     | data.database:init_database:31 - 初始化数据库连接: H:\PyProject\TeleTest\APPDATA\database\telegram_manager.db
2025-06-17 22:50:08.222 | DEBUG    | data.database:init_database:58 - 创建数据库表
2025-06-17 22:50:08.256 | INFO     | data.database:init_database:61 - 数据库初始化完成
2025-06-17 22:50:10.422 | DEBUG    | app.controllers.auth_controller:initialize_software:43 - 控制器: 初始化软件 app_id=1000, version=1.0
2025-06-17 22:50:10.422 | DEBUG    | core.auth.api_service:init_software:55 - 发送初始化请求: https://server.xile188.com/api/user/1000/windowns10/1.0/ini, params={}
2025-06-17 22:50:23.889 | DEBUG    | core.auth.api_service:init_software:57 - {'code': 200, 'msg': '成功', 'time': 1750171811, 'sign': '156ed94ab318ea939e6c39b39f1491d5', 'data': '9eefd48e8d0ccb4411b7429cf8b033f82544b065d147bea0ed46c0ec53e96aab51b1d6d04fab2220d26259dd7d02000222bd6a9513acf4cba1c5e6c83c21c362c31f9a5e62d737b41f8ea9c6df9df0d804ccfdf127daba905869dfef00464e33b0f0c12de0e729abd0895d1f1359ba3886c5f557320092dbe6143db57bd33ea2ffd7aa8ca686c4819991f96772ca02f025afb740a8641045b68ebf3bb4c88cec02d8afa7d00ba19e091832da41e97a6fcf3d53f8a5256fe3f002bdfb70dccbe4e13c35bb08d20c4bccf72e35406666f798ed8c47ea20531d3e2fd95c9781e1fb054fe1fede23789e40f0153273a3ba033d0d5e107dab3c7b99aafbfe1a470bc10fb38576b34d698c32e6bcd1fef8707482619a2154c7d95864d06744ab972eb50457bd3c9fe631fb075037fd9043fc7153c6f201075a59cf928c765e3a485e0d', 'run': {'ms': 14.3, 'ram': '97.38k'}}
2025-06-17 22:50:30.287 | DEBUG    | core.auth.api_service:init_software:67 - 初始化数据解密成功: {'new_content': '1.多账户管理\n2.ip池管理\n3.消息监听\n4.消息群发\n5.拉人入群', 'new_url': 'https://www.aonisite.com/update%20v1.2.6.zip', 'notice': {'id': '2', 'content': '公告内容测试3', 'time': '**********'}}
2025-06-17 22:50:51.060 | INFO     | __main__:main:111 - 应用程序已正常退出
2025-06-17 23:18:23.825 | INFO     | utils.logger:init_logger:98 - 日志系统初始化完成，默认级别: DEBUG，日志目录: H:\PyProject\TeleTest\APPDATA\logs
2025-06-17 23:18:26.256 | INFO     | data.database:init_database:31 - 初始化数据库连接: H:\PyProject\TeleTest\APPDATA\database\telegram_manager.db
2025-06-17 23:18:26.301 | DEBUG    | data.database:init_database:58 - 创建数据库表
2025-06-17 23:18:26.336 | INFO     | data.database:init_database:61 - 数据库初始化完成
2025-06-17 23:18:28.992 | DEBUG    | app.controllers.auth_controller:initialize_software:43 - 控制器: 初始化软件 app_id=1000, version=1.0
2025-06-17 23:18:28.992 | DEBUG    | core.auth.api_service:init_software:55 - 发送初始化请求: https://server.xile188.com/api/user/1000/windowns10/1.0/ini, params={}
2025-06-17 23:18:56.887 | DEBUG    | core.auth.api_service:init_software:57 - {'code': 200, 'msg': '成功', 'time': 1750173510, 'sign': 'f625ab3f82fc1f5c9a7d7af8603d5fbd', 'data': '9eefd48e8d0ccb4411b7429cf8b033f82544b065d147bea0ed46c0ec53e96aab51b1d6d04fab2220d26259dd7d02000222bd6a9513acf4cba1c5e6c83c21c362c31f9a5e62d737b41f8ea9c6df9df0d804ccfdf127daba905869dfef00464e33b0f0c12de0e729abd0895d1f1359ba3886c5f557320092dbe6143db57bd33ea2ffd7aa8ca686c4819991f96772ca02f025afb740a8641045b68ebf3bb4c88cec02d8afa7d00ba19e091832da41e97a6fcf3d53f8a5256fe3f002bdfb70dccbe4e13c35bb08d20c4bccf72e35406666f798ed8c47ea20531d3e2fd95c9781e1fb054fe1fede23789e40f0153273a3ba033d0d5e107dab3c7b99aafbfe1a470bc10fb38576b34d698c32e6bcd1fef8707482619a2154c7d95864d06744ab972eb50457bd3c9fe631fb075037fd9043fc7153c6f201075a59cf928c765e3a485e0d', 'run': {'ms': 13.94, 'ram': '97.38k'}}
2025-06-17 23:18:56.901 | DEBUG    | core.auth.api_service:init_software:67 - 初始化数据解密成功: {'new_content': '1.多账户管理\n2.ip池管理\n3.消息监听\n4.消息群发\n5.拉人入群', 'new_url': 'https://www.aonisite.com/update%20v1.2.6.zip', 'notice': {'id': '2', 'content': '公告内容测试3', 'time': '**********'}}
2025-06-17 23:19:02.420 | INFO     | __main__:main:111 - 应用程序已正常退出
2025-06-17 23:58:11.784 | INFO     | utils.logger:init_logger:98 - 日志系统初始化完成，默认级别: DEBUG，日志目录: h:\PyProject\TeleTest\APPDATA\logs
2025-06-17 23:58:15.308 | INFO     | data.database:init_database:31 - 初始化数据库连接: h:\PyProject\TeleTest\APPDATA\database\telegram_manager.db
2025-06-17 23:58:15.344 | DEBUG    | data.database:init_database:58 - 创建数据库表
2025-06-17 23:58:15.366 | INFO     | data.database:init_database:61 - 数据库初始化完成
2025-06-17 23:58:17.525 | DEBUG    | app.controllers.auth_controller:initialize_software:43 - 控制器: 初始化软件 app_id=1000, version=1.0
2025-06-17 23:58:17.525 | DEBUG    | core.auth.api_service:init_software:55 - 发送初始化请求: https://server.xile188.com/api/user/1000/windowns10/1.0/ini, params={}
2025-06-17 23:58:23.873 | INFO     | __main__:main:111 - 应用程序已正常退出
