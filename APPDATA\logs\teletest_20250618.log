2025-06-18 11:19:40.657 | INFO     | utils.logger:init_logger:98 - 日志系统初始化完成，默认级别: DEBUG，日志目录: h:\PyProject\TeleTest\APPDATA\logs
2025-06-18 11:19:44.361 | INFO     | data.database:init_database:31 - 初始化数据库连接: h:\PyProject\TeleTest\APPDATA\database\telegram_manager.db
2025-06-18 11:19:44.387 | DEBUG    | data.database:init_database:58 - 创建数据库表
2025-06-18 11:19:44.402 | INFO     | data.database:init_database:61 - 数据库初始化完成
2025-06-18 11:19:45.636 | DEBUG    | app.controllers.auth_controller:initialize_software:43 - 控制器: 初始化软件 app_id=1000, version=1.0
2025-06-18 11:19:45.636 | DEBUG    | core.auth.api_service:init_software:55 - 发送初始化请求: https://server.xile188.com/api/user/1000/windowns10/1.0/ini, params={}
2025-06-18 11:19:46.942 | INFO     | utils.update_checker:check_for_updates:114 - 发现新版本: 1.2
2025-06-18 11:20:05.459 | INFO     | utils.logger:init_logger:98 - 日志系统初始化完成，默认级别: DEBUG，日志目录: h:\PyProject\TeleTest\APPDATA\logs
2025-06-18 11:20:07.173 | INFO     | data.database:init_database:31 - 初始化数据库连接: h:\PyProject\TeleTest\APPDATA\database\telegram_manager.db
2025-06-18 11:20:07.194 | DEBUG    | data.database:init_database:58 - 创建数据库表
2025-06-18 11:20:07.210 | INFO     | data.database:init_database:61 - 数据库初始化完成
2025-06-18 11:20:10.538 | DEBUG    | app.controllers.auth_controller:initialize_software:43 - 控制器: 初始化软件 app_id=1000, version=1.0
2025-06-18 11:20:10.539 | DEBUG    | core.auth.api_service:init_software:55 - 发送初始化请求: https://server.xile188.com/api/user/1000/windowns10/1.0/ini, params={}
2025-06-18 11:20:10.910 | INFO     | utils.update_checker:check_for_updates:114 - 发现新版本: 1.2
2025-06-18 11:23:31.694 | INFO     | utils.logger:init_logger:98 - 日志系统初始化完成，默认级别: DEBUG，日志目录: h:\PyProject\TeleTest\APPDATA\logs
2025-06-18 11:23:33.175 | INFO     | data.database:init_database:31 - 初始化数据库连接: h:\PyProject\TeleTest\APPDATA\database\telegram_manager.db
2025-06-18 11:23:33.196 | DEBUG    | data.database:init_database:58 - 创建数据库表
2025-06-18 11:23:33.210 | INFO     | data.database:init_database:61 - 数据库初始化完成
2025-06-18 11:23:33.981 | DEBUG    | app.controllers.auth_controller:initialize_software:43 - 控制器: 初始化软件 app_id=1000, version=1.0
2025-06-18 11:23:33.982 | DEBUG    | core.auth.api_service:init_software:55 - 发送初始化请求: https://server.xile188.com/api/user/1000/windowns10/1.0/ini, params={}
2025-06-18 11:23:34.327 | INFO     | utils.update_checker:check_for_updates:114 - 发现新版本: 1.2
2025-06-18 11:26:34.390 | INFO     | utils.logger:init_logger:98 - 日志系统初始化完成，默认级别: DEBUG，日志目录: h:\PyProject\TeleTest\APPDATA\logs
2025-06-18 11:26:35.911 | INFO     | data.database:init_database:31 - 初始化数据库连接: h:\PyProject\TeleTest\APPDATA\database\telegram_manager.db
2025-06-18 11:26:35.931 | DEBUG    | data.database:init_database:58 - 创建数据库表
2025-06-18 11:26:35.945 | INFO     | data.database:init_database:61 - 数据库初始化完成
2025-06-18 11:26:37.280 | DEBUG    | app.controllers.auth_controller:initialize_software:43 - 控制器: 初始化软件 app_id=1000, version=1.0
2025-06-18 11:26:37.281 | DEBUG    | core.auth.api_service:init_software:55 - 发送初始化请求: https://server.xile188.com/api/user/1000/windowns10/1.0/ini, params={}
2025-06-18 11:26:37.656 | WARNING  | utils.update_checker:check_for_updates:109 - 无法加载更新配置文件: h:\PyProject\TeleTest\update.tmp
2025-06-18 11:26:43.078 | INFO     | __main__:main:111 - 应用程序已正常退出
2025-06-18 11:35:06.336 | INFO     | utils.logger:init_logger:98 - 日志系统初始化完成，默认级别: DEBUG，日志目录: h:\PyProject\TeleTest\APPDATA\logs
2025-06-18 11:35:07.932 | INFO     | data.database:init_database:31 - 初始化数据库连接: h:\PyProject\TeleTest\APPDATA\database\telegram_manager.db
2025-06-18 11:35:07.953 | DEBUG    | data.database:init_database:58 - 创建数据库表
2025-06-18 11:35:07.969 | INFO     | data.database:init_database:61 - 数据库初始化完成
2025-06-18 11:35:10.076 | DEBUG    | app.controllers.auth_controller:initialize_software:43 - 控制器: 初始化软件 app_id=1000, version=1.0
2025-06-18 11:35:10.082 | DEBUG    | core.auth.api_service:init_software:55 - 发送初始化请求: https://server.xile188.com/api/user/1000/windowns10/1.0/ini, params={}
2025-06-18 11:35:10.431 | INFO     | utils.update_checker:check_for_updates:110 - 检测到新版本链接: https://www.aonisite.com/update%20v1.2.6.zip
2025-06-18 11:35:46.277 | INFO     | utils.logger:init_logger:98 - 日志系统初始化完成，默认级别: DEBUG，日志目录: h:\PyProject\TeleTest\APPDATA\logs
2025-06-18 11:35:47.962 | INFO     | data.database:init_database:31 - 初始化数据库连接: h:\PyProject\TeleTest\APPDATA\database\telegram_manager.db
2025-06-18 11:35:47.981 | DEBUG    | data.database:init_database:58 - 创建数据库表
2025-06-18 11:35:47.993 | INFO     | data.database:init_database:61 - 数据库初始化完成
2025-06-18 11:35:48.660 | DEBUG    | app.controllers.auth_controller:initialize_software:43 - 控制器: 初始化软件 app_id=1000, version=1.0
2025-06-18 11:35:48.661 | DEBUG    | core.auth.api_service:init_software:55 - 发送初始化请求: https://server.xile188.com/api/user/1000/windowns10/1.0/ini, params={}
2025-06-18 11:35:48.962 | INFO     | utils.update_checker:check_for_updates:110 - 检测到新版本链接: https://www.aonisite.com/update%20v1.2.6.zip
2025-06-18 11:36:44.285 | INFO     | utils.logger:init_logger:98 - 日志系统初始化完成，默认级别: DEBUG，日志目录: h:\PyProject\TeleTest\APPDATA\logs
2025-06-18 11:36:45.972 | INFO     | data.database:init_database:31 - 初始化数据库连接: h:\PyProject\TeleTest\APPDATA\database\telegram_manager.db
2025-06-18 11:36:45.997 | DEBUG    | data.database:init_database:58 - 创建数据库表
2025-06-18 11:36:46.015 | INFO     | data.database:init_database:61 - 数据库初始化完成
2025-06-18 11:36:46.683 | DEBUG    | app.controllers.auth_controller:initialize_software:43 - 控制器: 初始化软件 app_id=1000, version=1.0
2025-06-18 11:36:46.683 | DEBUG    | core.auth.api_service:init_software:55 - 发送初始化请求: https://server.xile188.com/api/user/1000/windowns10/1.0/ini, params={}
2025-06-18 11:36:47.066 | DEBUG    | core.auth.api_service:init_software:66 - 初始化数据解密成功: {'new_content': '1.多账户管理\n2.ip池管理\n3.消息监听\n4.消息群发\n5.拉人入群', 'new_url': 'https://www.aonisite.com/update%20v1.2.6.zip', 'notice': {'id': '2', 'content': '公告内容测试3', 'time': '**********'}}
2025-06-18 11:36:47.067 | INFO     | utils.update_checker:check_for_updates:110 - 检测到新版本链接: https://www.aonisite.com/update%20v1.2.6.zip
2025-06-18 11:43:49.184 | INFO     | utils.logger:init_logger:98 - 日志系统初始化完成，默认级别: DEBUG，日志目录: h:\PyProject\TeleTest\APPDATA\logs
2025-06-18 11:43:50.976 | INFO     | data.database:init_database:31 - 初始化数据库连接: h:\PyProject\TeleTest\APPDATA\database\telegram_manager.db
2025-06-18 11:43:50.995 | DEBUG    | data.database:init_database:58 - 创建数据库表
2025-06-18 11:43:51.011 | INFO     | data.database:init_database:61 - 数据库初始化完成
2025-06-18 11:43:52.249 | DEBUG    | app.controllers.auth_controller:initialize_software:43 - 控制器: 初始化软件 app_id=('1000',), version=1.2.7
2025-06-18 11:43:52.252 | DEBUG    | core.auth.api_service:init_software:55 - 发送初始化请求: ('https://server.xile188.com/',)api/user/('1000',)/('windowns10',)/1.2.7/ini, params={}
2025-06-18 11:43:52.278 | ERROR    | core.auth.api_service:init_software:59 - 软件初始化失败: Network Error: ('https://server.xile188.com/',)api/user/('1000',)/('windowns10',)/1.2.7/ini
2025-06-18 11:43:52.280 | INFO     | utils.update_checker:check_for_updates:136 - 从文件检测到新版本: 1.3
2025-06-18 11:48:47.619 | INFO     | utils.logger:init_logger:98 - 日志系统初始化完成，默认级别: DEBUG，日志目录: h:\PyProject\TeleTest\APPDATA\logs
2025-06-18 11:48:49.352 | INFO     | data.database:init_database:31 - 初始化数据库连接: h:\PyProject\TeleTest\APPDATA\database\telegram_manager.db
2025-06-18 11:48:49.370 | DEBUG    | data.database:init_database:58 - 创建数据库表
2025-06-18 11:48:49.388 | INFO     | data.database:init_database:61 - 数据库初始化完成
2025-06-18 11:48:50.730 | DEBUG    | app.controllers.auth_controller:initialize_software:43 - 控制器: 初始化软件 app_id=1000, version=1.2.7
2025-06-18 11:48:50.735 | DEBUG    | core.auth.api_service:init_software:55 - 发送初始化请求: https://server.xile188.com/api/user/1000/windowns10/1.2.7/ini, params={}
2025-06-18 11:48:51.379 | DEBUG    | core.auth.api_service:init_software:66 - 初始化数据解密成功: {'new_content': '- 新增功能\n  - 1.新增图片上传功能\n  - 2.新增软件文档\n  - 3.新增软件自动更新，后续更新会自动检测版本更新，无需再下载更新包\n  - 4.新增设置账户信息及发信控制\n- 修复bug\n  - 删除监听任务时，不删除对应任务采集到的数据。\n  - 监听设置群无法发送到目标群\n  - 导出监听数据时应当导出单个任务而非所有数据\n- 优化记录\n  - 优化发信规则，直接调取符合要求的账户发送，无账户时才需要等待延时。\n  - 完善软件设置', 'new_url': None, 'notice': {'id': '2', 'content': '公告内容测试3', 'time': '**********'}}
2025-06-18 11:48:51.380 | INFO     | utils.update_checker:check_for_updates:136 - 从文件检测到新版本: 1.3
2025-06-18 11:49:23.264 | INFO     | utils.logger:init_logger:98 - 日志系统初始化完成，默认级别: DEBUG，日志目录: h:\PyProject\TeleTest\APPDATA\logs
2025-06-18 11:49:25.089 | INFO     | data.database:init_database:31 - 初始化数据库连接: h:\PyProject\TeleTest\APPDATA\database\telegram_manager.db
2025-06-18 11:49:25.110 | DEBUG    | data.database:init_database:58 - 创建数据库表
2025-06-18 11:49:25.130 | INFO     | data.database:init_database:61 - 数据库初始化完成
2025-06-18 11:49:25.957 | DEBUG    | app.controllers.auth_controller:initialize_software:43 - 控制器: 初始化软件 app_id=1000, version=1.2.7
2025-06-18 11:49:25.958 | DEBUG    | core.auth.api_service:init_software:55 - 发送初始化请求: https://server.xile188.com/api/user/1000/windowns10/1.2.7/ini, params={}
2025-06-18 11:49:26.338 | DEBUG    | core.auth.api_service:init_software:66 - 初始化数据解密成功: {'new_content': '- 新增功能\n  - 1.新增图片上传功能\n  - 2.新增软件文档\n  - 3.新增软件自动更新，后续更新会自动检测版本更新，无需再下载更新包\n  - 4.新增设置账户信息及发信控制\n- 修复bug\n  - 删除监听任务时，不删除对应任务采集到的数据。\n  - 监听设置群无法发送到目标群\n  - 导出监听数据时应当导出单个任务而非所有数据\n- 优化记录\n  - 优化发信规则，直接调取符合要求的账户发送，无账户时才需要等待延时。\n  - 完善软件设置', 'new_url': None, 'notice': {'id': '2', 'content': '公告内容测试3', 'time': '**********'}}
2025-06-18 11:50:45.307 | INFO     | __main__:main:111 - 应用程序已正常退出
2025-06-18 11:51:00.594 | INFO     | utils.logger:init_logger:98 - 日志系统初始化完成，默认级别: DEBUG，日志目录: h:\PyProject\TeleTest\APPDATA\logs
2025-06-18 11:51:02.392 | INFO     | data.database:init_database:31 - 初始化数据库连接: h:\PyProject\TeleTest\APPDATA\database\telegram_manager.db
2025-06-18 11:51:02.412 | DEBUG    | data.database:init_database:58 - 创建数据库表
2025-06-18 11:51:02.425 | INFO     | data.database:init_database:61 - 数据库初始化完成
2025-06-18 11:51:03.106 | DEBUG    | app.controllers.auth_controller:initialize_software:43 - 控制器: 初始化软件 app_id=1000, version=1.0
2025-06-18 11:51:03.107 | DEBUG    | core.auth.api_service:init_software:55 - 发送初始化请求: https://server.xile188.com/api/user/1000/windowns10/1.0/ini, params={}
2025-06-18 11:51:03.509 | DEBUG    | core.auth.api_service:init_software:66 - 初始化数据解密成功: {'new_content': '1.多账户管理\n2.ip池管理\n3.消息监听\n4.消息群发\n5.拉人入群', 'new_url': 'https://www.aonisite.com/update%20v1.2.6.zip', 'notice': {'id': '2', 'content': '公告内容测试3', 'time': '**********'}}
2025-06-18 11:51:03.510 | INFO     | utils.update_checker:check_for_updates:110 - 检测到新版本链接: https://www.aonisite.com/update%20v1.2.6.zip
2025-06-18 11:51:34.564 | INFO     | utils.logger:init_logger:98 - 日志系统初始化完成，默认级别: DEBUG，日志目录: h:\PyProject\TeleTest\APPDATA\logs
2025-06-18 11:51:36.263 | INFO     | data.database:init_database:31 - 初始化数据库连接: h:\PyProject\TeleTest\APPDATA\database\telegram_manager.db
2025-06-18 11:51:36.285 | DEBUG    | data.database:init_database:58 - 创建数据库表
2025-06-18 11:51:36.305 | INFO     | data.database:init_database:61 - 数据库初始化完成
2025-06-18 11:51:37.149 | DEBUG    | app.controllers.auth_controller:initialize_software:43 - 控制器: 初始化软件 app_id=1000, version=1.0
2025-06-18 11:51:37.150 | DEBUG    | core.auth.api_service:init_software:55 - 发送初始化请求: https://server.xile188.com/api/user/1000/windowns10/1.0/ini, params={}
2025-06-18 11:51:37.486 | DEBUG    | core.auth.api_service:init_software:66 - 初始化数据解密成功: {'new_content': '1.多账户管理\n2.ip池管理\n3.消息监听\n4.消息群发\n5.拉人入群', 'new_url': 'https://www.aonisite.com/update%20v1.2.6.zip', 'notice': {'id': '2', 'content': '公告内容测试3', 'time': '**********'}}
2025-06-18 11:51:37.487 | INFO     | utils.update_checker:check_for_updates:110 - 检测到新版本链接: https://www.aonisite.com/update%20v1.2.6.zip
2025-06-18 11:51:50.277 | INFO     | utils.logger:init_logger:98 - 日志系统初始化完成，默认级别: DEBUG，日志目录: h:\PyProject\TeleTest\APPDATA\logs
2025-06-18 11:51:52.001 | INFO     | data.database:init_database:31 - 初始化数据库连接: h:\PyProject\TeleTest\APPDATA\database\telegram_manager.db
2025-06-18 11:51:52.026 | DEBUG    | data.database:init_database:58 - 创建数据库表
2025-06-18 11:51:52.042 | INFO     | data.database:init_database:61 - 数据库初始化完成
2025-06-18 11:51:52.776 | DEBUG    | app.controllers.auth_controller:initialize_software:43 - 控制器: 初始化软件 app_id=1000, version=1.2.7
2025-06-18 11:51:52.778 | DEBUG    | core.auth.api_service:init_software:55 - 发送初始化请求: https://server.xile188.com/api/user/1000/windowns10/1.2.7/ini, params={}
2025-06-18 11:51:53.214 | DEBUG    | core.auth.api_service:init_software:66 - 初始化数据解密成功: {'new_content': '- 新增功能\n  - 1.新增图片上传功能\n  - 2.新增软件文档\n  - 3.新增软件自动更新，后续更新会自动检测版本更新，无需再下载更新包\n  - 4.新增设置账户信息及发信控制\n- 修复bug\n  - 删除监听任务时，不删除对应任务采集到的数据。\n  - 监听设置群无法发送到目标群\n  - 导出监听数据时应当导出单个任务而非所有数据\n- 优化记录\n  - 优化发信规则，直接调取符合要求的账户发送，无账户时才需要等待延时。\n  - 完善软件设置', 'new_url': None, 'notice': {'id': '2', 'content': '公告内容测试3', 'time': '**********'}}
2025-06-18 11:51:53.215 | INFO     | utils.update_checker:check_for_updates:136 - 从文件检测到新版本: 1.1
2025-06-18 11:54:59.438 | INFO     | utils.logger:init_logger:98 - 日志系统初始化完成，默认级别: DEBUG，日志目录: h:\PyProject\TeleTest\APPDATA\logs
2025-06-18 11:55:01.058 | INFO     | data.database:init_database:31 - 初始化数据库连接: h:\PyProject\TeleTest\APPDATA\database\telegram_manager.db
2025-06-18 11:55:01.075 | DEBUG    | data.database:init_database:58 - 创建数据库表
2025-06-18 11:55:01.090 | INFO     | data.database:init_database:61 - 数据库初始化完成
2025-06-18 11:55:01.712 | DEBUG    | app.controllers.auth_controller:initialize_software:43 - 控制器: 初始化软件 app_id=1000, version=1.2.7
2025-06-18 11:55:01.713 | DEBUG    | core.auth.api_service:init_software:55 - 发送初始化请求: https://server.xile188.com/api/user/1000/windowns10/1.2.7/ini, params={}
2025-06-18 11:55:02.299 | DEBUG    | core.auth.api_service:init_software:66 - 初始化数据解密成功: {'new_content': '- 新增功能\n  - 1.新增图片上传功能\n  - 2.新增软件文档\n  - 3.新增软件自动更新，后续更新会自动检测版本更新，无需再下载更新包\n  - 4.新增设置账户信息及发信控制\n- 修复bug\n  - 删除监听任务时，不删除对应任务采集到的数据。\n  - 监听设置群无法发送到目标群\n  - 导出监听数据时应当导出单个任务而非所有数据\n- 优化记录\n  - 优化发信规则，直接调取符合要求的账户发送，无账户时才需要等待延时。\n  - 完善软件设置', 'new_url': None, 'notice': {'id': '2', 'content': '公告内容测试3', 'time': '**********'}}
2025-06-18 11:55:02.301 | INFO     | utils.update_checker:check_for_updates:136 - 从文件检测到新版本: 1.1
2025-06-18 11:55:15.926 | INFO     | utils.logger:init_logger:98 - 日志系统初始化完成，默认级别: DEBUG，日志目录: h:\PyProject\TeleTest\APPDATA\logs
2025-06-18 11:55:17.489 | INFO     | data.database:init_database:31 - 初始化数据库连接: h:\PyProject\TeleTest\APPDATA\database\telegram_manager.db
2025-06-18 11:55:17.506 | DEBUG    | data.database:init_database:58 - 创建数据库表
2025-06-18 11:55:17.521 | INFO     | data.database:init_database:61 - 数据库初始化完成
2025-06-18 11:55:18.141 | DEBUG    | app.controllers.auth_controller:initialize_software:43 - 控制器: 初始化软件 app_id=1000, version=1.2.7
2025-06-18 11:55:18.143 | DEBUG    | core.auth.api_service:init_software:55 - 发送初始化请求: https://server.xile188.com/api/user/1000/windowns10/1.2.7/ini, params={}
2025-06-18 11:55:18.458 | DEBUG    | core.auth.api_service:init_software:66 - 初始化数据解密成功: {'new_content': '- 新增功能\n  - 1.新增图片上传功能\n  - 2.新增软件文档\n  - 3.新增软件自动更新，后续更新会自动检测版本更新，无需再下载更新包\n  - 4.新增设置账户信息及发信控制\n- 修复bug\n  - 删除监听任务时，不删除对应任务采集到的数据。\n  - 监听设置群无法发送到目标群\n  - 导出监听数据时应当导出单个任务而非所有数据\n- 优化记录\n  - 优化发信规则，直接调取符合要求的账户发送，无账户时才需要等待延时。\n  - 完善软件设置', 'new_url': None, 'notice': {'id': '2', 'content': '公告内容测试3', 'time': '**********'}}
2025-06-18 11:55:18.459 | INFO     | utils.update_checker:check_for_updates:136 - 从文件检测到新版本: 1.1
2025-06-18 14:10:44.211 | INFO     | utils.logger:init_logger:98 - 日志系统初始化完成，默认级别: DEBUG，日志目录: h:\PyProject\TeleTest\APPDATA\logs
2025-06-18 14:10:45.864 | INFO     | data.database:init_database:31 - 初始化数据库连接: h:\PyProject\TeleTest\APPDATA\database\telegram_manager.db
2025-06-18 14:10:45.892 | DEBUG    | data.database:init_database:58 - 创建数据库表
2025-06-18 14:10:45.908 | INFO     | data.database:init_database:61 - 数据库初始化完成
2025-06-18 14:10:46.927 | DEBUG    | app.controllers.auth_controller:initialize_software:43 - 控制器: 初始化软件 app_id=1000, version=1.2.7
2025-06-18 14:10:46.928 | DEBUG    | core.auth.api_service:init_software:55 - 发送初始化请求: https://server.xile188.com/api/user/1000/windowns10/1.2.7/ini, params={}
2025-06-18 14:10:47.549 | DEBUG    | core.auth.api_service:init_software:66 - 初始化数据解密成功: {'new_content': '- 新增功能\n  - 1.新增图片上传功能\n  - 2.新增软件文档\n  - 3.新增软件自动更新，后续更新会自动检测版本更新，无需再下载更新包\n  - 4.新增设置账户信息及发信控制\n- 修复bug\n  - 删除监听任务时，不删除对应任务采集到的数据。\n  - 监听设置群无法发送到目标群\n  - 导出监听数据时应当导出单个任务而非所有数据\n- 优化记录\n  - 优化发信规则，直接调取符合要求的账户发送，无账户时才需要等待延时。\n  - 完善软件设置', 'new_url': None, 'notice': {'id': '2', 'content': '公告内容测试3', 'time': '**********'}}
2025-06-18 14:10:47.551 | INFO     | utils.update_checker:check_for_updates:136 - 从文件检测到新版本: 1.1
2025-06-18 14:12:02.657 | INFO     | utils.logger:init_logger:98 - 日志系统初始化完成，默认级别: DEBUG，日志目录: h:\PyProject\TeleTest\APPDATA\logs
2025-06-18 14:12:04.102 | INFO     | data.database:init_database:31 - 初始化数据库连接: h:\PyProject\TeleTest\APPDATA\database\telegram_manager.db
2025-06-18 14:12:04.121 | DEBUG    | data.database:init_database:58 - 创建数据库表
2025-06-18 14:12:04.134 | INFO     | data.database:init_database:61 - 数据库初始化完成
2025-06-18 14:12:04.756 | DEBUG    | app.controllers.auth_controller:initialize_software:43 - 控制器: 初始化软件 app_id=1000, version=1.2.7
2025-06-18 14:12:04.757 | DEBUG    | core.auth.api_service:init_software:55 - 发送初始化请求: https://server.xile188.com/api/user/1000/windowns10/1.2.7/ini, params={}
2025-06-18 14:12:05.049 | DEBUG    | core.auth.api_service:init_software:66 - 初始化数据解密成功: {'new_content': '- 新增功能\n  - 1.新增图片上传功能\n  - 2.新增软件文档\n  - 3.新增软件自动更新，后续更新会自动检测版本更新，无需再下载更新包\n  - 4.新增设置账户信息及发信控制\n- 修复bug\n  - 删除监听任务时，不删除对应任务采集到的数据。\n  - 监听设置群无法发送到目标群\n  - 导出监听数据时应当导出单个任务而非所有数据\n- 优化记录\n  - 优化发信规则，直接调取符合要求的账户发送，无账户时才需要等待延时。\n  - 完善软件设置', 'new_url': None, 'notice': {'id': '2', 'content': '公告内容测试3', 'time': '**********'}}
2025-06-18 14:12:05.050 | INFO     | utils.update_checker:check_for_updates:136 - 从文件检测到新版本: 1.1
2025-06-18 14:12:59.469 | INFO     | utils.logger:init_logger:98 - 日志系统初始化完成，默认级别: DEBUG，日志目录: h:\PyProject\TeleTest\APPDATA\logs
2025-06-18 14:13:00.923 | INFO     | data.database:init_database:31 - 初始化数据库连接: h:\PyProject\TeleTest\APPDATA\database\telegram_manager.db
2025-06-18 14:13:00.942 | DEBUG    | data.database:init_database:58 - 创建数据库表
2025-06-18 14:13:00.966 | INFO     | data.database:init_database:61 - 数据库初始化完成
2025-06-18 14:13:01.584 | DEBUG    | app.controllers.auth_controller:initialize_software:43 - 控制器: 初始化软件 app_id=1000, version=1.2.7
2025-06-18 14:13:01.584 | DEBUG    | core.auth.api_service:init_software:55 - 发送初始化请求: https://server.xile188.com/api/user/1000/windowns10/1.2.7/ini, params={}
2025-06-18 14:13:01.877 | DEBUG    | core.auth.api_service:init_software:66 - 初始化数据解密成功: {'new_content': '- 新增功能\n  - 1.新增图片上传功能\n  - 2.新增软件文档\n  - 3.新增软件自动更新，后续更新会自动检测版本更新，无需再下载更新包\n  - 4.新增设置账户信息及发信控制\n- 修复bug\n  - 删除监听任务时，不删除对应任务采集到的数据。\n  - 监听设置群无法发送到目标群\n  - 导出监听数据时应当导出单个任务而非所有数据\n- 优化记录\n  - 优化发信规则，直接调取符合要求的账户发送，无账户时才需要等待延时。\n  - 完善软件设置', 'new_url': None, 'notice': {'id': '2', 'content': '公告内容测试3', 'time': '**********'}}
2025-06-18 14:13:03.556 | INFO     | app.controllers.auth_controller:login:61 - 控制器: 用户登录 account=demo01
2025-06-18 14:13:06.596 | INFO     | core.auth.api_service:login:127 - 用户登录: account=demo01
2025-06-18 14:13:06.850 | INFO     | core.auth.api_service:get_user_info:347 - 获取用户信息
2025-06-18 14:13:07.129 | INFO     | core.auth.api_service:get_user_info:375 - 获取用户信息成功
2025-06-18 14:13:07.143 | INFO     | __main__:on_auth_completed:93 - 用户登录成功，正在启动主窗口...
2025-06-18 14:13:07.199 | INFO     | core.telegram.client_worker:__init__:61 - Telegram客户端工作线程初始化
2025-06-18 14:13:07.199 | INFO     | ui.main_window:_initialize_core_components:82 - MainWindow: 初始化核心组件...
2025-06-18 14:13:07.200 | INFO     | core.telegram.client_worker:run:65 - Telegram客户端工作线程开始运行
2025-06-18 14:13:07.201 | INFO     | app.services.account_service:__init__:42 - 账户服务初始化
2025-06-18 14:13:07.201 | INFO     | core.proxy.proxy_task_manager:run:284 - 代理任务管理器线程启动
2025-06-18 14:13:07.201 | INFO     | app.controllers.account_controller:__init__:81 - 账户控制器初始化
2025-06-18 14:13:07.202 | INFO     | app.services.monitor_service:__init__:39 - 初始化监控任务服务
2025-06-18 14:13:07.203 | INFO     | core.monitor.filter:__init__:26 - 初始化消息过滤器
2025-06-18 14:13:07.203 | INFO     | core.telegram.client_manager:__init__:66 - Telegram客户端管理器初始化
2025-06-18 14:13:07.203 | INFO     | core.monitor.extractor:__init__:24 - 初始化关键词提取器
2025-06-18 14:13:07.204 | INFO     | core.telegram.user_manager:__init__:30 - Telegram用户管理器初始化
2025-06-18 14:13:07.204 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-06-18 14:13:07.204 | INFO     | core.telegram.group_manager:__init__:36 - Telegram群组管理器初始化
2025-06-18 14:13:07.205 | INFO     | core.monitor.notifier:__init__:90 - 初始化通知管理器
2025-06-18 14:13:07.205 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-06-18 14:13:07.206 | INFO     | app.controllers.telegram_monitor_controller:__init__:64 - 初始化 Telegram 监控控制器
2025-06-18 14:13:07.206 | INFO     | core.telegram.monitor_manager:__init__:26 - Telegram监控管理器初始化
2025-06-18 14:13:07.208 | DEBUG    | app.controllers.telegram_monitor_controller:__init__:82 - 控制器初始化完成
2025-06-18 14:13:07.209 | INFO     | core.telegram.telegram_service:__init__:39 - Telegram服务初始化完成
2025-06-18 14:13:07.452 | INFO     | ui.views.monitor_view:__init__:48 - 初始化监控视图
2025-06-18 14:13:07.453 | DEBUG    | ui.views.monitor_view:_connect_signals:80 - 连接控制器信号
2025-06-18 14:13:07.650 | INFO     | ui.views.proxy_view:__init__:96 - 初始化视图，加载第一页代理数据
2025-06-18 14:13:07.858 | INFO     | __main__:on_auth_completed:97 - 主窗口已启动
2025-06-18 14:13:07.922 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:88 - 尝试连接监听日志信号...
2025-06-18 14:13:07.923 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:90 - 监听日志信号连接成功
2025-06-18 14:13:07.924 | INFO     | core.auth.api_service:verify_vip:425 - 会员验证接口调用
2025-06-18 14:13:07.925 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-18 14:13:07.930 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-18 14:13:07.934 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:145 - 加载监控任务列表 (搜索关键词: '')
2025-06-18 14:13:07.934 | DEBUG    | ui.views.monitor_view:_on_loading_started:620 - 加载中: 正在加载任务列表...
2025-06-18 14:13:07.935 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-18 14:13:07.945 | INFO     | app.services.proxy_service:refresh_proxies:712 - 刷新代理列表: offset=0, limit=20
2025-06-18 14:13:07.946 | INFO     | core.proxy.proxy_task_manager:submit_task:172 - 提交耗时任务到子线程: refresh_proxies_0_20
2025-06-18 14:13:07.947 | DEBUG    | app.services.proxy_service:get_proxies:500 - 获取代理列表: offset=0, limit=20
2025-06-18 14:13:07.947 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-18 14:13:07.947 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-18 14:13:07.952 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-18 14:13:07.981 | INFO     | app.controllers.invite_controller:get_invite_tasks:38 - 获取邀请任务列表, status=None
2025-06-18 14:13:07.981 | INFO     | app.services.invite_service:get_invite_tasks:121 - 获取邀请任务列表, status=None
2025-06-18 14:13:07.982 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-18 14:13:07.985 | INFO     | app.services.invite_service:task_initialize:39 - 初始化邀请服务，重置运行中的任务为暂停状态
2025-06-18 14:13:07.986 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-18 14:13:07.987 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-18 14:13:07.989 | INFO     | core.proxy.proxy_task_manager:task_wrapper:199 - 任务 refresh_proxies_0_20 完成
2025-06-18 14:13:08.095 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-18 14:13:08.097 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-18 14:13:08.100 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-18 14:13:08.101 | INFO     | app.services.invite_service:task_initialize:48 - 成功重置 0 个任务状态
2025-06-18 14:13:08.102 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-18 14:13:08.105 | DEBUG    | ui.views.monitor_view:_refresh_task_list:175 - 刷新任务列表，共2个任务
2025-06-18 14:13:08.111 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-18 14:13:08.117 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-18 14:13:08.119 | INFO     | app.services.account_service:get_all_groups:96 - 获取所有账户分组成功, 共 2 个
2025-06-18 14:13:08.119 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-18 14:13:08.123 | DEBUG    | ui.views.account_view:_on_groups_loaded:442 - 分组加载完成: 2个分组
2025-06-18 14:13:08.140 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-18 14:13:08.145 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-18 14:13:08.205 | INFO     | app.services.account_service:get_all_accounts:289 - 获取所有账户成功, 共 2 个
2025-06-18 14:13:08.205 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-18 14:13:08.211 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-18 14:13:08.212 | DEBUG    | ui.views.account_view:_on_accounts_loaded:575 - 账户加载完成: 2个账户
2025-06-18 14:13:08.233 | DEBUG    | ui.views.account_view:_update_group_counts:184 - 分组计数已更新: {2: 1, 1: 1}
2025-06-18 14:13:08.236 | INFO     | ui.views.account_view:_auto_login_accounts:630 - 开始调用控制器进行 2 个账户的自动登录。
2025-06-18 14:13:08.236 | INFO     | app.services.account_service:batch_auto_login:1153 - 服务层：开始批量自动登录 2 个账户, 最大并发: 5
2025-06-18 14:13:08.237 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-18 14:13:08.241 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-18 14:13:08.246 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-18 14:13:08.247 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-06-18 14:13:08.263 | INFO     | app.services.account_service:get_all_accounts:289 - 获取所有账户成功, 共 2 个
2025-06-18 14:13:08.264 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-18 14:13:08.265 | INFO     | app.services.account_service:batch_auto_login:1192 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 1********:1080
2025-06-18 14:13:08.266 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-18 14:13:08.267 | INFO     | app.controllers.telegram_monitor_controller:_load_total_users_count_internal:191 - 成功加载总体统计数据: 总用户数 1845, 今日采集 0, 日均采集 264, 运行天数 7
2025-06-18 14:13:08.268 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:156 - 成功加载 2 个任务
2025-06-18 14:13:08.268 | DEBUG    | ui.views.monitor_view:_on_loading_finished:625 - 加载完成
2025-06-18 14:13:08.270 | DEBUG    | ui.views.account_view:_update_group_counts:184 - 分组计数已更新: {2: 1, 1: 1}
2025-06-18 14:13:08.271 | DEBUG    | ui.views.account_view:_init_cache:151 - 账户缓存初始化完成，共 2 个账户
2025-06-18 14:13:08.277 | DEBUG    | ui.views.account_view:_on_accounts_loaded:575 - 账户加载完成: 2个账户
2025-06-18 14:13:08.300 | DEBUG    | ui.views.account_view:_update_group_counts:184 - 分组计数已更新: {2: 1, 1: 1}
2025-06-18 14:13:08.308 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-18 14:13:08.311 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=True
2025-06-18 14:13:08.313 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=True
2025-06-18 14:13:08.315 | INFO     | ui.views.proxy_view:_load_initial_data:1044 - 初始数据加载完成，共 1 个代理
2025-06-18 14:13:08.319 | INFO     | app.services.account_service:batch_auto_login:1192 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 1********:1080
2025-06-18 14:13:08.319 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-18 14:13:08.321 | INFO     | app.services.account_service:batch_auto_login:1262 - 服务层：准备调用核心层处理 2 个账户的自动登录。
2025-06-18 14:13:08.322 | INFO     | app.services.account_service:batch_auto_login:1272 - 服务层：设置核心层任务超时为 120 秒。
2025-06-18 14:13:08.323 | INFO     | core.telegram.client_manager:batch_auto_login:1211 - 开始批量自动登录, 共 2 个账户, 最大并发 5
2025-06-18 14:13:08.324 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-18 14:13:08.325 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:254 - 创建客户端: h:\PyProject\TeleTest\APPDATA\sessions\+***********.session, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '1********', 'port': 1080}
2025-06-18 14:13:08.326 | DEBUG    | core.telegram.client_manager:get_proxy:91 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '1********', 'port': 1080}
2025-06-18 14:13:08.328 | DEBUG    | core.telegram.client_manager:get_proxy:138 - get_proxy: 使用 ip_pool 代理 1********:1080，转换结果: (<ProxyType.SOCKS5: 2>, '1********', 1080)
2025-06-18 14:13:08.333 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:254 - 创建客户端: h:\PyProject\TeleTest\APPDATA\sessions\+***********.session, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '1********', 'port': 1080}
2025-06-18 14:13:08.335 | DEBUG    | core.telegram.client_manager:get_proxy:91 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '1********', 'port': 1080}
2025-06-18 14:13:08.337 | DEBUG    | core.telegram.client_manager:get_proxy:138 - get_proxy: 使用 ip_pool 代理 1********:1080，转换结果: (<ProxyType.SOCKS5: 2>, '1********', 1080)
2025-06-18 14:13:08.360 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-18 14:13:08.371 | WARNING  | core.auth.api_service:verify_vip:441 - 会员验证失败: 验证失败
2025-06-18 14:13:08.738 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-18 14:13:10.457 | INFO     | core.telegram.client_manager:_connect_client:202 - 连接成功!
2025-06-18 14:13:10.533 | INFO     | core.telegram.client_manager:_connect_client:202 - 连接成功!
2025-06-18 14:13:11.091 | INFO     | core.telegram.client_manager:_verify_client_authorization:1186 - 账户已授权: +***********
2025-06-18 14:13:11.245 | INFO     | core.telegram.client_manager:_verify_client_authorization:1186 - 账户已授权: +***********
2025-06-18 14:13:13.248 | INFO     | core.telegram.client_manager:batch_auto_login:1360 - 批量登录完成: 总计 2, 成功 2, 失败 0
2025-06-18 14:13:13.381 | INFO     | app.services.account_service:batch_auto_login:1293 - 服务层：核心层登录完成: 成功 2, 失败 0 / 2
2025-06-18 14:14:07.140 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-18 14:15:07.138 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-18 14:16:07.137 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-18 14:16:26.146 | INFO     | ui.main_window:closeEvent:346 - MainWindow: 接收到关闭事件
2025-06-18 14:16:26.156 | INFO     | ui.main_window:_cleanup_before_quit:238 - MainWindow: 执行清理资源...
2025-06-18 14:16:26.156 | INFO     | core.telegram.client_worker:stop:108 - 正在停止Telegram客户端工作线程
2025-06-18 14:16:26.166 | INFO     | core.telegram.client_manager:cleanup_async:1131 - 开始清理资源并断开所有连接
2025-06-18 14:16:26.167 | INFO     | core.telegram.client_manager:disconnect_all_clients:716 - 正在断开所有客户端连接
2025-06-18 14:16:26.167 | INFO     | core.telegram.client_manager:_safe_disconnect:737 - 正在断开客户端连接: +***********
2025-06-18 14:16:26.168 | INFO     | core.telegram.client_manager:_safe_disconnect:737 - 正在断开客户端连接: +***********
2025-06-18 14:16:26.182 | INFO     | core.telegram.client_manager:_safe_disconnect:741 - 断开客户端连接成功: +***********
2025-06-18 14:16:26.183 | INFO     | core.telegram.client_manager:_safe_disconnect:741 - 断开客户端连接成功: +***********
2025-06-18 14:16:26.183 | INFO     | core.telegram.client_manager:cleanup_async:1150 - 资源清理完成
2025-06-18 14:16:26.669 | INFO     | core.telegram.client_manager:cleanup_async:1131 - 开始清理资源并断开所有连接
2025-06-18 14:16:26.669 | INFO     | core.telegram.client_manager:disconnect_all_clients:716 - 正在断开所有客户端连接
2025-06-18 14:16:26.670 | INFO     | core.telegram.client_manager:cleanup_async:1150 - 资源清理完成
2025-06-18 14:16:27.170 | INFO     | core.telegram.client_worker:run:97 - Telegram客户端工作线程已退出
2025-06-18 14:16:27.171 | INFO     | core.telegram.client_worker:stop:122 - Telegram客户端工作线程已停止
2025-06-18 14:16:27.171 | INFO     | ui.main_window:_cleanup_before_quit:247 - TelegramClientWorker 已停止。
2025-06-18 14:16:27.172 | INFO     | ui.main_window:_cleanup_before_quit:251 - MainWindow 清理完成
2025-06-18 14:16:27.180 | INFO     | __main__:main:111 - 应用程序已正常退出
2025-06-18 14:16:31.754 | INFO     | utils.logger:init_logger:98 - 日志系统初始化完成，默认级别: DEBUG，日志目录: h:\PyProject\TeleTest\APPDATA\logs
2025-06-18 14:16:33.353 | INFO     | data.database:init_database:31 - 初始化数据库连接: h:\PyProject\TeleTest\APPDATA\database\telegram_manager.db
2025-06-18 14:16:33.371 | DEBUG    | data.database:init_database:58 - 创建数据库表
2025-06-18 14:16:33.384 | INFO     | data.database:init_database:61 - 数据库初始化完成
2025-06-18 14:16:33.984 | DEBUG    | app.controllers.auth_controller:initialize_software:43 - 控制器: 初始化软件 app_id=1000, version=1.2.7
2025-06-18 14:16:33.985 | DEBUG    | core.auth.api_service:init_software:55 - 发送初始化请求: https://server.xile188.com/api/user/1000/windowns10/1.2.7/ini, params={}
2025-06-18 14:16:34.334 | DEBUG    | core.auth.api_service:init_software:66 - 初始化数据解密成功: {'new_content': '- 新增功能\n  - 1.新增图片上传功能\n  - 2.新增软件文档\n  - 3.新增软件自动更新，后续更新会自动检测版本更新，无需再下载更新包\n  - 4.新增设置账户信息及发信控制\n- 修复bug\n  - 删除监听任务时，不删除对应任务采集到的数据。\n  - 监听设置群无法发送到目标群\n  - 导出监听数据时应当导出单个任务而非所有数据\n- 优化记录\n  - 优化发信规则，直接调取符合要求的账户发送，无账户时才需要等待延时。\n  - 完善软件设置', 'new_url': None, 'notice': {'id': '2', 'content': '公告内容测试3', 'time': '**********'}}
2025-06-18 14:16:47.693 | INFO     | app.controllers.auth_controller:login:61 - 控制器: 用户登录 account=demo01
2025-06-18 14:16:50.627 | INFO     | core.auth.api_service:login:127 - 用户登录: account=demo01
2025-06-18 14:16:50.930 | INFO     | core.auth.api_service:get_user_info:347 - 获取用户信息
2025-06-18 14:16:51.144 | INFO     | core.auth.api_service:get_user_info:375 - 获取用户信息成功
2025-06-18 14:16:51.151 | INFO     | __main__:on_auth_completed:93 - 用户登录成功，正在启动主窗口...
2025-06-18 14:16:51.178 | INFO     | core.telegram.client_worker:__init__:61 - Telegram客户端工作线程初始化
2025-06-18 14:16:51.179 | INFO     | ui.main_window:_initialize_core_components:82 - MainWindow: 初始化核心组件...
2025-06-18 14:16:51.180 | INFO     | core.telegram.client_worker:run:65 - Telegram客户端工作线程开始运行
2025-06-18 14:16:51.180 | INFO     | core.proxy.proxy_task_manager:run:284 - 代理任务管理器线程启动
2025-06-18 14:16:51.181 | INFO     | app.services.account_service:__init__:42 - 账户服务初始化
2025-06-18 14:16:51.181 | INFO     | app.controllers.account_controller:__init__:81 - 账户控制器初始化
2025-06-18 14:16:51.182 | INFO     | app.services.monitor_service:__init__:39 - 初始化监控任务服务
2025-06-18 14:16:51.182 | INFO     | core.telegram.client_manager:__init__:66 - Telegram客户端管理器初始化
2025-06-18 14:16:51.183 | INFO     | core.monitor.filter:__init__:26 - 初始化消息过滤器
2025-06-18 14:16:51.183 | INFO     | core.telegram.user_manager:__init__:30 - Telegram用户管理器初始化
2025-06-18 14:16:51.183 | INFO     | core.monitor.extractor:__init__:24 - 初始化关键词提取器
2025-06-18 14:16:51.183 | INFO     | core.telegram.group_manager:__init__:36 - Telegram群组管理器初始化
2025-06-18 14:16:51.184 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-06-18 14:16:51.184 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-06-18 14:16:51.184 | INFO     | core.monitor.notifier:__init__:90 - 初始化通知管理器
2025-06-18 14:16:51.185 | INFO     | core.telegram.monitor_manager:__init__:26 - Telegram监控管理器初始化
2025-06-18 14:16:51.185 | INFO     | app.controllers.telegram_monitor_controller:__init__:64 - 初始化 Telegram 监控控制器
2025-06-18 14:16:51.185 | INFO     | core.telegram.telegram_service:__init__:39 - Telegram服务初始化完成
2025-06-18 14:16:51.186 | DEBUG    | app.controllers.telegram_monitor_controller:__init__:82 - 控制器初始化完成
2025-06-18 14:16:51.379 | INFO     | ui.views.monitor_view:__init__:48 - 初始化监控视图
2025-06-18 14:16:51.380 | DEBUG    | ui.views.monitor_view:_connect_signals:80 - 连接控制器信号
2025-06-18 14:16:51.562 | INFO     | ui.views.proxy_view:__init__:96 - 初始化视图，加载第一页代理数据
2025-06-18 14:16:51.756 | INFO     | __main__:on_auth_completed:97 - 主窗口已启动
2025-06-18 14:16:51.807 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:88 - 尝试连接监听日志信号...
2025-06-18 14:16:51.808 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:90 - 监听日志信号连接成功
2025-06-18 14:16:51.809 | INFO     | core.auth.api_service:verify_vip:425 - 会员验证接口调用
2025-06-18 14:16:51.810 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-18 14:16:51.816 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-18 14:16:51.819 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:145 - 加载监控任务列表 (搜索关键词: '')
2025-06-18 14:16:51.819 | DEBUG    | ui.views.monitor_view:_on_loading_started:620 - 加载中: 正在加载任务列表...
2025-06-18 14:16:51.820 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-18 14:16:51.827 | INFO     | app.services.proxy_service:refresh_proxies:712 - 刷新代理列表: offset=0, limit=20
2025-06-18 14:16:51.828 | INFO     | core.proxy.proxy_task_manager:submit_task:172 - 提交耗时任务到子线程: refresh_proxies_0_20
2025-06-18 14:16:51.828 | DEBUG    | app.services.proxy_service:get_proxies:500 - 获取代理列表: offset=0, limit=20
2025-06-18 14:16:51.828 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-18 14:16:51.829 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-18 14:16:51.834 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-18 14:16:51.856 | INFO     | app.controllers.invite_controller:get_invite_tasks:38 - 获取邀请任务列表, status=None
2025-06-18 14:16:51.856 | INFO     | app.services.invite_service:get_invite_tasks:121 - 获取邀请任务列表, status=None
2025-06-18 14:16:51.857 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-18 14:16:51.860 | INFO     | app.services.invite_service:task_initialize:39 - 初始化邀请服务，重置运行中的任务为暂停状态
2025-06-18 14:16:51.861 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-18 14:16:51.861 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-18 14:16:51.864 | INFO     | core.proxy.proxy_task_manager:task_wrapper:199 - 任务 refresh_proxies_0_20 完成
2025-06-18 14:16:52.061 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-18 14:16:52.065 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-18 14:16:52.071 | INFO     | app.services.invite_service:task_initialize:48 - 成功重置 0 个任务状态
2025-06-18 14:16:52.072 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-18 14:16:52.074 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-18 14:16:52.076 | DEBUG    | ui.views.monitor_view:_refresh_task_list:175 - 刷新任务列表，共2个任务
2025-06-18 14:16:52.081 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-18 14:16:52.091 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-18 14:16:52.094 | INFO     | app.services.account_service:get_all_groups:96 - 获取所有账户分组成功, 共 2 个
2025-06-18 14:16:52.095 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-18 14:16:52.097 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-18 14:16:52.100 | DEBUG    | ui.views.account_view:_on_groups_loaded:442 - 分组加载完成: 2个分组
2025-06-18 14:16:52.115 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-18 14:16:52.181 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-18 14:16:52.182 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-18 14:16:52.183 | INFO     | app.services.account_service:get_all_accounts:289 - 获取所有账户成功, 共 2 个
2025-06-18 14:16:52.183 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-18 14:16:52.185 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-06-18 14:16:52.199 | INFO     | app.controllers.telegram_monitor_controller:_load_total_users_count_internal:191 - 成功加载总体统计数据: 总用户数 1845, 今日采集 0, 日均采集 264, 运行天数 7
2025-06-18 14:16:52.199 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:156 - 成功加载 2 个任务
2025-06-18 14:16:52.200 | DEBUG    | ui.views.monitor_view:_on_loading_finished:625 - 加载完成
2025-06-18 14:16:52.201 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-18 14:16:52.202 | DEBUG    | ui.views.account_view:_on_accounts_loaded:575 - 账户加载完成: 2个账户
2025-06-18 14:16:52.224 | DEBUG    | ui.views.account_view:_update_group_counts:184 - 分组计数已更新: {2: 1, 1: 1}
2025-06-18 14:16:52.229 | INFO     | ui.views.account_view:_auto_login_accounts:630 - 开始调用控制器进行 2 个账户的自动登录。
2025-06-18 14:16:52.229 | INFO     | app.services.account_service:batch_auto_login:1153 - 服务层：开始批量自动登录 2 个账户, 最大并发: 5
2025-06-18 14:16:52.230 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-18 14:16:52.235 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=True
2025-06-18 14:16:52.237 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=True
2025-06-18 14:16:52.239 | INFO     | ui.views.proxy_view:_load_initial_data:1044 - 初始数据加载完成，共 1 个代理
2025-06-18 14:16:52.245 | INFO     | app.services.account_service:batch_auto_login:1192 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 1********:1080
2025-06-18 14:16:52.245 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-18 14:16:52.246 | INFO     | app.services.account_service:get_all_accounts:289 - 获取所有账户成功, 共 2 个
2025-06-18 14:16:52.246 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-18 14:16:52.249 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-18 14:16:52.250 | DEBUG    | ui.views.account_view:_update_group_counts:184 - 分组计数已更新: {2: 1, 1: 1}
2025-06-18 14:16:52.250 | DEBUG    | ui.views.account_view:_init_cache:151 - 账户缓存初始化完成，共 2 个账户
2025-06-18 14:16:52.254 | DEBUG    | ui.views.account_view:_on_accounts_loaded:575 - 账户加载完成: 2个账户
2025-06-18 14:16:52.276 | DEBUG    | ui.views.account_view:_update_group_counts:184 - 分组计数已更新: {2: 1, 1: 1}
2025-06-18 14:16:52.284 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-18 14:16:52.285 | INFO     | app.services.account_service:batch_auto_login:1192 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 1********:1080
2025-06-18 14:16:52.286 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-18 14:16:52.312 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-18 14:16:52.316 | WARNING  | core.auth.api_service:verify_vip:441 - 会员验证失败: 验证失败
2025-06-18 14:16:52.364 | INFO     | app.services.account_service:batch_auto_login:1262 - 服务层：准备调用核心层处理 2 个账户的自动登录。
2025-06-18 14:16:52.366 | INFO     | core.telegram.client_manager:batch_auto_login:1211 - 开始批量自动登录, 共 2 个账户, 最大并发 5
2025-06-18 14:16:52.366 | INFO     | app.services.account_service:batch_auto_login:1272 - 服务层：设置核心层任务超时为 120 秒。
2025-06-18 14:16:52.366 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:254 - 创建客户端: h:\PyProject\TeleTest\APPDATA\sessions\+***********.session, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '1********', 'port': 1080}
2025-06-18 14:16:52.367 | DEBUG    | core.telegram.client_manager:get_proxy:91 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '1********', 'port': 1080}
2025-06-18 14:16:52.368 | DEBUG    | core.telegram.client_manager:get_proxy:138 - get_proxy: 使用 ip_pool 代理 1********:1080，转换结果: (<ProxyType.SOCKS5: 2>, '1********', 1080)
2025-06-18 14:16:52.373 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:254 - 创建客户端: h:\PyProject\TeleTest\APPDATA\sessions\+***********.session, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '1********', 'port': 1080}
2025-06-18 14:16:52.374 | DEBUG    | core.telegram.client_manager:get_proxy:91 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '1********', 'port': 1080}
2025-06-18 14:16:52.376 | DEBUG    | core.telegram.client_manager:get_proxy:138 - get_proxy: 使用 ip_pool 代理 1********:1080，转换结果: (<ProxyType.SOCKS5: 2>, '1********', 1080)
2025-06-18 14:16:52.576 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-18 14:16:54.570 | INFO     | core.telegram.client_manager:_connect_client:202 - 连接成功!
2025-06-18 14:16:54.662 | INFO     | core.telegram.client_manager:_connect_client:202 - 连接成功!
2025-06-18 14:16:55.237 | INFO     | core.telegram.client_manager:_verify_client_authorization:1186 - 账户已授权: +***********
2025-06-18 14:16:55.413 | INFO     | core.telegram.client_manager:_verify_client_authorization:1186 - 账户已授权: +***********
2025-06-18 14:16:57.425 | INFO     | core.telegram.client_manager:batch_auto_login:1360 - 批量登录完成: 总计 2, 成功 2, 失败 0
2025-06-18 14:16:58.387 | INFO     | app.services.account_service:batch_auto_login:1293 - 服务层：核心层登录完成: 成功 2, 失败 0 / 2
2025-06-18 14:17:17.097 | INFO     | ui.main_window:closeEvent:346 - MainWindow: 接收到关闭事件
2025-06-18 14:17:17.107 | INFO     | ui.main_window:_cleanup_before_quit:238 - MainWindow: 执行清理资源...
2025-06-18 14:17:17.107 | INFO     | core.telegram.client_worker:stop:108 - 正在停止Telegram客户端工作线程
2025-06-18 14:17:17.115 | INFO     | core.telegram.client_manager:cleanup_async:1131 - 开始清理资源并断开所有连接
2025-06-18 14:17:17.116 | INFO     | core.telegram.client_manager:disconnect_all_clients:716 - 正在断开所有客户端连接
2025-06-18 14:17:17.116 | INFO     | core.telegram.client_manager:_safe_disconnect:737 - 正在断开客户端连接: +***********
2025-06-18 14:17:17.117 | INFO     | core.telegram.client_manager:_safe_disconnect:737 - 正在断开客户端连接: +***********
2025-06-18 14:17:17.127 | INFO     | core.telegram.client_manager:_safe_disconnect:741 - 断开客户端连接成功: +***********
2025-06-18 14:17:17.128 | INFO     | core.telegram.client_manager:_safe_disconnect:741 - 断开客户端连接成功: +***********
2025-06-18 14:17:17.128 | INFO     | core.telegram.client_manager:cleanup_async:1150 - 资源清理完成
2025-06-18 14:17:17.629 | INFO     | core.telegram.client_manager:cleanup_async:1131 - 开始清理资源并断开所有连接
2025-06-18 14:17:17.629 | INFO     | core.telegram.client_manager:disconnect_all_clients:716 - 正在断开所有客户端连接
2025-06-18 14:17:17.630 | INFO     | core.telegram.client_manager:cleanup_async:1150 - 资源清理完成
2025-06-18 14:17:18.115 | INFO     | core.telegram.client_worker:run:97 - Telegram客户端工作线程已退出
2025-06-18 14:17:18.116 | INFO     | core.telegram.client_worker:stop:122 - Telegram客户端工作线程已停止
2025-06-18 14:17:18.117 | INFO     | ui.main_window:_cleanup_before_quit:247 - TelegramClientWorker 已停止。
2025-06-18 14:17:18.117 | INFO     | ui.main_window:_cleanup_before_quit:251 - MainWindow 清理完成
2025-06-18 14:17:18.121 | INFO     | __main__:main:111 - 应用程序已正常退出
2025-06-18 14:24:43.476 | INFO     | utils.logger:init_logger:98 - 日志系统初始化完成，默认级别: DEBUG，日志目录: h:\PyProject\TeleTest\APPDATA\logs
2025-06-18 14:24:45.078 | INFO     | data.database:init_database:31 - 初始化数据库连接: h:\PyProject\TeleTest\APPDATA\database\telegram_manager.db
2025-06-18 14:24:45.096 | DEBUG    | data.database:init_database:58 - 创建数据库表
2025-06-18 14:24:45.108 | INFO     | data.database:init_database:61 - 数据库初始化完成
2025-06-18 14:24:45.719 | DEBUG    | app.controllers.auth_controller:initialize_software:43 - 控制器: 初始化软件 app_id=1000, version=1.2.7
2025-06-18 14:24:45.719 | DEBUG    | core.auth.api_service:init_software:55 - 发送初始化请求: https://server.xile188.com/api/user/1000/windowns10/1.2.7/ini, params={}
2025-06-18 14:24:46.057 | DEBUG    | core.auth.api_service:init_software:66 - 初始化数据解密成功: {'new_content': '- 新增功能\n  - 1.新增图片上传功能\n  - 2.新增软件文档\n  - 3.新增软件自动更新，后续更新会自动检测版本更新，无需再下载更新包\n  - 4.新增设置账户信息及发信控制\n- 修复bug\n  - 删除监听任务时，不删除对应任务采集到的数据。\n  - 监听设置群无法发送到目标群\n  - 导出监听数据时应当导出单个任务而非所有数据\n- 优化记录\n  - 优化发信规则，直接调取符合要求的账户发送，无账户时才需要等待延时。\n  - 完善软件设置', 'new_url': None, 'notice': {'id': '2', 'content': '公告内容测试3', 'time': '**********'}}
2025-06-18 14:24:50.706 | INFO     | app.controllers.auth_controller:login:61 - 控制器: 用户登录 account=demo01
2025-06-18 14:24:53.759 | INFO     | core.auth.api_service:login:127 - 用户登录: account=demo01
2025-06-18 14:24:54.069 | INFO     | core.auth.api_service:get_user_info:347 - 获取用户信息
2025-06-18 14:24:54.362 | INFO     | core.auth.api_service:get_user_info:375 - 获取用户信息成功
2025-06-18 14:24:54.368 | INFO     | __main__:on_auth_completed:93 - 用户登录成功，正在启动主窗口...
2025-06-18 14:24:54.391 | INFO     | core.telegram.client_worker:__init__:61 - Telegram客户端工作线程初始化
2025-06-18 14:24:54.392 | INFO     | ui.main_window:_initialize_core_components:82 - MainWindow: 初始化核心组件...
2025-06-18 14:24:54.392 | INFO     | core.telegram.client_worker:run:65 - Telegram客户端工作线程开始运行
2025-06-18 14:24:54.393 | INFO     | app.services.account_service:__init__:42 - 账户服务初始化
2025-06-18 14:24:54.394 | INFO     | core.proxy.proxy_task_manager:run:284 - 代理任务管理器线程启动
2025-06-18 14:24:54.394 | INFO     | app.controllers.account_controller:__init__:81 - 账户控制器初始化
2025-06-18 14:24:54.394 | INFO     | app.services.monitor_service:__init__:39 - 初始化监控任务服务
2025-06-18 14:24:54.395 | INFO     | core.monitor.filter:__init__:26 - 初始化消息过滤器
2025-06-18 14:24:54.396 | INFO     | core.telegram.client_manager:__init__:66 - Telegram客户端管理器初始化
2025-06-18 14:24:54.396 | INFO     | core.monitor.extractor:__init__:24 - 初始化关键词提取器
2025-06-18 14:24:54.397 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-06-18 14:24:54.396 | INFO     | core.telegram.user_manager:__init__:30 - Telegram用户管理器初始化
2025-06-18 14:24:54.398 | INFO     | core.monitor.notifier:__init__:90 - 初始化通知管理器
2025-06-18 14:24:54.399 | INFO     | core.telegram.group_manager:__init__:36 - Telegram群组管理器初始化
2025-06-18 14:24:54.399 | INFO     | app.controllers.telegram_monitor_controller:__init__:64 - 初始化 Telegram 监控控制器
2025-06-18 14:24:54.400 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-06-18 14:24:54.400 | DEBUG    | app.controllers.telegram_monitor_controller:__init__:82 - 控制器初始化完成
2025-06-18 14:24:54.400 | INFO     | core.telegram.monitor_manager:__init__:26 - Telegram监控管理器初始化
2025-06-18 14:24:54.402 | INFO     | core.telegram.telegram_service:__init__:39 - Telegram服务初始化完成
2025-06-18 14:24:54.597 | INFO     | ui.views.monitor_view:__init__:48 - 初始化监控视图
2025-06-18 14:24:54.597 | DEBUG    | ui.views.monitor_view:_connect_signals:80 - 连接控制器信号
2025-06-18 14:24:54.782 | INFO     | ui.views.proxy_view:__init__:96 - 初始化视图，加载第一页代理数据
2025-06-18 14:24:55.019 | INFO     | __main__:on_auth_completed:97 - 主窗口已启动
2025-06-18 14:24:55.081 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:88 - 尝试连接监听日志信号...
2025-06-18 14:24:55.082 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:90 - 监听日志信号连接成功
2025-06-18 14:24:55.083 | INFO     | core.auth.api_service:verify_vip:425 - 会员验证接口调用
2025-06-18 14:24:55.084 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-18 14:24:55.087 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-18 14:24:55.091 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:145 - 加载监控任务列表 (搜索关键词: '')
2025-06-18 14:24:55.091 | DEBUG    | ui.views.monitor_view:_on_loading_started:620 - 加载中: 正在加载任务列表...
2025-06-18 14:24:55.091 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-18 14:24:55.098 | INFO     | app.services.proxy_service:refresh_proxies:712 - 刷新代理列表: offset=0, limit=20
2025-06-18 14:24:55.098 | INFO     | core.proxy.proxy_task_manager:submit_task:172 - 提交耗时任务到子线程: refresh_proxies_0_20
2025-06-18 14:24:55.099 | DEBUG    | app.services.proxy_service:get_proxies:500 - 获取代理列表: offset=0, limit=20
2025-06-18 14:24:55.099 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-18 14:24:55.099 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-18 14:24:55.103 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-18 14:24:55.125 | INFO     | app.controllers.invite_controller:get_invite_tasks:38 - 获取邀请任务列表, status=None
2025-06-18 14:24:55.127 | INFO     | app.services.invite_service:get_invite_tasks:121 - 获取邀请任务列表, status=None
2025-06-18 14:24:55.127 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-18 14:24:55.128 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-18 14:24:55.129 | INFO     | app.services.invite_service:task_initialize:39 - 初始化邀请服务，重置运行中的任务为暂停状态
2025-06-18 14:24:55.130 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-18 14:24:55.131 | INFO     | core.proxy.proxy_task_manager:task_wrapper:199 - 任务 refresh_proxies_0_20 完成
2025-06-18 14:24:55.256 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-18 14:24:55.260 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-18 14:24:55.264 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-18 14:24:55.265 | INFO     | app.services.invite_service:task_initialize:48 - 成功重置 0 个任务状态
2025-06-18 14:24:55.266 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-18 14:24:55.268 | DEBUG    | ui.views.monitor_view:_refresh_task_list:175 - 刷新任务列表，共2个任务
2025-06-18 14:24:55.274 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-18 14:24:55.280 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-18 14:24:55.286 | INFO     | app.services.account_service:get_all_groups:96 - 获取所有账户分组成功, 共 2 个
2025-06-18 14:24:55.287 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-18 14:24:55.289 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-18 14:24:55.290 | DEBUG    | ui.views.account_view:_on_groups_loaded:442 - 分组加载完成: 2个分组
2025-06-18 14:24:55.303 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-18 14:24:55.364 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-18 14:24:55.369 | INFO     | app.services.account_service:get_all_accounts:289 - 获取所有账户成功, 共 2 个
2025-06-18 14:24:55.370 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-18 14:24:55.371 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-06-18 14:24:55.385 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-18 14:24:55.386 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-18 14:24:55.387 | DEBUG    | ui.views.account_view:_on_accounts_loaded:575 - 账户加载完成: 2个账户
2025-06-18 14:24:55.410 | DEBUG    | ui.views.account_view:_update_group_counts:184 - 分组计数已更新: {2: 1, 1: 1}
2025-06-18 14:24:55.415 | INFO     | ui.views.account_view:_auto_login_accounts:630 - 开始调用控制器进行 2 个账户的自动登录。
2025-06-18 14:24:55.415 | INFO     | app.services.account_service:batch_auto_login:1153 - 服务层：开始批量自动登录 2 个账户, 最大并发: 5
2025-06-18 14:24:55.416 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-18 14:24:55.421 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=True
2025-06-18 14:24:55.423 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=True
2025-06-18 14:24:55.425 | INFO     | ui.views.proxy_view:_load_initial_data:1044 - 初始数据加载完成，共 1 个代理
2025-06-18 14:24:55.426 | INFO     | app.controllers.telegram_monitor_controller:_load_total_users_count_internal:191 - 成功加载总体统计数据: 总用户数 1845, 今日采集 0, 日均采集 264, 运行天数 7
2025-06-18 14:24:55.427 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:156 - 成功加载 2 个任务
2025-06-18 14:24:55.427 | DEBUG    | ui.views.monitor_view:_on_loading_finished:625 - 加载完成
2025-06-18 14:24:55.432 | INFO     | app.services.account_service:batch_auto_login:1192 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 1********:1080
2025-06-18 14:24:55.433 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-18 14:24:55.434 | INFO     | app.services.account_service:get_all_accounts:289 - 获取所有账户成功, 共 2 个
2025-06-18 14:24:55.434 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-18 14:24:55.437 | DEBUG    | ui.views.account_view:_update_group_counts:184 - 分组计数已更新: {2: 1, 1: 1}
2025-06-18 14:24:55.437 | DEBUG    | ui.views.account_view:_init_cache:151 - 账户缓存初始化完成，共 2 个账户
2025-06-18 14:24:55.441 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-18 14:24:55.443 | DEBUG    | ui.views.account_view:_on_accounts_loaded:575 - 账户加载完成: 2个账户
2025-06-18 14:24:55.467 | DEBUG    | ui.views.account_view:_update_group_counts:184 - 分组计数已更新: {2: 1, 1: 1}
2025-06-18 14:24:55.476 | INFO     | app.services.account_service:batch_auto_login:1192 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 1********:1080
2025-06-18 14:24:55.476 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-18 14:24:55.477 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-18 14:24:55.478 | INFO     | app.services.account_service:batch_auto_login:1262 - 服务层：准备调用核心层处理 2 个账户的自动登录。
2025-06-18 14:24:55.479 | INFO     | app.services.account_service:batch_auto_login:1272 - 服务层：设置核心层任务超时为 120 秒。
2025-06-18 14:24:55.480 | INFO     | core.telegram.client_manager:batch_auto_login:1211 - 开始批量自动登录, 共 2 个账户, 最大并发 5
2025-06-18 14:24:55.481 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:254 - 创建客户端: h:\PyProject\TeleTest\APPDATA\sessions\+***********.session, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '1********', 'port': 1080}
2025-06-18 14:24:55.482 | DEBUG    | core.telegram.client_manager:get_proxy:91 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '1********', 'port': 1080}
2025-06-18 14:24:55.483 | DEBUG    | core.telegram.client_manager:get_proxy:138 - get_proxy: 使用 ip_pool 代理 1********:1080，转换结果: (<ProxyType.SOCKS5: 2>, '1********', 1080)
2025-06-18 14:24:55.489 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:254 - 创建客户端: h:\PyProject\TeleTest\APPDATA\sessions\+***********.session, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '1********', 'port': 1080}
2025-06-18 14:24:55.489 | DEBUG    | core.telegram.client_manager:get_proxy:91 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '1********', 'port': 1080}
2025-06-18 14:24:55.490 | DEBUG    | core.telegram.client_manager:get_proxy:138 - get_proxy: 使用 ip_pool 代理 1********:1080，转换结果: (<ProxyType.SOCKS5: 2>, '1********', 1080)
2025-06-18 14:24:55.510 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-18 14:24:55.515 | WARNING  | core.auth.api_service:verify_vip:441 - 会员验证失败: 验证失败
2025-06-18 14:24:55.814 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-18 14:24:57.874 | INFO     | core.telegram.client_manager:_connect_client:202 - 连接成功!
2025-06-18 14:24:58.078 | INFO     | core.telegram.client_manager:_connect_client:202 - 连接成功!
2025-06-18 14:24:58.822 | INFO     | core.telegram.client_manager:_verify_client_authorization:1186 - 账户已授权: +***********
2025-06-18 14:24:58.860 | INFO     | core.telegram.client_manager:_verify_client_authorization:1186 - 账户已授权: +***********
2025-06-18 14:25:00.863 | INFO     | core.telegram.client_manager:batch_auto_login:1360 - 批量登录完成: 总计 2, 成功 2, 失败 0
2025-06-18 14:25:01.543 | INFO     | app.services.account_service:batch_auto_login:1293 - 服务层：核心层登录完成: 成功 2, 失败 0 / 2
2025-06-18 14:25:54.354 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-18 14:26:54.358 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-18 14:27:54.354 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-18 14:28:54.356 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-18 14:29:54.351 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-18 14:30:54.358 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-18 14:31:28.626 | DEBUG    | ui.views.monitor_view:_on_task_item_clicked:258 - 任务项点击: 2
2025-06-18 14:31:28.628 | INFO     | app.controllers.telegram_monitor_controller:select_task:237 - 选择任务: 2
2025-06-18 14:31:28.628 | DEBUG    | ui.views.monitor_view:_on_task_selection_changed:281 - 任务选择变更: 2
2025-06-18 14:31:28.629 | DEBUG    | ui.views.monitor_view:_on_loading_started:620 - 加载中: 正在加载任务详情...
2025-06-18 14:31:28.629 | INFO     | app.controllers.telegram_monitor_controller:load_task_details:261 - 加载任务详情: ID 2
2025-06-18 14:31:28.629 | INFO     | app.services.monitor_service:get_task_detail:1018 - 获取任务详情: ID 2
2025-06-18 14:31:28.629 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-18 14:31:28.643 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-18 14:31:28.645 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-18 14:31:28.654 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-18 14:31:28.655 | DEBUG    | app.controllers.telegram_monitor_controller:load_task_details:271 - 获取到任务 2 的统计数据: {'total_users': 24, 'today_users': 0, 'avg_daily': 3, 'running_days': 7}
2025-06-18 14:31:28.656 | DEBUG    | ui.views.monitor_view:_on_task_details_loaded:309 - 任务详情加载完成: 2
2025-06-18 14:31:28.658 | DEBUG    | ui.views.monitor_view:_on_task_details_loaded:312 - 任务详情数据: {'created_at': '2025-06-11 10:02:49', 'description': '', 'id': '2', 'ignore_keywords': [], 'ignore_nicknames_rules': [], 'is_active': True, 'is_running': False, 'keywords': ['开户', '开通', '登录', '注册'], 'last_error': "所有账户监控设置均失败: object tuple can't be used in 'await' expression, object tuple can't be used in 'await' expression", 'monitor_messages': True, 'monitor_new_users': False, 'monitored_chats': [{'account_phone': '***********', 'chat_id': '**********', 'chat_title': '泰国华人交友聊天总群', 'id': 100}, {'account_phone': '***********', 'chat_id': '**********', 'chat_title': '赚钱项目灰产社区', 'id': 101}, {'account_phone': '***********', 'chat_id': '**********', 'chat_title': '深圳集团 - 藏楼1.0', 'id': 102}, {'account_phone': '***********', 'chat_id': '**********', 'chat_title': '干到你没钱16', 'id': 103}, {'account_phone': '***********', 'chat_id': '**********', 'chat_title': '东南亚博彩代理聊天群', 'id': 104}, {'account_phone': '***********', 'chat_id': '**********', 'chat_title': '柬埔寨泰国电子体育群', 'id': 105}, {'account_phone': '***********', 'chat_id': '**********', 'chat_title': '索引群聊❤️搜索王', 'id': 106}, {'account_phone': '***********', 'chat_id': '**********', 'chat_title': 'wiegnwi', 'id': 107}, {'account_phone': '***********', 'chat_id': '**********', 'chat_title': '区块节点NodeMining-Pool', 'id': 108}, {'account_phone': '***********', 'chat_id': '**********', 'chat_title': '区块节点NodeMining-Pool', 'id': 109}, {'account_phone': '***********', 'chat_id': '**********', 'chat_title': '🔥入金话术馆', 'id': 110}, {'account_phone': '***********', 'chat_id': '**********', 'chat_title': '广州会所资源_(部分预览)', 'id': 111}, {'account_phone': '***********', 'chat_id': '**********', 'chat_title': '内涵段子🔥搞笑视频', 'id': 112}, {'account_phone': '***********', 'chat_id': '**********', 'chat_title': '国外 话术 素材总部', 'id': 113}, {'account_phone': '***********', 'chat_id': '**********', 'chat_title': '精聊话术-人设套图大全', 'id': 114}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '🅰️中文搜片搜索👙自慰🩸抠逼🔍乱伦🗣口交', 'id': 115}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '软件开发🪸搭建网站🪸网站开发', 'id': 116}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '迷🦊 吃瓜 完整版破案‼️', 'id': 117}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '【官方】币安矿池置换中文群🇨🇳👤', 'id': 118}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '🈲 高中 学生资源库（加密）🎀', 'id': 119}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '🍒 萝莉屋18岁 资源群🦄', 'id': 120}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '【官方】币安矿池置换中文群🇨🇳', 'id': 121}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '【官方】币安BNB挖矿中文总社区🇨🇳❤️', 'id': 122}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '免费情色视频搜索🚀', 'id': 123}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '香港服务器交流群', 'id': 124}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': 'wiegnwi', 'id': 125}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '💡站长群 HostCLi.com 宝塔纯净版', 'id': 126}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': 'Fragment Universal Drop', 'id': 127}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '柯基币-礦池-官方社区', 'id': 128}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '中文算力MinerDog pool', 'id': 129}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '云泽社区', 'id': 130}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '国际联盟—海外实卡接码/邮寄公开群【支持担保】', 'id': 131}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '仙楼集团·资源发布站 @xianlou', 'id': 132}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '路易频道引流推广工作群', 'id': 133}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '泡妞笔记', 'id': 134}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '神奇宝贝 @so588', 'id': 135}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': 'TGbox助手|群发|拉人|引流|群发助手', 'id': 136}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '比特币购买，比特币行情，比特币怎么买', 'id': 137}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '泰国华人交友聊天总群', 'id': 157}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '索引群聊❤️搜索王', 'id': 158}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '东南亚博彩代理聊天群', 'id': 159}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '深圳集团 - 藏楼1.0', 'id': 160}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '柬埔寨泰国电子体育群', 'id': 161}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '赚钱项目灰产社区', 'id': 162}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '干到你没钱16', 'id': 163}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': 'Token FRAG Allocation', 'id': 164}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '广州会所资源_(部分预览)', 'id': 165}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '区块节点NodeMining-Pool', 'id': 166}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '区块节点NodeMining-Pool', 'id': 167}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '🔥入金话术馆', 'id': 168}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '国外 话术 素材总部', 'id': 169}, {'account_phone': '+***********', 'chat_id': '2607225038', 'chat_title': '神州联盟🇨🇳 海外顶尖话术', 'id': 170}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '精聊话术-人设套图大全', 'id': 171}, {'account_phone': '+***********', 'chat_id': '2097277980', 'chat_title': '狗推瑟瑟👠嗨吹群', 'id': 172}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': 'wiegnwi', 'id': 173}, {'account_phone': '+***********', 'chat_id': '2216757344', 'chat_title': '干到你没钱20', 'id': 174}, {'account_phone': '+***********', 'chat_id': '2406620234', 'chat_title': '🟥官方直开跑分🟦日赚过万项目🟨收款码代收🟧', 'id': 175}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '💡站长群 HostCLi.com 宝塔纯净版', 'id': 176}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '香港服务器交流群', 'id': 177}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '🈲 高中 学生资源库（加密）🎀', 'id': 178}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '🍒 萝莉屋18岁 资源群🦄', 'id': 179}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '🅰️中文搜片搜索👙自慰🩸抠逼🔍乱伦🗣口交', 'id': 180}, {'account_phone': '+***********', 'chat_id': '1787116124', 'chat_title': '币安BNB矿池官方社区🅥', 'id': 181}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '【官方】币安矿池置换中文群🇨🇳👤', 'id': 182}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '【官方】币安BNB挖矿中文总社区🇨🇳❤️❤️', 'id': 183}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '免费情色视频搜索🚀', 'id': 184}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '迷🦊 吃瓜 完整版破案‼️', 'id': 185}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '国际联盟—海外实卡接码/邮寄公开群【支持担保】', 'id': 186}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '【官方】币安矿池置换中文群🇨🇳❤️❤️', 'id': 187}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': 'FRAG Rewards', 'id': 188}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '柯基币-礦池-官方社区', 'id': 189}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '中文算力MinerDog pool', 'id': 190}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '云泽社区', 'id': 191}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '仙楼集团·资源发布站 @xianlou', 'id': 192}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '软件开发🪸搭建网站🪸网站开发', 'id': 193}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '路易频道引流推广工作群【结算时间19.00】', 'id': 194}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': 'wiegnwi', 'id': 195}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '泡妞笔记', 'id': 196}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': 'TGbox助手|群发|拉人|引流|群发助手', 'id': 197}, {'account_phone': '+***********', 'chat_id': '**********', 'chat_title': '比特币购买，比特币行情，比特币怎么买', 'id': 198}], 'name': '开户监听', 'stats': {'avg_daily': 3, 'running_days': 7, 'today_users': 0, 'total_users': 24}, 'updated_at': '2025-06-11 10:02:49'}
2025-06-18 14:31:28.666 | DEBUG    | ui.views.monitor_view:_on_task_details_loaded:316 - 统计数据: {'avg_daily': 3, 'running_days': 7, 'today_users': 0, 'total_users': 24}
2025-06-18 14:31:28.669 | DEBUG    | ui.views.monitor_view:_on_loading_started:620 - 加载中: 正在加载第 1 页数据...
2025-06-18 14:31:28.669 | INFO     | app.controllers.telegram_monitor_controller:load_task_users:396 - 加载任务用户数据: ID 2, 页码 1, 每页 10 条
2025-06-18 14:31:28.670 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-18 14:31:28.690 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-18 14:31:28.691 | INFO     | ui.views.monitor_view:_update_users_table:342 - 开始更新用户表格，收到10条数据
2025-06-18 14:31:28.692 | DEBUG    | ui.views.monitor_view:_update_users_table:343 - 分页信息：{'current_page': 1, 'page_size': 10, 'total_items': 24, 'total_pages': 3}
2025-06-18 14:31:28.692 | INFO     | ui.views.monitor_view:_update_users_table:360 - 示例数据[0]: {'join_date': '2025-06-11 04:13:04', 'keyword': '登录', 'nickname': '小高高 日 Phone login Tn web login SL fer', 'task_type': 'text', 'uid': '8010084637', 'username': ''}
2025-06-18 14:31:28.693 | INFO     | ui.views.monitor_view:_update_users_table:360 - 示例数据[1]: {'join_date': '2025-06-11 04:06:39', 'keyword': '注册', 'nickname': '中文索引 None', 'task_type': 'text', 'uid': '5064505565', 'username': ''}
2025-06-18 14:31:28.693 | INFO     | ui.views.monitor_view:_update_users_table:360 - 示例数据[2]: {'join_date': '2025-06-11 03:55:12', 'keyword': '注册', 'nickname': '币安矿池质押中文播报📣 None', 'task_type': 'text', 'uid': '8097600256', 'username': 'Binance_BNBP00L_Bot'}
2025-06-18 14:31:28.694 | INFO     | ui.views.monitor_view:_update_users_table:364 - 设置表格行数: 10
2025-06-18 14:31:28.709 | INFO     | ui.views.monitor_view:_update_users_table:388 - 用户表格更新完成
2025-06-18 14:31:28.709 | INFO     | app.controllers.telegram_monitor_controller:load_task_users:423 - 成功加载第 1 页用户数据，共 10 条
2025-06-18 14:31:28.710 | DEBUG    | ui.views.monitor_view:_on_loading_finished:625 - 加载完成
2025-06-18 14:31:28.710 | INFO     | app.controllers.telegram_monitor_controller:load_task_details:287 - 成功加载任务详情: 开户监听
2025-06-18 14:31:28.710 | DEBUG    | ui.views.monitor_view:_on_loading_finished:625 - 加载完成
2025-06-18 14:31:35.643 | INFO     | ui.main_window:closeEvent:346 - MainWindow: 接收到关闭事件
2025-06-18 14:31:35.657 | INFO     | ui.main_window:_cleanup_before_quit:238 - MainWindow: 执行清理资源...
2025-06-18 14:31:35.658 | INFO     | core.telegram.client_worker:stop:108 - 正在停止Telegram客户端工作线程
2025-06-18 14:31:35.668 | INFO     | core.telegram.client_manager:cleanup_async:1131 - 开始清理资源并断开所有连接
2025-06-18 14:31:35.669 | INFO     | core.telegram.client_manager:disconnect_all_clients:716 - 正在断开所有客户端连接
2025-06-18 14:31:35.670 | INFO     | core.telegram.client_manager:_safe_disconnect:737 - 正在断开客户端连接: +***********
2025-06-18 14:31:35.671 | INFO     | core.telegram.client_manager:_safe_disconnect:737 - 正在断开客户端连接: +***********
2025-06-18 14:31:35.685 | INFO     | core.telegram.client_manager:_safe_disconnect:741 - 断开客户端连接成功: +***********
2025-06-18 14:31:35.686 | INFO     | core.telegram.client_manager:_safe_disconnect:741 - 断开客户端连接成功: +***********
2025-06-18 14:31:35.686 | INFO     | core.telegram.client_manager:cleanup_async:1150 - 资源清理完成
2025-06-18 14:31:36.173 | INFO     | core.telegram.client_manager:cleanup_async:1131 - 开始清理资源并断开所有连接
2025-06-18 14:31:36.173 | INFO     | core.telegram.client_manager:disconnect_all_clients:716 - 正在断开所有客户端连接
2025-06-18 14:31:36.173 | INFO     | core.telegram.client_manager:cleanup_async:1150 - 资源清理完成
2025-06-18 14:31:36.674 | INFO     | core.telegram.client_worker:run:97 - Telegram客户端工作线程已退出
2025-06-18 14:31:36.675 | INFO     | core.telegram.client_worker:stop:122 - Telegram客户端工作线程已停止
2025-06-18 14:31:36.675 | INFO     | ui.main_window:_cleanup_before_quit:247 - TelegramClientWorker 已停止。
2025-06-18 14:31:36.675 | INFO     | ui.main_window:_cleanup_before_quit:251 - MainWindow 清理完成
2025-06-18 14:31:36.685 | INFO     | __main__:main:111 - 应用程序已正常退出
2025-06-18 16:09:09.182 | INFO     | utils.logger:init_logger:98 - 日志系统初始化完成，默认级别: DEBUG，日志目录: h:\PyProject\TeleTest\APPDATA\logs
2025-06-18 16:09:10.874 | INFO     | data.database:init_database:31 - 初始化数据库连接: h:\PyProject\TeleTest\APPDATA\database\telegram_manager.db
2025-06-18 16:09:10.890 | DEBUG    | data.database:init_database:58 - 创建数据库表
2025-06-18 16:09:10.901 | INFO     | data.database:init_database:61 - 数据库初始化完成
2025-06-18 16:09:11.842 | DEBUG    | app.controllers.auth_controller:initialize_software:43 - 控制器: 初始化软件 app_id=1000, version=1.2.7
2025-06-18 16:09:11.843 | DEBUG    | core.auth.api_service:init_software:55 - 发送初始化请求: https://server.xile188.com/api/user/1000/windowns10/1.2.7/ini, params={}
2025-06-18 16:09:12.336 | DEBUG    | core.auth.api_service:init_software:66 - 初始化数据解密成功: {'new_content': '- 新增功能\n  - 1.新增图片上传功能\n  - 2.新增软件文档\n  - 3.新增软件自动更新，后续更新会自动检测版本更新，无需再下载更新包\n  - 4.新增设置账户信息及发信控制\n- 修复bug\n  - 删除监听任务时，不删除对应任务采集到的数据。\n  - 监听设置群无法发送到目标群\n  - 导出监听数据时应当导出单个任务而非所有数据\n- 优化记录\n  - 优化发信规则，直接调取符合要求的账户发送，无账户时才需要等待延时。\n  - 完善软件设置', 'new_url': None, 'notice': {'id': '2', 'content': '公告内容测试3', 'time': '**********'}}
2025-06-18 16:09:46.299 | INFO     | app.controllers.auth_controller:login:61 - 控制器: 用户登录 account=demo01
2025-06-18 16:09:49.489 | INFO     | core.auth.api_service:login:127 - 用户登录: account=demo01
2025-06-18 16:09:49.798 | INFO     | core.auth.api_service:get_user_info:347 - 获取用户信息
2025-06-18 16:09:50.059 | INFO     | core.auth.api_service:get_user_info:375 - 获取用户信息成功
2025-06-18 16:09:50.066 | INFO     | __main__:on_auth_completed:93 - 用户登录成功，正在启动主窗口...
2025-06-18 16:09:50.092 | INFO     | core.telegram.client_worker:__init__:61 - Telegram客户端工作线程初始化
2025-06-18 16:09:50.093 | INFO     | ui.main_window:_initialize_core_components:82 - MainWindow: 初始化核心组件...
2025-06-18 16:09:50.094 | INFO     | core.telegram.client_worker:run:65 - Telegram客户端工作线程开始运行
2025-06-18 16:09:50.095 | INFO     | app.services.account_service:__init__:42 - 账户服务初始化
2025-06-18 16:09:50.095 | INFO     | core.proxy.proxy_task_manager:run:284 - 代理任务管理器线程启动
2025-06-18 16:09:50.096 | INFO     | app.controllers.account_controller:__init__:81 - 账户控制器初始化
2025-06-18 16:09:50.097 | INFO     | app.services.monitor_service:__init__:39 - 初始化监控任务服务
2025-06-18 16:09:50.097 | INFO     | core.telegram.client_manager:__init__:66 - Telegram客户端管理器初始化
2025-06-18 16:09:50.098 | INFO     | core.monitor.filter:__init__:26 - 初始化消息过滤器
2025-06-18 16:09:50.098 | INFO     | core.telegram.user_manager:__init__:30 - Telegram用户管理器初始化
2025-06-18 16:09:50.099 | INFO     | core.monitor.extractor:__init__:24 - 初始化关键词提取器
2025-06-18 16:09:50.099 | INFO     | core.telegram.group_manager:__init__:36 - Telegram群组管理器初始化
2025-06-18 16:09:50.100 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-06-18 16:09:50.100 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-06-18 16:09:50.101 | INFO     | core.monitor.notifier:__init__:90 - 初始化通知管理器
2025-06-18 16:09:50.101 | INFO     | core.telegram.monitor_manager:__init__:26 - Telegram监控管理器初始化
2025-06-18 16:09:50.101 | INFO     | app.controllers.telegram_monitor_controller:__init__:64 - 初始化 Telegram 监控控制器
2025-06-18 16:09:50.102 | INFO     | core.telegram.telegram_service:__init__:39 - Telegram服务初始化完成
2025-06-18 16:09:50.102 | DEBUG    | app.controllers.telegram_monitor_controller:__init__:82 - 控制器初始化完成
2025-06-18 16:09:50.321 | INFO     | ui.views.monitor_view:__init__:48 - 初始化监控视图
2025-06-18 16:09:50.321 | DEBUG    | ui.views.monitor_view:_connect_signals:80 - 连接控制器信号
2025-06-18 16:09:50.524 | INFO     | ui.views.proxy_view:__init__:96 - 初始化视图，加载第一页代理数据
2025-06-18 16:09:50.812 | INFO     | __main__:on_auth_completed:97 - 主窗口已启动
2025-06-18 16:09:50.877 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:88 - 尝试连接监听日志信号...
2025-06-18 16:09:50.878 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:90 - 监听日志信号连接成功
2025-06-18 16:09:50.879 | INFO     | core.auth.api_service:verify_vip:425 - 会员验证接口调用
2025-06-18 16:09:50.880 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-18 16:09:50.883 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-18 16:09:50.887 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:145 - 加载监控任务列表 (搜索关键词: '')
2025-06-18 16:09:50.887 | DEBUG    | ui.views.monitor_view:_on_loading_started:620 - 加载中: 正在加载任务列表...
2025-06-18 16:09:50.887 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-18 16:09:50.893 | INFO     | app.services.proxy_service:refresh_proxies:712 - 刷新代理列表: offset=0, limit=20
2025-06-18 16:09:50.894 | INFO     | core.proxy.proxy_task_manager:submit_task:172 - 提交耗时任务到子线程: refresh_proxies_0_20
2025-06-18 16:09:50.894 | DEBUG    | app.services.proxy_service:get_proxies:500 - 获取代理列表: offset=0, limit=20
2025-06-18 16:09:50.894 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-18 16:09:50.895 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-18 16:09:50.900 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-18 16:09:50.928 | INFO     | app.controllers.invite_controller:get_invite_tasks:38 - 获取邀请任务列表, status=None
2025-06-18 16:09:50.932 | INFO     | app.services.invite_service:get_invite_tasks:121 - 获取邀请任务列表, status=None
2025-06-18 16:09:50.933 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-18 16:09:50.934 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-18 16:09:50.936 | INFO     | app.services.invite_service:task_initialize:39 - 初始化邀请服务，重置运行中的任务为暂停状态
2025-06-18 16:09:50.937 | INFO     | core.proxy.proxy_task_manager:task_wrapper:199 - 任务 refresh_proxies_0_20 完成
2025-06-18 16:09:50.937 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-18 16:09:51.047 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-18 16:09:51.047 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-18 16:09:51.053 | DEBUG    | ui.views.monitor_view:_refresh_task_list:175 - 刷新任务列表，共2个任务
2025-06-18 16:09:51.059 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-18 16:09:51.061 | INFO     | app.services.invite_service:task_initialize:48 - 成功重置 0 个任务状态
2025-06-18 16:09:51.061 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-18 16:09:51.070 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-18 16:09:51.075 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-18 16:09:51.076 | INFO     | app.services.account_service:get_all_groups:96 - 获取所有账户分组成功, 共 2 个
2025-06-18 16:09:51.077 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-18 16:09:51.078 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-18 16:09:51.082 | DEBUG    | ui.views.account_view:_on_groups_loaded:442 - 分组加载完成: 2个分组
2025-06-18 16:09:51.097 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-18 16:09:51.174 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-18 16:09:51.175 | INFO     | app.services.account_service:get_all_accounts:289 - 获取所有账户成功, 共 2 个
2025-06-18 16:09:51.175 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-18 16:09:51.176 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-18 16:09:51.179 | INFO     | app.controllers.telegram_monitor_controller:_load_total_users_count_internal:191 - 成功加载总体统计数据: 总用户数 1845, 今日采集 0, 日均采集 264, 运行天数 7
2025-06-18 16:09:51.180 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:156 - 成功加载 2 个任务
2025-06-18 16:09:51.181 | DEBUG    | ui.views.monitor_view:_on_loading_finished:625 - 加载完成
2025-06-18 16:09:51.182 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-18 16:09:51.184 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-06-18 16:09:51.188 | DEBUG    | ui.views.account_view:_on_accounts_loaded:575 - 账户加载完成: 2个账户
2025-06-18 16:09:51.213 | DEBUG    | ui.views.account_view:_update_group_counts:184 - 分组计数已更新: {2: 1, 1: 1}
2025-06-18 16:09:51.218 | INFO     | ui.views.account_view:_auto_login_accounts:630 - 开始调用控制器进行 2 个账户的自动登录。
2025-06-18 16:09:51.219 | INFO     | app.services.account_service:batch_auto_login:1153 - 服务层：开始批量自动登录 2 个账户, 最大并发: 5
2025-06-18 16:09:51.219 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-18 16:09:51.238 | INFO     | app.services.account_service:get_all_accounts:289 - 获取所有账户成功, 共 2 个
2025-06-18 16:09:51.239 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-18 16:09:51.240 | INFO     | app.services.account_service:batch_auto_login:1192 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 1********:1080
2025-06-18 16:09:51.241 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-18 16:09:51.243 | DEBUG    | ui.views.account_view:_update_group_counts:184 - 分组计数已更新: {2: 1, 1: 1}
2025-06-18 16:09:51.244 | DEBUG    | ui.views.account_view:_init_cache:151 - 账户缓存初始化完成，共 2 个账户
2025-06-18 16:09:51.251 | DEBUG    | ui.views.account_view:_on_accounts_loaded:575 - 账户加载完成: 2个账户
2025-06-18 16:09:51.279 | DEBUG    | ui.views.account_view:_update_group_counts:184 - 分组计数已更新: {2: 1, 1: 1}
2025-06-18 16:09:51.284 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-18 16:09:51.289 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=True
2025-06-18 16:09:51.292 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=True
2025-06-18 16:09:51.294 | INFO     | ui.views.proxy_view:_load_initial_data:1044 - 初始数据加载完成，共 1 个代理
2025-06-18 16:09:51.295 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-18 16:09:51.296 | WARNING  | core.auth.api_service:verify_vip:441 - 会员验证失败: 验证失败
2025-06-18 16:09:51.554 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-18 16:09:51.555 | INFO     | app.services.account_service:batch_auto_login:1192 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 1********:1080
2025-06-18 16:09:51.556 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-18 16:09:51.600 | INFO     | app.services.account_service:batch_auto_login:1262 - 服务层：准备调用核心层处理 2 个账户的自动登录。
2025-06-18 16:09:51.601 | INFO     | app.services.account_service:batch_auto_login:1272 - 服务层：设置核心层任务超时为 120 秒。
2025-06-18 16:09:51.601 | INFO     | core.telegram.client_manager:batch_auto_login:1211 - 开始批量自动登录, 共 2 个账户, 最大并发 5
2025-06-18 16:09:51.601 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:254 - 创建客户端: h:\PyProject\TeleTest\APPDATA\sessions\+***********.session, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '1********', 'port': 1080}
2025-06-18 16:09:51.603 | DEBUG    | core.telegram.client_manager:get_proxy:91 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '1********', 'port': 1080}
2025-06-18 16:09:51.604 | DEBUG    | core.telegram.client_manager:get_proxy:138 - get_proxy: 使用 ip_pool 代理 1********:1080，转换结果: (<ProxyType.SOCKS5: 2>, '1********', 1080)
2025-06-18 16:09:51.607 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:254 - 创建客户端: h:\PyProject\TeleTest\APPDATA\sessions\+***********.session, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '1********', 'port': 1080}
2025-06-18 16:09:51.608 | DEBUG    | core.telegram.client_manager:get_proxy:91 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '1********', 'port': 1080}
2025-06-18 16:09:51.609 | DEBUG    | core.telegram.client_manager:get_proxy:138 - get_proxy: 使用 ip_pool 代理 1********:1080，转换结果: (<ProxyType.SOCKS5: 2>, '1********', 1080)
2025-06-18 16:09:51.621 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-18 16:09:54.918 | INFO     | core.telegram.client_manager:_connect_client:202 - 连接成功!
2025-06-18 16:09:55.048 | INFO     | core.telegram.client_manager:_connect_client:202 - 连接成功!
2025-06-18 16:09:55.988 | INFO     | core.telegram.client_manager:_verify_client_authorization:1186 - 账户已授权: +***********
2025-06-18 16:09:56.006 | INFO     | core.telegram.client_manager:_verify_client_authorization:1186 - 账户已授权: +***********
2025-06-18 16:09:58.019 | INFO     | core.telegram.client_manager:batch_auto_login:1360 - 批量登录完成: 总计 2, 成功 2, 失败 0
2025-06-18 16:09:58.630 | INFO     | app.services.account_service:batch_auto_login:1293 - 服务层：核心层登录完成: 成功 2, 失败 0 / 2
2025-06-18 16:10:50.056 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-18 16:11:34.153 | INFO     | ui.main_window:closeEvent:346 - MainWindow: 接收到关闭事件
2025-06-18 16:11:34.167 | INFO     | ui.main_window:_cleanup_before_quit:238 - MainWindow: 执行清理资源...
2025-06-18 16:11:34.168 | INFO     | core.telegram.client_worker:stop:108 - 正在停止Telegram客户端工作线程
2025-06-18 16:11:34.176 | INFO     | core.telegram.client_manager:cleanup_async:1131 - 开始清理资源并断开所有连接
2025-06-18 16:11:34.178 | INFO     | core.telegram.client_manager:disconnect_all_clients:716 - 正在断开所有客户端连接
2025-06-18 16:11:34.179 | INFO     | core.telegram.client_manager:_safe_disconnect:737 - 正在断开客户端连接: +***********
2025-06-18 16:11:34.180 | INFO     | core.telegram.client_manager:_safe_disconnect:737 - 正在断开客户端连接: +***********
2025-06-18 16:11:34.191 | INFO     | core.telegram.client_manager:_safe_disconnect:741 - 断开客户端连接成功: +***********
2025-06-18 16:11:34.191 | INFO     | core.telegram.client_manager:_safe_disconnect:741 - 断开客户端连接成功: +***********
2025-06-18 16:11:34.192 | INFO     | core.telegram.client_manager:cleanup_async:1150 - 资源清理完成
2025-06-18 16:11:34.677 | INFO     | core.telegram.client_manager:cleanup_async:1131 - 开始清理资源并断开所有连接
2025-06-18 16:11:34.677 | INFO     | core.telegram.client_manager:disconnect_all_clients:716 - 正在断开所有客户端连接
2025-06-18 16:11:34.678 | INFO     | core.telegram.client_manager:cleanup_async:1150 - 资源清理完成
2025-06-18 16:11:35.179 | INFO     | core.telegram.client_worker:run:97 - Telegram客户端工作线程已退出
2025-06-18 16:11:35.180 | INFO     | core.telegram.client_worker:stop:122 - Telegram客户端工作线程已停止
2025-06-18 16:11:35.180 | INFO     | ui.main_window:_cleanup_before_quit:247 - TelegramClientWorker 已停止。
2025-06-18 16:11:35.181 | INFO     | ui.main_window:_cleanup_before_quit:251 - MainWindow 清理完成
2025-06-18 16:11:35.193 | INFO     | __main__:main:111 - 应用程序已正常退出
2025-06-18 16:11:40.302 | INFO     | utils.logger:init_logger:98 - 日志系统初始化完成，默认级别: DEBUG，日志目录: h:\PyProject\TeleTest\APPDATA\logs
2025-06-18 16:11:41.912 | INFO     | data.database:init_database:31 - 初始化数据库连接: h:\PyProject\TeleTest\APPDATA\database\telegram_manager.db
2025-06-18 16:11:41.931 | DEBUG    | data.database:init_database:58 - 创建数据库表
2025-06-18 16:11:41.946 | INFO     | data.database:init_database:61 - 数据库初始化完成
2025-06-18 16:11:43.025 | DEBUG    | app.controllers.auth_controller:initialize_software:43 - 控制器: 初始化软件 app_id=1000, version=1.2.7
2025-06-18 16:11:43.026 | DEBUG    | core.auth.api_service:init_software:55 - 发送初始化请求: https://server.xile188.com/api/user/1000/windowns10/1.2.7/ini, params={}
2025-06-18 16:11:43.386 | DEBUG    | core.auth.api_service:init_software:66 - 初始化数据解密成功: {'new_content': '- 新增功能\n  - 1.新增图片上传功能\n  - 2.新增软件文档\n  - 3.新增软件自动更新，后续更新会自动检测版本更新，无需再下载更新包\n  - 4.新增设置账户信息及发信控制\n- 修复bug\n  - 删除监听任务时，不删除对应任务采集到的数据。\n  - 监听设置群无法发送到目标群\n  - 导出监听数据时应当导出单个任务而非所有数据\n- 优化记录\n  - 优化发信规则，直接调取符合要求的账户发送，无账户时才需要等待延时。\n  - 完善软件设置', 'new_url': None, 'notice': {'id': '2', 'content': '公告内容测试3', 'time': '**********'}}
2025-06-18 16:11:57.611 | INFO     | app.controllers.auth_controller:login:61 - 控制器: 用户登录 account=demo01
2025-06-18 16:12:00.590 | INFO     | core.auth.api_service:login:127 - 用户登录: account=demo01
2025-06-18 16:12:00.813 | INFO     | core.auth.api_service:get_user_info:347 - 获取用户信息
2025-06-18 16:12:01.080 | INFO     | core.auth.api_service:get_user_info:375 - 获取用户信息成功
2025-06-18 16:12:01.088 | INFO     | __main__:on_auth_completed:93 - 用户登录成功，正在启动主窗口...
2025-06-18 16:12:01.121 | INFO     | core.telegram.client_worker:__init__:61 - Telegram客户端工作线程初始化
2025-06-18 16:12:01.132 | INFO     | ui.main_window:_initialize_core_components:82 - MainWindow: 初始化核心组件...
2025-06-18 16:12:01.136 | INFO     | core.telegram.client_worker:run:65 - Telegram客户端工作线程开始运行
2025-06-18 16:12:01.137 | INFO     | app.services.account_service:__init__:42 - 账户服务初始化
2025-06-18 16:12:01.138 | INFO     | app.controllers.account_controller:__init__:81 - 账户控制器初始化
2025-06-18 16:12:01.138 | INFO     | app.services.monitor_service:__init__:39 - 初始化监控任务服务
2025-06-18 16:12:01.139 | INFO     | core.telegram.client_manager:__init__:66 - Telegram客户端管理器初始化
2025-06-18 16:12:01.139 | INFO     | core.monitor.filter:__init__:26 - 初始化消息过滤器
2025-06-18 16:12:01.139 | INFO     | core.telegram.user_manager:__init__:30 - Telegram用户管理器初始化
2025-06-18 16:12:01.139 | INFO     | core.monitor.extractor:__init__:24 - 初始化关键词提取器
2025-06-18 16:12:01.140 | INFO     | core.telegram.group_manager:__init__:36 - Telegram群组管理器初始化
2025-06-18 16:12:01.140 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-06-18 16:12:01.140 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-06-18 16:12:01.141 | INFO     | core.monitor.notifier:__init__:90 - 初始化通知管理器
2025-06-18 16:12:01.141 | INFO     | core.telegram.monitor_manager:__init__:26 - Telegram监控管理器初始化
2025-06-18 16:12:01.141 | INFO     | app.controllers.telegram_monitor_controller:__init__:64 - 初始化 Telegram 监控控制器
2025-06-18 16:12:01.142 | INFO     | core.telegram.telegram_service:__init__:39 - Telegram服务初始化完成
2025-06-18 16:12:01.142 | DEBUG    | app.controllers.telegram_monitor_controller:__init__:82 - 控制器初始化完成
2025-06-18 16:12:01.143 | INFO     | core.proxy.proxy_task_manager:run:284 - 代理任务管理器线程启动
2025-06-18 16:12:01.338 | INFO     | ui.views.monitor_view:__init__:48 - 初始化监控视图
2025-06-18 16:12:01.339 | DEBUG    | ui.views.monitor_view:_connect_signals:80 - 连接控制器信号
2025-06-18 16:12:01.522 | INFO     | ui.views.proxy_view:__init__:96 - 初始化视图，加载第一页代理数据
2025-06-18 16:12:01.776 | INFO     | __main__:on_auth_completed:97 - 主窗口已启动
2025-06-18 16:12:01.836 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:88 - 尝试连接监听日志信号...
2025-06-18 16:12:01.837 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:90 - 监听日志信号连接成功
2025-06-18 16:12:01.838 | INFO     | core.auth.api_service:verify_vip:425 - 会员验证接口调用
2025-06-18 16:12:01.839 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-18 16:12:01.844 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-18 16:12:01.848 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:145 - 加载监控任务列表 (搜索关键词: '')
2025-06-18 16:12:01.849 | DEBUG    | ui.views.monitor_view:_on_loading_started:620 - 加载中: 正在加载任务列表...
2025-06-18 16:12:01.849 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-18 16:12:01.857 | INFO     | app.services.proxy_service:refresh_proxies:712 - 刷新代理列表: offset=0, limit=20
2025-06-18 16:12:01.857 | INFO     | core.proxy.proxy_task_manager:submit_task:172 - 提交耗时任务到子线程: refresh_proxies_0_20
2025-06-18 16:12:01.858 | DEBUG    | app.services.proxy_service:get_proxies:500 - 获取代理列表: offset=0, limit=20
2025-06-18 16:12:01.858 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-18 16:12:01.858 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-18 16:12:01.868 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-18 16:12:01.891 | INFO     | app.controllers.invite_controller:get_invite_tasks:38 - 获取邀请任务列表, status=None
2025-06-18 16:12:01.893 | INFO     | app.services.invite_service:get_invite_tasks:121 - 获取邀请任务列表, status=None
2025-06-18 16:12:01.893 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-18 16:12:01.894 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-18 16:12:01.894 | INFO     | app.services.invite_service:task_initialize:39 - 初始化邀请服务，重置运行中的任务为暂停状态
2025-06-18 16:12:01.894 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-18 16:12:01.895 | INFO     | core.proxy.proxy_task_manager:task_wrapper:199 - 任务 refresh_proxies_0_20 完成
2025-06-18 16:12:02.025 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-18 16:12:02.028 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-18 16:12:02.032 | INFO     | app.services.invite_service:task_initialize:48 - 成功重置 0 个任务状态
2025-06-18 16:12:02.033 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-18 16:12:02.034 | DEBUG    | ui.views.monitor_view:_refresh_task_list:175 - 刷新任务列表，共2个任务
2025-06-18 16:12:02.039 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-18 16:12:02.048 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-18 16:12:02.051 | INFO     | app.services.account_service:get_all_groups:96 - 获取所有账户分组成功, 共 2 个
2025-06-18 16:12:02.052 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-18 16:12:02.054 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-18 16:12:02.054 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-18 16:12:02.061 | DEBUG    | ui.views.account_view:_on_groups_loaded:442 - 分组加载完成: 2个分组
2025-06-18 16:12:02.078 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-18 16:12:02.147 | INFO     | app.services.account_service:get_all_accounts:289 - 获取所有账户成功, 共 2 个
2025-06-18 16:12:02.147 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-18 16:12:02.148 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-18 16:12:02.149 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-18 16:12:02.151 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-18 16:12:02.151 | DEBUG    | ui.views.account_view:_on_accounts_loaded:575 - 账户加载完成: 2个账户
2025-06-18 16:12:02.174 | DEBUG    | ui.views.account_view:_update_group_counts:184 - 分组计数已更新: {2: 1, 1: 1}
2025-06-18 16:12:02.178 | INFO     | ui.views.account_view:_auto_login_accounts:630 - 开始调用控制器进行 2 个账户的自动登录。
2025-06-18 16:12:02.179 | INFO     | app.services.account_service:batch_auto_login:1153 - 服务层：开始批量自动登录 2 个账户, 最大并发: 5
2025-06-18 16:12:02.179 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-18 16:12:02.183 | INFO     | app.controllers.telegram_monitor_controller:_load_total_users_count_internal:191 - 成功加载总体统计数据: 总用户数 1845, 今日采集 0, 日均采集 264, 运行天数 7
2025-06-18 16:12:02.184 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:156 - 成功加载 2 个任务
2025-06-18 16:12:02.184 | DEBUG    | ui.views.monitor_view:_on_loading_finished:625 - 加载完成
2025-06-18 16:12:02.187 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-06-18 16:12:02.207 | INFO     | app.services.account_service:batch_auto_login:1192 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 1********:1080
2025-06-18 16:12:02.208 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-18 16:12:02.210 | INFO     | app.services.account_service:get_all_accounts:289 - 获取所有账户成功, 共 2 个
2025-06-18 16:12:02.210 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-18 16:12:02.213 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-18 16:12:02.216 | DEBUG    | ui.views.account_view:_update_group_counts:184 - 分组计数已更新: {2: 1, 1: 1}
2025-06-18 16:12:02.216 | DEBUG    | ui.views.account_view:_init_cache:151 - 账户缓存初始化完成，共 2 个账户
2025-06-18 16:12:02.223 | DEBUG    | ui.views.account_view:_on_accounts_loaded:575 - 账户加载完成: 2个账户
2025-06-18 16:12:02.246 | DEBUG    | ui.views.account_view:_update_group_counts:184 - 分组计数已更新: {2: 1, 1: 1}
2025-06-18 16:12:02.256 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=True
2025-06-18 16:12:02.258 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=True
2025-06-18 16:12:02.260 | INFO     | ui.views.proxy_view:_load_initial_data:1044 - 初始数据加载完成，共 1 个代理
2025-06-18 16:12:02.261 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-18 16:12:02.263 | INFO     | app.services.account_service:batch_auto_login:1192 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 1********:1080
2025-06-18 16:12:02.263 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-18 16:12:02.292 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-18 16:12:02.295 | INFO     | app.services.account_service:batch_auto_login:1262 - 服务层：准备调用核心层处理 2 个账户的自动登录。
2025-06-18 16:12:02.296 | INFO     | app.services.account_service:batch_auto_login:1272 - 服务层：设置核心层任务超时为 120 秒。
2025-06-18 16:12:02.296 | INFO     | core.telegram.client_manager:batch_auto_login:1211 - 开始批量自动登录, 共 2 个账户, 最大并发 5
2025-06-18 16:12:02.298 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:254 - 创建客户端: h:\PyProject\TeleTest\APPDATA\sessions\+***********.session, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '1********', 'port': 1080}
2025-06-18 16:12:02.299 | DEBUG    | core.telegram.client_manager:get_proxy:91 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '1********', 'port': 1080}
2025-06-18 16:12:02.300 | DEBUG    | core.telegram.client_manager:get_proxy:138 - get_proxy: 使用 ip_pool 代理 1********:1080，转换结果: (<ProxyType.SOCKS5: 2>, '1********', 1080)
2025-06-18 16:12:02.303 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:254 - 创建客户端: h:\PyProject\TeleTest\APPDATA\sessions\+***********.session, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '1********', 'port': 1080}
2025-06-18 16:12:02.305 | DEBUG    | core.telegram.client_manager:get_proxy:91 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '1********', 'port': 1080}
2025-06-18 16:12:02.305 | DEBUG    | core.telegram.client_manager:get_proxy:138 - get_proxy: 使用 ip_pool 代理 1********:1080，转换结果: (<ProxyType.SOCKS5: 2>, '1********', 1080)
2025-06-18 16:12:02.323 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-18 16:12:02.327 | WARNING  | core.auth.api_service:verify_vip:441 - 会员验证失败: 验证失败
2025-06-18 16:12:24.223 | INFO     | core.telegram.client_manager:_connect_client:202 - 连接成功!
2025-06-18 16:13:01.071 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-18 16:14:01.071 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-18 16:14:02.445 | ERROR    | app.services.account_service:batch_auto_login:1276 - 服务层：核心层批量自动登录失败: 任务结果获取超时
2025-06-18 16:14:26.532 | INFO     | core.telegram.client_manager:_verify_client_authorization:1186 - 账户已授权: +***********
2025-06-18 16:15:01.073 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-18 16:16:01.069 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-18 16:16:02.761 | INFO     | ui.main_window:closeEvent:346 - MainWindow: 接收到关闭事件
2025-06-18 16:16:02.773 | INFO     | ui.main_window:_cleanup_before_quit:238 - MainWindow: 执行清理资源...
2025-06-18 16:16:02.773 | INFO     | core.telegram.client_worker:stop:108 - 正在停止Telegram客户端工作线程
2025-06-18 16:16:02.774 | INFO     | core.telegram.client_worker:_graceful_shutdown:449 - 等待 1 个耗时任务完成...
2025-06-18 16:17:02.296 | WARNING  | core.telegram.client_manager:batch_auto_login:1347 - 批量登录队列处理超时
2025-06-18 16:17:02.296 | INFO     | core.telegram.client_manager:login_worker:1299 - 登录任务 +*********** 被取消
2025-06-18 16:17:04.297 | INFO     | core.telegram.client_manager:login_worker:1326 - 登录工作协程被取消
2025-06-18 16:17:04.297 | INFO     | core.telegram.client_manager:batch_auto_login:1360 - 批量登录完成: 总计 2, 成功 1, 失败 1
2025-06-18 16:17:04.301 | INFO     | core.telegram.client_manager:cleanup_async:1131 - 开始清理资源并断开所有连接
2025-06-18 16:17:04.302 | INFO     | core.telegram.client_manager:disconnect_all_clients:716 - 正在断开所有客户端连接
2025-06-18 16:17:04.302 | INFO     | core.telegram.client_manager:_safe_disconnect:737 - 正在断开客户端连接: +***********
2025-06-18 16:17:04.309 | INFO     | core.telegram.client_manager:_safe_disconnect:741 - 断开客户端连接成功: +***********
2025-06-18 16:17:04.310 | INFO     | core.telegram.client_manager:cleanup_async:1150 - 资源清理完成
2025-06-18 16:17:04.796 | INFO     | core.telegram.client_manager:cleanup_async:1131 - 开始清理资源并断开所有连接
2025-06-18 16:17:04.796 | INFO     | core.telegram.client_manager:disconnect_all_clients:716 - 正在断开所有客户端连接
2025-06-18 16:17:04.797 | INFO     | core.telegram.client_manager:cleanup_async:1150 - 资源清理完成
2025-06-18 16:17:05.298 | INFO     | core.telegram.client_worker:run:97 - Telegram客户端工作线程已退出
2025-06-18 16:17:05.299 | INFO     | core.telegram.client_worker:stop:122 - Telegram客户端工作线程已停止
2025-06-18 16:17:05.300 | INFO     | ui.main_window:_cleanup_before_quit:247 - TelegramClientWorker 已停止。
2025-06-18 16:17:05.300 | INFO     | ui.main_window:_cleanup_before_quit:251 - MainWindow 清理完成
2025-06-18 16:17:05.305 | INFO     | __main__:main:111 - 应用程序已正常退出
