2025-06-21 17:49:17.415 | INFO     | utils.logger:init_logger:98 - 日志系统初始化完成，默认级别: DEBUG，日志目录: H:\PyProject\TeleTest\APPDATA\logs
2025-06-21 17:49:19.696 | INFO     | data.database:init_database:31 - 初始化数据库连接: H:\PyProject\TeleTest\APPDATA\database\telegram_manager.db
2025-06-21 17:49:19.726 | DEBUG    | data.database:init_database:58 - 创建数据库表
2025-06-21 17:49:19.737 | INFO     | data.database:init_database:61 - 数据库初始化完成
2025-06-21 17:49:21.988 | DEBUG    | app.controllers.auth_controller:initialize_software:43 - 控制器: 初始化软件 app_id=1000, version=1.2.7
2025-06-21 17:49:21.988 | DEBUG    | core.auth.api_service:init_software:55 - 发送初始化请求: https://server.xile188.com/api/user/1000/windowns10/1.2.7/ini, params={}
2025-06-21 17:49:22.464 | DEBUG    | core.auth.api_service:init_software:66 - 初始化数据解密成功: {'new_content': '- 新增功能\n  - 1.新增图片上传功能\n  - 2.新增软件文档\n  - 3.新增软件自动更新，后续更新会自动检测版本更新，无需再下载更新包\n  - 4.新增设置账户信息及发信控制\n- 修复bug\n  - 删除监听任务时，不删除对应任务采集到的数据。\n  - 监听设置群无法发送到目标群\n  - 导出监听数据时应当导出单个任务而非所有数据\n- 优化记录\n  - 优化发信规则，直接调取符合要求的账户发送，无账户时才需要等待延时。\n  - 完善软件设置', 'new_url': None, 'notice': {'id': '2', 'content': '公告内容测试3', 'time': '**********'}}
2025-06-21 17:49:39.861 | INFO     | app.controllers.auth_controller:login:61 - 控制器: 用户登录 account=demo01
2025-06-21 17:49:42.996 | INFO     | core.auth.api_service:login:127 - 用户登录: account=demo01
2025-06-21 17:49:43.312 | INFO     | core.auth.api_service:get_user_info:347 - 获取用户信息
2025-06-21 17:49:43.538 | INFO     | core.auth.api_service:get_user_info:375 - 获取用户信息成功
2025-06-21 17:49:43.546 | INFO     | __main__:on_auth_completed:93 - 用户登录成功，正在启动主窗口...
2025-06-21 17:49:43.573 | INFO     | core.telegram.client_worker:__init__:61 - Telegram客户端工作线程初始化
2025-06-21 17:49:43.574 | INFO     | ui.main_window:_initialize_core_components:82 - MainWindow: 初始化核心组件...
2025-06-21 17:49:43.574 | INFO     | core.telegram.client_worker:run:65 - Telegram客户端工作线程开始运行
2025-06-21 17:49:43.575 | INFO     | app.services.account_service:__init__:42 - 账户服务初始化
2025-06-21 17:49:43.575 | INFO     | core.proxy.proxy_task_manager:run:284 - 代理任务管理器线程启动
2025-06-21 17:49:43.575 | INFO     | app.controllers.account_controller:__init__:81 - 账户控制器初始化
2025-06-21 17:49:43.576 | INFO     | app.services.monitor_service:__init__:39 - 初始化监控任务服务
2025-06-21 17:49:43.576 | INFO     | core.monitor.filter:__init__:26 - 初始化消息过滤器
2025-06-21 17:49:43.576 | INFO     | core.monitor.extractor:__init__:24 - 初始化关键词提取器
2025-06-21 17:49:43.576 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-06-21 17:49:43.577 | INFO     | core.monitor.notifier:__init__:90 - 初始化通知管理器
2025-06-21 17:49:43.577 | INFO     | app.controllers.telegram_monitor_controller:__init__:64 - 初始化 Telegram 监控控制器
2025-06-21 17:49:43.577 | DEBUG    | app.controllers.telegram_monitor_controller:__init__:82 - 控制器初始化完成
2025-06-21 17:49:43.577 | INFO     | core.telegram.client_manager:__init__:66 - Telegram客户端管理器初始化
2025-06-21 17:49:43.578 | INFO     | core.telegram.user_manager:__init__:30 - Telegram用户管理器初始化
2025-06-21 17:49:43.579 | INFO     | core.telegram.group_manager:__init__:36 - Telegram群组管理器初始化
2025-06-21 17:49:43.579 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-06-21 17:49:43.580 | INFO     | core.telegram.monitor_manager:__init__:26 - Telegram监控管理器初始化
2025-06-21 17:49:43.580 | INFO     | core.telegram.telegram_service:__init__:39 - Telegram服务初始化完成
2025-06-21 17:49:43.795 | INFO     | ui.views.monitor_view:__init__:48 - 初始化监控视图
2025-06-21 17:49:43.795 | DEBUG    | ui.views.monitor_view:_connect_signals:80 - 连接控制器信号
2025-06-21 17:49:44.015 | INFO     | ui.views.proxy_view:__init__:96 - 初始化视图，加载第一页代理数据
2025-06-21 17:49:44.266 | INFO     | __main__:on_auth_completed:97 - 主窗口已启动
2025-06-21 17:49:44.336 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:88 - 尝试连接监听日志信号...
2025-06-21 17:49:44.336 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:90 - 监听日志信号连接成功
2025-06-21 17:49:44.336 | INFO     | core.auth.api_service:verify_vip:425 - 会员验证接口调用
2025-06-21 17:49:44.337 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-21 17:49:44.340 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-21 17:49:44.343 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:145 - 加载监控任务列表 (搜索关键词: '')
2025-06-21 17:49:44.343 | DEBUG    | ui.views.monitor_view:_on_loading_started:620 - 加载中: 正在加载任务列表...
2025-06-21 17:49:44.343 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-21 17:49:44.349 | INFO     | app.services.proxy_service:refresh_proxies:712 - 刷新代理列表: offset=0, limit=20
2025-06-21 17:49:44.350 | INFO     | core.proxy.proxy_task_manager:submit_task:172 - 提交耗时任务到子线程: refresh_proxies_0_20
2025-06-21 17:49:44.350 | DEBUG    | app.services.proxy_service:get_proxies:500 - 获取代理列表: offset=0, limit=20
2025-06-21 17:49:44.350 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-21 17:49:44.350 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-21 17:49:44.354 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-21 17:49:44.378 | INFO     | app.controllers.invite_controller:get_invite_tasks:38 - 获取邀请任务列表, status=None
2025-06-21 17:49:44.378 | INFO     | app.services.invite_service:get_invite_tasks:121 - 获取邀请任务列表, status=None
2025-06-21 17:49:44.378 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-21 17:49:44.381 | INFO     | app.services.invite_service:task_initialize:39 - 初始化邀请服务，重置运行中的任务为暂停状态
2025-06-21 17:49:44.381 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-21 17:49:44.383 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-21 17:49:44.384 | INFO     | core.proxy.proxy_task_manager:task_wrapper:199 - 任务 refresh_proxies_0_20 完成
2025-06-21 17:49:44.617 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-21 17:49:44.620 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-21 17:49:44.628 | DEBUG    | ui.views.monitor_view:_refresh_task_list:175 - 刷新任务列表，共2个任务
2025-06-21 17:49:44.634 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-21 17:49:44.635 | INFO     | app.services.invite_service:task_initialize:48 - 成功重置 0 个任务状态
2025-06-21 17:49:44.635 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-21 17:49:44.639 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-21 17:49:44.646 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-21 17:49:44.649 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-21 17:49:44.650 | INFO     | app.services.account_service:get_all_groups:96 - 获取所有账户分组成功, 共 2 个
2025-06-21 17:49:44.650 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-21 17:49:44.703 | DEBUG    | ui.views.account_view:_on_groups_loaded:442 - 分组加载完成: 2个分组
2025-06-21 17:49:44.716 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-21 17:49:44.744 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-21 17:49:44.746 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-21 17:49:44.747 | INFO     | app.services.account_service:get_all_accounts:289 - 获取所有账户成功, 共 2 个
2025-06-21 17:49:44.747 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-21 17:49:44.749 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-06-21 17:49:44.762 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-21 17:49:44.764 | INFO     | app.controllers.telegram_monitor_controller:_load_total_users_count_internal:191 - 成功加载总体统计数据: 总用户数 1845, 今日采集 0, 日均采集 184, 运行天数 10
2025-06-21 17:49:44.765 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:156 - 成功加载 2 个任务
2025-06-21 17:49:44.765 | DEBUG    | ui.views.monitor_view:_on_loading_finished:625 - 加载完成
2025-06-21 17:49:44.766 | DEBUG    | ui.views.account_view:_on_accounts_loaded:575 - 账户加载完成: 2个账户
2025-06-21 17:49:44.791 | DEBUG    | ui.views.account_view:_update_group_counts:184 - 分组计数已更新: {2: 1, 1: 1}
2025-06-21 17:49:44.795 | INFO     | ui.views.account_view:_auto_login_accounts:630 - 开始调用控制器进行 2 个账户的自动登录。
2025-06-21 17:49:44.795 | INFO     | app.services.account_service:batch_auto_login:1153 - 服务层：开始批量自动登录 2 个账户, 最大并发: 5
2025-06-21 17:49:44.795 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-21 17:49:44.804 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=True
2025-06-21 17:49:44.806 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=True
2025-06-21 17:49:44.808 | INFO     | ui.views.proxy_view:_load_initial_data:1044 - 初始数据加载完成，共 1 个代理
2025-06-21 17:49:44.811 | INFO     | app.services.account_service:get_all_accounts:289 - 获取所有账户成功, 共 2 个
2025-06-21 17:49:44.811 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-21 17:49:44.814 | INFO     | app.services.account_service:batch_auto_login:1192 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-06-21 17:49:44.814 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-21 17:49:44.815 | DEBUG    | ui.views.account_view:_update_group_counts:184 - 分组计数已更新: {2: 1, 1: 1}
2025-06-21 17:49:44.816 | DEBUG    | ui.views.account_view:_init_cache:151 - 账户缓存初始化完成，共 2 个账户
2025-06-21 17:49:44.821 | DEBUG    | ui.views.account_view:_on_accounts_loaded:575 - 账户加载完成: 2个账户
2025-06-21 17:49:44.844 | DEBUG    | ui.views.account_view:_update_group_counts:184 - 分组计数已更新: {2: 1, 1: 1}
2025-06-21 17:49:44.854 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-21 17:49:44.856 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-21 17:49:44.890 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-21 17:49:44.900 | WARNING  | core.auth.api_service:verify_vip:441 - 会员验证失败: 验证失败
2025-06-21 17:49:45.089 | INFO     | app.services.account_service:batch_auto_login:1192 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-06-21 17:49:45.090 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-21 17:49:45.303 | INFO     | app.services.account_service:batch_auto_login:1262 - 服务层：准备调用核心层处理 2 个账户的自动登录。
2025-06-21 17:49:45.304 | INFO     | app.services.account_service:batch_auto_login:1272 - 服务层：设置核心层任务超时为 120 秒。
2025-06-21 17:49:45.304 | INFO     | core.telegram.client_manager:batch_auto_login:1211 - 开始批量自动登录, 共 2 个账户, 最大并发 5
2025-06-21 17:49:45.304 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:254 - 创建客户端: H:\PyProject\TeleTest\APPDATA\sessions\+***********.session, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-06-21 17:49:45.305 | DEBUG    | core.telegram.client_manager:get_proxy:91 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-06-21 17:49:45.305 | DEBUG    | core.telegram.client_manager:get_proxy:138 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-06-21 17:49:45.311 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:254 - 创建客户端: H:\PyProject\TeleTest\APPDATA\sessions\+***********.session, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-06-21 17:49:45.311 | DEBUG    | core.telegram.client_manager:get_proxy:91 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-06-21 17:49:45.311 | DEBUG    | core.telegram.client_manager:get_proxy:138 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-06-21 17:49:45.411 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-21 17:49:48.937 | INFO     | core.telegram.client_manager:_connect_client:202 - 连接成功!
2025-06-21 17:49:49.952 | INFO     | core.telegram.client_manager:_verify_client_authorization:1186 - 账户已授权: +***********
2025-06-21 17:49:49.987 | INFO     | core.telegram.client_manager:_connect_client:202 - 连接成功!
2025-06-21 17:49:51.070 | INFO     | core.telegram.client_manager:_verify_client_authorization:1186 - 账户已授权: +***********
2025-06-21 17:49:53.082 | INFO     | core.telegram.client_manager:batch_auto_login:1360 - 批量登录完成: 总计 2, 成功 2, 失败 0
2025-06-21 17:49:53.352 | INFO     | app.services.account_service:batch_auto_login:1293 - 服务层：核心层登录完成: 成功 2, 失败 0 / 2
2025-06-21 17:50:43.539 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 17:51:43.539 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 17:52:43.537 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 17:53:43.539 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 17:54:43.537 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 17:55:43.540 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 17:56:43.540 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 17:57:43.540 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 17:58:43.537 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 17:59:43.541 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 18:00:43.537 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 18:01:43.540 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 18:02:43.539 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 18:03:43.541 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 18:04:43.536 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 18:05:43.539 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 18:06:43.536 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 18:07:43.537 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 18:08:43.536 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 18:09:43.538 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 18:10:43.538 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 18:11:43.540 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 18:12:43.538 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 18:13:43.539 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 18:14:43.539 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 18:15:43.537 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 18:16:43.538 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 18:17:43.539 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 18:18:43.542 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 18:19:43.538 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 18:20:43.538 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 18:21:43.545 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 18:22:43.538 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 18:23:43.540 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 18:24:43.539 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 18:25:43.540 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 18:26:43.538 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 18:27:43.540 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 18:28:43.539 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 18:29:43.539 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 18:30:43.539 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 18:31:43.540 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 18:32:43.544 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 18:33:43.542 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 18:34:43.545 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 18:35:43.544 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 18:36:43.543 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 18:37:43.545 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 18:38:43.543 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 18:39:43.545 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 18:40:43.545 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 18:41:43.544 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 18:42:43.545 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 18:43:43.543 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 18:44:43.542 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 18:45:43.545 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 18:46:43.543 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 18:47:43.545 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 18:48:43.542 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 18:49:43.543 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 18:50:43.544 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 18:51:43.543 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 18:52:43.544 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 18:53:43.537 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 18:54:43.539 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 18:55:43.537 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 18:56:43.539 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 18:57:43.536 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 18:58:43.537 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 18:59:43.540 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 19:00:43.537 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 19:01:43.537 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 19:02:43.538 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 19:03:43.536 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 19:04:43.540 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 19:05:43.544 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 19:06:43.537 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 19:07:43.539 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 19:08:43.538 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 19:09:43.537 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 19:10:43.537 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 19:11:43.536 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 19:12:43.537 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 19:13:43.539 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 19:14:43.538 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 19:15:43.538 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 19:16:43.537 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 19:17:43.537 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 19:18:43.537 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 19:19:43.539 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 19:20:43.539 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 19:21:43.538 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 19:22:43.538 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 19:23:43.540 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 19:24:43.536 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 19:25:43.536 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 19:26:43.536 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 19:27:43.538 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 19:28:43.539 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 19:29:43.537 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 19:30:43.538 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 19:31:43.538 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 19:32:43.538 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 19:33:43.536 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 19:34:43.536 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 19:35:43.537 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 19:36:43.536 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 19:37:43.539 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 19:38:43.538 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 19:39:43.539 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 19:40:43.536 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 19:41:43.540 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 19:42:43.541 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 19:43:43.537 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 19:44:43.539 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 19:45:43.544 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 19:46:43.539 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 19:47:43.537 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 19:48:43.537 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 19:49:43.537 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 19:50:43.540 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 19:51:43.542 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 19:52:43.538 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 19:53:43.538 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 19:54:43.539 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 19:55:43.541 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 19:56:43.541 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 19:57:43.539 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 19:58:43.539 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 19:59:43.541 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 20:00:43.541 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 20:01:43.542 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 20:02:43.545 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 20:03:43.541 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 20:04:43.549 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 20:05:43.545 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 20:06:43.545 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 20:07:43.541 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 20:08:43.544 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 20:09:43.545 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 20:10:43.546 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 20:11:43.544 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 20:12:43.537 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 20:13:43.537 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 20:14:43.541 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 20:15:43.540 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 20:16:43.540 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 20:17:43.537 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 20:18:43.539 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 20:19:43.536 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 20:20:43.539 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 20:21:43.540 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 20:22:43.538 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 20:23:43.536 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 20:24:43.539 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 20:25:43.536 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 20:26:43.540 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 20:27:43.539 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 20:28:43.537 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 20:29:43.539 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 20:30:43.540 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 20:31:43.545 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 20:32:43.541 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 20:33:43.538 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 20:34:43.538 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 20:35:43.539 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 20:36:43.539 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 20:37:43.538 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 20:38:43.539 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 20:39:43.537 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 20:40:43.539 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 20:41:43.536 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 20:42:43.537 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 20:43:43.538 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 20:44:43.541 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 20:45:43.541 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 20:46:43.539 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 20:47:43.540 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 20:48:43.542 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 20:49:43.540 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 20:50:43.540 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 20:51:43.538 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 20:52:43.540 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 20:53:43.540 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 20:54:43.541 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 20:55:43.536 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 20:56:43.538 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 20:57:43.538 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 20:58:43.537 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 20:59:43.537 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 21:00:43.537 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 21:01:43.540 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 21:02:43.540 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 21:03:43.540 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 21:04:43.538 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 21:05:43.538 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 21:06:43.536 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 21:07:43.539 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 21:08:43.539 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 21:09:43.542 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 21:10:43.538 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 21:11:43.536 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 21:12:43.538 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 21:13:43.542 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 21:14:43.538 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 21:15:43.536 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 21:16:43.540 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 21:17:43.540 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 21:18:43.539 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 21:19:43.536 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 21:20:43.541 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 21:21:43.537 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 21:22:43.540 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 21:23:43.537 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 21:24:43.539 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 21:25:43.537 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 21:26:43.539 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 21:27:43.538 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 21:28:43.542 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 21:29:43.540 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 21:30:43.537 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 21:31:43.539 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 21:32:43.539 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 21:33:43.539 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 21:34:43.539 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 21:35:43.539 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 21:36:43.540 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 21:37:43.539 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 21:38:43.539 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 21:39:43.537 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 21:40:43.542 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 21:41:43.543 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 21:42:43.542 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 21:43:43.545 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 21:44:43.541 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 21:45:43.544 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 21:46:43.545 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 21:47:43.542 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 21:48:43.544 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 21:49:43.544 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 21:50:43.543 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 21:51:43.541 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 21:52:43.542 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 21:53:43.543 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 21:54:43.542 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 21:55:43.536 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 21:56:43.544 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 21:57:43.542 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 21:58:43.544 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 21:59:43.542 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 22:00:43.543 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 22:01:43.542 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 22:02:43.544 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 22:03:43.542 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 22:04:43.543 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 22:05:43.545 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 22:06:43.544 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 22:07:43.543 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 22:08:43.543 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 22:09:43.539 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 22:10:43.545 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 22:11:43.541 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 22:12:43.544 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 22:13:43.545 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 22:14:43.544 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 22:15:43.543 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 22:16:43.545 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 22:17:43.543 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 22:18:43.543 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 22:19:43.542 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 22:20:43.543 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 22:21:43.540 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 22:22:43.544 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 22:23:43.543 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 22:24:43.545 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 22:25:43.537 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 22:26:43.541 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 22:27:43.543 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 22:28:43.541 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 22:29:43.543 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 22:30:43.543 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 22:31:43.536 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 22:32:43.543 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 22:33:43.541 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 22:34:43.537 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-21 22:35:43.541 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
