2025-06-24 21:30:34.832 | INFO     | utils.logger:init_logger:98 - 日志系统初始化完成，默认级别: DEBUG，日志目录: H:\PyProject\TeleTest\APPDATA\logs
2025-06-24 21:30:37.067 | INFO     | data.database:init_database:31 - 初始化数据库连接: H:\PyProject\TeleTest\APPDATA\database\telegram_manager.db
2025-06-24 21:30:37.093 | DEBUG    | data.database:init_database:58 - 创建数据库表
2025-06-24 21:30:37.108 | INFO     | data.database:init_database:61 - 数据库初始化完成
2025-06-24 21:30:38.342 | DEBUG    | app.controllers.auth_controller:initialize_software:43 - 控制器: 初始化软件 app_id=1000, version=1.2.7
2025-06-24 21:30:38.343 | DEBUG    | core.auth.api_service:init_software:55 - 发送初始化请求: https://server.xile188.com/api/user/1000/windowns10/1.2.7/ini, params={}
2025-06-24 21:30:38.892 | DEBUG    | core.auth.api_service:init_software:66 - 初始化数据解密成功: {'new_content': '- 新增功能\n  - 1.新增图片上传功能\n  - 2.新增软件文档\n  - 3.新增软件自动更新，后续更新会自动检测版本更新，无需再下载更新包\n  - 4.新增设置账户信息及发信控制\n- 修复bug\n  - 删除监听任务时，不删除对应任务采集到的数据。\n  - 监听设置群无法发送到目标群\n  - 导出监听数据时应当导出单个任务而非所有数据\n- 优化记录\n  - 优化发信规则，直接调取符合要求的账户发送，无账户时才需要等待延时。\n  - 完善软件设置', 'new_url': None, 'notice': {'id': '2', 'content': '公告内容测试3', 'time': '**********'}}
2025-06-24 21:30:38.904 | INFO     | app.controllers.auth_controller:login:61 - 控制器: 用户登录 account=demo01
2025-06-24 21:30:41.920 | INFO     | core.auth.api_service:login:127 - 用户登录: account=demo01
2025-06-24 21:30:42.213 | INFO     | core.auth.api_service:get_user_info:347 - 获取用户信息
2025-06-24 21:30:42.468 | INFO     | core.auth.api_service:get_user_info:375 - 获取用户信息成功
2025-06-24 21:30:42.474 | INFO     | __main__:on_auth_completed:93 - 用户登录成功，正在启动主窗口...
2025-06-24 21:30:42.500 | INFO     | core.telegram.client_worker:__init__:61 - Telegram客户端工作线程初始化
2025-06-24 21:30:42.500 | INFO     | ui.main_window:_initialize_core_components:82 - MainWindow: 初始化核心组件...
2025-06-24 21:30:42.500 | INFO     | app.services.account_service:__init__:42 - 账户服务初始化
2025-06-24 21:30:42.500 | INFO     | core.telegram.client_worker:run:65 - Telegram客户端工作线程开始运行
2025-06-24 21:30:42.501 | INFO     | core.proxy.proxy_task_manager:run:284 - 代理任务管理器线程启动
2025-06-24 21:30:42.501 | INFO     | app.controllers.account_controller:__init__:81 - 账户控制器初始化
2025-06-24 21:30:42.501 | INFO     | app.services.monitor_service:__init__:39 - 初始化监控任务服务
2025-06-24 21:30:42.501 | INFO     | core.monitor.filter:__init__:26 - 初始化消息过滤器
2025-06-24 21:30:42.501 | INFO     | core.monitor.extractor:__init__:24 - 初始化关键词提取器
2025-06-24 21:30:42.501 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-06-24 21:30:42.502 | INFO     | core.monitor.notifier:__init__:90 - 初始化通知管理器
2025-06-24 21:30:42.502 | INFO     | app.controllers.telegram_monitor_controller:__init__:64 - 初始化 Telegram 监控控制器
2025-06-24 21:30:42.502 | DEBUG    | app.controllers.telegram_monitor_controller:__init__:82 - 控制器初始化完成
2025-06-24 21:30:42.503 | INFO     | core.telegram.client_manager:__init__:66 - Telegram客户端管理器初始化
2025-06-24 21:30:42.504 | INFO     | core.telegram.user_manager:__init__:30 - Telegram用户管理器初始化
2025-06-24 21:30:42.504 | INFO     | core.telegram.group_manager:__init__:36 - Telegram群组管理器初始化
2025-06-24 21:30:42.505 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-06-24 21:30:42.505 | INFO     | core.telegram.monitor_manager:__init__:26 - Telegram监控管理器初始化
2025-06-24 21:30:42.506 | INFO     | core.telegram.telegram_service:__init__:39 - Telegram服务初始化完成
2025-06-24 21:30:42.697 | INFO     | ui.views.monitor_view:__init__:48 - 初始化监控视图
2025-06-24 21:30:42.697 | DEBUG    | ui.views.monitor_view:_connect_signals:80 - 连接控制器信号
2025-06-24 21:30:42.906 | INFO     | ui.views.proxy_view:__init__:96 - 初始化视图，加载第一页代理数据
2025-06-24 21:30:43.134 | INFO     | __main__:on_auth_completed:97 - 主窗口已启动
2025-06-24 21:30:43.198 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:88 - 尝试连接监听日志信号...
2025-06-24 21:30:43.198 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:90 - 监听日志信号连接成功
2025-06-24 21:30:43.199 | INFO     | core.auth.api_service:verify_vip:425 - 会员验证接口调用
2025-06-24 21:30:43.199 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-24 21:30:43.202 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-24 21:30:43.205 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:145 - 加载监控任务列表 (搜索关键词: '')
2025-06-24 21:30:43.205 | DEBUG    | ui.views.monitor_view:_on_loading_started:620 - 加载中: 正在加载任务列表...
2025-06-24 21:30:43.205 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-24 21:30:43.211 | INFO     | app.services.proxy_service:refresh_proxies:712 - 刷新代理列表: offset=0, limit=20
2025-06-24 21:30:43.211 | INFO     | core.proxy.proxy_task_manager:submit_task:172 - 提交耗时任务到子线程: refresh_proxies_0_20
2025-06-24 21:30:43.211 | DEBUG    | app.services.proxy_service:get_proxies:500 - 获取代理列表: offset=0, limit=20
2025-06-24 21:30:43.211 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-24 21:30:43.211 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-24 21:30:43.215 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-24 21:30:43.238 | INFO     | app.controllers.invite_controller:get_invite_tasks:38 - 获取邀请任务列表, status=None
2025-06-24 21:30:43.238 | INFO     | app.services.invite_service:get_invite_tasks:121 - 获取邀请任务列表, status=None
2025-06-24 21:30:43.238 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-24 21:30:43.239 | INFO     | app.services.invite_service:task_initialize:39 - 初始化邀请服务，重置运行中的任务为暂停状态
2025-06-24 21:30:43.239 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-24 21:30:43.242 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-24 21:30:43.244 | INFO     | core.proxy.proxy_task_manager:task_wrapper:199 - 任务 refresh_proxies_0_20 完成
2025-06-24 21:30:43.517 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-24 21:30:43.517 | INFO     | app.services.invite_service:task_initialize:48 - 成功重置 0 个任务状态
2025-06-24 21:30:43.518 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-24 21:30:43.519 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-24 21:30:43.520 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-24 21:30:43.522 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-24 21:30:43.620 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-24 21:30:43.626 | INFO     | app.services.account_service:get_all_groups:96 - 获取所有账户分组成功, 共 2 个
2025-06-24 21:30:43.627 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-24 21:30:43.628 | DEBUG    | ui.views.monitor_view:_refresh_task_list:175 - 刷新任务列表，共2个任务
2025-06-24 21:30:43.634 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-24 21:30:43.645 | DEBUG    | ui.views.account_view:_on_groups_loaded:442 - 分组加载完成: 2个分组
2025-06-24 21:30:43.667 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-24 21:30:43.691 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-24 21:30:43.692 | INFO     | app.services.account_service:get_all_accounts:289 - 获取所有账户成功, 共 2 个
2025-06-24 21:30:43.692 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-24 21:30:43.696 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-06-24 21:30:43.710 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-24 21:30:43.711 | DEBUG    | ui.views.account_view:_on_accounts_loaded:575 - 账户加载完成: 2个账户
2025-06-24 21:30:43.737 | DEBUG    | ui.views.account_view:_update_group_counts:184 - 分组计数已更新: {2: 1, 1: 1}
2025-06-24 21:30:43.742 | INFO     | ui.views.account_view:_auto_login_accounts:630 - 开始调用控制器进行 2 个账户的自动登录。
2025-06-24 21:30:43.742 | INFO     | app.services.account_service:batch_auto_login:1153 - 服务层：开始批量自动登录 2 个账户, 最大并发: 5
2025-06-24 21:30:43.742 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-24 21:30:43.751 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-24 21:30:43.754 | WARNING  | core.auth.api_service:verify_vip:441 - 会员验证失败: 验证失败
2025-06-24 21:30:43.924 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=True
2025-06-24 21:30:43.926 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=True
2025-06-24 21:30:43.928 | INFO     | ui.views.proxy_view:_load_initial_data:1044 - 初始数据加载完成，共 1 个代理
2025-06-24 21:30:44.041 | INFO     | app.controllers.telegram_monitor_controller:_load_total_users_count_internal:191 - 成功加载总体统计数据: 总用户数 1845, 今日采集 0, 日均采集 142, 运行天数 13
2025-06-24 21:30:44.041 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:156 - 成功加载 2 个任务
2025-06-24 21:30:44.042 | DEBUG    | ui.views.monitor_view:_on_loading_finished:625 - 加载完成
2025-06-24 21:30:44.046 | INFO     | app.services.account_service:get_all_accounts:289 - 获取所有账户成功, 共 2 个
2025-06-24 21:30:44.046 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-24 21:30:44.047 | INFO     | app.services.account_service:batch_auto_login:1192 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-06-24 21:30:44.047 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-24 21:30:44.049 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-24 21:30:44.051 | DEBUG    | ui.views.account_view:_update_group_counts:184 - 分组计数已更新: {2: 1, 1: 1}
2025-06-24 21:30:44.051 | DEBUG    | ui.views.account_view:_init_cache:151 - 账户缓存初始化完成，共 2 个账户
2025-06-24 21:30:44.056 | DEBUG    | ui.views.account_view:_on_accounts_loaded:575 - 账户加载完成: 2个账户
2025-06-24 21:30:44.079 | DEBUG    | ui.views.account_view:_update_group_counts:184 - 分组计数已更新: {2: 1, 1: 1}
2025-06-24 21:30:44.088 | INFO     | app.services.account_service:batch_auto_login:1192 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-06-24 21:30:44.088 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-24 21:30:44.089 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-24 21:30:44.090 | INFO     | app.services.account_service:batch_auto_login:1262 - 服务层：准备调用核心层处理 2 个账户的自动登录。
2025-06-24 21:30:44.090 | INFO     | app.services.account_service:batch_auto_login:1272 - 服务层：设置核心层任务超时为 120 秒。
2025-06-24 21:30:44.090 | INFO     | core.telegram.client_manager:batch_auto_login:1211 - 开始批量自动登录, 共 2 个账户, 最大并发 5
2025-06-24 21:30:44.090 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:254 - 创建客户端: H:\PyProject\TeleTest\APPDATA\sessions\+***********.session, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-06-24 21:30:44.091 | DEBUG    | core.telegram.client_manager:get_proxy:91 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-06-24 21:30:44.091 | DEBUG    | core.telegram.client_manager:get_proxy:138 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-06-24 21:30:44.097 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:254 - 创建客户端: H:\PyProject\TeleTest\APPDATA\sessions\+***********.session, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-06-24 21:30:44.097 | DEBUG    | core.telegram.client_manager:get_proxy:91 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-06-24 21:30:44.098 | DEBUG    | core.telegram.client_manager:get_proxy:138 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-06-24 21:30:44.119 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-24 21:30:44.136 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-24 21:30:51.631 | INFO     | core.telegram.client_manager:_connect_client:202 - 连接成功!
2025-06-24 21:30:52.819 | INFO     | core.telegram.client_manager:_verify_client_authorization:1186 - 账户已授权: +***********
2025-06-24 21:30:56.965 | INFO     | core.telegram.client_manager:_connect_client:202 - 连接成功!
2025-06-24 21:30:59.878 | INFO     | core.telegram.client_manager:_verify_client_authorization:1186 - 账户已授权: +***********
2025-06-24 21:31:01.886 | INFO     | core.telegram.client_manager:batch_auto_login:1360 - 批量登录完成: 总计 2, 成功 2, 失败 0
2025-06-24 21:31:02.124 | INFO     | app.services.account_service:batch_auto_login:1293 - 服务层：核心层登录完成: 成功 2, 失败 0 / 2
2025-06-24 21:31:05.118 | INFO     | app.services.account_service:refresh_account_info:595 - 刷新账户信息: 2
2025-06-24 21:31:05.118 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-24 21:31:05.128 | INFO     | core.telegram.user_manager:get_user_info:41 - 正在获取用户信息: +***********
2025-06-24 21:31:09.798 | INFO     | core.telegram.user_manager:get_user_info:66 - 获取用户信息成功: +***********
2025-06-24 21:31:10.144 | INFO     | data.repositories.account_repo:update_account:378 - 更新账户成功: 2
2025-06-24 21:31:10.154 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-24 21:31:20.214 | INFO     | ui.views.add_msg_task_view:_load_initial_data:136 - 加载初始数据
2025-06-24 21:31:20.271 | INFO     | ui.views.add_msg_task_view:_load_account_groups:146 - 加载账户分组
2025-06-24 21:31:20.271 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-24 21:31:20.422 | INFO     | app.services.account_service:get_all_groups:96 - 获取所有账户分组成功, 共 2 个
2025-06-24 21:31:20.422 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-24 21:31:20.423 | DEBUG    | ui.views.account_view:_on_groups_loaded:442 - 分组加载完成: 2个分组
2025-06-24 21:31:20.424 | INFO     | ui.views.add_msg_task_view:_load_account_groups:162 - 加载账户分组数据: 2个分组
2025-06-24 21:31:20.425 | INFO     | ui.views.add_msg_task_view:_load_accounts_by_group:177 - 根据分组ID加载账户: -1
2025-06-24 21:31:20.425 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-24 21:31:20.446 | INFO     | app.services.account_service:get_all_accounts:289 - 获取所有账户成功, 共 2 个
2025-06-24 21:31:20.446 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-24 21:31:20.449 | INFO     | ui.views.add_msg_task_view:_load_accounts_by_group:213 - 已加载 2 个账户到列表
2025-06-24 21:31:20.449 | DEBUG    | ui.views.account_view:_on_accounts_loaded:575 - 账户加载完成: 2个账户
2025-06-24 21:31:20.469 | DEBUG    | ui.views.account_view:_update_group_counts:184 - 分组计数已更新: {2: 1, 1: 1}
2025-06-24 21:31:36.951 | INFO     | ui.views.add_msg_task_view:_toggleSelectAllGroups:362 - 全选账户
2025-06-24 21:31:42.280 | INFO     | app.controllers.send_msg_controller:create_task:174 - 创建任务: 图片测试
2025-06-24 21:31:42.281 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-24 21:31:42.285 | INFO     | data.repositories.message_repo:add_task:36 - 消息任务已添加, ID: 3, 名称: 图片测试, 账户切换时间: 10-30秒, 消息间隔: 60-180秒
2025-06-24 21:31:42.293 | INFO     | app.controllers.send_msg_controller:_on_task_created:49 - 收到任务创建事件: 3
2025-06-24 21:31:42.293 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-24 21:31:42.293 | INFO     | app.controllers.send_msg_controller:create_task:180 - 任务创建成功: ID=3, 名称=图片测试
2025-06-24 21:31:42.311 | INFO     | ui.views.send_msg_view:_on_task_created:724 - 收到任务创建事件: 3
2025-06-24 21:31:42.359 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-24 21:31:42.484 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-24 21:31:42.534 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-24 21:31:42.585 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-24 21:31:42.627 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-24 21:31:42.628 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-24 21:31:42.645 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-24 21:31:44.989 | INFO     | app.controllers.send_msg_controller:start_task:148 - 启动任务: 3
2025-06-24 21:31:44.990 | INFO     | app.services.message_sending_service:start_task:212 - 启动群发任务 [Task ID: 3]
2025-06-24 21:31:44.990 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-24 21:31:45.004 | INFO     | data.repositories.message_repo:update_task:64 - 消息任务已更新, ID: 3, 账户切换时间: 10-30秒, 消息间隔: 60-180秒
2025-06-24 21:31:45.008 | INFO     | app.controllers.send_msg_controller:_on_task_status_changed:67 - 收到任务状态变更事件: 3 -> running
2025-06-24 21:31:45.009 | INFO     | ui.views.send_msg_view:_on_task_status_changed:748 - 收到任务状态变更事件: 3 -> running
2025-06-24 21:31:45.014 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-24 21:31:45.031 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-24 21:31:45.032 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-24 21:31:45.037 | DEBUG    | app.services.message_sending_service:start_task:269 - 处理任务批次 [Task ID: 3], 目标数量: 1
2025-06-24 21:31:45.037 | DEBUG    | app.services.message_sending_service:start_task:281 - 处理目标 [Task ID: 3] [1/1] Target: ddv6_com
2025-06-24 21:31:45.038 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-24 21:31:45.046 | DEBUG    | app.services.message_sending_service:start_task:294 - 选择账户 [Task ID: 3] Account: +***********
2025-06-24 21:31:45.046 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-24 21:31:45.048 | DEBUG    | app.services.message_sending_service:start_task:313 - 发送消息 [Task ID: 3] Account: +*********** -> Target: ddv6_com
2025-06-24 21:31:45.048 | INFO     | core.telegram.message_manager:send_text_message:43 - 账户 +*********** 准备向 ddv6_com 发送文本消息
2025-06-24 21:31:45.052 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-24 21:31:46.053 | INFO     | core.telegram.message_manager:send_text_message:56 - 账户 +*********** 成功向 ddv6_com 发送消息 ID: 118，解析模式: html
2025-06-24 21:31:46.292 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-24 21:31:46.297 | INFO     | data.repositories.message_repo:update_target_log_status:119 - 更新目标日志状态: 任务3 目标ddv6_com 状态success
2025-06-24 21:31:46.299 | DEBUG    | app.services.message_sending_service:start_task:323 - 发送成功 [Task ID: 3] Target: ddv6_com
2025-06-24 21:31:46.303 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-24 21:31:46.303 | DEBUG    | app.controllers.send_msg_controller:_on_task_progress_updated:73 - 收到任务进度更新事件: 3, 已处理: 2, 成功: 1, 失败: 0
2025-06-24 21:31:46.304 | DEBUG    | ui.views.send_msg_view:_on_task_progress_updated:879 - 收到任务进度更新: 3, 已处理: 2, 成功: 1, 失败: 0
2025-06-24 21:31:46.308 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-24 21:31:46.315 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-24 21:31:46.317 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-24 21:31:46.320 | INFO     | app.services.message_sending_service:start_task:355 - 任务完成 [Task ID: 3] 总数: 2, 成功: 1, 失败: 0
2025-06-24 21:31:46.324 | INFO     | data.repositories.message_repo:update_task:64 - 消息任务已更新, ID: 3, 账户切换时间: 10-30秒, 消息间隔: 60-180秒
2025-06-24 21:31:46.338 | INFO     | app.controllers.send_msg_controller:_on_task_status_changed:67 - 收到任务状态变更事件: 3 -> completed
2025-06-24 21:31:46.338 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-24 21:31:46.342 | INFO     | ui.views.send_msg_view:_on_task_status_changed:748 - 收到任务状态变更事件: 3 -> completed
2025-06-24 21:31:46.348 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-24 21:31:46.365 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-24 21:31:46.376 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-24 21:32:42.463 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-24 21:33:37.536 | INFO     | utils.logger:init_logger:98 - 日志系统初始化完成，默认级别: DEBUG，日志目录: H:\PyProject\TeleTest\APPDATA\logs
2025-06-24 21:33:38.651 | INFO     | data.database:init_database:31 - 初始化数据库连接: H:\PyProject\TeleTest\APPDATA\database\telegram_manager.db
2025-06-24 21:33:38.667 | DEBUG    | data.database:init_database:58 - 创建数据库表
2025-06-24 21:33:38.676 | INFO     | data.database:init_database:61 - 数据库初始化完成
2025-06-24 21:33:39.374 | DEBUG    | app.controllers.auth_controller:initialize_software:43 - 控制器: 初始化软件 app_id=1000, version=1.2.7
2025-06-24 21:33:39.374 | DEBUG    | core.auth.api_service:init_software:55 - 发送初始化请求: https://server.xile188.com/api/user/1000/windowns10/1.2.7/ini, params={}
2025-06-24 21:33:39.635 | DEBUG    | core.auth.api_service:init_software:66 - 初始化数据解密成功: {'new_content': '- 新增功能\n  - 1.新增图片上传功能\n  - 2.新增软件文档\n  - 3.新增软件自动更新，后续更新会自动检测版本更新，无需再下载更新包\n  - 4.新增设置账户信息及发信控制\n- 修复bug\n  - 删除监听任务时，不删除对应任务采集到的数据。\n  - 监听设置群无法发送到目标群\n  - 导出监听数据时应当导出单个任务而非所有数据\n- 优化记录\n  - 优化发信规则，直接调取符合要求的账户发送，无账户时才需要等待延时。\n  - 完善软件设置', 'new_url': None, 'notice': {'id': '2', 'content': '公告内容测试3', 'time': '**********'}}
2025-06-24 21:33:39.642 | INFO     | app.controllers.auth_controller:login:61 - 控制器: 用户登录 account=demo01
2025-06-24 21:33:42.611 | INFO     | core.auth.api_service:login:127 - 用户登录: account=demo01
2025-06-24 21:33:42.835 | INFO     | core.auth.api_service:get_user_info:347 - 获取用户信息
2025-06-24 21:33:43.125 | INFO     | core.auth.api_service:get_user_info:375 - 获取用户信息成功
2025-06-24 21:33:43.131 | INFO     | __main__:on_auth_completed:93 - 用户登录成功，正在启动主窗口...
2025-06-24 21:33:43.155 | INFO     | core.telegram.client_worker:__init__:61 - Telegram客户端工作线程初始化
2025-06-24 21:33:43.155 | INFO     | ui.main_window:_initialize_core_components:82 - MainWindow: 初始化核心组件...
2025-06-24 21:33:43.156 | INFO     | app.services.account_service:__init__:42 - 账户服务初始化
2025-06-24 21:33:43.156 | INFO     | core.telegram.client_worker:run:65 - Telegram客户端工作线程开始运行
2025-06-24 21:33:43.156 | INFO     | core.proxy.proxy_task_manager:run:284 - 代理任务管理器线程启动
2025-06-24 21:33:43.156 | INFO     | app.controllers.account_controller:__init__:81 - 账户控制器初始化
2025-06-24 21:33:43.157 | INFO     | app.services.monitor_service:__init__:39 - 初始化监控任务服务
2025-06-24 21:33:43.157 | INFO     | core.monitor.filter:__init__:26 - 初始化消息过滤器
2025-06-24 21:33:43.157 | INFO     | core.monitor.extractor:__init__:24 - 初始化关键词提取器
2025-06-24 21:33:43.157 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-06-24 21:33:43.158 | INFO     | core.monitor.notifier:__init__:90 - 初始化通知管理器
2025-06-24 21:33:43.158 | INFO     | core.telegram.client_manager:__init__:66 - Telegram客户端管理器初始化
2025-06-24 21:33:43.158 | INFO     | app.controllers.telegram_monitor_controller:__init__:64 - 初始化 Telegram 监控控制器
2025-06-24 21:33:43.158 | INFO     | core.telegram.user_manager:__init__:30 - Telegram用户管理器初始化
2025-06-24 21:33:43.158 | DEBUG    | app.controllers.telegram_monitor_controller:__init__:82 - 控制器初始化完成
2025-06-24 21:33:43.158 | INFO     | core.telegram.group_manager:__init__:36 - Telegram群组管理器初始化
2025-06-24 21:33:43.160 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-06-24 21:33:43.161 | INFO     | core.telegram.monitor_manager:__init__:26 - Telegram监控管理器初始化
2025-06-24 21:33:43.161 | INFO     | core.telegram.telegram_service:__init__:39 - Telegram服务初始化完成
2025-06-24 21:33:43.346 | INFO     | ui.views.monitor_view:__init__:48 - 初始化监控视图
2025-06-24 21:33:43.346 | DEBUG    | ui.views.monitor_view:_connect_signals:80 - 连接控制器信号
2025-06-24 21:33:43.540 | INFO     | ui.views.proxy_view:__init__:96 - 初始化视图，加载第一页代理数据
2025-06-24 21:33:43.756 | INFO     | __main__:on_auth_completed:97 - 主窗口已启动
2025-06-24 21:33:43.808 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:88 - 尝试连接监听日志信号...
2025-06-24 21:33:43.809 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:90 - 监听日志信号连接成功
2025-06-24 21:33:43.809 | INFO     | core.auth.api_service:verify_vip:425 - 会员验证接口调用
2025-06-24 21:33:43.810 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-24 21:33:43.814 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-24 21:33:43.817 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:145 - 加载监控任务列表 (搜索关键词: '')
2025-06-24 21:33:43.817 | DEBUG    | ui.views.monitor_view:_on_loading_started:620 - 加载中: 正在加载任务列表...
2025-06-24 21:33:43.817 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-24 21:33:43.823 | INFO     | app.services.proxy_service:refresh_proxies:712 - 刷新代理列表: offset=0, limit=20
2025-06-24 21:33:43.824 | INFO     | core.proxy.proxy_task_manager:submit_task:172 - 提交耗时任务到子线程: refresh_proxies_0_20
2025-06-24 21:33:43.824 | DEBUG    | app.services.proxy_service:get_proxies:500 - 获取代理列表: offset=0, limit=20
2025-06-24 21:33:43.824 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-24 21:33:43.824 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-24 21:33:43.828 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-24 21:33:43.848 | INFO     | app.controllers.invite_controller:get_invite_tasks:38 - 获取邀请任务列表, status=None
2025-06-24 21:33:43.848 | INFO     | app.services.invite_service:get_invite_tasks:121 - 获取邀请任务列表, status=None
2025-06-24 21:33:43.848 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-24 21:33:43.849 | INFO     | app.services.invite_service:task_initialize:39 - 初始化邀请服务，重置运行中的任务为暂停状态
2025-06-24 21:33:43.850 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-24 21:33:43.853 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-24 21:33:43.854 | INFO     | core.proxy.proxy_task_manager:task_wrapper:199 - 任务 refresh_proxies_0_20 完成
2025-06-24 21:33:43.974 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-24 21:33:43.980 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-24 21:33:43.982 | INFO     | app.services.invite_service:task_initialize:48 - 成功重置 0 个任务状态
2025-06-24 21:33:43.982 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-24 21:33:43.984 | DEBUG    | ui.views.monitor_view:_refresh_task_list:175 - 刷新任务列表，共2个任务
2025-06-24 21:33:43.990 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-24 21:33:43.991 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-24 21:33:44.002 | INFO     | app.services.account_service:get_all_groups:96 - 获取所有账户分组成功, 共 2 个
2025-06-24 21:33:44.002 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-24 21:33:44.002 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-24 21:33:44.005 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-24 21:33:44.006 | DEBUG    | ui.views.account_view:_on_groups_loaded:442 - 分组加载完成: 2个分组
2025-06-24 21:33:44.021 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-24 21:33:44.092 | INFO     | app.services.account_service:get_all_accounts:289 - 获取所有账户成功, 共 2 个
2025-06-24 21:33:44.092 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-24 21:33:44.092 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-24 21:33:44.094 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-24 21:33:44.095 | DEBUG    | ui.views.account_view:_on_accounts_loaded:575 - 账户加载完成: 2个账户
2025-06-24 21:33:44.115 | DEBUG    | ui.views.account_view:_update_group_counts:184 - 分组计数已更新: {2: 1, 1: 1}
2025-06-24 21:33:44.118 | INFO     | ui.views.account_view:_auto_login_accounts:630 - 开始调用控制器进行 2 个账户的自动登录。
2025-06-24 21:33:44.118 | INFO     | app.services.account_service:batch_auto_login:1153 - 服务层：开始批量自动登录 2 个账户, 最大并发: 5
2025-06-24 21:33:44.118 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-24 21:33:44.121 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-24 21:33:44.121 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-06-24 21:33:44.138 | INFO     | app.controllers.telegram_monitor_controller:_load_total_users_count_internal:191 - 成功加载总体统计数据: 总用户数 1845, 今日采集 0, 日均采集 142, 运行天数 13
2025-06-24 21:33:44.138 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:156 - 成功加载 2 个任务
2025-06-24 21:33:44.138 | DEBUG    | ui.views.monitor_view:_on_loading_finished:625 - 加载完成
2025-06-24 21:33:44.141 | INFO     | app.services.account_service:get_all_accounts:289 - 获取所有账户成功, 共 2 个
2025-06-24 21:33:44.141 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-24 21:33:44.141 | INFO     | app.services.account_service:batch_auto_login:1192 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-06-24 21:33:44.143 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-24 21:33:44.143 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-24 21:33:44.145 | DEBUG    | ui.views.account_view:_update_group_counts:184 - 分组计数已更新: {2: 1, 1: 1}
2025-06-24 21:33:44.145 | DEBUG    | ui.views.account_view:_init_cache:151 - 账户缓存初始化完成，共 2 个账户
2025-06-24 21:33:44.150 | DEBUG    | ui.views.account_view:_on_accounts_loaded:575 - 账户加载完成: 2个账户
2025-06-24 21:33:44.169 | DEBUG    | ui.views.account_view:_update_group_counts:184 - 分组计数已更新: {2: 1, 1: 1}
2025-06-24 21:33:44.176 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=True
2025-06-24 21:33:44.178 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=True
2025-06-24 21:33:44.181 | INFO     | ui.views.proxy_view:_load_initial_data:1044 - 初始数据加载完成，共 1 个代理
2025-06-24 21:33:44.183 | INFO     | app.services.account_service:batch_auto_login:1192 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-06-24 21:33:44.183 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-24 21:33:44.185 | INFO     | app.services.account_service:batch_auto_login:1262 - 服务层：准备调用核心层处理 2 个账户的自动登录。
2025-06-24 21:33:44.185 | INFO     | app.services.account_service:batch_auto_login:1272 - 服务层：设置核心层任务超时为 120 秒。
2025-06-24 21:33:44.185 | INFO     | core.telegram.client_manager:batch_auto_login:1211 - 开始批量自动登录, 共 2 个账户, 最大并发 5
2025-06-24 21:33:44.186 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:254 - 创建客户端: H:\PyProject\TeleTest\APPDATA\sessions\+***********.session, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-06-24 21:33:44.186 | DEBUG    | core.telegram.client_manager:get_proxy:91 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-06-24 21:33:44.186 | DEBUG    | core.telegram.client_manager:get_proxy:138 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-06-24 21:33:44.191 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:254 - 创建客户端: H:\PyProject\TeleTest\APPDATA\sessions\+***********.session, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-06-24 21:33:44.192 | DEBUG    | core.telegram.client_manager:get_proxy:91 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-06-24 21:33:44.193 | DEBUG    | core.telegram.client_manager:get_proxy:138 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-06-24 21:33:44.193 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-24 21:33:44.242 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-24 21:33:44.247 | WARNING  | core.auth.api_service:verify_vip:441 - 会员验证失败: 验证失败
2025-06-24 21:33:44.736 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-24 21:33:47.446 | INFO     | core.telegram.client_manager:_connect_client:202 - 连接成功!
2025-06-24 21:33:48.904 | INFO     | core.telegram.client_manager:_verify_client_authorization:1186 - 账户已授权: +***********
2025-06-24 21:33:49.929 | INFO     | core.telegram.client_manager:_connect_client:202 - 连接成功!
2025-06-24 21:33:50.934 | INFO     | core.telegram.client_manager:_verify_client_authorization:1186 - 账户已授权: +***********
2025-06-24 21:33:52.947 | INFO     | core.telegram.client_manager:batch_auto_login:1360 - 批量登录完成: 总计 2, 成功 2, 失败 0
2025-06-24 21:33:53.244 | INFO     | app.services.account_service:batch_auto_login:1293 - 服务层：核心层登录完成: 成功 2, 失败 0 / 2
2025-06-24 21:34:43.124 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-24 21:35:11.321 | INFO     | ui.views.add_msg_task_view:_load_initial_data:136 - 加载初始数据
2025-06-24 21:35:11.370 | INFO     | ui.views.add_msg_task_view:_load_account_groups:146 - 加载账户分组
2025-06-24 21:35:11.370 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-24 21:35:11.558 | INFO     | app.services.account_service:get_all_groups:96 - 获取所有账户分组成功, 共 2 个
2025-06-24 21:35:11.558 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-24 21:35:11.559 | DEBUG    | ui.views.account_view:_on_groups_loaded:442 - 分组加载完成: 2个分组
2025-06-24 21:35:11.560 | INFO     | ui.views.add_msg_task_view:_load_account_groups:162 - 加载账户分组数据: 2个分组
2025-06-24 21:35:11.560 | INFO     | ui.views.add_msg_task_view:_load_accounts_by_group:177 - 根据分组ID加载账户: -1
2025-06-24 21:35:11.560 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-24 21:35:11.570 | INFO     | app.services.account_service:get_all_accounts:289 - 获取所有账户成功, 共 2 个
2025-06-24 21:35:11.571 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-24 21:35:11.575 | INFO     | ui.views.add_msg_task_view:_load_accounts_by_group:213 - 已加载 2 个账户到列表
2025-06-24 21:35:11.575 | DEBUG    | ui.views.account_view:_on_accounts_loaded:575 - 账户加载完成: 2个账户
2025-06-24 21:35:11.594 | DEBUG    | ui.views.account_view:_update_group_counts:184 - 分组计数已更新: {2: 1, 1: 1}
2025-06-24 21:35:34.961 | INFO     | ui.views.add_msg_task_view:_toggleSelectAllGroups:362 - 全选账户
2025-06-24 21:35:37.331 | INFO     | ui.views.add_msg_task_view:_toggleSelectAllGroups:362 - 取消全选账户
2025-06-24 21:35:43.091 | INFO     | app.controllers.send_msg_controller:create_task:174 - 创建任务: 测03
2025-06-24 21:35:43.091 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-24 21:35:43.095 | INFO     | data.repositories.message_repo:add_task:36 - 消息任务已添加, ID: 4, 名称: 测03, 账户切换时间: 10-30秒, 消息间隔: 60-180秒
2025-06-24 21:35:43.103 | INFO     | app.controllers.send_msg_controller:_on_task_created:49 - 收到任务创建事件: 4
2025-06-24 21:35:43.104 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-24 21:35:43.104 | INFO     | app.controllers.send_msg_controller:create_task:180 - 任务创建成功: ID=4, 名称=测03
2025-06-24 21:35:43.122 | INFO     | ui.views.send_msg_view:_on_task_created:724 - 收到任务创建事件: 4
2025-06-24 21:35:43.175 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-24 21:35:43.177 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-24 21:35:43.380 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-24 21:35:43.434 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-24 21:35:43.480 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-24 21:35:43.481 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-24 21:35:43.506 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-24 21:35:45.267 | INFO     | app.controllers.send_msg_controller:start_task:148 - 启动任务: 4
2025-06-24 21:35:45.267 | INFO     | app.services.message_sending_service:start_task:212 - 启动群发任务 [Task ID: 4]
2025-06-24 21:35:45.267 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-24 21:35:45.278 | INFO     | data.repositories.message_repo:update_task:64 - 消息任务已更新, ID: 4, 账户切换时间: 10-30秒, 消息间隔: 60-180秒
2025-06-24 21:35:45.283 | INFO     | app.controllers.send_msg_controller:_on_task_status_changed:67 - 收到任务状态变更事件: 4 -> running
2025-06-24 21:35:45.285 | INFO     | ui.views.send_msg_view:_on_task_status_changed:748 - 收到任务状态变更事件: 4 -> running
2025-06-24 21:35:45.290 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-24 21:35:45.308 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-24 21:35:45.309 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-24 21:35:45.314 | DEBUG    | app.services.message_sending_service:start_task:269 - 处理任务批次 [Task ID: 4], 目标数量: 1
2025-06-24 21:35:45.314 | DEBUG    | app.services.message_sending_service:start_task:281 - 处理目标 [Task ID: 4] [1/1] Target: ddv6_com
2025-06-24 21:35:45.314 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-24 21:35:45.318 | DEBUG    | app.services.message_sending_service:start_task:294 - 选择账户 [Task ID: 4] Account: +***********
2025-06-24 21:35:45.318 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-24 21:35:45.319 | DEBUG    | app.services.message_sending_service:start_task:313 - 发送消息 [Task ID: 4] Account: +*********** -> Target: ddv6_com
2025-06-24 21:35:45.319 | INFO     | core.telegram.message_manager:send_text_message:43 - 账户 +*********** 准备向 ddv6_com 发送文本消息<img src="C:/Users/<USER>/Pictures/logo-130718.png" width="122" height="40" /> 图片测试测试图片测试测试图片测试测试
图片测试测试图片测试测试
2025-06-24 21:35:45.330 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-24 21:35:46.376 | INFO     | core.telegram.message_manager:send_text_message:56 - 账户 +*********** 成功向 ddv6_com 发送消息 ID: 119，解析模式: html
2025-06-24 21:35:46.564 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-24 21:35:46.569 | INFO     | data.repositories.message_repo:update_target_log_status:119 - 更新目标日志状态: 任务4 目标ddv6_com 状态success
2025-06-24 21:35:46.574 | DEBUG    | app.services.message_sending_service:start_task:323 - 发送成功 [Task ID: 4] Target: ddv6_com
2025-06-24 21:35:46.579 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-24 21:35:46.580 | DEBUG    | app.controllers.send_msg_controller:_on_task_progress_updated:73 - 收到任务进度更新事件: 4, 已处理: 2, 成功: 1, 失败: 0
2025-06-24 21:35:46.580 | DEBUG    | ui.views.send_msg_view:_on_task_progress_updated:879 - 收到任务进度更新: 4, 已处理: 2, 成功: 1, 失败: 0
2025-06-24 21:35:46.586 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-24 21:35:46.594 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-24 21:35:46.597 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-24 21:35:46.604 | INFO     | app.services.message_sending_service:start_task:355 - 任务完成 [Task ID: 4] 总数: 2, 成功: 1, 失败: 0
2025-06-24 21:35:46.609 | INFO     | data.repositories.message_repo:update_task:64 - 消息任务已更新, ID: 4, 账户切换时间: 10-30秒, 消息间隔: 60-180秒
2025-06-24 21:35:46.621 | INFO     | app.controllers.send_msg_controller:_on_task_status_changed:67 - 收到任务状态变更事件: 4 -> completed
2025-06-24 21:35:46.621 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-24 21:35:46.625 | INFO     | ui.views.send_msg_view:_on_task_status_changed:748 - 收到任务状态变更事件: 4 -> completed
2025-06-24 21:35:46.632 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-24 21:35:46.662 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-24 21:35:46.672 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-24 21:36:43.121 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-24 21:37:43.125 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-24 21:38:43.125 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-24 21:39:43.121 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-24 21:40:43.124 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-24 21:41:43.125 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-24 21:42:43.119 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-24 21:43:43.122 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-24 21:44:43.125 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-24 21:45:43.122 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-24 21:46:43.123 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-24 21:47:43.121 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-24 21:48:43.121 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-24 21:49:08.635 | INFO     | ui.main_window:closeEvent:346 - MainWindow: 接收到关闭事件
2025-06-24 21:49:08.644 | INFO     | ui.main_window:_cleanup_before_quit:238 - MainWindow: 执行清理资源...
2025-06-24 21:49:08.644 | INFO     | core.telegram.client_worker:stop:108 - 正在停止Telegram客户端工作线程
2025-06-24 21:49:08.651 | INFO     | core.telegram.client_manager:cleanup_async:1131 - 开始清理资源并断开所有连接
2025-06-24 21:49:08.651 | INFO     | core.telegram.client_manager:disconnect_all_clients:716 - 正在断开所有客户端连接
2025-06-24 21:49:08.652 | INFO     | core.telegram.client_manager:_safe_disconnect:737 - 正在断开客户端连接: +***********
2025-06-24 21:49:08.652 | INFO     | core.telegram.client_manager:_safe_disconnect:737 - 正在断开客户端连接: +***********
2025-06-24 21:49:08.662 | INFO     | core.telegram.client_manager:_safe_disconnect:741 - 断开客户端连接成功: +***********
2025-06-24 21:49:08.663 | INFO     | core.telegram.client_manager:_safe_disconnect:741 - 断开客户端连接成功: +***********
2025-06-24 21:49:08.663 | INFO     | core.telegram.client_manager:cleanup_async:1150 - 资源清理完成
2025-06-24 21:49:09.168 | INFO     | core.telegram.client_manager:cleanup_async:1131 - 开始清理资源并断开所有连接
2025-06-24 21:49:09.168 | INFO     | core.telegram.client_manager:disconnect_all_clients:716 - 正在断开所有客户端连接
2025-06-24 21:49:09.168 | INFO     | core.telegram.client_manager:cleanup_async:1150 - 资源清理完成
2025-06-24 21:49:09.669 | INFO     | core.telegram.client_worker:run:97 - Telegram客户端工作线程已退出
2025-06-24 21:49:09.669 | INFO     | core.telegram.client_worker:stop:122 - Telegram客户端工作线程已停止
2025-06-24 21:49:09.669 | INFO     | ui.main_window:_cleanup_before_quit:247 - TelegramClientWorker 已停止。
2025-06-24 21:49:09.670 | INFO     | ui.main_window:_cleanup_before_quit:251 - MainWindow 清理完成
2025-06-24 21:49:09.687 | INFO     | __main__:main:111 - 应用程序已正常退出
