2025-06-27 17:21:48.295 | INFO     | utils.logger:init_logger:98 - 日志系统初始化完成，默认级别: DEBUG，日志目录: H:\PyProject\TeleTest\APPDATA\logs
2025-06-27 17:21:50.468 | INFO     | data.database:init_database:31 - 初始化数据库连接: H:\PyProject\TeleTest\APPDATA\database\telegram_manager.db
2025-06-27 17:21:50.501 | DEBUG    | data.database:init_database:58 - 创建数据库表
2025-06-27 17:21:50.511 | INFO     | data.database:init_database:61 - 数据库初始化完成
2025-06-27 17:21:51.618 | DEBUG    | app.controllers.auth_controller:initialize_software:43 - 控制器: 初始化软件 app_id=1000, version=1.2.7
2025-06-27 17:21:51.618 | DEBUG    | core.auth.api_service:init_software:55 - 发送初始化请求: https://server.xile188.com/api/user/1000/windowns10/1.2.7/ini, params={}
2025-06-27 17:21:52.059 | DEBUG    | core.auth.api_service:init_software:66 - 初始化数据解密成功: {'new_content': '- 新增功能\n  - 1.新增图片上传功能\n  - 2.新增软件文档\n  - 3.新增软件自动更新，后续更新会自动检测版本更新，无需再下载更新包\n  - 4.新增设置账户信息及发信控制\n- 修复bug\n  - 删除监听任务时，不删除对应任务采集到的数据。\n  - 监听设置群无法发送到目标群\n  - 导出监听数据时应当导出单个任务而非所有数据\n- 优化记录\n  - 优化发信规则，直接调取符合要求的账户发送，无账户时才需要等待延时。\n  - 完善软件设置', 'new_url': None, 'notice': {'id': '2', 'content': '公告内容测试3', 'time': '**********'}}
2025-06-27 17:21:52.070 | INFO     | app.controllers.auth_controller:login:61 - 控制器: 用户登录 account=demo01
2025-06-27 17:21:55.353 | INFO     | core.auth.api_service:login:127 - 用户登录: account=demo01
2025-06-27 17:21:55.610 | INFO     | core.auth.api_service:get_user_info:347 - 获取用户信息
2025-06-27 17:21:55.819 | INFO     | core.auth.api_service:get_user_info:375 - 获取用户信息成功
2025-06-27 17:21:55.826 | INFO     | __main__:on_auth_completed:93 - 用户登录成功，正在启动主窗口...
2025-06-27 17:21:55.854 | INFO     | core.telegram.client_worker:__init__:61 - Telegram客户端工作线程初始化
2025-06-27 17:21:55.855 | INFO     | ui.main_window:_initialize_core_components:107 - MainWindow: 初始化核心组件...
2025-06-27 17:21:55.855 | INFO     | core.telegram.client_worker:run:65 - Telegram客户端工作线程开始运行
2025-06-27 17:21:55.856 | INFO     | app.services.account_service:__init__:42 - 账户服务初始化
2025-06-27 17:21:55.856 | INFO     | core.proxy.proxy_task_manager:run:284 - 代理任务管理器线程启动
2025-06-27 17:21:55.856 | INFO     | app.controllers.account_controller:__init__:81 - 账户控制器初始化
2025-06-27 17:21:55.856 | INFO     | app.services.monitor_service:__init__:39 - 初始化监控任务服务
2025-06-27 17:21:55.856 | INFO     | core.monitor.filter:__init__:26 - 初始化消息过滤器
2025-06-27 17:21:55.857 | INFO     | core.monitor.extractor:__init__:24 - 初始化关键词提取器
2025-06-27 17:21:55.857 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-06-27 17:21:55.857 | INFO     | core.monitor.notifier:__init__:90 - 初始化通知管理器
2025-06-27 17:21:55.857 | INFO     | app.controllers.telegram_monitor_controller:__init__:64 - 初始化 Telegram 监控控制器
2025-06-27 17:21:55.858 | DEBUG    | app.controllers.telegram_monitor_controller:__init__:82 - 控制器初始化完成
2025-06-27 17:21:55.858 | INFO     | core.telegram.client_manager:__init__:66 - Telegram客户端管理器初始化
2025-06-27 17:21:55.860 | INFO     | core.telegram.user_manager:__init__:30 - Telegram用户管理器初始化
2025-06-27 17:21:55.861 | INFO     | core.telegram.group_manager:__init__:36 - Telegram群组管理器初始化
2025-06-27 17:21:55.862 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-06-27 17:21:55.862 | INFO     | core.telegram.monitor_manager:__init__:26 - Telegram监控管理器初始化
2025-06-27 17:21:55.862 | INFO     | core.telegram.telegram_service:__init__:39 - Telegram服务初始化完成
2025-06-27 17:21:55.955 | INFO     | core.auth.api_service:verify_vip:425 - 会员验证接口调用
2025-06-27 17:21:55.955 | INFO     | core.auth.api_service:verify_vip:425 - 会员验证接口调用
2025-06-27 17:21:55.956 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 17:21:55.982 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:88 - 尝试连接监听日志信号...
2025-06-27 17:21:55.983 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:90 - 监听日志信号连接成功
2025-06-27 17:21:55.988 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 17:21:56.190 | ERROR    | utils.client_http:post:94 - 网络请求异常: [Errno 1] [SSL: APPLICATION_DATA_AFTER_CLOSE_NOTIFY] application data after close notify (_ssl.c:2702)
2025-06-27 17:21:56.194 | INFO     | core.auth.api_service:verify_vip:439 - 会员验证成功
2025-06-27 17:22:55.721 | INFO     | utils.logger:init_logger:98 - 日志系统初始化完成，默认级别: DEBUG，日志目录: H:\PyProject\TeleTest\APPDATA\logs
2025-06-27 17:23:05.019 | INFO     | data.database:init_database:31 - 初始化数据库连接: H:\PyProject\TeleTest\APPDATA\database\telegram_manager.db
2025-06-27 17:23:05.065 | DEBUG    | data.database:init_database:58 - 创建数据库表
2025-06-27 17:23:05.085 | INFO     | data.database:init_database:61 - 数据库初始化完成
2025-06-27 17:23:06.243 | DEBUG    | app.controllers.auth_controller:initialize_software:43 - 控制器: 初始化软件 app_id=1000, version=1.2.7
2025-06-27 17:23:06.243 | DEBUG    | core.auth.api_service:init_software:55 - 发送初始化请求: https://server.xile188.com/api/user/1000/windowns10/1.2.7/ini, params={}
2025-06-27 17:23:06.489 | DEBUG    | core.auth.api_service:init_software:66 - 初始化数据解密成功: {'new_content': '- 新增功能\n  - 1.新增图片上传功能\n  - 2.新增软件文档\n  - 3.新增软件自动更新，后续更新会自动检测版本更新，无需再下载更新包\n  - 4.新增设置账户信息及发信控制\n- 修复bug\n  - 删除监听任务时，不删除对应任务采集到的数据。\n  - 监听设置群无法发送到目标群\n  - 导出监听数据时应当导出单个任务而非所有数据\n- 优化记录\n  - 优化发信规则，直接调取符合要求的账户发送，无账户时才需要等待延时。\n  - 完善软件设置', 'new_url': None, 'notice': {'id': '2', 'content': '公告内容测试3', 'time': '**********'}}
2025-06-27 17:23:06.500 | INFO     | app.controllers.auth_controller:login:61 - 控制器: 用户登录 account=demo01
2025-06-27 17:23:09.503 | INFO     | core.auth.api_service:login:127 - 用户登录: account=demo01
2025-06-27 17:23:09.792 | INFO     | core.auth.api_service:get_user_info:347 - 获取用户信息
2025-06-27 17:23:10.004 | INFO     | core.auth.api_service:get_user_info:375 - 获取用户信息成功
2025-06-27 17:23:10.012 | INFO     | __main__:on_auth_completed:93 - 用户登录成功，正在启动主窗口...
2025-06-27 17:23:10.042 | INFO     | core.telegram.client_worker:__init__:61 - Telegram客户端工作线程初始化
2025-06-27 17:23:10.043 | INFO     | ui.main_window:_initialize_core_components:107 - MainWindow: 初始化核心组件...
2025-06-27 17:23:10.043 | INFO     | core.telegram.client_worker:run:65 - Telegram客户端工作线程开始运行
2025-06-27 17:23:10.044 | INFO     | app.services.account_service:__init__:42 - 账户服务初始化
2025-06-27 17:23:10.044 | INFO     | core.proxy.proxy_task_manager:run:284 - 代理任务管理器线程启动
2025-06-27 17:23:10.045 | INFO     | app.controllers.account_controller:__init__:81 - 账户控制器初始化
2025-06-27 17:23:10.046 | INFO     | app.services.monitor_service:__init__:39 - 初始化监控任务服务
2025-06-27 17:23:10.046 | INFO     | core.monitor.filter:__init__:26 - 初始化消息过滤器
2025-06-27 17:23:10.046 | INFO     | core.monitor.extractor:__init__:24 - 初始化关键词提取器
2025-06-27 17:23:10.047 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-06-27 17:23:10.047 | INFO     | core.monitor.notifier:__init__:90 - 初始化通知管理器
2025-06-27 17:23:10.048 | INFO     | core.telegram.client_manager:__init__:66 - Telegram客户端管理器初始化
2025-06-27 17:23:10.048 | INFO     | app.controllers.telegram_monitor_controller:__init__:64 - 初始化 Telegram 监控控制器
2025-06-27 17:23:10.049 | INFO     | core.telegram.user_manager:__init__:30 - Telegram用户管理器初始化
2025-06-27 17:23:10.049 | DEBUG    | app.controllers.telegram_monitor_controller:__init__:82 - 控制器初始化完成
2025-06-27 17:23:10.049 | INFO     | core.telegram.group_manager:__init__:36 - Telegram群组管理器初始化
2025-06-27 17:23:10.051 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-06-27 17:23:10.052 | INFO     | core.telegram.monitor_manager:__init__:26 - Telegram监控管理器初始化
2025-06-27 17:23:10.052 | INFO     | core.telegram.telegram_service:__init__:39 - Telegram服务初始化完成
2025-06-27 17:23:10.271 | INFO     | ui.views.monitor_view:__init__:48 - 初始化监控视图
2025-06-27 17:23:10.272 | DEBUG    | ui.views.monitor_view:_connect_signals:80 - 连接控制器信号
2025-06-27 17:23:10.474 | INFO     | ui.views.proxy_view:__init__:96 - 初始化视图，加载第一页代理数据
2025-06-27 17:23:10.728 | INFO     | __main__:on_auth_completed:97 - 主窗口已启动
2025-06-27 17:23:10.789 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:88 - 尝试连接监听日志信号...
2025-06-27 17:23:10.790 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:90 - 监听日志信号连接成功
2025-06-27 17:23:10.790 | INFO     | core.auth.api_service:verify_vip:425 - 会员验证接口调用
2025-06-27 17:23:10.791 | INFO     | core.auth.api_service:verify_vip:425 - 会员验证接口调用
2025-06-27 17:23:10.792 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 17:23:10.797 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 17:23:10.801 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:145 - 加载监控任务列表 (搜索关键词: '')
2025-06-27 17:23:10.802 | DEBUG    | ui.views.monitor_view:_on_loading_started:620 - 加载中: 正在加载任务列表...
2025-06-27 17:23:10.802 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 17:23:10.810 | INFO     | app.services.proxy_service:refresh_proxies:712 - 刷新代理列表: offset=0, limit=20
2025-06-27 17:23:10.810 | INFO     | core.proxy.proxy_task_manager:submit_task:172 - 提交耗时任务到子线程: refresh_proxies_0_20
2025-06-27 17:23:10.810 | DEBUG    | app.services.proxy_service:get_proxies:500 - 获取代理列表: offset=0, limit=20
2025-06-27 17:23:10.810 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 17:23:10.812 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 17:23:10.816 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 17:23:10.856 | INFO     | app.controllers.invite_controller:get_invite_tasks:38 - 获取邀请任务列表, status=None
2025-06-27 17:23:10.862 | INFO     | app.services.invite_service:get_invite_tasks:121 - 获取邀请任务列表, status=None
2025-06-27 17:23:10.873 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 17:23:10.875 | INFO     | app.services.invite_service:task_initialize:39 - 初始化邀请服务，重置运行中的任务为暂停状态
2025-06-27 17:23:10.875 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 17:23:10.883 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 17:23:10.885 | INFO     | core.proxy.proxy_task_manager:task_wrapper:199 - 任务 refresh_proxies_0_20 完成
2025-06-27 17:23:11.000 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 17:23:11.001 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 17:23:11.022 | DEBUG    | ui.views.monitor_view:_refresh_task_list:175 - 刷新任务列表，共0个任务
2025-06-27 17:23:11.023 | INFO     | ui.views.monitor_view:_refresh_task_list:189 - 没有找到监控任务
2025-06-27 17:23:11.023 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 17:23:11.029 | INFO     | app.services.invite_service:task_initialize:48 - 成功重置 0 个任务状态
2025-06-27 17:23:11.029 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 17:23:11.034 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 17:23:11.042 | INFO     | app.services.account_service:get_all_groups:96 - 获取所有账户分组成功, 共 2 个
2025-06-27 17:23:11.042 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 17:23:11.043 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 17:23:11.046 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 17:23:11.054 | DEBUG    | ui.views.account_view:_on_groups_loaded:446 - 分组加载完成: 2个分组
2025-06-27 17:23:11.068 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 17:23:11.148 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 17:23:11.149 | INFO     | app.services.account_service:get_all_accounts:290 - 获取所有账户成功, 共 2 个
2025-06-27 17:23:11.149 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 17:23:11.153 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 17:23:11.155 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 17:23:11.157 | DEBUG    | ui.views.account_view:_on_accounts_loaded:579 - 账户加载完成: 2个账户
2025-06-27 17:23:11.176 | DEBUG    | ui.views.account_view:_update_group_counts:185 - 分组计数已更新: {2: 1, 1: 1}
2025-06-27 17:23:11.181 | INFO     | ui.views.account_view:_auto_login_accounts:634 - 开始调用控制器进行 2 个账户的自动登录。
2025-06-27 17:23:11.181 | INFO     | app.services.account_service:batch_auto_login:1165 - 服务层：开始批量自动登录 2 个账户, 最大并发: 5
2025-06-27 17:23:11.181 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 17:23:11.185 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-06-27 17:23:11.213 | INFO     | app.controllers.telegram_monitor_controller:_load_total_users_count_internal:191 - 成功加载总体统计数据: 总用户数 23, 今日采集 0, 日均采集 23, 运行天数 1
2025-06-27 17:23:11.214 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:156 - 成功加载 0 个任务
2025-06-27 17:23:11.214 | DEBUG    | ui.views.monitor_view:_on_loading_finished:625 - 加载完成
2025-06-27 17:23:11.217 | ERROR    | utils.client_http:post:94 - 网络请求异常: [Errno 1] [SSL: APPLICATION_DATA_AFTER_CLOSE_NOTIFY] application data after close notify (_ssl.c:2702)
2025-06-27 17:29:24.918 | INFO     | utils.logger:init_logger:98 - 日志系统初始化完成，默认级别: DEBUG，日志目录: H:\PyProject\TeleTest\APPDATA\logs
2025-06-27 17:29:33.117 | INFO     | data.database:init_database:31 - 初始化数据库连接: H:\PyProject\TeleTest\APPDATA\database\telegram_manager.db
2025-06-27 17:29:33.149 | DEBUG    | data.database:init_database:58 - 创建数据库表
2025-06-27 17:29:33.170 | INFO     | data.database:init_database:61 - 数据库初始化完成
2025-06-27 17:29:34.186 | DEBUG    | app.controllers.auth_controller:initialize_software:43 - 控制器: 初始化软件 app_id=1000, version=1.2.7
2025-06-27 17:29:34.187 | DEBUG    | core.auth.api_service:init_software:55 - 发送初始化请求: https://server.xile188.com/api/user/1000/windowns10/1.2.7/ini, params={}
2025-06-27 17:29:34.537 | DEBUG    | core.auth.api_service:init_software:66 - 初始化数据解密成功: {'new_content': '- 新增功能\n  - 1.新增图片上传功能\n  - 2.新增软件文档\n  - 3.新增软件自动更新，后续更新会自动检测版本更新，无需再下载更新包\n  - 4.新增设置账户信息及发信控制\n- 修复bug\n  - 删除监听任务时，不删除对应任务采集到的数据。\n  - 监听设置群无法发送到目标群\n  - 导出监听数据时应当导出单个任务而非所有数据\n- 优化记录\n  - 优化发信规则，直接调取符合要求的账户发送，无账户时才需要等待延时。\n  - 完善软件设置', 'new_url': None, 'notice': {'id': '2', 'content': '公告内容测试3', 'time': '**********'}}
2025-06-27 17:29:34.544 | INFO     | app.controllers.auth_controller:login:61 - 控制器: 用户登录 account=demo01
2025-06-27 17:29:37.631 | INFO     | core.auth.api_service:login:127 - 用户登录: account=demo01
2025-06-27 17:29:37.889 | INFO     | core.auth.api_service:get_user_info:347 - 获取用户信息
2025-06-27 17:29:38.178 | INFO     | core.auth.api_service:get_user_info:375 - 获取用户信息成功
2025-06-27 17:29:38.185 | INFO     | __main__:on_auth_completed:93 - 用户登录成功，正在启动主窗口...
2025-06-27 17:29:38.211 | INFO     | core.telegram.client_worker:__init__:61 - Telegram客户端工作线程初始化
2025-06-27 17:29:38.212 | INFO     | ui.main_window:_initialize_core_components:107 - MainWindow: 初始化核心组件...
2025-06-27 17:29:38.213 | INFO     | app.services.account_service:__init__:42 - 账户服务初始化
2025-06-27 17:29:38.213 | INFO     | app.controllers.account_controller:__init__:81 - 账户控制器初始化
2025-06-27 17:29:38.213 | INFO     | app.services.monitor_service:__init__:39 - 初始化监控任务服务
2025-06-27 17:29:38.213 | INFO     | core.monitor.filter:__init__:26 - 初始化消息过滤器
2025-06-27 17:29:38.214 | INFO     | core.proxy.proxy_task_manager:run:284 - 代理任务管理器线程启动
2025-06-27 17:29:38.214 | INFO     | core.telegram.client_worker:run:65 - Telegram客户端工作线程开始运行
2025-06-27 17:29:38.214 | INFO     | core.monitor.extractor:__init__:24 - 初始化关键词提取器
2025-06-27 17:29:38.215 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-06-27 17:29:38.215 | INFO     | core.monitor.notifier:__init__:90 - 初始化通知管理器
2025-06-27 17:29:38.216 | INFO     | app.controllers.telegram_monitor_controller:__init__:64 - 初始化 Telegram 监控控制器
2025-06-27 17:29:38.216 | DEBUG    | app.controllers.telegram_monitor_controller:__init__:82 - 控制器初始化完成
2025-06-27 17:29:38.216 | INFO     | core.telegram.client_manager:__init__:66 - Telegram客户端管理器初始化
2025-06-27 17:29:38.217 | INFO     | core.telegram.user_manager:__init__:30 - Telegram用户管理器初始化
2025-06-27 17:29:38.218 | INFO     | core.telegram.group_manager:__init__:36 - Telegram群组管理器初始化
2025-06-27 17:29:38.220 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-06-27 17:29:38.221 | INFO     | core.telegram.monitor_manager:__init__:26 - Telegram监控管理器初始化
2025-06-27 17:29:38.221 | INFO     | core.telegram.telegram_service:__init__:39 - Telegram服务初始化完成
2025-06-27 17:29:38.439 | INFO     | ui.views.monitor_view:__init__:48 - 初始化监控视图
2025-06-27 17:29:38.440 | DEBUG    | ui.views.monitor_view:_connect_signals:80 - 连接控制器信号
2025-06-27 17:29:38.655 | INFO     | ui.views.proxy_view:__init__:96 - 初始化视图，加载第一页代理数据
2025-06-27 17:29:38.920 | INFO     | __main__:on_auth_completed:97 - 主窗口已启动
2025-06-27 17:29:38.961 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:88 - 尝试连接监听日志信号...
2025-06-27 17:29:38.961 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:90 - 监听日志信号连接成功
2025-06-27 17:29:38.961 | INFO     | core.auth.api_service:verify_vip:425 - 会员验证接口调用
2025-06-27 17:29:38.963 | INFO     | core.auth.api_service:verify_vip:425 - 会员验证接口调用
2025-06-27 17:29:38.963 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 17:29:38.969 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 17:29:38.973 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:145 - 加载监控任务列表 (搜索关键词: '')
2025-06-27 17:29:38.974 | DEBUG    | ui.views.monitor_view:_on_loading_started:620 - 加载中: 正在加载任务列表...
2025-06-27 17:29:38.974 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 17:29:38.981 | INFO     | app.services.proxy_service:refresh_proxies:712 - 刷新代理列表: offset=0, limit=20
2025-06-27 17:29:38.981 | INFO     | core.proxy.proxy_task_manager:submit_task:172 - 提交耗时任务到子线程: refresh_proxies_0_20
2025-06-27 17:29:38.982 | DEBUG    | app.services.proxy_service:get_proxies:500 - 获取代理列表: offset=0, limit=20
2025-06-27 17:29:38.982 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 17:29:38.984 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 17:29:38.987 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 17:29:38.994 | INFO     | app.controllers.invite_controller:get_invite_tasks:38 - 获取邀请任务列表, status=None
2025-06-27 17:29:38.994 | INFO     | app.services.invite_service:get_invite_tasks:121 - 获取邀请任务列表, status=None
2025-06-27 17:29:38.994 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 17:29:39.018 | INFO     | app.services.invite_service:task_initialize:39 - 初始化邀请服务，重置运行中的任务为暂停状态
2025-06-27 17:29:39.047 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 17:29:39.056 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 17:29:39.059 | INFO     | core.proxy.proxy_task_manager:task_wrapper:199 - 任务 refresh_proxies_0_20 完成
2025-06-27 17:29:39.160 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 17:29:39.173 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 17:29:39.181 | DEBUG    | ui.views.monitor_view:_refresh_task_list:175 - 刷新任务列表，共0个任务
2025-06-27 17:29:39.181 | INFO     | ui.views.monitor_view:_refresh_task_list:189 - 没有找到监控任务
2025-06-27 17:29:39.182 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 17:29:39.187 | INFO     | app.services.invite_service:task_initialize:48 - 成功重置 0 个任务状态
2025-06-27 17:29:39.188 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 17:29:39.198 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 17:29:39.206 | INFO     | app.services.account_service:get_all_groups:96 - 获取所有账户分组成功, 共 2 个
2025-06-27 17:29:39.206 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 17:29:39.207 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 17:29:39.210 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 17:29:39.217 | DEBUG    | ui.views.account_view:_on_groups_loaded:446 - 分组加载完成: 2个分组
2025-06-27 17:29:39.233 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 17:29:39.318 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 17:29:39.320 | INFO     | app.services.account_service:get_all_accounts:290 - 获取所有账户成功, 共 2 个
2025-06-27 17:29:39.320 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 17:29:39.321 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 17:29:39.323 | INFO     | app.controllers.telegram_monitor_controller:_load_total_users_count_internal:191 - 成功加载总体统计数据: 总用户数 23, 今日采集 0, 日均采集 23, 运行天数 1
2025-06-27 17:29:39.324 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:156 - 成功加载 0 个任务
2025-06-27 17:29:39.324 | DEBUG    | ui.views.monitor_view:_on_loading_finished:625 - 加载完成
2025-06-27 17:29:39.326 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 17:29:39.327 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-06-27 17:29:39.332 | DEBUG    | ui.views.account_view:_on_accounts_loaded:579 - 账户加载完成: 2个账户
2025-06-27 17:29:39.351 | DEBUG    | ui.views.account_view:_update_group_counts:185 - 分组计数已更新: {2: 1, 1: 1}
2025-06-27 17:29:39.356 | INFO     | ui.views.account_view:_auto_login_accounts:634 - 开始调用控制器进行 2 个账户的自动登录。
2025-06-27 17:29:39.356 | INFO     | app.services.account_service:batch_auto_login:1165 - 服务层：开始批量自动登录 2 个账户, 最大并发: 5
2025-06-27 17:29:39.356 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 17:29:39.386 | ERROR    | utils.client_http:post:94 - 网络请求异常: [Errno 1] [SSL: APPLICATION_DATA_AFTER_CLOSE_NOTIFY] application data after close notify (_ssl.c:2702)
2025-06-27 17:30:39.853 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-27 17:31:07.889 | WARNING  | core.auth.api_service:verify_vip:441 - 会员验证失败: 客户端时间校验失败
2025-06-27 17:31:07.910 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-06-27 17:31:07.912 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-06-27 17:31:07.913 | INFO     | ui.views.proxy_view:_load_initial_data:1044 - 初始数据加载完成，共 1 个代理
2025-06-27 17:31:08.147 | INFO     | app.services.account_service:get_all_accounts:290 - 获取所有账户成功, 共 2 个
2025-06-27 17:31:08.148 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 17:31:08.150 | INFO     | app.services.account_service:batch_auto_login:1204 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-06-27 17:31:08.150 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 17:31:08.190 | DEBUG    | ui.views.account_view:_update_group_counts:185 - 分组计数已更新: {2: 1, 1: 1}
2025-06-27 17:31:08.191 | DEBUG    | ui.views.account_view:_init_cache:152 - 账户缓存初始化完成，共 2 个账户
2025-06-27 17:31:08.195 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 17:31:08.197 | DEBUG    | ui.views.account_view:_on_accounts_loaded:579 - 账户加载完成: 2个账户
2025-06-27 17:31:08.217 | DEBUG    | ui.views.account_view:_update_group_counts:185 - 分组计数已更新: {2: 1, 1: 1}
2025-06-27 17:31:08.228 | INFO     | app.services.account_service:batch_auto_login:1204 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-06-27 17:31:08.228 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 17:31:08.230 | INFO     | app.services.account_service:batch_auto_login:1274 - 服务层：准备调用核心层处理 2 个账户的自动登录。
2025-06-27 17:31:08.231 | INFO     | core.telegram.client_manager:batch_auto_login:1216 - 开始批量自动登录, 共 2 个账户, 最大并发 5
2025-06-27 17:31:08.232 | INFO     | app.services.account_service:batch_auto_login:1284 - 服务层：设置核心层任务超时为 120 秒。
2025-06-27 17:31:08.233 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:259 - 创建客户端: H:\PyProject\TeleTest\APPDATA\sessions\+***********, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-06-27 17:31:08.233 | DEBUG    | core.telegram.client_manager:get_proxy:96 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-06-27 17:31:08.233 | DEBUG    | core.telegram.client_manager:get_proxy:143 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-06-27 17:31:08.240 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:259 - 创建客户端: H:\PyProject\TeleTest\APPDATA\sessions\+***********, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-06-27 17:31:08.240 | DEBUG    | core.telegram.client_manager:get_proxy:96 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-06-27 17:31:08.241 | DEBUG    | core.telegram.client_manager:get_proxy:143 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-06-27 17:31:08.292 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 17:31:08.371 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 17:31:08.484 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 17:31:10.259 | INFO     | core.telegram.client_manager:_connect_client:207 - 连接成功!
2025-06-27 17:31:10.343 | INFO     | core.telegram.client_manager:_connect_client:207 - 连接成功!
2025-06-27 17:31:10.920 | INFO     | core.telegram.client_manager:_verify_client_authorization:1191 - 账户已授权: +***********
2025-06-27 17:31:11.157 | INFO     | core.telegram.client_manager:_verify_client_authorization:1191 - 账户已授权: +***********
2025-06-27 17:31:13.161 | INFO     | core.telegram.client_manager:batch_auto_login:1365 - 批量登录完成: 总计 2, 成功 2, 失败 0
2025-06-27 17:31:13.253 | INFO     | app.services.account_service:batch_auto_login:1305 - 服务层：核心层登录完成: 成功 2, 失败 0 / 2
2025-06-27 17:31:27.848 | INFO     | ui.main_window:closeEvent:372 - MainWindow: 接收到关闭事件
2025-06-27 17:31:27.861 | INFO     | ui.main_window:_cleanup_before_quit:264 - MainWindow: 执行清理资源...
2025-06-27 17:31:27.862 | INFO     | core.telegram.client_worker:stop:108 - 正在停止Telegram客户端工作线程
2025-06-27 17:31:27.875 | INFO     | core.telegram.client_manager:cleanup_async:1136 - 开始清理资源并断开所有连接
2025-06-27 17:31:27.875 | INFO     | core.telegram.client_manager:disconnect_all_clients:721 - 正在断开所有客户端连接
2025-06-27 17:31:27.876 | INFO     | core.telegram.client_manager:_safe_disconnect:742 - 正在断开客户端连接: +***********
2025-06-27 17:31:27.876 | INFO     | core.telegram.client_manager:_safe_disconnect:742 - 正在断开客户端连接: +***********
2025-06-27 17:31:27.894 | INFO     | core.telegram.client_manager:_safe_disconnect:746 - 断开客户端连接成功: +***********
2025-06-27 17:31:27.894 | INFO     | core.telegram.client_manager:_safe_disconnect:746 - 断开客户端连接成功: +***********
2025-06-27 17:31:27.895 | INFO     | core.telegram.client_manager:cleanup_async:1155 - 资源清理完成
2025-06-27 17:31:28.380 | INFO     | core.telegram.client_manager:cleanup_async:1136 - 开始清理资源并断开所有连接
2025-06-27 17:31:28.381 | INFO     | core.telegram.client_manager:disconnect_all_clients:721 - 正在断开所有客户端连接
2025-06-27 17:31:28.381 | INFO     | core.telegram.client_manager:cleanup_async:1155 - 资源清理完成
2025-06-27 17:31:28.882 | INFO     | core.telegram.client_worker:run:97 - Telegram客户端工作线程已退出
2025-06-27 17:31:28.889 | INFO     | core.telegram.client_worker:stop:122 - Telegram客户端工作线程已停止
2025-06-27 17:31:28.891 | INFO     | ui.main_window:_cleanup_before_quit:273 - TelegramClientWorker 已停止。
2025-06-27 17:31:28.892 | INFO     | ui.main_window:_cleanup_before_quit:277 - MainWindow 清理完成
2025-06-27 17:31:28.900 | INFO     | __main__:main:111 - 应用程序已正常退出
2025-06-27 17:37:03.344 | INFO     | utils.logger:init_logger:98 - 日志系统初始化完成，默认级别: DEBUG，日志目录: h:\PyProject\TeleTest\APPDATA\logs
2025-06-27 17:37:04.930 | INFO     | data.database:init_database:31 - 初始化数据库连接: h:\PyProject\TeleTest\APPDATA\database\telegram_manager.db
2025-06-27 17:37:04.949 | DEBUG    | data.database:init_database:58 - 创建数据库表
2025-06-27 17:37:04.959 | INFO     | data.database:init_database:61 - 数据库初始化完成
2025-06-27 17:37:06.109 | DEBUG    | app.controllers.auth_controller:initialize_software:43 - 控制器: 初始化软件 app_id=1000, version=1.2.7
2025-06-27 17:37:06.110 | DEBUG    | core.auth.api_service:init_software:55 - 发送初始化请求: https://server.xile188.com/api/user/1000/windowns10/1.2.7/ini, params={}
2025-06-27 17:37:06.416 | DEBUG    | core.auth.api_service:init_software:66 - 初始化数据解密成功: {'new_content': '- 新增功能\n  - 1.新增图片上传功能\n  - 2.新增软件文档\n  - 3.新增软件自动更新，后续更新会自动检测版本更新，无需再下载更新包\n  - 4.新增设置账户信息及发信控制\n- 修复bug\n  - 删除监听任务时，不删除对应任务采集到的数据。\n  - 监听设置群无法发送到目标群\n  - 导出监听数据时应当导出单个任务而非所有数据\n- 优化记录\n  - 优化发信规则，直接调取符合要求的账户发送，无账户时才需要等待延时。\n  - 完善软件设置', 'new_url': None, 'notice': {'id': '2', 'content': '公告内容测试3', 'time': '**********'}}
2025-06-27 17:37:06.423 | INFO     | app.controllers.auth_controller:login:61 - 控制器: 用户登录 account=demo01
2025-06-27 17:37:09.493 | INFO     | core.auth.api_service:login:127 - 用户登录: account=demo01
2025-06-27 17:37:09.718 | INFO     | core.auth.api_service:get_user_info:347 - 获取用户信息
2025-06-27 17:37:09.962 | INFO     | core.auth.api_service:get_user_info:375 - 获取用户信息成功
2025-06-27 17:37:09.968 | INFO     | __main__:on_auth_completed:93 - 用户登录成功，正在启动主窗口...
2025-06-27 17:37:09.992 | INFO     | core.telegram.client_worker:__init__:61 - Telegram客户端工作线程初始化
2025-06-27 17:37:09.993 | INFO     | utils.vip_checker:initialize:24 - VIP检查器已初始化，等待登录后开始检查
2025-06-27 17:37:09.994 | INFO     | ui.main_window:_initialize_core_components:107 - MainWindow: 初始化核心组件...
2025-06-27 17:37:09.994 | INFO     | core.telegram.client_worker:run:65 - Telegram客户端工作线程开始运行
2025-06-27 17:37:09.995 | INFO     | app.services.account_service:__init__:42 - 账户服务初始化
2025-06-27 17:37:09.995 | INFO     | core.proxy.proxy_task_manager:run:284 - 代理任务管理器线程启动
2025-06-27 17:37:09.996 | INFO     | app.controllers.account_controller:__init__:81 - 账户控制器初始化
2025-06-27 17:37:09.997 | INFO     | app.services.monitor_service:__init__:39 - 初始化监控任务服务
2025-06-27 17:37:09.997 | INFO     | core.monitor.filter:__init__:26 - 初始化消息过滤器
2025-06-27 17:37:09.997 | INFO     | core.telegram.client_manager:__init__:66 - Telegram客户端管理器初始化
2025-06-27 17:37:09.998 | INFO     | core.monitor.extractor:__init__:24 - 初始化关键词提取器
2025-06-27 17:37:09.998 | INFO     | core.telegram.user_manager:__init__:30 - Telegram用户管理器初始化
2025-06-27 17:37:09.998 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-06-27 17:37:09.999 | INFO     | core.telegram.group_manager:__init__:36 - Telegram群组管理器初始化
2025-06-27 17:37:09.999 | INFO     | core.monitor.notifier:__init__:90 - 初始化通知管理器
2025-06-27 17:37:09.999 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-06-27 17:37:10.000 | INFO     | app.controllers.telegram_monitor_controller:__init__:64 - 初始化 Telegram 监控控制器
2025-06-27 17:37:10.000 | INFO     | core.telegram.monitor_manager:__init__:26 - Telegram监控管理器初始化
2025-06-27 17:37:10.000 | DEBUG    | app.controllers.telegram_monitor_controller:__init__:82 - 控制器初始化完成
2025-06-27 17:37:10.001 | INFO     | core.telegram.telegram_service:__init__:39 - Telegram服务初始化完成
2025-06-27 17:37:10.182 | INFO     | ui.views.monitor_view:__init__:48 - 初始化监控视图
2025-06-27 17:37:10.183 | DEBUG    | ui.views.monitor_view:_connect_signals:80 - 连接控制器信号
2025-06-27 17:37:10.513 | INFO     | ui.views.proxy_view:__init__:96 - 初始化视图，加载第一页代理数据
2025-06-27 17:37:10.819 | INFO     | __main__:on_auth_completed:97 - 主窗口已启动
2025-06-27 17:37:10.879 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:88 - 尝试连接监听日志信号...
2025-06-27 17:37:10.880 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:90 - 监听日志信号连接成功
2025-06-27 17:37:10.881 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 17:37:10.884 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 17:37:10.887 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:145 - 加载监控任务列表 (搜索关键词: '')
2025-06-27 17:37:10.887 | DEBUG    | ui.views.monitor_view:_on_loading_started:620 - 加载中: 正在加载任务列表...
2025-06-27 17:37:10.888 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 17:37:10.894 | INFO     | app.services.proxy_service:refresh_proxies:712 - 刷新代理列表: offset=0, limit=20
2025-06-27 17:37:10.895 | INFO     | core.proxy.proxy_task_manager:submit_task:172 - 提交耗时任务到子线程: refresh_proxies_0_20
2025-06-27 17:37:10.895 | DEBUG    | app.services.proxy_service:get_proxies:500 - 获取代理列表: offset=0, limit=20
2025-06-27 17:37:10.895 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 17:37:10.896 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 17:37:10.900 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 17:37:10.926 | INFO     | app.controllers.invite_controller:get_invite_tasks:38 - 获取邀请任务列表, status=None
2025-06-27 17:37:10.927 | INFO     | app.services.invite_service:get_invite_tasks:121 - 获取邀请任务列表, status=None
2025-06-27 17:37:10.929 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 17:37:10.930 | INFO     | app.services.invite_service:task_initialize:39 - 初始化邀请服务，重置运行中的任务为暂停状态
2025-06-27 17:37:10.931 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 17:37:10.931 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 17:37:10.932 | INFO     | core.proxy.proxy_task_manager:task_wrapper:199 - 任务 refresh_proxies_0_20 完成
2025-06-27 17:37:10.935 | INFO     | app.controllers.auth_controller:get_user_info:135 - 控制器: 获取用户信息
2025-06-27 17:37:10.935 | INFO     | core.auth.api_service:get_user_info:347 - 获取用户信息
2025-06-27 17:37:11.061 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 17:37:11.063 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 17:37:11.069 | DEBUG    | ui.views.monitor_view:_refresh_task_list:175 - 刷新任务列表，共0个任务
2025-06-27 17:37:11.070 | INFO     | ui.views.monitor_view:_refresh_task_list:189 - 没有找到监控任务
2025-06-27 17:37:11.070 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 17:37:11.071 | INFO     | app.services.invite_service:task_initialize:48 - 成功重置 0 个任务状态
2025-06-27 17:37:11.072 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 17:37:11.073 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 17:37:11.077 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 17:37:11.080 | INFO     | app.services.account_service:get_all_groups:96 - 获取所有账户分组成功, 共 2 个
2025-06-27 17:37:11.081 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 17:37:11.083 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 17:37:11.085 | DEBUG    | ui.views.account_view:_on_groups_loaded:446 - 分组加载完成: 2个分组
2025-06-27 17:37:11.099 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 17:37:11.171 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 17:37:11.171 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 17:37:11.173 | INFO     | app.services.account_service:get_all_accounts:290 - 获取所有账户成功, 共 2 个
2025-06-27 17:37:11.173 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 17:37:11.176 | INFO     | app.controllers.telegram_monitor_controller:_load_total_users_count_internal:191 - 成功加载总体统计数据: 总用户数 23, 今日采集 0, 日均采集 23, 运行天数 1
2025-06-27 17:37:11.177 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:156 - 成功加载 0 个任务
2025-06-27 17:37:11.177 | DEBUG    | ui.views.monitor_view:_on_loading_finished:625 - 加载完成
2025-06-27 17:37:11.178 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-06-27 17:37:11.183 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 17:37:11.191 | DEBUG    | ui.views.account_view:_on_accounts_loaded:579 - 账户加载完成: 2个账户
2025-06-27 17:37:11.215 | DEBUG    | ui.views.account_view:_update_group_counts:185 - 分组计数已更新: {2: 1, 1: 1}
2025-06-27 17:37:11.220 | INFO     | ui.views.account_view:_auto_login_accounts:634 - 开始调用控制器进行 2 个账户的自动登录。
2025-06-27 17:37:11.220 | INFO     | app.services.account_service:batch_auto_login:1165 - 服务层：开始批量自动登录 2 个账户, 最大并发: 5
2025-06-27 17:37:11.221 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 17:37:11.229 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=True
2025-06-27 17:37:11.231 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=True
2025-06-27 17:37:11.233 | INFO     | ui.views.proxy_view:_load_initial_data:1044 - 初始数据加载完成，共 1 个代理
2025-06-27 17:37:11.239 | INFO     | app.services.account_service:get_all_accounts:290 - 获取所有账户成功, 共 2 个
2025-06-27 17:37:11.240 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 17:37:11.241 | INFO     | app.services.account_service:batch_auto_login:1204 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-06-27 17:37:11.242 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 17:37:11.244 | DEBUG    | ui.views.account_view:_update_group_counts:185 - 分组计数已更新: {2: 1, 1: 1}
2025-06-27 17:37:11.244 | DEBUG    | ui.views.account_view:_init_cache:152 - 账户缓存初始化完成，共 2 个账户
2025-06-27 17:37:11.249 | DEBUG    | ui.views.account_view:_on_accounts_loaded:579 - 账户加载完成: 2个账户
2025-06-27 17:37:11.277 | DEBUG    | ui.views.account_view:_update_group_counts:185 - 分组计数已更新: {2: 1, 1: 1}
2025-06-27 17:37:11.283 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 17:37:11.288 | INFO     | app.services.account_service:batch_auto_login:1204 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-06-27 17:37:11.289 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 17:37:11.292 | INFO     | app.services.account_service:batch_auto_login:1274 - 服务层：准备调用核心层处理 2 个账户的自动登录。
2025-06-27 17:37:11.293 | INFO     | app.services.account_service:batch_auto_login:1284 - 服务层：设置核心层任务超时为 120 秒。
2025-06-27 17:37:11.293 | INFO     | core.telegram.client_manager:batch_auto_login:1216 - 开始批量自动登录, 共 2 个账户, 最大并发 5
2025-06-27 17:37:11.294 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:259 - 创建客户端: h:\PyProject\TeleTest\APPDATA\sessions\+***********, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-06-27 17:37:11.295 | DEBUG    | core.telegram.client_manager:get_proxy:96 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-06-27 17:37:11.296 | DEBUG    | core.telegram.client_manager:get_proxy:143 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-06-27 17:37:11.301 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:259 - 创建客户端: h:\PyProject\TeleTest\APPDATA\sessions\+***********, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-06-27 17:37:11.302 | DEBUG    | core.telegram.client_manager:get_proxy:96 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-06-27 17:37:11.303 | DEBUG    | core.telegram.client_manager:get_proxy:143 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-06-27 17:37:11.329 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 17:37:11.417 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 17:37:11.422 | INFO     | core.auth.api_service:get_user_info:375 - 获取用户信息成功
2025-06-27 17:37:11.422 | INFO     | ui.main_window:check_login_and_vip:384 - 用户已登录，开始检查VIP状态
2025-06-27 17:37:11.423 | INFO     | utils.vip_checker:start_checking:32 - 开始VIP状态检查
2025-06-27 17:37:11.423 | DEBUG    | utils.vip_checker:_check_vip_status:47 - 正在检查VIP状态...
2025-06-27 17:37:11.424 | INFO     | core.auth.api_service:verify_vip:425 - 会员验证接口调用
2025-06-27 17:37:11.425 | INFO     | core.auth.api_service:verify_vip:425 - 会员验证接口调用
2025-06-27 17:37:11.460 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 17:37:11.631 | ERROR    | utils.client_http:post:94 - 网络请求异常: [Errno 1] [SSL: APPLICATION_DATA_AFTER_CLOSE_NOTIFY] application data after close notify (_ssl.c:2702)
2025-06-27 17:37:11.637 | INFO     | core.auth.api_service:verify_vip:439 - 会员验证成功
2025-06-27 17:37:13.462 | INFO     | core.telegram.client_manager:_connect_client:207 - 连接成功!
2025-06-27 17:37:13.763 | INFO     | core.telegram.client_manager:_connect_client:207 - 连接成功!
2025-06-27 17:37:14.174 | INFO     | core.telegram.client_manager:_verify_client_authorization:1191 - 账户已授权: +***********
2025-06-27 17:37:14.501 | INFO     | core.telegram.client_manager:_verify_client_authorization:1191 - 账户已授权: +***********
2025-06-27 17:37:16.504 | INFO     | core.telegram.client_manager:batch_auto_login:1365 - 批量登录完成: 总计 2, 成功 2, 失败 0
2025-06-27 17:37:17.302 | INFO     | app.services.account_service:batch_auto_login:1305 - 服务层：核心层登录完成: 成功 2, 失败 0 / 2
2025-06-27 17:37:23.607 | INFO     | ui.main_window:closeEvent:372 - MainWindow: 接收到关闭事件
2025-06-27 17:37:23.614 | INFO     | ui.main_window:_cleanup_before_quit:264 - MainWindow: 执行清理资源...
2025-06-27 17:37:23.615 | INFO     | core.telegram.client_worker:stop:108 - 正在停止Telegram客户端工作线程
2025-06-27 17:37:23.621 | INFO     | core.telegram.client_manager:cleanup_async:1136 - 开始清理资源并断开所有连接
2025-06-27 17:37:23.622 | INFO     | core.telegram.client_manager:disconnect_all_clients:721 - 正在断开所有客户端连接
2025-06-27 17:37:23.623 | INFO     | core.telegram.client_manager:_safe_disconnect:742 - 正在断开客户端连接: +***********
2025-06-27 17:37:23.624 | INFO     | core.telegram.client_manager:_safe_disconnect:742 - 正在断开客户端连接: +***********
2025-06-27 17:37:23.636 | INFO     | core.telegram.client_manager:_safe_disconnect:746 - 断开客户端连接成功: +***********
2025-06-27 17:37:23.636 | INFO     | core.telegram.client_manager:_safe_disconnect:746 - 断开客户端连接成功: +***********
2025-06-27 17:37:23.637 | INFO     | core.telegram.client_manager:cleanup_async:1155 - 资源清理完成
2025-06-27 17:37:24.123 | INFO     | core.telegram.client_manager:cleanup_async:1136 - 开始清理资源并断开所有连接
2025-06-27 17:37:24.124 | INFO     | core.telegram.client_manager:disconnect_all_clients:721 - 正在断开所有客户端连接
2025-06-27 17:37:24.124 | INFO     | core.telegram.client_manager:cleanup_async:1155 - 资源清理完成
2025-06-27 17:37:24.625 | INFO     | core.telegram.client_worker:run:97 - Telegram客户端工作线程已退出
2025-06-27 17:37:24.626 | INFO     | core.telegram.client_worker:stop:122 - Telegram客户端工作线程已停止
2025-06-27 17:37:24.626 | INFO     | ui.main_window:_cleanup_before_quit:273 - TelegramClientWorker 已停止。
2025-06-27 17:37:24.626 | INFO     | ui.main_window:_cleanup_before_quit:277 - MainWindow 清理完成
2025-06-27 17:37:24.639 | INFO     | __main__:main:111 - 应用程序已正常退出
2025-06-27 17:41:22.114 | INFO     | utils.logger:init_logger:98 - 日志系统初始化完成，默认级别: DEBUG，日志目录: h:\PyProject\TeleTest\APPDATA\logs
2025-06-27 17:41:23.523 | INFO     | data.database:init_database:31 - 初始化数据库连接: h:\PyProject\TeleTest\APPDATA\database\telegram_manager.db
2025-06-27 17:41:23.542 | DEBUG    | data.database:init_database:58 - 创建数据库表
2025-06-27 17:41:23.555 | INFO     | data.database:init_database:61 - 数据库初始化完成
2025-06-27 17:41:24.755 | DEBUG    | app.controllers.auth_controller:initialize_software:43 - 控制器: 初始化软件 app_id=1000, version=1.2.7
2025-06-27 17:41:24.756 | DEBUG    | core.auth.api_service:init_software:55 - 发送初始化请求: https://server.xile188.com/api/user/1000/windowns10/1.2.7/ini, params={}
2025-06-27 17:41:25.053 | DEBUG    | core.auth.api_service:init_software:66 - 初始化数据解密成功: {'new_content': '- 新增功能\n  - 1.新增图片上传功能\n  - 2.新增软件文档\n  - 3.新增软件自动更新，后续更新会自动检测版本更新，无需再下载更新包\n  - 4.新增设置账户信息及发信控制\n- 修复bug\n  - 删除监听任务时，不删除对应任务采集到的数据。\n  - 监听设置群无法发送到目标群\n  - 导出监听数据时应当导出单个任务而非所有数据\n- 优化记录\n  - 优化发信规则，直接调取符合要求的账户发送，无账户时才需要等待延时。\n  - 完善软件设置', 'new_url': None, 'notice': {'id': '2', 'content': '公告内容测试3', 'time': '**********'}}
2025-06-27 17:41:25.064 | INFO     | app.controllers.auth_controller:login:61 - 控制器: 用户登录 account=demo01
2025-06-27 17:41:28.107 | INFO     | core.auth.api_service:login:127 - 用户登录: account=demo01
2025-06-27 17:41:28.346 | INFO     | core.auth.api_service:get_user_info:347 - 获取用户信息
2025-06-27 17:41:28.563 | INFO     | core.auth.api_service:get_user_info:375 - 获取用户信息成功
2025-06-27 17:41:28.569 | INFO     | __main__:on_auth_completed:93 - 用户登录成功，正在启动主窗口...
2025-06-27 17:41:28.596 | INFO     | core.telegram.client_worker:__init__:61 - Telegram客户端工作线程初始化
2025-06-27 17:41:28.596 | INFO     | utils.vip_checker:initialize:24 - VIP检查器已初始化，等待登录后开始检查
2025-06-27 17:41:28.597 | INFO     | ui.main_window:_initialize_core_components:107 - MainWindow: 初始化核心组件...
2025-06-27 17:41:28.597 | INFO     | core.telegram.client_worker:run:65 - Telegram客户端工作线程开始运行
2025-06-27 17:41:28.599 | INFO     | core.proxy.proxy_task_manager:run:284 - 代理任务管理器线程启动
2025-06-27 17:41:28.599 | INFO     | app.services.account_service:__init__:42 - 账户服务初始化
2025-06-27 17:41:28.600 | INFO     | app.controllers.account_controller:__init__:81 - 账户控制器初始化
2025-06-27 17:41:28.600 | INFO     | core.telegram.client_manager:__init__:66 - Telegram客户端管理器初始化
2025-06-27 17:41:28.601 | INFO     | app.services.monitor_service:__init__:39 - 初始化监控任务服务
2025-06-27 17:41:28.601 | INFO     | core.telegram.user_manager:__init__:30 - Telegram用户管理器初始化
2025-06-27 17:41:28.601 | INFO     | core.monitor.filter:__init__:26 - 初始化消息过滤器
2025-06-27 17:41:28.601 | INFO     | core.telegram.group_manager:__init__:36 - Telegram群组管理器初始化
2025-06-27 17:41:28.602 | INFO     | core.monitor.extractor:__init__:24 - 初始化关键词提取器
2025-06-27 17:41:28.602 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-06-27 17:41:28.602 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-06-27 17:41:28.602 | INFO     | core.telegram.monitor_manager:__init__:26 - Telegram监控管理器初始化
2025-06-27 17:41:28.603 | INFO     | core.monitor.notifier:__init__:90 - 初始化通知管理器
2025-06-27 17:41:28.603 | INFO     | core.telegram.telegram_service:__init__:39 - Telegram服务初始化完成
2025-06-27 17:41:28.603 | INFO     | app.controllers.telegram_monitor_controller:__init__:64 - 初始化 Telegram 监控控制器
2025-06-27 17:41:28.604 | DEBUG    | app.controllers.telegram_monitor_controller:__init__:82 - 控制器初始化完成
2025-06-27 17:41:28.805 | INFO     | ui.views.monitor_view:__init__:48 - 初始化监控视图
2025-06-27 17:41:28.806 | DEBUG    | ui.views.monitor_view:_connect_signals:80 - 连接控制器信号
2025-06-27 17:41:29.023 | INFO     | ui.views.proxy_view:__init__:96 - 初始化视图，加载第一页代理数据
2025-06-27 17:41:29.269 | INFO     | __main__:on_auth_completed:97 - 主窗口已启动
2025-06-27 17:41:29.315 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:88 - 尝试连接监听日志信号...
2025-06-27 17:41:29.316 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:90 - 监听日志信号连接成功
2025-06-27 17:41:29.316 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 17:41:29.320 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 17:41:29.323 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:145 - 加载监控任务列表 (搜索关键词: '')
2025-06-27 17:41:29.324 | DEBUG    | ui.views.monitor_view:_on_loading_started:620 - 加载中: 正在加载任务列表...
2025-06-27 17:41:29.324 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 17:41:29.332 | INFO     | app.services.proxy_service:refresh_proxies:712 - 刷新代理列表: offset=0, limit=20
2025-06-27 17:41:29.333 | INFO     | core.proxy.proxy_task_manager:submit_task:172 - 提交耗时任务到子线程: refresh_proxies_0_20
2025-06-27 17:41:29.334 | DEBUG    | app.services.proxy_service:get_proxies:500 - 获取代理列表: offset=0, limit=20
2025-06-27 17:41:29.334 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 17:41:29.334 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 17:41:29.341 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 17:41:29.370 | INFO     | app.controllers.invite_controller:get_invite_tasks:38 - 获取邀请任务列表, status=None
2025-06-27 17:41:29.373 | INFO     | app.services.invite_service:get_invite_tasks:121 - 获取邀请任务列表, status=None
2025-06-27 17:41:29.373 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 17:41:29.374 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 17:41:29.375 | INFO     | app.services.invite_service:task_initialize:39 - 初始化邀请服务，重置运行中的任务为暂停状态
2025-06-27 17:41:29.375 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 17:41:29.376 | INFO     | core.proxy.proxy_task_manager:task_wrapper:199 - 任务 refresh_proxies_0_20 完成
2025-06-27 17:41:29.380 | INFO     | app.controllers.auth_controller:get_user_info:135 - 控制器: 获取用户信息
2025-06-27 17:41:29.381 | INFO     | core.auth.api_service:get_user_info:347 - 获取用户信息
2025-06-27 17:41:29.509 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 17:41:29.510 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 17:41:29.517 | INFO     | app.services.invite_service:task_initialize:48 - 成功重置 0 个任务状态
2025-06-27 17:41:29.517 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 17:41:29.518 | DEBUG    | ui.views.monitor_view:_refresh_task_list:175 - 刷新任务列表，共0个任务
2025-06-27 17:41:29.519 | INFO     | ui.views.monitor_view:_refresh_task_list:189 - 没有找到监控任务
2025-06-27 17:41:29.520 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 17:41:29.524 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 17:41:29.528 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 17:41:29.530 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 17:41:29.533 | INFO     | app.services.account_service:get_all_groups:96 - 获取所有账户分组成功, 共 2 个
2025-06-27 17:41:29.533 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 17:41:29.599 | DEBUG    | ui.views.account_view:_on_groups_loaded:446 - 分组加载完成: 2个分组
2025-06-27 17:41:29.618 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 17:41:29.634 | INFO     | app.services.account_service:get_all_accounts:290 - 获取所有账户成功, 共 2 个
2025-06-27 17:41:29.635 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 17:41:29.636 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 17:41:29.641 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 17:41:29.643 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 17:41:29.649 | INFO     | app.controllers.telegram_monitor_controller:_load_total_users_count_internal:191 - 成功加载总体统计数据: 总用户数 23, 今日采集 0, 日均采集 23, 运行天数 1
2025-06-27 17:41:29.650 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:156 - 成功加载 0 个任务
2025-06-27 17:41:29.650 | DEBUG    | ui.views.monitor_view:_on_loading_finished:625 - 加载完成
2025-06-27 17:41:29.651 | DEBUG    | ui.views.account_view:_on_accounts_loaded:579 - 账户加载完成: 2个账户
2025-06-27 17:41:29.682 | DEBUG    | ui.views.account_view:_update_group_counts:185 - 分组计数已更新: {2: 1, 1: 1}
2025-06-27 17:41:29.688 | INFO     | ui.views.account_view:_auto_login_accounts:634 - 开始调用控制器进行 2 个账户的自动登录。
2025-06-27 17:41:29.688 | INFO     | app.services.account_service:batch_auto_login:1165 - 服务层：开始批量自动登录 2 个账户, 最大并发: 5
2025-06-27 17:41:29.689 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 17:41:29.699 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-06-27 17:41:29.719 | INFO     | app.services.account_service:batch_auto_login:1204 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-06-27 17:41:29.720 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 17:41:29.721 | INFO     | app.services.account_service:get_all_accounts:290 - 获取所有账户成功, 共 2 个
2025-06-27 17:41:29.722 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 17:41:29.724 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 17:41:29.726 | DEBUG    | ui.views.account_view:_update_group_counts:185 - 分组计数已更新: {2: 1, 1: 1}
2025-06-27 17:41:29.727 | DEBUG    | ui.views.account_view:_init_cache:152 - 账户缓存初始化完成，共 2 个账户
2025-06-27 17:41:29.735 | DEBUG    | ui.views.account_view:_on_accounts_loaded:579 - 账户加载完成: 2个账户
2025-06-27 17:41:29.769 | DEBUG    | ui.views.account_view:_update_group_counts:185 - 分组计数已更新: {2: 1, 1: 1}
2025-06-27 17:41:29.783 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=True
2025-06-27 17:41:29.785 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=True
2025-06-27 17:41:29.788 | INFO     | ui.views.proxy_view:_load_initial_data:1044 - 初始数据加载完成，共 1 个代理
2025-06-27 17:41:29.790 | INFO     | app.services.account_service:batch_auto_login:1204 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-06-27 17:41:29.791 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 17:41:29.794 | INFO     | app.services.account_service:batch_auto_login:1274 - 服务层：准备调用核心层处理 2 个账户的自动登录。
2025-06-27 17:41:29.795 | INFO     | app.services.account_service:batch_auto_login:1284 - 服务层：设置核心层任务超时为 120 秒。
2025-06-27 17:41:29.796 | INFO     | core.telegram.client_manager:batch_auto_login:1216 - 开始批量自动登录, 共 2 个账户, 最大并发 5
2025-06-27 17:41:29.797 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:259 - 创建客户端: h:\PyProject\TeleTest\APPDATA\sessions\+***********, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-06-27 17:41:29.798 | DEBUG    | core.telegram.client_manager:get_proxy:96 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-06-27 17:41:29.799 | DEBUG    | core.telegram.client_manager:get_proxy:143 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-06-27 17:41:29.803 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:259 - 创建客户端: h:\PyProject\TeleTest\APPDATA\sessions\+***********, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-06-27 17:41:29.804 | DEBUG    | core.telegram.client_manager:get_proxy:96 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-06-27 17:41:29.805 | DEBUG    | core.telegram.client_manager:get_proxy:143 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-06-27 17:41:29.836 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 17:41:29.921 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 17:41:29.931 | INFO     | core.auth.api_service:get_user_info:375 - 获取用户信息成功
2025-06-27 17:41:29.932 | INFO     | ui.main_window:check_login_and_vip:383 - 用户已登录，开始检查VIP状态
2025-06-27 17:41:29.933 | INFO     | utils.vip_checker:start_checking:32 - 开始VIP状态检查
2025-06-27 17:41:29.933 | DEBUG    | utils.vip_checker:_check_vip_status:47 - 正在检查VIP状态...
2025-06-27 17:41:29.934 | INFO     | core.auth.api_service:verify_vip:425 - 会员验证接口调用
2025-06-27 17:41:29.995 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 17:41:30.192 | INFO     | core.auth.api_service:verify_vip:439 - 会员验证成功
2025-06-27 17:41:30.192 | ERROR    | utils.vip_checker:_check_vip_status:78 - VIP状态检查出现异常: 'int' object has no attribute 'get'
Traceback (most recent call last):

  File "h:\PyProject\TeleTest\main.py", line 118, in <module>
    main()
    └ <function main at 0x000002329D7FA480>

  File "h:\PyProject\TeleTest\main.py", line 109, in main
    event_loop.run_forever()
    │          └ <function _QEventLoop.run_forever at 0x00000232F9133560>
    └ <QIOCPEventLoop running=True closed=False debug=False>

  File "H:\PyProject\TeleTest\script\Lib\site-packages\qasync\__init__.py", line 377, in run_forever
    rslt = self.__app.exec()
           └ <QIOCPEventLoop running=True closed=False debug=False>
  File "H:\PyProject\TeleTest\script\Lib\site-packages\qasync\__init__.py", line 279, in timerEvent
    handle._run()
    │      └ <function Handle._run at 0x00000232F8880E00>
    └ <Handle Task.task_wakeup(<Task finishe...> result=None>)>
  File "D:\Program Files\python11\Lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle Task.task_wakeup(<Task finishe...> result=None>)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle Task.task_wakeup(<Task finishe...> result=None>)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle Task.task_wakeup(<Task finishe...> result=None>)>

> File "h:\PyProject\TeleTest\utils\vip_checker.py", line 61, in _check_vip_status
    is_vip = data.get('is_vip', True)  # 默认为True，兼容老接口
             └ 1751017289

AttributeError: 'int' object has no attribute 'get'
2025-06-27 17:41:31.841 | INFO     | core.telegram.client_manager:_connect_client:207 - 连接成功!
2025-06-27 17:41:32.061 | INFO     | core.telegram.client_manager:_connect_client:207 - 连接成功!
2025-06-27 17:41:32.526 | INFO     | core.telegram.client_manager:_verify_client_authorization:1191 - 账户已授权: +***********
2025-06-27 17:41:32.709 | INFO     | core.telegram.client_manager:_verify_client_authorization:1191 - 账户已授权: +***********
2025-06-27 17:41:34.711 | INFO     | core.telegram.client_manager:batch_auto_login:1365 - 批量登录完成: 总计 2, 成功 2, 失败 0
2025-06-27 17:42:28.567 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-27 17:42:37.401 | INFO     | ui.main_window:closeEvent:372 - MainWindow: 接收到关闭事件
2025-06-27 17:42:37.416 | INFO     | ui.main_window:_cleanup_before_quit:264 - MainWindow: 执行清理资源...
2025-06-27 17:42:37.416 | INFO     | core.telegram.client_worker:stop:108 - 正在停止Telegram客户端工作线程
2025-06-27 17:42:37.422 | INFO     | core.telegram.client_manager:cleanup_async:1136 - 开始清理资源并断开所有连接
2025-06-27 17:42:37.422 | INFO     | core.telegram.client_manager:disconnect_all_clients:721 - 正在断开所有客户端连接
2025-06-27 17:42:37.424 | INFO     | core.telegram.client_manager:_safe_disconnect:742 - 正在断开客户端连接: +***********
2025-06-27 17:42:37.424 | INFO     | core.telegram.client_manager:_safe_disconnect:742 - 正在断开客户端连接: +***********
2025-06-27 17:42:37.436 | INFO     | core.telegram.client_manager:_safe_disconnect:746 - 断开客户端连接成功: +***********
2025-06-27 17:42:37.437 | INFO     | core.telegram.client_manager:_safe_disconnect:746 - 断开客户端连接成功: +***********
2025-06-27 17:42:37.438 | INFO     | core.telegram.client_manager:cleanup_async:1155 - 资源清理完成
2025-06-27 17:42:37.922 | INFO     | core.telegram.client_manager:cleanup_async:1136 - 开始清理资源并断开所有连接
2025-06-27 17:42:37.922 | INFO     | core.telegram.client_manager:disconnect_all_clients:721 - 正在断开所有客户端连接
2025-06-27 17:42:37.922 | INFO     | core.telegram.client_manager:cleanup_async:1155 - 资源清理完成
2025-06-27 17:42:38.424 | INFO     | core.telegram.client_worker:run:97 - Telegram客户端工作线程已退出
2025-06-27 17:42:38.424 | INFO     | core.telegram.client_worker:stop:122 - Telegram客户端工作线程已停止
2025-06-27 17:42:38.425 | INFO     | ui.main_window:_cleanup_before_quit:273 - TelegramClientWorker 已停止。
2025-06-27 17:42:38.425 | INFO     | ui.main_window:_cleanup_before_quit:277 - MainWindow 清理完成
2025-06-27 17:42:38.430 | INFO     | __main__:main:111 - 应用程序已正常退出
2025-06-27 18:41:47.949 | INFO     | utils.logger:init_logger:98 - 日志系统初始化完成，默认级别: DEBUG，日志目录: h:\PyProject\TeleTest\APPDATA\logs
2025-06-27 18:41:50.635 | INFO     | data.database:init_database:31 - 初始化数据库连接: h:\PyProject\TeleTest\APPDATA\database\telegram_manager.db
2025-06-27 18:41:50.666 | DEBUG    | data.database:init_database:58 - 创建数据库表
2025-06-27 18:41:50.680 | INFO     | data.database:init_database:61 - 数据库初始化完成
2025-06-27 18:41:52.186 | DEBUG    | app.controllers.auth_controller:initialize_software:43 - 控制器: 初始化软件 app_id=1000, version=1.2.7
2025-06-27 18:41:52.186 | DEBUG    | core.auth.api_service:init_software:55 - 发送初始化请求: https://server.xile188.com/api/user/1000/windowns10/1.2.7/ini, params={}
2025-06-27 18:41:52.745 | DEBUG    | core.auth.api_service:init_software:66 - 初始化数据解密成功: {'new_content': '- 新增功能\n  - 1.新增图片上传功能\n  - 2.新增软件文档\n  - 3.新增软件自动更新，后续更新会自动检测版本更新，无需再下载更新包\n  - 4.新增设置账户信息及发信控制\n- 修复bug\n  - 删除监听任务时，不删除对应任务采集到的数据。\n  - 监听设置群无法发送到目标群\n  - 导出监听数据时应当导出单个任务而非所有数据\n- 优化记录\n  - 优化发信规则，直接调取符合要求的账户发送，无账户时才需要等待延时。\n  - 完善软件设置', 'new_url': None, 'notice': {'id': '2', 'content': '公告内容测试3', 'time': '**********'}}
2025-06-27 18:41:52.758 | INFO     | app.controllers.auth_controller:login:61 - 控制器: 用户登录 account=demo01
2025-06-27 18:41:55.680 | INFO     | core.auth.api_service:login:127 - 用户登录: account=demo01
2025-06-27 18:41:55.915 | INFO     | core.auth.api_service:get_user_info:347 - 获取用户信息
2025-06-27 18:41:56.175 | INFO     | core.auth.api_service:get_user_info:375 - 获取用户信息成功
2025-06-27 18:41:56.186 | INFO     | __main__:on_auth_completed:93 - 用户登录成功，正在启动主窗口...
2025-06-27 18:41:56.218 | INFO     | core.telegram.client_worker:__init__:61 - Telegram客户端工作线程初始化
2025-06-27 18:41:56.220 | INFO     | ui.main_window:_initialize_core_components:106 - MainWindow: 初始化核心组件...
2025-06-27 18:41:56.221 | INFO     | core.telegram.client_worker:run:65 - Telegram客户端工作线程开始运行
2025-06-27 18:41:56.222 | INFO     | app.services.account_service:__init__:42 - 账户服务初始化
2025-06-27 18:41:56.223 | INFO     | core.proxy.proxy_task_manager:run:284 - 代理任务管理器线程启动
2025-06-27 18:41:56.223 | INFO     | app.controllers.account_controller:__init__:81 - 账户控制器初始化
2025-06-27 18:41:56.224 | INFO     | core.telegram.client_manager:__init__:66 - Telegram客户端管理器初始化
2025-06-27 18:41:56.224 | INFO     | app.services.monitor_service:__init__:39 - 初始化监控任务服务
2025-06-27 18:41:56.225 | INFO     | core.telegram.user_manager:__init__:30 - Telegram用户管理器初始化
2025-06-27 18:41:56.225 | INFO     | core.monitor.filter:__init__:26 - 初始化消息过滤器
2025-06-27 18:41:56.226 | INFO     | core.telegram.group_manager:__init__:36 - Telegram群组管理器初始化
2025-06-27 18:41:56.226 | INFO     | core.monitor.extractor:__init__:24 - 初始化关键词提取器
2025-06-27 18:41:56.226 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-06-27 18:41:56.227 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-06-27 18:41:56.227 | INFO     | core.telegram.monitor_manager:__init__:26 - Telegram监控管理器初始化
2025-06-27 18:41:56.228 | INFO     | core.monitor.notifier:__init__:90 - 初始化通知管理器
2025-06-27 18:41:56.228 | INFO     | core.telegram.telegram_service:__init__:39 - Telegram服务初始化完成
2025-06-27 18:41:56.229 | INFO     | app.controllers.telegram_monitor_controller:__init__:64 - 初始化 Telegram 监控控制器
2025-06-27 18:41:56.229 | DEBUG    | app.controllers.telegram_monitor_controller:__init__:82 - 控制器初始化完成
2025-06-27 18:41:56.427 | INFO     | ui.views.monitor_view:__init__:48 - 初始化监控视图
2025-06-27 18:41:56.428 | DEBUG    | ui.views.monitor_view:_connect_signals:80 - 连接控制器信号
2025-06-27 18:41:56.600 | INFO     | ui.views.proxy_view:__init__:96 - 初始化视图，加载第一页代理数据
2025-06-27 18:41:56.824 | INFO     | __main__:on_auth_completed:97 - 主窗口已启动
2025-06-27 18:41:56.867 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:88 - 尝试连接监听日志信号...
2025-06-27 18:41:56.868 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:90 - 监听日志信号连接成功
2025-06-27 18:41:56.869 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 18:41:56.873 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 18:41:56.876 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:145 - 加载监控任务列表 (搜索关键词: '')
2025-06-27 18:41:56.877 | DEBUG    | ui.views.monitor_view:_on_loading_started:620 - 加载中: 正在加载任务列表...
2025-06-27 18:41:56.877 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 18:41:56.884 | INFO     | app.services.proxy_service:refresh_proxies:712 - 刷新代理列表: offset=0, limit=20
2025-06-27 18:41:56.885 | INFO     | core.proxy.proxy_task_manager:submit_task:172 - 提交耗时任务到子线程: refresh_proxies_0_20
2025-06-27 18:41:56.885 | DEBUG    | app.services.proxy_service:get_proxies:500 - 获取代理列表: offset=0, limit=20
2025-06-27 18:41:56.886 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 18:41:56.886 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 18:41:56.892 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 18:41:56.916 | INFO     | app.controllers.invite_controller:get_invite_tasks:38 - 获取邀请任务列表, status=None
2025-06-27 18:41:56.917 | INFO     | app.services.invite_service:get_invite_tasks:121 - 获取邀请任务列表, status=None
2025-06-27 18:41:56.918 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 18:41:56.920 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 18:41:56.920 | INFO     | app.services.invite_service:task_initialize:39 - 初始化邀请服务，重置运行中的任务为暂停状态
2025-06-27 18:41:56.921 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 18:41:56.922 | INFO     | core.proxy.proxy_task_manager:task_wrapper:199 - 任务 refresh_proxies_0_20 完成
2025-06-27 18:41:57.041 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 18:41:57.044 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 18:41:57.048 | DEBUG    | ui.views.monitor_view:_refresh_task_list:175 - 刷新任务列表，共0个任务
2025-06-27 18:41:57.048 | INFO     | ui.views.monitor_view:_refresh_task_list:189 - 没有找到监控任务
2025-06-27 18:41:57.049 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 18:41:57.050 | INFO     | app.services.invite_service:task_initialize:48 - 成功重置 0 个任务状态
2025-06-27 18:41:57.051 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 18:41:57.053 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 18:41:57.058 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 18:41:57.061 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 18:41:57.061 | INFO     | app.services.account_service:get_all_groups:96 - 获取所有账户分组成功, 共 2 个
2025-06-27 18:41:57.062 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 18:41:57.163 | DEBUG    | ui.views.account_view:_on_groups_loaded:446 - 分组加载完成: 2个分组
2025-06-27 18:41:57.176 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 18:41:57.185 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 18:41:57.186 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 18:41:57.187 | INFO     | app.services.account_service:get_all_accounts:290 - 获取所有账户成功, 共 2 个
2025-06-27 18:41:57.187 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 18:41:57.190 | INFO     | app.controllers.telegram_monitor_controller:_load_total_users_count_internal:191 - 成功加载总体统计数据: 总用户数 23, 今日采集 0, 日均采集 23, 运行天数 1
2025-06-27 18:41:57.190 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:156 - 成功加载 0 个任务
2025-06-27 18:41:57.190 | DEBUG    | ui.views.monitor_view:_on_loading_finished:625 - 加载完成
2025-06-27 18:41:57.192 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-06-27 18:41:57.203 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 18:41:57.204 | DEBUG    | ui.views.account_view:_on_accounts_loaded:579 - 账户加载完成: 2个账户
2025-06-27 18:41:57.227 | DEBUG    | ui.views.account_view:_update_group_counts:185 - 分组计数已更新: {2: 1, 1: 1}
2025-06-27 18:41:57.231 | INFO     | ui.views.account_view:_auto_login_accounts:634 - 开始调用控制器进行 2 个账户的自动登录。
2025-06-27 18:41:57.232 | INFO     | app.services.account_service:batch_auto_login:1165 - 服务层：开始批量自动登录 2 个账户, 最大并发: 5
2025-06-27 18:41:57.232 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 18:41:57.237 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=True
2025-06-27 18:41:57.239 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=True
2025-06-27 18:41:57.242 | INFO     | ui.views.proxy_view:_load_initial_data:1044 - 初始数据加载完成，共 1 个代理
2025-06-27 18:41:57.247 | INFO     | app.services.account_service:batch_auto_login:1204 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-06-27 18:41:57.247 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 18:41:57.248 | INFO     | app.services.account_service:get_all_accounts:290 - 获取所有账户成功, 共 2 个
2025-06-27 18:41:57.248 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 18:41:57.250 | DEBUG    | ui.views.account_view:_update_group_counts:185 - 分组计数已更新: {2: 1, 1: 1}
2025-06-27 18:41:57.251 | DEBUG    | ui.views.account_view:_init_cache:152 - 账户缓存初始化完成，共 2 个账户
2025-06-27 18:41:57.256 | DEBUG    | ui.views.account_view:_on_accounts_loaded:579 - 账户加载完成: 2个账户
2025-06-27 18:41:57.277 | DEBUG    | ui.views.account_view:_update_group_counts:185 - 分组计数已更新: {2: 1, 1: 1}
2025-06-27 18:41:57.283 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 18:41:57.288 | INFO     | app.services.account_service:batch_auto_login:1204 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-06-27 18:41:57.288 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 18:41:57.290 | INFO     | app.services.account_service:batch_auto_login:1274 - 服务层：准备调用核心层处理 2 个账户的自动登录。
2025-06-27 18:41:57.291 | INFO     | app.services.account_service:batch_auto_login:1284 - 服务层：设置核心层任务超时为 120 秒。
2025-06-27 18:41:57.291 | INFO     | core.telegram.client_manager:batch_auto_login:1216 - 开始批量自动登录, 共 2 个账户, 最大并发 5
2025-06-27 18:41:57.292 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:259 - 创建客户端: h:\PyProject\TeleTest\APPDATA\sessions\+***********, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-06-27 18:41:57.293 | DEBUG    | core.telegram.client_manager:get_proxy:96 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-06-27 18:41:57.294 | DEBUG    | core.telegram.client_manager:get_proxy:143 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-06-27 18:41:57.300 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:259 - 创建客户端: h:\PyProject\TeleTest\APPDATA\sessions\+***********, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-06-27 18:41:57.301 | DEBUG    | core.telegram.client_manager:get_proxy:96 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-06-27 18:41:57.302 | DEBUG    | core.telegram.client_manager:get_proxy:143 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-06-27 18:41:57.318 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 18:41:57.395 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 18:41:57.433 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 18:42:00.020 | INFO     | core.telegram.client_manager:_connect_client:207 - 连接成功!
2025-06-27 18:42:00.277 | INFO     | core.telegram.client_manager:_connect_client:207 - 连接成功!
2025-06-27 18:42:00.830 | INFO     | core.telegram.client_manager:_verify_client_authorization:1191 - 账户已授权: +***********
2025-06-27 18:42:01.162 | INFO     | core.telegram.client_manager:_verify_client_authorization:1191 - 账户已授权: +***********
2025-06-27 18:42:03.165 | INFO     | core.telegram.client_manager:batch_auto_login:1365 - 批量登录完成: 总计 2, 成功 2, 失败 0
2025-06-27 18:42:03.302 | INFO     | app.services.account_service:batch_auto_login:1305 - 服务层：核心层登录完成: 成功 2, 失败 0 / 2
2025-06-27 18:42:56.170 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-27 18:43:56.174 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-27 18:44:56.161 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-27 18:45:56.177 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-27 18:46:56.178 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-27 18:47:56.179 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-27 18:48:56.178 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-27 18:49:56.180 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-27 18:50:56.180 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-27 18:51:56.180 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-27 18:52:56.179 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-27 18:53:56.178 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-27 18:54:56.179 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-27 18:55:56.179 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-27 18:56:56.181 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-27 18:57:56.182 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-27 18:58:56.179 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-27 18:59:56.178 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-27 19:00:56.180 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-27 19:01:56.178 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-27 19:02:56.188 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-27 19:03:56.189 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-27 19:04:01.028 | INFO     | ui.main_window:closeEvent:381 - MainWindow: 接收到关闭事件
2025-06-27 19:04:01.039 | INFO     | ui.main_window:_cleanup_before_quit:263 - MainWindow: 执行清理资源...
2025-06-27 19:04:01.041 | INFO     | core.telegram.client_worker:stop:108 - 正在停止Telegram客户端工作线程
2025-06-27 19:04:01.047 | INFO     | core.telegram.client_manager:cleanup_async:1136 - 开始清理资源并断开所有连接
2025-06-27 19:04:01.048 | INFO     | core.telegram.client_manager:disconnect_all_clients:721 - 正在断开所有客户端连接
2025-06-27 19:04:01.049 | INFO     | core.telegram.client_manager:_safe_disconnect:742 - 正在断开客户端连接: +***********
2025-06-27 19:04:01.049 | INFO     | core.telegram.client_manager:_safe_disconnect:742 - 正在断开客户端连接: +***********
2025-06-27 19:04:01.057 | INFO     | core.telegram.client_manager:_safe_disconnect:746 - 断开客户端连接成功: +***********
2025-06-27 19:04:01.064 | INFO     | core.telegram.client_manager:_safe_disconnect:746 - 断开客户端连接成功: +***********
2025-06-27 19:04:01.064 | INFO     | core.telegram.client_manager:cleanup_async:1155 - 资源清理完成
2025-06-27 19:04:01.550 | INFO     | core.telegram.client_manager:cleanup_async:1136 - 开始清理资源并断开所有连接
2025-06-27 19:04:01.550 | INFO     | core.telegram.client_manager:disconnect_all_clients:721 - 正在断开所有客户端连接
2025-06-27 19:04:01.551 | INFO     | core.telegram.client_manager:cleanup_async:1155 - 资源清理完成
2025-06-27 19:04:02.051 | INFO     | core.telegram.client_worker:run:97 - Telegram客户端工作线程已退出
2025-06-27 19:04:02.052 | INFO     | core.telegram.client_worker:stop:122 - Telegram客户端工作线程已停止
2025-06-27 19:04:02.052 | INFO     | ui.main_window:_cleanup_before_quit:272 - TelegramClientWorker 已停止。
2025-06-27 19:04:02.053 | INFO     | ui.main_window:_cleanup_before_quit:276 - MainWindow 清理完成
2025-06-27 19:04:02.067 | INFO     | __main__:main:111 - 应用程序已正常退出
2025-06-27 19:15:09.142 | INFO     | utils.logger:init_logger:98 - 日志系统初始化完成，默认级别: DEBUG，日志目录: h:\PyProject\TeleTest\APPDATA\logs
2025-06-27 19:15:10.484 | INFO     | data.database:init_database:31 - 初始化数据库连接: h:\PyProject\TeleTest\APPDATA\database\telegram_manager.db
2025-06-27 19:15:10.502 | DEBUG    | data.database:init_database:58 - 创建数据库表
2025-06-27 19:15:10.515 | INFO     | data.database:init_database:61 - 数据库初始化完成
2025-06-27 19:15:11.761 | DEBUG    | app.controllers.auth_controller:initialize_software:43 - 控制器: 初始化软件 app_id=1000, version=1.2.7
2025-06-27 19:15:11.765 | DEBUG    | core.auth.api_service:init_software:55 - 发送初始化请求: https://server.xile188.com/api/user/1000/windowns10/1.2.7/ini, params={}
2025-06-27 19:15:12.137 | DEBUG    | core.auth.api_service:init_software:66 - 初始化数据解密成功: {'new_content': '- 新增功能\n  - 1.新增图片上传功能\n  - 2.新增软件文档\n  - 3.新增软件自动更新，后续更新会自动检测版本更新，无需再下载更新包\n  - 4.新增设置账户信息及发信控制\n- 修复bug\n  - 删除监听任务时，不删除对应任务采集到的数据。\n  - 监听设置群无法发送到目标群\n  - 导出监听数据时应当导出单个任务而非所有数据\n- 优化记录\n  - 优化发信规则，直接调取符合要求的账户发送，无账户时才需要等待延时。\n  - 完善软件设置', 'new_url': None, 'notice': {'id': '2', 'content': '公告内容测试3', 'time': '**********'}}
2025-06-27 19:15:12.145 | INFO     | app.controllers.auth_controller:login:61 - 控制器: 用户登录 account=demo01
2025-06-27 19:15:15.116 | INFO     | core.auth.api_service:login:127 - 用户登录: account=demo01
2025-06-27 19:15:15.342 | INFO     | core.auth.api_service:get_user_info:347 - 获取用户信息
2025-06-27 19:15:15.592 | INFO     | core.auth.api_service:get_user_info:375 - 获取用户信息成功
2025-06-27 19:15:15.600 | INFO     | __main__:on_auth_completed:93 - 用户登录成功，正在启动主窗口...
2025-06-27 19:15:15.624 | INFO     | core.telegram.client_worker:__init__:61 - Telegram客户端工作线程初始化
2025-06-27 19:15:15.625 | INFO     | utils.vip_checker:initialize:26 - VIP检查器已初始化，等待登录后开始检查
2025-06-27 19:15:15.625 | INFO     | utils.vip_checker:start_checking:38 - 开始VIP状态检查
2025-06-27 19:15:15.625 | INFO     | core.telegram.client_worker:run:65 - Telegram客户端工作线程开始运行
2025-06-27 19:15:15.626 | INFO     | ui.main_window:_initialize_core_components:111 - MainWindow: 初始化核心组件...
2025-06-27 19:15:15.628 | INFO     | app.services.account_service:__init__:42 - 账户服务初始化
2025-06-27 19:15:15.628 | INFO     | core.telegram.client_manager:__init__:66 - Telegram客户端管理器初始化
2025-06-27 19:15:15.628 | INFO     | core.proxy.proxy_task_manager:run:284 - 代理任务管理器线程启动
2025-06-27 19:15:15.629 | INFO     | app.controllers.account_controller:__init__:81 - 账户控制器初始化
2025-06-27 19:15:15.629 | INFO     | core.telegram.user_manager:__init__:30 - Telegram用户管理器初始化
2025-06-27 19:15:15.631 | INFO     | app.services.monitor_service:__init__:39 - 初始化监控任务服务
2025-06-27 19:15:15.631 | INFO     | core.telegram.group_manager:__init__:36 - Telegram群组管理器初始化
2025-06-27 19:15:15.632 | INFO     | core.monitor.filter:__init__:26 - 初始化消息过滤器
2025-06-27 19:15:15.632 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-06-27 19:15:15.633 | INFO     | core.monitor.extractor:__init__:24 - 初始化关键词提取器
2025-06-27 19:15:15.633 | INFO     | core.telegram.monitor_manager:__init__:26 - Telegram监控管理器初始化
2025-06-27 19:15:15.633 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-06-27 19:15:15.633 | INFO     | core.telegram.telegram_service:__init__:39 - Telegram服务初始化完成
2025-06-27 19:15:15.634 | INFO     | core.monitor.notifier:__init__:90 - 初始化通知管理器
2025-06-27 19:15:15.634 | INFO     | app.controllers.telegram_monitor_controller:__init__:64 - 初始化 Telegram 监控控制器
2025-06-27 19:15:15.635 | DEBUG    | app.controllers.telegram_monitor_controller:__init__:82 - 控制器初始化完成
2025-06-27 19:15:15.822 | INFO     | ui.views.monitor_view:__init__:48 - 初始化监控视图
2025-06-27 19:15:15.822 | DEBUG    | ui.views.monitor_view:_connect_signals:80 - 连接控制器信号
2025-06-27 19:15:16.007 | INFO     | ui.views.proxy_view:__init__:96 - 初始化视图，加载第一页代理数据
2025-06-27 19:15:16.257 | INFO     | __main__:on_auth_completed:97 - 主窗口已启动
2025-06-27 19:15:16.300 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:88 - 尝试连接监听日志信号...
2025-06-27 19:15:16.301 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:90 - 监听日志信号连接成功
2025-06-27 19:15:16.302 | DEBUG    | utils.vip_checker:_check_vip_status:61 - 正在检查VIP状态...
2025-06-27 19:15:16.302 | INFO     | core.auth.api_service:verify_vip:425 - 会员验证接口调用
2025-06-27 19:15:16.303 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 19:15:16.307 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 19:15:16.310 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:145 - 加载监控任务列表 (搜索关键词: '')
2025-06-27 19:15:16.311 | DEBUG    | ui.views.monitor_view:_on_loading_started:620 - 加载中: 正在加载任务列表...
2025-06-27 19:15:16.311 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 19:15:16.319 | INFO     | app.services.proxy_service:refresh_proxies:712 - 刷新代理列表: offset=0, limit=20
2025-06-27 19:15:16.320 | INFO     | core.proxy.proxy_task_manager:submit_task:172 - 提交耗时任务到子线程: refresh_proxies_0_20
2025-06-27 19:15:16.320 | DEBUG    | app.services.proxy_service:get_proxies:500 - 获取代理列表: offset=0, limit=20
2025-06-27 19:15:16.320 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 19:15:16.320 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 19:15:16.323 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 19:15:16.349 | INFO     | app.controllers.invite_controller:get_invite_tasks:38 - 获取邀请任务列表, status=None
2025-06-27 19:15:16.350 | INFO     | app.services.invite_service:get_invite_tasks:121 - 获取邀请任务列表, status=None
2025-06-27 19:15:16.351 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 19:15:16.353 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 19:15:16.353 | INFO     | app.services.invite_service:task_initialize:39 - 初始化邀请服务，重置运行中的任务为暂停状态
2025-06-27 19:15:16.353 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 19:15:16.354 | INFO     | core.proxy.proxy_task_manager:task_wrapper:199 - 任务 refresh_proxies_0_20 完成
2025-06-27 19:15:16.472 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 19:15:16.473 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 19:15:16.481 | DEBUG    | ui.views.monitor_view:_refresh_task_list:175 - 刷新任务列表，共0个任务
2025-06-27 19:15:16.481 | INFO     | ui.views.monitor_view:_refresh_task_list:189 - 没有找到监控任务
2025-06-27 19:15:16.482 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 19:15:16.483 | INFO     | app.services.invite_service:task_initialize:48 - 成功重置 0 个任务状态
2025-06-27 19:15:16.483 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 19:15:16.488 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 19:15:16.490 | INFO     | app.services.account_service:get_all_groups:96 - 获取所有账户分组成功, 共 2 个
2025-06-27 19:15:16.490 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 19:15:16.491 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 19:15:16.493 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 19:15:16.495 | DEBUG    | ui.views.account_view:_on_groups_loaded:446 - 分组加载完成: 2个分组
2025-06-27 19:15:16.509 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 19:15:16.577 | INFO     | app.services.account_service:get_all_accounts:290 - 获取所有账户成功, 共 2 个
2025-06-27 19:15:16.578 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 19:15:16.579 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 19:15:16.579 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 19:15:16.582 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 19:15:16.583 | DEBUG    | ui.views.account_view:_on_accounts_loaded:579 - 账户加载完成: 2个账户
2025-06-27 19:15:16.603 | DEBUG    | ui.views.account_view:_update_group_counts:185 - 分组计数已更新: {2: 1, 1: 1}
2025-06-27 19:15:16.608 | INFO     | ui.views.account_view:_auto_login_accounts:634 - 开始调用控制器进行 2 个账户的自动登录。
2025-06-27 19:15:16.608 | INFO     | app.services.account_service:batch_auto_login:1165 - 服务层：开始批量自动登录 2 个账户, 最大并发: 5
2025-06-27 19:15:16.609 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 19:15:16.613 | INFO     | app.controllers.telegram_monitor_controller:_load_total_users_count_internal:191 - 成功加载总体统计数据: 总用户数 23, 今日采集 0, 日均采集 23, 运行天数 1
2025-06-27 19:15:16.614 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:156 - 成功加载 0 个任务
2025-06-27 19:15:16.614 | DEBUG    | ui.views.monitor_view:_on_loading_finished:625 - 加载完成
2025-06-27 19:15:16.616 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-06-27 19:15:16.634 | INFO     | app.services.account_service:batch_auto_login:1204 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-06-27 19:15:16.634 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 19:15:16.635 | INFO     | app.services.account_service:get_all_accounts:290 - 获取所有账户成功, 共 2 个
2025-06-27 19:15:16.636 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 19:15:16.638 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 19:15:16.639 | DEBUG    | ui.views.account_view:_update_group_counts:185 - 分组计数已更新: {2: 1, 1: 1}
2025-06-27 19:15:16.640 | DEBUG    | ui.views.account_view:_init_cache:152 - 账户缓存初始化完成，共 2 个账户
2025-06-27 19:15:16.644 | DEBUG    | ui.views.account_view:_on_accounts_loaded:579 - 账户加载完成: 2个账户
2025-06-27 19:15:16.667 | DEBUG    | ui.views.account_view:_update_group_counts:185 - 分组计数已更新: {2: 1, 1: 1}
2025-06-27 19:15:16.676 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=True
2025-06-27 19:15:16.679 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=True
2025-06-27 19:15:16.681 | INFO     | ui.views.proxy_view:_load_initial_data:1044 - 初始数据加载完成，共 1 个代理
2025-06-27 19:15:16.684 | INFO     | core.auth.api_service:verify_vip:439 - 会员验证成功
2025-06-27 19:15:16.684 | ERROR    | utils.vip_checker:_check_vip_status:92 - VIP状态检查出现异常: 'int' object has no attribute 'get'
Traceback (most recent call last):

  File "h:\PyProject\TeleTest\main.py", line 118, in <module>
    main()
    └ <function main at 0x0000025F44A8E2A0>

  File "h:\PyProject\TeleTest\main.py", line 109, in main
    event_loop.run_forever()
    │          └ <function _QEventLoop.run_forever at 0x0000025F20473560>
    └ <QIOCPEventLoop running=True closed=False debug=False>

  File "H:\PyProject\TeleTest\script\Lib\site-packages\qasync\__init__.py", line 377, in run_forever
    rslt = self.__app.exec()
           └ <QIOCPEventLoop running=True closed=False debug=False>
  File "H:\PyProject\TeleTest\script\Lib\site-packages\qasync\__init__.py", line 279, in timerEvent
    handle._run()
    │      └ <function Handle._run at 0x0000025F1FBC0E00>
    └ <Handle Task.task_wakeup(<Task finishe...> result=None>)>
  File "D:\Program Files\python11\Lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle Task.task_wakeup(<Task finishe...> result=None>)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle Task.task_wakeup(<Task finishe...> result=None>)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle Task.task_wakeup(<Task finishe...> result=None>)>

  File "h:\PyProject\TeleTest\utils\vip_checker.py", line 54, in _on_timer_timeout
    await self._check_vip_status()
          │    └ <function VipChecker._check_vip_status at 0x0000025F314FBB00>
          └ <utils.vip_checker.VipChecker(0x25f41fbc7b0) at 0x0000025F3150C940>

> File "h:\PyProject\TeleTest\utils\vip_checker.py", line 75, in _check_vip_status
    is_vip = data.get('is_vip', True)  # 默认为True，兼容老接口
             └ 1751022916

AttributeError: 'int' object has no attribute 'get'
2025-06-27 19:16:09.531 | INFO     | ui.main_window:closeEvent:387 - MainWindow: 接收到关闭事件
2025-06-27 19:16:09.540 | INFO     | ui.main_window:_cleanup_before_quit:268 - MainWindow: 执行清理资源...
2025-06-27 19:16:09.540 | INFO     | core.telegram.client_worker:stop:108 - 正在停止Telegram客户端工作线程
2025-06-27 19:16:09.549 | INFO     | core.telegram.client_manager:cleanup_async:1136 - 开始清理资源并断开所有连接
2025-06-27 19:16:09.550 | INFO     | core.telegram.client_manager:disconnect_all_clients:721 - 正在断开所有客户端连接
2025-06-27 19:16:09.551 | INFO     | core.telegram.client_manager:cleanup_async:1155 - 资源清理完成
2025-06-27 19:16:10.052 | INFO     | core.telegram.client_manager:cleanup_async:1136 - 开始清理资源并断开所有连接
2025-06-27 19:16:10.052 | INFO     | core.telegram.client_manager:disconnect_all_clients:721 - 正在断开所有客户端连接
2025-06-27 19:16:10.053 | INFO     | core.telegram.client_manager:cleanup_async:1155 - 资源清理完成
2025-06-27 19:16:10.553 | INFO     | core.telegram.client_worker:run:97 - Telegram客户端工作线程已退出
2025-06-27 19:16:10.554 | INFO     | core.telegram.client_worker:stop:122 - Telegram客户端工作线程已停止
2025-06-27 19:16:10.554 | INFO     | ui.main_window:_cleanup_before_quit:277 - TelegramClientWorker 已停止。
2025-06-27 19:16:10.555 | INFO     | ui.main_window:_cleanup_before_quit:281 - MainWindow 清理完成
2025-06-27 19:16:10.563 | INFO     | __main__:main:111 - 应用程序已正常退出
2025-06-27 19:17:42.020 | INFO     | utils.logger:init_logger:98 - 日志系统初始化完成，默认级别: DEBUG，日志目录: H:\PyProject\TeleTest\APPDATA\logs
2025-06-27 19:17:44.224 | INFO     | data.database:init_database:31 - 初始化数据库连接: H:\PyProject\TeleTest\APPDATA\database\telegram_manager.db
2025-06-27 19:17:44.260 | DEBUG    | data.database:init_database:58 - 创建数据库表
2025-06-27 19:17:44.282 | INFO     | data.database:init_database:61 - 数据库初始化完成
2025-06-27 19:17:45.750 | DEBUG    | app.controllers.auth_controller:initialize_software:43 - 控制器: 初始化软件 app_id=1000, version=1.2.7
2025-06-27 19:17:45.750 | DEBUG    | core.auth.api_service:init_software:55 - 发送初始化请求: https://server.xile188.com/api/user/1000/windowns10/1.2.7/ini, params={}
2025-06-27 19:17:45.995 | DEBUG    | core.auth.api_service:init_software:66 - 初始化数据解密成功: {'new_content': '- 新增功能\n  - 1.新增图片上传功能\n  - 2.新增软件文档\n  - 3.新增软件自动更新，后续更新会自动检测版本更新，无需再下载更新包\n  - 4.新增设置账户信息及发信控制\n- 修复bug\n  - 删除监听任务时，不删除对应任务采集到的数据。\n  - 监听设置群无法发送到目标群\n  - 导出监听数据时应当导出单个任务而非所有数据\n- 优化记录\n  - 优化发信规则，直接调取符合要求的账户发送，无账户时才需要等待延时。\n  - 完善软件设置', 'new_url': None, 'notice': {'id': '2', 'content': '公告内容测试3', 'time': '**********'}}
2025-06-27 19:17:46.002 | INFO     | app.controllers.auth_controller:login:61 - 控制器: 用户登录 account=demo01
2025-06-27 19:17:49.033 | INFO     | core.auth.api_service:login:127 - 用户登录: account=demo01
2025-06-27 19:17:49.259 | INFO     | core.auth.api_service:get_user_info:347 - 获取用户信息
2025-06-27 19:17:49.507 | INFO     | core.auth.api_service:get_user_info:375 - 获取用户信息成功
2025-06-27 19:17:49.513 | INFO     | __main__:on_auth_completed:93 - 用户登录成功，正在启动主窗口...
2025-06-27 19:17:49.541 | INFO     | core.telegram.client_worker:__init__:61 - Telegram客户端工作线程初始化
2025-06-27 19:17:49.542 | INFO     | utils.vip_checker:initialize:26 - VIP检查器已初始化，等待登录后开始检查
2025-06-27 19:17:49.542 | INFO     | ui.main_window:_initialize_core_components:110 - MainWindow: 初始化核心组件...
2025-06-27 19:17:49.543 | INFO     | app.services.account_service:__init__:42 - 账户服务初始化
2025-06-27 19:17:49.543 | INFO     | core.proxy.proxy_task_manager:run:284 - 代理任务管理器线程启动
2025-06-27 19:17:49.544 | INFO     | core.telegram.client_worker:run:65 - Telegram客户端工作线程开始运行
2025-06-27 19:17:49.544 | INFO     | app.controllers.account_controller:__init__:81 - 账户控制器初始化
2025-06-27 19:17:49.545 | INFO     | app.services.monitor_service:__init__:39 - 初始化监控任务服务
2025-06-27 19:17:49.545 | INFO     | core.monitor.filter:__init__:26 - 初始化消息过滤器
2025-06-27 19:17:49.547 | INFO     | core.monitor.extractor:__init__:24 - 初始化关键词提取器
2025-06-27 19:17:49.547 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-06-27 19:17:49.547 | INFO     | core.monitor.notifier:__init__:90 - 初始化通知管理器
2025-06-27 19:17:49.547 | INFO     | core.telegram.client_manager:__init__:66 - Telegram客户端管理器初始化
2025-06-27 19:17:49.547 | INFO     | app.controllers.telegram_monitor_controller:__init__:64 - 初始化 Telegram 监控控制器
2025-06-27 19:17:49.548 | INFO     | core.telegram.user_manager:__init__:30 - Telegram用户管理器初始化
2025-06-27 19:17:49.548 | DEBUG    | app.controllers.telegram_monitor_controller:__init__:82 - 控制器初始化完成
2025-06-27 19:17:49.548 | INFO     | core.telegram.group_manager:__init__:36 - Telegram群组管理器初始化
2025-06-27 19:17:49.550 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-06-27 19:17:49.550 | INFO     | core.telegram.monitor_manager:__init__:26 - Telegram监控管理器初始化
2025-06-27 19:17:49.551 | INFO     | core.telegram.telegram_service:__init__:39 - Telegram服务初始化完成
2025-06-27 19:17:49.780 | INFO     | ui.views.monitor_view:__init__:48 - 初始化监控视图
2025-06-27 19:17:49.781 | DEBUG    | ui.views.monitor_view:_connect_signals:80 - 连接控制器信号
2025-06-27 19:17:50.001 | INFO     | ui.views.proxy_view:__init__:96 - 初始化视图，加载第一页代理数据
2025-06-27 19:17:50.255 | INFO     | __main__:on_auth_completed:97 - 主窗口已启动
2025-06-27 19:17:50.301 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:88 - 尝试连接监听日志信号...
2025-06-27 19:17:50.301 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:90 - 监听日志信号连接成功
2025-06-27 19:17:50.302 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 19:17:50.307 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 19:17:50.310 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:145 - 加载监控任务列表 (搜索关键词: '')
2025-06-27 19:17:50.310 | DEBUG    | ui.views.monitor_view:_on_loading_started:620 - 加载中: 正在加载任务列表...
2025-06-27 19:17:50.310 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 19:17:50.320 | INFO     | app.services.proxy_service:refresh_proxies:712 - 刷新代理列表: offset=0, limit=20
2025-06-27 19:17:50.321 | INFO     | core.proxy.proxy_task_manager:submit_task:172 - 提交耗时任务到子线程: refresh_proxies_0_20
2025-06-27 19:17:50.321 | DEBUG    | app.services.proxy_service:get_proxies:500 - 获取代理列表: offset=0, limit=20
2025-06-27 19:17:50.321 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 19:17:50.323 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 19:17:50.327 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 19:17:50.360 | INFO     | app.controllers.invite_controller:get_invite_tasks:38 - 获取邀请任务列表, status=None
2025-06-27 19:17:50.371 | INFO     | app.services.invite_service:get_invite_tasks:121 - 获取邀请任务列表, status=None
2025-06-27 19:17:50.377 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 19:17:50.390 | INFO     | app.services.invite_service:task_initialize:39 - 初始化邀请服务，重置运行中的任务为暂停状态
2025-06-27 19:17:50.391 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 19:17:50.398 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 19:17:50.400 | INFO     | core.proxy.proxy_task_manager:task_wrapper:199 - 任务 refresh_proxies_0_20 完成
2025-06-27 19:17:50.620 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 19:17:50.625 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 19:17:50.632 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 19:17:50.633 | INFO     | app.services.invite_service:task_initialize:48 - 成功重置 0 个任务状态
2025-06-27 19:17:50.633 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 19:17:50.636 | DEBUG    | ui.views.monitor_view:_refresh_task_list:175 - 刷新任务列表，共0个任务
2025-06-27 19:17:50.636 | INFO     | ui.views.monitor_view:_refresh_task_list:189 - 没有找到监控任务
2025-06-27 19:17:50.636 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 19:17:50.644 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 19:17:50.650 | INFO     | app.services.account_service:get_all_groups:96 - 获取所有账户分组成功, 共 2 个
2025-06-27 19:17:50.650 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 19:17:50.654 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 19:17:50.656 | DEBUG    | ui.views.account_view:_on_groups_loaded:446 - 分组加载完成: 2个分组
2025-06-27 19:17:50.670 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 19:17:50.752 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 19:17:50.756 | INFO     | app.services.account_service:get_all_accounts:290 - 获取所有账户成功, 共 2 个
2025-06-27 19:17:50.756 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 19:17:50.760 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-06-27 19:17:50.775 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 19:17:50.778 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 19:17:50.780 | DEBUG    | ui.views.account_view:_on_accounts_loaded:579 - 账户加载完成: 2个账户
2025-06-27 19:17:50.803 | DEBUG    | ui.views.account_view:_update_group_counts:185 - 分组计数已更新: {2: 1, 1: 1}
2025-06-27 19:17:50.807 | INFO     | ui.views.account_view:_auto_login_accounts:634 - 开始调用控制器进行 2 个账户的自动登录。
2025-06-27 19:17:50.807 | INFO     | app.services.account_service:batch_auto_login:1165 - 服务层：开始批量自动登录 2 个账户, 最大并发: 5
2025-06-27 19:17:50.807 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 19:17:50.821 | INFO     | app.controllers.telegram_monitor_controller:_load_total_users_count_internal:191 - 成功加载总体统计数据: 总用户数 23, 今日采集 0, 日均采集 23, 运行天数 1
2025-06-27 19:17:50.822 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:156 - 成功加载 0 个任务
2025-06-27 19:17:50.822 | DEBUG    | ui.views.monitor_view:_on_loading_finished:625 - 加载完成
2025-06-27 19:17:50.825 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=True
2025-06-27 19:17:50.828 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=True
2025-06-27 19:17:50.830 | INFO     | ui.views.proxy_view:_load_initial_data:1044 - 初始数据加载完成，共 1 个代理
2025-06-27 19:17:50.839 | INFO     | app.services.account_service:get_all_accounts:290 - 获取所有账户成功, 共 2 个
2025-06-27 19:17:50.839 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 19:17:50.842 | INFO     | app.services.account_service:batch_auto_login:1204 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-06-27 19:17:50.843 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 19:17:50.848 | DEBUG    | ui.views.account_view:_update_group_counts:185 - 分组计数已更新: {2: 1, 1: 1}
2025-06-27 19:17:50.849 | DEBUG    | ui.views.account_view:_init_cache:152 - 账户缓存初始化完成，共 2 个账户
2025-06-27 19:17:50.854 | DEBUG    | ui.views.account_view:_on_accounts_loaded:579 - 账户加载完成: 2个账户
2025-06-27 19:17:50.877 | DEBUG    | ui.views.account_view:_update_group_counts:185 - 分组计数已更新: {2: 1, 1: 1}
2025-06-27 19:17:50.886 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 19:17:50.898 | INFO     | app.services.account_service:batch_auto_login:1204 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-06-27 19:17:50.898 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 19:17:50.902 | INFO     | app.services.account_service:batch_auto_login:1274 - 服务层：准备调用核心层处理 2 个账户的自动登录。
2025-06-27 19:17:50.902 | INFO     | app.services.account_service:batch_auto_login:1284 - 服务层：设置核心层任务超时为 120 秒。
2025-06-27 19:17:50.903 | INFO     | core.telegram.client_manager:batch_auto_login:1216 - 开始批量自动登录, 共 2 个账户, 最大并发 5
2025-06-27 19:17:50.904 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:259 - 创建客户端: H:\PyProject\TeleTest\APPDATA\sessions\+***********, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-06-27 19:17:50.906 | DEBUG    | core.telegram.client_manager:get_proxy:96 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-06-27 19:17:50.906 | DEBUG    | core.telegram.client_manager:get_proxy:143 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-06-27 19:17:50.910 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:259 - 创建客户端: H:\PyProject\TeleTest\APPDATA\sessions\+***********, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-06-27 19:17:50.910 | DEBUG    | core.telegram.client_manager:get_proxy:96 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-06-27 19:17:50.911 | DEBUG    | core.telegram.client_manager:get_proxy:143 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-06-27 19:17:50.971 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 19:17:51.053 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 19:17:51.146 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 19:17:53.731 | INFO     | core.telegram.client_manager:_connect_client:207 - 连接成功!
2025-06-27 19:17:54.135 | INFO     | core.telegram.client_manager:_connect_client:207 - 连接成功!
2025-06-27 19:17:54.482 | INFO     | core.telegram.client_manager:_verify_client_authorization:1191 - 账户已授权: +***********
2025-06-27 19:17:55.018 | INFO     | core.telegram.client_manager:_verify_client_authorization:1191 - 账户已授权: +***********
2025-06-27 19:17:57.011 | INFO     | core.telegram.client_manager:batch_auto_login:1365 - 批量登录完成: 总计 2, 成功 2, 失败 0
2025-06-27 19:17:57.916 | INFO     | app.services.account_service:batch_auto_login:1305 - 服务层：核心层登录完成: 成功 2, 失败 0 / 2
2025-06-27 19:19:00.139 | INFO     | utils.logger:init_logger:98 - 日志系统初始化完成，默认级别: DEBUG，日志目录: H:\PyProject\TeleTest\APPDATA\logs
2025-06-27 19:19:01.976 | INFO     | data.database:init_database:31 - 初始化数据库连接: H:\PyProject\TeleTest\APPDATA\database\telegram_manager.db
2025-06-27 19:19:02.010 | DEBUG    | data.database:init_database:58 - 创建数据库表
2025-06-27 19:19:02.031 | INFO     | data.database:init_database:61 - 数据库初始化完成
2025-06-27 19:19:02.917 | DEBUG    | app.controllers.auth_controller:initialize_software:43 - 控制器: 初始化软件 app_id=1000, version=1.2.7
2025-06-27 19:19:02.918 | DEBUG    | core.auth.api_service:init_software:55 - 发送初始化请求: https://server.xile188.com/api/user/1000/windowns10/1.2.7/ini, params={}
2025-06-27 19:19:03.626 | DEBUG    | core.auth.api_service:init_software:66 - 初始化数据解密成功: {'new_content': '- 新增功能\n  - 1.新增图片上传功能\n  - 2.新增软件文档\n  - 3.新增软件自动更新，后续更新会自动检测版本更新，无需再下载更新包\n  - 4.新增设置账户信息及发信控制\n- 修复bug\n  - 删除监听任务时，不删除对应任务采集到的数据。\n  - 监听设置群无法发送到目标群\n  - 导出监听数据时应当导出单个任务而非所有数据\n- 优化记录\n  - 优化发信规则，直接调取符合要求的账户发送，无账户时才需要等待延时。\n  - 完善软件设置', 'new_url': None, 'notice': {'id': '2', 'content': '公告内容测试3', 'time': '**********'}}
2025-06-27 19:19:03.635 | INFO     | app.controllers.auth_controller:login:61 - 控制器: 用户登录 account=demo01
2025-06-27 19:19:06.716 | INFO     | core.auth.api_service:login:127 - 用户登录: account=demo01
2025-06-27 19:19:07.027 | INFO     | core.auth.api_service:get_user_info:347 - 获取用户信息
2025-06-27 19:19:07.230 | INFO     | core.auth.api_service:get_user_info:375 - 获取用户信息成功
2025-06-27 19:19:07.238 | INFO     | __main__:on_auth_completed:93 - 用户登录成功，正在启动主窗口...
2025-06-27 19:19:07.271 | INFO     | core.telegram.client_worker:__init__:61 - Telegram客户端工作线程初始化
2025-06-27 19:19:07.272 | INFO     | utils.vip_checker:initialize:26 - VIP检查器已初始化，等待登录后开始检查
2025-06-27 19:19:07.272 | INFO     | ui.main_window:_initialize_core_components:107 - MainWindow: 初始化核心组件...
2025-06-27 19:19:07.273 | INFO     | app.services.account_service:__init__:42 - 账户服务初始化
2025-06-27 19:19:07.273 | INFO     | core.proxy.proxy_task_manager:run:284 - 代理任务管理器线程启动
2025-06-27 19:19:07.274 | INFO     | app.controllers.account_controller:__init__:81 - 账户控制器初始化
2025-06-27 19:19:07.274 | INFO     | app.services.monitor_service:__init__:39 - 初始化监控任务服务
2025-06-27 19:19:07.274 | INFO     | core.monitor.filter:__init__:26 - 初始化消息过滤器
2025-06-27 19:19:07.275 | INFO     | core.telegram.client_worker:run:65 - Telegram客户端工作线程开始运行
2025-06-27 19:19:07.275 | INFO     | core.monitor.extractor:__init__:24 - 初始化关键词提取器
2025-06-27 19:19:07.275 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-06-27 19:19:07.275 | INFO     | core.monitor.notifier:__init__:90 - 初始化通知管理器
2025-06-27 19:19:07.276 | INFO     | app.controllers.telegram_monitor_controller:__init__:64 - 初始化 Telegram 监控控制器
2025-06-27 19:19:07.276 | DEBUG    | app.controllers.telegram_monitor_controller:__init__:82 - 控制器初始化完成
2025-06-27 19:19:07.280 | INFO     | core.telegram.client_manager:__init__:66 - Telegram客户端管理器初始化
2025-06-27 19:19:07.280 | INFO     | core.telegram.user_manager:__init__:30 - Telegram用户管理器初始化
2025-06-27 19:19:07.281 | INFO     | core.telegram.group_manager:__init__:36 - Telegram群组管理器初始化
2025-06-27 19:19:07.282 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-06-27 19:19:07.283 | INFO     | core.telegram.monitor_manager:__init__:26 - Telegram监控管理器初始化
2025-06-27 19:19:07.285 | INFO     | core.telegram.telegram_service:__init__:39 - Telegram服务初始化完成
2025-06-27 19:19:07.545 | INFO     | ui.views.monitor_view:__init__:48 - 初始化监控视图
2025-06-27 19:19:07.546 | DEBUG    | ui.views.monitor_view:_connect_signals:80 - 连接控制器信号
2025-06-27 19:19:07.741 | INFO     | ui.views.proxy_view:__init__:96 - 初始化视图，加载第一页代理数据
2025-06-27 19:19:08.179 | INFO     | __main__:on_auth_completed:97 - 主窗口已启动
2025-06-27 19:19:08.228 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:88 - 尝试连接监听日志信号...
2025-06-27 19:19:08.228 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:90 - 监听日志信号连接成功
2025-06-27 19:19:08.229 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 19:19:08.238 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 19:19:08.244 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:145 - 加载监控任务列表 (搜索关键词: '')
2025-06-27 19:19:08.245 | DEBUG    | ui.views.monitor_view:_on_loading_started:620 - 加载中: 正在加载任务列表...
2025-06-27 19:19:08.245 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 19:19:08.255 | INFO     | app.services.proxy_service:refresh_proxies:712 - 刷新代理列表: offset=0, limit=20
2025-06-27 19:19:08.255 | INFO     | core.proxy.proxy_task_manager:submit_task:172 - 提交耗时任务到子线程: refresh_proxies_0_20
2025-06-27 19:19:08.256 | DEBUG    | app.services.proxy_service:get_proxies:500 - 获取代理列表: offset=0, limit=20
2025-06-27 19:19:08.256 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 19:19:08.256 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 19:19:08.271 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 19:19:08.318 | INFO     | app.controllers.invite_controller:get_invite_tasks:38 - 获取邀请任务列表, status=None
2025-06-27 19:19:08.319 | INFO     | app.services.invite_service:get_invite_tasks:121 - 获取邀请任务列表, status=None
2025-06-27 19:19:08.319 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 19:19:08.323 | INFO     | app.services.invite_service:task_initialize:39 - 初始化邀请服务，重置运行中的任务为暂停状态
2025-06-27 19:19:08.324 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 19:19:08.326 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 19:19:08.328 | INFO     | core.proxy.proxy_task_manager:task_wrapper:199 - 任务 refresh_proxies_0_20 完成
2025-06-27 19:19:10.816 | INFO     | utils.vip_checker:start_checking:38 - 开始VIP状态检查
2025-06-27 19:19:10.816 | DEBUG    | utils.vip_checker:_check_vip_status:61 - 正在检查VIP状态...
2025-06-27 19:19:10.816 | INFO     | core.auth.api_service:verify_vip:425 - 会员验证接口调用
2025-06-27 19:19:10.953 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 19:19:10.960 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 19:19:10.974 | DEBUG    | ui.views.monitor_view:_refresh_task_list:175 - 刷新任务列表，共0个任务
2025-06-27 19:19:10.974 | INFO     | ui.views.monitor_view:_refresh_task_list:189 - 没有找到监控任务
2025-06-27 19:19:10.975 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 19:19:10.980 | INFO     | app.services.invite_service:task_initialize:48 - 成功重置 0 个任务状态
2025-06-27 19:19:10.980 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 19:19:10.983 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 19:19:10.985 | INFO     | app.services.account_service:get_all_groups:96 - 获取所有账户分组成功, 共 2 个
2025-06-27 19:19:10.985 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 19:19:10.992 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 19:19:10.993 | DEBUG    | ui.views.account_view:_on_groups_loaded:446 - 分组加载完成: 2个分组
2025-06-27 19:19:11.007 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 19:19:11.016 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 19:19:11.102 | INFO     | app.services.account_service:get_all_accounts:290 - 获取所有账户成功, 共 2 个
2025-06-27 19:19:11.103 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 19:19:11.109 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 19:19:11.111 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 19:19:11.113 | DEBUG    | ui.views.account_view:_on_accounts_loaded:579 - 账户加载完成: 2个账户
2025-06-27 19:19:11.135 | DEBUG    | ui.views.account_view:_update_group_counts:185 - 分组计数已更新: {2: 1, 1: 1}
2025-06-27 19:19:11.140 | INFO     | ui.views.account_view:_auto_login_accounts:634 - 开始调用控制器进行 2 个账户的自动登录。
2025-06-27 19:19:11.140 | INFO     | app.services.account_service:batch_auto_login:1165 - 服务层：开始批量自动登录 2 个账户, 最大并发: 5
2025-06-27 19:19:11.140 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 19:19:11.144 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 19:19:11.156 | INFO     | app.controllers.telegram_monitor_controller:_load_total_users_count_internal:191 - 成功加载总体统计数据: 总用户数 23, 今日采集 0, 日均采集 23, 运行天数 1
2025-06-27 19:19:11.157 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:156 - 成功加载 0 个任务
2025-06-27 19:19:11.157 | DEBUG    | ui.views.monitor_view:_on_loading_finished:625 - 加载完成
2025-06-27 19:19:11.161 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-06-27 19:19:11.182 | INFO     | app.services.account_service:get_all_accounts:290 - 获取所有账户成功, 共 2 个
2025-06-27 19:19:11.182 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 19:19:11.185 | INFO     | app.services.account_service:batch_auto_login:1204 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-06-27 19:19:11.185 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 19:19:11.186 | DEBUG    | ui.views.account_view:_update_group_counts:185 - 分组计数已更新: {2: 1, 1: 1}
2025-06-27 19:19:11.187 | DEBUG    | ui.views.account_view:_init_cache:152 - 账户缓存初始化完成，共 2 个账户
2025-06-27 19:19:11.192 | DEBUG    | ui.views.account_view:_on_accounts_loaded:579 - 账户加载完成: 2个账户
2025-06-27 19:19:11.213 | DEBUG    | ui.views.account_view:_update_group_counts:185 - 分组计数已更新: {2: 1, 1: 1}
2025-06-27 19:19:11.224 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=True
2025-06-27 19:19:11.226 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=True
2025-06-27 19:19:11.228 | INFO     | ui.views.proxy_view:_load_initial_data:1044 - 初始数据加载完成，共 1 个代理
2025-06-27 19:19:11.228 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 19:19:11.237 | INFO     | app.services.account_service:batch_auto_login:1204 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-06-27 19:19:11.237 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 19:19:11.239 | INFO     | app.services.account_service:batch_auto_login:1274 - 服务层：准备调用核心层处理 2 个账户的自动登录。
2025-06-27 19:19:11.240 | INFO     | app.services.account_service:batch_auto_login:1284 - 服务层：设置核心层任务超时为 120 秒。
2025-06-27 19:19:11.240 | INFO     | core.telegram.client_manager:batch_auto_login:1216 - 开始批量自动登录, 共 2 个账户, 最大并发 5
2025-06-27 19:19:11.241 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:259 - 创建客户端: H:\PyProject\TeleTest\APPDATA\sessions\+***********, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-06-27 19:19:11.243 | DEBUG    | core.telegram.client_manager:get_proxy:96 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-06-27 19:19:11.243 | DEBUG    | core.telegram.client_manager:get_proxy:143 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-06-27 19:19:11.248 | INFO     | core.auth.api_service:verify_vip:439 - 会员验证成功
2025-06-27 19:19:11.250 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:259 - 创建客户端: H:\PyProject\TeleTest\APPDATA\sessions\+***********, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-06-27 19:19:11.250 | DEBUG    | core.telegram.client_manager:get_proxy:96 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-06-27 19:19:11.250 | DEBUG    | core.telegram.client_manager:get_proxy:143 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-06-27 19:19:11.249 | ERROR    | utils.vip_checker:_check_vip_status:92 - VIP状态检查出现异常: 'int' object has no attribute 'get'
Traceback (most recent call last):

  File "D:\Program Files\JetBrains\PyCharm 2025.1.1\plugins\python-ce\helpers\pydev\pydevd.py", line 2270, in <module>
    main()
    └ <function main at 0x0000018E850DC680>

  File "D:\Program Files\JetBrains\PyCharm 2025.1.1\plugins\python-ce\helpers\pydev\pydevd.py", line 2252, in main
    globals = debugger.run(setup['file'], None, None, is_module)
              │        │   │                          └ False
              │        │   └ {'port': 42492, 'vm_type': None, 'client': '127.0.0.1', 'server': False, 'DEBUG_RECORD_SOCKET_READS': False, 'multiproc': Fal...
              │        └ <function PyDB.run at 0x0000018E850DB420>
              └ <__main__.PyDB object at 0x0000018EF1BBA890>

  File "D:\Program Files\JetBrains\PyCharm 2025.1.1\plugins\python-ce\helpers\pydev\pydevd.py", line 1563, in run
    return self._exec(is_module, entry_point_fn, module_name, file, globals, locals)
           │    │     │          │               │            │     │        └ {'__name__': '__main__', '__doc__': '\nTelegram多账户管理系统 - 主程序入口\n', '__package__': '', '__loader__': <_frozen_importlib_extern...
           │    │     │          │               │            │     └ {'__name__': '__main__', '__doc__': '\nTelegram多账户管理系统 - 主程序入口\n', '__package__': '', '__loader__': <_frozen_importlib_extern...
           │    │     │          │               │            └ 'H:\\PyProject\\TeleTest\\main.py'
           │    │     │          │               └ None
           │    │     │          └ ''
           │    │     └ False
           │    └ <function PyDB._exec at 0x0000018E850DB4C0>
           └ <__main__.PyDB object at 0x0000018EF1BBA890>

  File "D:\Program Files\JetBrains\PyCharm 2025.1.1\plugins\python-ce\helpers\pydev\pydevd.py", line 1570, in _exec
    pydev_imports.execfile(file, globals, locals)  # execute the script
    │             │        │     │        └ {'__name__': '__main__', '__doc__': '\nTelegram多账户管理系统 - 主程序入口\n', '__package__': '', '__loader__': <_frozen_importlib_extern...
    │             │        │     └ {'__name__': '__main__', '__doc__': '\nTelegram多账户管理系统 - 主程序入口\n', '__package__': '', '__loader__': <_frozen_importlib_extern...
    │             │        └ 'H:\\PyProject\\TeleTest\\main.py'
    │             └ <function execfile at 0x0000018E84CF9F80>
    └ <module '_pydev_bundle.pydev_imports' from 'D:\\Program Files\\JetBrains\\PyCharm 2025.1.1\\plugins\\python-ce\\helpers\\pyde...

  File "D:\Program Files\JetBrains\PyCharm 2025.1.1\plugins\python-ce\helpers\pydev\_pydev_imps\_pydev_execfile.py", line 18, in execfile
    exec(compile(contents+"\n", file, 'exec'), glob, loc)
                 │              │              │     └ {'__name__': '__main__', '__doc__': '\nTelegram多账户管理系统 - 主程序入口\n', '__package__': '', '__loader__': <_frozen_importlib_extern...
                 │              │              └ {'__name__': '__main__', '__doc__': '\nTelegram多账户管理系统 - 主程序入口\n', '__package__': '', '__loader__': <_frozen_importlib_extern...
                 │              └ 'H:\\PyProject\\TeleTest\\main.py'
                 └ '#!/usr/bin/env python\n# -*- coding: utf-8 -*-\n\n"""\nTelegram多账户管理系统 - 主程序入口\n"""\nimport sys\nimport os\nimport asyncio\n...

  File "H:\PyProject\TeleTest\main.py", line 118, in <module>
    main()
    └ <function main at 0x0000018E99083D80>

  File "H:\PyProject\TeleTest\main.py", line 109, in main
    event_loop.run_forever()
    │          └ <function _QEventLoop.run_forever at 0x0000018E854947C0>
    └ <QIOCPEventLoop running=True closed=False debug=False>

  File "H:\PyProject\TeleTest\script\Lib\site-packages\qasync\__init__.py", line 377, in run_forever
    rslt = self.__app.exec()
           └ <QIOCPEventLoop running=True closed=False debug=False>
  File "H:\PyProject\TeleTest\script\Lib\site-packages\qasync\__init__.py", line 279, in timerEvent
    handle._run()
    │      └ <function Handle._run at 0x0000018E84F9BC40>
    └ <Handle Task.task_wakeup(<Task finishe...> result=None>)>
  File "D:\Program Files\python11\Lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle Task.task_wakeup(<Task finishe...> result=None>)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle Task.task_wakeup(<Task finishe...> result=None>)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle Task.task_wakeup(<Task finishe...> result=None>)>

  File "H:\PyProject\TeleTest\ui\main_window.py", line 293, in on_login_succeeded
    await vip_checker.start_checking()
          │           └ <function VipChecker.start_checking at 0x0000018E85B394E0>
          └ <utils.vip_checker.VipChecker(0x18e87078450) at 0x0000018E85B2F180>

  File "H:\PyProject\TeleTest\utils\vip_checker.py", line 42, in start_checking
    await self._on_timer_timeout()
          │    └ <function VipChecker._on_timer_timeout at 0x0000018E85B39620>
          └ <utils.vip_checker.VipChecker(0x18e87078450) at 0x0000018E85B2F180>

  File "H:\PyProject\TeleTest\utils\vip_checker.py", line 54, in _on_timer_timeout
    await self._check_vip_status()
          │    └ <function VipChecker._check_vip_status at 0x0000018E85B396C0>
          └ <utils.vip_checker.VipChecker(0x18e87078450) at 0x0000018E85B2F180>

> File "H:\PyProject\TeleTest\utils\vip_checker.py", line 75, in _check_vip_status
    is_vip = data.get('is_vip', True)  # 默认为True，兼容老接口
             └ 1751023150

AttributeError: 'int' object has no attribute 'get'
2025-06-27 19:19:14.451 | INFO     | core.telegram.client_manager:_connect_client:207 - 连接成功!
2025-06-27 19:19:14.630 | INFO     | core.telegram.client_manager:_connect_client:207 - 连接成功!
2025-06-27 19:19:14.757 | ERROR    | data.repositories.message_repo:get_task_target_stats:187 - 获取任务4目标统计失败: Cannot switch to a different thread
	Current:  <greenlet.greenlet object at 0x0000018ED71B7C80 (otid=0x0000018ED71E5470) current active started main>
	Expected: <greenlet.greenlet object at 0x0000018E856EC540 (otid=0x0000018E8568B810) suspended active started main>
2025-06-27 19:19:14.757 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 19:19:14.765 | ERROR    | ui.views.send_msg_view:load_tasks:50 - 加载任务列表失败: coroutine ignored GeneratorExit
2025-06-27 19:20:16.031 | INFO     | utils.logger:init_logger:98 - 日志系统初始化完成，默认级别: DEBUG，日志目录: H:\PyProject\TeleTest\APPDATA\logs
2025-06-27 19:20:17.731 | INFO     | data.database:init_database:31 - 初始化数据库连接: H:\PyProject\TeleTest\APPDATA\database\telegram_manager.db
2025-06-27 19:20:17.762 | DEBUG    | data.database:init_database:58 - 创建数据库表
2025-06-27 19:20:17.783 | INFO     | data.database:init_database:61 - 数据库初始化完成
2025-06-27 19:20:18.615 | DEBUG    | app.controllers.auth_controller:initialize_software:43 - 控制器: 初始化软件 app_id=1000, version=1.2.7
2025-06-27 19:20:18.616 | DEBUG    | core.auth.api_service:init_software:55 - 发送初始化请求: https://server.xile188.com/api/user/1000/windowns10/1.2.7/ini, params={}
2025-06-27 19:20:18.914 | DEBUG    | core.auth.api_service:init_software:66 - 初始化数据解密成功: {'new_content': '- 新增功能\n  - 1.新增图片上传功能\n  - 2.新增软件文档\n  - 3.新增软件自动更新，后续更新会自动检测版本更新，无需再下载更新包\n  - 4.新增设置账户信息及发信控制\n- 修复bug\n  - 删除监听任务时，不删除对应任务采集到的数据。\n  - 监听设置群无法发送到目标群\n  - 导出监听数据时应当导出单个任务而非所有数据\n- 优化记录\n  - 优化发信规则，直接调取符合要求的账户发送，无账户时才需要等待延时。\n  - 完善软件设置', 'new_url': None, 'notice': {'id': '2', 'content': '公告内容测试3', 'time': '**********'}}
2025-06-27 19:20:18.924 | INFO     | app.controllers.auth_controller:login:61 - 控制器: 用户登录 account=demo01
2025-06-27 19:20:21.963 | INFO     | core.auth.api_service:login:127 - 用户登录: account=demo01
2025-06-27 19:20:22.186 | INFO     | core.auth.api_service:get_user_info:347 - 获取用户信息
2025-06-27 19:20:22.394 | INFO     | core.auth.api_service:get_user_info:375 - 获取用户信息成功
2025-06-27 19:20:22.401 | INFO     | __main__:on_auth_completed:93 - 用户登录成功，正在启动主窗口...
2025-06-27 19:20:22.434 | INFO     | core.telegram.client_worker:__init__:61 - Telegram客户端工作线程初始化
2025-06-27 19:20:22.435 | INFO     | utils.vip_checker:initialize:26 - VIP检查器已初始化，等待登录后开始检查
2025-06-27 19:20:22.435 | INFO     | ui.main_window:_initialize_core_components:107 - MainWindow: 初始化核心组件...
2025-06-27 19:20:22.436 | INFO     | core.telegram.client_worker:run:65 - Telegram客户端工作线程开始运行
2025-06-27 19:20:22.437 | INFO     | app.services.account_service:__init__:42 - 账户服务初始化
2025-06-27 19:20:22.438 | INFO     | core.proxy.proxy_task_manager:run:284 - 代理任务管理器线程启动
2025-06-27 19:20:22.439 | INFO     | app.controllers.account_controller:__init__:81 - 账户控制器初始化
2025-06-27 19:20:22.439 | INFO     | app.services.monitor_service:__init__:39 - 初始化监控任务服务
2025-06-27 19:20:22.440 | INFO     | core.monitor.filter:__init__:26 - 初始化消息过滤器
2025-06-27 19:20:22.440 | INFO     | core.telegram.client_manager:__init__:66 - Telegram客户端管理器初始化
2025-06-27 19:20:22.440 | INFO     | core.monitor.extractor:__init__:24 - 初始化关键词提取器
2025-06-27 19:20:22.441 | INFO     | core.telegram.user_manager:__init__:30 - Telegram用户管理器初始化
2025-06-27 19:20:22.441 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-06-27 19:20:22.441 | INFO     | core.telegram.group_manager:__init__:36 - Telegram群组管理器初始化
2025-06-27 19:20:22.441 | INFO     | core.monitor.notifier:__init__:90 - 初始化通知管理器
2025-06-27 19:20:22.442 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-06-27 19:20:22.442 | INFO     | app.controllers.telegram_monitor_controller:__init__:64 - 初始化 Telegram 监控控制器
2025-06-27 19:20:22.442 | INFO     | core.telegram.monitor_manager:__init__:26 - Telegram监控管理器初始化
2025-06-27 19:20:22.442 | DEBUG    | app.controllers.telegram_monitor_controller:__init__:82 - 控制器初始化完成
2025-06-27 19:20:22.442 | INFO     | core.telegram.telegram_service:__init__:39 - Telegram服务初始化完成
2025-06-27 19:20:22.683 | INFO     | ui.views.monitor_view:__init__:48 - 初始化监控视图
2025-06-27 19:20:22.684 | DEBUG    | ui.views.monitor_view:_connect_signals:80 - 连接控制器信号
2025-06-27 19:20:22.900 | INFO     | ui.views.proxy_view:__init__:96 - 初始化视图，加载第一页代理数据
2025-06-27 19:20:23.175 | INFO     | __main__:on_auth_completed:97 - 主窗口已启动
2025-06-27 19:20:23.225 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:88 - 尝试连接监听日志信号...
2025-06-27 19:20:23.226 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:90 - 监听日志信号连接成功
2025-06-27 19:20:23.226 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 19:20:23.232 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 19:20:23.236 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:145 - 加载监控任务列表 (搜索关键词: '')
2025-06-27 19:20:23.237 | DEBUG    | ui.views.monitor_view:_on_loading_started:620 - 加载中: 正在加载任务列表...
2025-06-27 19:20:23.237 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 19:20:23.245 | INFO     | app.services.proxy_service:refresh_proxies:712 - 刷新代理列表: offset=0, limit=20
2025-06-27 19:20:23.245 | INFO     | core.proxy.proxy_task_manager:submit_task:172 - 提交耗时任务到子线程: refresh_proxies_0_20
2025-06-27 19:20:23.245 | DEBUG    | app.services.proxy_service:get_proxies:500 - 获取代理列表: offset=0, limit=20
2025-06-27 19:20:23.246 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 19:20:23.246 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 19:20:23.254 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 19:20:23.275 | INFO     | app.controllers.invite_controller:get_invite_tasks:38 - 获取邀请任务列表, status=None
2025-06-27 19:20:23.275 | INFO     | app.services.invite_service:get_invite_tasks:121 - 获取邀请任务列表, status=None
2025-06-27 19:20:23.275 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 19:20:23.291 | INFO     | app.services.invite_service:task_initialize:39 - 初始化邀请服务，重置运行中的任务为暂停状态
2025-06-27 19:20:23.311 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 19:20:23.319 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 19:20:23.321 | INFO     | core.proxy.proxy_task_manager:task_wrapper:199 - 任务 refresh_proxies_0_20 完成
2025-06-27 19:20:25.837 | INFO     | utils.vip_checker:start_checking:38 - 开始VIP状态检查
2025-06-27 19:20:25.837 | DEBUG    | utils.vip_checker:_check_vip_status:61 - 正在检查VIP状态...
2025-06-27 19:20:25.838 | INFO     | core.auth.api_service:verify_vip:425 - 会员验证接口调用
2025-06-27 19:20:25.980 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 19:20:25.982 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 19:20:26.000 | DEBUG    | ui.views.monitor_view:_refresh_task_list:175 - 刷新任务列表，共0个任务
2025-06-27 19:20:26.000 | INFO     | ui.views.monitor_view:_refresh_task_list:189 - 没有找到监控任务
2025-06-27 19:20:26.001 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 19:20:26.003 | INFO     | app.services.invite_service:task_initialize:48 - 成功重置 0 个任务状态
2025-06-27 19:20:26.003 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 19:20:26.008 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 19:20:26.014 | INFO     | app.services.account_service:get_all_groups:96 - 获取所有账户分组成功, 共 2 个
2025-06-27 19:20:26.015 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 19:20:26.015 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 19:20:26.021 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 19:20:26.024 | DEBUG    | ui.views.account_view:_on_groups_loaded:446 - 分组加载完成: 2个分组
2025-06-27 19:20:26.039 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 19:20:26.134 | INFO     | app.services.account_service:get_all_accounts:290 - 获取所有账户成功, 共 2 个
2025-06-27 19:20:26.134 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 19:20:26.135 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 19:20:26.136 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 19:20:26.139 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 19:20:26.142 | INFO     | app.controllers.telegram_monitor_controller:_load_total_users_count_internal:191 - 成功加载总体统计数据: 总用户数 23, 今日采集 0, 日均采集 23, 运行天数 1
2025-06-27 19:20:26.142 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:156 - 成功加载 0 个任务
2025-06-27 19:20:26.142 | DEBUG    | ui.views.monitor_view:_on_loading_finished:625 - 加载完成
2025-06-27 19:20:26.142 | DEBUG    | ui.views.account_view:_on_accounts_loaded:579 - 账户加载完成: 2个账户
2025-06-27 19:20:26.163 | DEBUG    | ui.views.account_view:_update_group_counts:185 - 分组计数已更新: {2: 1, 1: 1}
2025-06-27 19:20:26.167 | INFO     | ui.views.account_view:_auto_login_accounts:634 - 开始调用控制器进行 2 个账户的自动登录。
2025-06-27 19:20:26.168 | INFO     | app.services.account_service:batch_auto_login:1165 - 服务层：开始批量自动登录 2 个账户, 最大并发: 5
2025-06-27 19:20:26.168 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 19:20:26.174 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-06-27 19:20:26.209 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=True
2025-06-27 19:20:26.211 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=True
2025-06-27 19:20:26.213 | INFO     | ui.views.proxy_view:_load_initial_data:1044 - 初始数据加载完成，共 1 个代理
2025-06-27 19:20:26.215 | INFO     | app.services.account_service:batch_auto_login:1204 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-06-27 19:20:26.215 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 19:20:26.216 | INFO     | app.services.account_service:get_all_accounts:290 - 获取所有账户成功, 共 2 个
2025-06-27 19:20:26.216 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 19:20:26.220 | DEBUG    | ui.views.account_view:_update_group_counts:185 - 分组计数已更新: {2: 1, 1: 1}
2025-06-27 19:20:26.220 | DEBUG    | ui.views.account_view:_init_cache:152 - 账户缓存初始化完成，共 2 个账户
2025-06-27 19:20:26.224 | DEBUG    | ui.views.account_view:_on_accounts_loaded:579 - 账户加载完成: 2个账户
2025-06-27 19:20:26.249 | DEBUG    | ui.views.account_view:_update_group_counts:185 - 分组计数已更新: {2: 1, 1: 1}
2025-06-27 19:20:26.255 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 19:20:26.262 | INFO     | app.services.account_service:batch_auto_login:1204 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-06-27 19:20:26.262 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 19:20:26.266 | INFO     | app.services.account_service:batch_auto_login:1274 - 服务层：准备调用核心层处理 2 个账户的自动登录。
2025-06-27 19:20:26.267 | INFO     | app.services.account_service:batch_auto_login:1284 - 服务层：设置核心层任务超时为 120 秒。
2025-06-27 19:20:26.267 | INFO     | core.telegram.client_manager:batch_auto_login:1216 - 开始批量自动登录, 共 2 个账户, 最大并发 5
2025-06-27 19:20:26.268 | INFO     | core.auth.api_service:verify_vip:439 - 会员验证成功
2025-06-27 19:20:26.268 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:259 - 创建客户端: H:\PyProject\TeleTest\APPDATA\sessions\+***********, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-06-27 19:20:26.268 | DEBUG    | core.telegram.client_manager:get_proxy:96 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-06-27 19:20:26.268 | DEBUG    | core.telegram.client_manager:get_proxy:143 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-06-27 19:20:26.288 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:259 - 创建客户端: H:\PyProject\TeleTest\APPDATA\sessions\+***********, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-06-27 19:20:26.289 | DEBUG    | core.telegram.client_manager:get_proxy:96 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-06-27 19:20:26.291 | DEBUG    | core.telegram.client_manager:get_proxy:143 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-06-27 19:20:26.268 | ERROR    | utils.vip_checker:_check_vip_status:92 - VIP状态检查出现异常: 'int' object has no attribute 'get'
Traceback (most recent call last):

  File "D:\Program Files\JetBrains\PyCharm 2025.1.1\plugins\python-ce\helpers\pydev\pydevd.py", line 2270, in <module>
    main()
    └ <function main at 0x000001B88701C680>

  File "D:\Program Files\JetBrains\PyCharm 2025.1.1\plugins\python-ce\helpers\pydev\pydevd.py", line 2252, in main
    globals = debugger.run(setup['file'], None, None, is_module)
              │        │   │                          └ False
              │        │   └ {'port': 42604, 'vm_type': None, 'client': '127.0.0.1', 'server': False, 'DEBUG_RECORD_SOCKET_READS': False, 'multiproc': Fal...
              │        └ <function PyDB.run at 0x000001B88701B420>
              └ <__main__.PyDB object at 0x000001B8F3B8A890>

  File "D:\Program Files\JetBrains\PyCharm 2025.1.1\plugins\python-ce\helpers\pydev\pydevd.py", line 1563, in run
    return self._exec(is_module, entry_point_fn, module_name, file, globals, locals)
           │    │     │          │               │            │     │        └ {'__name__': '__main__', '__doc__': '\nTelegram多账户管理系统 - 主程序入口\n', '__package__': '', '__loader__': <_frozen_importlib_extern...
           │    │     │          │               │            │     └ {'__name__': '__main__', '__doc__': '\nTelegram多账户管理系统 - 主程序入口\n', '__package__': '', '__loader__': <_frozen_importlib_extern...
           │    │     │          │               │            └ 'H:\\PyProject\\TeleTest\\main.py'
           │    │     │          │               └ None
           │    │     │          └ ''
           │    │     └ False
           │    └ <function PyDB._exec at 0x000001B88701B4C0>
           └ <__main__.PyDB object at 0x000001B8F3B8A890>

  File "D:\Program Files\JetBrains\PyCharm 2025.1.1\plugins\python-ce\helpers\pydev\pydevd.py", line 1570, in _exec
    pydev_imports.execfile(file, globals, locals)  # execute the script
    │             │        │     │        └ {'__name__': '__main__', '__doc__': '\nTelegram多账户管理系统 - 主程序入口\n', '__package__': '', '__loader__': <_frozen_importlib_extern...
    │             │        │     └ {'__name__': '__main__', '__doc__': '\nTelegram多账户管理系统 - 主程序入口\n', '__package__': '', '__loader__': <_frozen_importlib_extern...
    │             │        └ 'H:\\PyProject\\TeleTest\\main.py'
    │             └ <function execfile at 0x000001B886C39F80>
    └ <module '_pydev_bundle.pydev_imports' from 'D:\\Program Files\\JetBrains\\PyCharm 2025.1.1\\plugins\\python-ce\\helpers\\pyde...

  File "D:\Program Files\JetBrains\PyCharm 2025.1.1\plugins\python-ce\helpers\pydev\_pydev_imps\_pydev_execfile.py", line 18, in execfile
    exec(compile(contents+"\n", file, 'exec'), glob, loc)
                 │              │              │     └ {'__name__': '__main__', '__doc__': '\nTelegram多账户管理系统 - 主程序入口\n', '__package__': '', '__loader__': <_frozen_importlib_extern...
                 │              │              └ {'__name__': '__main__', '__doc__': '\nTelegram多账户管理系统 - 主程序入口\n', '__package__': '', '__loader__': <_frozen_importlib_extern...
                 │              └ 'H:\\PyProject\\TeleTest\\main.py'
                 └ '#!/usr/bin/env python\n# -*- coding: utf-8 -*-\n\n"""\nTelegram多账户管理系统 - 主程序入口\n"""\nimport sys\nimport os\nimport asyncio\n...

  File "H:\PyProject\TeleTest\main.py", line 118, in <module>
    main()
    └ <function main at 0x000001B89AFD7EC0>

  File "H:\PyProject\TeleTest\main.py", line 109, in main
    event_loop.run_forever()
    │          └ <function _QEventLoop.run_forever at 0x000001B8873D4860>
    └ <QIOCPEventLoop running=True closed=False debug=False>

  File "H:\PyProject\TeleTest\script\Lib\site-packages\qasync\__init__.py", line 377, in run_forever
    rslt = self.__app.exec()
           └ <QIOCPEventLoop running=True closed=False debug=False>
  File "H:\PyProject\TeleTest\script\Lib\site-packages\qasync\__init__.py", line 279, in timerEvent
    handle._run()
    │      └ <function Handle._run at 0x000001B886EDBC40>
    └ <Handle Task.task_wakeup(<Task finishe...> result=None>)>
  File "D:\Program Files\python11\Lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle Task.task_wakeup(<Task finishe...> result=None>)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle Task.task_wakeup(<Task finishe...> result=None>)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle Task.task_wakeup(<Task finishe...> result=None>)>

  File "H:\PyProject\TeleTest\ui\main_window.py", line 293, in on_login_succeeded
    await vip_checker.start_checking()
          │           └ <function VipChecker.start_checking at 0x000001B887A79620>
          └ <utils.vip_checker.VipChecker(0x1b8997c56b0) at 0x000001B887A6B700>

  File "H:\PyProject\TeleTest\utils\vip_checker.py", line 42, in start_checking
    await self._on_timer_timeout()
          │    └ <function VipChecker._on_timer_timeout at 0x000001B887A79760>
          └ <utils.vip_checker.VipChecker(0x1b8997c56b0) at 0x000001B887A6B700>

  File "H:\PyProject\TeleTest\utils\vip_checker.py", line 54, in _on_timer_timeout
    await self._check_vip_status()
          │    └ <function VipChecker._check_vip_status at 0x000001B887A79800>
          └ <utils.vip_checker.VipChecker(0x1b8997c56b0) at 0x000001B887A6B700>

> File "H:\PyProject\TeleTest\utils\vip_checker.py", line 75, in _check_vip_status
    is_vip = data.get('is_vip', True)  # 默认为True，兼容老接口
             └ 1751023226

AttributeError: 'int' object has no attribute 'get'
2025-06-27 19:20:28.929 | INFO     | core.telegram.client_manager:_connect_client:207 - 连接成功!
2025-06-27 19:20:29.717 | INFO     | core.telegram.client_manager:_verify_client_authorization:1191 - 账户已授权: +***********
2025-06-27 19:20:30.949 | INFO     | core.telegram.client_manager:_connect_client:207 - 连接成功!
2025-06-27 19:20:31.184 | ERROR    | data.repositories.message_repo:get_task_target_stats:187 - 获取任务4目标统计失败: Cannot switch to a different thread
	Current:  <greenlet.greenlet object at 0x000001B8D9177FC0 (otid=0x000001B8D9149CE0) current active started main>
	Expected: <greenlet.greenlet object at 0x000001B887624700 (otid=0x000001B8875CB810) suspended active started main>
2025-06-27 19:20:31.184 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 19:20:31.188 | ERROR    | ui.views.send_msg_view:load_tasks:50 - 加载任务列表失败: coroutine ignored GeneratorExit
2025-06-27 19:22:25.422 | INFO     | utils.logger:init_logger:98 - 日志系统初始化完成，默认级别: DEBUG，日志目录: H:\PyProject\TeleTest\APPDATA\logs
2025-06-27 19:22:27.125 | INFO     | data.database:init_database:31 - 初始化数据库连接: H:\PyProject\TeleTest\APPDATA\database\telegram_manager.db
2025-06-27 19:22:27.157 | DEBUG    | data.database:init_database:58 - 创建数据库表
2025-06-27 19:22:27.177 | INFO     | data.database:init_database:61 - 数据库初始化完成
2025-06-27 19:22:27.949 | DEBUG    | app.controllers.auth_controller:initialize_software:43 - 控制器: 初始化软件 app_id=1000, version=1.2.7
2025-06-27 19:22:27.950 | DEBUG    | core.auth.api_service:init_software:55 - 发送初始化请求: https://server.xile188.com/api/user/1000/windowns10/1.2.7/ini, params={}
2025-06-27 19:22:28.274 | DEBUG    | core.auth.api_service:init_software:66 - 初始化数据解密成功: {'new_content': '- 新增功能\n  - 1.新增图片上传功能\n  - 2.新增软件文档\n  - 3.新增软件自动更新，后续更新会自动检测版本更新，无需再下载更新包\n  - 4.新增设置账户信息及发信控制\n- 修复bug\n  - 删除监听任务时，不删除对应任务采集到的数据。\n  - 监听设置群无法发送到目标群\n  - 导出监听数据时应当导出单个任务而非所有数据\n- 优化记录\n  - 优化发信规则，直接调取符合要求的账户发送，无账户时才需要等待延时。\n  - 完善软件设置', 'new_url': None, 'notice': {'id': '2', 'content': '公告内容测试3', 'time': '**********'}}
2025-06-27 19:22:28.284 | INFO     | app.controllers.auth_controller:login:61 - 控制器: 用户登录 account=demo01
2025-06-27 19:22:31.300 | INFO     | core.auth.api_service:login:127 - 用户登录: account=demo01
2025-06-27 19:22:31.590 | INFO     | core.auth.api_service:get_user_info:347 - 获取用户信息
2025-06-27 19:22:31.869 | INFO     | core.auth.api_service:get_user_info:375 - 获取用户信息成功
2025-06-27 19:22:31.875 | INFO     | __main__:on_auth_completed:93 - 用户登录成功，正在启动主窗口...
2025-06-27 19:22:31.902 | INFO     | core.telegram.client_worker:__init__:61 - Telegram客户端工作线程初始化
2025-06-27 19:22:31.902 | INFO     | utils.vip_checker:initialize:26 - VIP检查器已初始化，等待登录后开始检查
2025-06-27 19:22:31.903 | INFO     | ui.main_window:_initialize_core_components:107 - MainWindow: 初始化核心组件...
2025-06-27 19:22:31.904 | INFO     | core.telegram.client_worker:run:65 - Telegram客户端工作线程开始运行
2025-06-27 19:22:31.904 | INFO     | app.services.account_service:__init__:42 - 账户服务初始化
2025-06-27 19:22:31.904 | INFO     | core.proxy.proxy_task_manager:run:284 - 代理任务管理器线程启动
2025-06-27 19:22:31.905 | INFO     | app.controllers.account_controller:__init__:81 - 账户控制器初始化
2025-06-27 19:22:31.905 | INFO     | app.services.monitor_service:__init__:39 - 初始化监控任务服务
2025-06-27 19:22:31.905 | INFO     | core.monitor.filter:__init__:26 - 初始化消息过滤器
2025-06-27 19:22:31.905 | INFO     | core.monitor.extractor:__init__:24 - 初始化关键词提取器
2025-06-27 19:22:31.906 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-06-27 19:22:31.906 | INFO     | core.monitor.notifier:__init__:90 - 初始化通知管理器
2025-06-27 19:22:31.907 | INFO     | app.controllers.telegram_monitor_controller:__init__:64 - 初始化 Telegram 监控控制器
2025-06-27 19:22:31.907 | INFO     | core.telegram.client_manager:__init__:66 - Telegram客户端管理器初始化
2025-06-27 19:22:31.907 | DEBUG    | app.controllers.telegram_monitor_controller:__init__:82 - 控制器初始化完成
2025-06-27 19:22:31.908 | INFO     | core.telegram.user_manager:__init__:30 - Telegram用户管理器初始化
2025-06-27 19:22:31.909 | INFO     | core.telegram.group_manager:__init__:36 - Telegram群组管理器初始化
2025-06-27 19:22:31.909 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-06-27 19:22:31.910 | INFO     | core.telegram.monitor_manager:__init__:26 - Telegram监控管理器初始化
2025-06-27 19:22:31.910 | INFO     | core.telegram.telegram_service:__init__:39 - Telegram服务初始化完成
2025-06-27 19:22:32.196 | INFO     | ui.views.monitor_view:__init__:48 - 初始化监控视图
2025-06-27 19:22:32.197 | DEBUG    | ui.views.monitor_view:_connect_signals:80 - 连接控制器信号
2025-06-27 19:22:32.411 | INFO     | ui.views.proxy_view:__init__:96 - 初始化视图，加载第一页代理数据
2025-06-27 19:22:32.685 | INFO     | __main__:on_auth_completed:97 - 主窗口已启动
2025-06-27 19:22:32.739 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:88 - 尝试连接监听日志信号...
2025-06-27 19:22:32.739 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:90 - 监听日志信号连接成功
2025-06-27 19:22:32.740 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 19:22:32.745 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 19:22:32.750 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:145 - 加载监控任务列表 (搜索关键词: '')
2025-06-27 19:22:32.750 | DEBUG    | ui.views.monitor_view:_on_loading_started:620 - 加载中: 正在加载任务列表...
2025-06-27 19:22:32.750 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 19:22:32.758 | INFO     | app.services.proxy_service:refresh_proxies:712 - 刷新代理列表: offset=0, limit=20
2025-06-27 19:22:32.758 | INFO     | core.proxy.proxy_task_manager:submit_task:172 - 提交耗时任务到子线程: refresh_proxies_0_20
2025-06-27 19:22:32.759 | DEBUG    | app.services.proxy_service:get_proxies:500 - 获取代理列表: offset=0, limit=20
2025-06-27 19:22:32.759 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 19:22:32.759 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 19:22:32.770 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 19:22:32.822 | INFO     | app.controllers.invite_controller:get_invite_tasks:38 - 获取邀请任务列表, status=None
2025-06-27 19:22:32.823 | INFO     | app.services.invite_service:get_invite_tasks:121 - 获取邀请任务列表, status=None
2025-06-27 19:22:32.823 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 19:22:32.824 | INFO     | app.services.invite_service:task_initialize:39 - 初始化邀请服务，重置运行中的任务为暂停状态
2025-06-27 19:22:32.825 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 19:22:32.832 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 19:22:32.834 | INFO     | core.proxy.proxy_task_manager:task_wrapper:199 - 任务 refresh_proxies_0_20 完成
2025-06-27 19:22:37.221 | INFO     | utils.vip_checker:start_checking:38 - 开始VIP状态检查
2025-06-27 19:22:37.221 | DEBUG    | utils.vip_checker:_check_vip_status:61 - 正在检查VIP状态...
2025-06-27 19:22:37.221 | INFO     | core.auth.api_service:verify_vip:425 - 会员验证接口调用
2025-06-27 19:22:37.380 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 19:22:37.383 | DEBUG    | ui.views.monitor_view:_refresh_task_list:175 - 刷新任务列表，共0个任务
2025-06-27 19:22:37.384 | INFO     | ui.views.monitor_view:_refresh_task_list:189 - 没有找到监控任务
2025-06-27 19:22:37.385 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 19:22:37.391 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 19:22:37.407 | INFO     | app.services.invite_service:task_initialize:48 - 成功重置 0 个任务状态
2025-06-27 19:22:37.407 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 19:22:37.409 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 19:22:37.417 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 19:22:37.422 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 19:22:37.424 | INFO     | app.services.account_service:get_all_groups:96 - 获取所有账户分组成功, 共 2 个
2025-06-27 19:22:37.424 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 19:22:37.499 | DEBUG    | ui.views.account_view:_on_groups_loaded:446 - 分组加载完成: 2个分组
2025-06-27 19:22:37.519 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 19:22:37.530 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 19:22:37.545 | INFO     | app.controllers.telegram_monitor_controller:_load_total_users_count_internal:191 - 成功加载总体统计数据: 总用户数 23, 今日采集 0, 日均采集 23, 运行天数 1
2025-06-27 19:22:37.545 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:156 - 成功加载 0 个任务
2025-06-27 19:22:37.546 | DEBUG    | ui.views.monitor_view:_on_loading_finished:625 - 加载完成
2025-06-27 19:22:37.548 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 19:22:37.550 | INFO     | app.services.account_service:get_all_accounts:290 - 获取所有账户成功, 共 2 个
2025-06-27 19:22:37.550 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 19:22:37.554 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 19:22:37.557 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-06-27 19:22:37.561 | DEBUG    | ui.views.account_view:_on_accounts_loaded:579 - 账户加载完成: 2个账户
2025-06-27 19:22:37.586 | DEBUG    | ui.views.account_view:_update_group_counts:185 - 分组计数已更新: {2: 1, 1: 1}
2025-06-27 19:22:37.591 | INFO     | ui.views.account_view:_auto_login_accounts:634 - 开始调用控制器进行 2 个账户的自动登录。
2025-06-27 19:22:37.591 | INFO     | app.services.account_service:batch_auto_login:1165 - 服务层：开始批量自动登录 2 个账户, 最大并发: 5
2025-06-27 19:22:37.592 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 19:22:37.629 | INFO     | app.services.account_service:get_all_accounts:290 - 获取所有账户成功, 共 2 个
2025-06-27 19:22:37.629 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 19:22:37.630 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=True
2025-06-27 19:22:37.633 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=True
2025-06-27 19:22:37.635 | INFO     | ui.views.proxy_view:_load_initial_data:1044 - 初始数据加载完成，共 1 个代理
2025-06-27 19:22:37.636 | INFO     | app.services.account_service:batch_auto_login:1204 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-06-27 19:22:37.636 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 19:22:37.640 | DEBUG    | ui.views.account_view:_update_group_counts:185 - 分组计数已更新: {2: 1, 1: 1}
2025-06-27 19:22:37.640 | DEBUG    | ui.views.account_view:_init_cache:152 - 账户缓存初始化完成，共 2 个账户
2025-06-27 19:22:37.645 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 19:22:37.647 | DEBUG    | ui.views.account_view:_on_accounts_loaded:579 - 账户加载完成: 2个账户
2025-06-27 19:22:37.671 | DEBUG    | ui.views.account_view:_update_group_counts:185 - 分组计数已更新: {2: 1, 1: 1}
2025-06-27 19:24:08.103 | INFO     | core.auth.api_service:verify_vip:441 - 会员验证成功
2025-06-27 19:24:08.104 | ERROR    | utils.vip_checker:_check_vip_status:92 - VIP状态检查出现异常: 'int' object has no attribute 'get'
Traceback (most recent call last):

  File "D:\Program Files\JetBrains\PyCharm 2025.1.1\plugins\python-ce\helpers\pydev\pydevd.py", line 2270, in <module>
    main()
    └ <function main at 0x00000200398BC680>

  File "D:\Program Files\JetBrains\PyCharm 2025.1.1\plugins\python-ce\helpers\pydev\pydevd.py", line 2252, in main
    globals = debugger.run(setup['file'], None, None, is_module)
              │        │   │                          └ False
              │        │   └ {'port': 42798, 'vm_type': None, 'client': '127.0.0.1', 'server': False, 'DEBUG_RECORD_SOCKET_READS': False, 'multiproc': Fal...
              │        └ <function PyDB.run at 0x00000200398BB420>
              └ <__main__.PyDB object at 0x000002002639A890>

  File "D:\Program Files\JetBrains\PyCharm 2025.1.1\plugins\python-ce\helpers\pydev\pydevd.py", line 1563, in run
    return self._exec(is_module, entry_point_fn, module_name, file, globals, locals)
           │    │     │          │               │            │     │        └ {'__name__': '__main__', '__doc__': '\nTelegram多账户管理系统 - 主程序入口\n', '__package__': '', '__loader__': <_frozen_importlib_extern...
           │    │     │          │               │            │     └ {'__name__': '__main__', '__doc__': '\nTelegram多账户管理系统 - 主程序入口\n', '__package__': '', '__loader__': <_frozen_importlib_extern...
           │    │     │          │               │            └ 'H:\\PyProject\\TeleTest\\main.py'
           │    │     │          │               └ None
           │    │     │          └ ''
           │    │     └ False
           │    └ <function PyDB._exec at 0x00000200398BB4C0>
           └ <__main__.PyDB object at 0x000002002639A890>

  File "D:\Program Files\JetBrains\PyCharm 2025.1.1\plugins\python-ce\helpers\pydev\pydevd.py", line 1570, in _exec
    pydev_imports.execfile(file, globals, locals)  # execute the script
    │             │        │     │        └ {'__name__': '__main__', '__doc__': '\nTelegram多账户管理系统 - 主程序入口\n', '__package__': '', '__loader__': <_frozen_importlib_extern...
    │             │        │     └ {'__name__': '__main__', '__doc__': '\nTelegram多账户管理系统 - 主程序入口\n', '__package__': '', '__loader__': <_frozen_importlib_extern...
    │             │        └ 'H:\\PyProject\\TeleTest\\main.py'
    │             └ <function execfile at 0x00000200394D9F80>
    └ <module '_pydev_bundle.pydev_imports' from 'D:\\Program Files\\JetBrains\\PyCharm 2025.1.1\\plugins\\python-ce\\helpers\\pyde...

  File "D:\Program Files\JetBrains\PyCharm 2025.1.1\plugins\python-ce\helpers\pydev\_pydev_imps\_pydev_execfile.py", line 18, in execfile
    exec(compile(contents+"\n", file, 'exec'), glob, loc)
                 │              │              │     └ {'__name__': '__main__', '__doc__': '\nTelegram多账户管理系统 - 主程序入口\n', '__package__': '', '__loader__': <_frozen_importlib_extern...
                 │              │              └ {'__name__': '__main__', '__doc__': '\nTelegram多账户管理系统 - 主程序入口\n', '__package__': '', '__loader__': <_frozen_importlib_extern...
                 │              └ 'H:\\PyProject\\TeleTest\\main.py'
                 └ '#!/usr/bin/env python\n# -*- coding: utf-8 -*-\n\n"""\nTelegram多账户管理系统 - 主程序入口\n"""\nimport sys\nimport os\nimport asyncio\n...

  File "H:\PyProject\TeleTest\main.py", line 118, in <module>
    main()
    └ <function main at 0x000002004D857F60>

  File "H:\PyProject\TeleTest\main.py", line 109, in main
    event_loop.run_forever()
    │          └ <function _QEventLoop.run_forever at 0x0000020039C74900>
    └ <QIOCPEventLoop running=True closed=False debug=False>

  File "H:\PyProject\TeleTest\script\Lib\site-packages\qasync\__init__.py", line 377, in run_forever
    rslt = self.__app.exec()
           └ <QIOCPEventLoop running=True closed=False debug=False>
  File "H:\PyProject\TeleTest\script\Lib\site-packages\qasync\__init__.py", line 279, in timerEvent
    handle._run()
    │      └ <function Handle._run at 0x000002003977BC40>
    └ <Handle Task.task_wakeup(<Task finishe...> result=None>)>
  File "D:\Program Files\python11\Lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle Task.task_wakeup(<Task finishe...> result=None>)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle Task.task_wakeup(<Task finishe...> result=None>)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle Task.task_wakeup(<Task finishe...> result=None>)>

  File "H:\PyProject\TeleTest\ui\main_window.py", line 293, in on_login_succeeded
    await vip_checker.start_checking()
          │           └ <function VipChecker.start_checking at 0x000002003A3196C0>
          └ <utils.vip_checker.VipChecker(0x2004bf02f50) at 0x000002003A30B4C0>

  File "H:\PyProject\TeleTest\utils\vip_checker.py", line 42, in start_checking
    await self._on_timer_timeout()
          │    └ <function VipChecker._on_timer_timeout at 0x000002003A319800>
          └ <utils.vip_checker.VipChecker(0x2004bf02f50) at 0x000002003A30B4C0>

  File "H:\PyProject\TeleTest\utils\vip_checker.py", line 54, in _on_timer_timeout
    await self._check_vip_status()
          │    └ <function VipChecker._check_vip_status at 0x000002003A3198A0>
          └ <utils.vip_checker.VipChecker(0x2004bf02f50) at 0x000002003A30B4C0>

> File "H:\PyProject\TeleTest\utils\vip_checker.py", line 75, in _check_vip_status
    is_vip = data.get('is_vip', True)  # 默认为True，兼容老接口
             └ 1751023357

AttributeError: 'int' object has no attribute 'get'
2025-06-27 19:24:16.372 | ERROR    | ui.main_window:on_login_succeeded:309 - 检查VIP状态时出错: 'bool' object has no attribute 'get'
2025-06-27 19:25:15.774 | INFO     | utils.logger:init_logger:98 - 日志系统初始化完成，默认级别: DEBUG，日志目录: H:\PyProject\TeleTest\APPDATA\logs
2025-06-27 19:25:17.497 | INFO     | data.database:init_database:31 - 初始化数据库连接: H:\PyProject\TeleTest\APPDATA\database\telegram_manager.db
2025-06-27 19:25:17.527 | DEBUG    | data.database:init_database:58 - 创建数据库表
2025-06-27 19:25:17.547 | INFO     | data.database:init_database:61 - 数据库初始化完成
2025-06-27 19:25:18.314 | DEBUG    | app.controllers.auth_controller:initialize_software:43 - 控制器: 初始化软件 app_id=1000, version=1.2.7
2025-06-27 19:25:18.314 | DEBUG    | core.auth.api_service:init_software:55 - 发送初始化请求: https://server.xile188.com/api/user/1000/windowns10/1.2.7/ini, params={}
2025-06-27 19:25:18.734 | DEBUG    | core.auth.api_service:init_software:66 - 初始化数据解密成功: {'new_content': '- 新增功能\n  - 1.新增图片上传功能\n  - 2.新增软件文档\n  - 3.新增软件自动更新，后续更新会自动检测版本更新，无需再下载更新包\n  - 4.新增设置账户信息及发信控制\n- 修复bug\n  - 删除监听任务时，不删除对应任务采集到的数据。\n  - 监听设置群无法发送到目标群\n  - 导出监听数据时应当导出单个任务而非所有数据\n- 优化记录\n  - 优化发信规则，直接调取符合要求的账户发送，无账户时才需要等待延时。\n  - 完善软件设置', 'new_url': None, 'notice': {'id': '2', 'content': '公告内容测试3', 'time': '**********'}}
2025-06-27 19:25:18.741 | INFO     | app.controllers.auth_controller:login:61 - 控制器: 用户登录 account=demo01
2025-06-27 19:25:21.760 | INFO     | core.auth.api_service:login:127 - 用户登录: account=demo01
2025-06-27 19:25:22.080 | INFO     | core.auth.api_service:get_user_info:347 - 获取用户信息
2025-06-27 19:25:22.331 | INFO     | core.auth.api_service:get_user_info:375 - 获取用户信息成功
2025-06-27 19:25:22.338 | INFO     | __main__:on_auth_completed:93 - 用户登录成功，正在启动主窗口...
2025-06-27 19:25:22.364 | INFO     | core.telegram.client_worker:__init__:61 - Telegram客户端工作线程初始化
2025-06-27 19:25:22.364 | INFO     | utils.vip_checker:initialize:26 - VIP检查器已初始化，等待登录后开始检查
2025-06-27 19:25:22.365 | INFO     | ui.main_window:_initialize_core_components:107 - MainWindow: 初始化核心组件...
2025-06-27 19:25:22.366 | INFO     | app.services.account_service:__init__:42 - 账户服务初始化
2025-06-27 19:25:22.366 | INFO     | core.proxy.proxy_task_manager:run:284 - 代理任务管理器线程启动
2025-06-27 19:25:22.366 | INFO     | core.telegram.client_worker:run:65 - Telegram客户端工作线程开始运行
2025-06-27 19:25:22.367 | INFO     | app.controllers.account_controller:__init__:81 - 账户控制器初始化
2025-06-27 19:25:22.368 | INFO     | app.services.monitor_service:__init__:39 - 初始化监控任务服务
2025-06-27 19:25:22.368 | INFO     | core.monitor.filter:__init__:26 - 初始化消息过滤器
2025-06-27 19:25:22.368 | INFO     | core.monitor.extractor:__init__:24 - 初始化关键词提取器
2025-06-27 19:25:22.368 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-06-27 19:25:22.369 | INFO     | core.monitor.notifier:__init__:90 - 初始化通知管理器
2025-06-27 19:25:22.369 | INFO     | core.telegram.client_manager:__init__:66 - Telegram客户端管理器初始化
2025-06-27 19:25:22.370 | INFO     | app.controllers.telegram_monitor_controller:__init__:64 - 初始化 Telegram 监控控制器
2025-06-27 19:25:22.370 | INFO     | core.telegram.user_manager:__init__:30 - Telegram用户管理器初始化
2025-06-27 19:25:22.370 | DEBUG    | app.controllers.telegram_monitor_controller:__init__:82 - 控制器初始化完成
2025-06-27 19:25:22.371 | INFO     | core.telegram.group_manager:__init__:36 - Telegram群组管理器初始化
2025-06-27 19:25:22.372 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-06-27 19:25:22.373 | INFO     | core.telegram.monitor_manager:__init__:26 - Telegram监控管理器初始化
2025-06-27 19:25:22.373 | INFO     | core.telegram.telegram_service:__init__:39 - Telegram服务初始化完成
2025-06-27 19:25:22.593 | INFO     | ui.views.monitor_view:__init__:48 - 初始化监控视图
2025-06-27 19:25:22.594 | DEBUG    | ui.views.monitor_view:_connect_signals:80 - 连接控制器信号
2025-06-27 19:25:22.794 | INFO     | ui.views.proxy_view:__init__:96 - 初始化视图，加载第一页代理数据
2025-06-27 19:25:23.061 | INFO     | __main__:on_auth_completed:97 - 主窗口已启动
2025-06-27 19:25:23.110 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:88 - 尝试连接监听日志信号...
2025-06-27 19:25:23.110 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:90 - 监听日志信号连接成功
2025-06-27 19:25:23.111 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 19:25:23.119 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 19:25:23.123 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:145 - 加载监控任务列表 (搜索关键词: '')
2025-06-27 19:25:23.124 | DEBUG    | ui.views.monitor_view:_on_loading_started:620 - 加载中: 正在加载任务列表...
2025-06-27 19:25:23.124 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 19:25:23.132 | INFO     | app.services.proxy_service:refresh_proxies:712 - 刷新代理列表: offset=0, limit=20
2025-06-27 19:25:23.133 | INFO     | core.proxy.proxy_task_manager:submit_task:172 - 提交耗时任务到子线程: refresh_proxies_0_20
2025-06-27 19:25:23.133 | DEBUG    | app.services.proxy_service:get_proxies:500 - 获取代理列表: offset=0, limit=20
2025-06-27 19:25:23.133 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 19:25:23.134 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 19:25:23.142 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 19:25:23.196 | INFO     | app.controllers.invite_controller:get_invite_tasks:38 - 获取邀请任务列表, status=None
2025-06-27 19:25:23.196 | INFO     | app.services.invite_service:get_invite_tasks:121 - 获取邀请任务列表, status=None
2025-06-27 19:25:23.196 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 19:25:23.202 | INFO     | app.services.invite_service:task_initialize:39 - 初始化邀请服务，重置运行中的任务为暂停状态
2025-06-27 19:25:23.203 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 19:25:23.204 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 19:25:23.207 | INFO     | core.proxy.proxy_task_manager:task_wrapper:199 - 任务 refresh_proxies_0_20 完成
2025-06-27 19:25:24.668 | INFO     | utils.vip_checker:start_checking:38 - 开始VIP状态检查
2025-06-27 19:25:24.668 | DEBUG    | utils.vip_checker:_check_vip_status:61 - 正在检查VIP状态...
2025-06-27 19:25:24.668 | INFO     | core.auth.api_service:verify_vip:425 - 会员验证接口调用
2025-06-27 19:25:24.842 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 19:25:24.848 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 19:25:24.855 | DEBUG    | ui.views.monitor_view:_refresh_task_list:175 - 刷新任务列表，共0个任务
2025-06-27 19:25:24.856 | INFO     | ui.views.monitor_view:_refresh_task_list:189 - 没有找到监控任务
2025-06-27 19:25:24.856 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 19:25:24.873 | INFO     | app.services.invite_service:task_initialize:48 - 成功重置 0 个任务状态
2025-06-27 19:25:24.873 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 19:25:24.880 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 19:25:24.889 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 19:25:24.897 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 19:25:24.899 | INFO     | app.services.account_service:get_all_groups:96 - 获取所有账户分组成功, 共 2 个
2025-06-27 19:25:24.899 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 19:25:24.997 | DEBUG    | ui.views.account_view:_on_groups_loaded:446 - 分组加载完成: 2个分组
2025-06-27 19:25:25.022 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 19:25:25.036 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 19:25:25.043 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 19:25:25.045 | INFO     | app.controllers.telegram_monitor_controller:_load_total_users_count_internal:191 - 成功加载总体统计数据: 总用户数 23, 今日采集 0, 日均采集 23, 运行天数 1
2025-06-27 19:25:25.045 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:156 - 成功加载 0 个任务
2025-06-27 19:25:25.045 | DEBUG    | ui.views.monitor_view:_on_loading_finished:625 - 加载完成
2025-06-27 19:25:25.047 | INFO     | app.services.account_service:get_all_accounts:290 - 获取所有账户成功, 共 2 个
2025-06-27 19:25:25.047 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 19:25:25.049 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-06-27 19:25:25.064 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 19:25:25.066 | DEBUG    | ui.views.account_view:_on_accounts_loaded:579 - 账户加载完成: 2个账户
2025-06-27 19:25:25.087 | DEBUG    | ui.views.account_view:_update_group_counts:185 - 分组计数已更新: {2: 1, 1: 1}
2025-06-27 19:25:25.092 | INFO     | ui.views.account_view:_auto_login_accounts:634 - 开始调用控制器进行 2 个账户的自动登录。
2025-06-27 19:25:25.092 | INFO     | app.services.account_service:batch_auto_login:1165 - 服务层：开始批量自动登录 2 个账户, 最大并发: 5
2025-06-27 19:25:25.092 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 19:25:25.106 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=True
2025-06-27 19:25:25.107 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=True
2025-06-27 19:25:25.109 | INFO     | ui.views.proxy_view:_load_initial_data:1044 - 初始数据加载完成，共 1 个代理
2025-06-27 19:25:25.116 | INFO     | app.services.account_service:batch_auto_login:1204 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-06-27 19:25:25.117 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 19:25:25.119 | INFO     | app.services.account_service:get_all_accounts:290 - 获取所有账户成功, 共 2 个
2025-06-27 19:25:25.119 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 19:25:25.121 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 19:25:25.124 | DEBUG    | ui.views.account_view:_update_group_counts:185 - 分组计数已更新: {2: 1, 1: 1}
2025-06-27 19:25:25.125 | DEBUG    | ui.views.account_view:_init_cache:152 - 账户缓存初始化完成，共 2 个账户
2025-06-27 19:25:25.130 | DEBUG    | ui.views.account_view:_on_accounts_loaded:579 - 账户加载完成: 2个账户
2025-06-27 19:25:25.152 | DEBUG    | ui.views.account_view:_update_group_counts:185 - 分组计数已更新: {2: 1, 1: 1}
2025-06-27 19:25:25.162 | INFO     | app.services.account_service:batch_auto_login:1204 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-06-27 19:25:25.162 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 19:25:25.164 | INFO     | app.services.account_service:batch_auto_login:1274 - 服务层：准备调用核心层处理 2 个账户的自动登录。
2025-06-27 19:25:25.164 | INFO     | app.services.account_service:batch_auto_login:1284 - 服务层：设置核心层任务超时为 120 秒。
2025-06-27 19:25:25.165 | INFO     | core.telegram.client_manager:batch_auto_login:1216 - 开始批量自动登录, 共 2 个账户, 最大并发 5
2025-06-27 19:25:25.167 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:259 - 创建客户端: H:\PyProject\TeleTest\APPDATA\sessions\+***********, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-06-27 19:25:25.167 | DEBUG    | core.telegram.client_manager:get_proxy:96 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-06-27 19:25:25.167 | DEBUG    | core.telegram.client_manager:get_proxy:143 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-06-27 19:25:25.171 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:259 - 创建客户端: H:\PyProject\TeleTest\APPDATA\sessions\+***********, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-06-27 19:25:25.172 | DEBUG    | core.telegram.client_manager:get_proxy:96 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-06-27 19:25:25.172 | DEBUG    | core.telegram.client_manager:get_proxy:143 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-06-27 19:25:28.009 | INFO     | core.telegram.client_manager:_connect_client:207 - 连接成功!
2025-06-27 19:25:28.323 | INFO     | core.telegram.client_manager:_connect_client:207 - 连接成功!
2025-06-27 19:25:28.844 | INFO     | core.telegram.client_manager:_verify_client_authorization:1191 - 账户已授权: +***********
2025-06-27 19:25:29.156 | INFO     | core.telegram.client_manager:_verify_client_authorization:1191 - 账户已授权: +***********
2025-06-27 19:25:31.159 | INFO     | core.telegram.client_manager:batch_auto_login:1365 - 批量登录完成: 总计 2, 成功 2, 失败 0
2025-06-27 19:25:37.090 | WARNING  | core.auth.api_service:verify_vip:447 - 会员验证失败: 验证失败
2025-06-27 19:25:37.090 | ERROR    | utils.vip_checker:_check_vip_status:89 - VIP验证失败: 验证失败
2025-06-27 19:25:39.986 | ERROR    | data.repositories.message_repo:get_task_target_stats:187 - 获取任务4目标统计失败: Cannot switch to a different thread
	Current:  <greenlet.greenlet object at 0x000001F43BD00DC0 (otid=0x000001F43BD244B0) current active started main>
	Expected: <greenlet.greenlet object at 0x000001F46B244900 (otid=0x000001F46B1EB810) suspended active started main>
2025-06-27 19:25:39.986 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 19:25:39.989 | ERROR    | ui.views.send_msg_view:load_tasks:50 - 加载任务列表失败: coroutine ignored GeneratorExit
2025-06-27 22:04:26.923 | INFO     | utils.logger:init_logger:98 - 日志系统初始化完成，默认级别: DEBUG，日志目录: H:\PyProject\TeleTest\APPDATA\logs
2025-06-27 22:04:28.809 | INFO     | data.database:init_database:31 - 初始化数据库连接: H:\PyProject\TeleTest\APPDATA\database\telegram_manager.db
2025-06-27 22:04:28.843 | DEBUG    | data.database:init_database:58 - 创建数据库表
2025-06-27 22:04:28.865 | INFO     | data.database:init_database:61 - 数据库初始化完成
2025-06-27 22:04:30.258 | DEBUG    | app.controllers.auth_controller:initialize_software:43 - 控制器: 初始化软件 app_id=1000, version=1.2.7
2025-06-27 22:04:30.258 | DEBUG    | core.auth.api_service:init_software:55 - 发送初始化请求: https://server.xile188.com/api/user/1000/windowns10/1.2.7/ini, params={}
2025-06-27 22:04:31.582 | DEBUG    | core.auth.api_service:init_software:66 - 初始化数据解密成功: {'new_content': '- 新增功能\n  - 1.新增图片上传功能\n  - 2.新增软件文档\n  - 3.新增软件自动更新，后续更新会自动检测版本更新，无需再下载更新包\n  - 4.新增设置账户信息及发信控制\n- 修复bug\n  - 删除监听任务时，不删除对应任务采集到的数据。\n  - 监听设置群无法发送到目标群\n  - 导出监听数据时应当导出单个任务而非所有数据\n- 优化记录\n  - 优化发信规则，直接调取符合要求的账户发送，无账户时才需要等待延时。\n  - 完善软件设置', 'new_url': None, 'notice': {'id': '2', 'content': '公告内容测试3', 'time': '**********'}}
2025-06-27 22:04:31.591 | INFO     | app.controllers.auth_controller:login:61 - 控制器: 用户登录 account=demo01
2025-06-27 22:04:34.638 | INFO     | core.auth.api_service:login:127 - 用户登录: account=demo01
2025-06-27 22:04:34.962 | INFO     | core.auth.api_service:get_user_info:347 - 获取用户信息
2025-06-27 22:04:35.216 | INFO     | core.auth.api_service:get_user_info:375 - 获取用户信息成功
2025-06-27 22:04:35.223 | INFO     | __main__:on_auth_completed:93 - 用户登录成功，正在启动主窗口...
2025-06-27 22:04:35.253 | INFO     | core.telegram.client_worker:__init__:61 - Telegram客户端工作线程初始化
2025-06-27 22:04:35.253 | INFO     | utils.vip_checker:initialize:26 - VIP检查器已初始化，等待登录后开始检查
2025-06-27 22:04:35.253 | INFO     | ui.main_window:_initialize_core_components:107 - MainWindow: 初始化核心组件...
2025-06-27 22:04:35.254 | INFO     | app.services.account_service:__init__:42 - 账户服务初始化
2025-06-27 22:04:35.255 | INFO     | core.telegram.client_worker:run:65 - Telegram客户端工作线程开始运行
2025-06-27 22:04:35.255 | INFO     | app.controllers.account_controller:__init__:81 - 账户控制器初始化
2025-06-27 22:04:35.255 | INFO     | core.proxy.proxy_task_manager:run:284 - 代理任务管理器线程启动
2025-06-27 22:04:35.256 | INFO     | app.services.monitor_service:__init__:39 - 初始化监控任务服务
2025-06-27 22:04:35.257 | INFO     | core.monitor.filter:__init__:26 - 初始化消息过滤器
2025-06-27 22:04:35.257 | INFO     | core.monitor.extractor:__init__:24 - 初始化关键词提取器
2025-06-27 22:04:35.257 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-06-27 22:04:35.258 | INFO     | core.monitor.notifier:__init__:90 - 初始化通知管理器
2025-06-27 22:04:35.258 | INFO     | app.controllers.telegram_monitor_controller:__init__:64 - 初始化 Telegram 监控控制器
2025-06-27 22:04:35.258 | INFO     | core.telegram.client_manager:__init__:66 - Telegram客户端管理器初始化
2025-06-27 22:04:35.258 | DEBUG    | app.controllers.telegram_monitor_controller:__init__:82 - 控制器初始化完成
2025-06-27 22:04:35.259 | INFO     | core.telegram.user_manager:__init__:30 - Telegram用户管理器初始化
2025-06-27 22:04:35.259 | INFO     | core.telegram.group_manager:__init__:36 - Telegram群组管理器初始化
2025-06-27 22:04:35.261 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-06-27 22:04:35.261 | INFO     | core.telegram.monitor_manager:__init__:26 - Telegram监控管理器初始化
2025-06-27 22:04:35.262 | INFO     | core.telegram.telegram_service:__init__:39 - Telegram服务初始化完成
2025-06-27 22:04:35.487 | INFO     | ui.views.monitor_view:__init__:48 - 初始化监控视图
2025-06-27 22:04:35.487 | DEBUG    | ui.views.monitor_view:_connect_signals:80 - 连接控制器信号
2025-06-27 22:04:35.699 | INFO     | ui.views.proxy_view:__init__:96 - 初始化视图，加载第一页代理数据
2025-06-27 22:04:35.961 | INFO     | __main__:on_auth_completed:97 - 主窗口已启动
2025-06-27 22:04:36.011 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:88 - 尝试连接监听日志信号...
2025-06-27 22:04:36.011 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:90 - 监听日志信号连接成功
2025-06-27 22:04:36.012 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:04:36.018 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:04:36.022 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:145 - 加载监控任务列表 (搜索关键词: '')
2025-06-27 22:04:36.022 | DEBUG    | ui.views.monitor_view:_on_loading_started:620 - 加载中: 正在加载任务列表...
2025-06-27 22:04:36.023 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:04:36.030 | INFO     | app.services.proxy_service:refresh_proxies:712 - 刷新代理列表: offset=0, limit=20
2025-06-27 22:04:36.031 | INFO     | core.proxy.proxy_task_manager:submit_task:172 - 提交耗时任务到子线程: refresh_proxies_0_20
2025-06-27 22:04:36.031 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:04:36.032 | DEBUG    | app.services.proxy_service:get_proxies:500 - 获取代理列表: offset=0, limit=20
2025-06-27 22:04:36.033 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:04:36.039 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:04:36.082 | INFO     | app.controllers.invite_controller:get_invite_tasks:38 - 获取邀请任务列表, status=None
2025-06-27 22:04:36.082 | INFO     | app.services.invite_service:get_invite_tasks:121 - 获取邀请任务列表, status=None
2025-06-27 22:04:36.082 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:04:36.098 | INFO     | app.services.invite_service:task_initialize:39 - 初始化邀请服务，重置运行中的任务为暂停状态
2025-06-27 22:04:36.099 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:04:36.107 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:04:36.109 | INFO     | core.proxy.proxy_task_manager:task_wrapper:199 - 任务 refresh_proxies_0_20 完成
2025-06-27 22:04:39.411 | INFO     | utils.vip_checker:start_checking:38 - 开始VIP状态检查
2025-06-27 22:04:39.411 | DEBUG    | utils.vip_checker:_check_vip_status:61 - 正在检查VIP状态...
2025-06-27 22:04:39.412 | INFO     | core.auth.api_service:verify_vip:425 - 会员验证接口调用
2025-06-27 22:04:39.665 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:04:39.667 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:04:39.680 | DEBUG    | ui.views.monitor_view:_refresh_task_list:175 - 刷新任务列表，共0个任务
2025-06-27 22:04:39.681 | INFO     | ui.views.monitor_view:_refresh_task_list:189 - 没有找到监控任务
2025-06-27 22:04:39.682 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:04:39.686 | INFO     | app.services.invite_service:task_initialize:48 - 成功重置 0 个任务状态
2025-06-27 22:04:39.686 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:04:39.692 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:04:39.699 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:04:39.700 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:04:39.701 | INFO     | app.services.account_service:get_all_groups:96 - 获取所有账户分组成功, 共 2 个
2025-06-27 22:04:39.702 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:04:39.709 | DEBUG    | ui.views.account_view:_on_groups_loaded:446 - 分组加载完成: 2个分组
2025-06-27 22:04:39.723 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:04:39.796 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:04:39.803 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:04:39.805 | INFO     | app.services.account_service:get_all_accounts:290 - 获取所有账户成功, 共 2 个
2025-06-27 22:04:39.806 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:04:39.808 | INFO     | app.controllers.telegram_monitor_controller:_load_total_users_count_internal:191 - 成功加载总体统计数据: 总用户数 23, 今日采集 0, 日均采集 23, 运行天数 1
2025-06-27 22:04:39.809 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:156 - 成功加载 0 个任务
2025-06-27 22:04:39.809 | DEBUG    | ui.views.monitor_view:_on_loading_finished:625 - 加载完成
2025-06-27 22:04:39.813 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-06-27 22:04:39.827 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:04:39.832 | DEBUG    | ui.views.account_view:_on_accounts_loaded:579 - 账户加载完成: 2个账户
2025-06-27 22:04:39.854 | DEBUG    | ui.views.account_view:_update_group_counts:185 - 分组计数已更新: {2: 1, 1: 1}
2025-06-27 22:04:39.858 | INFO     | ui.views.account_view:_auto_login_accounts:634 - 开始调用控制器进行 2 个账户的自动登录。
2025-06-27 22:04:39.858 | INFO     | app.services.account_service:batch_auto_login:1165 - 服务层：开始批量自动登录 2 个账户, 最大并发: 5
2025-06-27 22:04:39.859 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:04:39.875 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=True
2025-06-27 22:04:39.877 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=True
2025-06-27 22:04:39.878 | INFO     | ui.views.proxy_view:_load_initial_data:1044 - 初始数据加载完成，共 1 个代理
2025-06-27 22:04:39.887 | INFO     | app.services.account_service:batch_auto_login:1204 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-06-27 22:04:39.887 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:04:39.889 | INFO     | app.services.account_service:get_all_accounts:290 - 获取所有账户成功, 共 2 个
2025-06-27 22:04:39.889 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:04:39.891 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:04:39.894 | DEBUG    | ui.views.account_view:_update_group_counts:185 - 分组计数已更新: {2: 1, 1: 1}
2025-06-27 22:04:39.894 | DEBUG    | ui.views.account_view:_init_cache:152 - 账户缓存初始化完成，共 2 个账户
2025-06-27 22:04:39.899 | DEBUG    | ui.views.account_view:_on_accounts_loaded:579 - 账户加载完成: 2个账户
2025-06-27 22:04:39.921 | DEBUG    | ui.views.account_view:_update_group_counts:185 - 分组计数已更新: {2: 1, 1: 1}
2025-06-27 22:04:46.753 | WARNING  | core.auth.api_service:verify_vip:447 - 会员验证失败: 验证失败
2025-06-27 22:04:52.866 | ERROR    | utils.vip_checker:_check_vip_status:104 - VIP验证失败: 验证失败
2025-06-27 22:05:17.209 | ERROR    | ui.main_window:on_login_succeeded:309 - 检查VIP状态时出错: 'bool' object has no attribute 'get'
2025-06-27 22:05:26.282 | INFO     | ui.main_window:closeEvent:385 - MainWindow: 接收到关闭事件
2025-06-27 22:05:26.292 | INFO     | ui.main_window:_cleanup_before_quit:264 - MainWindow: 执行清理资源...
2025-06-27 22:05:26.292 | INFO     | core.telegram.client_worker:stop:108 - 正在停止Telegram客户端工作线程
2025-06-27 22:05:26.303 | INFO     | core.telegram.client_manager:cleanup_async:1136 - 开始清理资源并断开所有连接
2025-06-27 22:05:26.303 | INFO     | core.telegram.client_manager:disconnect_all_clients:721 - 正在断开所有客户端连接
2025-06-27 22:05:26.303 | INFO     | core.telegram.client_manager:cleanup_async:1155 - 资源清理完成
2025-06-27 22:05:26.804 | INFO     | core.telegram.client_manager:cleanup_async:1136 - 开始清理资源并断开所有连接
2025-06-27 22:05:26.804 | INFO     | core.telegram.client_manager:disconnect_all_clients:721 - 正在断开所有客户端连接
2025-06-27 22:05:26.804 | INFO     | core.telegram.client_manager:cleanup_async:1155 - 资源清理完成
2025-06-27 22:05:27.306 | INFO     | core.telegram.client_worker:run:97 - Telegram客户端工作线程已退出
2025-06-27 22:05:27.306 | INFO     | core.telegram.client_worker:stop:122 - Telegram客户端工作线程已停止
2025-06-27 22:05:27.307 | INFO     | ui.main_window:_cleanup_before_quit:273 - TelegramClientWorker 已停止。
2025-06-27 22:05:27.307 | INFO     | ui.main_window:_cleanup_before_quit:277 - MainWindow 清理完成
2025-06-27 22:05:27.319 | INFO     | __main__:main:111 - 应用程序已正常退出
2025-06-27 22:06:43.562 | INFO     | utils.logger:init_logger:98 - 日志系统初始化完成，默认级别: DEBUG，日志目录: H:\PyProject\TeleTest\APPDATA\logs
2025-06-27 22:06:47.757 | INFO     | data.database:init_database:31 - 初始化数据库连接: H:\PyProject\TeleTest\APPDATA\database\telegram_manager.db
2025-06-27 22:06:47.789 | DEBUG    | data.database:init_database:58 - 创建数据库表
2025-06-27 22:06:47.810 | INFO     | data.database:init_database:61 - 数据库初始化完成
2025-06-27 22:06:48.429 | DEBUG    | app.controllers.auth_controller:initialize_software:43 - 控制器: 初始化软件 app_id=1000, version=1.2.7
2025-06-27 22:06:48.429 | DEBUG    | core.auth.api_service:init_software:55 - 发送初始化请求: https://server.xile188.com/api/user/1000/windowns10/1.2.7/ini, params={}
2025-06-27 22:06:49.128 | DEBUG    | core.auth.api_service:init_software:66 - 初始化数据解密成功: {'new_content': '- 新增功能\n  - 1.新增图片上传功能\n  - 2.新增软件文档\n  - 3.新增软件自动更新，后续更新会自动检测版本更新，无需再下载更新包\n  - 4.新增设置账户信息及发信控制\n- 修复bug\n  - 删除监听任务时，不删除对应任务采集到的数据。\n  - 监听设置群无法发送到目标群\n  - 导出监听数据时应当导出单个任务而非所有数据\n- 优化记录\n  - 优化发信规则，直接调取符合要求的账户发送，无账户时才需要等待延时。\n  - 完善软件设置', 'new_url': None, 'notice': {'id': '2', 'content': '公告内容测试3', 'time': '**********'}}
2025-06-27 22:06:49.136 | INFO     | app.controllers.auth_controller:login:61 - 控制器: 用户登录 account=demo01
2025-06-27 22:06:52.206 | INFO     | core.auth.api_service:login:127 - 用户登录: account=demo01
2025-06-27 22:06:52.440 | INFO     | core.auth.api_service:get_user_info:347 - 获取用户信息
2025-06-27 22:06:52.651 | INFO     | core.auth.api_service:get_user_info:375 - 获取用户信息成功
2025-06-27 22:06:52.657 | INFO     | __main__:on_auth_completed:93 - 用户登录成功，正在启动主窗口...
2025-06-27 22:06:52.686 | INFO     | core.telegram.client_worker:__init__:61 - Telegram客户端工作线程初始化
2025-06-27 22:06:52.686 | INFO     | utils.vip_checker:initialize:26 - VIP检查器已初始化，等待登录后开始检查
2025-06-27 22:06:52.687 | INFO     | ui.main_window:_initialize_core_components:107 - MainWindow: 初始化核心组件...
2025-06-27 22:06:52.688 | INFO     | core.telegram.client_worker:run:65 - Telegram客户端工作线程开始运行
2025-06-27 22:06:52.688 | INFO     | core.proxy.proxy_task_manager:run:284 - 代理任务管理器线程启动
2025-06-27 22:06:52.688 | INFO     | app.services.account_service:__init__:42 - 账户服务初始化
2025-06-27 22:06:52.689 | INFO     | app.controllers.account_controller:__init__:81 - 账户控制器初始化
2025-06-27 22:06:52.689 | INFO     | app.services.monitor_service:__init__:39 - 初始化监控任务服务
2025-06-27 22:06:52.689 | INFO     | core.monitor.filter:__init__:26 - 初始化消息过滤器
2025-06-27 22:06:52.690 | INFO     | core.monitor.extractor:__init__:24 - 初始化关键词提取器
2025-06-27 22:06:52.690 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-06-27 22:06:52.691 | INFO     | core.telegram.client_manager:__init__:66 - Telegram客户端管理器初始化
2025-06-27 22:06:52.691 | INFO     | core.monitor.notifier:__init__:90 - 初始化通知管理器
2025-06-27 22:06:52.691 | INFO     | core.telegram.user_manager:__init__:30 - Telegram用户管理器初始化
2025-06-27 22:06:52.691 | INFO     | app.controllers.telegram_monitor_controller:__init__:64 - 初始化 Telegram 监控控制器
2025-06-27 22:06:52.692 | INFO     | core.telegram.group_manager:__init__:36 - Telegram群组管理器初始化
2025-06-27 22:06:52.692 | DEBUG    | app.controllers.telegram_monitor_controller:__init__:82 - 控制器初始化完成
2025-06-27 22:06:52.692 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-06-27 22:06:52.693 | INFO     | core.telegram.monitor_manager:__init__:26 - Telegram监控管理器初始化
2025-06-27 22:06:52.694 | INFO     | core.telegram.telegram_service:__init__:39 - Telegram服务初始化完成
2025-06-27 22:06:52.918 | INFO     | ui.views.monitor_view:__init__:48 - 初始化监控视图
2025-06-27 22:06:52.918 | DEBUG    | ui.views.monitor_view:_connect_signals:80 - 连接控制器信号
2025-06-27 22:06:53.116 | INFO     | ui.views.proxy_view:__init__:96 - 初始化视图，加载第一页代理数据
2025-06-27 22:06:53.367 | INFO     | __main__:on_auth_completed:97 - 主窗口已启动
2025-06-27 22:06:53.423 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:88 - 尝试连接监听日志信号...
2025-06-27 22:06:53.423 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:90 - 监听日志信号连接成功
2025-06-27 22:06:53.424 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:06:53.429 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:06:53.434 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:145 - 加载监控任务列表 (搜索关键词: '')
2025-06-27 22:06:53.434 | DEBUG    | ui.views.monitor_view:_on_loading_started:620 - 加载中: 正在加载任务列表...
2025-06-27 22:06:53.435 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:06:53.442 | INFO     | app.services.proxy_service:refresh_proxies:712 - 刷新代理列表: offset=0, limit=20
2025-06-27 22:06:53.442 | INFO     | core.proxy.proxy_task_manager:submit_task:172 - 提交耗时任务到子线程: refresh_proxies_0_20
2025-06-27 22:06:53.443 | DEBUG    | app.services.proxy_service:get_proxies:500 - 获取代理列表: offset=0, limit=20
2025-06-27 22:06:53.443 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:06:53.444 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:06:53.450 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:06:53.506 | INFO     | app.controllers.invite_controller:get_invite_tasks:38 - 获取邀请任务列表, status=None
2025-06-27 22:06:53.506 | INFO     | app.services.invite_service:get_invite_tasks:121 - 获取邀请任务列表, status=None
2025-06-27 22:06:53.506 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:06:53.511 | INFO     | app.services.invite_service:task_initialize:39 - 初始化邀请服务，重置运行中的任务为暂停状态
2025-06-27 22:06:53.511 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:06:53.513 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:06:53.516 | INFO     | core.proxy.proxy_task_manager:task_wrapper:199 - 任务 refresh_proxies_0_20 完成
2025-06-27 22:06:59.249 | INFO     | utils.vip_checker:start_checking:38 - 开始VIP状态检查
2025-06-27 22:06:59.250 | DEBUG    | utils.vip_checker:_check_vip_status:61 - 正在检查VIP状态...
2025-06-27 22:06:59.250 | INFO     | core.auth.api_service:verify_vip:425 - 会员验证接口调用
2025-06-27 22:06:59.531 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:06:59.538 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:06:59.545 | DEBUG    | ui.views.monitor_view:_refresh_task_list:175 - 刷新任务列表，共0个任务
2025-06-27 22:06:59.545 | INFO     | ui.views.monitor_view:_refresh_task_list:189 - 没有找到监控任务
2025-06-27 22:06:59.546 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:06:59.552 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:06:59.556 | INFO     | app.services.invite_service:task_initialize:48 - 成功重置 0 个任务状态
2025-06-27 22:06:59.556 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:06:59.560 | INFO     | app.services.account_service:get_all_groups:96 - 获取所有账户分组成功, 共 2 个
2025-06-27 22:06:59.560 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:06:59.561 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:06:59.564 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:06:59.568 | DEBUG    | ui.views.account_view:_on_groups_loaded:446 - 分组加载完成: 2个分组
2025-06-27 22:06:59.581 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:06:59.667 | INFO     | app.services.account_service:get_all_accounts:290 - 获取所有账户成功, 共 2 个
2025-06-27 22:06:59.668 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:06:59.668 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:06:59.669 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:06:59.673 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:06:59.675 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-06-27 22:06:59.680 | DEBUG    | ui.views.account_view:_on_accounts_loaded:579 - 账户加载完成: 2个账户
2025-06-27 22:06:59.700 | DEBUG    | ui.views.account_view:_update_group_counts:185 - 分组计数已更新: {2: 1, 1: 1}
2025-06-27 22:06:59.704 | INFO     | ui.views.account_view:_auto_login_accounts:634 - 开始调用控制器进行 2 个账户的自动登录。
2025-06-27 22:06:59.704 | INFO     | app.services.account_service:batch_auto_login:1165 - 服务层：开始批量自动登录 2 个账户, 最大并发: 5
2025-06-27 22:06:59.705 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:06:59.718 | INFO     | app.controllers.telegram_monitor_controller:_load_total_users_count_internal:191 - 成功加载总体统计数据: 总用户数 23, 今日采集 0, 日均采集 23, 运行天数 1
2025-06-27 22:06:59.718 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:156 - 成功加载 0 个任务
2025-06-27 22:06:59.718 | DEBUG    | ui.views.monitor_view:_on_loading_finished:625 - 加载完成
2025-06-27 22:06:59.739 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=True
2025-06-27 22:06:59.741 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=True
2025-06-27 22:06:59.743 | INFO     | ui.views.proxy_view:_load_initial_data:1044 - 初始数据加载完成，共 1 个代理
2025-06-27 22:06:59.744 | INFO     | app.services.account_service:get_all_accounts:290 - 获取所有账户成功, 共 2 个
2025-06-27 22:06:59.744 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:06:59.746 | INFO     | app.services.account_service:batch_auto_login:1204 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-06-27 22:06:59.746 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:06:59.748 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:06:59.751 | DEBUG    | ui.views.account_view:_update_group_counts:185 - 分组计数已更新: {2: 1, 1: 1}
2025-06-27 22:06:59.751 | DEBUG    | ui.views.account_view:_init_cache:152 - 账户缓存初始化完成，共 2 个账户
2025-06-27 22:06:59.756 | DEBUG    | ui.views.account_view:_on_accounts_loaded:579 - 账户加载完成: 2个账户
2025-06-27 22:06:59.776 | DEBUG    | ui.views.account_view:_update_group_counts:185 - 分组计数已更新: {2: 1, 1: 1}
2025-06-27 22:07:11.003 | WARNING  | core.auth.api_service:verify_vip:447 - 会员验证失败: 验证失败
2025-06-27 22:07:27.949 | ERROR    | utils.vip_checker:_check_vip_status:104 - VIP验证失败: 验证失败
2025-06-27 22:10:37.990 | INFO     | utils.logger:init_logger:98 - 日志系统初始化完成，默认级别: DEBUG，日志目录: H:\PyProject\TeleTest\APPDATA\logs
2025-06-27 22:10:43.708 | INFO     | data.database:init_database:31 - 初始化数据库连接: H:\PyProject\TeleTest\APPDATA\database\telegram_manager.db
2025-06-27 22:10:43.745 | DEBUG    | data.database:init_database:58 - 创建数据库表
2025-06-27 22:10:43.773 | INFO     | data.database:init_database:61 - 数据库初始化完成
2025-06-27 22:10:44.347 | DEBUG    | app.controllers.auth_controller:initialize_software:43 - 控制器: 初始化软件 app_id=1000, version=1.2.7
2025-06-27 22:10:44.348 | DEBUG    | core.auth.api_service:init_software:55 - 发送初始化请求: https://server.xile188.com/api/user/1000/windowns10/1.2.7/ini, params={}
2025-06-27 22:10:44.593 | DEBUG    | core.auth.api_service:init_software:66 - 初始化数据解密成功: {'new_content': '- 新增功能\n  - 1.新增图片上传功能\n  - 2.新增软件文档\n  - 3.新增软件自动更新，后续更新会自动检测版本更新，无需再下载更新包\n  - 4.新增设置账户信息及发信控制\n- 修复bug\n  - 删除监听任务时，不删除对应任务采集到的数据。\n  - 监听设置群无法发送到目标群\n  - 导出监听数据时应当导出单个任务而非所有数据\n- 优化记录\n  - 优化发信规则，直接调取符合要求的账户发送，无账户时才需要等待延时。\n  - 完善软件设置', 'new_url': None, 'notice': {'id': '2', 'content': '公告内容测试3', 'time': '**********'}}
2025-06-27 22:10:44.600 | INFO     | app.controllers.auth_controller:login:61 - 控制器: 用户登录 account=demo01
2025-06-27 22:10:47.684 | INFO     | core.auth.api_service:login:127 - 用户登录: account=demo01
2025-06-27 22:10:48.019 | INFO     | core.auth.api_service:get_user_info:347 - 获取用户信息
2025-06-27 22:10:48.223 | INFO     | core.auth.api_service:get_user_info:375 - 获取用户信息成功
2025-06-27 22:10:48.230 | INFO     | __main__:on_auth_completed:93 - 用户登录成功，正在启动主窗口...
2025-06-27 22:10:48.260 | INFO     | core.telegram.client_worker:__init__:61 - Telegram客户端工作线程初始化
2025-06-27 22:10:48.260 | INFO     | utils.vip_checker:initialize:26 - VIP检查器已初始化，等待登录后开始检查
2025-06-27 22:10:48.260 | INFO     | ui.main_window:_initialize_core_components:107 - MainWindow: 初始化核心组件...
2025-06-27 22:10:48.261 | INFO     | app.services.account_service:__init__:42 - 账户服务初始化
2025-06-27 22:10:48.262 | INFO     | app.controllers.account_controller:__init__:81 - 账户控制器初始化
2025-06-27 22:10:48.262 | INFO     | core.proxy.proxy_task_manager:run:284 - 代理任务管理器线程启动
2025-06-27 22:10:48.262 | INFO     | core.telegram.client_worker:run:65 - Telegram客户端工作线程开始运行
2025-06-27 22:10:48.263 | INFO     | app.services.monitor_service:__init__:39 - 初始化监控任务服务
2025-06-27 22:10:48.264 | INFO     | core.monitor.filter:__init__:26 - 初始化消息过滤器
2025-06-27 22:10:48.264 | INFO     | core.monitor.extractor:__init__:24 - 初始化关键词提取器
2025-06-27 22:10:48.264 | INFO     | core.telegram.client_manager:__init__:66 - Telegram客户端管理器初始化
2025-06-27 22:10:48.265 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-06-27 22:10:48.265 | INFO     | core.telegram.user_manager:__init__:30 - Telegram用户管理器初始化
2025-06-27 22:10:48.265 | INFO     | core.monitor.notifier:__init__:90 - 初始化通知管理器
2025-06-27 22:10:48.265 | INFO     | core.telegram.group_manager:__init__:36 - Telegram群组管理器初始化
2025-06-27 22:10:48.265 | INFO     | app.controllers.telegram_monitor_controller:__init__:64 - 初始化 Telegram 监控控制器
2025-06-27 22:10:48.266 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-06-27 22:10:48.266 | INFO     | core.telegram.monitor_manager:__init__:26 - Telegram监控管理器初始化
2025-06-27 22:10:48.266 | INFO     | core.telegram.telegram_service:__init__:39 - Telegram服务初始化完成
2025-06-27 22:10:48.266 | DEBUG    | app.controllers.telegram_monitor_controller:__init__:82 - 控制器初始化完成
2025-06-27 22:10:48.482 | INFO     | ui.views.monitor_view:__init__:48 - 初始化监控视图
2025-06-27 22:10:48.483 | DEBUG    | ui.views.monitor_view:_connect_signals:80 - 连接控制器信号
2025-06-27 22:10:48.688 | INFO     | ui.views.proxy_view:__init__:96 - 初始化视图，加载第一页代理数据
2025-06-27 22:10:48.957 | INFO     | __main__:on_auth_completed:97 - 主窗口已启动
2025-06-27 22:10:49.003 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:88 - 尝试连接监听日志信号...
2025-06-27 22:10:49.003 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:90 - 监听日志信号连接成功
2025-06-27 22:10:49.004 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:10:49.009 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:10:49.013 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:145 - 加载监控任务列表 (搜索关键词: '')
2025-06-27 22:10:49.013 | DEBUG    | ui.views.monitor_view:_on_loading_started:620 - 加载中: 正在加载任务列表...
2025-06-27 22:10:49.013 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:10:49.022 | INFO     | app.services.proxy_service:refresh_proxies:712 - 刷新代理列表: offset=0, limit=20
2025-06-27 22:10:49.022 | INFO     | core.proxy.proxy_task_manager:submit_task:172 - 提交耗时任务到子线程: refresh_proxies_0_20
2025-06-27 22:10:49.022 | DEBUG    | app.services.proxy_service:get_proxies:500 - 获取代理列表: offset=0, limit=20
2025-06-27 22:10:49.023 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:10:49.023 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:10:49.030 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:10:49.062 | INFO     | app.controllers.invite_controller:get_invite_tasks:38 - 获取邀请任务列表, status=None
2025-06-27 22:10:49.062 | INFO     | app.services.invite_service:get_invite_tasks:121 - 获取邀请任务列表, status=None
2025-06-27 22:10:49.068 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:10:49.085 | INFO     | app.services.invite_service:task_initialize:39 - 初始化邀请服务，重置运行中的任务为暂停状态
2025-06-27 22:10:49.086 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:10:49.093 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:10:49.095 | INFO     | core.proxy.proxy_task_manager:task_wrapper:199 - 任务 refresh_proxies_0_20 完成
2025-06-27 22:10:51.887 | INFO     | utils.vip_checker:start_checking:38 - 开始VIP状态检查
2025-06-27 22:10:51.888 | DEBUG    | utils.vip_checker:_check_vip_status:61 - 正在检查VIP状态...
2025-06-27 22:10:51.889 | INFO     | core.auth.api_service:verify_vip:425 - 会员验证接口调用
2025-06-27 22:10:51.995 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:10:51.996 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:10:52.007 | INFO     | app.services.invite_service:task_initialize:48 - 成功重置 0 个任务状态
2025-06-27 22:10:52.007 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:10:52.008 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:10:52.009 | DEBUG    | ui.views.monitor_view:_refresh_task_list:175 - 刷新任务列表，共0个任务
2025-06-27 22:10:52.010 | INFO     | ui.views.monitor_view:_refresh_task_list:189 - 没有找到监控任务
2025-06-27 22:10:52.010 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:10:52.015 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:10:52.017 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:10:52.071 | INFO     | app.services.account_service:get_all_groups:96 - 获取所有账户分组成功, 共 2 个
2025-06-27 22:10:52.071 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:10:52.090 | DEBUG    | ui.views.account_view:_on_groups_loaded:446 - 分组加载完成: 2个分组
2025-06-27 22:10:52.109 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:10:52.132 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:10:52.133 | INFO     | app.services.account_service:get_all_accounts:290 - 获取所有账户成功, 共 2 个
2025-06-27 22:10:52.134 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:10:52.134 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:10:52.142 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-06-27 22:10:52.148 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:10:52.162 | DEBUG    | ui.views.account_view:_on_accounts_loaded:579 - 账户加载完成: 2个账户
2025-06-27 22:10:52.189 | DEBUG    | ui.views.account_view:_update_group_counts:185 - 分组计数已更新: {2: 1, 1: 1}
2025-06-27 22:10:52.194 | INFO     | ui.views.account_view:_auto_login_accounts:634 - 开始调用控制器进行 2 个账户的自动登录。
2025-06-27 22:10:52.194 | INFO     | app.services.account_service:batch_auto_login:1165 - 服务层：开始批量自动登录 2 个账户, 最大并发: 5
2025-06-27 22:10:52.194 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:10:52.200 | INFO     | app.controllers.telegram_monitor_controller:_load_total_users_count_internal:191 - 成功加载总体统计数据: 总用户数 23, 今日采集 0, 日均采集 23, 运行天数 1
2025-06-27 22:10:52.200 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:156 - 成功加载 0 个任务
2025-06-27 22:10:52.200 | DEBUG    | ui.views.monitor_view:_on_loading_finished:625 - 加载完成
2025-06-27 22:10:52.218 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=True
2025-06-27 22:10:52.221 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=True
2025-06-27 22:10:52.223 | INFO     | ui.views.proxy_view:_load_initial_data:1044 - 初始数据加载完成，共 1 个代理
2025-06-27 22:10:52.227 | INFO     | app.services.account_service:get_all_accounts:290 - 获取所有账户成功, 共 2 个
2025-06-27 22:10:52.227 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:10:52.228 | INFO     | app.services.account_service:batch_auto_login:1204 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-06-27 22:10:52.229 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:10:52.232 | DEBUG    | ui.views.account_view:_update_group_counts:185 - 分组计数已更新: {2: 1, 1: 1}
2025-06-27 22:10:52.232 | DEBUG    | ui.views.account_view:_init_cache:152 - 账户缓存初始化完成，共 2 个账户
2025-06-27 22:10:52.237 | DEBUG    | ui.views.account_view:_on_accounts_loaded:579 - 账户加载完成: 2个账户
2025-06-27 22:10:52.257 | DEBUG    | ui.views.account_view:_update_group_counts:185 - 分组计数已更新: {2: 1, 1: 1}
2025-06-27 22:10:52.261 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:10:52.270 | INFO     | app.services.account_service:batch_auto_login:1204 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-06-27 22:10:52.270 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:10:52.273 | INFO     | app.services.account_service:batch_auto_login:1274 - 服务层：准备调用核心层处理 2 个账户的自动登录。
2025-06-27 22:10:52.273 | INFO     | app.services.account_service:batch_auto_login:1284 - 服务层：设置核心层任务超时为 120 秒。
2025-06-27 22:10:52.273 | INFO     | core.telegram.client_manager:batch_auto_login:1216 - 开始批量自动登录, 共 2 个账户, 最大并发 5
2025-06-27 22:10:52.274 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:259 - 创建客户端: H:\PyProject\TeleTest\APPDATA\sessions\+***********, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-06-27 22:10:52.275 | DEBUG    | core.telegram.client_manager:get_proxy:96 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-06-27 22:10:52.275 | DEBUG    | core.telegram.client_manager:get_proxy:143 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-06-27 22:10:52.282 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:259 - 创建客户端: H:\PyProject\TeleTest\APPDATA\sessions\+***********, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-06-27 22:10:52.282 | DEBUG    | core.telegram.client_manager:get_proxy:96 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-06-27 22:10:52.282 | DEBUG    | core.telegram.client_manager:get_proxy:143 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-06-27 22:10:52.337 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:10:52.427 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:10:54.497 | INFO     | core.telegram.client_manager:_connect_client:207 - 连接成功!
2025-06-27 22:10:54.873 | INFO     | core.telegram.client_manager:_connect_client:207 - 连接成功!
2025-06-27 22:10:55.299 | INFO     | core.telegram.client_manager:_verify_client_authorization:1191 - 账户已授权: +***********
2025-06-27 22:10:56.012 | INFO     | core.telegram.client_manager:_verify_client_authorization:1191 - 账户已授权: +***********
2025-06-27 22:10:58.007 | INFO     | core.telegram.client_manager:batch_auto_login:1365 - 批量登录完成: 总计 2, 成功 2, 失败 0
2025-06-27 22:10:58.266 | WARNING  | core.auth.api_service:verify_vip:447 - 会员验证失败: 验证失败
2025-06-27 22:11:26.585 | ERROR    | utils.vip_checker:_check_vip_status:107 - VIP状态检查出现异常: 'code'
Traceback (most recent call last):

  File "D:\Program Files\JetBrains\PyCharm 2025.1.1\plugins\python-ce\helpers\pydev\pydevd.py", line 2270, in <module>
    main()
    └ <function main at 0x0000026F47BCC680>

  File "D:\Program Files\JetBrains\PyCharm 2025.1.1\plugins\python-ce\helpers\pydev\pydevd.py", line 2252, in main
    globals = debugger.run(setup['file'], None, None, is_module)
              │        │   │                          └ False
              │        │   └ {'port': 56437, 'vm_type': None, 'client': '127.0.0.1', 'server': False, 'DEBUG_RECORD_SOCKET_READS': False, 'multiproc': Fal...
              │        └ <function PyDB.run at 0x0000026F47BCB420>
              └ <__main__.PyDB object at 0x0000026F346FA890>

  File "D:\Program Files\JetBrains\PyCharm 2025.1.1\plugins\python-ce\helpers\pydev\pydevd.py", line 1563, in run
    return self._exec(is_module, entry_point_fn, module_name, file, globals, locals)
           │    │     │          │               │            │     │        └ {'__name__': '__main__', '__doc__': '\nTelegram多账户管理系统 - 主程序入口\n', '__package__': '', '__loader__': <_frozen_importlib_extern...
           │    │     │          │               │            │     └ {'__name__': '__main__', '__doc__': '\nTelegram多账户管理系统 - 主程序入口\n', '__package__': '', '__loader__': <_frozen_importlib_extern...
           │    │     │          │               │            └ 'H:\\PyProject\\TeleTest\\main.py'
           │    │     │          │               └ None
           │    │     │          └ ''
           │    │     └ False
           │    └ <function PyDB._exec at 0x0000026F47BCB4C0>
           └ <__main__.PyDB object at 0x0000026F346FA890>

  File "D:\Program Files\JetBrains\PyCharm 2025.1.1\plugins\python-ce\helpers\pydev\pydevd.py", line 1570, in _exec
    pydev_imports.execfile(file, globals, locals)  # execute the script
    │             │        │     │        └ {'__name__': '__main__', '__doc__': '\nTelegram多账户管理系统 - 主程序入口\n', '__package__': '', '__loader__': <_frozen_importlib_extern...
    │             │        │     └ {'__name__': '__main__', '__doc__': '\nTelegram多账户管理系统 - 主程序入口\n', '__package__': '', '__loader__': <_frozen_importlib_extern...
    │             │        └ 'H:\\PyProject\\TeleTest\\main.py'
    │             └ <function execfile at 0x0000026F477E9F80>
    └ <module '_pydev_bundle.pydev_imports' from 'D:\\Program Files\\JetBrains\\PyCharm 2025.1.1\\plugins\\python-ce\\helpers\\pyde...

  File "D:\Program Files\JetBrains\PyCharm 2025.1.1\plugins\python-ce\helpers\pydev\_pydev_imps\_pydev_execfile.py", line 18, in execfile
    exec(compile(contents+"\n", file, 'exec'), glob, loc)
                 │              │              │     └ {'__name__': '__main__', '__doc__': '\nTelegram多账户管理系统 - 主程序入口\n', '__package__': '', '__loader__': <_frozen_importlib_extern...
                 │              │              └ {'__name__': '__main__', '__doc__': '\nTelegram多账户管理系统 - 主程序入口\n', '__package__': '', '__loader__': <_frozen_importlib_extern...
                 │              └ 'H:\\PyProject\\TeleTest\\main.py'
                 └ '#!/usr/bin/env python\n# -*- coding: utf-8 -*-\n\n"""\nTelegram多账户管理系统 - 主程序入口\n"""\nimport sys\nimport os\nimport asyncio\n...

  File "H:\PyProject\TeleTest\main.py", line 118, in <module>
    main()
    └ <function main at 0x0000026F5BB80C20>

  File "H:\PyProject\TeleTest\main.py", line 109, in main
    event_loop.run_forever()
    │          └ <function _QEventLoop.run_forever at 0x0000026F47FB4900>
    └ <QIOCPEventLoop running=True closed=False debug=False>

  File "H:\PyProject\TeleTest\script\Lib\site-packages\qasync\__init__.py", line 377, in run_forever
    rslt = self.__app.exec()
           └ <QIOCPEventLoop running=True closed=False debug=False>
  File "H:\PyProject\TeleTest\script\Lib\site-packages\qasync\__init__.py", line 279, in timerEvent
    handle._run()
    │      └ <function Handle._run at 0x0000026F47A8BC40>
    └ <Handle Task.task_wakeup(<Task finishe...> result=None>)>
  File "D:\Program Files\python11\Lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle Task.task_wakeup(<Task finishe...> result=None>)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle Task.task_wakeup(<Task finishe...> result=None>)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle Task.task_wakeup(<Task finishe...> result=None>)>

  File "H:\PyProject\TeleTest\ui\main_window.py", line 293, in on_login_succeeded
    await vip_checker.start_checking()
          │           └ <function VipChecker.start_checking at 0x0000026F48629760>
          └ <utils.vip_checker.VipChecker(0x26f5a42cca0) at 0x0000026F5B53C880>

  File "H:\PyProject\TeleTest\utils\vip_checker.py", line 42, in start_checking
    await self._check_vip_status()
          │    └ <function VipChecker._check_vip_status at 0x0000026F48629940>
          └ <utils.vip_checker.VipChecker(0x26f5a42cca0) at 0x0000026F5B53C880>

> File "H:\PyProject\TeleTest\utils\vip_checker.py", line 76, in _check_vip_status
    if result['code'] == 200 and result['msg']=='验证成功':
       │                         └ {'success': False, 'message': '验证失败'}
       └ {'success': False, 'message': '验证失败'}

KeyError: 'code'
2025-06-27 22:12:13.072 | ERROR    | data.repositories.message_repo:get_all_tasks:54 - 获取任务列表失败: Cannot switch to a different thread
	Current:  <greenlet.greenlet object at 0x0000026F1D666100 (otid=0x0000026F1D5C6580) current active started main>
	Expected: <greenlet.greenlet object at 0x0000026F481D4C80 (otid=0x0000026F4817B810) suspended active started main>
2025-06-27 22:12:13.072 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:12:13.072 | ERROR    | ui.views.send_msg_view:load_global_stats:987 - 加载全局统计数据失败: Method 'close()' can't be called here; method '_connection_for_bind()' is already in progress and this would cause an unexpected state change to <SessionTransactionState.CLOSED: 5> (Background on this error at: https://sqlalche.me/e/20/isce)
2025-06-27 22:12:30.716 | INFO     | utils.logger:init_logger:98 - 日志系统初始化完成，默认级别: DEBUG，日志目录: H:\PyProject\TeleTest\APPDATA\logs
2025-06-27 22:12:36.532 | INFO     | data.database:init_database:31 - 初始化数据库连接: H:\PyProject\TeleTest\APPDATA\database\telegram_manager.db
2025-06-27 22:12:36.562 | DEBUG    | data.database:init_database:58 - 创建数据库表
2025-06-27 22:12:36.584 | INFO     | data.database:init_database:61 - 数据库初始化完成
2025-06-27 22:12:37.136 | DEBUG    | app.controllers.auth_controller:initialize_software:43 - 控制器: 初始化软件 app_id=1000, version=1.2.7
2025-06-27 22:12:37.136 | DEBUG    | core.auth.api_service:init_software:55 - 发送初始化请求: https://server.xile188.com/api/user/1000/windowns10/1.2.7/ini, params={}
2025-06-27 22:12:37.424 | DEBUG    | core.auth.api_service:init_software:66 - 初始化数据解密成功: {'new_content': '- 新增功能\n  - 1.新增图片上传功能\n  - 2.新增软件文档\n  - 3.新增软件自动更新，后续更新会自动检测版本更新，无需再下载更新包\n  - 4.新增设置账户信息及发信控制\n- 修复bug\n  - 删除监听任务时，不删除对应任务采集到的数据。\n  - 监听设置群无法发送到目标群\n  - 导出监听数据时应当导出单个任务而非所有数据\n- 优化记录\n  - 优化发信规则，直接调取符合要求的账户发送，无账户时才需要等待延时。\n  - 完善软件设置', 'new_url': None, 'notice': {'id': '2', 'content': '公告内容测试3', 'time': '**********'}}
2025-06-27 22:12:37.431 | INFO     | app.controllers.auth_controller:login:61 - 控制器: 用户登录 account=demo01
2025-06-27 22:12:40.465 | INFO     | core.auth.api_service:login:127 - 用户登录: account=demo01
2025-06-27 22:12:40.765 | INFO     | core.auth.api_service:get_user_info:347 - 获取用户信息
2025-06-27 22:12:41.020 | INFO     | core.auth.api_service:get_user_info:375 - 获取用户信息成功
2025-06-27 22:12:41.026 | INFO     | __main__:on_auth_completed:93 - 用户登录成功，正在启动主窗口...
2025-06-27 22:12:41.054 | INFO     | core.telegram.client_worker:__init__:61 - Telegram客户端工作线程初始化
2025-06-27 22:12:41.055 | INFO     | utils.vip_checker:initialize:26 - VIP检查器已初始化，等待登录后开始检查
2025-06-27 22:12:41.055 | INFO     | ui.main_window:_initialize_core_components:107 - MainWindow: 初始化核心组件...
2025-06-27 22:12:41.057 | INFO     | core.telegram.client_worker:run:65 - Telegram客户端工作线程开始运行
2025-06-27 22:12:41.057 | INFO     | app.services.account_service:__init__:42 - 账户服务初始化
2025-06-27 22:12:41.057 | INFO     | core.proxy.proxy_task_manager:run:284 - 代理任务管理器线程启动
2025-06-27 22:12:41.057 | INFO     | app.controllers.account_controller:__init__:81 - 账户控制器初始化
2025-06-27 22:12:41.058 | INFO     | app.services.monitor_service:__init__:39 - 初始化监控任务服务
2025-06-27 22:12:41.058 | INFO     | core.monitor.filter:__init__:26 - 初始化消息过滤器
2025-06-27 22:12:41.058 | INFO     | core.monitor.extractor:__init__:24 - 初始化关键词提取器
2025-06-27 22:12:41.058 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-06-27 22:12:41.058 | INFO     | core.monitor.notifier:__init__:90 - 初始化通知管理器
2025-06-27 22:12:41.058 | INFO     | app.controllers.telegram_monitor_controller:__init__:64 - 初始化 Telegram 监控控制器
2025-06-27 22:12:41.059 | DEBUG    | app.controllers.telegram_monitor_controller:__init__:82 - 控制器初始化完成
2025-06-27 22:12:41.060 | INFO     | core.telegram.client_manager:__init__:66 - Telegram客户端管理器初始化
2025-06-27 22:12:41.061 | INFO     | core.telegram.user_manager:__init__:30 - Telegram用户管理器初始化
2025-06-27 22:12:41.062 | INFO     | core.telegram.group_manager:__init__:36 - Telegram群组管理器初始化
2025-06-27 22:12:41.064 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-06-27 22:12:41.064 | INFO     | core.telegram.monitor_manager:__init__:26 - Telegram监控管理器初始化
2025-06-27 22:12:41.065 | INFO     | core.telegram.telegram_service:__init__:39 - Telegram服务初始化完成
2025-06-27 22:12:41.277 | INFO     | ui.views.monitor_view:__init__:48 - 初始化监控视图
2025-06-27 22:12:41.277 | DEBUG    | ui.views.monitor_view:_connect_signals:80 - 连接控制器信号
2025-06-27 22:12:41.473 | INFO     | ui.views.proxy_view:__init__:96 - 初始化视图，加载第一页代理数据
2025-06-27 22:12:41.738 | INFO     | __main__:on_auth_completed:97 - 主窗口已启动
2025-06-27 22:12:41.796 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:88 - 尝试连接监听日志信号...
2025-06-27 22:12:41.797 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:90 - 监听日志信号连接成功
2025-06-27 22:12:41.798 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:12:41.803 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:12:41.808 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:145 - 加载监控任务列表 (搜索关键词: '')
2025-06-27 22:12:41.808 | DEBUG    | ui.views.monitor_view:_on_loading_started:620 - 加载中: 正在加载任务列表...
2025-06-27 22:12:41.808 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:12:41.816 | INFO     | app.services.proxy_service:refresh_proxies:712 - 刷新代理列表: offset=0, limit=20
2025-06-27 22:12:41.817 | INFO     | core.proxy.proxy_task_manager:submit_task:172 - 提交耗时任务到子线程: refresh_proxies_0_20
2025-06-27 22:12:41.817 | DEBUG    | app.services.proxy_service:get_proxies:500 - 获取代理列表: offset=0, limit=20
2025-06-27 22:12:41.817 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:12:41.817 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:12:41.832 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:12:41.881 | INFO     | app.controllers.invite_controller:get_invite_tasks:38 - 获取邀请任务列表, status=None
2025-06-27 22:12:41.881 | INFO     | app.services.invite_service:get_invite_tasks:121 - 获取邀请任务列表, status=None
2025-06-27 22:12:41.881 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:12:41.883 | INFO     | app.services.invite_service:task_initialize:39 - 初始化邀请服务，重置运行中的任务为暂停状态
2025-06-27 22:12:41.883 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:12:41.891 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:12:41.893 | INFO     | core.proxy.proxy_task_manager:task_wrapper:199 - 任务 refresh_proxies_0_20 完成
2025-06-27 22:12:45.538 | INFO     | utils.vip_checker:start_checking:38 - 开始VIP状态检查
2025-06-27 22:12:45.539 | DEBUG    | utils.vip_checker:_check_vip_status:61 - 正在检查VIP状态...
2025-06-27 22:12:45.539 | INFO     | core.auth.api_service:verify_vip:425 - 会员验证接口调用
2025-06-27 22:12:45.701 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:12:45.703 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:12:45.716 | DEBUG    | ui.views.monitor_view:_refresh_task_list:175 - 刷新任务列表，共0个任务
2025-06-27 22:12:45.717 | INFO     | ui.views.monitor_view:_refresh_task_list:189 - 没有找到监控任务
2025-06-27 22:12:45.717 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:12:45.720 | INFO     | app.services.invite_service:task_initialize:48 - 成功重置 0 个任务状态
2025-06-27 22:12:45.720 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:12:45.724 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:12:45.730 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:12:45.734 | INFO     | app.services.account_service:get_all_groups:96 - 获取所有账户分组成功, 共 2 个
2025-06-27 22:12:45.734 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:12:45.736 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:12:45.743 | DEBUG    | ui.views.account_view:_on_groups_loaded:446 - 分组加载完成: 2个分组
2025-06-27 22:12:45.761 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:12:45.843 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:12:45.844 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:12:45.845 | INFO     | app.services.account_service:get_all_accounts:290 - 获取所有账户成功, 共 2 个
2025-06-27 22:12:45.845 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:12:45.850 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-06-27 22:12:45.855 | INFO     | app.controllers.telegram_monitor_controller:_load_total_users_count_internal:191 - 成功加载总体统计数据: 总用户数 23, 今日采集 0, 日均采集 23, 运行天数 1
2025-06-27 22:12:45.856 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:156 - 成功加载 0 个任务
2025-06-27 22:12:45.856 | DEBUG    | ui.views.monitor_view:_on_loading_finished:625 - 加载完成
2025-06-27 22:12:45.867 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:12:45.870 | DEBUG    | ui.views.account_view:_on_accounts_loaded:579 - 账户加载完成: 2个账户
2025-06-27 22:12:45.897 | DEBUG    | ui.views.account_view:_update_group_counts:185 - 分组计数已更新: {2: 1, 1: 1}
2025-06-27 22:12:45.902 | INFO     | ui.views.account_view:_auto_login_accounts:634 - 开始调用控制器进行 2 个账户的自动登录。
2025-06-27 22:12:45.902 | INFO     | app.services.account_service:batch_auto_login:1165 - 服务层：开始批量自动登录 2 个账户, 最大并发: 5
2025-06-27 22:12:45.902 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:12:45.929 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=True
2025-06-27 22:12:45.931 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=True
2025-06-27 22:12:45.933 | INFO     | ui.views.proxy_view:_load_initial_data:1044 - 初始数据加载完成，共 1 个代理
2025-06-27 22:12:45.939 | INFO     | app.services.account_service:get_all_accounts:290 - 获取所有账户成功, 共 2 个
2025-06-27 22:12:45.939 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:12:45.941 | INFO     | app.services.account_service:batch_auto_login:1204 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-06-27 22:12:45.941 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:12:45.946 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:12:45.949 | DEBUG    | ui.views.account_view:_update_group_counts:185 - 分组计数已更新: {2: 1, 1: 1}
2025-06-27 22:12:45.949 | DEBUG    | ui.views.account_view:_init_cache:152 - 账户缓存初始化完成，共 2 个账户
2025-06-27 22:12:45.956 | DEBUG    | ui.views.account_view:_on_accounts_loaded:579 - 账户加载完成: 2个账户
2025-06-27 22:12:45.985 | DEBUG    | ui.views.account_view:_update_group_counts:185 - 分组计数已更新: {2: 1, 1: 1}
2025-06-27 22:12:46.002 | INFO     | app.services.account_service:batch_auto_login:1204 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-06-27 22:12:46.002 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:12:46.005 | INFO     | app.services.account_service:batch_auto_login:1274 - 服务层：准备调用核心层处理 2 个账户的自动登录。
2025-06-27 22:12:46.006 | INFO     | core.telegram.client_manager:batch_auto_login:1216 - 开始批量自动登录, 共 2 个账户, 最大并发 5
2025-06-27 22:12:46.006 | INFO     | app.services.account_service:batch_auto_login:1284 - 服务层：设置核心层任务超时为 120 秒。
2025-06-27 22:12:46.008 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:259 - 创建客户端: H:\PyProject\TeleTest\APPDATA\sessions\+***********, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-06-27 22:12:46.008 | DEBUG    | core.telegram.client_manager:get_proxy:96 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-06-27 22:12:46.008 | DEBUG    | core.telegram.client_manager:get_proxy:143 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-06-27 22:12:46.013 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:259 - 创建客户端: H:\PyProject\TeleTest\APPDATA\sessions\+***********, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-06-27 22:12:46.013 | DEBUG    | core.telegram.client_manager:get_proxy:96 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-06-27 22:12:46.014 | DEBUG    | core.telegram.client_manager:get_proxy:143 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-06-27 22:12:48.193 | INFO     | core.telegram.client_manager:_connect_client:207 - 连接成功!
2025-06-27 22:12:48.670 | INFO     | core.telegram.client_manager:_connect_client:207 - 连接成功!
2025-06-27 22:12:49.490 | INFO     | core.telegram.client_manager:_verify_client_authorization:1191 - 账户已授权: +***********
2025-06-27 22:12:49.700 | INFO     | core.telegram.client_manager:_verify_client_authorization:1191 - 账户已授权: +***********
2025-06-27 22:12:51.707 | INFO     | core.telegram.client_manager:batch_auto_login:1365 - 批量登录完成: 总计 2, 成功 2, 失败 0
2025-06-27 22:12:52.028 | WARNING  | core.auth.api_service:verify_vip:447 - 会员验证失败: 验证失败
2025-06-27 22:12:58.063 | ERROR    | utils.vip_checker:_check_vip_status:87 - VIP验证结果解析出错: 'code'
Traceback (most recent call last):

  File "D:\Program Files\JetBrains\PyCharm 2025.1.1\plugins\python-ce\helpers\pydev\pydevd.py", line 2270, in <module>
    main()
    └ <function main at 0x00000216B9B6C680>

  File "D:\Program Files\JetBrains\PyCharm 2025.1.1\plugins\python-ce\helpers\pydev\pydevd.py", line 2252, in main
    globals = debugger.run(setup['file'], None, None, is_module)
              │        │   │                          └ False
              │        │   └ {'port': 56592, 'vm_type': None, 'client': '127.0.0.1', 'server': False, 'DEBUG_RECORD_SOCKET_READS': False, 'multiproc': Fal...
              │        └ <function PyDB.run at 0x00000216B9B6B420>
              └ <__main__.PyDB object at 0x00000216A675A890>

  File "D:\Program Files\JetBrains\PyCharm 2025.1.1\plugins\python-ce\helpers\pydev\pydevd.py", line 1563, in run
    return self._exec(is_module, entry_point_fn, module_name, file, globals, locals)
           │    │     │          │               │            │     │        └ {'__name__': '__main__', '__doc__': '\nTelegram多账户管理系统 - 主程序入口\n', '__package__': '', '__loader__': <_frozen_importlib_extern...
           │    │     │          │               │            │     └ {'__name__': '__main__', '__doc__': '\nTelegram多账户管理系统 - 主程序入口\n', '__package__': '', '__loader__': <_frozen_importlib_extern...
           │    │     │          │               │            └ 'H:\\PyProject\\TeleTest\\main.py'
           │    │     │          │               └ None
           │    │     │          └ ''
           │    │     └ False
           │    └ <function PyDB._exec at 0x00000216B9B6B4C0>
           └ <__main__.PyDB object at 0x00000216A675A890>

  File "D:\Program Files\JetBrains\PyCharm 2025.1.1\plugins\python-ce\helpers\pydev\pydevd.py", line 1570, in _exec
    pydev_imports.execfile(file, globals, locals)  # execute the script
    │             │        │     │        └ {'__name__': '__main__', '__doc__': '\nTelegram多账户管理系统 - 主程序入口\n', '__package__': '', '__loader__': <_frozen_importlib_extern...
    │             │        │     └ {'__name__': '__main__', '__doc__': '\nTelegram多账户管理系统 - 主程序入口\n', '__package__': '', '__loader__': <_frozen_importlib_extern...
    │             │        └ 'H:\\PyProject\\TeleTest\\main.py'
    │             └ <function execfile at 0x00000216B9789F80>
    └ <module '_pydev_bundle.pydev_imports' from 'D:\\Program Files\\JetBrains\\PyCharm 2025.1.1\\plugins\\python-ce\\helpers\\pyde...

  File "D:\Program Files\JetBrains\PyCharm 2025.1.1\plugins\python-ce\helpers\pydev\_pydev_imps\_pydev_execfile.py", line 18, in execfile
    exec(compile(contents+"\n", file, 'exec'), glob, loc)
                 │              │              │     └ {'__name__': '__main__', '__doc__': '\nTelegram多账户管理系统 - 主程序入口\n', '__package__': '', '__loader__': <_frozen_importlib_extern...
                 │              │              └ {'__name__': '__main__', '__doc__': '\nTelegram多账户管理系统 - 主程序入口\n', '__package__': '', '__loader__': <_frozen_importlib_extern...
                 │              └ 'H:\\PyProject\\TeleTest\\main.py'
                 └ '#!/usr/bin/env python\n# -*- coding: utf-8 -*-\n\n"""\nTelegram多账户管理系统 - 主程序入口\n"""\nimport sys\nimport os\nimport asyncio\n...

  File "H:\PyProject\TeleTest\main.py", line 118, in <module>
    main()
    └ <function main at 0x00000216CDB404A0>

  File "H:\PyProject\TeleTest\main.py", line 109, in main
    event_loop.run_forever()
    │          └ <function _QEventLoop.run_forever at 0x00000216B9F84900>
    └ <QIOCPEventLoop running=True closed=False debug=False>

  File "H:\PyProject\TeleTest\script\Lib\site-packages\qasync\__init__.py", line 377, in run_forever
    rslt = self.__app.exec()
           └ <QIOCPEventLoop running=True closed=False debug=False>
  File "H:\PyProject\TeleTest\script\Lib\site-packages\qasync\__init__.py", line 279, in timerEvent
    handle._run()
    │      └ <function Handle._run at 0x00000216B9A2BC40>
    └ <Handle Task.task_wakeup(<Task finishe...> result=None>)>
  File "D:\Program Files\python11\Lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle Task.task_wakeup(<Task finishe...> result=None>)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle Task.task_wakeup(<Task finishe...> result=None>)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle Task.task_wakeup(<Task finishe...> result=None>)>

  File "H:\PyProject\TeleTest\ui\main_window.py", line 293, in on_login_succeeded
    await vip_checker.start_checking()
          │           └ <function VipChecker.start_checking at 0x00000216BA5C9760>
          └ <utils.vip_checker.VipChecker(0x216cc39eda0) at 0x00000216BA5BB900>

  File "H:\PyProject\TeleTest\utils\vip_checker.py", line 42, in start_checking
    await self._check_vip_status()
          │    └ <function VipChecker._check_vip_status at 0x00000216BA5C9940>
          └ <utils.vip_checker.VipChecker(0x216cc39eda0) at 0x00000216BA5BB900>

> File "H:\PyProject\TeleTest\utils\vip_checker.py", line 76, in _check_vip_status
    if result['code'] == 200 and result['msg']=='验证成功':
       │                         └ {'success': False, 'message': '验证失败'}
       └ {'success': False, 'message': '验证失败'}

KeyError: 'code'
2025-06-27 22:13:10.464 | ERROR    | data.repositories.message_repo:get_task_target_stats:187 - 获取任务1目标统计失败: Cannot switch to a different thread
	Current:  <greenlet.greenlet object at 0x00000216B9FD2D00 (otid=0x000002168E39F1E0) current active started main>
	Expected: <greenlet.greenlet object at 0x00000216BA184D00 (otid=0x00000216BA12B810) suspended active started main>
2025-06-27 22:13:10.465 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:13:10.471 | ERROR    | ui.views.send_msg_view:load_tasks:50 - 加载任务列表失败: coroutine ignored GeneratorExit
2025-06-27 22:13:22.801 | INFO     | utils.logger:init_logger:98 - 日志系统初始化完成，默认级别: DEBUG，日志目录: H:\PyProject\TeleTest\APPDATA\logs
2025-06-27 22:13:23.863 | INFO     | data.database:init_database:31 - 初始化数据库连接: H:\PyProject\TeleTest\APPDATA\database\telegram_manager.db
2025-06-27 22:13:23.885 | DEBUG    | data.database:init_database:58 - 创建数据库表
2025-06-27 22:13:23.900 | INFO     | data.database:init_database:61 - 数据库初始化完成
2025-06-27 22:13:24.454 | DEBUG    | app.controllers.auth_controller:initialize_software:43 - 控制器: 初始化软件 app_id=1000, version=1.2.7
2025-06-27 22:13:24.455 | DEBUG    | core.auth.api_service:init_software:55 - 发送初始化请求: https://server.xile188.com/api/user/1000/windowns10/1.2.7/ini, params={}
2025-06-27 22:13:24.761 | DEBUG    | core.auth.api_service:init_software:66 - 初始化数据解密成功: {'new_content': '- 新增功能\n  - 1.新增图片上传功能\n  - 2.新增软件文档\n  - 3.新增软件自动更新，后续更新会自动检测版本更新，无需再下载更新包\n  - 4.新增设置账户信息及发信控制\n- 修复bug\n  - 删除监听任务时，不删除对应任务采集到的数据。\n  - 监听设置群无法发送到目标群\n  - 导出监听数据时应当导出单个任务而非所有数据\n- 优化记录\n  - 优化发信规则，直接调取符合要求的账户发送，无账户时才需要等待延时。\n  - 完善软件设置', 'new_url': None, 'notice': {'id': '2', 'content': '公告内容测试3', 'time': '**********'}}
2025-06-27 22:13:24.768 | INFO     | app.controllers.auth_controller:login:61 - 控制器: 用户登录 account=demo01
2025-06-27 22:13:27.737 | INFO     | core.auth.api_service:login:127 - 用户登录: account=demo01
2025-06-27 22:13:28.006 | INFO     | core.auth.api_service:get_user_info:347 - 获取用户信息
2025-06-27 22:13:28.294 | INFO     | core.auth.api_service:get_user_info:375 - 获取用户信息成功
2025-06-27 22:13:28.300 | INFO     | __main__:on_auth_completed:93 - 用户登录成功，正在启动主窗口...
2025-06-27 22:13:28.321 | INFO     | core.telegram.client_worker:__init__:61 - Telegram客户端工作线程初始化
2025-06-27 22:13:28.321 | INFO     | utils.vip_checker:initialize:26 - VIP检查器已初始化，等待登录后开始检查
2025-06-27 22:13:28.321 | INFO     | ui.main_window:_initialize_core_components:107 - MainWindow: 初始化核心组件...
2025-06-27 22:13:28.322 | INFO     | app.services.account_service:__init__:42 - 账户服务初始化
2025-06-27 22:13:28.322 | INFO     | core.telegram.client_worker:run:65 - Telegram客户端工作线程开始运行
2025-06-27 22:13:28.322 | INFO     | app.controllers.account_controller:__init__:81 - 账户控制器初始化
2025-06-27 22:13:28.323 | INFO     | core.proxy.proxy_task_manager:run:284 - 代理任务管理器线程启动
2025-06-27 22:13:28.323 | INFO     | app.services.monitor_service:__init__:39 - 初始化监控任务服务
2025-06-27 22:13:28.323 | INFO     | core.monitor.filter:__init__:26 - 初始化消息过滤器
2025-06-27 22:13:28.324 | INFO     | core.monitor.extractor:__init__:24 - 初始化关键词提取器
2025-06-27 22:13:28.324 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-06-27 22:13:28.324 | INFO     | core.monitor.notifier:__init__:90 - 初始化通知管理器
2025-06-27 22:13:28.324 | INFO     | app.controllers.telegram_monitor_controller:__init__:64 - 初始化 Telegram 监控控制器
2025-06-27 22:13:28.324 | INFO     | core.telegram.client_manager:__init__:66 - Telegram客户端管理器初始化
2025-06-27 22:13:28.325 | DEBUG    | app.controllers.telegram_monitor_controller:__init__:82 - 控制器初始化完成
2025-06-27 22:13:28.325 | INFO     | core.telegram.user_manager:__init__:30 - Telegram用户管理器初始化
2025-06-27 22:13:28.326 | INFO     | core.telegram.group_manager:__init__:36 - Telegram群组管理器初始化
2025-06-27 22:13:28.326 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-06-27 22:13:28.326 | INFO     | core.telegram.monitor_manager:__init__:26 - Telegram监控管理器初始化
2025-06-27 22:13:28.326 | INFO     | core.telegram.telegram_service:__init__:39 - Telegram服务初始化完成
2025-06-27 22:13:28.506 | INFO     | ui.views.monitor_view:__init__:48 - 初始化监控视图
2025-06-27 22:13:28.506 | DEBUG    | ui.views.monitor_view:_connect_signals:80 - 连接控制器信号
2025-06-27 22:13:28.700 | INFO     | ui.views.proxy_view:__init__:96 - 初始化视图，加载第一页代理数据
2025-06-27 22:13:29.243 | INFO     | __main__:on_auth_completed:97 - 主窗口已启动
2025-06-27 22:13:29.280 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:88 - 尝试连接监听日志信号...
2025-06-27 22:13:29.280 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:90 - 监听日志信号连接成功
2025-06-27 22:13:29.280 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:13:29.283 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:13:29.286 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:145 - 加载监控任务列表 (搜索关键词: '')
2025-06-27 22:13:29.286 | DEBUG    | ui.views.monitor_view:_on_loading_started:620 - 加载中: 正在加载任务列表...
2025-06-27 22:13:29.287 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:13:29.292 | INFO     | app.services.proxy_service:refresh_proxies:712 - 刷新代理列表: offset=0, limit=20
2025-06-27 22:13:29.293 | INFO     | core.proxy.proxy_task_manager:submit_task:172 - 提交耗时任务到子线程: refresh_proxies_0_20
2025-06-27 22:13:29.293 | DEBUG    | app.services.proxy_service:get_proxies:500 - 获取代理列表: offset=0, limit=20
2025-06-27 22:13:29.293 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:13:29.293 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:13:29.295 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:13:29.319 | INFO     | app.controllers.invite_controller:get_invite_tasks:38 - 获取邀请任务列表, status=None
2025-06-27 22:13:29.319 | INFO     | app.services.invite_service:get_invite_tasks:121 - 获取邀请任务列表, status=None
2025-06-27 22:13:29.319 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:13:29.320 | INFO     | app.services.invite_service:task_initialize:39 - 初始化邀请服务，重置运行中的任务为暂停状态
2025-06-27 22:13:29.320 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:13:29.324 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:13:29.325 | INFO     | utils.vip_checker:start_checking:38 - 开始VIP状态检查
2025-06-27 22:13:29.325 | DEBUG    | utils.vip_checker:_check_vip_status:61 - 正在检查VIP状态...
2025-06-27 22:13:29.325 | INFO     | core.auth.api_service:verify_vip:425 - 会员验证接口调用
2025-06-27 22:13:29.326 | INFO     | core.proxy.proxy_task_manager:task_wrapper:199 - 任务 refresh_proxies_0_20 完成
2025-06-27 22:13:29.444 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:13:29.446 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:13:29.448 | INFO     | app.services.invite_service:task_initialize:48 - 成功重置 0 个任务状态
2025-06-27 22:13:29.448 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:13:29.449 | DEBUG    | ui.views.monitor_view:_refresh_task_list:175 - 刷新任务列表，共0个任务
2025-06-27 22:13:29.450 | INFO     | ui.views.monitor_view:_refresh_task_list:189 - 没有找到监控任务
2025-06-27 22:13:29.450 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:13:29.451 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:13:29.454 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:13:29.455 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:13:29.459 | INFO     | app.services.account_service:get_all_groups:96 - 获取所有账户分组成功, 共 2 个
2025-06-27 22:13:29.459 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:13:29.525 | DEBUG    | ui.views.account_view:_on_groups_loaded:446 - 分组加载完成: 2个分组
2025-06-27 22:13:29.540 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:13:29.553 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:13:29.554 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:13:29.554 | INFO     | app.services.account_service:get_all_accounts:290 - 获取所有账户成功, 共 2 个
2025-06-27 22:13:29.555 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:13:29.558 | INFO     | app.controllers.telegram_monitor_controller:_load_total_users_count_internal:191 - 成功加载总体统计数据: 总用户数 23, 今日采集 0, 日均采集 23, 运行天数 1
2025-06-27 22:13:29.558 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:156 - 成功加载 0 个任务
2025-06-27 22:13:29.558 | DEBUG    | ui.views.monitor_view:_on_loading_finished:625 - 加载完成
2025-06-27 22:13:29.559 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-06-27 22:13:29.569 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:13:29.570 | DEBUG    | ui.views.account_view:_on_accounts_loaded:579 - 账户加载完成: 2个账户
2025-06-27 22:13:29.591 | DEBUG    | ui.views.account_view:_update_group_counts:185 - 分组计数已更新: {2: 1, 1: 1}
2025-06-27 22:13:29.595 | INFO     | ui.views.account_view:_auto_login_accounts:634 - 开始调用控制器进行 2 个账户的自动登录。
2025-06-27 22:13:29.595 | INFO     | app.services.account_service:batch_auto_login:1165 - 服务层：开始批量自动登录 2 个账户, 最大并发: 5
2025-06-27 22:13:29.595 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:13:29.600 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=True
2025-06-27 22:13:29.602 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=True
2025-06-27 22:13:29.603 | INFO     | ui.views.proxy_view:_load_initial_data:1044 - 初始数据加载完成，共 1 个代理
2025-06-27 22:13:29.607 | INFO     | app.services.account_service:batch_auto_login:1204 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-06-27 22:13:29.607 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:13:29.608 | INFO     | app.services.account_service:get_all_accounts:290 - 获取所有账户成功, 共 2 个
2025-06-27 22:13:29.608 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:13:29.609 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:13:29.610 | DEBUG    | ui.views.account_view:_update_group_counts:185 - 分组计数已更新: {2: 1, 1: 1}
2025-06-27 22:13:29.610 | DEBUG    | ui.views.account_view:_init_cache:152 - 账户缓存初始化完成，共 2 个账户
2025-06-27 22:13:29.613 | DEBUG    | ui.views.account_view:_on_accounts_loaded:579 - 账户加载完成: 2个账户
2025-06-27 22:13:29.633 | DEBUG    | ui.views.account_view:_update_group_counts:185 - 分组计数已更新: {2: 1, 1: 1}
2025-06-27 22:13:29.641 | INFO     | app.services.account_service:batch_auto_login:1204 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-06-27 22:13:29.641 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:13:29.642 | INFO     | app.services.account_service:batch_auto_login:1274 - 服务层：准备调用核心层处理 2 个账户的自动登录。
2025-06-27 22:13:29.642 | INFO     | app.services.account_service:batch_auto_login:1284 - 服务层：设置核心层任务超时为 120 秒。
2025-06-27 22:13:29.643 | INFO     | core.telegram.client_manager:batch_auto_login:1216 - 开始批量自动登录, 共 2 个账户, 最大并发 5
2025-06-27 22:13:29.643 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:259 - 创建客户端: H:\PyProject\TeleTest\APPDATA\sessions\+***********, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-06-27 22:13:29.643 | DEBUG    | core.telegram.client_manager:get_proxy:96 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-06-27 22:13:29.644 | DEBUG    | core.telegram.client_manager:get_proxy:143 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-06-27 22:13:29.647 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:259 - 创建客户端: H:\PyProject\TeleTest\APPDATA\sessions\+***********, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-06-27 22:13:29.648 | DEBUG    | core.telegram.client_manager:get_proxy:96 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-06-27 22:13:29.648 | DEBUG    | core.telegram.client_manager:get_proxy:143 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-06-27 22:13:29.665 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:13:29.747 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:13:29.753 | WARNING  | core.auth.api_service:verify_vip:447 - 会员验证失败: 验证失败
2025-06-27 22:13:29.753 | ERROR    | utils.vip_checker:_check_vip_status:87 - VIP验证结果解析出错: 'code'
Traceback (most recent call last):

  File "H:\PyProject\TeleTest\main.py", line 118, in <module>
    main()
    └ <function main at 0x00000279141FEFC0>

  File "H:\PyProject\TeleTest\main.py", line 109, in main
    event_loop.run_forever()
    │          └ <function _QEventLoop.run_forever at 0x000002797F78EB60>
    └ <QIOCPEventLoop running=True closed=False debug=False>

  File "H:\PyProject\TeleTest\script\Lib\site-packages\qasync\__init__.py", line 377, in run_forever
    rslt = self.__app.exec()
           └ <QIOCPEventLoop running=True closed=False debug=False>
  File "H:\PyProject\TeleTest\script\Lib\site-packages\qasync\__init__.py", line 279, in timerEvent
    handle._run()
    │      └ <function Handle._run at 0x000002797F32C900>
    └ <Handle Task.task_wakeup(<Task finishe...> result=None>)>
  File "D:\Program Files\python11\Lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle Task.task_wakeup(<Task finishe...> result=None>)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle Task.task_wakeup(<Task finishe...> result=None>)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle Task.task_wakeup(<Task finishe...> result=None>)>

  File "H:\PyProject\TeleTest\ui\main_window.py", line 293, in on_login_succeeded
    await vip_checker.start_checking()
          │           └ <function VipChecker.start_checking at 0x0000027901480720>
          └ <utils.vip_checker.VipChecker(0x27910f253d0) at 0x0000027901479C80>

  File "H:\PyProject\TeleTest\utils\vip_checker.py", line 42, in start_checking
    await self._check_vip_status()
          │    └ <function VipChecker._check_vip_status at 0x0000027901480900>
          └ <utils.vip_checker.VipChecker(0x27910f253d0) at 0x0000027901479C80>

> File "H:\PyProject\TeleTest\utils\vip_checker.py", line 76, in _check_vip_status
    if result['code'] == 200 and result['msg']=='验证成功':
       │                         └ {'success': False, 'message': '验证失败'}
       └ {'success': False, 'message': '验证失败'}

KeyError: 'code'
2025-06-27 22:13:31.652 | INFO     | core.telegram.client_manager:_connect_client:207 - 连接成功!
2025-06-27 22:13:31.896 | INFO     | core.telegram.client_manager:_connect_client:207 - 连接成功!
2025-06-27 22:13:32.307 | INFO     | core.telegram.client_manager:_verify_client_authorization:1191 - 账户已授权: +***********
2025-06-27 22:13:32.654 | INFO     | core.telegram.client_manager:_verify_client_authorization:1191 - 账户已授权: +***********
2025-06-27 22:13:34.657 | INFO     | core.telegram.client_manager:batch_auto_login:1365 - 批量登录完成: 总计 2, 成功 2, 失败 0
2025-06-27 22:18:47.942 | INFO     | utils.logger:init_logger:98 - 日志系统初始化完成，默认级别: DEBUG，日志目录: H:\PyProject\TeleTest\APPDATA\logs
2025-06-27 22:18:53.966 | INFO     | utils.logger:init_logger:98 - 日志系统初始化完成，默认级别: DEBUG，日志目录: H:\PyProject\TeleTest\APPDATA\logs
2025-06-27 22:18:57.881 | INFO     | data.database:init_database:31 - 初始化数据库连接: H:\PyProject\TeleTest\APPDATA\database\telegram_manager.db
2025-06-27 22:18:57.913 | DEBUG    | data.database:init_database:58 - 创建数据库表
2025-06-27 22:18:57.936 | INFO     | data.database:init_database:61 - 数据库初始化完成
2025-06-27 22:18:58.693 | DEBUG    | app.controllers.auth_controller:initialize_software:43 - 控制器: 初始化软件 app_id=1000, version=1.2.7
2025-06-27 22:18:58.694 | DEBUG    | core.auth.api_service:init_software:55 - 发送初始化请求: https://server.xile188.com/api/user/1000/windowns10/1.2.7/ini, params={}
2025-06-27 22:18:58.985 | DEBUG    | core.auth.api_service:init_software:66 - 初始化数据解密成功: {'new_content': '- 新增功能\n  - 1.新增图片上传功能\n  - 2.新增软件文档\n  - 3.新增软件自动更新，后续更新会自动检测版本更新，无需再下载更新包\n  - 4.新增设置账户信息及发信控制\n- 修复bug\n  - 删除监听任务时，不删除对应任务采集到的数据。\n  - 监听设置群无法发送到目标群\n  - 导出监听数据时应当导出单个任务而非所有数据\n- 优化记录\n  - 优化发信规则，直接调取符合要求的账户发送，无账户时才需要等待延时。\n  - 完善软件设置', 'new_url': None, 'notice': {'id': '2', 'content': '公告内容测试3', 'time': '**********'}}
2025-06-27 22:18:59.004 | INFO     | app.controllers.auth_controller:login:61 - 控制器: 用户登录 account=demo01
2025-06-27 22:19:02.059 | INFO     | core.auth.api_service:login:127 - 用户登录: account=demo01
2025-06-27 22:19:02.365 | INFO     | core.auth.api_service:get_user_info:347 - 获取用户信息
2025-06-27 22:19:02.577 | INFO     | core.auth.api_service:get_user_info:375 - 获取用户信息成功
2025-06-27 22:19:02.584 | INFO     | __main__:on_auth_completed:93 - 用户登录成功，正在启动主窗口...
2025-06-27 22:19:02.612 | INFO     | core.telegram.client_worker:__init__:61 - Telegram客户端工作线程初始化
2025-06-27 22:19:02.612 | INFO     | utils.vip_checker:initialize:26 - VIP检查器已初始化，等待登录后开始检查
2025-06-27 22:19:02.612 | INFO     | ui.main_window:_initialize_core_components:107 - MainWindow: 初始化核心组件...
2025-06-27 22:19:02.613 | INFO     | app.services.account_service:__init__:42 - 账户服务初始化
2025-06-27 22:19:02.614 | INFO     | core.telegram.client_worker:run:65 - Telegram客户端工作线程开始运行
2025-06-27 22:19:02.614 | INFO     | app.controllers.account_controller:__init__:81 - 账户控制器初始化
2025-06-27 22:19:02.614 | INFO     | core.proxy.proxy_task_manager:run:284 - 代理任务管理器线程启动
2025-06-27 22:19:02.614 | INFO     | app.services.monitor_service:__init__:39 - 初始化监控任务服务
2025-06-27 22:19:02.615 | INFO     | core.monitor.filter:__init__:26 - 初始化消息过滤器
2025-06-27 22:19:02.615 | INFO     | core.monitor.extractor:__init__:24 - 初始化关键词提取器
2025-06-27 22:19:02.615 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-06-27 22:19:02.616 | INFO     | core.monitor.notifier:__init__:90 - 初始化通知管理器
2025-06-27 22:19:02.616 | INFO     | app.controllers.telegram_monitor_controller:__init__:64 - 初始化 Telegram 监控控制器
2025-06-27 22:19:02.617 | INFO     | core.telegram.client_manager:__init__:66 - Telegram客户端管理器初始化
2025-06-27 22:19:02.617 | DEBUG    | app.controllers.telegram_monitor_controller:__init__:82 - 控制器初始化完成
2025-06-27 22:19:02.617 | INFO     | core.telegram.user_manager:__init__:30 - Telegram用户管理器初始化
2025-06-27 22:19:02.617 | INFO     | core.telegram.group_manager:__init__:36 - Telegram群组管理器初始化
2025-06-27 22:19:02.619 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-06-27 22:19:02.619 | INFO     | core.telegram.monitor_manager:__init__:26 - Telegram监控管理器初始化
2025-06-27 22:19:02.620 | INFO     | core.telegram.telegram_service:__init__:39 - Telegram服务初始化完成
2025-06-27 22:19:02.825 | INFO     | ui.views.monitor_view:__init__:48 - 初始化监控视图
2025-06-27 22:19:02.825 | DEBUG    | ui.views.monitor_view:_connect_signals:80 - 连接控制器信号
2025-06-27 22:19:03.013 | INFO     | ui.views.proxy_view:__init__:96 - 初始化视图，加载第一页代理数据
2025-06-27 22:19:03.271 | INFO     | __main__:on_auth_completed:97 - 主窗口已启动
2025-06-27 22:19:03.312 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:88 - 尝试连接监听日志信号...
2025-06-27 22:19:03.312 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:90 - 监听日志信号连接成功
2025-06-27 22:19:03.313 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:19:03.318 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:19:03.322 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:145 - 加载监控任务列表 (搜索关键词: '')
2025-06-27 22:19:03.322 | DEBUG    | ui.views.monitor_view:_on_loading_started:620 - 加载中: 正在加载任务列表...
2025-06-27 22:19:03.322 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:19:03.329 | INFO     | app.services.proxy_service:refresh_proxies:712 - 刷新代理列表: offset=0, limit=20
2025-06-27 22:19:03.330 | INFO     | core.proxy.proxy_task_manager:submit_task:172 - 提交耗时任务到子线程: refresh_proxies_0_20
2025-06-27 22:19:03.330 | DEBUG    | app.services.proxy_service:get_proxies:500 - 获取代理列表: offset=0, limit=20
2025-06-27 22:19:03.330 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:19:03.330 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:19:03.337 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:19:03.341 | INFO     | app.controllers.invite_controller:get_invite_tasks:38 - 获取邀请任务列表, status=None
2025-06-27 22:19:03.341 | INFO     | app.services.invite_service:get_invite_tasks:121 - 获取邀请任务列表, status=None
2025-06-27 22:19:03.342 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:19:03.371 | INFO     | app.services.invite_service:task_initialize:39 - 初始化邀请服务，重置运行中的任务为暂停状态
2025-06-27 22:19:03.376 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:19:03.403 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:19:03.404 | INFO     | core.proxy.proxy_task_manager:task_wrapper:199 - 任务 refresh_proxies_0_20 完成
2025-06-27 22:19:06.218 | INFO     | utils.vip_checker:start_checking:38 - 开始VIP状态检查
2025-06-27 22:19:06.218 | DEBUG    | utils.vip_checker:_check_vip_status:61 - 正在检查VIP状态...
2025-06-27 22:19:06.218 | INFO     | core.auth.api_service:verify_vip:425 - 会员验证接口调用
2025-06-27 22:19:06.330 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:19:06.332 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:19:06.343 | DEBUG    | ui.views.monitor_view:_refresh_task_list:175 - 刷新任务列表，共0个任务
2025-06-27 22:19:06.344 | INFO     | ui.views.monitor_view:_refresh_task_list:189 - 没有找到监控任务
2025-06-27 22:19:06.344 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:19:06.346 | INFO     | app.services.invite_service:task_initialize:48 - 成功重置 0 个任务状态
2025-06-27 22:19:06.346 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:19:06.351 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:19:06.354 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:19:06.355 | INFO     | app.services.account_service:get_all_groups:96 - 获取所有账户分组成功, 共 2 个
2025-06-27 22:19:06.356 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:19:06.359 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:19:06.424 | DEBUG    | ui.views.account_view:_on_groups_loaded:446 - 分组加载完成: 2个分组
2025-06-27 22:19:06.441 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:19:06.462 | INFO     | app.services.account_service:get_all_accounts:290 - 获取所有账户成功, 共 2 个
2025-06-27 22:19:06.463 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:19:06.465 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:19:06.469 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:19:06.471 | DEBUG    | ui.views.account_view:_on_accounts_loaded:579 - 账户加载完成: 2个账户
2025-06-27 22:19:06.492 | DEBUG    | ui.views.account_view:_update_group_counts:185 - 分组计数已更新: {2: 1, 1: 1}
2025-06-27 22:19:06.496 | INFO     | ui.views.account_view:_auto_login_accounts:634 - 开始调用控制器进行 2 个账户的自动登录。
2025-06-27 22:19:06.497 | INFO     | app.services.account_service:batch_auto_login:1165 - 服务层：开始批量自动登录 2 个账户, 最大并发: 5
2025-06-27 22:19:06.497 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:19:06.502 | INFO     | app.controllers.telegram_monitor_controller:_load_total_users_count_internal:191 - 成功加载总体统计数据: 总用户数 23, 今日采集 0, 日均采集 23, 运行天数 1
2025-06-27 22:19:06.502 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:156 - 成功加载 0 个任务
2025-06-27 22:19:06.503 | DEBUG    | ui.views.monitor_view:_on_loading_finished:625 - 加载完成
2025-06-27 22:19:06.512 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:19:06.519 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-06-27 22:19:06.541 | INFO     | app.services.account_service:get_all_accounts:290 - 获取所有账户成功, 共 2 个
2025-06-27 22:19:06.542 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:19:06.543 | INFO     | app.services.account_service:batch_auto_login:1204 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-06-27 22:19:06.543 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:19:06.546 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:19:06.549 | DEBUG    | ui.views.account_view:_update_group_counts:185 - 分组计数已更新: {2: 1, 1: 1}
2025-06-27 22:19:06.549 | DEBUG    | ui.views.account_view:_init_cache:152 - 账户缓存初始化完成，共 2 个账户
2025-06-27 22:19:06.555 | DEBUG    | ui.views.account_view:_on_accounts_loaded:579 - 账户加载完成: 2个账户
2025-06-27 22:19:06.576 | DEBUG    | ui.views.account_view:_update_group_counts:185 - 分组计数已更新: {2: 1, 1: 1}
2025-06-27 22:19:06.590 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=True
2025-06-27 22:19:06.592 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=True
2025-06-27 22:19:06.593 | INFO     | ui.views.proxy_view:_load_initial_data:1044 - 初始数据加载完成，共 1 个代理
2025-06-27 22:19:06.598 | INFO     | app.services.account_service:batch_auto_login:1204 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-06-27 22:19:06.598 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:19:06.601 | INFO     | app.services.account_service:batch_auto_login:1274 - 服务层：准备调用核心层处理 2 个账户的自动登录。
2025-06-27 22:19:06.601 | INFO     | app.services.account_service:batch_auto_login:1284 - 服务层：设置核心层任务超时为 120 秒。
2025-06-27 22:19:06.602 | INFO     | core.telegram.client_manager:batch_auto_login:1216 - 开始批量自动登录, 共 2 个账户, 最大并发 5
2025-06-27 22:19:06.603 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:259 - 创建客户端: H:\PyProject\TeleTest\APPDATA\sessions\+***********, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-06-27 22:19:06.604 | DEBUG    | core.telegram.client_manager:get_proxy:96 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-06-27 22:19:06.605 | DEBUG    | core.telegram.client_manager:get_proxy:143 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-06-27 22:19:06.610 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:259 - 创建客户端: H:\PyProject\TeleTest\APPDATA\sessions\+***********, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-06-27 22:19:06.610 | DEBUG    | core.telegram.client_manager:get_proxy:96 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-06-27 22:19:06.611 | DEBUG    | core.telegram.client_manager:get_proxy:143 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-06-27 22:19:08.652 | INFO     | core.telegram.client_manager:_connect_client:207 - 连接成功!
2025-06-27 22:19:09.337 | INFO     | core.telegram.client_manager:_verify_client_authorization:1191 - 账户已授权: +***********
2025-06-27 22:19:09.680 | INFO     | core.telegram.client_manager:_connect_client:207 - 连接成功!
2025-06-27 22:19:10.369 | INFO     | core.telegram.client_manager:_verify_client_authorization:1191 - 账户已授权: +***********
2025-06-27 22:19:12.375 | INFO     | core.telegram.client_manager:batch_auto_login:1365 - 批量登录完成: 总计 2, 成功 2, 失败 0
2025-06-27 22:21:22.546 | INFO     | utils.logger:init_logger:98 - 日志系统初始化完成，默认级别: DEBUG，日志目录: H:\PyProject\TeleTest\APPDATA\logs
2025-06-27 22:21:26.600 | INFO     | data.database:init_database:31 - 初始化数据库连接: H:\PyProject\TeleTest\APPDATA\database\telegram_manager.db
2025-06-27 22:21:26.630 | DEBUG    | data.database:init_database:58 - 创建数据库表
2025-06-27 22:21:26.650 | INFO     | data.database:init_database:61 - 数据库初始化完成
2025-06-27 22:21:27.430 | DEBUG    | app.controllers.auth_controller:initialize_software:43 - 控制器: 初始化软件 app_id=1000, version=1.2.7
2025-06-27 22:21:27.431 | DEBUG    | core.auth.api_service:init_software:55 - 发送初始化请求: https://server.xile188.com/api/user/1000/windowns10/1.2.7/ini, params={}
2025-06-27 22:21:27.755 | DEBUG    | core.auth.api_service:init_software:66 - 初始化数据解密成功: {'new_content': '- 新增功能\n  - 1.新增图片上传功能\n  - 2.新增软件文档\n  - 3.新增软件自动更新，后续更新会自动检测版本更新，无需再下载更新包\n  - 4.新增设置账户信息及发信控制\n- 修复bug\n  - 删除监听任务时，不删除对应任务采集到的数据。\n  - 监听设置群无法发送到目标群\n  - 导出监听数据时应当导出单个任务而非所有数据\n- 优化记录\n  - 优化发信规则，直接调取符合要求的账户发送，无账户时才需要等待延时。\n  - 完善软件设置', 'new_url': None, 'notice': {'id': '2', 'content': '公告内容测试3', 'time': '**********'}}
2025-06-27 22:21:27.762 | INFO     | app.controllers.auth_controller:login:61 - 控制器: 用户登录 account=demo01
2025-06-27 22:21:30.825 | INFO     | core.auth.api_service:login:127 - 用户登录: account=demo01
2025-06-27 22:21:31.102 | INFO     | core.auth.api_service:get_user_info:347 - 获取用户信息
2025-06-27 22:21:31.354 | INFO     | core.auth.api_service:get_user_info:375 - 获取用户信息成功
2025-06-27 22:21:31.359 | INFO     | __main__:on_auth_completed:93 - 用户登录成功，正在启动主窗口...
2025-06-27 22:21:31.390 | INFO     | core.telegram.client_worker:__init__:61 - Telegram客户端工作线程初始化
2025-06-27 22:21:31.390 | INFO     | utils.vip_checker:initialize:26 - VIP检查器已初始化，等待登录后开始检查
2025-06-27 22:21:31.391 | INFO     | ui.main_window:_initialize_core_components:107 - MainWindow: 初始化核心组件...
2025-06-27 22:21:31.392 | INFO     | app.services.account_service:__init__:42 - 账户服务初始化
2025-06-27 22:21:31.392 | INFO     | core.proxy.proxy_task_manager:run:284 - 代理任务管理器线程启动
2025-06-27 22:21:31.392 | INFO     | core.telegram.client_worker:run:65 - Telegram客户端工作线程开始运行
2025-06-27 22:21:31.393 | INFO     | app.controllers.account_controller:__init__:81 - 账户控制器初始化
2025-06-27 22:21:31.393 | INFO     | app.services.monitor_service:__init__:39 - 初始化监控任务服务
2025-06-27 22:21:31.394 | INFO     | core.monitor.filter:__init__:26 - 初始化消息过滤器
2025-06-27 22:21:31.394 | INFO     | core.telegram.client_manager:__init__:66 - Telegram客户端管理器初始化
2025-06-27 22:21:31.395 | INFO     | core.monitor.extractor:__init__:24 - 初始化关键词提取器
2025-06-27 22:21:31.395 | INFO     | core.telegram.user_manager:__init__:30 - Telegram用户管理器初始化
2025-06-27 22:21:31.395 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-06-27 22:21:31.395 | INFO     | core.telegram.group_manager:__init__:36 - Telegram群组管理器初始化
2025-06-27 22:21:31.396 | INFO     | core.monitor.notifier:__init__:90 - 初始化通知管理器
2025-06-27 22:21:31.396 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-06-27 22:21:31.396 | INFO     | app.controllers.telegram_monitor_controller:__init__:64 - 初始化 Telegram 监控控制器
2025-06-27 22:21:31.396 | INFO     | core.telegram.monitor_manager:__init__:26 - Telegram监控管理器初始化
2025-06-27 22:21:31.397 | INFO     | core.telegram.telegram_service:__init__:39 - Telegram服务初始化完成
2025-06-27 22:21:31.397 | DEBUG    | app.controllers.telegram_monitor_controller:__init__:82 - 控制器初始化完成
2025-06-27 22:21:31.650 | INFO     | ui.views.monitor_view:__init__:48 - 初始化监控视图
2025-06-27 22:21:31.650 | DEBUG    | ui.views.monitor_view:_connect_signals:80 - 连接控制器信号
2025-06-27 22:21:31.866 | INFO     | ui.views.proxy_view:__init__:96 - 初始化视图，加载第一页代理数据
2025-06-27 22:21:32.165 | INFO     | __main__:on_auth_completed:97 - 主窗口已启动
2025-06-27 22:21:32.206 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:88 - 尝试连接监听日志信号...
2025-06-27 22:21:32.206 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:90 - 监听日志信号连接成功
2025-06-27 22:21:32.207 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:21:32.212 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:21:32.216 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:145 - 加载监控任务列表 (搜索关键词: '')
2025-06-27 22:21:32.216 | DEBUG    | ui.views.monitor_view:_on_loading_started:620 - 加载中: 正在加载任务列表...
2025-06-27 22:21:32.216 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:21:32.224 | INFO     | app.services.proxy_service:refresh_proxies:712 - 刷新代理列表: offset=0, limit=20
2025-06-27 22:21:32.225 | INFO     | core.proxy.proxy_task_manager:submit_task:172 - 提交耗时任务到子线程: refresh_proxies_0_20
2025-06-27 22:21:32.225 | DEBUG    | app.services.proxy_service:get_proxies:500 - 获取代理列表: offset=0, limit=20
2025-06-27 22:21:32.225 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:21:32.225 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:21:32.233 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:21:32.276 | INFO     | app.controllers.invite_controller:get_invite_tasks:38 - 获取邀请任务列表, status=None
2025-06-27 22:21:32.282 | INFO     | app.services.invite_service:get_invite_tasks:121 - 获取邀请任务列表, status=None
2025-06-27 22:21:32.282 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:21:32.287 | INFO     | app.services.invite_service:task_initialize:39 - 初始化邀请服务，重置运行中的任务为暂停状态
2025-06-27 22:21:32.287 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:21:32.296 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:21:32.298 | INFO     | core.proxy.proxy_task_manager:task_wrapper:199 - 任务 refresh_proxies_0_20 完成
2025-06-27 22:21:35.432 | INFO     | utils.vip_checker:start_checking:38 - 开始VIP状态检查
2025-06-27 22:21:35.433 | DEBUG    | utils.vip_checker:_check_vip_status:61 - 正在检查VIP状态...
2025-06-27 22:21:35.433 | INFO     | core.auth.api_service:verify_vip:425 - 会员验证接口调用
2025-06-27 22:21:35.537 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:21:35.540 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:21:35.555 | INFO     | app.services.invite_service:task_initialize:48 - 成功重置 0 个任务状态
2025-06-27 22:21:35.555 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:21:35.556 | DEBUG    | ui.views.monitor_view:_refresh_task_list:175 - 刷新任务列表，共0个任务
2025-06-27 22:21:35.557 | INFO     | ui.views.monitor_view:_refresh_task_list:189 - 没有找到监控任务
2025-06-27 22:21:35.557 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:21:35.563 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:21:35.571 | INFO     | app.services.account_service:get_all_groups:96 - 获取所有账户分组成功, 共 2 个
2025-06-27 22:21:35.571 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:21:35.572 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:21:35.577 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:21:35.578 | DEBUG    | ui.views.account_view:_on_groups_loaded:446 - 分组加载完成: 2个分组
2025-06-27 22:21:35.592 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:21:35.683 | INFO     | app.services.account_service:get_all_accounts:290 - 获取所有账户成功, 共 2 个
2025-06-27 22:21:35.683 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:21:35.685 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:21:35.686 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:21:35.688 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:21:35.689 | DEBUG    | ui.views.account_view:_on_accounts_loaded:579 - 账户加载完成: 2个账户
2025-06-27 22:21:35.714 | DEBUG    | ui.views.account_view:_update_group_counts:185 - 分组计数已更新: {2: 1, 1: 1}
2025-06-27 22:21:35.720 | INFO     | ui.views.account_view:_auto_login_accounts:634 - 开始调用控制器进行 2 个账户的自动登录。
2025-06-27 22:21:35.721 | INFO     | app.services.account_service:batch_auto_login:1165 - 服务层：开始批量自动登录 2 个账户, 最大并发: 5
2025-06-27 22:21:35.721 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:21:35.728 | INFO     | app.controllers.telegram_monitor_controller:_load_total_users_count_internal:191 - 成功加载总体统计数据: 总用户数 23, 今日采集 0, 日均采集 23, 运行天数 1
2025-06-27 22:21:35.728 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:156 - 成功加载 0 个任务
2025-06-27 22:21:35.729 | DEBUG    | ui.views.monitor_view:_on_loading_finished:625 - 加载完成
2025-06-27 22:21:35.738 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-06-27 22:21:35.769 | INFO     | app.services.account_service:get_all_accounts:290 - 获取所有账户成功, 共 2 个
2025-06-27 22:21:35.769 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:21:35.771 | INFO     | app.services.account_service:batch_auto_login:1204 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-06-27 22:21:35.771 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:21:35.778 | DEBUG    | ui.views.account_view:_update_group_counts:185 - 分组计数已更新: {2: 1, 1: 1}
2025-06-27 22:21:35.778 | DEBUG    | ui.views.account_view:_init_cache:152 - 账户缓存初始化完成，共 2 个账户
2025-06-27 22:21:35.785 | DEBUG    | ui.views.account_view:_on_accounts_loaded:579 - 账户加载完成: 2个账户
2025-06-27 22:21:35.808 | DEBUG    | ui.views.account_view:_update_group_counts:185 - 分组计数已更新: {2: 1, 1: 1}
2025-06-27 22:21:35.813 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:22:07.847 | INFO     | utils.logger:init_logger:98 - 日志系统初始化完成，默认级别: DEBUG，日志目录: H:\PyProject\TeleTest\APPDATA\logs
2025-06-27 22:22:13.411 | INFO     | data.database:init_database:31 - 初始化数据库连接: H:\PyProject\TeleTest\APPDATA\database\telegram_manager.db
2025-06-27 22:22:13.441 | DEBUG    | data.database:init_database:58 - 创建数据库表
2025-06-27 22:22:13.462 | INFO     | data.database:init_database:61 - 数据库初始化完成
2025-06-27 22:22:14.175 | DEBUG    | app.controllers.auth_controller:initialize_software:43 - 控制器: 初始化软件 app_id=1000, version=1.2.7
2025-06-27 22:22:14.176 | DEBUG    | core.auth.api_service:init_software:55 - 发送初始化请求: https://server.xile188.com/api/user/1000/windowns10/1.2.7/ini, params={}
2025-06-27 22:22:14.543 | DEBUG    | core.auth.api_service:init_software:66 - 初始化数据解密成功: {'new_content': '- 新增功能\n  - 1.新增图片上传功能\n  - 2.新增软件文档\n  - 3.新增软件自动更新，后续更新会自动检测版本更新，无需再下载更新包\n  - 4.新增设置账户信息及发信控制\n- 修复bug\n  - 删除监听任务时，不删除对应任务采集到的数据。\n  - 监听设置群无法发送到目标群\n  - 导出监听数据时应当导出单个任务而非所有数据\n- 优化记录\n  - 优化发信规则，直接调取符合要求的账户发送，无账户时才需要等待延时。\n  - 完善软件设置', 'new_url': None, 'notice': {'id': '2', 'content': '公告内容测试3', 'time': '**********'}}
2025-06-27 22:22:14.551 | INFO     | app.controllers.auth_controller:login:61 - 控制器: 用户登录 account=demo01
2025-06-27 22:22:17.560 | INFO     | core.auth.api_service:login:127 - 用户登录: account=demo01
2025-06-27 22:22:17.787 | INFO     | core.auth.api_service:get_user_info:347 - 获取用户信息
2025-06-27 22:22:18.040 | INFO     | core.auth.api_service:get_user_info:375 - 获取用户信息成功
2025-06-27 22:22:18.047 | INFO     | __main__:on_auth_completed:93 - 用户登录成功，正在启动主窗口...
2025-06-27 22:22:18.073 | INFO     | core.telegram.client_worker:__init__:61 - Telegram客户端工作线程初始化
2025-06-27 22:22:18.074 | INFO     | utils.vip_checker:initialize:26 - VIP检查器已初始化，等待登录后开始检查
2025-06-27 22:22:18.074 | INFO     | ui.main_window:_initialize_core_components:107 - MainWindow: 初始化核心组件...
2025-06-27 22:22:18.075 | INFO     | app.services.account_service:__init__:42 - 账户服务初始化
2025-06-27 22:22:18.075 | INFO     | app.controllers.account_controller:__init__:81 - 账户控制器初始化
2025-06-27 22:22:18.075 | INFO     | app.services.monitor_service:__init__:39 - 初始化监控任务服务
2025-06-27 22:22:18.076 | INFO     | core.telegram.client_worker:run:65 - Telegram客户端工作线程开始运行
2025-06-27 22:22:18.076 | INFO     | core.monitor.filter:__init__:26 - 初始化消息过滤器
2025-06-27 22:22:18.076 | INFO     | core.proxy.proxy_task_manager:run:284 - 代理任务管理器线程启动
2025-06-27 22:22:18.076 | INFO     | core.monitor.extractor:__init__:24 - 初始化关键词提取器
2025-06-27 22:22:18.077 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-06-27 22:22:18.078 | INFO     | core.monitor.notifier:__init__:90 - 初始化通知管理器
2025-06-27 22:22:18.078 | INFO     | core.telegram.client_manager:__init__:66 - Telegram客户端管理器初始化
2025-06-27 22:22:18.079 | INFO     | app.controllers.telegram_monitor_controller:__init__:64 - 初始化 Telegram 监控控制器
2025-06-27 22:22:18.079 | INFO     | core.telegram.user_manager:__init__:30 - Telegram用户管理器初始化
2025-06-27 22:22:18.079 | DEBUG    | app.controllers.telegram_monitor_controller:__init__:82 - 控制器初始化完成
2025-06-27 22:22:18.079 | INFO     | core.telegram.group_manager:__init__:36 - Telegram群组管理器初始化
2025-06-27 22:22:18.081 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-06-27 22:22:18.082 | INFO     | core.telegram.monitor_manager:__init__:26 - Telegram监控管理器初始化
2025-06-27 22:22:18.083 | INFO     | core.telegram.telegram_service:__init__:39 - Telegram服务初始化完成
2025-06-27 22:22:18.287 | INFO     | ui.views.monitor_view:__init__:48 - 初始化监控视图
2025-06-27 22:22:18.288 | DEBUG    | ui.views.monitor_view:_connect_signals:80 - 连接控制器信号
2025-06-27 22:22:18.474 | INFO     | ui.views.proxy_view:__init__:96 - 初始化视图，加载第一页代理数据
2025-06-27 22:22:18.762 | INFO     | __main__:on_auth_completed:97 - 主窗口已启动
2025-06-27 22:22:18.799 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:88 - 尝试连接监听日志信号...
2025-06-27 22:22:18.799 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:90 - 监听日志信号连接成功
2025-06-27 22:22:18.800 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:22:18.805 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:22:18.808 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:145 - 加载监控任务列表 (搜索关键词: '')
2025-06-27 22:22:18.808 | DEBUG    | ui.views.monitor_view:_on_loading_started:620 - 加载中: 正在加载任务列表...
2025-06-27 22:22:18.808 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:22:18.816 | INFO     | app.services.proxy_service:refresh_proxies:712 - 刷新代理列表: offset=0, limit=20
2025-06-27 22:22:18.816 | INFO     | core.proxy.proxy_task_manager:submit_task:172 - 提交耗时任务到子线程: refresh_proxies_0_20
2025-06-27 22:22:18.816 | DEBUG    | app.services.proxy_service:get_proxies:500 - 获取代理列表: offset=0, limit=20
2025-06-27 22:22:18.817 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:22:18.817 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:22:18.824 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:22:18.875 | INFO     | app.controllers.invite_controller:get_invite_tasks:38 - 获取邀请任务列表, status=None
2025-06-27 22:22:18.875 | INFO     | app.services.invite_service:get_invite_tasks:121 - 获取邀请任务列表, status=None
2025-06-27 22:22:18.875 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:22:18.877 | INFO     | app.services.invite_service:task_initialize:39 - 初始化邀请服务，重置运行中的任务为暂停状态
2025-06-27 22:22:18.878 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:22:18.884 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:22:18.886 | INFO     | core.proxy.proxy_task_manager:task_wrapper:199 - 任务 refresh_proxies_0_20 完成
2025-06-27 22:22:21.229 | INFO     | utils.vip_checker:start_checking:38 - 开始VIP状态检查
2025-06-27 22:22:21.229 | DEBUG    | utils.vip_checker:_check_vip_status:61 - 正在检查VIP状态...
2025-06-27 22:22:21.230 | INFO     | core.auth.api_service:verify_vip:425 - 会员验证接口调用
2025-06-27 22:22:21.330 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:22:21.334 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:22:21.348 | DEBUG    | ui.views.monitor_view:_refresh_task_list:175 - 刷新任务列表，共0个任务
2025-06-27 22:22:21.349 | INFO     | ui.views.monitor_view:_refresh_task_list:189 - 没有找到监控任务
2025-06-27 22:22:21.349 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:22:21.350 | INFO     | app.services.invite_service:task_initialize:48 - 成功重置 0 个任务状态
2025-06-27 22:22:21.351 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:22:21.354 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:22:21.362 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:22:21.366 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:22:21.368 | INFO     | app.services.account_service:get_all_groups:96 - 获取所有账户分组成功, 共 2 个
2025-06-27 22:22:21.368 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:22:21.421 | DEBUG    | ui.views.account_view:_on_groups_loaded:446 - 分组加载完成: 2个分组
2025-06-27 22:22:21.441 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:22:21.476 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:22:21.477 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:22:21.478 | INFO     | app.services.account_service:get_all_accounts:290 - 获取所有账户成功, 共 2 个
2025-06-27 22:22:21.478 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:22:21.484 | INFO     | app.controllers.telegram_monitor_controller:_load_total_users_count_internal:191 - 成功加载总体统计数据: 总用户数 23, 今日采集 0, 日均采集 23, 运行天数 1
2025-06-27 22:22:21.484 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:156 - 成功加载 0 个任务
2025-06-27 22:22:21.484 | DEBUG    | ui.views.monitor_view:_on_loading_finished:625 - 加载完成
2025-06-27 22:22:21.486 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-06-27 22:22:21.491 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:22:21.503 | DEBUG    | ui.views.account_view:_on_accounts_loaded:579 - 账户加载完成: 2个账户
2025-06-27 22:22:21.526 | DEBUG    | ui.views.account_view:_update_group_counts:185 - 分组计数已更新: {2: 1, 1: 1}
2025-06-27 22:22:21.530 | INFO     | ui.views.account_view:_auto_login_accounts:634 - 开始调用控制器进行 2 个账户的自动登录。
2025-06-27 22:22:21.531 | INFO     | app.services.account_service:batch_auto_login:1165 - 服务层：开始批量自动登录 2 个账户, 最大并发: 5
2025-06-27 22:22:21.531 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:22:21.548 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=True
2025-06-27 22:22:21.550 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=True
2025-06-27 22:22:21.552 | INFO     | ui.views.proxy_view:_load_initial_data:1044 - 初始数据加载完成，共 1 个代理
2025-06-27 22:22:21.556 | INFO     | app.services.account_service:get_all_accounts:290 - 获取所有账户成功, 共 2 个
2025-06-27 22:22:21.556 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:22:21.557 | INFO     | app.services.account_service:batch_auto_login:1204 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-06-27 22:22:21.557 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:22:21.559 | DEBUG    | ui.views.account_view:_update_group_counts:185 - 分组计数已更新: {2: 1, 1: 1}
2025-06-27 22:22:21.560 | DEBUG    | ui.views.account_view:_init_cache:152 - 账户缓存初始化完成，共 2 个账户
2025-06-27 22:22:21.564 | DEBUG    | ui.views.account_view:_on_accounts_loaded:579 - 账户加载完成: 2个账户
2025-06-27 22:22:21.584 | DEBUG    | ui.views.account_view:_update_group_counts:185 - 分组计数已更新: {2: 1, 1: 1}
2025-06-27 22:22:21.590 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:25:02.868 | INFO     | utils.logger:init_logger:98 - 日志系统初始化完成，默认级别: DEBUG，日志目录: H:\PyProject\TeleTest\APPDATA\logs
2025-06-27 22:25:07.530 | INFO     | data.database:init_database:31 - 初始化数据库连接: H:\PyProject\TeleTest\APPDATA\database\telegram_manager.db
2025-06-27 22:25:07.623 | DEBUG    | data.database:init_database:58 - 创建数据库表
2025-06-27 22:25:07.649 | INFO     | data.database:init_database:61 - 数据库初始化完成
2025-06-27 22:25:08.701 | DEBUG    | app.controllers.auth_controller:initialize_software:43 - 控制器: 初始化软件 app_id=1000, version=1.2.7
2025-06-27 22:25:08.701 | DEBUG    | core.auth.api_service:init_software:55 - 发送初始化请求: https://server.xile188.com/api/user/1000/windowns10/1.2.7/ini, params={}
2025-06-27 22:25:09.170 | DEBUG    | core.auth.api_service:init_software:66 - 初始化数据解密成功: {'new_content': '- 新增功能\n  - 1.新增图片上传功能\n  - 2.新增软件文档\n  - 3.新增软件自动更新，后续更新会自动检测版本更新，无需再下载更新包\n  - 4.新增设置账户信息及发信控制\n- 修复bug\n  - 删除监听任务时，不删除对应任务采集到的数据。\n  - 监听设置群无法发送到目标群\n  - 导出监听数据时应当导出单个任务而非所有数据\n- 优化记录\n  - 优化发信规则，直接调取符合要求的账户发送，无账户时才需要等待延时。\n  - 完善软件设置', 'new_url': None, 'notice': {'id': '2', 'content': '公告内容测试3', 'time': '**********'}}
2025-06-27 22:25:09.178 | INFO     | app.controllers.auth_controller:login:61 - 控制器: 用户登录 account=demo01
2025-06-27 22:25:12.241 | INFO     | core.auth.api_service:login:127 - 用户登录: account=demo01
2025-06-27 22:25:12.529 | INFO     | core.auth.api_service:get_user_info:347 - 获取用户信息
2025-06-27 22:25:12.815 | INFO     | core.auth.api_service:get_user_info:375 - 获取用户信息成功
2025-06-27 22:25:12.823 | INFO     | __main__:on_auth_completed:93 - 用户登录成功，正在启动主窗口...
2025-06-27 22:25:12.850 | INFO     | core.telegram.client_worker:__init__:61 - Telegram客户端工作线程初始化
2025-06-27 22:25:12.850 | INFO     | utils.vip_checker:initialize:26 - VIP检查器已初始化，等待登录后开始检查
2025-06-27 22:25:12.851 | INFO     | ui.main_window:_initialize_core_components:107 - MainWindow: 初始化核心组件...
2025-06-27 22:25:12.851 | INFO     | app.services.account_service:__init__:42 - 账户服务初始化
2025-06-27 22:25:12.852 | INFO     | core.proxy.proxy_task_manager:run:284 - 代理任务管理器线程启动
2025-06-27 22:25:12.852 | INFO     | core.telegram.client_worker:run:65 - Telegram客户端工作线程开始运行
2025-06-27 22:25:12.852 | INFO     | app.controllers.account_controller:__init__:81 - 账户控制器初始化
2025-06-27 22:25:12.853 | INFO     | app.services.monitor_service:__init__:39 - 初始化监控任务服务
2025-06-27 22:25:12.853 | INFO     | core.monitor.filter:__init__:26 - 初始化消息过滤器
2025-06-27 22:25:12.854 | INFO     | core.monitor.extractor:__init__:24 - 初始化关键词提取器
2025-06-27 22:25:12.854 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-06-27 22:25:12.854 | INFO     | core.monitor.notifier:__init__:90 - 初始化通知管理器
2025-06-27 22:25:12.854 | INFO     | core.telegram.client_manager:__init__:66 - Telegram客户端管理器初始化
2025-06-27 22:25:12.855 | INFO     | app.controllers.telegram_monitor_controller:__init__:64 - 初始化 Telegram 监控控制器
2025-06-27 22:25:12.855 | INFO     | core.telegram.user_manager:__init__:30 - Telegram用户管理器初始化
2025-06-27 22:25:12.855 | DEBUG    | app.controllers.telegram_monitor_controller:__init__:82 - 控制器初始化完成
2025-06-27 22:25:12.855 | INFO     | core.telegram.group_manager:__init__:36 - Telegram群组管理器初始化
2025-06-27 22:25:12.857 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-06-27 22:25:12.857 | INFO     | core.telegram.monitor_manager:__init__:26 - Telegram监控管理器初始化
2025-06-27 22:25:12.857 | INFO     | core.telegram.telegram_service:__init__:39 - Telegram服务初始化完成
2025-06-27 22:25:13.152 | INFO     | ui.views.monitor_view:__init__:48 - 初始化监控视图
2025-06-27 22:25:13.152 | DEBUG    | ui.views.monitor_view:_connect_signals:80 - 连接控制器信号
2025-06-27 22:25:13.366 | INFO     | ui.views.proxy_view:__init__:96 - 初始化视图，加载第一页代理数据
2025-06-27 22:25:13.664 | INFO     | __main__:on_auth_completed:97 - 主窗口已启动
2025-06-27 22:25:13.707 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:88 - 尝试连接监听日志信号...
2025-06-27 22:25:13.708 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:90 - 监听日志信号连接成功
2025-06-27 22:25:13.708 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:25:13.714 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:25:13.718 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:145 - 加载监控任务列表 (搜索关键词: '')
2025-06-27 22:25:13.719 | DEBUG    | ui.views.monitor_view:_on_loading_started:620 - 加载中: 正在加载任务列表...
2025-06-27 22:25:13.719 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:25:13.727 | INFO     | app.services.proxy_service:refresh_proxies:712 - 刷新代理列表: offset=0, limit=20
2025-06-27 22:25:13.727 | INFO     | core.proxy.proxy_task_manager:submit_task:172 - 提交耗时任务到子线程: refresh_proxies_0_20
2025-06-27 22:25:13.728 | DEBUG    | app.services.proxy_service:get_proxies:500 - 获取代理列表: offset=0, limit=20
2025-06-27 22:25:13.728 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:25:13.728 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:25:13.736 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:25:13.793 | INFO     | app.controllers.invite_controller:get_invite_tasks:38 - 获取邀请任务列表, status=None
2025-06-27 22:25:13.793 | INFO     | app.services.invite_service:get_invite_tasks:121 - 获取邀请任务列表, status=None
2025-06-27 22:25:13.794 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:25:13.795 | INFO     | app.services.invite_service:task_initialize:39 - 初始化邀请服务，重置运行中的任务为暂停状态
2025-06-27 22:25:13.796 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:25:13.803 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:25:13.805 | INFO     | core.proxy.proxy_task_manager:task_wrapper:199 - 任务 refresh_proxies_0_20 完成
2025-06-27 22:25:23.801 | INFO     | utils.vip_checker:start_checking:38 - 开始VIP状态检查
2025-06-27 22:25:23.801 | DEBUG    | utils.vip_checker:_check_vip_status:61 - 正在检查VIP状态...
2025-06-27 22:25:23.802 | INFO     | core.auth.api_service:verify_vip:425 - 会员验证接口调用
2025-06-27 22:25:23.914 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:25:23.919 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:25:23.931 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:25:23.934 | DEBUG    | ui.views.monitor_view:_refresh_task_list:175 - 刷新任务列表，共0个任务
2025-06-27 22:25:23.934 | INFO     | ui.views.monitor_view:_refresh_task_list:189 - 没有找到监控任务
2025-06-27 22:25:23.935 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:25:23.937 | INFO     | app.services.invite_service:task_initialize:48 - 成功重置 0 个任务状态
2025-06-27 22:25:23.937 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:25:23.945 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:25:23.954 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:25:23.954 | INFO     | app.services.account_service:get_all_groups:96 - 获取所有账户分组成功, 共 2 个
2025-06-27 22:25:23.954 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:25:24.020 | DEBUG    | ui.views.account_view:_on_groups_loaded:446 - 分组加载完成: 2个分组
2025-06-27 22:25:24.035 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:25:24.049 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:25:24.054 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:25:24.057 | INFO     | app.controllers.telegram_monitor_controller:_load_total_users_count_internal:191 - 成功加载总体统计数据: 总用户数 23, 今日采集 0, 日均采集 23, 运行天数 1
2025-06-27 22:25:24.057 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:156 - 成功加载 0 个任务
2025-06-27 22:25:24.058 | DEBUG    | ui.views.monitor_view:_on_loading_finished:625 - 加载完成
2025-06-27 22:25:24.059 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-06-27 22:25:24.075 | INFO     | app.services.account_service:get_all_accounts:290 - 获取所有账户成功, 共 2 个
2025-06-27 22:25:24.075 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:25:24.079 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:25:24.081 | DEBUG    | ui.views.account_view:_on_accounts_loaded:579 - 账户加载完成: 2个账户
2025-06-27 22:25:24.102 | DEBUG    | ui.views.account_view:_update_group_counts:185 - 分组计数已更新: {2: 1, 1: 1}
2025-06-27 22:25:24.107 | INFO     | ui.views.account_view:_auto_login_accounts:634 - 开始调用控制器进行 2 个账户的自动登录。
2025-06-27 22:25:24.107 | INFO     | app.services.account_service:batch_auto_login:1165 - 服务层：开始批量自动登录 2 个账户, 最大并发: 5
2025-06-27 22:25:24.107 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:25:24.122 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=True
2025-06-27 22:25:24.124 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=True
2025-06-27 22:25:24.125 | INFO     | ui.views.proxy_view:_load_initial_data:1044 - 初始数据加载完成，共 1 个代理
2025-06-27 22:25:24.131 | INFO     | app.services.account_service:batch_auto_login:1204 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-06-27 22:25:24.131 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:25:24.134 | INFO     | app.services.account_service:get_all_accounts:290 - 获取所有账户成功, 共 2 个
2025-06-27 22:25:24.134 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:25:24.135 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:25:24.139 | DEBUG    | ui.views.account_view:_update_group_counts:185 - 分组计数已更新: {2: 1, 1: 1}
2025-06-27 22:25:24.140 | DEBUG    | ui.views.account_view:_init_cache:152 - 账户缓存初始化完成，共 2 个账户
2025-06-27 22:25:24.144 | DEBUG    | ui.views.account_view:_on_accounts_loaded:579 - 账户加载完成: 2个账户
2025-06-27 22:25:24.164 | DEBUG    | ui.views.account_view:_update_group_counts:185 - 分组计数已更新: {2: 1, 1: 1}
2025-06-27 22:25:24.173 | INFO     | core.auth.api_service:verify_vip:438 - 会员验证成功
2025-06-27 22:25:24.173 | ERROR    | utils.vip_checker:_check_vip_status:85 - VIP状态检查出现异常: 'message'
Traceback (most recent call last):

  File "D:\Program Files\JetBrains\PyCharm 2025.1.1\plugins\python-ce\helpers\pydev\pydevd.py", line 2270, in <module>
    main()
    └ <function main at 0x00000162B25EC680>

  File "D:\Program Files\JetBrains\PyCharm 2025.1.1\plugins\python-ce\helpers\pydev\pydevd.py", line 2252, in main
    globals = debugger.run(setup['file'], None, None, is_module)
              │        │   │                          └ False
              │        │   └ {'port': 57741, 'vm_type': None, 'client': '127.0.0.1', 'server': False, 'DEBUG_RECORD_SOCKET_READS': False, 'multiproc': Fal...
              │        └ <function PyDB.run at 0x00000162B25EB420>
              └ <__main__.PyDB object at 0x000001629F1FA890>

  File "D:\Program Files\JetBrains\PyCharm 2025.1.1\plugins\python-ce\helpers\pydev\pydevd.py", line 1563, in run
    return self._exec(is_module, entry_point_fn, module_name, file, globals, locals)
           │    │     │          │               │            │     │        └ {'__name__': '__main__', '__doc__': '\nTelegram多账户管理系统 - 主程序入口\n', '__package__': '', '__loader__': <_frozen_importlib_extern...
           │    │     │          │               │            │     └ {'__name__': '__main__', '__doc__': '\nTelegram多账户管理系统 - 主程序入口\n', '__package__': '', '__loader__': <_frozen_importlib_extern...
           │    │     │          │               │            └ 'H:\\PyProject\\TeleTest\\main.py'
           │    │     │          │               └ None
           │    │     │          └ ''
           │    │     └ False
           │    └ <function PyDB._exec at 0x00000162B25EB4C0>
           └ <__main__.PyDB object at 0x000001629F1FA890>

  File "D:\Program Files\JetBrains\PyCharm 2025.1.1\plugins\python-ce\helpers\pydev\pydevd.py", line 1570, in _exec
    pydev_imports.execfile(file, globals, locals)  # execute the script
    │             │        │     │        └ {'__name__': '__main__', '__doc__': '\nTelegram多账户管理系统 - 主程序入口\n', '__package__': '', '__loader__': <_frozen_importlib_extern...
    │             │        │     └ {'__name__': '__main__', '__doc__': '\nTelegram多账户管理系统 - 主程序入口\n', '__package__': '', '__loader__': <_frozen_importlib_extern...
    │             │        └ 'H:\\PyProject\\TeleTest\\main.py'
    │             └ <function execfile at 0x00000162B2209F80>
    └ <module '_pydev_bundle.pydev_imports' from 'D:\\Program Files\\JetBrains\\PyCharm 2025.1.1\\plugins\\python-ce\\helpers\\pyde...

  File "D:\Program Files\JetBrains\PyCharm 2025.1.1\plugins\python-ce\helpers\pydev\_pydev_imps\_pydev_execfile.py", line 18, in execfile
    exec(compile(contents+"\n", file, 'exec'), glob, loc)
                 │              │              │     └ {'__name__': '__main__', '__doc__': '\nTelegram多账户管理系统 - 主程序入口\n', '__package__': '', '__loader__': <_frozen_importlib_extern...
                 │              │              └ {'__name__': '__main__', '__doc__': '\nTelegram多账户管理系统 - 主程序入口\n', '__package__': '', '__loader__': <_frozen_importlib_extern...
                 │              └ 'H:\\PyProject\\TeleTest\\main.py'
                 └ '#!/usr/bin/env python\n# -*- coding: utf-8 -*-\n\n"""\nTelegram多账户管理系统 - 主程序入口\n"""\nimport sys\nimport os\nimport asyncio\n...

  File "H:\PyProject\TeleTest\main.py", line 118, in <module>
    main()
    └ <function main at 0x00000162C6508680>

  File "H:\PyProject\TeleTest\main.py", line 109, in main
    event_loop.run_forever()
    │          └ <function _QEventLoop.run_forever at 0x00000162B29C4900>
    └ <QIOCPEventLoop running=True closed=False debug=False>

  File "H:\PyProject\TeleTest\script\Lib\site-packages\qasync\__init__.py", line 377, in run_forever
    rslt = self.__app.exec()
           └ <QIOCPEventLoop running=True closed=False debug=False>
  File "H:\PyProject\TeleTest\script\Lib\site-packages\qasync\__init__.py", line 279, in timerEvent
    handle._run()
    │      └ <function Handle._run at 0x00000162B24ABC40>
    └ <Handle Task.task_wakeup(<Task finishe...> result=None>)>
  File "D:\Program Files\python11\Lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle Task.task_wakeup(<Task finishe...> result=None>)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle Task.task_wakeup(<Task finishe...> result=None>)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle Task.task_wakeup(<Task finishe...> result=None>)>

  File "H:\PyProject\TeleTest\ui\main_window.py", line 293, in on_login_succeeded
    await vip_checker.start_checking()
          │           └ <function VipChecker.start_checking at 0x00000162B2FE5B20>
          └ <utils.vip_checker.VipChecker(0x162c4cf66d0) at 0x00000162B27B3580>

  File "H:\PyProject\TeleTest\utils\vip_checker.py", line 42, in start_checking
    await self._check_vip_status()
          │    └ <function VipChecker._check_vip_status at 0x00000162B2FE5D00>
          └ <utils.vip_checker.VipChecker(0x162c4cf66d0) at 0x00000162B27B3580>

> File "H:\PyProject\TeleTest\utils\vip_checker.py", line 63, in _check_vip_status
    result = await self._controller.verify_vip()
                   │    │           └ <function AuthController.verify_vip at 0x00000162B2F653A0>
                   │    └ <app.controllers.auth_controller.AuthController(0x162c4e60640) at 0x00000162C65ABC00>
                   └ <utils.vip_checker.VipChecker(0x162c4cf66d0) at 0x00000162B27B3580>

  File "H:\PyProject\TeleTest\app\controllers\auth_controller.py", line 190, in verify_vip
    return await self._auth_service.verify_vip()
                 │    │             └ <function AuthService.verify_vip at 0x00000162B2F64C20>
                 │    └ <app.services.auth_service.AuthService(0x162c4e60900) at 0x00000162C64FA480>
                 └ <app.controllers.auth_controller.AuthController(0x162c4e60640) at 0x00000162C65ABC00>

  File "H:\PyProject\TeleTest\app\services\auth_service.py", line 123, in verify_vip
    return await self._service.verify_vip()
                 │    │        └ <function ApiService.verify_vip at 0x00000162B2F64040>
                 │    └ <core.auth.api_service.ApiService object at 0x00000162B2F60C50>
                 └ <app.services.auth_service.AuthService(0x162c4e60900) at 0x00000162C64FA480>

  File "H:\PyProject\TeleTest\core\auth\api_service.py", line 439, in verify_vip
    return {"success": True, "data": result['time'],'message':result['message']}
                                     │                        └ {'code': 200, 'msg': '验证成功', 'time': 1751034323, 'sign': '89e916c3a27f51a8cfbe9e1920a3539d', 'run': {'ms': 3.95, 'ram': '125....
                                     └ {'code': 200, 'msg': '验证成功', 'time': 1751034323, 'sign': '89e916c3a27f51a8cfbe9e1920a3539d', 'run': {'ms': 3.95, 'ram': '125....

KeyError: 'message'
2025-06-27 22:26:12.809 | ERROR    | data.repositories.proxy_repo:find_by_id:134 - 根据ID查询代理出错: coroutine ignored GeneratorExit
Traceback (most recent call last):

  File "H:\PyProject\TeleTest\utils\vip_checker.py", line 63, in _check_vip_status
    result = await self._controller.verify_vip()
                   │    │           └ <function AuthController.verify_vip at 0x00000162B2F653A0>
                   │    └ <app.controllers.auth_controller.AuthController(0x162c4e60640) at 0x00000162C65ABC00>
                   └ <utils.vip_checker.VipChecker(0x162c4cf66d0) at 0x00000162B27B3580>

  File "H:\PyProject\TeleTest\app\controllers\auth_controller.py", line 190, in verify_vip
    return await self._auth_service.verify_vip()
                 │    │             └ <function AuthService.verify_vip at 0x00000162B2F64C20>
                 │    └ <app.services.auth_service.AuthService(0x162c4e60900) at 0x00000162C64FA480>
                 └ <app.controllers.auth_controller.AuthController(0x162c4e60640) at 0x00000162C65ABC00>

  File "H:\PyProject\TeleTest\app\services\auth_service.py", line 123, in verify_vip
    return await self._service.verify_vip()
                 │    │        └ <function ApiService.verify_vip at 0x00000162B2F64040>
                 │    └ <core.auth.api_service.ApiService object at 0x00000162B2F60C50>
                 └ <app.services.auth_service.AuthService(0x162c4e60900) at 0x00000162C64FA480>

  File "H:\PyProject\TeleTest\core\auth\api_service.py", line 439, in verify_vip
    return {"success": True, "data": result['time'],'message':result['message']}
                                     │                        └ {'code': 200, 'msg': '验证成功', 'time': 1751034323, 'sign': '89e916c3a27f51a8cfbe9e1920a3539d', 'run': {'ms': 3.95, 'ram': '125....
                                     └ {'code': 200, 'msg': '验证成功', 'time': 1751034323, 'sign': '89e916c3a27f51a8cfbe9e1920a3539d', 'run': {'ms': 3.95, 'ram': '125....

KeyError: 'message'


During handling of the above exception, another exception occurred:


Traceback (most recent call last):

  File "D:\Program Files\python11\Lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle <TaskStepMethWrapper object at 0x00000162C6592110>()>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle <TaskStepMethWrapper object at 0x00000162C6592110>()>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle <TaskStepMethWrapper object at 0x00000162C6592110>()>

RuntimeError: Cannot enter into task <Task pending name='Task-623' coro=<AuthService._on_heartbeat_timer() running at H:\PyProject\TeleTest\app\services\auth_service.py:85> cb=[asyncSlot.<locals>._error_handler() at H:\PyProject\TeleTest\script\Lib\site-packages\qasync\__init__.py:778]> while another task <Task pending name='Task-27' coro=<MainWindow.on_login_succeeded() running at H:\PyProject\TeleTest\ui\main_window.py:293> cb=[asyncSlot.<locals>._error_handler() at H:\PyProject\TeleTest\script\Lib\site-packages\qasync\__init__.py:778]> is being executed.


During handling of the above exception, another exception occurred:


Traceback (most recent call last):

  File "D:\Program Files\JetBrains\PyCharm 2025.1.1\plugins\python-ce\helpers\pydev\pydevd.py", line 2270, in <module>
    main()
    └ <function main at 0x00000162B25EC680>

  File "D:\Program Files\JetBrains\PyCharm 2025.1.1\plugins\python-ce\helpers\pydev\pydevd.py", line 2252, in main
    globals = debugger.run(setup['file'], None, None, is_module)
              │        │   │                          └ False
              │        │   └ {'port': 57741, 'vm_type': None, 'client': '127.0.0.1', 'server': False, 'DEBUG_RECORD_SOCKET_READS': False, 'multiproc': Fal...
              │        └ <function PyDB.run at 0x00000162B25EB420>
              └ <__main__.PyDB object at 0x000001629F1FA890>

  File "D:\Program Files\JetBrains\PyCharm 2025.1.1\plugins\python-ce\helpers\pydev\pydevd.py", line 1563, in run
    return self._exec(is_module, entry_point_fn, module_name, file, globals, locals)
           │    │     │          │               │            │     │        └ {'__name__': '__main__', '__doc__': '\nTelegram多账户管理系统 - 主程序入口\n', '__package__': '', '__loader__': <_frozen_importlib_extern...
           │    │     │          │               │            │     └ {'__name__': '__main__', '__doc__': '\nTelegram多账户管理系统 - 主程序入口\n', '__package__': '', '__loader__': <_frozen_importlib_extern...
           │    │     │          │               │            └ 'H:\\PyProject\\TeleTest\\main.py'
           │    │     │          │               └ None
           │    │     │          └ ''
           │    │     └ False
           │    └ <function PyDB._exec at 0x00000162B25EB4C0>
           └ <__main__.PyDB object at 0x000001629F1FA890>

  File "D:\Program Files\JetBrains\PyCharm 2025.1.1\plugins\python-ce\helpers\pydev\pydevd.py", line 1570, in _exec
    pydev_imports.execfile(file, globals, locals)  # execute the script
    │             │        │     │        └ {'__name__': '__main__', '__doc__': '\nTelegram多账户管理系统 - 主程序入口\n', '__package__': '', '__loader__': <_frozen_importlib_extern...
    │             │        │     └ {'__name__': '__main__', '__doc__': '\nTelegram多账户管理系统 - 主程序入口\n', '__package__': '', '__loader__': <_frozen_importlib_extern...
    │             │        └ 'H:\\PyProject\\TeleTest\\main.py'
    │             └ <function execfile at 0x00000162B2209F80>
    └ <module '_pydev_bundle.pydev_imports' from 'D:\\Program Files\\JetBrains\\PyCharm 2025.1.1\\plugins\\python-ce\\helpers\\pyde...

  File "D:\Program Files\JetBrains\PyCharm 2025.1.1\plugins\python-ce\helpers\pydev\_pydev_imps\_pydev_execfile.py", line 18, in execfile
    exec(compile(contents+"\n", file, 'exec'), glob, loc)
                 │              │              │     └ {'__name__': '__main__', '__doc__': '\nTelegram多账户管理系统 - 主程序入口\n', '__package__': '', '__loader__': <_frozen_importlib_extern...
                 │              │              └ {'__name__': '__main__', '__doc__': '\nTelegram多账户管理系统 - 主程序入口\n', '__package__': '', '__loader__': <_frozen_importlib_extern...
                 │              └ 'H:\\PyProject\\TeleTest\\main.py'
                 └ '#!/usr/bin/env python\n# -*- coding: utf-8 -*-\n\n"""\nTelegram多账户管理系统 - 主程序入口\n"""\nimport sys\nimport os\nimport asyncio\n...

  File "H:\PyProject\TeleTest\main.py", line 118, in <module>
    main()
    └ <function main at 0x00000162C6508680>

  File "H:\PyProject\TeleTest\main.py", line 109, in main
    event_loop.run_forever()
    │          └ <function _QEventLoop.run_forever at 0x00000162B29C4900>
    └ <QIOCPEventLoop running=True closed=False debug=False>

  File "H:\PyProject\TeleTest\script\Lib\site-packages\qasync\__init__.py", line 377, in run_forever
    rslt = self.__app.exec()
           └ <QIOCPEventLoop running=True closed=False debug=False>
  File "H:\PyProject\TeleTest\script\Lib\site-packages\qasync\__init__.py", line 279, in timerEvent
    handle._run()
    │      └ <function Handle._run at 0x00000162B24ABC40>
    └ <Handle Task.task_wakeup(<Task finishe...> result=None>)>
  File "D:\Program Files\python11\Lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle Task.task_wakeup(<Task finishe...> result=None>)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle Task.task_wakeup(<Task finishe...> result=None>)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle Task.task_wakeup(<Task finishe...> result=None>)>

  File "H:\PyProject\TeleTest\ui\main_window.py", line 293, in on_login_succeeded
    await vip_checker.start_checking()
          │           └ <function VipChecker.start_checking at 0x00000162B2FE5B20>
          └ <utils.vip_checker.VipChecker(0x162c4cf66d0) at 0x00000162B27B3580>

  File "H:\PyProject\TeleTest\utils\vip_checker.py", line 42, in start_checking
    await self._check_vip_status()
          │    └ <function VipChecker._check_vip_status at 0x00000162B2FE5D00>
          └ <utils.vip_checker.VipChecker(0x162c4cf66d0) at 0x00000162B27B3580>

  File "H:\PyProject\TeleTest\utils\vip_checker.py", line 88, in _check_vip_status
    self.vip_expired.emit(self._expire_message)
    │    │           │    │    └ "VIP验证出错: 'message'"
    │    │           │    └ <utils.vip_checker.VipChecker(0x162c4cf66d0) at 0x00000162B27B3580>
    │    │           └ <method 'emit' of 'PySide6.QtCore.SignalInstance' objects>
    │    └ <PySide6.QtCore.SignalInstance vip_expired(QString) at 0x00000162C5F8FF30>
    └ <utils.vip_checker.VipChecker(0x162c4cf66d0) at 0x00000162B27B3580>

  File "H:\PyProject\TeleTest\ui\main_window.py", line 93, in on_vip_expired
    ).exec()

  File "H:\PyProject\TeleTest\script\Lib\site-packages\qasync\__init__.py", line 279, in timerEvent
    handle._run()
    │      └ <function Handle._run at 0x00000162B24ABC40>
    └ <Handle <TaskStepMethWrapper object at 0x00000162C6592110>()>
  File "D:\Program Files\python11\Lib\asyncio\events.py", line 94, in _run
    self._loop.call_exception_handler(context)
    │    │                            └ {'message': 'Exception in callback <TaskStepMethWrapper object at 0x00000162C6592110>()', 'exception': RuntimeError("Cannot e...
    │    └ <member '_loop' of 'Handle' objects>
    └ <Handle <TaskStepMethWrapper object at 0x00000162C6592110>()>
  File "H:\PyProject\TeleTest\script\Lib\site-packages\qasync\__init__.py", line 678, in call_exception_handler
    self.default_exception_handler(context)
    │    │                         └ {'message': 'Exception in callback <TaskStepMethWrapper object at 0x00000162C6592110>()', 'exception': RuntimeError("Cannot e...
    │    └ <function _QEventLoop.default_exception_handler at 0x00000162B29C5580>
    └ <QIOCPEventLoop running=True closed=False debug=False>
  File "H:\PyProject\TeleTest\script\Lib\site-packages\qasync\__init__.py", line 673, in default_exception_handler
    self.__log_error("\n".join(log_lines), exc_info=exc_info)
    │                          │                    └ (<class 'RuntimeError'>, RuntimeError("Cannot enter into task <Task pending name='Task-623' coro=<AuthService._on_heartbeat_t...
    │                          └ ['Exception in callback <TaskStepMethWrapper object at 0x00000162C6592110>()', 'handle: <Handle <TaskStepMethWrapper object a...
    └ <QIOCPEventLoop running=True closed=False debug=False>
  File "H:\PyProject\TeleTest\script\Lib\site-packages\qasync\__init__.py", line 736, in __log_error
    cls._logger.error(*args, **kwds)
    │   │       │      │       └ {'exc_info': (<class 'RuntimeError'>, RuntimeError("Cannot enter into task <Task pending name='Task-623' coro=<AuthService._o...
    │   │       │      └ ('Exception in callback <TaskStepMethWrapper object at 0x00000162C6592110>()\nhandle: <Handle <TaskStepMethWrapper object at ...
    │   │       └ <function Logger.error at 0x00000162B2389080>
    │   └ <Logger qasync._QEventLoop (WARNING)>
    └ <class 'qasync.QIOCPEventLoop'>
  File "D:\Program Files\python11\Lib\logging\__init__.py", line 1518, in error
    self._log(ERROR, msg, args, **kwargs)
    │    │    │      │    │       └ {'exc_info': (<class 'RuntimeError'>, RuntimeError("Cannot enter into task <Task pending name='Task-623' coro=<AuthService._o...
    │    │    │      │    └ ()
    │    │    │      └ 'Exception in callback <TaskStepMethWrapper object at 0x00000162C6592110>()\nhandle: <Handle <TaskStepMethWrapper object at 0...
    │    │    └ 40
    │    └ <function Logger._log at 0x00000162B23894E0>
    └ <Logger qasync._QEventLoop (WARNING)>
  File "D:\Program Files\python11\Lib\logging\__init__.py", line 1634, in _log
    self.handle(record)
    │    │      └ <LogRecord: qasync._QEventLoop, 40, H:\PyProject\TeleTest\script\Lib\site-packages\qasync\__init__.py, 736, "Exception in cal...
    │    └ <function Logger.handle at 0x00000162B2389580>
    └ <Logger qasync._QEventLoop (WARNING)>
  File "D:\Program Files\python11\Lib\logging\__init__.py", line 1644, in handle
    self.callHandlers(record)
    │    │            └ <LogRecord: qasync._QEventLoop, 40, H:\PyProject\TeleTest\script\Lib\site-packages\qasync\__init__.py, 736, "Exception in cal...
    │    └ <function Logger.callHandlers at 0x00000162B2389800>
    └ <Logger qasync._QEventLoop (WARNING)>
  File "D:\Program Files\python11\Lib\logging\__init__.py", line 1714, in callHandlers
    lastResort.handle(record)
    │          │      └ <LogRecord: qasync._QEventLoop, 40, H:\PyProject\TeleTest\script\Lib\site-packages\qasync\__init__.py, 736, "Exception in cal...
    │          └ <function Handler.handle at 0x00000162B23476A0>
    └ <_StderrHandler <stderr> (WARNING)>
  File "D:\Program Files\python11\Lib\logging\__init__.py", line 978, in handle
    self.emit(record)
    │    │    └ <LogRecord: qasync._QEventLoop, 40, H:\PyProject\TeleTest\script\Lib\site-packages\qasync\__init__.py, 736, "Exception in cal...
    │    └ <function StreamHandler.emit at 0x00000162B2347C40>
    └ <_StderrHandler <stderr> (WARNING)>
  File "D:\Program Files\python11\Lib\logging\__init__.py", line 1110, in emit
    msg = self.format(record)
          │    │      └ <LogRecord: qasync._QEventLoop, 40, H:\PyProject\TeleTest\script\Lib\site-packages\qasync\__init__.py, 736, "Exception in cal...
          │    └ <function Handler.format at 0x00000162B2347560>
          └ <_StderrHandler <stderr> (WARNING)>
  File "D:\Program Files\python11\Lib\logging\__init__.py", line 953, in format
    return fmt.format(record)
           │   │      └ <LogRecord: qasync._QEventLoop, 40, H:\PyProject\TeleTest\script\Lib\site-packages\qasync\__init__.py, 736, "Exception in cal...
           │   └ <function Formatter.format at 0x00000162B23467A0>
           └ <logging.Formatter object at 0x00000162B23732D0>
  File "D:\Program Files\python11\Lib\logging\__init__.py", line 695, in format
    record.exc_text = self.formatException(record.exc_info)
    │      │          │    │               │      └ (<class 'RuntimeError'>, RuntimeError("Cannot enter into task <Task pending name='Task-623' coro=<AuthService._on_heartbeat_t...
    │      │          │    │               └ <LogRecord: qasync._QEventLoop, 40, H:\PyProject\TeleTest\script\Lib\site-packages\qasync\__init__.py, 736, "Exception in cal...
    │      │          │    └ <function Formatter.formatException at 0x00000162B2346520>
    │      │          └ <logging.Formatter object at 0x00000162B23732D0>
    │      └ None
    └ <LogRecord: qasync._QEventLoop, 40, H:\PyProject\TeleTest\script\Lib\site-packages\qasync\__init__.py, 736, "Exception in cal...
  File "D:\Program Files\python11\Lib\logging\__init__.py", line 645, in formatException
    traceback.print_exception(ei[0], ei[1], tb, None, sio)
    │         │               │      │      │         └ <_io.StringIO object at 0x00000162849369E0>
    │         │               │      │      └ <traceback object at 0x0000016282CD8B80>
    │         │               │      └ (<class 'RuntimeError'>, RuntimeError("Cannot enter into task <Task pending name='Task-623' coro=<AuthService._on_heartbeat_t...
    │         │               └ (<class 'RuntimeError'>, RuntimeError("Cannot enter into task <Task pending name='Task-623' coro=<AuthService._on_heartbeat_t...
    │         └ <function print_exception at 0x000001629F4E82C0>
    └ <module 'traceback' from 'D:\\Program Files\\python11\\Lib\\traceback.py'>
  File "D:\Program Files\python11\Lib\traceback.py", line 125, in print_exception
    te.print(file=file, chain=chain)
    │  │          │           └ True
    │  │          └ <_io.StringIO object at 0x00000162849369E0>
    │  └ <function TracebackException.print at 0x000001629F4EA200>
    └ <traceback.TracebackException object at 0x0000016281B35FD0>
  File "D:\Program Files\python11\Lib\traceback.py", line 979, in print
    for line in self.format(chain=chain):
        │       │    │            └ True
        │       │    └ <function TracebackException.format at 0x000001629F4EA160>
        │       └ <traceback.TracebackException object at 0x0000016281B35FD0>
        └ '  File "H:\\PyProject\\TeleTest\\utils\\vip_checker.py", line 63, in _check_vip_status\n    result = await self._controller....
  File "D:\Program Files\python11\Lib\traceback.py", line 916, in format
    yield from _ctx.emit(exc.stack.format())
               │    │    │   │     └ <function StackSummary.format at 0x000001629F4E94E0>
               │    │    │   └ [<FrameSummary file H:\PyProject\TeleTest\utils\vip_checker.py, line 63 in _check_vip_status>, <FrameSummary file H:\PyProjec...
               │    │    └ <traceback.TracebackException object at 0x0000016285918A50>
               │    └ <function _ExceptionPrintContext.emit at 0x000001629F4E9C60>
               └ <traceback._ExceptionPrintContext object at 0x0000016285918B50>

  File "D:\Program Files\JetBrains\PyCharm 2025.1.1\plugins\python-ce\helpers\pydev\_pydevd_bundle\pydevd_collect_try_except_info.py", line 163, in collect_return_info
    for instruction in _iter_instructions(co):
                       │                  └ <code object format at 0x000001629F406740, file "D:\Program Files\python11\Lib\traceback.py", line 874>
                       └ <function _iter_instructions at 0x00000162B2209760>

  File "D:\Program Files\JetBrains\PyCharm 2025.1.1\plugins\python-ce\helpers\pydev\_pydevd_bundle\pydevd_collect_try_except_info.py", line 137, in _iter_instructions
    iter_in = list(iter_in)
                   └ Bytecode(<code object format at 0x000001629F406740, file "D:\Program Files\python11\Lib\traceback.py", line 874>)

  File "D:\Program Files\python11\Lib\dis.py", line 451, in _get_instructions_bytes
    positions = Positions(*next(co_positions, ()))
                │               └ <positions_iterator object at 0x00000162847DA650>
                └ <class 'dis.Positions'>
  File "<string>", line 1, in <lambda>

> File "H:\PyProject\TeleTest\data\repositories\proxy_repo.py", line 129, in find_by_id
    result = await self.session.execute(
                   │    │       └ <function AsyncSession.execute at 0x00000162B383B420>
                   │    └ <sqlalchemy.ext.asyncio.session.AsyncSession object at 0x00000162848D4A50>
                   └ <data.repositories.proxy_repo.ProxyRepository object at 0x0000016284850C50>

  File "H:\PyProject\TeleTest\script\Lib\site-packages\sqlalchemy\ext\asyncio\session.py", line 463, in execute
    result = await greenlet_spawn(
                   └ <function greenlet_spawn at 0x00000162B2BA4FE0>

RuntimeError: coroutine ignored GeneratorExit
2025-06-27 22:26:12.832 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:26:12.833 | ERROR    | app.services.account_service:batch_auto_login:1228 - 服务层：账户 +*********** 查询代理ID 1 时出错: (sqlite3.OperationalError) no active connection
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-27 22:26:12.833 | INFO     | ui.views.account_view:_on_notify:665 - 收到通知: 自动登录错误_+***********
2025-06-27 22:26:12.841 | INFO     | app.services.account_service:batch_auto_login:1274 - 服务层：准备调用核心层处理 2 个账户的自动登录。
2025-06-27 22:26:12.841 | INFO     | app.services.account_service:batch_auto_login:1284 - 服务层：设置核心层任务超时为 120 秒。
2025-06-27 22:26:12.842 | ERROR    | app.controllers.account_controller:auto_login_accounts:590 - 批量自动登录异常: coroutine ignored GeneratorExit
2025-06-27 22:26:12.842 | INFO     | core.telegram.client_manager:batch_auto_login:1216 - 开始批量自动登录, 共 2 个账户, 最大并发 5
2025-06-27 22:26:12.844 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:259 - 创建客户端: H:\PyProject\TeleTest\APPDATA\sessions\+***********, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-06-27 22:26:12.844 | DEBUG    | core.telegram.client_manager:get_proxy:96 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-06-27 22:26:12.844 | DEBUG    | core.telegram.client_manager:get_proxy:143 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-06-27 22:26:12.847 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:259 - 创建客户端: H:\PyProject\TeleTest\APPDATA\sessions\+***********, 代理: None
2025-06-27 22:26:12.847 | DEBUG    | core.telegram.client_manager:get_proxy:96 - get_proxy 收到代理配置字典: None
2025-06-27 22:26:12.847 | DEBUG    | core.telegram.client_manager:get_proxy:100 - get_proxy: 代理为 None，返回 None
2025-06-27 22:26:15.048 | INFO     | core.telegram.client_manager:_connect_client:207 - 连接成功!
2025-06-27 22:26:15.931 | INFO     | core.telegram.client_manager:_verify_client_authorization:1191 - 账户已授权: +***********
2025-06-27 22:26:23.340 | INFO     | utils.logger:init_logger:98 - 日志系统初始化完成，默认级别: DEBUG，日志目录: H:\PyProject\TeleTest\APPDATA\logs
2025-06-27 22:26:24.981 | INFO     | data.database:init_database:31 - 初始化数据库连接: H:\PyProject\TeleTest\APPDATA\database\telegram_manager.db
2025-06-27 22:26:25.014 | DEBUG    | data.database:init_database:58 - 创建数据库表
2025-06-27 22:26:25.034 | INFO     | data.database:init_database:61 - 数据库初始化完成
2025-06-27 22:26:25.823 | DEBUG    | app.controllers.auth_controller:initialize_software:43 - 控制器: 初始化软件 app_id=1000, version=1.2.7
2025-06-27 22:26:25.823 | DEBUG    | core.auth.api_service:init_software:55 - 发送初始化请求: https://server.xile188.com/api/user/1000/windowns10/1.2.7/ini, params={}
2025-06-27 22:26:26.056 | DEBUG    | core.auth.api_service:init_software:66 - 初始化数据解密成功: {'new_content': '- 新增功能\n  - 1.新增图片上传功能\n  - 2.新增软件文档\n  - 3.新增软件自动更新，后续更新会自动检测版本更新，无需再下载更新包\n  - 4.新增设置账户信息及发信控制\n- 修复bug\n  - 删除监听任务时，不删除对应任务采集到的数据。\n  - 监听设置群无法发送到目标群\n  - 导出监听数据时应当导出单个任务而非所有数据\n- 优化记录\n  - 优化发信规则，直接调取符合要求的账户发送，无账户时才需要等待延时。\n  - 完善软件设置', 'new_url': None, 'notice': {'id': '2', 'content': '公告内容测试3', 'time': '**********'}}
2025-06-27 22:26:26.063 | INFO     | app.controllers.auth_controller:login:61 - 控制器: 用户登录 account=demo01
2025-06-27 22:26:29.382 | INFO     | core.auth.api_service:login:127 - 用户登录: account=demo01
2025-06-27 22:26:29.672 | INFO     | core.auth.api_service:get_user_info:347 - 获取用户信息
2025-06-27 22:26:29.960 | INFO     | core.auth.api_service:get_user_info:375 - 获取用户信息成功
2025-06-27 22:26:29.966 | INFO     | __main__:on_auth_completed:93 - 用户登录成功，正在启动主窗口...
2025-06-27 22:26:29.994 | INFO     | core.telegram.client_worker:__init__:61 - Telegram客户端工作线程初始化
2025-06-27 22:26:29.994 | INFO     | utils.vip_checker:initialize:26 - VIP检查器已初始化，等待登录后开始检查
2025-06-27 22:26:29.994 | INFO     | ui.main_window:_initialize_core_components:107 - MainWindow: 初始化核心组件...
2025-06-27 22:26:29.995 | INFO     | app.services.account_service:__init__:42 - 账户服务初始化
2025-06-27 22:26:29.995 | INFO     | app.controllers.account_controller:__init__:81 - 账户控制器初始化
2025-06-27 22:26:29.995 | INFO     | core.telegram.client_worker:run:65 - Telegram客户端工作线程开始运行
2025-06-27 22:26:29.996 | INFO     | core.proxy.proxy_task_manager:run:284 - 代理任务管理器线程启动
2025-06-27 22:26:29.996 | INFO     | app.services.monitor_service:__init__:39 - 初始化监控任务服务
2025-06-27 22:26:29.997 | INFO     | core.monitor.filter:__init__:26 - 初始化消息过滤器
2025-06-27 22:26:29.997 | INFO     | core.monitor.extractor:__init__:24 - 初始化关键词提取器
2025-06-27 22:26:29.997 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-06-27 22:26:29.998 | INFO     | core.monitor.notifier:__init__:90 - 初始化通知管理器
2025-06-27 22:26:29.998 | INFO     | app.controllers.telegram_monitor_controller:__init__:64 - 初始化 Telegram 监控控制器
2025-06-27 22:26:29.998 | INFO     | core.telegram.client_manager:__init__:66 - Telegram客户端管理器初始化
2025-06-27 22:26:29.999 | DEBUG    | app.controllers.telegram_monitor_controller:__init__:82 - 控制器初始化完成
2025-06-27 22:26:29.999 | INFO     | core.telegram.user_manager:__init__:30 - Telegram用户管理器初始化
2025-06-27 22:26:30.001 | INFO     | core.telegram.group_manager:__init__:36 - Telegram群组管理器初始化
2025-06-27 22:26:30.002 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-06-27 22:26:30.003 | INFO     | core.telegram.monitor_manager:__init__:26 - Telegram监控管理器初始化
2025-06-27 22:26:30.003 | INFO     | core.telegram.telegram_service:__init__:39 - Telegram服务初始化完成
2025-06-27 22:26:30.218 | INFO     | ui.views.monitor_view:__init__:48 - 初始化监控视图
2025-06-27 22:26:30.218 | DEBUG    | ui.views.monitor_view:_connect_signals:80 - 连接控制器信号
2025-06-27 22:26:30.412 | INFO     | ui.views.proxy_view:__init__:96 - 初始化视图，加载第一页代理数据
2025-06-27 22:26:30.658 | INFO     | __main__:on_auth_completed:97 - 主窗口已启动
2025-06-27 22:26:30.699 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:88 - 尝试连接监听日志信号...
2025-06-27 22:26:30.700 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:90 - 监听日志信号连接成功
2025-06-27 22:26:30.700 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:26:30.706 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:26:30.709 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:145 - 加载监控任务列表 (搜索关键词: '')
2025-06-27 22:26:30.709 | DEBUG    | ui.views.monitor_view:_on_loading_started:620 - 加载中: 正在加载任务列表...
2025-06-27 22:26:30.710 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:26:30.718 | INFO     | app.services.proxy_service:refresh_proxies:712 - 刷新代理列表: offset=0, limit=20
2025-06-27 22:26:30.718 | INFO     | core.proxy.proxy_task_manager:submit_task:172 - 提交耗时任务到子线程: refresh_proxies_0_20
2025-06-27 22:26:30.719 | DEBUG    | app.services.proxy_service:get_proxies:500 - 获取代理列表: offset=0, limit=20
2025-06-27 22:26:30.719 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:26:30.719 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:26:30.726 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:26:30.730 | INFO     | app.controllers.invite_controller:get_invite_tasks:38 - 获取邀请任务列表, status=None
2025-06-27 22:26:30.740 | INFO     | app.services.invite_service:get_invite_tasks:121 - 获取邀请任务列表, status=None
2025-06-27 22:26:30.740 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:26:30.756 | INFO     | app.services.invite_service:task_initialize:39 - 初始化邀请服务，重置运行中的任务为暂停状态
2025-06-27 22:26:30.773 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:26:30.789 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:26:30.791 | INFO     | core.proxy.proxy_task_manager:task_wrapper:199 - 任务 refresh_proxies_0_20 完成
2025-06-27 22:26:37.753 | INFO     | utils.vip_checker:start_checking:38 - 开始VIP状态检查
2025-06-27 22:26:37.753 | DEBUG    | utils.vip_checker:_check_vip_status:62 - 正在检查VIP状态...
2025-06-27 22:26:37.753 | INFO     | core.auth.api_service:verify_vip:425 - 会员验证接口调用
2025-06-27 22:26:37.866 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:26:37.868 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:26:37.876 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:26:37.878 | INFO     | app.services.invite_service:task_initialize:48 - 成功重置 0 个任务状态
2025-06-27 22:26:37.878 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:26:37.879 | DEBUG    | ui.views.monitor_view:_refresh_task_list:175 - 刷新任务列表，共0个任务
2025-06-27 22:26:37.879 | INFO     | ui.views.monitor_view:_refresh_task_list:189 - 没有找到监控任务
2025-06-27 22:26:37.880 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:26:37.885 | INFO     | app.services.account_service:get_all_groups:96 - 获取所有账户分组成功, 共 2 个
2025-06-27 22:26:37.885 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:26:37.886 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:26:37.892 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:26:37.893 | DEBUG    | ui.views.account_view:_on_groups_loaded:446 - 分组加载完成: 2个分组
2025-06-27 22:26:37.908 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:26:38.000 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:26:38.002 | INFO     | app.services.account_service:get_all_accounts:290 - 获取所有账户成功, 共 2 个
2025-06-27 22:26:38.002 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:26:38.006 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:26:38.008 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:26:38.010 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-06-27 22:26:38.015 | DEBUG    | ui.views.account_view:_on_accounts_loaded:579 - 账户加载完成: 2个账户
2025-06-27 22:26:38.039 | DEBUG    | ui.views.account_view:_update_group_counts:185 - 分组计数已更新: {2: 1, 1: 1}
2025-06-27 22:26:38.044 | INFO     | ui.views.account_view:_auto_login_accounts:634 - 开始调用控制器进行 2 个账户的自动登录。
2025-06-27 22:26:38.045 | INFO     | app.services.account_service:batch_auto_login:1165 - 服务层：开始批量自动登录 2 个账户, 最大并发: 5
2025-06-27 22:26:38.045 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:26:38.072 | INFO     | app.controllers.telegram_monitor_controller:_load_total_users_count_internal:191 - 成功加载总体统计数据: 总用户数 23, 今日采集 0, 日均采集 23, 运行天数 1
2025-06-27 22:26:38.073 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:156 - 成功加载 0 个任务
2025-06-27 22:26:38.073 | DEBUG    | ui.views.monitor_view:_on_loading_finished:625 - 加载完成
2025-06-27 22:26:38.082 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=True
2025-06-27 22:26:38.084 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=True
2025-06-27 22:26:38.086 | INFO     | ui.views.proxy_view:_load_initial_data:1044 - 初始数据加载完成，共 1 个代理
2025-06-27 22:26:38.089 | INFO     | app.services.account_service:batch_auto_login:1204 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-06-27 22:26:38.089 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:26:38.090 | INFO     | app.services.account_service:get_all_accounts:290 - 获取所有账户成功, 共 2 个
2025-06-27 22:26:38.090 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:26:38.092 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:26:38.094 | DEBUG    | ui.views.account_view:_update_group_counts:185 - 分组计数已更新: {2: 1, 1: 1}
2025-06-27 22:26:38.094 | DEBUG    | ui.views.account_view:_init_cache:152 - 账户缓存初始化完成，共 2 个账户
2025-06-27 22:26:38.098 | DEBUG    | ui.views.account_view:_on_accounts_loaded:579 - 账户加载完成: 2个账户
2025-06-27 22:26:38.118 | DEBUG    | ui.views.account_view:_update_group_counts:185 - 分组计数已更新: {2: 1, 1: 1}
2025-06-27 22:26:38.126 | INFO     | core.auth.api_service:verify_vip:438 - 会员验证成功
2025-06-27 22:26:38.126 | ERROR    | utils.vip_checker:_check_vip_status:87 - VIP状态检查出现异常: 'message'
Traceback (most recent call last):

  File "D:\Program Files\JetBrains\PyCharm 2025.1.1\plugins\python-ce\helpers\pydev\pydevd.py", line 2270, in <module>
    main()
    └ <function main at 0x000001E3EA22C680>

  File "D:\Program Files\JetBrains\PyCharm 2025.1.1\plugins\python-ce\helpers\pydev\pydevd.py", line 2252, in main
    globals = debugger.run(setup['file'], None, None, is_module)
              │        │   │                          └ False
              │        │   └ {'port': 57877, 'vm_type': None, 'client': '127.0.0.1', 'server': False, 'DEBUG_RECORD_SOCKET_READS': False, 'multiproc': Fal...
              │        └ <function PyDB.run at 0x000001E3EA22B420>
              └ <__main__.PyDB object at 0x000001E3EA12DB90>

  File "D:\Program Files\JetBrains\PyCharm 2025.1.1\plugins\python-ce\helpers\pydev\pydevd.py", line 1563, in run
    return self._exec(is_module, entry_point_fn, module_name, file, globals, locals)
           │    │     │          │               │            │     │        └ {'__name__': '__main__', '__doc__': '\nTelegram多账户管理系统 - 主程序入口\n', '__package__': '', '__loader__': <_frozen_importlib_extern...
           │    │     │          │               │            │     └ {'__name__': '__main__', '__doc__': '\nTelegram多账户管理系统 - 主程序入口\n', '__package__': '', '__loader__': <_frozen_importlib_extern...
           │    │     │          │               │            └ 'H:\\PyProject\\TeleTest\\main.py'
           │    │     │          │               └ None
           │    │     │          └ ''
           │    │     └ False
           │    └ <function PyDB._exec at 0x000001E3EA22B4C0>
           └ <__main__.PyDB object at 0x000001E3EA12DB90>

  File "D:\Program Files\JetBrains\PyCharm 2025.1.1\plugins\python-ce\helpers\pydev\pydevd.py", line 1570, in _exec
    pydev_imports.execfile(file, globals, locals)  # execute the script
    │             │        │     │        └ {'__name__': '__main__', '__doc__': '\nTelegram多账户管理系统 - 主程序入口\n', '__package__': '', '__loader__': <_frozen_importlib_extern...
    │             │        │     └ {'__name__': '__main__', '__doc__': '\nTelegram多账户管理系统 - 主程序入口\n', '__package__': '', '__loader__': <_frozen_importlib_extern...
    │             │        └ 'H:\\PyProject\\TeleTest\\main.py'
    │             └ <function execfile at 0x000001E3E9E49F80>
    └ <module '_pydev_bundle.pydev_imports' from 'D:\\Program Files\\JetBrains\\PyCharm 2025.1.1\\plugins\\python-ce\\helpers\\pyde...

  File "D:\Program Files\JetBrains\PyCharm 2025.1.1\plugins\python-ce\helpers\pydev\_pydev_imps\_pydev_execfile.py", line 18, in execfile
    exec(compile(contents+"\n", file, 'exec'), glob, loc)
                 │              │              │     └ {'__name__': '__main__', '__doc__': '\nTelegram多账户管理系统 - 主程序入口\n', '__package__': '', '__loader__': <_frozen_importlib_extern...
                 │              │              └ {'__name__': '__main__', '__doc__': '\nTelegram多账户管理系统 - 主程序入口\n', '__package__': '', '__loader__': <_frozen_importlib_extern...
                 │              └ 'H:\\PyProject\\TeleTest\\main.py'
                 └ '#!/usr/bin/env python\n# -*- coding: utf-8 -*-\n\n"""\nTelegram多账户管理系统 - 主程序入口\n"""\nimport sys\nimport os\nimport asyncio\n...

  File "H:\PyProject\TeleTest\main.py", line 118, in <module>
    main()
    └ <function main at 0x000001E3FE1B7EC0>

  File "H:\PyProject\TeleTest\main.py", line 109, in main
    event_loop.run_forever()
    │          └ <function _QEventLoop.run_forever at 0x000001E3EA5E4900>
    └ <QIOCPEventLoop running=True closed=False debug=False>

  File "H:\PyProject\TeleTest\script\Lib\site-packages\qasync\__init__.py", line 377, in run_forever
    rslt = self.__app.exec()
           └ <QIOCPEventLoop running=True closed=False debug=False>
  File "H:\PyProject\TeleTest\script\Lib\site-packages\qasync\__init__.py", line 279, in timerEvent
    handle._run()
    │      └ <function Handle._run at 0x000001E3EA0EBC40>
    └ <Handle Task.task_wakeup(<Task finishe...> result=None>)>
  File "D:\Program Files\python11\Lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle Task.task_wakeup(<Task finishe...> result=None>)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle Task.task_wakeup(<Task finishe...> result=None>)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle Task.task_wakeup(<Task finishe...> result=None>)>

  File "H:\PyProject\TeleTest\ui\main_window.py", line 293, in on_login_succeeded
    await vip_checker.start_checking()
          │           └ <function VipChecker.start_checking at 0x000001E3EAC89800>
          └ <utils.vip_checker.VipChecker(0x1e3fcb9d510) at 0x000001E3EAC7B4C0>

  File "H:\PyProject\TeleTest\utils\vip_checker.py", line 42, in start_checking
    await self._check_vip_status()
          │    └ <function VipChecker._check_vip_status at 0x000001E3EAC899E0>
          └ <utils.vip_checker.VipChecker(0x1e3fcb9d510) at 0x000001E3EAC7B4C0>

> File "H:\PyProject\TeleTest\utils\vip_checker.py", line 64, in _check_vip_status
    result = await self._controller.verify_vip()
                   │    │           └ <function AuthController.verify_vip at 0x000001E3EAC11080>
                   │    └ <app.controllers.auth_controller.AuthController(0x1e3fcf29e50) at 0x000001E3FE74B040>
                   └ <utils.vip_checker.VipChecker(0x1e3fcb9d510) at 0x000001E3EAC7B4C0>

  File "H:\PyProject\TeleTest\app\controllers\auth_controller.py", line 190, in verify_vip
    return await self._auth_service.verify_vip()
                 │    │             └ <function AuthService.verify_vip at 0x000001E3EAC10900>
                 │    └ <app.services.auth_service.AuthService(0x1e3fcf29c70) at 0x000001E3FE1C7B00>
                 └ <app.controllers.auth_controller.AuthController(0x1e3fcf29e50) at 0x000001E3FE74B040>

  File "H:\PyProject\TeleTest\app\services\auth_service.py", line 123, in verify_vip
    return await self._service.verify_vip()
                 │    │        └ <function ApiService.verify_vip at 0x000001E3EAC07CE0>
                 │    └ <core.auth.api_service.ApiService object at 0x000001E3EAC0DF90>
                 └ <app.services.auth_service.AuthService(0x1e3fcf29c70) at 0x000001E3FE1C7B00>

  File "H:\PyProject\TeleTest\core\auth\api_service.py", line 439, in verify_vip
    return {"success": True, "data": result['time'],'message':result['message']}
                                     │                        └ {'code': 200, 'msg': '验证成功', 'time': 1751034397, 'sign': 'c9b998b664052a3713417692f4fe3fe0', 'run': {'ms': 2.73, 'ram': '125....
                                     └ {'code': 200, 'msg': '验证成功', 'time': 1751034397, 'sign': 'c9b998b664052a3713417692f4fe3fe0', 'run': {'ms': 2.73, 'ram': '125....

KeyError: 'message'
2025-06-27 22:27:40.271 | INFO     | utils.logger:init_logger:98 - 日志系统初始化完成，默认级别: DEBUG，日志目录: H:\PyProject\TeleTest\APPDATA\logs
2025-06-27 22:27:41.962 | INFO     | data.database:init_database:31 - 初始化数据库连接: H:\PyProject\TeleTest\APPDATA\database\telegram_manager.db
2025-06-27 22:27:41.994 | DEBUG    | data.database:init_database:58 - 创建数据库表
2025-06-27 22:27:42.015 | INFO     | data.database:init_database:61 - 数据库初始化完成
2025-06-27 22:27:42.837 | DEBUG    | app.controllers.auth_controller:initialize_software:43 - 控制器: 初始化软件 app_id=1000, version=1.2.7
2025-06-27 22:27:42.837 | DEBUG    | core.auth.api_service:init_software:55 - 发送初始化请求: https://server.xile188.com/api/user/1000/windowns10/1.2.7/ini, params={}
2025-06-27 22:27:43.138 | DEBUG    | core.auth.api_service:init_software:66 - 初始化数据解密成功: {'new_content': '- 新增功能\n  - 1.新增图片上传功能\n  - 2.新增软件文档\n  - 3.新增软件自动更新，后续更新会自动检测版本更新，无需再下载更新包\n  - 4.新增设置账户信息及发信控制\n- 修复bug\n  - 删除监听任务时，不删除对应任务采集到的数据。\n  - 监听设置群无法发送到目标群\n  - 导出监听数据时应当导出单个任务而非所有数据\n- 优化记录\n  - 优化发信规则，直接调取符合要求的账户发送，无账户时才需要等待延时。\n  - 完善软件设置', 'new_url': None, 'notice': {'id': '2', 'content': '公告内容测试3', 'time': '**********'}}
2025-06-27 22:27:43.145 | INFO     | app.controllers.auth_controller:login:61 - 控制器: 用户登录 account=demo01
2025-06-27 22:27:46.154 | INFO     | core.auth.api_service:login:127 - 用户登录: account=demo01
2025-06-27 22:27:46.422 | INFO     | core.auth.api_service:get_user_info:347 - 获取用户信息
2025-06-27 22:27:46.669 | INFO     | core.auth.api_service:get_user_info:375 - 获取用户信息成功
2025-06-27 22:27:46.676 | INFO     | __main__:on_auth_completed:93 - 用户登录成功，正在启动主窗口...
2025-06-27 22:27:46.703 | INFO     | core.telegram.client_worker:__init__:61 - Telegram客户端工作线程初始化
2025-06-27 22:27:46.704 | INFO     | utils.vip_checker:initialize:26 - VIP检查器已初始化，等待登录后开始检查
2025-06-27 22:27:46.704 | INFO     | ui.main_window:_initialize_core_components:107 - MainWindow: 初始化核心组件...
2025-06-27 22:27:46.704 | INFO     | core.telegram.client_worker:run:65 - Telegram客户端工作线程开始运行
2025-06-27 22:27:46.705 | INFO     | app.services.account_service:__init__:42 - 账户服务初始化
2025-06-27 22:27:46.706 | INFO     | app.controllers.account_controller:__init__:81 - 账户控制器初始化
2025-06-27 22:27:46.706 | INFO     | app.services.monitor_service:__init__:39 - 初始化监控任务服务
2025-06-27 22:27:46.706 | INFO     | core.monitor.filter:__init__:26 - 初始化消息过滤器
2025-06-27 22:27:46.706 | INFO     | core.monitor.extractor:__init__:24 - 初始化关键词提取器
2025-06-27 22:27:46.706 | INFO     | core.proxy.proxy_task_manager:run:284 - 代理任务管理器线程启动
2025-06-27 22:27:46.707 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-06-27 22:27:46.707 | INFO     | core.monitor.notifier:__init__:90 - 初始化通知管理器
2025-06-27 22:27:46.708 | INFO     | core.telegram.client_manager:__init__:66 - Telegram客户端管理器初始化
2025-06-27 22:27:46.708 | INFO     | app.controllers.telegram_monitor_controller:__init__:64 - 初始化 Telegram 监控控制器
2025-06-27 22:27:46.709 | INFO     | core.telegram.user_manager:__init__:30 - Telegram用户管理器初始化
2025-06-27 22:27:46.709 | INFO     | core.telegram.group_manager:__init__:36 - Telegram群组管理器初始化
2025-06-27 22:27:46.709 | DEBUG    | app.controllers.telegram_monitor_controller:__init__:82 - 控制器初始化完成
2025-06-27 22:27:46.709 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-06-27 22:27:46.711 | INFO     | core.telegram.monitor_manager:__init__:26 - Telegram监控管理器初始化
2025-06-27 22:27:46.712 | INFO     | core.telegram.telegram_service:__init__:39 - Telegram服务初始化完成
2025-06-27 22:27:46.930 | INFO     | ui.views.monitor_view:__init__:48 - 初始化监控视图
2025-06-27 22:27:46.930 | DEBUG    | ui.views.monitor_view:_connect_signals:80 - 连接控制器信号
2025-06-27 22:27:47.131 | INFO     | ui.views.proxy_view:__init__:96 - 初始化视图，加载第一页代理数据
2025-06-27 22:27:47.394 | INFO     | __main__:on_auth_completed:97 - 主窗口已启动
2025-06-27 22:27:47.444 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:88 - 尝试连接监听日志信号...
2025-06-27 22:27:47.444 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:90 - 监听日志信号连接成功
2025-06-27 22:27:47.445 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:27:47.451 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:27:47.457 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:145 - 加载监控任务列表 (搜索关键词: '')
2025-06-27 22:27:47.458 | DEBUG    | ui.views.monitor_view:_on_loading_started:620 - 加载中: 正在加载任务列表...
2025-06-27 22:27:47.458 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:27:47.466 | INFO     | app.services.proxy_service:refresh_proxies:712 - 刷新代理列表: offset=0, limit=20
2025-06-27 22:27:47.466 | INFO     | core.proxy.proxy_task_manager:submit_task:172 - 提交耗时任务到子线程: refresh_proxies_0_20
2025-06-27 22:27:47.467 | DEBUG    | app.services.proxy_service:get_proxies:500 - 获取代理列表: offset=0, limit=20
2025-06-27 22:27:47.467 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:27:47.468 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:27:47.472 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:27:47.478 | INFO     | app.controllers.invite_controller:get_invite_tasks:38 - 获取邀请任务列表, status=None
2025-06-27 22:27:47.478 | INFO     | app.services.invite_service:get_invite_tasks:121 - 获取邀请任务列表, status=None
2025-06-27 22:27:47.479 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:27:47.510 | INFO     | app.services.invite_service:task_initialize:39 - 初始化邀请服务，重置运行中的任务为暂停状态
2025-06-27 22:27:47.527 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:27:47.537 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:27:47.539 | INFO     | core.proxy.proxy_task_manager:task_wrapper:199 - 任务 refresh_proxies_0_20 完成
2025-06-27 22:27:52.049 | INFO     | utils.vip_checker:start_checking:38 - 开始VIP状态检查
2025-06-27 22:27:52.049 | DEBUG    | utils.vip_checker:_check_vip_status:62 - 正在检查VIP状态...
2025-06-27 22:27:52.049 | INFO     | core.auth.api_service:verify_vip:425 - 会员验证接口调用
2025-06-27 22:27:52.215 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:27:52.217 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:27:52.227 | DEBUG    | ui.views.monitor_view:_refresh_task_list:175 - 刷新任务列表，共0个任务
2025-06-27 22:27:52.228 | INFO     | ui.views.monitor_view:_refresh_task_list:189 - 没有找到监控任务
2025-06-27 22:27:52.228 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:27:52.232 | INFO     | app.services.invite_service:task_initialize:48 - 成功重置 0 个任务状态
2025-06-27 22:27:52.232 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:27:52.240 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:27:52.241 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:27:52.305 | INFO     | app.services.account_service:get_all_groups:96 - 获取所有账户分组成功, 共 2 个
2025-06-27 22:27:52.305 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:27:52.305 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:27:52.315 | DEBUG    | ui.views.account_view:_on_groups_loaded:446 - 分组加载完成: 2个分组
2025-06-27 22:27:52.329 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:27:52.345 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:27:52.346 | INFO     | app.services.account_service:get_all_accounts:290 - 获取所有账户成功, 共 2 个
2025-06-27 22:27:52.346 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:27:52.347 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:27:52.353 | INFO     | app.controllers.telegram_monitor_controller:_load_total_users_count_internal:191 - 成功加载总体统计数据: 总用户数 23, 今日采集 0, 日均采集 23, 运行天数 1
2025-06-27 22:27:52.354 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:156 - 成功加载 0 个任务
2025-06-27 22:27:52.354 | DEBUG    | ui.views.monitor_view:_on_loading_finished:625 - 加载完成
2025-06-27 22:27:52.355 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:27:52.358 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-06-27 22:27:52.362 | DEBUG    | ui.views.account_view:_on_accounts_loaded:579 - 账户加载完成: 2个账户
2025-06-27 22:27:52.382 | DEBUG    | ui.views.account_view:_update_group_counts:185 - 分组计数已更新: {2: 1, 1: 1}
2025-06-27 22:27:52.386 | INFO     | ui.views.account_view:_auto_login_accounts:634 - 开始调用控制器进行 2 个账户的自动登录。
2025-06-27 22:27:52.386 | INFO     | app.services.account_service:batch_auto_login:1165 - 服务层：开始批量自动登录 2 个账户, 最大并发: 5
2025-06-27 22:27:52.387 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:27:52.421 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=True
2025-06-27 22:27:52.423 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=True
2025-06-27 22:27:52.425 | INFO     | ui.views.proxy_view:_load_initial_data:1044 - 初始数据加载完成，共 1 个代理
2025-06-27 22:27:52.426 | INFO     | app.services.account_service:get_all_accounts:290 - 获取所有账户成功, 共 2 个
2025-06-27 22:27:52.426 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:27:52.427 | INFO     | app.services.account_service:batch_auto_login:1204 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-06-27 22:27:52.427 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:27:52.431 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:27:52.433 | DEBUG    | ui.views.account_view:_update_group_counts:185 - 分组计数已更新: {2: 1, 1: 1}
2025-06-27 22:27:52.433 | DEBUG    | ui.views.account_view:_init_cache:152 - 账户缓存初始化完成，共 2 个账户
2025-06-27 22:27:52.438 | DEBUG    | ui.views.account_view:_on_accounts_loaded:579 - 账户加载完成: 2个账户
2025-06-27 22:27:52.457 | DEBUG    | ui.views.account_view:_update_group_counts:185 - 分组计数已更新: {2: 1, 1: 1}
2025-06-27 22:27:52.471 | INFO     | app.services.account_service:batch_auto_login:1204 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-06-27 22:27:52.471 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:27:52.474 | INFO     | app.services.account_service:batch_auto_login:1274 - 服务层：准备调用核心层处理 2 个账户的自动登录。
2025-06-27 22:27:52.474 | INFO     | app.services.account_service:batch_auto_login:1284 - 服务层：设置核心层任务超时为 120 秒。
2025-06-27 22:27:52.475 | INFO     | core.telegram.client_manager:batch_auto_login:1216 - 开始批量自动登录, 共 2 个账户, 最大并发 5
2025-06-27 22:27:52.477 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:259 - 创建客户端: H:\PyProject\TeleTest\APPDATA\sessions\+***********, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-06-27 22:27:52.477 | DEBUG    | core.telegram.client_manager:get_proxy:96 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-06-27 22:27:52.478 | DEBUG    | core.telegram.client_manager:get_proxy:143 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-06-27 22:27:52.483 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:259 - 创建客户端: H:\PyProject\TeleTest\APPDATA\sessions\+***********, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-06-27 22:27:52.483 | DEBUG    | core.telegram.client_manager:get_proxy:96 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-06-27 22:27:52.483 | DEBUG    | core.telegram.client_manager:get_proxy:143 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-06-27 22:27:52.531 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:27:52.605 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:27:52.613 | INFO     | core.auth.api_service:verify_vip:438 - 会员验证成功
2025-06-27 22:27:54.549 | INFO     | core.telegram.client_manager:_connect_client:207 - 连接成功!
2025-06-27 22:27:55.286 | INFO     | core.telegram.client_manager:_verify_client_authorization:1191 - 账户已授权: +***********
2025-06-27 22:27:55.753 | INFO     | core.telegram.client_manager:_connect_client:207 - 连接成功!
2025-06-27 22:27:56.440 | INFO     | core.telegram.client_manager:_verify_client_authorization:1191 - 账户已授权: +***********
2025-06-27 22:27:58.431 | INFO     | core.telegram.client_manager:batch_auto_login:1365 - 批量登录完成: 总计 2, 成功 2, 失败 0
2025-06-27 22:28:59.014 | INFO     | utils.vip_checker:_check_vip_status:80 - VIP状态有效
2025-06-27 22:28:59.021 | INFO     | app.services.account_service:batch_auto_login:1305 - 服务层：核心层登录完成: 成功 2, 失败 0 / 2
2025-06-27 22:28:59.032 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-27 22:28:59.113 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:29:09.241 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:29:09.480 | INFO     | app.services.account_service:get_all_groups:96 - 获取所有账户分组成功, 共 2 个
2025-06-27 22:29:09.481 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:29:09.482 | INFO     | ui.views.add_account_view:load_account_groups:94 - 成功加载 2 个分组
2025-06-27 22:29:10.349 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:29:10.440 | INFO     | app.services.account_service:get_all_accounts:290 - 获取所有账户成功, 共 2 个
2025-06-27 22:29:10.440 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:29:10.443 | DEBUG    | ui.views.account_view:_on_accounts_loaded:579 - 账户加载完成: 2个账户
2025-06-27 22:29:10.466 | DEBUG    | ui.views.account_view:_update_group_counts:185 - 分组计数已更新: {2: 1, 1: 1}
2025-06-27 22:29:11.522 | INFO     | ui.views.import_sessions_view:refresh_proxy_list_async:422 - 开始刷新代理列表...
2025-06-27 22:29:11.523 | INFO     | app.services.account_service:get_proxy_ips:1044 - 获取有效代理IP列表（包含绑定计数）
2025-06-27 22:29:11.523 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:29:11.667 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:29:11.675 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:29:11.682 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:29:11.690 | INFO     | app.services.account_service:get_proxy_ips:1096 - 成功获取 1 个有效代理（已附带绑定计数）
2025-06-27 22:29:11.691 | INFO     | app.controllers.account_controller:get_proxy_ips:538 - 获取到1个有效代理IP
2025-06-27 22:29:11.700 | INFO     | ui.views.import_sessions_view:refresh_groups_list_async:469 - 开始刷新分组列表...
2025-06-27 22:29:11.700 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:29:11.717 | INFO     | app.services.account_service:get_all_groups:96 - 获取所有账户分组成功, 共 2 个
2025-06-27 22:29:11.718 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:29:46.661 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-27 22:30:46.669 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-27 22:31:46.663 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-27 22:32:46.667 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-27 22:33:46.668 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-27 22:34:46.668 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-27 22:35:46.671 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-27 22:36:46.674 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-27 22:37:46.662 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-27 22:38:46.663 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-27 22:39:46.662 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-27 22:40:46.662 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-27 22:41:46.662 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-27 22:42:46.664 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-27 22:43:46.669 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-27 22:44:46.673 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-27 22:45:46.668 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-27 22:46:46.670 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-27 22:47:46.667 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-27 22:48:46.668 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-27 22:49:30.386 | INFO     | utils.logger:init_logger:98 - 日志系统初始化完成，默认级别: DEBUG，日志目录: H:\PyProject\TeleTest\APPDATA\logs
2025-06-27 22:49:32.228 | INFO     | data.database:init_database:31 - 初始化数据库连接: H:\PyProject\TeleTest\APPDATA\database\telegram_manager.db
2025-06-27 22:49:32.261 | DEBUG    | data.database:init_database:58 - 创建数据库表
2025-06-27 22:49:32.284 | INFO     | data.database:init_database:61 - 数据库初始化完成
2025-06-27 22:49:33.756 | DEBUG    | app.controllers.auth_controller:initialize_software:43 - 控制器: 初始化软件 app_id=1000, version=1.2.7
2025-06-27 22:49:33.756 | DEBUG    | core.auth.api_service:init_software:55 - 发送初始化请求: https://server.xile188.com/api/user/1000/windowns10/1.2.7/ini, params={}
2025-06-27 22:49:34.006 | DEBUG    | core.auth.api_service:init_software:66 - 初始化数据解密成功: {'new_content': '- 新增功能\n  - 1.新增图片上传功能\n  - 2.新增软件文档\n  - 3.新增软件自动更新，后续更新会自动检测版本更新，无需再下载更新包\n  - 4.新增设置账户信息及发信控制\n- 修复bug\n  - 删除监听任务时，不删除对应任务采集到的数据。\n  - 监听设置群无法发送到目标群\n  - 导出监听数据时应当导出单个任务而非所有数据\n- 优化记录\n  - 优化发信规则，直接调取符合要求的账户发送，无账户时才需要等待延时。\n  - 完善软件设置', 'new_url': None, 'notice': {'id': '2', 'content': '公告内容测试3', 'time': '**********'}}
2025-06-27 22:49:34.017 | INFO     | app.controllers.auth_controller:login:61 - 控制器: 用户登录 account=demo01
2025-06-27 22:49:37.052 | INFO     | core.auth.api_service:login:127 - 用户登录: account=demo01
2025-06-27 22:49:37.318 | INFO     | core.auth.api_service:get_user_info:347 - 获取用户信息
2025-06-27 22:49:37.525 | INFO     | core.auth.api_service:get_user_info:375 - 获取用户信息成功
2025-06-27 22:49:37.532 | INFO     | __main__:on_auth_completed:93 - 用户登录成功，正在启动主窗口...
2025-06-27 22:49:37.562 | INFO     | core.telegram.client_worker:__init__:61 - Telegram客户端工作线程初始化
2025-06-27 22:49:37.562 | INFO     | utils.vip_checker:initialize:26 - VIP检查器已初始化，等待登录后开始检查
2025-06-27 22:49:37.563 | INFO     | ui.main_window:_initialize_core_components:107 - MainWindow: 初始化核心组件...
2025-06-27 22:49:37.563 | INFO     | app.services.account_service:__init__:42 - 账户服务初始化
2025-06-27 22:49:37.564 | INFO     | app.controllers.account_controller:__init__:81 - 账户控制器初始化
2025-06-27 22:49:37.564 | INFO     | app.services.monitor_service:__init__:39 - 初始化监控任务服务
2025-06-27 22:49:37.564 | INFO     | core.monitor.filter:__init__:26 - 初始化消息过滤器
2025-06-27 22:49:37.564 | INFO     | core.telegram.client_worker:run:65 - Telegram客户端工作线程开始运行
2025-06-27 22:49:37.565 | INFO     | core.proxy.proxy_task_manager:run:284 - 代理任务管理器线程启动
2025-06-27 22:49:37.565 | INFO     | core.monitor.extractor:__init__:24 - 初始化关键词提取器
2025-06-27 22:49:37.565 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-06-27 22:49:37.565 | INFO     | core.monitor.notifier:__init__:90 - 初始化通知管理器
2025-06-27 22:49:37.566 | INFO     | app.controllers.telegram_monitor_controller:__init__:64 - 初始化 Telegram 监控控制器
2025-06-27 22:49:37.566 | DEBUG    | app.controllers.telegram_monitor_controller:__init__:82 - 控制器初始化完成
2025-06-27 22:49:37.568 | INFO     | core.telegram.client_manager:__init__:66 - Telegram客户端管理器初始化
2025-06-27 22:49:37.569 | INFO     | core.telegram.user_manager:__init__:30 - Telegram用户管理器初始化
2025-06-27 22:49:37.570 | INFO     | core.telegram.group_manager:__init__:36 - Telegram群组管理器初始化
2025-06-27 22:49:37.571 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-06-27 22:49:37.572 | INFO     | core.telegram.monitor_manager:__init__:26 - Telegram监控管理器初始化
2025-06-27 22:49:37.573 | INFO     | core.telegram.telegram_service:__init__:39 - Telegram服务初始化完成
2025-06-27 22:49:37.790 | INFO     | ui.views.monitor_view:__init__:48 - 初始化监控视图
2025-06-27 22:49:37.791 | DEBUG    | ui.views.monitor_view:_connect_signals:80 - 连接控制器信号
2025-06-27 22:49:37.998 | INFO     | ui.views.proxy_view:__init__:96 - 初始化视图，加载第一页代理数据
2025-06-27 22:49:38.262 | INFO     | __main__:on_auth_completed:97 - 主窗口已启动
2025-06-27 22:49:38.312 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:88 - 尝试连接监听日志信号...
2025-06-27 22:49:38.312 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:90 - 监听日志信号连接成功
2025-06-27 22:49:38.313 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:49:38.318 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:49:38.323 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:145 - 加载监控任务列表 (搜索关键词: '')
2025-06-27 22:49:38.323 | DEBUG    | ui.views.monitor_view:_on_loading_started:620 - 加载中: 正在加载任务列表...
2025-06-27 22:49:38.323 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:49:38.330 | INFO     | app.services.proxy_service:refresh_proxies:712 - 刷新代理列表: offset=0, limit=20
2025-06-27 22:49:38.331 | INFO     | core.proxy.proxy_task_manager:submit_task:172 - 提交耗时任务到子线程: refresh_proxies_0_20
2025-06-27 22:49:38.331 | DEBUG    | app.services.proxy_service:get_proxies:500 - 获取代理列表: offset=0, limit=20
2025-06-27 22:49:38.331 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:49:38.333 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:49:38.339 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:49:38.367 | INFO     | app.controllers.invite_controller:get_invite_tasks:38 - 获取邀请任务列表, status=None
2025-06-27 22:49:38.373 | INFO     | app.services.invite_service:get_invite_tasks:121 - 获取邀请任务列表, status=None
2025-06-27 22:49:38.373 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:49:38.395 | INFO     | app.services.invite_service:task_initialize:39 - 初始化邀请服务，重置运行中的任务为暂停状态
2025-06-27 22:49:38.396 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:49:38.402 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:49:38.404 | INFO     | utils.vip_checker:start_checking:38 - 开始VIP状态检查
2025-06-27 22:49:38.404 | DEBUG    | utils.vip_checker:_check_vip_status:62 - 正在检查VIP状态...
2025-06-27 22:49:38.404 | INFO     | core.auth.api_service:verify_vip:425 - 会员验证接口调用
2025-06-27 22:49:38.405 | INFO     | core.proxy.proxy_task_manager:task_wrapper:199 - 任务 refresh_proxies_0_20 完成
2025-06-27 22:49:38.619 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:49:38.636 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:49:38.640 | DEBUG    | ui.views.monitor_view:_refresh_task_list:175 - 刷新任务列表，共0个任务
2025-06-27 22:49:38.641 | INFO     | ui.views.monitor_view:_refresh_task_list:189 - 没有找到监控任务
2025-06-27 22:49:38.641 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:49:38.643 | INFO     | app.services.invite_service:task_initialize:48 - 成功重置 0 个任务状态
2025-06-27 22:49:38.644 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:49:38.648 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:49:38.655 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:49:38.657 | INFO     | app.services.account_service:get_all_groups:96 - 获取所有账户分组成功, 共 2 个
2025-06-27 22:49:38.657 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:49:38.660 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:49:38.667 | DEBUG    | ui.views.account_view:_on_groups_loaded:446 - 分组加载完成: 2个分组
2025-06-27 22:49:38.682 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:49:39.042 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:49:39.043 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:49:39.045 | INFO     | app.services.account_service:get_all_accounts:290 - 获取所有账户成功, 共 2 个
2025-06-27 22:49:39.046 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:49:39.046 | INFO     | core.auth.api_service:verify_vip:438 - 会员验证成功
2025-06-27 22:49:45.296 | INFO     | utils.vip_checker:_check_vip_status:79 - VIP状态有效
2025-06-27 22:49:45.364 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-06-27 22:49:45.371 | INFO     | app.controllers.telegram_monitor_controller:_load_total_users_count_internal:191 - 成功加载总体统计数据: 总用户数 23, 今日采集 0, 日均采集 23, 运行天数 1
2025-06-27 22:49:45.371 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:156 - 成功加载 0 个任务
2025-06-27 22:49:45.371 | DEBUG    | ui.views.monitor_view:_on_loading_finished:625 - 加载完成
2025-06-27 22:49:45.382 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:49:45.384 | DEBUG    | ui.views.account_view:_on_accounts_loaded:579 - 账户加载完成: 2个账户
2025-06-27 22:49:45.408 | DEBUG    | ui.views.account_view:_update_group_counts:185 - 分组计数已更新: {2: 1, 1: 1}
2025-06-27 22:49:45.413 | INFO     | ui.views.account_view:_auto_login_accounts:634 - 开始调用控制器进行 2 个账户的自动登录。
2025-06-27 22:49:45.413 | INFO     | app.services.account_service:batch_auto_login:1165 - 服务层：开始批量自动登录 2 个账户, 最大并发: 5
2025-06-27 22:49:45.413 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:49:45.428 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=True
2025-06-27 22:49:45.429 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=True
2025-06-27 22:49:45.431 | INFO     | ui.views.proxy_view:_load_initial_data:1044 - 初始数据加载完成，共 1 个代理
2025-06-27 22:49:45.437 | INFO     | app.services.account_service:get_all_accounts:290 - 获取所有账户成功, 共 2 个
2025-06-27 22:49:45.438 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:49:45.438 | INFO     | app.services.account_service:batch_auto_login:1204 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-06-27 22:49:45.439 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:49:45.441 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:49:45.444 | DEBUG    | ui.views.account_view:_update_group_counts:185 - 分组计数已更新: {2: 1, 1: 1}
2025-06-27 22:49:45.444 | DEBUG    | ui.views.account_view:_init_cache:152 - 账户缓存初始化完成，共 2 个账户
2025-06-27 22:49:45.448 | DEBUG    | ui.views.account_view:_on_accounts_loaded:579 - 账户加载完成: 2个账户
2025-06-27 22:49:45.467 | DEBUG    | ui.views.account_view:_update_group_counts:185 - 分组计数已更新: {2: 1, 1: 1}
2025-06-27 22:49:45.479 | INFO     | app.services.account_service:batch_auto_login:1204 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-06-27 22:49:45.480 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:49:45.481 | INFO     | app.services.account_service:batch_auto_login:1274 - 服务层：准备调用核心层处理 2 个账户的自动登录。
2025-06-27 22:49:45.482 | INFO     | app.services.account_service:batch_auto_login:1284 - 服务层：设置核心层任务超时为 120 秒。
2025-06-27 22:49:45.482 | INFO     | core.telegram.client_manager:batch_auto_login:1216 - 开始批量自动登录, 共 2 个账户, 最大并发 5
2025-06-27 22:49:45.484 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:259 - 创建客户端: H:\PyProject\TeleTest\APPDATA\sessions\+***********, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-06-27 22:49:45.484 | DEBUG    | core.telegram.client_manager:get_proxy:96 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-06-27 22:49:45.484 | DEBUG    | core.telegram.client_manager:get_proxy:143 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-06-27 22:49:45.491 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:259 - 创建客户端: H:\PyProject\TeleTest\APPDATA\sessions\+***********, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-06-27 22:49:45.491 | DEBUG    | core.telegram.client_manager:get_proxy:96 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-06-27 22:49:45.492 | DEBUG    | core.telegram.client_manager:get_proxy:143 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-06-27 22:49:45.537 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:49:45.613 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:49:45.674 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:49:47.797 | INFO     | core.telegram.client_manager:_connect_client:207 - 连接成功!
2025-06-27 22:49:48.066 | INFO     | core.telegram.client_manager:_connect_client:207 - 连接成功!
2025-06-27 22:49:48.478 | INFO     | core.telegram.client_manager:_verify_client_authorization:1191 - 账户已授权: +***********
2025-06-27 22:49:48.809 | INFO     | core.telegram.client_manager:_verify_client_authorization:1191 - 账户已授权: +***********
2025-06-27 22:49:50.816 | INFO     | core.telegram.client_manager:batch_auto_login:1365 - 批量登录完成: 总计 2, 成功 2, 失败 0
2025-06-27 22:49:51.493 | INFO     | app.services.account_service:batch_auto_login:1305 - 服务层：核心层登录完成: 成功 2, 失败 0 / 2
2025-06-27 22:49:51.883 | INFO     | ui.views.import_sessions_view:refresh_proxy_list_async:422 - 开始刷新代理列表...
2025-06-27 22:49:51.884 | INFO     | app.services.account_service:get_proxy_ips:1044 - 获取有效代理IP列表（包含绑定计数）
2025-06-27 22:49:51.884 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:49:52.018 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:49:52.019 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:49:52.033 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:49:52.034 | INFO     | app.services.account_service:get_proxy_ips:1096 - 成功获取 1 个有效代理（已附带绑定计数）
2025-06-27 22:49:52.034 | INFO     | app.controllers.account_controller:get_proxy_ips:538 - 获取到1个有效代理IP
2025-06-27 22:49:52.049 | INFO     | ui.views.import_sessions_view:refresh_groups_list_async:469 - 开始刷新分组列表...
2025-06-27 22:49:52.049 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:49:52.064 | INFO     | app.services.account_service:get_all_groups:96 - 获取所有账户分组成功, 共 2 个
2025-06-27 22:49:52.064 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:50:22.545 | INFO     | utils.logger:init_logger:98 - 日志系统初始化完成，默认级别: DEBUG，日志目录: H:\PyProject\TeleTest\APPDATA\logs
2025-06-27 22:50:24.326 | INFO     | data.database:init_database:31 - 初始化数据库连接: H:\PyProject\TeleTest\APPDATA\database\telegram_manager.db
2025-06-27 22:50:24.358 | DEBUG    | data.database:init_database:58 - 创建数据库表
2025-06-27 22:50:24.375 | INFO     | data.database:init_database:61 - 数据库初始化完成
2025-06-27 22:50:25.169 | DEBUG    | app.controllers.auth_controller:initialize_software:43 - 控制器: 初始化软件 app_id=1000, version=1.2.7
2025-06-27 22:50:25.169 | DEBUG    | core.auth.api_service:init_software:55 - 发送初始化请求: https://server.xile188.com/api/user/1000/windowns10/1.2.7/ini, params={}
2025-06-27 22:50:25.501 | DEBUG    | core.auth.api_service:init_software:66 - 初始化数据解密成功: {'new_content': '- 新增功能\n  - 1.新增图片上传功能\n  - 2.新增软件文档\n  - 3.新增软件自动更新，后续更新会自动检测版本更新，无需再下载更新包\n  - 4.新增设置账户信息及发信控制\n- 修复bug\n  - 删除监听任务时，不删除对应任务采集到的数据。\n  - 监听设置群无法发送到目标群\n  - 导出监听数据时应当导出单个任务而非所有数据\n- 优化记录\n  - 优化发信规则，直接调取符合要求的账户发送，无账户时才需要等待延时。\n  - 完善软件设置', 'new_url': None, 'notice': {'id': '2', 'content': '公告内容测试3', 'time': '**********'}}
2025-06-27 22:50:25.508 | INFO     | app.controllers.auth_controller:login:61 - 控制器: 用户登录 account=demo01
2025-06-27 22:50:28.551 | INFO     | core.auth.api_service:login:127 - 用户登录: account=demo01
2025-06-27 22:50:28.859 | INFO     | core.auth.api_service:get_user_info:347 - 获取用户信息
2025-06-27 22:50:29.144 | INFO     | core.auth.api_service:get_user_info:375 - 获取用户信息成功
2025-06-27 22:50:29.150 | INFO     | __main__:on_auth_completed:93 - 用户登录成功，正在启动主窗口...
2025-06-27 22:50:29.178 | INFO     | core.telegram.client_worker:__init__:61 - Telegram客户端工作线程初始化
2025-06-27 22:50:29.179 | INFO     | utils.vip_checker:initialize:26 - VIP检查器已初始化，等待登录后开始检查
2025-06-27 22:50:29.179 | INFO     | ui.main_window:_initialize_core_components:107 - MainWindow: 初始化核心组件...
2025-06-27 22:50:29.180 | INFO     | app.services.account_service:__init__:42 - 账户服务初始化
2025-06-27 22:50:29.180 | INFO     | core.proxy.proxy_task_manager:run:284 - 代理任务管理器线程启动
2025-06-27 22:50:29.180 | INFO     | core.telegram.client_worker:run:65 - Telegram客户端工作线程开始运行
2025-06-27 22:50:29.181 | INFO     | app.controllers.account_controller:__init__:81 - 账户控制器初始化
2025-06-27 22:50:29.181 | INFO     | app.services.monitor_service:__init__:39 - 初始化监控任务服务
2025-06-27 22:50:29.182 | INFO     | core.monitor.filter:__init__:26 - 初始化消息过滤器
2025-06-27 22:50:29.182 | INFO     | core.monitor.extractor:__init__:24 - 初始化关键词提取器
2025-06-27 22:50:29.182 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-06-27 22:50:29.183 | INFO     | core.monitor.notifier:__init__:90 - 初始化通知管理器
2025-06-27 22:50:29.183 | INFO     | core.telegram.client_manager:__init__:66 - Telegram客户端管理器初始化
2025-06-27 22:50:29.183 | INFO     | app.controllers.telegram_monitor_controller:__init__:64 - 初始化 Telegram 监控控制器
2025-06-27 22:50:29.183 | INFO     | core.telegram.user_manager:__init__:30 - Telegram用户管理器初始化
2025-06-27 22:50:29.183 | DEBUG    | app.controllers.telegram_monitor_controller:__init__:82 - 控制器初始化完成
2025-06-27 22:50:29.184 | INFO     | core.telegram.group_manager:__init__:36 - Telegram群组管理器初始化
2025-06-27 22:50:29.185 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-06-27 22:50:29.185 | INFO     | core.telegram.monitor_manager:__init__:26 - Telegram监控管理器初始化
2025-06-27 22:50:29.186 | INFO     | core.telegram.telegram_service:__init__:39 - Telegram服务初始化完成
2025-06-27 22:50:29.399 | INFO     | ui.views.monitor_view:__init__:48 - 初始化监控视图
2025-06-27 22:50:29.400 | DEBUG    | ui.views.monitor_view:_connect_signals:80 - 连接控制器信号
2025-06-27 22:50:29.596 | INFO     | ui.views.proxy_view:__init__:96 - 初始化视图，加载第一页代理数据
2025-06-27 22:50:29.841 | INFO     | __main__:on_auth_completed:97 - 主窗口已启动
2025-06-27 22:50:29.890 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:88 - 尝试连接监听日志信号...
2025-06-27 22:50:29.891 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:90 - 监听日志信号连接成功
2025-06-27 22:50:29.892 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:50:29.898 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:50:29.902 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:145 - 加载监控任务列表 (搜索关键词: '')
2025-06-27 22:50:29.902 | DEBUG    | ui.views.monitor_view:_on_loading_started:620 - 加载中: 正在加载任务列表...
2025-06-27 22:50:29.903 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:50:29.910 | INFO     | app.services.proxy_service:refresh_proxies:712 - 刷新代理列表: offset=0, limit=20
2025-06-27 22:50:29.910 | INFO     | core.proxy.proxy_task_manager:submit_task:172 - 提交耗时任务到子线程: refresh_proxies_0_20
2025-06-27 22:50:29.911 | DEBUG    | app.services.proxy_service:get_proxies:500 - 获取代理列表: offset=0, limit=20
2025-06-27 22:50:29.911 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:50:29.911 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:50:29.918 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:50:29.934 | INFO     | app.controllers.invite_controller:get_invite_tasks:38 - 获取邀请任务列表, status=None
2025-06-27 22:50:29.934 | INFO     | app.services.invite_service:get_invite_tasks:121 - 获取邀请任务列表, status=None
2025-06-27 22:50:29.934 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:50:29.963 | INFO     | app.services.invite_service:task_initialize:39 - 初始化邀请服务，重置运行中的任务为暂停状态
2025-06-27 22:50:29.963 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:50:29.981 | INFO     | utils.vip_checker:start_checking:38 - 开始VIP状态检查
2025-06-27 22:50:29.981 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:50:29.982 | DEBUG    | utils.vip_checker:_check_vip_status:62 - 正在检查VIP状态...
2025-06-27 22:50:29.983 | INFO     | core.auth.api_service:verify_vip:425 - 会员验证接口调用
2025-06-27 22:50:29.984 | INFO     | core.proxy.proxy_task_manager:task_wrapper:199 - 任务 refresh_proxies_0_20 完成
2025-06-27 22:50:30.084 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:50:30.088 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:50:30.108 | DEBUG    | ui.views.monitor_view:_refresh_task_list:175 - 刷新任务列表，共0个任务
2025-06-27 22:50:30.108 | INFO     | ui.views.monitor_view:_refresh_task_list:189 - 没有找到监控任务
2025-06-27 22:50:30.108 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:50:30.111 | INFO     | app.services.invite_service:task_initialize:48 - 成功重置 0 个任务状态
2025-06-27 22:50:30.112 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:50:30.119 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:50:30.128 | INFO     | app.services.account_service:get_all_groups:96 - 获取所有账户分组成功, 共 2 个
2025-06-27 22:50:30.128 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:50:30.129 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:50:30.132 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:50:30.141 | DEBUG    | ui.views.account_view:_on_groups_loaded:446 - 分组加载完成: 2个分组
2025-06-27 22:50:30.156 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:50:30.239 | INFO     | app.services.account_service:get_all_accounts:290 - 获取所有账户成功, 共 2 个
2025-06-27 22:50:30.239 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:50:30.240 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:50:30.241 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:50:30.245 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-06-27 22:50:30.259 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:50:30.262 | INFO     | app.controllers.telegram_monitor_controller:_load_total_users_count_internal:191 - 成功加载总体统计数据: 总用户数 23, 今日采集 0, 日均采集 23, 运行天数 1
2025-06-27 22:50:30.262 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:156 - 成功加载 0 个任务
2025-06-27 22:50:30.263 | DEBUG    | ui.views.monitor_view:_on_loading_finished:625 - 加载完成
2025-06-27 22:50:30.263 | DEBUG    | ui.views.account_view:_on_accounts_loaded:579 - 账户加载完成: 2个账户
2025-06-27 22:50:30.285 | DEBUG    | ui.views.account_view:_update_group_counts:185 - 分组计数已更新: {2: 1, 1: 1}
2025-06-27 22:50:30.291 | INFO     | ui.views.account_view:_auto_login_accounts:634 - 开始调用控制器进行 2 个账户的自动登录。
2025-06-27 22:50:30.291 | INFO     | app.services.account_service:batch_auto_login:1165 - 服务层：开始批量自动登录 2 个账户, 最大并发: 5
2025-06-27 22:50:30.291 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:50:30.310 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=True
2025-06-27 22:50:30.312 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=True
2025-06-27 22:50:30.314 | INFO     | ui.views.proxy_view:_load_initial_data:1044 - 初始数据加载完成，共 1 个代理
2025-06-27 22:50:30.323 | INFO     | app.services.account_service:get_all_accounts:290 - 获取所有账户成功, 共 2 个
2025-06-27 22:50:30.323 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:50:30.324 | INFO     | app.services.account_service:batch_auto_login:1204 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-06-27 22:50:30.324 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:50:30.328 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:50:30.331 | DEBUG    | ui.views.account_view:_update_group_counts:185 - 分组计数已更新: {2: 1, 1: 1}
2025-06-27 22:50:30.331 | DEBUG    | ui.views.account_view:_init_cache:152 - 账户缓存初始化完成，共 2 个账户
2025-06-27 22:50:30.336 | DEBUG    | ui.views.account_view:_on_accounts_loaded:579 - 账户加载完成: 2个账户
2025-06-27 22:50:30.364 | DEBUG    | ui.views.account_view:_update_group_counts:185 - 分组计数已更新: {2: 1, 1: 1}
2025-06-27 22:50:30.372 | WARNING  | core.auth.api_service:verify_vip:440 - 会员验证失败: 验证失败
2025-06-27 22:50:30.373 | ERROR    | utils.vip_checker:_check_vip_status:82 - VIP验证失败: 验证失败
2025-06-27 22:50:34.307 | ERROR    | ui.main_window:on_login_succeeded:309 - 检查VIP状态时出错: 'bool' object has no attribute 'get'
2025-06-27 22:51:29.135 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-27 22:51:29.146 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:51:29.149 | ERROR    | data.repositories.message_repo:get_task_target_stats:187 - 获取任务6目标统计失败: coroutine ignored GeneratorExit
2025-06-27 22:51:29.149 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:51:29.149 | ERROR    | ui.views.send_msg_view:load_tasks:50 - 加载任务列表失败: (sqlite3.OperationalError) no active connection
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-27 22:52:00.805 | INFO     | utils.logger:init_logger:98 - 日志系统初始化完成，默认级别: DEBUG，日志目录: H:\PyProject\TeleTest\APPDATA\logs
2025-06-27 22:52:02.550 | INFO     | data.database:init_database:31 - 初始化数据库连接: H:\PyProject\TeleTest\APPDATA\database\telegram_manager.db
2025-06-27 22:52:02.585 | DEBUG    | data.database:init_database:58 - 创建数据库表
2025-06-27 22:52:02.609 | INFO     | data.database:init_database:61 - 数据库初始化完成
2025-06-27 22:52:03.187 | DEBUG    | app.controllers.auth_controller:initialize_software:43 - 控制器: 初始化软件 app_id=1000, version=1.2.7
2025-06-27 22:52:03.187 | DEBUG    | core.auth.api_service:init_software:55 - 发送初始化请求: https://server.xile188.com/api/user/1000/windowns10/1.2.7/ini, params={}
2025-06-27 22:52:03.474 | DEBUG    | core.auth.api_service:init_software:66 - 初始化数据解密成功: {'new_content': '- 新增功能\n  - 1.新增图片上传功能\n  - 2.新增软件文档\n  - 3.新增软件自动更新，后续更新会自动检测版本更新，无需再下载更新包\n  - 4.新增设置账户信息及发信控制\n- 修复bug\n  - 删除监听任务时，不删除对应任务采集到的数据。\n  - 监听设置群无法发送到目标群\n  - 导出监听数据时应当导出单个任务而非所有数据\n- 优化记录\n  - 优化发信规则，直接调取符合要求的账户发送，无账户时才需要等待延时。\n  - 完善软件设置', 'new_url': None, 'notice': {'id': '2', 'content': '公告内容测试3', 'time': '**********'}}
2025-06-27 22:52:03.482 | INFO     | app.controllers.auth_controller:login:61 - 控制器: 用户登录 account=demo01
2025-06-27 22:52:06.498 | INFO     | core.auth.api_service:login:127 - 用户登录: account=demo01
2025-06-27 22:52:06.766 | INFO     | core.auth.api_service:get_user_info:347 - 获取用户信息
2025-06-27 22:52:07.024 | INFO     | core.auth.api_service:get_user_info:375 - 获取用户信息成功
2025-06-27 22:52:07.030 | INFO     | __main__:on_auth_completed:93 - 用户登录成功，正在启动主窗口...
2025-06-27 22:52:07.063 | INFO     | core.telegram.client_worker:__init__:61 - Telegram客户端工作线程初始化
2025-06-27 22:52:07.063 | INFO     | utils.vip_checker:initialize:26 - VIP检查器已初始化，等待登录后开始检查
2025-06-27 22:52:07.064 | INFO     | ui.main_window:_initialize_core_components:107 - MainWindow: 初始化核心组件...
2025-06-27 22:52:07.064 | INFO     | core.telegram.client_worker:run:65 - Telegram客户端工作线程开始运行
2025-06-27 22:52:07.065 | INFO     | app.services.account_service:__init__:42 - 账户服务初始化
2025-06-27 22:52:07.065 | INFO     | core.proxy.proxy_task_manager:run:284 - 代理任务管理器线程启动
2025-06-27 22:52:07.066 | INFO     | app.controllers.account_controller:__init__:81 - 账户控制器初始化
2025-06-27 22:52:07.066 | INFO     | app.services.monitor_service:__init__:39 - 初始化监控任务服务
2025-06-27 22:52:07.067 | INFO     | core.monitor.filter:__init__:26 - 初始化消息过滤器
2025-06-27 22:52:07.067 | INFO     | core.monitor.extractor:__init__:24 - 初始化关键词提取器
2025-06-27 22:52:07.068 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-06-27 22:52:07.069 | INFO     | core.monitor.notifier:__init__:90 - 初始化通知管理器
2025-06-27 22:52:07.069 | INFO     | core.telegram.client_manager:__init__:66 - Telegram客户端管理器初始化
2025-06-27 22:52:07.069 | INFO     | app.controllers.telegram_monitor_controller:__init__:64 - 初始化 Telegram 监控控制器
2025-06-27 22:52:07.069 | INFO     | core.telegram.user_manager:__init__:30 - Telegram用户管理器初始化
2025-06-27 22:52:07.069 | DEBUG    | app.controllers.telegram_monitor_controller:__init__:82 - 控制器初始化完成
2025-06-27 22:52:07.070 | INFO     | core.telegram.group_manager:__init__:36 - Telegram群组管理器初始化
2025-06-27 22:52:07.071 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-06-27 22:52:07.072 | INFO     | core.telegram.monitor_manager:__init__:26 - Telegram监控管理器初始化
2025-06-27 22:52:07.073 | INFO     | core.telegram.telegram_service:__init__:39 - Telegram服务初始化完成
2025-06-27 22:52:07.305 | INFO     | ui.views.monitor_view:__init__:48 - 初始化监控视图
2025-06-27 22:52:07.306 | DEBUG    | ui.views.monitor_view:_connect_signals:80 - 连接控制器信号
2025-06-27 22:52:07.504 | INFO     | ui.views.proxy_view:__init__:96 - 初始化视图，加载第一页代理数据
2025-06-27 22:52:07.769 | INFO     | __main__:on_auth_completed:97 - 主窗口已启动
2025-06-27 22:52:07.813 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:88 - 尝试连接监听日志信号...
2025-06-27 22:52:07.814 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:90 - 监听日志信号连接成功
2025-06-27 22:52:07.814 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:52:07.819 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:52:07.822 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:145 - 加载监控任务列表 (搜索关键词: '')
2025-06-27 22:52:07.822 | DEBUG    | ui.views.monitor_view:_on_loading_started:620 - 加载中: 正在加载任务列表...
2025-06-27 22:52:07.823 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:52:07.831 | INFO     | app.services.proxy_service:refresh_proxies:712 - 刷新代理列表: offset=0, limit=20
2025-06-27 22:52:07.831 | INFO     | core.proxy.proxy_task_manager:submit_task:172 - 提交耗时任务到子线程: refresh_proxies_0_20
2025-06-27 22:52:07.831 | DEBUG    | app.services.proxy_service:get_proxies:500 - 获取代理列表: offset=0, limit=20
2025-06-27 22:52:07.831 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:52:07.833 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:52:07.838 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:52:07.848 | INFO     | app.controllers.invite_controller:get_invite_tasks:38 - 获取邀请任务列表, status=None
2025-06-27 22:52:07.877 | INFO     | app.services.invite_service:get_invite_tasks:121 - 获取邀请任务列表, status=None
2025-06-27 22:52:07.883 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:52:07.894 | INFO     | app.services.invite_service:task_initialize:39 - 初始化邀请服务，重置运行中的任务为暂停状态
2025-06-27 22:52:07.895 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:52:07.902 | INFO     | utils.vip_checker:start_checking:38 - 开始VIP状态检查
2025-06-27 22:52:07.902 | DEBUG    | utils.vip_checker:_check_vip_status:62 - 正在检查VIP状态...
2025-06-27 22:52:07.902 | INFO     | core.auth.api_service:verify_vip:425 - 会员验证接口调用
2025-06-27 22:52:07.903 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:52:07.907 | INFO     | core.proxy.proxy_task_manager:task_wrapper:199 - 任务 refresh_proxies_0_20 完成
2025-06-27 22:52:08.004 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:52:08.010 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:52:08.026 | DEBUG    | ui.views.monitor_view:_refresh_task_list:175 - 刷新任务列表，共0个任务
2025-06-27 22:52:08.027 | INFO     | ui.views.monitor_view:_refresh_task_list:189 - 没有找到监控任务
2025-06-27 22:52:08.027 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:52:08.030 | INFO     | app.services.invite_service:task_initialize:48 - 成功重置 0 个任务状态
2025-06-27 22:52:08.031 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:52:08.031 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:52:08.044 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:52:08.047 | INFO     | app.services.account_service:get_all_groups:96 - 获取所有账户分组成功, 共 2 个
2025-06-27 22:52:08.048 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:52:08.053 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:52:08.058 | DEBUG    | ui.views.account_view:_on_groups_loaded:446 - 分组加载完成: 2个分组
2025-06-27 22:52:08.075 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:52:08.155 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:52:08.157 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:52:08.158 | INFO     | app.services.account_service:get_all_accounts:290 - 获取所有账户成功, 共 2 个
2025-06-27 22:52:08.158 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:52:08.161 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:52:08.162 | DEBUG    | ui.views.account_view:_on_accounts_loaded:579 - 账户加载完成: 2个账户
2025-06-27 22:52:08.182 | DEBUG    | ui.views.account_view:_update_group_counts:185 - 分组计数已更新: {2: 1, 1: 1}
2025-06-27 22:52:08.186 | INFO     | ui.views.account_view:_auto_login_accounts:634 - 开始调用控制器进行 2 个账户的自动登录。
2025-06-27 22:52:08.186 | INFO     | app.services.account_service:batch_auto_login:1165 - 服务层：开始批量自动登录 2 个账户, 最大并发: 5
2025-06-27 22:52:08.186 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:52:08.190 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-06-27 22:52:08.206 | INFO     | app.controllers.telegram_monitor_controller:_load_total_users_count_internal:191 - 成功加载总体统计数据: 总用户数 23, 今日采集 0, 日均采集 23, 运行天数 1
2025-06-27 22:52:08.207 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:156 - 成功加载 0 个任务
2025-06-27 22:52:08.208 | DEBUG    | ui.views.monitor_view:_on_loading_finished:625 - 加载完成
2025-06-27 22:52:08.231 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=True
2025-06-27 22:52:08.234 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=True
2025-06-27 22:52:08.236 | INFO     | ui.views.proxy_view:_load_initial_data:1044 - 初始数据加载完成，共 1 个代理
2025-06-27 22:52:08.239 | INFO     | app.services.account_service:get_all_accounts:290 - 获取所有账户成功, 共 2 个
2025-06-27 22:52:08.239 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:52:08.242 | INFO     | app.services.account_service:batch_auto_login:1204 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-06-27 22:52:08.243 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:52:08.246 | DEBUG    | ui.views.account_view:_update_group_counts:185 - 分组计数已更新: {2: 1, 1: 1}
2025-06-27 22:52:08.246 | DEBUG    | ui.views.account_view:_init_cache:152 - 账户缓存初始化完成，共 2 个账户
2025-06-27 22:52:08.251 | DEBUG    | ui.views.account_view:_on_accounts_loaded:579 - 账户加载完成: 2个账户
2025-06-27 22:52:08.280 | DEBUG    | ui.views.account_view:_update_group_counts:185 - 分组计数已更新: {2: 1, 1: 1}
2025-06-27 22:52:08.287 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:52:08.293 | WARNING  | core.auth.api_service:verify_vip:440 - 会员验证失败: 验证失败
2025-06-27 22:52:08.293 | ERROR    | utils.vip_checker:_check_vip_status:82 - VIP验证失败: 验证失败
2025-06-27 22:52:37.759 | ERROR    | ui.main_window:on_login_succeeded:310 - 检查VIP状态时出错: 'bool' object has no attribute 'get'
2025-06-27 22:53:07.021 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-27 22:53:07.027 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:53:07.031 | ERROR    | data.repositories.message_repo:get_task_target_stats:187 - 获取任务6目标统计失败: coroutine ignored GeneratorExit
2025-06-27 22:53:07.031 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:53:07.032 | ERROR    | ui.views.send_msg_view:load_tasks:50 - 加载任务列表失败: (sqlite3.OperationalError) no active connection
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-27 22:53:20.033 | INFO     | utils.logger:init_logger:98 - 日志系统初始化完成，默认级别: DEBUG，日志目录: H:\PyProject\TeleTest\APPDATA\logs
2025-06-27 22:53:21.863 | INFO     | data.database:init_database:31 - 初始化数据库连接: H:\PyProject\TeleTest\APPDATA\database\telegram_manager.db
2025-06-27 22:53:21.894 | DEBUG    | data.database:init_database:58 - 创建数据库表
2025-06-27 22:53:21.914 | INFO     | data.database:init_database:61 - 数据库初始化完成
2025-06-27 22:53:22.476 | DEBUG    | app.controllers.auth_controller:initialize_software:43 - 控制器: 初始化软件 app_id=1000, version=1.2.7
2025-06-27 22:53:22.476 | DEBUG    | core.auth.api_service:init_software:55 - 发送初始化请求: https://server.xile188.com/api/user/1000/windowns10/1.2.7/ini, params={}
2025-06-27 22:53:22.752 | DEBUG    | core.auth.api_service:init_software:66 - 初始化数据解密成功: {'new_content': '- 新增功能\n  - 1.新增图片上传功能\n  - 2.新增软件文档\n  - 3.新增软件自动更新，后续更新会自动检测版本更新，无需再下载更新包\n  - 4.新增设置账户信息及发信控制\n- 修复bug\n  - 删除监听任务时，不删除对应任务采集到的数据。\n  - 监听设置群无法发送到目标群\n  - 导出监听数据时应当导出单个任务而非所有数据\n- 优化记录\n  - 优化发信规则，直接调取符合要求的账户发送，无账户时才需要等待延时。\n  - 完善软件设置', 'new_url': None, 'notice': {'id': '2', 'content': '公告内容测试3', 'time': '**********'}}
2025-06-27 22:53:22.759 | INFO     | app.controllers.auth_controller:login:61 - 控制器: 用户登录 account=demo01
2025-06-27 22:53:25.762 | INFO     | core.auth.api_service:login:127 - 用户登录: account=demo01
2025-06-27 22:53:26.125 | INFO     | core.auth.api_service:get_user_info:347 - 获取用户信息
2025-06-27 22:53:26.422 | INFO     | core.auth.api_service:get_user_info:375 - 获取用户信息成功
2025-06-27 22:53:26.428 | INFO     | __main__:on_auth_completed:93 - 用户登录成功，正在启动主窗口...
2025-06-27 22:53:26.456 | INFO     | core.telegram.client_worker:__init__:61 - Telegram客户端工作线程初始化
2025-06-27 22:53:26.456 | INFO     | utils.vip_checker:initialize:26 - VIP检查器已初始化，等待登录后开始检查
2025-06-27 22:53:26.457 | INFO     | ui.main_window:_initialize_core_components:107 - MainWindow: 初始化核心组件...
2025-06-27 22:53:26.458 | INFO     | core.telegram.client_worker:run:65 - Telegram客户端工作线程开始运行
2025-06-27 22:53:26.459 | INFO     | app.services.account_service:__init__:42 - 账户服务初始化
2025-06-27 22:53:26.459 | INFO     | core.proxy.proxy_task_manager:run:284 - 代理任务管理器线程启动
2025-06-27 22:53:26.460 | INFO     | app.controllers.account_controller:__init__:81 - 账户控制器初始化
2025-06-27 22:53:26.460 | INFO     | app.services.monitor_service:__init__:39 - 初始化监控任务服务
2025-06-27 22:53:26.460 | INFO     | core.monitor.filter:__init__:26 - 初始化消息过滤器
2025-06-27 22:53:26.461 | INFO     | core.monitor.extractor:__init__:24 - 初始化关键词提取器
2025-06-27 22:53:26.461 | INFO     | core.telegram.client_manager:__init__:66 - Telegram客户端管理器初始化
2025-06-27 22:53:26.461 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-06-27 22:53:26.462 | INFO     | core.telegram.user_manager:__init__:30 - Telegram用户管理器初始化
2025-06-27 22:53:26.462 | INFO     | core.monitor.notifier:__init__:90 - 初始化通知管理器
2025-06-27 22:53:26.462 | INFO     | core.telegram.group_manager:__init__:36 - Telegram群组管理器初始化
2025-06-27 22:53:26.462 | INFO     | app.controllers.telegram_monitor_controller:__init__:64 - 初始化 Telegram 监控控制器
2025-06-27 22:53:26.462 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-06-27 22:53:26.463 | DEBUG    | app.controllers.telegram_monitor_controller:__init__:82 - 控制器初始化完成
2025-06-27 22:53:26.463 | INFO     | core.telegram.monitor_manager:__init__:26 - Telegram监控管理器初始化
2025-06-27 22:53:26.465 | INFO     | core.telegram.telegram_service:__init__:39 - Telegram服务初始化完成
2025-06-27 22:53:26.697 | INFO     | ui.views.monitor_view:__init__:48 - 初始化监控视图
2025-06-27 22:53:26.698 | DEBUG    | ui.views.monitor_view:_connect_signals:80 - 连接控制器信号
2025-06-27 22:53:26.886 | INFO     | ui.views.proxy_view:__init__:96 - 初始化视图，加载第一页代理数据
2025-06-27 22:53:27.130 | INFO     | __main__:on_auth_completed:97 - 主窗口已启动
2025-06-27 22:53:27.170 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:88 - 尝试连接监听日志信号...
2025-06-27 22:53:27.171 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:90 - 监听日志信号连接成功
2025-06-27 22:53:27.171 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:53:27.182 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:53:27.194 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:145 - 加载监控任务列表 (搜索关键词: '')
2025-06-27 22:53:27.195 | DEBUG    | ui.views.monitor_view:_on_loading_started:620 - 加载中: 正在加载任务列表...
2025-06-27 22:53:27.195 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:53:27.206 | INFO     | app.services.proxy_service:refresh_proxies:712 - 刷新代理列表: offset=0, limit=20
2025-06-27 22:53:27.207 | INFO     | core.proxy.proxy_task_manager:submit_task:172 - 提交耗时任务到子线程: refresh_proxies_0_20
2025-06-27 22:53:27.207 | DEBUG    | app.services.proxy_service:get_proxies:500 - 获取代理列表: offset=0, limit=20
2025-06-27 22:53:27.207 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:53:27.208 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:53:27.219 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:53:27.280 | INFO     | app.controllers.invite_controller:get_invite_tasks:38 - 获取邀请任务列表, status=None
2025-06-27 22:53:27.281 | INFO     | app.services.invite_service:get_invite_tasks:121 - 获取邀请任务列表, status=None
2025-06-27 22:53:27.281 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:53:27.286 | INFO     | app.services.invite_service:task_initialize:39 - 初始化邀请服务，重置运行中的任务为暂停状态
2025-06-27 22:53:27.286 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:53:27.289 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:53:27.292 | INFO     | core.proxy.proxy_task_manager:task_wrapper:199 - 任务 refresh_proxies_0_20 完成
2025-06-27 22:53:27.294 | INFO     | utils.vip_checker:start_checking:38 - 开始VIP状态检查
2025-06-27 22:53:27.294 | DEBUG    | utils.vip_checker:_check_vip_status:62 - 正在检查VIP状态...
2025-06-27 22:53:27.294 | INFO     | core.auth.api_service:verify_vip:425 - 会员验证接口调用
2025-06-27 22:53:27.411 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:53:27.419 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:53:27.439 | INFO     | app.services.invite_service:task_initialize:48 - 成功重置 0 个任务状态
2025-06-27 22:53:27.439 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:53:27.441 | DEBUG    | ui.views.monitor_view:_refresh_task_list:175 - 刷新任务列表，共0个任务
2025-06-27 22:53:27.442 | INFO     | ui.views.monitor_view:_refresh_task_list:189 - 没有找到监控任务
2025-06-27 22:53:27.442 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:53:27.448 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:53:27.462 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:53:27.466 | INFO     | app.services.account_service:get_all_groups:96 - 获取所有账户分组成功, 共 2 个
2025-06-27 22:53:27.466 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:53:27.471 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:53:27.481 | DEBUG    | ui.views.account_view:_on_groups_loaded:446 - 分组加载完成: 2个分组
2025-06-27 22:53:27.503 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:53:27.600 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:53:27.602 | INFO     | app.services.account_service:get_all_accounts:290 - 获取所有账户成功, 共 2 个
2025-06-27 22:53:27.602 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:53:27.602 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:53:27.608 | INFO     | app.controllers.telegram_monitor_controller:_load_total_users_count_internal:191 - 成功加载总体统计数据: 总用户数 23, 今日采集 0, 日均采集 23, 运行天数 1
2025-06-27 22:53:27.608 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:156 - 成功加载 0 个任务
2025-06-27 22:53:27.608 | DEBUG    | ui.views.monitor_view:_on_loading_finished:625 - 加载完成
2025-06-27 22:53:27.609 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:53:27.611 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-06-27 22:53:27.615 | DEBUG    | ui.views.account_view:_on_accounts_loaded:579 - 账户加载完成: 2个账户
2025-06-27 22:53:27.644 | DEBUG    | ui.views.account_view:_update_group_counts:185 - 分组计数已更新: {2: 1, 1: 1}
2025-06-27 22:53:27.649 | INFO     | ui.views.account_view:_auto_login_accounts:634 - 开始调用控制器进行 2 个账户的自动登录。
2025-06-27 22:53:27.649 | INFO     | app.services.account_service:batch_auto_login:1165 - 服务层：开始批量自动登录 2 个账户, 最大并发: 5
2025-06-27 22:53:27.650 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:53:27.687 | INFO     | app.services.account_service:get_all_accounts:290 - 获取所有账户成功, 共 2 个
2025-06-27 22:53:27.687 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:53:27.689 | INFO     | app.services.account_service:batch_auto_login:1204 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-06-27 22:53:27.689 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:53:27.693 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=True
2025-06-27 22:53:27.695 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=True
2025-06-27 22:53:27.697 | INFO     | ui.views.proxy_view:_load_initial_data:1044 - 初始数据加载完成，共 1 个代理
2025-06-27 22:53:27.700 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:53:27.704 | DEBUG    | ui.views.account_view:_update_group_counts:185 - 分组计数已更新: {2: 1, 1: 1}
2025-06-27 22:53:27.705 | DEBUG    | ui.views.account_view:_init_cache:152 - 账户缓存初始化完成，共 2 个账户
2025-06-27 22:53:27.711 | DEBUG    | ui.views.account_view:_on_accounts_loaded:579 - 账户加载完成: 2个账户
2025-06-27 22:53:27.735 | DEBUG    | ui.views.account_view:_update_group_counts:185 - 分组计数已更新: {2: 1, 1: 1}
2025-06-27 22:53:27.745 | WARNING  | core.auth.api_service:verify_vip:440 - 会员验证失败: 验证失败
2025-06-27 22:53:27.745 | ERROR    | utils.vip_checker:_check_vip_status:82 - VIP验证失败: 验证失败
2025-06-27 22:53:57.837 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:53:57.843 | ERROR    | data.repositories.message_repo:get_task_target_stats:187 - 获取任务6目标统计失败: coroutine ignored GeneratorExit
2025-06-27 22:53:57.843 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:53:57.844 | ERROR    | ui.views.send_msg_view:load_tasks:50 - 加载任务列表失败: (sqlite3.OperationalError) no active connection
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-27 22:55:29.594 | INFO     | utils.logger:init_logger:98 - 日志系统初始化完成，默认级别: DEBUG，日志目录: H:\PyProject\TeleTest\APPDATA\logs
2025-06-27 22:55:35.858 | INFO     | data.database:init_database:31 - 初始化数据库连接: H:\PyProject\TeleTest\APPDATA\database\telegram_manager.db
2025-06-27 22:55:35.889 | DEBUG    | data.database:init_database:58 - 创建数据库表
2025-06-27 22:55:35.914 | INFO     | data.database:init_database:61 - 数据库初始化完成
2025-06-27 22:55:36.484 | DEBUG    | app.controllers.auth_controller:initialize_software:43 - 控制器: 初始化软件 app_id=1000, version=1.2.7
2025-06-27 22:55:36.484 | DEBUG    | core.auth.api_service:init_software:55 - 发送初始化请求: https://server.xile188.com/api/user/1000/windowns10/1.2.7/ini, params={}
2025-06-27 22:55:36.869 | DEBUG    | core.auth.api_service:init_software:66 - 初始化数据解密成功: {'new_content': '- 新增功能\n  - 1.新增图片上传功能\n  - 2.新增软件文档\n  - 3.新增软件自动更新，后续更新会自动检测版本更新，无需再下载更新包\n  - 4.新增设置账户信息及发信控制\n- 修复bug\n  - 删除监听任务时，不删除对应任务采集到的数据。\n  - 监听设置群无法发送到目标群\n  - 导出监听数据时应当导出单个任务而非所有数据\n- 优化记录\n  - 优化发信规则，直接调取符合要求的账户发送，无账户时才需要等待延时。\n  - 完善软件设置', 'new_url': None, 'notice': {'id': '2', 'content': '公告内容测试3', 'time': '**********'}}
2025-06-27 22:55:36.881 | INFO     | app.controllers.auth_controller:login:61 - 控制器: 用户登录 account=demo01
2025-06-27 22:55:39.946 | INFO     | core.auth.api_service:login:127 - 用户登录: account=demo01
2025-06-27 22:55:40.252 | INFO     | core.auth.api_service:get_user_info:347 - 获取用户信息
2025-06-27 22:55:40.542 | INFO     | core.auth.api_service:get_user_info:375 - 获取用户信息成功
2025-06-27 22:55:40.549 | INFO     | __main__:on_auth_completed:93 - 用户登录成功，正在启动主窗口...
2025-06-27 22:55:40.576 | INFO     | core.telegram.client_worker:__init__:61 - Telegram客户端工作线程初始化
2025-06-27 22:55:40.577 | INFO     | utils.vip_checker:initialize:25 - VIP检查器已初始化，等待登录后开始检查
2025-06-27 22:55:40.577 | INFO     | ui.main_window:_initialize_core_components:107 - MainWindow: 初始化核心组件...
2025-06-27 22:55:40.578 | INFO     | app.services.account_service:__init__:42 - 账户服务初始化
2025-06-27 22:55:40.578 | INFO     | core.telegram.client_worker:run:65 - Telegram客户端工作线程开始运行
2025-06-27 22:55:40.578 | INFO     | app.controllers.account_controller:__init__:81 - 账户控制器初始化
2025-06-27 22:55:40.579 | INFO     | core.proxy.proxy_task_manager:run:284 - 代理任务管理器线程启动
2025-06-27 22:55:40.579 | INFO     | app.services.monitor_service:__init__:39 - 初始化监控任务服务
2025-06-27 22:55:40.579 | INFO     | core.monitor.filter:__init__:26 - 初始化消息过滤器
2025-06-27 22:55:40.580 | INFO     | core.monitor.extractor:__init__:24 - 初始化关键词提取器
2025-06-27 22:55:40.581 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-06-27 22:55:40.581 | INFO     | core.telegram.client_manager:__init__:66 - Telegram客户端管理器初始化
2025-06-27 22:55:40.581 | INFO     | core.monitor.notifier:__init__:90 - 初始化通知管理器
2025-06-27 22:55:40.581 | INFO     | core.telegram.user_manager:__init__:30 - Telegram用户管理器初始化
2025-06-27 22:55:40.581 | INFO     | app.controllers.telegram_monitor_controller:__init__:64 - 初始化 Telegram 监控控制器
2025-06-27 22:55:40.582 | INFO     | core.telegram.group_manager:__init__:36 - Telegram群组管理器初始化
2025-06-27 22:55:40.582 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-06-27 22:55:40.582 | DEBUG    | app.controllers.telegram_monitor_controller:__init__:82 - 控制器初始化完成
2025-06-27 22:55:40.582 | INFO     | core.telegram.monitor_manager:__init__:26 - Telegram监控管理器初始化
2025-06-27 22:55:40.583 | INFO     | core.telegram.telegram_service:__init__:39 - Telegram服务初始化完成
2025-06-27 22:55:40.791 | INFO     | ui.views.monitor_view:__init__:48 - 初始化监控视图
2025-06-27 22:55:40.792 | DEBUG    | ui.views.monitor_view:_connect_signals:80 - 连接控制器信号
2025-06-27 22:55:40.981 | INFO     | ui.views.proxy_view:__init__:96 - 初始化视图，加载第一页代理数据
2025-06-27 22:55:41.228 | INFO     | __main__:on_auth_completed:97 - 主窗口已启动
2025-06-27 22:55:41.272 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:88 - 尝试连接监听日志信号...
2025-06-27 22:55:41.272 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:90 - 监听日志信号连接成功
2025-06-27 22:55:41.272 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:55:41.278 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:55:41.282 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:145 - 加载监控任务列表 (搜索关键词: '')
2025-06-27 22:55:41.283 | DEBUG    | ui.views.monitor_view:_on_loading_started:620 - 加载中: 正在加载任务列表...
2025-06-27 22:55:41.283 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:55:41.290 | INFO     | app.services.proxy_service:refresh_proxies:712 - 刷新代理列表: offset=0, limit=20
2025-06-27 22:55:41.290 | INFO     | core.proxy.proxy_task_manager:submit_task:172 - 提交耗时任务到子线程: refresh_proxies_0_20
2025-06-27 22:55:41.291 | DEBUG    | app.services.proxy_service:get_proxies:500 - 获取代理列表: offset=0, limit=20
2025-06-27 22:55:41.291 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:55:41.291 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:55:41.298 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:55:41.302 | INFO     | app.controllers.invite_controller:get_invite_tasks:38 - 获取邀请任务列表, status=None
2025-06-27 22:55:41.302 | INFO     | app.services.invite_service:get_invite_tasks:121 - 获取邀请任务列表, status=None
2025-06-27 22:55:41.302 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:55:41.325 | INFO     | app.services.invite_service:task_initialize:39 - 初始化邀请服务，重置运行中的任务为暂停状态
2025-06-27 22:55:41.340 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:55:41.356 | INFO     | utils.vip_checker:start_checking:37 - 开始VIP状态检查
2025-06-27 22:55:41.360 | DEBUG    | utils.vip_checker:_check_vip_status:61 - 正在检查VIP状态...
2025-06-27 22:55:41.360 | INFO     | core.auth.api_service:verify_vip:425 - 会员验证接口调用
2025-06-27 22:55:41.363 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:55:41.365 | INFO     | core.proxy.proxy_task_manager:task_wrapper:199 - 任务 refresh_proxies_0_20 完成
2025-06-27 22:55:41.465 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:55:41.473 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:55:41.481 | DEBUG    | ui.views.monitor_view:_refresh_task_list:175 - 刷新任务列表，共0个任务
2025-06-27 22:55:41.481 | INFO     | ui.views.monitor_view:_refresh_task_list:189 - 没有找到监控任务
2025-06-27 22:55:41.482 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:55:41.484 | INFO     | app.services.invite_service:task_initialize:48 - 成功重置 0 个任务状态
2025-06-27 22:55:41.485 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:55:41.492 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:55:41.497 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:55:41.499 | INFO     | app.services.account_service:get_all_groups:96 - 获取所有账户分组成功, 共 2 个
2025-06-27 22:55:41.500 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:55:41.500 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:55:41.565 | DEBUG    | ui.views.account_view:_on_groups_loaded:446 - 分组加载完成: 2个分组
2025-06-27 22:55:41.580 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:55:41.597 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:55:41.601 | INFO     | app.controllers.telegram_monitor_controller:_load_total_users_count_internal:191 - 成功加载总体统计数据: 总用户数 23, 今日采集 0, 日均采集 23, 运行天数 1
2025-06-27 22:55:41.602 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:156 - 成功加载 0 个任务
2025-06-27 22:55:41.602 | DEBUG    | ui.views.monitor_view:_on_loading_finished:625 - 加载完成
2025-06-27 22:55:41.603 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:55:41.604 | INFO     | app.services.account_service:get_all_accounts:290 - 获取所有账户成功, 共 2 个
2025-06-27 22:55:41.604 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:55:41.607 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:55:41.608 | DEBUG    | ui.views.account_view:_on_accounts_loaded:579 - 账户加载完成: 2个账户
2025-06-27 22:55:41.628 | DEBUG    | ui.views.account_view:_update_group_counts:185 - 分组计数已更新: {2: 1, 1: 1}
2025-06-27 22:55:41.632 | INFO     | ui.views.account_view:_auto_login_accounts:634 - 开始调用控制器进行 2 个账户的自动登录。
2025-06-27 22:55:41.633 | INFO     | app.services.account_service:batch_auto_login:1165 - 服务层：开始批量自动登录 2 个账户, 最大并发: 5
2025-06-27 22:55:41.633 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:55:41.636 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-06-27 22:55:41.679 | INFO     | app.services.account_service:batch_auto_login:1204 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-06-27 22:55:41.679 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:55:41.680 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=True
2025-06-27 22:55:41.681 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=True
2025-06-27 22:55:41.683 | INFO     | ui.views.proxy_view:_load_initial_data:1044 - 初始数据加载完成，共 1 个代理
2025-06-27 22:55:41.685 | INFO     | app.services.account_service:get_all_accounts:290 - 获取所有账户成功, 共 2 个
2025-06-27 22:55:41.685 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:55:41.687 | DEBUG    | ui.views.account_view:_update_group_counts:185 - 分组计数已更新: {2: 1, 1: 1}
2025-06-27 22:55:41.688 | DEBUG    | ui.views.account_view:_init_cache:152 - 账户缓存初始化完成，共 2 个账户
2025-06-27 22:55:41.693 | DEBUG    | ui.views.account_view:_on_accounts_loaded:579 - 账户加载完成: 2个账户
2025-06-27 22:55:41.715 | DEBUG    | ui.views.account_view:_update_group_counts:185 - 分组计数已更新: {2: 1, 1: 1}
2025-06-27 22:55:41.720 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:55:41.723 | WARNING  | core.auth.api_service:verify_vip:440 - 会员验证失败: 验证失败
2025-06-27 22:55:41.723 | ERROR    | utils.vip_checker:_check_vip_status:81 - VIP验证失败: 验证失败
2025-06-27 22:56:33.648 | ERROR    | data.repositories.message_repo:get_task_target_stats:187 - 获取任务6目标统计失败: coroutine ignored GeneratorExit
2025-06-27 22:56:33.648 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:56:33.649 | ERROR    | ui.views.send_msg_view:load_tasks:50 - 加载任务列表失败: (sqlite3.OperationalError) no active connection
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-27 22:56:33.654 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:56:40.539 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-27 22:56:55.989 | INFO     | app.services.account_service:refresh_account_info:607 - 刷新账户信息: 2
2025-06-27 22:56:55.989 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:56:56.004 | INFO     | core.telegram.user_manager:get_user_info:41 - 正在获取用户信息: +***********
2025-06-27 22:56:56.004 | WARNING  | core.telegram.user_manager:get_user_info:47 - 获取用户信息失败, 找不到客户端: +***********
2025-06-27 22:56:56.149 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:57:25.687 | INFO     | utils.logger:init_logger:98 - 日志系统初始化完成，默认级别: DEBUG，日志目录: H:\PyProject\TeleTest\APPDATA\logs
2025-06-27 22:57:27.520 | INFO     | data.database:init_database:31 - 初始化数据库连接: H:\PyProject\TeleTest\APPDATA\database\telegram_manager.db
2025-06-27 22:57:27.550 | DEBUG    | data.database:init_database:58 - 创建数据库表
2025-06-27 22:57:27.569 | INFO     | data.database:init_database:61 - 数据库初始化完成
2025-06-27 22:57:28.339 | DEBUG    | app.controllers.auth_controller:initialize_software:43 - 控制器: 初始化软件 app_id=1000, version=1.2.7
2025-06-27 22:57:28.340 | DEBUG    | core.auth.api_service:init_software:55 - 发送初始化请求: https://server.xile188.com/api/user/1000/windowns10/1.2.7/ini, params={}
2025-06-27 22:57:28.642 | DEBUG    | core.auth.api_service:init_software:66 - 初始化数据解密成功: {'new_content': '- 新增功能\n  - 1.新增图片上传功能\n  - 2.新增软件文档\n  - 3.新增软件自动更新，后续更新会自动检测版本更新，无需再下载更新包\n  - 4.新增设置账户信息及发信控制\n- 修复bug\n  - 删除监听任务时，不删除对应任务采集到的数据。\n  - 监听设置群无法发送到目标群\n  - 导出监听数据时应当导出单个任务而非所有数据\n- 优化记录\n  - 优化发信规则，直接调取符合要求的账户发送，无账户时才需要等待延时。\n  - 完善软件设置', 'new_url': None, 'notice': {'id': '2', 'content': '公告内容测试3', 'time': '**********'}}
2025-06-27 22:57:28.649 | INFO     | app.controllers.auth_controller:login:61 - 控制器: 用户登录 account=demo01
2025-06-27 22:57:31.675 | INFO     | core.auth.api_service:login:127 - 用户登录: account=demo01
2025-06-27 22:57:31.966 | INFO     | core.auth.api_service:get_user_info:347 - 获取用户信息
2025-06-27 22:57:32.174 | INFO     | core.auth.api_service:get_user_info:375 - 获取用户信息成功
2025-06-27 22:57:32.182 | INFO     | __main__:on_auth_completed:93 - 用户登录成功，正在启动主窗口...
2025-06-27 22:57:32.219 | INFO     | core.telegram.client_worker:__init__:61 - Telegram客户端工作线程初始化
2025-06-27 22:57:32.219 | INFO     | utils.vip_checker:initialize:25 - VIP检查器已初始化，等待登录后开始检查
2025-06-27 22:57:32.220 | INFO     | ui.main_window:_initialize_core_components:107 - MainWindow: 初始化核心组件...
2025-06-27 22:57:32.220 | INFO     | app.services.account_service:__init__:42 - 账户服务初始化
2025-06-27 22:57:32.221 | INFO     | core.proxy.proxy_task_manager:run:284 - 代理任务管理器线程启动
2025-06-27 22:57:32.221 | INFO     | core.telegram.client_worker:run:65 - Telegram客户端工作线程开始运行
2025-06-27 22:57:32.221 | INFO     | app.controllers.account_controller:__init__:81 - 账户控制器初始化
2025-06-27 22:57:32.222 | INFO     | app.services.monitor_service:__init__:39 - 初始化监控任务服务
2025-06-27 22:57:32.223 | INFO     | core.monitor.filter:__init__:26 - 初始化消息过滤器
2025-06-27 22:57:32.223 | INFO     | core.monitor.extractor:__init__:24 - 初始化关键词提取器
2025-06-27 22:57:32.223 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-06-27 22:57:32.224 | INFO     | core.monitor.notifier:__init__:90 - 初始化通知管理器
2025-06-27 22:57:32.224 | INFO     | core.telegram.client_manager:__init__:66 - Telegram客户端管理器初始化
2025-06-27 22:57:32.224 | INFO     | app.controllers.telegram_monitor_controller:__init__:64 - 初始化 Telegram 监控控制器
2025-06-27 22:57:32.224 | INFO     | core.telegram.user_manager:__init__:30 - Telegram用户管理器初始化
2025-06-27 22:57:32.225 | DEBUG    | app.controllers.telegram_monitor_controller:__init__:82 - 控制器初始化完成
2025-06-27 22:57:32.225 | INFO     | core.telegram.group_manager:__init__:36 - Telegram群组管理器初始化
2025-06-27 22:57:32.226 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-06-27 22:57:32.227 | INFO     | core.telegram.monitor_manager:__init__:26 - Telegram监控管理器初始化
2025-06-27 22:57:32.228 | INFO     | core.telegram.telegram_service:__init__:39 - Telegram服务初始化完成
2025-06-27 22:57:32.446 | INFO     | ui.views.monitor_view:__init__:48 - 初始化监控视图
2025-06-27 22:57:32.447 | DEBUG    | ui.views.monitor_view:_connect_signals:80 - 连接控制器信号
2025-06-27 22:57:32.638 | INFO     | ui.views.proxy_view:__init__:96 - 初始化视图，加载第一页代理数据
2025-06-27 22:57:32.897 | INFO     | __main__:on_auth_completed:97 - 主窗口已启动
2025-06-27 22:57:32.941 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:88 - 尝试连接监听日志信号...
2025-06-27 22:57:32.941 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:90 - 监听日志信号连接成功
2025-06-27 22:57:32.941 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:57:32.952 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:57:32.956 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:145 - 加载监控任务列表 (搜索关键词: '')
2025-06-27 22:57:32.957 | DEBUG    | ui.views.monitor_view:_on_loading_started:620 - 加载中: 正在加载任务列表...
2025-06-27 22:57:32.957 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:57:32.967 | INFO     | app.services.proxy_service:refresh_proxies:712 - 刷新代理列表: offset=0, limit=20
2025-06-27 22:57:32.967 | INFO     | core.proxy.proxy_task_manager:submit_task:172 - 提交耗时任务到子线程: refresh_proxies_0_20
2025-06-27 22:57:32.968 | DEBUG    | app.services.proxy_service:get_proxies:500 - 获取代理列表: offset=0, limit=20
2025-06-27 22:57:32.968 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:57:32.968 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:57:32.977 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:57:33.030 | INFO     | app.controllers.invite_controller:get_invite_tasks:38 - 获取邀请任务列表, status=None
2025-06-27 22:57:33.030 | INFO     | app.services.invite_service:get_invite_tasks:121 - 获取邀请任务列表, status=None
2025-06-27 22:57:33.030 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:57:33.034 | INFO     | app.services.invite_service:task_initialize:39 - 初始化邀请服务，重置运行中的任务为暂停状态
2025-06-27 22:57:33.035 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:57:33.037 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:57:33.039 | INFO     | core.proxy.proxy_task_manager:task_wrapper:199 - 任务 refresh_proxies_0_20 完成
2025-06-27 22:57:33.041 | INFO     | utils.vip_checker:start_checking:37 - 开始VIP状态检查
2025-06-27 22:57:33.041 | DEBUG    | utils.vip_checker:_check_vip_status:61 - 正在检查VIP状态...
2025-06-27 22:57:33.041 | INFO     | core.auth.api_service:verify_vip:425 - 会员验证接口调用
2025-06-27 22:57:33.151 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:57:33.161 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:57:33.172 | DEBUG    | ui.views.monitor_view:_refresh_task_list:175 - 刷新任务列表，共0个任务
2025-06-27 22:57:33.173 | INFO     | ui.views.monitor_view:_refresh_task_list:189 - 没有找到监控任务
2025-06-27 22:57:33.173 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:57:33.175 | INFO     | app.services.invite_service:task_initialize:48 - 成功重置 0 个任务状态
2025-06-27 22:57:33.176 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:57:33.184 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:57:33.191 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:57:33.200 | INFO     | app.services.account_service:get_all_groups:96 - 获取所有账户分组成功, 共 2 个
2025-06-27 22:57:33.200 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:57:33.203 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:57:33.206 | DEBUG    | ui.views.account_view:_on_groups_loaded:446 - 分组加载完成: 2个分组
2025-06-27 22:57:33.221 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:57:33.298 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:57:33.303 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:57:33.304 | INFO     | app.controllers.telegram_monitor_controller:_load_total_users_count_internal:191 - 成功加载总体统计数据: 总用户数 23, 今日采集 0, 日均采集 23, 运行天数 1
2025-06-27 22:57:33.305 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:156 - 成功加载 0 个任务
2025-06-27 22:57:33.305 | DEBUG    | ui.views.monitor_view:_on_loading_finished:625 - 加载完成
2025-06-27 22:57:33.307 | INFO     | app.services.account_service:get_all_accounts:290 - 获取所有账户成功, 共 2 个
2025-06-27 22:57:33.307 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:57:33.310 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-06-27 22:57:33.328 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:57:33.330 | DEBUG    | ui.views.account_view:_on_accounts_loaded:579 - 账户加载完成: 2个账户
2025-06-27 22:57:33.352 | DEBUG    | ui.views.account_view:_update_group_counts:185 - 分组计数已更新: {2: 1, 1: 1}
2025-06-27 22:57:33.356 | INFO     | ui.views.account_view:_auto_login_accounts:634 - 开始调用控制器进行 2 个账户的自动登录。
2025-06-27 22:57:33.356 | INFO     | app.services.account_service:batch_auto_login:1165 - 服务层：开始批量自动登录 2 个账户, 最大并发: 5
2025-06-27 22:57:33.356 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:57:33.375 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=True
2025-06-27 22:57:33.378 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=True
2025-06-27 22:57:33.383 | INFO     | ui.views.proxy_view:_load_initial_data:1044 - 初始数据加载完成，共 1 个代理
2025-06-27 22:57:33.390 | INFO     | app.services.account_service:get_all_accounts:290 - 获取所有账户成功, 共 2 个
2025-06-27 22:57:33.390 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:57:33.393 | INFO     | app.services.account_service:batch_auto_login:1204 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-06-27 22:57:33.393 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:57:33.397 | DEBUG    | ui.views.account_view:_update_group_counts:185 - 分组计数已更新: {2: 1, 1: 1}
2025-06-27 22:57:33.397 | DEBUG    | ui.views.account_view:_init_cache:152 - 账户缓存初始化完成，共 2 个账户
2025-06-27 22:57:33.403 | DEBUG    | ui.views.account_view:_on_accounts_loaded:579 - 账户加载完成: 2个账户
2025-06-27 22:57:33.424 | DEBUG    | ui.views.account_view:_update_group_counts:185 - 分组计数已更新: {2: 1, 1: 1}
2025-06-27 22:57:33.434 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:57:33.438 | INFO     | core.auth.api_service:verify_vip:438 - 会员验证成功
2025-06-27 22:57:33.438 | INFO     | utils.vip_checker:_check_vip_status:76 - VIP状态有效
2025-06-27 22:57:33.443 | INFO     | app.services.account_service:batch_auto_login:1204 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-06-27 22:57:33.444 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:57:33.448 | INFO     | app.services.account_service:batch_auto_login:1274 - 服务层：准备调用核心层处理 2 个账户的自动登录。
2025-06-27 22:57:33.448 | INFO     | app.services.account_service:batch_auto_login:1284 - 服务层：设置核心层任务超时为 120 秒。
2025-06-27 22:57:33.449 | INFO     | core.telegram.client_manager:batch_auto_login:1216 - 开始批量自动登录, 共 2 个账户, 最大并发 5
2025-06-27 22:57:33.450 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:259 - 创建客户端: H:\PyProject\TeleTest\APPDATA\sessions\+***********, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-06-27 22:57:33.451 | DEBUG    | core.telegram.client_manager:get_proxy:96 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-06-27 22:57:33.452 | DEBUG    | core.telegram.client_manager:get_proxy:143 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-06-27 22:57:33.458 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:259 - 创建客户端: H:\PyProject\TeleTest\APPDATA\sessions\+***********, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-06-27 22:57:33.458 | DEBUG    | core.telegram.client_manager:get_proxy:96 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-06-27 22:57:33.458 | DEBUG    | core.telegram.client_manager:get_proxy:143 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-06-27 22:57:33.507 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:57:33.587 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:57:33.662 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:57:35.580 | INFO     | core.telegram.client_manager:_connect_client:207 - 连接成功!
2025-06-27 22:57:36.049 | INFO     | core.telegram.client_manager:_connect_client:207 - 连接成功!
2025-06-27 22:57:36.304 | INFO     | core.telegram.client_manager:_verify_client_authorization:1191 - 账户已授权: +***********
2025-06-27 22:57:36.827 | INFO     | core.telegram.client_manager:_verify_client_authorization:1191 - 账户已授权: +***********
2025-06-27 22:57:38.834 | INFO     | core.telegram.client_manager:batch_auto_login:1365 - 批量登录完成: 总计 2, 成功 2, 失败 0
2025-06-27 22:57:39.465 | INFO     | app.services.account_service:batch_auto_login:1305 - 服务层：核心层登录完成: 成功 2, 失败 0 / 2
2025-06-27 22:57:52.729 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:57:52.740 | INFO     | app.services.account_service:get_all_groups:96 - 获取所有账户分组成功, 共 2 个
2025-06-27 22:57:52.741 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:57:52.742 | INFO     | ui.views.add_account_view:load_account_groups:94 - 成功加载 2 个分组
2025-06-27 22:57:54.109 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 22:57:54.193 | INFO     | app.services.account_service:get_all_accounts:290 - 获取所有账户成功, 共 2 个
2025-06-27 22:57:54.194 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 22:57:54.196 | DEBUG    | ui.views.account_view:_on_accounts_loaded:579 - 账户加载完成: 2个账户
2025-06-27 22:57:54.217 | DEBUG    | ui.views.account_view:_update_group_counts:185 - 分组计数已更新: {2: 1, 1: 1}
2025-06-27 22:58:01.817 | INFO     | ui.main_window:closeEvent:378 - MainWindow: 接收到关闭事件
2025-06-27 22:58:01.827 | INFO     | ui.main_window:_cleanup_before_quit:264 - MainWindow: 执行清理资源...
2025-06-27 22:58:01.828 | INFO     | core.telegram.client_worker:stop:108 - 正在停止Telegram客户端工作线程
2025-06-27 22:58:01.839 | INFO     | core.telegram.client_manager:cleanup_async:1136 - 开始清理资源并断开所有连接
2025-06-27 22:58:01.839 | INFO     | core.telegram.client_manager:disconnect_all_clients:721 - 正在断开所有客户端连接
2025-06-27 22:58:01.840 | INFO     | core.telegram.client_manager:_safe_disconnect:742 - 正在断开客户端连接: +***********
2025-06-27 22:58:01.840 | INFO     | core.telegram.client_manager:_safe_disconnect:742 - 正在断开客户端连接: +***********
2025-06-27 22:58:01.856 | INFO     | core.telegram.client_manager:_safe_disconnect:746 - 断开客户端连接成功: +***********
2025-06-27 22:58:01.857 | INFO     | core.telegram.client_manager:_safe_disconnect:746 - 断开客户端连接成功: +***********
2025-06-27 22:58:01.857 | INFO     | core.telegram.client_manager:cleanup_async:1155 - 资源清理完成
2025-06-27 22:58:02.341 | INFO     | core.telegram.client_manager:cleanup_async:1136 - 开始清理资源并断开所有连接
2025-06-27 22:58:02.341 | INFO     | core.telegram.client_manager:disconnect_all_clients:721 - 正在断开所有客户端连接
2025-06-27 22:58:02.342 | INFO     | core.telegram.client_manager:cleanup_async:1155 - 资源清理完成
2025-06-27 22:58:02.843 | INFO     | core.telegram.client_worker:run:97 - Telegram客户端工作线程已退出
2025-06-27 22:58:02.845 | INFO     | core.telegram.client_worker:stop:122 - Telegram客户端工作线程已停止
2025-06-27 22:58:02.846 | INFO     | ui.main_window:_cleanup_before_quit:273 - TelegramClientWorker 已停止。
2025-06-27 22:58:02.846 | INFO     | ui.main_window:_cleanup_before_quit:277 - MainWindow 清理完成
2025-06-27 22:58:02.859 | INFO     | __main__:main:111 - 应用程序已正常退出
2025-06-27 23:22:04.946 | INFO     | utils.logger:init_logger:98 - 日志系统初始化完成，默认级别: DEBUG，日志目录: H:\PyProject\TeleTest\APPDATA\logs
2025-06-27 23:22:05.920 | INFO     | data.database:init_database:31 - 初始化数据库连接: H:\PyProject\TeleTest\APPDATA\database\telegram_manager.db
2025-06-27 23:22:05.936 | DEBUG    | data.database:init_database:58 - 创建数据库表
2025-06-27 23:22:05.947 | INFO     | data.database:init_database:61 - 数据库初始化完成
2025-06-27 23:22:06.547 | DEBUG    | app.controllers.auth_controller:initialize_software:43 - 控制器: 初始化软件 app_id=1000, version=1.2.7
2025-06-27 23:22:06.547 | DEBUG    | core.auth.api_service:init_software:55 - 发送初始化请求: https://server.xile188.com/api/user/1000/windowns10/1.2.7/ini, params={}
2025-06-27 23:22:07.205 | DEBUG    | core.auth.api_service:init_software:66 - 初始化数据解密成功: {'new_content': '- 新增功能\n  - 1.新增图片上传功能\n  - 2.新增软件文档\n  - 3.新增软件自动更新，后续更新会自动检测版本更新，无需再下载更新包\n  - 4.新增设置账户信息及发信控制\n- 修复bug\n  - 删除监听任务时，不删除对应任务采集到的数据。\n  - 监听设置群无法发送到目标群\n  - 导出监听数据时应当导出单个任务而非所有数据\n- 优化记录\n  - 优化发信规则，直接调取符合要求的账户发送，无账户时才需要等待延时。\n  - 完善软件设置', 'new_url': None, 'notice': {'id': '2', 'content': '公告内容测试3', 'time': '**********'}}
2025-06-27 23:22:07.211 | INFO     | app.controllers.auth_controller:login:61 - 控制器: 用户登录 account=demo01
2025-06-27 23:22:10.131 | INFO     | core.auth.api_service:login:127 - 用户登录: account=demo01
2025-06-27 23:22:10.418 | INFO     | core.auth.api_service:get_user_info:347 - 获取用户信息
2025-06-27 23:22:10.618 | INFO     | core.auth.api_service:get_user_info:375 - 获取用户信息成功
2025-06-27 23:22:10.625 | INFO     | __main__:on_auth_completed:93 - 用户登录成功，正在启动主窗口...
2025-06-27 23:22:10.649 | INFO     | core.telegram.client_worker:__init__:61 - Telegram客户端工作线程初始化
2025-06-27 23:22:10.649 | INFO     | utils.vip_checker:initialize:25 - VIP检查器已初始化，等待登录后开始检查
2025-06-27 23:22:10.649 | INFO     | ui.main_window:_initialize_core_components:107 - MainWindow: 初始化核心组件...
2025-06-27 23:22:10.650 | INFO     | app.services.account_service:__init__:42 - 账户服务初始化
2025-06-27 23:22:10.650 | INFO     | core.proxy.proxy_task_manager:run:284 - 代理任务管理器线程启动
2025-06-27 23:22:10.651 | INFO     | core.telegram.client_worker:run:65 - Telegram客户端工作线程开始运行
2025-06-27 23:22:10.651 | INFO     | app.controllers.account_controller:__init__:81 - 账户控制器初始化
2025-06-27 23:22:10.651 | INFO     | app.services.monitor_service:__init__:39 - 初始化监控任务服务
2025-06-27 23:22:10.651 | INFO     | core.monitor.filter:__init__:26 - 初始化消息过滤器
2025-06-27 23:22:10.651 | INFO     | core.monitor.extractor:__init__:24 - 初始化关键词提取器
2025-06-27 23:22:10.652 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-06-27 23:22:10.652 | INFO     | core.monitor.notifier:__init__:90 - 初始化通知管理器
2025-06-27 23:22:10.652 | INFO     | app.controllers.telegram_monitor_controller:__init__:64 - 初始化 Telegram 监控控制器
2025-06-27 23:22:10.652 | DEBUG    | app.controllers.telegram_monitor_controller:__init__:82 - 控制器初始化完成
2025-06-27 23:22:10.655 | INFO     | core.telegram.client_manager:__init__:66 - Telegram客户端管理器初始化
2025-06-27 23:22:10.655 | INFO     | core.telegram.user_manager:__init__:30 - Telegram用户管理器初始化
2025-06-27 23:22:10.655 | INFO     | core.telegram.group_manager:__init__:36 - Telegram群组管理器初始化
2025-06-27 23:22:10.655 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-06-27 23:22:10.655 | INFO     | core.telegram.monitor_manager:__init__:26 - Telegram监控管理器初始化
2025-06-27 23:22:10.655 | INFO     | core.telegram.telegram_service:__init__:39 - Telegram服务初始化完成
2025-06-27 23:22:10.819 | INFO     | ui.views.monitor_view:__init__:48 - 初始化监控视图
2025-06-27 23:22:10.820 | DEBUG    | ui.views.monitor_view:_connect_signals:80 - 连接控制器信号
2025-06-27 23:22:10.992 | INFO     | ui.views.proxy_view:__init__:96 - 初始化视图，加载第一页代理数据
2025-06-27 23:22:11.201 | INFO     | __main__:on_auth_completed:97 - 主窗口已启动
2025-06-27 23:22:11.250 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:88 - 尝试连接监听日志信号...
2025-06-27 23:22:11.250 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:90 - 监听日志信号连接成功
2025-06-27 23:22:11.251 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 23:22:11.254 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 23:22:11.257 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:145 - 加载监控任务列表 (搜索关键词: '')
2025-06-27 23:22:11.258 | DEBUG    | ui.views.monitor_view:_on_loading_started:620 - 加载中: 正在加载任务列表...
2025-06-27 23:22:11.258 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 23:22:11.263 | INFO     | app.services.proxy_service:refresh_proxies:712 - 刷新代理列表: offset=0, limit=20
2025-06-27 23:22:11.264 | INFO     | core.proxy.proxy_task_manager:submit_task:172 - 提交耗时任务到子线程: refresh_proxies_0_20
2025-06-27 23:22:11.264 | DEBUG    | app.services.proxy_service:get_proxies:500 - 获取代理列表: offset=0, limit=20
2025-06-27 23:22:11.264 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 23:22:11.265 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 23:22:11.267 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 23:22:11.269 | INFO     | app.controllers.invite_controller:get_invite_tasks:38 - 获取邀请任务列表, status=None
2025-06-27 23:22:11.270 | INFO     | app.services.invite_service:get_invite_tasks:121 - 获取邀请任务列表, status=None
2025-06-27 23:22:11.270 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 23:22:11.291 | INFO     | app.services.invite_service:task_initialize:39 - 初始化邀请服务，重置运行中的任务为暂停状态
2025-06-27 23:22:11.291 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 23:22:11.295 | INFO     | utils.vip_checker:start_checking:37 - 开始VIP状态检查
2025-06-27 23:22:11.295 | DEBUG    | utils.vip_checker:_check_vip_status:61 - 正在检查VIP状态...
2025-06-27 23:22:11.295 | INFO     | core.auth.api_service:verify_vip:425 - 会员验证接口调用
2025-06-27 23:22:11.296 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 23:22:11.298 | INFO     | core.proxy.proxy_task_manager:task_wrapper:199 - 任务 refresh_proxies_0_20 完成
2025-06-27 23:22:11.498 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 23:22:11.536 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 23:22:11.540 | INFO     | app.services.invite_service:task_initialize:48 - 成功重置 0 个任务状态
2025-06-27 23:22:11.540 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 23:22:11.540 | DEBUG    | ui.views.monitor_view:_refresh_task_list:175 - 刷新任务列表，共0个任务
2025-06-27 23:22:11.541 | INFO     | ui.views.monitor_view:_refresh_task_list:189 - 没有找到监控任务
2025-06-27 23:22:11.541 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 23:22:11.664 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 23:22:11.671 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 23:22:11.676 | INFO     | app.services.account_service:get_all_groups:96 - 获取所有账户分组成功, 共 2 个
2025-06-27 23:22:11.676 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 23:22:11.678 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 23:22:11.680 | DEBUG    | ui.views.account_view:_on_groups_loaded:446 - 分组加载完成: 2个分组
2025-06-27 23:22:11.699 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 23:22:11.786 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 23:22:11.787 | INFO     | app.services.account_service:get_all_accounts:290 - 获取所有账户成功, 共 2 个
2025-06-27 23:22:11.787 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 23:22:11.788 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 23:22:11.790 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 23:22:11.831 | INFO     | app.controllers.telegram_monitor_controller:_load_total_users_count_internal:191 - 成功加载总体统计数据: 总用户数 23, 今日采集 0, 日均采集 23, 运行天数 1
2025-06-27 23:22:11.831 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:156 - 成功加载 0 个任务
2025-06-27 23:22:11.831 | DEBUG    | ui.views.monitor_view:_on_loading_finished:625 - 加载完成
2025-06-27 23:22:11.832 | DEBUG    | ui.views.account_view:_on_accounts_loaded:579 - 账户加载完成: 2个账户
2025-06-27 23:22:11.863 | DEBUG    | ui.views.account_view:_update_group_counts:185 - 分组计数已更新: {2: 1, 1: 1}
2025-06-27 23:22:11.870 | INFO     | ui.views.account_view:_auto_login_accounts:634 - 开始调用控制器进行 2 个账户的自动登录。
2025-06-27 23:22:11.923 | INFO     | app.services.account_service:batch_auto_login:1165 - 服务层：开始批量自动登录 2 个账户, 最大并发: 5
2025-06-27 23:22:11.923 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 23:22:11.927 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-06-27 23:22:11.942 | INFO     | core.auth.api_service:verify_vip:438 - 会员验证成功
2025-06-27 23:22:11.942 | INFO     | utils.vip_checker:_check_vip_status:76 - VIP状态有效
2025-06-27 23:22:11.947 | INFO     | app.services.account_service:get_all_accounts:290 - 获取所有账户成功, 共 2 个
2025-06-27 23:22:11.947 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 23:22:11.947 | INFO     | app.services.account_service:batch_auto_login:1204 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-06-27 23:22:11.948 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 23:22:11.950 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 23:22:11.952 | DEBUG    | ui.views.account_view:_update_group_counts:185 - 分组计数已更新: {2: 1, 1: 1}
2025-06-27 23:22:11.952 | DEBUG    | ui.views.account_view:_init_cache:152 - 账户缓存初始化完成，共 2 个账户
2025-06-27 23:22:11.957 | DEBUG    | ui.views.account_view:_on_accounts_loaded:579 - 账户加载完成: 2个账户
2025-06-27 23:22:11.977 | DEBUG    | ui.views.account_view:_update_group_counts:185 - 分组计数已更新: {2: 1, 1: 1}
2025-06-27 23:22:11.984 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=True
2025-06-27 23:22:11.986 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=True
2025-06-27 23:22:11.988 | INFO     | ui.views.proxy_view:_load_initial_data:1044 - 初始数据加载完成，共 1 个代理
2025-06-27 23:22:11.990 | INFO     | app.services.account_service:batch_auto_login:1204 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-06-27 23:22:11.990 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 23:22:11.991 | INFO     | app.services.account_service:batch_auto_login:1274 - 服务层：准备调用核心层处理 2 个账户的自动登录。
2025-06-27 23:22:11.992 | INFO     | app.services.account_service:batch_auto_login:1284 - 服务层：设置核心层任务超时为 120 秒。
2025-06-27 23:22:11.992 | INFO     | core.telegram.client_manager:batch_auto_login:1216 - 开始批量自动登录, 共 2 个账户, 最大并发 5
2025-06-27 23:22:11.992 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:259 - 创建客户端: H:\PyProject\TeleTest\APPDATA\sessions\+***********, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-06-27 23:22:11.992 | DEBUG    | core.telegram.client_manager:get_proxy:96 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-06-27 23:22:11.992 | DEBUG    | core.telegram.client_manager:get_proxy:143 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-06-27 23:22:11.995 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:259 - 创建客户端: H:\PyProject\TeleTest\APPDATA\sessions\+***********, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-06-27 23:22:11.996 | DEBUG    | core.telegram.client_manager:get_proxy:96 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-06-27 23:22:11.996 | DEBUG    | core.telegram.client_manager:get_proxy:143 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-06-27 23:22:12.013 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 23:22:12.078 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-27 23:22:12.106 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-27 23:22:14.570 | INFO     | core.telegram.client_manager:_connect_client:207 - 连接成功!
2025-06-27 23:22:14.641 | INFO     | core.telegram.client_manager:_connect_client:207 - 连接成功!
2025-06-27 23:22:15.283 | INFO     | core.telegram.client_manager:_verify_client_authorization:1191 - 账户已授权: +***********
2025-06-27 23:22:15.634 | INFO     | core.telegram.client_manager:_verify_client_authorization:1191 - 账户已授权: +***********
2025-06-27 23:22:17.640 | INFO     | core.telegram.client_manager:batch_auto_login:1365 - 批量登录完成: 总计 2, 成功 2, 失败 0
2025-06-27 23:22:18.009 | INFO     | app.services.account_service:batch_auto_login:1305 - 服务层：核心层登录完成: 成功 2, 失败 0 / 2
2025-06-27 23:23:10.621 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-27 23:24:10.624 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-27 23:25:10.614 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-27 23:26:10.622 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-27 23:27:10.621 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-27 23:28:10.623 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-27 23:29:10.622 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-27 23:30:10.621 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-27 23:31:10.623 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-27 23:32:10.614 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-27 23:32:35.464 | INFO     | ui.main_window:closeEvent:378 - MainWindow: 接收到关闭事件
2025-06-27 23:32:35.485 | INFO     | ui.main_window:_cleanup_before_quit:264 - MainWindow: 执行清理资源...
2025-06-27 23:32:35.485 | INFO     | core.telegram.client_worker:stop:108 - 正在停止Telegram客户端工作线程
2025-06-27 23:32:35.497 | INFO     | core.telegram.client_manager:cleanup_async:1136 - 开始清理资源并断开所有连接
2025-06-27 23:32:35.499 | INFO     | core.telegram.client_manager:disconnect_all_clients:721 - 正在断开所有客户端连接
2025-06-27 23:32:35.499 | INFO     | core.telegram.client_manager:_safe_disconnect:742 - 正在断开客户端连接: +***********
2025-06-27 23:32:35.499 | INFO     | core.telegram.client_manager:_safe_disconnect:742 - 正在断开客户端连接: +***********
2025-06-27 23:32:35.512 | INFO     | core.telegram.client_manager:_safe_disconnect:746 - 断开客户端连接成功: +***********
2025-06-27 23:32:35.512 | INFO     | core.telegram.client_manager:_safe_disconnect:746 - 断开客户端连接成功: +***********
2025-06-27 23:32:35.512 | INFO     | core.telegram.client_manager:cleanup_async:1155 - 资源清理完成
2025-06-27 23:32:35.997 | INFO     | core.telegram.client_manager:cleanup_async:1136 - 开始清理资源并断开所有连接
2025-06-27 23:32:35.997 | INFO     | core.telegram.client_manager:disconnect_all_clients:721 - 正在断开所有客户端连接
2025-06-27 23:32:35.997 | INFO     | core.telegram.client_manager:cleanup_async:1155 - 资源清理完成
2025-06-27 23:32:36.498 | INFO     | core.telegram.client_worker:run:97 - Telegram客户端工作线程已退出
2025-06-27 23:32:36.499 | INFO     | core.telegram.client_worker:stop:122 - Telegram客户端工作线程已停止
2025-06-27 23:32:36.499 | INFO     | ui.main_window:_cleanup_before_quit:273 - TelegramClientWorker 已停止。
2025-06-27 23:32:36.499 | INFO     | ui.main_window:_cleanup_before_quit:277 - MainWindow 清理完成
2025-06-27 23:32:36.558 | INFO     | __main__:main:111 - 应用程序已正常退出
