2025-06-30 08:36:08.130 | INFO     | utils.logger:init_logger:98 - 日志系统初始化完成，默认级别: DEBUG，日志目录: H:\PyProject\TeleTest\APPDATA\logs
2025-06-30 08:36:10.138 | INFO     | data.database:init_database:31 - 初始化数据库连接: H:\PyProject\TeleTest\APPDATA\database\telegram_manager.db
2025-06-30 08:36:10.160 | DEBUG    | data.database:init_database:58 - 创建数据库表
2025-06-30 08:36:10.169 | INFO     | data.database:init_database:61 - 数据库初始化完成
2025-06-30 08:36:11.411 | DEBUG    | app.controllers.auth_controller:initialize_software:43 - 控制器: 初始化软件 app_id=1000, version=1.2.7
2025-06-30 08:36:11.412 | DEBUG    | core.auth.api_service:init_software:55 - 发送初始化请求: https://server.xile188.com/api/user/1000/windowns10/1.2.7/ini, params={}
2025-06-30 08:36:12.024 | DEBUG    | core.auth.api_service:init_software:66 - 初始化数据解密成功: {'new_content': '- 新增功能\n  - 1.新增图片上传功能\n  - 2.新增软件文档\n  - 3.新增软件自动更新，后续更新会自动检测版本更新，无需再下载更新包\n  - 4.新增设置账户信息及发信控制\n- 修复bug\n  - 删除监听任务时，不删除对应任务采集到的数据。\n  - 监听设置群无法发送到目标群\n  - 导出监听数据时应当导出单个任务而非所有数据\n- 优化记录\n  - 优化发信规则，直接调取符合要求的账户发送，无账户时才需要等待延时。\n  - 完善软件设置', 'new_url': None, 'notice': {'id': '2', 'content': '公告内容测试3', 'time': '**********'}}
2025-06-30 08:36:12.036 | INFO     | app.controllers.auth_controller:login:61 - 控制器: 用户登录 account=demo01
2025-06-30 08:36:15.116 | INFO     | core.auth.api_service:login:127 - 用户登录: account=demo01
2025-06-30 08:36:15.426 | INFO     | core.auth.api_service:get_user_info:347 - 获取用户信息
2025-06-30 08:36:15.694 | INFO     | core.auth.api_service:get_user_info:375 - 获取用户信息成功
2025-06-30 08:36:15.701 | INFO     | __main__:on_auth_completed:93 - 用户登录成功，正在启动主窗口...
2025-06-30 08:36:15.726 | INFO     | core.telegram.client_worker:__init__:61 - Telegram客户端工作线程初始化
2025-06-30 08:36:15.726 | INFO     | utils.vip_checker:initialize:25 - VIP检查器已初始化，等待登录后开始检查
2025-06-30 08:36:15.726 | INFO     | ui.main_window:_initialize_core_components:107 - MainWindow: 初始化核心组件...
2025-06-30 08:36:15.727 | INFO     | app.services.account_service:__init__:42 - 账户服务初始化
2025-06-30 08:36:15.727 | INFO     | core.telegram.client_worker:run:65 - Telegram客户端工作线程开始运行
2025-06-30 08:36:15.727 | INFO     | app.controllers.account_controller:__init__:81 - 账户控制器初始化
2025-06-30 08:36:15.728 | INFO     | core.proxy.proxy_task_manager:run:284 - 代理任务管理器线程启动
2025-06-30 08:36:15.728 | INFO     | app.services.monitor_service:__init__:39 - 初始化监控任务服务
2025-06-30 08:36:15.728 | INFO     | core.monitor.filter:__init__:26 - 初始化消息过滤器
2025-06-30 08:36:15.728 | INFO     | core.monitor.extractor:__init__:24 - 初始化关键词提取器
2025-06-30 08:36:15.729 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-06-30 08:36:15.729 | INFO     | core.monitor.notifier:__init__:90 - 初始化通知管理器
2025-06-30 08:36:15.729 | INFO     | app.controllers.telegram_monitor_controller:__init__:64 - 初始化 Telegram 监控控制器
2025-06-30 08:36:15.730 | DEBUG    | app.controllers.telegram_monitor_controller:__init__:82 - 控制器初始化完成
2025-06-30 08:36:15.730 | INFO     | core.telegram.client_manager:__init__:66 - Telegram客户端管理器初始化
2025-06-30 08:36:15.732 | INFO     | core.telegram.user_manager:__init__:30 - Telegram用户管理器初始化
2025-06-30 08:36:15.732 | INFO     | core.telegram.group_manager:__init__:36 - Telegram群组管理器初始化
2025-06-30 08:36:15.732 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-06-30 08:36:15.733 | INFO     | core.telegram.monitor_manager:__init__:26 - Telegram监控管理器初始化
2025-06-30 08:36:15.733 | INFO     | core.telegram.telegram_service:__init__:39 - Telegram服务初始化完成
2025-06-30 08:36:15.918 | INFO     | ui.views.monitor_view:__init__:48 - 初始化监控视图
2025-06-30 08:36:15.918 | DEBUG    | ui.views.monitor_view:_connect_signals:80 - 连接控制器信号
2025-06-30 08:36:16.115 | INFO     | ui.views.proxy_view:__init__:96 - 初始化视图，加载第一页代理数据
2025-06-30 08:36:16.364 | INFO     | __main__:on_auth_completed:97 - 主窗口已启动
2025-06-30 08:36:16.439 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:88 - 尝试连接监听日志信号...
2025-06-30 08:36:16.439 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:90 - 监听日志信号连接成功
2025-06-30 08:36:16.439 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-30 08:36:16.443 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-30 08:36:16.446 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:145 - 加载监控任务列表 (搜索关键词: '')
2025-06-30 08:36:16.447 | DEBUG    | ui.views.monitor_view:_on_loading_started:620 - 加载中: 正在加载任务列表...
2025-06-30 08:36:16.447 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-30 08:36:16.452 | INFO     | app.services.proxy_service:refresh_proxies:712 - 刷新代理列表: offset=0, limit=20
2025-06-30 08:36:16.453 | INFO     | core.proxy.proxy_task_manager:submit_task:172 - 提交耗时任务到子线程: refresh_proxies_0_20
2025-06-30 08:36:16.453 | DEBUG    | app.services.proxy_service:get_proxies:500 - 获取代理列表: offset=0, limit=20
2025-06-30 08:36:16.453 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-30 08:36:16.453 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-30 08:36:16.457 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-30 08:36:16.480 | INFO     | app.controllers.invite_controller:get_invite_tasks:38 - 获取邀请任务列表, status=None
2025-06-30 08:36:16.480 | INFO     | app.services.invite_service:get_invite_tasks:121 - 获取邀请任务列表, status=None
2025-06-30 08:36:16.480 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-30 08:36:16.482 | INFO     | app.services.invite_service:task_initialize:39 - 初始化邀请服务，重置运行中的任务为暂停状态
2025-06-30 08:36:16.483 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-30 08:36:16.484 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-30 08:36:16.485 | INFO     | core.proxy.proxy_task_manager:task_wrapper:199 - 任务 refresh_proxies_0_20 完成
2025-06-30 08:36:16.486 | INFO     | utils.vip_checker:start_checking:37 - 开始VIP状态检查
2025-06-30 08:36:16.486 | DEBUG    | utils.vip_checker:_check_vip_status:61 - 正在检查VIP状态...
2025-06-30 08:36:16.486 | INFO     | core.auth.api_service:verify_vip:425 - 会员验证接口调用
2025-06-30 08:36:16.706 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-30 08:36:16.707 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-30 08:36:16.713 | INFO     | app.services.invite_service:task_initialize:48 - 成功重置 0 个任务状态
2025-06-30 08:36:16.713 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-30 08:36:16.714 | DEBUG    | ui.views.monitor_view:_refresh_task_list:175 - 刷新任务列表，共0个任务
2025-06-30 08:36:16.714 | INFO     | ui.views.monitor_view:_refresh_task_list:189 - 没有找到监控任务
2025-06-30 08:36:16.714 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-30 08:36:16.715 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-30 08:36:16.719 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-30 08:36:16.720 | INFO     | app.services.account_service:get_all_groups:96 - 获取所有账户分组成功, 共 2 个
2025-06-30 08:36:16.720 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-30 08:36:16.721 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-30 08:36:16.724 | DEBUG    | ui.views.account_view:_on_groups_loaded:446 - 分组加载完成: 2个分组
2025-06-30 08:36:16.737 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-30 08:36:16.797 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-30 08:36:16.798 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-30 08:36:16.799 | INFO     | app.services.account_service:get_all_accounts:290 - 获取所有账户成功, 共 2 个
2025-06-30 08:36:16.799 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-30 08:36:16.801 | INFO     | app.controllers.telegram_monitor_controller:_load_total_users_count_internal:191 - 成功加载总体统计数据: 总用户数 23, 今日采集 0, 日均采集 23, 运行天数 1
2025-06-30 08:36:16.801 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:156 - 成功加载 0 个任务
2025-06-30 08:36:16.801 | DEBUG    | ui.views.monitor_view:_on_loading_finished:625 - 加载完成
2025-06-30 08:36:16.802 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-06-30 08:36:16.806 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-30 08:36:16.814 | DEBUG    | ui.views.account_view:_on_accounts_loaded:579 - 账户加载完成: 2个账户
2025-06-30 08:36:16.836 | DEBUG    | ui.views.account_view:_update_group_counts:185 - 分组计数已更新: {2: 1, 1: 1}
2025-06-30 08:36:16.840 | INFO     | ui.views.account_view:_auto_login_accounts:634 - 开始调用控制器进行 2 个账户的自动登录。
2025-06-30 08:36:16.840 | INFO     | app.services.account_service:batch_auto_login:1165 - 服务层：开始批量自动登录 2 个账户, 最大并发: 5
2025-06-30 08:36:16.840 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-30 08:36:16.847 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=True
2025-06-30 08:36:16.849 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=True
2025-06-30 08:36:16.851 | INFO     | ui.views.proxy_view:_load_initial_data:1044 - 初始数据加载完成，共 1 个代理
2025-06-30 08:36:16.853 | INFO     | app.services.account_service:get_all_accounts:290 - 获取所有账户成功, 共 2 个
2025-06-30 08:36:16.853 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-30 08:36:16.853 | INFO     | app.services.account_service:batch_auto_login:1204 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-06-30 08:36:16.853 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-30 08:36:16.855 | DEBUG    | ui.views.account_view:_update_group_counts:185 - 分组计数已更新: {2: 1, 1: 1}
2025-06-30 08:36:16.855 | DEBUG    | ui.views.account_view:_init_cache:152 - 账户缓存初始化完成，共 2 个账户
2025-06-30 08:36:16.859 | DEBUG    | ui.views.account_view:_on_accounts_loaded:579 - 账户加载完成: 2个账户
2025-06-30 08:36:16.879 | DEBUG    | ui.views.account_view:_update_group_counts:185 - 分组计数已更新: {2: 1, 1: 1}
2025-06-30 08:36:16.883 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-30 08:36:16.889 | INFO     | app.services.account_service:batch_auto_login:1204 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-06-30 08:36:16.889 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-30 08:36:16.891 | INFO     | app.services.account_service:batch_auto_login:1274 - 服务层：准备调用核心层处理 2 个账户的自动登录。
2025-06-30 08:36:16.891 | INFO     | app.services.account_service:batch_auto_login:1284 - 服务层：设置核心层任务超时为 120 秒。
2025-06-30 08:36:16.891 | INFO     | core.telegram.client_manager:batch_auto_login:1216 - 开始批量自动登录, 共 2 个账户, 最大并发 5
2025-06-30 08:36:16.891 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:259 - 创建客户端: H:\PyProject\TeleTest\APPDATA\sessions\+***********, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-06-30 08:36:16.893 | DEBUG    | core.telegram.client_manager:get_proxy:96 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-06-30 08:36:16.893 | DEBUG    | core.telegram.client_manager:get_proxy:143 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-06-30 08:36:16.897 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:259 - 创建客户端: H:\PyProject\TeleTest\APPDATA\sessions\+***********, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-06-30 08:36:16.898 | DEBUG    | core.telegram.client_manager:get_proxy:96 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-06-30 08:36:16.898 | DEBUG    | core.telegram.client_manager:get_proxy:143 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-06-30 08:36:16.916 | WARNING  | core.auth.api_service:verify_vip:440 - 会员验证失败: 验证失败
2025-06-30 08:36:16.917 | ERROR    | utils.vip_checker:_check_vip_status:81 - VIP验证失败: 验证失败
2025-06-30 08:36:20.503 | INFO     | core.telegram.client_manager:_connect_client:207 - 连接成功!
2025-06-30 08:36:21.515 | INFO     | core.telegram.client_manager:_verify_client_authorization:1191 - 账户已授权: +***********
2025-06-30 08:36:22.852 | INFO     | core.telegram.client_manager:_connect_client:207 - 连接成功!
2025-06-30 08:36:23.930 | INFO     | core.telegram.client_manager:_verify_client_authorization:1191 - 账户已授权: +***********
2025-06-30 08:36:25.931 | INFO     | core.telegram.client_manager:batch_auto_login:1365 - 批量登录完成: 总计 2, 成功 2, 失败 0
2025-06-30 08:36:29.857 | INFO     | ui.views.monitor_view:open_add_task_dialog:135 - 打开添加任务对话框 
2025-06-30 08:36:29.868 | ERROR    | data.repositories.message_repo:get_task_target_stats:187 - 获取任务1目标统计失败: coroutine ignored GeneratorExit
2025-06-30 08:36:29.868 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-30 08:36:29.868 | ERROR    | ui.views.send_msg_view:load_tasks:50 - 加载任务列表失败: (sqlite3.OperationalError) no active connection
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-30 08:36:29.962 | INFO     | ui.views.add_monitor_view:_load_initial_data:111 - 加载初始数据
2025-06-30 08:36:30.023 | INFO     | ui.views.add_monitor_view:_load_account_groups:400 - 加载账户分组
2025-06-30 08:36:30.023 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-30 08:36:30.024 | INFO     | ui.views.add_monitor_view:_load_accounts_by_group:417 - 根据分组ID加载账户: -1
2025-06-30 08:36:30.024 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-30 08:36:30.208 | INFO     | app.services.account_service:get_all_accounts:290 - 获取所有账户成功, 共 2 个
2025-06-30 08:36:30.208 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-30 08:36:30.211 | INFO     | ui.views.add_monitor_view:_load_accounts_by_group:442 - 已加载 2 个账户到列表
2025-06-30 08:36:30.211 | DEBUG    | ui.views.account_view:_on_accounts_loaded:579 - 账户加载完成: 2个账户
2025-06-30 08:36:30.233 | DEBUG    | ui.views.account_view:_update_group_counts:185 - 分组计数已更新: {2: 1, 1: 1}
2025-06-30 08:36:30.269 | INFO     | app.services.account_service:get_all_groups:96 - 获取所有账户分组成功, 共 2 个
2025-06-30 08:36:30.269 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-30 08:36:30.270 | DEBUG    | ui.views.account_view:_on_groups_loaded:446 - 分组加载完成: 2个分组
2025-06-30 08:36:30.271 | INFO     | ui.views.add_monitor_view:_on_group_index_changed:460 - 当前分组id:-1
2025-06-30 08:36:30.271 | INFO     | ui.views.add_monitor_view:_load_account_groups:409 - 加载账户分组数据: [{'id': 2, 'name': '主账户', 'description': '', 'account_count': 1}, {'id': 1, 'name': '营销', 'description': '', 'account_count': 1}]
2025-06-30 08:36:30.271 | INFO     | ui.views.add_monitor_view:_load_accounts_by_group:417 - 根据分组ID加载账户: -1
2025-06-30 08:36:30.271 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-30 08:36:30.275 | INFO     | app.services.account_service:get_all_accounts:290 - 获取所有账户成功, 共 2 个
2025-06-30 08:36:30.275 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-30 08:36:30.277 | INFO     | ui.views.add_monitor_view:_load_accounts_by_group:442 - 已加载 2 个账户到列表
2025-06-30 08:36:30.277 | DEBUG    | ui.views.account_view:_on_accounts_loaded:579 - 账户加载完成: 2个账户
2025-06-30 08:36:30.298 | DEBUG    | ui.views.account_view:_update_group_counts:185 - 分组计数已更新: {2: 1, 1: 1}
2025-06-30 08:36:33.002 | INFO     | app.services.account_service:refresh_account_info:607 - 刷新账户信息: 1
2025-06-30 08:36:33.003 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-30 08:36:33.020 | INFO     | core.telegram.user_manager:get_user_info:41 - 正在获取用户信息: +***********
2025-06-30 08:36:35.311 | INFO     | core.telegram.user_manager:get_user_info:66 - 获取用户信息成功: +***********
2025-06-30 08:36:36.054 | INFO     | data.repositories.account_repo:update_account:378 - 更新账户成功: 1
2025-06-30 08:36:36.197 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-30 08:37:13.381 | INFO     | utils.logger:init_logger:98 - 日志系统初始化完成，默认级别: DEBUG，日志目录: H:\PyProject\TeleTest\APPDATA\logs
2025-06-30 08:37:14.309 | INFO     | data.database:init_database:31 - 初始化数据库连接: H:\PyProject\TeleTest\APPDATA\database\telegram_manager.db
2025-06-30 08:37:14.324 | DEBUG    | data.database:init_database:58 - 创建数据库表
2025-06-30 08:37:14.334 | INFO     | data.database:init_database:61 - 数据库初始化完成
2025-06-30 08:37:14.987 | DEBUG    | app.controllers.auth_controller:initialize_software:43 - 控制器: 初始化软件 app_id=1000, version=1.2.7
2025-06-30 08:37:14.987 | DEBUG    | core.auth.api_service:init_software:55 - 发送初始化请求: https://server.xile188.com/api/user/1000/windowns10/1.2.7/ini, params={}
2025-06-30 08:37:15.270 | DEBUG    | core.auth.api_service:init_software:66 - 初始化数据解密成功: {'new_content': '- 新增功能\n  - 1.新增图片上传功能\n  - 2.新增软件文档\n  - 3.新增软件自动更新，后续更新会自动检测版本更新，无需再下载更新包\n  - 4.新增设置账户信息及发信控制\n- 修复bug\n  - 删除监听任务时，不删除对应任务采集到的数据。\n  - 监听设置群无法发送到目标群\n  - 导出监听数据时应当导出单个任务而非所有数据\n- 优化记录\n  - 优化发信规则，直接调取符合要求的账户发送，无账户时才需要等待延时。\n  - 完善软件设置', 'new_url': None, 'notice': {'id': '2', 'content': '公告内容测试3', 'time': '**********'}}
2025-06-30 08:37:15.280 | INFO     | app.controllers.auth_controller:login:61 - 控制器: 用户登录 account=demo01
2025-06-30 08:37:18.223 | INFO     | core.auth.api_service:login:127 - 用户登录: account=demo01
2025-06-30 08:37:18.445 | INFO     | core.auth.api_service:get_user_info:347 - 获取用户信息
2025-06-30 08:37:18.689 | INFO     | core.auth.api_service:get_user_info:375 - 获取用户信息成功
2025-06-30 08:37:18.696 | INFO     | __main__:on_auth_completed:93 - 用户登录成功，正在启动主窗口...
2025-06-30 08:37:18.717 | INFO     | core.telegram.client_worker:__init__:61 - Telegram客户端工作线程初始化
2025-06-30 08:37:18.717 | INFO     | utils.vip_checker:initialize:25 - VIP检查器已初始化，等待登录后开始检查
2025-06-30 08:37:18.717 | INFO     | ui.main_window:_initialize_core_components:107 - MainWindow: 初始化核心组件...
2025-06-30 08:37:18.718 | INFO     | app.services.account_service:__init__:42 - 账户服务初始化
2025-06-30 08:37:18.718 | INFO     | core.telegram.client_worker:run:65 - Telegram客户端工作线程开始运行
2025-06-30 08:37:18.718 | INFO     | core.proxy.proxy_task_manager:run:284 - 代理任务管理器线程启动
2025-06-30 08:37:18.718 | INFO     | app.controllers.account_controller:__init__:81 - 账户控制器初始化
2025-06-30 08:37:18.719 | INFO     | app.services.monitor_service:__init__:39 - 初始化监控任务服务
2025-06-30 08:37:18.719 | INFO     | core.monitor.filter:__init__:26 - 初始化消息过滤器
2025-06-30 08:37:18.719 | INFO     | core.monitor.extractor:__init__:24 - 初始化关键词提取器
2025-06-30 08:37:18.719 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-06-30 08:37:18.719 | INFO     | core.monitor.notifier:__init__:90 - 初始化通知管理器
2025-06-30 08:37:18.720 | INFO     | app.controllers.telegram_monitor_controller:__init__:64 - 初始化 Telegram 监控控制器
2025-06-30 08:37:18.720 | DEBUG    | app.controllers.telegram_monitor_controller:__init__:82 - 控制器初始化完成
2025-06-30 08:37:18.723 | INFO     | core.telegram.client_manager:__init__:66 - Telegram客户端管理器初始化
2025-06-30 08:37:18.723 | INFO     | core.telegram.user_manager:__init__:30 - Telegram用户管理器初始化
2025-06-30 08:37:18.723 | INFO     | core.telegram.group_manager:__init__:36 - Telegram群组管理器初始化
2025-06-30 08:37:18.723 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-06-30 08:37:18.723 | INFO     | core.telegram.monitor_manager:__init__:26 - Telegram监控管理器初始化
2025-06-30 08:37:18.723 | INFO     | core.telegram.telegram_service:__init__:39 - Telegram服务初始化完成
2025-06-30 08:37:18.891 | INFO     | ui.views.monitor_view:__init__:48 - 初始化监控视图
2025-06-30 08:37:18.891 | DEBUG    | ui.views.monitor_view:_connect_signals:80 - 连接控制器信号
2025-06-30 08:37:19.085 | INFO     | ui.views.proxy_view:__init__:96 - 初始化视图，加载第一页代理数据
2025-06-30 08:37:19.298 | INFO     | __main__:on_auth_completed:97 - 主窗口已启动
2025-06-30 08:37:19.345 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:88 - 尝试连接监听日志信号...
2025-06-30 08:37:19.346 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:90 - 监听日志信号连接成功
2025-06-30 08:37:19.346 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-30 08:37:19.350 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-30 08:37:19.353 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:145 - 加载监控任务列表 (搜索关键词: '')
2025-06-30 08:37:19.353 | DEBUG    | ui.views.monitor_view:_on_loading_started:620 - 加载中: 正在加载任务列表...
2025-06-30 08:37:19.353 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-30 08:37:19.358 | INFO     | app.services.proxy_service:refresh_proxies:712 - 刷新代理列表: offset=0, limit=20
2025-06-30 08:37:19.359 | INFO     | core.proxy.proxy_task_manager:submit_task:172 - 提交耗时任务到子线程: refresh_proxies_0_20
2025-06-30 08:37:19.359 | DEBUG    | app.services.proxy_service:get_proxies:500 - 获取代理列表: offset=0, limit=20
2025-06-30 08:37:19.359 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-30 08:37:19.360 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-30 08:37:19.363 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-30 08:37:19.365 | INFO     | app.controllers.invite_controller:get_invite_tasks:38 - 获取邀请任务列表, status=None
2025-06-30 08:37:19.365 | INFO     | app.services.invite_service:get_invite_tasks:121 - 获取邀请任务列表, status=None
2025-06-30 08:37:19.383 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-30 08:37:19.389 | INFO     | app.services.invite_service:task_initialize:39 - 初始化邀请服务，重置运行中的任务为暂停状态
2025-06-30 08:37:19.389 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-30 08:37:19.394 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-30 08:37:19.394 | INFO     | utils.vip_checker:start_checking:37 - 开始VIP状态检查
2025-06-30 08:37:19.395 | DEBUG    | utils.vip_checker:_check_vip_status:61 - 正在检查VIP状态...
2025-06-30 08:37:19.395 | INFO     | core.auth.api_service:verify_vip:425 - 会员验证接口调用
2025-06-30 08:37:19.397 | INFO     | core.proxy.proxy_task_manager:task_wrapper:199 - 任务 refresh_proxies_0_20 完成
2025-06-30 08:37:19.508 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-30 08:37:19.508 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-30 08:37:19.517 | DEBUG    | ui.views.monitor_view:_refresh_task_list:175 - 刷新任务列表，共0个任务
2025-06-30 08:37:19.518 | INFO     | ui.views.monitor_view:_refresh_task_list:189 - 没有找到监控任务
2025-06-30 08:37:19.518 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-30 08:37:19.520 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-30 08:37:19.521 | INFO     | app.services.invite_service:task_initialize:48 - 成功重置 0 个任务状态
2025-06-30 08:37:19.521 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-30 08:37:19.527 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-30 08:37:19.529 | INFO     | app.services.account_service:get_all_groups:96 - 获取所有账户分组成功, 共 2 个
2025-06-30 08:37:19.529 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-30 08:37:19.531 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-30 08:37:19.533 | DEBUG    | ui.views.account_view:_on_groups_loaded:446 - 分组加载完成: 2个分组
2025-06-30 08:37:19.549 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-30 08:37:19.617 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-30 08:37:19.617 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-30 08:37:19.621 | INFO     | app.services.account_service:get_all_accounts:290 - 获取所有账户成功, 共 2 个
2025-06-30 08:37:19.621 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-30 08:37:19.623 | INFO     | app.controllers.telegram_monitor_controller:_load_total_users_count_internal:191 - 成功加载总体统计数据: 总用户数 23, 今日采集 0, 日均采集 23, 运行天数 1
2025-06-30 08:37:19.623 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:156 - 成功加载 0 个任务
2025-06-30 08:37:19.623 | DEBUG    | ui.views.monitor_view:_on_loading_finished:625 - 加载完成
2025-06-30 08:37:19.624 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-06-30 08:37:19.637 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-30 08:37:19.638 | DEBUG    | ui.views.account_view:_on_accounts_loaded:579 - 账户加载完成: 2个账户
2025-06-30 08:37:19.658 | DEBUG    | ui.views.account_view:_update_group_counts:185 - 分组计数已更新: {2: 1, 1: 1}
2025-06-30 08:37:19.662 | INFO     | ui.views.account_view:_auto_login_accounts:634 - 开始调用控制器进行 2 个账户的自动登录。
2025-06-30 08:37:19.662 | INFO     | app.services.account_service:batch_auto_login:1165 - 服务层：开始批量自动登录 2 个账户, 最大并发: 5
2025-06-30 08:37:19.662 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-30 08:37:19.667 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=True
2025-06-30 08:37:19.669 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=True
2025-06-30 08:37:19.670 | INFO     | ui.views.proxy_view:_load_initial_data:1044 - 初始数据加载完成，共 1 个代理
2025-06-30 08:37:19.673 | INFO     | app.services.account_service:batch_auto_login:1204 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-06-30 08:37:19.673 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-30 08:37:19.674 | INFO     | app.services.account_service:get_all_accounts:290 - 获取所有账户成功, 共 2 个
2025-06-30 08:37:19.674 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-30 08:37:19.675 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-30 08:37:19.677 | DEBUG    | ui.views.account_view:_update_group_counts:185 - 分组计数已更新: {2: 1, 1: 1}
2025-06-30 08:37:19.678 | DEBUG    | ui.views.account_view:_init_cache:152 - 账户缓存初始化完成，共 2 个账户
2025-06-30 08:37:19.681 | DEBUG    | ui.views.account_view:_on_accounts_loaded:579 - 账户加载完成: 2个账户
2025-06-30 08:37:19.698 | DEBUG    | ui.views.account_view:_update_group_counts:185 - 分组计数已更新: {2: 1, 1: 1}
2025-06-30 08:37:19.707 | INFO     | app.services.account_service:batch_auto_login:1204 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-06-30 08:37:19.707 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-30 08:37:19.709 | INFO     | app.services.account_service:batch_auto_login:1274 - 服务层：准备调用核心层处理 2 个账户的自动登录。
2025-06-30 08:37:19.709 | INFO     | app.services.account_service:batch_auto_login:1284 - 服务层：设置核心层任务超时为 120 秒。
2025-06-30 08:37:19.709 | INFO     | core.telegram.client_manager:batch_auto_login:1216 - 开始批量自动登录, 共 2 个账户, 最大并发 5
2025-06-30 08:37:19.709 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:259 - 创建客户端: H:\PyProject\TeleTest\APPDATA\sessions\+***********, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-06-30 08:37:19.710 | DEBUG    | core.telegram.client_manager:get_proxy:96 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-06-30 08:37:19.710 | DEBUG    | core.telegram.client_manager:get_proxy:143 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-06-30 08:37:19.713 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:259 - 创建客户端: H:\PyProject\TeleTest\APPDATA\sessions\+***********, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-06-30 08:37:19.713 | DEBUG    | core.telegram.client_manager:get_proxy:96 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-06-30 08:37:19.714 | DEBUG    | core.telegram.client_manager:get_proxy:143 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-06-30 08:37:19.736 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-30 08:37:19.821 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-30 08:37:19.859 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-30 08:37:19.891 | INFO     | core.auth.api_service:verify_vip:438 - 会员验证成功
2025-06-30 08:37:19.891 | INFO     | utils.vip_checker:_check_vip_status:76 - VIP状态有效
2025-06-30 08:37:23.346 | INFO     | core.telegram.client_manager:_connect_client:207 - 连接成功!
2025-06-30 08:37:24.369 | INFO     | core.telegram.client_manager:_verify_client_authorization:1191 - 账户已授权: +***********
2025-06-30 08:37:25.870 | INFO     | core.telegram.client_manager:_connect_client:207 - 连接成功!
2025-06-30 08:37:26.789 | INFO     | core.telegram.client_manager:_verify_client_authorization:1191 - 账户已授权: +***********
2025-06-30 08:37:28.790 | INFO     | core.telegram.client_manager:batch_auto_login:1365 - 批量登录完成: 总计 2, 成功 2, 失败 0
2025-06-30 08:37:29.740 | INFO     | app.services.account_service:batch_auto_login:1305 - 服务层：核心层登录完成: 成功 2, 失败 0 / 2
2025-06-30 08:38:18.678 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-30 08:39:18.698 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-30 08:40:18.677 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-30 08:41:18.678 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-30 08:42:18.677 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-30 08:43:18.678 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-30 08:44:18.681 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-30 08:45:18.678 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-30 08:46:18.690 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-30 08:47:18.678 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-30 08:48:18.681 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-30 08:49:14.610 | INFO     | utils.logger:init_logger:98 - 日志系统初始化完成，默认级别: DEBUG，日志目录: H:\PyProject\TeleTest\APPDATA\logs
2025-06-30 08:49:15.546 | INFO     | data.database:init_database:31 - 初始化数据库连接: H:\PyProject\TeleTest\APPDATA\database\telegram_manager.db
2025-06-30 08:49:15.562 | DEBUG    | data.database:init_database:58 - 创建数据库表
2025-06-30 08:49:15.572 | INFO     | data.database:init_database:61 - 数据库初始化完成
2025-06-30 08:49:16.706 | DEBUG    | app.controllers.auth_controller:initialize_software:43 - 控制器: 初始化软件 app_id=1000, version=1.2.7
2025-06-30 08:49:16.707 | DEBUG    | core.auth.api_service:init_software:55 - 发送初始化请求: https://server.xile188.com/api/user/1000/windowns10/1.2.7/ini, params={}
2025-06-30 08:49:17.038 | DEBUG    | core.auth.api_service:init_software:66 - 初始化数据解密成功: {'new_content': '- 新增功能\n  - 1.新增图片上传功能\n  - 2.新增软件文档\n  - 3.新增软件自动更新，后续更新会自动检测版本更新，无需再下载更新包\n  - 4.新增设置账户信息及发信控制\n- 修复bug\n  - 删除监听任务时，不删除对应任务采集到的数据。\n  - 监听设置群无法发送到目标群\n  - 导出监听数据时应当导出单个任务而非所有数据\n- 优化记录\n  - 优化发信规则，直接调取符合要求的账户发送，无账户时才需要等待延时。\n  - 完善软件设置', 'new_url': None, 'notice': {'id': '2', 'content': '公告内容测试3', 'time': '**********'}}
2025-06-30 08:49:17.046 | INFO     | app.controllers.auth_controller:login:61 - 控制器: 用户登录 account=demo01
2025-06-30 08:49:19.953 | INFO     | core.auth.api_service:login:127 - 用户登录: account=demo01
2025-06-30 08:49:20.189 | INFO     | core.auth.api_service:get_user_info:347 - 获取用户信息
2025-06-30 08:49:20.467 | INFO     | core.auth.api_service:get_user_info:375 - 获取用户信息成功
2025-06-30 08:49:20.473 | INFO     | __main__:on_auth_completed:93 - 用户登录成功，正在启动主窗口...
2025-06-30 08:49:20.493 | INFO     | core.telegram.client_worker:__init__:61 - Telegram客户端工作线程初始化
2025-06-30 08:49:20.493 | INFO     | utils.vip_checker:initialize:25 - VIP检查器已初始化，等待登录后开始检查
2025-06-30 08:49:20.493 | INFO     | ui.main_window:_initialize_core_components:107 - MainWindow: 初始化核心组件...
2025-06-30 08:49:20.494 | INFO     | app.services.account_service:__init__:42 - 账户服务初始化
2025-06-30 08:49:20.494 | INFO     | app.controllers.account_controller:__init__:81 - 账户控制器初始化
2025-06-30 08:49:20.494 | INFO     | app.services.monitor_service:__init__:39 - 初始化监控任务服务
2025-06-30 08:49:20.494 | INFO     | core.monitor.filter:__init__:26 - 初始化消息过滤器
2025-06-30 08:49:20.494 | INFO     | core.telegram.client_worker:run:65 - Telegram客户端工作线程开始运行
2025-06-30 08:49:20.494 | INFO     | core.monitor.extractor:__init__:24 - 初始化关键词提取器
2025-06-30 08:49:20.495 | INFO     | core.proxy.proxy_task_manager:run:284 - 代理任务管理器线程启动
2025-06-30 08:49:20.495 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-06-30 08:49:20.495 | INFO     | core.monitor.notifier:__init__:90 - 初始化通知管理器
2025-06-30 08:49:20.495 | INFO     | app.controllers.telegram_monitor_controller:__init__:64 - 初始化 Telegram 监控控制器
2025-06-30 08:49:20.495 | DEBUG    | app.controllers.telegram_monitor_controller:__init__:82 - 控制器初始化完成
2025-06-30 08:49:20.498 | INFO     | core.telegram.client_manager:__init__:66 - Telegram客户端管理器初始化
2025-06-30 08:49:20.498 | INFO     | core.telegram.user_manager:__init__:30 - Telegram用户管理器初始化
2025-06-30 08:49:20.499 | INFO     | core.telegram.group_manager:__init__:36 - Telegram群组管理器初始化
2025-06-30 08:49:20.499 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-06-30 08:49:20.500 | INFO     | core.telegram.monitor_manager:__init__:26 - Telegram监控管理器初始化
2025-06-30 08:49:20.502 | INFO     | core.telegram.telegram_service:__init__:39 - Telegram服务初始化完成
2025-06-30 08:49:20.663 | INFO     | ui.views.monitor_view:__init__:48 - 初始化监控视图
2025-06-30 08:49:20.663 | DEBUG    | ui.views.monitor_view:_connect_signals:80 - 连接控制器信号
2025-06-30 08:49:20.846 | INFO     | ui.views.proxy_view:__init__:96 - 初始化视图，加载第一页代理数据
2025-06-30 08:49:21.049 | INFO     | __main__:on_auth_completed:97 - 主窗口已启动
2025-06-30 08:49:21.101 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:88 - 尝试连接监听日志信号...
2025-06-30 08:49:21.101 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:90 - 监听日志信号连接成功
2025-06-30 08:49:21.101 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-30 08:49:21.105 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-30 08:49:21.108 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:145 - 加载监控任务列表 (搜索关键词: '')
2025-06-30 08:49:21.108 | DEBUG    | ui.views.monitor_view:_on_loading_started:620 - 加载中: 正在加载任务列表...
2025-06-30 08:49:21.108 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-30 08:49:21.114 | INFO     | app.services.proxy_service:refresh_proxies:712 - 刷新代理列表: offset=0, limit=20
2025-06-30 08:49:21.114 | INFO     | core.proxy.proxy_task_manager:submit_task:172 - 提交耗时任务到子线程: refresh_proxies_0_20
2025-06-30 08:49:21.114 | DEBUG    | app.services.proxy_service:get_proxies:500 - 获取代理列表: offset=0, limit=20
2025-06-30 08:49:21.114 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-30 08:49:21.115 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-30 08:49:21.117 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-30 08:49:21.120 | INFO     | app.controllers.invite_controller:get_invite_tasks:38 - 获取邀请任务列表, status=None
2025-06-30 08:49:21.120 | INFO     | app.services.invite_service:get_invite_tasks:121 - 获取邀请任务列表, status=None
2025-06-30 08:49:21.120 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-30 08:49:21.139 | INFO     | app.services.invite_service:task_initialize:39 - 初始化邀请服务，重置运行中的任务为暂停状态
2025-06-30 08:49:21.141 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-30 08:49:21.145 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-30 08:49:21.145 | INFO     | utils.vip_checker:start_checking:37 - 开始VIP状态检查
2025-06-30 08:49:21.146 | DEBUG    | utils.vip_checker:_check_vip_status:61 - 正在检查VIP状态...
2025-06-30 08:49:21.146 | INFO     | core.auth.api_service:verify_vip:425 - 会员验证接口调用
2025-06-30 08:49:21.146 | INFO     | core.proxy.proxy_task_manager:task_wrapper:199 - 任务 refresh_proxies_0_20 完成
2025-06-30 08:49:21.263 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-30 08:49:21.263 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-30 08:49:21.266 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-30 08:49:21.269 | INFO     | app.services.invite_service:task_initialize:48 - 成功重置 0 个任务状态
2025-06-30 08:49:21.269 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-30 08:49:21.269 | DEBUG    | ui.views.monitor_view:_refresh_task_list:175 - 刷新任务列表，共0个任务
2025-06-30 08:49:21.269 | INFO     | ui.views.monitor_view:_refresh_task_list:189 - 没有找到监控任务
2025-06-30 08:49:21.269 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-30 08:49:21.271 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-30 08:49:21.274 | INFO     | app.services.account_service:get_all_groups:96 - 获取所有账户分组成功, 共 2 个
2025-06-30 08:49:21.274 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-30 08:49:21.275 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-30 08:49:21.276 | DEBUG    | ui.views.account_view:_on_groups_loaded:446 - 分组加载完成: 2个分组
2025-06-30 08:49:21.288 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-30 08:49:21.356 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-30 08:49:21.360 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-06-30 08:49:21.373 | INFO     | app.services.account_service:get_all_accounts:290 - 获取所有账户成功, 共 2 个
2025-06-30 08:49:21.374 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-30 08:49:21.374 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-30 08:49:21.376 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-30 08:49:21.377 | DEBUG    | ui.views.account_view:_on_accounts_loaded:579 - 账户加载完成: 2个账户
2025-06-30 08:49:21.404 | DEBUG    | ui.views.account_view:_update_group_counts:185 - 分组计数已更新: {2: 1, 1: 1}
2025-06-30 08:49:21.408 | INFO     | ui.views.account_view:_auto_login_accounts:634 - 开始调用控制器进行 2 个账户的自动登录。
2025-06-30 08:49:21.409 | INFO     | app.services.account_service:batch_auto_login:1165 - 服务层：开始批量自动登录 2 个账户, 最大并发: 5
2025-06-30 08:49:21.409 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-30 08:49:21.413 | INFO     | app.controllers.telegram_monitor_controller:_load_total_users_count_internal:191 - 成功加载总体统计数据: 总用户数 23, 今日采集 0, 日均采集 23, 运行天数 1
2025-06-30 08:49:21.413 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:156 - 成功加载 0 个任务
2025-06-30 08:49:21.413 | DEBUG    | ui.views.monitor_view:_on_loading_finished:625 - 加载完成
2025-06-30 08:49:21.416 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=True
2025-06-30 08:49:21.418 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=True
2025-06-30 08:49:21.421 | INFO     | ui.views.proxy_view:_load_initial_data:1044 - 初始数据加载完成，共 1 个代理
2025-06-30 08:49:21.425 | INFO     | app.services.account_service:get_all_accounts:290 - 获取所有账户成功, 共 2 个
2025-06-30 08:49:21.425 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-30 08:49:21.427 | INFO     | app.services.account_service:batch_auto_login:1204 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-06-30 08:49:21.427 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-30 08:49:21.428 | DEBUG    | ui.views.account_view:_update_group_counts:185 - 分组计数已更新: {2: 1, 1: 1}
2025-06-30 08:49:21.429 | DEBUG    | ui.views.account_view:_init_cache:152 - 账户缓存初始化完成，共 2 个账户
2025-06-30 08:49:21.433 | DEBUG    | ui.views.account_view:_on_accounts_loaded:579 - 账户加载完成: 2个账户
2025-06-30 08:49:21.451 | DEBUG    | ui.views.account_view:_update_group_counts:185 - 分组计数已更新: {2: 1, 1: 1}
2025-06-30 08:49:21.455 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-30 08:49:21.460 | INFO     | app.services.account_service:batch_auto_login:1204 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-06-30 08:49:21.461 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-30 08:49:21.461 | INFO     | app.services.account_service:batch_auto_login:1274 - 服务层：准备调用核心层处理 2 个账户的自动登录。
2025-06-30 08:49:21.462 | INFO     | app.services.account_service:batch_auto_login:1284 - 服务层：设置核心层任务超时为 120 秒。
2025-06-30 08:49:21.462 | INFO     | core.telegram.client_manager:batch_auto_login:1216 - 开始批量自动登录, 共 2 个账户, 最大并发 5
2025-06-30 08:49:21.462 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:259 - 创建客户端: H:\PyProject\TeleTest\APPDATA\sessions\+***********, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-06-30 08:49:21.462 | DEBUG    | core.telegram.client_manager:get_proxy:96 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-06-30 08:49:21.463 | DEBUG    | core.telegram.client_manager:get_proxy:143 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-06-30 08:49:21.465 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:259 - 创建客户端: H:\PyProject\TeleTest\APPDATA\sessions\+***********, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-06-30 08:49:21.465 | DEBUG    | core.telegram.client_manager:get_proxy:96 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-06-30 08:49:21.466 | DEBUG    | core.telegram.client_manager:get_proxy:143 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-06-30 08:49:21.483 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-30 08:49:21.548 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-30 08:49:21.588 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-30 08:49:21.620 | INFO     | core.auth.api_service:verify_vip:438 - 会员验证成功
2025-06-30 08:49:21.620 | INFO     | utils.vip_checker:_check_vip_status:76 - VIP状态有效
2025-06-30 08:49:25.666 | INFO     | core.telegram.client_manager:_connect_client:207 - 连接成功!
2025-06-30 08:49:29.732 | INFO     | core.telegram.client_manager:_connect_client:207 - 连接成功!
2025-06-30 08:49:42.094 | INFO     | core.telegram.client_manager:_verify_client_authorization:1191 - 账户已授权: +***********
2025-06-30 08:49:55.582 | INFO     | core.telegram.client_manager:_verify_client_authorization:1191 - 账户已授权: +***********
2025-06-30 08:49:57.582 | INFO     | core.telegram.client_manager:batch_auto_login:1365 - 批量登录完成: 总计 2, 成功 2, 失败 0
2025-06-30 08:49:58.491 | INFO     | app.services.account_service:batch_auto_login:1305 - 服务层：核心层登录完成: 成功 2, 失败 0 / 2
2025-06-30 08:50:20.458 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-30 08:51:20.471 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-30 08:52:20.459 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-30 08:53:20.460 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-30 08:54:20.459 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-30 08:55:20.459 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-30 08:56:20.461 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-30 08:57:20.461 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-30 08:58:20.460 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-30 08:59:20.460 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-30 09:00:20.458 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-30 09:01:20.458 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-30 09:02:20.461 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-30 09:03:20.457 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-30 09:04:20.462 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-30 09:04:36.934 | INFO     | ui.main_window:closeEvent:378 - MainWindow: 接收到关闭事件
2025-06-30 09:04:36.948 | INFO     | ui.main_window:_cleanup_before_quit:264 - MainWindow: 执行清理资源...
2025-06-30 09:04:36.949 | INFO     | core.telegram.client_worker:stop:108 - 正在停止Telegram客户端工作线程
2025-06-30 09:04:36.957 | INFO     | core.telegram.client_manager:cleanup_async:1136 - 开始清理资源并断开所有连接
2025-06-30 09:04:36.957 | INFO     | core.telegram.client_manager:disconnect_all_clients:721 - 正在断开所有客户端连接
2025-06-30 09:04:36.957 | INFO     | core.telegram.client_manager:_safe_disconnect:742 - 正在断开客户端连接: +***********
2025-06-30 09:04:36.957 | INFO     | core.telegram.client_manager:_safe_disconnect:742 - 正在断开客户端连接: +***********
2025-06-30 09:04:36.971 | INFO     | core.telegram.client_manager:_safe_disconnect:746 - 断开客户端连接成功: +***********
2025-06-30 09:04:36.971 | INFO     | core.telegram.client_manager:_safe_disconnect:746 - 断开客户端连接成功: +***********
2025-06-30 09:04:36.971 | INFO     | core.telegram.client_manager:cleanup_async:1155 - 资源清理完成
2025-06-30 09:04:37.457 | INFO     | core.telegram.client_manager:cleanup_async:1136 - 开始清理资源并断开所有连接
2025-06-30 09:04:37.457 | INFO     | core.telegram.client_manager:disconnect_all_clients:721 - 正在断开所有客户端连接
2025-06-30 09:04:37.457 | INFO     | core.telegram.client_manager:cleanup_async:1155 - 资源清理完成
2025-06-30 09:04:37.957 | INFO     | core.telegram.client_worker:run:97 - Telegram客户端工作线程已退出
2025-06-30 09:04:37.957 | INFO     | core.telegram.client_worker:stop:122 - Telegram客户端工作线程已停止
2025-06-30 09:04:37.957 | INFO     | ui.main_window:_cleanup_before_quit:273 - TelegramClientWorker 已停止。
2025-06-30 09:04:37.957 | INFO     | ui.main_window:_cleanup_before_quit:277 - MainWindow 清理完成
2025-06-30 09:04:37.963 | INFO     | __main__:main:111 - 应用程序已正常退出
2025-06-30 09:04:42.750 | INFO     | utils.logger:init_logger:98 - 日志系统初始化完成，默认级别: DEBUG，日志目录: H:\PyProject\TeleTest\APPDATA\logs
2025-06-30 09:04:43.820 | INFO     | data.database:init_database:31 - 初始化数据库连接: H:\PyProject\TeleTest\APPDATA\database\telegram_manager.db
2025-06-30 09:04:43.836 | DEBUG    | data.database:init_database:58 - 创建数据库表
2025-06-30 09:04:43.846 | INFO     | data.database:init_database:61 - 数据库初始化完成
2025-06-30 09:04:44.557 | DEBUG    | app.controllers.auth_controller:initialize_software:43 - 控制器: 初始化软件 app_id=1000, version=1.2.7
2025-06-30 09:04:44.557 | DEBUG    | core.auth.api_service:init_software:55 - 发送初始化请求: https://server.xile188.com/api/user/1000/windowns10/1.2.7/ini, params={}
2025-06-30 09:04:44.859 | DEBUG    | core.auth.api_service:init_software:66 - 初始化数据解密成功: {'new_content': '- 新增功能\n  - 1.新增图片上传功能\n  - 2.新增软件文档\n  - 3.新增软件自动更新，后续更新会自动检测版本更新，无需再下载更新包\n  - 4.新增设置账户信息及发信控制\n- 修复bug\n  - 删除监听任务时，不删除对应任务采集到的数据。\n  - 监听设置群无法发送到目标群\n  - 导出监听数据时应当导出单个任务而非所有数据\n- 优化记录\n  - 优化发信规则，直接调取符合要求的账户发送，无账户时才需要等待延时。\n  - 完善软件设置', 'new_url': None, 'notice': {'id': '2', 'content': '公告内容测试3', 'time': '**********'}}
2025-06-30 09:04:44.866 | INFO     | app.controllers.auth_controller:login:61 - 控制器: 用户登录 account=demo01
2025-06-30 09:04:47.794 | INFO     | core.auth.api_service:login:127 - 用户登录: account=demo01
2025-06-30 09:04:48.015 | INFO     | core.auth.api_service:get_user_info:347 - 获取用户信息
2025-06-30 09:04:48.329 | INFO     | core.auth.api_service:get_user_info:375 - 获取用户信息成功
2025-06-30 09:04:48.335 | INFO     | __main__:on_auth_completed:93 - 用户登录成功，正在启动主窗口...
2025-06-30 09:04:48.355 | INFO     | core.telegram.client_worker:__init__:61 - Telegram客户端工作线程初始化
2025-06-30 09:04:48.355 | INFO     | utils.vip_checker:initialize:25 - VIP检查器已初始化，等待登录后开始检查
2025-06-30 09:04:48.356 | INFO     | ui.main_window:_initialize_core_components:107 - MainWindow: 初始化核心组件...
2025-06-30 09:04:48.356 | INFO     | app.services.account_service:__init__:42 - 账户服务初始化
2025-06-30 09:04:48.356 | INFO     | core.telegram.client_worker:run:65 - Telegram客户端工作线程开始运行
2025-06-30 09:04:48.356 | INFO     | core.proxy.proxy_task_manager:run:284 - 代理任务管理器线程启动
2025-06-30 09:04:48.357 | INFO     | app.controllers.account_controller:__init__:81 - 账户控制器初始化
2025-06-30 09:04:48.357 | INFO     | app.services.monitor_service:__init__:39 - 初始化监控任务服务
2025-06-30 09:04:48.357 | INFO     | core.monitor.filter:__init__:26 - 初始化消息过滤器
2025-06-30 09:04:48.357 | INFO     | core.monitor.extractor:__init__:24 - 初始化关键词提取器
2025-06-30 09:04:48.357 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-06-30 09:04:48.357 | INFO     | core.monitor.notifier:__init__:90 - 初始化通知管理器
2025-06-30 09:04:48.358 | INFO     | core.telegram.client_manager:__init__:66 - Telegram客户端管理器初始化
2025-06-30 09:04:48.358 | INFO     | app.controllers.telegram_monitor_controller:__init__:64 - 初始化 Telegram 监控控制器
2025-06-30 09:04:48.358 | INFO     | core.telegram.user_manager:__init__:30 - Telegram用户管理器初始化
2025-06-30 09:04:48.359 | DEBUG    | app.controllers.telegram_monitor_controller:__init__:82 - 控制器初始化完成
2025-06-30 09:04:48.359 | INFO     | core.telegram.group_manager:__init__:36 - Telegram群组管理器初始化
2025-06-30 09:04:48.360 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-06-30 09:04:48.360 | INFO     | core.telegram.monitor_manager:__init__:26 - Telegram监控管理器初始化
2025-06-30 09:04:48.361 | INFO     | core.telegram.telegram_service:__init__:39 - Telegram服务初始化完成
2025-06-30 09:04:48.567 | INFO     | ui.views.monitor_view:__init__:48 - 初始化监控视图
2025-06-30 09:04:48.568 | DEBUG    | ui.views.monitor_view:_connect_signals:80 - 连接控制器信号
2025-06-30 09:04:48.758 | INFO     | ui.views.proxy_view:__init__:96 - 初始化视图，加载第一页代理数据
2025-06-30 09:04:48.989 | INFO     | __main__:on_auth_completed:97 - 主窗口已启动
2025-06-30 09:04:49.027 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:88 - 尝试连接监听日志信号...
2025-06-30 09:04:49.028 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:90 - 监听日志信号连接成功
2025-06-30 09:04:49.028 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-30 09:04:49.031 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-30 09:04:49.033 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:145 - 加载监控任务列表 (搜索关键词: '')
2025-06-30 09:04:49.034 | DEBUG    | ui.views.monitor_view:_on_loading_started:620 - 加载中: 正在加载任务列表...
2025-06-30 09:04:49.034 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-30 09:04:49.039 | INFO     | app.services.proxy_service:refresh_proxies:712 - 刷新代理列表: offset=0, limit=20
2025-06-30 09:04:49.039 | INFO     | core.proxy.proxy_task_manager:submit_task:172 - 提交耗时任务到子线程: refresh_proxies_0_20
2025-06-30 09:04:49.039 | DEBUG    | app.services.proxy_service:get_proxies:500 - 获取代理列表: offset=0, limit=20
2025-06-30 09:04:49.039 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-30 09:04:49.040 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-30 09:04:49.041 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-30 09:04:49.044 | INFO     | app.controllers.invite_controller:get_invite_tasks:38 - 获取邀请任务列表, status=None
2025-06-30 09:04:49.045 | INFO     | app.services.invite_service:get_invite_tasks:121 - 获取邀请任务列表, status=None
2025-06-30 09:04:49.045 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-30 09:04:49.063 | INFO     | app.services.invite_service:task_initialize:39 - 初始化邀请服务，重置运行中的任务为暂停状态
2025-06-30 09:04:49.067 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-30 09:04:49.070 | INFO     | utils.vip_checker:start_checking:37 - 开始VIP状态检查
2025-06-30 09:04:49.070 | DEBUG    | utils.vip_checker:_check_vip_status:61 - 正在检查VIP状态...
2025-06-30 09:04:49.070 | INFO     | core.auth.api_service:verify_vip:425 - 会员验证接口调用
2025-06-30 09:04:49.072 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-30 09:04:49.073 | INFO     | core.proxy.proxy_task_manager:task_wrapper:199 - 任务 refresh_proxies_0_20 完成
2025-06-30 09:04:49.290 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-30 09:04:49.293 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-30 09:04:49.298 | DEBUG    | ui.views.monitor_view:_refresh_task_list:175 - 刷新任务列表，共0个任务
2025-06-30 09:04:49.299 | INFO     | ui.views.monitor_view:_refresh_task_list:189 - 没有找到监控任务
2025-06-30 09:04:49.299 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-30 09:04:49.301 | INFO     | app.services.invite_service:task_initialize:48 - 成功重置 0 个任务状态
2025-06-30 09:04:49.301 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-30 09:04:49.303 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-30 09:04:49.307 | INFO     | app.services.account_service:get_all_groups:96 - 获取所有账户分组成功, 共 2 个
2025-06-30 09:04:49.307 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-30 09:04:49.307 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-30 09:04:49.310 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-30 09:04:49.312 | DEBUG    | ui.views.account_view:_on_groups_loaded:446 - 分组加载完成: 2个分组
2025-06-30 09:04:49.324 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-30 09:04:49.385 | INFO     | app.services.account_service:get_all_accounts:290 - 获取所有账户成功, 共 2 个
2025-06-30 09:04:49.385 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-30 09:04:49.386 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-30 09:04:49.386 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-30 09:04:49.388 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-06-30 09:04:49.398 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-30 09:04:49.400 | INFO     | app.controllers.telegram_monitor_controller:_load_total_users_count_internal:191 - 成功加载总体统计数据: 总用户数 23, 今日采集 0, 日均采集 23, 运行天数 1
2025-06-30 09:04:49.400 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:156 - 成功加载 0 个任务
2025-06-30 09:04:49.400 | DEBUG    | ui.views.monitor_view:_on_loading_finished:625 - 加载完成
2025-06-30 09:04:49.400 | DEBUG    | ui.views.account_view:_on_accounts_loaded:579 - 账户加载完成: 2个账户
2025-06-30 09:04:49.421 | DEBUG    | ui.views.account_view:_update_group_counts:185 - 分组计数已更新: {2: 1, 1: 1}
2025-06-30 09:04:49.425 | INFO     | ui.views.account_view:_auto_login_accounts:634 - 开始调用控制器进行 2 个账户的自动登录。
2025-06-30 09:04:49.425 | INFO     | app.services.account_service:batch_auto_login:1165 - 服务层：开始批量自动登录 2 个账户, 最大并发: 5
2025-06-30 09:04:49.425 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-30 09:04:49.431 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=True
2025-06-30 09:04:49.433 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=True
2025-06-30 09:04:49.435 | INFO     | ui.views.proxy_view:_load_initial_data:1044 - 初始数据加载完成，共 1 个代理
2025-06-30 09:04:49.438 | INFO     | app.services.account_service:batch_auto_login:1204 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-06-30 09:04:49.438 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-30 09:04:49.439 | INFO     | app.services.account_service:get_all_accounts:290 - 获取所有账户成功, 共 2 个
2025-06-30 09:04:49.439 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-30 09:04:49.440 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-30 09:04:49.442 | DEBUG    | ui.views.account_view:_update_group_counts:185 - 分组计数已更新: {2: 1, 1: 1}
2025-06-30 09:04:49.442 | DEBUG    | ui.views.account_view:_init_cache:152 - 账户缓存初始化完成，共 2 个账户
2025-06-30 09:04:49.446 | DEBUG    | ui.views.account_view:_on_accounts_loaded:579 - 账户加载完成: 2个账户
2025-06-30 09:04:49.464 | DEBUG    | ui.views.account_view:_update_group_counts:185 - 分组计数已更新: {2: 1, 1: 1}
2025-06-30 09:04:49.473 | INFO     | app.services.account_service:batch_auto_login:1204 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-06-30 09:04:49.473 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-30 09:04:49.474 | INFO     | app.services.account_service:batch_auto_login:1274 - 服务层：准备调用核心层处理 2 个账户的自动登录。
2025-06-30 09:04:49.474 | INFO     | app.services.account_service:batch_auto_login:1284 - 服务层：设置核心层任务超时为 120 秒。
2025-06-30 09:04:49.475 | INFO     | core.telegram.client_manager:batch_auto_login:1216 - 开始批量自动登录, 共 2 个账户, 最大并发 5
2025-06-30 09:04:49.475 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:259 - 创建客户端: H:\PyProject\TeleTest\APPDATA\sessions\+***********, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-06-30 09:04:49.475 | DEBUG    | core.telegram.client_manager:get_proxy:96 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-06-30 09:04:49.476 | DEBUG    | core.telegram.client_manager:get_proxy:143 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-06-30 09:04:49.479 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:259 - 创建客户端: H:\PyProject\TeleTest\APPDATA\sessions\+***********, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-06-30 09:04:49.479 | DEBUG    | core.telegram.client_manager:get_proxy:96 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-06-30 09:04:49.479 | DEBUG    | core.telegram.client_manager:get_proxy:143 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-06-30 09:04:49.498 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-30 09:04:49.588 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-30 09:04:49.596 | INFO     | core.auth.api_service:verify_vip:438 - 会员验证成功
2025-06-30 09:04:49.596 | INFO     | utils.vip_checker:_check_vip_status:76 - VIP状态有效
2025-06-30 09:04:49.634 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-30 09:04:53.100 | INFO     | core.telegram.client_manager:_connect_client:207 - 连接成功!
2025-06-30 09:04:53.668 | INFO     | core.telegram.client_manager:_connect_client:207 - 连接成功!
2025-06-30 09:04:54.124 | INFO     | core.telegram.client_manager:_verify_client_authorization:1191 - 账户已授权: +***********
2025-06-30 09:04:55.570 | INFO     | core.telegram.client_manager:_verify_client_authorization:1191 - 账户已授权: +***********
2025-06-30 09:04:57.577 | INFO     | core.telegram.client_manager:batch_auto_login:1365 - 批量登录完成: 总计 2, 成功 2, 失败 0
2025-06-30 09:04:58.494 | INFO     | app.services.account_service:batch_auto_login:1305 - 服务层：核心层登录完成: 成功 2, 失败 0 / 2
2025-06-30 09:05:48.329 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-30 09:06:48.326 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-30 09:07:48.319 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-30 09:08:48.319 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-30 09:09:48.330 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-30 09:10:48.324 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-30 09:11:48.320 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-30 09:12:48.320 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-30 09:13:48.329 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-30 09:14:48.320 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-30 09:15:48.318 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-30 09:16:48.330 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-30 09:17:48.329 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-30 09:18:48.331 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-30 09:19:48.323 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-30 09:20:48.321 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-30 09:21:48.324 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-30 09:22:48.321 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-30 09:23:48.319 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-30 09:24:48.320 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-30 09:25:48.318 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-30 09:26:11.085 | INFO     | ui.main_window:closeEvent:378 - MainWindow: 接收到关闭事件
2025-06-30 09:26:11.097 | INFO     | ui.main_window:_cleanup_before_quit:264 - MainWindow: 执行清理资源...
2025-06-30 09:26:11.097 | INFO     | core.telegram.client_worker:stop:108 - 正在停止Telegram客户端工作线程
2025-06-30 09:26:11.102 | INFO     | core.telegram.client_manager:cleanup_async:1136 - 开始清理资源并断开所有连接
2025-06-30 09:26:11.103 | INFO     | core.telegram.client_manager:disconnect_all_clients:721 - 正在断开所有客户端连接
2025-06-30 09:26:11.103 | INFO     | core.telegram.client_manager:_safe_disconnect:742 - 正在断开客户端连接: +***********
2025-06-30 09:26:11.103 | INFO     | core.telegram.client_manager:_safe_disconnect:742 - 正在断开客户端连接: +***********
2025-06-30 09:26:11.110 | INFO     | core.telegram.client_manager:_safe_disconnect:746 - 断开客户端连接成功: +***********
2025-06-30 09:26:11.116 | INFO     | core.telegram.client_manager:_safe_disconnect:746 - 断开客户端连接成功: +***********
2025-06-30 09:26:11.116 | INFO     | core.telegram.client_manager:cleanup_async:1155 - 资源清理完成
2025-06-30 09:26:11.601 | INFO     | core.telegram.client_manager:cleanup_async:1136 - 开始清理资源并断开所有连接
2025-06-30 09:26:11.601 | INFO     | core.telegram.client_manager:disconnect_all_clients:721 - 正在断开所有客户端连接
2025-06-30 09:26:11.601 | INFO     | core.telegram.client_manager:cleanup_async:1155 - 资源清理完成
2025-06-30 09:26:12.101 | INFO     | core.telegram.client_worker:run:97 - Telegram客户端工作线程已退出
2025-06-30 09:26:12.101 | INFO     | core.telegram.client_worker:stop:122 - Telegram客户端工作线程已停止
2025-06-30 09:26:12.101 | INFO     | ui.main_window:_cleanup_before_quit:273 - TelegramClientWorker 已停止。
2025-06-30 09:26:12.101 | INFO     | ui.main_window:_cleanup_before_quit:277 - MainWindow 清理完成
2025-06-30 09:26:12.111 | INFO     | __main__:main:111 - 应用程序已正常退出
2025-06-30 09:26:20.048 | INFO     | utils.logger:init_logger:98 - 日志系统初始化完成，默认级别: DEBUG，日志目录: H:\PyProject\TeleTest\APPDATA\logs
2025-06-30 09:26:20.984 | INFO     | data.database:init_database:31 - 初始化数据库连接: H:\PyProject\TeleTest\APPDATA\database\telegram_manager.db
2025-06-30 09:26:20.999 | DEBUG    | data.database:init_database:58 - 创建数据库表
2025-06-30 09:26:21.010 | INFO     | data.database:init_database:61 - 数据库初始化完成
2025-06-30 09:26:22.098 | DEBUG    | app.controllers.auth_controller:initialize_software:43 - 控制器: 初始化软件 app_id=1000, version=1.2.7
2025-06-30 09:26:22.098 | DEBUG    | core.auth.api_service:init_software:55 - 发送初始化请求: https://server.xile188.com/api/user/1000/windowns10/1.2.7/ini, params={}
2025-06-30 09:26:22.341 | DEBUG    | core.auth.api_service:init_software:66 - 初始化数据解密成功: {'new_content': '- 新增功能\n  - 1.新增图片上传功能\n  - 2.新增软件文档\n  - 3.新增软件自动更新，后续更新会自动检测版本更新，无需再下载更新包\n  - 4.新增设置账户信息及发信控制\n- 修复bug\n  - 删除监听任务时，不删除对应任务采集到的数据。\n  - 监听设置群无法发送到目标群\n  - 导出监听数据时应当导出单个任务而非所有数据\n- 优化记录\n  - 优化发信规则，直接调取符合要求的账户发送，无账户时才需要等待延时。\n  - 完善软件设置', 'new_url': None, 'notice': {'id': '2', 'content': '公告内容测试3', 'time': '**********'}}
2025-06-30 09:26:22.347 | INFO     | app.controllers.auth_controller:login:61 - 控制器: 用户登录 account=demo01
2025-06-30 09:26:25.273 | INFO     | core.auth.api_service:login:127 - 用户登录: account=demo01
2025-06-30 09:26:25.488 | INFO     | core.auth.api_service:get_user_info:347 - 获取用户信息
2025-06-30 09:26:25.764 | INFO     | core.auth.api_service:get_user_info:375 - 获取用户信息成功
2025-06-30 09:26:25.770 | INFO     | __main__:on_auth_completed:93 - 用户登录成功，正在启动主窗口...
2025-06-30 09:26:25.793 | INFO     | core.telegram.client_worker:__init__:61 - Telegram客户端工作线程初始化
2025-06-30 09:26:25.793 | INFO     | utils.vip_checker:initialize:25 - VIP检查器已初始化，等待登录后开始检查
2025-06-30 09:26:25.793 | INFO     | ui.main_window:_initialize_core_components:107 - MainWindow: 初始化核心组件...
2025-06-30 09:26:25.794 | INFO     | core.telegram.client_worker:run:65 - Telegram客户端工作线程开始运行
2025-06-30 09:26:25.794 | INFO     | app.services.account_service:__init__:42 - 账户服务初始化
2025-06-30 09:26:25.794 | INFO     | core.proxy.proxy_task_manager:run:284 - 代理任务管理器线程启动
2025-06-30 09:26:25.794 | INFO     | app.controllers.account_controller:__init__:81 - 账户控制器初始化
2025-06-30 09:26:25.794 | INFO     | app.services.monitor_service:__init__:39 - 初始化监控任务服务
2025-06-30 09:26:25.795 | INFO     | core.monitor.filter:__init__:26 - 初始化消息过滤器
2025-06-30 09:26:25.795 | INFO     | core.monitor.extractor:__init__:24 - 初始化关键词提取器
2025-06-30 09:26:25.795 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-06-30 09:26:25.795 | INFO     | core.monitor.notifier:__init__:90 - 初始化通知管理器
2025-06-30 09:26:25.795 | INFO     | app.controllers.telegram_monitor_controller:__init__:64 - 初始化 Telegram 监控控制器
2025-06-30 09:26:25.796 | DEBUG    | app.controllers.telegram_monitor_controller:__init__:82 - 控制器初始化完成
2025-06-30 09:26:25.796 | INFO     | core.telegram.client_manager:__init__:66 - Telegram客户端管理器初始化
2025-06-30 09:26:25.797 | INFO     | core.telegram.user_manager:__init__:30 - Telegram用户管理器初始化
2025-06-30 09:26:25.797 | INFO     | core.telegram.group_manager:__init__:36 - Telegram群组管理器初始化
2025-06-30 09:26:25.798 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-06-30 09:26:25.798 | INFO     | core.telegram.monitor_manager:__init__:26 - Telegram监控管理器初始化
2025-06-30 09:26:25.798 | INFO     | core.telegram.telegram_service:__init__:39 - Telegram服务初始化完成
2025-06-30 09:26:25.886 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-30 09:26:25.933 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:88 - 尝试连接监听日志信号...
2025-06-30 09:26:25.933 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:90 - 监听日志信号连接成功
2025-06-30 09:26:25.941 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-30 09:27:25.764 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-30 09:28:25.757 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-30 09:29:25.756 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-30 09:30:25.755 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-30 09:31:48.075 | INFO     | utils.logger:init_logger:98 - 日志系统初始化完成，默认级别: DEBUG，日志目录: H:\PyProject\TeleTest\APPDATA\logs
2025-06-30 09:31:49.073 | INFO     | data.database:init_database:31 - 初始化数据库连接: H:\PyProject\TeleTest\APPDATA\database\telegram_manager.db
2025-06-30 09:31:49.090 | DEBUG    | data.database:init_database:58 - 创建数据库表
2025-06-30 09:31:49.100 | INFO     | data.database:init_database:61 - 数据库初始化完成
2025-06-30 09:31:49.754 | DEBUG    | app.controllers.auth_controller:initialize_software:43 - 控制器: 初始化软件 app_id=1000, version=1.2.7
2025-06-30 09:31:49.754 | DEBUG    | core.auth.api_service:init_software:55 - 发送初始化请求: https://server.xile188.com/api/user/1000/windowns10/1.2.7/ini, params={}
2025-06-30 09:31:50.070 | DEBUG    | core.auth.api_service:init_software:66 - 初始化数据解密成功: {'new_content': '- 新增功能\n  - 1.新增图片上传功能\n  - 2.新增软件文档\n  - 3.新增软件自动更新，后续更新会自动检测版本更新，无需再下载更新包\n  - 4.新增设置账户信息及发信控制\n- 修复bug\n  - 删除监听任务时，不删除对应任务采集到的数据。\n  - 监听设置群无法发送到目标群\n  - 导出监听数据时应当导出单个任务而非所有数据\n- 优化记录\n  - 优化发信规则，直接调取符合要求的账户发送，无账户时才需要等待延时。\n  - 完善软件设置', 'new_url': None, 'notice': {'id': '2', 'content': '公告内容测试3', 'time': '**********'}}
2025-06-30 09:31:50.078 | INFO     | app.controllers.auth_controller:login:61 - 控制器: 用户登录 account=demo01
2025-06-30 09:31:53.017 | INFO     | core.auth.api_service:login:127 - 用户登录: account=demo01
2025-06-30 09:31:53.286 | INFO     | core.auth.api_service:get_user_info:347 - 获取用户信息
2025-06-30 09:31:53.555 | INFO     | core.auth.api_service:get_user_info:375 - 获取用户信息成功
2025-06-30 09:31:53.561 | INFO     | __main__:on_auth_completed:93 - 用户登录成功，正在启动主窗口...
2025-06-30 09:31:53.586 | INFO     | core.telegram.client_worker:__init__:61 - Telegram客户端工作线程初始化
2025-06-30 09:31:53.586 | INFO     | utils.vip_checker:initialize:25 - VIP检查器已初始化，等待登录后开始检查
2025-06-30 09:31:53.586 | INFO     | ui.main_window:_initialize_core_components:107 - MainWindow: 初始化核心组件...
2025-06-30 09:31:53.587 | INFO     | app.services.account_service:__init__:42 - 账户服务初始化
2025-06-30 09:31:53.587 | INFO     | app.controllers.account_controller:__init__:81 - 账户控制器初始化
2025-06-30 09:31:53.587 | INFO     | core.telegram.client_worker:run:65 - Telegram客户端工作线程开始运行
2025-06-30 09:31:53.587 | INFO     | core.proxy.proxy_task_manager:run:284 - 代理任务管理器线程启动
2025-06-30 09:31:53.588 | INFO     | app.services.monitor_service:__init__:39 - 初始化监控任务服务
2025-06-30 09:31:53.588 | INFO     | core.monitor.filter:__init__:26 - 初始化消息过滤器
2025-06-30 09:31:53.588 | INFO     | core.monitor.extractor:__init__:24 - 初始化关键词提取器
2025-06-30 09:31:53.588 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-06-30 09:31:53.588 | INFO     | core.monitor.notifier:__init__:90 - 初始化通知管理器
2025-06-30 09:31:53.589 | INFO     | app.controllers.telegram_monitor_controller:__init__:64 - 初始化 Telegram 监控控制器
2025-06-30 09:31:53.589 | DEBUG    | app.controllers.telegram_monitor_controller:__init__:82 - 控制器初始化完成
2025-06-30 09:31:53.590 | INFO     | core.telegram.client_manager:__init__:66 - Telegram客户端管理器初始化
2025-06-30 09:31:53.590 | INFO     | core.telegram.user_manager:__init__:30 - Telegram用户管理器初始化
2025-06-30 09:31:53.591 | INFO     | core.telegram.group_manager:__init__:36 - Telegram群组管理器初始化
2025-06-30 09:31:53.591 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-06-30 09:31:53.592 | INFO     | core.telegram.monitor_manager:__init__:26 - Telegram监控管理器初始化
2025-06-30 09:31:53.592 | INFO     | core.telegram.telegram_service:__init__:39 - Telegram服务初始化完成
2025-06-30 09:31:53.784 | INFO     | ui.views.monitor_view:__init__:48 - 初始化监控视图
2025-06-30 09:31:53.784 | DEBUG    | ui.views.monitor_view:_connect_signals:80 - 连接控制器信号
2025-06-30 09:31:53.958 | INFO     | ui.views.proxy_view:__init__:96 - 初始化视图，加载第一页代理数据
2025-06-30 09:31:54.247 | INFO     | __main__:on_auth_completed:97 - 主窗口已启动
2025-06-30 09:31:54.297 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:88 - 尝试连接监听日志信号...
2025-06-30 09:31:54.297 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:90 - 监听日志信号连接成功
2025-06-30 09:31:54.297 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-30 09:31:54.301 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-30 09:31:54.303 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:145 - 加载监控任务列表 (搜索关键词: '')
2025-06-30 09:31:54.304 | DEBUG    | ui.views.monitor_view:_on_loading_started:620 - 加载中: 正在加载任务列表...
2025-06-30 09:31:54.304 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-30 09:31:54.310 | INFO     | app.services.proxy_service:refresh_proxies:712 - 刷新代理列表: offset=0, limit=20
2025-06-30 09:31:54.310 | INFO     | core.proxy.proxy_task_manager:submit_task:172 - 提交耗时任务到子线程: refresh_proxies_0_20
2025-06-30 09:31:54.310 | DEBUG    | app.services.proxy_service:get_proxies:500 - 获取代理列表: offset=0, limit=20
2025-06-30 09:31:54.310 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-30 09:31:54.311 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-30 09:31:54.320 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-30 09:31:54.337 | INFO     | app.controllers.invite_controller:get_invite_tasks:38 - 获取邀请任务列表, status=None
2025-06-30 09:31:54.339 | INFO     | app.services.invite_service:get_invite_tasks:121 - 获取邀请任务列表, status=None
2025-06-30 09:31:54.339 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-30 09:31:54.339 | INFO     | app.services.invite_service:task_initialize:39 - 初始化邀请服务，重置运行中的任务为暂停状态
2025-06-30 09:31:54.340 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-30 09:31:54.340 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-30 09:31:54.341 | INFO     | core.proxy.proxy_task_manager:task_wrapper:199 - 任务 refresh_proxies_0_20 完成
2025-06-30 09:31:54.342 | INFO     | utils.vip_checker:start_checking:37 - 开始VIP状态检查
2025-06-30 09:31:54.343 | DEBUG    | utils.vip_checker:_check_vip_status:61 - 正在检查VIP状态...
2025-06-30 09:31:54.343 | INFO     | core.auth.api_service:verify_vip:425 - 会员验证接口调用
2025-06-30 09:31:54.671 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-30 09:31:54.696 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-30 09:31:54.730 | DEBUG    | ui.views.monitor_view:_refresh_task_list:175 - 刷新任务列表，共0个任务
2025-06-30 09:31:54.731 | INFO     | ui.views.monitor_view:_refresh_task_list:189 - 没有找到监控任务
2025-06-30 09:31:54.731 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-30 09:31:54.732 | INFO     | app.services.invite_service:task_initialize:48 - 成功重置 0 个任务状态
2025-06-30 09:31:54.732 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-30 09:31:54.733 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-30 09:31:54.735 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-30 09:31:54.736 | INFO     | app.services.account_service:get_all_groups:96 - 获取所有账户分组成功, 共 2 个
2025-06-30 09:31:54.736 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-30 09:31:54.737 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-30 09:31:54.740 | DEBUG    | ui.views.account_view:_on_groups_loaded:515 - 分组加载完成: 2个分组
2025-06-30 09:31:54.753 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-30 09:31:54.818 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-30 09:31:54.818 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-30 09:31:54.819 | INFO     | app.services.account_service:get_all_accounts:290 - 获取所有账户成功, 共 2 个
2025-06-30 09:31:54.819 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-30 09:31:54.821 | INFO     | app.controllers.telegram_monitor_controller:_load_total_users_count_internal:191 - 成功加载总体统计数据: 总用户数 23, 今日采集 0, 日均采集 23, 运行天数 1
2025-06-30 09:31:54.822 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:156 - 成功加载 0 个任务
2025-06-30 09:31:54.822 | DEBUG    | ui.views.monitor_view:_on_loading_finished:625 - 加载完成
2025-06-30 09:31:54.823 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-06-30 09:31:54.827 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-30 09:31:54.833 | DEBUG    | ui.views.account_view:_on_accounts_loaded:648 - 账户加载完成: 2个账户
2025-06-30 09:31:54.855 | DEBUG    | ui.views.account_view:_update_group_counts:186 - 分组计数已更新: {2: 1, 1: 1}
2025-06-30 09:31:54.859 | INFO     | ui.views.account_view:_auto_login_accounts:703 - 开始调用控制器进行 2 个账户的自动登录。
2025-06-30 09:31:54.859 | INFO     | app.services.account_service:batch_auto_login:1165 - 服务层：开始批量自动登录 2 个账户, 最大并发: 5
2025-06-30 09:31:54.860 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-30 09:31:54.865 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=True
2025-06-30 09:31:54.867 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=True
2025-06-30 09:31:54.868 | INFO     | ui.views.proxy_view:_load_initial_data:1044 - 初始数据加载完成，共 1 个代理
2025-06-30 09:31:54.872 | INFO     | app.services.account_service:batch_auto_login:1204 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-06-30 09:31:54.873 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-30 09:31:54.874 | INFO     | app.services.account_service:get_all_accounts:290 - 获取所有账户成功, 共 2 个
2025-06-30 09:31:54.874 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-30 09:31:54.875 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-30 09:31:54.877 | DEBUG    | ui.views.account_view:_update_group_counts:186 - 分组计数已更新: {2: 1, 1: 1}
2025-06-30 09:31:54.877 | DEBUG    | ui.views.account_view:_init_cache:153 - 账户缓存初始化完成，共 2 个账户
2025-06-30 09:31:54.881 | DEBUG    | ui.views.account_view:_on_accounts_loaded:648 - 账户加载完成: 2个账户
2025-06-30 09:31:54.905 | DEBUG    | ui.views.account_view:_update_group_counts:186 - 分组计数已更新: {2: 1, 1: 1}
2025-06-30 09:31:54.912 | INFO     | core.auth.api_service:verify_vip:438 - 会员验证成功
2025-06-30 09:31:54.913 | INFO     | utils.vip_checker:_check_vip_status:76 - VIP状态有效
2025-06-30 09:31:54.916 | INFO     | app.services.account_service:batch_auto_login:1204 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-06-30 09:31:54.917 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-30 09:31:54.918 | INFO     | app.services.account_service:batch_auto_login:1274 - 服务层：准备调用核心层处理 2 个账户的自动登录。
2025-06-30 09:31:54.918 | INFO     | app.services.account_service:batch_auto_login:1284 - 服务层：设置核心层任务超时为 120 秒。
2025-06-30 09:31:54.918 | INFO     | core.telegram.client_manager:batch_auto_login:1216 - 开始批量自动登录, 共 2 个账户, 最大并发 5
2025-06-30 09:31:54.919 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:259 - 创建客户端: H:\PyProject\TeleTest\APPDATA\sessions\+***********, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-06-30 09:31:54.919 | DEBUG    | core.telegram.client_manager:get_proxy:96 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-06-30 09:31:54.919 | DEBUG    | core.telegram.client_manager:get_proxy:143 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-06-30 09:31:54.923 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:259 - 创建客户端: H:\PyProject\TeleTest\APPDATA\sessions\+***********, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-06-30 09:31:54.923 | DEBUG    | core.telegram.client_manager:get_proxy:96 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-06-30 09:31:54.923 | DEBUG    | core.telegram.client_manager:get_proxy:143 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-06-30 09:31:54.952 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-30 09:31:55.028 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-30 09:31:55.068 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-30 09:31:58.236 | INFO     | core.telegram.client_manager:_connect_client:207 - 连接成功!
2025-06-30 09:31:58.282 | INFO     | core.telegram.client_manager:_connect_client:207 - 连接成功!
2025-06-30 09:31:59.142 | INFO     | core.telegram.client_manager:_verify_client_authorization:1191 - 账户已授权: +***********
2025-06-30 09:31:59.637 | INFO     | core.telegram.client_manager:_verify_client_authorization:1191 - 账户已授权: +***********
2025-06-30 09:32:01.638 | INFO     | core.telegram.client_manager:batch_auto_login:1365 - 批量登录完成: 总计 2, 成功 2, 失败 0
2025-06-30 09:32:01.953 | INFO     | app.services.account_service:batch_auto_login:1305 - 服务层：核心层登录完成: 成功 2, 失败 0 / 2
2025-06-30 09:32:53.553 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-30 09:33:53.553 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-30 10:58:50.896 | INFO     | utils.logger:init_logger:98 - 日志系统初始化完成，默认级别: DEBUG，日志目录: h:\PyProject\TeleTest\APPDATA\logs
2025-06-30 10:58:53.495 | INFO     | data.database:init_database:31 - 初始化数据库连接: h:\PyProject\TeleTest\APPDATA\database\telegram_manager.db
2025-06-30 10:58:53.528 | DEBUG    | data.database:init_database:58 - 创建数据库表
2025-06-30 10:58:53.546 | INFO     | data.database:init_database:61 - 数据库初始化完成
2025-06-30 10:58:54.997 | DEBUG    | app.controllers.auth_controller:initialize_software:43 - 控制器: 初始化软件 app_id=1000, version=1.2.7
2025-06-30 10:58:54.998 | DEBUG    | core.auth.api_service:init_software:55 - 发送初始化请求: https://server.xile188.com/api/user/1000/windowns10/1.2.7/ini, params={}
2025-06-30 10:58:55.445 | DEBUG    | core.auth.api_service:init_software:66 - 初始化数据解密成功: {'new_content': '- 新增功能\n  - 1.新增图片上传功能\n  - 2.新增软件文档\n  - 3.新增软件自动更新，后续更新会自动检测版本更新，无需再下载更新包\n  - 4.新增设置账户信息及发信控制\n- 修复bug\n  - 删除监听任务时，不删除对应任务采集到的数据。\n  - 监听设置群无法发送到目标群\n  - 导出监听数据时应当导出单个任务而非所有数据\n- 优化记录\n  - 优化发信规则，直接调取符合要求的账户发送，无账户时才需要等待延时。\n  - 完善软件设置', 'new_url': None, 'notice': {'id': '2', 'content': '公告内容测试3', 'time': '**********'}}
2025-06-30 10:58:55.461 | INFO     | app.controllers.auth_controller:login:61 - 控制器: 用户登录 account=demo01
2025-06-30 10:58:58.446 | INFO     | core.auth.api_service:login:127 - 用户登录: account=demo01
2025-06-30 10:58:58.736 | INFO     | core.auth.api_service:get_user_info:347 - 获取用户信息
2025-06-30 10:58:58.993 | INFO     | core.auth.api_service:get_user_info:375 - 获取用户信息成功
2025-06-30 10:58:59.002 | INFO     | __main__:on_auth_completed:93 - 用户登录成功，正在启动主窗口...
2025-06-30 10:58:59.043 | INFO     | core.telegram.client_worker:__init__:61 - Telegram客户端工作线程初始化
2025-06-30 10:58:59.044 | INFO     | utils.vip_checker:initialize:25 - VIP检查器已初始化，等待登录后开始检查
2025-06-30 10:58:59.044 | INFO     | ui.main_window:_initialize_core_components:107 - MainWindow: 初始化核心组件...
2025-06-30 10:58:59.044 | INFO     | core.telegram.client_worker:run:65 - Telegram客户端工作线程开始运行
2025-06-30 10:58:59.046 | INFO     | app.services.account_service:__init__:42 - 账户服务初始化
2025-06-30 10:58:59.046 | INFO     | core.proxy.proxy_task_manager:run:284 - 代理任务管理器线程启动
2025-06-30 10:58:59.047 | INFO     | app.controllers.account_controller:__init__:81 - 账户控制器初始化
2025-06-30 10:58:59.047 | INFO     | core.telegram.client_manager:__init__:66 - Telegram客户端管理器初始化
2025-06-30 10:58:59.048 | INFO     | app.services.monitor_service:__init__:39 - 初始化监控任务服务
2025-06-30 10:58:59.048 | INFO     | core.telegram.user_manager:__init__:30 - Telegram用户管理器初始化
2025-06-30 10:58:59.049 | INFO     | core.monitor.filter:__init__:26 - 初始化消息过滤器
2025-06-30 10:58:59.050 | INFO     | core.telegram.group_manager:__init__:36 - Telegram群组管理器初始化
2025-06-30 10:58:59.050 | INFO     | core.monitor.extractor:__init__:24 - 初始化关键词提取器
2025-06-30 10:58:59.050 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-06-30 10:58:59.050 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-06-30 10:58:59.051 | INFO     | core.telegram.monitor_manager:__init__:26 - Telegram监控管理器初始化
2025-06-30 10:58:59.051 | INFO     | core.monitor.notifier:__init__:90 - 初始化通知管理器
2025-06-30 10:58:59.051 | INFO     | core.telegram.telegram_service:__init__:39 - Telegram服务初始化完成
2025-06-30 10:58:59.052 | INFO     | app.controllers.telegram_monitor_controller:__init__:64 - 初始化 Telegram 监控控制器
2025-06-30 10:58:59.053 | DEBUG    | app.controllers.telegram_monitor_controller:__init__:82 - 控制器初始化完成
2025-06-30 10:58:59.278 | INFO     | ui.views.monitor_view:__init__:48 - 初始化监控视图
2025-06-30 10:58:59.279 | DEBUG    | ui.views.monitor_view:_connect_signals:80 - 连接控制器信号
2025-06-30 10:58:59.469 | INFO     | ui.views.proxy_view:__init__:96 - 初始化视图，加载第一页代理数据
2025-06-30 10:58:59.707 | INFO     | __main__:on_auth_completed:97 - 主窗口已启动
2025-06-30 10:58:59.760 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:88 - 尝试连接监听日志信号...
2025-06-30 10:58:59.761 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:90 - 监听日志信号连接成功
2025-06-30 10:58:59.761 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-30 10:58:59.766 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-30 10:58:59.770 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:145 - 加载监控任务列表 (搜索关键词: '')
2025-06-30 10:58:59.770 | DEBUG    | ui.views.monitor_view:_on_loading_started:620 - 加载中: 正在加载任务列表...
2025-06-30 10:58:59.771 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-30 10:58:59.777 | INFO     | app.services.proxy_service:refresh_proxies:712 - 刷新代理列表: offset=0, limit=20
2025-06-30 10:58:59.778 | INFO     | core.proxy.proxy_task_manager:submit_task:172 - 提交耗时任务到子线程: refresh_proxies_0_20
2025-06-30 10:58:59.779 | DEBUG    | app.services.proxy_service:get_proxies:500 - 获取代理列表: offset=0, limit=20
2025-06-30 10:58:59.779 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-30 10:58:59.779 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-30 10:58:59.784 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-30 10:58:59.811 | INFO     | app.controllers.invite_controller:get_invite_tasks:38 - 获取邀请任务列表, status=None
2025-06-30 10:58:59.811 | INFO     | app.services.invite_service:get_invite_tasks:121 - 获取邀请任务列表, status=None
2025-06-30 10:58:59.812 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-30 10:58:59.816 | INFO     | app.services.invite_service:task_initialize:39 - 初始化邀请服务，重置运行中的任务为暂停状态
2025-06-30 10:58:59.817 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-30 10:58:59.817 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-30 10:58:59.819 | INFO     | core.proxy.proxy_task_manager:task_wrapper:199 - 任务 refresh_proxies_0_20 完成
2025-06-30 10:58:59.821 | INFO     | utils.vip_checker:start_checking:37 - 开始VIP状态检查
2025-06-30 10:58:59.821 | DEBUG    | utils.vip_checker:_check_vip_status:61 - 正在检查VIP状态...
2025-06-30 10:58:59.821 | INFO     | core.auth.api_service:verify_vip:425 - 会员验证接口调用
2025-06-30 10:59:00.058 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-30 10:59:00.062 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-30 10:59:00.065 | INFO     | app.services.invite_service:task_initialize:48 - 成功重置 0 个任务状态
2025-06-30 10:59:00.065 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-30 10:59:00.067 | DEBUG    | ui.views.monitor_view:_refresh_task_list:175 - 刷新任务列表，共0个任务
2025-06-30 10:59:00.068 | INFO     | ui.views.monitor_view:_refresh_task_list:189 - 没有找到监控任务
2025-06-30 10:59:00.068 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-30 10:59:00.070 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-30 10:59:00.075 | INFO     | app.services.account_service:get_all_groups:96 - 获取所有账户分组成功, 共 2 个
2025-06-30 10:59:00.075 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-30 10:59:00.075 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-30 10:59:00.079 | DEBUG    | ui.views.account_view:_on_groups_loaded:515 - 分组加载完成: 2个分组
2025-06-30 10:59:00.093 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-30 10:59:00.098 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-30 10:59:00.163 | INFO     | app.services.account_service:get_all_accounts:290 - 获取所有账户成功, 共 2 个
2025-06-30 10:59:00.163 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-30 10:59:00.165 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-30 10:59:00.166 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-30 10:59:00.168 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-30 10:59:00.168 | DEBUG    | ui.views.account_view:_on_accounts_loaded:648 - 账户加载完成: 2个账户
2025-06-30 10:59:00.189 | DEBUG    | ui.views.account_view:_update_group_counts:186 - 分组计数已更新: {2: 1, 1: 1}
2025-06-30 10:59:00.193 | INFO     | ui.views.account_view:_auto_login_accounts:703 - 开始调用控制器进行 2 个账户的自动登录。
2025-06-30 10:59:00.194 | INFO     | app.services.account_service:batch_auto_login:1165 - 服务层：开始批量自动登录 2 个账户, 最大并发: 5
2025-06-30 10:59:00.194 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-30 10:59:00.198 | INFO     | app.controllers.telegram_monitor_controller:_load_total_users_count_internal:191 - 成功加载总体统计数据: 总用户数 23, 今日采集 0, 日均采集 23, 运行天数 1
2025-06-30 10:59:00.199 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:156 - 成功加载 0 个任务
2025-06-30 10:59:00.200 | DEBUG    | ui.views.monitor_view:_on_loading_finished:625 - 加载完成
2025-06-30 10:59:00.202 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-06-30 10:59:00.220 | INFO     | app.services.account_service:get_all_accounts:290 - 获取所有账户成功, 共 2 个
2025-06-30 10:59:00.220 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-30 10:59:00.221 | INFO     | app.services.account_service:batch_auto_login:1204 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-06-30 10:59:00.222 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-30 10:59:00.224 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-30 10:59:00.226 | DEBUG    | ui.views.account_view:_update_group_counts:186 - 分组计数已更新: {2: 1, 1: 1}
2025-06-30 10:59:00.226 | DEBUG    | ui.views.account_view:_init_cache:153 - 账户缓存初始化完成，共 2 个账户
2025-06-30 10:59:00.232 | DEBUG    | ui.views.account_view:_on_accounts_loaded:648 - 账户加载完成: 2个账户
2025-06-30 10:59:00.256 | DEBUG    | ui.views.account_view:_update_group_counts:186 - 分组计数已更新: {2: 1, 1: 1}
2025-06-30 10:59:00.266 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=True
2025-06-30 10:59:00.268 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=True
2025-06-30 10:59:00.271 | INFO     | ui.views.proxy_view:_load_initial_data:1044 - 初始数据加载完成，共 1 个代理
2025-06-30 10:59:00.273 | INFO     | app.services.account_service:batch_auto_login:1204 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-06-30 10:59:00.274 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-30 10:59:00.275 | INFO     | app.services.account_service:batch_auto_login:1274 - 服务层：准备调用核心层处理 2 个账户的自动登录。
2025-06-30 10:59:00.276 | INFO     | app.services.account_service:batch_auto_login:1284 - 服务层：设置核心层任务超时为 120 秒。
2025-06-30 10:59:00.276 | INFO     | core.telegram.client_manager:batch_auto_login:1216 - 开始批量自动登录, 共 2 个账户, 最大并发 5
2025-06-30 10:59:00.277 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:259 - 创建客户端: h:\PyProject\TeleTest\APPDATA\sessions\+***********, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-06-30 10:59:00.278 | DEBUG    | core.telegram.client_manager:get_proxy:96 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-06-30 10:59:00.279 | DEBUG    | core.telegram.client_manager:get_proxy:143 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-06-30 10:59:00.284 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:259 - 创建客户端: h:\PyProject\TeleTest\APPDATA\sessions\+***********, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-06-30 10:59:00.285 | DEBUG    | core.telegram.client_manager:get_proxy:96 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-06-30 10:59:00.286 | DEBUG    | core.telegram.client_manager:get_proxy:143 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-06-30 10:59:00.309 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-30 10:59:00.388 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-30 10:59:00.427 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-30 10:59:00.611 | INFO     | core.auth.api_service:verify_vip:438 - 会员验证成功
2025-06-30 10:59:00.612 | INFO     | utils.vip_checker:_check_vip_status:76 - VIP状态有效
2025-06-30 10:59:03.801 | INFO     | core.telegram.client_manager:_connect_client:207 - 连接成功!
2025-06-30 10:59:04.277 | INFO     | core.telegram.client_manager:_connect_client:207 - 连接成功!
2025-06-30 10:59:05.261 | INFO     | core.telegram.client_manager:_verify_client_authorization:1191 - 账户已授权: +***********
2025-06-30 10:59:05.559 | INFO     | core.telegram.client_manager:_verify_client_authorization:1191 - 账户已授权: +***********
2025-06-30 10:59:07.553 | INFO     | core.telegram.client_manager:batch_auto_login:1365 - 批量登录完成: 总计 2, 成功 2, 失败 0
2025-06-30 10:59:07.742 | INFO     | app.services.account_service:batch_auto_login:1305 - 服务层：核心层登录完成: 成功 2, 失败 0 / 2
2025-06-30 10:59:58.999 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-30 11:00:58.990 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-30 11:01:58.992 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-30 11:02:58.992 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-30 11:03:58.992 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-30 11:04:58.992 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-30 11:05:58.992 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-30 11:06:58.993 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-30 11:07:58.992 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-30 11:08:58.994 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-30 11:09:58.990 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-30 11:10:58.992 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-30 11:11:58.991 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-30 11:12:58.989 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-30 11:13:58.993 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-30 11:14:58.992 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-30 11:15:58.989 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-30 11:16:58.991 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-30 11:17:58.992 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-30 11:18:58.990 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-30 11:19:58.993 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-30 11:20:58.992 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-30 11:21:58.996 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-30 11:22:58.995 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-30 11:23:58.991 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-30 11:24:58.995 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-30 11:25:58.990 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-30 11:26:58.989 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-30 11:27:58.990 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-30 11:28:58.990 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-30 11:29:58.994 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-30 11:30:58.993 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-30 11:31:58.993 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-30 11:32:58.991 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-30 11:33:58.989 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-30 11:34:58.991 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-30 11:35:58.990 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-30 11:36:58.990 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-30 11:37:58.992 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-30 11:38:58.993 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-30 11:39:58.992 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-30 11:40:58.991 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-30 11:41:58.995 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-30 11:42:58.996 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-30 11:43:58.989 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-30 11:44:58.994 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-30 11:45:58.989 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-30 11:46:58.994 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-30 11:47:58.990 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-30 11:48:58.990 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-30 11:49:58.992 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-30 11:50:58.995 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-30 11:51:58.995 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-30 11:52:58.996 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-30 11:53:58.995 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-30 11:54:58.997 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-30 11:55:58.997 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-30 11:56:58.997 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-30 11:57:58.993 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-30 11:58:58.991 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-30 11:59:02.474 | INFO     | ui.main_window:closeEvent:378 - MainWindow: 接收到关闭事件
2025-06-30 11:59:02.486 | INFO     | ui.main_window:_cleanup_before_quit:264 - MainWindow: 执行清理资源...
2025-06-30 11:59:02.487 | INFO     | core.telegram.client_worker:stop:108 - 正在停止Telegram客户端工作线程
2025-06-30 11:59:02.491 | INFO     | core.telegram.client_manager:cleanup_async:1136 - 开始清理资源并断开所有连接
2025-06-30 11:59:02.491 | INFO     | core.telegram.client_manager:disconnect_all_clients:721 - 正在断开所有客户端连接
2025-06-30 11:59:02.492 | INFO     | core.telegram.client_manager:_safe_disconnect:742 - 正在断开客户端连接: +***********
2025-06-30 11:59:02.492 | INFO     | core.telegram.client_manager:_safe_disconnect:742 - 正在断开客户端连接: +***********
2025-06-30 11:59:02.504 | INFO     | core.telegram.client_manager:_safe_disconnect:746 - 断开客户端连接成功: +***********
2025-06-30 11:59:02.505 | INFO     | core.telegram.client_manager:_safe_disconnect:746 - 断开客户端连接成功: +***********
2025-06-30 11:59:02.505 | INFO     | core.telegram.client_manager:cleanup_async:1155 - 资源清理完成
2025-06-30 11:59:02.992 | INFO     | core.telegram.client_manager:cleanup_async:1136 - 开始清理资源并断开所有连接
2025-06-30 11:59:02.992 | INFO     | core.telegram.client_manager:disconnect_all_clients:721 - 正在断开所有客户端连接
2025-06-30 11:59:02.992 | INFO     | core.telegram.client_manager:cleanup_async:1155 - 资源清理完成
2025-06-30 11:59:03.493 | INFO     | core.telegram.client_worker:run:97 - Telegram客户端工作线程已退出
2025-06-30 11:59:03.495 | INFO     | core.telegram.client_worker:stop:122 - Telegram客户端工作线程已停止
2025-06-30 11:59:03.496 | INFO     | ui.main_window:_cleanup_before_quit:273 - TelegramClientWorker 已停止。
2025-06-30 11:59:03.496 | INFO     | ui.main_window:_cleanup_before_quit:277 - MainWindow 清理完成
2025-06-30 11:59:03.502 | INFO     | __main__:main:111 - 应用程序已正常退出
2025-06-30 21:45:33.540 | INFO     | utils.logger:init_logger:98 - 日志系统初始化完成，默认级别: DEBUG，日志目录: h:\PyProject\TeleTest\APPDATA\logs
2025-06-30 21:45:50.658 | INFO     | utils.logger:init_logger:98 - 日志系统初始化完成，默认级别: DEBUG，日志目录: h:\PyProject\TeleTest\APPDATA\logs
2025-06-30 21:46:01.471 | INFO     | utils.logger:init_logger:98 - 日志系统初始化完成，默认级别: DEBUG，日志目录: h:\PyProject\TeleTest\APPDATA\logs
2025-06-30 21:46:02.874 | INFO     | data.database:init_database:31 - 初始化数据库连接: h:\PyProject\TeleTest\APPDATA\database\telegram_manager.db
2025-06-30 21:46:02.910 | DEBUG    | data.database:init_database:58 - 创建数据库表
2025-06-30 21:46:02.922 | INFO     | data.database:init_database:61 - 数据库初始化完成
2025-06-30 21:46:03.515 | DEBUG    | app.controllers.auth_controller:initialize_software:43 - 控制器: 初始化软件 app_id=1000, version=1.2.7
2025-06-30 21:46:03.515 | DEBUG    | core.auth.api_service:init_software:55 - 发送初始化请求: https://server.xile188.com/api/user/1000/windowns10/1.2.7/ini, params={}
2025-06-30 21:46:04.027 | DEBUG    | core.auth.api_service:init_software:66 - 初始化数据解密成功: {'new_content': '- 新增功能\n  - 1.新增图片上传功能\n  - 2.新增软件文档\n  - 3.新增软件自动更新，后续更新会自动检测版本更新，无需再下载更新包\n  - 4.新增设置账户信息及发信控制\n- 修复bug\n  - 删除监听任务时，不删除对应任务采集到的数据。\n  - 监听设置群无法发送到目标群\n  - 导出监听数据时应当导出单个任务而非所有数据\n- 优化记录\n  - 优化发信规则，直接调取符合要求的账户发送，无账户时才需要等待延时。\n  - 完善软件设置', 'new_url': None, 'notice': {'id': '2', 'content': '公告内容测试3', 'time': '**********'}}
2025-06-30 21:46:04.035 | INFO     | app.controllers.auth_controller:login:61 - 控制器: 用户登录 account=demo01
2025-06-30 21:46:07.065 | INFO     | core.auth.api_service:login:127 - 用户登录: account=demo01
2025-06-30 21:46:07.377 | INFO     | core.auth.api_service:get_user_info:347 - 获取用户信息
2025-06-30 21:46:07.642 | INFO     | core.auth.api_service:get_user_info:375 - 获取用户信息成功
2025-06-30 21:46:07.650 | INFO     | __main__:on_auth_completed:93 - 用户登录成功，正在启动主窗口...
2025-06-30 21:46:07.676 | INFO     | core.telegram.client_worker:__init__:61 - Telegram客户端工作线程初始化
2025-06-30 21:46:07.676 | INFO     | utils.vip_checker:initialize:25 - VIP检查器已初始化，等待登录后开始检查
2025-06-30 21:46:07.677 | INFO     | ui.main_window:_initialize_core_components:108 - MainWindow: 初始化核心组件...
2025-06-30 21:46:07.677 | INFO     | core.telegram.client_worker:run:65 - Telegram客户端工作线程开始运行
2025-06-30 21:46:07.678 | INFO     | app.services.account_service:__init__:42 - 账户服务初始化
2025-06-30 21:46:07.678 | INFO     | core.proxy.proxy_task_manager:run:284 - 代理任务管理器线程启动
2025-06-30 21:46:07.679 | INFO     | app.controllers.account_controller:__init__:81 - 账户控制器初始化
2025-06-30 21:46:07.679 | INFO     | app.services.monitor_service:__init__:39 - 初始化监控任务服务
2025-06-30 21:46:07.680 | INFO     | core.monitor.filter:__init__:26 - 初始化消息过滤器
2025-06-30 21:46:07.680 | INFO     | core.telegram.client_manager:__init__:66 - Telegram客户端管理器初始化
2025-06-30 21:46:07.681 | INFO     | core.monitor.extractor:__init__:24 - 初始化关键词提取器
2025-06-30 21:46:07.681 | INFO     | core.telegram.user_manager:__init__:30 - Telegram用户管理器初始化
2025-06-30 21:46:07.681 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-06-30 21:46:07.682 | INFO     | core.telegram.group_manager:__init__:36 - Telegram群组管理器初始化
2025-06-30 21:46:07.682 | INFO     | core.monitor.notifier:__init__:90 - 初始化通知管理器
2025-06-30 21:46:07.682 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-06-30 21:46:07.683 | INFO     | app.controllers.telegram_monitor_controller:__init__:64 - 初始化 Telegram 监控控制器
2025-06-30 21:46:07.683 | INFO     | core.telegram.monitor_manager:__init__:26 - Telegram监控管理器初始化
2025-06-30 21:46:07.684 | DEBUG    | app.controllers.telegram_monitor_controller:__init__:82 - 控制器初始化完成
2025-06-30 21:46:07.684 | INFO     | core.telegram.telegram_service:__init__:39 - Telegram服务初始化完成
2025-06-30 21:46:07.805 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:88 - 尝试连接监听日志信号...
2025-06-30 21:46:07.805 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:90 - 监听日志信号连接成功
2025-06-30 21:46:07.806 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-30 21:46:07.830 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-30 21:46:07.868 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-30 21:46:07.872 | INFO     | app.services.account_service:get_all_groups:96 - 获取所有账户分组成功, 共 2 个
2025-06-30 21:46:07.872 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-30 21:46:07.874 | DEBUG    | ui.views.account_view:_on_groups_loaded:515 - 分组加载完成: 2个分组
2025-06-30 21:46:07.887 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-30 21:46:07.892 | INFO     | app.services.account_service:get_all_accounts:290 - 获取所有账户成功, 共 2 个
2025-06-30 21:46:07.892 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-30 21:46:07.894 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-30 21:46:07.895 | DEBUG    | ui.views.account_view:_on_accounts_loaded:648 - 账户加载完成: 2个账户
2025-06-30 21:46:07.915 | DEBUG    | ui.views.account_view:_update_group_counts:186 - 分组计数已更新: {2: 1, 1: 1}
2025-06-30 21:46:07.920 | INFO     | ui.views.account_view:_auto_login_accounts:703 - 开始调用控制器进行 2 个账户的自动登录。
2025-06-30 21:46:07.921 | INFO     | app.services.account_service:batch_auto_login:1165 - 服务层：开始批量自动登录 2 个账户, 最大并发: 5
2025-06-30 21:46:07.921 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-30 21:46:07.927 | INFO     | app.services.account_service:batch_auto_login:1204 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-06-30 21:46:07.928 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-30 21:46:07.929 | INFO     | app.services.account_service:get_all_accounts:290 - 获取所有账户成功, 共 2 个
2025-06-30 21:46:07.930 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-30 21:46:07.930 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-30 21:46:07.933 | DEBUG    | ui.views.account_view:_update_group_counts:186 - 分组计数已更新: {2: 1, 1: 1}
2025-06-30 21:46:07.933 | DEBUG    | ui.views.account_view:_init_cache:153 - 账户缓存初始化完成，共 2 个账户
2025-06-30 21:46:07.938 | DEBUG    | ui.views.account_view:_on_accounts_loaded:648 - 账户加载完成: 2个账户
2025-06-30 21:46:07.958 | DEBUG    | ui.views.account_view:_update_group_counts:186 - 分组计数已更新: {2: 1, 1: 1}
2025-06-30 21:46:07.967 | INFO     | app.services.account_service:batch_auto_login:1204 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-06-30 21:46:07.967 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-30 21:46:07.968 | INFO     | app.services.account_service:batch_auto_login:1274 - 服务层：准备调用核心层处理 2 个账户的自动登录。
2025-06-30 21:46:07.969 | INFO     | core.telegram.client_manager:batch_auto_login:1216 - 开始批量自动登录, 共 2 个账户, 最大并发 5
2025-06-30 21:46:07.969 | INFO     | app.services.account_service:batch_auto_login:1284 - 服务层：设置核心层任务超时为 120 秒。
2025-06-30 21:46:07.970 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:259 - 创建客户端: h:\PyProject\TeleTest\APPDATA\sessions\+***********, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-06-30 21:46:07.971 | DEBUG    | core.telegram.client_manager:get_proxy:96 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-06-30 21:46:07.971 | DEBUG    | core.telegram.client_manager:get_proxy:143 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-06-30 21:46:07.974 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:259 - 创建客户端: h:\PyProject\TeleTest\APPDATA\sessions\+***********, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-06-30 21:46:07.974 | DEBUG    | core.telegram.client_manager:get_proxy:96 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-06-30 21:46:07.975 | DEBUG    | core.telegram.client_manager:get_proxy:143 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-06-30 21:46:10.252 | INFO     | core.telegram.client_manager:_connect_client:207 - 连接成功!
2025-06-30 21:46:10.376 | INFO     | core.telegram.client_manager:_connect_client:207 - 连接成功!
2025-06-30 21:46:11.024 | INFO     | core.telegram.client_manager:_verify_client_authorization:1191 - 账户已授权: +***********
2025-06-30 21:46:11.077 | INFO     | core.telegram.client_manager:_verify_client_authorization:1191 - 账户已授权: +***********
2025-06-30 21:46:13.079 | INFO     | core.telegram.client_manager:batch_auto_login:1365 - 批量登录完成: 总计 2, 成功 2, 失败 0
2025-06-30 21:46:13.975 | INFO     | app.services.account_service:batch_auto_login:1305 - 服务层：核心层登录完成: 成功 2, 失败 0 / 2
2025-06-30 21:47:07.630 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-30 21:47:36.103 | INFO     | utils.logger:init_logger:98 - 日志系统初始化完成，默认级别: DEBUG，日志目录: h:\PyProject\TeleTest\APPDATA\logs
2025-06-30 21:47:37.421 | INFO     | data.database:init_database:31 - 初始化数据库连接: h:\PyProject\TeleTest\APPDATA\database\telegram_manager.db
2025-06-30 21:47:37.438 | DEBUG    | data.database:init_database:58 - 创建数据库表
2025-06-30 21:47:37.451 | INFO     | data.database:init_database:61 - 数据库初始化完成
2025-06-30 21:47:38.040 | DEBUG    | app.controllers.auth_controller:initialize_software:43 - 控制器: 初始化软件 app_id=1000, version=1.2.7
2025-06-30 21:47:38.041 | DEBUG    | core.auth.api_service:init_software:55 - 发送初始化请求: https://server.xile188.com/api/user/1000/windowns10/1.2.7/ini, params={}
2025-06-30 21:47:38.326 | DEBUG    | core.auth.api_service:init_software:66 - 初始化数据解密成功: {'new_content': '- 新增功能\n  - 1.新增图片上传功能\n  - 2.新增软件文档\n  - 3.新增软件自动更新，后续更新会自动检测版本更新，无需再下载更新包\n  - 4.新增设置账户信息及发信控制\n- 修复bug\n  - 删除监听任务时，不删除对应任务采集到的数据。\n  - 监听设置群无法发送到目标群\n  - 导出监听数据时应当导出单个任务而非所有数据\n- 优化记录\n  - 优化发信规则，直接调取符合要求的账户发送，无账户时才需要等待延时。\n  - 完善软件设置', 'new_url': None, 'notice': {'id': '2', 'content': '公告内容测试3', 'time': '**********'}}
2025-06-30 21:47:38.334 | INFO     | app.controllers.auth_controller:login:61 - 控制器: 用户登录 account=demo01
2025-06-30 21:47:41.258 | INFO     | core.auth.api_service:login:127 - 用户登录: account=demo01
2025-06-30 21:47:41.527 | INFO     | core.auth.api_service:get_user_info:347 - 获取用户信息
2025-06-30 21:47:41.769 | INFO     | core.auth.api_service:get_user_info:375 - 获取用户信息成功
2025-06-30 21:47:41.776 | INFO     | __main__:on_auth_completed:93 - 用户登录成功，正在启动主窗口...
2025-06-30 21:47:41.801 | INFO     | core.telegram.client_worker:__init__:61 - Telegram客户端工作线程初始化
2025-06-30 21:47:41.801 | INFO     | utils.vip_checker:initialize:25 - VIP检查器已初始化，等待登录后开始检查
2025-06-30 21:47:41.802 | INFO     | ui.main_window:_initialize_core_components:108 - MainWindow: 初始化核心组件...
2025-06-30 21:47:41.802 | INFO     | core.telegram.client_worker:run:65 - Telegram客户端工作线程开始运行
2025-06-30 21:47:41.803 | INFO     | app.services.account_service:__init__:42 - 账户服务初始化
2025-06-30 21:47:41.804 | INFO     | core.proxy.proxy_task_manager:run:284 - 代理任务管理器线程启动
2025-06-30 21:47:41.804 | INFO     | app.controllers.account_controller:__init__:81 - 账户控制器初始化
2025-06-30 21:47:41.805 | INFO     | app.services.monitor_service:__init__:39 - 初始化监控任务服务
2025-06-30 21:47:41.805 | INFO     | core.telegram.client_manager:__init__:66 - Telegram客户端管理器初始化
2025-06-30 21:47:41.805 | INFO     | core.monitor.filter:__init__:26 - 初始化消息过滤器
2025-06-30 21:47:41.806 | INFO     | core.telegram.user_manager:__init__:30 - Telegram用户管理器初始化
2025-06-30 21:47:41.806 | INFO     | core.monitor.extractor:__init__:24 - 初始化关键词提取器
2025-06-30 21:47:41.806 | INFO     | core.telegram.group_manager:__init__:36 - Telegram群组管理器初始化
2025-06-30 21:47:41.807 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-06-30 21:47:41.807 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-06-30 21:47:41.808 | INFO     | core.monitor.notifier:__init__:90 - 初始化通知管理器
2025-06-30 21:47:41.808 | INFO     | core.telegram.monitor_manager:__init__:26 - Telegram监控管理器初始化
2025-06-30 21:47:41.808 | INFO     | app.controllers.telegram_monitor_controller:__init__:64 - 初始化 Telegram 监控控制器
2025-06-30 21:47:41.809 | INFO     | core.telegram.telegram_service:__init__:39 - Telegram服务初始化完成
2025-06-30 21:47:41.809 | DEBUG    | app.controllers.telegram_monitor_controller:__init__:82 - 控制器初始化完成
2025-06-30 21:47:41.998 | INFO     | ui.views.monitor_view:__init__:48 - 初始化监控视图
2025-06-30 21:47:41.999 | DEBUG    | ui.views.monitor_view:_connect_signals:80 - 连接控制器信号
2025-06-30 21:47:42.163 | INFO     | ui.views.proxy_view:__init__:96 - 初始化视图，加载第一页代理数据
2025-06-30 21:47:42.393 | INFO     | __main__:on_auth_completed:97 - 主窗口已启动
2025-06-30 21:47:42.438 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:88 - 尝试连接监听日志信号...
2025-06-30 21:47:42.439 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:90 - 监听日志信号连接成功
2025-06-30 21:47:42.440 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-30 21:47:42.444 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-30 21:47:42.449 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:145 - 加载监控任务列表 (搜索关键词: '')
2025-06-30 21:47:42.449 | DEBUG    | ui.views.monitor_view:_on_loading_started:620 - 加载中: 正在加载任务列表...
2025-06-30 21:47:42.450 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-30 21:47:42.457 | INFO     | app.services.proxy_service:refresh_proxies:712 - 刷新代理列表: offset=0, limit=20
2025-06-30 21:47:42.458 | INFO     | core.proxy.proxy_task_manager:submit_task:172 - 提交耗时任务到子线程: refresh_proxies_0_20
2025-06-30 21:47:42.458 | DEBUG    | app.services.proxy_service:get_proxies:500 - 获取代理列表: offset=0, limit=20
2025-06-30 21:47:42.458 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-30 21:47:42.458 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-30 21:47:42.461 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-30 21:47:42.475 | INFO     | app.controllers.invite_controller:get_invite_tasks:38 - 获取邀请任务列表, status=None
2025-06-30 21:47:42.487 | INFO     | app.services.invite_service:get_invite_tasks:121 - 获取邀请任务列表, status=None
2025-06-30 21:47:42.487 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-30 21:47:42.489 | INFO     | app.services.invite_service:task_initialize:39 - 初始化邀请服务，重置运行中的任务为暂停状态
2025-06-30 21:47:42.491 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-30 21:47:42.492 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-30 21:47:42.494 | INFO     | core.proxy.proxy_task_manager:task_wrapper:199 - 任务 refresh_proxies_0_20 完成
2025-06-30 21:47:42.495 | INFO     | utils.vip_checker:start_checking:37 - 开始VIP状态检查
2025-06-30 21:47:42.495 | DEBUG    | utils.vip_checker:_check_vip_status:61 - 正在检查VIP状态...
2025-06-30 21:47:42.495 | INFO     | core.auth.api_service:verify_vip:425 - 会员验证接口调用
2025-06-30 21:47:42.743 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-30 21:47:42.746 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-30 21:47:42.748 | INFO     | app.services.invite_service:task_initialize:48 - 成功重置 0 个任务状态
2025-06-30 21:47:42.749 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-30 21:47:42.750 | DEBUG    | ui.views.monitor_view:_refresh_task_list:175 - 刷新任务列表，共0个任务
2025-06-30 21:47:42.750 | INFO     | ui.views.monitor_view:_refresh_task_list:189 - 没有找到监控任务
2025-06-30 21:47:42.751 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-30 21:47:42.752 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-30 21:47:42.757 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-30 21:47:42.760 | INFO     | app.services.account_service:get_all_groups:96 - 获取所有账户分组成功, 共 2 个
2025-06-30 21:47:42.760 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-30 21:47:42.761 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-30 21:47:42.806 | DEBUG    | ui.views.account_view:_on_groups_loaded:515 - 分组加载完成: 2个分组
2025-06-30 21:47:42.821 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-30 21:47:42.842 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-30 21:47:42.843 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-30 21:47:42.844 | INFO     | app.services.account_service:get_all_accounts:290 - 获取所有账户成功, 共 2 个
2025-06-30 21:47:42.845 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-30 21:47:42.846 | INFO     | app.controllers.telegram_monitor_controller:_load_total_users_count_internal:191 - 成功加载总体统计数据: 总用户数 23, 今日采集 0, 日均采集 23, 运行天数 1
2025-06-30 21:47:42.847 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:156 - 成功加载 0 个任务
2025-06-30 21:47:42.847 | DEBUG    | ui.views.monitor_view:_on_loading_finished:625 - 加载完成
2025-06-30 21:47:42.849 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-06-30 21:47:42.862 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-30 21:47:42.864 | DEBUG    | ui.views.account_view:_on_accounts_loaded:648 - 账户加载完成: 2个账户
2025-06-30 21:47:42.888 | DEBUG    | ui.views.account_view:_update_group_counts:186 - 分组计数已更新: {2: 1, 1: 1}
2025-06-30 21:47:42.892 | INFO     | ui.views.account_view:_auto_login_accounts:703 - 开始调用控制器进行 2 个账户的自动登录。
2025-06-30 21:47:42.893 | INFO     | app.services.account_service:batch_auto_login:1165 - 服务层：开始批量自动登录 2 个账户, 最大并发: 5
2025-06-30 21:47:42.893 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-30 21:47:42.910 | INFO     | app.services.account_service:batch_auto_login:1204 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-06-30 21:47:42.910 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-30 21:47:42.912 | INFO     | app.services.account_service:get_all_accounts:290 - 获取所有账户成功, 共 2 个
2025-06-30 21:47:42.912 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-30 21:47:42.914 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-30 21:47:42.917 | DEBUG    | ui.views.account_view:_update_group_counts:186 - 分组计数已更新: {2: 1, 1: 1}
2025-06-30 21:47:42.917 | DEBUG    | ui.views.account_view:_init_cache:153 - 账户缓存初始化完成，共 2 个账户
2025-06-30 21:47:42.922 | DEBUG    | ui.views.account_view:_on_accounts_loaded:648 - 账户加载完成: 2个账户
2025-06-30 21:47:42.946 | DEBUG    | ui.views.account_view:_update_group_counts:186 - 分组计数已更新: {2: 1, 1: 1}
2025-06-30 21:47:42.956 | INFO     | app.services.account_service:batch_auto_login:1204 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-06-30 21:47:42.956 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-30 21:47:42.958 | INFO     | app.services.account_service:batch_auto_login:1274 - 服务层：准备调用核心层处理 2 个账户的自动登录。
2025-06-30 21:47:42.959 | INFO     | app.services.account_service:batch_auto_login:1284 - 服务层：设置核心层任务超时为 120 秒。
2025-06-30 21:47:42.959 | INFO     | core.telegram.client_manager:batch_auto_login:1216 - 开始批量自动登录, 共 2 个账户, 最大并发 5
2025-06-30 21:47:42.960 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:259 - 创建客户端: h:\PyProject\TeleTest\APPDATA\sessions\+***********, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-06-30 21:47:42.961 | DEBUG    | core.telegram.client_manager:get_proxy:96 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-06-30 21:47:42.962 | DEBUG    | core.telegram.client_manager:get_proxy:143 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-06-30 21:47:42.967 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:259 - 创建客户端: h:\PyProject\TeleTest\APPDATA\sessions\+***********, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-06-30 21:47:42.968 | DEBUG    | core.telegram.client_manager:get_proxy:96 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-06-30 21:47:42.969 | DEBUG    | core.telegram.client_manager:get_proxy:143 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-06-30 21:47:42.973 | INFO     | core.auth.api_service:verify_vip:438 - 会员验证成功
2025-06-30 21:47:42.974 | INFO     | utils.vip_checker:_check_vip_status:76 - VIP状态有效
2025-06-30 21:47:42.993 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-30 21:47:43.068 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-30 21:47:43.075 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-06-30 21:47:43.077 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-06-30 21:47:43.079 | INFO     | ui.views.proxy_view:_load_initial_data:1044 - 初始数据加载完成，共 1 个代理
2025-06-30 21:47:43.110 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-30 21:47:44.957 | INFO     | core.telegram.client_manager:_connect_client:207 - 连接成功!
2025-06-30 21:47:45.235 | INFO     | core.telegram.client_manager:_connect_client:207 - 连接成功!
2025-06-30 21:47:45.643 | INFO     | core.telegram.client_manager:_verify_client_authorization:1191 - 账户已授权: +***********
2025-06-30 21:47:45.920 | INFO     | core.telegram.client_manager:_verify_client_authorization:1191 - 账户已授权: +***********
2025-06-30 21:47:47.926 | INFO     | core.telegram.client_manager:batch_auto_login:1365 - 批量登录完成: 总计 2, 成功 2, 失败 0
2025-06-30 21:47:47.978 | INFO     | app.services.account_service:batch_auto_login:1305 - 服务层：核心层登录完成: 成功 2, 失败 0 / 2
2025-06-30 21:48:06.444 | INFO     | ui.main_window:closeEvent:380 - MainWindow: 接收到关闭事件
2025-06-30 21:48:08.898 | INFO     | ui.main_window:_cleanup_before_quit:266 - MainWindow: 执行清理资源...
2025-06-30 21:48:08.899 | INFO     | core.telegram.client_worker:stop:108 - 正在停止Telegram客户端工作线程
2025-06-30 21:48:08.911 | INFO     | core.telegram.client_manager:cleanup_async:1136 - 开始清理资源并断开所有连接
2025-06-30 21:48:08.912 | INFO     | core.telegram.client_manager:disconnect_all_clients:721 - 正在断开所有客户端连接
2025-06-30 21:48:08.912 | INFO     | core.telegram.client_manager:_safe_disconnect:742 - 正在断开客户端连接: +***********
2025-06-30 21:48:08.914 | INFO     | core.telegram.client_manager:_safe_disconnect:742 - 正在断开客户端连接: +***********
2025-06-30 21:48:08.926 | INFO     | core.telegram.client_manager:_safe_disconnect:746 - 断开客户端连接成功: +***********
2025-06-30 21:48:08.927 | INFO     | core.telegram.client_manager:_safe_disconnect:746 - 断开客户端连接成功: +***********
2025-06-30 21:48:08.927 | INFO     | core.telegram.client_manager:cleanup_async:1155 - 资源清理完成
2025-06-30 21:48:09.413 | INFO     | core.telegram.client_manager:cleanup_async:1136 - 开始清理资源并断开所有连接
2025-06-30 21:48:09.413 | INFO     | core.telegram.client_manager:disconnect_all_clients:721 - 正在断开所有客户端连接
2025-06-30 21:48:09.414 | INFO     | core.telegram.client_manager:cleanup_async:1155 - 资源清理完成
2025-06-30 21:48:09.915 | INFO     | core.telegram.client_worker:run:97 - Telegram客户端工作线程已退出
2025-06-30 21:48:09.916 | INFO     | core.telegram.client_worker:stop:122 - Telegram客户端工作线程已停止
2025-06-30 21:48:09.916 | INFO     | ui.main_window:_cleanup_before_quit:275 - TelegramClientWorker 已停止。
2025-06-30 21:48:09.917 | INFO     | ui.main_window:_cleanup_before_quit:279 - MainWindow 清理完成
2025-06-30 21:48:09.926 | INFO     | __main__:main:111 - 应用程序已正常退出
2025-06-30 23:44:14.409 | INFO     | utils.logger:init_logger:98 - 日志系统初始化完成，默认级别: DEBUG，日志目录: h:\PyProject\TeleTest\APPDATA\logs
2025-06-30 23:44:16.349 | INFO     | data.database:init_database:31 - 初始化数据库连接: h:\PyProject\TeleTest\APPDATA\database\telegram_manager.db
2025-06-30 23:44:16.372 | DEBUG    | data.database:init_database:58 - 创建数据库表
2025-06-30 23:44:16.388 | INFO     | data.database:init_database:61 - 数据库初始化完成
2025-06-30 23:44:18.930 | DEBUG    | app.controllers.auth_controller:initialize_software:43 - 控制器: 初始化软件 app_id=1000, version=1.2.7
2025-06-30 23:44:18.930 | DEBUG    | core.auth.api_service:init_software:55 - 发送初始化请求: https://server.xile188.com/api/user/1000/windowns10/1.2.7/ini, params={}
2025-06-30 23:44:19.520 | DEBUG    | core.auth.api_service:init_software:66 - 初始化数据解密成功: {'new_content': '- 新增功能\n  - 1.新增图片上传功能\n  - 2.新增软件文档\n  - 3.新增软件自动更新，后续更新会自动检测版本更新，无需再下载更新包\n  - 4.新增设置账户信息及发信控制\n- 修复bug\n  - 删除监听任务时，不删除对应任务采集到的数据。\n  - 监听设置群无法发送到目标群\n  - 导出监听数据时应当导出单个任务而非所有数据\n- 优化记录\n  - 优化发信规则，直接调取符合要求的账户发送，无账户时才需要等待延时。\n  - 完善软件设置', 'new_url': None, 'notice': {'id': '2', 'content': '公告内容测试3', 'time': '**********'}}
2025-06-30 23:44:19.532 | INFO     | app.controllers.auth_controller:login:61 - 控制器: 用户登录 account=demo01
2025-06-30 23:44:22.553 | INFO     | core.auth.api_service:login:127 - 用户登录: account=demo01
2025-06-30 23:44:22.776 | INFO     | core.auth.api_service:get_user_info:347 - 获取用户信息
2025-06-30 23:44:22.982 | INFO     | core.auth.api_service:get_user_info:375 - 获取用户信息成功
2025-06-30 23:44:22.990 | INFO     | __main__:on_auth_completed:93 - 用户登录成功，正在启动主窗口...
2025-06-30 23:44:23.013 | INFO     | core.telegram.client_worker:__init__:61 - Telegram客户端工作线程初始化
2025-06-30 23:44:23.014 | INFO     | utils.vip_checker:initialize:25 - VIP检查器已初始化，等待登录后开始检查
2025-06-30 23:44:23.014 | INFO     | ui.main_window:_initialize_core_components:108 - MainWindow: 初始化核心组件...
2025-06-30 23:44:23.015 | INFO     | core.telegram.client_worker:run:65 - Telegram客户端工作线程开始运行
2025-06-30 23:44:23.016 | INFO     | app.services.account_service:__init__:42 - 账户服务初始化
2025-06-30 23:44:23.016 | INFO     | core.proxy.proxy_task_manager:run:284 - 代理任务管理器线程启动
2025-06-30 23:44:23.017 | INFO     | app.controllers.account_controller:__init__:81 - 账户控制器初始化
2025-06-30 23:44:23.018 | INFO     | core.telegram.client_manager:__init__:66 - Telegram客户端管理器初始化
2025-06-30 23:44:23.018 | INFO     | app.services.monitor_service:__init__:39 - 初始化监控任务服务
2025-06-30 23:44:23.018 | INFO     | core.telegram.user_manager:__init__:30 - Telegram用户管理器初始化
2025-06-30 23:44:23.018 | INFO     | core.monitor.filter:__init__:26 - 初始化消息过滤器
2025-06-30 23:44:23.019 | INFO     | core.telegram.group_manager:__init__:36 - Telegram群组管理器初始化
2025-06-30 23:44:23.019 | INFO     | core.monitor.extractor:__init__:24 - 初始化关键词提取器
2025-06-30 23:44:23.020 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-06-30 23:44:23.020 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-06-30 23:44:23.020 | INFO     | core.telegram.monitor_manager:__init__:26 - Telegram监控管理器初始化
2025-06-30 23:44:23.020 | INFO     | core.monitor.notifier:__init__:90 - 初始化通知管理器
2025-06-30 23:44:23.021 | INFO     | core.telegram.telegram_service:__init__:39 - Telegram服务初始化完成
2025-06-30 23:44:23.021 | INFO     | app.controllers.telegram_monitor_controller:__init__:64 - 初始化 Telegram 监控控制器
2025-06-30 23:44:23.021 | DEBUG    | app.controllers.telegram_monitor_controller:__init__:82 - 控制器初始化完成
2025-06-30 23:44:23.245 | INFO     | ui.views.monitor_view:__init__:48 - 初始化监控视图
2025-06-30 23:44:23.246 | DEBUG    | ui.views.monitor_view:_connect_signals:80 - 连接控制器信号
2025-06-30 23:44:23.445 | INFO     | ui.views.proxy_view:__init__:96 - 初始化视图，加载第一页代理数据
2025-06-30 23:44:23.714 | INFO     | __main__:on_auth_completed:97 - 主窗口已启动
2025-06-30 23:44:23.755 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:88 - 尝试连接监听日志信号...
2025-06-30 23:44:23.756 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:90 - 监听日志信号连接成功
2025-06-30 23:44:23.756 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-30 23:44:23.760 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-30 23:44:23.763 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:145 - 加载监控任务列表 (搜索关键词: '')
2025-06-30 23:44:23.764 | DEBUG    | ui.views.monitor_view:_on_loading_started:620 - 加载中: 正在加载任务列表...
2025-06-30 23:44:23.764 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-30 23:44:23.773 | INFO     | app.services.proxy_service:refresh_proxies:712 - 刷新代理列表: offset=0, limit=20
2025-06-30 23:44:23.773 | INFO     | core.proxy.proxy_task_manager:submit_task:172 - 提交耗时任务到子线程: refresh_proxies_0_20
2025-06-30 23:44:23.774 | DEBUG    | app.services.proxy_service:get_proxies:500 - 获取代理列表: offset=0, limit=20
2025-06-30 23:44:23.774 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-30 23:44:23.774 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-30 23:44:23.778 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-30 23:44:23.804 | INFO     | app.controllers.invite_controller:get_invite_tasks:38 - 获取邀请任务列表, status=None
2025-06-30 23:44:23.805 | INFO     | app.services.invite_service:get_invite_tasks:121 - 获取邀请任务列表, status=None
2025-06-30 23:44:23.807 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-30 23:44:23.809 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-30 23:44:23.809 | INFO     | app.services.invite_service:task_initialize:39 - 初始化邀请服务，重置运行中的任务为暂停状态
2025-06-30 23:44:23.810 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-30 23:44:23.811 | INFO     | core.proxy.proxy_task_manager:task_wrapper:199 - 任务 refresh_proxies_0_20 完成
2025-06-30 23:44:23.813 | INFO     | utils.vip_checker:start_checking:37 - 开始VIP状态检查
2025-06-30 23:44:23.814 | DEBUG    | utils.vip_checker:_check_vip_status:61 - 正在检查VIP状态...
2025-06-30 23:44:23.814 | INFO     | core.auth.api_service:verify_vip:425 - 会员验证接口调用
2025-06-30 23:44:23.975 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-30 23:44:23.980 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-30 23:44:23.985 | INFO     | app.services.invite_service:task_initialize:48 - 成功重置 0 个任务状态
2025-06-30 23:44:23.986 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-30 23:44:23.986 | DEBUG    | ui.views.monitor_view:_refresh_task_list:175 - 刷新任务列表，共0个任务
2025-06-30 23:44:23.987 | INFO     | ui.views.monitor_view:_refresh_task_list:189 - 没有找到监控任务
2025-06-30 23:44:23.987 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-30 23:44:23.988 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-30 23:44:23.992 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-30 23:44:23.994 | INFO     | app.services.account_service:get_all_groups:96 - 获取所有账户分组成功, 共 2 个
2025-06-30 23:44:23.994 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-30 23:44:23.996 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-30 23:44:23.999 | DEBUG    | ui.views.account_view:_on_groups_loaded:515 - 分组加载完成: 2个分组
2025-06-30 23:44:24.013 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-30 23:44:24.074 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-30 23:44:24.076 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-30 23:44:24.077 | INFO     | app.services.account_service:get_all_accounts:290 - 获取所有账户成功, 共 2 个
2025-06-30 23:44:24.077 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-30 23:44:24.079 | INFO     | app.controllers.telegram_monitor_controller:_load_total_users_count_internal:191 - 成功加载总体统计数据: 总用户数 23, 今日采集 0, 日均采集 23, 运行天数 1
2025-06-30 23:44:24.079 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:156 - 成功加载 0 个任务
2025-06-30 23:44:24.079 | DEBUG    | ui.views.monitor_view:_on_loading_finished:625 - 加载完成
2025-06-30 23:44:24.081 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-30 23:44:24.082 | DEBUG    | ui.views.account_view:_on_accounts_loaded:648 - 账户加载完成: 2个账户
2025-06-30 23:44:24.103 | DEBUG    | ui.views.account_view:_update_group_counts:186 - 分组计数已更新: {2: 1, 1: 1}
2025-06-30 23:44:24.106 | INFO     | ui.views.account_view:_auto_login_accounts:703 - 开始调用控制器进行 2 个账户的自动登录。
2025-06-30 23:44:24.107 | INFO     | app.services.account_service:batch_auto_login:1165 - 服务层：开始批量自动登录 2 个账户, 最大并发: 5
2025-06-30 23:44:24.107 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-30 23:44:24.110 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-06-30 23:44:24.127 | INFO     | app.services.account_service:get_all_accounts:290 - 获取所有账户成功, 共 2 个
2025-06-30 23:44:24.127 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-30 23:44:24.130 | INFO     | app.services.account_service:batch_auto_login:1204 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-06-30 23:44:24.131 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-30 23:44:24.132 | DEBUG    | ui.views.account_view:_update_group_counts:186 - 分组计数已更新: {2: 1, 1: 1}
2025-06-30 23:44:24.133 | DEBUG    | ui.views.account_view:_init_cache:153 - 账户缓存初始化完成，共 2 个账户
2025-06-30 23:44:24.139 | DEBUG    | ui.views.account_view:_on_accounts_loaded:648 - 账户加载完成: 2个账户
2025-06-30 23:44:24.161 | DEBUG    | ui.views.account_view:_update_group_counts:186 - 分组计数已更新: {2: 1, 1: 1}
2025-06-30 23:44:24.171 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-30 23:44:24.182 | INFO     | app.services.account_service:batch_auto_login:1204 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-06-30 23:44:24.182 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-30 23:44:24.185 | INFO     | app.services.account_service:batch_auto_login:1274 - 服务层：准备调用核心层处理 2 个账户的自动登录。
2025-06-30 23:44:24.186 | INFO     | app.services.account_service:batch_auto_login:1284 - 服务层：设置核心层任务超时为 120 秒。
2025-06-30 23:44:24.186 | INFO     | core.telegram.client_manager:batch_auto_login:1216 - 开始批量自动登录, 共 2 个账户, 最大并发 5
2025-06-30 23:44:24.187 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:259 - 创建客户端: h:\PyProject\TeleTest\APPDATA\sessions\+***********, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-06-30 23:44:24.188 | DEBUG    | core.telegram.client_manager:get_proxy:96 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-06-30 23:44:24.189 | DEBUG    | core.telegram.client_manager:get_proxy:143 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-06-30 23:44:24.195 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:259 - 创建客户端: h:\PyProject\TeleTest\APPDATA\sessions\+***********, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-06-30 23:44:24.196 | DEBUG    | core.telegram.client_manager:get_proxy:96 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-06-30 23:44:24.196 | DEBUG    | core.telegram.client_manager:get_proxy:143 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-06-30 23:44:24.220 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-30 23:44:24.299 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-30 23:44:24.304 | INFO     | core.auth.api_service:verify_vip:438 - 会员验证成功
2025-06-30 23:44:24.305 | INFO     | utils.vip_checker:_check_vip_status:76 - VIP状态有效
2025-06-30 23:44:24.334 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-30 23:44:24.352 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-06-30 23:44:24.354 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-06-30 23:44:24.356 | INFO     | ui.views.proxy_view:_load_initial_data:1044 - 初始数据加载完成，共 1 个代理
2025-06-30 23:44:26.400 | INFO     | core.telegram.client_manager:_connect_client:207 - 连接成功!
2025-06-30 23:44:27.155 | INFO     | core.telegram.client_manager:_verify_client_authorization:1191 - 账户已授权: +***********
2025-06-30 23:44:27.640 | INFO     | core.telegram.client_manager:_connect_client:207 - 连接成功!
2025-06-30 23:44:28.347 | INFO     | core.telegram.client_manager:_verify_client_authorization:1191 - 账户已授权: +***********
2025-06-30 23:44:30.359 | INFO     | core.telegram.client_manager:batch_auto_login:1365 - 批量登录完成: 总计 2, 成功 2, 失败 0
2025-06-30 23:44:31.194 | INFO     | app.services.account_service:batch_auto_login:1305 - 服务层：核心层登录完成: 成功 2, 失败 0 / 2
2025-06-30 23:45:22.976 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-30 23:46:22.986 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-06-30 23:46:43.877 | INFO     | ui.main_window:closeEvent:380 - MainWindow: 接收到关闭事件
2025-06-30 23:46:43.893 | INFO     | ui.main_window:_cleanup_before_quit:266 - MainWindow: 执行清理资源...
2025-06-30 23:46:43.893 | INFO     | core.telegram.client_worker:stop:108 - 正在停止Telegram客户端工作线程
2025-06-30 23:46:43.902 | INFO     | core.telegram.client_manager:cleanup_async:1136 - 开始清理资源并断开所有连接
2025-06-30 23:46:43.903 | INFO     | core.telegram.client_manager:disconnect_all_clients:721 - 正在断开所有客户端连接
2025-06-30 23:46:43.904 | INFO     | core.telegram.client_manager:_safe_disconnect:742 - 正在断开客户端连接: +***********
2025-06-30 23:46:43.904 | INFO     | core.telegram.client_manager:_safe_disconnect:742 - 正在断开客户端连接: +***********
2025-06-30 23:46:43.915 | INFO     | core.telegram.client_manager:_safe_disconnect:746 - 断开客户端连接成功: +***********
2025-06-30 23:46:43.916 | INFO     | core.telegram.client_manager:_safe_disconnect:746 - 断开客户端连接成功: +***********
2025-06-30 23:46:43.916 | INFO     | core.telegram.client_manager:cleanup_async:1155 - 资源清理完成
2025-06-30 23:46:44.404 | INFO     | core.telegram.client_manager:cleanup_async:1136 - 开始清理资源并断开所有连接
2025-06-30 23:46:44.404 | INFO     | core.telegram.client_manager:disconnect_all_clients:721 - 正在断开所有客户端连接
2025-06-30 23:46:44.404 | INFO     | core.telegram.client_manager:cleanup_async:1155 - 资源清理完成
2025-06-30 23:46:44.909 | INFO     | core.telegram.client_worker:run:97 - Telegram客户端工作线程已退出
2025-06-30 23:46:44.911 | INFO     | core.telegram.client_worker:stop:122 - Telegram客户端工作线程已停止
2025-06-30 23:46:44.915 | INFO     | ui.main_window:_cleanup_before_quit:275 - TelegramClientWorker 已停止。
2025-06-30 23:46:44.916 | INFO     | ui.main_window:_cleanup_before_quit:279 - MainWindow 清理完成
2025-06-30 23:46:44.921 | INFO     | __main__:main:111 - 应用程序已正常退出
2025-06-30 23:47:33.460 | INFO     | utils.logger:init_logger:98 - 日志系统初始化完成，默认级别: DEBUG，日志目录: h:\PyProject\TeleTest\APPDATA\logs
2025-06-30 23:47:34.939 | INFO     | data.database:init_database:31 - 初始化数据库连接: h:\PyProject\TeleTest\APPDATA\database\telegram_manager.db
2025-06-30 23:47:34.963 | DEBUG    | data.database:init_database:58 - 创建数据库表
2025-06-30 23:47:34.977 | INFO     | data.database:init_database:61 - 数据库初始化完成
2025-06-30 23:47:35.469 | DEBUG    | app.controllers.auth_controller:initialize_software:43 - 控制器: 初始化软件 app_id=1000, version=1.2.7
2025-06-30 23:47:35.469 | DEBUG    | core.auth.api_service:init_software:55 - 发送初始化请求: https://server.xile188.com/api/user/1000/windowns10/1.2.7/ini, params={}
2025-06-30 23:47:35.692 | DEBUG    | core.auth.api_service:init_software:66 - 初始化数据解密成功: {'new_content': '- 新增功能\n  - 1.新增图片上传功能\n  - 2.新增软件文档\n  - 3.新增软件自动更新，后续更新会自动检测版本更新，无需再下载更新包\n  - 4.新增设置账户信息及发信控制\n- 修复bug\n  - 删除监听任务时，不删除对应任务采集到的数据。\n  - 监听设置群无法发送到目标群\n  - 导出监听数据时应当导出单个任务而非所有数据\n- 优化记录\n  - 优化发信规则，直接调取符合要求的账户发送，无账户时才需要等待延时。\n  - 完善软件设置', 'new_url': None, 'notice': {'id': '2', 'content': '公告内容测试3', 'time': '**********'}}
2025-06-30 23:47:35.701 | INFO     | app.controllers.auth_controller:login:61 - 控制器: 用户登录 account=demo01
2025-06-30 23:47:38.666 | INFO     | core.auth.api_service:login:127 - 用户登录: account=demo01
2025-06-30 23:47:38.928 | INFO     | core.auth.api_service:get_user_info:347 - 获取用户信息
2025-06-30 23:47:39.201 | INFO     | core.auth.api_service:get_user_info:375 - 获取用户信息成功
2025-06-30 23:47:39.208 | INFO     | __main__:on_auth_completed:93 - 用户登录成功，正在启动主窗口...
2025-06-30 23:47:39.237 | INFO     | core.telegram.client_worker:__init__:61 - Telegram客户端工作线程初始化
2025-06-30 23:47:39.238 | INFO     | utils.vip_checker:initialize:25 - VIP检查器已初始化，等待登录后开始检查
2025-06-30 23:47:39.239 | INFO     | ui.main_window:_initialize_core_components:108 - MainWindow: 初始化核心组件...
2025-06-30 23:47:39.240 | INFO     | app.services.account_service:__init__:42 - 账户服务初始化
2025-06-30 23:47:39.240 | INFO     | core.telegram.client_worker:run:65 - Telegram客户端工作线程开始运行
2025-06-30 23:47:39.240 | INFO     | core.proxy.proxy_task_manager:run:284 - 代理任务管理器线程启动
2025-06-30 23:47:39.240 | INFO     | app.controllers.account_controller:__init__:81 - 账户控制器初始化
2025-06-30 23:47:39.241 | INFO     | app.services.monitor_service:__init__:39 - 初始化监控任务服务
2025-06-30 23:47:39.242 | INFO     | core.telegram.client_manager:__init__:66 - Telegram客户端管理器初始化
2025-06-30 23:47:39.242 | INFO     | core.monitor.filter:__init__:26 - 初始化消息过滤器
2025-06-30 23:47:39.242 | INFO     | core.telegram.user_manager:__init__:30 - Telegram用户管理器初始化
2025-06-30 23:47:39.242 | INFO     | core.monitor.extractor:__init__:24 - 初始化关键词提取器
2025-06-30 23:47:39.242 | INFO     | core.telegram.group_manager:__init__:36 - Telegram群组管理器初始化
2025-06-30 23:47:39.242 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-06-30 23:47:39.243 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-06-30 23:47:39.243 | INFO     | core.monitor.notifier:__init__:90 - 初始化通知管理器
2025-06-30 23:47:39.243 | INFO     | core.telegram.monitor_manager:__init__:26 - Telegram监控管理器初始化
2025-06-30 23:47:39.243 | INFO     | app.controllers.telegram_monitor_controller:__init__:64 - 初始化 Telegram 监控控制器
2025-06-30 23:47:39.243 | INFO     | core.telegram.telegram_service:__init__:39 - Telegram服务初始化完成
2025-06-30 23:47:39.244 | DEBUG    | app.controllers.telegram_monitor_controller:__init__:82 - 控制器初始化完成
2025-06-30 23:47:39.436 | INFO     | ui.views.monitor_view:__init__:48 - 初始化监控视图
2025-06-30 23:47:39.437 | DEBUG    | ui.views.monitor_view:_connect_signals:80 - 连接控制器信号
2025-06-30 23:47:39.609 | INFO     | ui.views.proxy_view:__init__:96 - 初始化视图，加载第一页代理数据
2025-06-30 23:47:39.844 | INFO     | __main__:on_auth_completed:97 - 主窗口已启动
2025-06-30 23:47:39.887 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:88 - 尝试连接监听日志信号...
2025-06-30 23:47:39.888 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:90 - 监听日志信号连接成功
2025-06-30 23:47:39.888 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-30 23:47:39.891 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-30 23:47:39.895 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:145 - 加载监控任务列表 (搜索关键词: '')
2025-06-30 23:47:39.895 | DEBUG    | ui.views.monitor_view:_on_loading_started:620 - 加载中: 正在加载任务列表...
2025-06-30 23:47:39.897 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-30 23:47:39.904 | INFO     | app.services.proxy_service:refresh_proxies:712 - 刷新代理列表: offset=0, limit=20
2025-06-30 23:47:39.905 | INFO     | core.proxy.proxy_task_manager:submit_task:172 - 提交耗时任务到子线程: refresh_proxies_0_20
2025-06-30 23:47:39.905 | DEBUG    | app.services.proxy_service:get_proxies:500 - 获取代理列表: offset=0, limit=20
2025-06-30 23:47:39.905 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-30 23:47:39.906 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-30 23:47:39.909 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-30 23:47:39.933 | INFO     | app.controllers.invite_controller:get_invite_tasks:38 - 获取邀请任务列表, status=None
2025-06-30 23:47:39.933 | INFO     | app.services.invite_service:get_invite_tasks:121 - 获取邀请任务列表, status=None
2025-06-30 23:47:39.934 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-30 23:47:39.937 | INFO     | app.services.invite_service:task_initialize:39 - 初始化邀请服务，重置运行中的任务为暂停状态
2025-06-30 23:47:39.938 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-30 23:47:39.939 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-30 23:47:39.941 | INFO     | core.proxy.proxy_task_manager:task_wrapper:199 - 任务 refresh_proxies_0_20 完成
2025-06-30 23:47:39.942 | INFO     | utils.vip_checker:start_checking:37 - 开始VIP状态检查
2025-06-30 23:47:39.943 | DEBUG    | utils.vip_checker:_check_vip_status:61 - 正在检查VIP状态...
2025-06-30 23:47:39.943 | INFO     | core.auth.api_service:verify_vip:425 - 会员验证接口调用
2025-06-30 23:47:40.122 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-30 23:47:40.123 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-30 23:47:40.126 | INFO     | app.services.invite_service:task_initialize:48 - 成功重置 0 个任务状态
2025-06-30 23:47:40.127 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-30 23:47:40.128 | DEBUG    | ui.views.monitor_view:_refresh_task_list:175 - 刷新任务列表，共0个任务
2025-06-30 23:47:40.129 | INFO     | ui.views.monitor_view:_refresh_task_list:189 - 没有找到监控任务
2025-06-30 23:47:40.129 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-30 23:47:40.135 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-30 23:47:40.139 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-30 23:47:40.140 | INFO     | app.services.account_service:get_all_groups:96 - 获取所有账户分组成功, 共 2 个
2025-06-30 23:47:40.140 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-30 23:47:40.141 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-30 23:47:40.145 | DEBUG    | ui.views.account_view:_on_groups_loaded:515 - 分组加载完成: 2个分组
2025-06-30 23:47:40.158 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-30 23:47:40.221 | INFO     | app.services.account_service:get_all_accounts:290 - 获取所有账户成功, 共 2 个
2025-06-30 23:47:40.221 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-30 23:47:40.222 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-30 23:47:40.223 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-30 23:47:40.224 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-30 23:47:40.225 | DEBUG    | ui.views.account_view:_on_accounts_loaded:648 - 账户加载完成: 2个账户
2025-06-30 23:47:40.245 | DEBUG    | ui.views.account_view:_update_group_counts:186 - 分组计数已更新: {2: 1, 1: 1}
2025-06-30 23:47:40.248 | INFO     | ui.views.account_view:_auto_login_accounts:703 - 开始调用控制器进行 2 个账户的自动登录。
2025-06-30 23:47:40.249 | INFO     | app.services.account_service:batch_auto_login:1165 - 服务层：开始批量自动登录 2 个账户, 最大并发: 5
2025-06-30 23:47:40.249 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-30 23:47:40.252 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-06-30 23:47:40.257 | INFO     | app.controllers.telegram_monitor_controller:_load_total_users_count_internal:191 - 成功加载总体统计数据: 总用户数 23, 今日采集 0, 日均采集 23, 运行天数 1
2025-06-30 23:47:40.258 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:156 - 成功加载 0 个任务
2025-06-30 23:47:40.259 | DEBUG    | ui.views.monitor_view:_on_loading_finished:625 - 加载完成
2025-06-30 23:47:40.273 | INFO     | app.services.account_service:batch_auto_login:1204 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-06-30 23:47:40.274 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-30 23:47:40.275 | INFO     | app.services.account_service:get_all_accounts:290 - 获取所有账户成功, 共 2 个
2025-06-30 23:47:40.275 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-30 23:47:40.277 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-30 23:47:40.279 | DEBUG    | ui.views.account_view:_update_group_counts:186 - 分组计数已更新: {2: 1, 1: 1}
2025-06-30 23:47:40.279 | DEBUG    | ui.views.account_view:_init_cache:153 - 账户缓存初始化完成，共 2 个账户
2025-06-30 23:47:40.285 | DEBUG    | ui.views.account_view:_on_accounts_loaded:648 - 账户加载完成: 2个账户
2025-06-30 23:47:40.307 | DEBUG    | ui.views.account_view:_update_group_counts:186 - 分组计数已更新: {2: 1, 1: 1}
2025-06-30 23:47:40.322 | INFO     | core.auth.api_service:verify_vip:438 - 会员验证成功
2025-06-30 23:47:40.322 | INFO     | utils.vip_checker:_check_vip_status:76 - VIP状态有效
2025-06-30 23:47:40.324 | INFO     | app.services.account_service:batch_auto_login:1204 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-06-30 23:47:40.325 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-30 23:47:40.327 | INFO     | app.services.account_service:batch_auto_login:1274 - 服务层：准备调用核心层处理 2 个账户的自动登录。
2025-06-30 23:47:40.328 | INFO     | app.services.account_service:batch_auto_login:1284 - 服务层：设置核心层任务超时为 120 秒。
2025-06-30 23:47:40.328 | INFO     | core.telegram.client_manager:batch_auto_login:1216 - 开始批量自动登录, 共 2 个账户, 最大并发 5
2025-06-30 23:47:40.329 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:259 - 创建客户端: h:\PyProject\TeleTest\APPDATA\sessions\+***********, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-06-30 23:47:40.330 | DEBUG    | core.telegram.client_manager:get_proxy:96 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-06-30 23:47:40.330 | DEBUG    | core.telegram.client_manager:get_proxy:143 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-06-30 23:47:40.336 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:259 - 创建客户端: h:\PyProject\TeleTest\APPDATA\sessions\+***********, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-06-30 23:47:40.337 | DEBUG    | core.telegram.client_manager:get_proxy:96 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-06-30 23:47:40.338 | DEBUG    | core.telegram.client_manager:get_proxy:143 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-06-30 23:47:40.362 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-30 23:47:40.444 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-06-30 23:47:40.503 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-06-30 23:47:40.523 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-06-30 23:47:40.525 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-06-30 23:47:40.527 | INFO     | ui.views.proxy_view:_load_initial_data:1044 - 初始数据加载完成，共 1 个代理
2025-06-30 23:47:42.707 | INFO     | core.telegram.client_manager:_connect_client:207 - 连接成功!
2025-06-30 23:47:43.415 | INFO     | core.telegram.client_manager:_verify_client_authorization:1191 - 账户已授权: +***********
2025-06-30 23:47:44.361 | INFO     | core.telegram.client_manager:_connect_client:207 - 连接成功!
2025-06-30 23:47:45.027 | INFO     | core.telegram.client_manager:_verify_client_authorization:1191 - 账户已授权: +***********
2025-06-30 23:47:47.024 | INFO     | core.telegram.client_manager:batch_auto_login:1365 - 批量登录完成: 总计 2, 成功 2, 失败 0
2025-06-30 23:47:47.334 | INFO     | app.services.account_service:batch_auto_login:1305 - 服务层：核心层登录完成: 成功 2, 失败 0 / 2
2025-06-30 23:48:02.683 | INFO     | ui.main_window:closeEvent:380 - MainWindow: 接收到关闭事件
2025-06-30 23:48:02.699 | INFO     | ui.main_window:_cleanup_before_quit:266 - MainWindow: 执行清理资源...
2025-06-30 23:48:02.702 | INFO     | core.telegram.client_worker:stop:108 - 正在停止Telegram客户端工作线程
2025-06-30 23:48:02.710 | INFO     | core.telegram.client_manager:cleanup_async:1136 - 开始清理资源并断开所有连接
2025-06-30 23:48:02.711 | INFO     | core.telegram.client_manager:disconnect_all_clients:721 - 正在断开所有客户端连接
2025-06-30 23:48:02.711 | INFO     | core.telegram.client_manager:_safe_disconnect:742 - 正在断开客户端连接: +***********
2025-06-30 23:48:02.712 | INFO     | core.telegram.client_manager:_safe_disconnect:742 - 正在断开客户端连接: +***********
2025-06-30 23:48:02.722 | INFO     | core.telegram.client_manager:_safe_disconnect:746 - 断开客户端连接成功: +***********
2025-06-30 23:48:02.723 | INFO     | core.telegram.client_manager:_safe_disconnect:746 - 断开客户端连接成功: +***********
2025-06-30 23:48:02.723 | INFO     | core.telegram.client_manager:cleanup_async:1155 - 资源清理完成
2025-06-30 23:48:03.209 | INFO     | core.telegram.client_manager:cleanup_async:1136 - 开始清理资源并断开所有连接
2025-06-30 23:48:03.209 | INFO     | core.telegram.client_manager:disconnect_all_clients:721 - 正在断开所有客户端连接
2025-06-30 23:48:03.210 | INFO     | core.telegram.client_manager:cleanup_async:1155 - 资源清理完成
2025-06-30 23:48:03.711 | INFO     | core.telegram.client_worker:run:97 - Telegram客户端工作线程已退出
2025-06-30 23:48:03.712 | INFO     | core.telegram.client_worker:stop:122 - Telegram客户端工作线程已停止
2025-06-30 23:48:03.712 | INFO     | ui.main_window:_cleanup_before_quit:275 - TelegramClientWorker 已停止。
2025-06-30 23:48:03.712 | INFO     | ui.main_window:_cleanup_before_quit:279 - MainWindow 清理完成
2025-06-30 23:48:03.722 | INFO     | __main__:main:111 - 应用程序已正常退出
