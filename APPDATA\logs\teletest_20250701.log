2025-07-01 07:15:12.556 | INFO     | utils.logger:init_logger:98 - 日志系统初始化完成，默认级别: DEBUG，日志目录: H:\PyProject\TeleTest\APPDATA\logs
2025-07-01 07:15:15.512 | INFO     | data.database:init_database:31 - 初始化数据库连接: H:\PyProject\TeleTest\APPDATA\database\telegram_manager.db
2025-07-01 07:15:15.537 | DEBUG    | data.database:init_database:58 - 创建数据库表
2025-07-01 07:15:15.551 | INFO     | data.database:init_database:61 - 数据库初始化完成
2025-07-01 07:15:16.338 | DEBUG    | app.controllers.auth_controller:initialize_software:43 - 控制器: 初始化软件 app_id=1000, version=1.2.7
2025-07-01 07:15:16.339 | DEBUG    | core.auth.api_service:init_software:55 - 发送初始化请求: https://server.xile188.com/api/user/1000/windowns10/1.2.7/ini, params={}
2025-07-01 07:15:16.822 | DEBUG    | core.auth.api_service:init_software:66 - 初始化数据解密成功: {'new_content': '- 新增功能\n  - 1.新增图片上传功能\n  - 2.新增软件文档\n  - 3.新增软件自动更新，后续更新会自动检测版本更新，无需再下载更新包\n  - 4.新增设置账户信息及发信控制\n- 修复bug\n  - 删除监听任务时，不删除对应任务采集到的数据。\n  - 监听设置群无法发送到目标群\n  - 导出监听数据时应当导出单个任务而非所有数据\n- 优化记录\n  - 优化发信规则，直接调取符合要求的账户发送，无账户时才需要等待延时。\n  - 完善软件设置', 'new_url': None, 'notice': {'id': '2', 'content': '公告内容测试3', 'time': '**********'}}
2025-07-01 07:15:16.830 | INFO     | app.controllers.auth_controller:login:61 - 控制器: 用户登录 account=demo01
2025-07-01 07:15:19.731 | INFO     | core.auth.api_service:login:127 - 用户登录: account=demo01
2025-07-01 07:15:20.001 | INFO     | core.auth.api_service:get_user_info:347 - 获取用户信息
2025-07-01 07:15:20.207 | INFO     | core.auth.api_service:get_user_info:375 - 获取用户信息成功
2025-07-01 07:15:20.214 | INFO     | __main__:on_auth_completed:93 - 用户登录成功，正在启动主窗口...
2025-07-01 07:15:20.238 | INFO     | core.telegram.client_worker:__init__:61 - Telegram客户端工作线程初始化
2025-07-01 07:15:20.239 | INFO     | utils.vip_checker:initialize:25 - VIP检查器已初始化，等待登录后开始检查
2025-07-01 07:15:20.239 | INFO     | ui.main_window:_initialize_core_components:108 - MainWindow: 初始化核心组件...
2025-07-01 07:15:20.239 | INFO     | core.telegram.client_worker:run:65 - Telegram客户端工作线程开始运行
2025-07-01 07:15:20.240 | INFO     | core.proxy.proxy_task_manager:run:284 - 代理任务管理器线程启动
2025-07-01 07:15:20.240 | INFO     | app.services.account_service:__init__:42 - 账户服务初始化
2025-07-01 07:15:20.241 | INFO     | app.controllers.account_controller:__init__:81 - 账户控制器初始化
2025-07-01 07:15:20.241 | INFO     | core.telegram.client_manager:__init__:66 - Telegram客户端管理器初始化
2025-07-01 07:15:20.242 | INFO     | app.services.monitor_service:__init__:39 - 初始化监控任务服务
2025-07-01 07:15:20.242 | INFO     | core.telegram.user_manager:__init__:30 - Telegram用户管理器初始化
2025-07-01 07:15:20.242 | INFO     | core.monitor.filter:__init__:26 - 初始化消息过滤器
2025-07-01 07:15:20.243 | INFO     | core.telegram.group_manager:__init__:36 - Telegram群组管理器初始化
2025-07-01 07:15:20.243 | INFO     | core.monitor.extractor:__init__:24 - 初始化关键词提取器
2025-07-01 07:15:20.243 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-07-01 07:15:20.244 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-07-01 07:15:20.244 | INFO     | core.telegram.monitor_manager:__init__:26 - Telegram监控管理器初始化
2025-07-01 07:15:20.244 | INFO     | core.monitor.notifier:__init__:90 - 初始化通知管理器
2025-07-01 07:15:20.245 | INFO     | core.telegram.telegram_service:__init__:39 - Telegram服务初始化完成
2025-07-01 07:15:20.245 | INFO     | app.controllers.telegram_monitor_controller:__init__:64 - 初始化 Telegram 监控控制器
2025-07-01 07:15:20.246 | DEBUG    | app.controllers.telegram_monitor_controller:__init__:82 - 控制器初始化完成
2025-07-01 07:15:20.430 | INFO     | ui.views.monitor_view:__init__:48 - 初始化监控视图
2025-07-01 07:15:20.431 | DEBUG    | ui.views.monitor_view:_connect_signals:80 - 连接控制器信号
2025-07-01 07:15:20.590 | INFO     | ui.views.proxy_view:__init__:96 - 初始化视图，加载第一页代理数据
2025-07-01 07:15:20.805 | INFO     | __main__:on_auth_completed:97 - 主窗口已启动
2025-07-01 07:15:20.854 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:88 - 尝试连接监听日志信号...
2025-07-01 07:15:20.855 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:90 - 监听日志信号连接成功
2025-07-01 07:15:20.856 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-01 07:15:20.860 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-01 07:15:20.864 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:145 - 加载监控任务列表 (搜索关键词: '')
2025-07-01 07:15:20.864 | DEBUG    | ui.views.monitor_view:_on_loading_started:620 - 加载中: 正在加载任务列表...
2025-07-01 07:15:20.865 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-01 07:15:20.871 | INFO     | app.services.proxy_service:refresh_proxies:712 - 刷新代理列表: offset=0, limit=20
2025-07-01 07:15:20.872 | INFO     | core.proxy.proxy_task_manager:submit_task:172 - 提交耗时任务到子线程: refresh_proxies_0_20
2025-07-01 07:15:20.872 | DEBUG    | app.services.proxy_service:get_proxies:500 - 获取代理列表: offset=0, limit=20
2025-07-01 07:15:20.873 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-01 07:15:20.873 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-01 07:15:20.878 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-01 07:15:20.901 | INFO     | app.controllers.invite_controller:get_invite_tasks:38 - 获取邀请任务列表, status=None
2025-07-01 07:15:20.902 | INFO     | app.services.invite_service:get_invite_tasks:121 - 获取邀请任务列表, status=None
2025-07-01 07:15:20.902 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-01 07:15:20.905 | INFO     | app.services.invite_service:task_initialize:39 - 初始化邀请服务，重置运行中的任务为暂停状态
2025-07-01 07:15:20.905 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-01 07:15:20.906 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-01 07:15:20.907 | INFO     | core.proxy.proxy_task_manager:task_wrapper:199 - 任务 refresh_proxies_0_20 完成
2025-07-01 07:15:20.909 | INFO     | utils.vip_checker:start_checking:37 - 开始VIP状态检查
2025-07-01 07:15:20.910 | DEBUG    | utils.vip_checker:_check_vip_status:61 - 正在检查VIP状态...
2025-07-01 07:15:20.910 | INFO     | core.auth.api_service:verify_vip:425 - 会员验证接口调用
2025-07-01 07:15:21.150 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-01 07:15:21.151 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-01 07:15:21.155 | DEBUG    | ui.views.monitor_view:_refresh_task_list:175 - 刷新任务列表，共0个任务
2025-07-01 07:15:21.155 | INFO     | ui.views.monitor_view:_refresh_task_list:189 - 没有找到监控任务
2025-07-01 07:15:21.155 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-01 07:15:21.157 | INFO     | app.services.invite_service:task_initialize:48 - 成功重置 0 个任务状态
2025-07-01 07:15:21.157 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-01 07:15:21.159 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-01 07:15:21.161 | INFO     | app.services.account_service:get_all_groups:96 - 获取所有账户分组成功, 共 2 个
2025-07-01 07:15:21.161 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-01 07:15:21.163 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-01 07:15:21.164 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-01 07:15:21.166 | DEBUG    | ui.views.account_view:_on_groups_loaded:515 - 分组加载完成: 2个分组
2025-07-01 07:15:21.181 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-01 07:15:21.239 | INFO     | app.services.account_service:get_all_accounts:290 - 获取所有账户成功, 共 2 个
2025-07-01 07:15:21.240 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-01 07:15:21.241 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-01 07:15:21.246 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-01 07:15:21.247 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-01 07:15:21.249 | DEBUG    | ui.views.account_view:_on_accounts_loaded:648 - 账户加载完成: 2个账户
2025-07-01 07:15:21.268 | DEBUG    | ui.views.account_view:_update_group_counts:186 - 分组计数已更新: {2: 1, 1: 1}
2025-07-01 07:15:21.272 | INFO     | ui.views.account_view:_auto_login_accounts:703 - 开始调用控制器进行 2 个账户的自动登录。
2025-07-01 07:15:21.273 | INFO     | app.services.account_service:batch_auto_login:1165 - 服务层：开始批量自动登录 2 个账户, 最大并发: 5
2025-07-01 07:15:21.273 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-01 07:15:21.277 | INFO     | app.controllers.telegram_monitor_controller:_load_total_users_count_internal:191 - 成功加载总体统计数据: 总用户数 23, 今日采集 0, 日均采集 23, 运行天数 1
2025-07-01 07:15:21.278 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:156 - 成功加载 0 个任务
2025-07-01 07:15:21.279 | DEBUG    | ui.views.monitor_view:_on_loading_finished:625 - 加载完成
2025-07-01 07:15:21.282 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-07-01 07:15:21.326 | INFO     | app.services.account_service:batch_auto_login:1204 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-07-01 07:15:21.327 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-01 07:15:21.329 | INFO     | app.services.account_service:get_all_accounts:290 - 获取所有账户成功, 共 2 个
2025-07-01 07:15:21.329 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-01 07:15:21.330 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-01 07:15:21.333 | DEBUG    | ui.views.account_view:_update_group_counts:186 - 分组计数已更新: {2: 1, 1: 1}
2025-07-01 07:15:21.334 | DEBUG    | ui.views.account_view:_init_cache:153 - 账户缓存初始化完成，共 2 个账户
2025-07-01 07:15:21.341 | DEBUG    | ui.views.account_view:_on_accounts_loaded:648 - 账户加载完成: 2个账户
2025-07-01 07:15:21.362 | DEBUG    | ui.views.account_view:_update_group_counts:186 - 分组计数已更新: {2: 1, 1: 1}
2025-07-01 07:15:21.370 | INFO     | app.services.account_service:batch_auto_login:1204 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-07-01 07:15:21.370 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-01 07:15:21.376 | INFO     | core.auth.api_service:verify_vip:438 - 会员验证成功
2025-07-01 07:15:21.376 | INFO     | utils.vip_checker:_check_vip_status:76 - VIP状态有效
2025-07-01 07:15:21.378 | INFO     | app.services.account_service:batch_auto_login:1274 - 服务层：准备调用核心层处理 2 个账户的自动登录。
2025-07-01 07:15:21.379 | INFO     | app.services.account_service:batch_auto_login:1284 - 服务层：设置核心层任务超时为 120 秒。
2025-07-01 07:15:21.379 | INFO     | core.telegram.client_manager:batch_auto_login:1216 - 开始批量自动登录, 共 2 个账户, 最大并发 5
2025-07-01 07:15:21.380 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:259 - 创建客户端: H:\PyProject\TeleTest\APPDATA\sessions\+***********, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-01 07:15:21.380 | DEBUG    | core.telegram.client_manager:get_proxy:96 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-01 07:15:21.381 | DEBUG    | core.telegram.client_manager:get_proxy:143 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-07-01 07:15:21.386 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:259 - 创建客户端: H:\PyProject\TeleTest\APPDATA\sessions\+***********, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-01 07:15:21.387 | DEBUG    | core.telegram.client_manager:get_proxy:96 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-01 07:15:21.387 | DEBUG    | core.telegram.client_manager:get_proxy:143 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-07-01 07:15:21.414 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-01 07:15:21.485 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-01 07:15:21.517 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-01 07:15:21.527 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-07-01 07:15:21.529 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-07-01 07:15:21.531 | INFO     | ui.views.proxy_view:_load_initial_data:1044 - 初始数据加载完成，共 1 个代理
2025-07-01 07:15:24.451 | INFO     | core.telegram.client_manager:_connect_client:207 - 连接成功!
2025-07-01 07:15:24.744 | INFO     | core.telegram.client_manager:_connect_client:207 - 连接成功!
2025-07-01 07:15:25.341 | INFO     | core.telegram.client_manager:_verify_client_authorization:1191 - 账户已授权: +***********
2025-07-01 07:15:25.822 | INFO     | core.telegram.client_manager:_verify_client_authorization:1191 - 账户已授权: +***********
2025-07-01 07:15:27.827 | INFO     | core.telegram.client_manager:batch_auto_login:1365 - 批量登录完成: 总计 2, 成功 2, 失败 0
2025-07-01 07:15:28.385 | INFO     | app.services.account_service:batch_auto_login:1305 - 服务层：核心层登录完成: 成功 2, 失败 0 / 2
2025-07-01 07:15:54.574 | INFO     | app.services.account_service:refresh_account_info:607 - 刷新账户信息: 1
2025-07-01 07:15:54.574 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-01 07:15:54.586 | INFO     | core.telegram.user_manager:get_user_info:41 - 正在获取用户信息: +***********
2025-07-01 07:15:55.469 | INFO     | core.telegram.user_manager:get_user_info:66 - 获取用户信息成功: +***********
2025-07-01 07:15:55.835 | INFO     | data.repositories.account_repo:update_account:378 - 更新账户成功: 1
2025-07-01 07:15:55.843 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-01 07:15:59.034 | INFO     | app.services.account_service:refresh_account_info:607 - 刷新账户信息: 2
2025-07-01 07:15:59.035 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-01 07:15:59.044 | INFO     | core.telegram.user_manager:get_user_info:41 - 正在获取用户信息: +***********
2025-07-01 07:16:00.066 | INFO     | core.telegram.user_manager:get_user_info:66 - 获取用户信息成功: +***********
2025-07-01 07:16:00.289 | INFO     | data.repositories.account_repo:update_account:378 - 更新账户成功: 2
2025-07-01 07:16:00.329 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-01 07:16:03.136 | INFO     | ui.main_window:closeEvent:380 - MainWindow: 接收到关闭事件
2025-07-01 07:16:03.153 | INFO     | ui.main_window:_cleanup_before_quit:266 - MainWindow: 执行清理资源...
2025-07-01 07:16:03.154 | INFO     | core.telegram.client_worker:stop:108 - 正在停止Telegram客户端工作线程
2025-07-01 07:16:03.162 | INFO     | core.telegram.client_manager:cleanup_async:1136 - 开始清理资源并断开所有连接
2025-07-01 07:16:03.162 | INFO     | core.telegram.client_manager:disconnect_all_clients:721 - 正在断开所有客户端连接
2025-07-01 07:16:03.163 | INFO     | core.telegram.client_manager:_safe_disconnect:742 - 正在断开客户端连接: +***********
2025-07-01 07:16:03.164 | INFO     | core.telegram.client_manager:_safe_disconnect:742 - 正在断开客户端连接: +***********
2025-07-01 07:16:03.404 | INFO     | core.telegram.client_manager:_safe_disconnect:746 - 断开客户端连接成功: +***********
2025-07-01 07:16:03.405 | INFO     | core.telegram.client_manager:_safe_disconnect:746 - 断开客户端连接成功: +***********
2025-07-01 07:16:03.405 | INFO     | core.telegram.client_manager:cleanup_async:1155 - 资源清理完成
2025-07-01 07:16:03.671 | INFO     | core.telegram.client_manager:cleanup_async:1136 - 开始清理资源并断开所有连接
2025-07-01 07:16:03.671 | INFO     | core.telegram.client_manager:disconnect_all_clients:721 - 正在断开所有客户端连接
2025-07-01 07:16:03.671 | INFO     | core.telegram.client_manager:cleanup_async:1155 - 资源清理完成
2025-07-01 07:16:04.173 | INFO     | core.telegram.client_worker:run:97 - Telegram客户端工作线程已退出
2025-07-01 07:16:04.174 | INFO     | core.telegram.client_worker:stop:122 - Telegram客户端工作线程已停止
2025-07-01 07:16:04.175 | INFO     | ui.main_window:_cleanup_before_quit:275 - TelegramClientWorker 已停止。
2025-07-01 07:16:04.175 | INFO     | ui.main_window:_cleanup_before_quit:279 - MainWindow 清理完成
2025-07-01 07:16:04.184 | INFO     | __main__:main:111 - 应用程序已正常退出
2025-07-01 10:43:09.578 | INFO     | utils.logger:init_logger:98 - 日志系统初始化完成，默认级别: DEBUG，日志目录: H:\PyProject\TeleTest\APPDATA\logs
2025-07-01 10:43:11.410 | INFO     | data.database:init_database:31 - 初始化数据库连接: H:\PyProject\TeleTest\APPDATA\database\telegram_manager.db
2025-07-01 10:43:11.429 | DEBUG    | data.database:init_database:58 - 创建数据库表
2025-07-01 10:43:11.440 | INFO     | data.database:init_database:61 - 数据库初始化完成
2025-07-01 10:43:13.011 | DEBUG    | app.controllers.auth_controller:initialize_software:43 - 控制器: 初始化软件 app_id=1000, version=1.2.7
2025-07-01 10:43:13.013 | DEBUG    | core.auth.api_service:init_software:55 - 发送初始化请求: https://server.xile188.com/api/user/1000/windowns10/1.2.7/ini, params={}
2025-07-01 10:43:13.464 | DEBUG    | core.auth.api_service:init_software:66 - 初始化数据解密成功: {'new_content': '- 新增功能\n  - 1.新增图片上传功能\n  - 2.新增软件文档\n  - 3.新增软件自动更新，后续更新会自动检测版本更新，无需再下载更新包\n  - 4.新增设置账户信息及发信控制\n- 修复bug\n  - 删除监听任务时，不删除对应任务采集到的数据。\n  - 监听设置群无法发送到目标群\n  - 导出监听数据时应当导出单个任务而非所有数据\n- 优化记录\n  - 优化发信规则，直接调取符合要求的账户发送，无账户时才需要等待延时。\n  - 完善软件设置', 'new_url': None, 'notice': {'id': '2', 'content': '公告内容测试3', 'time': '**********'}}
2025-07-01 10:43:13.478 | INFO     | app.controllers.auth_controller:login:61 - 控制器: 用户登录 account=demo01
2025-07-01 10:43:16.420 | INFO     | core.auth.api_service:login:127 - 用户登录: account=demo01
2025-07-01 10:43:16.704 | INFO     | core.auth.api_service:get_user_info:347 - 获取用户信息
2025-07-01 10:43:16.980 | INFO     | core.auth.api_service:get_user_info:375 - 获取用户信息成功
2025-07-01 10:43:16.988 | INFO     | __main__:on_auth_completed:93 - 用户登录成功，正在启动主窗口...
2025-07-01 10:43:17.011 | INFO     | core.telegram.client_worker:__init__:61 - Telegram客户端工作线程初始化
2025-07-01 10:43:17.011 | INFO     | utils.vip_checker:initialize:25 - VIP检查器已初始化，等待登录后开始检查
2025-07-01 10:43:17.012 | INFO     | ui.main_window:_initialize_core_components:108 - MainWindow: 初始化核心组件...
2025-07-01 10:43:17.012 | INFO     | core.telegram.client_worker:run:65 - Telegram客户端工作线程开始运行
2025-07-01 10:43:17.013 | INFO     | app.services.account_service:__init__:42 - 账户服务初始化
2025-07-01 10:43:17.013 | INFO     | core.proxy.proxy_task_manager:run:284 - 代理任务管理器线程启动
2025-07-01 10:43:17.013 | INFO     | app.controllers.account_controller:__init__:81 - 账户控制器初始化
2025-07-01 10:43:17.014 | INFO     | core.telegram.client_manager:__init__:66 - Telegram客户端管理器初始化
2025-07-01 10:43:17.014 | INFO     | app.services.monitor_service:__init__:39 - 初始化监控任务服务
2025-07-01 10:43:17.014 | INFO     | core.telegram.user_manager:__init__:30 - Telegram用户管理器初始化
2025-07-01 10:43:17.014 | INFO     | core.monitor.filter:__init__:26 - 初始化消息过滤器
2025-07-01 10:43:17.015 | INFO     | core.telegram.group_manager:__init__:36 - Telegram群组管理器初始化
2025-07-01 10:43:17.015 | INFO     | core.monitor.extractor:__init__:24 - 初始化关键词提取器
2025-07-01 10:43:17.015 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-07-01 10:43:17.015 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-07-01 10:43:17.016 | INFO     | core.telegram.monitor_manager:__init__:26 - Telegram监控管理器初始化
2025-07-01 10:43:17.016 | INFO     | core.monitor.notifier:__init__:90 - 初始化通知管理器
2025-07-01 10:43:17.016 | INFO     | core.telegram.telegram_service:__init__:39 - Telegram服务初始化完成
2025-07-01 10:43:17.016 | INFO     | app.controllers.telegram_monitor_controller:__init__:64 - 初始化 Telegram 监控控制器
2025-07-01 10:43:17.016 | DEBUG    | app.controllers.telegram_monitor_controller:__init__:82 - 控制器初始化完成
2025-07-01 10:43:17.199 | INFO     | ui.views.monitor_view:__init__:48 - 初始化监控视图
2025-07-01 10:43:17.199 | DEBUG    | ui.views.monitor_view:_connect_signals:80 - 连接控制器信号
2025-07-01 10:43:17.358 | INFO     | ui.views.proxy_view:__init__:96 - 初始化视图，加载第一页代理数据
2025-07-01 10:43:17.581 | INFO     | __main__:on_auth_completed:97 - 主窗口已启动
2025-07-01 10:43:17.645 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:88 - 尝试连接监听日志信号...
2025-07-01 10:43:17.645 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:90 - 监听日志信号连接成功
2025-07-01 10:43:17.646 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-01 10:43:17.650 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-01 10:43:17.655 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:145 - 加载监控任务列表 (搜索关键词: '')
2025-07-01 10:43:17.656 | DEBUG    | ui.views.monitor_view:_on_loading_started:620 - 加载中: 正在加载任务列表...
2025-07-01 10:43:17.656 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-01 10:43:17.663 | INFO     | app.services.proxy_service:refresh_proxies:712 - 刷新代理列表: offset=0, limit=20
2025-07-01 10:43:17.663 | INFO     | core.proxy.proxy_task_manager:submit_task:172 - 提交耗时任务到子线程: refresh_proxies_0_20
2025-07-01 10:43:17.663 | DEBUG    | app.services.proxy_service:get_proxies:500 - 获取代理列表: offset=0, limit=20
2025-07-01 10:43:17.663 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-01 10:43:17.664 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-01 10:43:17.669 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-01 10:43:17.692 | INFO     | app.controllers.invite_controller:get_invite_tasks:38 - 获取邀请任务列表, status=None
2025-07-01 10:43:17.692 | INFO     | app.services.invite_service:get_invite_tasks:121 - 获取邀请任务列表, status=None
2025-07-01 10:43:17.693 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-01 10:43:17.695 | INFO     | app.services.invite_service:task_initialize:39 - 初始化邀请服务，重置运行中的任务为暂停状态
2025-07-01 10:43:17.696 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-01 10:43:17.696 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-01 10:43:17.698 | INFO     | core.proxy.proxy_task_manager:task_wrapper:199 - 任务 refresh_proxies_0_20 完成
2025-07-01 10:43:17.699 | INFO     | utils.vip_checker:start_checking:37 - 开始VIP状态检查
2025-07-01 10:43:17.700 | DEBUG    | utils.vip_checker:_check_vip_status:61 - 正在检查VIP状态...
2025-07-01 10:43:17.700 | INFO     | core.auth.api_service:verify_vip:425 - 会员验证接口调用
2025-07-01 10:43:17.823 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-01 10:43:17.824 | INFO     | app.services.invite_service:task_initialize:48 - 成功重置 0 个任务状态
2025-07-01 10:43:17.825 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-01 10:43:17.827 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-01 10:43:17.830 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-01 10:43:17.831 | DEBUG    | ui.views.monitor_view:_refresh_task_list:175 - 刷新任务列表，共0个任务
2025-07-01 10:43:17.831 | INFO     | ui.views.monitor_view:_refresh_task_list:189 - 没有找到监控任务
2025-07-01 10:43:17.831 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-01 10:43:17.833 | INFO     | app.services.account_service:get_all_groups:96 - 获取所有账户分组成功, 共 2 个
2025-07-01 10:43:17.834 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-01 10:43:17.836 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-01 10:43:17.838 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-01 10:43:17.839 | DEBUG    | ui.views.account_view:_on_groups_loaded:515 - 分组加载完成: 2个分组
2025-07-01 10:43:17.852 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-01 10:43:17.912 | INFO     | app.services.account_service:get_all_accounts:290 - 获取所有账户成功, 共 2 个
2025-07-01 10:43:17.913 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-01 10:43:17.915 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-01 10:43:17.917 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-01 10:43:17.918 | DEBUG    | ui.views.account_view:_on_accounts_loaded:648 - 账户加载完成: 2个账户
2025-07-01 10:43:17.938 | DEBUG    | ui.views.account_view:_update_group_counts:186 - 分组计数已更新: {2: 1, 1: 1}
2025-07-01 10:43:17.942 | INFO     | ui.views.account_view:_auto_login_accounts:703 - 开始调用控制器进行 2 个账户的自动登录。
2025-07-01 10:43:17.942 | INFO     | app.services.account_service:batch_auto_login:1165 - 服务层：开始批量自动登录 2 个账户, 最大并发: 5
2025-07-01 10:43:17.942 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-01 10:43:17.946 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-01 10:43:17.947 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-07-01 10:43:18.007 | INFO     | app.controllers.telegram_monitor_controller:_load_total_users_count_internal:191 - 成功加载总体统计数据: 总用户数 23, 今日采集 0, 日均采集 23, 运行天数 1
2025-07-01 10:43:18.007 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:156 - 成功加载 0 个任务
2025-07-01 10:43:18.008 | DEBUG    | ui.views.monitor_view:_on_loading_finished:625 - 加载完成
2025-07-01 10:43:18.010 | INFO     | app.services.account_service:get_all_accounts:290 - 获取所有账户成功, 共 2 个
2025-07-01 10:43:18.010 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-01 10:43:18.012 | INFO     | app.services.account_service:batch_auto_login:1204 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-07-01 10:43:18.012 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-01 10:43:18.013 | DEBUG    | ui.views.account_view:_update_group_counts:186 - 分组计数已更新: {2: 1, 1: 1}
2025-07-01 10:43:18.014 | DEBUG    | ui.views.account_view:_init_cache:153 - 账户缓存初始化完成，共 2 个账户
2025-07-01 10:43:18.021 | DEBUG    | ui.views.account_view:_on_accounts_loaded:648 - 账户加载完成: 2个账户
2025-07-01 10:43:18.045 | DEBUG    | ui.views.account_view:_update_group_counts:186 - 分组计数已更新: {2: 1, 1: 1}
2025-07-01 10:43:18.052 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-01 10:43:18.061 | INFO     | app.services.account_service:batch_auto_login:1204 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-07-01 10:43:18.061 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-01 10:43:18.063 | INFO     | app.services.account_service:batch_auto_login:1274 - 服务层：准备调用核心层处理 2 个账户的自动登录。
2025-07-01 10:43:18.063 | INFO     | app.services.account_service:batch_auto_login:1284 - 服务层：设置核心层任务超时为 120 秒。
2025-07-01 10:43:18.063 | INFO     | core.telegram.client_manager:batch_auto_login:1216 - 开始批量自动登录, 共 2 个账户, 最大并发 5
2025-07-01 10:43:18.064 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:259 - 创建客户端: H:\PyProject\TeleTest\APPDATA\sessions\+***********, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-01 10:43:18.065 | DEBUG    | core.telegram.client_manager:get_proxy:96 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-01 10:43:18.065 | DEBUG    | core.telegram.client_manager:get_proxy:143 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-07-01 10:43:18.069 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:259 - 创建客户端: H:\PyProject\TeleTest\APPDATA\sessions\+***********, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-01 10:43:18.070 | DEBUG    | core.telegram.client_manager:get_proxy:96 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-01 10:43:18.071 | DEBUG    | core.telegram.client_manager:get_proxy:143 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-07-01 10:43:18.144 | INFO     | core.auth.api_service:verify_vip:438 - 会员验证成功
2025-07-01 10:43:18.144 | INFO     | utils.vip_checker:_check_vip_status:76 - VIP状态有效
2025-07-01 10:43:18.149 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-01 10:43:18.228 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-01 10:43:18.233 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-07-01 10:43:18.235 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-07-01 10:43:18.237 | INFO     | ui.views.proxy_view:_load_initial_data:1044 - 初始数据加载完成，共 1 个代理
2025-07-01 10:43:18.264 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-01 10:43:22.545 | INFO     | core.telegram.client_manager:_connect_client:207 - 连接成功!
2025-07-01 10:43:23.581 | INFO     | core.telegram.client_manager:_verify_client_authorization:1191 - 账户已授权: +***********
2025-07-01 10:43:23.969 | INFO     | core.telegram.client_manager:_connect_client:207 - 连接成功!
2025-07-01 10:43:25.053 | INFO     | core.telegram.client_manager:_verify_client_authorization:1191 - 账户已授权: +***********
2025-07-01 10:43:27.064 | INFO     | core.telegram.client_manager:batch_auto_login:1365 - 批量登录完成: 总计 2, 成功 2, 失败 0
2025-07-01 10:43:27.131 | INFO     | app.services.account_service:batch_auto_login:1305 - 服务层：核心层登录完成: 成功 2, 失败 0 / 2
2025-07-01 10:43:29.202 | INFO     | ui.main_window:closeEvent:380 - MainWindow: 接收到关闭事件
2025-07-01 10:43:29.216 | INFO     | ui.main_window:_cleanup_before_quit:266 - MainWindow: 执行清理资源...
2025-07-01 10:43:29.218 | INFO     | core.telegram.client_worker:stop:108 - 正在停止Telegram客户端工作线程
2025-07-01 10:43:29.227 | INFO     | core.telegram.client_manager:cleanup_async:1136 - 开始清理资源并断开所有连接
2025-07-01 10:43:29.228 | INFO     | core.telegram.client_manager:disconnect_all_clients:721 - 正在断开所有客户端连接
2025-07-01 10:43:29.228 | INFO     | core.telegram.client_manager:_safe_disconnect:742 - 正在断开客户端连接: +***********
2025-07-01 10:43:29.230 | INFO     | core.telegram.client_manager:_safe_disconnect:742 - 正在断开客户端连接: +***********
2025-07-01 10:43:29.246 | INFO     | core.telegram.client_manager:_safe_disconnect:746 - 断开客户端连接成功: +***********
2025-07-01 10:43:29.246 | INFO     | core.telegram.client_manager:_safe_disconnect:746 - 断开客户端连接成功: +***********
2025-07-01 10:43:29.247 | INFO     | core.telegram.client_manager:cleanup_async:1155 - 资源清理完成
2025-07-01 10:43:29.733 | INFO     | core.telegram.client_manager:cleanup_async:1136 - 开始清理资源并断开所有连接
2025-07-01 10:43:29.734 | INFO     | core.telegram.client_manager:disconnect_all_clients:721 - 正在断开所有客户端连接
2025-07-01 10:43:29.734 | INFO     | core.telegram.client_manager:cleanup_async:1155 - 资源清理完成
2025-07-01 10:43:30.236 | INFO     | core.telegram.client_worker:run:97 - Telegram客户端工作线程已退出
2025-07-01 10:43:30.237 | INFO     | core.telegram.client_worker:stop:122 - Telegram客户端工作线程已停止
2025-07-01 10:43:30.237 | INFO     | ui.main_window:_cleanup_before_quit:275 - TelegramClientWorker 已停止。
2025-07-01 10:43:30.238 | INFO     | ui.main_window:_cleanup_before_quit:279 - MainWindow 清理完成
2025-07-01 10:43:30.251 | INFO     | __main__:main:111 - 应用程序已正常退出
