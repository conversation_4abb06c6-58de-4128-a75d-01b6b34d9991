2025-07-10 10:43:40.279 | INFO     | utils.logger:init_logger:98 - 日志系统初始化完成，默认级别: DEBUG，日志目录: H:\PyProject\TeleTest\APPDATA\logs
2025-07-10 10:43:43.264 | INFO     | data.database:init_database:31 - 初始化数据库连接: H:\PyProject\TeleTest\APPDATA\database\telegram_manager.db
2025-07-10 10:43:43.298 | DEBUG    | data.database:init_database:58 - 创建数据库表
2025-07-10 10:43:43.316 | INFO     | data.database:init_database:61 - 数据库初始化完成
2025-07-10 10:43:44.337 | DEBUG    | app.controllers.auth_controller:initialize_software:43 - 控制器: 初始化软件 app_id=1000, version=1.2.7
2025-07-10 10:43:44.340 | DEBUG    | core.auth.api_service:init_software:55 - 发送初始化请求: https://server.xile188.com/api/user/1000/windowns10/1.2.7/ini, params={}
2025-07-10 10:43:44.854 | DEBUG    | core.auth.api_service:init_software:66 - 初始化数据解密成功: {'new_content': '- 新增功能\n  - 1.新增图片上传功能\n  - 2.新增软件文档\n  - 3.新增软件自动更新，后续更新会自动检测版本更新，无需再下载更新包\n  - 4.新增设置账户信息及发信控制\n- 修复bug\n  - 删除监听任务时，不删除对应任务采集到的数据。\n  - 监听设置群无法发送到目标群\n  - 导出监听数据时应当导出单个任务而非所有数据\n- 优化记录\n  - 优化发信规则，直接调取符合要求的账户发送，无账户时才需要等待延时。\n  - 完善软件设置', 'new_url': None, 'notice': {'id': '2', 'content': '公告内容测试3', 'time': '**********'}}
2025-07-10 10:43:44.864 | INFO     | app.controllers.auth_controller:login:61 - 控制器: 用户登录 account=demo01
2025-07-10 10:43:47.832 | INFO     | core.auth.api_service:login:127 - 用户登录: account=demo01
2025-07-10 10:43:48.129 | INFO     | core.auth.api_service:get_user_info:347 - 获取用户信息
2025-07-10 10:43:48.381 | INFO     | core.auth.api_service:get_user_info:375 - 获取用户信息成功
2025-07-10 10:43:48.392 | INFO     | __main__:on_auth_completed:93 - 用户登录成功，正在启动主窗口...
2025-07-10 10:43:48.418 | INFO     | core.telegram.client_worker:__init__:61 - Telegram客户端工作线程初始化
2025-07-10 10:43:48.419 | INFO     | utils.vip_checker:initialize:25 - VIP检查器已初始化，等待登录后开始检查
2025-07-10 10:43:48.419 | INFO     | ui.main_window:_initialize_core_components:108 - MainWindow: 初始化核心组件...
2025-07-10 10:43:48.419 | INFO     | core.telegram.client_worker:run:65 - Telegram客户端工作线程开始运行
2025-07-10 10:43:48.420 | INFO     | core.proxy.proxy_task_manager:run:284 - 代理任务管理器线程启动
2025-07-10 10:43:48.420 | INFO     | app.services.account_service:__init__:42 - 账户服务初始化
2025-07-10 10:43:48.421 | INFO     | app.controllers.account_controller:__init__:81 - 账户控制器初始化
2025-07-10 10:43:48.422 | INFO     | app.services.monitor_service:__init__:39 - 初始化监控任务服务
2025-07-10 10:43:48.422 | INFO     | core.telegram.client_manager:__init__:66 - Telegram客户端管理器初始化
2025-07-10 10:43:48.422 | INFO     | core.monitor.filter:__init__:26 - 初始化消息过滤器
2025-07-10 10:43:48.423 | INFO     | core.telegram.user_manager:__init__:30 - Telegram用户管理器初始化
2025-07-10 10:43:48.423 | INFO     | core.monitor.extractor:__init__:24 - 初始化关键词提取器
2025-07-10 10:43:48.424 | INFO     | core.telegram.group_manager:__init__:36 - Telegram群组管理器初始化
2025-07-10 10:43:48.424 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-07-10 10:43:48.424 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-07-10 10:43:48.425 | INFO     | core.monitor.notifier:__init__:90 - 初始化通知管理器
2025-07-10 10:43:48.425 | INFO     | core.telegram.monitor_manager:__init__:26 - Telegram监控管理器初始化
2025-07-10 10:43:48.425 | INFO     | app.controllers.telegram_monitor_controller:__init__:64 - 初始化 Telegram 监控控制器
2025-07-10 10:43:48.426 | INFO     | core.telegram.telegram_service:__init__:39 - Telegram服务初始化完成
2025-07-10 10:43:48.426 | DEBUG    | app.controllers.telegram_monitor_controller:__init__:82 - 控制器初始化完成
2025-07-10 10:43:48.640 | INFO     | ui.views.monitor_view:__init__:48 - 初始化监控视图
2025-07-10 10:43:48.640 | DEBUG    | ui.views.monitor_view:_connect_signals:80 - 连接控制器信号
2025-07-10 10:43:48.815 | INFO     | ui.views.proxy_view:__init__:96 - 初始化视图，加载第一页代理数据
2025-07-10 10:43:49.051 | INFO     | __main__:on_auth_completed:97 - 主窗口已启动
2025-07-10 10:43:49.113 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:88 - 尝试连接监听日志信号...
2025-07-10 10:43:49.114 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:90 - 监听日志信号连接成功
2025-07-10 10:43:49.115 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-10 10:43:49.118 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-10 10:43:49.123 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:145 - 加载监控任务列表 (搜索关键词: '')
2025-07-10 10:43:49.123 | DEBUG    | ui.views.monitor_view:_on_loading_started:620 - 加载中: 正在加载任务列表...
2025-07-10 10:43:49.124 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-10 10:43:49.131 | INFO     | app.services.proxy_service:refresh_proxies:712 - 刷新代理列表: offset=0, limit=20
2025-07-10 10:43:49.132 | INFO     | core.proxy.proxy_task_manager:submit_task:172 - 提交耗时任务到子线程: refresh_proxies_0_20
2025-07-10 10:43:49.133 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-10 10:43:49.133 | DEBUG    | app.services.proxy_service:get_proxies:500 - 获取代理列表: offset=0, limit=20
2025-07-10 10:43:49.134 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-10 10:43:49.156 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-10 10:43:49.167 | INFO     | app.controllers.invite_controller:get_invite_tasks:38 - 获取邀请任务列表, status=None
2025-07-10 10:43:49.169 | INFO     | app.services.invite_service:get_invite_tasks:121 - 获取邀请任务列表, status=None
2025-07-10 10:43:49.170 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-10 10:43:49.170 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-10 10:43:49.172 | INFO     | app.services.invite_service:task_initialize:39 - 初始化邀请服务，重置运行中的任务为暂停状态
2025-07-10 10:43:49.173 | INFO     | core.proxy.proxy_task_manager:task_wrapper:199 - 任务 refresh_proxies_0_20 完成
2025-07-10 10:43:49.173 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-10 10:43:49.176 | INFO     | utils.vip_checker:start_checking:37 - 开始VIP状态检查
2025-07-10 10:43:49.177 | DEBUG    | utils.vip_checker:_check_vip_status:61 - 正在检查VIP状态...
2025-07-10 10:43:49.177 | INFO     | core.auth.api_service:verify_vip:425 - 会员验证接口调用
2025-07-10 10:43:49.286 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-10 10:43:49.286 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-10 10:43:49.291 | DEBUG    | ui.views.monitor_view:_refresh_task_list:175 - 刷新任务列表，共0个任务
2025-07-10 10:43:49.292 | INFO     | ui.views.monitor_view:_refresh_task_list:189 - 没有找到监控任务
2025-07-10 10:43:49.292 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-10 10:43:49.293 | INFO     | app.services.invite_service:task_initialize:48 - 成功重置 0 个任务状态
2025-07-10 10:43:49.294 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-10 10:43:49.296 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-10 10:43:49.299 | INFO     | app.services.account_service:get_all_groups:96 - 获取所有账户分组成功, 共 2 个
2025-07-10 10:43:49.299 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-10 10:43:49.300 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-10 10:43:49.302 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-10 10:43:49.304 | DEBUG    | ui.views.account_view:_on_groups_loaded:515 - 分组加载完成: 2个分组
2025-07-10 10:43:49.319 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-10 10:43:49.390 | INFO     | app.services.account_service:get_all_accounts:290 - 获取所有账户成功, 共 2 个
2025-07-10 10:43:49.390 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-10 10:43:49.391 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-10 10:43:49.391 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-10 10:43:49.393 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-10 10:43:49.394 | DEBUG    | ui.views.account_view:_on_accounts_loaded:648 - 账户加载完成: 2个账户
2025-07-10 10:43:49.419 | DEBUG    | ui.views.account_view:_update_group_counts:186 - 分组计数已更新: {2: 1, 1: 1}
2025-07-10 10:43:49.425 | INFO     | ui.views.account_view:_auto_login_accounts:703 - 开始调用控制器进行 2 个账户的自动登录。
2025-07-10 10:43:49.426 | INFO     | app.services.account_service:batch_auto_login:1165 - 服务层：开始批量自动登录 2 个账户, 最大并发: 5
2025-07-10 10:43:49.426 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-10 10:43:49.432 | INFO     | app.controllers.telegram_monitor_controller:_load_total_users_count_internal:191 - 成功加载总体统计数据: 总用户数 23, 今日采集 0, 日均采集 23, 运行天数 1
2025-07-10 10:43:49.433 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:156 - 成功加载 0 个任务
2025-07-10 10:43:49.433 | DEBUG    | ui.views.monitor_view:_on_loading_finished:625 - 加载完成
2025-07-10 10:43:49.434 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-07-10 10:43:49.452 | INFO     | app.services.account_service:get_all_accounts:290 - 获取所有账户成功, 共 2 个
2025-07-10 10:43:49.453 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-10 10:43:49.456 | INFO     | app.services.account_service:batch_auto_login:1204 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-07-10 10:43:49.457 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-10 10:43:49.459 | DEBUG    | ui.views.account_view:_update_group_counts:186 - 分组计数已更新: {2: 1, 1: 1}
2025-07-10 10:43:49.459 | DEBUG    | ui.views.account_view:_init_cache:153 - 账户缓存初始化完成，共 2 个账户
2025-07-10 10:43:49.464 | DEBUG    | ui.views.account_view:_on_accounts_loaded:648 - 账户加载完成: 2个账户
2025-07-10 10:43:49.491 | DEBUG    | ui.views.account_view:_update_group_counts:186 - 分组计数已更新: {2: 1, 1: 1}
2025-07-10 10:43:49.500 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-10 10:43:49.543 | INFO     | app.services.account_service:batch_auto_login:1204 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-07-10 10:43:49.543 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-10 10:43:49.550 | INFO     | app.services.account_service:batch_auto_login:1274 - 服务层：准备调用核心层处理 2 个账户的自动登录。
2025-07-10 10:43:49.551 | INFO     | core.telegram.client_manager:batch_auto_login:1216 - 开始批量自动登录, 共 2 个账户, 最大并发 5
2025-07-10 10:43:49.551 | INFO     | app.services.account_service:batch_auto_login:1284 - 服务层：设置核心层任务超时为 120 秒。
2025-07-10 10:43:49.552 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:259 - 创建客户端: H:\PyProject\TeleTest\APPDATA\sessions\+***********, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-10 10:43:49.552 | DEBUG    | core.telegram.client_manager:get_proxy:96 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-10 10:43:49.553 | DEBUG    | core.telegram.client_manager:get_proxy:143 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-07-10 10:43:49.559 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:259 - 创建客户端: H:\PyProject\TeleTest\APPDATA\sessions\+***********, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-10 10:43:49.560 | DEBUG    | core.telegram.client_manager:get_proxy:96 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-10 10:43:49.560 | DEBUG    | core.telegram.client_manager:get_proxy:143 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-07-10 10:43:49.637 | WARNING  | core.auth.api_service:verify_vip:440 - 会员验证失败: 验证失败
2025-07-10 10:43:49.637 | ERROR    | utils.vip_checker:_check_vip_status:81 - VIP验证失败: 验证失败
2025-07-10 10:43:52.840 | INFO     | core.telegram.client_manager:_connect_client:207 - 连接成功!
2025-07-10 10:43:53.742 | INFO     | core.telegram.client_manager:_connect_client:207 - 连接成功!
2025-07-10 10:43:53.749 | INFO     | core.telegram.client_manager:_verify_client_authorization:1191 - 账户已授权: +***********
2025-07-10 10:43:54.680 | INFO     | core.telegram.client_manager:_verify_client_authorization:1191 - 账户已授权: +***********
2025-07-10 10:43:56.684 | INFO     | core.telegram.client_manager:batch_auto_login:1365 - 批量登录完成: 总计 2, 成功 2, 失败 0
2025-07-10 10:44:04.694 | INFO     | app.services.account_service:refresh_account_info:607 - 刷新账户信息: 1
2025-07-10 10:44:04.694 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-10 10:44:04.702 | ERROR    | data.repositories.message_repo:get_task_target_stats:187 - 获取任务2目标统计失败: coroutine ignored GeneratorExit
2025-07-10 10:44:04.703 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-10 10:44:04.704 | ERROR    | ui.views.send_msg_view:load_tasks:50 - 加载任务列表失败: (sqlite3.OperationalError) no active connection
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 10:44:04.726 | INFO     | core.telegram.user_manager:get_user_info:41 - 正在获取用户信息: +***********
2025-07-10 10:44:05.726 | INFO     | core.telegram.user_manager:get_user_info:66 - 获取用户信息成功: +***********
2025-07-10 10:44:05.976 | INFO     | data.repositories.account_repo:update_account:378 - 更新账户成功: 1
2025-07-10 10:44:11.018 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-10 10:44:11.019 | ERROR    | app.services.account_service:refresh_account_info:666 - 刷新账户信息异常: (sqlite3.OperationalError) database is locked
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 10:44:46.894 | INFO     | ui.main_window:closeEvent:380 - MainWindow: 接收到关闭事件
2025-07-10 10:44:46.905 | INFO     | ui.main_window:_cleanup_before_quit:266 - MainWindow: 执行清理资源...
2025-07-10 10:44:46.908 | INFO     | core.telegram.client_worker:stop:108 - 正在停止Telegram客户端工作线程
2025-07-10 10:44:46.919 | INFO     | core.telegram.client_manager:cleanup_async:1136 - 开始清理资源并断开所有连接
2025-07-10 10:44:46.920 | INFO     | core.telegram.client_manager:disconnect_all_clients:721 - 正在断开所有客户端连接
2025-07-10 10:44:46.920 | INFO     | core.telegram.client_manager:_safe_disconnect:742 - 正在断开客户端连接: +***********
2025-07-10 10:44:46.922 | INFO     | core.telegram.client_manager:_safe_disconnect:742 - 正在断开客户端连接: +***********
2025-07-10 10:44:46.933 | INFO     | core.telegram.client_manager:_safe_disconnect:746 - 断开客户端连接成功: +***********
2025-07-10 10:44:46.934 | INFO     | core.telegram.client_manager:_safe_disconnect:746 - 断开客户端连接成功: +***********
2025-07-10 10:44:46.935 | INFO     | core.telegram.client_manager:cleanup_async:1155 - 资源清理完成
2025-07-10 10:44:47.420 | INFO     | core.telegram.client_manager:cleanup_async:1136 - 开始清理资源并断开所有连接
2025-07-10 10:44:47.420 | INFO     | core.telegram.client_manager:disconnect_all_clients:721 - 正在断开所有客户端连接
2025-07-10 10:44:47.421 | INFO     | core.telegram.client_manager:cleanup_async:1155 - 资源清理完成
2025-07-10 10:44:47.922 | INFO     | core.telegram.client_worker:run:97 - Telegram客户端工作线程已退出
2025-07-10 10:44:47.923 | INFO     | core.telegram.client_worker:stop:122 - Telegram客户端工作线程已停止
2025-07-10 10:44:47.924 | INFO     | ui.main_window:_cleanup_before_quit:275 - TelegramClientWorker 已停止。
2025-07-10 10:44:47.924 | INFO     | ui.main_window:_cleanup_before_quit:279 - MainWindow 清理完成
2025-07-10 10:44:47.932 | INFO     | __main__:main:111 - 应用程序已正常退出
