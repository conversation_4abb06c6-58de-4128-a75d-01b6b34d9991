2025-07-13 08:54:47.945 | INFO     | utils.logger:init_logger:98 - 日志系统初始化完成，默认级别: DEBUG，日志目录: H:\PyProject\TeleTest\APPDATA\logs
2025-07-13 08:54:50.293 | INFO     | data.database:init_database:31 - 初始化数据库连接: H:\PyProject\TeleTest\APPDATA\database\telegram_manager.db
2025-07-13 08:54:50.317 | DEBUG    | data.database:init_database:58 - 创建数据库表
2025-07-13 08:54:50.326 | INFO     | data.database:init_database:61 - 数据库初始化完成
2025-07-13 08:54:51.420 | DEBUG    | app.controllers.auth_controller:initialize_software:43 - 控制器: 初始化软件 app_id=1000, version=1.2.7
2025-07-13 08:54:51.420 | DEBUG    | core.auth.api_service:init_software:55 - 发送初始化请求: https://server.xile188.com/api/user/1000/windowns10/1.2.7/ini, params={}
2025-07-13 08:54:52.160 | DEBUG    | core.auth.api_service:init_software:66 - 初始化数据解密成功: {'new_content': '- 新增功能\n  - 1.新增图片上传功能\n  - 2.新增软件文档\n  - 3.新增软件自动更新，后续更新会自动检测版本更新，无需再下载更新包\n  - 4.新增设置账户信息及发信控制\n- 修复bug\n  - 删除监听任务时，不删除对应任务采集到的数据。\n  - 监听设置群无法发送到目标群\n  - 导出监听数据时应当导出单个任务而非所有数据\n- 优化记录\n  - 优化发信规则，直接调取符合要求的账户发送，无账户时才需要等待延时。\n  - 完善软件设置', 'new_url': None, 'notice': {'id': '2', 'content': '公告内容测试3', 'time': '**********'}}
2025-07-13 08:54:52.168 | INFO     | app.controllers.auth_controller:login:61 - 控制器: 用户登录 account=demo01
2025-07-13 08:54:55.162 | INFO     | core.auth.api_service:login:127 - 用户登录: account=demo01
2025-07-13 08:54:55.458 | INFO     | core.auth.api_service:get_user_info:347 - 获取用户信息
2025-07-13 08:54:55.671 | INFO     | core.auth.api_service:get_user_info:375 - 获取用户信息成功
2025-07-13 08:54:55.677 | INFO     | __main__:on_auth_completed:93 - 用户登录成功，正在启动主窗口...
2025-07-13 08:54:55.708 | INFO     | core.telegram.client_worker:__init__:61 - Telegram客户端工作线程初始化
2025-07-13 08:54:55.708 | INFO     | utils.vip_checker:initialize:25 - VIP检查器已初始化，等待登录后开始检查
2025-07-13 08:54:55.708 | INFO     | ui.main_window:_initialize_core_components:108 - MainWindow: 初始化核心组件...
2025-07-13 08:54:55.709 | INFO     | app.services.account_service:__init__:42 - 账户服务初始化
2025-07-13 08:54:55.709 | INFO     | app.controllers.account_controller:__init__:81 - 账户控制器初始化
2025-07-13 08:54:55.709 | INFO     | app.services.monitor_service:__init__:39 - 初始化监控任务服务
2025-07-13 08:54:55.709 | INFO     | core.telegram.client_worker:run:65 - Telegram客户端工作线程开始运行
2025-07-13 08:54:55.710 | INFO     | core.proxy.proxy_task_manager:run:284 - 代理任务管理器线程启动
2025-07-13 08:54:55.710 | INFO     | core.monitor.filter:__init__:26 - 初始化消息过滤器
2025-07-13 08:54:55.710 | INFO     | core.monitor.extractor:__init__:24 - 初始化关键词提取器
2025-07-13 08:54:55.710 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-07-13 08:54:55.711 | INFO     | core.monitor.notifier:__init__:90 - 初始化通知管理器
2025-07-13 08:54:55.711 | INFO     | app.controllers.telegram_monitor_controller:__init__:64 - 初始化 Telegram 监控控制器
2025-07-13 08:54:55.711 | DEBUG    | app.controllers.telegram_monitor_controller:__init__:82 - 控制器初始化完成
2025-07-13 08:54:55.713 | INFO     | core.telegram.client_manager:__init__:66 - Telegram客户端管理器初始化
2025-07-13 08:54:55.714 | INFO     | core.telegram.user_manager:__init__:30 - Telegram用户管理器初始化
2025-07-13 08:54:55.716 | INFO     | core.telegram.group_manager:__init__:36 - Telegram群组管理器初始化
2025-07-13 08:54:55.716 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-07-13 08:54:55.717 | INFO     | core.telegram.monitor_manager:__init__:26 - Telegram监控管理器初始化
2025-07-13 08:54:55.720 | INFO     | core.telegram.telegram_service:__init__:39 - Telegram服务初始化完成
2025-07-13 08:54:55.915 | INFO     | ui.views.monitor_view:__init__:48 - 初始化监控视图
2025-07-13 08:54:55.916 | DEBUG    | ui.views.monitor_view:_connect_signals:80 - 连接控制器信号
2025-07-13 08:54:56.108 | INFO     | ui.views.proxy_view:__init__:96 - 初始化视图，加载第一页代理数据
2025-07-13 08:54:56.450 | INFO     | __main__:on_auth_completed:97 - 主窗口已启动
2025-07-13 08:54:56.559 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:88 - 尝试连接监听日志信号...
2025-07-13 08:54:56.560 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:90 - 监听日志信号连接成功
2025-07-13 08:54:56.560 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-13 08:54:56.580 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-13 08:54:56.621 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:145 - 加载监控任务列表 (搜索关键词: '')
2025-07-13 08:54:56.621 | DEBUG    | ui.views.monitor_view:_on_loading_started:620 - 加载中: 正在加载任务列表...
2025-07-13 08:54:56.621 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-13 08:54:56.632 | INFO     | app.services.proxy_service:refresh_proxies:712 - 刷新代理列表: offset=0, limit=20
2025-07-13 08:54:56.633 | INFO     | core.proxy.proxy_task_manager:submit_task:172 - 提交耗时任务到子线程: refresh_proxies_0_20
2025-07-13 08:54:56.633 | DEBUG    | app.services.proxy_service:get_proxies:500 - 获取代理列表: offset=0, limit=20
2025-07-13 08:54:56.633 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-13 08:54:56.633 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-13 08:54:56.637 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-13 08:54:56.663 | INFO     | app.controllers.invite_controller:get_invite_tasks:38 - 获取邀请任务列表, status=None
2025-07-13 08:54:56.664 | INFO     | app.services.invite_service:get_invite_tasks:121 - 获取邀请任务列表, status=None
2025-07-13 08:54:56.667 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-13 08:54:56.669 | INFO     | app.services.invite_service:task_initialize:39 - 初始化邀请服务，重置运行中的任务为暂停状态
2025-07-13 08:54:56.669 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-13 08:54:56.671 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-13 08:54:56.673 | INFO     | core.proxy.proxy_task_manager:task_wrapper:199 - 任务 refresh_proxies_0_20 完成
2025-07-13 08:54:56.676 | INFO     | utils.vip_checker:start_checking:37 - 开始VIP状态检查
2025-07-13 08:54:56.676 | DEBUG    | utils.vip_checker:_check_vip_status:61 - 正在检查VIP状态...
2025-07-13 08:54:56.677 | INFO     | core.auth.api_service:verify_vip:425 - 会员验证接口调用
2025-07-13 08:54:56.759 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-13 08:54:56.760 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-13 08:54:56.765 | DEBUG    | ui.views.monitor_view:_refresh_task_list:175 - 刷新任务列表，共0个任务
2025-07-13 08:54:56.766 | INFO     | ui.views.monitor_view:_refresh_task_list:189 - 没有找到监控任务
2025-07-13 08:54:56.766 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-13 08:54:56.767 | INFO     | app.services.invite_service:task_initialize:48 - 成功重置 0 个任务状态
2025-07-13 08:54:56.767 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-13 08:54:56.770 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-13 08:54:56.772 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-13 08:54:56.773 | INFO     | app.services.account_service:get_all_groups:96 - 获取所有账户分组成功, 共 2 个
2025-07-13 08:54:56.773 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-13 08:54:56.774 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-13 08:54:56.777 | DEBUG    | ui.views.account_view:_on_groups_loaded:515 - 分组加载完成: 2个分组
2025-07-13 08:54:56.791 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-13 08:54:56.851 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-13 08:54:56.851 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-13 08:54:56.852 | INFO     | app.services.account_service:get_all_accounts:290 - 获取所有账户成功, 共 2 个
2025-07-13 08:54:56.852 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-13 08:54:56.854 | INFO     | app.controllers.telegram_monitor_controller:_load_total_users_count_internal:191 - 成功加载总体统计数据: 总用户数 23, 今日采集 0, 日均采集 23, 运行天数 1
2025-07-13 08:54:56.855 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:156 - 成功加载 0 个任务
2025-07-13 08:54:56.855 | DEBUG    | ui.views.monitor_view:_on_loading_finished:625 - 加载完成
2025-07-13 08:54:56.856 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-07-13 08:54:56.865 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-13 08:54:56.866 | DEBUG    | ui.views.account_view:_on_accounts_loaded:648 - 账户加载完成: 2个账户
2025-07-13 08:54:56.892 | DEBUG    | ui.views.account_view:_update_group_counts:186 - 分组计数已更新: {2: 1, 1: 1}
2025-07-13 08:54:56.896 | INFO     | ui.views.account_view:_auto_login_accounts:703 - 开始调用控制器进行 2 个账户的自动登录。
2025-07-13 08:54:56.896 | INFO     | app.services.account_service:batch_auto_login:1165 - 服务层：开始批量自动登录 2 个账户, 最大并发: 5
2025-07-13 08:54:56.896 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-13 08:54:56.914 | INFO     | app.services.account_service:batch_auto_login:1204 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-07-13 08:54:56.915 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-13 08:54:56.915 | INFO     | app.services.account_service:get_all_accounts:290 - 获取所有账户成功, 共 2 个
2025-07-13 08:54:56.915 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-13 08:54:56.917 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-13 08:54:56.919 | DEBUG    | ui.views.account_view:_update_group_counts:186 - 分组计数已更新: {2: 1, 1: 1}
2025-07-13 08:54:56.919 | DEBUG    | ui.views.account_view:_init_cache:153 - 账户缓存初始化完成，共 2 个账户
2025-07-13 08:54:56.927 | DEBUG    | ui.views.account_view:_on_accounts_loaded:648 - 账户加载完成: 2个账户
2025-07-13 08:54:56.954 | DEBUG    | ui.views.account_view:_update_group_counts:186 - 分组计数已更新: {2: 1, 1: 1}
2025-07-13 08:54:56.964 | INFO     | app.services.account_service:batch_auto_login:1204 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-07-13 08:54:56.965 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-13 08:54:56.967 | INFO     | app.services.account_service:batch_auto_login:1274 - 服务层：准备调用核心层处理 2 个账户的自动登录。
2025-07-13 08:54:56.967 | INFO     | app.services.account_service:batch_auto_login:1284 - 服务层：设置核心层任务超时为 120 秒。
2025-07-13 08:54:56.967 | INFO     | core.telegram.client_manager:batch_auto_login:1216 - 开始批量自动登录, 共 2 个账户, 最大并发 5
2025-07-13 08:54:56.968 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:259 - 创建客户端: H:\PyProject\TeleTest\APPDATA\sessions\+***********, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-13 08:54:56.968 | DEBUG    | core.telegram.client_manager:get_proxy:96 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-13 08:54:56.968 | DEBUG    | core.telegram.client_manager:get_proxy:143 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-07-13 08:54:56.973 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:259 - 创建客户端: H:\PyProject\TeleTest\APPDATA\sessions\+***********, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-13 08:54:56.973 | DEBUG    | core.telegram.client_manager:get_proxy:96 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-13 08:54:56.973 | DEBUG    | core.telegram.client_manager:get_proxy:143 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-07-13 08:54:57.010 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-13 08:54:57.164 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-13 08:54:57.171 | WARNING  | core.auth.api_service:verify_vip:440 - 会员验证失败: 验证失败
2025-07-13 08:54:57.171 | ERROR    | utils.vip_checker:_check_vip_status:81 - VIP验证失败: 验证失败
2025-07-13 08:54:59.990 | INFO     | core.telegram.client_manager:_connect_client:207 - 连接成功!
2025-07-13 08:55:00.091 | INFO     | core.telegram.client_manager:_connect_client:207 - 连接成功!
2025-07-13 08:55:00.937 | INFO     | core.telegram.client_manager:_verify_client_authorization:1191 - 账户已授权: +***********
2025-07-13 08:55:01.062 | INFO     | core.telegram.client_manager:_verify_client_authorization:1191 - 账户已授权: +***********
2025-07-13 08:55:03.055 | INFO     | core.telegram.client_manager:batch_auto_login:1365 - 批量登录完成: 总计 2, 成功 2, 失败 0
2025-07-13 08:55:04.382 | ERROR    | data.repositories.message_repo:get_all_tasks:54 - 获取任务列表失败: Cannot switch to a different thread
	Current:  <greenlet.greenlet object at 0x000001EAD8863580 (otid=0x000001EAD93382D0) current active started main>
	Expected: <greenlet.greenlet object at 0x000001EAF68FC940 (otid=0x000001EAF68A3ED0) suspended active started main>
2025-07-13 08:55:04.383 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-13 08:55:04.383 | ERROR    | ui.views.send_msg_view:load_global_stats:987 - 加载全局统计数据失败: Method 'close()' can't be called here; method '_connection_for_bind()' is already in progress and this would cause an unexpected state change to <SessionTransactionState.CLOSED: 5> (Background on this error at: https://sqlalche.me/e/20/isce)
2025-07-13 08:55:13.576 | INFO     | ui.main_window:closeEvent:380 - MainWindow: 接收到关闭事件
2025-07-13 08:55:13.596 | INFO     | ui.main_window:_cleanup_before_quit:266 - MainWindow: 执行清理资源...
2025-07-13 08:55:13.596 | INFO     | core.telegram.client_worker:stop:108 - 正在停止Telegram客户端工作线程
2025-07-13 08:55:13.608 | INFO     | core.telegram.client_manager:cleanup_async:1136 - 开始清理资源并断开所有连接
2025-07-13 08:55:13.608 | INFO     | core.telegram.client_manager:disconnect_all_clients:721 - 正在断开所有客户端连接
2025-07-13 08:55:13.609 | INFO     | core.telegram.client_manager:_safe_disconnect:742 - 正在断开客户端连接: +***********
2025-07-13 08:55:13.609 | INFO     | core.telegram.client_manager:_safe_disconnect:742 - 正在断开客户端连接: +***********
2025-07-13 08:55:13.618 | INFO     | core.telegram.client_manager:_safe_disconnect:746 - 断开客户端连接成功: +***********
2025-07-13 08:55:13.618 | INFO     | core.telegram.client_manager:_safe_disconnect:746 - 断开客户端连接成功: +***********
2025-07-13 08:55:13.618 | INFO     | core.telegram.client_manager:cleanup_async:1155 - 资源清理完成
2025-07-13 08:55:14.113 | INFO     | core.telegram.client_manager:cleanup_async:1136 - 开始清理资源并断开所有连接
2025-07-13 08:55:14.113 | INFO     | core.telegram.client_manager:disconnect_all_clients:721 - 正在断开所有客户端连接
2025-07-13 08:55:14.113 | INFO     | core.telegram.client_manager:cleanup_async:1155 - 资源清理完成
2025-07-13 08:55:14.620 | INFO     | core.telegram.client_worker:run:97 - Telegram客户端工作线程已退出
2025-07-13 08:55:14.620 | INFO     | core.telegram.client_worker:stop:122 - Telegram客户端工作线程已停止
2025-07-13 08:55:14.620 | INFO     | ui.main_window:_cleanup_before_quit:275 - TelegramClientWorker 已停止。
2025-07-13 08:55:14.621 | INFO     | ui.main_window:_cleanup_before_quit:279 - MainWindow 清理完成
2025-07-13 08:55:14.639 | INFO     | __main__:main:111 - 应用程序已正常退出
2025-07-13 16:37:05.078 | INFO     | utils.logger:init_logger:98 - 日志系统初始化完成，默认级别: DEBUG，日志目录: H:\PyProject\TeleTest\APPDATA\logs
2025-07-13 16:37:06.378 | INFO     | data.database:init_database:31 - 初始化数据库连接: H:\PyProject\TeleTest\APPDATA\database\telegram_manager.db
2025-07-13 16:37:06.398 | DEBUG    | data.database:init_database:58 - 创建数据库表
2025-07-13 16:37:06.413 | INFO     | data.database:init_database:61 - 数据库初始化完成
2025-07-13 16:37:07.643 | DEBUG    | app.controllers.auth_controller:initialize_software:43 - 控制器: 初始化软件 app_id=1000, version=1.2.7
2025-07-13 16:37:07.643 | DEBUG    | core.auth.api_service:init_software:55 - 发送初始化请求: https://server.xile188.com/api/user/1000/windowns10/1.2.7/ini, params={}
2025-07-13 16:37:08.171 | DEBUG    | core.auth.api_service:init_software:66 - 初始化数据解密成功: {'new_content': '- 新增功能\n  - 1.新增图片上传功能\n  - 2.新增软件文档\n  - 3.新增软件自动更新，后续更新会自动检测版本更新，无需再下载更新包\n  - 4.新增设置账户信息及发信控制\n- 修复bug\n  - 删除监听任务时，不删除对应任务采集到的数据。\n  - 监听设置群无法发送到目标群\n  - 导出监听数据时应当导出单个任务而非所有数据\n- 优化记录\n  - 优化发信规则，直接调取符合要求的账户发送，无账户时才需要等待延时。\n  - 完善软件设置', 'new_url': None, 'notice': {'id': '2', 'content': '公告内容测试3', 'time': '**********'}}
2025-07-13 16:37:08.177 | INFO     | app.controllers.auth_controller:login:61 - 控制器: 用户登录 account=demo01
2025-07-13 16:37:11.277 | INFO     | core.auth.api_service:login:127 - 用户登录: account=demo01
2025-07-13 16:37:11.564 | INFO     | core.auth.api_service:get_user_info:347 - 获取用户信息
2025-07-13 16:37:11.838 | INFO     | core.auth.api_service:get_user_info:375 - 获取用户信息成功
2025-07-13 16:37:11.844 | INFO     | __main__:on_auth_completed:91 - 用户登录成功，正在启动主窗口...
2025-07-13 16:37:11.865 | INFO     | core.telegram.client_worker:__init__:61 - Telegram客户端工作线程初始化
2025-07-13 16:37:11.866 | INFO     | utils.vip_checker:initialize:25 - VIP检查器已初始化，等待登录后开始检查
2025-07-13 16:37:11.866 | INFO     | ui.main_window:_initialize_core_components:108 - MainWindow: 初始化核心组件...
2025-07-13 16:37:11.866 | INFO     | app.services.account_service:__init__:42 - 账户服务初始化
2025-07-13 16:37:11.867 | INFO     | app.controllers.account_controller:__init__:81 - 账户控制器初始化
2025-07-13 16:37:11.867 | INFO     | core.telegram.client_worker:run:65 - Telegram客户端工作线程开始运行
2025-07-13 16:37:11.867 | INFO     | app.services.monitor_service:__init__:39 - 初始化监控任务服务
2025-07-13 16:37:11.867 | INFO     | core.proxy.proxy_task_manager:run:284 - 代理任务管理器线程启动
2025-07-13 16:37:11.868 | INFO     | core.monitor.filter:__init__:26 - 初始化消息过滤器
2025-07-13 16:37:11.868 | INFO     | core.monitor.extractor:__init__:24 - 初始化关键词提取器
2025-07-13 16:37:11.868 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-07-13 16:37:11.868 | INFO     | core.monitor.notifier:__init__:90 - 初始化通知管理器
2025-07-13 16:37:11.869 | INFO     | app.controllers.telegram_monitor_controller:__init__:64 - 初始化 Telegram 监控控制器
2025-07-13 16:37:11.869 | DEBUG    | app.controllers.telegram_monitor_controller:__init__:82 - 控制器初始化完成
2025-07-13 16:37:11.869 | INFO     | core.telegram.client_manager:__init__:66 - Telegram客户端管理器初始化
2025-07-13 16:37:11.872 | INFO     | core.telegram.user_manager:__init__:30 - Telegram用户管理器初始化
2025-07-13 16:37:11.872 | INFO     | core.telegram.group_manager:__init__:36 - Telegram群组管理器初始化
2025-07-13 16:37:11.873 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-07-13 16:37:11.875 | INFO     | core.telegram.monitor_manager:__init__:26 - Telegram监控管理器初始化
2025-07-13 16:37:11.875 | INFO     | core.telegram.telegram_service:__init__:39 - Telegram服务初始化完成
2025-07-13 16:37:12.069 | INFO     | ui.views.monitor_view:__init__:48 - 初始化监控视图
2025-07-13 16:37:12.069 | DEBUG    | ui.views.monitor_view:_connect_signals:80 - 连接控制器信号
2025-07-13 16:37:12.274 | INFO     | ui.views.proxy_view:__init__:96 - 初始化视图，加载第一页代理数据
2025-07-13 16:37:12.632 | INFO     | __main__:on_auth_completed:95 - 主窗口已启动
2025-07-13 16:37:12.634 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:88 - 尝试连接监听日志信号...
2025-07-13 16:37:12.635 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:90 - 监听日志信号连接成功
2025-07-13 16:37:12.635 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-13 16:37:12.641 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-13 16:37:12.645 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:145 - 加载监控任务列表 (搜索关键词: '')
2025-07-13 16:37:12.645 | DEBUG    | ui.views.monitor_view:_on_loading_started:620 - 加载中: 正在加载任务列表...
2025-07-13 16:37:12.645 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-13 16:37:12.653 | INFO     | app.services.proxy_service:refresh_proxies:712 - 刷新代理列表: offset=0, limit=20
2025-07-13 16:37:12.653 | INFO     | core.proxy.proxy_task_manager:submit_task:172 - 提交耗时任务到子线程: refresh_proxies_0_20
2025-07-13 16:37:12.653 | DEBUG    | app.services.proxy_service:get_proxies:500 - 获取代理列表: offset=0, limit=20
2025-07-13 16:37:12.653 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-13 16:37:12.653 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-13 16:37:12.657 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-13 16:37:12.683 | INFO     | app.controllers.invite_controller:get_invite_tasks:38 - 获取邀请任务列表, status=None
2025-07-13 16:37:12.684 | INFO     | app.services.invite_service:get_invite_tasks:121 - 获取邀请任务列表, status=None
2025-07-13 16:37:12.684 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-13 16:37:12.686 | INFO     | app.services.invite_service:task_initialize:39 - 初始化邀请服务，重置运行中的任务为暂停状态
2025-07-13 16:37:12.687 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-13 16:37:12.687 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-13 16:37:12.688 | INFO     | core.proxy.proxy_task_manager:task_wrapper:199 - 任务 refresh_proxies_0_20 完成
2025-07-13 16:37:12.690 | INFO     | utils.vip_checker:start_checking:37 - 开始VIP状态检查
2025-07-13 16:37:12.691 | DEBUG    | utils.vip_checker:_check_vip_status:61 - 正在检查VIP状态...
2025-07-13 16:37:12.691 | INFO     | core.auth.api_service:verify_vip:425 - 会员验证接口调用
2025-07-13 16:37:12.819 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-13 16:37:12.822 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-13 16:37:12.826 | INFO     | app.services.invite_service:task_initialize:48 - 成功重置 0 个任务状态
2025-07-13 16:37:12.826 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-13 16:37:12.827 | DEBUG    | ui.views.monitor_view:_refresh_task_list:175 - 刷新任务列表，共0个任务
2025-07-13 16:37:12.828 | INFO     | ui.views.monitor_view:_refresh_task_list:189 - 没有找到监控任务
2025-07-13 16:37:12.828 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-13 16:37:12.835 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-13 16:37:12.906 | INFO     | app.services.account_service:get_all_groups:96 - 获取所有账户分组成功, 共 2 个
2025-07-13 16:37:12.906 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-13 16:37:12.915 | DEBUG    | ui.views.account_view:_on_groups_loaded:515 - 分组加载完成: 2个分组
2025-07-13 16:37:12.934 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-13 16:37:12.938 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-13 16:37:12.940 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-13 16:37:12.943 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-13 16:37:12.946 | INFO     | app.controllers.telegram_monitor_controller:_load_total_users_count_internal:191 - 成功加载总体统计数据: 总用户数 23, 今日采集 0, 日均采集 23, 运行天数 1
2025-07-13 16:37:12.947 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:156 - 成功加载 0 个任务
2025-07-13 16:37:12.947 | DEBUG    | ui.views.monitor_view:_on_loading_finished:625 - 加载完成
2025-07-13 16:37:12.949 | INFO     | app.services.account_service:get_all_accounts:290 - 获取所有账户成功, 共 2 个
2025-07-13 16:37:12.949 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-13 16:37:12.953 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-13 16:37:12.954 | DEBUG    | ui.views.account_view:_on_accounts_loaded:648 - 账户加载完成: 2个账户
2025-07-13 16:37:12.981 | DEBUG    | ui.views.account_view:_update_group_counts:186 - 分组计数已更新: {2: 1, 1: 1}
2025-07-13 16:37:12.987 | INFO     | ui.views.account_view:_auto_login_accounts:703 - 开始调用控制器进行 2 个账户的自动登录。
2025-07-13 16:37:12.987 | INFO     | app.services.account_service:batch_auto_login:1165 - 服务层：开始批量自动登录 2 个账户, 最大并发: 5
2025-07-13 16:37:12.987 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-13 16:37:13.229 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-13 16:37:13.230 | INFO     | app.services.account_service:batch_auto_login:1204 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-07-13 16:37:13.230 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-13 16:37:13.230 | INFO     | app.services.account_service:get_all_accounts:290 - 获取所有账户成功, 共 2 个
2025-07-13 16:37:13.231 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-13 16:37:13.233 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-07-13 16:37:13.244 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-13 16:37:13.246 | DEBUG    | ui.views.account_view:_update_group_counts:186 - 分组计数已更新: {2: 1, 1: 1}
2025-07-13 16:37:13.246 | DEBUG    | ui.views.account_view:_init_cache:153 - 账户缓存初始化完成，共 2 个账户
2025-07-13 16:37:13.255 | DEBUG    | ui.views.account_view:_on_accounts_loaded:648 - 账户加载完成: 2个账户
2025-07-13 16:37:13.277 | DEBUG    | ui.views.account_view:_update_group_counts:186 - 分组计数已更新: {2: 1, 1: 1}
2025-07-13 16:37:13.291 | WARNING  | core.auth.api_service:verify_vip:440 - 会员验证失败: 验证失败
2025-07-13 16:37:13.292 | ERROR    | utils.vip_checker:_check_vip_status:81 - VIP验证失败: 验证失败
2025-07-13 16:37:16.875 | ERROR    | data.repositories.message_repo:get_task_target_stats:187 - 获取任务6目标统计失败: coroutine ignored GeneratorExit
2025-07-13 16:37:16.875 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-13 16:37:16.875 | ERROR    | ui.views.send_msg_view:load_tasks:50 - 加载任务列表失败: (sqlite3.OperationalError) no active connection
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-13 16:37:16.880 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-13 16:38:11.844 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 16:39:11.844 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 16:40:11.834 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 16:40:14.657 | INFO     | utils.logger:init_logger:98 - 日志系统初始化完成，默认级别: DEBUG，日志目录: H:\PyProject\TeleTest\APPDATA\logs
2025-07-13 16:40:15.778 | INFO     | data.database:init_database:31 - 初始化数据库连接: H:\PyProject\TeleTest\APPDATA\database\telegram_manager.db
2025-07-13 16:40:15.792 | DEBUG    | data.database:init_database:58 - 创建数据库表
2025-07-13 16:40:15.803 | INFO     | data.database:init_database:61 - 数据库初始化完成
2025-07-13 16:40:16.571 | DEBUG    | app.controllers.auth_controller:initialize_software:43 - 控制器: 初始化软件 app_id=1000, version=1.2.7
2025-07-13 16:40:16.571 | DEBUG    | core.auth.api_service:init_software:55 - 发送初始化请求: https://server.xile188.com/api/user/1000/windowns10/1.2.7/ini, params={}
2025-07-13 16:40:16.845 | DEBUG    | core.auth.api_service:init_software:66 - 初始化数据解密成功: {'new_content': '- 新增功能\n  - 1.新增图片上传功能\n  - 2.新增软件文档\n  - 3.新增软件自动更新，后续更新会自动检测版本更新，无需再下载更新包\n  - 4.新增设置账户信息及发信控制\n- 修复bug\n  - 删除监听任务时，不删除对应任务采集到的数据。\n  - 监听设置群无法发送到目标群\n  - 导出监听数据时应当导出单个任务而非所有数据\n- 优化记录\n  - 优化发信规则，直接调取符合要求的账户发送，无账户时才需要等待延时。\n  - 完善软件设置', 'new_url': None, 'notice': {'id': '2', 'content': '公告内容测试3', 'time': '**********'}}
2025-07-13 16:40:16.852 | INFO     | app.controllers.auth_controller:login:61 - 控制器: 用户登录 account=demo01
2025-07-13 16:40:19.816 | INFO     | core.auth.api_service:login:127 - 用户登录: account=demo01
2025-07-13 16:40:20.133 | INFO     | core.auth.api_service:get_user_info:347 - 获取用户信息
2025-07-13 16:40:20.337 | INFO     | core.auth.api_service:get_user_info:375 - 获取用户信息成功
2025-07-13 16:40:20.343 | INFO     | __main__:on_auth_completed:91 - 用户登录成功，正在启动主窗口...
2025-07-13 16:40:20.371 | INFO     | core.telegram.client_worker:__init__:61 - Telegram客户端工作线程初始化
2025-07-13 16:40:20.371 | INFO     | utils.vip_checker:initialize:25 - VIP检查器已初始化，等待登录后开始检查
2025-07-13 16:40:20.372 | INFO     | ui.main_window:_initialize_core_components:108 - MainWindow: 初始化核心组件...
2025-07-13 16:40:20.372 | INFO     | app.services.account_service:__init__:42 - 账户服务初始化
2025-07-13 16:40:20.373 | INFO     | app.controllers.account_controller:__init__:81 - 账户控制器初始化
2025-07-13 16:40:20.373 | INFO     | core.telegram.client_worker:run:65 - Telegram客户端工作线程开始运行
2025-07-13 16:40:20.373 | INFO     | core.proxy.proxy_task_manager:run:284 - 代理任务管理器线程启动
2025-07-13 16:40:20.373 | INFO     | app.services.monitor_service:__init__:39 - 初始化监控任务服务
2025-07-13 16:40:20.373 | INFO     | core.monitor.filter:__init__:26 - 初始化消息过滤器
2025-07-13 16:40:20.373 | INFO     | core.monitor.extractor:__init__:24 - 初始化关键词提取器
2025-07-13 16:40:20.374 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-07-13 16:40:20.374 | INFO     | core.monitor.notifier:__init__:90 - 初始化通知管理器
2025-07-13 16:40:20.374 | INFO     | app.controllers.telegram_monitor_controller:__init__:64 - 初始化 Telegram 监控控制器
2025-07-13 16:40:20.374 | INFO     | core.telegram.client_manager:__init__:66 - Telegram客户端管理器初始化
2025-07-13 16:40:20.375 | DEBUG    | app.controllers.telegram_monitor_controller:__init__:82 - 控制器初始化完成
2025-07-13 16:40:20.375 | INFO     | core.telegram.user_manager:__init__:30 - Telegram用户管理器初始化
2025-07-13 16:40:20.376 | INFO     | core.telegram.group_manager:__init__:36 - Telegram群组管理器初始化
2025-07-13 16:40:20.377 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-07-13 16:40:20.378 | INFO     | core.telegram.monitor_manager:__init__:26 - Telegram监控管理器初始化
2025-07-13 16:40:20.378 | INFO     | core.telegram.telegram_service:__init__:39 - Telegram服务初始化完成
2025-07-13 16:40:20.616 | INFO     | ui.views.monitor_view:__init__:48 - 初始化监控视图
2025-07-13 16:40:20.616 | DEBUG    | ui.views.monitor_view:_connect_signals:80 - 连接控制器信号
2025-07-13 16:40:20.789 | INFO     | ui.views.proxy_view:__init__:96 - 初始化视图，加载第一页代理数据
2025-07-13 16:40:21.029 | INFO     | __main__:on_auth_completed:95 - 主窗口已启动
2025-07-13 16:40:21.071 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:88 - 尝试连接监听日志信号...
2025-07-13 16:40:21.071 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:90 - 监听日志信号连接成功
2025-07-13 16:40:21.071 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-13 16:40:21.074 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-13 16:40:21.078 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:145 - 加载监控任务列表 (搜索关键词: '')
2025-07-13 16:40:21.078 | DEBUG    | ui.views.monitor_view:_on_loading_started:620 - 加载中: 正在加载任务列表...
2025-07-13 16:40:21.078 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-13 16:40:21.084 | INFO     | app.services.proxy_service:refresh_proxies:712 - 刷新代理列表: offset=0, limit=20
2025-07-13 16:40:21.084 | INFO     | core.proxy.proxy_task_manager:submit_task:172 - 提交耗时任务到子线程: refresh_proxies_0_20
2025-07-13 16:40:21.084 | DEBUG    | app.services.proxy_service:get_proxies:500 - 获取代理列表: offset=0, limit=20
2025-07-13 16:40:21.085 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-13 16:40:21.085 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-13 16:40:21.087 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-13 16:40:21.102 | INFO     | app.controllers.invite_controller:get_invite_tasks:38 - 获取邀请任务列表, status=None
2025-07-13 16:40:21.108 | INFO     | app.services.invite_service:get_invite_tasks:121 - 获取邀请任务列表, status=None
2025-07-13 16:40:21.111 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-13 16:40:21.112 | INFO     | app.services.invite_service:task_initialize:39 - 初始化邀请服务，重置运行中的任务为暂停状态
2025-07-13 16:40:21.112 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-13 16:40:21.116 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-13 16:40:21.117 | INFO     | utils.vip_checker:start_checking:37 - 开始VIP状态检查
2025-07-13 16:40:21.117 | DEBUG    | utils.vip_checker:_check_vip_status:61 - 正在检查VIP状态...
2025-07-13 16:40:21.117 | INFO     | core.auth.api_service:verify_vip:425 - 会员验证接口调用
2025-07-13 16:40:21.119 | INFO     | core.proxy.proxy_task_manager:task_wrapper:199 - 任务 refresh_proxies_0_20 完成
2025-07-13 16:40:21.314 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-13 16:40:21.322 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-13 16:40:21.322 | INFO     | app.services.invite_service:task_initialize:48 - 成功重置 0 个任务状态
2025-07-13 16:40:21.322 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-13 16:40:21.324 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-13 16:40:21.325 | DEBUG    | ui.views.monitor_view:_refresh_task_list:175 - 刷新任务列表，共0个任务
2025-07-13 16:40:21.325 | INFO     | ui.views.monitor_view:_refresh_task_list:189 - 没有找到监控任务
2025-07-13 16:40:21.325 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-13 16:40:21.327 | INFO     | app.services.account_service:get_all_groups:96 - 获取所有账户分组成功, 共 2 个
2025-07-13 16:40:21.327 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-13 16:40:21.328 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-13 16:40:21.330 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-13 16:40:21.331 | DEBUG    | ui.views.account_view:_on_groups_loaded:515 - 分组加载完成: 2个分组
2025-07-13 16:40:21.342 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-13 16:40:21.453 | INFO     | app.services.account_service:get_all_accounts:290 - 获取所有账户成功, 共 2 个
2025-07-13 16:40:21.453 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-13 16:40:21.454 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-13 16:40:21.455 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-13 16:40:21.456 | DEBUG    | ui.views.account_view:_on_accounts_loaded:648 - 账户加载完成: 2个账户
2025-07-13 16:40:21.474 | DEBUG    | ui.views.account_view:_update_group_counts:186 - 分组计数已更新: {2: 1, 1: 1}
2025-07-13 16:40:21.478 | INFO     | ui.views.account_view:_auto_login_accounts:703 - 开始调用控制器进行 2 个账户的自动登录。
2025-07-13 16:40:21.478 | INFO     | app.services.account_service:batch_auto_login:1165 - 服务层：开始批量自动登录 2 个账户, 最大并发: 5
2025-07-13 16:40:21.478 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-13 16:40:21.482 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-13 16:40:21.483 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-07-13 16:40:21.496 | INFO     | app.controllers.telegram_monitor_controller:_load_total_users_count_internal:191 - 成功加载总体统计数据: 总用户数 23, 今日采集 0, 日均采集 23, 运行天数 1
2025-07-13 16:40:21.496 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:156 - 成功加载 0 个任务
2025-07-13 16:40:21.496 | DEBUG    | ui.views.monitor_view:_on_loading_finished:625 - 加载完成
2025-07-13 16:40:21.498 | INFO     | app.services.account_service:batch_auto_login:1204 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-07-13 16:40:21.498 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-13 16:40:21.498 | INFO     | app.services.account_service:get_all_accounts:290 - 获取所有账户成功, 共 2 个
2025-07-13 16:40:21.498 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-13 16:40:21.500 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-13 16:40:21.501 | DEBUG    | ui.views.account_view:_update_group_counts:186 - 分组计数已更新: {2: 1, 1: 1}
2025-07-13 16:40:21.501 | DEBUG    | ui.views.account_view:_init_cache:153 - 账户缓存初始化完成，共 2 个账户
2025-07-13 16:40:21.505 | DEBUG    | ui.views.account_view:_on_accounts_loaded:648 - 账户加载完成: 2个账户
2025-07-13 16:40:21.525 | DEBUG    | ui.views.account_view:_update_group_counts:186 - 分组计数已更新: {2: 1, 1: 1}
2025-07-13 16:40:21.541 | INFO     | app.services.account_service:batch_auto_login:1204 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-07-13 16:40:21.543 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-13 16:40:21.543 | INFO     | app.services.account_service:batch_auto_login:1274 - 服务层：准备调用核心层处理 2 个账户的自动登录。
2025-07-13 16:40:21.543 | INFO     | app.services.account_service:batch_auto_login:1284 - 服务层：设置核心层任务超时为 120 秒。
2025-07-13 16:40:21.543 | INFO     | core.telegram.client_manager:batch_auto_login:1216 - 开始批量自动登录, 共 2 个账户, 最大并发 5
2025-07-13 16:40:21.544 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:259 - 创建客户端: H:\PyProject\TeleTest\APPDATA\sessions\+***********, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-13 16:40:21.544 | DEBUG    | core.telegram.client_manager:get_proxy:96 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-13 16:40:21.545 | DEBUG    | core.telegram.client_manager:get_proxy:143 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-07-13 16:40:21.549 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:259 - 创建客户端: H:\PyProject\TeleTest\APPDATA\sessions\+***********, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-13 16:40:21.550 | DEBUG    | core.telegram.client_manager:get_proxy:96 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-13 16:40:21.550 | DEBUG    | core.telegram.client_manager:get_proxy:143 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-07-13 16:40:21.551 | INFO     | core.auth.api_service:verify_vip:438 - 会员验证成功
2025-07-13 16:40:21.551 | INFO     | utils.vip_checker:_check_vip_status:76 - VIP状态有效
2025-07-13 16:40:21.574 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-13 16:40:21.644 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-13 16:40:21.685 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-13 16:40:21.702 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-07-13 16:40:21.704 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-07-13 16:40:21.706 | INFO     | ui.views.proxy_view:_load_initial_data:1044 - 初始数据加载完成，共 1 个代理
2025-07-13 16:40:25.481 | INFO     | core.telegram.client_manager:_connect_client:207 - 连接成功!
2025-07-13 16:40:26.720 | INFO     | core.telegram.client_manager:_verify_client_authorization:1191 - 账户已授权: +***********
2025-07-13 16:40:27.543 | INFO     | core.telegram.client_manager:_connect_client:207 - 连接成功!
2025-07-13 16:40:28.377 | INFO     | core.telegram.client_manager:_verify_client_authorization:1191 - 账户已授权: +***********
2025-07-13 16:40:30.380 | INFO     | core.telegram.client_manager:batch_auto_login:1365 - 批量登录完成: 总计 2, 成功 2, 失败 0
2025-07-13 16:40:30.599 | INFO     | app.services.account_service:batch_auto_login:1305 - 服务层：核心层登录完成: 成功 2, 失败 0 / 2
2025-07-13 16:40:35.644 | INFO     | app.services.account_service:refresh_account_info:607 - 刷新账户信息: 2
2025-07-13 16:40:35.644 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-13 16:40:35.654 | INFO     | core.telegram.user_manager:get_user_info:41 - 正在获取用户信息: +***********
2025-07-13 16:40:36.670 | INFO     | core.telegram.user_manager:get_user_info:66 - 获取用户信息成功: +***********
2025-07-13 16:40:36.899 | INFO     | data.repositories.account_repo:update_account:378 - 更新账户成功: 2
2025-07-13 16:40:36.908 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-13 16:41:20.334 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 16:42:20.336 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 16:43:20.333 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 16:44:20.349 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 16:45:20.342 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 16:46:20.338 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 16:47:20.335 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 16:48:20.340 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 16:49:20.346 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 16:50:20.341 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 16:51:20.340 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 16:52:20.337 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 16:52:25.443 | INFO     | ui.main_window:closeEvent:380 - MainWindow: 接收到关闭事件
2025-07-13 16:52:25.460 | INFO     | ui.main_window:_cleanup_before_quit:266 - MainWindow: 执行清理资源...
2025-07-13 16:52:25.460 | INFO     | core.telegram.client_worker:stop:108 - 正在停止Telegram客户端工作线程
2025-07-13 16:52:25.467 | INFO     | core.telegram.client_manager:cleanup_async:1136 - 开始清理资源并断开所有连接
2025-07-13 16:52:25.468 | INFO     | core.telegram.client_manager:disconnect_all_clients:721 - 正在断开所有客户端连接
2025-07-13 16:52:25.468 | INFO     | core.telegram.client_manager:_safe_disconnect:742 - 正在断开客户端连接: +***********
2025-07-13 16:52:25.468 | INFO     | core.telegram.client_manager:_safe_disconnect:742 - 正在断开客户端连接: +***********
2025-07-13 16:52:25.480 | INFO     | core.telegram.client_manager:_safe_disconnect:746 - 断开客户端连接成功: +***********
2025-07-13 16:52:25.480 | INFO     | core.telegram.client_manager:_safe_disconnect:746 - 断开客户端连接成功: +***********
2025-07-13 16:52:25.481 | INFO     | core.telegram.client_manager:cleanup_async:1155 - 资源清理完成
2025-07-13 16:52:25.974 | INFO     | core.telegram.client_manager:cleanup_async:1136 - 开始清理资源并断开所有连接
2025-07-13 16:52:25.974 | INFO     | core.telegram.client_manager:disconnect_all_clients:721 - 正在断开所有客户端连接
2025-07-13 16:52:25.974 | INFO     | core.telegram.client_manager:cleanup_async:1155 - 资源清理完成
2025-07-13 16:52:26.478 | INFO     | core.telegram.client_worker:run:97 - Telegram客户端工作线程已退出
2025-07-13 16:52:26.479 | INFO     | core.telegram.client_worker:stop:122 - Telegram客户端工作线程已停止
2025-07-13 16:52:26.479 | INFO     | ui.main_window:_cleanup_before_quit:275 - TelegramClientWorker 已停止。
2025-07-13 16:52:26.479 | INFO     | ui.main_window:_cleanup_before_quit:279 - MainWindow 清理完成
2025-07-13 16:52:26.496 | INFO     | __main__:main:109 - 应用程序已正常退出
2025-07-13 17:02:57.146 | INFO     | utils.logger:init_logger:98 - 日志系统初始化完成，默认级别: DEBUG，日志目录: H:\PyProject\TeleTest\APPDATA\logs
2025-07-13 17:02:59.935 | INFO     | data.database:init_database:31 - 初始化数据库连接: H:\PyProject\TeleTest\APPDATA\database\telegram_manager.db
2025-07-13 17:02:59.966 | DEBUG    | data.database:init_database:58 - 创建数据库表
2025-07-13 17:02:59.983 | INFO     | data.database:init_database:61 - 数据库初始化完成
2025-07-13 17:03:00.755 | DEBUG    | app.controllers.auth_controller:initialize_software:43 - 控制器: 初始化软件 app_id=1000, version=1.2.7
2025-07-13 17:03:00.756 | DEBUG    | core.auth.api_service:init_software:55 - 发送初始化请求: https://server.xile188.com/api/user/1000/windowns10/1.2.7/ini, params={}
2025-07-13 17:03:01.408 | DEBUG    | core.auth.api_service:init_software:66 - 初始化数据解密成功: {'new_content': '- 新增功能\n  - 1.新增图片上传功能\n  - 2.新增软件文档\n  - 3.新增软件自动更新，后续更新会自动检测版本更新，无需再下载更新包\n  - 4.新增设置账户信息及发信控制\n- 修复bug\n  - 删除监听任务时，不删除对应任务采集到的数据。\n  - 监听设置群无法发送到目标群\n  - 导出监听数据时应当导出单个任务而非所有数据\n- 优化记录\n  - 优化发信规则，直接调取符合要求的账户发送，无账户时才需要等待延时。\n  - 完善软件设置', 'new_url': None, 'notice': {'id': '2', 'content': '公告内容测试3', 'time': '**********'}}
2025-07-13 17:03:01.419 | INFO     | app.controllers.auth_controller:login:61 - 控制器: 用户登录 account=demo01
2025-07-13 17:03:04.430 | INFO     | core.auth.api_service:login:127 - 用户登录: account=demo01
2025-07-13 17:03:04.697 | INFO     | core.auth.api_service:get_user_info:347 - 获取用户信息
2025-07-13 17:03:04.903 | INFO     | core.auth.api_service:get_user_info:375 - 获取用户信息成功
2025-07-13 17:03:04.910 | INFO     | __main__:on_auth_completed:91 - 用户登录成功，正在启动主窗口...
2025-07-13 17:03:04.938 | INFO     | core.telegram.client_worker:__init__:61 - Telegram客户端工作线程初始化
2025-07-13 17:03:04.939 | INFO     | utils.vip_checker:initialize:25 - VIP检查器已初始化，等待登录后开始检查
2025-07-13 17:03:04.940 | INFO     | ui.main_window:_initialize_core_components:108 - MainWindow: 初始化核心组件...
2025-07-13 17:03:04.940 | INFO     | core.telegram.client_worker:run:65 - Telegram客户端工作线程开始运行
2025-07-13 17:03:04.941 | INFO     | app.services.account_service:__init__:42 - 账户服务初始化
2025-07-13 17:03:04.941 | INFO     | core.proxy.proxy_task_manager:run:284 - 代理任务管理器线程启动
2025-07-13 17:03:04.942 | INFO     | app.controllers.account_controller:__init__:81 - 账户控制器初始化
2025-07-13 17:03:04.942 | INFO     | app.services.monitor_service:__init__:39 - 初始化监控任务服务
2025-07-13 17:03:04.942 | INFO     | core.telegram.client_manager:__init__:66 - Telegram客户端管理器初始化
2025-07-13 17:03:04.943 | INFO     | core.monitor.filter:__init__:26 - 初始化消息过滤器
2025-07-13 17:03:04.943 | INFO     | core.telegram.user_manager:__init__:30 - Telegram用户管理器初始化
2025-07-13 17:03:04.943 | INFO     | core.monitor.extractor:__init__:24 - 初始化关键词提取器
2025-07-13 17:03:04.944 | INFO     | core.telegram.group_manager:__init__:36 - Telegram群组管理器初始化
2025-07-13 17:03:04.944 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-07-13 17:03:04.944 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-07-13 17:03:04.945 | INFO     | core.monitor.notifier:__init__:90 - 初始化通知管理器
2025-07-13 17:03:04.945 | INFO     | core.telegram.monitor_manager:__init__:26 - Telegram监控管理器初始化
2025-07-13 17:03:04.946 | INFO     | app.controllers.telegram_monitor_controller:__init__:64 - 初始化 Telegram 监控控制器
2025-07-13 17:03:04.946 | INFO     | core.telegram.telegram_service:__init__:39 - Telegram服务初始化完成
2025-07-13 17:03:04.946 | DEBUG    | app.controllers.telegram_monitor_controller:__init__:82 - 控制器初始化完成
2025-07-13 17:03:05.156 | INFO     | ui.views.monitor_view:__init__:48 - 初始化监控视图
2025-07-13 17:03:05.156 | DEBUG    | ui.views.monitor_view:_connect_signals:80 - 连接控制器信号
2025-07-13 17:03:05.335 | INFO     | ui.views.proxy_view:__init__:96 - 初始化视图，加载第一页代理数据
2025-07-13 17:03:05.575 | INFO     | __main__:on_auth_completed:95 - 主窗口已启动
2025-07-13 17:03:05.621 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:88 - 尝试连接监听日志信号...
2025-07-13 17:03:05.622 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:90 - 监听日志信号连接成功
2025-07-13 17:03:05.623 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-13 17:03:05.627 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-13 17:03:05.631 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:145 - 加载监控任务列表 (搜索关键词: '')
2025-07-13 17:03:05.631 | DEBUG    | ui.views.monitor_view:_on_loading_started:620 - 加载中: 正在加载任务列表...
2025-07-13 17:03:05.632 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-13 17:03:05.640 | INFO     | app.services.proxy_service:refresh_proxies:712 - 刷新代理列表: offset=0, limit=20
2025-07-13 17:03:05.641 | INFO     | core.proxy.proxy_task_manager:submit_task:172 - 提交耗时任务到子线程: refresh_proxies_0_20
2025-07-13 17:03:05.641 | DEBUG    | app.services.proxy_service:get_proxies:500 - 获取代理列表: offset=0, limit=20
2025-07-13 17:03:05.641 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-13 17:03:05.641 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-13 17:03:05.645 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-13 17:03:05.678 | INFO     | app.controllers.invite_controller:get_invite_tasks:38 - 获取邀请任务列表, status=None
2025-07-13 17:03:05.678 | INFO     | app.services.invite_service:get_invite_tasks:121 - 获取邀请任务列表, status=None
2025-07-13 17:03:05.680 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-13 17:03:05.681 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-13 17:03:05.681 | INFO     | app.services.invite_service:task_initialize:39 - 初始化邀请服务，重置运行中的任务为暂停状态
2025-07-13 17:03:05.681 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-13 17:03:05.682 | INFO     | core.proxy.proxy_task_manager:task_wrapper:199 - 任务 refresh_proxies_0_20 完成
2025-07-13 17:03:05.684 | INFO     | utils.vip_checker:start_checking:37 - 开始VIP状态检查
2025-07-13 17:03:05.685 | DEBUG    | utils.vip_checker:_check_vip_status:61 - 正在检查VIP状态...
2025-07-13 17:03:05.685 | INFO     | core.auth.api_service:verify_vip:425 - 会员验证接口调用
2025-07-13 17:03:05.907 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-13 17:03:05.907 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-13 17:03:05.917 | DEBUG    | ui.views.monitor_view:_refresh_task_list:175 - 刷新任务列表，共0个任务
2025-07-13 17:03:05.918 | INFO     | ui.views.monitor_view:_refresh_task_list:189 - 没有找到监控任务
2025-07-13 17:03:05.918 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-13 17:03:05.920 | INFO     | app.services.invite_service:task_initialize:48 - 成功重置 0 个任务状态
2025-07-13 17:03:05.921 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-13 17:03:05.923 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-13 17:03:05.933 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-13 17:03:05.936 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-13 17:03:05.938 | INFO     | app.services.account_service:get_all_groups:96 - 获取所有账户分组成功, 共 2 个
2025-07-13 17:03:05.938 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-13 17:03:06.007 | DEBUG    | ui.views.account_view:_on_groups_loaded:515 - 分组加载完成: 2个分组
2025-07-13 17:03:06.022 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-13 17:03:06.035 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-13 17:03:06.037 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-13 17:03:06.041 | INFO     | app.services.account_service:get_all_accounts:290 - 获取所有账户成功, 共 2 个
2025-07-13 17:03:06.041 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-13 17:03:06.043 | INFO     | app.controllers.telegram_monitor_controller:_load_total_users_count_internal:191 - 成功加载总体统计数据: 总用户数 23, 今日采集 0, 日均采集 23, 运行天数 1
2025-07-13 17:03:06.044 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:156 - 成功加载 0 个任务
2025-07-13 17:03:06.045 | DEBUG    | ui.views.monitor_view:_on_loading_finished:625 - 加载完成
2025-07-13 17:03:06.046 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-07-13 17:03:06.060 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-13 17:03:06.062 | DEBUG    | ui.views.account_view:_on_accounts_loaded:648 - 账户加载完成: 2个账户
2025-07-13 17:03:06.086 | DEBUG    | ui.views.account_view:_update_group_counts:186 - 分组计数已更新: {2: 1, 1: 1}
2025-07-13 17:03:06.092 | INFO     | ui.views.account_view:_auto_login_accounts:703 - 开始调用控制器进行 2 个账户的自动登录。
2025-07-13 17:03:06.093 | INFO     | app.services.account_service:batch_auto_login:1165 - 服务层：开始批量自动登录 2 个账户, 最大并发: 5
2025-07-13 17:03:06.093 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-13 17:03:06.114 | INFO     | app.services.account_service:batch_auto_login:1204 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-07-13 17:03:06.115 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-13 17:03:06.116 | INFO     | app.services.account_service:get_all_accounts:290 - 获取所有账户成功, 共 2 个
2025-07-13 17:03:06.116 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-13 17:03:06.119 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-13 17:03:06.121 | DEBUG    | ui.views.account_view:_update_group_counts:186 - 分组计数已更新: {2: 1, 1: 1}
2025-07-13 17:03:06.122 | DEBUG    | ui.views.account_view:_init_cache:153 - 账户缓存初始化完成，共 2 个账户
2025-07-13 17:03:06.128 | DEBUG    | ui.views.account_view:_on_accounts_loaded:648 - 账户加载完成: 2个账户
2025-07-13 17:03:06.156 | DEBUG    | ui.views.account_view:_update_group_counts:186 - 分组计数已更新: {2: 1, 1: 1}
2025-07-13 17:03:06.173 | INFO     | core.auth.api_service:verify_vip:438 - 会员验证成功
2025-07-13 17:03:06.173 | INFO     | utils.vip_checker:_check_vip_status:76 - VIP状态有效
2025-07-13 17:03:06.179 | INFO     | app.services.account_service:batch_auto_login:1204 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-07-13 17:03:06.179 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-13 17:03:06.182 | INFO     | app.services.account_service:batch_auto_login:1274 - 服务层：准备调用核心层处理 2 个账户的自动登录。
2025-07-13 17:03:06.182 | INFO     | app.services.account_service:batch_auto_login:1284 - 服务层：设置核心层任务超时为 120 秒。
2025-07-13 17:03:06.182 | INFO     | core.telegram.client_manager:batch_auto_login:1216 - 开始批量自动登录, 共 2 个账户, 最大并发 5
2025-07-13 17:03:06.183 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:259 - 创建客户端: H:\PyProject\TeleTest\APPDATA\sessions\+***********, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-13 17:03:06.184 | DEBUG    | core.telegram.client_manager:get_proxy:96 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-13 17:03:06.184 | DEBUG    | core.telegram.client_manager:get_proxy:143 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-07-13 17:03:06.189 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:259 - 创建客户端: H:\PyProject\TeleTest\APPDATA\sessions\+***********, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-13 17:03:06.189 | DEBUG    | core.telegram.client_manager:get_proxy:96 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-13 17:03:06.190 | DEBUG    | core.telegram.client_manager:get_proxy:143 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-07-13 17:03:06.254 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-13 17:03:06.340 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-13 17:03:06.414 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-07-13 17:03:06.416 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-07-13 17:03:06.418 | INFO     | ui.views.proxy_view:_load_initial_data:1044 - 初始数据加载完成，共 1 个代理
2025-07-13 17:03:06.470 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-13 17:03:09.676 | INFO     | core.telegram.client_manager:_connect_client:207 - 连接成功!
2025-07-13 17:03:10.476 | INFO     | core.telegram.client_manager:_verify_client_authorization:1191 - 账户已授权: +***********
2025-07-13 17:03:10.608 | INFO     | core.telegram.client_manager:_connect_client:207 - 连接成功!
2025-07-13 17:03:11.882 | INFO     | core.telegram.client_manager:_verify_client_authorization:1191 - 账户已授权: +***********
2025-07-13 17:03:13.895 | INFO     | core.telegram.client_manager:batch_auto_login:1365 - 批量登录完成: 总计 2, 成功 2, 失败 0
2025-07-13 17:03:14.294 | INFO     | app.services.account_service:batch_auto_login:1305 - 服务层：核心层登录完成: 成功 2, 失败 0 / 2
2025-07-13 17:04:04.897 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 17:05:04.906 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 17:06:04.900 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 17:07:04.896 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 17:08:04.897 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 17:09:04.897 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 17:10:04.898 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 17:11:04.899 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 17:12:04.897 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 17:13:04.900 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 17:14:04.904 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 17:14:26.902 | INFO     | ui.main_window:closeEvent:380 - MainWindow: 接收到关闭事件
2025-07-13 17:14:26.917 | INFO     | ui.main_window:_cleanup_before_quit:266 - MainWindow: 执行清理资源...
2025-07-13 17:14:26.918 | INFO     | core.telegram.client_worker:stop:108 - 正在停止Telegram客户端工作线程
2025-07-13 17:14:26.926 | INFO     | core.telegram.client_manager:cleanup_async:1136 - 开始清理资源并断开所有连接
2025-07-13 17:14:26.926 | INFO     | core.telegram.client_manager:disconnect_all_clients:721 - 正在断开所有客户端连接
2025-07-13 17:14:26.927 | INFO     | core.telegram.client_manager:_safe_disconnect:742 - 正在断开客户端连接: +***********
2025-07-13 17:14:26.928 | INFO     | core.telegram.client_manager:_safe_disconnect:742 - 正在断开客户端连接: +***********
2025-07-13 17:14:26.941 | INFO     | core.telegram.client_manager:_safe_disconnect:746 - 断开客户端连接成功: +***********
2025-07-13 17:14:26.942 | INFO     | core.telegram.client_manager:_safe_disconnect:746 - 断开客户端连接成功: +***********
2025-07-13 17:14:26.942 | INFO     | core.telegram.client_manager:cleanup_async:1155 - 资源清理完成
2025-07-13 17:14:27.413 | INFO     | core.telegram.client_manager:cleanup_async:1136 - 开始清理资源并断开所有连接
2025-07-13 17:14:27.414 | INFO     | core.telegram.client_manager:disconnect_all_clients:721 - 正在断开所有客户端连接
2025-07-13 17:14:27.414 | INFO     | core.telegram.client_manager:cleanup_async:1155 - 资源清理完成
2025-07-13 17:14:27.929 | INFO     | core.telegram.client_worker:run:97 - Telegram客户端工作线程已退出
2025-07-13 17:14:27.930 | INFO     | core.telegram.client_worker:stop:122 - Telegram客户端工作线程已停止
2025-07-13 17:14:27.931 | INFO     | ui.main_window:_cleanup_before_quit:275 - TelegramClientWorker 已停止。
2025-07-13 17:14:27.931 | INFO     | ui.main_window:_cleanup_before_quit:279 - MainWindow 清理完成
2025-07-13 17:14:27.947 | INFO     | __main__:main:109 - 应用程序已正常退出
2025-07-13 17:25:50.854 | INFO     | utils.logger:init_logger:98 - 日志系统初始化完成，默认级别: DEBUG，日志目录: H:\PyProject\TeleTest\APPDATA\logs
2025-07-13 17:25:52.559 | INFO     | data.database:init_database:31 - 初始化数据库连接: H:\PyProject\TeleTest\APPDATA\database\telegram_manager.db
2025-07-13 17:25:52.575 | DEBUG    | data.database:init_database:58 - 创建数据库表
2025-07-13 17:25:52.585 | INFO     | data.database:init_database:61 - 数据库初始化完成
2025-07-13 17:25:53.849 | DEBUG    | app.controllers.auth_controller:initialize_software:43 - 控制器: 初始化软件 app_id=1000, version=1.2.7
2025-07-13 17:25:53.849 | DEBUG    | core.auth.api_service:init_software:55 - 发送初始化请求: https://server.xile188.com/api/user/1000/windowns10/1.2.7/ini, params={}
2025-07-13 17:25:54.088 | DEBUG    | core.auth.api_service:init_software:66 - 初始化数据解密成功: {'new_content': '- 新增功能\n  - 1.新增图片上传功能\n  - 2.新增软件文档\n  - 3.新增软件自动更新，后续更新会自动检测版本更新，无需再下载更新包\n  - 4.新增设置账户信息及发信控制\n- 修复bug\n  - 删除监听任务时，不删除对应任务采集到的数据。\n  - 监听设置群无法发送到目标群\n  - 导出监听数据时应当导出单个任务而非所有数据\n- 优化记录\n  - 优化发信规则，直接调取符合要求的账户发送，无账户时才需要等待延时。\n  - 完善软件设置', 'new_url': None, 'notice': {'id': '2', 'content': '公告内容测试3', 'time': '**********'}}
2025-07-13 17:25:54.096 | INFO     | app.controllers.auth_controller:login:61 - 控制器: 用户登录 account=demo01
2025-07-13 17:25:57.014 | INFO     | core.auth.api_service:login:127 - 用户登录: account=demo01
2025-07-13 17:25:57.253 | INFO     | core.auth.api_service:get_user_info:347 - 获取用户信息
2025-07-13 17:25:57.507 | INFO     | core.auth.api_service:get_user_info:375 - 获取用户信息成功
2025-07-13 17:25:57.513 | INFO     | __main__:on_auth_completed:91 - 用户登录成功，正在启动主窗口...
2025-07-13 17:25:57.535 | INFO     | core.telegram.client_worker:__init__:61 - Telegram客户端工作线程初始化
2025-07-13 17:25:57.536 | INFO     | utils.vip_checker:initialize:25 - VIP检查器已初始化，等待登录后开始检查
2025-07-13 17:25:57.536 | INFO     | ui.main_window:_initialize_core_components:108 - MainWindow: 初始化核心组件...
2025-07-13 17:25:57.536 | INFO     | app.services.account_service:__init__:42 - 账户服务初始化
2025-07-13 17:25:57.536 | INFO     | app.controllers.account_controller:__init__:81 - 账户控制器初始化
2025-07-13 17:25:57.537 | INFO     | app.services.monitor_service:__init__:39 - 初始化监控任务服务
2025-07-13 17:25:57.537 | INFO     | core.monitor.filter:__init__:26 - 初始化消息过滤器
2025-07-13 17:25:57.537 | INFO     | core.monitor.extractor:__init__:24 - 初始化关键词提取器
2025-07-13 17:25:57.537 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-07-13 17:25:57.537 | INFO     | core.monitor.notifier:__init__:90 - 初始化通知管理器
2025-07-13 17:25:57.537 | INFO     | app.controllers.telegram_monitor_controller:__init__:64 - 初始化 Telegram 监控控制器
2025-07-13 17:25:57.537 | DEBUG    | app.controllers.telegram_monitor_controller:__init__:82 - 控制器初始化完成
2025-07-13 17:25:57.537 | INFO     | core.telegram.client_worker:run:65 - Telegram客户端工作线程开始运行
2025-07-13 17:25:57.539 | INFO     | core.proxy.proxy_task_manager:run:284 - 代理任务管理器线程启动
2025-07-13 17:25:57.542 | INFO     | core.telegram.client_manager:__init__:66 - Telegram客户端管理器初始化
2025-07-13 17:25:57.542 | INFO     | core.telegram.user_manager:__init__:30 - Telegram用户管理器初始化
2025-07-13 17:25:57.542 | INFO     | core.telegram.group_manager:__init__:36 - Telegram群组管理器初始化
2025-07-13 17:25:57.542 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-07-13 17:25:57.542 | INFO     | core.telegram.monitor_manager:__init__:26 - Telegram监控管理器初始化
2025-07-13 17:25:57.542 | INFO     | core.telegram.telegram_service:__init__:39 - Telegram服务初始化完成
2025-07-13 17:25:57.723 | INFO     | ui.views.monitor_view:__init__:48 - 初始化监控视图
2025-07-13 17:25:57.723 | DEBUG    | ui.views.monitor_view:_connect_signals:80 - 连接控制器信号
2025-07-13 17:25:57.903 | INFO     | ui.views.proxy_view:__init__:96 - 初始化视图，加载第一页代理数据
2025-07-13 17:25:58.141 | INFO     | __main__:on_auth_completed:95 - 主窗口已启动
2025-07-13 17:25:58.181 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:88 - 尝试连接监听日志信号...
2025-07-13 17:25:58.181 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:90 - 监听日志信号连接成功
2025-07-13 17:25:58.181 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-13 17:25:58.185 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-13 17:25:58.189 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:145 - 加载监控任务列表 (搜索关键词: '')
2025-07-13 17:25:58.189 | DEBUG    | ui.views.monitor_view:_on_loading_started:620 - 加载中: 正在加载任务列表...
2025-07-13 17:25:58.189 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-13 17:25:58.196 | INFO     | app.services.proxy_service:refresh_proxies:712 - 刷新代理列表: offset=0, limit=20
2025-07-13 17:25:58.196 | INFO     | core.proxy.proxy_task_manager:submit_task:172 - 提交耗时任务到子线程: refresh_proxies_0_20
2025-07-13 17:25:58.196 | DEBUG    | app.services.proxy_service:get_proxies:500 - 获取代理列表: offset=0, limit=20
2025-07-13 17:25:58.196 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-13 17:25:58.197 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-13 17:25:58.212 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-13 17:25:58.223 | INFO     | app.controllers.invite_controller:get_invite_tasks:38 - 获取邀请任务列表, status=None
2025-07-13 17:25:58.223 | INFO     | app.services.invite_service:get_invite_tasks:121 - 获取邀请任务列表, status=None
2025-07-13 17:25:58.223 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-13 17:25:58.224 | INFO     | app.services.invite_service:task_initialize:39 - 初始化邀请服务，重置运行中的任务为暂停状态
2025-07-13 17:25:58.226 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-13 17:25:58.228 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-13 17:25:58.230 | INFO     | core.proxy.proxy_task_manager:task_wrapper:199 - 任务 refresh_proxies_0_20 完成
2025-07-13 17:25:58.230 | INFO     | utils.vip_checker:start_checking:37 - 开始VIP状态检查
2025-07-13 17:25:58.230 | DEBUG    | utils.vip_checker:_check_vip_status:61 - 正在检查VIP状态...
2025-07-13 17:25:58.230 | INFO     | core.auth.api_service:verify_vip:425 - 会员验证接口调用
2025-07-13 17:25:58.353 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-13 17:25:58.354 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-13 17:25:58.361 | INFO     | app.services.invite_service:task_initialize:48 - 成功重置 0 个任务状态
2025-07-13 17:25:58.361 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-13 17:25:58.364 | DEBUG    | ui.views.monitor_view:_refresh_task_list:175 - 刷新任务列表，共0个任务
2025-07-13 17:25:58.364 | INFO     | ui.views.monitor_view:_refresh_task_list:189 - 没有找到监控任务
2025-07-13 17:25:58.365 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-13 17:25:58.373 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-13 17:25:58.374 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-13 17:25:58.375 | INFO     | app.services.account_service:get_all_groups:96 - 获取所有账户分组成功, 共 2 个
2025-07-13 17:25:58.375 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-13 17:25:58.378 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-13 17:25:58.427 | DEBUG    | ui.views.account_view:_on_groups_loaded:515 - 分组加载完成: 2个分组
2025-07-13 17:25:58.445 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-13 17:25:58.471 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-13 17:25:58.528 | INFO     | app.services.account_service:get_all_accounts:290 - 获取所有账户成功, 共 2 个
2025-07-13 17:25:58.528 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-13 17:25:58.532 | INFO     | app.controllers.telegram_monitor_controller:_load_total_users_count_internal:191 - 成功加载总体统计数据: 总用户数 23, 今日采集 0, 日均采集 23, 运行天数 1
2025-07-13 17:25:58.532 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:156 - 成功加载 0 个任务
2025-07-13 17:25:58.532 | DEBUG    | ui.views.monitor_view:_on_loading_finished:625 - 加载完成
2025-07-13 17:25:58.534 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-13 17:25:58.535 | DEBUG    | ui.views.account_view:_on_accounts_loaded:648 - 账户加载完成: 2个账户
2025-07-13 17:25:58.553 | DEBUG    | ui.views.account_view:_update_group_counts:186 - 分组计数已更新: {2: 1, 1: 1}
2025-07-13 17:25:58.557 | INFO     | ui.views.account_view:_auto_login_accounts:703 - 开始调用控制器进行 2 个账户的自动登录。
2025-07-13 17:25:58.557 | INFO     | app.services.account_service:batch_auto_login:1165 - 服务层：开始批量自动登录 2 个账户, 最大并发: 5
2025-07-13 17:25:58.557 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-13 17:25:58.560 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-13 17:25:58.563 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-07-13 17:25:58.577 | INFO     | app.services.account_service:batch_auto_login:1204 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-07-13 17:25:58.577 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-13 17:25:58.578 | INFO     | app.services.account_service:get_all_accounts:290 - 获取所有账户成功, 共 2 个
2025-07-13 17:25:58.578 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-13 17:25:58.579 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-13 17:25:58.582 | DEBUG    | ui.views.account_view:_update_group_counts:186 - 分组计数已更新: {2: 1, 1: 1}
2025-07-13 17:25:58.582 | DEBUG    | ui.views.account_view:_init_cache:153 - 账户缓存初始化完成，共 2 个账户
2025-07-13 17:25:58.588 | DEBUG    | ui.views.account_view:_on_accounts_loaded:648 - 账户加载完成: 2个账户
2025-07-13 17:25:58.615 | DEBUG    | ui.views.account_view:_update_group_counts:186 - 分组计数已更新: {2: 1, 1: 1}
2025-07-13 17:25:58.632 | INFO     | app.services.account_service:batch_auto_login:1204 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-07-13 17:25:58.632 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-13 17:25:58.635 | INFO     | app.services.account_service:batch_auto_login:1274 - 服务层：准备调用核心层处理 2 个账户的自动登录。
2025-07-13 17:25:58.635 | INFO     | app.services.account_service:batch_auto_login:1284 - 服务层：设置核心层任务超时为 120 秒。
2025-07-13 17:25:58.635 | INFO     | core.telegram.client_manager:batch_auto_login:1216 - 开始批量自动登录, 共 2 个账户, 最大并发 5
2025-07-13 17:25:58.636 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:259 - 创建客户端: H:\PyProject\TeleTest\APPDATA\sessions\+***********, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-13 17:25:58.636 | DEBUG    | core.telegram.client_manager:get_proxy:96 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-13 17:25:58.636 | DEBUG    | core.telegram.client_manager:get_proxy:143 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-07-13 17:25:58.641 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:259 - 创建客户端: H:\PyProject\TeleTest\APPDATA\sessions\+***********, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-13 17:25:58.641 | DEBUG    | core.telegram.client_manager:get_proxy:96 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-13 17:25:58.642 | DEBUG    | core.telegram.client_manager:get_proxy:143 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-07-13 17:25:58.672 | INFO     | core.auth.api_service:verify_vip:438 - 会员验证成功
2025-07-13 17:25:58.673 | INFO     | utils.vip_checker:_check_vip_status:76 - VIP状态有效
2025-07-13 17:25:58.674 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-13 17:25:58.757 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-13 17:25:58.799 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-13 17:25:58.807 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-07-13 17:25:58.809 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-07-13 17:25:58.810 | INFO     | ui.views.proxy_view:_load_initial_data:1044 - 初始数据加载完成，共 1 个代理
2025-07-13 17:26:02.156 | INFO     | core.telegram.client_manager:_connect_client:207 - 连接成功!
2025-07-13 17:26:03.138 | INFO     | core.telegram.client_manager:_verify_client_authorization:1191 - 账户已授权: +***********
2025-07-13 17:26:06.330 | INFO     | core.telegram.client_manager:_connect_client:207 - 连接成功!
2025-07-13 17:26:08.614 | INFO     | core.telegram.client_manager:_verify_client_authorization:1191 - 账户已授权: +***********
2025-07-13 17:26:10.614 | INFO     | core.telegram.client_manager:batch_auto_login:1365 - 批量登录完成: 总计 2, 成功 2, 失败 0
2025-07-13 17:26:10.657 | INFO     | app.services.account_service:batch_auto_login:1305 - 服务层：核心层登录完成: 成功 2, 失败 0 / 2
2025-07-13 17:26:57.509 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 17:28:57.506 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 18:55:02.958 | INFO     | utils.logger:init_logger:98 - 日志系统初始化完成，默认级别: DEBUG，日志目录: H:\PyProject\TeleTest\APPDATA\logs
2025-07-13 18:55:04.038 | INFO     | data.database:init_database:31 - 初始化数据库连接: H:\PyProject\TeleTest\APPDATA\database\telegram_manager.db
2025-07-13 18:55:04.055 | DEBUG    | data.database:init_database:58 - 创建数据库表
2025-07-13 18:55:04.071 | INFO     | data.database:init_database:61 - 数据库初始化完成
2025-07-13 18:55:05.253 | DEBUG    | app.controllers.auth_controller:initialize_software:43 - 控制器: 初始化软件 app_id=1000, version=1.2.7
2025-07-13 18:55:05.253 | DEBUG    | core.auth.api_service:init_software:55 - 发送初始化请求: https://server.xile188.com/api/user/1000/windowns10/1.2.7/ini, params={}
2025-07-13 18:55:05.670 | DEBUG    | core.auth.api_service:init_software:66 - 初始化数据解密成功: {'new_content': '- 新增功能\n  - 1.新增图片上传功能\n  - 2.新增软件文档\n  - 3.新增软件自动更新，后续更新会自动检测版本更新，无需再下载更新包\n  - 4.新增设置账户信息及发信控制\n- 修复bug\n  - 删除监听任务时，不删除对应任务采集到的数据。\n  - 监听设置群无法发送到目标群\n  - 导出监听数据时应当导出单个任务而非所有数据\n- 优化记录\n  - 优化发信规则，直接调取符合要求的账户发送，无账户时才需要等待延时。\n  - 完善软件设置', 'new_url': None, 'notice': {'id': '2', 'content': '公告内容测试3', 'time': '**********'}}
2025-07-13 18:55:05.678 | INFO     | app.controllers.auth_controller:login:61 - 控制器: 用户登录 account=demo01
2025-07-13 18:55:08.635 | INFO     | core.auth.api_service:login:127 - 用户登录: account=demo01
2025-07-13 18:55:08.928 | INFO     | core.auth.api_service:get_user_info:347 - 获取用户信息
2025-07-13 18:55:09.199 | INFO     | core.auth.api_service:get_user_info:375 - 获取用户信息成功
2025-07-13 18:55:09.205 | INFO     | __main__:on_auth_completed:91 - 用户登录成功，正在启动主窗口...
2025-07-13 18:55:09.227 | INFO     | core.telegram.client_worker:__init__:61 - Telegram客户端工作线程初始化
2025-07-13 18:55:09.227 | INFO     | utils.vip_checker:initialize:25 - VIP检查器已初始化，等待登录后开始检查
2025-07-13 18:55:09.227 | INFO     | ui.main_window:_initialize_core_components:108 - MainWindow: 初始化核心组件...
2025-07-13 18:55:09.227 | INFO     | app.services.account_service:__init__:42 - 账户服务初始化
2025-07-13 18:55:09.228 | INFO     | core.telegram.client_worker:run:65 - Telegram客户端工作线程开始运行
2025-07-13 18:55:09.228 | INFO     | core.proxy.proxy_task_manager:run:284 - 代理任务管理器线程启动
2025-07-13 18:55:09.228 | INFO     | app.controllers.account_controller:__init__:81 - 账户控制器初始化
2025-07-13 18:55:09.228 | INFO     | app.services.monitor_service:__init__:39 - 初始化监控任务服务
2025-07-13 18:55:09.228 | INFO     | core.monitor.filter:__init__:26 - 初始化消息过滤器
2025-07-13 18:55:09.228 | INFO     | core.monitor.extractor:__init__:24 - 初始化关键词提取器
2025-07-13 18:55:09.229 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-07-13 18:55:09.229 | INFO     | core.monitor.notifier:__init__:90 - 初始化通知管理器
2025-07-13 18:55:09.229 | INFO     | app.controllers.telegram_monitor_controller:__init__:64 - 初始化 Telegram 监控控制器
2025-07-13 18:55:09.229 | DEBUG    | app.controllers.telegram_monitor_controller:__init__:82 - 控制器初始化完成
2025-07-13 18:55:09.231 | INFO     | core.telegram.client_manager:__init__:66 - Telegram客户端管理器初始化
2025-07-13 18:55:09.232 | INFO     | core.telegram.user_manager:__init__:30 - Telegram用户管理器初始化
2025-07-13 18:55:09.233 | INFO     | core.telegram.group_manager:__init__:36 - Telegram群组管理器初始化
2025-07-13 18:55:09.233 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-07-13 18:55:09.235 | INFO     | core.telegram.monitor_manager:__init__:26 - Telegram监控管理器初始化
2025-07-13 18:55:09.235 | INFO     | core.telegram.telegram_service:__init__:39 - Telegram服务初始化完成
2025-07-13 18:55:09.417 | INFO     | ui.views.monitor_view:__init__:48 - 初始化监控视图
2025-07-13 18:55:09.418 | DEBUG    | ui.views.monitor_view:_connect_signals:80 - 连接控制器信号
2025-07-13 18:55:09.608 | INFO     | ui.views.proxy_view:__init__:96 - 初始化视图，加载第一页代理数据
2025-07-13 18:55:09.866 | INFO     | __main__:on_auth_completed:95 - 主窗口已启动
2025-07-13 18:55:09.924 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:88 - 尝试连接监听日志信号...
2025-07-13 18:55:09.925 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:90 - 监听日志信号连接成功
2025-07-13 18:55:09.925 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-13 18:55:09.931 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-13 18:55:09.935 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:145 - 加载监控任务列表 (搜索关键词: '')
2025-07-13 18:55:09.936 | DEBUG    | ui.views.monitor_view:_on_loading_started:620 - 加载中: 正在加载任务列表...
2025-07-13 18:55:09.936 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-13 18:55:09.943 | INFO     | app.services.proxy_service:refresh_proxies:712 - 刷新代理列表: offset=0, limit=20
2025-07-13 18:55:09.943 | INFO     | core.proxy.proxy_task_manager:submit_task:172 - 提交耗时任务到子线程: refresh_proxies_0_20
2025-07-13 18:55:09.943 | DEBUG    | app.services.proxy_service:get_proxies:500 - 获取代理列表: offset=0, limit=20
2025-07-13 18:55:09.943 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-13 18:55:09.943 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-13 18:55:09.947 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-13 18:55:09.971 | INFO     | app.controllers.invite_controller:get_invite_tasks:38 - 获取邀请任务列表, status=None
2025-07-13 18:55:09.971 | INFO     | app.services.invite_service:get_invite_tasks:121 - 获取邀请任务列表, status=None
2025-07-13 18:55:09.971 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-13 18:55:09.973 | INFO     | app.services.invite_service:task_initialize:39 - 初始化邀请服务，重置运行中的任务为暂停状态
2025-07-13 18:55:09.974 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-13 18:55:09.975 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-13 18:55:09.977 | INFO     | core.proxy.proxy_task_manager:task_wrapper:199 - 任务 refresh_proxies_0_20 完成
2025-07-13 18:55:09.978 | INFO     | utils.vip_checker:start_checking:37 - 开始VIP状态检查
2025-07-13 18:55:09.978 | DEBUG    | utils.vip_checker:_check_vip_status:61 - 正在检查VIP状态...
2025-07-13 18:55:09.978 | INFO     | core.auth.api_service:verify_vip:425 - 会员验证接口调用
2025-07-13 18:55:10.105 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-13 18:55:10.106 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-13 18:55:10.113 | INFO     | app.services.invite_service:task_initialize:48 - 成功重置 0 个任务状态
2025-07-13 18:55:10.113 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-13 18:55:10.114 | DEBUG    | ui.views.monitor_view:_refresh_task_list:175 - 刷新任务列表，共0个任务
2025-07-13 18:55:10.115 | INFO     | ui.views.monitor_view:_refresh_task_list:189 - 没有找到监控任务
2025-07-13 18:55:10.115 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-13 18:55:10.123 | INFO     | app.services.account_service:get_all_groups:96 - 获取所有账户分组成功, 共 2 个
2025-07-13 18:55:10.123 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-13 18:55:10.125 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-13 18:55:10.126 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-13 18:55:10.129 | DEBUG    | ui.views.account_view:_on_groups_loaded:515 - 分组加载完成: 2个分组
2025-07-13 18:55:10.144 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-13 18:55:10.147 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-13 18:55:10.268 | INFO     | app.services.account_service:get_all_accounts:290 - 获取所有账户成功, 共 2 个
2025-07-13 18:55:10.268 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-13 18:55:10.269 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-13 18:55:10.271 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-13 18:55:10.272 | DEBUG    | ui.views.account_view:_on_accounts_loaded:648 - 账户加载完成: 2个账户
2025-07-13 18:55:10.292 | DEBUG    | ui.views.account_view:_update_group_counts:186 - 分组计数已更新: {2: 1, 1: 1}
2025-07-13 18:55:10.295 | INFO     | ui.views.account_view:_auto_login_accounts:703 - 开始调用控制器进行 2 个账户的自动登录。
2025-07-13 18:55:10.296 | INFO     | app.services.account_service:batch_auto_login:1165 - 服务层：开始批量自动登录 2 个账户, 最大并发: 5
2025-07-13 18:55:10.296 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-13 18:55:10.300 | INFO     | app.controllers.telegram_monitor_controller:_load_total_users_count_internal:191 - 成功加载总体统计数据: 总用户数 23, 今日采集 0, 日均采集 23, 运行天数 1
2025-07-13 18:55:10.300 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:156 - 成功加载 0 个任务
2025-07-13 18:55:10.300 | DEBUG    | ui.views.monitor_view:_on_loading_finished:625 - 加载完成
2025-07-13 18:55:10.303 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-13 18:55:10.306 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-07-13 18:55:10.318 | INFO     | app.services.account_service:get_all_accounts:290 - 获取所有账户成功, 共 2 个
2025-07-13 18:55:10.319 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-13 18:55:10.319 | INFO     | app.services.account_service:batch_auto_login:1204 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-07-13 18:55:10.320 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-13 18:55:10.322 | DEBUG    | ui.views.account_view:_update_group_counts:186 - 分组计数已更新: {2: 1, 1: 1}
2025-07-13 18:55:10.323 | DEBUG    | ui.views.account_view:_init_cache:153 - 账户缓存初始化完成，共 2 个账户
2025-07-13 18:55:10.327 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-13 18:55:10.328 | DEBUG    | ui.views.account_view:_on_accounts_loaded:648 - 账户加载完成: 2个账户
2025-07-13 18:55:10.355 | DEBUG    | ui.views.account_view:_update_group_counts:186 - 分组计数已更新: {2: 1, 1: 1}
2025-07-13 18:55:10.375 | INFO     | app.services.account_service:batch_auto_login:1204 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-07-13 18:55:10.375 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-13 18:55:10.378 | INFO     | app.services.account_service:batch_auto_login:1274 - 服务层：准备调用核心层处理 2 个账户的自动登录。
2025-07-13 18:55:10.379 | INFO     | app.services.account_service:batch_auto_login:1284 - 服务层：设置核心层任务超时为 120 秒。
2025-07-13 18:55:10.379 | INFO     | core.telegram.client_manager:batch_auto_login:1216 - 开始批量自动登录, 共 2 个账户, 最大并发 5
2025-07-13 18:55:10.380 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:259 - 创建客户端: H:\PyProject\TeleTest\APPDATA\sessions\+***********, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-13 18:55:10.380 | DEBUG    | core.telegram.client_manager:get_proxy:96 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-13 18:55:10.380 | DEBUG    | core.telegram.client_manager:get_proxy:143 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-07-13 18:55:10.385 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:259 - 创建客户端: H:\PyProject\TeleTest\APPDATA\sessions\+***********, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-13 18:55:10.386 | DEBUG    | core.telegram.client_manager:get_proxy:96 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-13 18:55:10.386 | DEBUG    | core.telegram.client_manager:get_proxy:143 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-07-13 18:55:10.426 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-13 18:55:10.428 | INFO     | core.auth.api_service:verify_vip:438 - 会员验证成功
2025-07-13 18:55:10.428 | INFO     | utils.vip_checker:_check_vip_status:76 - VIP状态有效
2025-07-13 18:55:10.536 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-13 18:55:10.573 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-07-13 18:55:10.575 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-07-13 18:55:10.577 | INFO     | ui.views.proxy_view:_load_initial_data:1044 - 初始数据加载完成，共 1 个代理
2025-07-13 18:55:10.595 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-13 18:55:23.259 | INFO     | core.telegram.client_manager:_connect_client:207 - 连接成功!
2025-07-13 18:55:24.231 | INFO     | core.telegram.client_manager:_verify_client_authorization:1191 - 账户已授权: +***********
2025-07-13 18:56:09.198 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 18:56:16.781 | INFO     | core.telegram.client_manager:_connect_client:207 - 连接成功!
2025-07-13 18:56:17.717 | INFO     | core.telegram.client_manager:_verify_client_authorization:1191 - 账户已授权: +***********
2025-07-13 18:56:19.721 | INFO     | core.telegram.client_manager:batch_auto_login:1365 - 批量登录完成: 总计 2, 成功 2, 失败 0
2025-07-13 18:56:20.650 | INFO     | app.services.account_service:batch_auto_login:1305 - 服务层：核心层登录完成: 成功 2, 失败 0 / 2
2025-07-13 18:57:09.200 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 18:58:09.196 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 18:59:09.208 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 19:00:09.202 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 19:01:09.197 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 19:02:09.194 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 19:03:09.194 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 19:04:09.197 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 19:05:09.200 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 19:06:09.201 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 19:07:09.196 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 19:08:09.199 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 19:09:09.196 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 19:10:09.195 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 19:11:09.198 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 19:12:09.200 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 19:13:09.202 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 19:14:09.198 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 19:15:09.199 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 19:16:09.207 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 19:17:09.197 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 19:18:09.193 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 19:19:09.212 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 19:20:09.210 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 19:21:09.220 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 19:22:09.211 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 19:23:09.213 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 19:24:09.220 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 19:25:09.210 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 19:26:09.215 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 19:27:09.208 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 19:28:09.213 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 19:29:09.208 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 19:30:09.219 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 19:31:09.212 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 19:32:09.209 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 19:33:09.220 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 19:34:09.209 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 19:35:09.224 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 19:36:09.221 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 19:37:09.212 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 19:38:09.214 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 19:39:09.214 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 19:40:09.210 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 19:41:09.218 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 19:42:09.209 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 19:43:09.217 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 19:44:09.219 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 19:45:09.210 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 19:46:09.218 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 19:47:09.213 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 19:48:09.216 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 19:49:09.220 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 19:50:09.223 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 19:51:09.213 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 19:52:09.208 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 19:53:09.214 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 19:54:09.212 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 19:55:09.217 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 19:56:09.216 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 19:57:09.209 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 19:58:09.210 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 19:59:09.216 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 20:00:09.217 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 20:01:09.215 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 20:02:09.221 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 20:03:09.214 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 20:04:09.212 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 20:05:09.223 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 20:06:09.209 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 20:07:09.216 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 20:08:09.221 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 20:09:09.219 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 20:10:09.216 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 20:11:09.212 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 20:12:09.212 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 20:13:09.219 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 20:14:09.216 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 20:15:09.213 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 20:16:09.213 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 20:17:09.213 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 20:18:09.215 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 20:19:09.223 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 20:20:09.208 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 20:21:09.218 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 20:22:09.222 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 20:23:09.221 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 20:24:09.223 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 20:25:09.212 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 20:26:09.218 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 20:27:09.213 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 20:28:09.219 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 20:29:09.217 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 20:30:09.227 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 20:31:09.237 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 20:32:09.224 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 20:33:09.235 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 20:34:09.236 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 20:35:09.230 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 20:36:09.239 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 20:37:09.223 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 20:38:09.231 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 20:39:09.235 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 20:40:09.237 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 20:41:09.231 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 20:42:09.231 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 20:43:09.238 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 20:44:09.229 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 20:45:09.238 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 20:46:09.232 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 20:47:09.229 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 20:48:09.226 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 20:49:09.226 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 20:50:09.233 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 20:51:09.230 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 20:52:09.223 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 20:53:09.231 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 20:54:09.228 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 20:55:09.230 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 20:56:09.225 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 20:57:09.240 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 20:58:09.238 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 20:59:09.226 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 21:00:09.243 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 21:01:09.242 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 21:02:09.240 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 21:03:09.241 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 21:04:09.241 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 21:05:09.254 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 21:06:09.246 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 21:07:09.248 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 21:08:09.249 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 21:09:09.248 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 21:10:09.250 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 21:11:09.240 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 21:12:09.242 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 21:13:09.254 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 21:14:09.253 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 21:15:09.251 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 21:16:09.258 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 21:17:09.271 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 21:18:09.269 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 21:19:09.266 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 21:20:09.268 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 21:21:09.259 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 21:22:09.269 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 21:23:09.261 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 21:24:09.267 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 21:25:09.260 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 21:26:09.263 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 21:27:09.260 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 21:28:09.270 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 21:29:09.262 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 21:30:09.260 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 21:31:09.263 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 21:32:09.260 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 21:33:09.257 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 21:34:09.258 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 21:35:09.266 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 21:36:09.256 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 21:37:09.257 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 21:38:09.266 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 21:39:09.261 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 21:40:09.264 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 21:41:09.257 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 21:42:09.262 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 21:43:09.257 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 21:44:09.261 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 21:45:09.269 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 21:46:09.267 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 21:47:09.266 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 21:48:09.259 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 21:49:09.261 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 21:50:09.256 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 21:51:09.264 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 21:52:09.260 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 21:53:09.268 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 21:54:09.267 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 21:55:09.268 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 21:56:09.260 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 21:57:09.256 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 21:58:09.269 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 21:59:09.262 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 22:00:09.262 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 22:01:09.269 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 22:02:09.265 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 22:03:09.261 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 22:04:09.267 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 22:05:09.268 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 22:06:09.260 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 22:07:09.264 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 22:08:09.265 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 22:09:09.263 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 22:10:09.255 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 22:11:09.263 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 22:12:09.259 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 22:13:09.270 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 22:14:09.260 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 22:15:09.268 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 22:16:09.269 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 22:17:09.261 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 22:18:09.258 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 22:19:09.259 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 22:20:09.268 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 22:21:09.264 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 22:22:09.270 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 22:23:09.255 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 22:24:09.265 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 22:25:09.261 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 22:26:09.256 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 22:27:09.260 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 22:28:09.264 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 22:29:09.260 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 22:30:09.256 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 22:31:09.262 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 22:32:09.262 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 22:33:09.257 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 22:34:09.262 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 22:35:09.257 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 22:36:09.258 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 22:37:09.262 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 22:38:09.257 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 22:39:09.263 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 22:40:09.257 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 22:41:09.268 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 22:42:09.259 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 22:43:09.257 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 22:44:09.266 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 22:45:09.257 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 22:46:09.258 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 22:47:09.265 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 22:48:09.258 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 22:49:09.255 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 22:50:09.268 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 22:51:09.261 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 22:52:09.265 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 22:53:09.264 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 22:54:09.260 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 22:55:09.258 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 22:56:09.263 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 22:57:09.271 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 22:58:09.261 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 22:59:09.262 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 23:00:09.269 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 23:01:09.259 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 23:02:09.257 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 23:03:09.262 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 23:04:09.258 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 23:05:09.270 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 23:06:09.261 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 23:07:09.268 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 23:08:09.259 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 23:09:09.269 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 23:10:09.261 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 23:11:09.263 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 23:12:09.258 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 23:13:09.260 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 23:14:09.266 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 23:15:09.257 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 23:16:09.259 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 23:17:09.265 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 23:18:09.259 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 23:19:09.259 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 23:20:09.269 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 23:21:09.269 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 23:22:09.263 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 23:23:09.256 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 23:24:09.262 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 23:25:09.265 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 23:26:09.266 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 23:27:09.262 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 23:28:09.266 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 23:29:09.268 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 23:30:09.268 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 23:31:09.259 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 23:32:09.264 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-13 23:32:43.898 | INFO     | ui.main_window:_cleanup_before_quit:266 - MainWindow: 执行清理资源...
2025-07-13 23:32:43.898 | INFO     | core.telegram.client_worker:stop:108 - 正在停止Telegram客户端工作线程
2025-07-13 23:32:43.901 | INFO     | core.telegram.client_manager:cleanup_async:1136 - 开始清理资源并断开所有连接
2025-07-13 23:32:43.901 | INFO     | core.telegram.client_manager:disconnect_all_clients:721 - 正在断开所有客户端连接
2025-07-13 23:32:43.902 | INFO     | core.telegram.client_manager:_safe_disconnect:742 - 正在断开客户端连接: +***********
2025-07-13 23:32:43.902 | INFO     | core.telegram.client_manager:_safe_disconnect:742 - 正在断开客户端连接: +***********
2025-07-13 23:32:43.926 | INFO     | core.telegram.client_manager:_safe_disconnect:746 - 断开客户端连接成功: +***********
2025-07-13 23:32:43.926 | INFO     | core.telegram.client_manager:_safe_disconnect:746 - 断开客户端连接成功: +***********
2025-07-13 23:32:43.926 | INFO     | core.telegram.client_manager:cleanup_async:1155 - 资源清理完成
2025-07-13 23:32:44.414 | INFO     | core.telegram.client_manager:cleanup_async:1136 - 开始清理资源并断开所有连接
2025-07-13 23:32:44.414 | INFO     | core.telegram.client_manager:disconnect_all_clients:721 - 正在断开所有客户端连接
2025-07-13 23:32:44.414 | INFO     | core.telegram.client_manager:cleanup_async:1155 - 资源清理完成
2025-07-13 23:32:44.915 | INFO     | core.telegram.client_worker:run:97 - Telegram客户端工作线程已退出
2025-07-13 23:32:44.915 | INFO     | core.telegram.client_worker:stop:122 - Telegram客户端工作线程已停止
2025-07-13 23:32:44.915 | INFO     | ui.main_window:_cleanup_before_quit:275 - TelegramClientWorker 已停止。
2025-07-13 23:32:44.916 | INFO     | ui.main_window:_cleanup_before_quit:279 - MainWindow 清理完成
2025-07-13 23:32:44.916 | INFO     | ui.main_window:closeEvent:380 - MainWindow: 接收到关闭事件
