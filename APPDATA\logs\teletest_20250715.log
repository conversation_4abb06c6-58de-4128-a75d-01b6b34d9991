2025-07-15 09:15:19.588 | INFO     | utils.logger:init_logger:98 - 日志系统初始化完成，默认级别: DEBUG，日志目录: H:\PyProject\TeleTest\APPDATA\logs
2025-07-15 09:15:22.314 | INFO     | data.database:init_database:31 - 初始化数据库连接: H:\PyProject\TeleTest\APPDATA\database\telegram_manager.db
2025-07-15 09:15:22.343 | DEBUG    | data.database:init_database:58 - 创建数据库表
2025-07-15 09:15:22.361 | INFO     | data.database:init_database:61 - 数据库初始化完成
2025-07-15 09:15:25.310 | DEBUG    | app.controllers.auth_controller:initialize_software:43 - 控制器: 初始化软件 app_id=1000, version=1.2.7
2025-07-15 09:15:25.311 | DEBUG    | core.auth.api_service:init_software:55 - 发送初始化请求: https://server.xile188.com/api/user/1000/windowns10/1.2.7/ini, params={}
2025-07-15 09:15:25.560 | DEBUG    | core.auth.api_service:init_software:66 - 初始化数据解密成功: {'new_content': '- 新增功能\n  - 1.新增图片上传功能\n  - 2.新增软件文档\n  - 3.新增软件自动更新，后续更新会自动检测版本更新，无需再下载更新包\n  - 4.新增设置账户信息及发信控制\n- 修复bug\n  - 删除监听任务时，不删除对应任务采集到的数据。\n  - 监听设置群无法发送到目标群\n  - 导出监听数据时应当导出单个任务而非所有数据\n- 优化记录\n  - 优化发信规则，直接调取符合要求的账户发送，无账户时才需要等待延时。\n  - 完善软件设置', 'new_url': None, 'notice': {'id': '2', 'content': '公告内容测试3', 'time': '**********'}}
2025-07-15 09:15:25.571 | INFO     | app.controllers.auth_controller:login:61 - 控制器: 用户登录 account=demo01
2025-07-15 09:15:28.682 | INFO     | core.auth.api_service:login:127 - 用户登录: account=demo01
2025-07-15 09:15:28.911 | INFO     | core.auth.api_service:get_user_info:347 - 获取用户信息
2025-07-15 09:15:29.114 | INFO     | core.auth.api_service:get_user_info:375 - 获取用户信息成功
2025-07-15 09:15:29.122 | INFO     | __main__:on_auth_completed:91 - 用户登录成功，正在启动主窗口...
2025-07-15 09:15:29.159 | INFO     | core.telegram.client_worker:__init__:61 - Telegram客户端工作线程初始化
2025-07-15 09:15:29.160 | INFO     | utils.vip_checker:initialize:25 - VIP检查器已初始化，等待登录后开始检查
2025-07-15 09:15:29.161 | INFO     | ui.main_window:_initialize_core_components:108 - MainWindow: 初始化核心组件...
2025-07-15 09:15:29.161 | INFO     | core.telegram.client_worker:run:65 - Telegram客户端工作线程开始运行
2025-07-15 09:15:29.163 | INFO     | app.services.account_service:__init__:42 - 账户服务初始化
2025-07-15 09:15:29.163 | INFO     | core.proxy.proxy_task_manager:run:284 - 代理任务管理器线程启动
2025-07-15 09:15:29.163 | INFO     | app.controllers.account_controller:__init__:81 - 账户控制器初始化
2025-07-15 09:15:29.164 | INFO     | app.services.monitor_service:__init__:39 - 初始化监控任务服务
2025-07-15 09:15:29.165 | INFO     | core.monitor.filter:__init__:26 - 初始化消息过滤器
2025-07-15 09:15:29.165 | INFO     | core.telegram.client_manager:__init__:66 - Telegram客户端管理器初始化
2025-07-15 09:15:29.166 | INFO     | core.monitor.extractor:__init__:24 - 初始化关键词提取器
2025-07-15 09:15:29.167 | INFO     | core.telegram.user_manager:__init__:30 - Telegram用户管理器初始化
2025-07-15 09:15:29.167 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-07-15 09:15:29.168 | INFO     | core.telegram.group_manager:__init__:36 - Telegram群组管理器初始化
2025-07-15 09:15:29.168 | INFO     | core.monitor.notifier:__init__:90 - 初始化通知管理器
2025-07-15 09:15:29.169 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-07-15 09:15:29.169 | INFO     | app.controllers.telegram_monitor_controller:__init__:64 - 初始化 Telegram 监控控制器
2025-07-15 09:15:29.170 | INFO     | core.telegram.monitor_manager:__init__:26 - Telegram监控管理器初始化
2025-07-15 09:15:29.170 | DEBUG    | app.controllers.telegram_monitor_controller:__init__:82 - 控制器初始化完成
2025-07-15 09:15:29.171 | INFO     | core.telegram.telegram_service:__init__:39 - Telegram服务初始化完成
2025-07-15 09:15:29.421 | INFO     | ui.views.monitor_view:__init__:48 - 初始化监控视图
2025-07-15 09:15:29.422 | DEBUG    | ui.views.monitor_view:_connect_signals:80 - 连接控制器信号
2025-07-15 09:15:29.638 | INFO     | ui.views.proxy_view:__init__:96 - 初始化视图，加载第一页代理数据
2025-07-15 09:15:30.014 | INFO     | __main__:on_auth_completed:95 - 主窗口已启动
2025-07-15 09:15:30.080 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:88 - 尝试连接监听日志信号...
2025-07-15 09:15:30.081 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:90 - 监听日志信号连接成功
2025-07-15 09:15:30.081 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-15 09:15:30.086 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-15 09:15:30.092 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:145 - 加载监控任务列表 (搜索关键词: '')
2025-07-15 09:15:30.093 | DEBUG    | ui.views.monitor_view:_on_loading_started:620 - 加载中: 正在加载任务列表...
2025-07-15 09:15:30.093 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-15 09:15:30.102 | INFO     | app.services.proxy_service:refresh_proxies:712 - 刷新代理列表: offset=0, limit=20
2025-07-15 09:15:30.103 | INFO     | core.proxy.proxy_task_manager:submit_task:172 - 提交耗时任务到子线程: refresh_proxies_0_20
2025-07-15 09:15:30.104 | DEBUG    | app.services.proxy_service:get_proxies:500 - 获取代理列表: offset=0, limit=20
2025-07-15 09:15:30.104 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-15 09:15:30.104 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-15 09:15:30.110 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-15 09:15:30.144 | INFO     | app.controllers.invite_controller:get_invite_tasks:38 - 获取邀请任务列表, status=None
2025-07-15 09:15:30.146 | INFO     | app.services.invite_service:get_invite_tasks:121 - 获取邀请任务列表, status=None
2025-07-15 09:15:30.147 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-15 09:15:30.147 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-15 09:15:30.148 | INFO     | app.services.invite_service:task_initialize:39 - 初始化邀请服务，重置运行中的任务为暂停状态
2025-07-15 09:15:30.149 | INFO     | core.proxy.proxy_task_manager:task_wrapper:199 - 任务 refresh_proxies_0_20 完成
2025-07-15 09:15:30.149 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-15 09:15:30.152 | INFO     | utils.vip_checker:start_checking:37 - 开始VIP状态检查
2025-07-15 09:15:30.153 | DEBUG    | utils.vip_checker:_check_vip_status:61 - 正在检查VIP状态...
2025-07-15 09:15:30.153 | INFO     | core.auth.api_service:verify_vip:425 - 会员验证接口调用
2025-07-15 09:15:30.251 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-15 09:15:30.259 | DEBUG    | ui.views.monitor_view:_refresh_task_list:175 - 刷新任务列表，共0个任务
2025-07-15 09:15:30.260 | INFO     | ui.views.monitor_view:_refresh_task_list:189 - 没有找到监控任务
2025-07-15 09:15:30.260 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-15 09:15:30.263 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-15 09:15:30.266 | INFO     | app.services.invite_service:task_initialize:48 - 成功重置 0 个任务状态
2025-07-15 09:15:30.267 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-15 09:15:30.268 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-15 09:15:30.270 | INFO     | app.services.account_service:get_all_groups:96 - 获取所有账户分组成功, 共 2 个
2025-07-15 09:15:30.271 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-15 09:15:30.273 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-15 09:15:30.277 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-15 09:15:30.279 | DEBUG    | ui.views.account_view:_on_groups_loaded:508 - 分组加载完成: 2个分组
2025-07-15 09:15:30.296 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-15 09:15:30.365 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-15 09:15:30.367 | INFO     | app.services.account_service:get_all_accounts:290 - 获取所有账户成功, 共 2 个
2025-07-15 09:15:30.367 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-15 09:15:30.368 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-15 09:15:30.370 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-07-15 09:15:30.385 | INFO     | app.controllers.telegram_monitor_controller:_load_total_users_count_internal:191 - 成功加载总体统计数据: 总用户数 23, 今日采集 0, 日均采集 23, 运行天数 1
2025-07-15 09:15:30.386 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:156 - 成功加载 0 个任务
2025-07-15 09:15:30.386 | DEBUG    | ui.views.monitor_view:_on_loading_finished:625 - 加载完成
2025-07-15 09:15:30.387 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-15 09:15:30.389 | DEBUG    | ui.views.account_view:_on_accounts_loaded:641 - 账户加载完成: 2个账户
2025-07-15 09:15:30.416 | DEBUG    | ui.views.account_view:_update_group_counts:186 - 分组计数已更新: {2: 1, 1: 1}
2025-07-15 09:15:30.421 | INFO     | ui.views.account_view:_auto_login_accounts:696 - 开始调用控制器进行 2 个账户的自动登录。
2025-07-15 09:15:30.422 | INFO     | app.services.account_service:batch_auto_login:1165 - 服务层：开始批量自动登录 2 个账户, 最大并发: 5
2025-07-15 09:15:30.422 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-15 09:15:30.442 | INFO     | app.services.account_service:get_all_accounts:290 - 获取所有账户成功, 共 2 个
2025-07-15 09:15:30.442 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-15 09:15:30.443 | INFO     | app.services.account_service:batch_auto_login:1204 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-07-15 09:15:30.444 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-15 09:15:30.447 | DEBUG    | ui.views.account_view:_update_group_counts:186 - 分组计数已更新: {2: 1, 1: 1}
2025-07-15 09:15:30.447 | DEBUG    | ui.views.account_view:_init_cache:153 - 账户缓存初始化完成，共 2 个账户
2025-07-15 09:15:30.453 | DEBUG    | ui.views.account_view:_on_accounts_loaded:641 - 账户加载完成: 2个账户
2025-07-15 09:15:30.480 | DEBUG    | ui.views.account_view:_update_group_counts:186 - 分组计数已更新: {2: 1, 1: 1}
2025-07-15 09:15:30.487 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-15 09:15:30.494 | INFO     | app.services.account_service:batch_auto_login:1204 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-07-15 09:15:30.494 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-15 09:15:30.496 | INFO     | app.services.account_service:batch_auto_login:1274 - 服务层：准备调用核心层处理 2 个账户的自动登录。
2025-07-15 09:15:30.497 | INFO     | app.services.account_service:batch_auto_login:1284 - 服务层：设置核心层任务超时为 120 秒。
2025-07-15 09:15:30.497 | INFO     | core.telegram.client_manager:batch_auto_login:1216 - 开始批量自动登录, 共 2 个账户, 最大并发 5
2025-07-15 09:15:30.498 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:259 - 创建客户端: H:\PyProject\TeleTest\APPDATA\sessions\+***********, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-15 09:15:30.499 | DEBUG    | core.telegram.client_manager:get_proxy:96 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-15 09:15:30.499 | DEBUG    | core.telegram.client_manager:get_proxy:143 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-07-15 09:15:30.503 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:259 - 创建客户端: H:\PyProject\TeleTest\APPDATA\sessions\+***********, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-15 09:15:30.505 | DEBUG    | core.telegram.client_manager:get_proxy:96 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-15 09:15:30.505 | DEBUG    | core.telegram.client_manager:get_proxy:143 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-07-15 09:15:30.536 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-15 09:15:30.695 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-15 09:15:30.701 | INFO     | core.auth.api_service:verify_vip:438 - 会员验证成功
2025-07-15 09:15:30.702 | INFO     | utils.vip_checker:_check_vip_status:76 - VIP状态有效
2025-07-15 09:15:30.703 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-07-15 09:15:30.706 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-07-15 09:15:30.709 | INFO     | ui.views.proxy_view:_load_initial_data:1044 - 初始数据加载完成，共 1 个代理
2025-07-15 09:15:30.743 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-15 09:15:33.793 | INFO     | core.telegram.client_manager:_connect_client:207 - 连接成功!
2025-07-15 09:15:33.902 | INFO     | core.telegram.client_manager:_connect_client:207 - 连接成功!
2025-07-15 09:15:34.704 | INFO     | core.telegram.client_manager:_verify_client_authorization:1191 - 账户已授权: +***********
2025-07-15 09:15:34.826 | INFO     | core.telegram.client_manager:_verify_client_authorization:1191 - 账户已授权: +***********
2025-07-15 09:15:36.836 | INFO     | core.telegram.client_manager:batch_auto_login:1365 - 批量登录完成: 总计 2, 成功 2, 失败 0
2025-07-15 09:16:29.111 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 09:17:29.114 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 09:18:29.111 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 09:19:29.113 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 09:20:29.118 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 09:21:29.112 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 09:22:29.110 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 09:23:29.110 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 09:24:29.115 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 09:25:29.111 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 09:26:29.110 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 09:27:29.112 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 09:28:29.111 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 09:29:29.114 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 09:30:29.114 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 09:31:29.118 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 09:32:29.112 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 09:33:29.114 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 09:34:29.114 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 09:35:29.115 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 09:36:29.114 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 09:37:29.213 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 09:38:29.204 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 09:39:29.205 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 09:40:29.207 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 09:40:58.310 | INFO     | ui.main_window:closeEvent:380 - MainWindow: 接收到关闭事件
2025-07-15 09:40:58.321 | INFO     | ui.main_window:_cleanup_before_quit:266 - MainWindow: 执行清理资源...
2025-07-15 09:40:58.322 | INFO     | core.telegram.client_worker:stop:108 - 正在停止Telegram客户端工作线程
2025-07-15 09:40:58.329 | INFO     | core.telegram.client_manager:cleanup_async:1136 - 开始清理资源并断开所有连接
2025-07-15 09:40:58.330 | INFO     | core.telegram.client_manager:disconnect_all_clients:721 - 正在断开所有客户端连接
2025-07-15 09:40:58.331 | INFO     | core.telegram.client_manager:_safe_disconnect:742 - 正在断开客户端连接: +***********
2025-07-15 09:40:58.331 | INFO     | core.telegram.client_manager:_safe_disconnect:742 - 正在断开客户端连接: +***********
2025-07-15 09:40:58.343 | INFO     | core.telegram.client_manager:_safe_disconnect:746 - 断开客户端连接成功: +***********
2025-07-15 09:40:58.344 | INFO     | core.telegram.client_manager:_safe_disconnect:746 - 断开客户端连接成功: +***********
2025-07-15 09:40:58.344 | INFO     | core.telegram.client_manager:cleanup_async:1155 - 资源清理完成
2025-07-15 09:40:58.836 | INFO     | core.telegram.client_manager:cleanup_async:1136 - 开始清理资源并断开所有连接
2025-07-15 09:40:58.836 | INFO     | core.telegram.client_manager:disconnect_all_clients:721 - 正在断开所有客户端连接
2025-07-15 09:40:58.837 | INFO     | core.telegram.client_manager:cleanup_async:1155 - 资源清理完成
2025-07-15 09:40:59.338 | INFO     | core.telegram.client_worker:run:97 - Telegram客户端工作线程已退出
2025-07-15 09:40:59.339 | INFO     | core.telegram.client_worker:stop:122 - Telegram客户端工作线程已停止
2025-07-15 09:40:59.339 | INFO     | ui.main_window:_cleanup_before_quit:275 - TelegramClientWorker 已停止。
2025-07-15 09:40:59.340 | INFO     | ui.main_window:_cleanup_before_quit:279 - MainWindow 清理完成
2025-07-15 09:40:59.357 | INFO     | __main__:main:109 - 应用程序已正常退出
2025-07-15 10:03:47.835 | INFO     | utils.logger:init_logger:98 - 日志系统初始化完成，默认级别: DEBUG，日志目录: H:\PyProject\TeleTest\APPDATA\logs
2025-07-15 10:03:49.218 | INFO     | data.database:init_database:31 - 初始化数据库连接: H:\PyProject\TeleTest\APPDATA\database\telegram_manager.db
2025-07-15 10:03:49.235 | DEBUG    | data.database:init_database:58 - 创建数据库表
2025-07-15 10:03:49.248 | INFO     | data.database:init_database:61 - 数据库初始化完成
2025-07-15 10:03:50.257 | DEBUG    | app.controllers.auth_controller:initialize_software:43 - 控制器: 初始化软件 app_id=1000, version=1.2.7
2025-07-15 10:03:50.258 | DEBUG    | core.auth.api_service:init_software:55 - 发送初始化请求: https://server.xile188.com/api/user/1000/windowns10/1.2.7/ini, params={}
2025-07-15 10:03:50.778 | DEBUG    | core.auth.api_service:init_software:66 - 初始化数据解密成功: {'new_content': '- 新增功能\n  - 1.新增图片上传功能\n  - 2.新增软件文档\n  - 3.新增软件自动更新，后续更新会自动检测版本更新，无需再下载更新包\n  - 4.新增设置账户信息及发信控制\n- 修复bug\n  - 删除监听任务时，不删除对应任务采集到的数据。\n  - 监听设置群无法发送到目标群\n  - 导出监听数据时应当导出单个任务而非所有数据\n- 优化记录\n  - 优化发信规则，直接调取符合要求的账户发送，无账户时才需要等待延时。\n  - 完善软件设置', 'new_url': None, 'notice': {'id': '2', 'content': '公告内容测试3', 'time': '**********'}}
2025-07-15 10:03:50.785 | INFO     | app.controllers.auth_controller:login:61 - 控制器: 用户登录 account=demo01
2025-07-15 10:03:53.695 | INFO     | core.auth.api_service:login:127 - 用户登录: account=demo01
2025-07-15 10:03:53.918 | INFO     | core.auth.api_service:get_user_info:347 - 获取用户信息
2025-07-15 10:03:54.124 | INFO     | core.auth.api_service:get_user_info:375 - 获取用户信息成功
2025-07-15 10:03:54.131 | INFO     | __main__:on_auth_completed:91 - 用户登录成功，正在启动主窗口...
2025-07-15 10:03:54.155 | INFO     | core.telegram.client_worker:__init__:61 - Telegram客户端工作线程初始化
2025-07-15 10:03:54.155 | INFO     | utils.vip_checker:initialize:25 - VIP检查器已初始化，等待登录后开始检查
2025-07-15 10:03:54.156 | INFO     | ui.main_window:_initialize_core_components:108 - MainWindow: 初始化核心组件...
2025-07-15 10:03:54.156 | INFO     | core.telegram.client_worker:run:65 - Telegram客户端工作线程开始运行
2025-07-15 10:03:54.158 | INFO     | app.services.account_service:__init__:42 - 账户服务初始化
2025-07-15 10:03:54.158 | INFO     | core.proxy.proxy_task_manager:run:284 - 代理任务管理器线程启动
2025-07-15 10:03:54.158 | INFO     | app.controllers.account_controller:__init__:81 - 账户控制器初始化
2025-07-15 10:03:54.159 | INFO     | core.telegram.client_manager:__init__:66 - Telegram客户端管理器初始化
2025-07-15 10:03:54.159 | INFO     | app.services.monitor_service:__init__:39 - 初始化监控任务服务
2025-07-15 10:03:54.159 | INFO     | core.telegram.user_manager:__init__:30 - Telegram用户管理器初始化
2025-07-15 10:03:54.160 | INFO     | core.monitor.filter:__init__:26 - 初始化消息过滤器
2025-07-15 10:03:54.160 | INFO     | core.telegram.group_manager:__init__:36 - Telegram群组管理器初始化
2025-07-15 10:03:54.161 | INFO     | core.monitor.extractor:__init__:24 - 初始化关键词提取器
2025-07-15 10:03:54.161 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-07-15 10:03:54.161 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-07-15 10:03:54.162 | INFO     | core.telegram.monitor_manager:__init__:26 - Telegram监控管理器初始化
2025-07-15 10:03:54.162 | INFO     | core.monitor.notifier:__init__:90 - 初始化通知管理器
2025-07-15 10:03:54.163 | INFO     | core.telegram.telegram_service:__init__:39 - Telegram服务初始化完成
2025-07-15 10:03:54.163 | INFO     | app.controllers.telegram_monitor_controller:__init__:64 - 初始化 Telegram 监控控制器
2025-07-15 10:03:54.164 | DEBUG    | app.controllers.telegram_monitor_controller:__init__:82 - 控制器初始化完成
2025-07-15 10:03:54.384 | INFO     | ui.views.monitor_view:__init__:48 - 初始化监控视图
2025-07-15 10:03:54.385 | DEBUG    | ui.views.monitor_view:_connect_signals:80 - 连接控制器信号
2025-07-15 10:03:54.549 | INFO     | ui.views.proxy_view:__init__:96 - 初始化视图，加载第一页代理数据
2025-07-15 10:03:54.771 | INFO     | __main__:on_auth_completed:95 - 主窗口已启动
2025-07-15 10:03:54.820 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:88 - 尝试连接监听日志信号...
2025-07-15 10:03:54.821 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:90 - 监听日志信号连接成功
2025-07-15 10:03:54.822 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-15 10:03:54.826 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-15 10:03:54.832 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:145 - 加载监控任务列表 (搜索关键词: '')
2025-07-15 10:03:54.832 | DEBUG    | ui.views.monitor_view:_on_loading_started:620 - 加载中: 正在加载任务列表...
2025-07-15 10:03:54.833 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-15 10:03:54.840 | INFO     | app.services.proxy_service:refresh_proxies:712 - 刷新代理列表: offset=0, limit=20
2025-07-15 10:03:54.841 | INFO     | core.proxy.proxy_task_manager:submit_task:172 - 提交耗时任务到子线程: refresh_proxies_0_20
2025-07-15 10:03:54.841 | DEBUG    | app.services.proxy_service:get_proxies:500 - 获取代理列表: offset=0, limit=20
2025-07-15 10:03:54.842 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-15 10:03:54.842 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-15 10:03:54.845 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-15 10:03:54.869 | INFO     | app.controllers.invite_controller:get_invite_tasks:38 - 获取邀请任务列表, status=None
2025-07-15 10:03:54.870 | INFO     | app.services.invite_service:get_invite_tasks:121 - 获取邀请任务列表, status=None
2025-07-15 10:03:54.872 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-15 10:03:54.873 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-15 10:03:54.873 | INFO     | app.services.invite_service:task_initialize:39 - 初始化邀请服务，重置运行中的任务为暂停状态
2025-07-15 10:03:54.874 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-15 10:03:54.875 | INFO     | core.proxy.proxy_task_manager:task_wrapper:199 - 任务 refresh_proxies_0_20 完成
2025-07-15 10:03:54.878 | INFO     | utils.vip_checker:start_checking:37 - 开始VIP状态检查
2025-07-15 10:03:54.878 | DEBUG    | utils.vip_checker:_check_vip_status:61 - 正在检查VIP状态...
2025-07-15 10:03:54.878 | INFO     | core.auth.api_service:verify_vip:425 - 会员验证接口调用
2025-07-15 10:03:55.075 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-15 10:03:55.079 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-15 10:03:55.084 | DEBUG    | ui.views.monitor_view:_refresh_task_list:175 - 刷新任务列表，共0个任务
2025-07-15 10:03:55.085 | INFO     | ui.views.monitor_view:_refresh_task_list:189 - 没有找到监控任务
2025-07-15 10:03:55.085 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-15 10:03:55.086 | INFO     | app.services.invite_service:task_initialize:48 - 成功重置 0 个任务状态
2025-07-15 10:03:55.087 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-15 10:03:55.088 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-15 10:03:55.092 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-15 10:03:55.094 | INFO     | app.services.account_service:get_all_groups:96 - 获取所有账户分组成功, 共 2 个
2025-07-15 10:03:55.095 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-15 10:03:55.096 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-15 10:03:55.100 | DEBUG    | ui.views.account_view:_on_groups_loaded:508 - 分组加载完成: 2个分组
2025-07-15 10:03:55.114 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-15 10:03:55.174 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-15 10:03:55.177 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-15 10:03:55.178 | INFO     | app.controllers.telegram_monitor_controller:_load_total_users_count_internal:191 - 成功加载总体统计数据: 总用户数 23, 今日采集 0, 日均采集 23, 运行天数 1
2025-07-15 10:03:55.179 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:156 - 成功加载 0 个任务
2025-07-15 10:03:55.179 | DEBUG    | ui.views.monitor_view:_on_loading_finished:625 - 加载完成
2025-07-15 10:03:55.180 | INFO     | app.services.account_service:get_all_accounts:290 - 获取所有账户成功, 共 2 个
2025-07-15 10:03:55.181 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-15 10:03:55.183 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-07-15 10:03:55.194 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-15 10:03:55.196 | DEBUG    | ui.views.account_view:_on_accounts_loaded:641 - 账户加载完成: 2个账户
2025-07-15 10:03:55.218 | DEBUG    | ui.views.account_view:_update_group_counts:186 - 分组计数已更新: {2: 1, 1: 1}
2025-07-15 10:03:55.224 | INFO     | ui.views.account_view:_auto_login_accounts:696 - 开始调用控制器进行 2 个账户的自动登录。
2025-07-15 10:03:55.225 | INFO     | app.services.account_service:batch_auto_login:1165 - 服务层：开始批量自动登录 2 个账户, 最大并发: 5
2025-07-15 10:03:55.225 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-15 10:03:55.243 | INFO     | app.services.account_service:batch_auto_login:1204 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-07-15 10:03:55.244 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-15 10:03:55.245 | INFO     | app.services.account_service:get_all_accounts:290 - 获取所有账户成功, 共 2 个
2025-07-15 10:03:55.246 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-15 10:03:55.248 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-15 10:03:55.250 | DEBUG    | ui.views.account_view:_update_group_counts:186 - 分组计数已更新: {2: 1, 1: 1}
2025-07-15 10:03:55.251 | DEBUG    | ui.views.account_view:_init_cache:153 - 账户缓存初始化完成，共 2 个账户
2025-07-15 10:03:55.260 | DEBUG    | ui.views.account_view:_on_accounts_loaded:641 - 账户加载完成: 2个账户
2025-07-15 10:03:55.284 | DEBUG    | ui.views.account_view:_update_group_counts:186 - 分组计数已更新: {2: 1, 1: 1}
2025-07-15 10:03:55.295 | INFO     | app.services.account_service:batch_auto_login:1204 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-07-15 10:03:55.296 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-15 10:03:55.297 | INFO     | app.services.account_service:batch_auto_login:1274 - 服务层：准备调用核心层处理 2 个账户的自动登录。
2025-07-15 10:03:55.298 | INFO     | app.services.account_service:batch_auto_login:1284 - 服务层：设置核心层任务超时为 120 秒。
2025-07-15 10:03:55.298 | INFO     | core.telegram.client_manager:batch_auto_login:1216 - 开始批量自动登录, 共 2 个账户, 最大并发 5
2025-07-15 10:03:55.299 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:259 - 创建客户端: H:\PyProject\TeleTest\APPDATA\sessions\+***********, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-15 10:03:55.300 | DEBUG    | core.telegram.client_manager:get_proxy:96 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-15 10:03:55.301 | INFO     | core.auth.api_service:verify_vip:438 - 会员验证成功
2025-07-15 10:03:55.301 | DEBUG    | core.telegram.client_manager:get_proxy:143 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-07-15 10:03:55.301 | INFO     | utils.vip_checker:_check_vip_status:76 - VIP状态有效
2025-07-15 10:03:55.306 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:259 - 创建客户端: H:\PyProject\TeleTest\APPDATA\sessions\+***********, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-15 10:03:55.306 | DEBUG    | core.telegram.client_manager:get_proxy:96 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-15 10:03:55.308 | DEBUG    | core.telegram.client_manager:get_proxy:143 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-07-15 10:03:55.336 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-15 10:03:55.468 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-15 10:03:55.473 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-07-15 10:03:55.476 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-07-15 10:03:55.478 | INFO     | ui.views.proxy_view:_load_initial_data:1044 - 初始数据加载完成，共 1 个代理
2025-07-15 10:03:55.513 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-15 10:03:58.677 | INFO     | core.telegram.client_manager:_connect_client:207 - 连接成功!
2025-07-15 10:03:58.729 | INFO     | core.telegram.client_manager:_connect_client:207 - 连接成功!
2025-07-15 10:03:59.662 | INFO     | core.telegram.client_manager:_verify_client_authorization:1191 - 账户已授权: +***********
2025-07-15 10:03:59.881 | INFO     | core.telegram.client_manager:_verify_client_authorization:1191 - 账户已授权: +***********
2025-07-15 10:04:01.900 | INFO     | core.telegram.client_manager:batch_auto_login:1365 - 批量登录完成: 总计 2, 成功 2, 失败 0
2025-07-15 10:04:02.338 | INFO     | app.services.account_service:batch_auto_login:1305 - 服务层：核心层登录完成: 成功 2, 失败 0 / 2
2025-07-15 10:04:54.114 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 10:05:54.116 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 10:06:54.113 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 10:07:54.119 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 10:08:54.119 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 10:09:54.116 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 10:10:54.113 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 10:11:27.563 | INFO     | ui.main_window:closeEvent:380 - MainWindow: 接收到关闭事件
2025-07-15 10:11:27.573 | INFO     | ui.main_window:_cleanup_before_quit:266 - MainWindow: 执行清理资源...
2025-07-15 10:11:27.573 | INFO     | core.telegram.client_worker:stop:108 - 正在停止Telegram客户端工作线程
2025-07-15 10:11:27.580 | INFO     | core.telegram.client_manager:cleanup_async:1136 - 开始清理资源并断开所有连接
2025-07-15 10:11:27.581 | INFO     | core.telegram.client_manager:disconnect_all_clients:721 - 正在断开所有客户端连接
2025-07-15 10:11:27.581 | INFO     | core.telegram.client_manager:_safe_disconnect:742 - 正在断开客户端连接: +***********
2025-07-15 10:11:27.581 | INFO     | core.telegram.client_manager:_safe_disconnect:742 - 正在断开客户端连接: +***********
2025-07-15 10:11:27.595 | INFO     | core.telegram.client_manager:_safe_disconnect:746 - 断开客户端连接成功: +***********
2025-07-15 10:11:27.595 | INFO     | core.telegram.client_manager:_safe_disconnect:746 - 断开客户端连接成功: +***********
2025-07-15 10:11:27.596 | INFO     | core.telegram.client_manager:cleanup_async:1155 - 资源清理完成
2025-07-15 10:11:28.094 | INFO     | core.telegram.client_manager:cleanup_async:1136 - 开始清理资源并断开所有连接
2025-07-15 10:11:28.094 | INFO     | core.telegram.client_manager:disconnect_all_clients:721 - 正在断开所有客户端连接
2025-07-15 10:11:28.095 | INFO     | core.telegram.client_manager:cleanup_async:1155 - 资源清理完成
2025-07-15 10:11:28.596 | INFO     | core.telegram.client_worker:run:97 - Telegram客户端工作线程已退出
2025-07-15 10:11:28.597 | INFO     | core.telegram.client_worker:stop:122 - Telegram客户端工作线程已停止
2025-07-15 10:11:28.597 | INFO     | ui.main_window:_cleanup_before_quit:275 - TelegramClientWorker 已停止。
2025-07-15 10:11:28.597 | INFO     | ui.main_window:_cleanup_before_quit:279 - MainWindow 清理完成
2025-07-15 10:11:28.616 | INFO     | __main__:main:109 - 应用程序已正常退出
2025-07-15 10:11:33.942 | INFO     | utils.logger:init_logger:98 - 日志系统初始化完成，默认级别: DEBUG，日志目录: H:\PyProject\TeleTest\APPDATA\logs
2025-07-15 10:11:35.225 | INFO     | data.database:init_database:31 - 初始化数据库连接: H:\PyProject\TeleTest\APPDATA\database\telegram_manager.db
2025-07-15 10:11:35.243 | DEBUG    | data.database:init_database:58 - 创建数据库表
2025-07-15 10:11:35.254 | INFO     | data.database:init_database:61 - 数据库初始化完成
2025-07-15 10:11:35.886 | DEBUG    | app.controllers.auth_controller:initialize_software:43 - 控制器: 初始化软件 app_id=1000, version=1.2.7
2025-07-15 10:11:35.887 | DEBUG    | core.auth.api_service:init_software:55 - 发送初始化请求: https://server.xile188.com/api/user/1000/windowns10/1.2.7/ini, params={}
2025-07-15 10:11:36.123 | DEBUG    | core.auth.api_service:init_software:66 - 初始化数据解密成功: {'new_content': '- 新增功能\n  - 1.新增图片上传功能\n  - 2.新增软件文档\n  - 3.新增软件自动更新，后续更新会自动检测版本更新，无需再下载更新包\n  - 4.新增设置账户信息及发信控制\n- 修复bug\n  - 删除监听任务时，不删除对应任务采集到的数据。\n  - 监听设置群无法发送到目标群\n  - 导出监听数据时应当导出单个任务而非所有数据\n- 优化记录\n  - 优化发信规则，直接调取符合要求的账户发送，无账户时才需要等待延时。\n  - 完善软件设置', 'new_url': None, 'notice': {'id': '2', 'content': '公告内容测试3', 'time': '**********'}}
2025-07-15 10:11:36.130 | INFO     | app.controllers.auth_controller:login:61 - 控制器: 用户登录 account=demo01
2025-07-15 10:11:39.314 | INFO     | core.auth.api_service:login:127 - 用户登录: account=demo01
2025-07-15 10:11:39.610 | INFO     | core.auth.api_service:get_user_info:347 - 获取用户信息
2025-07-15 10:11:39.863 | INFO     | core.auth.api_service:get_user_info:375 - 获取用户信息成功
2025-07-15 10:11:39.870 | INFO     | __main__:on_auth_completed:91 - 用户登录成功，正在启动主窗口...
2025-07-15 10:11:39.896 | INFO     | core.telegram.client_worker:__init__:61 - Telegram客户端工作线程初始化
2025-07-15 10:11:39.896 | INFO     | utils.vip_checker:initialize:25 - VIP检查器已初始化，等待登录后开始检查
2025-07-15 10:11:39.897 | INFO     | ui.main_window:_initialize_core_components:108 - MainWindow: 初始化核心组件...
2025-07-15 10:11:39.897 | INFO     | core.telegram.client_worker:run:65 - Telegram客户端工作线程开始运行
2025-07-15 10:11:39.899 | INFO     | app.services.account_service:__init__:42 - 账户服务初始化
2025-07-15 10:11:39.899 | INFO     | core.proxy.proxy_task_manager:run:284 - 代理任务管理器线程启动
2025-07-15 10:11:39.899 | INFO     | app.controllers.account_controller:__init__:81 - 账户控制器初始化
2025-07-15 10:11:39.900 | INFO     | app.services.monitor_service:__init__:39 - 初始化监控任务服务
2025-07-15 10:11:39.900 | INFO     | core.telegram.client_manager:__init__:66 - Telegram客户端管理器初始化
2025-07-15 10:11:39.900 | INFO     | core.monitor.filter:__init__:26 - 初始化消息过滤器
2025-07-15 10:11:39.901 | INFO     | core.telegram.user_manager:__init__:30 - Telegram用户管理器初始化
2025-07-15 10:11:39.901 | INFO     | core.monitor.extractor:__init__:24 - 初始化关键词提取器
2025-07-15 10:11:39.901 | INFO     | core.telegram.group_manager:__init__:36 - Telegram群组管理器初始化
2025-07-15 10:11:39.902 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-07-15 10:11:39.902 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-07-15 10:11:39.902 | INFO     | core.monitor.notifier:__init__:90 - 初始化通知管理器
2025-07-15 10:11:39.902 | INFO     | core.telegram.monitor_manager:__init__:26 - Telegram监控管理器初始化
2025-07-15 10:11:39.903 | INFO     | app.controllers.telegram_monitor_controller:__init__:64 - 初始化 Telegram 监控控制器
2025-07-15 10:11:39.903 | INFO     | core.telegram.telegram_service:__init__:39 - Telegram服务初始化完成
2025-07-15 10:11:39.903 | DEBUG    | app.controllers.telegram_monitor_controller:__init__:82 - 控制器初始化完成
2025-07-15 10:11:40.097 | INFO     | ui.views.monitor_view:__init__:48 - 初始化监控视图
2025-07-15 10:11:40.097 | DEBUG    | ui.views.monitor_view:_connect_signals:80 - 连接控制器信号
2025-07-15 10:11:40.260 | INFO     | ui.views.proxy_view:__init__:96 - 初始化视图，加载第一页代理数据
2025-07-15 10:11:40.491 | INFO     | __main__:on_auth_completed:95 - 主窗口已启动
2025-07-15 10:11:40.536 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:88 - 尝试连接监听日志信号...
2025-07-15 10:11:40.537 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:90 - 监听日志信号连接成功
2025-07-15 10:11:40.538 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-15 10:11:40.541 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-15 10:11:40.546 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:145 - 加载监控任务列表 (搜索关键词: '')
2025-07-15 10:11:40.546 | DEBUG    | ui.views.monitor_view:_on_loading_started:620 - 加载中: 正在加载任务列表...
2025-07-15 10:11:40.547 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-15 10:11:40.553 | INFO     | app.services.proxy_service:refresh_proxies:712 - 刷新代理列表: offset=0, limit=20
2025-07-15 10:11:40.554 | INFO     | core.proxy.proxy_task_manager:submit_task:172 - 提交耗时任务到子线程: refresh_proxies_0_20
2025-07-15 10:11:40.554 | DEBUG    | app.services.proxy_service:get_proxies:500 - 获取代理列表: offset=0, limit=20
2025-07-15 10:11:40.555 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-15 10:11:40.555 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-15 10:11:40.558 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-15 10:11:40.584 | INFO     | app.controllers.invite_controller:get_invite_tasks:38 - 获取邀请任务列表, status=None
2025-07-15 10:11:40.584 | INFO     | app.services.invite_service:get_invite_tasks:121 - 获取邀请任务列表, status=None
2025-07-15 10:11:40.586 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-15 10:11:40.588 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-15 10:11:40.588 | INFO     | app.services.invite_service:task_initialize:39 - 初始化邀请服务，重置运行中的任务为暂停状态
2025-07-15 10:11:40.589 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-15 10:11:40.590 | INFO     | core.proxy.proxy_task_manager:task_wrapper:199 - 任务 refresh_proxies_0_20 完成
2025-07-15 10:11:40.592 | INFO     | utils.vip_checker:start_checking:37 - 开始VIP状态检查
2025-07-15 10:11:40.592 | DEBUG    | utils.vip_checker:_check_vip_status:61 - 正在检查VIP状态...
2025-07-15 10:11:40.592 | INFO     | core.auth.api_service:verify_vip:425 - 会员验证接口调用
2025-07-15 10:11:40.786 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-15 10:11:40.789 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-15 10:11:40.793 | DEBUG    | ui.views.monitor_view:_refresh_task_list:175 - 刷新任务列表，共0个任务
2025-07-15 10:11:40.793 | INFO     | ui.views.monitor_view:_refresh_task_list:189 - 没有找到监控任务
2025-07-15 10:11:40.794 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-15 10:11:40.796 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-15 10:11:40.796 | INFO     | app.services.invite_service:task_initialize:48 - 成功重置 0 个任务状态
2025-07-15 10:11:40.796 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-15 10:11:40.800 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-15 10:11:40.802 | INFO     | app.services.account_service:get_all_groups:96 - 获取所有账户分组成功, 共 2 个
2025-07-15 10:11:40.803 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-15 10:11:40.805 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-15 10:11:40.806 | DEBUG    | ui.views.account_view:_on_groups_loaded:508 - 分组加载完成: 2个分组
2025-07-15 10:11:40.819 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-15 10:11:40.883 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-15 10:11:40.884 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-15 10:11:40.885 | INFO     | app.services.account_service:get_all_accounts:290 - 获取所有账户成功, 共 2 个
2025-07-15 10:11:40.886 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-15 10:11:40.888 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-07-15 10:11:40.893 | INFO     | app.controllers.telegram_monitor_controller:_load_total_users_count_internal:191 - 成功加载总体统计数据: 总用户数 23, 今日采集 0, 日均采集 23, 运行天数 1
2025-07-15 10:11:40.893 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:156 - 成功加载 0 个任务
2025-07-15 10:11:40.894 | DEBUG    | ui.views.monitor_view:_on_loading_finished:625 - 加载完成
2025-07-15 10:11:40.901 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-15 10:11:40.903 | DEBUG    | ui.views.account_view:_on_accounts_loaded:641 - 账户加载完成: 2个账户
2025-07-15 10:11:40.924 | DEBUG    | ui.views.account_view:_update_group_counts:186 - 分组计数已更新: {2: 1, 1: 1}
2025-07-15 10:11:40.928 | INFO     | ui.views.account_view:_auto_login_accounts:696 - 开始调用控制器进行 2 个账户的自动登录。
2025-07-15 10:11:40.929 | INFO     | app.services.account_service:batch_auto_login:1165 - 服务层：开始批量自动登录 2 个账户, 最大并发: 5
2025-07-15 10:11:40.929 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-15 10:11:40.945 | INFO     | app.services.account_service:get_all_accounts:290 - 获取所有账户成功, 共 2 个
2025-07-15 10:11:40.946 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-15 10:11:40.946 | INFO     | app.services.account_service:batch_auto_login:1204 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-07-15 10:11:40.947 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-15 10:11:40.950 | DEBUG    | ui.views.account_view:_update_group_counts:186 - 分组计数已更新: {2: 1, 1: 1}
2025-07-15 10:11:40.950 | DEBUG    | ui.views.account_view:_init_cache:153 - 账户缓存初始化完成，共 2 个账户
2025-07-15 10:11:40.956 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-15 10:11:40.957 | DEBUG    | ui.views.account_view:_on_accounts_loaded:641 - 账户加载完成: 2个账户
2025-07-15 10:11:40.979 | DEBUG    | ui.views.account_view:_update_group_counts:186 - 分组计数已更新: {2: 1, 1: 1}
2025-07-15 10:11:40.990 | INFO     | app.services.account_service:batch_auto_login:1204 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-07-15 10:11:40.990 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-15 10:11:40.993 | INFO     | app.services.account_service:batch_auto_login:1274 - 服务层：准备调用核心层处理 2 个账户的自动登录。
2025-07-15 10:11:40.993 | INFO     | app.services.account_service:batch_auto_login:1284 - 服务层：设置核心层任务超时为 120 秒。
2025-07-15 10:11:40.993 | INFO     | core.telegram.client_manager:batch_auto_login:1216 - 开始批量自动登录, 共 2 个账户, 最大并发 5
2025-07-15 10:11:40.995 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:259 - 创建客户端: H:\PyProject\TeleTest\APPDATA\sessions\+***********, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-15 10:11:40.996 | DEBUG    | core.telegram.client_manager:get_proxy:96 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-15 10:11:40.997 | DEBUG    | core.telegram.client_manager:get_proxy:143 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-07-15 10:11:41.003 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:259 - 创建客户端: H:\PyProject\TeleTest\APPDATA\sessions\+***********, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-15 10:11:41.004 | DEBUG    | core.telegram.client_manager:get_proxy:96 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-15 10:11:41.004 | INFO     | core.auth.api_service:verify_vip:438 - 会员验证成功
2025-07-15 10:11:41.005 | DEBUG    | core.telegram.client_manager:get_proxy:143 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-07-15 10:11:41.005 | INFO     | utils.vip_checker:_check_vip_status:76 - VIP状态有效
2025-07-15 10:11:41.028 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-15 10:11:41.151 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-15 10:11:41.157 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-07-15 10:11:41.159 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-07-15 10:11:41.162 | INFO     | ui.views.proxy_view:_load_initial_data:1044 - 初始数据加载完成，共 1 个代理
2025-07-15 10:11:41.193 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-15 10:11:44.666 | INFO     | core.telegram.client_manager:_connect_client:207 - 连接成功!
2025-07-15 10:11:45.025 | INFO     | core.telegram.client_manager:_connect_client:207 - 连接成功!
2025-07-15 10:11:45.549 | INFO     | core.telegram.client_manager:_verify_client_authorization:1191 - 账户已授权: +***********
2025-07-15 10:11:46.562 | INFO     | core.telegram.client_manager:_verify_client_authorization:1191 - 账户已授权: +***********
2025-07-15 10:11:48.571 | INFO     | core.telegram.client_manager:batch_auto_login:1365 - 批量登录完成: 总计 2, 成功 2, 失败 0
2025-07-15 10:11:49.046 | INFO     | app.services.account_service:batch_auto_login:1305 - 服务层：核心层登录完成: 成功 2, 失败 0 / 2
2025-07-15 10:12:31.446 | INFO     | ui.main_window:closeEvent:380 - MainWindow: 接收到关闭事件
2025-07-15 10:12:31.461 | INFO     | ui.main_window:_cleanup_before_quit:266 - MainWindow: 执行清理资源...
2025-07-15 10:12:31.462 | INFO     | core.telegram.client_worker:stop:108 - 正在停止Telegram客户端工作线程
2025-07-15 10:12:31.468 | INFO     | core.telegram.client_manager:cleanup_async:1136 - 开始清理资源并断开所有连接
2025-07-15 10:12:31.468 | INFO     | core.telegram.client_manager:disconnect_all_clients:721 - 正在断开所有客户端连接
2025-07-15 10:12:31.469 | INFO     | core.telegram.client_manager:_safe_disconnect:742 - 正在断开客户端连接: +***********
2025-07-15 10:12:31.470 | INFO     | core.telegram.client_manager:_safe_disconnect:742 - 正在断开客户端连接: +***********
2025-07-15 10:12:31.481 | INFO     | core.telegram.client_manager:_safe_disconnect:746 - 断开客户端连接成功: +***********
2025-07-15 10:12:31.481 | INFO     | core.telegram.client_manager:_safe_disconnect:746 - 断开客户端连接成功: +***********
2025-07-15 10:12:31.482 | INFO     | core.telegram.client_manager:cleanup_async:1155 - 资源清理完成
2025-07-15 10:12:31.972 | INFO     | core.telegram.client_manager:cleanup_async:1136 - 开始清理资源并断开所有连接
2025-07-15 10:12:31.972 | INFO     | core.telegram.client_manager:disconnect_all_clients:721 - 正在断开所有客户端连接
2025-07-15 10:12:31.973 | INFO     | core.telegram.client_manager:cleanup_async:1155 - 资源清理完成
2025-07-15 10:12:32.488 | INFO     | core.telegram.client_worker:run:97 - Telegram客户端工作线程已退出
2025-07-15 10:12:32.489 | INFO     | core.telegram.client_worker:stop:122 - Telegram客户端工作线程已停止
2025-07-15 10:12:32.490 | INFO     | ui.main_window:_cleanup_before_quit:275 - TelegramClientWorker 已停止。
2025-07-15 10:12:32.490 | INFO     | ui.main_window:_cleanup_before_quit:279 - MainWindow 清理完成
2025-07-15 10:12:32.507 | INFO     | __main__:main:109 - 应用程序已正常退出
2025-07-15 10:12:37.114 | INFO     | utils.logger:init_logger:98 - 日志系统初始化完成，默认级别: DEBUG，日志目录: H:\PyProject\TeleTest\APPDATA\logs
2025-07-15 10:12:38.386 | INFO     | data.database:init_database:31 - 初始化数据库连接: H:\PyProject\TeleTest\APPDATA\database\telegram_manager.db
2025-07-15 10:12:38.404 | DEBUG    | data.database:init_database:58 - 创建数据库表
2025-07-15 10:12:38.419 | INFO     | data.database:init_database:61 - 数据库初始化完成
2025-07-15 10:12:39.200 | DEBUG    | app.controllers.auth_controller:initialize_software:43 - 控制器: 初始化软件 app_id=1000, version=1.2.7
2025-07-15 10:12:39.200 | DEBUG    | core.auth.api_service:init_software:55 - 发送初始化请求: https://server.xile188.com/api/user/1000/windowns10/1.2.7/ini, params={}
2025-07-15 10:12:39.448 | DEBUG    | core.auth.api_service:init_software:66 - 初始化数据解密成功: {'new_content': '- 新增功能\n  - 1.新增图片上传功能\n  - 2.新增软件文档\n  - 3.新增软件自动更新，后续更新会自动检测版本更新，无需再下载更新包\n  - 4.新增设置账户信息及发信控制\n- 修复bug\n  - 删除监听任务时，不删除对应任务采集到的数据。\n  - 监听设置群无法发送到目标群\n  - 导出监听数据时应当导出单个任务而非所有数据\n- 优化记录\n  - 优化发信规则，直接调取符合要求的账户发送，无账户时才需要等待延时。\n  - 完善软件设置', 'new_url': None, 'notice': {'id': '2', 'content': '公告内容测试3', 'time': '**********'}}
2025-07-15 10:12:39.456 | INFO     | app.controllers.auth_controller:login:61 - 控制器: 用户登录 account=demo01
2025-07-15 10:12:42.363 | INFO     | core.auth.api_service:login:127 - 用户登录: account=demo01
2025-07-15 10:12:42.655 | INFO     | core.auth.api_service:get_user_info:347 - 获取用户信息
2025-07-15 10:12:42.865 | INFO     | core.auth.api_service:get_user_info:375 - 获取用户信息成功
2025-07-15 10:12:42.872 | INFO     | __main__:on_auth_completed:91 - 用户登录成功，正在启动主窗口...
2025-07-15 10:12:42.896 | INFO     | core.telegram.client_worker:__init__:61 - Telegram客户端工作线程初始化
2025-07-15 10:12:42.897 | INFO     | utils.vip_checker:initialize:25 - VIP检查器已初始化，等待登录后开始检查
2025-07-15 10:12:42.897 | INFO     | ui.main_window:_initialize_core_components:108 - MainWindow: 初始化核心组件...
2025-07-15 10:12:42.897 | INFO     | core.telegram.client_worker:run:65 - Telegram客户端工作线程开始运行
2025-07-15 10:12:42.899 | INFO     | app.services.account_service:__init__:42 - 账户服务初始化
2025-07-15 10:12:42.899 | INFO     | core.proxy.proxy_task_manager:run:284 - 代理任务管理器线程启动
2025-07-15 10:12:42.899 | INFO     | app.controllers.account_controller:__init__:81 - 账户控制器初始化
2025-07-15 10:12:42.900 | INFO     | core.telegram.client_manager:__init__:66 - Telegram客户端管理器初始化
2025-07-15 10:12:42.900 | INFO     | app.services.monitor_service:__init__:39 - 初始化监控任务服务
2025-07-15 10:12:42.900 | INFO     | core.telegram.user_manager:__init__:30 - Telegram用户管理器初始化
2025-07-15 10:12:42.901 | INFO     | core.monitor.filter:__init__:26 - 初始化消息过滤器
2025-07-15 10:12:42.901 | INFO     | core.telegram.group_manager:__init__:36 - Telegram群组管理器初始化
2025-07-15 10:12:42.901 | INFO     | core.monitor.extractor:__init__:24 - 初始化关键词提取器
2025-07-15 10:12:42.901 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-07-15 10:12:42.902 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-07-15 10:12:42.902 | INFO     | core.telegram.monitor_manager:__init__:26 - Telegram监控管理器初始化
2025-07-15 10:12:42.902 | INFO     | core.monitor.notifier:__init__:90 - 初始化通知管理器
2025-07-15 10:12:42.902 | INFO     | core.telegram.telegram_service:__init__:39 - Telegram服务初始化完成
2025-07-15 10:12:42.903 | INFO     | app.controllers.telegram_monitor_controller:__init__:64 - 初始化 Telegram 监控控制器
2025-07-15 10:12:42.903 | DEBUG    | app.controllers.telegram_monitor_controller:__init__:82 - 控制器初始化完成
2025-07-15 10:12:43.092 | INFO     | ui.views.monitor_view:__init__:48 - 初始化监控视图
2025-07-15 10:12:43.093 | DEBUG    | ui.views.monitor_view:_connect_signals:80 - 连接控制器信号
2025-07-15 10:12:43.253 | INFO     | ui.views.proxy_view:__init__:96 - 初始化视图，加载第一页代理数据
2025-07-15 10:12:43.473 | INFO     | __main__:on_auth_completed:95 - 主窗口已启动
2025-07-15 10:12:43.520 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:88 - 尝试连接监听日志信号...
2025-07-15 10:12:43.520 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:90 - 监听日志信号连接成功
2025-07-15 10:12:43.521 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-15 10:12:43.526 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-15 10:12:43.532 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:145 - 加载监控任务列表 (搜索关键词: '')
2025-07-15 10:12:43.533 | DEBUG    | ui.views.monitor_view:_on_loading_started:620 - 加载中: 正在加载任务列表...
2025-07-15 10:12:43.533 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-15 10:12:43.540 | INFO     | app.services.proxy_service:refresh_proxies:712 - 刷新代理列表: offset=0, limit=20
2025-07-15 10:12:43.541 | INFO     | core.proxy.proxy_task_manager:submit_task:172 - 提交耗时任务到子线程: refresh_proxies_0_20
2025-07-15 10:12:43.541 | DEBUG    | app.services.proxy_service:get_proxies:500 - 获取代理列表: offset=0, limit=20
2025-07-15 10:12:43.541 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-15 10:12:43.541 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-15 10:12:43.565 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-15 10:12:43.572 | INFO     | app.controllers.invite_controller:get_invite_tasks:38 - 获取邀请任务列表, status=None
2025-07-15 10:12:43.575 | INFO     | app.services.invite_service:get_invite_tasks:121 - 获取邀请任务列表, status=None
2025-07-15 10:12:43.576 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-15 10:12:43.577 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-15 10:12:43.578 | INFO     | app.services.invite_service:task_initialize:39 - 初始化邀请服务，重置运行中的任务为暂停状态
2025-07-15 10:12:43.579 | INFO     | core.proxy.proxy_task_manager:task_wrapper:199 - 任务 refresh_proxies_0_20 完成
2025-07-15 10:12:43.579 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-15 10:12:43.583 | INFO     | utils.vip_checker:start_checking:37 - 开始VIP状态检查
2025-07-15 10:12:43.584 | DEBUG    | utils.vip_checker:_check_vip_status:61 - 正在检查VIP状态...
2025-07-15 10:12:43.584 | INFO     | core.auth.api_service:verify_vip:425 - 会员验证接口调用
2025-07-15 10:12:43.698 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-15 10:12:43.699 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-15 10:12:43.704 | DEBUG    | ui.views.monitor_view:_refresh_task_list:175 - 刷新任务列表，共0个任务
2025-07-15 10:12:43.705 | INFO     | ui.views.monitor_view:_refresh_task_list:189 - 没有找到监控任务
2025-07-15 10:12:43.705 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-15 10:12:43.710 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-15 10:12:43.711 | INFO     | app.services.account_service:get_all_groups:96 - 获取所有账户分组成功, 共 2 个
2025-07-15 10:12:43.711 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-15 10:12:43.714 | INFO     | app.services.invite_service:task_initialize:48 - 成功重置 0 个任务状态
2025-07-15 10:12:43.715 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-15 10:12:43.715 | DEBUG    | ui.views.account_view:_on_groups_loaded:508 - 分组加载完成: 2个分组
2025-07-15 10:12:43.727 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-15 10:12:43.787 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-15 10:12:43.788 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-15 10:12:43.790 | INFO     | app.services.account_service:get_all_accounts:290 - 获取所有账户成功, 共 2 个
2025-07-15 10:12:43.790 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-15 10:12:43.793 | INFO     | app.controllers.telegram_monitor_controller:_load_total_users_count_internal:191 - 成功加载总体统计数据: 总用户数 23, 今日采集 0, 日均采集 23, 运行天数 1
2025-07-15 10:12:43.794 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:156 - 成功加载 0 个任务
2025-07-15 10:12:43.794 | DEBUG    | ui.views.monitor_view:_on_loading_finished:625 - 加载完成
2025-07-15 10:12:43.795 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-15 10:12:43.797 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-15 10:12:43.798 | DEBUG    | ui.views.account_view:_on_accounts_loaded:641 - 账户加载完成: 2个账户
2025-07-15 10:12:43.820 | DEBUG    | ui.views.account_view:_update_group_counts:186 - 分组计数已更新: {2: 1, 1: 1}
2025-07-15 10:12:43.824 | INFO     | ui.views.account_view:_auto_login_accounts:696 - 开始调用控制器进行 2 个账户的自动登录。
2025-07-15 10:12:43.824 | INFO     | app.services.account_service:batch_auto_login:1165 - 服务层：开始批量自动登录 2 个账户, 最大并发: 5
2025-07-15 10:12:43.825 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-15 10:12:43.833 | INFO     | app.services.account_service:get_all_accounts:290 - 获取所有账户成功, 共 2 个
2025-07-15 10:12:43.834 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-15 10:12:43.889 | INFO     | app.services.account_service:batch_auto_login:1204 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-07-15 10:12:43.890 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-15 10:12:43.893 | DEBUG    | ui.views.account_view:_update_group_counts:186 - 分组计数已更新: {2: 1, 1: 1}
2025-07-15 10:12:43.893 | DEBUG    | ui.views.account_view:_init_cache:153 - 账户缓存初始化完成，共 2 个账户
2025-07-15 10:12:43.898 | DEBUG    | ui.views.account_view:_on_accounts_loaded:641 - 账户加载完成: 2个账户
2025-07-15 10:12:43.918 | DEBUG    | ui.views.account_view:_update_group_counts:186 - 分组计数已更新: {2: 1, 1: 1}
2025-07-15 10:12:43.925 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-15 10:12:43.926 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-15 10:12:43.928 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-07-15 10:12:43.942 | INFO     | app.services.account_service:batch_auto_login:1204 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-07-15 10:12:43.942 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-15 10:12:43.944 | INFO     | app.services.account_service:batch_auto_login:1274 - 服务层：准备调用核心层处理 2 个账户的自动登录。
2025-07-15 10:12:43.945 | INFO     | app.services.account_service:batch_auto_login:1284 - 服务层：设置核心层任务超时为 120 秒。
2025-07-15 10:12:43.945 | INFO     | core.telegram.client_manager:batch_auto_login:1216 - 开始批量自动登录, 共 2 个账户, 最大并发 5
2025-07-15 10:12:43.946 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:259 - 创建客户端: H:\PyProject\TeleTest\APPDATA\sessions\+***********, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-15 10:12:43.947 | DEBUG    | core.telegram.client_manager:get_proxy:96 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-15 10:12:43.949 | DEBUG    | core.telegram.client_manager:get_proxy:143 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-07-15 10:12:43.953 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:259 - 创建客户端: H:\PyProject\TeleTest\APPDATA\sessions\+***********, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-15 10:12:43.954 | DEBUG    | core.telegram.client_manager:get_proxy:96 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-15 10:12:43.955 | DEBUG    | core.telegram.client_manager:get_proxy:143 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-07-15 10:12:43.987 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-15 10:12:44.062 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-15 10:12:44.068 | INFO     | core.auth.api_service:verify_vip:438 - 会员验证成功
2025-07-15 10:12:44.069 | INFO     | utils.vip_checker:_check_vip_status:76 - VIP状态有效
2025-07-15 10:12:44.099 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-15 10:12:44.123 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-07-15 10:12:44.125 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-07-15 10:12:44.126 | INFO     | ui.views.proxy_view:_load_initial_data:1044 - 初始数据加载完成，共 1 个代理
2025-07-15 10:12:47.278 | INFO     | core.telegram.client_manager:_connect_client:207 - 连接成功!
2025-07-15 10:12:48.141 | INFO     | core.telegram.client_manager:_verify_client_authorization:1191 - 账户已授权: +***********
2025-07-15 10:12:50.024 | INFO     | core.telegram.client_manager:_connect_client:207 - 连接成功!
2025-07-15 10:12:50.862 | INFO     | core.telegram.client_manager:_verify_client_authorization:1191 - 账户已授权: +***********
2025-07-15 10:12:52.870 | INFO     | core.telegram.client_manager:batch_auto_login:1365 - 批量登录完成: 总计 2, 成功 2, 失败 0
2025-07-15 10:12:52.977 | INFO     | app.services.account_service:batch_auto_login:1305 - 服务层：核心层登录完成: 成功 2, 失败 0 / 2
2025-07-15 10:13:00.230 | INFO     | app.services.account_service:get_proxy_ips:1044 - 获取有效代理IP列表（包含绑定计数）
2025-07-15 10:13:00.231 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-15 10:13:00.245 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-15 10:13:00.247 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-15 10:13:00.252 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-15 10:13:00.254 | INFO     | app.services.account_service:get_proxy_ips:1096 - 成功获取 1 个有效代理（已附带绑定计数）
2025-07-15 10:13:00.254 | INFO     | app.controllers.account_controller:get_proxy_ips:538 - 获取到1个有效代理IP
2025-07-15 10:13:15.170 | INFO     | app.services.account_service:get_proxy_ips:1044 - 获取有效代理IP列表（包含绑定计数）
2025-07-15 10:13:15.171 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-15 10:13:15.176 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-15 10:13:15.177 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-15 10:13:15.181 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-15 10:13:15.183 | INFO     | app.services.account_service:get_proxy_ips:1096 - 成功获取 1 个有效代理（已附带绑定计数）
2025-07-15 10:13:15.183 | INFO     | app.controllers.account_controller:get_proxy_ips:538 - 获取到1个有效代理IP
2025-07-15 10:13:42.872 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 10:14:42.864 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 10:14:44.112 | INFO     | ui.main_window:closeEvent:380 - MainWindow: 接收到关闭事件
2025-07-15 10:14:44.122 | INFO     | ui.main_window:_cleanup_before_quit:266 - MainWindow: 执行清理资源...
2025-07-15 10:14:44.123 | INFO     | core.telegram.client_worker:stop:108 - 正在停止Telegram客户端工作线程
2025-07-15 10:14:44.127 | INFO     | core.telegram.client_manager:cleanup_async:1136 - 开始清理资源并断开所有连接
2025-07-15 10:14:44.128 | INFO     | core.telegram.client_manager:disconnect_all_clients:721 - 正在断开所有客户端连接
2025-07-15 10:14:44.129 | INFO     | core.telegram.client_manager:_safe_disconnect:742 - 正在断开客户端连接: +***********
2025-07-15 10:14:44.130 | INFO     | core.telegram.client_manager:_safe_disconnect:742 - 正在断开客户端连接: +***********
2025-07-15 10:14:44.143 | INFO     | core.telegram.client_manager:_safe_disconnect:746 - 断开客户端连接成功: +***********
2025-07-15 10:14:44.143 | INFO     | core.telegram.client_manager:_safe_disconnect:746 - 断开客户端连接成功: +***********
2025-07-15 10:14:44.144 | INFO     | core.telegram.client_manager:cleanup_async:1155 - 资源清理完成
2025-07-15 10:14:44.638 | INFO     | core.telegram.client_manager:cleanup_async:1136 - 开始清理资源并断开所有连接
2025-07-15 10:14:44.638 | INFO     | core.telegram.client_manager:disconnect_all_clients:721 - 正在断开所有客户端连接
2025-07-15 10:14:44.639 | INFO     | core.telegram.client_manager:cleanup_async:1155 - 资源清理完成
2025-07-15 10:14:45.154 | INFO     | core.telegram.client_worker:run:97 - Telegram客户端工作线程已退出
2025-07-15 10:14:45.155 | INFO     | core.telegram.client_worker:stop:122 - Telegram客户端工作线程已停止
2025-07-15 10:14:45.155 | INFO     | ui.main_window:_cleanup_before_quit:275 - TelegramClientWorker 已停止。
2025-07-15 10:14:45.155 | INFO     | ui.main_window:_cleanup_before_quit:279 - MainWindow 清理完成
2025-07-15 10:14:45.172 | INFO     | __main__:main:109 - 应用程序已正常退出
2025-07-15 13:26:55.919 | INFO     | utils.logger:init_logger:98 - 日志系统初始化完成，默认级别: DEBUG，日志目录: H:\PyProject\TeleTest\APPDATA\logs
2025-07-15 13:26:57.315 | INFO     | data.database:init_database:31 - 初始化数据库连接: H:\PyProject\TeleTest\APPDATA\database\telegram_manager.db
2025-07-15 13:26:57.332 | DEBUG    | data.database:init_database:58 - 创建数据库表
2025-07-15 13:26:57.348 | INFO     | data.database:init_database:61 - 数据库初始化完成
2025-07-15 13:26:58.343 | DEBUG    | app.controllers.auth_controller:initialize_software:43 - 控制器: 初始化软件 app_id=1000, version=1.2.7
2025-07-15 13:26:58.343 | DEBUG    | core.auth.api_service:init_software:55 - 发送初始化请求: https://server.xile188.com/api/user/1000/windowns10/1.2.7/ini, params={}
2025-07-15 13:26:58.861 | DEBUG    | core.auth.api_service:init_software:66 - 初始化数据解密成功: {'new_content': '- 新增功能\n  - 1.新增图片上传功能\n  - 2.新增软件文档\n  - 3.新增软件自动更新，后续更新会自动检测版本更新，无需再下载更新包\n  - 4.新增设置账户信息及发信控制\n- 修复bug\n  - 删除监听任务时，不删除对应任务采集到的数据。\n  - 监听设置群无法发送到目标群\n  - 导出监听数据时应当导出单个任务而非所有数据\n- 优化记录\n  - 优化发信规则，直接调取符合要求的账户发送，无账户时才需要等待延时。\n  - 完善软件设置', 'new_url': None, 'notice': {'id': '2', 'content': '公告内容测试3', 'time': '**********'}}
2025-07-15 13:26:58.868 | INFO     | app.controllers.auth_controller:login:61 - 控制器: 用户登录 account=demo01
2025-07-15 13:27:01.877 | INFO     | core.auth.api_service:login:127 - 用户登录: account=demo01
2025-07-15 13:27:02.191 | INFO     | core.auth.api_service:get_user_info:347 - 获取用户信息
2025-07-15 13:27:02.399 | INFO     | core.auth.api_service:get_user_info:375 - 获取用户信息成功
2025-07-15 13:27:02.407 | INFO     | __main__:on_auth_completed:91 - 用户登录成功，正在启动主窗口...
2025-07-15 13:27:02.434 | INFO     | core.telegram.client_worker:__init__:61 - Telegram客户端工作线程初始化
2025-07-15 13:27:02.434 | INFO     | utils.vip_checker:initialize:25 - VIP检查器已初始化，等待登录后开始检查
2025-07-15 13:27:02.435 | INFO     | ui.main_window:_initialize_core_components:108 - MainWindow: 初始化核心组件...
2025-07-15 13:27:02.435 | INFO     | core.telegram.client_worker:run:65 - Telegram客户端工作线程开始运行
2025-07-15 13:27:02.436 | INFO     | app.services.account_service:__init__:42 - 账户服务初始化
2025-07-15 13:27:02.436 | INFO     | core.proxy.proxy_task_manager:run:284 - 代理任务管理器线程启动
2025-07-15 13:27:02.436 | INFO     | app.controllers.account_controller:__init__:81 - 账户控制器初始化
2025-07-15 13:27:02.437 | INFO     | app.services.monitor_service:__init__:39 - 初始化监控任务服务
2025-07-15 13:27:02.437 | INFO     | core.telegram.client_manager:__init__:66 - Telegram客户端管理器初始化
2025-07-15 13:27:02.437 | INFO     | core.monitor.filter:__init__:26 - 初始化消息过滤器
2025-07-15 13:27:02.437 | INFO     | core.telegram.user_manager:__init__:30 - Telegram用户管理器初始化
2025-07-15 13:27:02.438 | INFO     | core.monitor.extractor:__init__:24 - 初始化关键词提取器
2025-07-15 13:27:02.438 | INFO     | core.telegram.group_manager:__init__:36 - Telegram群组管理器初始化
2025-07-15 13:27:02.438 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-07-15 13:27:02.438 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-07-15 13:27:02.438 | INFO     | core.monitor.notifier:__init__:90 - 初始化通知管理器
2025-07-15 13:27:02.439 | INFO     | core.telegram.monitor_manager:__init__:26 - Telegram监控管理器初始化
2025-07-15 13:27:02.439 | INFO     | app.controllers.telegram_monitor_controller:__init__:64 - 初始化 Telegram 监控控制器
2025-07-15 13:27:02.439 | INFO     | core.telegram.telegram_service:__init__:39 - Telegram服务初始化完成
2025-07-15 13:27:02.439 | DEBUG    | app.controllers.telegram_monitor_controller:__init__:82 - 控制器初始化完成
2025-07-15 13:27:02.673 | INFO     | ui.views.monitor_view:__init__:48 - 初始化监控视图
2025-07-15 13:27:02.673 | DEBUG    | ui.views.monitor_view:_connect_signals:80 - 连接控制器信号
2025-07-15 13:27:02.856 | INFO     | ui.views.proxy_view:__init__:96 - 初始化视图，加载第一页代理数据
2025-07-15 13:27:03.085 | INFO     | __main__:on_auth_completed:95 - 主窗口已启动
2025-07-15 13:27:03.137 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:88 - 尝试连接监听日志信号...
2025-07-15 13:27:03.138 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:90 - 监听日志信号连接成功
2025-07-15 13:27:03.139 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-15 13:27:03.143 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-15 13:27:03.148 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:145 - 加载监控任务列表 (搜索关键词: '')
2025-07-15 13:27:03.149 | DEBUG    | ui.views.monitor_view:_on_loading_started:620 - 加载中: 正在加载任务列表...
2025-07-15 13:27:03.150 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-15 13:27:03.160 | INFO     | app.services.proxy_service:refresh_proxies:712 - 刷新代理列表: offset=0, limit=20
2025-07-15 13:27:03.160 | INFO     | core.proxy.proxy_task_manager:submit_task:172 - 提交耗时任务到子线程: refresh_proxies_0_20
2025-07-15 13:27:03.161 | DEBUG    | app.services.proxy_service:get_proxies:500 - 获取代理列表: offset=0, limit=20
2025-07-15 13:27:03.161 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-15 13:27:03.161 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-15 13:27:03.165 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-15 13:27:03.194 | INFO     | app.controllers.invite_controller:get_invite_tasks:38 - 获取邀请任务列表, status=None
2025-07-15 13:27:03.195 | INFO     | app.services.invite_service:get_invite_tasks:121 - 获取邀请任务列表, status=None
2025-07-15 13:27:03.197 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-15 13:27:03.198 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-15 13:27:03.199 | INFO     | app.services.invite_service:task_initialize:39 - 初始化邀请服务，重置运行中的任务为暂停状态
2025-07-15 13:27:03.199 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-15 13:27:03.201 | INFO     | core.proxy.proxy_task_manager:task_wrapper:199 - 任务 refresh_proxies_0_20 完成
2025-07-15 13:27:03.204 | INFO     | utils.vip_checker:start_checking:37 - 开始VIP状态检查
2025-07-15 13:27:03.204 | DEBUG    | utils.vip_checker:_check_vip_status:61 - 正在检查VIP状态...
2025-07-15 13:27:03.204 | INFO     | core.auth.api_service:verify_vip:425 - 会员验证接口调用
2025-07-15 13:27:03.318 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-15 13:27:03.320 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-15 13:27:03.325 | INFO     | app.services.invite_service:task_initialize:48 - 成功重置 0 个任务状态
2025-07-15 13:27:03.326 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-15 13:27:03.327 | DEBUG    | ui.views.monitor_view:_refresh_task_list:175 - 刷新任务列表，共0个任务
2025-07-15 13:27:03.327 | INFO     | ui.views.monitor_view:_refresh_task_list:189 - 没有找到监控任务
2025-07-15 13:27:03.328 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-15 13:27:03.331 | INFO     | app.services.account_service:get_all_groups:96 - 获取所有账户分组成功, 共 2 个
2025-07-15 13:27:03.332 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-15 13:27:03.333 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-15 13:27:03.336 | DEBUG    | ui.views.account_view:_on_groups_loaded:482 - 分组加载完成: 2个分组
2025-07-15 13:27:03.352 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-15 13:27:03.353 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-15 13:27:03.354 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-15 13:27:03.417 | INFO     | app.services.account_service:get_all_accounts:290 - 获取所有账户成功, 共 2 个
2025-07-15 13:27:03.418 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-15 13:27:03.422 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-15 13:27:03.423 | DEBUG    | ui.views.account_view:_on_accounts_loaded:615 - 账户加载完成: 2个账户
2025-07-15 13:27:03.445 | DEBUG    | ui.views.account_view:_update_group_counts:186 - 分组计数已更新: {2: 1, 1: 1}
2025-07-15 13:27:03.449 | INFO     | ui.views.account_view:_auto_login_accounts:670 - 开始调用控制器进行 2 个账户的自动登录。
2025-07-15 13:27:03.450 | INFO     | app.services.account_service:batch_auto_login:1165 - 服务层：开始批量自动登录 2 个账户, 最大并发: 5
2025-07-15 13:27:03.450 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-15 13:27:03.457 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-15 13:27:03.458 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-15 13:27:03.464 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-07-15 13:27:03.470 | INFO     | app.controllers.telegram_monitor_controller:_load_total_users_count_internal:191 - 成功加载总体统计数据: 总用户数 23, 今日采集 0, 日均采集 23, 运行天数 1
2025-07-15 13:27:03.471 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:156 - 成功加载 0 个任务
2025-07-15 13:27:03.472 | DEBUG    | ui.views.monitor_view:_on_loading_finished:625 - 加载完成
2025-07-15 13:27:03.483 | INFO     | app.services.account_service:batch_auto_login:1204 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-07-15 13:27:03.484 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-15 13:27:03.485 | INFO     | app.services.account_service:get_all_accounts:290 - 获取所有账户成功, 共 2 个
2025-07-15 13:27:03.486 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-15 13:27:03.489 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-15 13:27:03.492 | DEBUG    | ui.views.account_view:_update_group_counts:186 - 分组计数已更新: {2: 1, 1: 1}
2025-07-15 13:27:03.493 | DEBUG    | ui.views.account_view:_init_cache:153 - 账户缓存初始化完成，共 2 个账户
2025-07-15 13:27:03.499 | DEBUG    | ui.views.account_view:_on_accounts_loaded:615 - 账户加载完成: 2个账户
2025-07-15 13:27:03.526 | DEBUG    | ui.views.account_view:_update_group_counts:186 - 分组计数已更新: {2: 1, 1: 1}
2025-07-15 13:27:03.535 | INFO     | core.auth.api_service:verify_vip:438 - 会员验证成功
2025-07-15 13:27:03.536 | INFO     | utils.vip_checker:_check_vip_status:76 - VIP状态有效
2025-07-15 13:27:03.546 | INFO     | app.services.account_service:batch_auto_login:1204 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-07-15 13:27:03.546 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-15 13:27:03.548 | INFO     | app.services.account_service:batch_auto_login:1274 - 服务层：准备调用核心层处理 2 个账户的自动登录。
2025-07-15 13:27:03.549 | INFO     | core.telegram.client_manager:batch_auto_login:1216 - 开始批量自动登录, 共 2 个账户, 最大并发 5
2025-07-15 13:27:03.549 | INFO     | app.services.account_service:batch_auto_login:1284 - 服务层：设置核心层任务超时为 120 秒。
2025-07-15 13:27:03.550 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:259 - 创建客户端: H:\PyProject\TeleTest\APPDATA\sessions\+***********, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-15 13:27:03.551 | DEBUG    | core.telegram.client_manager:get_proxy:96 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-15 13:27:03.551 | DEBUG    | core.telegram.client_manager:get_proxy:143 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-07-15 13:27:03.630 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:259 - 创建客户端: H:\PyProject\TeleTest\APPDATA\sessions\+***********, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-15 13:27:03.630 | DEBUG    | core.telegram.client_manager:get_proxy:96 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-15 13:27:03.631 | DEBUG    | core.telegram.client_manager:get_proxy:143 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-07-15 13:27:03.660 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-15 13:27:03.733 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-15 13:27:03.739 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-07-15 13:27:03.741 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-07-15 13:27:03.744 | INFO     | ui.views.proxy_view:_load_initial_data:1044 - 初始数据加载完成，共 1 个代理
2025-07-15 13:27:03.773 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-15 13:27:07.024 | INFO     | core.telegram.client_manager:_connect_client:207 - 连接成功!
2025-07-15 13:27:08.064 | INFO     | core.telegram.client_manager:_verify_client_authorization:1191 - 账户已授权: +***********
2025-07-15 13:27:09.561 | INFO     | core.telegram.client_manager:_connect_client:207 - 连接成功!
2025-07-15 13:27:10.999 | INFO     | core.telegram.client_manager:_verify_client_authorization:1191 - 账户已授权: +***********
2025-07-15 13:27:13.004 | INFO     | core.telegram.client_manager:batch_auto_login:1365 - 批量登录完成: 总计 2, 成功 2, 失败 0
2025-07-15 13:27:13.640 | INFO     | app.services.account_service:batch_auto_login:1305 - 服务层：核心层登录完成: 成功 2, 失败 0 / 2
2025-07-15 13:27:21.697 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-15 13:27:21.707 | INFO     | app.services.account_service:get_accounts_by_group:312 - 获取分组 2 的账户成功, 共 1 个
2025-07-15 13:27:21.707 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-15 13:27:21.709 | DEBUG    | ui.views.account_view:_on_accounts_loaded:615 - 账户加载完成: 1个账户
2025-07-15 13:27:22.164 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-15 13:27:22.190 | INFO     | app.services.account_service:get_all_accounts:290 - 获取所有账户成功, 共 2 个
2025-07-15 13:27:22.191 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-15 13:27:22.193 | DEBUG    | ui.views.account_view:_on_accounts_loaded:615 - 账户加载完成: 2个账户
2025-07-15 13:27:22.213 | DEBUG    | ui.views.account_view:_update_group_counts:186 - 分组计数已更新: {2: 1, 1: 1}
2025-07-15 13:28:02.391 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 13:29:02.404 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 13:30:02.395 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 13:31:02.395 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 13:32:02.402 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 13:33:02.400 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 13:34:02.394 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 13:35:02.392 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 13:36:02.403 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 13:37:02.391 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 13:38:02.405 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 13:39:02.392 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 13:40:02.392 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 13:41:02.397 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 13:42:02.396 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 13:43:02.394 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 13:44:02.400 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 13:45:02.391 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 13:46:02.396 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 13:47:02.405 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 13:48:02.396 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 13:49:02.396 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 13:50:02.403 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 13:51:02.392 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 13:52:02.399 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 13:53:02.402 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 13:54:02.399 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 13:55:02.392 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 13:56:02.395 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 13:57:02.391 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 13:58:02.400 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 13:59:02.392 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 14:00:02.391 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 14:01:02.393 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 14:02:02.392 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 14:03:02.398 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 14:04:02.398 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 14:05:02.393 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 14:06:02.398 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 14:07:02.392 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 14:08:02.395 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 14:09:02.397 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 14:10:02.392 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 14:11:02.396 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 14:12:02.393 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 14:13:02.393 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 14:14:02.393 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 14:15:02.395 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 14:16:02.392 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 14:17:02.405 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 14:18:02.400 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 14:19:02.393 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 14:20:02.391 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 14:21:02.395 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 14:22:02.392 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 14:23:02.392 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 14:24:02.394 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 14:25:02.396 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 14:26:02.400 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 14:27:02.396 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 14:28:02.400 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 14:29:02.392 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 14:30:02.393 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 14:31:02.396 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 14:32:02.392 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 14:33:02.396 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 14:34:02.391 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 14:35:02.394 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 14:36:02.405 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 14:37:02.394 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 14:38:02.398 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 14:39:02.402 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 14:40:02.395 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 14:41:02.402 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 14:42:02.394 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 14:43:02.399 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 14:44:02.392 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 14:45:02.399 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 14:46:02.397 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 14:47:02.405 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 14:48:02.391 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 14:49:02.405 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 14:50:02.398 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 14:51:02.396 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 14:52:02.392 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 14:53:02.398 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 14:54:02.403 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 14:55:02.399 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 14:56:02.396 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 14:57:02.392 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 14:58:02.395 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 14:59:02.403 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 15:00:02.391 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 15:01:02.405 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 15:02:02.396 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 15:03:02.403 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 15:04:02.398 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 15:05:02.406 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 15:06:02.403 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 15:07:02.401 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 15:08:02.401 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 15:09:02.397 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 15:10:02.396 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 15:11:02.393 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 15:12:02.397 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 15:13:02.392 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 15:14:02.401 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 15:15:02.392 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 15:16:02.398 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 15:17:02.394 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 15:18:02.393 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 15:19:02.407 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 15:20:02.408 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 15:21:02.411 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 15:22:02.420 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 15:23:02.413 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 15:24:02.412 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 15:25:02.420 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 15:26:02.414 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 15:27:02.418 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 15:28:02.418 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 15:29:02.421 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 15:30:02.420 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 15:31:02.409 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 15:32:02.416 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 15:33:02.409 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 15:34:02.421 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 15:35:02.418 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 15:36:02.408 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 15:37:02.418 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 15:38:02.409 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 15:39:02.421 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 15:40:02.413 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 15:41:02.417 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 15:42:02.411 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 15:43:02.413 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 15:44:02.413 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 15:45:02.407 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 15:46:02.413 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 15:47:02.416 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 15:48:02.413 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 15:49:02.412 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 15:50:02.412 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 15:51:02.407 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 15:52:02.410 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 15:53:02.407 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 15:54:02.409 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 15:55:02.408 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 15:56:02.409 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 15:57:02.408 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 15:58:02.409 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 15:59:02.408 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 16:00:02.412 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 16:01:02.413 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 16:02:02.414 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 16:03:02.415 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 16:04:02.409 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 16:05:02.412 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 16:06:02.407 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 16:07:02.415 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 16:08:02.407 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 16:09:02.408 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 16:10:02.414 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 16:11:02.416 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 16:12:02.410 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 16:13:02.409 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 16:14:02.410 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 16:15:02.409 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 16:16:02.412 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 16:17:02.410 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 16:18:02.423 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 16:19:02.408 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 16:20:02.422 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 16:21:02.415 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 16:22:02.414 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 16:23:02.409 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 16:24:02.417 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 16:25:02.409 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 16:26:02.411 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 16:27:02.411 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 16:28:02.410 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 16:29:02.417 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 16:30:02.414 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 16:31:02.417 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 16:32:02.421 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 16:33:02.413 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 16:34:02.412 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 16:35:02.414 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 16:36:02.418 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 16:37:02.413 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 16:38:02.417 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 16:39:02.412 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 16:40:02.417 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 16:41:02.411 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 16:42:02.419 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 16:43:02.409 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 16:44:02.407 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 16:45:02.417 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 16:46:02.421 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 16:47:02.413 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 16:48:02.413 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 16:49:02.412 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 16:50:02.408 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 16:51:02.411 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 16:52:02.418 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 16:53:02.422 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 16:54:02.407 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 16:55:02.408 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 16:56:02.417 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 16:57:02.409 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 16:58:02.416 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 16:59:02.423 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 17:00:02.434 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 17:01:02.435 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 17:02:02.433 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 17:03:02.423 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 17:04:02.426 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 17:05:02.433 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 17:06:02.427 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 17:07:02.429 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 17:08:02.436 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 17:09:02.437 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 17:10:02.436 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 17:11:02.429 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 17:12:02.434 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 17:13:02.427 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 17:14:02.433 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 17:15:02.429 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-15 17:16:00.310 | INFO     | ui.main_window:closeEvent:380 - MainWindow: 接收到关闭事件
2025-07-15 17:16:00.334 | INFO     | ui.main_window:_cleanup_before_quit:266 - MainWindow: 执行清理资源...
2025-07-15 17:16:00.334 | INFO     | core.telegram.client_worker:stop:108 - 正在停止Telegram客户端工作线程
2025-07-15 17:16:00.338 | INFO     | core.telegram.client_manager:cleanup_async:1136 - 开始清理资源并断开所有连接
2025-07-15 17:16:00.338 | INFO     | core.telegram.client_manager:disconnect_all_clients:721 - 正在断开所有客户端连接
2025-07-15 17:16:00.340 | INFO     | core.telegram.client_manager:_safe_disconnect:742 - 正在断开客户端连接: +***********
2025-07-15 17:16:00.340 | INFO     | core.telegram.client_manager:_safe_disconnect:742 - 正在断开客户端连接: +***********
2025-07-15 17:16:00.361 | INFO     | core.telegram.client_manager:_safe_disconnect:746 - 断开客户端连接成功: +***********
2025-07-15 17:16:00.362 | INFO     | core.telegram.client_manager:_safe_disconnect:746 - 断开客户端连接成功: +***********
2025-07-15 17:16:00.363 | INFO     | core.telegram.client_manager:cleanup_async:1155 - 资源清理完成
2025-07-15 17:16:00.847 | INFO     | core.telegram.client_manager:cleanup_async:1136 - 开始清理资源并断开所有连接
2025-07-15 17:16:00.848 | INFO     | core.telegram.client_manager:disconnect_all_clients:721 - 正在断开所有客户端连接
2025-07-15 17:16:00.848 | INFO     | core.telegram.client_manager:cleanup_async:1155 - 资源清理完成
2025-07-15 17:16:01.360 | INFO     | core.telegram.client_worker:run:97 - Telegram客户端工作线程已退出
2025-07-15 17:16:01.362 | INFO     | core.telegram.client_worker:stop:122 - Telegram客户端工作线程已停止
2025-07-15 17:16:01.362 | INFO     | ui.main_window:_cleanup_before_quit:275 - TelegramClientWorker 已停止。
2025-07-15 17:16:01.363 | INFO     | ui.main_window:_cleanup_before_quit:279 - MainWindow 清理完成
2025-07-15 17:16:01.380 | INFO     | __main__:main:109 - 应用程序已正常退出
