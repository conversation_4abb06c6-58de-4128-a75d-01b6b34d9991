2025-07-19 00:22:19.520 | INFO     | utils.logger:init_logger:98 - 日志系统初始化完成，默认级别: DEBUG，日志目录: H:\PyProject\TeleTest\APPDATA\logs
2025-07-19 00:22:22.168 | INFO     | data.database:init_database:31 - 初始化数据库连接: H:\PyProject\TeleTest\APPDATA\database\telegram_manager.db
2025-07-19 00:22:22.198 | DEBUG    | data.database:init_database:58 - 创建数据库表
2025-07-19 00:22:22.214 | INFO     | data.database:init_database:61 - 数据库初始化完成
2025-07-19 00:22:23.647 | DEBUG    | app.controllers.auth_controller:initialize_software:43 - 控制器: 初始化软件 app_id=1000, version=1.2.7
2025-07-19 00:22:23.647 | DEBUG    | core.auth.api_service:init_software:55 - 发送初始化请求: https://server.xile188.com/api/user/1000/windowns10/1.2.7/ini, params={}
2025-07-19 00:22:24.116 | DEBUG    | core.auth.api_service:init_software:66 - 初始化数据解密成功: {'new_content': '- 新增功能\n  - 1.新增图片上传功能\n  - 2.新增软件文档\n  - 3.新增软件自动更新，后续更新会自动检测版本更新，无需再下载更新包\n  - 4.新增设置账户信息及发信控制\n- 修复bug\n  - 删除监听任务时，不删除对应任务采集到的数据。\n  - 监听设置群无法发送到目标群\n  - 导出监听数据时应当导出单个任务而非所有数据\n- 优化记录\n  - 优化发信规则，直接调取符合要求的账户发送，无账户时才需要等待延时。\n  - 完善软件设置', 'new_url': None, 'notice': {'id': '2', 'content': '公告内容测试3', 'time': '**********'}}
2025-07-19 00:22:24.127 | INFO     | app.controllers.auth_controller:login:61 - 控制器: 用户登录 account=demo01
2025-07-19 00:22:27.419 | INFO     | core.auth.api_service:login:127 - 用户登录: account=demo01
2025-07-19 00:22:27.935 | INFO     | core.auth.api_service:get_user_info:347 - 获取用户信息
2025-07-19 00:22:28.129 | INFO     | core.auth.api_service:get_user_info:375 - 获取用户信息成功
2025-07-19 00:22:28.137 | INFO     | __main__:on_auth_completed:91 - 用户登录成功，正在启动主窗口...
2025-07-19 00:22:28.174 | INFO     | core.telegram.client_worker:__init__:61 - Telegram客户端工作线程初始化
2025-07-19 00:22:28.174 | INFO     | utils.vip_checker:initialize:25 - VIP检查器已初始化，等待登录后开始检查
2025-07-19 00:22:28.174 | INFO     | ui.main_window:_initialize_core_components:108 - MainWindow: 初始化核心组件...
2025-07-19 00:22:28.175 | INFO     | core.telegram.client_worker:run:65 - Telegram客户端工作线程开始运行
2025-07-19 00:22:28.176 | INFO     | core.proxy.proxy_task_manager:run:284 - 代理任务管理器线程启动
2025-07-19 00:22:28.176 | INFO     | app.services.account_service:__init__:42 - 账户服务初始化
2025-07-19 00:22:28.176 | INFO     | app.controllers.account_controller:__init__:81 - 账户控制器初始化
2025-07-19 00:22:28.176 | INFO     | app.services.monitor_service:__init__:39 - 初始化监控任务服务
2025-07-19 00:22:28.177 | INFO     | core.monitor.filter:__init__:26 - 初始化消息过滤器
2025-07-19 00:22:28.177 | INFO     | core.monitor.extractor:__init__:24 - 初始化关键词提取器
2025-07-19 00:22:28.177 | INFO     | core.telegram.client_manager:__init__:66 - Telegram客户端管理器初始化
2025-07-19 00:22:28.177 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-07-19 00:22:28.177 | INFO     | core.telegram.user_manager:__init__:30 - Telegram用户管理器初始化
2025-07-19 00:22:28.177 | INFO     | core.monitor.notifier:__init__:90 - 初始化通知管理器
2025-07-19 00:22:28.178 | INFO     | core.telegram.group_manager:__init__:36 - Telegram群组管理器初始化
2025-07-19 00:22:28.178 | INFO     | app.controllers.telegram_monitor_controller:__init__:64 - 初始化 Telegram 监控控制器
2025-07-19 00:22:28.178 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-07-19 00:22:28.178 | DEBUG    | app.controllers.telegram_monitor_controller:__init__:82 - 控制器初始化完成
2025-07-19 00:22:28.178 | INFO     | core.telegram.monitor_manager:__init__:26 - Telegram监控管理器初始化
2025-07-19 00:22:28.180 | INFO     | core.telegram.telegram_service:__init__:39 - Telegram服务初始化完成
2025-07-19 00:22:28.486 | INFO     | ui.views.monitor_view:__init__:48 - 初始化监控视图
2025-07-19 00:22:28.486 | DEBUG    | ui.views.monitor_view:_connect_signals:80 - 连接控制器信号
2025-07-19 00:22:28.686 | INFO     | ui.views.proxy_view:__init__:96 - 初始化视图，加载第一页代理数据
2025-07-19 00:22:28.977 | INFO     | __main__:on_auth_completed:95 - 主窗口已启动
2025-07-19 00:22:29.050 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:88 - 尝试连接监听日志信号...
2025-07-19 00:22:29.050 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:90 - 监听日志信号连接成功
2025-07-19 00:22:29.051 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-19 00:22:29.055 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-19 00:22:29.059 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:145 - 加载监控任务列表 (搜索关键词: '')
2025-07-19 00:22:29.059 | DEBUG    | ui.views.monitor_view:_on_loading_started:620 - 加载中: 正在加载任务列表...
2025-07-19 00:22:29.059 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-19 00:22:29.066 | INFO     | app.services.proxy_service:refresh_proxies:712 - 刷新代理列表: offset=0, limit=20
2025-07-19 00:22:29.066 | INFO     | core.proxy.proxy_task_manager:submit_task:172 - 提交耗时任务到子线程: refresh_proxies_0_20
2025-07-19 00:22:29.067 | DEBUG    | app.services.proxy_service:get_proxies:500 - 获取代理列表: offset=0, limit=20
2025-07-19 00:22:29.067 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-19 00:22:29.067 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-19 00:22:29.071 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-19 00:22:29.094 | INFO     | app.controllers.invite_controller:get_invite_tasks:38 - 获取邀请任务列表, status=None
2025-07-19 00:22:29.094 | INFO     | app.services.invite_service:get_invite_tasks:121 - 获取邀请任务列表, status=None
2025-07-19 00:22:29.095 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-19 00:22:29.096 | INFO     | app.services.invite_service:task_initialize:39 - 初始化邀请服务，重置运行中的任务为暂停状态
2025-07-19 00:22:29.100 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-19 00:22:29.102 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-19 00:22:29.103 | INFO     | core.proxy.proxy_task_manager:task_wrapper:199 - 任务 refresh_proxies_0_20 完成
2025-07-19 00:22:29.104 | INFO     | utils.vip_checker:start_checking:37 - 开始VIP状态检查
2025-07-19 00:22:29.104 | DEBUG    | utils.vip_checker:_check_vip_status:61 - 正在检查VIP状态...
2025-07-19 00:22:29.104 | INFO     | core.auth.api_service:verify_vip:425 - 会员验证接口调用
2025-07-19 00:22:29.376 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-19 00:22:29.380 | DEBUG    | ui.views.monitor_view:_refresh_task_list:175 - 刷新任务列表，共0个任务
2025-07-19 00:22:29.380 | INFO     | ui.views.monitor_view:_refresh_task_list:189 - 没有找到监控任务
2025-07-19 00:22:29.380 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-19 00:22:29.382 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-19 00:22:29.389 | INFO     | app.services.invite_service:task_initialize:48 - 成功重置 0 个任务状态
2025-07-19 00:22:29.389 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-19 00:22:29.390 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-19 00:22:29.393 | INFO     | app.services.account_service:get_all_groups:96 - 获取所有账户分组成功, 共 2 个
2025-07-19 00:22:29.393 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-19 00:22:29.394 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-19 00:22:29.399 | DEBUG    | ui.views.account_view:_on_groups_loaded:533 - 分组加载完成: 2个分组
2025-07-19 00:22:29.412 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-19 00:22:29.415 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-19 00:22:29.418 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-19 00:22:29.480 | INFO     | app.controllers.telegram_monitor_controller:_load_total_users_count_internal:191 - 成功加载总体统计数据: 总用户数 23, 今日采集 0, 日均采集 23, 运行天数 1
2025-07-19 00:22:29.480 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:156 - 成功加载 0 个任务
2025-07-19 00:22:29.480 | DEBUG    | ui.views.monitor_view:_on_loading_finished:625 - 加载完成
2025-07-19 00:22:29.482 | INFO     | app.services.account_service:get_all_accounts:290 - 获取所有账户成功, 共 2 个
2025-07-19 00:22:29.482 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-19 00:22:29.486 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-19 00:22:29.487 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-19 00:22:29.488 | DEBUG    | ui.views.account_view:_on_accounts_loaded:666 - 账户加载完成: 2个账户
2025-07-19 00:22:29.510 | DEBUG    | ui.views.account_view:_update_group_counts:186 - 分组计数已更新: {2: 1}
2025-07-19 00:22:29.516 | INFO     | ui.views.account_view:_auto_login_accounts:721 - 开始调用控制器进行 2 个账户的自动登录。
2025-07-19 00:22:29.516 | INFO     | app.services.account_service:batch_auto_login:1165 - 服务层：开始批量自动登录 2 个账户, 最大并发: 5
2025-07-19 00:22:29.517 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-19 00:22:29.521 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-07-19 00:22:29.624 | INFO     | core.auth.api_service:verify_vip:438 - 会员验证成功
2025-07-19 00:22:29.625 | INFO     | utils.vip_checker:_check_vip_status:76 - VIP状态有效
2025-07-19 00:22:29.696 | INFO     | app.services.account_service:batch_auto_login:1204 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-07-19 00:22:29.696 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-19 00:22:29.697 | INFO     | app.services.account_service:get_all_accounts:290 - 获取所有账户成功, 共 2 个
2025-07-19 00:22:29.697 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-19 00:22:29.700 | DEBUG    | ui.views.account_view:_update_group_counts:186 - 分组计数已更新: {2: 1}
2025-07-19 00:22:29.700 | DEBUG    | ui.views.account_view:_init_cache:153 - 账户缓存初始化完成，共 2 个账户
2025-07-19 00:22:29.704 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-19 00:22:29.705 | DEBUG    | ui.views.account_view:_on_accounts_loaded:666 - 账户加载完成: 2个账户
2025-07-19 00:22:29.728 | DEBUG    | ui.views.account_view:_update_group_counts:186 - 分组计数已更新: {2: 1}
2025-07-19 00:22:29.738 | INFO     | app.services.account_service:batch_auto_login:1204 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-07-19 00:22:29.738 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-19 00:22:29.739 | INFO     | app.services.account_service:batch_auto_login:1274 - 服务层：准备调用核心层处理 2 个账户的自动登录。
2025-07-19 00:22:29.740 | INFO     | app.services.account_service:batch_auto_login:1284 - 服务层：设置核心层任务超时为 120 秒。
2025-07-19 00:22:29.740 | INFO     | core.telegram.client_manager:batch_auto_login:1230 - 开始批量自动登录, 共 2 个账户, 最大并发 5
2025-07-19 00:22:29.740 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:271 - 创建客户端: H:\PyProject\TeleTest\APPDATA\sessions\+***********, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-19 00:22:29.740 | DEBUG    | core.telegram.client_manager:get_proxy:96 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-19 00:22:29.741 | DEBUG    | core.telegram.client_manager:get_proxy:143 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-07-19 00:22:29.746 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:271 - 创建客户端: H:\PyProject\TeleTest\APPDATA\sessions\+***********, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-19 00:22:29.746 | DEBUG    | core.telegram.client_manager:get_proxy:96 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-19 00:22:29.747 | DEBUG    | core.telegram.client_manager:get_proxy:143 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-07-19 00:22:29.767 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-19 00:22:29.858 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-19 00:22:29.898 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-07-19 00:22:29.900 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-07-19 00:22:29.902 | INFO     | ui.views.proxy_view:_load_initial_data:1044 - 初始数据加载完成，共 1 个代理
2025-07-19 00:22:29.925 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-19 00:22:32.279 | INFO     | core.telegram.client_manager:_connect_client:219 - 连接成功!
2025-07-19 00:22:33.161 | INFO     | core.telegram.client_manager:_verify_client_authorization:1205 - 账户已授权: +***********
2025-07-19 00:22:33.674 | INFO     | core.telegram.client_manager:_connect_client:219 - 连接成功!
2025-07-19 00:22:34.474 | INFO     | core.telegram.client_manager:_verify_client_authorization:1205 - 账户已授权: +***********
2025-07-19 00:22:36.495 | INFO     | core.telegram.client_manager:batch_auto_login:1379 - 批量登录完成: 总计 2, 成功 2, 失败 0
2025-07-19 00:22:36.756 | INFO     | app.services.account_service:batch_auto_login:1305 - 服务层：核心层登录完成: 成功 2, 失败 0 / 2
2025-07-19 00:22:58.559 | INFO     | app.services.account_service:get_proxy_ips:1044 - 获取有效代理IP列表（包含绑定计数）
2025-07-19 00:22:58.560 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-19 00:22:58.598 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-19 00:22:58.600 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-19 00:22:58.603 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-19 00:22:58.603 | INFO     | app.services.account_service:get_proxy_ips:1096 - 成功获取 1 个有效代理（已附带绑定计数）
2025-07-19 00:22:58.603 | INFO     | app.controllers.account_controller:get_proxy_ips:566 - 获取到1个有效代理IP
2025-07-19 00:23:28.118 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-19 00:23:55.561 | INFO     | ui.main_window:closeEvent:380 - MainWindow: 接收到关闭事件
2025-07-19 00:23:55.577 | INFO     | ui.main_window:_cleanup_before_quit:266 - MainWindow: 执行清理资源...
2025-07-19 00:23:55.577 | INFO     | core.telegram.client_worker:stop:108 - 正在停止Telegram客户端工作线程
2025-07-19 00:23:55.579 | INFO     | core.telegram.client_manager:cleanup_async:1150 - 开始清理资源并断开所有连接
2025-07-19 00:23:55.580 | INFO     | core.telegram.client_manager:disconnect_all_clients:735 - 正在断开所有客户端连接
2025-07-19 00:23:55.580 | INFO     | core.telegram.client_manager:_safe_disconnect:756 - 正在断开客户端连接: +***********
2025-07-19 00:23:55.580 | INFO     | core.telegram.client_manager:_safe_disconnect:756 - 正在断开客户端连接: +***********
2025-07-19 00:23:55.593 | INFO     | core.telegram.client_manager:_safe_disconnect:760 - 断开客户端连接成功: +***********
2025-07-19 00:23:55.593 | INFO     | core.telegram.client_manager:_safe_disconnect:760 - 断开客户端连接成功: +***********
2025-07-19 00:23:55.593 | INFO     | core.telegram.client_manager:cleanup_async:1169 - 资源清理完成
2025-07-19 00:23:56.088 | INFO     | core.telegram.client_manager:cleanup_async:1150 - 开始清理资源并断开所有连接
2025-07-19 00:23:56.088 | INFO     | core.telegram.client_manager:disconnect_all_clients:735 - 正在断开所有客户端连接
2025-07-19 00:23:56.088 | INFO     | core.telegram.client_manager:cleanup_async:1169 - 资源清理完成
2025-07-19 00:23:56.594 | INFO     | core.telegram.client_worker:run:97 - Telegram客户端工作线程已退出
2025-07-19 00:23:56.594 | INFO     | core.telegram.client_worker:stop:122 - Telegram客户端工作线程已停止
2025-07-19 00:23:56.594 | INFO     | ui.main_window:_cleanup_before_quit:275 - TelegramClientWorker 已停止。
2025-07-19 00:23:56.595 | INFO     | ui.main_window:_cleanup_before_quit:279 - MainWindow 清理完成
2025-07-19 00:23:56.612 | INFO     | __main__:main:109 - 应用程序已正常退出
2025-07-19 11:17:49.260 | INFO     | utils.logger:init_logger:98 - 日志系统初始化完成，默认级别: DEBUG，日志目录: H:\PyProject\TeleTest\APPDATA\logs
2025-07-19 11:17:51.344 | INFO     | data.database:init_database:31 - 初始化数据库连接: H:\PyProject\TeleTest\APPDATA\database\telegram_manager.db
2025-07-19 11:17:51.374 | DEBUG    | data.database:init_database:58 - 创建数据库表
2025-07-19 11:17:51.387 | INFO     | data.database:init_database:61 - 数据库初始化完成
2025-07-19 11:17:53.003 | DEBUG    | app.controllers.auth_controller:initialize_software:43 - 控制器: 初始化软件 app_id=1000, version=1.2.7
2025-07-19 11:17:53.003 | DEBUG    | core.auth.api_service:init_software:55 - 发送初始化请求: https://server.xile188.com/api/user/1000/windowns10/1.2.7/ini, params={}
2025-07-19 11:17:53.472 | DEBUG    | core.auth.api_service:init_software:66 - 初始化数据解密成功: {'new_content': '- 新增功能\n  - 1.新增图片上传功能\n  - 2.新增软件文档\n  - 3.新增软件自动更新，后续更新会自动检测版本更新，无需再下载更新包\n  - 4.新增设置账户信息及发信控制\n- 修复bug\n  - 删除监听任务时，不删除对应任务采集到的数据。\n  - 监听设置群无法发送到目标群\n  - 导出监听数据时应当导出单个任务而非所有数据\n- 优化记录\n  - 优化发信规则，直接调取符合要求的账户发送，无账户时才需要等待延时。\n  - 完善软件设置', 'new_url': None, 'notice': {'id': '2', 'content': '公告内容测试3', 'time': '**********'}}
2025-07-19 11:17:53.484 | INFO     | app.controllers.auth_controller:login:61 - 控制器: 用户登录 account=demo01
2025-07-19 11:17:56.504 | INFO     | core.auth.api_service:login:127 - 用户登录: account=demo01
2025-07-19 11:17:56.996 | INFO     | core.auth.api_service:get_user_info:347 - 获取用户信息
2025-07-19 11:17:57.254 | INFO     | core.auth.api_service:get_user_info:375 - 获取用户信息成功
2025-07-19 11:17:57.262 | INFO     | __main__:on_auth_completed:91 - 用户登录成功，正在启动主窗口...
2025-07-19 11:17:57.292 | INFO     | core.telegram.client_worker:__init__:61 - Telegram客户端工作线程初始化
2025-07-19 11:17:57.292 | INFO     | utils.vip_checker:initialize:25 - VIP检查器已初始化，等待登录后开始检查
2025-07-19 11:17:57.292 | INFO     | ui.main_window:_initialize_core_components:108 - MainWindow: 初始化核心组件...
2025-07-19 11:17:57.293 | INFO     | core.telegram.client_worker:run:65 - Telegram客户端工作线程开始运行
2025-07-19 11:17:57.293 | INFO     | app.services.account_service:__init__:42 - 账户服务初始化
2025-07-19 11:17:57.293 | INFO     | core.proxy.proxy_task_manager:run:284 - 代理任务管理器线程启动
2025-07-19 11:17:57.294 | INFO     | app.controllers.account_controller:__init__:81 - 账户控制器初始化
2025-07-19 11:17:57.294 | INFO     | app.services.monitor_service:__init__:39 - 初始化监控任务服务
2025-07-19 11:17:57.294 | INFO     | core.monitor.filter:__init__:26 - 初始化消息过滤器
2025-07-19 11:17:57.294 | INFO     | core.monitor.extractor:__init__:24 - 初始化关键词提取器
2025-07-19 11:17:57.294 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-07-19 11:17:57.295 | INFO     | core.monitor.notifier:__init__:90 - 初始化通知管理器
2025-07-19 11:17:57.295 | INFO     | core.telegram.client_manager:__init__:66 - Telegram客户端管理器初始化
2025-07-19 11:17:57.295 | INFO     | app.controllers.telegram_monitor_controller:__init__:64 - 初始化 Telegram 监控控制器
2025-07-19 11:17:57.295 | INFO     | core.telegram.user_manager:__init__:30 - Telegram用户管理器初始化
2025-07-19 11:17:57.295 | DEBUG    | app.controllers.telegram_monitor_controller:__init__:82 - 控制器初始化完成
2025-07-19 11:17:57.295 | INFO     | core.telegram.group_manager:__init__:36 - Telegram群组管理器初始化
2025-07-19 11:17:57.297 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-07-19 11:17:57.297 | INFO     | core.telegram.monitor_manager:__init__:26 - Telegram监控管理器初始化
2025-07-19 11:17:57.297 | INFO     | core.telegram.telegram_service:__init__:39 - Telegram服务初始化完成
2025-07-19 11:17:57.530 | INFO     | ui.views.monitor_view:__init__:48 - 初始化监控视图
2025-07-19 11:17:57.531 | DEBUG    | ui.views.monitor_view:_connect_signals:80 - 连接控制器信号
2025-07-19 11:17:57.708 | INFO     | ui.views.proxy_view:__init__:96 - 初始化视图，加载第一页代理数据
2025-07-19 11:17:57.973 | INFO     | __main__:on_auth_completed:95 - 主窗口已启动
2025-07-19 11:17:58.150 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:88 - 尝试连接监听日志信号...
2025-07-19 11:17:58.150 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:90 - 监听日志信号连接成功
2025-07-19 11:17:58.151 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-19 11:17:58.177 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-19 11:17:58.201 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:145 - 加载监控任务列表 (搜索关键词: '')
2025-07-19 11:17:58.201 | DEBUG    | ui.views.monitor_view:_on_loading_started:620 - 加载中: 正在加载任务列表...
2025-07-19 11:17:58.201 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-19 11:17:58.208 | INFO     | app.services.proxy_service:refresh_proxies:712 - 刷新代理列表: offset=0, limit=20
2025-07-19 11:17:58.208 | INFO     | core.proxy.proxy_task_manager:submit_task:172 - 提交耗时任务到子线程: refresh_proxies_0_20
2025-07-19 11:17:58.208 | DEBUG    | app.services.proxy_service:get_proxies:500 - 获取代理列表: offset=0, limit=20
2025-07-19 11:17:58.208 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-19 11:17:58.208 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-19 11:17:58.213 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-19 11:17:58.236 | INFO     | app.controllers.invite_controller:get_invite_tasks:38 - 获取邀请任务列表, status=None
2025-07-19 11:17:58.236 | INFO     | app.services.invite_service:get_invite_tasks:121 - 获取邀请任务列表, status=None
2025-07-19 11:17:58.236 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-19 11:17:58.237 | INFO     | app.services.invite_service:task_initialize:39 - 初始化邀请服务，重置运行中的任务为暂停状态
2025-07-19 11:17:58.244 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-19 11:17:58.247 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-19 11:17:58.249 | INFO     | core.proxy.proxy_task_manager:task_wrapper:199 - 任务 refresh_proxies_0_20 完成
2025-07-19 11:17:58.253 | INFO     | utils.vip_checker:start_checking:37 - 开始VIP状态检查
2025-07-19 11:17:58.254 | DEBUG    | utils.vip_checker:_check_vip_status:61 - 正在检查VIP状态...
2025-07-19 11:17:58.254 | INFO     | core.auth.api_service:verify_vip:425 - 会员验证接口调用
2025-07-19 11:17:58.418 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-19 11:17:58.419 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-19 11:17:58.425 | INFO     | app.services.invite_service:task_initialize:48 - 成功重置 0 个任务状态
2025-07-19 11:17:58.425 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-19 11:17:58.425 | DEBUG    | ui.views.monitor_view:_refresh_task_list:175 - 刷新任务列表，共0个任务
2025-07-19 11:17:58.425 | INFO     | ui.views.monitor_view:_refresh_task_list:189 - 没有找到监控任务
2025-07-19 11:17:58.425 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-19 11:17:58.427 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-19 11:17:58.429 | INFO     | app.services.account_service:get_all_groups:96 - 获取所有账户分组成功, 共 2 个
2025-07-19 11:17:58.429 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-19 11:17:58.431 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-19 11:17:58.433 | DEBUG    | ui.views.account_view:_on_groups_loaded:533 - 分组加载完成: 2个分组
2025-07-19 11:17:58.448 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-19 11:17:58.451 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-19 11:17:58.520 | INFO     | app.services.account_service:get_all_accounts:290 - 获取所有账户成功, 共 2 个
2025-07-19 11:17:58.520 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-19 11:17:58.525 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-19 11:17:58.525 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-19 11:17:58.526 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-19 11:17:58.527 | DEBUG    | ui.views.account_view:_on_accounts_loaded:666 - 账户加载完成: 2个账户
2025-07-19 11:17:58.549 | DEBUG    | ui.views.account_view:_update_group_counts:186 - 分组计数已更新: {2: 1}
2025-07-19 11:17:58.554 | INFO     | ui.views.account_view:_auto_login_accounts:721 - 开始调用控制器进行 2 个账户的自动登录。
2025-07-19 11:17:58.554 | INFO     | app.services.account_service:batch_auto_login:1165 - 服务层：开始批量自动登录 2 个账户, 最大并发: 5
2025-07-19 11:17:58.554 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-19 11:17:58.559 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-07-19 11:17:58.572 | INFO     | app.controllers.telegram_monitor_controller:_load_total_users_count_internal:191 - 成功加载总体统计数据: 总用户数 23, 今日采集 0, 日均采集 23, 运行天数 1
2025-07-19 11:17:58.572 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:156 - 成功加载 0 个任务
2025-07-19 11:17:58.572 | DEBUG    | ui.views.monitor_view:_on_loading_finished:625 - 加载完成
2025-07-19 11:17:58.577 | INFO     | app.services.account_service:batch_auto_login:1204 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-07-19 11:17:58.577 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-19 11:17:58.578 | INFO     | app.services.account_service:get_all_accounts:290 - 获取所有账户成功, 共 2 个
2025-07-19 11:17:58.578 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-19 11:17:58.580 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-19 11:17:58.582 | DEBUG    | ui.views.account_view:_update_group_counts:186 - 分组计数已更新: {2: 1}
2025-07-19 11:17:58.582 | DEBUG    | ui.views.account_view:_init_cache:153 - 账户缓存初始化完成，共 2 个账户
2025-07-19 11:17:58.587 | DEBUG    | ui.views.account_view:_on_accounts_loaded:666 - 账户加载完成: 2个账户
2025-07-19 11:17:58.607 | DEBUG    | ui.views.account_view:_update_group_counts:186 - 分组计数已更新: {2: 1}
2025-07-19 11:17:58.610 | INFO     | core.auth.api_service:verify_vip:438 - 会员验证成功
2025-07-19 11:17:58.611 | INFO     | utils.vip_checker:_check_vip_status:76 - VIP状态有效
2025-07-19 11:17:58.623 | INFO     | app.services.account_service:batch_auto_login:1204 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-07-19 11:17:58.623 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-19 11:17:58.625 | INFO     | app.services.account_service:batch_auto_login:1274 - 服务层：准备调用核心层处理 2 个账户的自动登录。
2025-07-19 11:17:58.625 | INFO     | app.services.account_service:batch_auto_login:1284 - 服务层：设置核心层任务超时为 120 秒。
2025-07-19 11:17:58.625 | INFO     | core.telegram.client_manager:batch_auto_login:1230 - 开始批量自动登录, 共 2 个账户, 最大并发 5
2025-07-19 11:17:58.626 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:271 - 创建客户端: H:\PyProject\TeleTest\APPDATA\sessions\+***********, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-19 11:17:58.626 | DEBUG    | core.telegram.client_manager:get_proxy:96 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-19 11:17:58.626 | DEBUG    | core.telegram.client_manager:get_proxy:143 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-07-19 11:17:58.633 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:271 - 创建客户端: H:\PyProject\TeleTest\APPDATA\sessions\+***********, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-19 11:17:58.634 | DEBUG    | core.telegram.client_manager:get_proxy:96 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-19 11:17:58.634 | DEBUG    | core.telegram.client_manager:get_proxy:143 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-07-19 11:17:58.661 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-19 11:17:58.755 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-19 11:17:58.793 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-19 11:17:58.808 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-07-19 11:17:58.810 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-07-19 11:17:58.812 | INFO     | ui.views.proxy_view:_load_initial_data:1044 - 初始数据加载完成，共 1 个代理
2025-07-19 11:18:02.295 | INFO     | core.telegram.client_manager:_connect_client:219 - 连接成功!
2025-07-19 11:18:03.113 | INFO     | core.telegram.client_manager:_connect_client:219 - 连接成功!
2025-07-19 11:18:03.236 | INFO     | core.telegram.client_manager:_verify_client_authorization:1205 - 账户已授权: +***********
2025-07-19 11:18:03.949 | INFO     | core.telegram.client_manager:_verify_client_authorization:1205 - 账户已授权: +***********
2025-07-19 11:18:05.950 | INFO     | core.telegram.client_manager:batch_auto_login:1379 - 批量登录完成: 总计 2, 成功 2, 失败 0
2025-07-19 11:18:06.631 | INFO     | app.services.account_service:batch_auto_login:1305 - 服务层：核心层登录完成: 成功 2, 失败 0 / 2
2025-07-19 11:18:50.072 | INFO     | app.services.account_service:get_proxy_ips:1044 - 获取有效代理IP列表（包含绑定计数）
2025-07-19 11:18:50.073 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-19 11:18:50.287 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-19 11:18:50.287 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-19 11:18:50.291 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-19 11:18:50.291 | INFO     | app.services.account_service:get_proxy_ips:1096 - 成功获取 1 个有效代理（已附带绑定计数）
2025-07-19 11:18:50.291 | INFO     | app.controllers.account_controller:get_proxy_ips:566 - 获取到1个有效代理IP
2025-07-19 11:18:57.241 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-19 11:19:57.253 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-19 11:20:57.240 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-19 11:21:57.247 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-19 11:22:57.248 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-19 11:23:14.416 | INFO     | ui.main_window:closeEvent:380 - MainWindow: 接收到关闭事件
2025-07-19 11:23:14.433 | INFO     | ui.main_window:_cleanup_before_quit:266 - MainWindow: 执行清理资源...
2025-07-19 11:23:14.433 | INFO     | core.telegram.client_worker:stop:108 - 正在停止Telegram客户端工作线程
2025-07-19 11:23:14.437 | INFO     | core.telegram.client_manager:cleanup_async:1150 - 开始清理资源并断开所有连接
2025-07-19 11:23:14.437 | INFO     | core.telegram.client_manager:disconnect_all_clients:735 - 正在断开所有客户端连接
2025-07-19 11:23:14.438 | INFO     | core.telegram.client_manager:_safe_disconnect:756 - 正在断开客户端连接: +***********
2025-07-19 11:23:14.438 | INFO     | core.telegram.client_manager:_safe_disconnect:756 - 正在断开客户端连接: +***********
2025-07-19 11:23:14.452 | INFO     | core.telegram.client_manager:_safe_disconnect:760 - 断开客户端连接成功: +***********
2025-07-19 11:23:14.453 | INFO     | core.telegram.client_manager:_safe_disconnect:760 - 断开客户端连接成功: +***********
2025-07-19 11:23:14.453 | INFO     | core.telegram.client_manager:cleanup_async:1169 - 资源清理完成
2025-07-19 11:23:14.947 | INFO     | core.telegram.client_manager:cleanup_async:1150 - 开始清理资源并断开所有连接
2025-07-19 11:23:14.947 | INFO     | core.telegram.client_manager:disconnect_all_clients:735 - 正在断开所有客户端连接
2025-07-19 11:23:14.947 | INFO     | core.telegram.client_manager:cleanup_async:1169 - 资源清理完成
2025-07-19 11:23:15.450 | INFO     | core.telegram.client_worker:run:97 - Telegram客户端工作线程已退出
2025-07-19 11:23:15.451 | INFO     | core.telegram.client_worker:stop:122 - Telegram客户端工作线程已停止
2025-07-19 11:23:15.451 | INFO     | ui.main_window:_cleanup_before_quit:275 - TelegramClientWorker 已停止。
2025-07-19 11:23:15.451 | INFO     | ui.main_window:_cleanup_before_quit:279 - MainWindow 清理完成
2025-07-19 11:23:15.470 | INFO     | __main__:main:109 - 应用程序已正常退出
