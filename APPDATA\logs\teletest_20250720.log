2025-07-20 00:54:35.046 | INFO     | utils.logger:init_logger:98 - 日志系统初始化完成，默认级别: DEBUG，日志目录: H:\PyProject\TeleTest\APPDATA\logs
2025-07-20 00:54:36.901 | INFO     | data.database:init_database:31 - 初始化数据库连接: H:\PyProject\TeleTest\APPDATA\database\telegram_manager.db
2025-07-20 00:54:36.937 | DEBUG    | data.database:init_database:58 - 创建数据库表
2025-07-20 00:54:36.950 | INFO     | data.database:init_database:61 - 数据库初始化完成
2025-07-20 00:54:38.149 | DEBUG    | app.controllers.auth_controller:initialize_software:43 - 控制器: 初始化软件 app_id=1000, version=1.2.7
2025-07-20 00:54:38.150 | DEBUG    | core.auth.api_service:init_software:55 - 发送初始化请求: https://server.xile188.com/api/user/1000/windowns10/1.2.7/ini, params={}
2025-07-20 00:54:38.857 | DEBUG    | core.auth.api_service:init_software:66 - 初始化数据解密成功: {'new_content': '- 新增功能\n  - 1.新增图片上传功能\n  - 2.新增软件文档\n  - 3.新增软件自动更新，后续更新会自动检测版本更新，无需再下载更新包\n  - 4.新增设置账户信息及发信控制\n- 修复bug\n  - 删除监听任务时，不删除对应任务采集到的数据。\n  - 监听设置群无法发送到目标群\n  - 导出监听数据时应当导出单个任务而非所有数据\n- 优化记录\n  - 优化发信规则，直接调取符合要求的账户发送，无账户时才需要等待延时。\n  - 完善软件设置', 'new_url': None, 'notice': {'id': '2', 'content': '公告内容测试3', 'time': '**********'}}
2025-07-20 00:54:38.869 | INFO     | app.controllers.auth_controller:login:61 - 控制器: 用户登录 account=demo01
2025-07-20 00:54:41.847 | INFO     | core.auth.api_service:login:127 - 用户登录: account=demo01
2025-07-20 00:54:42.472 | INFO     | core.auth.api_service:get_user_info:347 - 获取用户信息
2025-07-20 00:54:42.710 | INFO     | core.auth.api_service:get_user_info:375 - 获取用户信息成功
2025-07-20 00:54:42.730 | INFO     | __main__:on_auth_completed:91 - 用户登录成功，正在启动主窗口...
2025-07-20 00:54:42.765 | INFO     | core.telegram.client_worker:__init__:61 - Telegram客户端工作线程初始化
2025-07-20 00:54:42.765 | INFO     | utils.vip_checker:initialize:25 - VIP检查器已初始化，等待登录后开始检查
2025-07-20 00:54:42.765 | INFO     | ui.main_window:_initialize_core_components:108 - MainWindow: 初始化核心组件...
2025-07-20 00:54:42.766 | INFO     | app.services.account_service:__init__:41 - 账户服务初始化
2025-07-20 00:54:42.766 | INFO     | core.telegram.client_worker:run:65 - Telegram客户端工作线程开始运行
2025-07-20 00:54:42.767 | INFO     | app.controllers.account_controller:__init__:81 - 账户控制器初始化
2025-07-20 00:54:42.767 | INFO     | core.proxy.proxy_task_manager:run:284 - 代理任务管理器线程启动
2025-07-20 00:54:42.767 | INFO     | app.services.monitor_service:__init__:39 - 初始化监控任务服务
2025-07-20 00:54:42.767 | INFO     | core.monitor.filter:__init__:26 - 初始化消息过滤器
2025-07-20 00:54:42.768 | INFO     | core.monitor.extractor:__init__:24 - 初始化关键词提取器
2025-07-20 00:54:42.768 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-07-20 00:54:42.768 | INFO     | core.monitor.notifier:__init__:90 - 初始化通知管理器
2025-07-20 00:54:42.768 | INFO     | app.controllers.telegram_monitor_controller:__init__:64 - 初始化 Telegram 监控控制器
2025-07-20 00:54:42.768 | DEBUG    | app.controllers.telegram_monitor_controller:__init__:82 - 控制器初始化完成
2025-07-20 00:54:42.771 | INFO     | core.telegram.client_manager:__init__:66 - Telegram客户端管理器初始化
2025-07-20 00:54:42.771 | INFO     | core.telegram.user_manager:__init__:30 - Telegram用户管理器初始化
2025-07-20 00:54:42.772 | INFO     | core.telegram.group_manager:__init__:36 - Telegram群组管理器初始化
2025-07-20 00:54:42.774 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-07-20 00:54:42.776 | INFO     | core.telegram.monitor_manager:__init__:26 - Telegram监控管理器初始化
2025-07-20 00:54:42.777 | INFO     | core.telegram.telegram_service:__init__:39 - Telegram服务初始化完成
2025-07-20 00:54:43.054 | INFO     | ui.views.monitor_view:__init__:48 - 初始化监控视图
2025-07-20 00:54:43.054 | DEBUG    | ui.views.monitor_view:_connect_signals:80 - 连接控制器信号
2025-07-20 00:54:43.253 | INFO     | ui.views.proxy_view:__init__:96 - 初始化视图，加载第一页代理数据
2025-07-20 00:54:43.517 | INFO     | __main__:on_auth_completed:95 - 主窗口已启动
2025-07-20 00:54:43.593 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:88 - 尝试连接监听日志信号...
2025-07-20 00:54:43.593 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:90 - 监听日志信号连接成功
2025-07-20 00:54:43.599 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-20 00:54:43.610 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-20 00:54:43.614 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:145 - 加载监控任务列表 (搜索关键词: '')
2025-07-20 00:54:43.614 | DEBUG    | ui.views.monitor_view:_on_loading_started:620 - 加载中: 正在加载任务列表...
2025-07-20 00:54:43.614 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-20 00:54:43.621 | INFO     | app.services.proxy_service:refresh_proxies:712 - 刷新代理列表: offset=0, limit=20
2025-07-20 00:54:43.621 | INFO     | core.proxy.proxy_task_manager:submit_task:172 - 提交耗时任务到子线程: refresh_proxies_0_20
2025-07-20 00:54:43.621 | DEBUG    | app.services.proxy_service:get_proxies:500 - 获取代理列表: offset=0, limit=20
2025-07-20 00:54:43.621 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-20 00:54:43.622 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-20 00:54:43.624 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-20 00:54:43.627 | INFO     | app.controllers.invite_controller:get_invite_tasks:38 - 获取邀请任务列表, status=None
2025-07-20 00:54:43.628 | INFO     | app.services.invite_service:get_invite_tasks:121 - 获取邀请任务列表, status=None
2025-07-20 00:54:43.652 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-20 00:54:43.654 | INFO     | app.services.invite_service:task_initialize:39 - 初始化邀请服务，重置运行中的任务为暂停状态
2025-07-20 00:54:43.654 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-20 00:54:43.659 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-20 00:54:43.660 | INFO     | utils.vip_checker:start_checking:37 - 开始VIP状态检查
2025-07-20 00:54:43.661 | DEBUG    | utils.vip_checker:_check_vip_status:61 - 正在检查VIP状态...
2025-07-20 00:54:43.661 | INFO     | core.auth.api_service:verify_vip:425 - 会员验证接口调用
2025-07-20 00:54:43.662 | INFO     | core.proxy.proxy_task_manager:task_wrapper:199 - 任务 refresh_proxies_0_20 完成
2025-07-20 00:54:43.899 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-20 00:54:43.903 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-20 00:54:43.914 | INFO     | app.services.invite_service:task_initialize:48 - 成功重置 0 个任务状态
2025-07-20 00:54:43.914 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-20 00:54:43.914 | DEBUG    | ui.views.monitor_view:_refresh_task_list:175 - 刷新任务列表，共0个任务
2025-07-20 00:54:43.915 | INFO     | ui.views.monitor_view:_refresh_task_list:189 - 没有找到监控任务
2025-07-20 00:54:43.915 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-20 00:54:43.917 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-20 00:54:43.928 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-20 00:54:43.931 | INFO     | app.services.account_service:get_all_groups:95 - 获取所有账户分组成功, 共 2 个
2025-07-20 00:54:43.931 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-20 00:54:43.944 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-20 00:54:43.944 | DEBUG    | ui.views.account_view:_on_groups_loaded:533 - 分组加载完成: 2个分组
2025-07-20 00:54:43.958 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-20 00:54:44.062 | INFO     | app.services.account_service:get_all_accounts:289 - 获取所有账户成功, 共 2 个
2025-07-20 00:54:44.062 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-20 00:54:44.063 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-20 00:54:44.068 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-20 00:54:44.071 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-20 00:54:44.073 | INFO     | app.controllers.telegram_monitor_controller:_load_total_users_count_internal:191 - 成功加载总体统计数据: 总用户数 23, 今日采集 0, 日均采集 23, 运行天数 1
2025-07-20 00:54:44.073 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:156 - 成功加载 0 个任务
2025-07-20 00:54:44.073 | DEBUG    | ui.views.monitor_view:_on_loading_finished:625 - 加载完成
2025-07-20 00:54:44.073 | DEBUG    | ui.views.account_view:_on_accounts_loaded:666 - 账户加载完成: 2个账户
2025-07-20 00:54:44.097 | DEBUG    | ui.views.account_view:_update_group_counts:186 - 分组计数已更新: {2: 1}
2025-07-20 00:54:44.101 | INFO     | ui.views.account_view:_auto_login_accounts:721 - 开始调用控制器进行 2 个账户的自动登录。
2025-07-20 00:54:44.101 | INFO     | app.services.account_service:batch_auto_login:1164 - 服务层：开始批量自动登录 2 个账户, 最大并发: 5
2025-07-20 00:54:44.101 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-20 00:54:44.104 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-07-20 00:54:44.204 | INFO     | core.auth.api_service:verify_vip:438 - 会员验证成功
2025-07-20 00:54:44.205 | INFO     | utils.vip_checker:_check_vip_status:76 - VIP状态有效
2025-07-20 00:54:44.383 | INFO     | app.services.account_service:get_all_accounts:289 - 获取所有账户成功, 共 2 个
2025-07-20 00:54:44.383 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-20 00:54:44.385 | INFO     | app.services.account_service:batch_auto_login:1203 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-07-20 00:54:44.385 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-20 00:54:44.387 | DEBUG    | ui.views.account_view:_update_group_counts:186 - 分组计数已更新: {2: 1}
2025-07-20 00:54:44.387 | DEBUG    | ui.views.account_view:_init_cache:153 - 账户缓存初始化完成，共 2 个账户
2025-07-20 00:54:44.394 | DEBUG    | ui.views.account_view:_on_accounts_loaded:666 - 账户加载完成: 2个账户
2025-07-20 00:54:44.422 | DEBUG    | ui.views.account_view:_update_group_counts:186 - 分组计数已更新: {2: 1}
2025-07-20 00:54:44.430 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-20 00:54:44.435 | INFO     | app.services.account_service:batch_auto_login:1203 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-07-20 00:54:44.435 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-20 00:54:44.438 | INFO     | app.services.account_service:batch_auto_login:1273 - 服务层：准备调用核心层处理 2 个账户的自动登录。
2025-07-20 00:54:44.438 | INFO     | app.services.account_service:batch_auto_login:1283 - 服务层：设置核心层任务超时为 120 秒。
2025-07-20 00:54:44.438 | INFO     | core.telegram.client_manager:batch_auto_login:1230 - 开始批量自动登录, 共 2 个账户, 最大并发 5
2025-07-20 00:54:44.439 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:271 - 创建客户端: H:\PyProject\TeleTest\APPDATA\sessions\+***********, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-20 00:54:44.439 | DEBUG    | core.telegram.client_manager:get_proxy:96 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-20 00:54:44.439 | DEBUG    | core.telegram.client_manager:get_proxy:143 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-07-20 00:54:44.446 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:271 - 创建客户端: H:\PyProject\TeleTest\APPDATA\sessions\+***********, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-20 00:54:44.446 | DEBUG    | core.telegram.client_manager:get_proxy:96 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-20 00:54:44.446 | DEBUG    | core.telegram.client_manager:get_proxy:143 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-07-20 00:54:44.481 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-20 00:54:44.562 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-20 00:54:44.567 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-07-20 00:54:44.569 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-07-20 00:54:44.570 | INFO     | ui.views.proxy_view:_load_initial_data:1044 - 初始数据加载完成，共 1 个代理
2025-07-20 00:54:44.612 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-20 00:54:46.486 | INFO     | ui.main_window:closeEvent:380 - MainWindow: 接收到关闭事件
2025-07-20 00:54:46.503 | INFO     | ui.main_window:_cleanup_before_quit:266 - MainWindow: 执行清理资源...
2025-07-20 00:54:46.503 | INFO     | core.telegram.client_worker:stop:108 - 正在停止Telegram客户端工作线程
2025-07-20 00:54:46.503 | INFO     | core.telegram.client_worker:_graceful_shutdown:455 - 等待 1 个耗时任务完成...
2025-07-20 00:54:49.692 | INFO     | core.telegram.client_manager:_connect_client:219 - 连接成功!
2025-07-20 00:54:49.854 | INFO     | core.telegram.client_manager:_connect_client:219 - 连接成功!
2025-07-20 00:54:50.764 | INFO     | core.telegram.client_manager:_verify_client_authorization:1205 - 账户已授权: +***********
2025-07-20 00:54:51.006 | INFO     | core.telegram.client_manager:_verify_client_authorization:1205 - 账户已授权: +***********
2025-07-20 00:54:53.014 | INFO     | core.telegram.client_manager:batch_auto_login:1379 - 批量登录完成: 总计 2, 成功 2, 失败 0
2025-07-20 00:54:53.020 | INFO     | core.telegram.client_manager:cleanup_async:1150 - 开始清理资源并断开所有连接
2025-07-20 00:54:53.020 | INFO     | core.telegram.client_manager:disconnect_all_clients:735 - 正在断开所有客户端连接
2025-07-20 00:54:53.020 | INFO     | core.telegram.client_manager:_safe_disconnect:756 - 正在断开客户端连接: +***********
2025-07-20 00:54:53.020 | INFO     | core.telegram.client_manager:_safe_disconnect:756 - 正在断开客户端连接: +***********
2025-07-20 00:54:53.030 | INFO     | core.telegram.client_manager:_safe_disconnect:760 - 断开客户端连接成功: +***********
2025-07-20 00:54:53.030 | INFO     | core.telegram.client_manager:_safe_disconnect:760 - 断开客户端连接成功: +***********
2025-07-20 00:54:53.030 | INFO     | core.telegram.client_manager:cleanup_async:1169 - 资源清理完成
2025-07-20 00:54:53.526 | INFO     | core.telegram.client_manager:cleanup_async:1150 - 开始清理资源并断开所有连接
2025-07-20 00:54:53.526 | INFO     | core.telegram.client_manager:disconnect_all_clients:735 - 正在断开所有客户端连接
2025-07-20 00:54:53.527 | INFO     | core.telegram.client_manager:cleanup_async:1169 - 资源清理完成
2025-07-20 00:54:54.038 | INFO     | core.telegram.client_worker:run:97 - Telegram客户端工作线程已退出
2025-07-20 00:54:54.038 | INFO     | core.telegram.client_worker:stop:122 - Telegram客户端工作线程已停止
2025-07-20 00:54:54.038 | INFO     | ui.main_window:_cleanup_before_quit:275 - TelegramClientWorker 已停止。
2025-07-20 00:54:54.039 | INFO     | ui.main_window:_cleanup_before_quit:279 - MainWindow 清理完成
2025-07-20 00:54:54.057 | INFO     | __main__:main:109 - 应用程序已正常退出
2025-07-20 11:49:52.463 | INFO     | utils.logger:init_logger:98 - 日志系统初始化完成，默认级别: DEBUG，日志目录: H:\PyProject\TeleTest\APPDATA\logs
2025-07-20 11:49:54.600 | INFO     | data.database:init_database:31 - 初始化数据库连接: H:\PyProject\TeleTest\APPDATA\database\telegram_manager.db
2025-07-20 11:49:54.637 | DEBUG    | data.database:init_database:58 - 创建数据库表
2025-07-20 11:49:54.652 | INFO     | data.database:init_database:61 - 数据库初始化完成
2025-07-20 11:49:55.709 | DEBUG    | app.controllers.auth_controller:initialize_software:43 - 控制器: 初始化软件 app_id=1000, version=1.2.7
2025-07-20 11:49:55.710 | DEBUG    | core.auth.api_service:init_software:55 - 发送初始化请求: https://server.xile188.com/api/user/1000/windowns10/1.2.7/ini, params={}
2025-07-20 11:49:56.132 | DEBUG    | core.auth.api_service:init_software:66 - 初始化数据解密成功: {'new_content': '- 新增功能\n  - 1.新增图片上传功能\n  - 2.新增软件文档\n  - 3.新增软件自动更新，后续更新会自动检测版本更新，无需再下载更新包\n  - 4.新增设置账户信息及发信控制\n- 修复bug\n  - 删除监听任务时，不删除对应任务采集到的数据。\n  - 监听设置群无法发送到目标群\n  - 导出监听数据时应当导出单个任务而非所有数据\n- 优化记录\n  - 优化发信规则，直接调取符合要求的账户发送，无账户时才需要等待延时。\n  - 完善软件设置', 'new_url': None, 'notice': {'id': '2', 'content': '公告内容测试3', 'time': '**********'}}
2025-07-20 11:49:56.181 | INFO     | app.controllers.auth_controller:login:61 - 控制器: 用户登录 account=demo01
2025-07-20 11:49:59.218 | INFO     | core.auth.api_service:login:127 - 用户登录: account=demo01
2025-07-20 11:49:59.921 | INFO     | core.auth.api_service:get_user_info:347 - 获取用户信息
2025-07-20 11:50:00.161 | INFO     | core.auth.api_service:get_user_info:375 - 获取用户信息成功
2025-07-20 11:50:00.178 | INFO     | __main__:on_auth_completed:91 - 用户登录成功，正在启动主窗口...
2025-07-20 11:50:00.211 | INFO     | core.telegram.client_worker:__init__:61 - Telegram客户端工作线程初始化
2025-07-20 11:50:00.212 | INFO     | utils.vip_checker:initialize:25 - VIP检查器已初始化，等待登录后开始检查
2025-07-20 11:50:00.212 | INFO     | ui.main_window:_initialize_core_components:108 - MainWindow: 初始化核心组件...
2025-07-20 11:50:00.212 | INFO     | core.telegram.client_worker:run:65 - Telegram客户端工作线程开始运行
2025-07-20 11:50:00.213 | INFO     | app.services.account_service:__init__:41 - 账户服务初始化
2025-07-20 11:50:00.213 | INFO     | core.proxy.proxy_task_manager:run:284 - 代理任务管理器线程启动
2025-07-20 11:50:00.213 | INFO     | app.controllers.account_controller:__init__:81 - 账户控制器初始化
2025-07-20 11:50:00.214 | INFO     | app.services.monitor_service:__init__:39 - 初始化监控任务服务
2025-07-20 11:50:00.214 | INFO     | core.monitor.filter:__init__:26 - 初始化消息过滤器
2025-07-20 11:50:00.214 | INFO     | core.monitor.extractor:__init__:24 - 初始化关键词提取器
2025-07-20 11:50:00.215 | INFO     | core.telegram.client_manager:__init__:66 - Telegram客户端管理器初始化
2025-07-20 11:50:00.216 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-07-20 11:50:00.216 | INFO     | core.telegram.user_manager:__init__:30 - Telegram用户管理器初始化
2025-07-20 11:50:00.216 | INFO     | core.monitor.notifier:__init__:90 - 初始化通知管理器
2025-07-20 11:50:00.216 | INFO     | core.telegram.group_manager:__init__:36 - Telegram群组管理器初始化
2025-07-20 11:50:00.216 | INFO     | app.controllers.telegram_monitor_controller:__init__:64 - 初始化 Telegram 监控控制器
2025-07-20 11:50:00.216 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-07-20 11:50:00.216 | DEBUG    | app.controllers.telegram_monitor_controller:__init__:82 - 控制器初始化完成
2025-07-20 11:50:00.217 | INFO     | core.telegram.monitor_manager:__init__:26 - Telegram监控管理器初始化
2025-07-20 11:50:00.219 | INFO     | core.telegram.telegram_service:__init__:39 - Telegram服务初始化完成
2025-07-20 11:50:00.470 | INFO     | ui.views.monitor_view:__init__:48 - 初始化监控视图
2025-07-20 11:50:00.470 | DEBUG    | ui.views.monitor_view:_connect_signals:80 - 连接控制器信号
2025-07-20 11:50:00.672 | INFO     | ui.views.proxy_view:__init__:96 - 初始化视图，加载第一页代理数据
2025-07-20 11:50:00.958 | INFO     | __main__:on_auth_completed:95 - 主窗口已启动
2025-07-20 11:50:01.024 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:88 - 尝试连接监听日志信号...
2025-07-20 11:50:01.024 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:90 - 监听日志信号连接成功
2025-07-20 11:50:01.025 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-20 11:50:01.029 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-20 11:50:01.033 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:145 - 加载监控任务列表 (搜索关键词: '')
2025-07-20 11:50:01.033 | DEBUG    | ui.views.monitor_view:_on_loading_started:620 - 加载中: 正在加载任务列表...
2025-07-20 11:50:01.033 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-20 11:50:01.039 | INFO     | app.services.proxy_service:refresh_proxies:712 - 刷新代理列表: offset=0, limit=20
2025-07-20 11:50:01.040 | INFO     | core.proxy.proxy_task_manager:submit_task:172 - 提交耗时任务到子线程: refresh_proxies_0_20
2025-07-20 11:50:01.040 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-20 11:50:01.041 | DEBUG    | app.services.proxy_service:get_proxies:500 - 获取代理列表: offset=0, limit=20
2025-07-20 11:50:01.041 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-20 11:50:01.047 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-20 11:50:01.080 | INFO     | app.controllers.invite_controller:get_invite_tasks:38 - 获取邀请任务列表, status=None
2025-07-20 11:50:01.080 | INFO     | app.services.invite_service:get_invite_tasks:121 - 获取邀请任务列表, status=None
2025-07-20 11:50:01.080 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-20 11:50:01.081 | INFO     | app.services.invite_service:task_initialize:39 - 初始化邀请服务，重置运行中的任务为暂停状态
2025-07-20 11:50:01.082 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-20 11:50:01.088 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-20 11:50:01.090 | INFO     | core.proxy.proxy_task_manager:task_wrapper:199 - 任务 refresh_proxies_0_20 完成
2025-07-20 11:50:01.091 | INFO     | utils.vip_checker:start_checking:37 - 开始VIP状态检查
2025-07-20 11:50:01.091 | DEBUG    | utils.vip_checker:_check_vip_status:61 - 正在检查VIP状态...
2025-07-20 11:50:01.091 | INFO     | core.auth.api_service:verify_vip:425 - 会员验证接口调用
2025-07-20 11:50:01.251 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-20 11:50:01.255 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-20 11:50:01.259 | DEBUG    | ui.views.monitor_view:_refresh_task_list:175 - 刷新任务列表，共0个任务
2025-07-20 11:50:01.259 | INFO     | ui.views.monitor_view:_refresh_task_list:189 - 没有找到监控任务
2025-07-20 11:50:01.259 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-20 11:50:01.261 | INFO     | app.services.invite_service:task_initialize:48 - 成功重置 0 个任务状态
2025-07-20 11:50:01.262 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-20 11:50:01.265 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-20 11:50:01.271 | INFO     | app.services.account_service:get_all_groups:95 - 获取所有账户分组成功, 共 2 个
2025-07-20 11:50:01.271 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-20 11:50:01.274 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-20 11:50:01.275 | DEBUG    | ui.views.account_view:_on_groups_loaded:445 - 分组加载完成: 2个分组
2025-07-20 11:50:01.293 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-20 11:50:01.298 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-20 11:50:01.439 | INFO     | app.services.account_service:get_all_accounts:289 - 获取所有账户成功, 共 2 个
2025-07-20 11:50:01.439 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-20 11:50:01.444 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-20 11:50:01.445 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-20 11:50:01.446 | DEBUG    | ui.views.account_view:_on_accounts_loaded:578 - 账户加载完成: 2个账户
2025-07-20 11:50:01.469 | DEBUG    | ui.views.account_view:_update_group_counts:186 - 分组计数已更新: {2: 1}
2025-07-20 11:50:01.473 | INFO     | ui.views.account_view:_auto_login_accounts:633 - 开始调用控制器进行 2 个账户的自动登录。
2025-07-20 11:50:01.473 | INFO     | app.services.account_service:batch_auto_login:1164 - 服务层：开始批量自动登录 2 个账户, 最大并发: 5
2025-07-20 11:50:01.473 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-20 11:50:01.477 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-20 11:50:01.480 | INFO     | app.controllers.telegram_monitor_controller:_load_total_users_count_internal:191 - 成功加载总体统计数据: 总用户数 23, 今日采集 0, 日均采集 23, 运行天数 1
2025-07-20 11:50:01.480 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:156 - 成功加载 0 个任务
2025-07-20 11:50:01.480 | DEBUG    | ui.views.monitor_view:_on_loading_finished:625 - 加载完成
2025-07-20 11:50:01.484 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-07-20 11:50:01.498 | INFO     | app.services.account_service:get_all_accounts:289 - 获取所有账户成功, 共 2 个
2025-07-20 11:50:01.498 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-20 11:50:01.498 | INFO     | app.services.account_service:batch_auto_login:1203 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-07-20 11:50:01.499 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-20 11:50:01.501 | INFO     | core.auth.api_service:verify_vip:438 - 会员验证成功
2025-07-20 11:50:01.501 | INFO     | utils.vip_checker:_check_vip_status:76 - VIP状态有效
2025-07-20 11:50:01.503 | DEBUG    | ui.views.account_view:_update_group_counts:186 - 分组计数已更新: {2: 1}
2025-07-20 11:50:01.503 | DEBUG    | ui.views.account_view:_init_cache:153 - 账户缓存初始化完成，共 2 个账户
2025-07-20 11:50:01.508 | DEBUG    | ui.views.account_view:_on_accounts_loaded:578 - 账户加载完成: 2个账户
2025-07-20 11:50:01.532 | DEBUG    | ui.views.account_view:_update_group_counts:186 - 分组计数已更新: {2: 1}
2025-07-20 11:50:01.539 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-20 11:50:01.553 | INFO     | app.services.account_service:batch_auto_login:1203 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-07-20 11:50:01.553 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-20 11:50:01.555 | INFO     | app.services.account_service:batch_auto_login:1273 - 服务层：准备调用核心层处理 2 个账户的自动登录。
2025-07-20 11:50:01.556 | INFO     | app.services.account_service:batch_auto_login:1283 - 服务层：设置核心层任务超时为 120 秒。
2025-07-20 11:50:01.556 | INFO     | core.telegram.client_manager:batch_auto_login:1230 - 开始批量自动登录, 共 2 个账户, 最大并发 5
2025-07-20 11:50:01.556 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:271 - 创建客户端: H:\PyProject\TeleTest\APPDATA\sessions\+***********, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-20 11:50:01.557 | DEBUG    | core.telegram.client_manager:get_proxy:96 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-20 11:50:01.557 | DEBUG    | core.telegram.client_manager:get_proxy:143 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-07-20 11:50:01.563 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:271 - 创建客户端: H:\PyProject\TeleTest\APPDATA\sessions\+***********, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-20 11:50:01.564 | DEBUG    | core.telegram.client_manager:get_proxy:96 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-20 11:50:01.565 | DEBUG    | core.telegram.client_manager:get_proxy:143 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-07-20 11:50:01.589 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-20 11:50:01.684 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-20 11:50:01.716 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-07-20 11:50:01.718 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-07-20 11:50:01.720 | INFO     | ui.views.proxy_view:_load_initial_data:1044 - 初始数据加载完成，共 1 个代理
2025-07-20 11:50:01.728 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-20 11:50:04.923 | INFO     | core.telegram.client_manager:_connect_client:219 - 连接成功!
2025-07-20 11:50:05.775 | INFO     | core.telegram.client_manager:_verify_client_authorization:1205 - 账户已授权: +***********
2025-07-20 11:50:06.589 | INFO     | core.telegram.client_manager:_connect_client:219 - 连接成功!
2025-07-20 11:50:07.887 | INFO     | core.telegram.client_manager:_verify_client_authorization:1205 - 账户已授权: +***********
2025-07-20 11:50:09.890 | INFO     | core.telegram.client_manager:batch_auto_login:1379 - 批量登录完成: 总计 2, 成功 2, 失败 0
2025-07-20 11:50:10.571 | INFO     | app.services.account_service:batch_auto_login:1304 - 服务层：核心层登录完成: 成功 2, 失败 0 / 2
2025-07-20 11:50:49.626 | INFO     | app.services.account_service:get_proxy_ips:1043 - 获取有效代理IP列表（包含绑定计数）
2025-07-20 11:50:49.626 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-20 11:50:49.682 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-20 11:50:49.683 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-20 11:50:49.847 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-20 11:50:49.849 | INFO     | app.services.account_service:get_proxy_ips:1095 - 成功获取 1 个有效代理（已附带绑定计数）
2025-07-20 11:50:49.849 | INFO     | app.controllers.account_controller:get_proxy_ips:566 - 获取到1个有效代理IP
2025-07-20 11:50:58.668 | INFO     | app.services.account_service:update_account:327 - 更新账户信息: 2
2025-07-20 11:50:58.668 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-20 11:50:58.678 | INFO     | data.repositories.account_repo:update_account:378 - 更新账户成功: 2
2025-07-20 11:50:58.687 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-20 11:51:00.163 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-20 11:51:00.211 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-20 11:51:00.292 | INFO     | app.services.account_service:get_all_accounts:289 - 获取所有账户成功, 共 2 个
2025-07-20 11:51:00.292 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-20 11:51:00.294 | DEBUG    | ui.views.account_view:_on_accounts_loaded:578 - 账户加载完成: 2个账户
2025-07-20 11:51:00.316 | DEBUG    | ui.views.account_view:_update_group_counts:186 - 分组计数已更新: {2: 2}
2025-07-20 11:52:00.158 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-20 11:52:06.390 | INFO     | app.services.account_service:get_proxy_ips:1043 - 获取有效代理IP列表（包含绑定计数）
2025-07-20 11:52:06.390 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-20 11:52:06.626 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-20 11:52:06.627 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-20 11:52:06.637 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-20 11:52:06.637 | INFO     | app.services.account_service:get_proxy_ips:1095 - 成功获取 1 个有效代理（已附带绑定计数）
2025-07-20 11:52:06.638 | INFO     | app.controllers.account_controller:get_proxy_ips:566 - 获取到1个有效代理IP
2025-07-20 11:52:25.372 | INFO     | app.services.account_service:update_account:327 - 更新账户信息: 1
2025-07-20 11:52:25.372 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-20 11:52:25.381 | INFO     | data.repositories.account_repo:update_account:378 - 更新账户成功: 1
2025-07-20 11:52:25.387 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-20 11:52:26.918 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-20 11:52:27.003 | INFO     | app.services.account_service:get_all_accounts:289 - 获取所有账户成功, 共 2 个
2025-07-20 11:52:27.003 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-20 11:52:27.004 | DEBUG    | ui.views.account_view:_on_accounts_loaded:578 - 账户加载完成: 2个账户
2025-07-20 11:52:27.024 | DEBUG    | ui.views.account_view:_update_group_counts:186 - 分组计数已更新: {2: 2}
2025-07-20 11:52:29.478 | INFO     | app.services.account_service:refresh_account_info:606 - 刷新账户信息: 1
2025-07-20 11:52:29.478 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-20 11:52:29.489 | INFO     | core.telegram.user_manager:get_user_info:41 - 正在获取用户信息: +***********
2025-07-20 11:52:30.404 | INFO     | core.telegram.user_manager:get_user_info:66 - 获取用户信息成功: +***********
2025-07-20 11:52:30.742 | INFO     | data.repositories.account_repo:update_account:378 - 更新账户成功: 1
2025-07-20 11:52:30.748 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-20 11:52:44.899 | INFO     | ui.main_window:closeEvent:380 - MainWindow: 接收到关闭事件
2025-07-20 11:52:44.909 | INFO     | ui.main_window:_cleanup_before_quit:266 - MainWindow: 执行清理资源...
2025-07-20 11:52:44.909 | INFO     | core.telegram.client_worker:stop:108 - 正在停止Telegram客户端工作线程
2025-07-20 11:52:44.919 | INFO     | core.telegram.client_manager:cleanup_async:1150 - 开始清理资源并断开所有连接
2025-07-20 11:52:44.919 | INFO     | core.telegram.client_manager:disconnect_all_clients:735 - 正在断开所有客户端连接
2025-07-20 11:52:44.919 | INFO     | core.telegram.client_manager:_safe_disconnect:756 - 正在断开客户端连接: +***********
2025-07-20 11:52:44.920 | INFO     | core.telegram.client_manager:_safe_disconnect:756 - 正在断开客户端连接: +***********
2025-07-20 11:52:44.931 | INFO     | core.telegram.client_manager:_safe_disconnect:760 - 断开客户端连接成功: +***********
2025-07-20 11:52:44.931 | INFO     | core.telegram.client_manager:_safe_disconnect:760 - 断开客户端连接成功: +***********
2025-07-20 11:52:44.931 | INFO     | core.telegram.client_manager:cleanup_async:1169 - 资源清理完成
2025-07-20 11:52:45.418 | INFO     | core.telegram.client_manager:cleanup_async:1150 - 开始清理资源并断开所有连接
2025-07-20 11:52:45.418 | INFO     | core.telegram.client_manager:disconnect_all_clients:735 - 正在断开所有客户端连接
2025-07-20 11:52:45.418 | INFO     | core.telegram.client_manager:cleanup_async:1169 - 资源清理完成
2025-07-20 11:52:45.922 | INFO     | core.telegram.client_worker:run:97 - Telegram客户端工作线程已退出
2025-07-20 11:52:45.924 | INFO     | core.telegram.client_worker:stop:122 - Telegram客户端工作线程已停止
2025-07-20 11:52:45.924 | INFO     | ui.main_window:_cleanup_before_quit:275 - TelegramClientWorker 已停止。
2025-07-20 11:52:45.924 | INFO     | ui.main_window:_cleanup_before_quit:279 - MainWindow 清理完成
2025-07-20 11:52:45.943 | INFO     | __main__:main:109 - 应用程序已正常退出
2025-07-20 15:04:13.102 | INFO     | utils.logger:init_logger:98 - 日志系统初始化完成，默认级别: DEBUG，日志目录: H:\PyProject\TeleTest\APPDATA\logs
2025-07-20 15:04:14.859 | INFO     | data.database:init_database:31 - 初始化数据库连接: H:\PyProject\TeleTest\APPDATA\database\telegram_manager.db
2025-07-20 15:04:14.890 | DEBUG    | data.database:init_database:58 - 创建数据库表
2025-07-20 15:04:14.904 | INFO     | data.database:init_database:61 - 数据库初始化完成
2025-07-20 15:04:16.231 | DEBUG    | app.controllers.auth_controller:initialize_software:43 - 控制器: 初始化软件 app_id=1000, version=1.2.7
2025-07-20 15:04:16.231 | DEBUG    | core.auth.api_service:init_software:55 - 发送初始化请求: https://server.xile188.com/api/user/1000/windowns10/1.2.7/ini, params={}
2025-07-20 15:04:16.758 | DEBUG    | core.auth.api_service:init_software:66 - 初始化数据解密成功: {'new_content': '- 新增功能\n  - 1.新增图片上传功能\n  - 2.新增软件文档\n  - 3.新增软件自动更新，后续更新会自动检测版本更新，无需再下载更新包\n  - 4.新增设置账户信息及发信控制\n- 修复bug\n  - 删除监听任务时，不删除对应任务采集到的数据。\n  - 监听设置群无法发送到目标群\n  - 导出监听数据时应当导出单个任务而非所有数据\n- 优化记录\n  - 优化发信规则，直接调取符合要求的账户发送，无账户时才需要等待延时。\n  - 完善软件设置', 'new_url': None, 'notice': {'id': '2', 'content': '公告内容测试3', 'time': '**********'}}
2025-07-20 15:04:16.770 | INFO     | app.controllers.auth_controller:login:61 - 控制器: 用户登录 account=demo01
2025-07-20 15:04:19.704 | INFO     | core.auth.api_service:login:127 - 用户登录: account=demo01
2025-07-20 15:04:20.281 | INFO     | core.auth.api_service:get_user_info:347 - 获取用户信息
2025-07-20 15:04:21.623 | INFO     | core.auth.api_service:get_user_info:375 - 获取用户信息成功
2025-07-20 15:04:21.631 | INFO     | __main__:on_auth_completed:91 - 用户登录成功，正在启动主窗口...
2025-07-20 15:04:21.663 | INFO     | core.telegram.client_worker:__init__:61 - Telegram客户端工作线程初始化
2025-07-20 15:04:21.663 | INFO     | utils.vip_checker:initialize:25 - VIP检查器已初始化，等待登录后开始检查
2025-07-20 15:04:21.663 | INFO     | ui.main_window:_initialize_core_components:108 - MainWindow: 初始化核心组件...
2025-07-20 15:04:21.663 | INFO     | core.telegram.client_worker:run:65 - Telegram客户端工作线程开始运行
2025-07-20 15:04:21.664 | INFO     | core.proxy.proxy_task_manager:run:284 - 代理任务管理器线程启动
2025-07-20 15:04:21.664 | INFO     | app.services.account_service:__init__:41 - 账户服务初始化
2025-07-20 15:04:21.665 | INFO     | app.controllers.account_controller:__init__:81 - 账户控制器初始化
2025-07-20 15:04:21.665 | INFO     | app.services.monitor_service:__init__:39 - 初始化监控任务服务
2025-07-20 15:04:21.665 | INFO     | core.telegram.client_manager:__init__:66 - Telegram客户端管理器初始化
2025-07-20 15:04:21.665 | INFO     | core.monitor.filter:__init__:26 - 初始化消息过滤器
2025-07-20 15:04:21.665 | INFO     | core.telegram.user_manager:__init__:30 - Telegram用户管理器初始化
2025-07-20 15:04:21.666 | INFO     | core.monitor.extractor:__init__:24 - 初始化关键词提取器
2025-07-20 15:04:21.666 | INFO     | core.telegram.group_manager:__init__:36 - Telegram群组管理器初始化
2025-07-20 15:04:21.666 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-07-20 15:04:21.666 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-07-20 15:04:21.666 | INFO     | core.monitor.notifier:__init__:90 - 初始化通知管理器
2025-07-20 15:04:21.666 | INFO     | core.telegram.monitor_manager:__init__:26 - Telegram监控管理器初始化
2025-07-20 15:04:21.666 | INFO     | app.controllers.telegram_monitor_controller:__init__:64 - 初始化 Telegram 监控控制器
2025-07-20 15:04:21.666 | INFO     | core.telegram.telegram_service:__init__:39 - Telegram服务初始化完成
2025-07-20 15:04:21.666 | DEBUG    | app.controllers.telegram_monitor_controller:__init__:82 - 控制器初始化完成
2025-07-20 15:04:21.898 | INFO     | ui.views.monitor_view:__init__:48 - 初始化监控视图
2025-07-20 15:04:21.899 | DEBUG    | ui.views.monitor_view:_connect_signals:80 - 连接控制器信号
2025-07-20 15:04:22.077 | INFO     | ui.views.proxy_view:__init__:96 - 初始化视图，加载第一页代理数据
2025-07-20 15:04:22.320 | INFO     | __main__:on_auth_completed:95 - 主窗口已启动
2025-07-20 15:04:22.364 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:88 - 尝试连接监听日志信号...
2025-07-20 15:04:22.364 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:90 - 监听日志信号连接成功
2025-07-20 15:04:22.365 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-20 15:04:22.369 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-20 15:04:22.373 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:145 - 加载监控任务列表 (搜索关键词: '')
2025-07-20 15:04:22.373 | DEBUG    | ui.views.monitor_view:_on_loading_started:620 - 加载中: 正在加载任务列表...
2025-07-20 15:04:22.373 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-20 15:04:22.379 | INFO     | app.services.proxy_service:refresh_proxies:712 - 刷新代理列表: offset=0, limit=20
2025-07-20 15:04:22.379 | INFO     | core.proxy.proxy_task_manager:submit_task:172 - 提交耗时任务到子线程: refresh_proxies_0_20
2025-07-20 15:04:22.380 | DEBUG    | app.services.proxy_service:get_proxies:500 - 获取代理列表: offset=0, limit=20
2025-07-20 15:04:22.380 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-20 15:04:22.380 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-20 15:04:22.382 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-20 15:04:22.385 | INFO     | app.controllers.invite_controller:get_invite_tasks:38 - 获取邀请任务列表, status=None
2025-07-20 15:04:22.385 | INFO     | app.services.invite_service:get_invite_tasks:121 - 获取邀请任务列表, status=None
2025-07-20 15:04:22.385 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-20 15:04:22.403 | INFO     | app.services.invite_service:task_initialize:39 - 初始化邀请服务，重置运行中的任务为暂停状态
2025-07-20 15:04:22.407 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-20 15:04:22.412 | INFO     | utils.vip_checker:start_checking:37 - 开始VIP状态检查
2025-07-20 15:04:22.412 | DEBUG    | utils.vip_checker:_check_vip_status:61 - 正在检查VIP状态...
2025-07-20 15:04:22.412 | INFO     | core.auth.api_service:verify_vip:425 - 会员验证接口调用
2025-07-20 15:04:22.414 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-20 15:04:22.415 | INFO     | core.proxy.proxy_task_manager:task_wrapper:199 - 任务 refresh_proxies_0_20 完成
2025-07-20 15:04:22.617 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-20 15:04:22.620 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-20 15:04:22.625 | DEBUG    | ui.views.monitor_view:_refresh_task_list:175 - 刷新任务列表，共0个任务
2025-07-20 15:04:22.625 | INFO     | ui.views.monitor_view:_refresh_task_list:189 - 没有找到监控任务
2025-07-20 15:04:22.625 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-20 15:04:22.628 | INFO     | app.services.invite_service:task_initialize:48 - 成功重置 0 个任务状态
2025-07-20 15:04:22.628 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-20 15:04:22.631 | INFO     | app.services.account_service:get_all_groups:95 - 获取所有账户分组成功, 共 2 个
2025-07-20 15:04:22.631 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-20 15:04:22.631 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-20 15:04:22.637 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-20 15:04:22.639 | DEBUG    | ui.views.account_view:_on_groups_loaded:445 - 分组加载完成: 2个分组
2025-07-20 15:04:22.653 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-20 15:04:22.657 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-20 15:04:22.717 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-20 15:04:22.773 | INFO     | app.services.account_service:get_all_accounts:289 - 获取所有账户成功, 共 2 个
2025-07-20 15:04:22.773 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-20 15:04:22.779 | INFO     | app.controllers.telegram_monitor_controller:_load_total_users_count_internal:191 - 成功加载总体统计数据: 总用户数 23, 今日采集 0, 日均采集 23, 运行天数 1
2025-07-20 15:04:22.779 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:156 - 成功加载 0 个任务
2025-07-20 15:04:22.779 | DEBUG    | ui.views.monitor_view:_on_loading_finished:625 - 加载完成
2025-07-20 15:04:22.780 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-20 15:04:22.781 | DEBUG    | ui.views.account_view:_on_accounts_loaded:578 - 账户加载完成: 2个账户
2025-07-20 15:04:22.801 | DEBUG    | ui.views.account_view:_update_group_counts:186 - 分组计数已更新: {2: 2}
2025-07-20 15:04:22.805 | INFO     | ui.views.account_view:_auto_login_accounts:633 - 开始调用控制器进行 2 个账户的自动登录。
2025-07-20 15:04:22.805 | INFO     | app.services.account_service:batch_auto_login:1164 - 服务层：开始批量自动登录 2 个账户, 最大并发: 5
2025-07-20 15:04:22.806 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-20 15:04:22.809 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-20 15:04:22.816 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-07-20 15:04:22.832 | INFO     | app.services.account_service:get_all_accounts:289 - 获取所有账户成功, 共 2 个
2025-07-20 15:04:22.832 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-20 15:04:22.834 | INFO     | app.services.account_service:batch_auto_login:1203 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-07-20 15:04:22.834 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-20 15:04:22.843 | DEBUG    | ui.views.account_view:_update_group_counts:186 - 分组计数已更新: {2: 2}
2025-07-20 15:04:22.843 | DEBUG    | ui.views.account_view:_init_cache:153 - 账户缓存初始化完成，共 2 个账户
2025-07-20 15:04:22.848 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-20 15:04:22.851 | DEBUG    | ui.views.account_view:_on_accounts_loaded:578 - 账户加载完成: 2个账户
2025-07-20 15:04:22.874 | DEBUG    | ui.views.account_view:_update_group_counts:186 - 分组计数已更新: {2: 2}
2025-07-20 15:04:22.889 | INFO     | core.auth.api_service:verify_vip:438 - 会员验证成功
2025-07-20 15:04:22.889 | INFO     | utils.vip_checker:_check_vip_status:76 - VIP状态有效
2025-07-20 15:04:22.915 | INFO     | app.services.account_service:batch_auto_login:1203 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-07-20 15:04:22.915 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-20 15:04:22.918 | INFO     | app.services.account_service:batch_auto_login:1273 - 服务层：准备调用核心层处理 2 个账户的自动登录。
2025-07-20 15:04:22.919 | INFO     | core.telegram.client_manager:batch_auto_login:1230 - 开始批量自动登录, 共 2 个账户, 最大并发 5
2025-07-20 15:04:22.919 | INFO     | app.services.account_service:batch_auto_login:1283 - 服务层：设置核心层任务超时为 120 秒。
2025-07-20 15:04:22.919 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:271 - 创建客户端: H:\PyProject\TeleTest\APPDATA\sessions\+***********, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-20 15:04:22.920 | DEBUG    | core.telegram.client_manager:get_proxy:96 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-20 15:04:22.920 | DEBUG    | core.telegram.client_manager:get_proxy:143 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-07-20 15:04:22.926 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:271 - 创建客户端: H:\PyProject\TeleTest\APPDATA\sessions\+***********, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-20 15:04:22.926 | DEBUG    | core.telegram.client_manager:get_proxy:96 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-20 15:04:22.926 | DEBUG    | core.telegram.client_manager:get_proxy:143 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-07-20 15:04:22.960 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-20 15:04:23.035 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-20 15:04:23.082 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-07-20 15:04:23.085 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-07-20 15:04:23.086 | INFO     | ui.views.proxy_view:_load_initial_data:1044 - 初始数据加载完成，共 1 个代理
2025-07-20 15:04:23.115 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-20 15:04:26.246 | INFO     | core.telegram.client_manager:_connect_client:219 - 连接成功!
2025-07-20 15:04:26.930 | INFO     | core.telegram.client_manager:_connect_client:219 - 连接成功!
2025-07-20 15:04:27.500 | INFO     | core.telegram.client_manager:_verify_client_authorization:1205 - 账户已授权: +***********
2025-07-20 15:04:27.810 | INFO     | core.telegram.client_manager:_verify_client_authorization:1205 - 账户已授权: +***********
2025-07-20 15:04:29.821 | INFO     | core.telegram.client_manager:batch_auto_login:1379 - 批量登录完成: 总计 2, 成功 2, 失败 0
2025-07-20 15:04:29.946 | INFO     | app.services.account_service:batch_auto_login:1304 - 服务层：核心层登录完成: 成功 2, 失败 0 / 2
2025-07-20 15:04:33.344 | INFO     | app.services.account_service:get_proxy_ips:1043 - 获取有效代理IP列表（包含绑定计数）
2025-07-20 15:04:33.344 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-20 15:04:33.533 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-20 15:04:33.534 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-20 15:04:33.537 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-20 15:04:33.538 | INFO     | app.services.account_service:get_proxy_ips:1095 - 成功获取 1 个有效代理（已附带绑定计数）
2025-07-20 15:04:33.538 | INFO     | app.controllers.account_controller:get_proxy_ips:566 - 获取到1个有效代理IP
2025-07-20 15:04:48.629 | INFO     | app.services.account_service:update_account_profile:490 - 更新账户资料: 2
2025-07-20 15:04:48.629 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-20 15:04:48.629 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-20 15:04:48.629 | ERROR    | app.services.account_service:update_account_profile:532 - 更新账户资料异常: 'coroutine' object has no attribute 'phone'
2025-07-20 15:05:21.613 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-20 15:06:21.621 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-20 15:07:21.611 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-20 15:08:21.615 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-20 15:09:21.614 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-20 15:10:21.611 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-20 15:11:21.612 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-20 15:12:21.626 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-20 15:13:21.621 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-20 15:14:21.614 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-20 15:15:21.620 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-20 15:16:21.610 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-20 15:17:21.624 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-20 15:18:21.624 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-20 15:19:21.616 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-20 15:20:21.614 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-20 15:21:21.617 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-20 15:22:21.617 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-20 16:15:39.062 | INFO     | utils.logger:init_logger:98 - 日志系统初始化完成，默认级别: DEBUG，日志目录: H:\PyProject\TeleTest\APPDATA\logs
2025-07-20 16:15:40.058 | INFO     | data.database:init_database:31 - 初始化数据库连接: H:\PyProject\TeleTest\APPDATA\database\telegram_manager.db
2025-07-20 16:15:40.074 | DEBUG    | data.database:init_database:58 - 创建数据库表
2025-07-20 16:15:40.085 | INFO     | data.database:init_database:61 - 数据库初始化完成
2025-07-20 16:15:41.157 | DEBUG    | app.controllers.auth_controller:initialize_software:43 - 控制器: 初始化软件 app_id=1000, version=1.2.7
2025-07-20 16:15:41.157 | DEBUG    | core.auth.api_service:init_software:55 - 发送初始化请求: https://server.xile188.com/api/user/1000/windowns10/1.2.7/ini, params={}
2025-07-20 16:15:41.613 | DEBUG    | core.auth.api_service:init_software:66 - 初始化数据解密成功: {'new_content': '- 新增功能\n  - 1.新增图片上传功能\n  - 2.新增软件文档\n  - 3.新增软件自动更新，后续更新会自动检测版本更新，无需再下载更新包\n  - 4.新增设置账户信息及发信控制\n- 修复bug\n  - 删除监听任务时，不删除对应任务采集到的数据。\n  - 监听设置群无法发送到目标群\n  - 导出监听数据时应当导出单个任务而非所有数据\n- 优化记录\n  - 优化发信规则，直接调取符合要求的账户发送，无账户时才需要等待延时。\n  - 完善软件设置', 'new_url': None, 'notice': {'id': '2', 'content': '公告内容测试3', 'time': '**********'}}
2025-07-20 16:15:41.619 | INFO     | app.controllers.auth_controller:login:61 - 控制器: 用户登录 account=demo01
2025-07-20 16:15:44.515 | INFO     | core.auth.api_service:login:127 - 用户登录: account=demo01
2025-07-20 16:15:45.057 | INFO     | core.auth.api_service:get_user_info:347 - 获取用户信息
2025-07-20 16:15:45.279 | INFO     | core.auth.api_service:get_user_info:375 - 获取用户信息成功
2025-07-20 16:15:45.286 | INFO     | __main__:on_auth_completed:91 - 用户登录成功，正在启动主窗口...
2025-07-20 16:15:45.311 | INFO     | core.telegram.client_worker:__init__:61 - Telegram客户端工作线程初始化
2025-07-20 16:15:45.311 | INFO     | utils.vip_checker:initialize:25 - VIP检查器已初始化，等待登录后开始检查
2025-07-20 16:15:45.311 | INFO     | ui.main_window:_initialize_core_components:108 - MainWindow: 初始化核心组件...
2025-07-20 16:15:45.312 | INFO     | app.services.account_service:__init__:41 - 账户服务初始化
2025-07-20 16:15:45.312 | INFO     | app.controllers.account_controller:__init__:81 - 账户控制器初始化
2025-07-20 16:15:45.312 | INFO     | core.proxy.proxy_task_manager:run:284 - 代理任务管理器线程启动
2025-07-20 16:15:45.313 | INFO     | core.telegram.client_worker:run:65 - Telegram客户端工作线程开始运行
2025-07-20 16:15:45.313 | INFO     | app.services.monitor_service:__init__:39 - 初始化监控任务服务
2025-07-20 16:15:45.313 | INFO     | core.monitor.filter:__init__:26 - 初始化消息过滤器
2025-07-20 16:15:45.313 | INFO     | core.monitor.extractor:__init__:24 - 初始化关键词提取器
2025-07-20 16:15:45.314 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-07-20 16:15:45.314 | INFO     | core.monitor.notifier:__init__:90 - 初始化通知管理器
2025-07-20 16:15:45.316 | INFO     | app.controllers.telegram_monitor_controller:__init__:64 - 初始化 Telegram 监控控制器
2025-07-20 16:15:45.316 | DEBUG    | app.controllers.telegram_monitor_controller:__init__:82 - 控制器初始化完成
2025-07-20 16:15:45.319 | INFO     | core.telegram.client_manager:__init__:66 - Telegram客户端管理器初始化
2025-07-20 16:15:45.319 | INFO     | core.telegram.user_manager:__init__:30 - Telegram用户管理器初始化
2025-07-20 16:15:45.320 | INFO     | core.telegram.group_manager:__init__:36 - Telegram群组管理器初始化
2025-07-20 16:15:45.320 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-07-20 16:15:45.320 | INFO     | core.telegram.monitor_manager:__init__:26 - Telegram监控管理器初始化
2025-07-20 16:15:45.320 | INFO     | core.telegram.telegram_service:__init__:39 - Telegram服务初始化完成
2025-07-20 16:15:45.562 | INFO     | ui.views.monitor_view:__init__:48 - 初始化监控视图
2025-07-20 16:15:45.562 | DEBUG    | ui.views.monitor_view:_connect_signals:80 - 连接控制器信号
2025-07-20 16:15:45.758 | INFO     | ui.views.proxy_view:__init__:96 - 初始化视图，加载第一页代理数据
2025-07-20 16:15:46.016 | INFO     | __main__:on_auth_completed:95 - 主窗口已启动
2025-07-20 16:15:46.062 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:88 - 尝试连接监听日志信号...
2025-07-20 16:15:46.062 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:90 - 监听日志信号连接成功
2025-07-20 16:15:46.063 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-20 16:15:46.067 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-20 16:15:46.071 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:145 - 加载监控任务列表 (搜索关键词: '')
2025-07-20 16:15:46.072 | DEBUG    | ui.views.monitor_view:_on_loading_started:620 - 加载中: 正在加载任务列表...
2025-07-20 16:15:46.072 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-20 16:15:46.078 | INFO     | app.services.proxy_service:refresh_proxies:712 - 刷新代理列表: offset=0, limit=20
2025-07-20 16:15:46.078 | INFO     | core.proxy.proxy_task_manager:submit_task:172 - 提交耗时任务到子线程: refresh_proxies_0_20
2025-07-20 16:15:46.079 | DEBUG    | app.services.proxy_service:get_proxies:500 - 获取代理列表: offset=0, limit=20
2025-07-20 16:15:46.079 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-20 16:15:46.079 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-20 16:15:46.083 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-20 16:15:46.106 | INFO     | app.controllers.invite_controller:get_invite_tasks:38 - 获取邀请任务列表, status=None
2025-07-20 16:15:46.107 | INFO     | app.services.invite_service:get_invite_tasks:121 - 获取邀请任务列表, status=None
2025-07-20 16:15:46.107 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-20 16:15:46.108 | INFO     | app.services.invite_service:task_initialize:39 - 初始化邀请服务，重置运行中的任务为暂停状态
2025-07-20 16:15:46.108 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-20 16:15:46.143 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-20 16:15:46.144 | INFO     | utils.vip_checker:start_checking:37 - 开始VIP状态检查
2025-07-20 16:15:46.144 | DEBUG    | utils.vip_checker:_check_vip_status:61 - 正在检查VIP状态...
2025-07-20 16:15:46.144 | INFO     | core.auth.api_service:verify_vip:425 - 会员验证接口调用
2025-07-20 16:15:46.145 | INFO     | core.proxy.proxy_task_manager:task_wrapper:199 - 任务 refresh_proxies_0_20 完成
2025-07-20 16:15:46.265 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-20 16:15:46.266 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-20 16:15:46.278 | DEBUG    | ui.views.monitor_view:_refresh_task_list:175 - 刷新任务列表，共0个任务
2025-07-20 16:15:46.278 | INFO     | ui.views.monitor_view:_refresh_task_list:189 - 没有找到监控任务
2025-07-20 16:15:46.278 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-20 16:15:46.283 | INFO     | app.services.invite_service:task_initialize:48 - 成功重置 0 个任务状态
2025-07-20 16:15:46.283 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-20 16:15:46.284 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-20 16:15:46.288 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-20 16:15:46.321 | INFO     | app.services.account_service:get_all_groups:95 - 获取所有账户分组成功, 共 2 个
2025-07-20 16:15:46.321 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-20 16:15:46.323 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-20 16:15:46.325 | DEBUG    | ui.views.account_view:_on_groups_loaded:445 - 分组加载完成: 2个分组
2025-07-20 16:15:46.349 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-20 16:15:46.553 | INFO     | core.auth.api_service:verify_vip:438 - 会员验证成功
2025-07-20 16:15:46.554 | INFO     | utils.vip_checker:_check_vip_status:76 - VIP状态有效
2025-07-20 16:15:46.557 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-20 16:15:46.558 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-20 16:15:46.559 | INFO     | app.services.account_service:get_all_accounts:289 - 获取所有账户成功, 共 2 个
2025-07-20 16:15:46.559 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-20 16:15:46.561 | INFO     | app.controllers.telegram_monitor_controller:_load_total_users_count_internal:191 - 成功加载总体统计数据: 总用户数 23, 今日采集 0, 日均采集 23, 运行天数 1
2025-07-20 16:15:46.561 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:156 - 成功加载 0 个任务
2025-07-20 16:15:46.561 | DEBUG    | ui.views.monitor_view:_on_loading_finished:625 - 加载完成
2025-07-20 16:15:46.563 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-07-20 16:15:46.576 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-20 16:15:46.578 | DEBUG    | ui.views.account_view:_on_accounts_loaded:578 - 账户加载完成: 2个账户
2025-07-20 16:15:46.604 | DEBUG    | ui.views.account_view:_update_group_counts:186 - 分组计数已更新: {2: 2}
2025-07-20 16:15:46.607 | INFO     | ui.views.account_view:_auto_login_accounts:633 - 开始调用控制器进行 2 个账户的自动登录。
2025-07-20 16:15:46.607 | INFO     | app.services.account_service:batch_auto_login:1164 - 服务层：开始批量自动登录 2 个账户, 最大并发: 5
2025-07-20 16:15:46.608 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-20 16:15:46.622 | INFO     | app.services.account_service:batch_auto_login:1203 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-07-20 16:15:46.622 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-20 16:15:46.622 | INFO     | app.services.account_service:get_all_accounts:289 - 获取所有账户成功, 共 2 个
2025-07-20 16:15:46.623 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-20 16:15:46.624 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-20 16:15:46.625 | DEBUG    | ui.views.account_view:_update_group_counts:186 - 分组计数已更新: {2: 2}
2025-07-20 16:15:46.625 | DEBUG    | ui.views.account_view:_init_cache:153 - 账户缓存初始化完成，共 2 个账户
2025-07-20 16:15:46.630 | DEBUG    | ui.views.account_view:_on_accounts_loaded:578 - 账户加载完成: 2个账户
2025-07-20 16:15:46.650 | DEBUG    | ui.views.account_view:_update_group_counts:186 - 分组计数已更新: {2: 2}
2025-07-20 16:15:46.660 | INFO     | app.services.account_service:batch_auto_login:1203 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-07-20 16:15:46.660 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-20 16:15:46.661 | INFO     | app.services.account_service:batch_auto_login:1273 - 服务层：准备调用核心层处理 2 个账户的自动登录。
2025-07-20 16:15:46.662 | INFO     | app.services.account_service:batch_auto_login:1283 - 服务层：设置核心层任务超时为 120 秒。
2025-07-20 16:15:46.662 | INFO     | core.telegram.client_manager:batch_auto_login:1230 - 开始批量自动登录, 共 2 个账户, 最大并发 5
2025-07-20 16:15:46.662 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:271 - 创建客户端: H:\PyProject\TeleTest\APPDATA\sessions\+***********, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-20 16:15:46.662 | DEBUG    | core.telegram.client_manager:get_proxy:96 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-20 16:15:46.662 | DEBUG    | core.telegram.client_manager:get_proxy:143 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-07-20 16:15:46.666 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:271 - 创建客户端: H:\PyProject\TeleTest\APPDATA\sessions\+***********, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-20 16:15:46.667 | DEBUG    | core.telegram.client_manager:get_proxy:96 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-20 16:15:46.667 | DEBUG    | core.telegram.client_manager:get_proxy:143 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-07-20 16:15:46.694 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-20 16:15:46.779 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-20 16:15:46.906 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-07-20 16:15:46.908 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-07-20 16:15:46.909 | INFO     | ui.views.proxy_view:_load_initial_data:1044 - 初始数据加载完成，共 1 个代理
2025-07-20 16:15:46.986 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-20 16:15:50.100 | INFO     | core.telegram.client_manager:_connect_client:219 - 连接成功!
2025-07-20 16:15:51.050 | INFO     | core.telegram.client_manager:_connect_client:219 - 连接成功!
2025-07-20 16:15:51.492 | INFO     | core.telegram.client_manager:_verify_client_authorization:1205 - 账户已授权: +***********
2025-07-20 16:15:51.971 | INFO     | core.telegram.client_manager:_verify_client_authorization:1205 - 账户已授权: +***********
2025-07-20 16:15:53.984 | INFO     | core.telegram.client_manager:batch_auto_login:1379 - 批量登录完成: 总计 2, 成功 2, 失败 0
2025-07-20 16:15:54.717 | INFO     | app.services.account_service:batch_auto_login:1304 - 服务层：核心层登录完成: 成功 2, 失败 0 / 2
2025-07-20 16:15:56.878 | INFO     | app.services.account_service:get_proxy_ips:1043 - 获取有效代理IP列表（包含绑定计数）
2025-07-20 16:15:56.879 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-20 16:15:57.112 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-20 16:15:57.113 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-20 16:15:57.117 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-20 16:15:57.118 | INFO     | app.services.account_service:get_proxy_ips:1095 - 成功获取 1 个有效代理（已附带绑定计数）
2025-07-20 16:15:57.118 | INFO     | app.controllers.account_controller:get_proxy_ips:566 - 获取到1个有效代理IP
2025-07-20 16:16:08.182 | INFO     | app.services.account_service:update_account_profile:490 - 更新账户资料: 2
2025-07-20 16:16:08.182 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-20 16:16:08.191 | INFO     | core.telegram.user_manager:update_profile:89 - 正在更新用户资料: +***********
2025-07-20 16:16:10.001 | INFO     | core.telegram.user_manager:update_profile:120 - 更新用户资料成功: +***********
2025-07-20 16:16:10.215 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-20 16:16:11.751 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-20 16:16:11.758 | INFO     | app.services.account_service:get_all_accounts:289 - 获取所有账户成功, 共 2 个
2025-07-20 16:16:11.758 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-20 16:16:11.759 | DEBUG    | ui.views.account_view:_on_accounts_loaded:578 - 账户加载完成: 2个账户
2025-07-20 16:16:11.780 | DEBUG    | ui.views.account_view:_update_group_counts:186 - 分组计数已更新: {2: 2}
2025-07-20 16:16:45.275 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-20 16:17:45.277 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-20 16:18:45.275 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-20 16:19:45.279 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-20 16:20:45.273 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-20 16:21:45.515 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-20 16:22:45.279 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-20 16:23:45.274 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-20 16:24:45.273 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-20 16:25:45.279 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-20 16:26:45.270 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-20 16:27:45.267 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-20 16:28:45.279 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-20 16:29:45.561 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-20 16:30:45.407 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-20 16:31:45.269 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-20 16:32:45.271 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-20 16:33:45.269 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-20 16:34:45.270 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-20 16:35:45.281 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-20 16:36:45.279 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-20 16:37:45.315 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-20 16:38:45.278 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-20 16:39:45.268 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-20 20:36:44.846 | INFO     | utils.logger:init_logger:98 - 日志系统初始化完成，默认级别: DEBUG，日志目录: H:\PyProject\TeleTest\APPDATA\logs
2025-07-20 20:36:47.401 | INFO     | data.database:init_database:31 - 初始化数据库连接: H:\PyProject\TeleTest\APPDATA\database\telegram_manager.db
2025-07-20 20:36:47.453 | DEBUG    | data.database:init_database:58 - 创建数据库表
2025-07-20 20:36:47.476 | INFO     | data.database:init_database:61 - 数据库初始化完成
2025-07-20 20:36:48.806 | DEBUG    | app.controllers.auth_controller:initialize_software:43 - 控制器: 初始化软件 app_id=1000, version=1.2.7
2025-07-20 20:36:48.806 | DEBUG    | core.auth.api_service:init_software:55 - 发送初始化请求: https://server.xile188.com/api/user/1000/windowns10/1.2.7/ini, params={}
2025-07-20 20:36:49.289 | DEBUG    | core.auth.api_service:init_software:66 - 初始化数据解密成功: {'new_content': '- 新增功能\n  - 1.新增图片上传功能\n  - 2.新增软件文档\n  - 3.新增软件自动更新，后续更新会自动检测版本更新，无需再下载更新包\n  - 4.新增设置账户信息及发信控制\n- 修复bug\n  - 删除监听任务时，不删除对应任务采集到的数据。\n  - 监听设置群无法发送到目标群\n  - 导出监听数据时应当导出单个任务而非所有数据\n- 优化记录\n  - 优化发信规则，直接调取符合要求的账户发送，无账户时才需要等待延时。\n  - 完善软件设置', 'new_url': None, 'notice': {'id': '2', 'content': '公告内容测试3', 'time': '**********'}}
2025-07-20 20:36:49.322 | INFO     | app.controllers.auth_controller:login:61 - 控制器: 用户登录 account=demo01
2025-07-20 20:36:52.352 | INFO     | core.auth.api_service:login:127 - 用户登录: account=demo01
2025-07-20 20:36:52.886 | INFO     | core.auth.api_service:get_user_info:347 - 获取用户信息
2025-07-20 20:36:53.138 | INFO     | core.auth.api_service:get_user_info:375 - 获取用户信息成功
2025-07-20 20:36:53.146 | INFO     | __main__:on_auth_completed:91 - 用户登录成功，正在启动主窗口...
2025-07-20 20:36:53.185 | INFO     | core.telegram.client_worker:__init__:61 - Telegram客户端工作线程初始化
2025-07-20 20:36:53.185 | INFO     | utils.vip_checker:initialize:25 - VIP检查器已初始化，等待登录后开始检查
2025-07-20 20:36:53.185 | INFO     | ui.main_window:_initialize_core_components:108 - MainWindow: 初始化核心组件...
2025-07-20 20:36:53.187 | INFO     | app.services.account_service:__init__:41 - 账户服务初始化
2025-07-20 20:36:53.187 | INFO     | core.telegram.client_worker:run:65 - Telegram客户端工作线程开始运行
2025-07-20 20:36:53.187 | INFO     | app.controllers.account_controller:__init__:81 - 账户控制器初始化
2025-07-20 20:36:53.188 | INFO     | core.proxy.proxy_task_manager:run:284 - 代理任务管理器线程启动
2025-07-20 20:36:53.188 | INFO     | app.services.monitor_service:__init__:39 - 初始化监控任务服务
2025-07-20 20:36:53.189 | INFO     | core.monitor.filter:__init__:26 - 初始化消息过滤器
2025-07-20 20:36:53.189 | INFO     | core.monitor.extractor:__init__:24 - 初始化关键词提取器
2025-07-20 20:36:53.190 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-07-20 20:36:53.190 | INFO     | core.monitor.notifier:__init__:90 - 初始化通知管理器
2025-07-20 20:36:53.190 | INFO     | app.controllers.telegram_monitor_controller:__init__:64 - 初始化 Telegram 监控控制器
2025-07-20 20:36:53.191 | DEBUG    | app.controllers.telegram_monitor_controller:__init__:82 - 控制器初始化完成
2025-07-20 20:36:53.194 | INFO     | core.telegram.client_manager:__init__:66 - Telegram客户端管理器初始化
2025-07-20 20:36:53.196 | INFO     | core.telegram.user_manager:__init__:30 - Telegram用户管理器初始化
2025-07-20 20:36:53.196 | INFO     | core.telegram.group_manager:__init__:36 - Telegram群组管理器初始化
2025-07-20 20:36:53.196 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-07-20 20:36:53.197 | INFO     | core.telegram.monitor_manager:__init__:26 - Telegram监控管理器初始化
2025-07-20 20:36:53.197 | INFO     | core.telegram.telegram_service:__init__:39 - Telegram服务初始化完成
2025-07-20 20:36:53.473 | INFO     | ui.views.monitor_view:__init__:48 - 初始化监控视图
2025-07-20 20:36:53.473 | DEBUG    | ui.views.monitor_view:_connect_signals:80 - 连接控制器信号
2025-07-20 20:36:53.678 | INFO     | ui.views.proxy_view:__init__:96 - 初始化视图，加载第一页代理数据
2025-07-20 20:36:54.314 | INFO     | __main__:on_auth_completed:95 - 主窗口已启动
2025-07-20 20:36:54.418 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:88 - 尝试连接监听日志信号...
2025-07-20 20:36:54.418 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:90 - 监听日志信号连接成功
2025-07-20 20:36:54.419 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-20 20:36:54.426 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-20 20:36:54.431 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:145 - 加载监控任务列表 (搜索关键词: '')
2025-07-20 20:36:54.431 | DEBUG    | ui.views.monitor_view:_on_loading_started:620 - 加载中: 正在加载任务列表...
2025-07-20 20:36:54.432 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-20 20:36:54.439 | INFO     | app.services.proxy_service:refresh_proxies:712 - 刷新代理列表: offset=0, limit=20
2025-07-20 20:36:54.440 | INFO     | core.proxy.proxy_task_manager:submit_task:172 - 提交耗时任务到子线程: refresh_proxies_0_20
2025-07-20 20:36:54.440 | DEBUG    | app.services.proxy_service:get_proxies:500 - 获取代理列表: offset=0, limit=20
2025-07-20 20:36:54.440 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-20 20:36:54.441 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-20 20:36:54.447 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-20 20:36:54.464 | INFO     | app.controllers.invite_controller:get_invite_tasks:38 - 获取邀请任务列表, status=None
2025-07-20 20:36:54.470 | INFO     | app.services.invite_service:get_invite_tasks:121 - 获取邀请任务列表, status=None
2025-07-20 20:36:54.470 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-20 20:36:54.506 | INFO     | app.services.invite_service:task_initialize:39 - 初始化邀请服务，重置运行中的任务为暂停状态
2025-07-20 20:36:54.506 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-20 20:36:54.515 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-20 20:36:54.517 | INFO     | utils.vip_checker:start_checking:37 - 开始VIP状态检查
2025-07-20 20:36:54.517 | DEBUG    | utils.vip_checker:_check_vip_status:61 - 正在检查VIP状态...
2025-07-20 20:36:54.517 | INFO     | core.auth.api_service:verify_vip:425 - 会员验证接口调用
2025-07-20 20:36:54.519 | INFO     | core.proxy.proxy_task_manager:task_wrapper:199 - 任务 refresh_proxies_0_20 完成
2025-07-20 20:36:54.751 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-20 20:36:54.761 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-20 20:36:54.769 | DEBUG    | ui.views.monitor_view:_refresh_task_list:175 - 刷新任务列表，共0个任务
2025-07-20 20:36:54.770 | INFO     | ui.views.monitor_view:_refresh_task_list:189 - 没有找到监控任务
2025-07-20 20:36:54.771 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-20 20:36:54.775 | INFO     | app.services.invite_service:task_initialize:48 - 成功重置 0 个任务状态
2025-07-20 20:36:54.775 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-20 20:36:54.780 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-20 20:36:54.788 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-20 20:36:54.790 | INFO     | app.services.account_service:get_all_groups:95 - 获取所有账户分组成功, 共 2 个
2025-07-20 20:36:54.791 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-20 20:36:54.792 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-20 20:36:54.797 | DEBUG    | ui.views.account_view:_on_groups_loaded:445 - 分组加载完成: 2个分组
2025-07-20 20:36:54.812 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-20 20:36:54.899 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-20 20:36:54.903 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-20 20:36:54.904 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-07-20 20:36:54.919 | INFO     | app.services.account_service:get_all_accounts:289 - 获取所有账户成功, 共 2 个
2025-07-20 20:36:54.919 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-20 20:36:54.925 | INFO     | app.controllers.telegram_monitor_controller:_load_total_users_count_internal:191 - 成功加载总体统计数据: 总用户数 23, 今日采集 0, 日均采集 23, 运行天数 1
2025-07-20 20:36:54.926 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:156 - 成功加载 0 个任务
2025-07-20 20:36:54.926 | DEBUG    | ui.views.monitor_view:_on_loading_finished:625 - 加载完成
2025-07-20 20:36:54.928 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-20 20:36:54.930 | DEBUG    | ui.views.account_view:_on_accounts_loaded:578 - 账户加载完成: 2个账户
2025-07-20 20:36:54.951 | DEBUG    | ui.views.account_view:_update_group_counts:186 - 分组计数已更新: {2: 2}
2025-07-20 20:36:54.956 | INFO     | ui.views.account_view:_auto_login_accounts:633 - 开始调用控制器进行 2 个账户的自动登录。
2025-07-20 20:36:54.956 | INFO     | app.services.account_service:batch_auto_login:1164 - 服务层：开始批量自动登录 2 个账户, 最大并发: 5
2025-07-20 20:36:54.956 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-20 20:36:54.972 | INFO     | core.auth.api_service:verify_vip:438 - 会员验证成功
2025-07-20 20:36:54.972 | INFO     | utils.vip_checker:_check_vip_status:76 - VIP状态有效
2025-07-20 20:36:54.992 | INFO     | app.services.account_service:get_all_accounts:289 - 获取所有账户成功, 共 2 个
2025-07-20 20:36:54.993 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-20 20:36:54.994 | INFO     | app.services.account_service:batch_auto_login:1203 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-07-20 20:36:54.994 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-20 20:36:54.998 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-20 20:36:55.005 | DEBUG    | ui.views.account_view:_update_group_counts:186 - 分组计数已更新: {2: 2}
2025-07-20 20:36:55.005 | DEBUG    | ui.views.account_view:_init_cache:153 - 账户缓存初始化完成，共 2 个账户
2025-07-20 20:36:55.011 | DEBUG    | ui.views.account_view:_on_accounts_loaded:578 - 账户加载完成: 2个账户
2025-07-20 20:36:55.036 | DEBUG    | ui.views.account_view:_update_group_counts:186 - 分组计数已更新: {2: 2}
2025-07-20 20:36:55.054 | INFO     | app.services.account_service:batch_auto_login:1203 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-07-20 20:36:55.054 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-20 20:36:55.058 | INFO     | app.services.account_service:batch_auto_login:1273 - 服务层：准备调用核心层处理 2 个账户的自动登录。
2025-07-20 20:36:55.059 | INFO     | core.telegram.client_manager:batch_auto_login:1230 - 开始批量自动登录, 共 2 个账户, 最大并发 5
2025-07-20 20:36:55.059 | INFO     | app.services.account_service:batch_auto_login:1283 - 服务层：设置核心层任务超时为 120 秒。
2025-07-20 20:36:55.060 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:271 - 创建客户端: H:\PyProject\TeleTest\APPDATA\sessions\+***********, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-20 20:36:55.060 | DEBUG    | core.telegram.client_manager:get_proxy:96 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-20 20:36:55.062 | DEBUG    | core.telegram.client_manager:get_proxy:143 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-07-20 20:36:55.073 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:271 - 创建客户端: H:\PyProject\TeleTest\APPDATA\sessions\+***********, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-20 20:36:55.073 | DEBUG    | core.telegram.client_manager:get_proxy:96 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-20 20:36:55.074 | DEBUG    | core.telegram.client_manager:get_proxy:143 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-07-20 20:36:55.129 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-20 20:36:55.210 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-20 20:36:55.221 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-07-20 20:36:55.223 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-07-20 20:36:55.225 | INFO     | ui.views.proxy_view:_load_initial_data:1044 - 初始数据加载完成，共 1 个代理
2025-07-20 20:36:55.292 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-20 20:36:58.562 | INFO     | core.telegram.client_manager:_connect_client:219 - 连接成功!
2025-07-20 20:36:58.755 | INFO     | core.telegram.client_manager:_connect_client:219 - 连接成功!
2025-07-20 20:36:59.440 | INFO     | core.telegram.client_manager:_verify_client_authorization:1205 - 账户已授权: +***********
2025-07-20 20:36:59.735 | INFO     | core.telegram.client_manager:_verify_client_authorization:1205 - 账户已授权: +***********
2025-07-20 20:37:01.743 | INFO     | core.telegram.client_manager:batch_auto_login:1379 - 批量登录完成: 总计 2, 成功 2, 失败 0
2025-07-20 20:37:02.066 | INFO     | app.services.account_service:batch_auto_login:1304 - 服务层：核心层登录完成: 成功 2, 失败 0 / 2
2025-07-20 20:37:53.131 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-20 20:38:53.141 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-20 20:39:53.130 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-20 20:40:53.128 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-20 20:41:53.138 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-20 20:42:53.127 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-20 20:43:53.129 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-20 20:44:53.134 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-20 20:45:53.126 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-20 20:46:53.127 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-20 20:47:53.140 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-20 20:48:53.139 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-20 20:49:53.132 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-20 20:50:53.129 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-20 20:51:53.139 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-20 20:52:53.131 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-20 20:53:53.127 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-20 20:54:23.260 | INFO     | app.services.account_service:get_proxy_ips:1043 - 获取有效代理IP列表（包含绑定计数）
2025-07-20 20:54:23.260 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-20 20:54:23.513 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-20 20:54:23.514 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-20 20:54:23.525 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-20 20:54:23.527 | INFO     | app.services.account_service:get_proxy_ips:1095 - 成功获取 1 个有效代理（已附带绑定计数）
2025-07-20 20:54:23.528 | INFO     | app.controllers.account_controller:get_proxy_ips:566 - 获取到1个有效代理IP
2025-07-20 20:54:38.267 | INFO     | app.services.account_service:update_account_profile:490 - 更新账户资料: 2
2025-07-20 20:54:38.267 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-20 20:54:40.519 | INFO     | core.telegram.user_manager:update_profile:89 - 正在更新用户资料: +***********
2025-07-20 20:55:01.782 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-20 20:55:31.790 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-20 20:55:32.352 | INFO     | core.telegram.user_manager:update_profile:120 - 更新用户资料成功: +***********
2025-07-20 20:55:53.128 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-20 20:56:05.136 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-20 20:56:05.163 | INFO     | app.services.account_service:get_accounts_by_group:311 - 获取分组 2 的账户成功, 共 2 个
2025-07-20 20:56:05.163 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-20 20:56:05.164 | DEBUG    | ui.views.account_view:_on_accounts_loaded:578 - 账户加载完成: 2个账户
2025-07-20 20:56:05.659 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-20 20:56:05.670 | INFO     | app.services.account_service:get_all_accounts:289 - 获取所有账户成功, 共 2 个
2025-07-20 20:56:05.670 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-20 20:56:05.672 | DEBUG    | ui.views.account_view:_on_accounts_loaded:578 - 账户加载完成: 2个账户
2025-07-20 20:56:05.694 | DEBUG    | ui.views.account_view:_update_group_counts:186 - 分组计数已更新: {2: 2}
2025-07-20 20:56:08.355 | INFO     | app.services.account_service:get_proxy_ips:1043 - 获取有效代理IP列表（包含绑定计数）
2025-07-20 20:56:08.355 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-20 20:56:08.586 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-20 20:56:08.588 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-20 20:56:08.593 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-20 20:56:08.595 | INFO     | app.services.account_service:get_proxy_ips:1095 - 成功获取 1 个有效代理（已附带绑定计数）
2025-07-20 20:56:08.596 | INFO     | app.controllers.account_controller:get_proxy_ips:566 - 获取到1个有效代理IP
2025-07-20 20:56:16.701 | INFO     | app.services.account_service:update_account_profile:490 - 更新账户资料: 2
2025-07-20 20:56:16.701 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-20 20:56:17.646 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-20 20:56:19.553 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-20 20:56:19.835 | INFO     | app.services.account_service:get_all_accounts:289 - 获取所有账户成功, 共 2 个
2025-07-20 20:56:19.836 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-20 20:56:19.839 | DEBUG    | ui.views.account_view:_on_accounts_loaded:578 - 账户加载完成: 2个账户
2025-07-20 20:56:20.110 | DEBUG    | ui.views.account_view:_update_group_counts:186 - 分组计数已更新: {2: 2}
2025-07-20 20:56:53.128 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-20 20:57:53.140 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-20 20:58:00.923 | INFO     | app.services.account_service:get_proxy_ips:1043 - 获取有效代理IP列表（包含绑定计数）
2025-07-20 20:58:00.924 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-20 20:58:00.948 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-20 20:58:00.950 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-20 20:58:00.957 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-20 20:58:00.959 | INFO     | app.services.account_service:get_proxy_ips:1095 - 成功获取 1 个有效代理（已附带绑定计数）
2025-07-20 20:58:00.959 | INFO     | app.controllers.account_controller:get_proxy_ips:566 - 获取到1个有效代理IP
2025-07-20 20:58:33.652 | INFO     | app.services.account_service:update_account_profile:490 - 更新账户资料: 1
2025-07-20 20:58:33.653 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-20 20:58:40.014 | INFO     | core.telegram.user_manager:update_profile:89 - 正在更新用户资料: +***********
2025-07-20 20:58:53.104 | ERROR    | core.telegram.user_manager:update_profile:125 - 更新用户资料错误: +***********, 更新用户资料失败: UpdateProfileRequest.__init__() got an unexpected keyword argument 'bio'
2025-07-20 20:58:53.148 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-20 20:58:53.754 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-20 20:58:55.314 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-20 20:58:55.328 | INFO     | app.services.account_service:get_all_accounts:289 - 获取所有账户成功, 共 2 个
2025-07-20 20:58:55.328 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-20 20:58:55.332 | DEBUG    | ui.views.account_view:_on_accounts_loaded:578 - 账户加载完成: 2个账户
2025-07-20 20:58:55.357 | DEBUG    | ui.views.account_view:_update_group_counts:186 - 分组计数已更新: {2: 2}
2025-07-20 20:59:02.626 | INFO     | app.services.account_service:refresh_account_info:606 - 刷新账户信息: 2
2025-07-20 20:59:02.626 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-20 20:59:02.641 | INFO     | core.telegram.user_manager:get_user_info:41 - 正在获取用户信息: +***********
2025-07-20 20:59:03.584 | INFO     | core.telegram.user_manager:get_user_info:66 - 获取用户信息成功: +***********
2025-07-20 20:59:03.880 | INFO     | data.repositories.account_repo:update_account:378 - 更新账户成功: 2
2025-07-20 20:59:03.894 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-20 20:59:04.867 | INFO     | app.services.account_service:refresh_account_info:606 - 刷新账户信息: 1
2025-07-20 20:59:04.867 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-20 20:59:04.970 | INFO     | core.telegram.user_manager:get_user_info:41 - 正在获取用户信息: +***********
2025-07-20 20:59:05.931 | INFO     | core.telegram.user_manager:get_user_info:66 - 获取用户信息成功: +***********
2025-07-20 20:59:06.209 | INFO     | data.repositories.account_repo:update_account:378 - 更新账户成功: 1
2025-07-20 20:59:06.218 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-20 20:59:53.128 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-20 21:00:53.129 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-20 21:20:19.342 | INFO     | utils.logger:init_logger:98 - 日志系统初始化完成，默认级别: DEBUG，日志目录: H:\PyProject\TeleTest\APPDATA\logs
2025-07-20 21:20:21.090 | INFO     | data.database:init_database:31 - 初始化数据库连接: H:\PyProject\TeleTest\APPDATA\database\telegram_manager.db
2025-07-20 21:20:21.120 | DEBUG    | data.database:init_database:58 - 创建数据库表
2025-07-20 21:20:21.134 | INFO     | data.database:init_database:61 - 数据库初始化完成
2025-07-20 21:20:22.217 | DEBUG    | app.controllers.auth_controller:initialize_software:43 - 控制器: 初始化软件 app_id=1000, version=1.2.7
2025-07-20 21:20:22.217 | DEBUG    | core.auth.api_service:init_software:55 - 发送初始化请求: https://server.xile188.com/api/user/1000/windowns10/1.2.7/ini, params={}
2025-07-20 21:20:22.944 | DEBUG    | core.auth.api_service:init_software:66 - 初始化数据解密成功: {'new_content': '- 新增功能\n  - 1.新增图片上传功能\n  - 2.新增软件文档\n  - 3.新增软件自动更新，后续更新会自动检测版本更新，无需再下载更新包\n  - 4.新增设置账户信息及发信控制\n- 修复bug\n  - 删除监听任务时，不删除对应任务采集到的数据。\n  - 监听设置群无法发送到目标群\n  - 导出监听数据时应当导出单个任务而非所有数据\n- 优化记录\n  - 优化发信规则，直接调取符合要求的账户发送，无账户时才需要等待延时。\n  - 完善软件设置', 'new_url': None, 'notice': {'id': '2', 'content': '公告内容测试3', 'time': '**********'}}
2025-07-20 21:20:22.959 | INFO     | app.controllers.auth_controller:login:61 - 控制器: 用户登录 account=demo01
2025-07-20 21:20:25.984 | INFO     | core.auth.api_service:login:127 - 用户登录: account=demo01
2025-07-20 21:20:26.191 | INFO     | core.auth.api_service:get_user_info:347 - 获取用户信息
2025-07-20 21:20:26.429 | INFO     | core.auth.api_service:get_user_info:375 - 获取用户信息成功
2025-07-20 21:20:26.443 | INFO     | __main__:on_auth_completed:91 - 用户登录成功，正在启动主窗口...
2025-07-20 21:20:26.476 | INFO     | core.telegram.client_worker:__init__:61 - Telegram客户端工作线程初始化
2025-07-20 21:20:26.476 | INFO     | utils.vip_checker:initialize:25 - VIP检查器已初始化，等待登录后开始检查
2025-07-20 21:20:26.476 | INFO     | ui.main_window:_initialize_core_components:108 - MainWindow: 初始化核心组件...
2025-07-20 21:20:26.477 | INFO     | app.services.account_service:__init__:41 - 账户服务初始化
2025-07-20 21:20:26.477 | INFO     | core.telegram.client_worker:run:65 - Telegram客户端工作线程开始运行
2025-07-20 21:20:26.478 | INFO     | app.controllers.account_controller:__init__:81 - 账户控制器初始化
2025-07-20 21:20:26.478 | INFO     | core.proxy.proxy_task_manager:run:284 - 代理任务管理器线程启动
2025-07-20 21:20:26.478 | INFO     | app.services.monitor_service:__init__:39 - 初始化监控任务服务
2025-07-20 21:20:26.479 | INFO     | core.monitor.filter:__init__:26 - 初始化消息过滤器
2025-07-20 21:20:26.479 | INFO     | core.monitor.extractor:__init__:24 - 初始化关键词提取器
2025-07-20 21:20:26.479 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-07-20 21:20:26.481 | INFO     | core.monitor.notifier:__init__:90 - 初始化通知管理器
2025-07-20 21:20:26.481 | INFO     | core.telegram.client_manager:__init__:66 - Telegram客户端管理器初始化
2025-07-20 21:20:26.481 | INFO     | app.controllers.telegram_monitor_controller:__init__:64 - 初始化 Telegram 监控控制器
2025-07-20 21:20:26.481 | INFO     | core.telegram.user_manager:__init__:30 - Telegram用户管理器初始化
2025-07-20 21:20:26.481 | INFO     | core.telegram.group_manager:__init__:36 - Telegram群组管理器初始化
2025-07-20 21:20:26.481 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-07-20 21:20:26.481 | INFO     | core.telegram.monitor_manager:__init__:26 - Telegram监控管理器初始化
2025-07-20 21:20:26.481 | INFO     | core.telegram.telegram_service:__init__:39 - Telegram服务初始化完成
2025-07-20 21:20:26.482 | DEBUG    | app.controllers.telegram_monitor_controller:__init__:82 - 控制器初始化完成
2025-07-20 21:20:26.782 | INFO     | ui.views.monitor_view:__init__:48 - 初始化监控视图
2025-07-20 21:20:26.782 | DEBUG    | ui.views.monitor_view:_connect_signals:80 - 连接控制器信号
2025-07-20 21:20:26.964 | INFO     | ui.views.proxy_view:__init__:96 - 初始化视图，加载第一页代理数据
2025-07-20 21:20:27.252 | INFO     | __main__:on_auth_completed:95 - 主窗口已启动
2025-07-20 21:20:27.311 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:88 - 尝试连接监听日志信号...
2025-07-20 21:20:27.311 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:90 - 监听日志信号连接成功
2025-07-20 21:20:27.312 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-20 21:20:27.316 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-20 21:20:27.321 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:145 - 加载监控任务列表 (搜索关键词: '')
2025-07-20 21:20:27.321 | DEBUG    | ui.views.monitor_view:_on_loading_started:620 - 加载中: 正在加载任务列表...
2025-07-20 21:20:27.321 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-20 21:20:27.327 | INFO     | app.services.proxy_service:refresh_proxies:712 - 刷新代理列表: offset=0, limit=20
2025-07-20 21:20:27.328 | INFO     | core.proxy.proxy_task_manager:submit_task:172 - 提交耗时任务到子线程: refresh_proxies_0_20
2025-07-20 21:20:27.328 | DEBUG    | app.services.proxy_service:get_proxies:500 - 获取代理列表: offset=0, limit=20
2025-07-20 21:20:27.328 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-20 21:20:27.329 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-20 21:20:27.333 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-20 21:20:27.356 | INFO     | app.controllers.invite_controller:get_invite_tasks:38 - 获取邀请任务列表, status=None
2025-07-20 21:20:27.356 | INFO     | app.services.invite_service:get_invite_tasks:121 - 获取邀请任务列表, status=None
2025-07-20 21:20:27.356 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-20 21:20:27.357 | INFO     | app.services.invite_service:task_initialize:39 - 初始化邀请服务，重置运行中的任务为暂停状态
2025-07-20 21:20:27.357 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-20 21:20:27.361 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-20 21:20:27.363 | INFO     | core.proxy.proxy_task_manager:task_wrapper:199 - 任务 refresh_proxies_0_20 完成
2025-07-20 21:20:27.363 | INFO     | utils.vip_checker:start_checking:37 - 开始VIP状态检查
2025-07-20 21:20:27.363 | DEBUG    | utils.vip_checker:_check_vip_status:61 - 正在检查VIP状态...
2025-07-20 21:20:27.363 | INFO     | core.auth.api_service:verify_vip:425 - 会员验证接口调用
2025-07-20 21:20:27.501 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-20 21:20:27.502 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-20 21:20:27.509 | DEBUG    | ui.views.monitor_view:_refresh_task_list:175 - 刷新任务列表，共0个任务
2025-07-20 21:20:27.509 | INFO     | ui.views.monitor_view:_refresh_task_list:189 - 没有找到监控任务
2025-07-20 21:20:27.510 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-20 21:20:27.512 | INFO     | app.services.invite_service:task_initialize:48 - 成功重置 0 个任务状态
2025-07-20 21:20:27.512 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-20 21:20:27.515 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-20 21:20:27.518 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-20 21:20:27.521 | INFO     | app.services.account_service:get_all_groups:95 - 获取所有账户分组成功, 共 2 个
2025-07-20 21:20:27.521 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-20 21:20:27.521 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-20 21:20:27.525 | DEBUG    | ui.views.account_view:_on_groups_loaded:519 - 分组加载完成: 2个分组
2025-07-20 21:20:27.541 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-20 21:20:27.666 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-20 21:20:27.667 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-20 21:20:27.668 | INFO     | app.services.account_service:get_all_accounts:289 - 获取所有账户成功, 共 2 个
2025-07-20 21:20:27.668 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-20 21:20:27.670 | INFO     | app.controllers.telegram_monitor_controller:_load_total_users_count_internal:191 - 成功加载总体统计数据: 总用户数 23, 今日采集 0, 日均采集 23, 运行天数 1
2025-07-20 21:20:27.670 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:156 - 成功加载 0 个任务
2025-07-20 21:20:27.671 | DEBUG    | ui.views.monitor_view:_on_loading_finished:625 - 加载完成
2025-07-20 21:20:27.671 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-07-20 21:20:27.676 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-20 21:20:27.682 | DEBUG    | ui.views.account_view:_on_accounts_loaded:652 - 账户加载完成: 2个账户
2025-07-20 21:20:27.703 | DEBUG    | ui.views.account_view:_update_group_counts:188 - 分组计数已更新: {2: 2}
2025-07-20 21:20:27.707 | INFO     | ui.views.account_view:_auto_login_accounts:707 - 开始调用控制器进行 2 个账户的自动登录。
2025-07-20 21:20:27.707 | INFO     | app.services.account_service:batch_auto_login:1164 - 服务层：开始批量自动登录 2 个账户, 最大并发: 5
2025-07-20 21:20:27.707 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-20 21:20:27.724 | INFO     | app.services.account_service:get_all_accounts:289 - 获取所有账户成功, 共 2 个
2025-07-20 21:20:27.725 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-20 21:20:27.727 | INFO     | app.services.account_service:batch_auto_login:1203 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-07-20 21:20:27.727 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-20 21:20:27.729 | DEBUG    | ui.views.account_view:_update_group_counts:188 - 分组计数已更新: {2: 2}
2025-07-20 21:20:27.729 | DEBUG    | ui.views.account_view:_init_cache:155 - 账户缓存初始化完成，共 2 个账户
2025-07-20 21:20:27.734 | DEBUG    | ui.views.account_view:_on_accounts_loaded:652 - 账户加载完成: 2个账户
2025-07-20 21:20:27.759 | DEBUG    | ui.views.account_view:_update_group_counts:188 - 分组计数已更新: {2: 2}
2025-07-20 21:20:27.769 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-20 21:20:27.775 | INFO     | app.services.account_service:batch_auto_login:1203 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-07-20 21:20:27.775 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-20 21:20:27.779 | INFO     | app.services.account_service:batch_auto_login:1273 - 服务层：准备调用核心层处理 2 个账户的自动登录。
2025-07-20 21:20:27.780 | INFO     | app.services.account_service:batch_auto_login:1283 - 服务层：设置核心层任务超时为 120 秒。
2025-07-20 21:20:27.780 | INFO     | core.telegram.client_manager:batch_auto_login:1230 - 开始批量自动登录, 共 2 个账户, 最大并发 5
2025-07-20 21:20:27.780 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:271 - 创建客户端: H:\PyProject\TeleTest\APPDATA\sessions\+***********, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-20 21:20:27.781 | DEBUG    | core.telegram.client_manager:get_proxy:96 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-20 21:20:27.781 | DEBUG    | core.telegram.client_manager:get_proxy:143 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-07-20 21:20:27.785 | INFO     | core.auth.api_service:verify_vip:438 - 会员验证成功
2025-07-20 21:20:27.785 | INFO     | utils.vip_checker:_check_vip_status:76 - VIP状态有效
2025-07-20 21:20:27.788 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:271 - 创建客户端: H:\PyProject\TeleTest\APPDATA\sessions\+***********, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-20 21:20:27.789 | DEBUG    | core.telegram.client_manager:get_proxy:96 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-20 21:20:27.790 | DEBUG    | core.telegram.client_manager:get_proxy:143 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-07-20 21:20:27.820 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-20 21:20:27.897 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-20 21:20:27.903 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-07-20 21:20:27.905 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-07-20 21:20:27.906 | INFO     | ui.views.proxy_view:_load_initial_data:1044 - 初始数据加载完成，共 1 个代理
2025-07-20 21:20:27.950 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-20 21:20:32.978 | INFO     | core.telegram.client_manager:_connect_client:219 - 连接成功!
2025-07-20 21:20:34.050 | INFO     | core.telegram.client_manager:_verify_client_authorization:1205 - 账户已授权: +***********
2025-07-20 21:20:41.375 | INFO     | core.telegram.client_manager:_connect_client:219 - 连接成功!
2025-07-20 21:20:43.292 | INFO     | core.telegram.client_manager:_verify_client_authorization:1205 - 账户已授权: +***********
2025-07-20 21:20:45.298 | INFO     | core.telegram.client_manager:batch_auto_login:1379 - 批量登录完成: 总计 2, 成功 2, 失败 0
2025-07-20 21:20:45.783 | INFO     | app.services.account_service:batch_auto_login:1304 - 服务层：核心层登录完成: 成功 2, 失败 0 / 2
2025-07-20 21:21:26.428 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-20 21:22:26.424 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-20 21:23:08.335 | INFO     | app.services.account_service:refresh_account_info:606 - 刷新账户信息: 1
2025-07-20 21:23:08.336 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-20 21:23:08.346 | INFO     | core.telegram.user_manager:get_user_info:41 - 正在获取用户信息: +***********
2025-07-20 21:23:09.736 | INFO     | core.telegram.user_manager:get_user_info:66 - 获取用户信息成功: +***********
2025-07-20 21:23:10.368 | INFO     | data.repositories.account_repo:update_account:378 - 更新账户成功: 1
2025-07-20 21:23:10.376 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-20 21:23:10.381 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-20 21:23:10.392 | INFO     | ui.views.account_view:_update_single_account_display:337 - 已实时更新账户显示: +***********
2025-07-20 21:23:10.392 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-20 21:23:26.422 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-20 21:23:29.491 | INFO     | app.services.account_service:refresh_account_info:606 - 刷新账户信息: 1
2025-07-20 21:23:29.491 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-20 21:23:29.500 | INFO     | core.telegram.user_manager:get_user_info:41 - 正在获取用户信息: +***********
2025-07-20 21:23:30.680 | INFO     | core.telegram.user_manager:get_user_info:66 - 获取用户信息成功: +***********
2025-07-20 21:23:30.739 | INFO     | data.repositories.account_repo:update_account:378 - 更新账户成功: 1
2025-07-20 21:23:30.747 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-20 21:23:30.752 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-20 21:23:30.759 | INFO     | ui.views.account_view:_update_single_account_display:337 - 已实时更新账户显示: +***********
2025-07-20 21:23:30.759 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-20 23:41:00.758 | INFO     | utils.logger:init_logger:98 - 日志系统初始化完成，默认级别: DEBUG，日志目录: H:\PyProject\TeleTest\APPDATA\logs
2025-07-20 23:41:02.692 | INFO     | data.database:init_database:31 - 初始化数据库连接: H:\PyProject\TeleTest\APPDATA\database\telegram_manager.db
2025-07-20 23:41:02.720 | DEBUG    | data.database:init_database:58 - 创建数据库表
2025-07-20 23:41:02.733 | INFO     | data.database:init_database:61 - 数据库初始化完成
2025-07-20 23:41:03.994 | DEBUG    | app.controllers.auth_controller:initialize_software:43 - 控制器: 初始化软件 app_id=1000, version=1.2.7
2025-07-20 23:41:03.994 | DEBUG    | core.auth.api_service:init_software:55 - 发送初始化请求: https://server.xile188.com/api/user/1000/windowns10/1.2.7/ini, params={}
2025-07-20 23:41:04.478 | DEBUG    | core.auth.api_service:init_software:66 - 初始化数据解密成功: {'new_content': '- 新增功能\n  - 1.新增图片上传功能\n  - 2.新增软件文档\n  - 3.新增软件自动更新，后续更新会自动检测版本更新，无需再下载更新包\n  - 4.新增设置账户信息及发信控制\n- 修复bug\n  - 删除监听任务时，不删除对应任务采集到的数据。\n  - 监听设置群无法发送到目标群\n  - 导出监听数据时应当导出单个任务而非所有数据\n- 优化记录\n  - 优化发信规则，直接调取符合要求的账户发送，无账户时才需要等待延时。\n  - 完善软件设置', 'new_url': None, 'notice': {'id': '2', 'content': '公告内容测试3', 'time': '**********'}}
2025-07-20 23:41:04.489 | INFO     | app.controllers.auth_controller:login:61 - 控制器: 用户登录 account=demo01
2025-07-20 23:41:07.950 | INFO     | core.auth.api_service:login:127 - 用户登录: account=demo01
2025-07-20 23:41:08.351 | INFO     | core.auth.api_service:get_user_info:347 - 获取用户信息
2025-07-20 23:41:10.596 | INFO     | core.auth.api_service:get_user_info:375 - 获取用户信息成功
2025-07-20 23:41:10.601 | INFO     | __main__:on_auth_completed:91 - 用户登录成功，正在启动主窗口...
2025-07-20 23:41:10.633 | INFO     | core.telegram.client_worker:__init__:61 - Telegram客户端工作线程初始化
2025-07-20 23:41:10.634 | INFO     | utils.vip_checker:initialize:25 - VIP检查器已初始化，等待登录后开始检查
2025-07-20 23:41:10.634 | INFO     | ui.main_window:_initialize_core_components:108 - MainWindow: 初始化核心组件...
2025-07-20 23:41:10.634 | INFO     | core.telegram.client_worker:run:65 - Telegram客户端工作线程开始运行
2025-07-20 23:41:10.635 | INFO     | app.services.account_service:__init__:41 - 账户服务初始化
2025-07-20 23:41:10.635 | INFO     | core.proxy.proxy_task_manager:run:284 - 代理任务管理器线程启动
2025-07-20 23:41:10.635 | INFO     | app.controllers.account_controller:__init__:81 - 账户控制器初始化
2025-07-20 23:41:10.636 | INFO     | app.services.monitor_service:__init__:39 - 初始化监控任务服务
2025-07-20 23:41:10.636 | INFO     | core.monitor.filter:__init__:26 - 初始化消息过滤器
2025-07-20 23:41:10.637 | INFO     | core.telegram.client_manager:__init__:66 - Telegram客户端管理器初始化
2025-07-20 23:41:10.636 | INFO     | core.monitor.extractor:__init__:24 - 初始化关键词提取器
2025-07-20 23:41:10.638 | INFO     | core.telegram.user_manager:__init__:30 - Telegram用户管理器初始化
2025-07-20 23:41:10.638 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-07-20 23:41:10.638 | INFO     | core.telegram.group_manager:__init__:36 - Telegram群组管理器初始化
2025-07-20 23:41:10.638 | INFO     | core.monitor.notifier:__init__:90 - 初始化通知管理器
2025-07-20 23:41:10.638 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-07-20 23:41:10.639 | INFO     | app.controllers.telegram_monitor_controller:__init__:64 - 初始化 Telegram 监控控制器
2025-07-20 23:41:10.639 | INFO     | core.telegram.monitor_manager:__init__:26 - Telegram监控管理器初始化
2025-07-20 23:41:10.639 | DEBUG    | app.controllers.telegram_monitor_controller:__init__:82 - 控制器初始化完成
2025-07-20 23:41:10.639 | INFO     | core.telegram.telegram_service:__init__:39 - Telegram服务初始化完成
2025-07-20 23:41:10.941 | INFO     | ui.views.monitor_view:__init__:48 - 初始化监控视图
2025-07-20 23:41:10.941 | DEBUG    | ui.views.monitor_view:_connect_signals:80 - 连接控制器信号
2025-07-20 23:41:11.176 | INFO     | ui.views.proxy_view:__init__:96 - 初始化视图，加载第一页代理数据
2025-07-20 23:41:11.477 | INFO     | __main__:on_auth_completed:95 - 主窗口已启动
2025-07-20 23:41:11.533 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:88 - 尝试连接监听日志信号...
2025-07-20 23:41:11.533 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:90 - 监听日志信号连接成功
2025-07-20 23:41:11.534 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-20 23:41:11.539 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-20 23:41:11.544 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:145 - 加载监控任务列表 (搜索关键词: '')
2025-07-20 23:41:11.544 | DEBUG    | ui.views.monitor_view:_on_loading_started:620 - 加载中: 正在加载任务列表...
2025-07-20 23:41:11.544 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-20 23:41:11.554 | INFO     | app.services.proxy_service:refresh_proxies:712 - 刷新代理列表: offset=0, limit=20
2025-07-20 23:41:11.554 | INFO     | core.proxy.proxy_task_manager:submit_task:172 - 提交耗时任务到子线程: refresh_proxies_0_20
2025-07-20 23:41:11.554 | DEBUG    | app.services.proxy_service:get_proxies:500 - 获取代理列表: offset=0, limit=20
2025-07-20 23:41:11.555 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-20 23:41:11.555 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-20 23:41:11.559 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-20 23:41:11.593 | INFO     | app.controllers.invite_controller:get_invite_tasks:38 - 获取邀请任务列表, status=None
2025-07-20 23:41:11.593 | INFO     | app.services.invite_service:get_invite_tasks:121 - 获取邀请任务列表, status=None
2025-07-20 23:41:11.593 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-20 23:41:11.594 | INFO     | app.services.invite_service:task_initialize:39 - 初始化邀请服务，重置运行中的任务为暂停状态
2025-07-20 23:41:11.596 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-20 23:41:11.598 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-20 23:41:11.599 | INFO     | core.proxy.proxy_task_manager:task_wrapper:199 - 任务 refresh_proxies_0_20 完成
2025-07-20 23:41:11.600 | INFO     | utils.vip_checker:start_checking:37 - 开始VIP状态检查
2025-07-20 23:41:11.600 | DEBUG    | utils.vip_checker:_check_vip_status:61 - 正在检查VIP状态...
2025-07-20 23:41:11.600 | INFO     | core.auth.api_service:verify_vip:425 - 会员验证接口调用
2025-07-20 23:41:11.835 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-20 23:41:11.843 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-20 23:41:11.848 | DEBUG    | ui.views.monitor_view:_refresh_task_list:175 - 刷新任务列表，共0个任务
2025-07-20 23:41:11.849 | INFO     | ui.views.monitor_view:_refresh_task_list:189 - 没有找到监控任务
2025-07-20 23:41:11.849 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-20 23:41:11.852 | INFO     | app.services.invite_service:task_initialize:48 - 成功重置 0 个任务状态
2025-07-20 23:41:11.852 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-20 23:41:11.855 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-20 23:41:11.859 | INFO     | app.services.account_service:get_all_groups:95 - 获取所有账户分组成功, 共 2 个
2025-07-20 23:41:11.859 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-20 23:41:11.861 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-20 23:41:11.863 | DEBUG    | ui.views.account_view:_on_groups_loaded:519 - 分组加载完成: 2个分组
2025-07-20 23:41:11.884 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-20 23:41:11.887 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-20 23:41:11.966 | INFO     | app.services.account_service:get_all_accounts:289 - 获取所有账户成功, 共 2 个
2025-07-20 23:41:11.966 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-20 23:41:11.968 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-20 23:41:11.968 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-20 23:41:11.969 | DEBUG    | ui.views.account_view:_on_accounts_loaded:652 - 账户加载完成: 2个账户
2025-07-20 23:41:12.068 | DEBUG    | ui.views.account_view:_update_group_counts:188 - 分组计数已更新: {2: 2}
2025-07-20 23:41:12.075 | INFO     | ui.views.account_view:_auto_login_accounts:707 - 开始调用控制器进行 2 个账户的自动登录。
2025-07-20 23:41:12.075 | INFO     | app.services.account_service:batch_auto_login:1164 - 服务层：开始批量自动登录 2 个账户, 最大并发: 5
2025-07-20 23:41:12.075 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-20 23:41:12.080 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-20 23:41:12.082 | INFO     | core.auth.api_service:verify_vip:438 - 会员验证成功
2025-07-20 23:41:12.082 | INFO     | utils.vip_checker:_check_vip_status:76 - VIP状态有效
2025-07-20 23:41:12.084 | INFO     | app.controllers.telegram_monitor_controller:_load_total_users_count_internal:191 - 成功加载总体统计数据: 总用户数 23, 今日采集 0, 日均采集 23, 运行天数 1
2025-07-20 23:41:12.085 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:156 - 成功加载 0 个任务
2025-07-20 23:41:12.085 | DEBUG    | ui.views.monitor_view:_on_loading_finished:625 - 加载完成
2025-07-20 23:41:12.092 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-07-20 23:41:12.110 | INFO     | app.services.account_service:get_all_accounts:289 - 获取所有账户成功, 共 2 个
2025-07-20 23:41:12.110 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-20 23:41:12.111 | INFO     | app.services.account_service:batch_auto_login:1203 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-07-20 23:41:12.111 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-20 23:41:12.114 | DEBUG    | ui.views.account_view:_update_group_counts:188 - 分组计数已更新: {2: 2}
2025-07-20 23:41:12.115 | DEBUG    | ui.views.account_view:_init_cache:155 - 账户缓存初始化完成，共 2 个账户
2025-07-20 23:41:12.122 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-20 23:41:12.125 | DEBUG    | ui.views.account_view:_on_accounts_loaded:652 - 账户加载完成: 2个账户
2025-07-20 23:41:12.164 | DEBUG    | ui.views.account_view:_update_group_counts:188 - 分组计数已更新: {2: 2}
2025-07-20 23:41:12.181 | INFO     | app.services.account_service:batch_auto_login:1203 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-07-20 23:41:12.181 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-20 23:41:12.183 | INFO     | app.services.account_service:batch_auto_login:1273 - 服务层：准备调用核心层处理 2 个账户的自动登录。
2025-07-20 23:41:12.183 | INFO     | app.services.account_service:batch_auto_login:1283 - 服务层：设置核心层任务超时为 120 秒。
2025-07-20 23:41:12.183 | INFO     | core.telegram.client_manager:batch_auto_login:1230 - 开始批量自动登录, 共 2 个账户, 最大并发 5
2025-07-20 23:41:12.184 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:271 - 创建客户端: H:\PyProject\TeleTest\APPDATA\sessions\+***********, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-20 23:41:12.184 | DEBUG    | core.telegram.client_manager:get_proxy:96 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-20 23:41:12.185 | DEBUG    | core.telegram.client_manager:get_proxy:143 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-07-20 23:41:12.193 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:271 - 创建客户端: H:\PyProject\TeleTest\APPDATA\sessions\+***********, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-20 23:41:12.193 | DEBUG    | core.telegram.client_manager:get_proxy:96 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-20 23:41:12.194 | DEBUG    | core.telegram.client_manager:get_proxy:143 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-07-20 23:41:12.220 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-20 23:41:12.339 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-20 23:41:12.404 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-20 23:41:12.428 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-07-20 23:41:12.430 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-07-20 23:41:12.432 | INFO     | ui.views.proxy_view:_load_initial_data:1044 - 初始数据加载完成，共 1 个代理
2025-07-20 23:41:16.226 | INFO     | core.telegram.client_manager:_connect_client:219 - 连接成功!
2025-07-20 23:41:17.310 | INFO     | core.telegram.client_manager:_verify_client_authorization:1205 - 账户已授权: +***********
2025-07-20 23:41:17.937 | INFO     | core.telegram.client_manager:_connect_client:219 - 连接成功!
2025-07-20 23:41:19.215 | INFO     | core.telegram.client_manager:_verify_client_authorization:1205 - 账户已授权: +***********
2025-07-20 23:41:21.230 | INFO     | core.telegram.client_manager:batch_auto_login:1379 - 批量登录完成: 总计 2, 成功 2, 失败 0
2025-07-20 23:41:22.217 | INFO     | app.services.account_service:batch_auto_login:1304 - 服务层：核心层登录完成: 成功 2, 失败 0 / 2
2025-07-20 23:41:44.101 | INFO     | app.services.account_service:get_proxy_ips:1043 - 获取有效代理IP列表（包含绑定计数）
2025-07-20 23:41:44.102 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-20 23:41:44.307 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-20 23:41:44.308 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-20 23:41:44.311 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-20 23:41:44.312 | INFO     | app.services.account_service:get_proxy_ips:1095 - 成功获取 1 个有效代理（已附带绑定计数）
2025-07-20 23:41:44.312 | INFO     | app.controllers.account_controller:get_proxy_ips:566 - 获取到1个有效代理IP
2025-07-20 23:42:10.595 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-20 23:42:24.034 | INFO     | ui.main_window:closeEvent:380 - MainWindow: 接收到关闭事件
2025-07-20 23:42:24.047 | INFO     | ui.main_window:_cleanup_before_quit:266 - MainWindow: 执行清理资源...
2025-07-20 23:42:24.047 | INFO     | core.telegram.client_worker:stop:108 - 正在停止Telegram客户端工作线程
2025-07-20 23:42:24.059 | INFO     | core.telegram.client_manager:cleanup_async:1150 - 开始清理资源并断开所有连接
2025-07-20 23:42:24.060 | INFO     | core.telegram.client_manager:disconnect_all_clients:735 - 正在断开所有客户端连接
2025-07-20 23:42:24.060 | INFO     | core.telegram.client_manager:_safe_disconnect:756 - 正在断开客户端连接: +***********
2025-07-20 23:42:24.060 | INFO     | core.telegram.client_manager:_safe_disconnect:756 - 正在断开客户端连接: +***********
2025-07-20 23:42:24.072 | INFO     | core.telegram.client_manager:_safe_disconnect:760 - 断开客户端连接成功: +***********
2025-07-20 23:42:24.072 | INFO     | core.telegram.client_manager:_safe_disconnect:760 - 断开客户端连接成功: +***********
2025-07-20 23:42:24.073 | INFO     | core.telegram.client_manager:cleanup_async:1169 - 资源清理完成
2025-07-20 23:42:24.565 | INFO     | core.telegram.client_manager:cleanup_async:1150 - 开始清理资源并断开所有连接
2025-07-20 23:42:24.565 | INFO     | core.telegram.client_manager:disconnect_all_clients:735 - 正在断开所有客户端连接
2025-07-20 23:42:24.565 | INFO     | core.telegram.client_manager:cleanup_async:1169 - 资源清理完成
2025-07-20 23:42:25.066 | INFO     | core.telegram.client_worker:run:97 - Telegram客户端工作线程已退出
2025-07-20 23:42:25.067 | INFO     | core.telegram.client_worker:stop:122 - Telegram客户端工作线程已停止
2025-07-20 23:42:25.067 | INFO     | ui.main_window:_cleanup_before_quit:275 - TelegramClientWorker 已停止。
2025-07-20 23:42:25.067 | INFO     | ui.main_window:_cleanup_before_quit:279 - MainWindow 清理完成
2025-07-20 23:42:25.084 | INFO     | __main__:main:109 - 应用程序已正常退出
