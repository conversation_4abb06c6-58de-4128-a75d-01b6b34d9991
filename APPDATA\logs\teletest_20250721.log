2025-07-21 00:13:01.690 | INFO     | utils.logger:init_logger:98 - 日志系统初始化完成，默认级别: DEBUG，日志目录: H:\PyProject\TeleTest\APPDATA\logs
2025-07-21 00:13:03.267 | INFO     | data.database:init_database:31 - 初始化数据库连接: H:\PyProject\TeleTest\APPDATA\database\telegram_manager.db
2025-07-21 00:13:03.286 | DEBUG    | data.database:init_database:58 - 创建数据库表
2025-07-21 00:13:03.300 | INFO     | data.database:init_database:61 - 数据库初始化完成
2025-07-21 00:13:04.336 | DEBUG    | app.controllers.auth_controller:initialize_software:43 - 控制器: 初始化软件 app_id=1000, version=1.2.7
2025-07-21 00:13:04.337 | DEBUG    | core.auth.api_service:init_software:55 - 发送初始化请求: https://server.xile188.com/api/user/1000/windowns10/1.2.7/ini, params={}
2025-07-21 00:13:04.821 | DEBUG    | core.auth.api_service:init_software:66 - 初始化数据解密成功: {'new_content': '- 新增功能\n  - 1.新增图片上传功能\n  - 2.新增软件文档\n  - 3.新增软件自动更新，后续更新会自动检测版本更新，无需再下载更新包\n  - 4.新增设置账户信息及发信控制\n- 修复bug\n  - 删除监听任务时，不删除对应任务采集到的数据。\n  - 监听设置群无法发送到目标群\n  - 导出监听数据时应当导出单个任务而非所有数据\n- 优化记录\n  - 优化发信规则，直接调取符合要求的账户发送，无账户时才需要等待延时。\n  - 完善软件设置', 'new_url': None, 'notice': {'id': '2', 'content': '公告内容测试3', 'time': '**********'}}
2025-07-21 00:13:04.828 | INFO     | app.controllers.auth_controller:login:61 - 控制器: 用户登录 account=demo01
2025-07-21 00:13:07.884 | INFO     | core.auth.api_service:login:127 - 用户登录: account=demo01
2025-07-21 00:13:08.143 | INFO     | core.auth.api_service:get_user_info:347 - 获取用户信息
2025-07-21 00:13:08.340 | INFO     | core.auth.api_service:get_user_info:375 - 获取用户信息成功
2025-07-21 00:13:08.347 | INFO     | __main__:on_auth_completed:91 - 用户登录成功，正在启动主窗口...
2025-07-21 00:13:08.371 | INFO     | core.telegram.client_worker:__init__:61 - Telegram客户端工作线程初始化
2025-07-21 00:13:08.371 | INFO     | utils.vip_checker:initialize:25 - VIP检查器已初始化，等待登录后开始检查
2025-07-21 00:13:08.371 | INFO     | ui.main_window:_initialize_core_components:108 - MainWindow: 初始化核心组件...
2025-07-21 00:13:08.372 | INFO     | core.telegram.client_worker:run:65 - Telegram客户端工作线程开始运行
2025-07-21 00:13:08.373 | INFO     | app.services.account_service:__init__:41 - 账户服务初始化
2025-07-21 00:13:08.374 | INFO     | core.proxy.proxy_task_manager:run:284 - 代理任务管理器线程启动
2025-07-21 00:13:08.374 | INFO     | app.controllers.account_controller:__init__:81 - 账户控制器初始化
2025-07-21 00:13:08.374 | INFO     | core.telegram.client_manager:__init__:66 - Telegram客户端管理器初始化
2025-07-21 00:13:08.375 | INFO     | app.services.monitor_service:__init__:39 - 初始化监控任务服务
2025-07-21 00:13:08.375 | INFO     | core.telegram.user_manager:__init__:30 - Telegram用户管理器初始化
2025-07-21 00:13:08.375 | INFO     | core.monitor.filter:__init__:26 - 初始化消息过滤器
2025-07-21 00:13:08.375 | INFO     | core.telegram.group_manager:__init__:36 - Telegram群组管理器初始化
2025-07-21 00:13:08.376 | INFO     | core.monitor.extractor:__init__:24 - 初始化关键词提取器
2025-07-21 00:13:08.376 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-07-21 00:13:08.376 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-07-21 00:13:08.376 | INFO     | core.telegram.monitor_manager:__init__:26 - Telegram监控管理器初始化
2025-07-21 00:13:08.377 | INFO     | core.monitor.notifier:__init__:90 - 初始化通知管理器
2025-07-21 00:13:08.377 | INFO     | core.telegram.telegram_service:__init__:39 - Telegram服务初始化完成
2025-07-21 00:13:08.377 | INFO     | app.controllers.telegram_monitor_controller:__init__:64 - 初始化 Telegram 监控控制器
2025-07-21 00:13:08.378 | DEBUG    | app.controllers.telegram_monitor_controller:__init__:82 - 控制器初始化完成
2025-07-21 00:13:08.576 | INFO     | ui.views.monitor_view:__init__:48 - 初始化监控视图
2025-07-21 00:13:08.577 | DEBUG    | ui.views.monitor_view:_connect_signals:80 - 连接控制器信号
2025-07-21 00:13:08.764 | INFO     | ui.views.proxy_view:__init__:96 - 初始化视图，加载第一页代理数据
2025-07-21 00:13:09.022 | INFO     | __main__:on_auth_completed:95 - 主窗口已启动
2025-07-21 00:13:09.067 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:88 - 尝试连接监听日志信号...
2025-07-21 00:13:09.068 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:90 - 监听日志信号连接成功
2025-07-21 00:13:09.069 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 00:13:09.073 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 00:13:09.077 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:145 - 加载监控任务列表 (搜索关键词: '')
2025-07-21 00:13:09.077 | DEBUG    | ui.views.monitor_view:_on_loading_started:620 - 加载中: 正在加载任务列表...
2025-07-21 00:13:09.078 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 00:13:09.086 | INFO     | app.services.proxy_service:refresh_proxies:712 - 刷新代理列表: offset=0, limit=20
2025-07-21 00:13:09.086 | INFO     | core.proxy.proxy_task_manager:submit_task:172 - 提交耗时任务到子线程: refresh_proxies_0_20
2025-07-21 00:13:09.087 | DEBUG    | app.services.proxy_service:get_proxies:500 - 获取代理列表: offset=0, limit=20
2025-07-21 00:13:09.087 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 00:13:09.087 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 00:13:09.091 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 00:13:09.116 | INFO     | app.controllers.invite_controller:get_invite_tasks:38 - 获取邀请任务列表, status=None
2025-07-21 00:13:09.117 | INFO     | app.services.invite_service:get_invite_tasks:121 - 获取邀请任务列表, status=None
2025-07-21 00:13:09.117 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 00:13:09.120 | INFO     | app.services.invite_service:task_initialize:39 - 初始化邀请服务，重置运行中的任务为暂停状态
2025-07-21 00:13:09.121 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 00:13:09.121 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 00:13:09.123 | INFO     | core.proxy.proxy_task_manager:task_wrapper:199 - 任务 refresh_proxies_0_20 完成
2025-07-21 00:13:09.127 | INFO     | utils.vip_checker:start_checking:37 - 开始VIP状态检查
2025-07-21 00:13:09.127 | DEBUG    | utils.vip_checker:_check_vip_status:61 - 正在检查VIP状态...
2025-07-21 00:13:09.128 | INFO     | core.auth.api_service:verify_vip:425 - 会员验证接口调用
2025-07-21 00:13:09.238 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 00:13:09.240 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 00:13:09.246 | DEBUG    | ui.views.monitor_view:_refresh_task_list:175 - 刷新任务列表，共0个任务
2025-07-21 00:13:09.247 | INFO     | ui.views.monitor_view:_refresh_task_list:189 - 没有找到监控任务
2025-07-21 00:13:09.247 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 00:13:09.249 | INFO     | app.services.invite_service:task_initialize:48 - 成功重置 0 个任务状态
2025-07-21 00:13:09.249 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 00:13:09.251 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 00:13:09.254 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 00:13:09.255 | INFO     | app.services.account_service:get_all_groups:95 - 获取所有账户分组成功, 共 2 个
2025-07-21 00:13:09.256 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 00:13:09.257 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 00:13:09.259 | DEBUG    | ui.views.account_view:_on_groups_loaded:519 - 分组加载完成: 2个分组
2025-07-21 00:13:09.273 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 00:13:09.353 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 00:13:09.353 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 00:13:09.354 | INFO     | app.services.account_service:get_all_accounts:289 - 获取所有账户成功, 共 2 个
2025-07-21 00:13:09.354 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 00:13:09.357 | INFO     | app.controllers.telegram_monitor_controller:_load_total_users_count_internal:191 - 成功加载总体统计数据: 总用户数 23, 今日采集 0, 日均采集 23, 运行天数 1
2025-07-21 00:13:09.357 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:156 - 成功加载 0 个任务
2025-07-21 00:13:09.358 | DEBUG    | ui.views.monitor_view:_on_loading_finished:625 - 加载完成
2025-07-21 00:13:09.359 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-07-21 00:13:09.371 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 00:13:09.372 | DEBUG    | ui.views.account_view:_on_accounts_loaded:652 - 账户加载完成: 2个账户
2025-07-21 00:13:09.394 | DEBUG    | ui.views.account_view:_update_group_counts:188 - 分组计数已更新: {2: 2}
2025-07-21 00:13:09.399 | INFO     | ui.views.account_view:_auto_login_accounts:707 - 开始调用控制器进行 2 个账户的自动登录。
2025-07-21 00:13:09.399 | INFO     | app.services.account_service:batch_auto_login:1164 - 服务层：开始批量自动登录 2 个账户, 最大并发: 5
2025-07-21 00:13:09.399 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 00:13:09.478 | INFO     | app.services.account_service:get_all_accounts:289 - 获取所有账户成功, 共 2 个
2025-07-21 00:13:09.479 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 00:13:09.479 | INFO     | app.services.account_service:batch_auto_login:1203 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-07-21 00:13:09.480 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 00:13:09.482 | DEBUG    | ui.views.account_view:_update_group_counts:188 - 分组计数已更新: {2: 2}
2025-07-21 00:13:09.482 | DEBUG    | ui.views.account_view:_init_cache:155 - 账户缓存初始化完成，共 2 个账户
2025-07-21 00:13:09.487 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 00:13:09.488 | DEBUG    | ui.views.account_view:_on_accounts_loaded:652 - 账户加载完成: 2个账户
2025-07-21 00:13:09.513 | DEBUG    | ui.views.account_view:_update_group_counts:188 - 分组计数已更新: {2: 2}
2025-07-21 00:13:09.526 | INFO     | app.services.account_service:batch_auto_login:1203 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-07-21 00:13:09.527 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 00:13:09.529 | INFO     | app.services.account_service:batch_auto_login:1273 - 服务层：准备调用核心层处理 2 个账户的自动登录。
2025-07-21 00:13:09.529 | INFO     | app.services.account_service:batch_auto_login:1283 - 服务层：设置核心层任务超时为 120 秒。
2025-07-21 00:13:09.529 | INFO     | core.telegram.client_manager:batch_auto_login:1230 - 开始批量自动登录, 共 2 个账户, 最大并发 5
2025-07-21 00:13:09.530 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:271 - 创建客户端: H:\PyProject\TeleTest\APPDATA\sessions\+***********, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-21 00:13:09.531 | DEBUG    | core.telegram.client_manager:get_proxy:96 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-21 00:13:09.531 | DEBUG    | core.telegram.client_manager:get_proxy:143 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-07-21 00:13:09.536 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:271 - 创建客户端: H:\PyProject\TeleTest\APPDATA\sessions\+***********, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-21 00:13:09.537 | DEBUG    | core.telegram.client_manager:get_proxy:96 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-21 00:13:09.537 | DEBUG    | core.telegram.client_manager:get_proxy:143 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-07-21 00:13:09.558 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 00:13:09.629 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 00:13:09.635 | INFO     | core.auth.api_service:verify_vip:438 - 会员验证成功
2025-07-21 00:13:09.636 | INFO     | utils.vip_checker:_check_vip_status:76 - VIP状态有效
2025-07-21 00:13:09.637 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-07-21 00:13:09.639 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-07-21 00:13:09.641 | INFO     | ui.views.proxy_view:_load_initial_data:1044 - 初始数据加载完成，共 1 个代理
2025-07-21 00:13:09.670 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 00:13:14.188 | INFO     | core.telegram.client_manager:_connect_client:219 - 连接成功!
2025-07-21 00:13:14.867 | INFO     | core.telegram.client_manager:_connect_client:219 - 连接成功!
2025-07-21 00:13:15.557 | INFO     | core.telegram.client_manager:_verify_client_authorization:1205 - 账户已授权: +***********
2025-07-21 00:13:15.808 | INFO     | core.telegram.client_manager:_verify_client_authorization:1205 - 账户已授权: +***********
2025-07-21 00:13:17.829 | INFO     | core.telegram.client_manager:batch_auto_login:1379 - 批量登录完成: 总计 2, 成功 2, 失败 0
2025-07-21 00:13:18.556 | INFO     | app.services.account_service:batch_auto_login:1304 - 服务层：核心层登录完成: 成功 2, 失败 0 / 2
2025-07-21 00:13:32.034 | INFO     | app.services.account_service:get_proxy_ips:1043 - 获取有效代理IP列表（包含绑定计数）
2025-07-21 00:13:32.035 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 00:13:32.185 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 00:13:32.186 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 00:13:32.190 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 00:13:32.191 | INFO     | app.services.account_service:get_proxy_ips:1095 - 成功获取 1 个有效代理（已附带绑定计数）
2025-07-21 00:13:32.191 | INFO     | app.controllers.account_controller:get_proxy_ips:566 - 获取到1个有效代理IP
2025-07-21 00:14:08.348 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-21 00:14:20.708 | INFO     | ui.main_window:closeEvent:380 - MainWindow: 接收到关闭事件
2025-07-21 00:14:20.722 | INFO     | ui.main_window:_cleanup_before_quit:266 - MainWindow: 执行清理资源...
2025-07-21 00:14:20.724 | INFO     | core.telegram.client_worker:stop:108 - 正在停止Telegram客户端工作线程
2025-07-21 00:14:20.736 | INFO     | core.telegram.client_manager:cleanup_async:1150 - 开始清理资源并断开所有连接
2025-07-21 00:14:20.736 | INFO     | core.telegram.client_manager:disconnect_all_clients:735 - 正在断开所有客户端连接
2025-07-21 00:14:20.738 | INFO     | core.telegram.client_manager:_safe_disconnect:756 - 正在断开客户端连接: +***********
2025-07-21 00:14:20.738 | INFO     | core.telegram.client_manager:_safe_disconnect:756 - 正在断开客户端连接: +***********
2025-07-21 00:14:20.752 | INFO     | core.telegram.client_manager:_safe_disconnect:760 - 断开客户端连接成功: +***********
2025-07-21 00:14:20.752 | INFO     | core.telegram.client_manager:_safe_disconnect:760 - 断开客户端连接成功: +***********
2025-07-21 00:14:20.753 | INFO     | core.telegram.client_manager:cleanup_async:1169 - 资源清理完成
2025-07-21 00:14:21.245 | INFO     | core.telegram.client_manager:cleanup_async:1150 - 开始清理资源并断开所有连接
2025-07-21 00:14:21.246 | INFO     | core.telegram.client_manager:disconnect_all_clients:735 - 正在断开所有客户端连接
2025-07-21 00:14:21.246 | INFO     | core.telegram.client_manager:cleanup_async:1169 - 资源清理完成
2025-07-21 00:14:21.756 | INFO     | core.telegram.client_worker:run:97 - Telegram客户端工作线程已退出
2025-07-21 00:14:21.757 | INFO     | core.telegram.client_worker:stop:122 - Telegram客户端工作线程已停止
2025-07-21 00:14:21.757 | INFO     | ui.main_window:_cleanup_before_quit:275 - TelegramClientWorker 已停止。
2025-07-21 00:14:21.758 | INFO     | ui.main_window:_cleanup_before_quit:279 - MainWindow 清理完成
2025-07-21 00:14:21.775 | INFO     | __main__:main:109 - 应用程序已正常退出
2025-07-21 00:46:44.633 | INFO     | utils.logger:init_logger:98 - 日志系统初始化完成，默认级别: DEBUG，日志目录: H:\PyProject\TeleTest\APPDATA\logs
2025-07-21 00:46:45.972 | INFO     | data.database:init_database:31 - 初始化数据库连接: H:\PyProject\TeleTest\APPDATA\database\telegram_manager.db
2025-07-21 00:46:45.990 | DEBUG    | data.database:init_database:58 - 创建数据库表
2025-07-21 00:46:46.002 | INFO     | data.database:init_database:61 - 数据库初始化完成
2025-07-21 00:46:46.506 | DEBUG    | app.controllers.auth_controller:initialize_software:43 - 控制器: 初始化软件 app_id=1000, version=1.2.7
2025-07-21 00:46:46.506 | DEBUG    | core.auth.api_service:init_software:55 - 发送初始化请求: https://server.xile188.com/api/user/1000/windowns10/1.2.7/ini, params={}
2025-07-21 00:46:46.881 | DEBUG    | core.auth.api_service:init_software:66 - 初始化数据解密成功: {'new_content': '- 新增功能\n  - 1.新增图片上传功能\n  - 2.新增软件文档\n  - 3.新增软件自动更新，后续更新会自动检测版本更新，无需再下载更新包\n  - 4.新增设置账户信息及发信控制\n- 修复bug\n  - 删除监听任务时，不删除对应任务采集到的数据。\n  - 监听设置群无法发送到目标群\n  - 导出监听数据时应当导出单个任务而非所有数据\n- 优化记录\n  - 优化发信规则，直接调取符合要求的账户发送，无账户时才需要等待延时。\n  - 完善软件设置', 'new_url': None, 'notice': {'id': '2', 'content': '公告内容测试3', 'time': '**********'}}
2025-07-21 00:46:46.888 | INFO     | app.controllers.auth_controller:login:61 - 控制器: 用户登录 account=demo01
2025-07-21 00:46:49.850 | INFO     | core.auth.api_service:login:127 - 用户登录: account=demo01
2025-07-21 00:46:50.062 | INFO     | core.auth.api_service:get_user_info:347 - 获取用户信息
2025-07-21 00:46:50.378 | INFO     | core.auth.api_service:get_user_info:375 - 获取用户信息成功
2025-07-21 00:46:50.386 | INFO     | __main__:on_auth_completed:91 - 用户登录成功，正在启动主窗口...
2025-07-21 00:46:50.410 | INFO     | core.telegram.client_worker:__init__:61 - Telegram客户端工作线程初始化
2025-07-21 00:46:50.410 | INFO     | utils.vip_checker:initialize:25 - VIP检查器已初始化，等待登录后开始检查
2025-07-21 00:46:50.411 | INFO     | ui.main_window:_initialize_core_components:108 - MainWindow: 初始化核心组件...
2025-07-21 00:46:50.411 | INFO     | core.telegram.client_worker:run:65 - Telegram客户端工作线程开始运行
2025-07-21 00:46:50.412 | INFO     | app.services.account_service:__init__:41 - 账户服务初始化
2025-07-21 00:46:50.412 | INFO     | core.proxy.proxy_task_manager:run:284 - 代理任务管理器线程启动
2025-07-21 00:46:50.413 | INFO     | app.controllers.account_controller:__init__:81 - 账户控制器初始化
2025-07-21 00:46:50.413 | INFO     | app.services.monitor_service:__init__:39 - 初始化监控任务服务
2025-07-21 00:46:50.414 | INFO     | core.monitor.filter:__init__:26 - 初始化消息过滤器
2025-07-21 00:46:50.414 | INFO     | core.telegram.client_manager:__init__:66 - Telegram客户端管理器初始化
2025-07-21 00:46:50.414 | INFO     | core.monitor.extractor:__init__:24 - 初始化关键词提取器
2025-07-21 00:46:50.414 | INFO     | core.telegram.user_manager:__init__:30 - Telegram用户管理器初始化
2025-07-21 00:46:50.415 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-07-21 00:46:50.415 | INFO     | core.telegram.group_manager:__init__:36 - Telegram群组管理器初始化
2025-07-21 00:46:50.415 | INFO     | core.monitor.notifier:__init__:90 - 初始化通知管理器
2025-07-21 00:46:50.415 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-07-21 00:46:50.416 | INFO     | app.controllers.telegram_monitor_controller:__init__:64 - 初始化 Telegram 监控控制器
2025-07-21 00:46:50.416 | INFO     | core.telegram.monitor_manager:__init__:26 - Telegram监控管理器初始化
2025-07-21 00:46:50.416 | DEBUG    | app.controllers.telegram_monitor_controller:__init__:82 - 控制器初始化完成
2025-07-21 00:46:50.417 | INFO     | core.telegram.telegram_service:__init__:39 - Telegram服务初始化完成
2025-07-21 00:46:50.633 | INFO     | ui.views.monitor_view:__init__:48 - 初始化监控视图
2025-07-21 00:46:50.633 | DEBUG    | ui.views.monitor_view:_connect_signals:80 - 连接控制器信号
2025-07-21 00:46:50.802 | INFO     | ui.views.proxy_view:__init__:96 - 初始化视图，加载第一页代理数据
2025-07-21 00:46:51.036 | INFO     | __main__:on_auth_completed:95 - 主窗口已启动
2025-07-21 00:46:51.080 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:88 - 尝试连接监听日志信号...
2025-07-21 00:46:51.081 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:90 - 监听日志信号连接成功
2025-07-21 00:46:51.081 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 00:46:51.086 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 00:46:51.091 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:145 - 加载监控任务列表 (搜索关键词: '')
2025-07-21 00:46:51.091 | DEBUG    | ui.views.monitor_view:_on_loading_started:620 - 加载中: 正在加载任务列表...
2025-07-21 00:46:51.091 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 00:46:51.098 | INFO     | app.services.proxy_service:refresh_proxies:712 - 刷新代理列表: offset=0, limit=20
2025-07-21 00:46:51.098 | INFO     | core.proxy.proxy_task_manager:submit_task:172 - 提交耗时任务到子线程: refresh_proxies_0_20
2025-07-21 00:46:51.098 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 00:46:51.098 | DEBUG    | app.services.proxy_service:get_proxies:500 - 获取代理列表: offset=0, limit=20
2025-07-21 00:46:51.100 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 00:46:51.109 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 00:46:51.129 | INFO     | app.controllers.invite_controller:get_invite_tasks:38 - 获取邀请任务列表, status=None
2025-07-21 00:46:51.129 | INFO     | app.services.invite_service:get_invite_tasks:121 - 获取邀请任务列表, status=None
2025-07-21 00:46:51.132 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 00:46:51.133 | INFO     | app.services.invite_service:task_initialize:39 - 初始化邀请服务，重置运行中的任务为暂停状态
2025-07-21 00:46:51.134 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 00:46:51.134 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 00:46:51.136 | INFO     | core.proxy.proxy_task_manager:task_wrapper:199 - 任务 refresh_proxies_0_20 完成
2025-07-21 00:46:51.138 | INFO     | utils.vip_checker:start_checking:37 - 开始VIP状态检查
2025-07-21 00:46:51.138 | DEBUG    | utils.vip_checker:_check_vip_status:61 - 正在检查VIP状态...
2025-07-21 00:46:51.139 | INFO     | core.auth.api_service:verify_vip:425 - 会员验证接口调用
2025-07-21 00:46:51.243 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 00:46:51.246 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 00:46:51.248 | DEBUG    | ui.views.monitor_view:_refresh_task_list:175 - 刷新任务列表，共0个任务
2025-07-21 00:46:51.249 | INFO     | ui.views.monitor_view:_refresh_task_list:189 - 没有找到监控任务
2025-07-21 00:46:51.249 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 00:46:51.252 | INFO     | app.services.invite_service:task_initialize:48 - 成功重置 0 个任务状态
2025-07-21 00:46:51.252 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 00:46:51.254 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 00:46:51.257 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 00:46:51.259 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 00:46:51.261 | INFO     | app.services.account_service:get_all_groups:95 - 获取所有账户分组成功, 共 2 个
2025-07-21 00:46:51.261 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 00:46:51.318 | DEBUG    | ui.views.account_view:_on_groups_loaded:519 - 分组加载完成: 2个分组
2025-07-21 00:46:51.332 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 00:46:51.345 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 00:46:51.346 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 00:46:51.347 | INFO     | app.services.account_service:get_all_accounts:289 - 获取所有账户成功, 共 2 个
2025-07-21 00:46:51.347 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 00:46:51.350 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-07-21 00:46:51.357 | INFO     | app.controllers.telegram_monitor_controller:_load_total_users_count_internal:191 - 成功加载总体统计数据: 总用户数 23, 今日采集 0, 日均采集 23, 运行天数 1
2025-07-21 00:46:51.357 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:156 - 成功加载 0 个任务
2025-07-21 00:46:51.358 | DEBUG    | ui.views.monitor_view:_on_loading_finished:625 - 加载完成
2025-07-21 00:46:51.365 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 00:46:51.367 | DEBUG    | ui.views.account_view:_on_accounts_loaded:652 - 账户加载完成: 2个账户
2025-07-21 00:46:51.395 | DEBUG    | ui.views.account_view:_update_group_counts:188 - 分组计数已更新: {2: 2}
2025-07-21 00:46:51.401 | INFO     | ui.views.account_view:_auto_login_accounts:707 - 开始调用控制器进行 2 个账户的自动登录。
2025-07-21 00:46:51.401 | INFO     | app.services.account_service:batch_auto_login:1164 - 服务层：开始批量自动登录 2 个账户, 最大并发: 5
2025-07-21 00:46:51.401 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 00:46:51.488 | INFO     | app.services.account_service:batch_auto_login:1203 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-07-21 00:46:51.488 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 00:46:51.490 | INFO     | app.services.account_service:get_all_accounts:289 - 获取所有账户成功, 共 2 个
2025-07-21 00:46:51.490 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 00:46:51.494 | INFO     | core.auth.api_service:verify_vip:438 - 会员验证成功
2025-07-21 00:46:51.494 | INFO     | utils.vip_checker:_check_vip_status:76 - VIP状态有效
2025-07-21 00:46:51.496 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 00:46:51.498 | DEBUG    | ui.views.account_view:_update_group_counts:188 - 分组计数已更新: {2: 2}
2025-07-21 00:46:51.498 | DEBUG    | ui.views.account_view:_init_cache:155 - 账户缓存初始化完成，共 2 个账户
2025-07-21 00:46:51.504 | DEBUG    | ui.views.account_view:_on_accounts_loaded:652 - 账户加载完成: 2个账户
2025-07-21 00:46:51.534 | DEBUG    | ui.views.account_view:_update_group_counts:188 - 分组计数已更新: {2: 2}
2025-07-21 00:46:51.545 | INFO     | app.services.account_service:batch_auto_login:1203 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-07-21 00:46:51.546 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 00:46:51.548 | INFO     | app.services.account_service:batch_auto_login:1273 - 服务层：准备调用核心层处理 2 个账户的自动登录。
2025-07-21 00:46:51.549 | INFO     | app.services.account_service:batch_auto_login:1283 - 服务层：设置核心层任务超时为 120 秒。
2025-07-21 00:46:51.549 | INFO     | core.telegram.client_manager:batch_auto_login:1230 - 开始批量自动登录, 共 2 个账户, 最大并发 5
2025-07-21 00:46:51.550 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:271 - 创建客户端: H:\PyProject\TeleTest\APPDATA\sessions\+***********, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-21 00:46:51.550 | DEBUG    | core.telegram.client_manager:get_proxy:96 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-21 00:46:51.552 | DEBUG    | core.telegram.client_manager:get_proxy:143 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-07-21 00:46:51.557 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:271 - 创建客户端: H:\PyProject\TeleTest\APPDATA\sessions\+***********, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-21 00:46:51.559 | DEBUG    | core.telegram.client_manager:get_proxy:96 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-21 00:46:51.560 | DEBUG    | core.telegram.client_manager:get_proxy:143 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-07-21 00:46:51.589 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 00:46:51.671 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 00:46:51.676 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-07-21 00:46:51.679 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-07-21 00:46:51.680 | INFO     | ui.views.proxy_view:_load_initial_data:1044 - 初始数据加载完成，共 1 个代理
2025-07-21 00:46:51.717 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 00:46:55.257 | INFO     | core.telegram.client_manager:_connect_client:219 - 连接成功!
2025-07-21 00:46:56.178 | INFO     | core.telegram.client_manager:_verify_client_authorization:1205 - 账户已授权: +***********
2025-07-21 00:46:56.617 | INFO     | core.telegram.client_manager:_connect_client:219 - 连接成功!
2025-07-21 00:46:57.520 | INFO     | core.telegram.client_manager:_verify_client_authorization:1205 - 账户已授权: +***********
2025-07-21 00:46:59.517 | INFO     | core.telegram.client_manager:batch_auto_login:1379 - 批量登录完成: 总计 2, 成功 2, 失败 0
2025-07-21 00:46:59.568 | INFO     | app.services.account_service:batch_auto_login:1304 - 服务层：核心层登录完成: 成功 2, 失败 0 / 2
2025-07-21 00:47:27.648 | INFO     | ui.views.dashboard_view:_on_start_telegram:29 - 正在启动Telegram...
2025-07-21 00:47:39.264 | INFO     | app.services.account_service:get_proxy_ips:1043 - 获取有效代理IP列表（包含绑定计数）
2025-07-21 00:47:39.264 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 00:47:39.462 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 00:47:39.464 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 00:47:39.470 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 00:47:39.471 | INFO     | app.services.account_service:get_proxy_ips:1095 - 成功获取 1 个有效代理（已附带绑定计数）
2025-07-21 00:47:39.471 | INFO     | app.controllers.account_controller:get_proxy_ips:566 - 获取到1个有效代理IP
2025-07-21 00:47:49.348 | INFO     | app.services.account_service:update_account_profile:490 - 更新账户资料: 2
2025-07-21 00:47:49.349 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 00:47:49.358 | INFO     | core.telegram.user_manager:update_profile:89 - 正在更新用户资料: +***********
2025-07-21 00:47:49.358 | ERROR    | core.telegram.user_manager:update_profile:125 - 更新用户资料错误: +***********, 更新用户资料失败: UpdateProfileRequest.__init__() got an unexpected keyword argument 'username'
2025-07-21 00:47:49.507 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 00:47:50.375 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-21 00:47:51.051 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 00:47:51.113 | INFO     | app.services.account_service:get_all_accounts:289 - 获取所有账户成功, 共 2 个
2025-07-21 00:47:51.115 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 00:47:51.119 | DEBUG    | ui.views.account_view:_on_accounts_loaded:652 - 账户加载完成: 2个账户
2025-07-21 00:47:51.141 | DEBUG    | ui.views.account_view:_update_group_counts:188 - 分组计数已更新: {2: 2}
2025-07-21 00:47:57.677 | INFO     | ui.main_window:closeEvent:380 - MainWindow: 接收到关闭事件
2025-07-21 00:47:57.694 | INFO     | ui.main_window:_cleanup_before_quit:266 - MainWindow: 执行清理资源...
2025-07-21 00:47:57.696 | INFO     | core.telegram.client_worker:stop:108 - 正在停止Telegram客户端工作线程
2025-07-21 00:47:57.700 | INFO     | core.telegram.client_manager:cleanup_async:1150 - 开始清理资源并断开所有连接
2025-07-21 00:47:57.700 | INFO     | core.telegram.client_manager:disconnect_all_clients:735 - 正在断开所有客户端连接
2025-07-21 00:47:57.701 | INFO     | core.telegram.client_manager:_safe_disconnect:756 - 正在断开客户端连接: +***********
2025-07-21 00:47:57.702 | INFO     | core.telegram.client_manager:_safe_disconnect:756 - 正在断开客户端连接: +***********
2025-07-21 00:47:57.713 | INFO     | core.telegram.client_manager:_safe_disconnect:760 - 断开客户端连接成功: +***********
2025-07-21 00:47:57.714 | INFO     | core.telegram.client_manager:_safe_disconnect:760 - 断开客户端连接成功: +***********
2025-07-21 00:47:57.714 | INFO     | core.telegram.client_manager:cleanup_async:1169 - 资源清理完成
2025-07-21 00:47:58.210 | INFO     | core.telegram.client_manager:cleanup_async:1150 - 开始清理资源并断开所有连接
2025-07-21 00:47:58.210 | INFO     | core.telegram.client_manager:disconnect_all_clients:735 - 正在断开所有客户端连接
2025-07-21 00:47:58.211 | INFO     | core.telegram.client_manager:cleanup_async:1169 - 资源清理完成
2025-07-21 00:47:58.724 | INFO     | core.telegram.client_worker:run:97 - Telegram客户端工作线程已退出
2025-07-21 00:47:58.725 | INFO     | core.telegram.client_worker:stop:122 - Telegram客户端工作线程已停止
2025-07-21 00:47:58.725 | INFO     | ui.main_window:_cleanup_before_quit:275 - TelegramClientWorker 已停止。
2025-07-21 00:47:58.725 | INFO     | ui.main_window:_cleanup_before_quit:279 - MainWindow 清理完成
2025-07-21 00:47:58.741 | INFO     | __main__:main:109 - 应用程序已正常退出
2025-07-21 08:53:03.070 | INFO     | utils.logger:init_logger:98 - 日志系统初始化完成，默认级别: DEBUG，日志目录: H:\PyProject\TeleTest\APPDATA\logs
2025-07-21 08:53:05.140 | INFO     | data.database:init_database:31 - 初始化数据库连接: H:\PyProject\TeleTest\APPDATA\database\telegram_manager.db
2025-07-21 08:53:05.169 | DEBUG    | data.database:init_database:58 - 创建数据库表
2025-07-21 08:53:05.180 | INFO     | data.database:init_database:61 - 数据库初始化完成
2025-07-21 08:53:06.617 | DEBUG    | app.controllers.auth_controller:initialize_software:43 - 控制器: 初始化软件 app_id=1000, version=1.2.7
2025-07-21 08:53:06.617 | DEBUG    | core.auth.api_service:init_software:55 - 发送初始化请求: https://server.xile188.com/api/user/1000/windowns10/1.2.7/ini, params={}
2025-07-21 08:53:07.057 | DEBUG    | core.auth.api_service:init_software:66 - 初始化数据解密成功: {'new_content': '- 新增功能\n  - 1.新增图片上传功能\n  - 2.新增软件文档\n  - 3.新增软件自动更新，后续更新会自动检测版本更新，无需再下载更新包\n  - 4.新增设置账户信息及发信控制\n- 修复bug\n  - 删除监听任务时，不删除对应任务采集到的数据。\n  - 监听设置群无法发送到目标群\n  - 导出监听数据时应当导出单个任务而非所有数据\n- 优化记录\n  - 优化发信规则，直接调取符合要求的账户发送，无账户时才需要等待延时。\n  - 完善软件设置', 'new_url': None, 'notice': {'id': '2', 'content': '公告内容测试3', 'time': '**********'}}
2025-07-21 08:53:07.067 | INFO     | app.controllers.auth_controller:login:61 - 控制器: 用户登录 account=demo01
2025-07-21 08:53:10.144 | INFO     | core.auth.api_service:login:127 - 用户登录: account=demo01
2025-07-21 08:53:10.589 | INFO     | core.auth.api_service:get_user_info:347 - 获取用户信息
2025-07-21 08:53:11.197 | INFO     | core.auth.api_service:get_user_info:375 - 获取用户信息成功
2025-07-21 08:53:11.206 | INFO     | __main__:on_auth_completed:91 - 用户登录成功，正在启动主窗口...
2025-07-21 08:53:11.241 | INFO     | core.telegram.client_worker:__init__:61 - Telegram客户端工作线程初始化
2025-07-21 08:53:11.241 | INFO     | utils.vip_checker:initialize:25 - VIP检查器已初始化，等待登录后开始检查
2025-07-21 08:53:11.241 | INFO     | ui.main_window:_initialize_core_components:108 - MainWindow: 初始化核心组件...
2025-07-21 08:53:11.242 | INFO     | core.telegram.client_worker:run:65 - Telegram客户端工作线程开始运行
2025-07-21 08:53:11.243 | INFO     | app.services.account_service:__init__:41 - 账户服务初始化
2025-07-21 08:53:11.243 | INFO     | core.proxy.proxy_task_manager:run:284 - 代理任务管理器线程启动
2025-07-21 08:53:11.244 | INFO     | app.controllers.account_controller:__init__:81 - 账户控制器初始化
2025-07-21 08:53:11.245 | INFO     | app.services.monitor_service:__init__:39 - 初始化监控任务服务
2025-07-21 08:53:11.247 | INFO     | core.monitor.filter:__init__:26 - 初始化消息过滤器
2025-07-21 08:53:11.248 | INFO     | core.monitor.extractor:__init__:24 - 初始化关键词提取器
2025-07-21 08:53:11.248 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-07-21 08:53:11.248 | INFO     | core.telegram.client_manager:__init__:66 - Telegram客户端管理器初始化
2025-07-21 08:53:11.248 | INFO     | core.monitor.notifier:__init__:90 - 初始化通知管理器
2025-07-21 08:53:11.248 | INFO     | core.telegram.user_manager:__init__:30 - Telegram用户管理器初始化
2025-07-21 08:53:11.249 | INFO     | app.controllers.telegram_monitor_controller:__init__:64 - 初始化 Telegram 监控控制器
2025-07-21 08:53:11.249 | INFO     | core.telegram.group_manager:__init__:36 - Telegram群组管理器初始化
2025-07-21 08:53:11.249 | DEBUG    | app.controllers.telegram_monitor_controller:__init__:82 - 控制器初始化完成
2025-07-21 08:53:11.249 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-07-21 08:53:11.250 | INFO     | core.telegram.monitor_manager:__init__:26 - Telegram监控管理器初始化
2025-07-21 08:53:11.251 | INFO     | core.telegram.telegram_service:__init__:39 - Telegram服务初始化完成
2025-07-21 08:53:11.621 | INFO     | ui.views.monitor_view:__init__:48 - 初始化监控视图
2025-07-21 08:53:11.622 | DEBUG    | ui.views.monitor_view:_connect_signals:80 - 连接控制器信号
2025-07-21 08:53:11.826 | INFO     | ui.views.proxy_view:__init__:96 - 初始化视图，加载第一页代理数据
2025-07-21 08:53:12.104 | INFO     | __main__:on_auth_completed:95 - 主窗口已启动
2025-07-21 08:53:12.154 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:88 - 尝试连接监听日志信号...
2025-07-21 08:53:12.154 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:90 - 监听日志信号连接成功
2025-07-21 08:53:12.155 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 08:53:12.159 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 08:53:12.165 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:145 - 加载监控任务列表 (搜索关键词: '')
2025-07-21 08:53:12.166 | DEBUG    | ui.views.monitor_view:_on_loading_started:620 - 加载中: 正在加载任务列表...
2025-07-21 08:53:12.166 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 08:53:12.175 | INFO     | app.services.proxy_service:refresh_proxies:712 - 刷新代理列表: offset=0, limit=20
2025-07-21 08:53:12.175 | INFO     | core.proxy.proxy_task_manager:submit_task:172 - 提交耗时任务到子线程: refresh_proxies_0_20
2025-07-21 08:53:12.176 | DEBUG    | app.services.proxy_service:get_proxies:500 - 获取代理列表: offset=0, limit=20
2025-07-21 08:53:12.176 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 08:53:12.176 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 08:53:12.183 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 08:53:12.211 | INFO     | app.controllers.invite_controller:get_invite_tasks:38 - 获取邀请任务列表, status=None
2025-07-21 08:53:12.212 | INFO     | app.services.invite_service:get_invite_tasks:121 - 获取邀请任务列表, status=None
2025-07-21 08:53:12.212 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 08:53:12.215 | INFO     | app.services.invite_service:task_initialize:39 - 初始化邀请服务，重置运行中的任务为暂停状态
2025-07-21 08:53:12.215 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 08:53:12.217 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 08:53:12.218 | INFO     | core.proxy.proxy_task_manager:task_wrapper:199 - 任务 refresh_proxies_0_20 完成
2025-07-21 08:53:12.220 | INFO     | utils.vip_checker:start_checking:37 - 开始VIP状态检查
2025-07-21 08:53:12.220 | DEBUG    | utils.vip_checker:_check_vip_status:61 - 正在检查VIP状态...
2025-07-21 08:53:12.220 | INFO     | core.auth.api_service:verify_vip:425 - 会员验证接口调用
2025-07-21 08:53:12.327 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 08:53:12.328 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 08:53:12.330 | INFO     | app.services.invite_service:task_initialize:48 - 成功重置 0 个任务状态
2025-07-21 08:53:12.331 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 08:53:12.337 | DEBUG    | ui.views.monitor_view:_refresh_task_list:175 - 刷新任务列表，共0个任务
2025-07-21 08:53:12.337 | INFO     | ui.views.monitor_view:_refresh_task_list:189 - 没有找到监控任务
2025-07-21 08:53:12.338 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 08:53:12.341 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 08:53:12.344 | INFO     | app.services.account_service:get_all_groups:95 - 获取所有账户分组成功, 共 2 个
2025-07-21 08:53:12.345 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 08:53:12.347 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 08:53:12.349 | DEBUG    | ui.views.account_view:_on_groups_loaded:519 - 分组加载完成: 2个分组
2025-07-21 08:53:12.366 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 08:53:12.370 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 08:53:12.449 | INFO     | app.services.account_service:get_all_accounts:289 - 获取所有账户成功, 共 2 个
2025-07-21 08:53:12.450 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 08:53:12.453 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 08:53:12.454 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 08:53:12.455 | DEBUG    | ui.views.account_view:_on_accounts_loaded:652 - 账户加载完成: 2个账户
2025-07-21 08:53:12.563 | DEBUG    | ui.views.account_view:_update_group_counts:188 - 分组计数已更新: {2: 2}
2025-07-21 08:53:12.571 | INFO     | ui.views.account_view:_auto_login_accounts:707 - 开始调用控制器进行 2 个账户的自动登录。
2025-07-21 08:53:12.572 | INFO     | app.services.account_service:batch_auto_login:1164 - 服务层：开始批量自动登录 2 个账户, 最大并发: 5
2025-07-21 08:53:12.572 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 08:53:12.575 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 08:53:12.578 | INFO     | core.auth.api_service:verify_vip:438 - 会员验证成功
2025-07-21 08:53:12.578 | INFO     | utils.vip_checker:_check_vip_status:76 - VIP状态有效
2025-07-21 08:53:12.580 | INFO     | app.controllers.telegram_monitor_controller:_load_total_users_count_internal:191 - 成功加载总体统计数据: 总用户数 23, 今日采集 0, 日均采集 23, 运行天数 1
2025-07-21 08:53:12.580 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:156 - 成功加载 0 个任务
2025-07-21 08:53:12.580 | DEBUG    | ui.views.monitor_view:_on_loading_finished:625 - 加载完成
2025-07-21 08:53:12.585 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-07-21 08:53:12.601 | INFO     | app.services.account_service:get_all_accounts:289 - 获取所有账户成功, 共 2 个
2025-07-21 08:53:12.602 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 08:53:12.602 | INFO     | app.services.account_service:batch_auto_login:1203 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-07-21 08:53:12.603 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 08:53:12.605 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 08:53:12.606 | DEBUG    | ui.views.account_view:_update_group_counts:188 - 分组计数已更新: {2: 2}
2025-07-21 08:53:12.607 | DEBUG    | ui.views.account_view:_init_cache:155 - 账户缓存初始化完成，共 2 个账户
2025-07-21 08:53:12.612 | DEBUG    | ui.views.account_view:_on_accounts_loaded:652 - 账户加载完成: 2个账户
2025-07-21 08:53:12.639 | DEBUG    | ui.views.account_view:_update_group_counts:188 - 分组计数已更新: {2: 2}
2025-07-21 08:53:12.658 | INFO     | app.services.account_service:batch_auto_login:1203 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-07-21 08:53:12.658 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 08:53:12.659 | INFO     | app.services.account_service:batch_auto_login:1273 - 服务层：准备调用核心层处理 2 个账户的自动登录。
2025-07-21 08:53:12.660 | INFO     | app.services.account_service:batch_auto_login:1283 - 服务层：设置核心层任务超时为 120 秒。
2025-07-21 08:53:12.660 | INFO     | core.telegram.client_manager:batch_auto_login:1230 - 开始批量自动登录, 共 2 个账户, 最大并发 5
2025-07-21 08:53:12.661 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:271 - 创建客户端: H:\PyProject\TeleTest\APPDATA\sessions\+***********, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-21 08:53:12.661 | DEBUG    | core.telegram.client_manager:get_proxy:96 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-21 08:53:12.661 | DEBUG    | core.telegram.client_manager:get_proxy:143 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-07-21 08:53:12.667 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:271 - 创建客户端: H:\PyProject\TeleTest\APPDATA\sessions\+***********, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-21 08:53:12.667 | DEBUG    | core.telegram.client_manager:get_proxy:96 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-21 08:53:12.667 | DEBUG    | core.telegram.client_manager:get_proxy:143 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-07-21 08:53:12.689 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 08:53:12.783 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 08:53:12.835 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 08:53:12.849 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-07-21 08:53:12.852 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-07-21 08:53:12.855 | INFO     | ui.views.proxy_view:_load_initial_data:1044 - 初始数据加载完成，共 1 个代理
2025-07-21 08:53:16.499 | INFO     | core.telegram.client_manager:_connect_client:219 - 连接成功!
2025-07-21 08:53:17.328 | INFO     | core.telegram.client_manager:_connect_client:219 - 连接成功!
2025-07-21 08:53:17.350 | INFO     | core.telegram.client_manager:_verify_client_authorization:1205 - 账户已授权: +***********
2025-07-21 08:53:18.448 | INFO     | core.telegram.client_manager:_verify_client_authorization:1205 - 账户已授权: +***********
2025-07-21 08:53:20.462 | INFO     | core.telegram.client_manager:batch_auto_login:1379 - 批量登录完成: 总计 2, 成功 2, 失败 0
2025-07-21 08:53:20.693 | INFO     | app.services.account_service:batch_auto_login:1304 - 服务层：核心层登录完成: 成功 2, 失败 0 / 2
2025-07-21 08:53:24.213 | INFO     | app.services.account_service:refresh_account_info:606 - 刷新账户信息: 2
2025-07-21 08:53:24.213 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 08:53:24.222 | INFO     | core.telegram.user_manager:get_user_info:41 - 正在获取用户信息: +***********
2025-07-21 08:53:25.480 | INFO     | core.telegram.user_manager:get_user_info:66 - 获取用户信息成功: +***********
2025-07-21 08:53:26.229 | INFO     | data.repositories.account_repo:update_account:378 - 更新账户成功: 2
2025-07-21 08:53:26.239 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 08:53:26.244 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 08:53:26.254 | INFO     | ui.views.account_view:_update_single_account_display:337 - 已实时更新账户显示: +***********
2025-07-21 08:53:26.254 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 08:53:30.154 | INFO     | app.services.account_service:get_proxy_ips:1043 - 获取有效代理IP列表（包含绑定计数）
2025-07-21 08:53:30.155 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 08:53:30.196 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 08:53:30.197 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 08:53:30.200 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 08:53:30.201 | INFO     | app.services.account_service:get_proxy_ips:1095 - 成功获取 1 个有效代理（已附带绑定计数）
2025-07-21 08:53:30.201 | INFO     | app.controllers.account_controller:get_proxy_ips:566 - 获取到1个有效代理IP
2025-07-21 08:54:11.195 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-21 08:54:12.081 | INFO     | app.services.account_service:update_account_profile:490 - 更新账户资料: 2
2025-07-21 08:54:12.081 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 08:54:12.090 | INFO     | core.telegram.user_manager:update_profile:89 - 正在更新用户资料: +***********
2025-07-21 08:54:12.090 | ERROR    | core.telegram.user_manager:update_profile:125 - 更新用户资料错误: +***********, 更新用户资料失败: UpdateProfileRequest.__init__() got an unexpected keyword argument 'bio'
2025-07-21 08:54:12.245 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 08:54:13.777 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 08:54:13.977 | INFO     | app.services.account_service:get_all_accounts:289 - 获取所有账户成功, 共 2 个
2025-07-21 08:54:13.977 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 08:54:13.979 | DEBUG    | ui.views.account_view:_on_accounts_loaded:652 - 账户加载完成: 2个账户
2025-07-21 08:54:14.007 | DEBUG    | ui.views.account_view:_update_group_counts:188 - 分组计数已更新: {2: 2}
2025-07-21 08:55:11.196 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-21 08:56:11.194 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-21 08:57:11.193 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-21 08:58:11.201 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-21 08:59:11.194 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-21 09:00:11.195 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-21 09:01:11.195 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-21 09:02:11.193 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-21 09:03:11.199 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-21 09:04:11.199 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-21 09:05:11.196 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-21 09:06:11.193 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-21 09:07:11.199 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-21 09:08:11.199 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-21 09:09:11.194 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-21 09:10:11.195 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-21 09:11:11.211 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-21 09:12:11.209 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-21 09:13:11.210 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-21 09:14:11.231 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-21 09:15:11.226 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-21 09:16:11.231 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-21 09:17:11.226 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-21 09:18:11.225 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-21 09:19:11.225 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-21 09:20:11.227 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-21 09:21:11.227 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-21 09:22:11.228 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-21 09:23:11.229 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-21 09:23:12.211 | DEBUG    | utils.vip_checker:_check_vip_status:61 - 正在检查VIP状态...
2025-07-21 09:23:12.211 | INFO     | core.auth.api_service:verify_vip:425 - 会员验证接口调用
2025-07-21 09:23:12.430 | INFO     | core.auth.api_service:verify_vip:438 - 会员验证成功
2025-07-21 09:23:12.430 | INFO     | utils.vip_checker:_check_vip_status:76 - VIP状态有效
2025-07-21 09:24:11.229 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-21 09:25:11.228 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-21 09:25:16.246 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 09:25:16.264 | INFO     | app.services.account_service:get_accounts_by_group:311 - 获取分组 2 的账户成功, 共 2 个
2025-07-21 09:25:16.264 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 09:25:16.265 | DEBUG    | ui.views.account_view:_on_accounts_loaded:652 - 账户加载完成: 2个账户
2025-07-21 09:25:16.940 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 09:25:16.950 | INFO     | app.services.account_service:get_all_accounts:289 - 获取所有账户成功, 共 2 个
2025-07-21 09:25:16.951 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 09:25:16.951 | DEBUG    | ui.views.account_view:_on_accounts_loaded:652 - 账户加载完成: 2个账户
2025-07-21 09:25:16.971 | DEBUG    | ui.views.account_view:_update_group_counts:188 - 分组计数已更新: {2: 2}
2025-07-21 09:26:11.227 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-21 09:27:11.231 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-21 09:28:11.227 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-21 09:29:11.225 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-21 09:30:11.225 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-21 09:31:11.231 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-21 09:32:11.226 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-21 09:33:11.225 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-21 09:34:11.231 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-21 09:35:11.231 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-21 09:36:11.227 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-21 09:37:11.226 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-21 09:38:11.228 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-21 09:39:11.229 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-21 09:40:11.230 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-21 09:41:11.233 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-21 09:42:11.232 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-21 09:43:11.225 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-21 09:44:11.226 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-21 09:45:11.225 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-21 09:45:40.734 | INFO     | ui.main_window:closeEvent:380 - MainWindow: 接收到关闭事件
2025-07-21 09:45:40.746 | INFO     | ui.main_window:_cleanup_before_quit:266 - MainWindow: 执行清理资源...
2025-07-21 09:45:40.750 | INFO     | core.telegram.client_worker:stop:108 - 正在停止Telegram客户端工作线程
2025-07-21 09:45:40.755 | INFO     | core.telegram.client_manager:cleanup_async:1150 - 开始清理资源并断开所有连接
2025-07-21 09:45:40.755 | INFO     | core.telegram.client_manager:disconnect_all_clients:735 - 正在断开所有客户端连接
2025-07-21 09:45:40.755 | INFO     | core.telegram.client_manager:_safe_disconnect:756 - 正在断开客户端连接: +***********
2025-07-21 09:45:40.755 | INFO     | core.telegram.client_manager:_safe_disconnect:756 - 正在断开客户端连接: +***********
2025-07-21 09:45:40.769 | INFO     | core.telegram.client_manager:_safe_disconnect:760 - 断开客户端连接成功: +***********
2025-07-21 09:45:40.769 | INFO     | core.telegram.client_manager:_safe_disconnect:760 - 断开客户端连接成功: +***********
2025-07-21 09:45:40.769 | INFO     | core.telegram.client_manager:cleanup_async:1169 - 资源清理完成
2025-07-21 09:45:41.259 | INFO     | core.telegram.client_manager:cleanup_async:1150 - 开始清理资源并断开所有连接
2025-07-21 09:45:41.259 | INFO     | core.telegram.client_manager:disconnect_all_clients:735 - 正在断开所有客户端连接
2025-07-21 09:45:41.259 | INFO     | core.telegram.client_manager:cleanup_async:1169 - 资源清理完成
2025-07-21 09:45:41.762 | INFO     | core.telegram.client_worker:run:97 - Telegram客户端工作线程已退出
2025-07-21 09:45:41.764 | INFO     | core.telegram.client_worker:stop:122 - Telegram客户端工作线程已停止
2025-07-21 09:45:41.764 | INFO     | ui.main_window:_cleanup_before_quit:275 - TelegramClientWorker 已停止。
2025-07-21 09:45:41.764 | INFO     | ui.main_window:_cleanup_before_quit:279 - MainWindow 清理完成
2025-07-21 09:45:41.780 | INFO     | __main__:main:109 - 应用程序已正常退出
2025-07-21 09:45:53.042 | INFO     | utils.logger:init_logger:98 - 日志系统初始化完成，默认级别: DEBUG，日志目录: H:\PyProject\TeleTest\APPDATA\logs
2025-07-21 09:45:54.699 | INFO     | data.database:init_database:31 - 初始化数据库连接: H:\PyProject\TeleTest\APPDATA\database\telegram_manager.db
2025-07-21 09:45:54.721 | DEBUG    | data.database:init_database:58 - 创建数据库表
2025-07-21 09:45:54.735 | INFO     | data.database:init_database:61 - 数据库初始化完成
2025-07-21 09:45:56.877 | DEBUG    | app.controllers.auth_controller:initialize_software:43 - 控制器: 初始化软件 app_id=1000, version=1.2.7
2025-07-21 09:45:56.877 | DEBUG    | core.auth.api_service:init_software:55 - 发送初始化请求: https://server.xile188.com/api/user/1000/windowns10/1.2.7/ini, params={}
2025-07-21 09:45:57.168 | DEBUG    | core.auth.api_service:init_software:66 - 初始化数据解密成功: {'new_content': '- 新增功能\n  - 1.新增图片上传功能\n  - 2.新增软件文档\n  - 3.新增软件自动更新，后续更新会自动检测版本更新，无需再下载更新包\n  - 4.新增设置账户信息及发信控制\n- 修复bug\n  - 删除监听任务时，不删除对应任务采集到的数据。\n  - 监听设置群无法发送到目标群\n  - 导出监听数据时应当导出单个任务而非所有数据\n- 优化记录\n  - 优化发信规则，直接调取符合要求的账户发送，无账户时才需要等待延时。\n  - 完善软件设置', 'new_url': None, 'notice': {'id': '2', 'content': '公告内容测试3', 'time': '**********'}}
2025-07-21 09:45:57.178 | INFO     | app.controllers.auth_controller:login:61 - 控制器: 用户登录 account=demo01
2025-07-21 09:46:00.432 | INFO     | core.auth.api_service:login:127 - 用户登录: account=demo01
2025-07-21 09:46:00.693 | INFO     | core.auth.api_service:get_user_info:347 - 获取用户信息
2025-07-21 09:46:00.937 | INFO     | core.auth.api_service:get_user_info:375 - 获取用户信息成功
2025-07-21 09:46:00.943 | INFO     | __main__:on_auth_completed:91 - 用户登录成功，正在启动主窗口...
2025-07-21 09:46:00.965 | INFO     | core.telegram.client_worker:__init__:61 - Telegram客户端工作线程初始化
2025-07-21 09:46:00.966 | INFO     | utils.vip_checker:initialize:25 - VIP检查器已初始化，等待登录后开始检查
2025-07-21 09:46:00.966 | INFO     | ui.main_window:_initialize_core_components:108 - MainWindow: 初始化核心组件...
2025-07-21 09:46:00.968 | INFO     | core.telegram.client_worker:run:65 - Telegram客户端工作线程开始运行
2025-07-21 09:46:00.968 | INFO     | core.proxy.proxy_task_manager:run:284 - 代理任务管理器线程启动
2025-07-21 09:46:00.969 | INFO     | app.services.account_service:__init__:41 - 账户服务初始化
2025-07-21 09:46:00.970 | INFO     | app.controllers.account_controller:__init__:81 - 账户控制器初始化
2025-07-21 09:46:00.970 | INFO     | app.services.monitor_service:__init__:39 - 初始化监控任务服务
2025-07-21 09:46:00.970 | INFO     | core.monitor.filter:__init__:26 - 初始化消息过滤器
2025-07-21 09:46:00.970 | INFO     | core.monitor.extractor:__init__:24 - 初始化关键词提取器
2025-07-21 09:46:00.970 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-07-21 09:46:00.971 | INFO     | core.monitor.notifier:__init__:90 - 初始化通知管理器
2025-07-21 09:46:00.971 | INFO     | app.controllers.telegram_monitor_controller:__init__:64 - 初始化 Telegram 监控控制器
2025-07-21 09:46:00.971 | DEBUG    | app.controllers.telegram_monitor_controller:__init__:82 - 控制器初始化完成
2025-07-21 09:46:00.971 | INFO     | core.telegram.client_manager:__init__:66 - Telegram客户端管理器初始化
2025-07-21 09:46:00.972 | INFO     | core.telegram.user_manager:__init__:30 - Telegram用户管理器初始化
2025-07-21 09:46:00.973 | INFO     | core.telegram.group_manager:__init__:36 - Telegram群组管理器初始化
2025-07-21 09:46:00.973 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-07-21 09:46:00.974 | INFO     | core.telegram.monitor_manager:__init__:26 - Telegram监控管理器初始化
2025-07-21 09:46:00.974 | INFO     | core.telegram.telegram_service:__init__:39 - Telegram服务初始化完成
2025-07-21 09:46:01.190 | INFO     | ui.views.monitor_view:__init__:48 - 初始化监控视图
2025-07-21 09:46:01.190 | DEBUG    | ui.views.monitor_view:_connect_signals:80 - 连接控制器信号
2025-07-21 09:46:01.390 | INFO     | ui.views.proxy_view:__init__:96 - 初始化视图，加载第一页代理数据
2025-07-21 09:46:01.658 | INFO     | __main__:on_auth_completed:95 - 主窗口已启动
2025-07-21 09:46:01.720 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:88 - 尝试连接监听日志信号...
2025-07-21 09:46:01.720 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:90 - 监听日志信号连接成功
2025-07-21 09:46:01.721 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 09:46:01.727 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 09:46:01.732 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:145 - 加载监控任务列表 (搜索关键词: '')
2025-07-21 09:46:01.732 | DEBUG    | ui.views.monitor_view:_on_loading_started:620 - 加载中: 正在加载任务列表...
2025-07-21 09:46:01.732 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 09:46:01.741 | INFO     | app.services.proxy_service:refresh_proxies:712 - 刷新代理列表: offset=0, limit=20
2025-07-21 09:46:01.741 | INFO     | core.proxy.proxy_task_manager:submit_task:172 - 提交耗时任务到子线程: refresh_proxies_0_20
2025-07-21 09:46:01.741 | DEBUG    | app.services.proxy_service:get_proxies:500 - 获取代理列表: offset=0, limit=20
2025-07-21 09:46:01.741 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 09:46:01.741 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 09:46:01.746 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 09:46:01.786 | INFO     | app.controllers.invite_controller:get_invite_tasks:38 - 获取邀请任务列表, status=None
2025-07-21 09:46:01.787 | INFO     | app.services.invite_service:get_invite_tasks:121 - 获取邀请任务列表, status=None
2025-07-21 09:46:01.787 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 09:46:01.799 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 09:46:01.800 | INFO     | core.proxy.proxy_task_manager:task_wrapper:199 - 任务 refresh_proxies_0_20 完成
2025-07-21 09:46:01.803 | INFO     | app.services.invite_service:task_initialize:39 - 初始化邀请服务，重置运行中的任务为暂停状态
2025-07-21 09:46:01.803 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 09:46:01.810 | INFO     | utils.vip_checker:start_checking:37 - 开始VIP状态检查
2025-07-21 09:46:01.810 | DEBUG    | utils.vip_checker:_check_vip_status:61 - 正在检查VIP状态...
2025-07-21 09:46:01.811 | INFO     | core.auth.api_service:verify_vip:425 - 会员验证接口调用
2025-07-21 09:46:01.894 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 09:46:01.898 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 09:46:01.906 | DEBUG    | ui.views.monitor_view:_refresh_task_list:175 - 刷新任务列表，共0个任务
2025-07-21 09:46:01.907 | INFO     | ui.views.monitor_view:_refresh_task_list:189 - 没有找到监控任务
2025-07-21 09:46:01.907 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 09:46:01.908 | INFO     | app.services.invite_service:task_initialize:48 - 成功重置 0 个任务状态
2025-07-21 09:46:01.908 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 09:46:01.910 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 09:46:01.913 | INFO     | app.services.account_service:get_all_groups:95 - 获取所有账户分组成功, 共 2 个
2025-07-21 09:46:01.913 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 09:46:01.916 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 09:46:01.917 | DEBUG    | ui.views.account_view:_on_groups_loaded:519 - 分组加载完成: 2个分组
2025-07-21 09:46:01.932 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 09:46:01.934 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 09:46:02.075 | INFO     | app.services.account_service:get_all_accounts:289 - 获取所有账户成功, 共 2 个
2025-07-21 09:46:02.076 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 09:46:02.090 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 09:46:02.091 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 09:46:02.093 | DEBUG    | ui.views.account_view:_on_accounts_loaded:652 - 账户加载完成: 2个账户
2025-07-21 09:46:02.121 | DEBUG    | ui.views.account_view:_update_group_counts:188 - 分组计数已更新: {2: 2}
2025-07-21 09:46:02.125 | INFO     | ui.views.account_view:_auto_login_accounts:707 - 开始调用控制器进行 2 个账户的自动登录。
2025-07-21 09:46:02.126 | INFO     | app.services.account_service:batch_auto_login:1164 - 服务层：开始批量自动登录 2 个账户, 最大并发: 5
2025-07-21 09:46:02.126 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 09:46:02.130 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 09:46:02.133 | INFO     | app.controllers.telegram_monitor_controller:_load_total_users_count_internal:191 - 成功加载总体统计数据: 总用户数 23, 今日采集 0, 日均采集 23, 运行天数 1
2025-07-21 09:46:02.133 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:156 - 成功加载 0 个任务
2025-07-21 09:46:02.133 | DEBUG    | ui.views.monitor_view:_on_loading_finished:625 - 加载完成
2025-07-21 09:46:02.137 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-07-21 09:46:02.152 | INFO     | core.auth.api_service:verify_vip:438 - 会员验证成功
2025-07-21 09:46:02.152 | INFO     | utils.vip_checker:_check_vip_status:76 - VIP状态有效
2025-07-21 09:46:02.154 | INFO     | app.services.account_service:batch_auto_login:1203 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-07-21 09:46:02.155 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 09:46:02.155 | INFO     | app.services.account_service:get_all_accounts:289 - 获取所有账户成功, 共 2 个
2025-07-21 09:46:02.155 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 09:46:02.157 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 09:46:02.159 | DEBUG    | ui.views.account_view:_update_group_counts:188 - 分组计数已更新: {2: 2}
2025-07-21 09:46:02.159 | DEBUG    | ui.views.account_view:_init_cache:155 - 账户缓存初始化完成，共 2 个账户
2025-07-21 09:46:02.166 | DEBUG    | ui.views.account_view:_on_accounts_loaded:652 - 账户加载完成: 2个账户
2025-07-21 09:46:02.193 | DEBUG    | ui.views.account_view:_update_group_counts:188 - 分组计数已更新: {2: 2}
2025-07-21 09:46:02.213 | INFO     | app.services.account_service:batch_auto_login:1203 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-07-21 09:46:02.213 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 09:46:02.215 | INFO     | app.services.account_service:batch_auto_login:1273 - 服务层：准备调用核心层处理 2 个账户的自动登录。
2025-07-21 09:46:02.215 | INFO     | app.services.account_service:batch_auto_login:1283 - 服务层：设置核心层任务超时为 120 秒。
2025-07-21 09:46:02.215 | INFO     | core.telegram.client_manager:batch_auto_login:1230 - 开始批量自动登录, 共 2 个账户, 最大并发 5
2025-07-21 09:46:02.216 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:271 - 创建客户端: H:\PyProject\TeleTest\APPDATA\sessions\+***********, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-21 09:46:02.216 | DEBUG    | core.telegram.client_manager:get_proxy:96 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-21 09:46:02.216 | DEBUG    | core.telegram.client_manager:get_proxy:143 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-07-21 09:46:02.221 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:271 - 创建客户端: H:\PyProject\TeleTest\APPDATA\sessions\+***********, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-21 09:46:02.221 | DEBUG    | core.telegram.client_manager:get_proxy:96 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-21 09:46:02.221 | DEBUG    | core.telegram.client_manager:get_proxy:143 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-07-21 09:46:02.260 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 09:46:02.406 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 09:46:02.477 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-07-21 09:46:02.479 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-07-21 09:46:02.480 | INFO     | ui.views.proxy_view:_load_initial_data:1044 - 初始数据加载完成，共 1 个代理
2025-07-21 09:46:02.483 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 09:46:05.209 | INFO     | core.telegram.client_manager:_connect_client:219 - 连接成功!
2025-07-21 09:46:05.622 | INFO     | core.telegram.client_manager:_connect_client:219 - 连接成功!
2025-07-21 09:46:06.395 | INFO     | core.telegram.client_manager:_verify_client_authorization:1205 - 账户已授权: +***********
2025-07-21 09:46:06.695 | INFO     | core.telegram.client_manager:_verify_client_authorization:1205 - 账户已授权: +***********
2025-07-21 09:46:08.701 | INFO     | core.telegram.client_manager:batch_auto_login:1379 - 批量登录完成: 总计 2, 成功 2, 失败 0
2025-07-21 09:46:09.287 | INFO     | app.services.account_service:batch_auto_login:1304 - 服务层：核心层登录完成: 成功 2, 失败 0 / 2
2025-07-21 09:46:13.973 | INFO     | app.services.account_service:get_proxy_ips:1043 - 获取有效代理IP列表（包含绑定计数）
2025-07-21 09:46:13.974 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 09:46:14.017 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 09:46:14.018 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 09:46:14.021 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 09:46:14.022 | INFO     | app.services.account_service:get_proxy_ips:1095 - 成功获取 1 个有效代理（已附带绑定计数）
2025-07-21 09:46:14.022 | INFO     | app.controllers.account_controller:get_proxy_ips:566 - 获取到1个有效代理IP
2025-07-21 09:46:41.019 | INFO     | app.services.account_service:update_account_profile:490 - 更新账户资料: 1
2025-07-21 09:46:41.020 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 09:46:41.028 | INFO     | core.telegram.user_manager:update_profile:89 - 正在更新用户资料: +***********
2025-07-21 09:46:41.028 | ERROR    | core.telegram.user_manager:update_profile:129 - 更新用户资料错误: +***********, 更新用户资料失败: UpdateProfileRequest.__init__() got an unexpected keyword argument 'username'
2025-07-21 09:46:41.180 | INFO     | data.repositories.account_repo:update_account:378 - 更新账户成功: 1
2025-07-21 09:46:41.180 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 09:46:42.704 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 09:46:42.757 | INFO     | app.services.account_service:get_all_accounts:289 - 获取所有账户成功, 共 2 个
2025-07-21 09:46:42.757 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 09:46:42.762 | DEBUG    | ui.views.account_view:_on_accounts_loaded:652 - 账户加载完成: 2个账户
2025-07-21 09:46:42.784 | DEBUG    | ui.views.account_view:_update_group_counts:188 - 分组计数已更新: {2: 2}
2025-07-21 09:47:00.934 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-21 10:16:59.301 | INFO     | utils.logger:init_logger:98 - 日志系统初始化完成，默认级别: DEBUG，日志目录: H:\PyProject\TeleTest\APPDATA\logs
2025-07-21 10:17:00.577 | INFO     | data.database:init_database:31 - 初始化数据库连接: H:\PyProject\TeleTest\APPDATA\database\telegram_manager.db
2025-07-21 10:17:00.597 | DEBUG    | data.database:init_database:58 - 创建数据库表
2025-07-21 10:17:00.608 | INFO     | data.database:init_database:61 - 数据库初始化完成
2025-07-21 10:17:02.023 | DEBUG    | app.controllers.auth_controller:initialize_software:43 - 控制器: 初始化软件 app_id=1000, version=1.2.7
2025-07-21 10:17:02.023 | DEBUG    | core.auth.api_service:init_software:55 - 发送初始化请求: https://server.xile188.com/api/user/1000/windowns10/1.2.7/ini, params={}
2025-07-21 10:17:03.331 | DEBUG    | core.auth.api_service:init_software:66 - 初始化数据解密成功: {'new_content': '- 新增功能\n  - 1.新增图片上传功能\n  - 2.新增软件文档\n  - 3.新增软件自动更新，后续更新会自动检测版本更新，无需再下载更新包\n  - 4.新增设置账户信息及发信控制\n- 修复bug\n  - 删除监听任务时，不删除对应任务采集到的数据。\n  - 监听设置群无法发送到目标群\n  - 导出监听数据时应当导出单个任务而非所有数据\n- 优化记录\n  - 优化发信规则，直接调取符合要求的账户发送，无账户时才需要等待延时。\n  - 完善软件设置', 'new_url': None, 'notice': {'id': '2', 'content': '公告内容测试3', 'time': '**********'}}
2025-07-21 10:17:03.338 | INFO     | app.controllers.auth_controller:login:61 - 控制器: 用户登录 account=demo01
2025-07-21 10:17:06.376 | INFO     | core.auth.api_service:login:127 - 用户登录: account=demo01
2025-07-21 10:17:06.584 | INFO     | core.auth.api_service:get_user_info:347 - 获取用户信息
2025-07-21 10:17:06.816 | INFO     | core.auth.api_service:get_user_info:375 - 获取用户信息成功
2025-07-21 10:17:06.822 | INFO     | __main__:on_auth_completed:91 - 用户登录成功，正在启动主窗口...
2025-07-21 10:17:06.847 | INFO     | core.telegram.client_worker:__init__:61 - Telegram客户端工作线程初始化
2025-07-21 10:17:06.847 | INFO     | utils.vip_checker:initialize:25 - VIP检查器已初始化，等待登录后开始检查
2025-07-21 10:17:06.847 | INFO     | ui.main_window:_initialize_core_components:108 - MainWindow: 初始化核心组件...
2025-07-21 10:17:06.848 | INFO     | core.telegram.client_worker:run:65 - Telegram客户端工作线程开始运行
2025-07-21 10:17:06.848 | INFO     | app.services.account_service:__init__:41 - 账户服务初始化
2025-07-21 10:17:06.848 | INFO     | core.proxy.proxy_task_manager:run:284 - 代理任务管理器线程启动
2025-07-21 10:17:06.849 | INFO     | app.controllers.account_controller:__init__:81 - 账户控制器初始化
2025-07-21 10:17:06.849 | INFO     | app.services.monitor_service:__init__:39 - 初始化监控任务服务
2025-07-21 10:17:06.849 | INFO     | core.monitor.filter:__init__:26 - 初始化消息过滤器
2025-07-21 10:17:06.849 | INFO     | core.monitor.extractor:__init__:24 - 初始化关键词提取器
2025-07-21 10:17:06.851 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-07-21 10:17:06.851 | INFO     | core.monitor.notifier:__init__:90 - 初始化通知管理器
2025-07-21 10:17:06.852 | INFO     | app.controllers.telegram_monitor_controller:__init__:64 - 初始化 Telegram 监控控制器
2025-07-21 10:17:06.852 | INFO     | core.telegram.client_manager:__init__:66 - Telegram客户端管理器初始化
2025-07-21 10:17:06.852 | DEBUG    | app.controllers.telegram_monitor_controller:__init__:82 - 控制器初始化完成
2025-07-21 10:17:06.852 | INFO     | core.telegram.user_manager:__init__:30 - Telegram用户管理器初始化
2025-07-21 10:17:06.853 | INFO     | core.telegram.group_manager:__init__:36 - Telegram群组管理器初始化
2025-07-21 10:17:06.854 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-07-21 10:17:06.854 | INFO     | core.telegram.monitor_manager:__init__:26 - Telegram监控管理器初始化
2025-07-21 10:17:06.855 | INFO     | core.telegram.telegram_service:__init__:39 - Telegram服务初始化完成
2025-07-21 10:17:07.268 | INFO     | ui.views.monitor_view:__init__:48 - 初始化监控视图
2025-07-21 10:17:07.268 | DEBUG    | ui.views.monitor_view:_connect_signals:80 - 连接控制器信号
2025-07-21 10:17:07.454 | INFO     | ui.views.proxy_view:__init__:96 - 初始化视图，加载第一页代理数据
2025-07-21 10:17:07.696 | INFO     | __main__:on_auth_completed:95 - 主窗口已启动
2025-07-21 10:17:07.753 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:88 - 尝试连接监听日志信号...
2025-07-21 10:17:07.753 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:90 - 监听日志信号连接成功
2025-07-21 10:17:07.754 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 10:17:07.757 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 10:17:07.761 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:145 - 加载监控任务列表 (搜索关键词: '')
2025-07-21 10:17:07.761 | DEBUG    | ui.views.monitor_view:_on_loading_started:620 - 加载中: 正在加载任务列表...
2025-07-21 10:17:07.761 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 10:17:07.767 | INFO     | app.services.proxy_service:refresh_proxies:712 - 刷新代理列表: offset=0, limit=20
2025-07-21 10:17:07.768 | INFO     | core.proxy.proxy_task_manager:submit_task:172 - 提交耗时任务到子线程: refresh_proxies_0_20
2025-07-21 10:17:07.768 | DEBUG    | app.services.proxy_service:get_proxies:500 - 获取代理列表: offset=0, limit=20
2025-07-21 10:17:07.768 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 10:17:07.768 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 10:17:07.772 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 10:17:07.796 | INFO     | app.controllers.invite_controller:get_invite_tasks:38 - 获取邀请任务列表, status=None
2025-07-21 10:17:07.796 | INFO     | app.services.invite_service:get_invite_tasks:121 - 获取邀请任务列表, status=None
2025-07-21 10:17:07.796 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 10:17:07.797 | INFO     | app.services.invite_service:task_initialize:39 - 初始化邀请服务，重置运行中的任务为暂停状态
2025-07-21 10:17:07.797 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 10:17:07.802 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 10:17:07.803 | INFO     | utils.vip_checker:start_checking:37 - 开始VIP状态检查
2025-07-21 10:17:07.803 | DEBUG    | utils.vip_checker:_check_vip_status:61 - 正在检查VIP状态...
2025-07-21 10:17:07.803 | INFO     | core.auth.api_service:verify_vip:425 - 会员验证接口调用
2025-07-21 10:17:07.804 | INFO     | core.proxy.proxy_task_manager:task_wrapper:199 - 任务 refresh_proxies_0_20 完成
2025-07-21 10:17:07.927 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 10:17:07.932 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 10:17:07.936 | INFO     | app.services.invite_service:task_initialize:48 - 成功重置 0 个任务状态
2025-07-21 10:17:07.936 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 10:17:07.937 | DEBUG    | ui.views.monitor_view:_refresh_task_list:175 - 刷新任务列表，共0个任务
2025-07-21 10:17:07.938 | INFO     | ui.views.monitor_view:_refresh_task_list:189 - 没有找到监控任务
2025-07-21 10:17:07.938 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 10:17:07.941 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 10:17:07.945 | INFO     | app.services.account_service:get_all_groups:95 - 获取所有账户分组成功, 共 2 个
2025-07-21 10:17:07.945 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 10:17:07.946 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 10:17:07.947 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 10:17:07.951 | DEBUG    | ui.views.account_view:_on_groups_loaded:519 - 分组加载完成: 2个分组
2025-07-21 10:17:07.967 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 10:17:08.086 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 10:17:08.088 | INFO     | app.services.account_service:get_all_accounts:289 - 获取所有账户成功, 共 2 个
2025-07-21 10:17:08.088 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 10:17:08.089 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 10:17:08.093 | INFO     | app.controllers.telegram_monitor_controller:_load_total_users_count_internal:191 - 成功加载总体统计数据: 总用户数 23, 今日采集 0, 日均采集 23, 运行天数 1
2025-07-21 10:17:08.093 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:156 - 成功加载 0 个任务
2025-07-21 10:17:08.093 | DEBUG    | ui.views.monitor_view:_on_loading_finished:625 - 加载完成
2025-07-21 10:17:08.093 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 10:17:08.094 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-07-21 10:17:08.098 | DEBUG    | ui.views.account_view:_on_accounts_loaded:652 - 账户加载完成: 2个账户
2025-07-21 10:17:08.114 | DEBUG    | ui.views.account_view:_update_group_counts:188 - 分组计数已更新: {2: 2}
2025-07-21 10:17:08.118 | INFO     | ui.views.account_view:_auto_login_accounts:707 - 开始调用控制器进行 2 个账户的自动登录。
2025-07-21 10:17:08.118 | INFO     | app.services.account_service:batch_auto_login:1164 - 服务层：开始批量自动登录 2 个账户, 最大并发: 5
2025-07-21 10:17:08.118 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 10:17:08.136 | INFO     | app.services.account_service:batch_auto_login:1203 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-07-21 10:17:08.136 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 10:17:08.136 | INFO     | app.services.account_service:get_all_accounts:289 - 获取所有账户成功, 共 2 个
2025-07-21 10:17:08.136 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 10:17:08.141 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 10:17:08.143 | DEBUG    | ui.views.account_view:_update_group_counts:188 - 分组计数已更新: {2: 2}
2025-07-21 10:17:08.144 | DEBUG    | ui.views.account_view:_init_cache:155 - 账户缓存初始化完成，共 2 个账户
2025-07-21 10:17:08.155 | DEBUG    | ui.views.account_view:_on_accounts_loaded:652 - 账户加载完成: 2个账户
2025-07-21 10:17:08.184 | DEBUG    | ui.views.account_view:_update_group_counts:188 - 分组计数已更新: {2: 2}
2025-07-21 10:17:08.192 | INFO     | core.auth.api_service:verify_vip:438 - 会员验证成功
2025-07-21 10:17:08.193 | INFO     | utils.vip_checker:_check_vip_status:76 - VIP状态有效
2025-07-21 10:17:08.203 | INFO     | app.services.account_service:batch_auto_login:1203 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-07-21 10:17:08.203 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 10:17:08.205 | INFO     | app.services.account_service:batch_auto_login:1273 - 服务层：准备调用核心层处理 2 个账户的自动登录。
2025-07-21 10:17:08.205 | INFO     | app.services.account_service:batch_auto_login:1283 - 服务层：设置核心层任务超时为 120 秒。
2025-07-21 10:17:08.205 | INFO     | core.telegram.client_manager:batch_auto_login:1230 - 开始批量自动登录, 共 2 个账户, 最大并发 5
2025-07-21 10:17:08.206 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:271 - 创建客户端: H:\PyProject\TeleTest\APPDATA\sessions\+***********, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-21 10:17:08.206 | DEBUG    | core.telegram.client_manager:get_proxy:96 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-21 10:17:08.206 | DEBUG    | core.telegram.client_manager:get_proxy:143 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-07-21 10:17:08.213 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:271 - 创建客户端: H:\PyProject\TeleTest\APPDATA\sessions\+***********, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-21 10:17:08.213 | DEBUG    | core.telegram.client_manager:get_proxy:96 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-21 10:17:08.213 | DEBUG    | core.telegram.client_manager:get_proxy:143 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-07-21 10:17:08.243 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 10:17:08.323 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 10:17:08.370 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 10:17:08.385 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-07-21 10:17:08.387 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-07-21 10:17:08.389 | INFO     | ui.views.proxy_view:_load_initial_data:1044 - 初始数据加载完成，共 1 个代理
2025-07-21 10:17:13.508 | INFO     | core.telegram.client_manager:_connect_client:219 - 连接成功!
2025-07-21 10:17:14.292 | INFO     | core.telegram.client_manager:_verify_client_authorization:1205 - 账户已授权: +***********
2025-07-21 10:17:16.724 | INFO     | core.telegram.client_manager:_connect_client:219 - 连接成功!
2025-07-21 10:17:18.555 | INFO     | core.telegram.client_manager:_verify_client_authorization:1205 - 账户已授权: +***********
2025-07-21 10:17:20.567 | INFO     | core.telegram.client_manager:batch_auto_login:1379 - 批量登录完成: 总计 2, 成功 2, 失败 0
2025-07-21 10:17:21.230 | INFO     | app.services.account_service:batch_auto_login:1304 - 服务层：核心层登录完成: 成功 2, 失败 0 / 2
2025-07-21 10:18:06.805 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-21 10:19:06.802 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-21 10:20:06.805 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-21 10:21:06.803 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-21 10:22:06.803 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-21 10:22:21.740 | INFO     | app.services.account_service:get_proxy_ips:1043 - 获取有效代理IP列表（包含绑定计数）
2025-07-21 10:22:21.740 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 10:22:21.961 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 10:22:21.962 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 10:22:21.965 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 10:22:21.966 | INFO     | app.services.account_service:get_proxy_ips:1095 - 成功获取 1 个有效代理（已附带绑定计数）
2025-07-21 10:22:21.966 | INFO     | app.controllers.account_controller:get_proxy_ips:566 - 获取到1个有效代理IP
2025-07-21 10:22:36.542 | INFO     | app.services.account_service:update_account_profile:490 - 更新账户资料: 2
2025-07-21 10:22:36.542 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 10:22:36.553 | INFO     | core.telegram.user_manager:update_profile:89 - 正在更新用户资料: +***********
2025-07-21 10:22:38.425 | INFO     | core.telegram.user_manager:update_profile:130 - 更新用户资料成功: +***********
2025-07-21 10:22:38.578 | INFO     | data.repositories.account_repo:update_account:378 - 更新账户成功: 2
2025-07-21 10:22:38.578 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 10:22:40.114 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 10:22:40.192 | INFO     | app.services.account_service:get_all_accounts:289 - 获取所有账户成功, 共 2 个
2025-07-21 10:22:40.192 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 10:22:40.193 | DEBUG    | ui.views.account_view:_on_accounts_loaded:652 - 账户加载完成: 2个账户
2025-07-21 10:22:40.214 | DEBUG    | ui.views.account_view:_update_group_counts:188 - 分组计数已更新: {2: 2}
2025-07-21 10:22:44.341 | INFO     | app.services.account_service:refresh_account_info:606 - 刷新账户信息: 2
2025-07-21 10:22:44.341 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 10:22:44.350 | INFO     | core.telegram.user_manager:get_user_info:41 - 正在获取用户信息: +***********
2025-07-21 10:22:45.511 | INFO     | app.services.account_service:refresh_account_info:606 - 刷新账户信息: 1
2025-07-21 10:22:45.511 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 10:22:45.526 | INFO     | core.telegram.user_manager:get_user_info:41 - 正在获取用户信息: +***********
2025-07-21 10:22:45.556 | INFO     | core.telegram.user_manager:get_user_info:66 - 获取用户信息成功: +***********
2025-07-21 10:22:45.605 | INFO     | data.repositories.account_repo:update_account:378 - 更新账户成功: 2
2025-07-21 10:22:45.614 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 10:22:45.618 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 10:22:45.630 | INFO     | ui.views.account_view:_update_single_account_display:337 - 已实时更新账户显示: +***********
2025-07-21 10:22:45.630 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 10:22:46.730 | INFO     | core.telegram.user_manager:get_user_info:66 - 获取用户信息成功: +***********
2025-07-21 10:22:46.780 | INFO     | data.repositories.account_repo:update_account:378 - 更新账户成功: 1
2025-07-21 10:22:46.787 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 10:22:46.792 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 10:22:46.800 | INFO     | ui.views.account_view:_update_single_account_display:337 - 已实时更新账户显示: +***********
2025-07-21 10:22:46.800 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 10:23:06.803 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-21 10:23:51.467 | INFO     | app.services.account_service:get_proxy_ips:1043 - 获取有效代理IP列表（包含绑定计数）
2025-07-21 10:23:51.468 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 10:23:51.674 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 10:23:51.675 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 10:23:51.678 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 10:23:51.678 | INFO     | app.services.account_service:get_proxy_ips:1095 - 成功获取 1 个有效代理（已附带绑定计数）
2025-07-21 10:23:51.678 | INFO     | app.controllers.account_controller:get_proxy_ips:566 - 获取到1个有效代理IP
2025-07-21 10:24:06.804 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-21 10:24:09.683 | INFO     | app.services.account_service:update_account_profile:490 - 更新账户资料: 2
2025-07-21 10:24:09.683 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 10:24:09.691 | INFO     | core.telegram.user_manager:update_profile:89 - 正在更新用户资料: +***********
2025-07-21 10:24:14.301 | INFO     | core.telegram.user_manager:update_profile:130 - 更新用户资料成功: +***********
2025-07-21 10:24:14.694 | INFO     | data.repositories.account_repo:update_account:378 - 更新账户成功: 2
2025-07-21 10:24:14.694 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 10:24:16.244 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 10:24:16.329 | INFO     | app.services.account_service:get_all_accounts:289 - 获取所有账户成功, 共 2 个
2025-07-21 10:24:16.330 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 10:24:16.331 | DEBUG    | ui.views.account_view:_on_accounts_loaded:652 - 账户加载完成: 2个账户
2025-07-21 10:24:16.351 | DEBUG    | ui.views.account_view:_update_group_counts:188 - 分组计数已更新: {2: 2}
2025-07-21 10:25:06.808 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-21 10:26:06.803 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-21 10:26:59.026 | INFO     | app.services.account_service:get_proxy_ips:1043 - 获取有效代理IP列表（包含绑定计数）
2025-07-21 10:26:59.026 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 10:26:59.231 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 10:26:59.232 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 10:26:59.235 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 10:26:59.236 | INFO     | app.services.account_service:get_proxy_ips:1095 - 成功获取 1 个有效代理（已附带绑定计数）
2025-07-21 10:26:59.236 | INFO     | app.controllers.account_controller:get_proxy_ips:566 - 获取到1个有效代理IP
2025-07-21 10:27:05.778 | INFO     | app.services.account_service:update_account_profile:490 - 更新账户资料: 2
2025-07-21 10:27:05.778 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 10:27:05.786 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 10:27:06.802 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-21 10:27:07.345 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 10:27:07.396 | INFO     | app.services.account_service:get_all_accounts:289 - 获取所有账户成功, 共 2 个
2025-07-21 10:27:07.396 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 10:27:07.400 | DEBUG    | ui.views.account_view:_on_accounts_loaded:652 - 账户加载完成: 2个账户
2025-07-21 10:27:07.419 | DEBUG    | ui.views.account_view:_update_group_counts:188 - 分组计数已更新: {2: 2}
2025-07-21 10:28:06.808 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-21 10:29:06.809 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-21 10:30:06.805 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-21 10:55:15.559 | INFO     | utils.logger:init_logger:98 - 日志系统初始化完成，默认级别: DEBUG，日志目录: H:\PyProject\TeleTest\APPDATA\logs
2025-07-21 10:55:16.904 | INFO     | data.database:init_database:31 - 初始化数据库连接: H:\PyProject\TeleTest\APPDATA\database\telegram_manager.db
2025-07-21 10:55:16.921 | DEBUG    | data.database:init_database:58 - 创建数据库表
2025-07-21 10:55:16.932 | INFO     | data.database:init_database:61 - 数据库初始化完成
2025-07-21 10:55:18.493 | DEBUG    | app.controllers.auth_controller:initialize_software:43 - 控制器: 初始化软件 app_id=1000, version=1.2.7
2025-07-21 10:55:18.493 | DEBUG    | core.auth.api_service:init_software:55 - 发送初始化请求: https://server.xile188.com/api/user/1000/windowns10/1.2.7/ini, params={}
2025-07-21 10:55:18.741 | DEBUG    | core.auth.api_service:init_software:66 - 初始化数据解密成功: {'new_content': '- 新增功能\n  - 1.新增图片上传功能\n  - 2.新增软件文档\n  - 3.新增软件自动更新，后续更新会自动检测版本更新，无需再下载更新包\n  - 4.新增设置账户信息及发信控制\n- 修复bug\n  - 删除监听任务时，不删除对应任务采集到的数据。\n  - 监听设置群无法发送到目标群\n  - 导出监听数据时应当导出单个任务而非所有数据\n- 优化记录\n  - 优化发信规则，直接调取符合要求的账户发送，无账户时才需要等待延时。\n  - 完善软件设置', 'new_url': None, 'notice': {'id': '2', 'content': '公告内容测试3', 'time': '**********'}}
2025-07-21 10:55:18.747 | INFO     | app.controllers.auth_controller:login:61 - 控制器: 用户登录 account=demo01
2025-07-21 10:55:21.776 | INFO     | core.auth.api_service:login:127 - 用户登录: account=demo01
2025-07-21 10:55:22.025 | INFO     | core.auth.api_service:get_user_info:347 - 获取用户信息
2025-07-21 10:55:22.253 | INFO     | core.auth.api_service:get_user_info:375 - 获取用户信息成功
2025-07-21 10:55:22.269 | INFO     | __main__:on_auth_completed:91 - 用户登录成功，正在启动主窗口...
2025-07-21 10:55:22.303 | INFO     | core.telegram.client_worker:__init__:61 - Telegram客户端工作线程初始化
2025-07-21 10:55:22.304 | INFO     | utils.vip_checker:initialize:25 - VIP检查器已初始化，等待登录后开始检查
2025-07-21 10:55:22.304 | INFO     | ui.main_window:_initialize_core_components:108 - MainWindow: 初始化核心组件...
2025-07-21 10:55:22.305 | INFO     | core.telegram.client_worker:run:65 - Telegram客户端工作线程开始运行
2025-07-21 10:55:22.305 | INFO     | app.services.account_service:__init__:41 - 账户服务初始化
2025-07-21 10:55:22.305 | INFO     | core.proxy.proxy_task_manager:run:284 - 代理任务管理器线程启动
2025-07-21 10:55:22.306 | INFO     | app.controllers.account_controller:__init__:81 - 账户控制器初始化
2025-07-21 10:55:22.309 | INFO     | app.services.monitor_service:__init__:39 - 初始化监控任务服务
2025-07-21 10:55:22.310 | INFO     | core.monitor.filter:__init__:26 - 初始化消息过滤器
2025-07-21 10:55:22.310 | INFO     | core.monitor.extractor:__init__:24 - 初始化关键词提取器
2025-07-21 10:55:22.310 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-07-21 10:55:22.310 | INFO     | core.telegram.client_manager:__init__:66 - Telegram客户端管理器初始化
2025-07-21 10:55:22.310 | INFO     | core.monitor.notifier:__init__:90 - 初始化通知管理器
2025-07-21 10:55:22.310 | INFO     | core.telegram.user_manager:__init__:30 - Telegram用户管理器初始化
2025-07-21 10:55:22.311 | INFO     | app.controllers.telegram_monitor_controller:__init__:64 - 初始化 Telegram 监控控制器
2025-07-21 10:55:22.311 | INFO     | core.telegram.group_manager:__init__:36 - Telegram群组管理器初始化
2025-07-21 10:55:22.311 | DEBUG    | app.controllers.telegram_monitor_controller:__init__:82 - 控制器初始化完成
2025-07-21 10:55:22.311 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-07-21 10:55:22.313 | INFO     | core.telegram.monitor_manager:__init__:26 - Telegram监控管理器初始化
2025-07-21 10:55:22.314 | INFO     | core.telegram.telegram_service:__init__:39 - Telegram服务初始化完成
2025-07-21 10:55:22.577 | INFO     | ui.views.monitor_view:__init__:48 - 初始化监控视图
2025-07-21 10:55:22.577 | DEBUG    | ui.views.monitor_view:_connect_signals:80 - 连接控制器信号
2025-07-21 10:55:22.773 | INFO     | ui.views.proxy_view:__init__:96 - 初始化视图，加载第一页代理数据
2025-07-21 10:55:23.045 | INFO     | __main__:on_auth_completed:95 - 主窗口已启动
2025-07-21 10:55:23.092 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:88 - 尝试连接监听日志信号...
2025-07-21 10:55:23.092 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:90 - 监听日志信号连接成功
2025-07-21 10:55:23.093 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 10:55:23.097 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 10:55:23.101 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:145 - 加载监控任务列表 (搜索关键词: '')
2025-07-21 10:55:23.101 | DEBUG    | ui.views.monitor_view:_on_loading_started:620 - 加载中: 正在加载任务列表...
2025-07-21 10:55:23.101 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 10:55:23.108 | INFO     | app.services.proxy_service:refresh_proxies:712 - 刷新代理列表: offset=0, limit=20
2025-07-21 10:55:23.109 | INFO     | core.proxy.proxy_task_manager:submit_task:172 - 提交耗时任务到子线程: refresh_proxies_0_20
2025-07-21 10:55:23.109 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 10:55:23.111 | DEBUG    | app.services.proxy_service:get_proxies:500 - 获取代理列表: offset=0, limit=20
2025-07-21 10:55:23.111 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 10:55:23.140 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 10:55:23.144 | INFO     | app.controllers.invite_controller:get_invite_tasks:38 - 获取邀请任务列表, status=None
2025-07-21 10:55:23.144 | INFO     | app.services.invite_service:get_invite_tasks:121 - 获取邀请任务列表, status=None
2025-07-21 10:55:23.144 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 10:55:23.149 | INFO     | app.services.invite_service:task_initialize:39 - 初始化邀请服务，重置运行中的任务为暂停状态
2025-07-21 10:55:23.149 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 10:55:23.150 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 10:55:23.152 | INFO     | core.proxy.proxy_task_manager:task_wrapper:199 - 任务 refresh_proxies_0_20 完成
2025-07-21 10:55:23.154 | INFO     | utils.vip_checker:start_checking:37 - 开始VIP状态检查
2025-07-21 10:55:23.154 | DEBUG    | utils.vip_checker:_check_vip_status:61 - 正在检查VIP状态...
2025-07-21 10:55:23.154 | INFO     | core.auth.api_service:verify_vip:425 - 会员验证接口调用
2025-07-21 10:55:23.265 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 10:55:23.272 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 10:55:23.284 | INFO     | app.services.invite_service:task_initialize:48 - 成功重置 0 个任务状态
2025-07-21 10:55:23.284 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 10:55:23.284 | DEBUG    | ui.views.monitor_view:_refresh_task_list:175 - 刷新任务列表，共0个任务
2025-07-21 10:55:23.285 | INFO     | ui.views.monitor_view:_refresh_task_list:189 - 没有找到监控任务
2025-07-21 10:55:23.285 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 10:55:23.288 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 10:55:23.294 | INFO     | app.services.account_service:get_all_groups:95 - 获取所有账户分组成功, 共 2 个
2025-07-21 10:55:23.295 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 10:55:23.295 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 10:55:23.299 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 10:55:23.304 | DEBUG    | ui.views.account_view:_on_groups_loaded:519 - 分组加载完成: 2个分组
2025-07-21 10:55:23.332 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 10:55:23.485 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 10:55:23.486 | INFO     | app.services.account_service:get_all_accounts:289 - 获取所有账户成功, 共 2 个
2025-07-21 10:55:23.486 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 10:55:23.487 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 10:55:23.490 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 10:55:23.492 | INFO     | app.controllers.telegram_monitor_controller:_load_total_users_count_internal:191 - 成功加载总体统计数据: 总用户数 23, 今日采集 0, 日均采集 23, 运行天数 1
2025-07-21 10:55:23.492 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:156 - 成功加载 0 个任务
2025-07-21 10:55:23.492 | DEBUG    | ui.views.monitor_view:_on_loading_finished:625 - 加载完成
2025-07-21 10:55:23.492 | DEBUG    | ui.views.account_view:_on_accounts_loaded:652 - 账户加载完成: 2个账户
2025-07-21 10:55:23.520 | DEBUG    | ui.views.account_view:_update_group_counts:188 - 分组计数已更新: {2: 2}
2025-07-21 10:55:23.525 | INFO     | ui.views.account_view:_auto_login_accounts:707 - 开始调用控制器进行 2 个账户的自动登录。
2025-07-21 10:55:23.525 | INFO     | app.services.account_service:batch_auto_login:1164 - 服务层：开始批量自动登录 2 个账户, 最大并发: 5
2025-07-21 10:55:23.525 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 10:55:23.529 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-07-21 10:55:23.551 | INFO     | core.auth.api_service:verify_vip:438 - 会员验证成功
2025-07-21 10:55:23.551 | INFO     | utils.vip_checker:_check_vip_status:76 - VIP状态有效
2025-07-21 10:55:23.555 | INFO     | app.services.account_service:batch_auto_login:1203 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-07-21 10:55:23.556 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 10:55:23.556 | INFO     | app.services.account_service:get_all_accounts:289 - 获取所有账户成功, 共 2 个
2025-07-21 10:55:23.556 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 10:55:23.559 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 10:55:23.561 | DEBUG    | ui.views.account_view:_update_group_counts:188 - 分组计数已更新: {2: 2}
2025-07-21 10:55:23.561 | DEBUG    | ui.views.account_view:_init_cache:155 - 账户缓存初始化完成，共 2 个账户
2025-07-21 10:55:23.567 | DEBUG    | ui.views.account_view:_on_accounts_loaded:652 - 账户加载完成: 2个账户
2025-07-21 10:55:23.592 | DEBUG    | ui.views.account_view:_update_group_counts:188 - 分组计数已更新: {2: 2}
2025-07-21 10:55:23.612 | INFO     | app.services.account_service:batch_auto_login:1203 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-07-21 10:55:23.612 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 10:55:23.615 | INFO     | app.services.account_service:batch_auto_login:1273 - 服务层：准备调用核心层处理 2 个账户的自动登录。
2025-07-21 10:55:23.615 | INFO     | app.services.account_service:batch_auto_login:1283 - 服务层：设置核心层任务超时为 120 秒。
2025-07-21 10:55:23.615 | INFO     | core.telegram.client_manager:batch_auto_login:1230 - 开始批量自动登录, 共 2 个账户, 最大并发 5
2025-07-21 10:55:23.616 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:271 - 创建客户端: H:\PyProject\TeleTest\APPDATA\sessions\+***********, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-21 10:55:23.616 | DEBUG    | core.telegram.client_manager:get_proxy:96 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-21 10:55:23.616 | DEBUG    | core.telegram.client_manager:get_proxy:143 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-07-21 10:55:23.621 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:271 - 创建客户端: H:\PyProject\TeleTest\APPDATA\sessions\+***********, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-21 10:55:23.621 | DEBUG    | core.telegram.client_manager:get_proxy:96 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-21 10:55:23.621 | DEBUG    | core.telegram.client_manager:get_proxy:143 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-07-21 10:55:23.650 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 10:55:23.766 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 10:55:23.804 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-07-21 10:55:23.806 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-07-21 10:55:23.808 | INFO     | ui.views.proxy_view:_load_initial_data:1044 - 初始数据加载完成，共 1 个代理
2025-07-21 10:55:23.823 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 10:55:27.067 | INFO     | core.telegram.client_manager:_connect_client:219 - 连接成功!
2025-07-21 10:55:28.051 | INFO     | core.telegram.client_manager:_verify_client_authorization:1205 - 账户已授权: +***********
2025-07-21 10:55:28.456 | INFO     | core.telegram.client_manager:_connect_client:219 - 连接成功!
2025-07-21 10:55:29.359 | INFO     | core.telegram.client_manager:_verify_client_authorization:1205 - 账户已授权: +***********
2025-07-21 10:55:31.365 | INFO     | core.telegram.client_manager:batch_auto_login:1379 - 批量登录完成: 总计 2, 成功 2, 失败 0
2025-07-21 10:55:31.633 | INFO     | app.services.account_service:batch_auto_login:1304 - 服务层：核心层登录完成: 成功 2, 失败 0 / 2
2025-07-21 10:56:22.246 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-21 10:57:22.245 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-21 10:58:22.239 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-21 10:59:22.242 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-21 11:00:04.765 | INFO     | ui.main_window:closeEvent:380 - MainWindow: 接收到关闭事件
2025-07-21 11:00:04.781 | INFO     | ui.main_window:_cleanup_before_quit:266 - MainWindow: 执行清理资源...
2025-07-21 11:00:04.781 | INFO     | core.telegram.client_worker:stop:108 - 正在停止Telegram客户端工作线程
2025-07-21 11:00:04.790 | INFO     | core.telegram.client_manager:cleanup_async:1150 - 开始清理资源并断开所有连接
2025-07-21 11:00:04.790 | INFO     | core.telegram.client_manager:disconnect_all_clients:735 - 正在断开所有客户端连接
2025-07-21 11:00:04.791 | INFO     | core.telegram.client_manager:_safe_disconnect:756 - 正在断开客户端连接: +***********
2025-07-21 11:00:04.791 | INFO     | core.telegram.client_manager:_safe_disconnect:756 - 正在断开客户端连接: +***********
2025-07-21 11:00:04.806 | INFO     | core.telegram.client_manager:_safe_disconnect:760 - 断开客户端连接成功: +***********
2025-07-21 11:00:04.806 | INFO     | core.telegram.client_manager:_safe_disconnect:760 - 断开客户端连接成功: +***********
2025-07-21 11:00:04.806 | INFO     | core.telegram.client_manager:cleanup_async:1169 - 资源清理完成
2025-07-21 11:00:05.293 | INFO     | core.telegram.client_manager:cleanup_async:1150 - 开始清理资源并断开所有连接
2025-07-21 11:00:05.293 | INFO     | core.telegram.client_manager:disconnect_all_clients:735 - 正在断开所有客户端连接
2025-07-21 11:00:05.293 | INFO     | core.telegram.client_manager:cleanup_async:1169 - 资源清理完成
2025-07-21 11:00:05.799 | INFO     | core.telegram.client_worker:run:97 - Telegram客户端工作线程已退出
2025-07-21 11:00:05.799 | INFO     | core.telegram.client_worker:stop:122 - Telegram客户端工作线程已停止
2025-07-21 11:00:05.800 | INFO     | ui.main_window:_cleanup_before_quit:275 - TelegramClientWorker 已停止。
2025-07-21 11:00:05.800 | INFO     | ui.main_window:_cleanup_before_quit:279 - MainWindow 清理完成
2025-07-21 11:00:05.818 | INFO     | __main__:main:109 - 应用程序已正常退出
2025-07-21 11:26:51.499 | INFO     | utils.logger:init_logger:98 - 日志系统初始化完成，默认级别: DEBUG，日志目录: H:\PyProject\TeleTest\APPDATA\logs
2025-07-21 11:26:52.503 | INFO     | data.database:init_database:31 - 初始化数据库连接: H:\PyProject\TeleTest\APPDATA\database\telegram_manager.db
2025-07-21 11:26:52.519 | DEBUG    | data.database:init_database:58 - 创建数据库表
2025-07-21 11:26:52.529 | INFO     | data.database:init_database:61 - 数据库初始化完成
2025-07-21 11:26:53.121 | DEBUG    | app.controllers.auth_controller:initialize_software:43 - 控制器: 初始化软件 app_id=1000, version=1.2.7
2025-07-21 11:26:53.122 | DEBUG    | core.auth.api_service:init_software:55 - 发送初始化请求: https://server.xile188.com/api/user/1000/windowns10/1.2.7/ini, params={}
2025-07-21 11:26:53.388 | DEBUG    | core.auth.api_service:init_software:66 - 初始化数据解密成功: {'new_content': '- 新增功能\n  - 1.新增图片上传功能\n  - 2.新增软件文档\n  - 3.新增软件自动更新，后续更新会自动检测版本更新，无需再下载更新包\n  - 4.新增设置账户信息及发信控制\n- 修复bug\n  - 删除监听任务时，不删除对应任务采集到的数据。\n  - 监听设置群无法发送到目标群\n  - 导出监听数据时应当导出单个任务而非所有数据\n- 优化记录\n  - 优化发信规则，直接调取符合要求的账户发送，无账户时才需要等待延时。\n  - 完善软件设置', 'new_url': None, 'notice': {'id': '2', 'content': '公告内容测试3', 'time': '**********'}}
2025-07-21 11:26:53.396 | INFO     | app.controllers.auth_controller:login:61 - 控制器: 用户登录 account=demo01
2025-07-21 11:26:56.306 | INFO     | core.auth.api_service:login:127 - 用户登录: account=demo01
2025-07-21 11:26:56.570 | INFO     | core.auth.api_service:get_user_info:347 - 获取用户信息
2025-07-21 11:26:56.859 | INFO     | core.auth.api_service:get_user_info:375 - 获取用户信息成功
2025-07-21 11:26:56.866 | INFO     | __main__:on_auth_completed:91 - 用户登录成功，正在启动主窗口...
2025-07-21 11:26:56.888 | INFO     | core.telegram.client_worker:__init__:61 - Telegram客户端工作线程初始化
2025-07-21 11:26:56.888 | INFO     | utils.vip_checker:initialize:25 - VIP检查器已初始化，等待登录后开始检查
2025-07-21 11:26:56.888 | INFO     | ui.main_window:_initialize_core_components:108 - MainWindow: 初始化核心组件...
2025-07-21 11:26:56.890 | INFO     | core.telegram.client_worker:run:65 - Telegram客户端工作线程开始运行
2025-07-21 11:26:56.890 | INFO     | core.proxy.proxy_task_manager:run:284 - 代理任务管理器线程启动
2025-07-21 11:26:56.891 | INFO     | app.services.account_service:__init__:44 - 账户服务初始化
2025-07-21 11:26:56.891 | INFO     | app.controllers.account_controller:__init__:81 - 账户控制器初始化
2025-07-21 11:26:56.891 | INFO     | app.services.monitor_service:__init__:39 - 初始化监控任务服务
2025-07-21 11:26:56.891 | INFO     | core.monitor.filter:__init__:26 - 初始化消息过滤器
2025-07-21 11:26:56.892 | INFO     | core.monitor.extractor:__init__:24 - 初始化关键词提取器
2025-07-21 11:26:56.892 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-07-21 11:26:56.892 | INFO     | core.monitor.notifier:__init__:90 - 初始化通知管理器
2025-07-21 11:26:56.893 | INFO     | app.controllers.telegram_monitor_controller:__init__:64 - 初始化 Telegram 监控控制器
2025-07-21 11:26:56.893 | DEBUG    | app.controllers.telegram_monitor_controller:__init__:82 - 控制器初始化完成
2025-07-21 11:26:56.894 | INFO     | core.telegram.client_manager:__init__:66 - Telegram客户端管理器初始化
2025-07-21 11:26:56.895 | INFO     | core.telegram.user_manager:__init__:30 - Telegram用户管理器初始化
2025-07-21 11:26:56.896 | INFO     | core.telegram.group_manager:__init__:36 - Telegram群组管理器初始化
2025-07-21 11:26:56.896 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-07-21 11:26:56.897 | INFO     | core.telegram.monitor_manager:__init__:26 - Telegram监控管理器初始化
2025-07-21 11:26:56.898 | INFO     | core.telegram.telegram_service:__init__:39 - Telegram服务初始化完成
2025-07-21 11:26:57.087 | INFO     | ui.views.monitor_view:__init__:48 - 初始化监控视图
2025-07-21 11:26:57.088 | DEBUG    | ui.views.monitor_view:_connect_signals:80 - 连接控制器信号
2025-07-21 11:26:57.267 | INFO     | ui.views.proxy_view:__init__:96 - 初始化视图，加载第一页代理数据
2025-07-21 11:26:57.502 | INFO     | __main__:on_auth_completed:95 - 主窗口已启动
2025-07-21 11:26:57.548 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:88 - 尝试连接监听日志信号...
2025-07-21 11:26:57.548 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:90 - 监听日志信号连接成功
2025-07-21 11:26:57.548 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 11:26:57.552 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 11:26:57.556 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:145 - 加载监控任务列表 (搜索关键词: '')
2025-07-21 11:26:57.556 | DEBUG    | ui.views.monitor_view:_on_loading_started:620 - 加载中: 正在加载任务列表...
2025-07-21 11:26:57.556 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 11:26:57.563 | INFO     | app.services.proxy_service:refresh_proxies:712 - 刷新代理列表: offset=0, limit=20
2025-07-21 11:26:57.563 | INFO     | core.proxy.proxy_task_manager:submit_task:172 - 提交耗时任务到子线程: refresh_proxies_0_20
2025-07-21 11:26:57.563 | DEBUG    | app.services.proxy_service:get_proxies:500 - 获取代理列表: offset=0, limit=20
2025-07-21 11:26:57.564 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 11:26:57.564 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 11:26:57.567 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 11:26:57.587 | INFO     | app.controllers.invite_controller:get_invite_tasks:38 - 获取邀请任务列表, status=None
2025-07-21 11:26:57.591 | INFO     | app.services.invite_service:get_invite_tasks:121 - 获取邀请任务列表, status=None
2025-07-21 11:26:57.591 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 11:26:57.592 | INFO     | app.services.invite_service:task_initialize:39 - 初始化邀请服务，重置运行中的任务为暂停状态
2025-07-21 11:26:57.592 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 11:26:57.598 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 11:26:57.598 | INFO     | utils.vip_checker:start_checking:37 - 开始VIP状态检查
2025-07-21 11:26:57.598 | DEBUG    | utils.vip_checker:_check_vip_status:61 - 正在检查VIP状态...
2025-07-21 11:26:57.599 | INFO     | core.auth.api_service:verify_vip:425 - 会员验证接口调用
2025-07-21 11:26:57.599 | INFO     | core.proxy.proxy_task_manager:task_wrapper:199 - 任务 refresh_proxies_0_20 完成
2025-07-21 11:26:57.725 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 11:26:57.726 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 11:26:57.736 | DEBUG    | ui.views.monitor_view:_refresh_task_list:175 - 刷新任务列表，共0个任务
2025-07-21 11:26:57.736 | INFO     | ui.views.monitor_view:_refresh_task_list:189 - 没有找到监控任务
2025-07-21 11:26:57.736 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 11:26:57.737 | INFO     | app.services.invite_service:task_initialize:48 - 成功重置 0 个任务状态
2025-07-21 11:26:57.737 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 11:26:57.739 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 11:26:57.743 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 11:26:57.745 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 11:26:57.746 | INFO     | app.services.account_service:get_all_groups:98 - 获取所有账户分组成功, 共 2 个
2025-07-21 11:26:57.746 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 11:26:57.749 | DEBUG    | ui.views.account_view:_on_groups_loaded:519 - 分组加载完成: 2个分组
2025-07-21 11:26:57.764 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 11:26:57.883 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 11:26:57.886 | INFO     | app.services.account_service:get_all_accounts:292 - 获取所有账户成功, 共 2 个
2025-07-21 11:26:57.886 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 11:26:57.888 | INFO     | app.controllers.telegram_monitor_controller:_load_total_users_count_internal:191 - 成功加载总体统计数据: 总用户数 23, 今日采集 0, 日均采集 23, 运行天数 1
2025-07-21 11:26:57.888 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:156 - 成功加载 0 个任务
2025-07-21 11:26:57.888 | DEBUG    | ui.views.monitor_view:_on_loading_finished:625 - 加载完成
2025-07-21 11:26:57.888 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 11:26:57.891 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 11:26:57.891 | DEBUG    | ui.views.account_view:_on_accounts_loaded:652 - 账户加载完成: 2个账户
2025-07-21 11:26:57.911 | DEBUG    | ui.views.account_view:_update_group_counts:188 - 分组计数已更新: {2: 2}
2025-07-21 11:26:57.914 | INFO     | ui.views.account_view:_auto_login_accounts:707 - 开始调用控制器进行 2 个账户的自动登录。
2025-07-21 11:26:57.914 | INFO     | app.services.account_service:batch_auto_login:1181 - 服务层：开始批量自动登录 2 个账户, 最大并发: 5
2025-07-21 11:26:57.914 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 11:26:57.917 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-07-21 11:26:57.931 | INFO     | app.services.account_service:batch_auto_login:1220 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-07-21 11:26:57.931 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 11:26:57.933 | INFO     | app.services.account_service:get_all_accounts:292 - 获取所有账户成功, 共 2 个
2025-07-21 11:26:57.933 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 11:26:57.934 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 11:26:57.935 | DEBUG    | ui.views.account_view:_update_group_counts:188 - 分组计数已更新: {2: 2}
2025-07-21 11:26:57.936 | DEBUG    | ui.views.account_view:_init_cache:155 - 账户缓存初始化完成，共 2 个账户
2025-07-21 11:26:57.941 | DEBUG    | ui.views.account_view:_on_accounts_loaded:652 - 账户加载完成: 2个账户
2025-07-21 11:26:57.966 | DEBUG    | ui.views.account_view:_update_group_counts:188 - 分组计数已更新: {2: 2}
2025-07-21 11:26:57.985 | INFO     | app.services.account_service:batch_auto_login:1220 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-07-21 11:26:57.985 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 11:26:57.987 | INFO     | app.services.account_service:batch_auto_login:1290 - 服务层：准备调用核心层处理 2 个账户的自动登录。
2025-07-21 11:26:57.987 | INFO     | app.services.account_service:batch_auto_login:1300 - 服务层：设置核心层任务超时为 120 秒。
2025-07-21 11:26:57.987 | INFO     | core.telegram.client_manager:batch_auto_login:1230 - 开始批量自动登录, 共 2 个账户, 最大并发 5
2025-07-21 11:26:57.988 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:271 - 创建客户端: H:\PyProject\TeleTest\APPDATA\sessions\+***********, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-21 11:26:57.988 | DEBUG    | core.telegram.client_manager:get_proxy:96 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-21 11:26:57.988 | DEBUG    | core.telegram.client_manager:get_proxy:143 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-07-21 11:26:57.993 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:271 - 创建客户端: H:\PyProject\TeleTest\APPDATA\sessions\+***********, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-21 11:26:57.993 | DEBUG    | core.telegram.client_manager:get_proxy:96 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-21 11:26:57.993 | DEBUG    | core.telegram.client_manager:get_proxy:143 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-07-21 11:26:58.018 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 11:26:58.097 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 11:26:58.103 | INFO     | core.auth.api_service:verify_vip:438 - 会员验证成功
2025-07-21 11:26:58.103 | INFO     | utils.vip_checker:_check_vip_status:76 - VIP状态有效
2025-07-21 11:26:58.130 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 11:26:58.141 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-07-21 11:26:58.143 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-07-21 11:26:58.145 | INFO     | ui.views.proxy_view:_load_initial_data:1044 - 初始数据加载完成，共 1 个代理
2025-07-21 11:27:02.065 | INFO     | core.telegram.client_manager:_connect_client:219 - 连接成功!
2025-07-21 11:27:02.970 | INFO     | core.telegram.client_manager:_connect_client:219 - 连接成功!
2025-07-21 11:27:03.105 | INFO     | core.telegram.client_manager:_verify_client_authorization:1205 - 账户已授权: +***********
2025-07-21 11:27:03.857 | INFO     | core.telegram.client_manager:_verify_client_authorization:1205 - 账户已授权: +***********
2025-07-21 11:27:05.855 | INFO     | core.telegram.client_manager:batch_auto_login:1379 - 批量登录完成: 总计 2, 成功 2, 失败 0
2025-07-21 11:27:05.991 | INFO     | app.services.account_service:batch_auto_login:1321 - 服务层：核心层登录完成: 成功 2, 失败 0 / 2
2025-07-21 11:27:44.752 | INFO     | app.services.account_service:get_proxy_ips:1060 - 获取有效代理IP列表（包含绑定计数）
2025-07-21 11:27:44.753 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 11:27:44.960 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 11:27:44.961 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 11:27:44.964 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 11:27:44.965 | INFO     | app.services.account_service:get_proxy_ips:1112 - 成功获取 1 个有效代理（已附带绑定计数）
2025-07-21 11:27:44.965 | INFO     | app.controllers.account_controller:get_proxy_ips:566 - 获取到1个有效代理IP
2025-07-21 11:27:56.858 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-21 11:40:42.573 | INFO     | utils.logger:init_logger:98 - 日志系统初始化完成，默认级别: DEBUG，日志目录: H:\PyProject\TeleTest\APPDATA\logs
2025-07-21 11:40:43.681 | INFO     | data.database:init_database:31 - 初始化数据库连接: H:\PyProject\TeleTest\APPDATA\database\telegram_manager.db
2025-07-21 11:40:43.701 | DEBUG    | data.database:init_database:58 - 创建数据库表
2025-07-21 11:40:43.713 | INFO     | data.database:init_database:61 - 数据库初始化完成
2025-07-21 11:40:45.197 | DEBUG    | app.controllers.auth_controller:initialize_software:43 - 控制器: 初始化软件 app_id=1000, version=1.2.7
2025-07-21 11:40:45.197 | DEBUG    | core.auth.api_service:init_software:55 - 发送初始化请求: https://server.xile188.com/api/user/1000/windowns10/1.2.7/ini, params={}
2025-07-21 11:40:45.650 | DEBUG    | core.auth.api_service:init_software:66 - 初始化数据解密成功: {'new_content': '- 新增功能\n  - 1.新增图片上传功能\n  - 2.新增软件文档\n  - 3.新增软件自动更新，后续更新会自动检测版本更新，无需再下载更新包\n  - 4.新增设置账户信息及发信控制\n- 修复bug\n  - 删除监听任务时，不删除对应任务采集到的数据。\n  - 监听设置群无法发送到目标群\n  - 导出监听数据时应当导出单个任务而非所有数据\n- 优化记录\n  - 优化发信规则，直接调取符合要求的账户发送，无账户时才需要等待延时。\n  - 完善软件设置', 'new_url': None, 'notice': {'id': '2', 'content': '公告内容测试3', 'time': '**********'}}
2025-07-21 11:40:45.656 | INFO     | app.controllers.auth_controller:login:61 - 控制器: 用户登录 account=demo01
2025-07-21 11:40:48.701 | INFO     | core.auth.api_service:login:127 - 用户登录: account=demo01
2025-07-21 11:40:48.958 | INFO     | core.auth.api_service:get_user_info:347 - 获取用户信息
2025-07-21 11:40:49.193 | INFO     | core.auth.api_service:get_user_info:375 - 获取用户信息成功
2025-07-21 11:40:49.198 | INFO     | __main__:on_auth_completed:91 - 用户登录成功，正在启动主窗口...
2025-07-21 11:40:49.220 | INFO     | core.telegram.client_worker:__init__:61 - Telegram客户端工作线程初始化
2025-07-21 11:40:49.220 | INFO     | utils.vip_checker:initialize:25 - VIP检查器已初始化，等待登录后开始检查
2025-07-21 11:40:49.221 | INFO     | ui.main_window:_initialize_core_components:108 - MainWindow: 初始化核心组件...
2025-07-21 11:40:49.221 | INFO     | app.services.account_service:__init__:44 - 账户服务初始化
2025-07-21 11:40:49.221 | INFO     | app.controllers.account_controller:__init__:82 - 账户控制器初始化
2025-07-21 11:40:49.221 | INFO     | core.telegram.client_worker:run:65 - Telegram客户端工作线程开始运行
2025-07-21 11:40:49.222 | INFO     | core.proxy.proxy_task_manager:run:284 - 代理任务管理器线程启动
2025-07-21 11:40:49.222 | INFO     | app.services.monitor_service:__init__:39 - 初始化监控任务服务
2025-07-21 11:40:49.222 | INFO     | core.monitor.filter:__init__:26 - 初始化消息过滤器
2025-07-21 11:40:49.222 | INFO     | core.monitor.extractor:__init__:24 - 初始化关键词提取器
2025-07-21 11:40:49.224 | INFO     | core.telegram.client_manager:__init__:66 - Telegram客户端管理器初始化
2025-07-21 11:40:49.222 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-07-21 11:40:49.224 | INFO     | core.telegram.user_manager:__init__:30 - Telegram用户管理器初始化
2025-07-21 11:40:49.224 | INFO     | core.monitor.notifier:__init__:90 - 初始化通知管理器
2025-07-21 11:40:49.224 | INFO     | core.telegram.group_manager:__init__:36 - Telegram群组管理器初始化
2025-07-21 11:40:49.225 | INFO     | app.controllers.telegram_monitor_controller:__init__:64 - 初始化 Telegram 监控控制器
2025-07-21 11:40:49.225 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-07-21 11:40:49.225 | DEBUG    | app.controllers.telegram_monitor_controller:__init__:82 - 控制器初始化完成
2025-07-21 11:40:49.225 | INFO     | core.telegram.monitor_manager:__init__:26 - Telegram监控管理器初始化
2025-07-21 11:40:49.226 | INFO     | core.telegram.telegram_service:__init__:39 - Telegram服务初始化完成
2025-07-21 11:40:49.414 | INFO     | ui.views.monitor_view:__init__:48 - 初始化监控视图
2025-07-21 11:40:49.414 | DEBUG    | ui.views.monitor_view:_connect_signals:80 - 连接控制器信号
2025-07-21 11:40:49.593 | INFO     | ui.views.proxy_view:__init__:96 - 初始化视图，加载第一页代理数据
2025-07-21 11:40:49.827 | INFO     | __main__:on_auth_completed:95 - 主窗口已启动
2025-07-21 11:40:49.866 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:88 - 尝试连接监听日志信号...
2025-07-21 11:40:49.866 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:90 - 监听日志信号连接成功
2025-07-21 11:40:49.866 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 11:40:49.870 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 11:40:49.873 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:145 - 加载监控任务列表 (搜索关键词: '')
2025-07-21 11:40:49.874 | DEBUG    | ui.views.monitor_view:_on_loading_started:620 - 加载中: 正在加载任务列表...
2025-07-21 11:40:49.874 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 11:40:49.880 | INFO     | app.services.proxy_service:refresh_proxies:712 - 刷新代理列表: offset=0, limit=20
2025-07-21 11:40:49.880 | INFO     | core.proxy.proxy_task_manager:submit_task:172 - 提交耗时任务到子线程: refresh_proxies_0_20
2025-07-21 11:40:49.880 | DEBUG    | app.services.proxy_service:get_proxies:500 - 获取代理列表: offset=0, limit=20
2025-07-21 11:40:49.880 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 11:40:49.881 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 11:40:49.882 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 11:40:49.885 | INFO     | app.controllers.invite_controller:get_invite_tasks:38 - 获取邀请任务列表, status=None
2025-07-21 11:40:49.886 | INFO     | app.services.invite_service:get_invite_tasks:121 - 获取邀请任务列表, status=None
2025-07-21 11:40:49.897 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 11:40:49.907 | INFO     | app.services.invite_service:task_initialize:39 - 初始化邀请服务，重置运行中的任务为暂停状态
2025-07-21 11:40:49.907 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 11:40:49.912 | INFO     | utils.vip_checker:start_checking:37 - 开始VIP状态检查
2025-07-21 11:40:49.913 | DEBUG    | utils.vip_checker:_check_vip_status:61 - 正在检查VIP状态...
2025-07-21 11:40:49.913 | INFO     | core.auth.api_service:verify_vip:425 - 会员验证接口调用
2025-07-21 11:40:49.913 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 11:40:49.915 | INFO     | core.proxy.proxy_task_manager:task_wrapper:199 - 任务 refresh_proxies_0_20 完成
2025-07-21 11:40:50.032 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 11:40:50.035 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 11:40:50.040 | DEBUG    | ui.views.monitor_view:_refresh_task_list:175 - 刷新任务列表，共0个任务
2025-07-21 11:40:50.041 | INFO     | ui.views.monitor_view:_refresh_task_list:189 - 没有找到监控任务
2025-07-21 11:40:50.041 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 11:40:50.045 | INFO     | app.services.invite_service:task_initialize:48 - 成功重置 0 个任务状态
2025-07-21 11:40:50.045 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 11:40:50.047 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 11:40:50.050 | INFO     | app.services.account_service:get_all_groups:98 - 获取所有账户分组成功, 共 2 个
2025-07-21 11:40:50.051 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 11:40:50.051 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 11:40:50.052 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 11:40:50.055 | DEBUG    | ui.views.account_view:_on_groups_loaded:520 - 分组加载完成: 2个分组
2025-07-21 11:40:50.069 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 11:40:50.134 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 11:40:50.193 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 11:40:50.194 | INFO     | app.services.account_service:get_all_accounts:292 - 获取所有账户成功, 共 2 个
2025-07-21 11:40:50.194 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 11:40:50.195 | INFO     | app.controllers.telegram_monitor_controller:_load_total_users_count_internal:191 - 成功加载总体统计数据: 总用户数 23, 今日采集 0, 日均采集 23, 运行天数 1
2025-07-21 11:40:50.195 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:156 - 成功加载 0 个任务
2025-07-21 11:40:50.195 | DEBUG    | ui.views.monitor_view:_on_loading_finished:625 - 加载完成
2025-07-21 11:40:50.197 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-07-21 11:40:50.207 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 11:40:50.209 | DEBUG    | ui.views.account_view:_on_accounts_loaded:653 - 账户加载完成: 2个账户
2025-07-21 11:40:50.228 | DEBUG    | ui.views.account_view:_update_group_counts:189 - 分组计数已更新: {2: 2}
2025-07-21 11:40:50.231 | INFO     | ui.views.account_view:_auto_login_accounts:708 - 开始调用控制器进行 2 个账户的自动登录。
2025-07-21 11:40:50.232 | INFO     | app.services.account_service:batch_auto_login:1181 - 服务层：开始批量自动登录 2 个账户, 最大并发: 5
2025-07-21 11:40:50.232 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 11:40:50.246 | INFO     | app.services.account_service:batch_auto_login:1220 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-07-21 11:40:50.247 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 11:40:50.248 | INFO     | core.auth.api_service:verify_vip:438 - 会员验证成功
2025-07-21 11:40:50.249 | INFO     | utils.vip_checker:_check_vip_status:76 - VIP状态有效
2025-07-21 11:40:50.249 | INFO     | app.services.account_service:get_all_accounts:292 - 获取所有账户成功, 共 2 个
2025-07-21 11:40:50.249 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 11:40:50.252 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 11:40:50.254 | DEBUG    | ui.views.account_view:_update_group_counts:189 - 分组计数已更新: {2: 2}
2025-07-21 11:40:50.254 | DEBUG    | ui.views.account_view:_init_cache:156 - 账户缓存初始化完成，共 2 个账户
2025-07-21 11:40:50.260 | DEBUG    | ui.views.account_view:_on_accounts_loaded:653 - 账户加载完成: 2个账户
2025-07-21 11:40:50.284 | DEBUG    | ui.views.account_view:_update_group_counts:189 - 分组计数已更新: {2: 2}
2025-07-21 11:40:50.293 | INFO     | app.services.account_service:batch_auto_login:1220 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-07-21 11:40:50.294 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 11:40:50.296 | INFO     | app.services.account_service:batch_auto_login:1290 - 服务层：准备调用核心层处理 2 个账户的自动登录。
2025-07-21 11:40:50.296 | INFO     | app.services.account_service:batch_auto_login:1300 - 服务层：设置核心层任务超时为 120 秒。
2025-07-21 11:40:50.296 | INFO     | core.telegram.client_manager:batch_auto_login:1230 - 开始批量自动登录, 共 2 个账户, 最大并发 5
2025-07-21 11:40:50.297 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:271 - 创建客户端: H:\PyProject\TeleTest\APPDATA\sessions\+***********, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-21 11:40:50.297 | DEBUG    | core.telegram.client_manager:get_proxy:96 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-21 11:40:50.297 | DEBUG    | core.telegram.client_manager:get_proxy:143 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-07-21 11:40:50.302 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:271 - 创建客户端: H:\PyProject\TeleTest\APPDATA\sessions\+***********, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-21 11:40:50.303 | DEBUG    | core.telegram.client_manager:get_proxy:96 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-21 11:40:50.303 | DEBUG    | core.telegram.client_manager:get_proxy:143 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-07-21 11:40:50.332 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 11:40:50.407 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 11:40:50.412 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-07-21 11:40:50.414 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-07-21 11:40:50.415 | INFO     | ui.views.proxy_view:_load_initial_data:1044 - 初始数据加载完成，共 1 个代理
2025-07-21 11:40:50.439 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 11:40:54.255 | INFO     | core.telegram.client_manager:_connect_client:219 - 连接成功!
2025-07-21 11:40:54.603 | INFO     | core.telegram.client_manager:_connect_client:219 - 连接成功!
2025-07-21 11:40:55.060 | INFO     | core.telegram.client_manager:_verify_client_authorization:1205 - 账户已授权: +***********
2025-07-21 11:40:55.866 | INFO     | core.telegram.client_manager:_verify_client_authorization:1205 - 账户已授权: +***********
2025-07-21 11:40:57.886 | INFO     | core.telegram.client_manager:batch_auto_login:1379 - 批量登录完成: 总计 2, 成功 2, 失败 0
2025-07-21 11:40:58.311 | INFO     | app.services.account_service:batch_auto_login:1321 - 服务层：核心层登录完成: 成功 2, 失败 0 / 2
2025-07-21 11:40:58.311 | INFO     | app.services.account_service:_process_batch_login_results:1683 - 开始处理批量登录结果，共 2 个账户
2025-07-21 11:40:58.311 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 11:40:58.319 | INFO     | data.repositories.account_repo:update_account:394 - 更新账户成功: 2
2025-07-21 11:40:58.319 | INFO     | app.services.account_service:_process_batch_login_results:1719 - 已更新账户 +*********** 的用户信息
2025-07-21 11:40:58.325 | INFO     | data.repositories.account_repo:update_account:394 - 更新账户成功: 1
2025-07-21 11:40:58.325 | INFO     | app.services.account_service:_process_batch_login_results:1719 - 已更新账户 +*********** 的用户信息
2025-07-21 11:40:58.332 | INFO     | app.services.account_service:_process_batch_login_results:1742 - 批量登录结果处理完成，数据库已更新
2025-07-21 11:40:58.332 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 11:40:58.332 | INFO     | app.controllers.account_controller:auto_login_accounts:640 - 批量登录完成，触发账户数据刷新
2025-07-21 11:40:58.336 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 11:40:58.340 | INFO     | app.services.account_service:get_all_accounts:292 - 获取所有账户成功, 共 2 个
2025-07-21 11:40:58.340 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 11:40:58.341 | DEBUG    | ui.views.account_view:_on_accounts_loaded:653 - 账户加载完成: 2个账户
2025-07-21 11:40:58.366 | DEBUG    | ui.views.account_view:_update_group_counts:189 - 分组计数已更新: {2: 2}
2025-07-21 11:41:49.194 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-21 11:42:15.989 | INFO     | utils.logger:init_logger:98 - 日志系统初始化完成，默认级别: DEBUG，日志目录: H:\PyProject\TeleTest\APPDATA\logs
2025-07-21 11:42:17.058 | INFO     | data.database:init_database:31 - 初始化数据库连接: H:\PyProject\TeleTest\APPDATA\database\telegram_manager.db
2025-07-21 11:42:17.075 | DEBUG    | data.database:init_database:58 - 创建数据库表
2025-07-21 11:42:17.085 | INFO     | data.database:init_database:61 - 数据库初始化完成
2025-07-21 11:42:17.887 | DEBUG    | app.controllers.auth_controller:initialize_software:43 - 控制器: 初始化软件 app_id=1000, version=1.2.7
2025-07-21 11:42:17.887 | DEBUG    | core.auth.api_service:init_software:55 - 发送初始化请求: https://server.xile188.com/api/user/1000/windowns10/1.2.7/ini, params={}
2025-07-21 11:42:18.155 | DEBUG    | core.auth.api_service:init_software:66 - 初始化数据解密成功: {'new_content': '- 新增功能\n  - 1.新增图片上传功能\n  - 2.新增软件文档\n  - 3.新增软件自动更新，后续更新会自动检测版本更新，无需再下载更新包\n  - 4.新增设置账户信息及发信控制\n- 修复bug\n  - 删除监听任务时，不删除对应任务采集到的数据。\n  - 监听设置群无法发送到目标群\n  - 导出监听数据时应当导出单个任务而非所有数据\n- 优化记录\n  - 优化发信规则，直接调取符合要求的账户发送，无账户时才需要等待延时。\n  - 完善软件设置', 'new_url': None, 'notice': {'id': '2', 'content': '公告内容测试3', 'time': '**********'}}
2025-07-21 11:42:18.161 | INFO     | app.controllers.auth_controller:login:61 - 控制器: 用户登录 account=demo01
2025-07-21 11:42:21.138 | INFO     | core.auth.api_service:login:127 - 用户登录: account=demo01
2025-07-21 11:42:21.377 | INFO     | core.auth.api_service:get_user_info:347 - 获取用户信息
2025-07-21 11:42:21.680 | INFO     | core.auth.api_service:get_user_info:375 - 获取用户信息成功
2025-07-21 11:42:21.687 | INFO     | __main__:on_auth_completed:91 - 用户登录成功，正在启动主窗口...
2025-07-21 11:42:21.710 | INFO     | core.telegram.client_worker:__init__:61 - Telegram客户端工作线程初始化
2025-07-21 11:42:21.710 | INFO     | utils.vip_checker:initialize:25 - VIP检查器已初始化，等待登录后开始检查
2025-07-21 11:42:21.710 | INFO     | ui.main_window:_initialize_core_components:108 - MainWindow: 初始化核心组件...
2025-07-21 11:42:21.711 | INFO     | app.services.account_service:__init__:44 - 账户服务初始化
2025-07-21 11:42:21.711 | INFO     | core.telegram.client_worker:run:65 - Telegram客户端工作线程开始运行
2025-07-21 11:42:21.711 | INFO     | app.controllers.account_controller:__init__:82 - 账户控制器初始化
2025-07-21 11:42:21.711 | INFO     | core.proxy.proxy_task_manager:run:284 - 代理任务管理器线程启动
2025-07-21 11:42:21.711 | INFO     | app.services.monitor_service:__init__:39 - 初始化监控任务服务
2025-07-21 11:42:21.711 | INFO     | core.monitor.filter:__init__:26 - 初始化消息过滤器
2025-07-21 11:42:21.711 | INFO     | core.monitor.extractor:__init__:24 - 初始化关键词提取器
2025-07-21 11:42:21.711 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-07-21 11:42:21.712 | INFO     | core.monitor.notifier:__init__:90 - 初始化通知管理器
2025-07-21 11:42:21.713 | INFO     | app.controllers.telegram_monitor_controller:__init__:64 - 初始化 Telegram 监控控制器
2025-07-21 11:42:21.713 | INFO     | core.telegram.client_manager:__init__:66 - Telegram客户端管理器初始化
2025-07-21 11:42:21.713 | DEBUG    | app.controllers.telegram_monitor_controller:__init__:82 - 控制器初始化完成
2025-07-21 11:42:21.714 | INFO     | core.telegram.user_manager:__init__:30 - Telegram用户管理器初始化
2025-07-21 11:42:21.715 | INFO     | core.telegram.group_manager:__init__:36 - Telegram群组管理器初始化
2025-07-21 11:42:21.716 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-07-21 11:42:21.716 | INFO     | core.telegram.monitor_manager:__init__:26 - Telegram监控管理器初始化
2025-07-21 11:42:21.716 | INFO     | core.telegram.telegram_service:__init__:39 - Telegram服务初始化完成
2025-07-21 11:42:21.910 | INFO     | ui.views.monitor_view:__init__:48 - 初始化监控视图
2025-07-21 11:42:21.910 | DEBUG    | ui.views.monitor_view:_connect_signals:80 - 连接控制器信号
2025-07-21 11:42:22.087 | INFO     | ui.views.proxy_view:__init__:96 - 初始化视图，加载第一页代理数据
2025-07-21 11:42:22.317 | INFO     | __main__:on_auth_completed:95 - 主窗口已启动
2025-07-21 11:42:22.361 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:88 - 尝试连接监听日志信号...
2025-07-21 11:42:22.362 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:90 - 监听日志信号连接成功
2025-07-21 11:42:22.362 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 11:42:22.367 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 11:42:22.371 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:145 - 加载监控任务列表 (搜索关键词: '')
2025-07-21 11:42:22.371 | DEBUG    | ui.views.monitor_view:_on_loading_started:620 - 加载中: 正在加载任务列表...
2025-07-21 11:42:22.371 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 11:42:22.378 | INFO     | app.services.proxy_service:refresh_proxies:712 - 刷新代理列表: offset=0, limit=20
2025-07-21 11:42:22.378 | INFO     | core.proxy.proxy_task_manager:submit_task:172 - 提交耗时任务到子线程: refresh_proxies_0_20
2025-07-21 11:42:22.378 | DEBUG    | app.services.proxy_service:get_proxies:500 - 获取代理列表: offset=0, limit=20
2025-07-21 11:42:22.378 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 11:42:22.378 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 11:42:22.381 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 11:42:22.389 | INFO     | app.controllers.invite_controller:get_invite_tasks:38 - 获取邀请任务列表, status=None
2025-07-21 11:42:22.389 | INFO     | app.services.invite_service:get_invite_tasks:121 - 获取邀请任务列表, status=None
2025-07-21 11:42:22.395 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 11:42:22.406 | INFO     | app.services.invite_service:task_initialize:39 - 初始化邀请服务，重置运行中的任务为暂停状态
2025-07-21 11:42:22.406 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 11:42:22.411 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 11:42:22.411 | INFO     | utils.vip_checker:start_checking:37 - 开始VIP状态检查
2025-07-21 11:42:22.411 | DEBUG    | utils.vip_checker:_check_vip_status:61 - 正在检查VIP状态...
2025-07-21 11:42:22.412 | INFO     | core.auth.api_service:verify_vip:425 - 会员验证接口调用
2025-07-21 11:42:22.413 | INFO     | core.proxy.proxy_task_manager:task_wrapper:199 - 任务 refresh_proxies_0_20 完成
2025-07-21 11:42:22.619 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 11:42:22.620 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 11:42:22.629 | DEBUG    | ui.views.monitor_view:_refresh_task_list:175 - 刷新任务列表，共0个任务
2025-07-21 11:42:22.630 | INFO     | ui.views.monitor_view:_refresh_task_list:189 - 没有找到监控任务
2025-07-21 11:42:22.630 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 11:42:22.632 | INFO     | app.services.invite_service:task_initialize:48 - 成功重置 0 个任务状态
2025-07-21 11:42:22.632 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 11:42:22.636 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 11:42:22.637 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 11:42:22.637 | INFO     | app.services.account_service:get_all_groups:98 - 获取所有账户分组成功, 共 2 个
2025-07-21 11:42:22.638 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 11:42:22.638 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 11:42:22.641 | DEBUG    | ui.views.account_view:_on_groups_loaded:520 - 分组加载完成: 2个分组
2025-07-21 11:42:22.653 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 11:42:22.765 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 11:42:22.767 | INFO     | core.auth.api_service:verify_vip:438 - 会员验证成功
2025-07-21 11:42:22.767 | INFO     | utils.vip_checker:_check_vip_status:76 - VIP状态有效
2025-07-21 11:42:22.768 | INFO     | app.services.account_service:get_all_accounts:292 - 获取所有账户成功, 共 2 个
2025-07-21 11:42:22.768 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 11:42:22.769 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 11:42:22.771 | INFO     | app.controllers.telegram_monitor_controller:_load_total_users_count_internal:191 - 成功加载总体统计数据: 总用户数 23, 今日采集 0, 日均采集 23, 运行天数 1
2025-07-21 11:42:22.771 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:156 - 成功加载 0 个任务
2025-07-21 11:42:22.771 | DEBUG    | ui.views.monitor_view:_on_loading_finished:625 - 加载完成
2025-07-21 11:42:22.772 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-07-21 11:42:22.783 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 11:42:22.784 | DEBUG    | ui.views.account_view:_on_accounts_loaded:653 - 账户加载完成: 2个账户
2025-07-21 11:42:22.804 | DEBUG    | ui.views.account_view:_update_group_counts:189 - 分组计数已更新: {2: 2}
2025-07-21 11:42:22.808 | INFO     | ui.views.account_view:_auto_login_accounts:708 - 开始调用控制器进行 2 个账户的自动登录。
2025-07-21 11:42:22.808 | INFO     | app.services.account_service:batch_auto_login:1181 - 服务层：开始批量自动登录 2 个账户, 最大并发: 5
2025-07-21 11:42:22.808 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 11:42:22.821 | INFO     | app.services.account_service:get_all_accounts:292 - 获取所有账户成功, 共 2 个
2025-07-21 11:42:22.821 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 11:42:22.822 | INFO     | app.services.account_service:batch_auto_login:1220 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-07-21 11:42:22.823 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 11:42:22.823 | DEBUG    | ui.views.account_view:_update_group_counts:189 - 分组计数已更新: {2: 2}
2025-07-21 11:42:22.823 | DEBUG    | ui.views.account_view:_init_cache:156 - 账户缓存初始化完成，共 2 个账户
2025-07-21 11:42:22.828 | DEBUG    | ui.views.account_view:_on_accounts_loaded:653 - 账户加载完成: 2个账户
2025-07-21 11:42:22.889 | DEBUG    | ui.views.account_view:_update_group_counts:189 - 分组计数已更新: {2: 2}
2025-07-21 11:42:22.898 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 11:42:22.981 | INFO     | app.services.account_service:batch_auto_login:1220 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-07-21 11:42:22.981 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 11:42:22.982 | INFO     | app.services.account_service:batch_auto_login:1290 - 服务层：准备调用核心层处理 2 个账户的自动登录。
2025-07-21 11:42:22.983 | INFO     | app.services.account_service:batch_auto_login:1300 - 服务层：设置核心层任务超时为 120 秒。
2025-07-21 11:42:22.983 | INFO     | core.telegram.client_manager:batch_auto_login:1230 - 开始批量自动登录, 共 2 个账户, 最大并发 5
2025-07-21 11:42:22.984 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:271 - 创建客户端: H:\PyProject\TeleTest\APPDATA\sessions\+***********, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-21 11:42:22.984 | DEBUG    | core.telegram.client_manager:get_proxy:96 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-21 11:42:22.984 | DEBUG    | core.telegram.client_manager:get_proxy:143 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-07-21 11:42:22.990 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:271 - 创建客户端: H:\PyProject\TeleTest\APPDATA\sessions\+***********, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-21 11:42:22.990 | DEBUG    | core.telegram.client_manager:get_proxy:96 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-21 11:42:22.990 | DEBUG    | core.telegram.client_manager:get_proxy:143 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-07-21 11:42:23.017 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 11:42:23.148 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 11:42:23.196 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 11:42:23.253 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-07-21 11:42:23.255 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-07-21 11:42:23.257 | INFO     | ui.views.proxy_view:_load_initial_data:1044 - 初始数据加载完成，共 1 个代理
2025-07-21 11:42:26.969 | INFO     | core.telegram.client_manager:_connect_client:219 - 连接成功!
2025-07-21 11:42:27.958 | INFO     | core.telegram.client_manager:_verify_client_authorization:1205 - 账户已授权: +***********
2025-07-21 11:42:31.686 | INFO     | core.telegram.client_manager:_connect_client:219 - 连接成功!
2025-07-21 11:42:32.530 | INFO     | core.telegram.client_manager:_verify_client_authorization:1205 - 账户已授权: +***********
2025-07-21 11:42:34.543 | INFO     | core.telegram.client_manager:batch_auto_login:1379 - 批量登录完成: 总计 2, 成功 2, 失败 0
2025-07-21 11:42:35.046 | INFO     | app.services.account_service:batch_auto_login:1321 - 服务层：核心层登录完成: 成功 2, 失败 0 / 2
2025-07-21 11:42:35.047 | INFO     | app.services.account_service:_process_batch_login_results:1683 - 开始处理批量登录结果，共 2 个账户
2025-07-21 11:42:35.047 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 11:42:35.054 | INFO     | data.repositories.account_repo:update_account:394 - 更新账户成功: 1
2025-07-21 11:42:35.054 | INFO     | app.services.account_service:_process_batch_login_results:1719 - 已更新账户 +*********** 的用户信息
2025-07-21 11:42:35.059 | INFO     | data.repositories.account_repo:update_account:394 - 更新账户成功: 2
2025-07-21 11:42:35.059 | INFO     | app.services.account_service:_process_batch_login_results:1719 - 已更新账户 +*********** 的用户信息
2025-07-21 11:42:35.065 | INFO     | app.services.account_service:_process_batch_login_results:1742 - 批量登录结果处理完成，数据库已更新
2025-07-21 11:42:35.065 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 11:42:35.066 | INFO     | app.controllers.account_controller:auto_login_accounts:640 - 批量登录完成，触发账户数据刷新
2025-07-21 11:42:35.071 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 11:42:35.076 | INFO     | app.services.account_service:get_all_accounts:292 - 获取所有账户成功, 共 2 个
2025-07-21 11:42:35.077 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 11:42:35.078 | DEBUG    | ui.views.account_view:_on_accounts_loaded:653 - 账户加载完成: 2个账户
2025-07-21 11:42:35.097 | DEBUG    | ui.views.account_view:_update_group_counts:189 - 分组计数已更新: {2: 2}
2025-07-21 11:42:48.083 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 11:42:48.101 | INFO     | app.services.account_service:get_accounts_by_group:314 - 获取分组 2 的账户成功, 共 2 个
2025-07-21 11:42:48.101 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 11:42:48.102 | DEBUG    | ui.views.account_view:_on_accounts_loaded:653 - 账户加载完成: 2个账户
2025-07-21 11:42:48.948 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 11:42:48.955 | INFO     | app.services.account_service:get_all_accounts:292 - 获取所有账户成功, 共 2 个
2025-07-21 11:42:48.955 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 11:42:48.955 | DEBUG    | ui.views.account_view:_on_accounts_loaded:653 - 账户加载完成: 2个账户
2025-07-21 11:42:48.974 | DEBUG    | ui.views.account_view:_update_group_counts:189 - 分组计数已更新: {2: 2}
2025-07-21 11:42:49.776 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 11:42:49.783 | INFO     | app.services.account_service:get_accounts_by_group:314 - 获取分组 2 的账户成功, 共 2 个
2025-07-21 11:42:49.784 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 11:42:49.784 | DEBUG    | ui.views.account_view:_on_accounts_loaded:653 - 账户加载完成: 2个账户
2025-07-21 11:42:50.459 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 11:42:50.466 | INFO     | app.services.account_service:get_all_accounts:292 - 获取所有账户成功, 共 2 个
2025-07-21 11:42:50.467 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 11:42:50.468 | DEBUG    | ui.views.account_view:_on_accounts_loaded:653 - 账户加载完成: 2个账户
2025-07-21 11:42:50.486 | DEBUG    | ui.views.account_view:_update_group_counts:189 - 分组计数已更新: {2: 2}
2025-07-21 11:42:53.204 | INFO     | app.services.account_service:get_proxy_ips:1060 - 获取有效代理IP列表（包含绑定计数）
2025-07-21 11:42:53.204 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 11:42:53.423 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 11:42:53.423 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 11:42:53.427 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 11:42:53.428 | INFO     | app.services.account_service:get_proxy_ips:1112 - 成功获取 1 个有效代理（已附带绑定计数）
2025-07-21 11:42:53.428 | INFO     | app.controllers.account_controller:get_proxy_ips:567 - 获取到1个有效代理IP
2025-07-21 11:43:05.916 | INFO     | app.services.account_service:update_account_profile:493 - 更新账户资料: 1
2025-07-21 11:43:05.916 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 11:43:05.925 | INFO     | data.repositories.account_repo:update_account:394 - 更新账户成功: 1
2025-07-21 11:43:05.925 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 11:43:07.452 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 11:43:07.526 | INFO     | app.services.account_service:get_all_accounts:292 - 获取所有账户成功, 共 2 个
2025-07-21 11:43:07.527 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 11:43:07.528 | DEBUG    | ui.views.account_view:_on_accounts_loaded:653 - 账户加载完成: 2个账户
2025-07-21 11:43:07.549 | DEBUG    | ui.views.account_view:_update_group_counts:189 - 分组计数已更新: {2: 2}
2025-07-21 11:43:11.318 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 11:43:11.335 | INFO     | app.services.account_service:get_accounts_by_group:314 - 获取分组 2 的账户成功, 共 2 个
2025-07-21 11:43:11.335 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 11:43:11.336 | DEBUG    | ui.views.account_view:_on_accounts_loaded:653 - 账户加载完成: 2个账户
2025-07-21 11:43:11.841 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 11:43:11.852 | INFO     | app.services.account_service:get_accounts_by_group:314 - 获取分组 1 的账户成功, 共 0 个
2025-07-21 11:43:11.852 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 11:43:11.852 | DEBUG    | ui.views.account_view:_on_accounts_loaded:653 - 账户加载完成: 0个账户
2025-07-21 11:43:12.377 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 11:43:12.385 | INFO     | app.services.account_service:get_accounts_by_group:314 - 获取分组 2 的账户成功, 共 2 个
2025-07-21 11:43:12.386 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 11:43:12.387 | DEBUG    | ui.views.account_view:_on_accounts_loaded:653 - 账户加载完成: 2个账户
2025-07-21 11:43:13.880 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 11:43:13.889 | INFO     | app.services.account_service:get_all_accounts:292 - 获取所有账户成功, 共 2 个
2025-07-21 11:43:13.889 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 11:43:13.890 | DEBUG    | ui.views.account_view:_on_accounts_loaded:653 - 账户加载完成: 2个账户
2025-07-21 11:43:13.909 | DEBUG    | ui.views.account_view:_update_group_counts:189 - 分组计数已更新: {2: 2}
2025-07-21 11:43:14.484 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 11:43:14.492 | INFO     | app.services.account_service:get_accounts_by_group:314 - 获取分组 1 的账户成功, 共 0 个
2025-07-21 11:43:14.492 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 11:43:14.495 | DEBUG    | ui.views.account_view:_on_accounts_loaded:653 - 账户加载完成: 0个账户
2025-07-21 11:43:15.537 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 11:43:15.548 | INFO     | app.services.account_service:get_all_accounts:292 - 获取所有账户成功, 共 2 个
2025-07-21 11:43:15.549 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 11:43:15.550 | DEBUG    | ui.views.account_view:_on_accounts_loaded:653 - 账户加载完成: 2个账户
2025-07-21 11:43:15.589 | DEBUG    | ui.views.account_view:_update_group_counts:189 - 分组计数已更新: {2: 2}
2025-07-21 11:43:16.018 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 11:43:16.027 | INFO     | app.services.account_service:get_all_accounts:292 - 获取所有账户成功, 共 2 个
2025-07-21 11:43:16.027 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 11:43:16.029 | DEBUG    | ui.views.account_view:_on_accounts_loaded:653 - 账户加载完成: 2个账户
2025-07-21 11:43:16.049 | DEBUG    | ui.views.account_view:_update_group_counts:189 - 分组计数已更新: {2: 2}
2025-07-21 11:43:21.680 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-21 11:43:23.988 | INFO     | utils.logger:init_logger:98 - 日志系统初始化完成，默认级别: DEBUG，日志目录: H:\PyProject\TeleTest\APPDATA\logs
2025-07-21 11:43:25.051 | INFO     | data.database:init_database:31 - 初始化数据库连接: H:\PyProject\TeleTest\APPDATA\database\telegram_manager.db
2025-07-21 11:43:25.066 | DEBUG    | data.database:init_database:58 - 创建数据库表
2025-07-21 11:43:25.077 | INFO     | data.database:init_database:61 - 数据库初始化完成
2025-07-21 11:43:25.656 | DEBUG    | app.controllers.auth_controller:initialize_software:43 - 控制器: 初始化软件 app_id=1000, version=1.2.7
2025-07-21 11:43:25.657 | DEBUG    | core.auth.api_service:init_software:55 - 发送初始化请求: https://server.xile188.com/api/user/1000/windowns10/1.2.7/ini, params={}
2025-07-21 11:43:25.899 | DEBUG    | core.auth.api_service:init_software:66 - 初始化数据解密成功: {'new_content': '- 新增功能\n  - 1.新增图片上传功能\n  - 2.新增软件文档\n  - 3.新增软件自动更新，后续更新会自动检测版本更新，无需再下载更新包\n  - 4.新增设置账户信息及发信控制\n- 修复bug\n  - 删除监听任务时，不删除对应任务采集到的数据。\n  - 监听设置群无法发送到目标群\n  - 导出监听数据时应当导出单个任务而非所有数据\n- 优化记录\n  - 优化发信规则，直接调取符合要求的账户发送，无账户时才需要等待延时。\n  - 完善软件设置', 'new_url': None, 'notice': {'id': '2', 'content': '公告内容测试3', 'time': '**********'}}
2025-07-21 11:43:25.907 | INFO     | app.controllers.auth_controller:login:61 - 控制器: 用户登录 account=demo01
2025-07-21 11:43:28.788 | INFO     | core.auth.api_service:login:127 - 用户登录: account=demo01
2025-07-21 11:43:29.121 | INFO     | core.auth.api_service:get_user_info:347 - 获取用户信息
2025-07-21 11:43:29.320 | INFO     | core.auth.api_service:get_user_info:375 - 获取用户信息成功
2025-07-21 11:43:29.327 | INFO     | __main__:on_auth_completed:91 - 用户登录成功，正在启动主窗口...
2025-07-21 11:43:29.352 | INFO     | core.telegram.client_worker:__init__:61 - Telegram客户端工作线程初始化
2025-07-21 11:43:29.352 | INFO     | utils.vip_checker:initialize:25 - VIP检查器已初始化，等待登录后开始检查
2025-07-21 11:43:29.352 | INFO     | ui.main_window:_initialize_core_components:108 - MainWindow: 初始化核心组件...
2025-07-21 11:43:29.354 | INFO     | core.telegram.client_worker:run:65 - Telegram客户端工作线程开始运行
2025-07-21 11:43:29.354 | INFO     | core.proxy.proxy_task_manager:run:284 - 代理任务管理器线程启动
2025-07-21 11:43:29.355 | INFO     | app.services.account_service:__init__:44 - 账户服务初始化
2025-07-21 11:43:29.356 | INFO     | app.controllers.account_controller:__init__:82 - 账户控制器初始化
2025-07-21 11:43:29.356 | INFO     | app.services.monitor_service:__init__:39 - 初始化监控任务服务
2025-07-21 11:43:29.356 | INFO     | core.monitor.filter:__init__:26 - 初始化消息过滤器
2025-07-21 11:43:29.357 | INFO     | core.monitor.extractor:__init__:24 - 初始化关键词提取器
2025-07-21 11:43:29.357 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-07-21 11:43:29.357 | INFO     | core.monitor.notifier:__init__:90 - 初始化通知管理器
2025-07-21 11:43:29.357 | INFO     | app.controllers.telegram_monitor_controller:__init__:64 - 初始化 Telegram 监控控制器
2025-07-21 11:43:29.357 | INFO     | core.telegram.client_manager:__init__:66 - Telegram客户端管理器初始化
2025-07-21 11:43:29.357 | DEBUG    | app.controllers.telegram_monitor_controller:__init__:82 - 控制器初始化完成
2025-07-21 11:43:29.357 | INFO     | core.telegram.user_manager:__init__:30 - Telegram用户管理器初始化
2025-07-21 11:43:29.359 | INFO     | core.telegram.group_manager:__init__:36 - Telegram群组管理器初始化
2025-07-21 11:43:29.359 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-07-21 11:43:29.360 | INFO     | core.telegram.monitor_manager:__init__:26 - Telegram监控管理器初始化
2025-07-21 11:43:29.360 | INFO     | core.telegram.telegram_service:__init__:39 - Telegram服务初始化完成
2025-07-21 11:43:29.547 | INFO     | ui.views.monitor_view:__init__:48 - 初始化监控视图
2025-07-21 11:43:29.548 | DEBUG    | ui.views.monitor_view:_connect_signals:80 - 连接控制器信号
2025-07-21 11:43:29.724 | INFO     | ui.views.proxy_view:__init__:96 - 初始化视图，加载第一页代理数据
2025-07-21 11:43:29.956 | INFO     | __main__:on_auth_completed:95 - 主窗口已启动
2025-07-21 11:43:30.000 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:88 - 尝试连接监听日志信号...
2025-07-21 11:43:30.000 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:90 - 监听日志信号连接成功
2025-07-21 11:43:30.002 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 11:43:30.007 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 11:43:30.011 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:145 - 加载监控任务列表 (搜索关键词: '')
2025-07-21 11:43:30.011 | DEBUG    | ui.views.monitor_view:_on_loading_started:620 - 加载中: 正在加载任务列表...
2025-07-21 11:43:30.011 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 11:43:30.017 | INFO     | app.services.proxy_service:refresh_proxies:712 - 刷新代理列表: offset=0, limit=20
2025-07-21 11:43:30.017 | INFO     | core.proxy.proxy_task_manager:submit_task:172 - 提交耗时任务到子线程: refresh_proxies_0_20
2025-07-21 11:43:30.017 | DEBUG    | app.services.proxy_service:get_proxies:500 - 获取代理列表: offset=0, limit=20
2025-07-21 11:43:30.017 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 11:43:30.018 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 11:43:30.020 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 11:43:30.023 | INFO     | app.controllers.invite_controller:get_invite_tasks:38 - 获取邀请任务列表, status=None
2025-07-21 11:43:30.023 | INFO     | app.services.invite_service:get_invite_tasks:121 - 获取邀请任务列表, status=None
2025-07-21 11:43:30.023 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 11:43:30.041 | INFO     | app.services.invite_service:task_initialize:39 - 初始化邀请服务，重置运行中的任务为暂停状态
2025-07-21 11:43:30.045 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 11:43:30.050 | INFO     | utils.vip_checker:start_checking:37 - 开始VIP状态检查
2025-07-21 11:43:30.050 | DEBUG    | utils.vip_checker:_check_vip_status:61 - 正在检查VIP状态...
2025-07-21 11:43:30.050 | INFO     | core.auth.api_service:verify_vip:425 - 会员验证接口调用
2025-07-21 11:43:30.051 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 11:43:30.052 | INFO     | core.proxy.proxy_task_manager:task_wrapper:199 - 任务 refresh_proxies_0_20 完成
2025-07-21 11:43:30.190 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 11:43:30.191 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 11:43:30.198 | DEBUG    | ui.views.monitor_view:_refresh_task_list:175 - 刷新任务列表，共0个任务
2025-07-21 11:43:30.198 | INFO     | ui.views.monitor_view:_refresh_task_list:189 - 没有找到监控任务
2025-07-21 11:43:30.199 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 11:43:30.200 | INFO     | app.services.invite_service:task_initialize:48 - 成功重置 0 个任务状态
2025-07-21 11:43:30.200 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 11:43:30.201 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 11:43:30.205 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 11:43:30.207 | INFO     | app.services.account_service:get_all_groups:98 - 获取所有账户分组成功, 共 2 个
2025-07-21 11:43:30.208 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 11:43:30.208 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 11:43:30.269 | DEBUG    | ui.views.account_view:_on_groups_loaded:520 - 分组加载完成: 2个分组
2025-07-21 11:43:30.282 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 11:43:30.344 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 11:43:30.346 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 11:43:30.348 | INFO     | app.controllers.telegram_monitor_controller:_load_total_users_count_internal:191 - 成功加载总体统计数据: 总用户数 23, 今日采集 0, 日均采集 23, 运行天数 1
2025-07-21 11:43:30.348 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:156 - 成功加载 0 个任务
2025-07-21 11:43:30.348 | DEBUG    | ui.views.monitor_view:_on_loading_finished:625 - 加载完成
2025-07-21 11:43:30.350 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-07-21 11:43:30.354 | INFO     | app.services.account_service:get_all_accounts:292 - 获取所有账户成功, 共 2 个
2025-07-21 11:43:30.354 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 11:43:30.363 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 11:43:30.364 | DEBUG    | ui.views.account_view:_on_accounts_loaded:653 - 账户加载完成: 2个账户
2025-07-21 11:43:30.384 | DEBUG    | ui.views.account_view:_update_group_counts:189 - 分组计数已更新: {2: 2}
2025-07-21 11:43:30.388 | INFO     | ui.views.account_view:_auto_login_accounts:708 - 开始调用控制器进行 2 个账户的自动登录。
2025-07-21 11:43:30.388 | INFO     | app.services.account_service:batch_auto_login:1181 - 服务层：开始批量自动登录 2 个账户, 最大并发: 5
2025-07-21 11:43:30.388 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 11:43:30.401 | INFO     | app.services.account_service:get_all_accounts:292 - 获取所有账户成功, 共 2 个
2025-07-21 11:43:30.402 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 11:43:30.403 | INFO     | app.services.account_service:batch_auto_login:1220 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-07-21 11:43:30.403 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 11:43:30.405 | DEBUG    | ui.views.account_view:_update_group_counts:189 - 分组计数已更新: {2: 2}
2025-07-21 11:43:30.405 | DEBUG    | ui.views.account_view:_init_cache:156 - 账户缓存初始化完成，共 2 个账户
2025-07-21 11:43:30.410 | DEBUG    | ui.views.account_view:_on_accounts_loaded:653 - 账户加载完成: 2个账户
2025-07-21 11:43:30.440 | DEBUG    | ui.views.account_view:_update_group_counts:189 - 分组计数已更新: {2: 2}
2025-07-21 11:43:30.448 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 11:43:30.451 | INFO     | core.auth.api_service:verify_vip:438 - 会员验证成功
2025-07-21 11:43:30.451 | INFO     | utils.vip_checker:_check_vip_status:76 - VIP状态有效
2025-07-21 11:43:30.456 | INFO     | app.services.account_service:batch_auto_login:1220 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-07-21 11:43:30.456 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 11:43:30.457 | INFO     | app.services.account_service:batch_auto_login:1290 - 服务层：准备调用核心层处理 2 个账户的自动登录。
2025-07-21 11:43:30.458 | INFO     | app.services.account_service:batch_auto_login:1300 - 服务层：设置核心层任务超时为 120 秒。
2025-07-21 11:43:30.458 | INFO     | core.telegram.client_manager:batch_auto_login:1230 - 开始批量自动登录, 共 2 个账户, 最大并发 5
2025-07-21 11:43:30.458 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:271 - 创建客户端: H:\PyProject\TeleTest\APPDATA\sessions\+***********, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-21 11:43:30.459 | DEBUG    | core.telegram.client_manager:get_proxy:96 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-21 11:43:30.459 | DEBUG    | core.telegram.client_manager:get_proxy:143 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-07-21 11:43:30.465 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:271 - 创建客户端: H:\PyProject\TeleTest\APPDATA\sessions\+***********, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-21 11:43:30.465 | DEBUG    | core.telegram.client_manager:get_proxy:96 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-21 11:43:30.466 | DEBUG    | core.telegram.client_manager:get_proxy:143 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-07-21 11:43:30.496 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 11:43:30.568 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 11:43:30.574 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-07-21 11:43:30.576 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-07-21 11:43:30.577 | INFO     | ui.views.proxy_view:_load_initial_data:1044 - 初始数据加载完成，共 1 个代理
2025-07-21 11:43:30.641 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 11:43:34.690 | INFO     | core.telegram.client_manager:_connect_client:219 - 连接成功!
2025-07-21 11:43:35.287 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 11:43:35.296 | INFO     | app.services.account_service:get_accounts_by_group:314 - 获取分组 2 的账户成功, 共 2 个
2025-07-21 11:43:35.296 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 11:43:35.297 | DEBUG    | ui.views.account_view:_on_accounts_loaded:653 - 账户加载完成: 2个账户
2025-07-21 11:43:36.346 | INFO     | core.telegram.client_manager:_verify_client_authorization:1205 - 账户已授权: +***********
2025-07-21 11:43:37.064 | INFO     | core.telegram.client_manager:_connect_client:219 - 连接成功!
2025-07-21 11:43:38.226 | INFO     | core.telegram.client_manager:_verify_client_authorization:1205 - 账户已授权: +***********
2025-07-21 11:43:40.233 | INFO     | core.telegram.client_manager:batch_auto_login:1379 - 批量登录完成: 总计 2, 成功 2, 失败 0
2025-07-21 11:43:40.481 | INFO     | app.services.account_service:batch_auto_login:1321 - 服务层：核心层登录完成: 成功 2, 失败 0 / 2
2025-07-21 11:43:40.481 | INFO     | app.services.account_service:_process_batch_login_results:1683 - 开始处理批量登录结果，共 2 个账户
2025-07-21 11:43:40.481 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 11:43:40.486 | INFO     | data.repositories.account_repo:update_account:394 - 更新账户成功: 1
2025-07-21 11:43:40.486 | INFO     | app.services.account_service:_process_batch_login_results:1719 - 已更新账户 +*********** 的用户信息
2025-07-21 11:43:40.492 | INFO     | data.repositories.account_repo:update_account:394 - 更新账户成功: 2
2025-07-21 11:43:40.492 | INFO     | app.services.account_service:_process_batch_login_results:1719 - 已更新账户 +*********** 的用户信息
2025-07-21 11:43:40.497 | INFO     | app.services.account_service:_process_batch_login_results:1742 - 批量登录结果处理完成，数据库已更新
2025-07-21 11:43:40.497 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 11:43:40.498 | INFO     | app.controllers.account_controller:auto_login_accounts:640 - 批量登录完成，触发账户数据刷新
2025-07-21 11:43:40.502 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 11:43:40.508 | INFO     | app.services.account_service:get_accounts_by_group:314 - 获取分组 2 的账户成功, 共 2 个
2025-07-21 11:43:40.508 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 11:43:40.509 | DEBUG    | ui.views.account_view:_on_accounts_loaded:653 - 账户加载完成: 2个账户
2025-07-21 11:44:29.318 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-21 12:08:09.364 | INFO     | utils.logger:init_logger:98 - 日志系统初始化完成，默认级别: DEBUG，日志目录: H:\PyProject\TeleTest\APPDATA\logs
2025-07-21 12:08:10.993 | INFO     | data.database:init_database:31 - 初始化数据库连接: H:\PyProject\TeleTest\APPDATA\database\telegram_manager.db
2025-07-21 12:08:11.024 | DEBUG    | data.database:init_database:58 - 创建数据库表
2025-07-21 12:08:11.038 | INFO     | data.database:init_database:61 - 数据库初始化完成
2025-07-21 12:08:12.567 | DEBUG    | app.controllers.auth_controller:initialize_software:43 - 控制器: 初始化软件 app_id=1000, version=1.2.7
2025-07-21 12:08:12.567 | DEBUG    | core.auth.api_service:init_software:55 - 发送初始化请求: https://server.xile188.com/api/user/1000/windowns10/1.2.7/ini, params={}
2025-07-21 12:08:13.107 | DEBUG    | core.auth.api_service:init_software:66 - 初始化数据解密成功: {'new_content': '- 新增功能\n  - 1.新增图片上传功能\n  - 2.新增软件文档\n  - 3.新增软件自动更新，后续更新会自动检测版本更新，无需再下载更新包\n  - 4.新增设置账户信息及发信控制\n- 修复bug\n  - 删除监听任务时，不删除对应任务采集到的数据。\n  - 监听设置群无法发送到目标群\n  - 导出监听数据时应当导出单个任务而非所有数据\n- 优化记录\n  - 优化发信规则，直接调取符合要求的账户发送，无账户时才需要等待延时。\n  - 完善软件设置', 'new_url': None, 'notice': {'id': '2', 'content': '公告内容测试3', 'time': '**********'}}
2025-07-21 12:08:13.118 | INFO     | app.controllers.auth_controller:login:61 - 控制器: 用户登录 account=demo01
2025-07-21 12:08:16.155 | INFO     | core.auth.api_service:login:127 - 用户登录: account=demo01
2025-07-21 12:08:16.403 | INFO     | core.auth.api_service:get_user_info:347 - 获取用户信息
2025-07-21 12:08:16.594 | INFO     | core.auth.api_service:get_user_info:375 - 获取用户信息成功
2025-07-21 12:08:16.602 | INFO     | __main__:on_auth_completed:91 - 用户登录成功，正在启动主窗口...
2025-07-21 12:08:16.635 | INFO     | core.telegram.client_worker:__init__:61 - Telegram客户端工作线程初始化
2025-07-21 12:08:16.635 | INFO     | utils.vip_checker:initialize:25 - VIP检查器已初始化，等待登录后开始检查
2025-07-21 12:08:16.635 | INFO     | ui.main_window:_initialize_core_components:108 - MainWindow: 初始化核心组件...
2025-07-21 12:08:16.638 | INFO     | core.telegram.client_worker:run:65 - Telegram客户端工作线程开始运行
2025-07-21 12:08:16.639 | INFO     | core.proxy.proxy_task_manager:run:284 - 代理任务管理器线程启动
2025-07-21 12:08:16.639 | INFO     | app.services.account_service:__init__:44 - 账户服务初始化
2025-07-21 12:08:16.640 | INFO     | app.controllers.account_controller:__init__:82 - 账户控制器初始化
2025-07-21 12:08:16.640 | INFO     | app.services.monitor_service:__init__:39 - 初始化监控任务服务
2025-07-21 12:08:16.640 | INFO     | core.monitor.filter:__init__:26 - 初始化消息过滤器
2025-07-21 12:08:16.640 | INFO     | core.monitor.extractor:__init__:24 - 初始化关键词提取器
2025-07-21 12:08:16.641 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-07-21 12:08:16.641 | INFO     | core.monitor.notifier:__init__:90 - 初始化通知管理器
2025-07-21 12:08:16.641 | INFO     | app.controllers.telegram_monitor_controller:__init__:64 - 初始化 Telegram 监控控制器
2025-07-21 12:08:16.641 | DEBUG    | app.controllers.telegram_monitor_controller:__init__:82 - 控制器初始化完成
2025-07-21 12:08:16.645 | INFO     | core.telegram.client_manager:__init__:66 - Telegram客户端管理器初始化
2025-07-21 12:08:16.645 | INFO     | core.telegram.user_manager:__init__:30 - Telegram用户管理器初始化
2025-07-21 12:08:16.645 | INFO     | core.telegram.group_manager:__init__:36 - Telegram群组管理器初始化
2025-07-21 12:08:16.645 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-07-21 12:08:16.645 | INFO     | core.telegram.monitor_manager:__init__:26 - Telegram监控管理器初始化
2025-07-21 12:08:16.646 | INFO     | core.telegram.telegram_service:__init__:39 - Telegram服务初始化完成
2025-07-21 12:08:16.863 | INFO     | ui.views.monitor_view:__init__:48 - 初始化监控视图
2025-07-21 12:08:16.863 | DEBUG    | ui.views.monitor_view:_connect_signals:80 - 连接控制器信号
2025-07-21 12:08:17.053 | INFO     | ui.views.proxy_view:__init__:96 - 初始化视图，加载第一页代理数据
2025-07-21 12:08:17.345 | INFO     | __main__:on_auth_completed:95 - 主窗口已启动
2025-07-21 12:08:17.399 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:88 - 尝试连接监听日志信号...
2025-07-21 12:08:17.399 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:90 - 监听日志信号连接成功
2025-07-21 12:08:17.400 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 12:08:17.404 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 12:08:17.408 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:145 - 加载监控任务列表 (搜索关键词: '')
2025-07-21 12:08:17.408 | DEBUG    | ui.views.monitor_view:_on_loading_started:620 - 加载中: 正在加载任务列表...
2025-07-21 12:08:17.408 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 12:08:17.414 | INFO     | app.services.proxy_service:refresh_proxies:712 - 刷新代理列表: offset=0, limit=20
2025-07-21 12:08:17.414 | INFO     | core.proxy.proxy_task_manager:submit_task:172 - 提交耗时任务到子线程: refresh_proxies_0_20
2025-07-21 12:08:17.414 | DEBUG    | app.services.proxy_service:get_proxies:500 - 获取代理列表: offset=0, limit=20
2025-07-21 12:08:17.415 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 12:08:17.415 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 12:08:17.418 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 12:08:17.440 | INFO     | app.controllers.invite_controller:get_invite_tasks:38 - 获取邀请任务列表, status=None
2025-07-21 12:08:17.440 | INFO     | app.services.invite_service:get_invite_tasks:121 - 获取邀请任务列表, status=None
2025-07-21 12:08:17.440 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 12:08:17.443 | INFO     | app.services.invite_service:task_initialize:39 - 初始化邀请服务，重置运行中的任务为暂停状态
2025-07-21 12:08:17.443 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 12:08:17.444 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 12:08:17.445 | INFO     | core.proxy.proxy_task_manager:task_wrapper:199 - 任务 refresh_proxies_0_20 完成
2025-07-21 12:08:17.446 | INFO     | utils.vip_checker:start_checking:37 - 开始VIP状态检查
2025-07-21 12:08:17.446 | DEBUG    | utils.vip_checker:_check_vip_status:61 - 正在检查VIP状态...
2025-07-21 12:08:17.446 | INFO     | core.auth.api_service:verify_vip:425 - 会员验证接口调用
2025-07-21 12:08:17.569 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 12:08:17.575 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 12:08:17.580 | DEBUG    | ui.views.monitor_view:_refresh_task_list:175 - 刷新任务列表，共0个任务
2025-07-21 12:08:17.580 | INFO     | ui.views.monitor_view:_refresh_task_list:189 - 没有找到监控任务
2025-07-21 12:08:17.581 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 12:08:17.582 | INFO     | app.services.invite_service:task_initialize:48 - 成功重置 0 个任务状态
2025-07-21 12:08:17.582 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 12:08:17.584 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 12:08:17.587 | INFO     | app.services.account_service:get_all_groups:98 - 获取所有账户分组成功, 共 2 个
2025-07-21 12:08:17.588 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 12:08:17.588 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 12:08:17.589 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 12:08:17.593 | DEBUG    | ui.views.account_view:_on_groups_loaded:520 - 分组加载完成: 2个分组
2025-07-21 12:08:17.609 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 12:08:17.739 | INFO     | app.services.account_service:get_all_accounts:292 - 获取所有账户成功, 共 2 个
2025-07-21 12:08:17.739 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 12:08:17.740 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 12:08:17.741 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 12:08:17.743 | INFO     | app.controllers.telegram_monitor_controller:_load_total_users_count_internal:191 - 成功加载总体统计数据: 总用户数 23, 今日采集 0, 日均采集 23, 运行天数 1
2025-07-21 12:08:17.743 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:156 - 成功加载 0 个任务
2025-07-21 12:08:17.743 | DEBUG    | ui.views.monitor_view:_on_loading_finished:625 - 加载完成
2025-07-21 12:08:17.744 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 12:08:17.745 | DEBUG    | ui.views.account_view:_on_accounts_loaded:653 - 账户加载完成: 2个账户
2025-07-21 12:08:17.764 | DEBUG    | ui.views.account_view:_update_group_counts:189 - 分组计数已更新: {2: 2}
2025-07-21 12:08:17.768 | INFO     | ui.views.account_view:_auto_login_accounts:708 - 开始调用控制器进行 2 个账户的自动登录。
2025-07-21 12:08:17.768 | INFO     | app.services.account_service:batch_auto_login:1187 - 服务层：开始批量自动登录 2 个账户, 最大并发: 5
2025-07-21 12:08:17.768 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 12:08:17.772 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-07-21 12:08:17.790 | INFO     | app.services.account_service:get_all_accounts:292 - 获取所有账户成功, 共 2 个
2025-07-21 12:08:17.791 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 12:08:17.793 | INFO     | app.services.account_service:batch_auto_login:1226 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-07-21 12:08:17.793 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 12:08:17.794 | INFO     | core.auth.api_service:verify_vip:438 - 会员验证成功
2025-07-21 12:08:17.794 | INFO     | utils.vip_checker:_check_vip_status:76 - VIP状态有效
2025-07-21 12:08:17.796 | DEBUG    | ui.views.account_view:_update_group_counts:189 - 分组计数已更新: {2: 2}
2025-07-21 12:08:17.796 | DEBUG    | ui.views.account_view:_init_cache:156 - 账户缓存初始化完成，共 2 个账户
2025-07-21 12:08:17.801 | DEBUG    | ui.views.account_view:_on_accounts_loaded:653 - 账户加载完成: 2个账户
2025-07-21 12:08:17.822 | DEBUG    | ui.views.account_view:_update_group_counts:189 - 分组计数已更新: {2: 2}
2025-07-21 12:08:17.828 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 12:08:17.840 | INFO     | app.services.account_service:batch_auto_login:1226 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-07-21 12:08:17.840 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 12:08:17.842 | INFO     | app.services.account_service:batch_auto_login:1296 - 服务层：准备调用核心层处理 2 个账户的自动登录。
2025-07-21 12:08:17.842 | INFO     | app.services.account_service:batch_auto_login:1306 - 服务层：设置核心层任务超时为 120 秒。
2025-07-21 12:08:17.843 | INFO     | core.telegram.client_manager:batch_auto_login:1230 - 开始批量自动登录, 共 2 个账户, 最大并发 5
2025-07-21 12:08:17.843 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:271 - 创建客户端: H:\PyProject\TeleTest\APPDATA\sessions\+***********, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-21 12:08:17.843 | DEBUG    | core.telegram.client_manager:get_proxy:96 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-21 12:08:17.843 | DEBUG    | core.telegram.client_manager:get_proxy:143 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-07-21 12:08:17.848 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:271 - 创建客户端: H:\PyProject\TeleTest\APPDATA\sessions\+***********, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-21 12:08:17.848 | DEBUG    | core.telegram.client_manager:get_proxy:96 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-21 12:08:17.849 | DEBUG    | core.telegram.client_manager:get_proxy:143 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-07-21 12:08:17.876 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 12:08:17.951 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 12:08:17.984 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 12:08:18.010 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-07-21 12:08:18.012 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-07-21 12:08:18.013 | INFO     | ui.views.proxy_view:_load_initial_data:1044 - 初始数据加载完成，共 1 个代理
2025-07-21 12:08:21.127 | INFO     | core.telegram.client_manager:_connect_client:219 - 连接成功!
2025-07-21 12:08:21.942 | INFO     | core.telegram.client_manager:_verify_client_authorization:1205 - 账户已授权: +***********
2025-07-21 12:08:22.108 | INFO     | core.telegram.client_manager:_connect_client:219 - 连接成功!
2025-07-21 12:08:22.979 | INFO     | core.telegram.client_manager:_verify_client_authorization:1205 - 账户已授权: +***********
2025-07-21 12:08:24.979 | INFO     | core.telegram.client_manager:batch_auto_login:1379 - 批量登录完成: 总计 2, 成功 2, 失败 0
2025-07-21 12:08:25.870 | INFO     | app.services.account_service:batch_auto_login:1327 - 服务层：核心层登录完成: 成功 2, 失败 0 / 2
2025-07-21 12:08:25.870 | INFO     | app.services.account_service:_process_batch_login_results:1689 - 开始处理批量登录结果，共 2 个账户
2025-07-21 12:08:25.870 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 12:08:25.876 | INFO     | data.repositories.account_repo:update_account:394 - 更新账户成功: 1
2025-07-21 12:08:25.876 | INFO     | app.services.account_service:_process_batch_login_results:1725 - 已更新账户 +*********** 的用户信息
2025-07-21 12:08:25.883 | INFO     | data.repositories.account_repo:update_account:394 - 更新账户成功: 2
2025-07-21 12:08:25.883 | INFO     | app.services.account_service:_process_batch_login_results:1725 - 已更新账户 +*********** 的用户信息
2025-07-21 12:08:25.890 | INFO     | app.services.account_service:_process_batch_login_results:1748 - 批量登录结果处理完成，数据库已更新
2025-07-21 12:08:25.891 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 12:08:25.891 | INFO     | app.controllers.account_controller:auto_login_accounts:640 - 批量登录完成，触发账户数据刷新
2025-07-21 12:08:25.895 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 12:08:25.900 | INFO     | app.services.account_service:get_all_accounts:292 - 获取所有账户成功, 共 2 个
2025-07-21 12:08:25.901 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 12:08:25.902 | DEBUG    | ui.views.account_view:_on_accounts_loaded:653 - 账户加载完成: 2个账户
2025-07-21 12:08:25.922 | DEBUG    | ui.views.account_view:_update_group_counts:189 - 分组计数已更新: {2: 2}
2025-07-21 12:08:26.605 | INFO     | app.services.account_service:get_proxy_ips:1066 - 获取有效代理IP列表（包含绑定计数）
2025-07-21 12:08:26.605 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 12:08:26.816 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 12:08:26.817 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 12:08:26.822 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 12:08:26.822 | INFO     | app.services.account_service:get_proxy_ips:1118 - 成功获取 1 个有效代理（已附带绑定计数）
2025-07-21 12:08:26.822 | INFO     | app.controllers.account_controller:get_proxy_ips:567 - 获取到1个有效代理IP
2025-07-21 12:09:16.586 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-21 12:10:16.586 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-21 12:52:04.241 | INFO     | utils.logger:init_logger:98 - 日志系统初始化完成，默认级别: DEBUG，日志目录: H:\PyProject\TeleTest\APPDATA\logs
2025-07-21 12:52:05.370 | INFO     | data.database:init_database:31 - 初始化数据库连接: H:\PyProject\TeleTest\APPDATA\database\telegram_manager.db
2025-07-21 12:52:05.386 | DEBUG    | data.database:init_database:58 - 创建数据库表
2025-07-21 12:52:05.399 | INFO     | data.database:init_database:61 - 数据库初始化完成
2025-07-21 12:52:06.226 | DEBUG    | app.controllers.auth_controller:initialize_software:43 - 控制器: 初始化软件 app_id=1000, version=1.2.7
2025-07-21 12:52:06.226 | DEBUG    | core.auth.api_service:init_software:55 - 发送初始化请求: https://server.xile188.com/api/user/1000/windowns10/1.2.7/ini, params={}
2025-07-21 12:52:06.715 | DEBUG    | core.auth.api_service:init_software:66 - 初始化数据解密成功: {'new_content': '- 新增功能\n  - 1.新增图片上传功能\n  - 2.新增软件文档\n  - 3.新增软件自动更新，后续更新会自动检测版本更新，无需再下载更新包\n  - 4.新增设置账户信息及发信控制\n- 修复bug\n  - 删除监听任务时，不删除对应任务采集到的数据。\n  - 监听设置群无法发送到目标群\n  - 导出监听数据时应当导出单个任务而非所有数据\n- 优化记录\n  - 优化发信规则，直接调取符合要求的账户发送，无账户时才需要等待延时。\n  - 完善软件设置', 'new_url': None, 'notice': {'id': '2', 'content': '公告内容测试3', 'time': '**********'}}
2025-07-21 12:52:06.721 | INFO     | app.controllers.auth_controller:login:61 - 控制器: 用户登录 account=demo01
2025-07-21 12:52:09.696 | INFO     | core.auth.api_service:login:127 - 用户登录: account=demo01
2025-07-21 12:52:10.033 | INFO     | core.auth.api_service:get_user_info:347 - 获取用户信息
2025-07-21 12:52:10.375 | INFO     | core.auth.api_service:get_user_info:375 - 获取用户信息成功
2025-07-21 12:52:10.383 | INFO     | __main__:on_auth_completed:91 - 用户登录成功，正在启动主窗口...
2025-07-21 12:52:10.416 | INFO     | core.telegram.client_worker:__init__:61 - Telegram客户端工作线程初始化
2025-07-21 12:52:10.416 | INFO     | utils.vip_checker:initialize:25 - VIP检查器已初始化，等待登录后开始检查
2025-07-21 12:52:10.416 | INFO     | ui.main_window:_initialize_core_components:108 - MainWindow: 初始化核心组件...
2025-07-21 12:52:10.419 | INFO     | core.telegram.client_worker:run:65 - Telegram客户端工作线程开始运行
2025-07-21 12:52:10.419 | INFO     | core.proxy.proxy_task_manager:run:284 - 代理任务管理器线程启动
2025-07-21 12:52:10.420 | INFO     | app.services.account_service:__init__:44 - 账户服务初始化
2025-07-21 12:52:10.421 | INFO     | app.controllers.account_controller:__init__:82 - 账户控制器初始化
2025-07-21 12:52:10.422 | INFO     | app.services.monitor_service:__init__:39 - 初始化监控任务服务
2025-07-21 12:52:10.422 | INFO     | core.monitor.filter:__init__:26 - 初始化消息过滤器
2025-07-21 12:52:10.422 | INFO     | core.monitor.extractor:__init__:24 - 初始化关键词提取器
2025-07-21 12:52:10.423 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-07-21 12:52:10.423 | INFO     | core.monitor.notifier:__init__:90 - 初始化通知管理器
2025-07-21 12:52:10.423 | INFO     | app.controllers.telegram_monitor_controller:__init__:64 - 初始化 Telegram 监控控制器
2025-07-21 12:52:10.423 | INFO     | core.telegram.client_manager:__init__:66 - Telegram客户端管理器初始化
2025-07-21 12:52:10.423 | DEBUG    | app.controllers.telegram_monitor_controller:__init__:82 - 控制器初始化完成
2025-07-21 12:52:10.423 | INFO     | core.telegram.user_manager:__init__:30 - Telegram用户管理器初始化
2025-07-21 12:52:10.426 | INFO     | core.telegram.group_manager:__init__:36 - Telegram群组管理器初始化
2025-07-21 12:52:10.437 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-07-21 12:52:10.437 | INFO     | core.telegram.monitor_manager:__init__:26 - Telegram监控管理器初始化
2025-07-21 12:52:10.438 | INFO     | core.telegram.telegram_service:__init__:39 - Telegram服务初始化完成
2025-07-21 12:52:10.721 | INFO     | ui.views.monitor_view:__init__:48 - 初始化监控视图
2025-07-21 12:52:10.721 | DEBUG    | ui.views.monitor_view:_connect_signals:80 - 连接控制器信号
2025-07-21 12:52:10.963 | INFO     | ui.views.proxy_view:__init__:96 - 初始化视图，加载第一页代理数据
2025-07-21 12:52:11.719 | INFO     | __main__:on_auth_completed:95 - 主窗口已启动
2025-07-21 12:52:11.791 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:88 - 尝试连接监听日志信号...
2025-07-21 12:52:11.792 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:90 - 监听日志信号连接成功
2025-07-21 12:52:11.792 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 12:52:11.797 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 12:52:11.803 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:145 - 加载监控任务列表 (搜索关键词: '')
2025-07-21 12:52:11.804 | DEBUG    | ui.views.monitor_view:_on_loading_started:620 - 加载中: 正在加载任务列表...
2025-07-21 12:52:11.804 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 12:52:11.811 | INFO     | app.services.proxy_service:refresh_proxies:712 - 刷新代理列表: offset=0, limit=20
2025-07-21 12:52:11.811 | INFO     | core.proxy.proxy_task_manager:submit_task:172 - 提交耗时任务到子线程: refresh_proxies_0_20
2025-07-21 12:52:11.811 | DEBUG    | app.services.proxy_service:get_proxies:500 - 获取代理列表: offset=0, limit=20
2025-07-21 12:52:11.811 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 12:52:11.811 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 12:52:11.850 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 12:52:11.882 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 12:52:11.883 | INFO     | core.proxy.proxy_task_manager:task_wrapper:199 - 任务 refresh_proxies_0_20 完成
2025-07-21 12:52:11.902 | INFO     | app.controllers.invite_controller:get_invite_tasks:38 - 获取邀请任务列表, status=None
2025-07-21 12:52:11.902 | INFO     | app.services.invite_service:get_invite_tasks:121 - 获取邀请任务列表, status=None
2025-07-21 12:52:11.902 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 12:52:11.908 | INFO     | app.services.invite_service:task_initialize:39 - 初始化邀请服务，重置运行中的任务为暂停状态
2025-07-21 12:52:11.908 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 12:52:11.912 | INFO     | utils.vip_checker:start_checking:37 - 开始VIP状态检查
2025-07-21 12:52:11.912 | DEBUG    | utils.vip_checker:_check_vip_status:61 - 正在检查VIP状态...
2025-07-21 12:52:11.913 | INFO     | core.auth.api_service:verify_vip:425 - 会员验证接口调用
2025-07-21 12:52:12.058 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 12:52:12.060 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 12:52:12.068 | DEBUG    | ui.views.monitor_view:_refresh_task_list:175 - 刷新任务列表，共0个任务
2025-07-21 12:52:12.070 | INFO     | ui.views.monitor_view:_refresh_task_list:189 - 没有找到监控任务
2025-07-21 12:52:12.070 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 12:52:12.078 | INFO     | app.services.account_service:get_all_groups:98 - 获取所有账户分组成功, 共 2 个
2025-07-21 12:52:12.079 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 12:52:12.080 | INFO     | app.services.invite_service:task_initialize:48 - 成功重置 0 个任务状态
2025-07-21 12:52:12.081 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 12:52:12.082 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 12:52:12.083 | DEBUG    | ui.views.account_view:_on_groups_loaded:520 - 分组加载完成: 2个分组
2025-07-21 12:52:12.108 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 12:52:12.121 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 12:52:12.123 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 12:52:12.219 | INFO     | app.services.account_service:get_all_accounts:292 - 获取所有账户成功, 共 2 个
2025-07-21 12:52:12.220 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 12:52:12.328 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 12:52:12.329 | DEBUG    | ui.views.account_view:_on_accounts_loaded:653 - 账户加载完成: 2个账户
2025-07-21 12:52:12.358 | DEBUG    | ui.views.account_view:_update_group_counts:189 - 分组计数已更新: {2: 2}
2025-07-21 12:52:12.367 | INFO     | ui.views.account_view:_auto_login_accounts:708 - 开始调用控制器进行 2 个账户的自动登录。
2025-07-21 12:52:12.367 | INFO     | app.services.account_service:batch_auto_login:1207 - 服务层：开始批量自动登录 2 个账户, 最大并发: 5
2025-07-21 12:52:12.367 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 12:52:12.370 | INFO     | core.auth.api_service:verify_vip:438 - 会员验证成功
2025-07-21 12:52:12.370 | INFO     | utils.vip_checker:_check_vip_status:76 - VIP状态有效
2025-07-21 12:52:12.371 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 12:52:12.375 | INFO     | app.controllers.telegram_monitor_controller:_load_total_users_count_internal:191 - 成功加载总体统计数据: 总用户数 23, 今日采集 0, 日均采集 23, 运行天数 1
2025-07-21 12:52:12.376 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:156 - 成功加载 0 个任务
2025-07-21 12:52:12.376 | DEBUG    | ui.views.monitor_view:_on_loading_finished:625 - 加载完成
2025-07-21 12:52:12.377 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 12:52:12.379 | INFO     | app.services.account_service:batch_auto_login:1246 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-07-21 12:52:12.379 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 12:52:12.380 | INFO     | app.services.account_service:get_all_accounts:292 - 获取所有账户成功, 共 2 个
2025-07-21 12:52:12.380 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 12:52:12.382 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-07-21 12:52:12.397 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 12:52:12.402 | DEBUG    | ui.views.account_view:_update_group_counts:189 - 分组计数已更新: {2: 2}
2025-07-21 12:52:12.402 | DEBUG    | ui.views.account_view:_init_cache:156 - 账户缓存初始化完成，共 2 个账户
2025-07-21 12:52:12.409 | DEBUG    | ui.views.account_view:_on_accounts_loaded:653 - 账户加载完成: 2个账户
2025-07-21 12:52:12.446 | DEBUG    | ui.views.account_view:_update_group_counts:189 - 分组计数已更新: {2: 2}
2025-07-21 12:52:12.463 | INFO     | app.services.account_service:batch_auto_login:1246 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-07-21 12:52:12.463 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 12:52:12.510 | INFO     | app.services.account_service:batch_auto_login:1316 - 服务层：准备调用核心层处理 2 个账户的自动登录。
2025-07-21 12:52:12.511 | INFO     | app.services.account_service:batch_auto_login:1326 - 服务层：设置核心层任务超时为 120 秒。
2025-07-21 12:52:12.511 | INFO     | core.telegram.client_manager:batch_auto_login:1230 - 开始批量自动登录, 共 2 个账户, 最大并发 5
2025-07-21 12:52:12.511 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:271 - 创建客户端: H:\PyProject\TeleTest\APPDATA\sessions\+***********, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-21 12:52:12.511 | DEBUG    | core.telegram.client_manager:get_proxy:96 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-21 12:52:12.512 | DEBUG    | core.telegram.client_manager:get_proxy:143 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-07-21 12:52:12.516 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:271 - 创建客户端: H:\PyProject\TeleTest\APPDATA\sessions\+***********, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-21 12:52:12.517 | DEBUG    | core.telegram.client_manager:get_proxy:96 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-21 12:52:12.517 | DEBUG    | core.telegram.client_manager:get_proxy:143 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-07-21 12:52:12.767 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 12:52:12.853 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 12:52:12.871 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-07-21 12:52:12.873 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-07-21 12:52:12.875 | INFO     | ui.views.proxy_view:_load_initial_data:1044 - 初始数据加载完成，共 1 个代理
2025-07-21 12:52:12.896 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 12:52:17.025 | INFO     | core.telegram.client_manager:_connect_client:219 - 连接成功!
2025-07-21 12:52:17.091 | INFO     | core.telegram.client_manager:_connect_client:219 - 连接成功!
2025-07-21 12:52:17.872 | INFO     | core.telegram.client_manager:_verify_client_authorization:1205 - 账户已授权: +***********
2025-07-21 12:52:17.906 | INFO     | core.telegram.client_manager:_verify_client_authorization:1205 - 账户已授权: +***********
2025-07-21 12:52:19.927 | INFO     | core.telegram.client_manager:batch_auto_login:1379 - 批量登录完成: 总计 2, 成功 2, 失败 0
2025-07-21 12:52:20.727 | INFO     | app.services.account_service:batch_auto_login:1347 - 服务层：核心层登录完成: 成功 2, 失败 0 / 2
2025-07-21 12:52:20.728 | INFO     | app.services.account_service:_process_batch_login_results:1709 - 开始处理批量登录结果，共 2 个账户
2025-07-21 12:52:20.728 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 12:52:20.733 | INFO     | data.repositories.account_repo:update_account:394 - 更新账户成功: 2
2025-07-21 12:52:20.734 | INFO     | app.services.account_service:_process_batch_login_results:1745 - 已更新账户 +*********** 的用户信息
2025-07-21 12:52:20.740 | INFO     | data.repositories.account_repo:update_account:394 - 更新账户成功: 1
2025-07-21 12:52:20.740 | INFO     | app.services.account_service:_process_batch_login_results:1745 - 已更新账户 +*********** 的用户信息
2025-07-21 12:52:20.748 | INFO     | app.services.account_service:_process_batch_login_results:1768 - 批量登录结果处理完成，数据库已更新
2025-07-21 12:52:20.748 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 12:52:20.748 | INFO     | app.controllers.account_controller:auto_login_accounts:640 - 批量登录完成，触发账户数据刷新
2025-07-21 12:52:20.768 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 12:52:20.791 | INFO     | app.services.account_service:get_all_accounts:292 - 获取所有账户成功, 共 2 个
2025-07-21 12:52:20.791 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 12:52:20.794 | DEBUG    | ui.views.account_view:_on_accounts_loaded:653 - 账户加载完成: 2个账户
2025-07-21 12:52:20.813 | DEBUG    | ui.views.account_view:_update_group_counts:189 - 分组计数已更新: {2: 2}
2025-07-21 13:56:55.850 | INFO     | utils.logger:init_logger:98 - 日志系统初始化完成，默认级别: DEBUG，日志目录: H:\PyProject\TeleTest\APPDATA\logs
2025-07-21 13:56:57.943 | INFO     | data.database:init_database:31 - 初始化数据库连接: H:\PyProject\TeleTest\APPDATA\database\telegram_manager.db
2025-07-21 13:56:57.973 | DEBUG    | data.database:init_database:58 - 创建数据库表
2025-07-21 13:56:57.987 | INFO     | data.database:init_database:61 - 数据库初始化完成
2025-07-21 13:56:58.917 | DEBUG    | app.controllers.auth_controller:initialize_software:43 - 控制器: 初始化软件 app_id=1000, version=1.2.7
2025-07-21 13:56:58.917 | DEBUG    | core.auth.api_service:init_software:55 - 发送初始化请求: https://server.xile188.com/api/user/1000/windowns10/1.2.7/ini, params={}
2025-07-21 13:56:59.394 | DEBUG    | core.auth.api_service:init_software:66 - 初始化数据解密成功: {'new_content': '- 新增功能\n  - 1.新增图片上传功能\n  - 2.新增软件文档\n  - 3.新增软件自动更新，后续更新会自动检测版本更新，无需再下载更新包\n  - 4.新增设置账户信息及发信控制\n- 修复bug\n  - 删除监听任务时，不删除对应任务采集到的数据。\n  - 监听设置群无法发送到目标群\n  - 导出监听数据时应当导出单个任务而非所有数据\n- 优化记录\n  - 优化发信规则，直接调取符合要求的账户发送，无账户时才需要等待延时。\n  - 完善软件设置', 'new_url': None, 'notice': {'id': '2', 'content': '公告内容测试3', 'time': '**********'}}
2025-07-21 13:56:59.418 | INFO     | app.controllers.auth_controller:login:61 - 控制器: 用户登录 account=demo01
2025-07-21 13:57:03.024 | INFO     | core.auth.api_service:login:127 - 用户登录: account=demo01
2025-07-21 13:57:03.283 | INFO     | core.auth.api_service:get_user_info:347 - 获取用户信息
2025-07-21 13:57:03.532 | INFO     | core.auth.api_service:get_user_info:375 - 获取用户信息成功
2025-07-21 13:57:03.542 | INFO     | __main__:on_auth_completed:91 - 用户登录成功，正在启动主窗口...
2025-07-21 13:57:03.581 | INFO     | core.telegram.client_worker:__init__:61 - Telegram客户端工作线程初始化
2025-07-21 13:57:03.581 | INFO     | utils.vip_checker:initialize:25 - VIP检查器已初始化，等待登录后开始检查
2025-07-21 13:57:03.581 | INFO     | ui.main_window:_initialize_core_components:108 - MainWindow: 初始化核心组件...
2025-07-21 13:57:03.582 | INFO     | core.telegram.client_worker:run:65 - Telegram客户端工作线程开始运行
2025-07-21 13:57:03.583 | INFO     | core.proxy.proxy_task_manager:run:284 - 代理任务管理器线程启动
2025-07-21 13:57:03.583 | INFO     | app.services.account_service:__init__:44 - 账户服务初始化
2025-07-21 13:57:03.583 | INFO     | app.controllers.account_controller:__init__:82 - 账户控制器初始化
2025-07-21 13:57:03.584 | INFO     | app.services.monitor_service:__init__:39 - 初始化监控任务服务
2025-07-21 13:57:03.584 | INFO     | core.monitor.filter:__init__:26 - 初始化消息过滤器
2025-07-21 13:57:03.584 | INFO     | core.monitor.extractor:__init__:24 - 初始化关键词提取器
2025-07-21 13:57:03.585 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-07-21 13:57:03.585 | INFO     | core.monitor.notifier:__init__:90 - 初始化通知管理器
2025-07-21 13:57:03.585 | INFO     | app.controllers.telegram_monitor_controller:__init__:64 - 初始化 Telegram 监控控制器
2025-07-21 13:57:03.585 | DEBUG    | app.controllers.telegram_monitor_controller:__init__:82 - 控制器初始化完成
2025-07-21 13:57:03.585 | INFO     | core.telegram.client_manager:__init__:66 - Telegram客户端管理器初始化
2025-07-21 13:57:03.587 | INFO     | core.telegram.user_manager:__init__:30 - Telegram用户管理器初始化
2025-07-21 13:57:03.588 | INFO     | core.telegram.group_manager:__init__:36 - Telegram群组管理器初始化
2025-07-21 13:57:03.588 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-07-21 13:57:03.588 | INFO     | core.telegram.monitor_manager:__init__:26 - Telegram监控管理器初始化
2025-07-21 13:57:03.588 | INFO     | core.telegram.telegram_service:__init__:39 - Telegram服务初始化完成
2025-07-21 13:57:03.905 | INFO     | ui.views.monitor_view:__init__:48 - 初始化监控视图
2025-07-21 13:57:03.905 | DEBUG    | ui.views.monitor_view:_connect_signals:80 - 连接控制器信号
2025-07-21 13:57:04.105 | INFO     | ui.views.proxy_view:__init__:96 - 初始化视图，加载第一页代理数据
2025-07-21 13:57:04.432 | INFO     | __main__:on_auth_completed:95 - 主窗口已启动
2025-07-21 13:57:04.493 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:88 - 尝试连接监听日志信号...
2025-07-21 13:57:04.493 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:90 - 监听日志信号连接成功
2025-07-21 13:57:04.493 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 13:57:04.497 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 13:57:04.502 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:145 - 加载监控任务列表 (搜索关键词: '')
2025-07-21 13:57:04.502 | DEBUG    | ui.views.monitor_view:_on_loading_started:620 - 加载中: 正在加载任务列表...
2025-07-21 13:57:04.502 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 13:57:04.510 | INFO     | app.services.proxy_service:refresh_proxies:712 - 刷新代理列表: offset=0, limit=20
2025-07-21 13:57:04.510 | INFO     | core.proxy.proxy_task_manager:submit_task:172 - 提交耗时任务到子线程: refresh_proxies_0_20
2025-07-21 13:57:04.510 | DEBUG    | app.services.proxy_service:get_proxies:500 - 获取代理列表: offset=0, limit=20
2025-07-21 13:57:04.510 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 13:57:04.511 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 13:57:04.518 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 13:57:04.537 | INFO     | app.controllers.invite_controller:get_invite_tasks:38 - 获取邀请任务列表, status=None
2025-07-21 13:57:04.542 | INFO     | app.services.invite_service:get_invite_tasks:121 - 获取邀请任务列表, status=None
2025-07-21 13:57:04.542 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 13:57:04.543 | INFO     | app.services.invite_service:task_initialize:39 - 初始化邀请服务，重置运行中的任务为暂停状态
2025-07-21 13:57:04.543 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 13:57:04.549 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 13:57:04.550 | INFO     | core.proxy.proxy_task_manager:task_wrapper:199 - 任务 refresh_proxies_0_20 完成
2025-07-21 13:57:04.550 | INFO     | utils.vip_checker:start_checking:37 - 开始VIP状态检查
2025-07-21 13:57:04.551 | DEBUG    | utils.vip_checker:_check_vip_status:61 - 正在检查VIP状态...
2025-07-21 13:57:04.551 | INFO     | core.auth.api_service:verify_vip:425 - 会员验证接口调用
2025-07-21 13:57:04.661 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 13:57:04.665 | DEBUG    | ui.views.monitor_view:_refresh_task_list:175 - 刷新任务列表，共0个任务
2025-07-21 13:57:04.666 | INFO     | ui.views.monitor_view:_refresh_task_list:189 - 没有找到监控任务
2025-07-21 13:57:04.666 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 13:57:04.670 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 13:57:04.676 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 13:57:04.680 | INFO     | app.services.invite_service:task_initialize:48 - 成功重置 0 个任务状态
2025-07-21 13:57:04.680 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 13:57:04.680 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 13:57:04.681 | INFO     | app.services.account_service:get_all_groups:98 - 获取所有账户分组成功, 共 2 个
2025-07-21 13:57:04.682 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 13:57:04.686 | DEBUG    | ui.views.account_view:_on_groups_loaded:519 - 分组加载完成: 2个分组
2025-07-21 13:57:04.703 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 13:57:04.708 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 13:57:04.785 | INFO     | app.services.account_service:get_all_accounts:292 - 获取所有账户成功, 共 2 个
2025-07-21 13:57:04.785 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 13:57:04.785 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 13:57:04.786 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 13:57:04.793 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 13:57:04.794 | DEBUG    | ui.views.account_view:_on_accounts_loaded:652 - 账户加载完成: 2个账户
2025-07-21 13:57:04.818 | DEBUG    | ui.views.account_view:_update_group_counts:188 - 分组计数已更新: {2: 2}
2025-07-21 13:57:04.822 | INFO     | ui.views.account_view:_auto_login_accounts:707 - 开始调用控制器进行 2 个账户的自动登录。
2025-07-21 13:57:04.822 | INFO     | app.services.account_service:batch_auto_login:1207 - 服务层：开始批量自动登录 2 个账户, 最大并发: 5
2025-07-21 13:57:04.822 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 13:57:04.832 | INFO     | app.controllers.telegram_monitor_controller:_load_total_users_count_internal:191 - 成功加载总体统计数据: 总用户数 23, 今日采集 0, 日均采集 23, 运行天数 1
2025-07-21 13:57:04.832 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:156 - 成功加载 0 个任务
2025-07-21 13:57:04.832 | DEBUG    | ui.views.monitor_view:_on_loading_finished:625 - 加载完成
2025-07-21 13:57:04.833 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-07-21 13:57:04.854 | INFO     | app.services.account_service:batch_auto_login:1246 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-07-21 13:57:04.854 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 13:57:04.855 | INFO     | app.services.account_service:get_all_accounts:292 - 获取所有账户成功, 共 2 个
2025-07-21 13:57:04.855 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 13:57:04.857 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 13:57:04.859 | DEBUG    | ui.views.account_view:_update_group_counts:188 - 分组计数已更新: {2: 2}
2025-07-21 13:57:04.859 | DEBUG    | ui.views.account_view:_init_cache:155 - 账户缓存初始化完成，共 2 个账户
2025-07-21 13:57:04.863 | DEBUG    | ui.views.account_view:_on_accounts_loaded:652 - 账户加载完成: 2个账户
2025-07-21 13:57:04.894 | DEBUG    | ui.views.account_view:_update_group_counts:188 - 分组计数已更新: {2: 2}
2025-07-21 13:57:04.916 | INFO     | app.services.account_service:batch_auto_login:1246 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-07-21 13:57:04.917 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 13:57:04.918 | INFO     | core.auth.api_service:verify_vip:438 - 会员验证成功
2025-07-21 13:57:04.918 | INFO     | utils.vip_checker:_check_vip_status:76 - VIP状态有效
2025-07-21 13:57:04.920 | INFO     | app.services.account_service:batch_auto_login:1316 - 服务层：准备调用核心层处理 2 个账户的自动登录。
2025-07-21 13:57:04.920 | INFO     | app.services.account_service:batch_auto_login:1326 - 服务层：设置核心层任务超时为 120 秒。
2025-07-21 13:57:04.920 | INFO     | core.telegram.client_manager:batch_auto_login:1230 - 开始批量自动登录, 共 2 个账户, 最大并发 5
2025-07-21 13:57:04.920 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:271 - 创建客户端: H:\PyProject\TeleTest\APPDATA\sessions\+***********, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-21 13:57:04.921 | DEBUG    | core.telegram.client_manager:get_proxy:96 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-21 13:57:04.921 | DEBUG    | core.telegram.client_manager:get_proxy:143 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-07-21 13:57:04.927 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:271 - 创建客户端: H:\PyProject\TeleTest\APPDATA\sessions\+***********, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-21 13:57:04.928 | DEBUG    | core.telegram.client_manager:get_proxy:96 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-21 13:57:04.928 | DEBUG    | core.telegram.client_manager:get_proxy:143 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-07-21 13:57:04.960 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 13:57:05.057 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 13:57:05.115 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-07-21 13:57:05.118 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-07-21 13:57:05.119 | INFO     | ui.views.proxy_view:_load_initial_data:1044 - 初始数据加载完成，共 1 个代理
2025-07-21 13:57:05.122 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 13:57:08.603 | INFO     | core.telegram.client_manager:_connect_client:219 - 连接成功!
2025-07-21 13:57:08.717 | INFO     | core.telegram.client_manager:_connect_client:219 - 连接成功!
2025-07-21 13:57:09.879 | INFO     | core.telegram.client_manager:_verify_client_authorization:1205 - 账户已授权: +***********
2025-07-21 13:57:11.434 | INFO     | core.telegram.client_manager:_verify_client_authorization:1205 - 账户已授权: +***********
2025-07-21 13:57:13.437 | INFO     | core.telegram.client_manager:batch_auto_login:1379 - 批量登录完成: 总计 2, 成功 2, 失败 0
2025-07-21 13:57:13.965 | INFO     | app.services.account_service:batch_auto_login:1347 - 服务层：核心层登录完成: 成功 2, 失败 0 / 2
2025-07-21 13:57:13.965 | INFO     | app.services.account_service:_process_batch_login_results:1709 - 开始处理批量登录结果，共 2 个账户
2025-07-21 13:57:13.965 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 13:57:13.972 | INFO     | data.repositories.account_repo:update_account:394 - 更新账户成功: 2
2025-07-21 13:57:13.972 | INFO     | app.services.account_service:_process_batch_login_results:1745 - 已更新账户 +*********** 的用户信息
2025-07-21 13:57:13.979 | INFO     | data.repositories.account_repo:update_account:394 - 更新账户成功: 1
2025-07-21 13:57:13.979 | INFO     | app.services.account_service:_process_batch_login_results:1745 - 已更新账户 +*********** 的用户信息
2025-07-21 13:57:13.986 | INFO     | app.services.account_service:_process_batch_login_results:1768 - 批量登录结果处理完成，数据库已更新
2025-07-21 13:57:13.986 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 13:57:13.987 | INFO     | app.controllers.account_controller:auto_login_accounts:640 - 批量登录完成，触发账户数据刷新
2025-07-21 13:58:03.527 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-21 13:59:03.522 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-21 14:00:03.521 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-21 14:01:03.521 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-21 14:02:03.523 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-21 14:03:03.541 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-21 14:04:03.540 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-21 14:05:03.538 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-21 14:06:03.537 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-21 14:07:03.540 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-21 14:08:03.539 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-21 14:08:11.597 | INFO     | utils.logger:init_logger:98 - 日志系统初始化完成，默认级别: DEBUG，日志目录: H:\PyProject\TeleTest\APPDATA\logs
2025-07-21 14:08:13.025 | INFO     | data.database:init_database:31 - 初始化数据库连接: H:\PyProject\TeleTest\APPDATA\database\telegram_manager.db
2025-07-21 14:08:13.043 | DEBUG    | data.database:init_database:58 - 创建数据库表
2025-07-21 14:08:13.054 | INFO     | data.database:init_database:61 - 数据库初始化完成
2025-07-21 14:08:14.193 | DEBUG    | app.controllers.auth_controller:initialize_software:43 - 控制器: 初始化软件 app_id=1000, version=1.2.7
2025-07-21 14:08:14.194 | DEBUG    | core.auth.api_service:init_software:55 - 发送初始化请求: https://server.xile188.com/api/user/1000/windowns10/1.2.7/ini, params={}
2025-07-21 14:08:14.467 | DEBUG    | core.auth.api_service:init_software:66 - 初始化数据解密成功: {'new_content': '- 新增功能\n  - 1.新增图片上传功能\n  - 2.新增软件文档\n  - 3.新增软件自动更新，后续更新会自动检测版本更新，无需再下载更新包\n  - 4.新增设置账户信息及发信控制\n- 修复bug\n  - 删除监听任务时，不删除对应任务采集到的数据。\n  - 监听设置群无法发送到目标群\n  - 导出监听数据时应当导出单个任务而非所有数据\n- 优化记录\n  - 优化发信规则，直接调取符合要求的账户发送，无账户时才需要等待延时。\n  - 完善软件设置', 'new_url': None, 'notice': {'id': '2', 'content': '公告内容测试3', 'time': '**********'}}
2025-07-21 14:08:14.473 | INFO     | app.controllers.auth_controller:login:61 - 控制器: 用户登录 account=demo01
2025-07-21 14:08:17.512 | INFO     | core.auth.api_service:login:127 - 用户登录: account=demo01
2025-07-21 14:08:17.722 | INFO     | core.auth.api_service:get_user_info:347 - 获取用户信息
2025-07-21 14:08:17.963 | INFO     | core.auth.api_service:get_user_info:375 - 获取用户信息成功
2025-07-21 14:08:17.969 | INFO     | __main__:on_auth_completed:91 - 用户登录成功，正在启动主窗口...
2025-07-21 14:08:17.991 | INFO     | core.telegram.client_worker:__init__:61 - Telegram客户端工作线程初始化
2025-07-21 14:08:17.991 | INFO     | utils.vip_checker:initialize:25 - VIP检查器已初始化，等待登录后开始检查
2025-07-21 14:08:17.991 | INFO     | ui.main_window:_initialize_core_components:108 - MainWindow: 初始化核心组件...
2025-07-21 14:08:17.992 | INFO     | app.services.account_service:__init__:44 - 账户服务初始化
2025-07-21 14:08:17.992 | INFO     | core.telegram.client_worker:run:65 - Telegram客户端工作线程开始运行
2025-07-21 14:08:17.993 | INFO     | core.proxy.proxy_task_manager:run:284 - 代理任务管理器线程启动
2025-07-21 14:08:17.993 | INFO     | app.controllers.account_controller:__init__:82 - 账户控制器初始化
2025-07-21 14:08:17.993 | INFO     | app.services.monitor_service:__init__:39 - 初始化监控任务服务
2025-07-21 14:08:17.994 | INFO     | core.monitor.filter:__init__:26 - 初始化消息过滤器
2025-07-21 14:08:17.994 | INFO     | core.monitor.extractor:__init__:24 - 初始化关键词提取器
2025-07-21 14:08:17.994 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-07-21 14:08:17.994 | INFO     | core.monitor.notifier:__init__:90 - 初始化通知管理器
2025-07-21 14:08:17.994 | INFO     | app.controllers.telegram_monitor_controller:__init__:64 - 初始化 Telegram 监控控制器
2025-07-21 14:08:17.994 | DEBUG    | app.controllers.telegram_monitor_controller:__init__:82 - 控制器初始化完成
2025-07-21 14:08:17.996 | INFO     | core.telegram.client_manager:__init__:66 - Telegram客户端管理器初始化
2025-07-21 14:08:17.997 | INFO     | core.telegram.user_manager:__init__:30 - Telegram用户管理器初始化
2025-07-21 14:08:17.997 | INFO     | core.telegram.group_manager:__init__:36 - Telegram群组管理器初始化
2025-07-21 14:08:17.997 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-07-21 14:08:17.998 | INFO     | core.telegram.monitor_manager:__init__:26 - Telegram监控管理器初始化
2025-07-21 14:08:17.998 | INFO     | core.telegram.telegram_service:__init__:39 - Telegram服务初始化完成
2025-07-21 14:08:18.192 | INFO     | ui.views.monitor_view:__init__:48 - 初始化监控视图
2025-07-21 14:08:18.192 | DEBUG    | ui.views.monitor_view:_connect_signals:80 - 连接控制器信号
2025-07-21 14:08:18.377 | INFO     | ui.views.proxy_view:__init__:96 - 初始化视图，加载第一页代理数据
2025-07-21 14:08:18.674 | INFO     | __main__:on_auth_completed:95 - 主窗口已启动
2025-07-21 14:08:18.719 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:88 - 尝试连接监听日志信号...
2025-07-21 14:08:18.719 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:90 - 监听日志信号连接成功
2025-07-21 14:08:18.719 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 14:08:18.725 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 14:08:18.729 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:145 - 加载监控任务列表 (搜索关键词: '')
2025-07-21 14:08:18.729 | DEBUG    | ui.views.monitor_view:_on_loading_started:620 - 加载中: 正在加载任务列表...
2025-07-21 14:08:18.729 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 14:08:18.735 | INFO     | app.services.proxy_service:refresh_proxies:712 - 刷新代理列表: offset=0, limit=20
2025-07-21 14:08:18.735 | INFO     | core.proxy.proxy_task_manager:submit_task:172 - 提交耗时任务到子线程: refresh_proxies_0_20
2025-07-21 14:08:18.735 | DEBUG    | app.services.proxy_service:get_proxies:500 - 获取代理列表: offset=0, limit=20
2025-07-21 14:08:18.735 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 14:08:18.735 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 14:08:18.738 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 14:08:18.740 | INFO     | app.controllers.invite_controller:get_invite_tasks:38 - 获取邀请任务列表, status=None
2025-07-21 14:08:18.740 | INFO     | app.services.invite_service:get_invite_tasks:121 - 获取邀请任务列表, status=None
2025-07-21 14:08:18.741 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 14:08:18.758 | INFO     | app.services.invite_service:task_initialize:39 - 初始化邀请服务，重置运行中的任务为暂停状态
2025-07-21 14:08:18.762 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 14:08:18.767 | INFO     | utils.vip_checker:start_checking:37 - 开始VIP状态检查
2025-07-21 14:08:18.767 | DEBUG    | utils.vip_checker:_check_vip_status:61 - 正在检查VIP状态...
2025-07-21 14:08:18.767 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 14:08:18.767 | INFO     | core.auth.api_service:verify_vip:425 - 会员验证接口调用
2025-07-21 14:08:18.769 | INFO     | core.proxy.proxy_task_manager:task_wrapper:199 - 任务 refresh_proxies_0_20 完成
2025-07-21 14:08:18.885 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 14:08:18.892 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 14:08:18.896 | INFO     | app.services.invite_service:task_initialize:48 - 成功重置 0 个任务状态
2025-07-21 14:08:18.897 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 14:08:18.897 | DEBUG    | ui.views.monitor_view:_refresh_task_list:175 - 刷新任务列表，共0个任务
2025-07-21 14:08:18.898 | INFO     | ui.views.monitor_view:_refresh_task_list:189 - 没有找到监控任务
2025-07-21 14:08:18.898 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 14:08:18.901 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 14:08:18.903 | INFO     | app.services.account_service:get_all_groups:98 - 获取所有账户分组成功, 共 2 个
2025-07-21 14:08:18.903 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 14:08:18.904 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 14:08:18.904 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 14:08:18.957 | DEBUG    | ui.views.account_view:_on_groups_loaded:519 - 分组加载完成: 2个分组
2025-07-21 14:08:18.973 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 14:08:18.994 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 14:08:18.998 | INFO     | app.services.account_service:get_all_accounts:292 - 获取所有账户成功, 共 2 个
2025-07-21 14:08:18.998 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 14:08:18.999 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 14:08:19.000 | INFO     | app.controllers.telegram_monitor_controller:_load_total_users_count_internal:191 - 成功加载总体统计数据: 总用户数 23, 今日采集 0, 日均采集 23, 运行天数 1
2025-07-21 14:08:19.000 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:156 - 成功加载 0 个任务
2025-07-21 14:08:19.000 | DEBUG    | ui.views.monitor_view:_on_loading_finished:625 - 加载完成
2025-07-21 14:08:19.002 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 14:08:19.003 | DEBUG    | ui.views.account_view:_on_accounts_loaded:652 - 账户加载完成: 2个账户
2025-07-21 14:08:19.021 | DEBUG    | ui.views.account_view:_update_group_counts:188 - 分组计数已更新: {2: 2}
2025-07-21 14:08:19.025 | INFO     | ui.views.account_view:_auto_login_accounts:707 - 开始调用控制器进行 2 个账户的自动登录。
2025-07-21 14:08:19.025 | INFO     | app.services.account_service:batch_auto_login:1207 - 服务层：开始批量自动登录 2 个账户, 最大并发: 5
2025-07-21 14:08:19.025 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 14:08:19.029 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-07-21 14:08:19.044 | INFO     | app.services.account_service:batch_auto_login:1246 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-07-21 14:08:19.044 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 14:08:19.046 | INFO     | app.services.account_service:get_all_accounts:292 - 获取所有账户成功, 共 2 个
2025-07-21 14:08:19.046 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 14:08:19.047 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 14:08:19.049 | DEBUG    | ui.views.account_view:_update_group_counts:188 - 分组计数已更新: {2: 2}
2025-07-21 14:08:19.049 | DEBUG    | ui.views.account_view:_init_cache:155 - 账户缓存初始化完成，共 2 个账户
2025-07-21 14:08:19.053 | DEBUG    | ui.views.account_view:_on_accounts_loaded:652 - 账户加载完成: 2个账户
2025-07-21 14:08:19.073 | DEBUG    | ui.views.account_view:_update_group_counts:188 - 分组计数已更新: {2: 2}
2025-07-21 14:08:19.086 | INFO     | app.services.account_service:batch_auto_login:1246 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-07-21 14:08:19.087 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 14:08:19.089 | INFO     | app.services.account_service:batch_auto_login:1316 - 服务层：准备调用核心层处理 2 个账户的自动登录。
2025-07-21 14:08:19.089 | INFO     | app.services.account_service:batch_auto_login:1326 - 服务层：设置核心层任务超时为 120 秒。
2025-07-21 14:08:19.090 | INFO     | core.telegram.client_manager:batch_auto_login:1230 - 开始批量自动登录, 共 2 个账户, 最大并发 5
2025-07-21 14:08:19.090 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:271 - 创建客户端: H:\PyProject\TeleTest\APPDATA\sessions\+***********, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-21 14:08:19.090 | DEBUG    | core.telegram.client_manager:get_proxy:96 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-21 14:08:19.090 | DEBUG    | core.telegram.client_manager:get_proxy:143 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-07-21 14:08:19.096 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:271 - 创建客户端: H:\PyProject\TeleTest\APPDATA\sessions\+***********, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-21 14:08:19.096 | DEBUG    | core.telegram.client_manager:get_proxy:96 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-21 14:08:19.096 | DEBUG    | core.telegram.client_manager:get_proxy:143 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-07-21 14:08:19.128 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 14:08:19.221 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 14:08:19.226 | INFO     | core.auth.api_service:verify_vip:438 - 会员验证成功
2025-07-21 14:08:19.227 | INFO     | utils.vip_checker:_check_vip_status:76 - VIP状态有效
2025-07-21 14:08:19.255 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 14:08:19.271 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-07-21 14:08:19.272 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-07-21 14:08:19.274 | INFO     | ui.views.proxy_view:_load_initial_data:1044 - 初始数据加载完成，共 1 个代理
2025-07-21 14:08:23.567 | INFO     | core.telegram.client_manager:_connect_client:219 - 连接成功!
2025-07-21 14:08:23.683 | INFO     | core.telegram.client_manager:_connect_client:219 - 连接成功!
2025-07-21 14:08:24.771 | INFO     | core.telegram.client_manager:_verify_client_authorization:1205 - 账户已授权: +***********
2025-07-21 14:08:25.589 | INFO     | core.telegram.client_manager:_verify_client_authorization:1205 - 账户已授权: +***********
2025-07-21 14:08:27.613 | INFO     | core.telegram.client_manager:batch_auto_login:1379 - 批量登录完成: 总计 2, 成功 2, 失败 0
2025-07-21 14:08:28.118 | INFO     | app.services.account_service:batch_auto_login:1347 - 服务层：核心层登录完成: 成功 2, 失败 0 / 2
2025-07-21 14:08:28.118 | INFO     | app.services.account_service:_process_batch_login_results:1709 - 开始处理批量登录结果，共 2 个账户
2025-07-21 14:08:28.118 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 14:08:28.123 | INFO     | data.repositories.account_repo:update_account:394 - 更新账户成功: 1
2025-07-21 14:08:28.124 | INFO     | app.services.account_service:_process_batch_login_results:1745 - 已更新账户 +*********** 的用户信息
2025-07-21 14:08:28.129 | INFO     | data.repositories.account_repo:update_account:394 - 更新账户成功: 2
2025-07-21 14:08:28.129 | INFO     | app.services.account_service:_process_batch_login_results:1745 - 已更新账户 +*********** 的用户信息
2025-07-21 14:08:28.136 | INFO     | app.services.account_service:_process_batch_login_results:1768 - 批量登录结果处理完成，数据库已更新
2025-07-21 14:08:28.136 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 14:08:28.136 | INFO     | app.controllers.account_controller:auto_login_accounts:640 - 批量登录完成，触发账户数据刷新
2025-07-21 14:09:17.971 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-21 14:16:44.884 | INFO     | utils.logger:init_logger:98 - 日志系统初始化完成，默认级别: DEBUG，日志目录: H:\PyProject\TeleTest\APPDATA\logs
2025-07-21 14:16:45.867 | INFO     | data.database:init_database:31 - 初始化数据库连接: H:\PyProject\TeleTest\APPDATA\database\telegram_manager.db
2025-07-21 14:16:45.883 | DEBUG    | data.database:init_database:58 - 创建数据库表
2025-07-21 14:16:45.892 | INFO     | data.database:init_database:61 - 数据库初始化完成
2025-07-21 14:16:46.976 | DEBUG    | app.controllers.auth_controller:initialize_software:43 - 控制器: 初始化软件 app_id=1000, version=1.2.7
2025-07-21 14:16:46.976 | DEBUG    | core.auth.api_service:init_software:55 - 发送初始化请求: https://server.xile188.com/api/user/1000/windowns10/1.2.7/ini, params={}
2025-07-21 14:16:47.416 | DEBUG    | core.auth.api_service:init_software:66 - 初始化数据解密成功: {'new_content': '- 新增功能\n  - 1.新增图片上传功能\n  - 2.新增软件文档\n  - 3.新增软件自动更新，后续更新会自动检测版本更新，无需再下载更新包\n  - 4.新增设置账户信息及发信控制\n- 修复bug\n  - 删除监听任务时，不删除对应任务采集到的数据。\n  - 监听设置群无法发送到目标群\n  - 导出监听数据时应当导出单个任务而非所有数据\n- 优化记录\n  - 优化发信规则，直接调取符合要求的账户发送，无账户时才需要等待延时。\n  - 完善软件设置', 'new_url': None, 'notice': {'id': '2', 'content': '公告内容测试3', 'time': '**********'}}
2025-07-21 14:16:47.422 | INFO     | app.controllers.auth_controller:login:61 - 控制器: 用户登录 account=demo01
2025-07-21 14:16:50.445 | INFO     | core.auth.api_service:login:127 - 用户登录: account=demo01
2025-07-21 14:16:50.649 | INFO     | core.auth.api_service:get_user_info:347 - 获取用户信息
2025-07-21 14:16:50.894 | INFO     | core.auth.api_service:get_user_info:375 - 获取用户信息成功
2025-07-21 14:16:50.900 | INFO     | __main__:on_auth_completed:91 - 用户登录成功，正在启动主窗口...
2025-07-21 14:16:50.923 | INFO     | core.telegram.client_worker:__init__:61 - Telegram客户端工作线程初始化
2025-07-21 14:16:50.923 | INFO     | utils.vip_checker:initialize:25 - VIP检查器已初始化，等待登录后开始检查
2025-07-21 14:16:50.923 | INFO     | ui.main_window:_initialize_core_components:108 - MainWindow: 初始化核心组件...
2025-07-21 14:16:50.924 | INFO     | app.services.account_service:__init__:44 - 账户服务初始化
2025-07-21 14:16:50.924 | INFO     | app.controllers.account_controller:__init__:82 - 账户控制器初始化
2025-07-21 14:16:50.924 | INFO     | app.services.monitor_service:__init__:39 - 初始化监控任务服务
2025-07-21 14:16:50.924 | INFO     | core.telegram.client_worker:run:65 - Telegram客户端工作线程开始运行
2025-07-21 14:16:50.924 | INFO     | core.monitor.filter:__init__:26 - 初始化消息过滤器
2025-07-21 14:16:50.925 | INFO     | core.proxy.proxy_task_manager:run:284 - 代理任务管理器线程启动
2025-07-21 14:16:50.925 | INFO     | core.monitor.extractor:__init__:24 - 初始化关键词提取器
2025-07-21 14:16:50.925 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-07-21 14:16:50.925 | INFO     | core.monitor.notifier:__init__:90 - 初始化通知管理器
2025-07-21 14:16:50.925 | INFO     | app.controllers.telegram_monitor_controller:__init__:64 - 初始化 Telegram 监控控制器
2025-07-21 14:16:50.925 | DEBUG    | app.controllers.telegram_monitor_controller:__init__:82 - 控制器初始化完成
2025-07-21 14:16:50.928 | INFO     | core.telegram.client_manager:__init__:66 - Telegram客户端管理器初始化
2025-07-21 14:16:50.929 | INFO     | core.telegram.user_manager:__init__:30 - Telegram用户管理器初始化
2025-07-21 14:16:50.929 | INFO     | core.telegram.group_manager:__init__:36 - Telegram群组管理器初始化
2025-07-21 14:16:50.930 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-07-21 14:16:50.930 | INFO     | core.telegram.monitor_manager:__init__:26 - Telegram监控管理器初始化
2025-07-21 14:16:50.932 | INFO     | core.telegram.telegram_service:__init__:39 - Telegram服务初始化完成
2025-07-21 14:16:51.121 | INFO     | ui.views.monitor_view:__init__:48 - 初始化监控视图
2025-07-21 14:16:51.121 | DEBUG    | ui.views.monitor_view:_connect_signals:80 - 连接控制器信号
2025-07-21 14:16:51.300 | INFO     | ui.views.proxy_view:__init__:96 - 初始化视图，加载第一页代理数据
2025-07-21 14:16:51.537 | INFO     | __main__:on_auth_completed:95 - 主窗口已启动
2025-07-21 14:16:51.583 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:88 - 尝试连接监听日志信号...
2025-07-21 14:16:51.583 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:90 - 监听日志信号连接成功
2025-07-21 14:16:51.584 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 14:16:51.587 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 14:16:51.591 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:145 - 加载监控任务列表 (搜索关键词: '')
2025-07-21 14:16:51.591 | DEBUG    | ui.views.monitor_view:_on_loading_started:620 - 加载中: 正在加载任务列表...
2025-07-21 14:16:51.591 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 14:16:51.597 | INFO     | app.services.proxy_service:refresh_proxies:712 - 刷新代理列表: offset=0, limit=20
2025-07-21 14:16:51.597 | INFO     | core.proxy.proxy_task_manager:submit_task:172 - 提交耗时任务到子线程: refresh_proxies_0_20
2025-07-21 14:16:51.597 | DEBUG    | app.services.proxy_service:get_proxies:500 - 获取代理列表: offset=0, limit=20
2025-07-21 14:16:51.597 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 14:16:51.597 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 14:16:51.600 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 14:16:51.609 | INFO     | app.controllers.invite_controller:get_invite_tasks:38 - 获取邀请任务列表, status=None
2025-07-21 14:16:51.614 | INFO     | app.services.invite_service:get_invite_tasks:121 - 获取邀请任务列表, status=None
2025-07-21 14:16:51.620 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 14:16:51.625 | INFO     | app.services.invite_service:task_initialize:39 - 初始化邀请服务，重置运行中的任务为暂停状态
2025-07-21 14:16:51.626 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 14:16:51.630 | INFO     | utils.vip_checker:start_checking:37 - 开始VIP状态检查
2025-07-21 14:16:51.630 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 14:16:51.630 | DEBUG    | utils.vip_checker:_check_vip_status:61 - 正在检查VIP状态...
2025-07-21 14:16:51.631 | INFO     | core.auth.api_service:verify_vip:425 - 会员验证接口调用
2025-07-21 14:16:51.633 | INFO     | core.proxy.proxy_task_manager:task_wrapper:199 - 任务 refresh_proxies_0_20 完成
2025-07-21 14:16:51.861 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 14:16:51.862 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 14:16:51.864 | INFO     | app.services.invite_service:task_initialize:48 - 成功重置 0 个任务状态
2025-07-21 14:16:51.864 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 14:16:51.864 | DEBUG    | ui.views.monitor_view:_refresh_task_list:175 - 刷新任务列表，共0个任务
2025-07-21 14:16:51.865 | INFO     | ui.views.monitor_view:_refresh_task_list:189 - 没有找到监控任务
2025-07-21 14:16:51.865 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 14:16:51.867 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 14:16:51.869 | INFO     | app.services.account_service:get_all_groups:98 - 获取所有账户分组成功, 共 2 个
2025-07-21 14:16:51.869 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 14:16:51.870 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 14:16:51.871 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 14:16:51.873 | DEBUG    | ui.views.account_view:_on_groups_loaded:519 - 分组加载完成: 2个分组
2025-07-21 14:16:51.885 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 14:16:52.056 | INFO     | app.services.account_service:get_all_accounts:292 - 获取所有账户成功, 共 2 个
2025-07-21 14:16:52.056 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 14:16:52.057 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 14:16:52.059 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 14:16:52.059 | INFO     | core.auth.api_service:verify_vip:438 - 会员验证成功
2025-07-21 14:16:52.059 | INFO     | utils.vip_checker:_check_vip_status:76 - VIP状态有效
2025-07-21 14:16:52.061 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 14:16:52.079 | DEBUG    | ui.views.account_view:_on_accounts_loaded:652 - 账户加载完成: 2个账户
2025-07-21 14:16:52.142 | DEBUG    | ui.views.account_view:_update_group_counts:188 - 分组计数已更新: {2: 2}
2025-07-21 14:16:52.147 | INFO     | ui.views.account_view:_auto_login_accounts:707 - 开始调用控制器进行 2 个账户的自动登录。
2025-07-21 14:16:52.147 | INFO     | app.services.account_service:batch_auto_login:1207 - 服务层：开始批量自动登录 2 个账户, 最大并发: 5
2025-07-21 14:16:52.147 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 14:16:52.154 | INFO     | app.controllers.telegram_monitor_controller:_load_total_users_count_internal:191 - 成功加载总体统计数据: 总用户数 23, 今日采集 0, 日均采集 23, 运行天数 1
2025-07-21 14:16:52.154 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:156 - 成功加载 0 个任务
2025-07-21 14:16:52.154 | DEBUG    | ui.views.monitor_view:_on_loading_finished:625 - 加载完成
2025-07-21 14:16:52.156 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-07-21 14:16:52.178 | INFO     | app.services.account_service:batch_auto_login:1246 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-07-21 14:16:52.178 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 14:16:52.179 | INFO     | app.services.account_service:get_all_accounts:292 - 获取所有账户成功, 共 2 个
2025-07-21 14:16:52.179 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 14:16:52.181 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 14:16:52.183 | DEBUG    | ui.views.account_view:_update_group_counts:188 - 分组计数已更新: {2: 2}
2025-07-21 14:16:52.183 | DEBUG    | ui.views.account_view:_init_cache:155 - 账户缓存初始化完成，共 2 个账户
2025-07-21 14:16:52.191 | DEBUG    | ui.views.account_view:_on_accounts_loaded:652 - 账户加载完成: 2个账户
2025-07-21 14:16:52.366 | DEBUG    | ui.views.account_view:_update_group_counts:188 - 分组计数已更新: {2: 2}
2025-07-21 14:16:52.386 | INFO     | app.services.account_service:batch_auto_login:1246 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-07-21 14:16:52.386 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 14:16:52.388 | INFO     | app.services.account_service:batch_auto_login:1316 - 服务层：准备调用核心层处理 2 个账户的自动登录。
2025-07-21 14:16:52.388 | INFO     | app.services.account_service:batch_auto_login:1326 - 服务层：设置核心层任务超时为 120 秒。
2025-07-21 14:16:52.388 | INFO     | core.telegram.client_manager:batch_auto_login:1230 - 开始批量自动登录, 共 2 个账户, 最大并发 5
2025-07-21 14:16:52.389 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:271 - 创建客户端: H:\PyProject\TeleTest\APPDATA\sessions\+***********, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-21 14:16:52.389 | DEBUG    | core.telegram.client_manager:get_proxy:96 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-21 14:16:52.389 | DEBUG    | core.telegram.client_manager:get_proxy:143 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-07-21 14:16:52.394 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:271 - 创建客户端: H:\PyProject\TeleTest\APPDATA\sessions\+***********, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-21 14:16:52.394 | DEBUG    | core.telegram.client_manager:get_proxy:96 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-21 14:16:52.394 | DEBUG    | core.telegram.client_manager:get_proxy:143 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-07-21 14:16:52.425 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 14:16:52.505 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 14:16:52.537 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 14:16:52.566 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-07-21 14:16:52.568 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-07-21 14:16:52.570 | INFO     | ui.views.proxy_view:_load_initial_data:1044 - 初始数据加载完成，共 1 个代理
2025-07-21 14:16:55.897 | INFO     | core.telegram.client_manager:_connect_client:219 - 连接成功!
2025-07-21 14:16:56.710 | INFO     | core.telegram.client_manager:_verify_client_authorization:1205 - 账户已授权: +***********
2025-07-21 14:16:56.838 | INFO     | core.telegram.client_manager:_connect_client:219 - 连接成功!
2025-07-21 14:16:57.737 | INFO     | core.telegram.client_manager:_verify_client_authorization:1205 - 账户已授权: +***********
2025-07-21 14:16:59.742 | INFO     | core.telegram.client_manager:batch_auto_login:1379 - 批量登录完成: 总计 2, 成功 2, 失败 0
2025-07-21 14:17:00.399 | INFO     | app.services.account_service:batch_auto_login:1347 - 服务层：核心层登录完成: 成功 2, 失败 0 / 2
2025-07-21 14:17:00.399 | INFO     | app.services.account_service:_process_batch_login_results:1709 - 开始处理批量登录结果，共 2 个账户
2025-07-21 14:17:00.399 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 14:17:00.404 | INFO     | data.repositories.account_repo:update_account:394 - 更新账户成功: 1
2025-07-21 14:17:00.404 | INFO     | app.services.account_service:_process_batch_login_results:1745 - 已更新账户 +*********** 的用户信息
2025-07-21 14:17:00.409 | INFO     | data.repositories.account_repo:update_account:394 - 更新账户成功: 2
2025-07-21 14:17:00.409 | INFO     | app.services.account_service:_process_batch_login_results:1745 - 已更新账户 +*********** 的用户信息
2025-07-21 14:17:00.417 | INFO     | app.services.account_service:_process_batch_login_results:1768 - 批量登录结果处理完成，数据库已更新
2025-07-21 14:17:00.417 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 14:17:00.417 | INFO     | app.controllers.account_controller:auto_login_accounts:640 - 批量登录完成，触发账户数据刷新
2025-07-21 14:17:50.886 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-21 14:18:50.884 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-21 14:19:50.880 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-21 14:20:50.886 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-21 14:44:53.403 | INFO     | utils.logger:init_logger:98 - 日志系统初始化完成，默认级别: DEBUG，日志目录: H:\PyProject\TeleTest\APPDATA\logs
2025-07-21 14:44:54.957 | INFO     | data.database:init_database:31 - 初始化数据库连接: H:\PyProject\TeleTest\APPDATA\database\telegram_manager.db
2025-07-21 14:44:54.985 | DEBUG    | data.database:init_database:58 - 创建数据库表
2025-07-21 14:44:54.997 | INFO     | data.database:init_database:61 - 数据库初始化完成
2025-07-21 14:44:56.610 | DEBUG    | app.controllers.auth_controller:initialize_software:43 - 控制器: 初始化软件 app_id=1000, version=1.2.7
2025-07-21 14:44:56.610 | DEBUG    | core.auth.api_service:init_software:55 - 发送初始化请求: https://server.xile188.com/api/user/1000/windowns10/1.2.7/ini, params={}
2025-07-21 14:44:57.356 | DEBUG    | core.auth.api_service:init_software:66 - 初始化数据解密成功: {'new_content': '- 新增功能\n  - 1.新增图片上传功能\n  - 2.新增软件文档\n  - 3.新增软件自动更新，后续更新会自动检测版本更新，无需再下载更新包\n  - 4.新增设置账户信息及发信控制\n- 修复bug\n  - 删除监听任务时，不删除对应任务采集到的数据。\n  - 监听设置群无法发送到目标群\n  - 导出监听数据时应当导出单个任务而非所有数据\n- 优化记录\n  - 优化发信规则，直接调取符合要求的账户发送，无账户时才需要等待延时。\n  - 完善软件设置', 'new_url': None, 'notice': {'id': '2', 'content': '公告内容测试3', 'time': '**********'}}
2025-07-21 14:44:57.367 | INFO     | app.controllers.auth_controller:login:61 - 控制器: 用户登录 account=demo01
2025-07-21 14:45:00.381 | INFO     | core.auth.api_service:login:127 - 用户登录: account=demo01
2025-07-21 14:45:00.587 | INFO     | core.auth.api_service:get_user_info:347 - 获取用户信息
2025-07-21 14:45:00.812 | INFO     | core.auth.api_service:get_user_info:375 - 获取用户信息成功
2025-07-21 14:45:00.821 | INFO     | __main__:on_auth_completed:91 - 用户登录成功，正在启动主窗口...
2025-07-21 14:45:00.852 | INFO     | core.telegram.client_worker:__init__:61 - Telegram客户端工作线程初始化
2025-07-21 14:45:00.853 | INFO     | utils.vip_checker:initialize:25 - VIP检查器已初始化，等待登录后开始检查
2025-07-21 14:45:00.853 | INFO     | ui.main_window:_initialize_core_components:108 - MainWindow: 初始化核心组件...
2025-07-21 14:45:00.854 | INFO     | app.services.account_service:__init__:44 - 账户服务初始化
2025-07-21 14:45:00.854 | INFO     | core.telegram.client_worker:run:65 - Telegram客户端工作线程开始运行
2025-07-21 14:45:00.854 | INFO     | app.controllers.account_controller:__init__:82 - 账户控制器初始化
2025-07-21 14:45:00.854 | INFO     | core.proxy.proxy_task_manager:run:284 - 代理任务管理器线程启动
2025-07-21 14:45:00.854 | INFO     | app.services.monitor_service:__init__:39 - 初始化监控任务服务
2025-07-21 14:45:00.855 | INFO     | core.monitor.filter:__init__:26 - 初始化消息过滤器
2025-07-21 14:45:00.855 | INFO     | core.monitor.extractor:__init__:24 - 初始化关键词提取器
2025-07-21 14:45:00.855 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-07-21 14:45:00.855 | INFO     | core.monitor.notifier:__init__:90 - 初始化通知管理器
2025-07-21 14:45:00.855 | INFO     | app.controllers.telegram_monitor_controller:__init__:64 - 初始化 Telegram 监控控制器
2025-07-21 14:45:00.855 | DEBUG    | app.controllers.telegram_monitor_controller:__init__:82 - 控制器初始化完成
2025-07-21 14:45:00.857 | INFO     | core.telegram.client_manager:__init__:66 - Telegram客户端管理器初始化
2025-07-21 14:45:00.857 | INFO     | core.telegram.user_manager:__init__:30 - Telegram用户管理器初始化
2025-07-21 14:45:00.857 | INFO     | core.telegram.group_manager:__init__:36 - Telegram群组管理器初始化
2025-07-21 14:45:00.858 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-07-21 14:45:00.858 | INFO     | core.telegram.monitor_manager:__init__:26 - Telegram监控管理器初始化
2025-07-21 14:45:00.858 | INFO     | core.telegram.telegram_service:__init__:39 - Telegram服务初始化完成
2025-07-21 14:45:01.074 | INFO     | ui.views.monitor_view:__init__:48 - 初始化监控视图
2025-07-21 14:45:01.074 | DEBUG    | ui.views.monitor_view:_connect_signals:80 - 连接控制器信号
2025-07-21 14:45:01.256 | INFO     | ui.views.proxy_view:__init__:96 - 初始化视图，加载第一页代理数据
2025-07-21 14:45:01.490 | INFO     | __main__:on_auth_completed:95 - 主窗口已启动
2025-07-21 14:45:01.538 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:88 - 尝试连接监听日志信号...
2025-07-21 14:45:01.538 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:90 - 监听日志信号连接成功
2025-07-21 14:45:01.539 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 14:45:01.544 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 14:45:01.547 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:145 - 加载监控任务列表 (搜索关键词: '')
2025-07-21 14:45:01.547 | DEBUG    | ui.views.monitor_view:_on_loading_started:620 - 加载中: 正在加载任务列表...
2025-07-21 14:45:01.547 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 14:45:01.557 | INFO     | app.services.proxy_service:refresh_proxies:712 - 刷新代理列表: offset=0, limit=20
2025-07-21 14:45:01.557 | INFO     | core.proxy.proxy_task_manager:submit_task:172 - 提交耗时任务到子线程: refresh_proxies_0_20
2025-07-21 14:45:01.557 | DEBUG    | app.services.proxy_service:get_proxies:500 - 获取代理列表: offset=0, limit=20
2025-07-21 14:45:01.557 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 14:45:01.558 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 14:45:01.560 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 14:45:01.562 | INFO     | app.controllers.invite_controller:get_invite_tasks:38 - 获取邀请任务列表, status=None
2025-07-21 14:45:01.563 | INFO     | app.services.invite_service:get_invite_tasks:121 - 获取邀请任务列表, status=None
2025-07-21 14:45:01.563 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 14:45:01.574 | INFO     | app.services.invite_service:task_initialize:39 - 初始化邀请服务，重置运行中的任务为暂停状态
2025-07-21 14:45:01.580 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 14:45:01.589 | INFO     | utils.vip_checker:start_checking:37 - 开始VIP状态检查
2025-07-21 14:45:01.590 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 14:45:01.590 | DEBUG    | utils.vip_checker:_check_vip_status:61 - 正在检查VIP状态...
2025-07-21 14:45:01.590 | INFO     | core.auth.api_service:verify_vip:425 - 会员验证接口调用
2025-07-21 14:45:01.591 | INFO     | core.proxy.proxy_task_manager:task_wrapper:199 - 任务 refresh_proxies_0_20 完成
2025-07-21 14:45:01.718 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 14:45:01.721 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 14:45:01.728 | INFO     | app.services.invite_service:task_initialize:48 - 成功重置 0 个任务状态
2025-07-21 14:45:01.728 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 14:45:01.729 | DEBUG    | ui.views.monitor_view:_refresh_task_list:175 - 刷新任务列表，共0个任务
2025-07-21 14:45:01.729 | INFO     | ui.views.monitor_view:_refresh_task_list:189 - 没有找到监控任务
2025-07-21 14:45:01.729 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 14:45:01.733 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 14:45:01.736 | INFO     | app.services.account_service:get_all_groups:98 - 获取所有账户分组成功, 共 2 个
2025-07-21 14:45:01.736 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 14:45:01.737 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 14:45:01.740 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 14:45:01.743 | DEBUG    | ui.views.account_view:_on_groups_loaded:519 - 分组加载完成: 2个分组
2025-07-21 14:45:01.757 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 14:45:01.881 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 14:45:01.881 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 14:45:01.882 | INFO     | app.services.account_service:get_all_accounts:292 - 获取所有账户成功, 共 2 个
2025-07-21 14:45:01.882 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 14:45:01.884 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-07-21 14:45:01.897 | INFO     | app.controllers.telegram_monitor_controller:_load_total_users_count_internal:191 - 成功加载总体统计数据: 总用户数 23, 今日采集 0, 日均采集 23, 运行天数 1
2025-07-21 14:45:01.898 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:156 - 成功加载 0 个任务
2025-07-21 14:45:01.898 | DEBUG    | ui.views.monitor_view:_on_loading_finished:625 - 加载完成
2025-07-21 14:45:01.900 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 14:45:01.901 | DEBUG    | ui.views.account_view:_on_accounts_loaded:652 - 账户加载完成: 2个账户
2025-07-21 14:45:01.921 | DEBUG    | ui.views.account_view:_update_group_counts:188 - 分组计数已更新: {2: 2}
2025-07-21 14:45:01.925 | INFO     | ui.views.account_view:_auto_login_accounts:707 - 开始调用控制器进行 2 个账户的自动登录。
2025-07-21 14:45:01.925 | INFO     | app.services.account_service:batch_auto_login:1207 - 服务层：开始批量自动登录 2 个账户, 最大并发: 5
2025-07-21 14:45:01.926 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 14:45:01.942 | INFO     | app.services.account_service:batch_auto_login:1246 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-07-21 14:45:01.942 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 14:45:01.943 | INFO     | app.services.account_service:get_all_accounts:292 - 获取所有账户成功, 共 2 个
2025-07-21 14:45:01.943 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 14:45:01.945 | DEBUG    | ui.views.account_view:_update_group_counts:188 - 分组计数已更新: {2: 2}
2025-07-21 14:45:01.945 | DEBUG    | ui.views.account_view:_init_cache:155 - 账户缓存初始化完成，共 2 个账户
2025-07-21 14:45:01.950 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 14:45:01.951 | DEBUG    | ui.views.account_view:_on_accounts_loaded:652 - 账户加载完成: 2个账户
2025-07-21 14:45:01.975 | DEBUG    | ui.views.account_view:_update_group_counts:188 - 分组计数已更新: {2: 2}
2025-07-21 14:45:01.986 | INFO     | core.auth.api_service:verify_vip:438 - 会员验证成功
2025-07-21 14:45:01.986 | INFO     | utils.vip_checker:_check_vip_status:76 - VIP状态有效
2025-07-21 14:45:01.989 | INFO     | app.services.account_service:batch_auto_login:1246 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-07-21 14:45:01.989 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 14:45:01.991 | INFO     | app.services.account_service:batch_auto_login:1316 - 服务层：准备调用核心层处理 2 个账户的自动登录。
2025-07-21 14:45:01.991 | INFO     | app.services.account_service:batch_auto_login:1326 - 服务层：设置核心层任务超时为 120 秒。
2025-07-21 14:45:01.992 | INFO     | core.telegram.client_manager:batch_auto_login:1230 - 开始批量自动登录, 共 2 个账户, 最大并发 5
2025-07-21 14:45:01.993 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:271 - 创建客户端: H:\PyProject\TeleTest\APPDATA\sessions\+***********, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-21 14:45:01.994 | DEBUG    | core.telegram.client_manager:get_proxy:96 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-21 14:45:01.994 | DEBUG    | core.telegram.client_manager:get_proxy:143 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-07-21 14:45:01.999 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:271 - 创建客户端: H:\PyProject\TeleTest\APPDATA\sessions\+***********, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-21 14:45:02.000 | DEBUG    | core.telegram.client_manager:get_proxy:96 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-21 14:45:02.000 | DEBUG    | core.telegram.client_manager:get_proxy:143 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-07-21 14:45:02.023 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 14:45:02.098 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 14:45:02.122 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-07-21 14:45:02.123 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-07-21 14:45:02.125 | INFO     | ui.views.proxy_view:_load_initial_data:1044 - 初始数据加载完成，共 1 个代理
2025-07-21 14:45:02.136 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 14:45:05.023 | INFO     | core.telegram.client_manager:_connect_client:219 - 连接成功!
2025-07-21 14:45:05.277 | INFO     | core.telegram.client_manager:_connect_client:219 - 连接成功!
2025-07-21 14:45:06.169 | INFO     | core.telegram.client_manager:_verify_client_authorization:1205 - 账户已授权: +***********
2025-07-21 14:45:07.210 | INFO     | core.telegram.client_manager:_verify_client_authorization:1205 - 账户已授权: +***********
2025-07-21 14:45:09.224 | INFO     | core.telegram.client_manager:batch_auto_login:1379 - 批量登录完成: 总计 2, 成功 2, 失败 0
2025-07-21 14:45:10.008 | INFO     | app.services.account_service:batch_auto_login:1347 - 服务层：核心层登录完成: 成功 2, 失败 0 / 2
2025-07-21 14:45:10.008 | INFO     | app.services.account_service:_process_batch_login_results:1709 - 开始处理批量登录结果，共 2 个账户
2025-07-21 14:45:10.008 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 14:45:10.013 | INFO     | data.repositories.account_repo:update_account:394 - 更新账户成功: 1
2025-07-21 14:45:10.014 | INFO     | app.services.account_service:_process_batch_login_results:1745 - 已更新账户 +*********** 的用户信息
2025-07-21 14:45:10.020 | INFO     | data.repositories.account_repo:update_account:394 - 更新账户成功: 2
2025-07-21 14:45:10.020 | INFO     | app.services.account_service:_process_batch_login_results:1745 - 已更新账户 +*********** 的用户信息
2025-07-21 14:45:10.026 | INFO     | app.services.account_service:_process_batch_login_results:1768 - 批量登录结果处理完成，数据库已更新
2025-07-21 14:45:10.027 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 14:45:10.027 | INFO     | app.controllers.account_controller:auto_login_accounts:640 - 批量登录完成，触发账户数据刷新
2025-07-21 14:45:15.800 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 14:45:15.809 | INFO     | app.services.account_service:get_accounts_by_group:314 - 获取分组 2 的账户成功, 共 2 个
2025-07-21 14:45:15.809 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 14:45:15.810 | DEBUG    | ui.views.account_view:_on_accounts_loaded:652 - 账户加载完成: 2个账户
2025-07-21 14:45:16.496 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 14:45:16.503 | INFO     | app.services.account_service:get_all_accounts:292 - 获取所有账户成功, 共 2 个
2025-07-21 14:45:16.503 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 14:45:16.504 | DEBUG    | ui.views.account_view:_on_accounts_loaded:652 - 账户加载完成: 2个账户
2025-07-21 14:45:16.523 | DEBUG    | ui.views.account_view:_update_group_counts:188 - 分组计数已更新: {2: 2}
2025-07-21 14:45:40.240 | INFO     | app.services.account_service:update_account_profile:493 - 更新账户资料: 2
2025-07-21 14:45:40.241 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 14:45:40.282 | INFO     | app.services.account_service:_generate_unique_username:1632 - 生成唯一用户名成功: tianyibiirorxiqarm (尝试 1/5)
2025-07-21 14:45:40.282 | INFO     | core.telegram.user_manager:update_profile:89 - 正在更新用户资料: +***********
2025-07-21 14:46:00.803 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-21 14:46:01.510 | INFO     | core.telegram.user_manager:update_profile:130 - 更新用户资料成功: +***********
2025-07-21 14:46:02.429 | INFO     | data.repositories.account_repo:update_account:394 - 更新账户成功: 2
2025-07-21 14:46:02.436 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 14:46:03.960 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 14:46:03.966 | INFO     | app.services.account_service:get_all_accounts:292 - 获取所有账户成功, 共 2 个
2025-07-21 14:46:03.967 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 14:46:03.968 | DEBUG    | ui.views.account_view:_on_accounts_loaded:652 - 账户加载完成: 2个账户
2025-07-21 14:46:03.990 | DEBUG    | ui.views.account_view:_update_group_counts:188 - 分组计数已更新: {2: 1, 1: 1}
2025-07-21 14:47:00.808 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-21 14:48:00.805 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-21 14:49:00.803 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-21 15:02:23.862 | INFO     | utils.logger:init_logger:98 - 日志系统初始化完成，默认级别: DEBUG，日志目录: H:\PyProject\TeleTest\APPDATA\logs
2025-07-21 15:02:24.903 | INFO     | data.database:init_database:31 - 初始化数据库连接: H:\PyProject\TeleTest\APPDATA\database\telegram_manager.db
2025-07-21 15:02:24.920 | DEBUG    | data.database:init_database:58 - 创建数据库表
2025-07-21 15:02:24.929 | INFO     | data.database:init_database:61 - 数据库初始化完成
2025-07-21 15:02:26.239 | DEBUG    | app.controllers.auth_controller:initialize_software:43 - 控制器: 初始化软件 app_id=1000, version=1.2.7
2025-07-21 15:02:26.240 | DEBUG    | core.auth.api_service:init_software:55 - 发送初始化请求: https://server.xile188.com/api/user/1000/windowns10/1.2.7/ini, params={}
2025-07-21 15:02:26.473 | DEBUG    | core.auth.api_service:init_software:66 - 初始化数据解密成功: {'new_content': '- 新增功能\n  - 1.新增图片上传功能\n  - 2.新增软件文档\n  - 3.新增软件自动更新，后续更新会自动检测版本更新，无需再下载更新包\n  - 4.新增设置账户信息及发信控制\n- 修复bug\n  - 删除监听任务时，不删除对应任务采集到的数据。\n  - 监听设置群无法发送到目标群\n  - 导出监听数据时应当导出单个任务而非所有数据\n- 优化记录\n  - 优化发信规则，直接调取符合要求的账户发送，无账户时才需要等待延时。\n  - 完善软件设置', 'new_url': None, 'notice': {'id': '2', 'content': '公告内容测试3', 'time': '**********'}}
2025-07-21 15:02:26.480 | INFO     | app.controllers.auth_controller:login:61 - 控制器: 用户登录 account=demo01
2025-07-21 15:02:29.480 | INFO     | core.auth.api_service:login:127 - 用户登录: account=demo01
2025-07-21 15:02:29.722 | INFO     | core.auth.api_service:get_user_info:347 - 获取用户信息
2025-07-21 15:02:29.932 | INFO     | core.auth.api_service:get_user_info:375 - 获取用户信息成功
2025-07-21 15:02:29.939 | INFO     | __main__:on_auth_completed:91 - 用户登录成功，正在启动主窗口...
2025-07-21 15:02:29.964 | INFO     | core.telegram.client_worker:__init__:61 - Telegram客户端工作线程初始化
2025-07-21 15:02:29.964 | INFO     | utils.vip_checker:initialize:25 - VIP检查器已初始化，等待登录后开始检查
2025-07-21 15:02:29.964 | INFO     | ui.main_window:_initialize_core_components:108 - MainWindow: 初始化核心组件...
2025-07-21 15:02:29.964 | INFO     | app.services.account_service:__init__:44 - 账户服务初始化
2025-07-21 15:02:29.965 | INFO     | app.controllers.account_controller:__init__:82 - 账户控制器初始化
2025-07-21 15:02:29.965 | INFO     | core.telegram.client_worker:run:65 - Telegram客户端工作线程开始运行
2025-07-21 15:02:29.965 | INFO     | core.proxy.proxy_task_manager:run:284 - 代理任务管理器线程启动
2025-07-21 15:02:29.965 | INFO     | app.services.monitor_service:__init__:39 - 初始化监控任务服务
2025-07-21 15:02:29.965 | INFO     | core.monitor.filter:__init__:26 - 初始化消息过滤器
2025-07-21 15:02:29.966 | INFO     | core.monitor.extractor:__init__:24 - 初始化关键词提取器
2025-07-21 15:02:29.966 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-07-21 15:02:29.966 | INFO     | core.monitor.notifier:__init__:90 - 初始化通知管理器
2025-07-21 15:02:29.966 | INFO     | app.controllers.telegram_monitor_controller:__init__:64 - 初始化 Telegram 监控控制器
2025-07-21 15:02:29.966 | DEBUG    | app.controllers.telegram_monitor_controller:__init__:82 - 控制器初始化完成
2025-07-21 15:02:29.969 | INFO     | core.telegram.client_manager:__init__:66 - Telegram客户端管理器初始化
2025-07-21 15:02:29.969 | INFO     | core.telegram.user_manager:__init__:30 - Telegram用户管理器初始化
2025-07-21 15:02:29.969 | INFO     | core.telegram.group_manager:__init__:36 - Telegram群组管理器初始化
2025-07-21 15:02:29.970 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-07-21 15:02:29.970 | INFO     | core.telegram.monitor_manager:__init__:26 - Telegram监控管理器初始化
2025-07-21 15:02:29.972 | INFO     | core.telegram.telegram_service:__init__:39 - Telegram服务初始化完成
2025-07-21 15:02:30.159 | INFO     | ui.views.monitor_view:__init__:48 - 初始化监控视图
2025-07-21 15:02:30.159 | DEBUG    | ui.views.monitor_view:_connect_signals:80 - 连接控制器信号
2025-07-21 15:02:30.341 | INFO     | ui.views.proxy_view:__init__:96 - 初始化视图，加载第一页代理数据
2025-07-21 15:02:30.575 | INFO     | __main__:on_auth_completed:95 - 主窗口已启动
2025-07-21 15:02:30.620 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:88 - 尝试连接监听日志信号...
2025-07-21 15:02:30.621 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:90 - 监听日志信号连接成功
2025-07-21 15:02:30.621 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 15:02:30.626 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 15:02:30.629 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:145 - 加载监控任务列表 (搜索关键词: '')
2025-07-21 15:02:30.630 | DEBUG    | ui.views.monitor_view:_on_loading_started:620 - 加载中: 正在加载任务列表...
2025-07-21 15:02:30.630 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 15:02:30.636 | INFO     | app.services.proxy_service:refresh_proxies:712 - 刷新代理列表: offset=0, limit=20
2025-07-21 15:02:30.636 | INFO     | core.proxy.proxy_task_manager:submit_task:172 - 提交耗时任务到子线程: refresh_proxies_0_20
2025-07-21 15:02:30.636 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 15:02:30.636 | DEBUG    | app.services.proxy_service:get_proxies:500 - 获取代理列表: offset=0, limit=20
2025-07-21 15:02:30.637 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 15:02:30.639 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 15:02:30.663 | INFO     | app.controllers.invite_controller:get_invite_tasks:38 - 获取邀请任务列表, status=None
2025-07-21 15:02:30.663 | INFO     | app.services.invite_service:get_invite_tasks:121 - 获取邀请任务列表, status=None
2025-07-21 15:02:30.663 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 15:02:30.664 | INFO     | app.services.invite_service:task_initialize:39 - 初始化邀请服务，重置运行中的任务为暂停状态
2025-07-21 15:02:30.664 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 15:02:30.669 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 15:02:30.669 | INFO     | utils.vip_checker:start_checking:37 - 开始VIP状态检查
2025-07-21 15:02:30.669 | DEBUG    | utils.vip_checker:_check_vip_status:61 - 正在检查VIP状态...
2025-07-21 15:02:30.669 | INFO     | core.auth.api_service:verify_vip:425 - 会员验证接口调用
2025-07-21 15:02:30.670 | INFO     | core.proxy.proxy_task_manager:task_wrapper:199 - 任务 refresh_proxies_0_20 完成
2025-07-21 15:02:30.798 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 15:02:30.800 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 15:02:30.804 | INFO     | app.services.invite_service:task_initialize:48 - 成功重置 0 个任务状态
2025-07-21 15:02:30.805 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 15:02:30.806 | DEBUG    | ui.views.monitor_view:_refresh_task_list:175 - 刷新任务列表，共0个任务
2025-07-21 15:02:30.807 | INFO     | ui.views.monitor_view:_refresh_task_list:189 - 没有找到监控任务
2025-07-21 15:02:30.807 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 15:02:30.809 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 15:02:30.814 | INFO     | app.services.account_service:get_all_groups:98 - 获取所有账户分组成功, 共 2 个
2025-07-21 15:02:30.814 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 15:02:30.815 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 15:02:30.817 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 15:02:30.818 | DEBUG    | ui.views.account_view:_on_groups_loaded:519 - 分组加载完成: 2个分组
2025-07-21 15:02:30.832 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 15:02:30.957 | INFO     | app.services.account_service:get_all_accounts:292 - 获取所有账户成功, 共 2 个
2025-07-21 15:02:30.957 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 15:02:30.958 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 15:02:30.959 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 15:02:30.962 | INFO     | app.controllers.telegram_monitor_controller:_load_total_users_count_internal:191 - 成功加载总体统计数据: 总用户数 23, 今日采集 0, 日均采集 23, 运行天数 1
2025-07-21 15:02:30.962 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:156 - 成功加载 0 个任务
2025-07-21 15:02:30.962 | DEBUG    | ui.views.monitor_view:_on_loading_finished:625 - 加载完成
2025-07-21 15:02:30.963 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 15:02:30.965 | DEBUG    | ui.views.account_view:_on_accounts_loaded:652 - 账户加载完成: 2个账户
2025-07-21 15:02:30.983 | DEBUG    | ui.views.account_view:_update_group_counts:188 - 分组计数已更新: {2: 1, 1: 1}
2025-07-21 15:02:30.987 | INFO     | ui.views.account_view:_auto_login_accounts:707 - 开始调用控制器进行 2 个账户的自动登录。
2025-07-21 15:02:30.987 | INFO     | app.services.account_service:batch_auto_login:1207 - 服务层：开始批量自动登录 2 个账户, 最大并发: 5
2025-07-21 15:02:30.987 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 15:02:30.992 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-07-21 15:02:31.007 | INFO     | app.services.account_service:batch_auto_login:1246 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-07-21 15:02:31.007 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 15:02:31.008 | INFO     | app.services.account_service:get_all_accounts:292 - 获取所有账户成功, 共 2 个
2025-07-21 15:02:31.008 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 15:02:31.012 | DEBUG    | ui.views.account_view:_update_group_counts:188 - 分组计数已更新: {2: 1, 1: 1}
2025-07-21 15:02:31.012 | DEBUG    | ui.views.account_view:_init_cache:155 - 账户缓存初始化完成，共 2 个账户
2025-07-21 15:02:31.017 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 15:02:31.019 | DEBUG    | ui.views.account_view:_on_accounts_loaded:652 - 账户加载完成: 2个账户
2025-07-21 15:02:31.042 | DEBUG    | ui.views.account_view:_update_group_counts:188 - 分组计数已更新: {2: 1, 1: 1}
2025-07-21 15:02:31.056 | INFO     | core.auth.api_service:verify_vip:438 - 会员验证成功
2025-07-21 15:02:31.056 | INFO     | utils.vip_checker:_check_vip_status:76 - VIP状态有效
2025-07-21 15:02:31.060 | INFO     | app.services.account_service:batch_auto_login:1246 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-07-21 15:02:31.060 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 15:02:31.062 | INFO     | app.services.account_service:batch_auto_login:1316 - 服务层：准备调用核心层处理 2 个账户的自动登录。
2025-07-21 15:02:31.062 | INFO     | app.services.account_service:batch_auto_login:1326 - 服务层：设置核心层任务超时为 120 秒。
2025-07-21 15:02:31.062 | INFO     | core.telegram.client_manager:batch_auto_login:1230 - 开始批量自动登录, 共 2 个账户, 最大并发 5
2025-07-21 15:02:31.063 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:271 - 创建客户端: H:\PyProject\TeleTest\APPDATA\sessions\+***********, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-21 15:02:31.063 | DEBUG    | core.telegram.client_manager:get_proxy:96 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-21 15:02:31.063 | DEBUG    | core.telegram.client_manager:get_proxy:143 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-07-21 15:02:31.068 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:271 - 创建客户端: H:\PyProject\TeleTest\APPDATA\sessions\+***********, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-21 15:02:31.068 | DEBUG    | core.telegram.client_manager:get_proxy:96 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-21 15:02:31.069 | DEBUG    | core.telegram.client_manager:get_proxy:143 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-07-21 15:02:31.094 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 15:02:31.173 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 15:02:31.262 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 15:02:31.599 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-07-21 15:02:31.601 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-07-21 15:02:31.602 | INFO     | ui.views.proxy_view:_load_initial_data:1044 - 初始数据加载完成，共 1 个代理
2025-07-21 15:02:34.722 | INFO     | core.telegram.client_manager:_connect_client:219 - 连接成功!
2025-07-21 15:02:35.594 | INFO     | core.telegram.client_manager:_connect_client:219 - 连接成功!
2025-07-21 15:02:35.672 | INFO     | core.telegram.client_manager:_verify_client_authorization:1205 - 账户已授权: +***********
2025-07-21 15:02:36.835 | INFO     | core.telegram.client_manager:_verify_client_authorization:1205 - 账户已授权: +***********
2025-07-21 15:02:38.848 | INFO     | core.telegram.client_manager:batch_auto_login:1379 - 批量登录完成: 总计 2, 成功 2, 失败 0
2025-07-21 15:02:39.081 | INFO     | app.services.account_service:batch_auto_login:1347 - 服务层：核心层登录完成: 成功 2, 失败 0 / 2
2025-07-21 15:02:39.081 | INFO     | app.services.account_service:_process_batch_login_results:1709 - 开始处理批量登录结果，共 2 个账户
2025-07-21 15:02:39.081 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 15:02:39.088 | INFO     | data.repositories.account_repo:update_account:394 - 更新账户成功: 1
2025-07-21 15:02:39.088 | INFO     | app.services.account_service:_process_batch_login_results:1745 - 已更新账户 +*********** 的用户信息
2025-07-21 15:02:39.092 | INFO     | data.repositories.account_repo:update_account:394 - 更新账户成功: 2
2025-07-21 15:02:39.092 | INFO     | app.services.account_service:_process_batch_login_results:1745 - 已更新账户 +*********** 的用户信息
2025-07-21 15:02:39.099 | INFO     | app.services.account_service:_process_batch_login_results:1768 - 批量登录结果处理完成，数据库已更新
2025-07-21 15:02:39.100 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 15:02:39.100 | INFO     | app.controllers.account_controller:auto_login_accounts:640 - 批量登录完成，触发账户数据刷新
2025-07-21 15:03:29.451 | INFO     | app.services.account_service:update_account_profile:493 - 更新账户资料: 2
2025-07-21 15:03:29.451 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 15:03:29.490 | INFO     | app.services.account_service:_generate_unique_username:1632 - 生成唯一用户名成功: tianyibiirorxiqarm893 (尝试 1/5)
2025-07-21 15:03:29.490 | INFO     | core.telegram.user_manager:update_profile:89 - 正在更新用户资料: +***********
2025-07-21 15:03:29.927 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-21 15:03:32.728 | INFO     | core.telegram.user_manager:update_profile:130 - 更新用户资料成功: +***********
2025-07-21 15:03:33.509 | INFO     | data.repositories.account_repo:update_account:394 - 更新账户成功: 2
2025-07-21 15:03:33.516 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 15:03:35.544 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 15:03:35.624 | INFO     | app.services.account_service:get_all_accounts:292 - 获取所有账户成功, 共 2 个
2025-07-21 15:03:35.624 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 15:03:35.625 | DEBUG    | ui.views.account_view:_on_accounts_loaded:652 - 账户加载完成: 2个账户
2025-07-21 15:03:35.646 | DEBUG    | ui.views.account_view:_update_group_counts:188 - 分组计数已更新: {2: 1, 1: 1}
2025-07-21 15:03:41.357 | INFO     | app.services.account_service:refresh_account_info:649 - 刷新账户信息: 1
2025-07-21 15:03:41.357 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 15:03:41.366 | INFO     | core.telegram.user_manager:get_user_info:41 - 正在获取用户信息: +***********
2025-07-21 15:03:42.274 | INFO     | core.telegram.user_manager:get_user_info:66 - 获取用户信息成功: +***********
2025-07-21 15:03:42.619 | INFO     | data.repositories.account_repo:update_account:394 - 更新账户成功: 1
2025-07-21 15:03:42.625 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 15:03:42.629 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 15:03:42.640 | INFO     | ui.views.account_view:_update_single_account_display:337 - 已实时更新账户显示: +***********
2025-07-21 15:03:42.640 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 15:03:44.508 | INFO     | app.services.account_service:refresh_account_info:649 - 刷新账户信息: 2
2025-07-21 15:03:44.508 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 15:03:44.520 | INFO     | core.telegram.user_manager:get_user_info:41 - 正在获取用户信息: +***********
2025-07-21 15:03:46.035 | INFO     | core.telegram.user_manager:get_user_info:66 - 获取用户信息成功: +***********
2025-07-21 15:03:46.620 | INFO     | data.repositories.account_repo:update_account:394 - 更新账户成功: 2
2025-07-21 15:03:46.629 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 15:03:46.635 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 15:03:46.649 | INFO     | ui.views.account_view:_update_single_account_display:337 - 已实时更新账户显示: +***********
2025-07-21 15:03:46.650 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 15:04:29.933 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-21 15:05:29.933 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-21 15:06:29.940 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-21 15:07:29.929 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-21 15:08:29.931 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-21 15:09:29.927 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-21 15:10:29.928 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-21 15:10:57.709 | INFO     | ui.main_window:closeEvent:380 - MainWindow: 接收到关闭事件
2025-07-21 15:10:57.727 | INFO     | ui.main_window:_cleanup_before_quit:266 - MainWindow: 执行清理资源...
2025-07-21 15:10:57.728 | INFO     | core.telegram.client_worker:stop:108 - 正在停止Telegram客户端工作线程
2025-07-21 15:10:57.734 | INFO     | core.telegram.client_manager:cleanup_async:1150 - 开始清理资源并断开所有连接
2025-07-21 15:10:57.735 | INFO     | core.telegram.client_manager:disconnect_all_clients:735 - 正在断开所有客户端连接
2025-07-21 15:10:57.735 | INFO     | core.telegram.client_manager:_safe_disconnect:756 - 正在断开客户端连接: +***********
2025-07-21 15:10:57.735 | INFO     | core.telegram.client_manager:_safe_disconnect:756 - 正在断开客户端连接: +***********
2025-07-21 15:10:57.824 | INFO     | core.telegram.client_manager:_safe_disconnect:760 - 断开客户端连接成功: +***********
2025-07-21 15:10:57.824 | INFO     | core.telegram.client_manager:_safe_disconnect:760 - 断开客户端连接成功: +***********
2025-07-21 15:10:57.824 | INFO     | core.telegram.client_manager:cleanup_async:1169 - 资源清理完成
2025-07-21 15:10:58.237 | INFO     | core.telegram.client_manager:cleanup_async:1150 - 开始清理资源并断开所有连接
2025-07-21 15:10:58.238 | INFO     | core.telegram.client_manager:disconnect_all_clients:735 - 正在断开所有客户端连接
2025-07-21 15:10:58.238 | INFO     | core.telegram.client_manager:cleanup_async:1169 - 资源清理完成
2025-07-21 15:10:58.751 | INFO     | core.telegram.client_worker:run:97 - Telegram客户端工作线程已退出
2025-07-21 15:10:58.752 | INFO     | core.telegram.client_worker:stop:122 - Telegram客户端工作线程已停止
2025-07-21 15:10:58.752 | INFO     | ui.main_window:_cleanup_before_quit:275 - TelegramClientWorker 已停止。
2025-07-21 15:10:58.753 | INFO     | ui.main_window:_cleanup_before_quit:279 - MainWindow 清理完成
2025-07-21 15:10:58.802 | INFO     | __main__:main:109 - 应用程序已正常退出
2025-07-21 16:07:37.975 | INFO     | utils.logger:init_logger:98 - 日志系统初始化完成，默认级别: DEBUG，日志目录: H:\PyProject\TeleTest\APPDATA\logs
2025-07-21 16:07:39.797 | INFO     | data.database:init_database:31 - 初始化数据库连接: H:\PyProject\TeleTest\APPDATA\database\telegram_manager.db
2025-07-21 16:07:39.825 | DEBUG    | data.database:init_database:58 - 创建数据库表
2025-07-21 16:07:39.840 | INFO     | data.database:init_database:61 - 数据库初始化完成
2025-07-21 16:07:41.105 | DEBUG    | app.controllers.auth_controller:initialize_software:43 - 控制器: 初始化软件 app_id=1000, version=1.2.7
2025-07-21 16:07:41.105 | DEBUG    | core.auth.api_service:init_software:55 - 发送初始化请求: https://server.xile188.com/api/user/1000/windowns10/1.2.7/ini, params={}
2025-07-21 16:07:41.594 | DEBUG    | core.auth.api_service:init_software:66 - 初始化数据解密成功: {'new_content': '- 新增功能\n  - 1.新增图片上传功能\n  - 2.新增软件文档\n  - 3.新增软件自动更新，后续更新会自动检测版本更新，无需再下载更新包\n  - 4.新增设置账户信息及发信控制\n- 修复bug\n  - 删除监听任务时，不删除对应任务采集到的数据。\n  - 监听设置群无法发送到目标群\n  - 导出监听数据时应当导出单个任务而非所有数据\n- 优化记录\n  - 优化发信规则，直接调取符合要求的账户发送，无账户时才需要等待延时。\n  - 完善软件设置', 'new_url': None, 'notice': {'id': '2', 'content': '公告内容测试3', 'time': '**********'}}
2025-07-21 16:07:41.606 | INFO     | app.controllers.auth_controller:login:61 - 控制器: 用户登录 account=demo01
2025-07-21 16:07:44.651 | INFO     | core.auth.api_service:login:127 - 用户登录: account=demo01
2025-07-21 16:07:44.922 | INFO     | core.auth.api_service:get_user_info:347 - 获取用户信息
2025-07-21 16:07:45.148 | INFO     | core.auth.api_service:get_user_info:375 - 获取用户信息成功
2025-07-21 16:07:45.157 | INFO     | __main__:on_auth_completed:91 - 用户登录成功，正在启动主窗口...
2025-07-21 16:07:45.196 | INFO     | core.telegram.client_worker:__init__:61 - Telegram客户端工作线程初始化
2025-07-21 16:07:45.196 | INFO     | utils.vip_checker:initialize:25 - VIP检查器已初始化，等待登录后开始检查
2025-07-21 16:07:45.196 | INFO     | ui.main_window:_initialize_core_components:108 - MainWindow: 初始化核心组件...
2025-07-21 16:07:45.197 | INFO     | core.telegram.client_worker:run:65 - Telegram客户端工作线程开始运行
2025-07-21 16:07:45.197 | INFO     | app.services.account_service:__init__:44 - 账户服务初始化
2025-07-21 16:07:45.198 | INFO     | core.proxy.proxy_task_manager:run:284 - 代理任务管理器线程启动
2025-07-21 16:07:45.198 | INFO     | app.controllers.account_controller:__init__:82 - 账户控制器初始化
2025-07-21 16:07:45.199 | INFO     | app.services.monitor_service:__init__:39 - 初始化监控任务服务
2025-07-21 16:07:45.199 | INFO     | core.monitor.filter:__init__:26 - 初始化消息过滤器
2025-07-21 16:07:45.199 | INFO     | core.monitor.extractor:__init__:24 - 初始化关键词提取器
2025-07-21 16:07:45.199 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-07-21 16:07:45.200 | INFO     | core.monitor.notifier:__init__:90 - 初始化通知管理器
2025-07-21 16:07:45.200 | INFO     | app.controllers.telegram_monitor_controller:__init__:64 - 初始化 Telegram 监控控制器
2025-07-21 16:07:45.200 | INFO     | core.telegram.client_manager:__init__:66 - Telegram客户端管理器初始化
2025-07-21 16:07:45.200 | DEBUG    | app.controllers.telegram_monitor_controller:__init__:82 - 控制器初始化完成
2025-07-21 16:07:45.201 | INFO     | core.telegram.user_manager:__init__:30 - Telegram用户管理器初始化
2025-07-21 16:07:45.202 | INFO     | core.telegram.group_manager:__init__:36 - Telegram群组管理器初始化
2025-07-21 16:07:45.202 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-07-21 16:07:45.203 | INFO     | core.telegram.monitor_manager:__init__:26 - Telegram监控管理器初始化
2025-07-21 16:07:45.204 | INFO     | core.telegram.telegram_service:__init__:39 - Telegram服务初始化完成
2025-07-21 16:07:45.463 | INFO     | ui.views.monitor_view:__init__:48 - 初始化监控视图
2025-07-21 16:07:45.464 | DEBUG    | ui.views.monitor_view:_connect_signals:80 - 连接控制器信号
2025-07-21 16:07:45.661 | INFO     | ui.views.proxy_view:__init__:96 - 初始化视图，加载第一页代理数据
2025-07-21 16:07:45.920 | INFO     | __main__:on_auth_completed:95 - 主窗口已启动
2025-07-21 16:07:45.973 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:88 - 尝试连接监听日志信号...
2025-07-21 16:07:45.974 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:90 - 监听日志信号连接成功
2025-07-21 16:07:45.975 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 16:07:45.979 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 16:07:45.983 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:145 - 加载监控任务列表 (搜索关键词: '')
2025-07-21 16:07:45.983 | DEBUG    | ui.views.monitor_view:_on_loading_started:620 - 加载中: 正在加载任务列表...
2025-07-21 16:07:45.983 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 16:07:45.989 | INFO     | app.services.proxy_service:refresh_proxies:712 - 刷新代理列表: offset=0, limit=20
2025-07-21 16:07:45.989 | INFO     | core.proxy.proxy_task_manager:submit_task:172 - 提交耗时任务到子线程: refresh_proxies_0_20
2025-07-21 16:07:45.990 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 16:07:45.990 | DEBUG    | app.services.proxy_service:get_proxies:500 - 获取代理列表: offset=0, limit=20
2025-07-21 16:07:45.991 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 16:07:45.993 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 16:07:45.996 | INFO     | app.controllers.invite_controller:get_invite_tasks:38 - 获取邀请任务列表, status=None
2025-07-21 16:07:46.002 | INFO     | app.services.invite_service:get_invite_tasks:121 - 获取邀请任务列表, status=None
2025-07-21 16:07:46.008 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 16:07:46.019 | INFO     | app.services.invite_service:task_initialize:39 - 初始化邀请服务，重置运行中的任务为暂停状态
2025-07-21 16:07:46.020 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 16:07:46.025 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 16:07:46.025 | INFO     | utils.vip_checker:start_checking:37 - 开始VIP状态检查
2025-07-21 16:07:46.025 | DEBUG    | utils.vip_checker:_check_vip_status:61 - 正在检查VIP状态...
2025-07-21 16:07:46.025 | INFO     | core.auth.api_service:verify_vip:425 - 会员验证接口调用
2025-07-21 16:07:46.026 | INFO     | core.proxy.proxy_task_manager:task_wrapper:199 - 任务 refresh_proxies_0_20 完成
2025-07-21 16:07:46.306 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 16:07:46.313 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 16:07:46.316 | DEBUG    | ui.views.monitor_view:_refresh_task_list:175 - 刷新任务列表，共0个任务
2025-07-21 16:07:46.316 | INFO     | ui.views.monitor_view:_refresh_task_list:189 - 没有找到监控任务
2025-07-21 16:07:46.316 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 16:07:46.327 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 16:07:46.329 | INFO     | app.services.invite_service:task_initialize:48 - 成功重置 0 个任务状态
2025-07-21 16:07:46.330 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 16:07:46.334 | INFO     | app.services.account_service:get_all_groups:98 - 获取所有账户分组成功, 共 2 个
2025-07-21 16:07:46.334 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 16:07:46.339 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 16:07:46.341 | DEBUG    | ui.views.account_view:_on_groups_loaded:519 - 分组加载完成: 2个分组
2025-07-21 16:07:46.374 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 16:07:46.424 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 16:07:46.541 | INFO     | app.services.account_service:get_all_accounts:292 - 获取所有账户成功, 共 2 个
2025-07-21 16:07:46.541 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 16:07:46.543 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 16:07:46.544 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 16:07:46.545 | DEBUG    | ui.views.account_view:_on_accounts_loaded:652 - 账户加载完成: 2个账户
2025-07-21 16:07:46.563 | DEBUG    | ui.views.account_view:_update_group_counts:188 - 分组计数已更新: {2: 1, 1: 1}
2025-07-21 16:07:46.567 | INFO     | ui.views.account_view:_auto_login_accounts:707 - 开始调用控制器进行 2 个账户的自动登录。
2025-07-21 16:07:46.567 | INFO     | app.services.account_service:batch_auto_login:1207 - 服务层：开始批量自动登录 2 个账户, 最大并发: 5
2025-07-21 16:07:46.568 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 16:07:46.574 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 16:07:46.577 | INFO     | app.controllers.telegram_monitor_controller:_load_total_users_count_internal:191 - 成功加载总体统计数据: 总用户数 23, 今日采集 0, 日均采集 23, 运行天数 1
2025-07-21 16:07:46.577 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:156 - 成功加载 0 个任务
2025-07-21 16:07:46.577 | DEBUG    | ui.views.monitor_view:_on_loading_finished:625 - 加载完成
2025-07-21 16:07:46.580 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-07-21 16:07:46.596 | INFO     | app.services.account_service:get_all_accounts:292 - 获取所有账户成功, 共 2 个
2025-07-21 16:07:46.596 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 16:07:46.596 | INFO     | app.services.account_service:batch_auto_login:1246 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-07-21 16:07:46.597 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 16:07:46.599 | DEBUG    | ui.views.account_view:_update_group_counts:188 - 分组计数已更新: {2: 1, 1: 1}
2025-07-21 16:07:46.599 | DEBUG    | ui.views.account_view:_init_cache:155 - 账户缓存初始化完成，共 2 个账户
2025-07-21 16:07:46.604 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 16:07:46.605 | DEBUG    | ui.views.account_view:_on_accounts_loaded:652 - 账户加载完成: 2个账户
2025-07-21 16:07:46.624 | DEBUG    | ui.views.account_view:_update_group_counts:188 - 分组计数已更新: {2: 1, 1: 1}
2025-07-21 16:07:46.639 | INFO     | core.auth.api_service:verify_vip:438 - 会员验证成功
2025-07-21 16:07:46.639 | INFO     | utils.vip_checker:_check_vip_status:76 - VIP状态有效
2025-07-21 16:07:46.653 | INFO     | app.services.account_service:batch_auto_login:1246 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-07-21 16:07:46.653 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 16:07:46.654 | INFO     | app.services.account_service:batch_auto_login:1316 - 服务层：准备调用核心层处理 2 个账户的自动登录。
2025-07-21 16:07:46.654 | INFO     | app.services.account_service:batch_auto_login:1326 - 服务层：设置核心层任务超时为 120 秒。
2025-07-21 16:07:46.655 | INFO     | core.telegram.client_manager:batch_auto_login:1230 - 开始批量自动登录, 共 2 个账户, 最大并发 5
2025-07-21 16:07:46.655 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:271 - 创建客户端: H:\PyProject\TeleTest\APPDATA\sessions\+***********, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-21 16:07:46.655 | DEBUG    | core.telegram.client_manager:get_proxy:96 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-21 16:07:46.656 | DEBUG    | core.telegram.client_manager:get_proxy:143 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-07-21 16:07:46.661 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:271 - 创建客户端: H:\PyProject\TeleTest\APPDATA\sessions\+***********, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-21 16:07:46.661 | DEBUG    | core.telegram.client_manager:get_proxy:96 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-21 16:07:46.662 | DEBUG    | core.telegram.client_manager:get_proxy:143 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-07-21 16:07:46.706 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 16:07:46.797 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 16:07:46.840 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-07-21 16:07:46.841 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-07-21 16:07:46.843 | INFO     | ui.views.proxy_view:_load_initial_data:1044 - 初始数据加载完成，共 1 个代理
2025-07-21 16:07:46.862 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 16:07:50.069 | INFO     | core.telegram.client_manager:_connect_client:219 - 连接成功!
2025-07-21 16:07:50.809 | INFO     | core.telegram.client_manager:_connect_client:219 - 连接成功!
2025-07-21 16:07:50.978 | INFO     | core.telegram.client_manager:_verify_client_authorization:1205 - 账户已授权: +***********
2025-07-21 16:07:53.992 | INFO     | core.telegram.client_manager:_verify_client_authorization:1205 - 账户已授权: +***********
2025-07-21 16:07:55.997 | INFO     | core.telegram.client_manager:batch_auto_login:1379 - 批量登录完成: 总计 2, 成功 2, 失败 0
2025-07-21 16:07:56.679 | INFO     | app.services.account_service:batch_auto_login:1347 - 服务层：核心层登录完成: 成功 2, 失败 0 / 2
2025-07-21 16:07:56.679 | INFO     | app.services.account_service:_process_batch_login_results:1709 - 开始处理批量登录结果，共 2 个账户
2025-07-21 16:07:56.679 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 16:07:56.705 | INFO     | data.repositories.account_repo:update_account:394 - 更新账户成功: 2
2025-07-21 16:07:56.706 | INFO     | app.services.account_service:_process_batch_login_results:1745 - 已更新账户 +*********** 的用户信息
2025-07-21 16:07:56.715 | INFO     | data.repositories.account_repo:update_account:394 - 更新账户成功: 1
2025-07-21 16:07:56.715 | INFO     | app.services.account_service:_process_batch_login_results:1745 - 已更新账户 +*********** 的用户信息
2025-07-21 16:07:56.724 | INFO     | app.services.account_service:_process_batch_login_results:1768 - 批量登录结果处理完成，数据库已更新
2025-07-21 16:07:56.724 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 16:07:56.725 | INFO     | app.controllers.account_controller:auto_login_accounts:640 - 批量登录完成，触发账户数据刷新
2025-07-21 16:08:37.818 | INFO     | app.services.account_service:update_account_profile:493 - 更新账户资料: 2
2025-07-21 16:08:37.818 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 16:08:37.869 | INFO     | core.telegram.user_manager:update_profile:89 - 正在更新用户资料: +***********
2025-07-21 16:08:41.848 | INFO     | core.telegram.user_manager:update_profile:130 - 更新用户资料成功: +***********
2025-07-21 16:08:41.882 | INFO     | data.repositories.account_repo:update_account:394 - 更新账户成功: 2
2025-07-21 16:08:41.890 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 16:08:43.922 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 16:08:43.933 | INFO     | app.services.account_service:get_all_accounts:292 - 获取所有账户成功, 共 2 个
2025-07-21 16:08:43.933 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 16:08:43.935 | DEBUG    | ui.views.account_view:_on_accounts_loaded:652 - 账户加载完成: 2个账户
2025-07-21 16:08:43.957 | DEBUG    | ui.views.account_view:_update_group_counts:188 - 分组计数已更新: {2: 1, 1: 1}
2025-07-21 16:08:45.147 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-21 16:09:12.308 | INFO     | app.services.account_service:refresh_account_info:649 - 刷新账户信息: 2
2025-07-21 16:09:12.308 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 16:09:12.317 | INFO     | core.telegram.user_manager:get_user_info:41 - 正在获取用户信息: +***********
2025-07-21 16:09:13.286 | INFO     | core.telegram.user_manager:get_user_info:66 - 获取用户信息成功: +***********
2025-07-21 16:09:13.569 | INFO     | data.repositories.account_repo:update_account:394 - 更新账户成功: 2
2025-07-21 16:09:13.578 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 16:09:13.583 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 16:09:13.592 | INFO     | ui.views.account_view:_update_single_account_display:337 - 已实时更新账户显示: +***********
2025-07-21 16:09:13.592 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 16:09:15.520 | INFO     | app.services.account_service:refresh_account_info:649 - 刷新账户信息: 1
2025-07-21 16:09:15.520 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 16:09:15.534 | INFO     | core.telegram.user_manager:get_user_info:41 - 正在获取用户信息: +***********
2025-07-21 16:09:18.676 | INFO     | core.telegram.user_manager:get_user_info:66 - 获取用户信息成功: +***********
2025-07-21 16:09:19.542 | INFO     | data.repositories.account_repo:update_account:394 - 更新账户成功: 1
2025-07-21 16:09:19.548 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 16:09:19.553 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 16:09:19.561 | INFO     | ui.views.account_view:_update_single_account_display:337 - 已实时更新账户显示: +***********
2025-07-21 16:09:19.561 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 16:09:45.153 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-21 16:09:45.765 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 16:09:45.785 | INFO     | app.services.account_service:get_accounts_by_group:314 - 获取分组 1 的账户成功, 共 1 个
2025-07-21 16:09:45.785 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 16:09:45.786 | DEBUG    | ui.views.account_view:_on_accounts_loaded:652 - 账户加载完成: 1个账户
2025-07-21 16:09:46.299 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 16:09:46.309 | INFO     | app.services.account_service:get_accounts_by_group:314 - 获取分组 2 的账户成功, 共 1 个
2025-07-21 16:09:46.309 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 16:09:46.310 | DEBUG    | ui.views.account_view:_on_accounts_loaded:652 - 账户加载完成: 1个账户
2025-07-21 16:09:46.859 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 16:09:46.867 | INFO     | app.services.account_service:get_all_accounts:292 - 获取所有账户成功, 共 2 个
2025-07-21 16:09:46.867 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 16:09:46.869 | DEBUG    | ui.views.account_view:_on_accounts_loaded:652 - 账户加载完成: 2个账户
2025-07-21 16:09:46.890 | DEBUG    | ui.views.account_view:_update_group_counts:188 - 分组计数已更新: {2: 1, 1: 1}
2025-07-21 16:09:48.083 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 16:09:48.097 | INFO     | app.services.account_service:get_accounts_by_group:314 - 获取分组 2 的账户成功, 共 1 个
2025-07-21 16:09:48.097 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 16:09:48.100 | DEBUG    | ui.views.account_view:_on_accounts_loaded:652 - 账户加载完成: 1个账户
2025-07-21 16:09:48.701 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 16:09:48.712 | INFO     | app.services.account_service:get_accounts_by_group:314 - 获取分组 1 的账户成功, 共 1 个
2025-07-21 16:09:48.712 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 16:09:48.714 | DEBUG    | ui.views.account_view:_on_accounts_loaded:652 - 账户加载完成: 1个账户
2025-07-21 16:09:49.386 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 16:09:49.396 | INFO     | app.services.account_service:get_all_accounts:292 - 获取所有账户成功, 共 2 个
2025-07-21 16:09:49.396 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 16:09:49.397 | DEBUG    | ui.views.account_view:_on_accounts_loaded:652 - 账户加载完成: 2个账户
2025-07-21 16:09:49.418 | DEBUG    | ui.views.account_view:_update_group_counts:188 - 分组计数已更新: {2: 1, 1: 1}
2025-07-21 16:10:45.167 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-21 16:11:45.166 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-21 16:12:45.165 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-21 16:13:45.161 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-21 16:14:45.161 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-21 16:15:45.163 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-21 16:16:45.165 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-21 16:17:45.162 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-21 16:31:08.211 | INFO     | utils.logger:init_logger:98 - 日志系统初始化完成，默认级别: DEBUG，日志目录: H:\PyProject\TeleTest\APPDATA\logs
2025-07-21 16:31:09.366 | INFO     | data.database:init_database:31 - 初始化数据库连接: H:\PyProject\TeleTest\APPDATA\database\telegram_manager.db
2025-07-21 16:31:09.384 | DEBUG    | data.database:init_database:58 - 创建数据库表
2025-07-21 16:31:09.395 | INFO     | data.database:init_database:61 - 数据库初始化完成
2025-07-21 16:31:10.549 | DEBUG    | app.controllers.auth_controller:initialize_software:43 - 控制器: 初始化软件 app_id=1000, version=1.2.7
2025-07-21 16:31:10.550 | DEBUG    | core.auth.api_service:init_software:55 - 发送初始化请求: https://server.xile188.com/api/user/1000/windowns10/1.2.7/ini, params={}
2025-07-21 16:31:10.775 | DEBUG    | core.auth.api_service:init_software:66 - 初始化数据解密成功: {'new_content': '- 新增功能\n  - 1.新增图片上传功能\n  - 2.新增软件文档\n  - 3.新增软件自动更新，后续更新会自动检测版本更新，无需再下载更新包\n  - 4.新增设置账户信息及发信控制\n- 修复bug\n  - 删除监听任务时，不删除对应任务采集到的数据。\n  - 监听设置群无法发送到目标群\n  - 导出监听数据时应当导出单个任务而非所有数据\n- 优化记录\n  - 优化发信规则，直接调取符合要求的账户发送，无账户时才需要等待延时。\n  - 完善软件设置', 'new_url': None, 'notice': {'id': '2', 'content': '公告内容测试3', 'time': '**********'}}
2025-07-21 16:31:10.782 | INFO     | app.controllers.auth_controller:login:61 - 控制器: 用户登录 account=demo01
2025-07-21 16:31:13.962 | INFO     | core.auth.api_service:login:127 - 用户登录: account=demo01
2025-07-21 16:31:14.200 | INFO     | core.auth.api_service:get_user_info:347 - 获取用户信息
2025-07-21 16:31:14.503 | INFO     | core.auth.api_service:get_user_info:375 - 获取用户信息成功
2025-07-21 16:31:14.510 | INFO     | __main__:on_auth_completed:91 - 用户登录成功，正在启动主窗口...
2025-07-21 16:31:14.542 | INFO     | core.telegram.client_worker:__init__:61 - Telegram客户端工作线程初始化
2025-07-21 16:31:14.542 | INFO     | utils.vip_checker:initialize:25 - VIP检查器已初始化，等待登录后开始检查
2025-07-21 16:31:14.542 | INFO     | ui.main_window:_initialize_core_components:108 - MainWindow: 初始化核心组件...
2025-07-21 16:31:14.543 | INFO     | app.services.account_service:__init__:44 - 账户服务初始化
2025-07-21 16:31:14.543 | INFO     | core.telegram.client_worker:run:65 - Telegram客户端工作线程开始运行
2025-07-21 16:31:14.543 | INFO     | core.proxy.proxy_task_manager:run:284 - 代理任务管理器线程启动
2025-07-21 16:31:14.543 | INFO     | app.controllers.account_controller:__init__:82 - 账户控制器初始化
2025-07-21 16:31:14.544 | INFO     | app.services.monitor_service:__init__:39 - 初始化监控任务服务
2025-07-21 16:31:14.544 | INFO     | core.monitor.filter:__init__:26 - 初始化消息过滤器
2025-07-21 16:31:14.544 | INFO     | core.monitor.extractor:__init__:24 - 初始化关键词提取器
2025-07-21 16:31:14.544 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-07-21 16:31:14.544 | INFO     | core.monitor.notifier:__init__:90 - 初始化通知管理器
2025-07-21 16:31:14.545 | INFO     | app.controllers.telegram_monitor_controller:__init__:64 - 初始化 Telegram 监控控制器
2025-07-21 16:31:14.545 | INFO     | core.telegram.client_manager:__init__:66 - Telegram客户端管理器初始化
2025-07-21 16:31:14.545 | DEBUG    | app.controllers.telegram_monitor_controller:__init__:82 - 控制器初始化完成
2025-07-21 16:31:14.545 | INFO     | core.telegram.user_manager:__init__:30 - Telegram用户管理器初始化
2025-07-21 16:31:14.546 | INFO     | core.telegram.group_manager:__init__:36 - Telegram群组管理器初始化
2025-07-21 16:31:14.547 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-07-21 16:31:14.547 | INFO     | core.telegram.monitor_manager:__init__:26 - Telegram监控管理器初始化
2025-07-21 16:31:14.547 | INFO     | core.telegram.telegram_service:__init__:39 - Telegram服务初始化完成
2025-07-21 16:31:14.821 | INFO     | ui.views.monitor_view:__init__:48 - 初始化监控视图
2025-07-21 16:31:14.821 | DEBUG    | ui.views.monitor_view:_connect_signals:80 - 连接控制器信号
2025-07-21 16:31:15.114 | INFO     | ui.views.proxy_view:__init__:96 - 初始化视图，加载第一页代理数据
2025-07-21 16:31:15.403 | INFO     | __main__:on_auth_completed:95 - 主窗口已启动
2025-07-21 16:31:15.457 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:88 - 尝试连接监听日志信号...
2025-07-21 16:31:15.458 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:90 - 监听日志信号连接成功
2025-07-21 16:31:15.458 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 16:31:15.462 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 16:31:15.467 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:145 - 加载监控任务列表 (搜索关键词: '')
2025-07-21 16:31:15.467 | DEBUG    | ui.views.monitor_view:_on_loading_started:620 - 加载中: 正在加载任务列表...
2025-07-21 16:31:15.467 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 16:31:15.474 | INFO     | app.services.proxy_service:refresh_proxies:712 - 刷新代理列表: offset=0, limit=20
2025-07-21 16:31:15.475 | INFO     | core.proxy.proxy_task_manager:submit_task:172 - 提交耗时任务到子线程: refresh_proxies_0_20
2025-07-21 16:31:15.475 | DEBUG    | app.services.proxy_service:get_proxies:500 - 获取代理列表: offset=0, limit=20
2025-07-21 16:31:15.475 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 16:31:15.475 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 16:31:15.485 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 16:31:15.514 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 16:31:15.526 | INFO     | core.proxy.proxy_task_manager:task_wrapper:199 - 任务 refresh_proxies_0_20 完成
2025-07-21 16:31:15.686 | INFO     | app.controllers.invite_controller:get_invite_tasks:38 - 获取邀请任务列表, status=None
2025-07-21 16:31:15.686 | INFO     | app.services.invite_service:get_invite_tasks:121 - 获取邀请任务列表, status=None
2025-07-21 16:31:15.687 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 16:31:15.687 | INFO     | app.services.invite_service:task_initialize:39 - 初始化邀请服务，重置运行中的任务为暂停状态
2025-07-21 16:31:15.688 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 16:31:15.692 | INFO     | utils.vip_checker:start_checking:37 - 开始VIP状态检查
2025-07-21 16:31:15.692 | DEBUG    | utils.vip_checker:_check_vip_status:61 - 正在检查VIP状态...
2025-07-21 16:31:15.692 | INFO     | core.auth.api_service:verify_vip:425 - 会员验证接口调用
2025-07-21 16:31:16.005 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 16:31:16.006 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 16:31:16.014 | INFO     | app.services.invite_service:task_initialize:48 - 成功重置 0 个任务状态
2025-07-21 16:31:16.015 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 16:31:16.017 | DEBUG    | ui.views.monitor_view:_refresh_task_list:175 - 刷新任务列表，共0个任务
2025-07-21 16:31:16.018 | INFO     | ui.views.monitor_view:_refresh_task_list:189 - 没有找到监控任务
2025-07-21 16:31:16.018 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 16:31:16.025 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 16:31:16.026 | INFO     | app.services.account_service:get_all_groups:98 - 获取所有账户分组成功, 共 2 个
2025-07-21 16:31:16.026 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 16:31:16.028 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 16:31:16.030 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 16:31:16.032 | DEBUG    | ui.views.account_view:_on_groups_loaded:519 - 分组加载完成: 2个分组
2025-07-21 16:31:16.056 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 16:31:16.235 | INFO     | core.auth.api_service:verify_vip:438 - 会员验证成功
2025-07-21 16:31:16.235 | INFO     | utils.vip_checker:_check_vip_status:76 - VIP状态有效
2025-07-21 16:31:16.237 | INFO     | app.services.account_service:get_all_accounts:292 - 获取所有账户成功, 共 2 个
2025-07-21 16:31:16.237 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 16:31:16.239 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 16:31:16.241 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 16:31:16.242 | DEBUG    | ui.views.account_view:_on_accounts_loaded:652 - 账户加载完成: 2个账户
2025-07-21 16:31:16.273 | DEBUG    | ui.views.account_view:_update_group_counts:188 - 分组计数已更新: {2: 1, 1: 1}
2025-07-21 16:31:16.278 | INFO     | ui.views.account_view:_auto_login_accounts:707 - 开始调用控制器进行 2 个账户的自动登录。
2025-07-21 16:31:16.278 | INFO     | app.services.account_service:batch_auto_login:1237 - 服务层：开始批量自动登录 2 个账户, 最大并发: 5
2025-07-21 16:31:16.278 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 16:31:16.284 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 16:31:16.286 | INFO     | app.controllers.telegram_monitor_controller:_load_total_users_count_internal:191 - 成功加载总体统计数据: 总用户数 23, 今日采集 0, 日均采集 23, 运行天数 1
2025-07-21 16:31:16.286 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:156 - 成功加载 0 个任务
2025-07-21 16:31:16.287 | DEBUG    | ui.views.monitor_view:_on_loading_finished:625 - 加载完成
2025-07-21 16:31:16.295 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-07-21 16:31:16.311 | INFO     | app.services.account_service:get_all_accounts:292 - 获取所有账户成功, 共 2 个
2025-07-21 16:31:16.311 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 16:31:16.312 | INFO     | app.services.account_service:batch_auto_login:1276 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-07-21 16:31:16.312 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 16:31:16.314 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 16:31:16.316 | DEBUG    | ui.views.account_view:_update_group_counts:188 - 分组计数已更新: {2: 1, 1: 1}
2025-07-21 16:31:16.316 | DEBUG    | ui.views.account_view:_init_cache:155 - 账户缓存初始化完成，共 2 个账户
2025-07-21 16:31:16.323 | DEBUG    | ui.views.account_view:_on_accounts_loaded:652 - 账户加载完成: 2个账户
2025-07-21 16:31:16.353 | DEBUG    | ui.views.account_view:_update_group_counts:188 - 分组计数已更新: {2: 1, 1: 1}
2025-07-21 16:31:16.375 | INFO     | app.services.account_service:batch_auto_login:1276 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-07-21 16:31:16.375 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 16:31:16.377 | INFO     | app.services.account_service:batch_auto_login:1346 - 服务层：准备调用核心层处理 2 个账户的自动登录。
2025-07-21 16:31:16.377 | INFO     | app.services.account_service:batch_auto_login:1356 - 服务层：设置核心层任务超时为 120 秒。
2025-07-21 16:31:16.377 | INFO     | core.telegram.client_manager:batch_auto_login:1230 - 开始批量自动登录, 共 2 个账户, 最大并发 5
2025-07-21 16:31:16.378 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:271 - 创建客户端: H:\PyProject\TeleTest\APPDATA\sessions\+***********, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-21 16:31:16.378 | DEBUG    | core.telegram.client_manager:get_proxy:96 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-21 16:31:16.378 | DEBUG    | core.telegram.client_manager:get_proxy:143 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-07-21 16:31:16.385 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:271 - 创建客户端: H:\PyProject\TeleTest\APPDATA\sessions\+***********, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-21 16:31:16.385 | DEBUG    | core.telegram.client_manager:get_proxy:96 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-21 16:31:16.386 | DEBUG    | core.telegram.client_manager:get_proxy:143 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-07-21 16:31:16.417 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 16:31:16.545 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 16:31:16.624 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-07-21 16:31:16.626 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-07-21 16:31:16.629 | INFO     | ui.views.proxy_view:_load_initial_data:1044 - 初始数据加载完成，共 1 个代理
2025-07-21 16:31:16.662 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 16:31:19.795 | INFO     | core.telegram.client_manager:_connect_client:219 - 连接成功!
2025-07-21 16:31:20.759 | INFO     | core.telegram.client_manager:_verify_client_authorization:1205 - 账户已授权: +***********
2025-07-21 16:31:24.637 | INFO     | core.telegram.client_manager:_connect_client:219 - 连接成功!
2025-07-21 16:31:25.541 | INFO     | core.telegram.client_manager:_verify_client_authorization:1205 - 账户已授权: +***********
2025-07-21 16:31:27.553 | INFO     | core.telegram.client_manager:batch_auto_login:1379 - 批量登录完成: 总计 2, 成功 2, 失败 0
2025-07-21 16:31:28.413 | INFO     | app.services.account_service:batch_auto_login:1377 - 服务层：核心层登录完成: 成功 2, 失败 0 / 2
2025-07-21 16:31:28.413 | INFO     | app.services.account_service:_process_batch_login_results:1739 - 开始处理批量登录结果，共 2 个账户
2025-07-21 16:31:28.413 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 16:31:28.422 | INFO     | data.repositories.account_repo:update_account:394 - 更新账户成功: 2
2025-07-21 16:31:28.422 | INFO     | app.services.account_service:_process_batch_login_results:1775 - 已更新账户 +*********** 的用户信息
2025-07-21 16:31:28.430 | INFO     | data.repositories.account_repo:update_account:394 - 更新账户成功: 1
2025-07-21 16:31:28.430 | INFO     | app.services.account_service:_process_batch_login_results:1775 - 已更新账户 +*********** 的用户信息
2025-07-21 16:31:28.440 | INFO     | app.services.account_service:_process_batch_login_results:1798 - 批量登录结果处理完成，数据库已更新
2025-07-21 16:31:28.440 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 16:31:28.440 | INFO     | app.controllers.account_controller:auto_login_accounts:640 - 批量登录完成，触发账户数据刷新
2025-07-21 16:32:02.959 | INFO     | app.services.account_service:update_account_profile:495 - 更新账户资料: 2
2025-07-21 16:32:02.959 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 16:32:03.206 | INFO     | app.services.account_service:_generate_unique_username:1662 - 生成唯一用户名成功: tianyi499 (尝试 1/5)
2025-07-21 16:32:03.207 | INFO     | core.telegram.user_manager:update_profile:89 - 正在更新用户资料: +***********
2025-07-21 16:32:08.966 | INFO     | core.telegram.user_manager:update_profile:130 - 更新用户资料成功: +***********
2025-07-21 16:32:09.241 | INFO     | data.repositories.account_repo:update_account:394 - 更新账户成功: 2
2025-07-21 16:32:09.253 | INFO     | app.services.account_service:update_account_profile:559 - 更新账户 +*********** 消息限制为: 10
2025-07-21 16:32:09.256 | ERROR    | app.services.account_service:update_account_profile:572 - 更新邀请限制失败: InviteService.__init__() missing 1 required positional argument: 'client_worker'
2025-07-21 16:32:09.257 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 16:32:14.496 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-21 16:33:14.490 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-21 16:34:12.839 | INFO     | utils.logger:init_logger:98 - 日志系统初始化完成，默认级别: DEBUG，日志目录: H:\PyProject\TeleTest\APPDATA\logs
2025-07-21 16:34:13.996 | INFO     | data.database:init_database:31 - 初始化数据库连接: H:\PyProject\TeleTest\APPDATA\database\telegram_manager.db
2025-07-21 16:34:14.015 | DEBUG    | data.database:init_database:58 - 创建数据库表
2025-07-21 16:34:14.024 | INFO     | data.database:init_database:61 - 数据库初始化完成
2025-07-21 16:34:14.811 | DEBUG    | app.controllers.auth_controller:initialize_software:43 - 控制器: 初始化软件 app_id=1000, version=1.2.7
2025-07-21 16:34:14.811 | DEBUG    | core.auth.api_service:init_software:55 - 发送初始化请求: https://server.xile188.com/api/user/1000/windowns10/1.2.7/ini, params={}
2025-07-21 16:34:15.055 | DEBUG    | core.auth.api_service:init_software:66 - 初始化数据解密成功: {'new_content': '- 新增功能\n  - 1.新增图片上传功能\n  - 2.新增软件文档\n  - 3.新增软件自动更新，后续更新会自动检测版本更新，无需再下载更新包\n  - 4.新增设置账户信息及发信控制\n- 修复bug\n  - 删除监听任务时，不删除对应任务采集到的数据。\n  - 监听设置群无法发送到目标群\n  - 导出监听数据时应当导出单个任务而非所有数据\n- 优化记录\n  - 优化发信规则，直接调取符合要求的账户发送，无账户时才需要等待延时。\n  - 完善软件设置', 'new_url': None, 'notice': {'id': '2', 'content': '公告内容测试3', 'time': '**********'}}
2025-07-21 16:34:15.061 | INFO     | app.controllers.auth_controller:login:61 - 控制器: 用户登录 account=demo01
2025-07-21 16:34:18.224 | INFO     | core.auth.api_service:login:127 - 用户登录: account=demo01
2025-07-21 16:34:18.476 | INFO     | core.auth.api_service:get_user_info:347 - 获取用户信息
2025-07-21 16:34:18.716 | INFO     | core.auth.api_service:get_user_info:375 - 获取用户信息成功
2025-07-21 16:34:18.722 | INFO     | __main__:on_auth_completed:91 - 用户登录成功，正在启动主窗口...
2025-07-21 16:34:18.746 | INFO     | core.telegram.client_worker:__init__:61 - Telegram客户端工作线程初始化
2025-07-21 16:34:18.746 | INFO     | utils.vip_checker:initialize:25 - VIP检查器已初始化，等待登录后开始检查
2025-07-21 16:34:18.747 | INFO     | ui.main_window:_initialize_core_components:108 - MainWindow: 初始化核心组件...
2025-07-21 16:34:18.747 | INFO     | app.services.account_service:__init__:44 - 账户服务初始化
2025-07-21 16:34:18.747 | INFO     | app.controllers.account_controller:__init__:82 - 账户控制器初始化
2025-07-21 16:34:18.748 | INFO     | app.services.monitor_service:__init__:39 - 初始化监控任务服务
2025-07-21 16:34:18.748 | INFO     | core.monitor.filter:__init__:26 - 初始化消息过滤器
2025-07-21 16:34:18.748 | INFO     | core.monitor.extractor:__init__:24 - 初始化关键词提取器
2025-07-21 16:34:18.748 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-07-21 16:34:18.748 | INFO     | core.monitor.notifier:__init__:90 - 初始化通知管理器
2025-07-21 16:34:18.748 | INFO     | app.controllers.telegram_monitor_controller:__init__:64 - 初始化 Telegram 监控控制器
2025-07-21 16:34:18.748 | DEBUG    | app.controllers.telegram_monitor_controller:__init__:82 - 控制器初始化完成
2025-07-21 16:34:18.748 | INFO     | core.proxy.proxy_task_manager:run:284 - 代理任务管理器线程启动
2025-07-21 16:34:18.748 | INFO     | core.telegram.client_worker:run:65 - Telegram客户端工作线程开始运行
2025-07-21 16:34:18.755 | INFO     | core.telegram.client_manager:__init__:66 - Telegram客户端管理器初始化
2025-07-21 16:34:18.755 | INFO     | core.telegram.user_manager:__init__:30 - Telegram用户管理器初始化
2025-07-21 16:34:18.756 | INFO     | core.telegram.group_manager:__init__:36 - Telegram群组管理器初始化
2025-07-21 16:34:18.759 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-07-21 16:34:18.759 | INFO     | core.telegram.monitor_manager:__init__:26 - Telegram监控管理器初始化
2025-07-21 16:34:18.760 | INFO     | core.telegram.telegram_service:__init__:39 - Telegram服务初始化完成
2025-07-21 16:34:19.001 | INFO     | ui.views.monitor_view:__init__:48 - 初始化监控视图
2025-07-21 16:34:19.001 | DEBUG    | ui.views.monitor_view:_connect_signals:80 - 连接控制器信号
2025-07-21 16:34:19.198 | INFO     | ui.views.proxy_view:__init__:96 - 初始化视图，加载第一页代理数据
2025-07-21 16:34:19.472 | INFO     | __main__:on_auth_completed:95 - 主窗口已启动
2025-07-21 16:34:19.517 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:88 - 尝试连接监听日志信号...
2025-07-21 16:34:19.517 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:90 - 监听日志信号连接成功
2025-07-21 16:34:19.517 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 16:34:19.521 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 16:34:19.525 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:145 - 加载监控任务列表 (搜索关键词: '')
2025-07-21 16:34:19.525 | DEBUG    | ui.views.monitor_view:_on_loading_started:620 - 加载中: 正在加载任务列表...
2025-07-21 16:34:19.525 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 16:34:19.531 | INFO     | app.services.proxy_service:refresh_proxies:712 - 刷新代理列表: offset=0, limit=20
2025-07-21 16:34:19.531 | INFO     | core.proxy.proxy_task_manager:submit_task:172 - 提交耗时任务到子线程: refresh_proxies_0_20
2025-07-21 16:34:19.531 | DEBUG    | app.services.proxy_service:get_proxies:500 - 获取代理列表: offset=0, limit=20
2025-07-21 16:34:19.532 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 16:34:19.532 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 16:34:19.535 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 16:34:19.537 | INFO     | app.controllers.invite_controller:get_invite_tasks:38 - 获取邀请任务列表, status=None
2025-07-21 16:34:19.543 | INFO     | app.services.invite_service:get_invite_tasks:121 - 获取邀请任务列表, status=None
2025-07-21 16:34:19.549 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 16:34:19.561 | INFO     | app.services.invite_service:task_initialize:39 - 初始化邀请服务，重置运行中的任务为暂停状态
2025-07-21 16:34:19.561 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 16:34:19.566 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 16:34:19.567 | INFO     | utils.vip_checker:start_checking:37 - 开始VIP状态检查
2025-07-21 16:34:19.567 | DEBUG    | utils.vip_checker:_check_vip_status:61 - 正在检查VIP状态...
2025-07-21 16:34:19.567 | INFO     | core.auth.api_service:verify_vip:425 - 会员验证接口调用
2025-07-21 16:34:19.568 | INFO     | core.proxy.proxy_task_manager:task_wrapper:199 - 任务 refresh_proxies_0_20 完成
2025-07-21 16:34:19.709 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 16:34:19.714 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 16:34:19.730 | INFO     | app.services.invite_service:task_initialize:48 - 成功重置 0 个任务状态
2025-07-21 16:34:19.731 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 16:34:19.734 | DEBUG    | ui.views.monitor_view:_refresh_task_list:175 - 刷新任务列表，共0个任务
2025-07-21 16:34:19.734 | INFO     | ui.views.monitor_view:_refresh_task_list:189 - 没有找到监控任务
2025-07-21 16:34:19.735 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 16:34:19.744 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 16:34:19.750 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 16:34:19.752 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 16:34:19.755 | INFO     | app.services.account_service:get_all_groups:98 - 获取所有账户分组成功, 共 2 个
2025-07-21 16:34:19.755 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 16:34:19.856 | DEBUG    | ui.views.account_view:_on_groups_loaded:519 - 分组加载完成: 2个分组
2025-07-21 16:34:19.881 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 16:34:20.000 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 16:34:20.002 | INFO     | app.services.account_service:get_all_accounts:292 - 获取所有账户成功, 共 2 个
2025-07-21 16:34:20.002 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 16:34:20.004 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 16:34:20.010 | INFO     | app.controllers.telegram_monitor_controller:_load_total_users_count_internal:191 - 成功加载总体统计数据: 总用户数 23, 今日采集 0, 日均采集 23, 运行天数 1
2025-07-21 16:34:20.010 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:156 - 成功加载 0 个任务
2025-07-21 16:34:20.010 | DEBUG    | ui.views.monitor_view:_on_loading_finished:625 - 加载完成
2025-07-21 16:34:20.011 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 16:34:20.013 | DEBUG    | ui.views.account_view:_on_accounts_loaded:652 - 账户加载完成: 2个账户
2025-07-21 16:34:20.040 | DEBUG    | ui.views.account_view:_update_group_counts:188 - 分组计数已更新: {2: 1, 1: 1}
2025-07-21 16:34:20.046 | INFO     | ui.views.account_view:_auto_login_accounts:707 - 开始调用控制器进行 2 个账户的自动登录。
2025-07-21 16:34:20.047 | INFO     | app.services.account_service:batch_auto_login:1207 - 服务层：开始批量自动登录 2 个账户, 最大并发: 5
2025-07-21 16:34:20.047 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 16:34:20.052 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-07-21 16:34:20.070 | INFO     | core.auth.api_service:verify_vip:438 - 会员验证成功
2025-07-21 16:34:20.071 | INFO     | utils.vip_checker:_check_vip_status:76 - VIP状态有效
2025-07-21 16:34:20.083 | INFO     | app.services.account_service:batch_auto_login:1246 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-07-21 16:34:20.083 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 16:34:20.084 | INFO     | app.services.account_service:get_all_accounts:292 - 获取所有账户成功, 共 2 个
2025-07-21 16:34:20.085 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 16:34:20.090 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 16:34:20.093 | DEBUG    | ui.views.account_view:_update_group_counts:188 - 分组计数已更新: {2: 1, 1: 1}
2025-07-21 16:34:20.093 | DEBUG    | ui.views.account_view:_init_cache:155 - 账户缓存初始化完成，共 2 个账户
2025-07-21 16:34:20.101 | DEBUG    | ui.views.account_view:_on_accounts_loaded:652 - 账户加载完成: 2个账户
2025-07-21 16:34:20.145 | DEBUG    | ui.views.account_view:_update_group_counts:188 - 分组计数已更新: {2: 1, 1: 1}
2025-07-21 16:34:20.182 | INFO     | app.services.account_service:batch_auto_login:1246 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-07-21 16:34:20.182 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 16:34:20.186 | INFO     | app.services.account_service:batch_auto_login:1316 - 服务层：准备调用核心层处理 2 个账户的自动登录。
2025-07-21 16:34:20.186 | INFO     | app.services.account_service:batch_auto_login:1326 - 服务层：设置核心层任务超时为 120 秒。
2025-07-21 16:34:20.187 | INFO     | core.telegram.client_manager:batch_auto_login:1230 - 开始批量自动登录, 共 2 个账户, 最大并发 5
2025-07-21 16:34:20.190 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:271 - 创建客户端: H:\PyProject\TeleTest\APPDATA\sessions\+***********, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-21 16:34:20.190 | DEBUG    | core.telegram.client_manager:get_proxy:96 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-21 16:34:20.191 | DEBUG    | core.telegram.client_manager:get_proxy:143 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-07-21 16:34:20.196 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:271 - 创建客户端: H:\PyProject\TeleTest\APPDATA\sessions\+***********, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-21 16:34:20.196 | DEBUG    | core.telegram.client_manager:get_proxy:96 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-21 16:34:20.197 | DEBUG    | core.telegram.client_manager:get_proxy:143 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-07-21 16:34:20.262 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 16:34:20.384 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 16:34:20.405 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-07-21 16:34:20.407 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-07-21 16:34:20.410 | INFO     | ui.views.proxy_view:_load_initial_data:1044 - 初始数据加载完成，共 1 个代理
2025-07-21 16:34:20.425 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 16:34:23.404 | INFO     | core.telegram.client_manager:_connect_client:219 - 连接成功!
2025-07-21 16:34:24.272 | INFO     | core.telegram.client_manager:_verify_client_authorization:1205 - 账户已授权: +***********
2025-07-21 16:34:24.473 | INFO     | core.telegram.client_manager:_connect_client:219 - 连接成功!
2025-07-21 16:34:25.290 | INFO     | core.telegram.client_manager:_verify_client_authorization:1205 - 账户已授权: +***********
2025-07-21 16:34:27.309 | INFO     | core.telegram.client_manager:batch_auto_login:1379 - 批量登录完成: 总计 2, 成功 2, 失败 0
2025-07-21 16:34:28.240 | INFO     | app.services.account_service:batch_auto_login:1347 - 服务层：核心层登录完成: 成功 2, 失败 0 / 2
2025-07-21 16:34:28.240 | INFO     | app.services.account_service:_process_batch_login_results:1709 - 开始处理批量登录结果，共 2 个账户
2025-07-21 16:34:28.241 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 16:34:28.247 | INFO     | data.repositories.account_repo:update_account:394 - 更新账户成功: 2
2025-07-21 16:34:28.247 | INFO     | app.services.account_service:_process_batch_login_results:1745 - 已更新账户 +*********** 的用户信息
2025-07-21 16:34:28.253 | INFO     | data.repositories.account_repo:update_account:394 - 更新账户成功: 1
2025-07-21 16:34:28.253 | INFO     | app.services.account_service:_process_batch_login_results:1745 - 已更新账户 +*********** 的用户信息
2025-07-21 16:34:28.260 | INFO     | app.services.account_service:_process_batch_login_results:1768 - 批量登录结果处理完成，数据库已更新
2025-07-21 16:34:28.260 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 16:34:28.260 | INFO     | app.controllers.account_controller:auto_login_accounts:640 - 批量登录完成，触发账户数据刷新
2025-07-21 16:34:56.271 | INFO     | app.services.account_service:refresh_account_info:649 - 刷新账户信息: 1
2025-07-21 16:34:56.271 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 16:34:56.281 | INFO     | core.telegram.user_manager:get_user_info:41 - 正在获取用户信息: +***********
2025-07-21 16:34:57.533 | INFO     | core.telegram.user_manager:get_user_info:66 - 获取用户信息成功: +***********
2025-07-21 16:34:58.292 | INFO     | data.repositories.account_repo:update_account:394 - 更新账户成功: 1
2025-07-21 16:34:58.298 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 16:34:58.302 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 16:34:58.315 | INFO     | ui.views.account_view:_update_single_account_display:337 - 已实时更新账户显示: +***********
2025-07-21 16:34:58.315 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 16:35:00.283 | INFO     | app.services.account_service:refresh_account_info:649 - 刷新账户信息: 2
2025-07-21 16:35:00.283 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 16:35:00.296 | INFO     | core.telegram.user_manager:get_user_info:41 - 正在获取用户信息: +***********
2025-07-21 16:35:01.487 | INFO     | core.telegram.user_manager:get_user_info:66 - 获取用户信息成功: +***********
2025-07-21 16:35:01.545 | INFO     | data.repositories.account_repo:update_account:394 - 更新账户成功: 2
2025-07-21 16:35:01.551 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 16:35:01.555 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 16:35:01.563 | INFO     | ui.views.account_view:_update_single_account_display:337 - 已实时更新账户显示: +***********
2025-07-21 16:35:01.563 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 16:35:18.724 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-21 16:35:49.791 | INFO     | app.services.account_service:update_account_profile:493 - 更新账户资料: 2
2025-07-21 16:35:49.791 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 16:35:49.835 | INFO     | app.services.account_service:_generate_unique_username:1632 - 生成唯一用户名成功: tianyi49940 (尝试 1/5)
2025-07-21 16:35:49.835 | INFO     | core.telegram.user_manager:update_profile:89 - 正在更新用户资料: +***********
2025-07-21 16:35:55.150 | INFO     | core.telegram.user_manager:update_profile:130 - 更新用户资料成功: +***********
2025-07-21 16:35:55.852 | INFO     | data.repositories.account_repo:update_account:394 - 更新账户成功: 2
2025-07-21 16:35:55.859 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 16:35:57.887 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 16:35:57.965 | INFO     | app.services.account_service:get_all_accounts:292 - 获取所有账户成功, 共 2 个
2025-07-21 16:35:57.965 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 16:35:57.966 | DEBUG    | ui.views.account_view:_on_accounts_loaded:652 - 账户加载完成: 2个账户
2025-07-21 16:35:57.989 | DEBUG    | ui.views.account_view:_update_group_counts:188 - 分组计数已更新: {2: 1, 1: 1}
2025-07-21 16:36:14.432 | INFO     | app.services.account_service:refresh_account_info:649 - 刷新账户信息: 2
2025-07-21 16:36:14.432 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 16:36:14.441 | INFO     | core.telegram.user_manager:get_user_info:41 - 正在获取用户信息: +***********
2025-07-21 16:36:15.303 | INFO     | core.telegram.user_manager:get_user_info:66 - 获取用户信息成功: +***********
2025-07-21 16:36:15.681 | INFO     | data.repositories.account_repo:update_account:394 - 更新账户成功: 2
2025-07-21 16:36:15.689 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 16:36:15.696 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 16:36:15.704 | INFO     | ui.views.account_view:_update_single_account_display:337 - 已实时更新账户显示: +***********
2025-07-21 16:36:15.704 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 16:36:18.708 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-21 16:36:43.037 | INFO     | app.services.account_service:update_account_profile:493 - 更新账户资料: 2
2025-07-21 16:36:43.038 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 16:36:43.069 | INFO     | app.services.account_service:_generate_unique_username:1632 - 生成唯一用户名成功: tianyi49940p (尝试 1/5)
2025-07-21 16:36:43.070 | INFO     | core.telegram.user_manager:update_profile:89 - 正在更新用户资料: +***********
2025-07-21 16:36:48.907 | INFO     | core.telegram.user_manager:update_profile:130 - 更新用户资料成功: +***********
2025-07-21 16:36:49.085 | INFO     | data.repositories.account_repo:update_account:394 - 更新账户成功: 2
2025-07-21 16:36:49.093 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 16:36:51.119 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 16:36:51.125 | INFO     | app.services.account_service:get_all_accounts:292 - 获取所有账户成功, 共 2 个
2025-07-21 16:36:51.125 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 16:36:51.128 | DEBUG    | ui.views.account_view:_on_accounts_loaded:652 - 账户加载完成: 2个账户
2025-07-21 16:36:51.148 | DEBUG    | ui.views.account_view:_update_group_counts:188 - 分组计数已更新: {2: 1, 1: 1}
2025-07-21 16:37:03.766 | INFO     | app.services.account_service:refresh_account_info:649 - 刷新账户信息: 2
2025-07-21 16:37:03.766 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 16:37:03.775 | INFO     | core.telegram.user_manager:get_user_info:41 - 正在获取用户信息: +***********
2025-07-21 16:37:05.062 | INFO     | core.telegram.user_manager:get_user_info:66 - 获取用户信息成功: +***********
2025-07-21 16:37:05.789 | INFO     | data.repositories.account_repo:update_account:394 - 更新账户成功: 2
2025-07-21 16:37:05.796 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 16:37:05.800 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 16:37:05.807 | INFO     | ui.views.account_view:_update_single_account_display:337 - 已实时更新账户显示: +***********
2025-07-21 16:37:05.807 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 16:37:18.710 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-21 16:40:58.260 | INFO     | utils.logger:init_logger:98 - 日志系统初始化完成，默认级别: DEBUG，日志目录: H:\PyProject\TeleTest\APPDATA\logs
2025-07-21 16:40:59.481 | INFO     | data.database:init_database:31 - 初始化数据库连接: H:\PyProject\TeleTest\APPDATA\database\telegram_manager.db
2025-07-21 16:40:59.504 | DEBUG    | data.database:init_database:58 - 创建数据库表
2025-07-21 16:40:59.522 | INFO     | data.database:init_database:61 - 数据库初始化完成
2025-07-21 16:41:00.415 | DEBUG    | app.controllers.auth_controller:initialize_software:43 - 控制器: 初始化软件 app_id=1000, version=1.2.7
2025-07-21 16:41:00.415 | DEBUG    | core.auth.api_service:init_software:55 - 发送初始化请求: https://server.xile188.com/api/user/1000/windowns10/1.2.7/ini, params={}
2025-07-21 16:41:00.664 | DEBUG    | core.auth.api_service:init_software:66 - 初始化数据解密成功: {'new_content': '- 新增功能\n  - 1.新增图片上传功能\n  - 2.新增软件文档\n  - 3.新增软件自动更新，后续更新会自动检测版本更新，无需再下载更新包\n  - 4.新增设置账户信息及发信控制\n- 修复bug\n  - 删除监听任务时，不删除对应任务采集到的数据。\n  - 监听设置群无法发送到目标群\n  - 导出监听数据时应当导出单个任务而非所有数据\n- 优化记录\n  - 优化发信规则，直接调取符合要求的账户发送，无账户时才需要等待延时。\n  - 完善软件设置', 'new_url': None, 'notice': {'id': '2', 'content': '公告内容测试3', 'time': '**********'}}
2025-07-21 16:41:00.671 | INFO     | app.controllers.auth_controller:login:61 - 控制器: 用户登录 account=demo01
2025-07-21 16:41:03.547 | INFO     | core.auth.api_service:login:127 - 用户登录: account=demo01
2025-07-21 16:41:03.754 | INFO     | core.auth.api_service:get_user_info:347 - 获取用户信息
2025-07-21 16:41:03.983 | INFO     | core.auth.api_service:get_user_info:375 - 获取用户信息成功
2025-07-21 16:41:03.990 | INFO     | __main__:on_auth_completed:91 - 用户登录成功，正在启动主窗口...
2025-07-21 16:41:04.015 | INFO     | core.telegram.client_worker:__init__:61 - Telegram客户端工作线程初始化
2025-07-21 16:41:04.015 | INFO     | utils.vip_checker:initialize:25 - VIP检查器已初始化，等待登录后开始检查
2025-07-21 16:41:04.015 | INFO     | ui.main_window:_initialize_core_components:108 - MainWindow: 初始化核心组件...
2025-07-21 16:41:04.016 | INFO     | core.telegram.client_worker:run:65 - Telegram客户端工作线程开始运行
2025-07-21 16:41:04.016 | INFO     | app.services.account_service:__init__:44 - 账户服务初始化
2025-07-21 16:41:04.017 | INFO     | core.proxy.proxy_task_manager:run:284 - 代理任务管理器线程启动
2025-07-21 16:41:04.017 | INFO     | app.controllers.account_controller:__init__:82 - 账户控制器初始化
2025-07-21 16:41:04.017 | INFO     | app.services.monitor_service:__init__:39 - 初始化监控任务服务
2025-07-21 16:41:04.017 | INFO     | core.monitor.filter:__init__:26 - 初始化消息过滤器
2025-07-21 16:41:04.017 | INFO     | core.monitor.extractor:__init__:24 - 初始化关键词提取器
2025-07-21 16:41:04.017 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-07-21 16:41:04.017 | INFO     | core.monitor.notifier:__init__:90 - 初始化通知管理器
2025-07-21 16:41:04.018 | INFO     | app.controllers.telegram_monitor_controller:__init__:64 - 初始化 Telegram 监控控制器
2025-07-21 16:41:04.018 | DEBUG    | app.controllers.telegram_monitor_controller:__init__:82 - 控制器初始化完成
2025-07-21 16:41:04.018 | INFO     | core.telegram.client_manager:__init__:66 - Telegram客户端管理器初始化
2025-07-21 16:41:04.019 | INFO     | core.telegram.user_manager:__init__:30 - Telegram用户管理器初始化
2025-07-21 16:41:04.019 | INFO     | core.telegram.group_manager:__init__:36 - Telegram群组管理器初始化
2025-07-21 16:41:04.020 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-07-21 16:41:04.020 | INFO     | core.telegram.monitor_manager:__init__:26 - Telegram监控管理器初始化
2025-07-21 16:41:04.020 | INFO     | core.telegram.telegram_service:__init__:39 - Telegram服务初始化完成
2025-07-21 16:41:04.232 | INFO     | ui.views.monitor_view:__init__:48 - 初始化监控视图
2025-07-21 16:41:04.232 | DEBUG    | ui.views.monitor_view:_connect_signals:80 - 连接控制器信号
2025-07-21 16:41:04.512 | INFO     | ui.views.proxy_view:__init__:96 - 初始化视图，加载第一页代理数据
2025-07-21 16:41:04.825 | INFO     | __main__:on_auth_completed:95 - 主窗口已启动
2025-07-21 16:41:04.871 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:88 - 尝试连接监听日志信号...
2025-07-21 16:41:04.871 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:90 - 监听日志信号连接成功
2025-07-21 16:41:04.872 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 16:41:04.875 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 16:41:04.879 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:145 - 加载监控任务列表 (搜索关键词: '')
2025-07-21 16:41:04.879 | DEBUG    | ui.views.monitor_view:_on_loading_started:620 - 加载中: 正在加载任务列表...
2025-07-21 16:41:04.879 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 16:41:04.886 | INFO     | app.services.proxy_service:refresh_proxies:712 - 刷新代理列表: offset=0, limit=20
2025-07-21 16:41:04.887 | INFO     | core.proxy.proxy_task_manager:submit_task:172 - 提交耗时任务到子线程: refresh_proxies_0_20
2025-07-21 16:41:04.887 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 16:41:04.888 | DEBUG    | app.services.proxy_service:get_proxies:500 - 获取代理列表: offset=0, limit=20
2025-07-21 16:41:04.888 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 16:41:04.892 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 16:41:04.913 | INFO     | app.controllers.invite_controller:get_invite_tasks:38 - 获取邀请任务列表, status=None
2025-07-21 16:41:04.913 | INFO     | app.services.invite_service:get_invite_tasks:121 - 获取邀请任务列表, status=None
2025-07-21 16:41:04.913 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 16:41:04.914 | INFO     | app.services.invite_service:task_initialize:39 - 初始化邀请服务，重置运行中的任务为暂停状态
2025-07-21 16:41:04.914 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 16:41:04.919 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 16:41:04.920 | INFO     | utils.vip_checker:start_checking:37 - 开始VIP状态检查
2025-07-21 16:41:04.920 | DEBUG    | utils.vip_checker:_check_vip_status:61 - 正在检查VIP状态...
2025-07-21 16:41:04.920 | INFO     | core.auth.api_service:verify_vip:425 - 会员验证接口调用
2025-07-21 16:41:04.921 | INFO     | core.proxy.proxy_task_manager:task_wrapper:199 - 任务 refresh_proxies_0_20 完成
2025-07-21 16:41:05.143 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 16:41:05.147 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 16:41:05.151 | DEBUG    | ui.views.monitor_view:_refresh_task_list:175 - 刷新任务列表，共0个任务
2025-07-21 16:41:05.152 | INFO     | ui.views.monitor_view:_refresh_task_list:189 - 没有找到监控任务
2025-07-21 16:41:05.152 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 16:41:05.153 | INFO     | app.services.invite_service:task_initialize:48 - 成功重置 0 个任务状态
2025-07-21 16:41:05.153 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 16:41:05.155 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 16:41:05.158 | INFO     | app.services.account_service:get_all_groups:98 - 获取所有账户分组成功, 共 2 个
2025-07-21 16:41:05.158 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 16:41:05.158 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 16:41:05.159 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 16:41:05.162 | DEBUG    | ui.views.account_view:_on_groups_loaded:519 - 分组加载完成: 2个分组
2025-07-21 16:41:05.175 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 16:41:05.303 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 16:41:05.303 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 16:41:05.304 | INFO     | app.services.account_service:get_all_accounts:292 - 获取所有账户成功, 共 2 个
2025-07-21 16:41:05.304 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 16:41:05.305 | INFO     | core.auth.api_service:verify_vip:438 - 会员验证成功
2025-07-21 16:41:05.305 | INFO     | utils.vip_checker:_check_vip_status:76 - VIP状态有效
2025-07-21 16:41:05.307 | INFO     | app.controllers.telegram_monitor_controller:_load_total_users_count_internal:191 - 成功加载总体统计数据: 总用户数 23, 今日采集 0, 日均采集 23, 运行天数 1
2025-07-21 16:41:05.307 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:156 - 成功加载 0 个任务
2025-07-21 16:41:05.307 | DEBUG    | ui.views.monitor_view:_on_loading_finished:625 - 加载完成
2025-07-21 16:41:05.308 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-07-21 16:41:05.313 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 16:41:05.320 | DEBUG    | ui.views.account_view:_on_accounts_loaded:652 - 账户加载完成: 2个账户
2025-07-21 16:41:05.340 | DEBUG    | ui.views.account_view:_update_group_counts:188 - 分组计数已更新: {2: 1, 1: 1}
2025-07-21 16:41:05.344 | INFO     | ui.views.account_view:_auto_login_accounts:707 - 开始调用控制器进行 2 个账户的自动登录。
2025-07-21 16:41:05.344 | INFO     | app.services.account_service:batch_auto_login:1207 - 服务层：开始批量自动登录 2 个账户, 最大并发: 5
2025-07-21 16:41:05.344 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 16:41:05.367 | INFO     | app.services.account_service:get_all_accounts:292 - 获取所有账户成功, 共 2 个
2025-07-21 16:41:05.367 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 16:41:05.370 | INFO     | app.services.account_service:batch_auto_login:1246 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-07-21 16:41:05.370 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 16:41:05.372 | DEBUG    | ui.views.account_view:_update_group_counts:188 - 分组计数已更新: {2: 1, 1: 1}
2025-07-21 16:41:05.372 | DEBUG    | ui.views.account_view:_init_cache:155 - 账户缓存初始化完成，共 2 个账户
2025-07-21 16:41:05.378 | DEBUG    | ui.views.account_view:_on_accounts_loaded:652 - 账户加载完成: 2个账户
2025-07-21 16:41:05.406 | DEBUG    | ui.views.account_view:_update_group_counts:188 - 分组计数已更新: {2: 1, 1: 1}
2025-07-21 16:41:05.417 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 16:41:05.430 | INFO     | app.services.account_service:batch_auto_login:1246 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-07-21 16:41:05.430 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 16:41:05.433 | INFO     | app.services.account_service:batch_auto_login:1316 - 服务层：准备调用核心层处理 2 个账户的自动登录。
2025-07-21 16:41:05.434 | INFO     | app.services.account_service:batch_auto_login:1326 - 服务层：设置核心层任务超时为 120 秒。
2025-07-21 16:41:05.434 | INFO     | core.telegram.client_manager:batch_auto_login:1230 - 开始批量自动登录, 共 2 个账户, 最大并发 5
2025-07-21 16:41:05.434 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:271 - 创建客户端: H:\PyProject\TeleTest\APPDATA\sessions\+***********, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-21 16:41:05.435 | DEBUG    | core.telegram.client_manager:get_proxy:96 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-21 16:41:05.435 | DEBUG    | core.telegram.client_manager:get_proxy:143 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-07-21 16:41:05.440 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:271 - 创建客户端: H:\PyProject\TeleTest\APPDATA\sessions\+***********, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-21 16:41:05.441 | DEBUG    | core.telegram.client_manager:get_proxy:96 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-21 16:41:05.441 | DEBUG    | core.telegram.client_manager:get_proxy:143 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-07-21 16:41:05.476 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 16:41:05.551 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 16:41:05.560 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-07-21 16:41:05.562 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-07-21 16:41:05.564 | INFO     | ui.views.proxy_view:_load_initial_data:1044 - 初始数据加载完成，共 1 个代理
2025-07-21 16:41:05.629 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 16:41:11.478 | INFO     | core.telegram.client_manager:_connect_client:219 - 连接成功!
2025-07-21 16:41:13.112 | INFO     | core.telegram.client_manager:_verify_client_authorization:1205 - 账户已授权: +***********
2025-07-21 16:41:14.520 | INFO     | core.telegram.client_manager:_connect_client:219 - 连接成功!
2025-07-21 16:41:15.487 | INFO     | core.telegram.client_manager:_verify_client_authorization:1205 - 账户已授权: +***********
2025-07-21 16:41:17.488 | INFO     | core.telegram.client_manager:batch_auto_login:1379 - 批量登录完成: 总计 2, 成功 2, 失败 0
2025-07-21 16:41:18.476 | INFO     | app.services.account_service:batch_auto_login:1347 - 服务层：核心层登录完成: 成功 2, 失败 0 / 2
2025-07-21 16:41:18.476 | INFO     | app.services.account_service:_process_batch_login_results:1709 - 开始处理批量登录结果，共 2 个账户
2025-07-21 16:41:18.476 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 16:41:18.481 | INFO     | data.repositories.account_repo:update_account:394 - 更新账户成功: 1
2025-07-21 16:41:18.481 | INFO     | app.services.account_service:_process_batch_login_results:1745 - 已更新账户 +*********** 的用户信息
2025-07-21 16:41:18.486 | INFO     | data.repositories.account_repo:update_account:394 - 更新账户成功: 2
2025-07-21 16:41:18.487 | INFO     | app.services.account_service:_process_batch_login_results:1745 - 已更新账户 +*********** 的用户信息
2025-07-21 16:41:18.493 | INFO     | app.services.account_service:_process_batch_login_results:1768 - 批量登录结果处理完成，数据库已更新
2025-07-21 16:41:18.494 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 16:41:18.494 | INFO     | app.controllers.account_controller:auto_login_accounts:640 - 批量登录完成，触发账户数据刷新
2025-07-21 16:41:36.957 | INFO     | app.services.account_service:update_account_profile:493 - 更新账户资料: 2
2025-07-21 16:41:36.957 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 16:41:36.993 | INFO     | app.services.account_service:_generate_unique_username:1632 - 生成唯一用户名成功: tianyiblgrk (尝试 1/5)
2025-07-21 16:41:36.993 | INFO     | core.telegram.user_manager:update_profile:89 - 正在更新用户资料: +***********
2025-07-21 16:41:49.377 | INFO     | core.telegram.user_manager:update_profile:130 - 更新用户资料成功: +***********
2025-07-21 16:41:50.027 | INFO     | data.repositories.account_repo:update_account:394 - 更新账户成功: 2
2025-07-21 16:41:50.035 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 16:41:52.094 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 16:41:52.188 | INFO     | app.services.account_service:get_all_accounts:292 - 获取所有账户成功, 共 2 个
2025-07-21 16:41:52.188 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 16:41:52.190 | DEBUG    | ui.views.account_view:_on_accounts_loaded:652 - 账户加载完成: 2个账户
2025-07-21 16:41:52.212 | DEBUG    | ui.views.account_view:_update_group_counts:188 - 分组计数已更新: {2: 1, 1: 1}
2025-07-21 16:42:03.979 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-21 16:43:03.977 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-21 16:43:34.583 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 16:43:34.592 | INFO     | app.services.account_service:get_accounts_by_group:314 - 获取分组 1 的账户成功, 共 1 个
2025-07-21 16:43:34.592 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 16:43:34.593 | DEBUG    | ui.views.account_view:_on_accounts_loaded:652 - 账户加载完成: 1个账户
2025-07-21 16:43:35.065 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 16:43:35.074 | INFO     | app.services.account_service:get_accounts_by_group:314 - 获取分组 2 的账户成功, 共 1 个
2025-07-21 16:43:35.074 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 16:43:35.076 | DEBUG    | ui.views.account_view:_on_accounts_loaded:652 - 账户加载完成: 1个账户
2025-07-21 16:44:04.000 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-21 16:44:12.984 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 16:44:12.997 | INFO     | app.services.account_service:get_accounts_by_group:314 - 获取分组 1 的账户成功, 共 1 个
2025-07-21 16:44:12.997 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 16:44:12.999 | DEBUG    | ui.views.account_view:_on_accounts_loaded:652 - 账户加载完成: 1个账户
2025-07-21 16:44:13.523 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-21 16:44:13.529 | INFO     | app.services.account_service:get_all_accounts:292 - 获取所有账户成功, 共 2 个
2025-07-21 16:44:13.530 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-21 16:44:13.531 | DEBUG    | ui.views.account_view:_on_accounts_loaded:652 - 账户加载完成: 2个账户
2025-07-21 16:44:13.550 | DEBUG    | ui.views.account_view:_update_group_counts:188 - 分组计数已更新: {2: 1, 1: 1}
2025-07-21 16:44:23.553 | INFO     | ui.main_window:closeEvent:380 - MainWindow: 接收到关闭事件
2025-07-21 16:44:23.563 | INFO     | ui.main_window:_cleanup_before_quit:266 - MainWindow: 执行清理资源...
2025-07-21 16:44:23.563 | INFO     | core.telegram.client_worker:stop:108 - 正在停止Telegram客户端工作线程
2025-07-21 16:44:23.568 | INFO     | core.telegram.client_manager:cleanup_async:1150 - 开始清理资源并断开所有连接
2025-07-21 16:44:23.569 | INFO     | core.telegram.client_manager:disconnect_all_clients:735 - 正在断开所有客户端连接
2025-07-21 16:44:23.569 | INFO     | core.telegram.client_manager:_safe_disconnect:756 - 正在断开客户端连接: +***********
2025-07-21 16:44:23.569 | INFO     | core.telegram.client_manager:_safe_disconnect:756 - 正在断开客户端连接: +***********
2025-07-21 16:44:23.580 | INFO     | core.telegram.client_manager:_safe_disconnect:760 - 断开客户端连接成功: +***********
2025-07-21 16:44:23.580 | INFO     | core.telegram.client_manager:_safe_disconnect:760 - 断开客户端连接成功: +***********
2025-07-21 16:44:23.580 | INFO     | core.telegram.client_manager:cleanup_async:1169 - 资源清理完成
2025-07-21 16:44:24.083 | INFO     | core.telegram.client_manager:cleanup_async:1150 - 开始清理资源并断开所有连接
2025-07-21 16:44:24.083 | INFO     | core.telegram.client_manager:disconnect_all_clients:735 - 正在断开所有客户端连接
2025-07-21 16:44:24.083 | INFO     | core.telegram.client_manager:cleanup_async:1169 - 资源清理完成
2025-07-21 16:44:24.597 | INFO     | core.telegram.client_worker:run:97 - Telegram客户端工作线程已退出
2025-07-21 16:44:24.597 | INFO     | core.telegram.client_worker:stop:122 - Telegram客户端工作线程已停止
2025-07-21 16:44:24.598 | INFO     | ui.main_window:_cleanup_before_quit:275 - TelegramClientWorker 已停止。
2025-07-21 16:44:24.598 | INFO     | ui.main_window:_cleanup_before_quit:279 - MainWindow 清理完成
2025-07-21 16:44:24.617 | INFO     | __main__:main:109 - 应用程序已正常退出
