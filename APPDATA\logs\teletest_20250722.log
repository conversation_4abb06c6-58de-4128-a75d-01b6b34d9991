2025-07-22 16:04:22.103 | INFO     | utils.logger:init_logger:98 - 日志系统初始化完成，默认级别: DEBUG，日志目录: H:\PyProject\TeleTest\APPDATA\logs
2025-07-22 16:04:23.865 | INFO     | data.database:init_database:31 - 初始化数据库连接: H:\PyProject\TeleTest\APPDATA\database\telegram_manager.db
2025-07-22 16:04:23.894 | DEBUG    | data.database:init_database:58 - 创建数据库表
2025-07-22 16:04:23.907 | INFO     | data.database:init_database:61 - 数据库初始化完成
2025-07-22 16:04:25.307 | DEBUG    | app.controllers.auth_controller:initialize_software:43 - 控制器: 初始化软件 app_id=1000, version=1.2.7
2025-07-22 16:04:25.308 | DEBUG    | core.auth.api_service:init_software:55 - 发送初始化请求: https://server.xile188.com/api/user/1000/windowns10/1.2.7/ini, params={}
2025-07-22 16:04:25.816 | DEBUG    | core.auth.api_service:init_software:66 - 初始化数据解密成功: {'new_content': '- 新增功能\n  - 1.新增图片上传功能\n  - 2.新增软件文档\n  - 3.新增软件自动更新，后续更新会自动检测版本更新，无需再下载更新包\n  - 4.新增设置账户信息及发信控制\n- 修复bug\n  - 删除监听任务时，不删除对应任务采集到的数据。\n  - 监听设置群无法发送到目标群\n  - 导出监听数据时应当导出单个任务而非所有数据\n- 优化记录\n  - 优化发信规则，直接调取符合要求的账户发送，无账户时才需要等待延时。\n  - 完善软件设置', 'new_url': None, 'notice': {'id': '2', 'content': '公告内容测试3', 'time': '**********'}}
2025-07-22 16:04:25.829 | INFO     | app.controllers.auth_controller:login:61 - 控制器: 用户登录 account=demo01
2025-07-22 16:04:28.892 | INFO     | core.auth.api_service:login:127 - 用户登录: account=demo01
2025-07-22 16:04:31.075 | INFO     | core.auth.api_service:get_user_info:347 - 获取用户信息
2025-07-22 16:04:31.333 | INFO     | core.auth.api_service:get_user_info:375 - 获取用户信息成功
2025-07-22 16:04:31.339 | INFO     | __main__:on_auth_completed:91 - 用户登录成功，正在启动主窗口...
2025-07-22 16:04:31.369 | INFO     | core.telegram.client_worker:__init__:61 - Telegram客户端工作线程初始化
2025-07-22 16:04:31.369 | INFO     | utils.vip_checker:initialize:25 - VIP检查器已初始化，等待登录后开始检查
2025-07-22 16:04:31.369 | INFO     | ui.main_window:_initialize_core_components:108 - MainWindow: 初始化核心组件...
2025-07-22 16:04:31.371 | INFO     | core.proxy.proxy_task_manager:run:284 - 代理任务管理器线程启动
2025-07-22 16:04:31.371 | INFO     | app.services.account_service:__init__:44 - 账户服务初始化
2025-07-22 16:04:31.370 | INFO     | core.telegram.client_worker:run:65 - Telegram客户端工作线程开始运行
2025-07-22 16:04:31.371 | INFO     | app.controllers.account_controller:__init__:82 - 账户控制器初始化
2025-07-22 16:04:31.372 | INFO     | app.services.monitor_service:__init__:39 - 初始化监控任务服务
2025-07-22 16:04:31.372 | INFO     | core.monitor.filter:__init__:26 - 初始化消息过滤器
2025-07-22 16:04:31.372 | INFO     | core.monitor.extractor:__init__:24 - 初始化关键词提取器
2025-07-22 16:04:31.372 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-07-22 16:04:31.373 | INFO     | core.monitor.notifier:__init__:90 - 初始化通知管理器
2025-07-22 16:04:31.373 | INFO     | app.controllers.telegram_monitor_controller:__init__:64 - 初始化 Telegram 监控控制器
2025-07-22 16:04:31.373 | INFO     | core.telegram.client_manager:__init__:66 - Telegram客户端管理器初始化
2025-07-22 16:04:31.373 | DEBUG    | app.controllers.telegram_monitor_controller:__init__:82 - 控制器初始化完成
2025-07-22 16:04:31.373 | INFO     | core.telegram.user_manager:__init__:30 - Telegram用户管理器初始化
2025-07-22 16:04:31.374 | INFO     | core.telegram.group_manager:__init__:36 - Telegram群组管理器初始化
2025-07-22 16:04:31.375 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-07-22 16:04:31.375 | INFO     | core.telegram.monitor_manager:__init__:26 - Telegram监控管理器初始化
2025-07-22 16:04:31.375 | INFO     | core.telegram.telegram_service:__init__:39 - Telegram服务初始化完成
2025-07-22 16:04:31.619 | INFO     | ui.views.monitor_view:__init__:48 - 初始化监控视图
2025-07-22 16:04:31.619 | DEBUG    | ui.views.monitor_view:_connect_signals:80 - 连接控制器信号
2025-07-22 16:04:31.816 | INFO     | ui.views.proxy_view:__init__:96 - 初始化视图，加载第一页代理数据
2025-07-22 16:04:32.086 | INFO     | __main__:on_auth_completed:95 - 主窗口已启动
2025-07-22 16:04:32.131 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:88 - 尝试连接监听日志信号...
2025-07-22 16:04:32.131 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:90 - 监听日志信号连接成功
2025-07-22 16:04:32.132 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-22 16:04:32.136 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-22 16:04:32.139 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:145 - 加载监控任务列表 (搜索关键词: '')
2025-07-22 16:04:32.140 | DEBUG    | ui.views.monitor_view:_on_loading_started:620 - 加载中: 正在加载任务列表...
2025-07-22 16:04:32.140 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-22 16:04:32.147 | INFO     | app.services.proxy_service:refresh_proxies:712 - 刷新代理列表: offset=0, limit=20
2025-07-22 16:04:32.147 | INFO     | core.proxy.proxy_task_manager:submit_task:172 - 提交耗时任务到子线程: refresh_proxies_0_20
2025-07-22 16:04:32.147 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-22 16:04:32.148 | DEBUG    | app.services.proxy_service:get_proxies:500 - 获取代理列表: offset=0, limit=20
2025-07-22 16:04:32.148 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-22 16:04:32.152 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-22 16:04:32.175 | INFO     | app.controllers.invite_controller:get_invite_tasks:38 - 获取邀请任务列表, status=None
2025-07-22 16:04:32.176 | INFO     | app.services.invite_service:get_invite_tasks:121 - 获取邀请任务列表, status=None
2025-07-22 16:04:32.176 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-22 16:04:32.177 | INFO     | app.services.invite_service:task_initialize:39 - 初始化邀请服务，重置运行中的任务为暂停状态
2025-07-22 16:04:32.177 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-22 16:04:32.181 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-22 16:04:32.182 | INFO     | core.proxy.proxy_task_manager:task_wrapper:199 - 任务 refresh_proxies_0_20 完成
2025-07-22 16:04:32.183 | INFO     | utils.vip_checker:start_checking:37 - 开始VIP状态检查
2025-07-22 16:04:32.183 | DEBUG    | utils.vip_checker:_check_vip_status:61 - 正在检查VIP状态...
2025-07-22 16:04:32.183 | INFO     | core.auth.api_service:verify_vip:425 - 会员验证接口调用
2025-07-22 16:04:32.394 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-22 16:04:32.399 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-22 16:04:32.404 | DEBUG    | ui.views.monitor_view:_refresh_task_list:175 - 刷新任务列表，共0个任务
2025-07-22 16:04:32.405 | INFO     | ui.views.monitor_view:_refresh_task_list:189 - 没有找到监控任务
2025-07-22 16:04:32.405 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-22 16:04:32.408 | INFO     | app.services.invite_service:task_initialize:48 - 成功重置 0 个任务状态
2025-07-22 16:04:32.408 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-22 16:04:32.409 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-22 16:04:32.415 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-22 16:04:32.417 | INFO     | app.services.account_service:get_all_groups:98 - 获取所有账户分组成功, 共 2 个
2025-07-22 16:04:32.417 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-22 16:04:32.420 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-22 16:04:32.422 | DEBUG    | ui.views.account_view:_on_groups_loaded:519 - 分组加载完成: 2个分组
2025-07-22 16:04:32.436 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-22 16:04:32.559 | INFO     | app.services.account_service:get_all_accounts:292 - 获取所有账户成功, 共 2 个
2025-07-22 16:04:32.559 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-22 16:04:32.560 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-22 16:04:32.561 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-22 16:04:32.561 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-22 16:04:32.564 | INFO     | app.controllers.telegram_monitor_controller:_load_total_users_count_internal:191 - 成功加载总体统计数据: 总用户数 23, 今日采集 0, 日均采集 23, 运行天数 1
2025-07-22 16:04:32.564 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:156 - 成功加载 0 个任务
2025-07-22 16:04:32.564 | DEBUG    | ui.views.monitor_view:_on_loading_finished:625 - 加载完成
2025-07-22 16:04:32.564 | DEBUG    | ui.views.account_view:_on_accounts_loaded:652 - 账户加载完成: 2个账户
2025-07-22 16:04:32.582 | DEBUG    | ui.views.account_view:_update_group_counts:188 - 分组计数已更新: {2: 1, 1: 1}
2025-07-22 16:04:32.586 | INFO     | ui.views.account_view:_auto_login_accounts:707 - 开始调用控制器进行 2 个账户的自动登录。
2025-07-22 16:04:32.586 | INFO     | app.services.account_service:batch_auto_login:1212 - 服务层：开始批量自动登录 2 个账户, 最大并发: 5
2025-07-22 16:04:32.587 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-22 16:04:32.590 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-07-22 16:04:32.608 | INFO     | app.services.account_service:get_all_accounts:292 - 获取所有账户成功, 共 2 个
2025-07-22 16:04:32.608 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-22 16:04:32.609 | INFO     | app.services.account_service:batch_auto_login:1251 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-07-22 16:04:32.609 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-22 16:04:32.612 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-22 16:04:32.613 | DEBUG    | ui.views.account_view:_update_group_counts:188 - 分组计数已更新: {2: 1, 1: 1}
2025-07-22 16:04:32.613 | DEBUG    | ui.views.account_view:_init_cache:155 - 账户缓存初始化完成，共 2 个账户
2025-07-22 16:04:32.620 | DEBUG    | ui.views.account_view:_on_accounts_loaded:652 - 账户加载完成: 2个账户
2025-07-22 16:04:32.643 | DEBUG    | ui.views.account_view:_update_group_counts:188 - 分组计数已更新: {2: 1, 1: 1}
2025-07-22 16:04:32.653 | INFO     | core.auth.api_service:verify_vip:438 - 会员验证成功
2025-07-22 16:04:32.653 | INFO     | utils.vip_checker:_check_vip_status:76 - VIP状态有效
2025-07-22 16:04:32.663 | INFO     | app.services.account_service:batch_auto_login:1251 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-07-22 16:04:32.663 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-22 16:04:32.665 | INFO     | app.services.account_service:batch_auto_login:1321 - 服务层：准备调用核心层处理 2 个账户的自动登录。
2025-07-22 16:04:32.665 | INFO     | app.services.account_service:batch_auto_login:1331 - 服务层：设置核心层任务超时为 120 秒。
2025-07-22 16:04:32.665 | INFO     | core.telegram.client_manager:batch_auto_login:1230 - 开始批量自动登录, 共 2 个账户, 最大并发 5
2025-07-22 16:04:32.665 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:271 - 创建客户端: H:\PyProject\TeleTest\APPDATA\sessions\+***********, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-22 16:04:32.666 | DEBUG    | core.telegram.client_manager:get_proxy:96 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-22 16:04:32.666 | DEBUG    | core.telegram.client_manager:get_proxy:143 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-07-22 16:04:32.671 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:271 - 创建客户端: H:\PyProject\TeleTest\APPDATA\sessions\+***********, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-22 16:04:32.672 | DEBUG    | core.telegram.client_manager:get_proxy:96 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-22 16:04:32.672 | DEBUG    | core.telegram.client_manager:get_proxy:143 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-07-22 16:04:32.709 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-22 16:04:32.798 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-22 16:04:32.850 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-07-22 16:04:32.852 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-07-22 16:04:32.853 | INFO     | ui.views.proxy_view:_load_initial_data:1044 - 初始数据加载完成，共 1 个代理
2025-07-22 16:04:32.863 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-22 16:04:36.337 | INFO     | core.telegram.client_manager:_connect_client:219 - 连接成功!
2025-07-22 16:04:37.267 | INFO     | core.telegram.client_manager:_verify_client_authorization:1205 - 账户已授权: +***********
2025-07-22 16:04:43.203 | INFO     | core.telegram.client_manager:_connect_client:219 - 连接成功!
2025-07-22 16:04:45.641 | INFO     | core.telegram.client_manager:_verify_client_authorization:1205 - 账户已授权: +***********
2025-07-22 16:04:47.649 | INFO     | core.telegram.client_manager:batch_auto_login:1379 - 批量登录完成: 总计 2, 成功 2, 失败 0
2025-07-22 16:04:47.711 | INFO     | app.services.account_service:batch_auto_login:1352 - 服务层：核心层登录完成: 成功 2, 失败 0 / 2
2025-07-22 16:04:47.711 | INFO     | app.services.account_service:_process_batch_login_results:1786 - 开始处理批量登录结果，共 2 个账户
2025-07-22 16:04:47.711 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-22 16:04:47.718 | INFO     | data.repositories.account_repo:update_account:394 - 更新账户成功: 1
2025-07-22 16:04:47.718 | INFO     | app.services.account_service:_process_batch_login_results:1822 - 已更新账户 +*********** 的用户信息
2025-07-22 16:04:47.726 | INFO     | data.repositories.account_repo:update_account:394 - 更新账户成功: 2
2025-07-22 16:04:47.727 | INFO     | app.services.account_service:_process_batch_login_results:1822 - 已更新账户 +*********** 的用户信息
2025-07-22 16:04:47.735 | INFO     | app.services.account_service:_process_batch_login_results:1845 - 批量登录结果处理完成，数据库已更新
2025-07-22 16:04:47.735 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-22 16:04:47.735 | INFO     | app.controllers.account_controller:auto_login_accounts:640 - 批量登录完成，触发账户数据刷新
2025-07-22 16:05:31.333 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-22 16:05:45.271 | INFO     | app.services.account_service:update_account_profile:493 - 更新账户资料: 1
2025-07-22 16:05:45.271 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-22 16:05:45.317 | INFO     | app.services.account_service:_generate_unique_username:1658 - 生成唯一用户名成功: tiany9 (尝试 1/5)
2025-07-22 16:05:45.318 | INFO     | core.telegram.user_manager:update_profile:89 - 正在更新用户资料: +***********
2025-07-22 16:05:46.235 | ERROR    | core.telegram.user_manager:update_profile:135 - 更新用户资料错误: +***********, 更新用户资料失败: The username is already taken (caused by UpdateUsernameRequest)
2025-07-22 16:05:46.571 | INFO     | data.repositories.account_repo:update_account:394 - 更新账户成功: 1
2025-07-22 16:05:46.580 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-22 16:05:48.604 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-22 16:05:48.810 | INFO     | app.services.account_service:get_all_accounts:292 - 获取所有账户成功, 共 2 个
2025-07-22 16:05:48.810 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-22 16:05:48.812 | DEBUG    | ui.views.account_view:_on_accounts_loaded:652 - 账户加载完成: 2个账户
2025-07-22 16:05:48.832 | DEBUG    | ui.views.account_view:_update_group_counts:188 - 分组计数已更新: {2: 1, 1: 1}
2025-07-22 16:05:51.573 | INFO     | app.services.account_service:refresh_account_info:654 - 刷新账户信息: 2
2025-07-22 16:05:51.573 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-22 16:05:51.582 | INFO     | core.telegram.user_manager:get_user_info:41 - 正在获取用户信息: +***********
2025-07-22 16:05:52.392 | INFO     | core.telegram.user_manager:get_user_info:66 - 获取用户信息成功: +***********
2025-07-22 16:05:52.821 | INFO     | data.repositories.account_repo:update_account:394 - 更新账户成功: 2
2025-07-22 16:05:52.828 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-22 16:05:52.832 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-22 16:05:52.841 | INFO     | ui.views.account_view:_update_single_account_display:337 - 已实时更新账户显示: +***********
2025-07-22 16:05:52.841 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-22 16:05:54.129 | INFO     | app.services.account_service:refresh_account_info:654 - 刷新账户信息: 1
2025-07-22 16:05:54.130 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-22 16:05:54.142 | INFO     | core.telegram.user_manager:get_user_info:41 - 正在获取用户信息: +***********
2025-07-22 16:05:55.092 | INFO     | core.telegram.user_manager:get_user_info:66 - 获取用户信息成功: +***********
2025-07-22 16:05:55.391 | INFO     | data.repositories.account_repo:update_account:394 - 更新账户成功: 1
2025-07-22 16:05:55.397 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-22 16:05:55.402 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-22 16:05:55.410 | INFO     | ui.views.account_view:_update_single_account_display:337 - 已实时更新账户显示: +***********
2025-07-22 16:05:55.410 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-22 16:06:31.333 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-22 16:07:31.336 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-22 16:07:56.416 | INFO     | app.services.account_service:update_account_profile:493 - 更新账户资料: 2
2025-07-22 16:07:56.416 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-22 16:07:56.458 | INFO     | app.services.account_service:_generate_unique_username:1658 - 生成唯一用户名成功: tianer098 (尝试 1/5)
2025-07-22 16:07:56.459 | INFO     | core.telegram.user_manager:update_profile:89 - 正在更新用户资料: +***********
2025-07-22 16:08:01.932 | INFO     | core.telegram.user_manager:update_profile:130 - 更新用户资料成功: +***********
2025-07-22 16:08:02.477 | INFO     | data.repositories.account_repo:update_account:394 - 更新账户成功: 2
2025-07-22 16:08:02.484 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-22 16:08:04.520 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-22 16:08:04.581 | INFO     | app.services.account_service:get_all_accounts:292 - 获取所有账户成功, 共 2 个
2025-07-22 16:08:04.581 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-22 16:08:04.582 | DEBUG    | ui.views.account_view:_on_accounts_loaded:652 - 账户加载完成: 2个账户
2025-07-22 16:08:04.605 | DEBUG    | ui.views.account_view:_update_group_counts:188 - 分组计数已更新: {2: 2}
2025-07-22 16:08:31.334 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-22 16:09:31.334 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-22 16:10:31.336 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-22 16:11:31.337 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-22 16:12:31.336 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-22 16:13:31.341 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-22 16:14:31.337 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-22 16:26:57.652 | INFO     | utils.logger:init_logger:98 - 日志系统初始化完成，默认级别: DEBUG，日志目录: H:\PyProject\TeleTest\APPDATA\logs
2025-07-22 16:26:58.694 | INFO     | data.database:init_database:31 - 初始化数据库连接: H:\PyProject\TeleTest\APPDATA\database\telegram_manager.db
2025-07-22 16:26:58.709 | DEBUG    | data.database:init_database:58 - 创建数据库表
2025-07-22 16:26:58.718 | INFO     | data.database:init_database:61 - 数据库初始化完成
2025-07-22 16:26:59.857 | DEBUG    | app.controllers.auth_controller:initialize_software:43 - 控制器: 初始化软件 app_id=1000, version=1.2.7
2025-07-22 16:26:59.858 | DEBUG    | core.auth.api_service:init_software:55 - 发送初始化请求: https://server.xile188.com/api/user/1000/windowns10/1.2.7/ini, params={}
2025-07-22 16:27:00.265 | DEBUG    | core.auth.api_service:init_software:66 - 初始化数据解密成功: {'new_content': '- 新增功能\n  - 1.新增图片上传功能\n  - 2.新增软件文档\n  - 3.新增软件自动更新，后续更新会自动检测版本更新，无需再下载更新包\n  - 4.新增设置账户信息及发信控制\n- 修复bug\n  - 删除监听任务时，不删除对应任务采集到的数据。\n  - 监听设置群无法发送到目标群\n  - 导出监听数据时应当导出单个任务而非所有数据\n- 优化记录\n  - 优化发信规则，直接调取符合要求的账户发送，无账户时才需要等待延时。\n  - 完善软件设置', 'new_url': None, 'notice': {'id': '2', 'content': '公告内容测试3', 'time': '**********'}}
2025-07-22 16:27:00.271 | INFO     | app.controllers.auth_controller:login:61 - 控制器: 用户登录 account=demo01
2025-07-22 16:27:03.259 | INFO     | core.auth.api_service:login:127 - 用户登录: account=demo01
2025-07-22 16:27:03.504 | INFO     | core.auth.api_service:get_user_info:347 - 获取用户信息
2025-07-22 16:27:03.702 | INFO     | core.auth.api_service:get_user_info:375 - 获取用户信息成功
2025-07-22 16:27:03.708 | INFO     | __main__:on_auth_completed:91 - 用户登录成功，正在启动主窗口...
2025-07-22 16:27:03.733 | INFO     | core.telegram.client_worker:__init__:61 - Telegram客户端工作线程初始化
2025-07-22 16:27:03.733 | INFO     | utils.vip_checker:initialize:25 - VIP检查器已初始化，等待登录后开始检查
2025-07-22 16:27:03.733 | INFO     | ui.main_window:_initialize_core_components:108 - MainWindow: 初始化核心组件...
2025-07-22 16:27:03.735 | INFO     | core.telegram.client_worker:run:65 - Telegram客户端工作线程开始运行
2025-07-22 16:27:03.736 | INFO     | app.services.account_service:__init__:44 - 账户服务初始化
2025-07-22 16:27:03.736 | INFO     | core.proxy.proxy_task_manager:run:284 - 代理任务管理器线程启动
2025-07-22 16:27:03.737 | INFO     | app.controllers.account_controller:__init__:82 - 账户控制器初始化
2025-07-22 16:27:03.737 | INFO     | app.services.monitor_service:__init__:39 - 初始化监控任务服务
2025-07-22 16:27:03.737 | INFO     | core.monitor.filter:__init__:26 - 初始化消息过滤器
2025-07-22 16:27:03.738 | INFO     | core.monitor.extractor:__init__:24 - 初始化关键词提取器
2025-07-22 16:27:03.738 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-07-22 16:27:03.738 | INFO     | core.monitor.notifier:__init__:90 - 初始化通知管理器
2025-07-22 16:27:03.738 | INFO     | app.controllers.telegram_monitor_controller:__init__:64 - 初始化 Telegram 监控控制器
2025-07-22 16:27:03.738 | DEBUG    | app.controllers.telegram_monitor_controller:__init__:82 - 控制器初始化完成
2025-07-22 16:27:03.742 | INFO     | core.telegram.client_manager:__init__:66 - Telegram客户端管理器初始化
2025-07-22 16:27:03.743 | INFO     | core.telegram.user_manager:__init__:30 - Telegram用户管理器初始化
2025-07-22 16:27:03.743 | INFO     | core.telegram.group_manager:__init__:36 - Telegram群组管理器初始化
2025-07-22 16:27:03.744 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-07-22 16:27:03.746 | INFO     | core.telegram.monitor_manager:__init__:26 - Telegram监控管理器初始化
2025-07-22 16:27:03.747 | INFO     | core.telegram.telegram_service:__init__:39 - Telegram服务初始化完成
2025-07-22 16:27:03.953 | INFO     | ui.views.monitor_view:__init__:48 - 初始化监控视图
2025-07-22 16:27:03.953 | DEBUG    | ui.views.monitor_view:_connect_signals:80 - 连接控制器信号
2025-07-22 16:27:04.155 | INFO     | ui.views.proxy_view:__init__:96 - 初始化视图，加载第一页代理数据
2025-07-22 16:27:04.424 | INFO     | __main__:on_auth_completed:95 - 主窗口已启动
2025-07-22 16:27:04.479 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:88 - 尝试连接监听日志信号...
2025-07-22 16:27:04.479 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:90 - 监听日志信号连接成功
2025-07-22 16:27:04.479 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-22 16:27:04.483 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-22 16:27:04.486 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:145 - 加载监控任务列表 (搜索关键词: '')
2025-07-22 16:27:04.487 | DEBUG    | ui.views.monitor_view:_on_loading_started:620 - 加载中: 正在加载任务列表...
2025-07-22 16:27:04.487 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-22 16:27:04.492 | INFO     | app.services.proxy_service:refresh_proxies:712 - 刷新代理列表: offset=0, limit=20
2025-07-22 16:27:04.493 | INFO     | core.proxy.proxy_task_manager:submit_task:172 - 提交耗时任务到子线程: refresh_proxies_0_20
2025-07-22 16:27:04.493 | DEBUG    | app.services.proxy_service:get_proxies:500 - 获取代理列表: offset=0, limit=20
2025-07-22 16:27:04.493 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-22 16:27:04.494 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-22 16:27:04.495 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-22 16:27:04.510 | INFO     | app.controllers.invite_controller:get_invite_tasks:38 - 获取邀请任务列表, status=None
2025-07-22 16:27:04.521 | INFO     | app.services.invite_service:get_invite_tasks:121 - 获取邀请任务列表, status=None
2025-07-22 16:27:04.521 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-22 16:27:04.521 | INFO     | app.services.invite_service:task_initialize:39 - 初始化邀请服务，重置运行中的任务为暂停状态
2025-07-22 16:27:04.522 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-22 16:27:04.526 | INFO     | utils.vip_checker:start_checking:37 - 开始VIP状态检查
2025-07-22 16:27:04.527 | DEBUG    | utils.vip_checker:_check_vip_status:61 - 正在检查VIP状态...
2025-07-22 16:27:04.527 | INFO     | core.auth.api_service:verify_vip:425 - 会员验证接口调用
2025-07-22 16:27:04.528 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-22 16:27:04.529 | INFO     | core.proxy.proxy_task_manager:task_wrapper:199 - 任务 refresh_proxies_0_20 完成
2025-07-22 16:27:04.657 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-22 16:27:04.660 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-22 16:27:04.665 | INFO     | app.services.invite_service:task_initialize:48 - 成功重置 0 个任务状态
2025-07-22 16:27:04.665 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-22 16:27:04.667 | DEBUG    | ui.views.monitor_view:_refresh_task_list:175 - 刷新任务列表，共0个任务
2025-07-22 16:27:04.668 | INFO     | ui.views.monitor_view:_refresh_task_list:189 - 没有找到监控任务
2025-07-22 16:27:04.669 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-22 16:27:04.671 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-22 16:27:04.674 | INFO     | app.services.account_service:get_all_groups:98 - 获取所有账户分组成功, 共 2 个
2025-07-22 16:27:04.675 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-22 16:27:04.676 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-22 16:27:04.679 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-22 16:27:04.680 | DEBUG    | ui.views.account_view:_on_groups_loaded:519 - 分组加载完成: 2个分组
2025-07-22 16:27:04.697 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-22 16:27:04.834 | INFO     | app.services.account_service:get_all_accounts:292 - 获取所有账户成功, 共 2 个
2025-07-22 16:27:04.834 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-22 16:27:04.836 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-22 16:27:04.837 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-22 16:27:04.838 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-22 16:27:04.838 | DEBUG    | ui.views.account_view:_on_accounts_loaded:652 - 账户加载完成: 2个账户
2025-07-22 16:27:04.857 | DEBUG    | ui.views.account_view:_update_group_counts:188 - 分组计数已更新: {2: 2}
2025-07-22 16:27:04.861 | INFO     | ui.views.account_view:_auto_login_accounts:707 - 开始调用控制器进行 2 个账户的自动登录。
2025-07-22 16:27:04.862 | INFO     | app.services.account_service:batch_auto_login:1212 - 服务层：开始批量自动登录 2 个账户, 最大并发: 5
2025-07-22 16:27:04.862 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-22 16:27:04.868 | INFO     | app.controllers.telegram_monitor_controller:_load_total_users_count_internal:191 - 成功加载总体统计数据: 总用户数 23, 今日采集 0, 日均采集 23, 运行天数 1
2025-07-22 16:27:04.868 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:156 - 成功加载 0 个任务
2025-07-22 16:27:04.869 | DEBUG    | ui.views.monitor_view:_on_loading_finished:625 - 加载完成
2025-07-22 16:27:04.870 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-07-22 16:27:04.890 | INFO     | app.services.account_service:get_all_accounts:292 - 获取所有账户成功, 共 2 个
2025-07-22 16:27:04.890 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-22 16:27:04.891 | INFO     | app.services.account_service:batch_auto_login:1251 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-07-22 16:27:04.892 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-22 16:27:04.893 | DEBUG    | ui.views.account_view:_update_group_counts:188 - 分组计数已更新: {2: 2}
2025-07-22 16:27:04.893 | DEBUG    | ui.views.account_view:_init_cache:155 - 账户缓存初始化完成，共 2 个账户
2025-07-22 16:27:04.899 | DEBUG    | ui.views.account_view:_on_accounts_loaded:652 - 账户加载完成: 2个账户
2025-07-22 16:27:04.927 | DEBUG    | ui.views.account_view:_update_group_counts:188 - 分组计数已更新: {2: 2}
2025-07-22 16:27:04.932 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-22 16:27:04.938 | INFO     | core.auth.api_service:verify_vip:438 - 会员验证成功
2025-07-22 16:27:04.938 | INFO     | utils.vip_checker:_check_vip_status:76 - VIP状态有效
2025-07-22 16:27:04.948 | INFO     | app.services.account_service:batch_auto_login:1251 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-07-22 16:27:04.949 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-22 16:27:04.951 | INFO     | app.services.account_service:batch_auto_login:1321 - 服务层：准备调用核心层处理 2 个账户的自动登录。
2025-07-22 16:27:04.952 | INFO     | app.services.account_service:batch_auto_login:1331 - 服务层：设置核心层任务超时为 120 秒。
2025-07-22 16:27:04.952 | INFO     | core.telegram.client_manager:batch_auto_login:1230 - 开始批量自动登录, 共 2 个账户, 最大并发 5
2025-07-22 16:27:04.953 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:271 - 创建客户端: H:\PyProject\TeleTest\APPDATA\sessions\+***********, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-22 16:27:04.953 | DEBUG    | core.telegram.client_manager:get_proxy:96 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-22 16:27:04.953 | DEBUG    | core.telegram.client_manager:get_proxy:143 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-07-22 16:27:04.959 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:271 - 创建客户端: H:\PyProject\TeleTest\APPDATA\sessions\+***********, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-22 16:27:04.959 | DEBUG    | core.telegram.client_manager:get_proxy:96 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-22 16:27:04.960 | DEBUG    | core.telegram.client_manager:get_proxy:143 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-07-22 16:27:04.990 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-22 16:27:05.063 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-22 16:27:05.118 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-07-22 16:27:05.123 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-07-22 16:27:05.126 | INFO     | ui.views.proxy_view:_load_initial_data:1044 - 初始数据加载完成，共 1 个代理
2025-07-22 16:27:05.133 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-22 16:27:09.387 | INFO     | core.telegram.client_manager:_connect_client:219 - 连接成功!
2025-07-22 16:27:11.737 | INFO     | core.telegram.client_manager:_verify_client_authorization:1205 - 账户已授权: +***********
2025-07-22 16:27:27.345 | INFO     | core.telegram.client_manager:_connect_client:219 - 连接成功!
2025-07-22 16:27:28.238 | INFO     | core.telegram.client_manager:_verify_client_authorization:1205 - 账户已授权: +***********
2025-07-22 16:27:30.241 | INFO     | core.telegram.client_manager:batch_auto_login:1379 - 批量登录完成: 总计 2, 成功 2, 失败 0
2025-07-22 16:27:30.961 | INFO     | app.services.account_service:batch_auto_login:1352 - 服务层：核心层登录完成: 成功 2, 失败 0 / 2
2025-07-22 16:27:30.961 | INFO     | app.services.account_service:_process_batch_login_results:1786 - 开始处理批量登录结果，共 2 个账户
2025-07-22 16:27:30.961 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-22 16:27:30.966 | INFO     | data.repositories.account_repo:update_account:394 - 更新账户成功: 2
2025-07-22 16:27:30.966 | INFO     | app.services.account_service:_process_batch_login_results:1822 - 已更新账户 +*********** 的用户信息
2025-07-22 16:27:30.971 | INFO     | data.repositories.account_repo:update_account:394 - 更新账户成功: 1
2025-07-22 16:27:30.971 | INFO     | app.services.account_service:_process_batch_login_results:1822 - 已更新账户 +*********** 的用户信息
2025-07-22 16:27:30.978 | INFO     | app.services.account_service:_process_batch_login_results:1845 - 批量登录结果处理完成，数据库已更新
2025-07-22 16:27:30.979 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-22 16:27:30.979 | INFO     | app.controllers.account_controller:auto_login_accounts:640 - 批量登录完成，触发账户数据刷新
2025-07-22 16:27:43.307 | INFO     | app.services.account_service:batch_update_profiles:565 - 批量更新账户资料: 0 个
2025-07-22 16:27:43.307 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-22 16:27:43.307 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-22 16:28:03.696 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-22 16:48:29.324 | INFO     | utils.logger:init_logger:98 - 日志系统初始化完成，默认级别: DEBUG，日志目录: H:\PyProject\TeleTest\APPDATA\logs
2025-07-22 16:48:30.335 | INFO     | data.database:init_database:31 - 初始化数据库连接: H:\PyProject\TeleTest\APPDATA\database\telegram_manager.db
2025-07-22 16:48:30.351 | DEBUG    | data.database:init_database:58 - 创建数据库表
2025-07-22 16:48:30.360 | INFO     | data.database:init_database:61 - 数据库初始化完成
2025-07-22 16:48:31.343 | DEBUG    | app.controllers.auth_controller:initialize_software:43 - 控制器: 初始化软件 app_id=1000, version=1.2.7
2025-07-22 16:48:31.344 | DEBUG    | core.auth.api_service:init_software:55 - 发送初始化请求: https://server.xile188.com/api/user/1000/windowns10/1.2.7/ini, params={}
2025-07-22 16:48:31.901 | DEBUG    | core.auth.api_service:init_software:66 - 初始化数据解密成功: {'new_content': '- 新增功能\n  - 1.新增图片上传功能\n  - 2.新增软件文档\n  - 3.新增软件自动更新，后续更新会自动检测版本更新，无需再下载更新包\n  - 4.新增设置账户信息及发信控制\n- 修复bug\n  - 删除监听任务时，不删除对应任务采集到的数据。\n  - 监听设置群无法发送到目标群\n  - 导出监听数据时应当导出单个任务而非所有数据\n- 优化记录\n  - 优化发信规则，直接调取符合要求的账户发送，无账户时才需要等待延时。\n  - 完善软件设置', 'new_url': None, 'notice': {'id': '2', 'content': '公告内容测试3', 'time': '**********'}}
2025-07-22 16:48:31.908 | INFO     | app.controllers.auth_controller:login:61 - 控制器: 用户登录 account=demo01
2025-07-22 16:48:34.886 | INFO     | core.auth.api_service:login:127 - 用户登录: account=demo01
2025-07-22 16:48:35.128 | INFO     | core.auth.api_service:get_user_info:347 - 获取用户信息
2025-07-22 16:48:35.386 | INFO     | core.auth.api_service:get_user_info:375 - 获取用户信息成功
2025-07-22 16:48:35.392 | INFO     | __main__:on_auth_completed:91 - 用户登录成功，正在启动主窗口...
2025-07-22 16:48:35.412 | INFO     | core.telegram.client_worker:__init__:61 - Telegram客户端工作线程初始化
2025-07-22 16:48:35.413 | INFO     | utils.vip_checker:initialize:25 - VIP检查器已初始化，等待登录后开始检查
2025-07-22 16:48:35.413 | INFO     | ui.main_window:_initialize_core_components:108 - MainWindow: 初始化核心组件...
2025-07-22 16:48:35.415 | INFO     | core.telegram.client_worker:run:65 - Telegram客户端工作线程开始运行
2025-07-22 16:48:35.415 | INFO     | core.proxy.proxy_task_manager:run:284 - 代理任务管理器线程启动
2025-07-22 16:48:35.416 | INFO     | app.services.account_service:__init__:44 - 账户服务初始化
2025-07-22 16:48:35.416 | INFO     | app.controllers.account_controller:__init__:82 - 账户控制器初始化
2025-07-22 16:48:35.416 | INFO     | app.services.monitor_service:__init__:39 - 初始化监控任务服务
2025-07-22 16:48:35.416 | INFO     | core.monitor.filter:__init__:26 - 初始化消息过滤器
2025-07-22 16:48:35.416 | INFO     | core.monitor.extractor:__init__:24 - 初始化关键词提取器
2025-07-22 16:48:35.416 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-07-22 16:48:35.417 | INFO     | core.monitor.notifier:__init__:90 - 初始化通知管理器
2025-07-22 16:48:35.417 | INFO     | app.controllers.telegram_monitor_controller:__init__:64 - 初始化 Telegram 监控控制器
2025-07-22 16:48:35.417 | DEBUG    | app.controllers.telegram_monitor_controller:__init__:82 - 控制器初始化完成
2025-07-22 16:48:35.418 | INFO     | core.telegram.client_manager:__init__:66 - Telegram客户端管理器初始化
2025-07-22 16:48:35.419 | INFO     | core.telegram.user_manager:__init__:30 - Telegram用户管理器初始化
2025-07-22 16:48:35.419 | INFO     | core.telegram.group_manager:__init__:36 - Telegram群组管理器初始化
2025-07-22 16:48:35.419 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-07-22 16:48:35.419 | INFO     | core.telegram.monitor_manager:__init__:26 - Telegram监控管理器初始化
2025-07-22 16:48:35.420 | INFO     | core.telegram.telegram_service:__init__:39 - Telegram服务初始化完成
2025-07-22 16:48:35.615 | INFO     | ui.views.monitor_view:__init__:48 - 初始化监控视图
2025-07-22 16:48:35.615 | DEBUG    | ui.views.monitor_view:_connect_signals:80 - 连接控制器信号
2025-07-22 16:48:35.807 | INFO     | ui.views.proxy_view:__init__:96 - 初始化视图，加载第一页代理数据
2025-07-22 16:48:36.045 | INFO     | __main__:on_auth_completed:95 - 主窗口已启动
2025-07-22 16:48:36.090 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:88 - 尝试连接监听日志信号...
2025-07-22 16:48:36.091 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:90 - 监听日志信号连接成功
2025-07-22 16:48:36.091 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-22 16:48:36.096 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-22 16:48:36.100 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:145 - 加载监控任务列表 (搜索关键词: '')
2025-07-22 16:48:36.100 | DEBUG    | ui.views.monitor_view:_on_loading_started:620 - 加载中: 正在加载任务列表...
2025-07-22 16:48:36.100 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-22 16:48:36.106 | INFO     | app.services.proxy_service:refresh_proxies:712 - 刷新代理列表: offset=0, limit=20
2025-07-22 16:48:36.106 | INFO     | core.proxy.proxy_task_manager:submit_task:172 - 提交耗时任务到子线程: refresh_proxies_0_20
2025-07-22 16:48:36.106 | DEBUG    | app.services.proxy_service:get_proxies:500 - 获取代理列表: offset=0, limit=20
2025-07-22 16:48:36.106 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-22 16:48:36.108 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-22 16:48:36.109 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-22 16:48:36.112 | INFO     | app.controllers.invite_controller:get_invite_tasks:38 - 获取邀请任务列表, status=None
2025-07-22 16:48:36.112 | INFO     | app.services.invite_service:get_invite_tasks:121 - 获取邀请任务列表, status=None
2025-07-22 16:48:36.112 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-22 16:48:36.134 | INFO     | app.services.invite_service:task_initialize:39 - 初始化邀请服务，重置运行中的任务为暂停状态
2025-07-22 16:48:36.134 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-22 16:48:36.138 | INFO     | utils.vip_checker:start_checking:37 - 开始VIP状态检查
2025-07-22 16:48:36.139 | DEBUG    | utils.vip_checker:_check_vip_status:61 - 正在检查VIP状态...
2025-07-22 16:48:36.139 | INFO     | core.auth.api_service:verify_vip:425 - 会员验证接口调用
2025-07-22 16:48:36.139 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-22 16:48:36.141 | INFO     | core.proxy.proxy_task_manager:task_wrapper:199 - 任务 refresh_proxies_0_20 完成
2025-07-22 16:48:36.256 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-22 16:48:36.257 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-22 16:48:36.267 | DEBUG    | ui.views.monitor_view:_refresh_task_list:175 - 刷新任务列表，共0个任务
2025-07-22 16:48:36.268 | INFO     | ui.views.monitor_view:_refresh_task_list:189 - 没有找到监控任务
2025-07-22 16:48:36.268 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-22 16:48:36.269 | INFO     | app.services.invite_service:task_initialize:48 - 成功重置 0 个任务状态
2025-07-22 16:48:36.269 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-22 16:48:36.273 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-22 16:48:36.277 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-22 16:48:36.278 | INFO     | app.services.account_service:get_all_groups:98 - 获取所有账户分组成功, 共 2 个
2025-07-22 16:48:36.278 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-22 16:48:36.279 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-22 16:48:36.283 | DEBUG    | ui.views.account_view:_on_groups_loaded:519 - 分组加载完成: 2个分组
2025-07-22 16:48:36.298 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-22 16:48:36.426 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-22 16:48:36.426 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-22 16:48:36.428 | INFO     | app.services.account_service:get_all_accounts:292 - 获取所有账户成功, 共 2 个
2025-07-22 16:48:36.428 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-22 16:48:36.429 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-07-22 16:48:36.434 | INFO     | app.controllers.telegram_monitor_controller:_load_total_users_count_internal:191 - 成功加载总体统计数据: 总用户数 23, 今日采集 0, 日均采集 23, 运行天数 1
2025-07-22 16:48:36.434 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:156 - 成功加载 0 个任务
2025-07-22 16:48:36.434 | DEBUG    | ui.views.monitor_view:_on_loading_finished:625 - 加载完成
2025-07-22 16:48:36.444 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-22 16:48:36.445 | DEBUG    | ui.views.account_view:_on_accounts_loaded:652 - 账户加载完成: 2个账户
2025-07-22 16:48:36.471 | DEBUG    | ui.views.account_view:_update_group_counts:188 - 分组计数已更新: {2: 2}
2025-07-22 16:48:36.476 | INFO     | ui.views.account_view:_auto_login_accounts:707 - 开始调用控制器进行 2 个账户的自动登录。
2025-07-22 16:48:36.476 | INFO     | app.services.account_service:batch_auto_login:1212 - 服务层：开始批量自动登录 2 个账户, 最大并发: 5
2025-07-22 16:48:36.476 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-22 16:48:36.495 | INFO     | app.services.account_service:batch_auto_login:1251 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-07-22 16:48:36.495 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-22 16:48:36.496 | INFO     | app.services.account_service:get_all_accounts:292 - 获取所有账户成功, 共 2 个
2025-07-22 16:48:36.496 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-22 16:48:36.498 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-22 16:48:36.500 | DEBUG    | ui.views.account_view:_update_group_counts:188 - 分组计数已更新: {2: 2}
2025-07-22 16:48:36.500 | DEBUG    | ui.views.account_view:_init_cache:155 - 账户缓存初始化完成，共 2 个账户
2025-07-22 16:48:36.506 | DEBUG    | ui.views.account_view:_on_accounts_loaded:652 - 账户加载完成: 2个账户
2025-07-22 16:48:36.533 | DEBUG    | ui.views.account_view:_update_group_counts:188 - 分组计数已更新: {2: 2}
2025-07-22 16:48:36.542 | INFO     | core.auth.api_service:verify_vip:438 - 会员验证成功
2025-07-22 16:48:36.542 | INFO     | utils.vip_checker:_check_vip_status:76 - VIP状态有效
2025-07-22 16:48:36.547 | INFO     | app.services.account_service:batch_auto_login:1251 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-07-22 16:48:36.547 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-22 16:48:36.549 | INFO     | app.services.account_service:batch_auto_login:1321 - 服务层：准备调用核心层处理 2 个账户的自动登录。
2025-07-22 16:48:36.550 | INFO     | app.services.account_service:batch_auto_login:1331 - 服务层：设置核心层任务超时为 120 秒。
2025-07-22 16:48:36.550 | INFO     | core.telegram.client_manager:batch_auto_login:1230 - 开始批量自动登录, 共 2 个账户, 最大并发 5
2025-07-22 16:48:36.550 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:271 - 创建客户端: H:\PyProject\TeleTest\APPDATA\sessions\+***********, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-22 16:48:36.551 | DEBUG    | core.telegram.client_manager:get_proxy:96 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-22 16:48:36.551 | DEBUG    | core.telegram.client_manager:get_proxy:143 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-07-22 16:48:36.556 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:271 - 创建客户端: H:\PyProject\TeleTest\APPDATA\sessions\+***********, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-22 16:48:36.556 | DEBUG    | core.telegram.client_manager:get_proxy:96 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-22 16:48:36.557 | DEBUG    | core.telegram.client_manager:get_proxy:143 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-07-22 16:48:36.583 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-22 16:48:36.658 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-22 16:48:36.666 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-07-22 16:48:36.668 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-07-22 16:48:36.669 | INFO     | ui.views.proxy_view:_load_initial_data:1044 - 初始数据加载完成，共 1 个代理
2025-07-22 16:48:36.697 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-22 16:48:39.399 | INFO     | core.telegram.client_manager:_connect_client:219 - 连接成功!
2025-07-22 16:48:40.287 | INFO     | core.telegram.client_manager:_verify_client_authorization:1205 - 账户已授权: +***********
2025-07-22 16:48:41.095 | INFO     | core.telegram.client_manager:_connect_client:219 - 连接成功!
2025-07-22 16:48:43.140 | INFO     | core.telegram.client_manager:_verify_client_authorization:1205 - 账户已授权: +***********
2025-07-22 16:48:45.146 | INFO     | core.telegram.client_manager:batch_auto_login:1379 - 批量登录完成: 总计 2, 成功 2, 失败 0
2025-07-22 16:48:45.555 | INFO     | app.services.account_service:batch_auto_login:1352 - 服务层：核心层登录完成: 成功 2, 失败 0 / 2
2025-07-22 16:48:45.555 | INFO     | app.services.account_service:_process_batch_login_results:1786 - 开始处理批量登录结果，共 2 个账户
2025-07-22 16:48:45.555 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-22 16:48:45.560 | INFO     | data.repositories.account_repo:update_account:394 - 更新账户成功: 2
2025-07-22 16:48:45.560 | INFO     | app.services.account_service:_process_batch_login_results:1822 - 已更新账户 +*********** 的用户信息
2025-07-22 16:48:45.565 | INFO     | data.repositories.account_repo:update_account:394 - 更新账户成功: 1
2025-07-22 16:48:45.565 | INFO     | app.services.account_service:_process_batch_login_results:1822 - 已更新账户 +*********** 的用户信息
2025-07-22 16:48:45.571 | INFO     | app.services.account_service:_process_batch_login_results:1845 - 批量登录结果处理完成，数据库已更新
2025-07-22 16:48:45.571 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-22 16:48:45.571 | INFO     | app.controllers.account_controller:auto_login_accounts:640 - 批量登录完成，触发账户数据刷新
2025-07-22 16:49:35.382 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-22 16:50:35.380 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-22 16:51:35.383 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-22 16:52:35.382 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-22 16:53:35.387 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-22 16:53:56.496 | INFO     | utils.logger:init_logger:98 - 日志系统初始化完成，默认级别: DEBUG，日志目录: H:\PyProject\TeleTest\APPDATA\logs
2025-07-22 16:53:57.512 | INFO     | data.database:init_database:31 - 初始化数据库连接: H:\PyProject\TeleTest\APPDATA\database\telegram_manager.db
2025-07-22 16:53:57.528 | DEBUG    | data.database:init_database:58 - 创建数据库表
2025-07-22 16:53:57.539 | INFO     | data.database:init_database:61 - 数据库初始化完成
2025-07-22 16:53:58.239 | DEBUG    | app.controllers.auth_controller:initialize_software:43 - 控制器: 初始化软件 app_id=1000, version=1.2.7
2025-07-22 16:53:58.240 | DEBUG    | core.auth.api_service:init_software:55 - 发送初始化请求: https://server.xile188.com/api/user/1000/windowns10/1.2.7/ini, params={}
2025-07-22 16:53:58.448 | DEBUG    | core.auth.api_service:init_software:66 - 初始化数据解密成功: {'new_content': '- 新增功能\n  - 1.新增图片上传功能\n  - 2.新增软件文档\n  - 3.新增软件自动更新，后续更新会自动检测版本更新，无需再下载更新包\n  - 4.新增设置账户信息及发信控制\n- 修复bug\n  - 删除监听任务时，不删除对应任务采集到的数据。\n  - 监听设置群无法发送到目标群\n  - 导出监听数据时应当导出单个任务而非所有数据\n- 优化记录\n  - 优化发信规则，直接调取符合要求的账户发送，无账户时才需要等待延时。\n  - 完善软件设置', 'new_url': None, 'notice': {'id': '2', 'content': '公告内容测试3', 'time': '**********'}}
2025-07-22 16:53:58.456 | INFO     | app.controllers.auth_controller:login:61 - 控制器: 用户登录 account=demo01
2025-07-22 16:54:01.429 | INFO     | core.auth.api_service:login:127 - 用户登录: account=demo01
2025-07-22 16:54:01.637 | INFO     | core.auth.api_service:get_user_info:347 - 获取用户信息
2025-07-22 16:54:01.866 | INFO     | core.auth.api_service:get_user_info:375 - 获取用户信息成功
2025-07-22 16:54:01.874 | INFO     | __main__:on_auth_completed:91 - 用户登录成功，正在启动主窗口...
2025-07-22 16:54:01.893 | INFO     | core.telegram.client_worker:__init__:61 - Telegram客户端工作线程初始化
2025-07-22 16:54:01.894 | INFO     | utils.vip_checker:initialize:25 - VIP检查器已初始化，等待登录后开始检查
2025-07-22 16:54:01.894 | INFO     | ui.main_window:_initialize_core_components:108 - MainWindow: 初始化核心组件...
2025-07-22 16:54:01.895 | INFO     | core.telegram.client_worker:run:65 - Telegram客户端工作线程开始运行
2025-07-22 16:54:01.895 | INFO     | core.proxy.proxy_task_manager:run:284 - 代理任务管理器线程启动
2025-07-22 16:54:01.896 | INFO     | app.services.account_service:__init__:44 - 账户服务初始化
2025-07-22 16:54:01.896 | INFO     | app.controllers.account_controller:__init__:82 - 账户控制器初始化
2025-07-22 16:54:01.896 | INFO     | app.services.monitor_service:__init__:39 - 初始化监控任务服务
2025-07-22 16:54:01.896 | INFO     | core.monitor.filter:__init__:26 - 初始化消息过滤器
2025-07-22 16:54:01.897 | INFO     | core.monitor.extractor:__init__:24 - 初始化关键词提取器
2025-07-22 16:54:01.897 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-07-22 16:54:01.897 | INFO     | core.monitor.notifier:__init__:90 - 初始化通知管理器
2025-07-22 16:54:01.897 | INFO     | core.telegram.client_manager:__init__:66 - Telegram客户端管理器初始化
2025-07-22 16:54:01.898 | INFO     | app.controllers.telegram_monitor_controller:__init__:64 - 初始化 Telegram 监控控制器
2025-07-22 16:54:01.898 | INFO     | core.telegram.user_manager:__init__:30 - Telegram用户管理器初始化
2025-07-22 16:54:01.898 | DEBUG    | app.controllers.telegram_monitor_controller:__init__:82 - 控制器初始化完成
2025-07-22 16:54:01.898 | INFO     | core.telegram.group_manager:__init__:36 - Telegram群组管理器初始化
2025-07-22 16:54:01.900 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-07-22 16:54:01.900 | INFO     | core.telegram.monitor_manager:__init__:26 - Telegram监控管理器初始化
2025-07-22 16:54:01.900 | INFO     | core.telegram.telegram_service:__init__:39 - Telegram服务初始化完成
2025-07-22 16:54:02.095 | INFO     | ui.views.monitor_view:__init__:48 - 初始化监控视图
2025-07-22 16:54:02.096 | DEBUG    | ui.views.monitor_view:_connect_signals:80 - 连接控制器信号
2025-07-22 16:54:02.278 | INFO     | ui.views.proxy_view:__init__:96 - 初始化视图，加载第一页代理数据
2025-07-22 16:54:02.517 | INFO     | __main__:on_auth_completed:95 - 主窗口已启动
2025-07-22 16:54:02.562 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:88 - 尝试连接监听日志信号...
2025-07-22 16:54:02.562 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:90 - 监听日志信号连接成功
2025-07-22 16:54:02.563 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-22 16:54:02.567 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-22 16:54:02.570 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:145 - 加载监控任务列表 (搜索关键词: '')
2025-07-22 16:54:02.571 | DEBUG    | ui.views.monitor_view:_on_loading_started:620 - 加载中: 正在加载任务列表...
2025-07-22 16:54:02.571 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-22 16:54:02.577 | INFO     | app.services.proxy_service:refresh_proxies:712 - 刷新代理列表: offset=0, limit=20
2025-07-22 16:54:02.577 | INFO     | core.proxy.proxy_task_manager:submit_task:172 - 提交耗时任务到子线程: refresh_proxies_0_20
2025-07-22 16:54:02.577 | DEBUG    | app.services.proxy_service:get_proxies:500 - 获取代理列表: offset=0, limit=20
2025-07-22 16:54:02.577 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-22 16:54:02.578 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-22 16:54:02.581 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-22 16:54:02.603 | INFO     | app.controllers.invite_controller:get_invite_tasks:38 - 获取邀请任务列表, status=None
2025-07-22 16:54:02.603 | INFO     | app.services.invite_service:get_invite_tasks:121 - 获取邀请任务列表, status=None
2025-07-22 16:54:02.604 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-22 16:54:02.604 | INFO     | app.services.invite_service:task_initialize:39 - 初始化邀请服务，重置运行中的任务为暂停状态
2025-07-22 16:54:02.604 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-22 16:54:02.608 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-22 16:54:02.610 | INFO     | core.proxy.proxy_task_manager:task_wrapper:199 - 任务 refresh_proxies_0_20 完成
2025-07-22 16:54:02.610 | INFO     | utils.vip_checker:start_checking:37 - 开始VIP状态检查
2025-07-22 16:54:02.610 | DEBUG    | utils.vip_checker:_check_vip_status:61 - 正在检查VIP状态...
2025-07-22 16:54:02.610 | INFO     | core.auth.api_service:verify_vip:425 - 会员验证接口调用
2025-07-22 16:54:02.827 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-22 16:54:02.829 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-22 16:54:02.836 | DEBUG    | ui.views.monitor_view:_refresh_task_list:175 - 刷新任务列表，共0个任务
2025-07-22 16:54:02.836 | INFO     | ui.views.monitor_view:_refresh_task_list:189 - 没有找到监控任务
2025-07-22 16:54:02.836 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-22 16:54:02.838 | INFO     | app.services.invite_service:task_initialize:48 - 成功重置 0 个任务状态
2025-07-22 16:54:02.838 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-22 16:54:02.839 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-22 16:54:02.843 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-22 16:54:02.844 | INFO     | app.services.account_service:get_all_groups:98 - 获取所有账户分组成功, 共 2 个
2025-07-22 16:54:02.845 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-22 16:54:02.846 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-22 16:54:02.848 | DEBUG    | ui.views.account_view:_on_groups_loaded:519 - 分组加载完成: 2个分组
2025-07-22 16:54:02.862 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-22 16:54:02.991 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-22 16:54:02.992 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-22 16:54:02.993 | INFO     | app.services.account_service:get_all_accounts:292 - 获取所有账户成功, 共 2 个
2025-07-22 16:54:02.993 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-22 16:54:02.995 | INFO     | app.controllers.telegram_monitor_controller:_load_total_users_count_internal:191 - 成功加载总体统计数据: 总用户数 23, 今日采集 0, 日均采集 23, 运行天数 1
2025-07-22 16:54:02.995 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:156 - 成功加载 0 个任务
2025-07-22 16:54:02.995 | DEBUG    | ui.views.monitor_view:_on_loading_finished:625 - 加载完成
2025-07-22 16:54:02.996 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-07-22 16:54:03.006 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-22 16:54:03.009 | DEBUG    | ui.views.account_view:_on_accounts_loaded:652 - 账户加载完成: 2个账户
2025-07-22 16:54:03.035 | DEBUG    | ui.views.account_view:_update_group_counts:188 - 分组计数已更新: {2: 2}
2025-07-22 16:54:03.044 | INFO     | ui.views.account_view:_auto_login_accounts:707 - 开始调用控制器进行 2 个账户的自动登录。
2025-07-22 16:54:03.044 | INFO     | app.services.account_service:batch_auto_login:1212 - 服务层：开始批量自动登录 2 个账户, 最大并发: 5
2025-07-22 16:54:03.044 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-22 16:54:03.063 | INFO     | app.services.account_service:get_all_accounts:292 - 获取所有账户成功, 共 2 个
2025-07-22 16:54:03.063 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-22 16:54:03.064 | INFO     | app.services.account_service:batch_auto_login:1251 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-07-22 16:54:03.065 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-22 16:54:03.067 | DEBUG    | ui.views.account_view:_update_group_counts:188 - 分组计数已更新: {2: 2}
2025-07-22 16:54:03.067 | DEBUG    | ui.views.account_view:_init_cache:155 - 账户缓存初始化完成，共 2 个账户
2025-07-22 16:54:03.073 | DEBUG    | ui.views.account_view:_on_accounts_loaded:652 - 账户加载完成: 2个账户
2025-07-22 16:54:03.098 | DEBUG    | ui.views.account_view:_update_group_counts:188 - 分组计数已更新: {2: 2}
2025-07-22 16:54:03.105 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-22 16:54:03.110 | INFO     | app.services.account_service:batch_auto_login:1251 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-07-22 16:54:03.110 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-22 16:54:03.112 | INFO     | app.services.account_service:batch_auto_login:1321 - 服务层：准备调用核心层处理 2 个账户的自动登录。
2025-07-22 16:54:03.112 | INFO     | app.services.account_service:batch_auto_login:1331 - 服务层：设置核心层任务超时为 120 秒。
2025-07-22 16:54:03.113 | INFO     | core.telegram.client_manager:batch_auto_login:1230 - 开始批量自动登录, 共 2 个账户, 最大并发 5
2025-07-22 16:54:03.113 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:271 - 创建客户端: H:\PyProject\TeleTest\APPDATA\sessions\+***********, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-22 16:54:03.113 | DEBUG    | core.telegram.client_manager:get_proxy:96 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-22 16:54:03.114 | DEBUG    | core.telegram.client_manager:get_proxy:143 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-07-22 16:54:03.118 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:271 - 创建客户端: H:\PyProject\TeleTest\APPDATA\sessions\+***********, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-22 16:54:03.119 | DEBUG    | core.telegram.client_manager:get_proxy:96 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-22 16:54:03.119 | DEBUG    | core.telegram.client_manager:get_proxy:143 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-07-22 16:54:03.133 | INFO     | core.auth.api_service:verify_vip:438 - 会员验证成功
2025-07-22 16:54:03.133 | INFO     | utils.vip_checker:_check_vip_status:76 - VIP状态有效
2025-07-22 16:54:03.147 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-22 16:54:03.287 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-22 16:54:03.431 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-22 16:54:03.511 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-07-22 16:54:03.513 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-07-22 16:54:03.515 | INFO     | ui.views.proxy_view:_load_initial_data:1044 - 初始数据加载完成，共 1 个代理
2025-07-22 16:54:06.832 | INFO     | core.telegram.client_manager:_connect_client:219 - 连接成功!
2025-07-22 16:54:07.793 | INFO     | core.telegram.client_manager:_connect_client:219 - 连接成功!
2025-07-22 16:54:08.314 | INFO     | core.telegram.client_manager:_verify_client_authorization:1205 - 账户已授权: +***********
2025-07-22 16:54:08.867 | INFO     | core.telegram.client_manager:_verify_client_authorization:1205 - 账户已授权: +***********
2025-07-22 16:54:10.880 | INFO     | core.telegram.client_manager:batch_auto_login:1379 - 批量登录完成: 总计 2, 成功 2, 失败 0
2025-07-22 16:54:11.193 | INFO     | app.services.account_service:batch_auto_login:1352 - 服务层：核心层登录完成: 成功 2, 失败 0 / 2
2025-07-22 16:54:11.194 | INFO     | app.services.account_service:_process_batch_login_results:1786 - 开始处理批量登录结果，共 2 个账户
2025-07-22 16:54:11.194 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-22 16:54:11.249 | INFO     | data.repositories.account_repo:update_account:394 - 更新账户成功: 2
2025-07-22 16:54:11.249 | INFO     | app.services.account_service:_process_batch_login_results:1822 - 已更新账户 +*********** 的用户信息
2025-07-22 16:54:11.261 | INFO     | data.repositories.account_repo:update_account:394 - 更新账户成功: 1
2025-07-22 16:54:11.261 | INFO     | app.services.account_service:_process_batch_login_results:1822 - 已更新账户 +*********** 的用户信息
2025-07-22 16:54:11.273 | INFO     | app.services.account_service:_process_batch_login_results:1845 - 批量登录结果处理完成，数据库已更新
2025-07-22 16:54:11.273 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-22 16:54:11.273 | INFO     | app.controllers.account_controller:auto_login_accounts:640 - 批量登录完成，触发账户数据刷新
2025-07-22 16:54:19.898 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-22 16:54:19.907 | INFO     | app.services.account_service:get_accounts_by_group:314 - 获取分组 2 的账户成功, 共 2 个
2025-07-22 16:54:19.907 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-22 16:54:19.908 | DEBUG    | ui.views.account_view:_on_accounts_loaded:652 - 账户加载完成: 2个账户
2025-07-22 16:54:21.005 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-22 16:54:21.010 | INFO     | app.services.account_service:get_all_accounts:292 - 获取所有账户成功, 共 2 个
2025-07-22 16:54:21.011 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-22 16:54:21.011 | DEBUG    | ui.views.account_view:_on_accounts_loaded:652 - 账户加载完成: 2个账户
2025-07-22 16:54:21.030 | DEBUG    | ui.views.account_view:_update_group_counts:188 - 分组计数已更新: {2: 2}
2025-07-22 16:54:46.349 | INFO     | app.services.account_service:batch_update_profiles:565 - 批量更新账户资料: 0 个
2025-07-22 16:54:46.349 | INFO     | app.services.account_service:_batch_update_with_username_template:1680 - 批量更新包含用户名模板的账户资料: 0 个
2025-07-22 16:54:46.349 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-22 16:54:46.350 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-22 16:55:01.869 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-22 19:26:08.343 | INFO     | utils.logger:init_logger:98 - 日志系统初始化完成，默认级别: DEBUG，日志目录: H:\PyProject\TeleTest\APPDATA\logs
2025-07-22 19:26:09.487 | INFO     | data.database:init_database:31 - 初始化数据库连接: H:\PyProject\TeleTest\APPDATA\database\telegram_manager.db
2025-07-22 19:26:09.504 | DEBUG    | data.database:init_database:58 - 创建数据库表
2025-07-22 19:26:09.515 | INFO     | data.database:init_database:61 - 数据库初始化完成
2025-07-22 19:26:10.596 | DEBUG    | app.controllers.auth_controller:initialize_software:43 - 控制器: 初始化软件 app_id=1000, version=1.2.7
2025-07-22 19:26:10.596 | DEBUG    | core.auth.api_service:init_software:55 - 发送初始化请求: https://server.xile188.com/api/user/1000/windowns10/1.2.7/ini, params={}
2025-07-22 19:26:11.029 | DEBUG    | core.auth.api_service:init_software:66 - 初始化数据解密成功: {'new_content': '- 新增功能\n  - 1.新增图片上传功能\n  - 2.新增软件文档\n  - 3.新增软件自动更新，后续更新会自动检测版本更新，无需再下载更新包\n  - 4.新增设置账户信息及发信控制\n- 修复bug\n  - 删除监听任务时，不删除对应任务采集到的数据。\n  - 监听设置群无法发送到目标群\n  - 导出监听数据时应当导出单个任务而非所有数据\n- 优化记录\n  - 优化发信规则，直接调取符合要求的账户发送，无账户时才需要等待延时。\n  - 完善软件设置', 'new_url': None, 'notice': {'id': '2', 'content': '公告内容测试3', 'time': '**********'}}
2025-07-22 19:26:11.035 | INFO     | app.controllers.auth_controller:login:61 - 控制器: 用户登录 account=demo01
2025-07-22 19:26:13.998 | INFO     | core.auth.api_service:login:127 - 用户登录: account=demo01
2025-07-22 19:26:14.237 | INFO     | core.auth.api_service:get_user_info:347 - 获取用户信息
2025-07-22 19:26:14.493 | INFO     | core.auth.api_service:get_user_info:375 - 获取用户信息成功
2025-07-22 19:26:14.499 | INFO     | __main__:on_auth_completed:91 - 用户登录成功，正在启动主窗口...
2025-07-22 19:26:14.521 | INFO     | core.telegram.client_worker:__init__:61 - Telegram客户端工作线程初始化
2025-07-22 19:26:14.521 | INFO     | utils.vip_checker:initialize:25 - VIP检查器已初始化，等待登录后开始检查
2025-07-22 19:26:14.521 | INFO     | ui.main_window:_initialize_core_components:112 - MainWindow: 初始化核心组件...
2025-07-22 19:26:14.522 | INFO     | app.services.account_service:__init__:44 - 账户服务初始化
2025-07-22 19:26:14.522 | INFO     | app.controllers.account_controller:__init__:82 - 账户控制器初始化
2025-07-22 19:26:14.523 | INFO     | core.telegram.client_worker:run:65 - Telegram客户端工作线程开始运行
2025-07-22 19:26:14.523 | INFO     | app.services.monitor_service:__init__:39 - 初始化监控任务服务
2025-07-22 19:26:14.523 | INFO     | core.proxy.proxy_task_manager:run:284 - 代理任务管理器线程启动
2025-07-22 19:26:14.523 | INFO     | core.monitor.filter:__init__:26 - 初始化消息过滤器
2025-07-22 19:26:14.523 | INFO     | core.monitor.extractor:__init__:24 - 初始化关键词提取器
2025-07-22 19:26:14.523 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-07-22 19:26:14.524 | INFO     | core.monitor.notifier:__init__:90 - 初始化通知管理器
2025-07-22 19:26:14.524 | INFO     | app.controllers.telegram_monitor_controller:__init__:64 - 初始化 Telegram 监控控制器
2025-07-22 19:26:14.524 | DEBUG    | app.controllers.telegram_monitor_controller:__init__:82 - 控制器初始化完成
2025-07-22 19:26:14.526 | INFO     | core.telegram.client_manager:__init__:66 - Telegram客户端管理器初始化
2025-07-22 19:26:14.527 | INFO     | core.telegram.user_manager:__init__:30 - Telegram用户管理器初始化
2025-07-22 19:26:14.527 | INFO     | core.telegram.group_manager:__init__:36 - Telegram群组管理器初始化
2025-07-22 19:26:14.527 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-07-22 19:26:14.527 | INFO     | core.telegram.monitor_manager:__init__:26 - Telegram监控管理器初始化
2025-07-22 19:26:14.528 | INFO     | core.telegram.telegram_service:__init__:39 - Telegram服务初始化完成
2025-07-22 19:26:14.665 | INFO     | app.controllers.convert_controller:set_account_controller:35 - 账户控制器已设置
2025-07-22 19:26:14.765 | INFO     | ui.views.monitor_view:__init__:48 - 初始化监控视图
2025-07-22 19:26:14.765 | DEBUG    | ui.views.monitor_view:_connect_signals:80 - 连接控制器信号
2025-07-22 19:26:14.946 | INFO     | ui.views.proxy_view:__init__:96 - 初始化视图，加载第一页代理数据
2025-07-22 19:26:15.042 | INFO     | ui.main_window:_setup_log_managers:421 - 已为 9 个视图创建日志管理器
2025-07-22 19:26:15.252 | INFO     | __main__:on_auth_completed:95 - 主窗口已启动
2025-07-22 19:26:15.298 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:88 - 尝试连接监听日志信号...
2025-07-22 19:26:15.298 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:90 - 监听日志信号连接成功
2025-07-22 19:26:15.299 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-22 19:26:15.303 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-22 19:26:15.309 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:145 - 加载监控任务列表 (搜索关键词: '')
2025-07-22 19:26:15.310 | DEBUG    | ui.views.monitor_view:_on_loading_started:620 - 加载中: 正在加载任务列表...
2025-07-22 19:26:15.310 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-22 19:26:15.316 | INFO     | app.services.proxy_service:refresh_proxies:712 - 刷新代理列表: offset=0, limit=20
2025-07-22 19:26:15.316 | INFO     | core.proxy.proxy_task_manager:submit_task:172 - 提交耗时任务到子线程: refresh_proxies_0_20
2025-07-22 19:26:15.316 | DEBUG    | app.services.proxy_service:get_proxies:500 - 获取代理列表: offset=0, limit=20
2025-07-22 19:26:15.317 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-22 19:26:15.317 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-22 19:26:15.319 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-22 19:26:15.321 | INFO     | app.controllers.invite_controller:get_invite_tasks:38 - 获取邀请任务列表, status=None
2025-07-22 19:26:15.321 | INFO     | app.services.invite_service:get_invite_tasks:121 - 获取邀请任务列表, status=None
2025-07-22 19:26:15.322 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-22 19:26:15.340 | INFO     | app.services.invite_service:task_initialize:39 - 初始化邀请服务，重置运行中的任务为暂停状态
2025-07-22 19:26:15.344 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-22 19:26:15.348 | INFO     | utils.vip_checker:start_checking:37 - 开始VIP状态检查
2025-07-22 19:26:15.348 | DEBUG    | utils.vip_checker:_check_vip_status:61 - 正在检查VIP状态...
2025-07-22 19:26:15.348 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-22 19:26:15.349 | INFO     | core.auth.api_service:verify_vip:425 - 会员验证接口调用
2025-07-22 19:26:15.350 | INFO     | core.proxy.proxy_task_manager:task_wrapper:199 - 任务 refresh_proxies_0_20 完成
2025-07-22 19:26:15.485 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-22 19:26:15.485 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-22 19:26:15.570 | INFO     | app.services.invite_service:task_initialize:48 - 成功重置 0 个任务状态
2025-07-22 19:26:15.571 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-22 19:26:15.571 | DEBUG    | ui.views.monitor_view:_refresh_task_list:175 - 刷新任务列表，共0个任务
2025-07-22 19:26:15.572 | INFO     | ui.views.monitor_view:_refresh_task_list:189 - 没有找到监控任务
2025-07-22 19:26:15.572 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-22 19:26:15.577 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-22 19:26:15.581 | INFO     | app.services.account_service:get_all_groups:98 - 获取所有账户分组成功, 共 2 个
2025-07-22 19:26:15.581 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-22 19:26:15.584 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-22 19:26:15.585 | DEBUG    | ui.views.account_view:_on_groups_loaded:519 - 分组加载完成: 2个分组
2025-07-22 19:26:15.603 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-22 19:26:15.606 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-22 19:26:15.669 | INFO     | app.services.account_service:get_all_accounts:292 - 获取所有账户成功, 共 2 个
2025-07-22 19:26:15.669 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-22 19:26:15.670 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-22 19:26:15.671 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-22 19:26:15.672 | DEBUG    | ui.views.account_view:_on_accounts_loaded:652 - 账户加载完成: 2个账户
2025-07-22 19:26:15.690 | DEBUG    | ui.views.account_view:_update_group_counts:188 - 分组计数已更新: {2: 2}
2025-07-22 19:26:15.695 | INFO     | ui.views.account_view:_auto_login_accounts:707 - 开始调用控制器进行 2 个账户的自动登录。
2025-07-22 19:26:15.695 | INFO     | app.services.account_service:batch_auto_login:1212 - 服务层：开始批量自动登录 2 个账户, 最大并发: 5
2025-07-22 19:26:15.695 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-22 19:26:15.699 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-22 19:26:15.702 | INFO     | app.controllers.telegram_monitor_controller:_load_total_users_count_internal:191 - 成功加载总体统计数据: 总用户数 23, 今日采集 0, 日均采集 23, 运行天数 1
2025-07-22 19:26:15.702 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:156 - 成功加载 0 个任务
2025-07-22 19:26:15.702 | DEBUG    | ui.views.monitor_view:_on_loading_finished:625 - 加载完成
2025-07-22 19:26:15.707 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-07-22 19:26:15.721 | INFO     | app.services.account_service:batch_auto_login:1251 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-07-22 19:26:15.721 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-22 19:26:15.722 | INFO     | app.services.account_service:get_all_accounts:292 - 获取所有账户成功, 共 2 个
2025-07-22 19:26:15.722 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-22 19:26:15.724 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-22 19:26:15.726 | DEBUG    | ui.views.account_view:_update_group_counts:188 - 分组计数已更新: {2: 2}
2025-07-22 19:26:15.726 | DEBUG    | ui.views.account_view:_init_cache:155 - 账户缓存初始化完成，共 2 个账户
2025-07-22 19:26:15.733 | DEBUG    | ui.views.account_view:_on_accounts_loaded:652 - 账户加载完成: 2个账户
2025-07-22 19:26:15.757 | DEBUG    | ui.views.account_view:_update_group_counts:188 - 分组计数已更新: {2: 2}
2025-07-22 19:26:15.768 | INFO     | core.auth.api_service:verify_vip:438 - 会员验证成功
2025-07-22 19:26:15.768 | INFO     | utils.vip_checker:_check_vip_status:76 - VIP状态有效
2025-07-22 19:26:15.778 | INFO     | app.services.account_service:batch_auto_login:1251 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-07-22 19:26:15.779 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-22 19:26:15.781 | INFO     | app.services.account_service:batch_auto_login:1321 - 服务层：准备调用核心层处理 2 个账户的自动登录。
2025-07-22 19:26:15.782 | INFO     | app.services.account_service:batch_auto_login:1331 - 服务层：设置核心层任务超时为 120 秒。
2025-07-22 19:26:15.782 | INFO     | core.telegram.client_manager:batch_auto_login:1230 - 开始批量自动登录, 共 2 个账户, 最大并发 5
2025-07-22 19:26:15.782 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:271 - 创建客户端: H:\PyProject\TeleTest\APPDATA\sessions\+***********, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-22 19:26:15.783 | DEBUG    | core.telegram.client_manager:get_proxy:96 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-22 19:26:15.783 | DEBUG    | core.telegram.client_manager:get_proxy:143 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-07-22 19:26:15.789 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:271 - 创建客户端: H:\PyProject\TeleTest\APPDATA\sessions\+***********, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-22 19:26:15.790 | DEBUG    | core.telegram.client_manager:get_proxy:96 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-22 19:26:15.790 | DEBUG    | core.telegram.client_manager:get_proxy:143 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-07-22 19:26:15.816 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-22 19:26:15.891 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-22 19:26:15.944 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-22 19:26:15.964 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-07-22 19:26:15.967 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-07-22 19:26:15.969 | INFO     | ui.views.proxy_view:_load_initial_data:1044 - 初始数据加载完成，共 1 个代理
2025-07-22 19:26:19.053 | INFO     | core.telegram.client_manager:_connect_client:219 - 连接成功!
2025-07-22 19:26:19.945 | INFO     | core.telegram.client_manager:_verify_client_authorization:1205 - 账户已授权: +***********
2025-07-22 19:26:20.813 | INFO     | core.telegram.client_manager:_connect_client:219 - 连接成功!
2025-07-22 19:26:22.098 | INFO     | core.telegram.client_manager:_verify_client_authorization:1205 - 账户已授权: +***********
2025-07-22 19:26:24.097 | INFO     | core.telegram.client_manager:batch_auto_login:1379 - 批量登录完成: 总计 2, 成功 2, 失败 0
2025-07-22 19:26:24.788 | INFO     | app.services.account_service:batch_auto_login:1352 - 服务层：核心层登录完成: 成功 2, 失败 0 / 2
2025-07-22 19:26:24.788 | INFO     | app.services.account_service:_process_batch_login_results:1786 - 开始处理批量登录结果，共 2 个账户
2025-07-22 19:26:24.788 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-22 19:26:24.796 | INFO     | data.repositories.account_repo:update_account:394 - 更新账户成功: 2
2025-07-22 19:26:24.797 | INFO     | app.services.account_service:_process_batch_login_results:1822 - 已更新账户 +*********** 的用户信息
2025-07-22 19:26:24.811 | INFO     | data.repositories.account_repo:update_account:394 - 更新账户成功: 1
2025-07-22 19:26:24.811 | INFO     | app.services.account_service:_process_batch_login_results:1822 - 已更新账户 +*********** 的用户信息
2025-07-22 19:26:24.818 | INFO     | app.services.account_service:_process_batch_login_results:1845 - 批量登录结果处理完成，数据库已更新
2025-07-22 19:26:24.818 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-22 19:26:24.818 | INFO     | app.controllers.account_controller:auto_login_accounts:640 - 批量登录完成，触发账户数据刷新
2025-07-22 19:26:44.375 | INFO     | ui.views.convert_view:log_message:967 - 已导入 0 个文件
2025-07-22 19:26:45.754 | INFO     | ui.main_window:closeEvent:431 - MainWindow: 接收到关闭事件
2025-07-22 19:26:45.776 | INFO     | ui.main_window:_cleanup_before_quit:272 - MainWindow: 执行清理资源...
2025-07-22 19:26:45.776 | INFO     | core.telegram.client_worker:stop:108 - 正在停止Telegram客户端工作线程
2025-07-22 19:26:45.782 | INFO     | core.telegram.client_manager:cleanup_async:1150 - 开始清理资源并断开所有连接
2025-07-22 19:26:45.782 | INFO     | core.telegram.client_manager:disconnect_all_clients:735 - 正在断开所有客户端连接
2025-07-22 19:26:45.783 | INFO     | core.telegram.client_manager:_safe_disconnect:756 - 正在断开客户端连接: +***********
2025-07-22 19:26:45.783 | INFO     | core.telegram.client_manager:_safe_disconnect:756 - 正在断开客户端连接: +***********
2025-07-22 19:26:45.792 | INFO     | core.telegram.client_manager:_safe_disconnect:760 - 断开客户端连接成功: +***********
2025-07-22 19:26:45.792 | INFO     | core.telegram.client_manager:_safe_disconnect:760 - 断开客户端连接成功: +***********
2025-07-22 19:26:45.792 | INFO     | core.telegram.client_manager:cleanup_async:1169 - 资源清理完成
2025-07-22 19:26:46.283 | INFO     | core.telegram.client_manager:cleanup_async:1150 - 开始清理资源并断开所有连接
2025-07-22 19:26:46.283 | INFO     | core.telegram.client_manager:disconnect_all_clients:735 - 正在断开所有客户端连接
2025-07-22 19:26:46.283 | INFO     | core.telegram.client_manager:cleanup_async:1169 - 资源清理完成
2025-07-22 19:26:46.798 | INFO     | core.telegram.client_worker:run:97 - Telegram客户端工作线程已退出
2025-07-22 19:26:46.799 | INFO     | core.telegram.client_worker:stop:122 - Telegram客户端工作线程已停止
2025-07-22 19:26:46.799 | INFO     | ui.main_window:_cleanup_before_quit:284 - TelegramClientWorker 已停止。
2025-07-22 19:26:46.799 | INFO     | ui.main_window:_cleanup_before_quit:288 - MainWindow 清理完成
2025-07-22 19:26:46.820 | INFO     | __main__:main:109 - 应用程序已正常退出
2025-07-22 20:45:00.670 | INFO     | utils.logger:init_logger:98 - 日志系统初始化完成，默认级别: DEBUG，日志目录: H:\PyProject\TeleTest\APPDATA\logs
2025-07-22 20:45:01.806 | INFO     | data.database:init_database:31 - 初始化数据库连接: H:\PyProject\TeleTest\APPDATA\database\telegram_manager.db
2025-07-22 20:45:01.825 | DEBUG    | data.database:init_database:58 - 创建数据库表
2025-07-22 20:45:01.842 | INFO     | data.database:init_database:61 - 数据库初始化完成
2025-07-22 20:45:02.918 | DEBUG    | app.controllers.auth_controller:initialize_software:43 - 控制器: 初始化软件 app_id=1000, version=1.2.7
2025-07-22 20:45:02.918 | DEBUG    | core.auth.api_service:init_software:55 - 发送初始化请求: https://server.xile188.com/api/user/1000/windowns10/1.2.7/ini, params={}
2025-07-22 20:45:03.158 | DEBUG    | core.auth.api_service:init_software:66 - 初始化数据解密成功: {'new_content': '- 新增功能\n  - 1.新增图片上传功能\n  - 2.新增软件文档\n  - 3.新增软件自动更新，后续更新会自动检测版本更新，无需再下载更新包\n  - 4.新增设置账户信息及发信控制\n- 修复bug\n  - 删除监听任务时，不删除对应任务采集到的数据。\n  - 监听设置群无法发送到目标群\n  - 导出监听数据时应当导出单个任务而非所有数据\n- 优化记录\n  - 优化发信规则，直接调取符合要求的账户发送，无账户时才需要等待延时。\n  - 完善软件设置', 'new_url': None, 'notice': {'id': '2', 'content': '公告内容测试3', 'time': '**********'}}
2025-07-22 20:45:03.172 | INFO     | app.controllers.auth_controller:login:61 - 控制器: 用户登录 account=demo01
2025-07-22 20:45:07.263 | INFO     | core.auth.api_service:login:127 - 用户登录: account=demo01
2025-07-22 20:45:07.467 | INFO     | core.auth.api_service:get_user_info:347 - 获取用户信息
2025-07-22 20:45:07.660 | INFO     | core.auth.api_service:get_user_info:375 - 获取用户信息成功
2025-07-22 20:45:07.667 | INFO     | __main__:on_auth_completed:91 - 用户登录成功，正在启动主窗口...
2025-07-22 20:45:07.697 | INFO     | core.telegram.client_worker:__init__:61 - Telegram客户端工作线程初始化
2025-07-22 20:45:07.697 | INFO     | utils.vip_checker:initialize:25 - VIP检查器已初始化，等待登录后开始检查
2025-07-22 20:45:07.697 | INFO     | ui.main_window:_initialize_core_components:112 - MainWindow: 初始化核心组件...
2025-07-22 20:45:07.698 | INFO     | app.services.account_service:__init__:44 - 账户服务初始化
2025-07-22 20:45:07.698 | INFO     | core.telegram.client_worker:run:65 - Telegram客户端工作线程开始运行
2025-07-22 20:45:07.699 | INFO     | app.controllers.account_controller:__init__:82 - 账户控制器初始化
2025-07-22 20:45:07.699 | INFO     | core.proxy.proxy_task_manager:run:284 - 代理任务管理器线程启动
2025-07-22 20:45:07.700 | INFO     | app.services.monitor_service:__init__:39 - 初始化监控任务服务
2025-07-22 20:45:07.700 | INFO     | core.monitor.filter:__init__:26 - 初始化消息过滤器
2025-07-22 20:45:07.700 | INFO     | core.monitor.extractor:__init__:24 - 初始化关键词提取器
2025-07-22 20:45:07.700 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-07-22 20:45:07.701 | INFO     | core.monitor.notifier:__init__:90 - 初始化通知管理器
2025-07-22 20:45:07.701 | INFO     | app.controllers.telegram_monitor_controller:__init__:64 - 初始化 Telegram 监控控制器
2025-07-22 20:45:07.701 | DEBUG    | app.controllers.telegram_monitor_controller:__init__:82 - 控制器初始化完成
2025-07-22 20:45:07.702 | INFO     | core.telegram.client_manager:__init__:66 - Telegram客户端管理器初始化
2025-07-22 20:45:07.704 | INFO     | core.telegram.user_manager:__init__:30 - Telegram用户管理器初始化
2025-07-22 20:45:07.705 | INFO     | core.telegram.group_manager:__init__:36 - Telegram群组管理器初始化
2025-07-22 20:45:07.705 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-07-22 20:45:07.705 | INFO     | core.telegram.monitor_manager:__init__:26 - Telegram监控管理器初始化
2025-07-22 20:45:07.705 | INFO     | core.telegram.telegram_service:__init__:39 - Telegram服务初始化完成
2025-07-22 20:45:07.860 | INFO     | app.controllers.convert_controller:set_account_controller:35 - 账户控制器已设置
2025-07-22 20:45:07.970 | INFO     | ui.views.monitor_view:__init__:48 - 初始化监控视图
2025-07-22 20:45:07.970 | DEBUG    | ui.views.monitor_view:_connect_signals:80 - 连接控制器信号
2025-07-22 20:45:08.166 | INFO     | ui.views.proxy_view:__init__:96 - 初始化视图，加载第一页代理数据
2025-07-22 20:45:08.263 | INFO     | ui.main_window:_setup_log_managers:421 - 已为 9 个视图创建日志管理器
2025-07-22 20:45:08.469 | INFO     | __main__:on_auth_completed:95 - 主窗口已启动
2025-07-22 20:45:08.515 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:88 - 尝试连接监听日志信号...
2025-07-22 20:45:08.515 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:90 - 监听日志信号连接成功
2025-07-22 20:45:08.515 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-22 20:45:08.520 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-22 20:45:08.526 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:145 - 加载监控任务列表 (搜索关键词: '')
2025-07-22 20:45:08.526 | DEBUG    | ui.views.monitor_view:_on_loading_started:620 - 加载中: 正在加载任务列表...
2025-07-22 20:45:08.527 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-22 20:45:08.533 | INFO     | app.services.proxy_service:refresh_proxies:712 - 刷新代理列表: offset=0, limit=20
2025-07-22 20:45:08.533 | INFO     | core.proxy.proxy_task_manager:submit_task:172 - 提交耗时任务到子线程: refresh_proxies_0_20
2025-07-22 20:45:08.533 | DEBUG    | app.services.proxy_service:get_proxies:500 - 获取代理列表: offset=0, limit=20
2025-07-22 20:45:08.533 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-22 20:45:08.534 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-22 20:45:08.537 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-22 20:45:08.561 | INFO     | app.controllers.invite_controller:get_invite_tasks:38 - 获取邀请任务列表, status=None
2025-07-22 20:45:08.561 | INFO     | app.services.invite_service:get_invite_tasks:121 - 获取邀请任务列表, status=None
2025-07-22 20:45:08.561 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-22 20:45:08.563 | INFO     | app.services.invite_service:task_initialize:39 - 初始化邀请服务，重置运行中的任务为暂停状态
2025-07-22 20:45:08.563 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-22 20:45:08.566 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-22 20:45:08.567 | INFO     | core.proxy.proxy_task_manager:task_wrapper:199 - 任务 refresh_proxies_0_20 完成
2025-07-22 20:45:08.568 | INFO     | utils.vip_checker:start_checking:37 - 开始VIP状态检查
2025-07-22 20:45:08.568 | DEBUG    | utils.vip_checker:_check_vip_status:61 - 正在检查VIP状态...
2025-07-22 20:45:08.569 | INFO     | core.auth.api_service:verify_vip:425 - 会员验证接口调用
2025-07-22 20:45:08.705 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-22 20:45:08.707 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-22 20:45:08.783 | INFO     | app.services.invite_service:task_initialize:48 - 成功重置 0 个任务状态
2025-07-22 20:45:08.783 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-22 20:45:08.784 | DEBUG    | ui.views.monitor_view:_refresh_task_list:175 - 刷新任务列表，共0个任务
2025-07-22 20:45:08.785 | INFO     | ui.views.monitor_view:_refresh_task_list:189 - 没有找到监控任务
2025-07-22 20:45:08.785 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-22 20:45:08.788 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-22 20:45:08.793 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-22 20:45:08.794 | INFO     | app.services.account_service:get_all_groups:98 - 获取所有账户分组成功, 共 2 个
2025-07-22 20:45:08.795 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-22 20:45:08.797 | DEBUG    | ui.views.account_view:_on_groups_loaded:519 - 分组加载完成: 2个分组
2025-07-22 20:45:08.814 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-22 20:45:08.820 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-22 20:45:08.892 | INFO     | app.services.account_service:get_all_accounts:292 - 获取所有账户成功, 共 2 个
2025-07-22 20:45:08.893 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-22 20:45:08.894 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-22 20:45:08.895 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-22 20:45:08.895 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-22 20:45:08.896 | DEBUG    | ui.views.account_view:_on_accounts_loaded:652 - 账户加载完成: 2个账户
2025-07-22 20:45:08.927 | DEBUG    | ui.views.account_view:_update_group_counts:188 - 分组计数已更新: {2: 2}
2025-07-22 20:45:08.931 | INFO     | ui.views.account_view:_auto_login_accounts:707 - 开始调用控制器进行 2 个账户的自动登录。
2025-07-22 20:45:08.931 | INFO     | app.services.account_service:batch_auto_login:1212 - 服务层：开始批量自动登录 2 个账户, 最大并发: 5
2025-07-22 20:45:08.931 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-22 20:45:08.937 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-07-22 20:45:08.943 | INFO     | app.controllers.telegram_monitor_controller:_load_total_users_count_internal:191 - 成功加载总体统计数据: 总用户数 23, 今日采集 0, 日均采集 23, 运行天数 1
2025-07-22 20:45:08.944 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:156 - 成功加载 0 个任务
2025-07-22 20:45:08.944 | DEBUG    | ui.views.monitor_view:_on_loading_finished:625 - 加载完成
2025-07-22 20:45:08.955 | INFO     | app.services.account_service:get_all_accounts:292 - 获取所有账户成功, 共 2 个
2025-07-22 20:45:08.955 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-22 20:45:08.956 | INFO     | app.services.account_service:batch_auto_login:1251 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-07-22 20:45:08.956 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-22 20:45:08.957 | INFO     | core.auth.api_service:verify_vip:438 - 会员验证成功
2025-07-22 20:45:08.958 | INFO     | utils.vip_checker:_check_vip_status:76 - VIP状态有效
2025-07-22 20:45:08.959 | DEBUG    | ui.views.account_view:_update_group_counts:188 - 分组计数已更新: {2: 2}
2025-07-22 20:45:08.959 | DEBUG    | ui.views.account_view:_init_cache:155 - 账户缓存初始化完成，共 2 个账户
2025-07-22 20:45:08.964 | DEBUG    | ui.views.account_view:_on_accounts_loaded:652 - 账户加载完成: 2个账户
2025-07-22 20:45:08.985 | DEBUG    | ui.views.account_view:_update_group_counts:188 - 分组计数已更新: {2: 2}
2025-07-22 20:45:08.991 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-22 20:45:09.004 | INFO     | app.services.account_service:batch_auto_login:1251 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-07-22 20:45:09.004 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-22 20:45:09.006 | INFO     | app.services.account_service:batch_auto_login:1321 - 服务层：准备调用核心层处理 2 个账户的自动登录。
2025-07-22 20:45:09.006 | INFO     | app.services.account_service:batch_auto_login:1331 - 服务层：设置核心层任务超时为 120 秒。
2025-07-22 20:45:09.006 | INFO     | core.telegram.client_manager:batch_auto_login:1230 - 开始批量自动登录, 共 2 个账户, 最大并发 5
2025-07-22 20:45:09.008 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:271 - 创建客户端: H:\PyProject\TeleTest\APPDATA\sessions\+***********, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-22 20:45:09.009 | DEBUG    | core.telegram.client_manager:get_proxy:96 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-22 20:45:09.009 | DEBUG    | core.telegram.client_manager:get_proxy:143 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-07-22 20:45:09.016 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:271 - 创建客户端: H:\PyProject\TeleTest\APPDATA\sessions\+***********, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-22 20:45:09.016 | DEBUG    | core.telegram.client_manager:get_proxy:96 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-22 20:45:09.017 | DEBUG    | core.telegram.client_manager:get_proxy:143 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-07-22 20:45:09.047 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-22 20:45:09.149 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-22 20:45:09.187 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-07-22 20:45:09.189 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-07-22 20:45:09.190 | INFO     | ui.views.proxy_view:_load_initial_data:1044 - 初始数据加载完成，共 1 个代理
2025-07-22 20:45:09.199 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-22 20:45:12.355 | INFO     | core.telegram.client_manager:_connect_client:219 - 连接成功!
2025-07-22 20:45:14.493 | INFO     | core.telegram.client_manager:_connect_client:219 - 连接成功!
2025-07-22 20:45:14.642 | INFO     | core.telegram.client_manager:_verify_client_authorization:1205 - 账户已授权: +***********
2025-07-22 20:45:17.552 | INFO     | core.telegram.client_manager:_verify_client_authorization:1205 - 账户已授权: +***********
2025-07-22 20:45:19.823 | INFO     | core.telegram.client_manager:batch_auto_login:1379 - 批量登录完成: 总计 2, 成功 2, 失败 0
2025-07-22 20:45:20.822 | INFO     | app.services.account_service:batch_auto_login:1352 - 服务层：核心层登录完成: 成功 2, 失败 0 / 2
2025-07-22 20:45:20.822 | INFO     | app.services.account_service:_process_batch_login_results:1786 - 开始处理批量登录结果，共 2 个账户
2025-07-22 20:45:20.823 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-22 20:45:20.829 | INFO     | data.repositories.account_repo:update_account:394 - 更新账户成功: 2
2025-07-22 20:45:20.829 | INFO     | app.services.account_service:_process_batch_login_results:1822 - 已更新账户 +*********** 的用户信息
2025-07-22 20:45:20.836 | INFO     | data.repositories.account_repo:update_account:394 - 更新账户成功: 1
2025-07-22 20:45:20.836 | INFO     | app.services.account_service:_process_batch_login_results:1822 - 已更新账户 +*********** 的用户信息
2025-07-22 20:45:20.844 | INFO     | app.services.account_service:_process_batch_login_results:1845 - 批量登录结果处理完成，数据库已更新
2025-07-22 20:45:20.844 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-22 20:45:20.845 | INFO     | app.controllers.account_controller:auto_login_accounts:640 - 批量登录完成，触发账户数据刷新
2025-07-22 20:46:07.665 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-22 20:47:07.661 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-22 20:47:12.410 | INFO     | app.services.account_service:update_account_profile:493 - 更新账户资料: 1
2025-07-22 20:47:12.410 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-22 20:47:12.421 | INFO     | app.services.account_service:_generate_unique_username:1658 - 生成唯一用户名成功: tiany1d2a9aa76j (尝试 1/5)
2025-07-22 20:47:12.422 | INFO     | core.telegram.user_manager:update_profile:89 - 正在更新用户资料: +***********
2025-07-22 20:47:42.103 | INFO     | core.telegram.user_manager:update_profile:130 - 更新用户资料成功: +***********
2025-07-22 20:47:42.701 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-22 20:48:07.663 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-22 20:48:18.660 | INFO     | app.services.account_service:refresh_account_info:654 - 刷新账户信息: 1
2025-07-22 20:48:18.661 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-22 20:48:18.672 | INFO     | core.telegram.user_manager:get_user_info:41 - 正在获取用户信息: +***********
2025-07-22 20:48:19.529 | INFO     | core.telegram.user_manager:get_user_info:66 - 获取用户信息成功: +***********
2025-07-22 20:48:19.923 | INFO     | data.repositories.account_repo:update_account:394 - 更新账户成功: 1
2025-07-22 20:48:19.932 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-22 20:48:19.937 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-22 20:48:19.948 | INFO     | ui.views.account_view:_update_single_account_display:337 - 已实时更新账户显示: +***********
2025-07-22 20:48:19.948 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-22 20:49:07.670 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-22 20:50:07.663 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-22 21:28:24.244 | INFO     | utils.logger:init_logger:98 - 日志系统初始化完成，默认级别: DEBUG，日志目录: H:\PyProject\TeleTest\APPDATA\logs
2025-07-22 21:29:27.398 | INFO     | utils.logger:init_logger:98 - 日志系统初始化完成，默认级别: DEBUG，日志目录: H:\PyProject\TeleTest\APPDATA\logs
2025-07-22 21:29:28.691 | INFO     | data.database:init_database:31 - 初始化数据库连接: H:\PyProject\TeleTest\APPDATA\database\telegram_manager.db
2025-07-22 21:29:28.709 | DEBUG    | data.database:init_database:58 - 创建数据库表
2025-07-22 21:29:28.720 | INFO     | data.database:init_database:61 - 数据库初始化完成
2025-07-22 21:29:29.304 | DEBUG    | app.controllers.auth_controller:initialize_software:43 - 控制器: 初始化软件 app_id=1000, version=1.2.7
2025-07-22 21:29:29.305 | DEBUG    | core.auth.api_service:init_software:55 - 发送初始化请求: https://server.xile188.com/api/user/1000/windowns10/1.2.7/ini, params={}
2025-07-22 21:29:29.523 | DEBUG    | core.auth.api_service:init_software:66 - 初始化数据解密成功: {'new_content': '- 新增功能\n  - 1.新增图片上传功能\n  - 2.新增软件文档\n  - 3.新增软件自动更新，后续更新会自动检测版本更新，无需再下载更新包\n  - 4.新增设置账户信息及发信控制\n- 修复bug\n  - 删除监听任务时，不删除对应任务采集到的数据。\n  - 监听设置群无法发送到目标群\n  - 导出监听数据时应当导出单个任务而非所有数据\n- 优化记录\n  - 优化发信规则，直接调取符合要求的账户发送，无账户时才需要等待延时。\n  - 完善软件设置', 'new_url': None, 'notice': {'id': '2', 'content': '公告内容测试3', 'time': '**********'}}
2025-07-22 21:29:29.530 | INFO     | app.controllers.auth_controller:login:61 - 控制器: 用户登录 account=demo01
2025-07-22 21:29:32.410 | INFO     | core.auth.api_service:login:127 - 用户登录: account=demo01
2025-07-22 21:29:32.644 | INFO     | core.auth.api_service:get_user_info:347 - 获取用户信息
2025-07-22 21:29:33.030 | INFO     | core.auth.api_service:get_user_info:375 - 获取用户信息成功
2025-07-22 21:29:33.035 | INFO     | __main__:on_auth_completed:91 - 用户登录成功，正在启动主窗口...
2025-07-22 21:29:33.060 | INFO     | core.telegram.client_worker:__init__:61 - Telegram客户端工作线程初始化
2025-07-22 21:29:33.060 | INFO     | utils.vip_checker:initialize:25 - VIP检查器已初始化，等待登录后开始检查
2025-07-22 21:29:33.061 | INFO     | ui.main_window:_initialize_core_components:113 - MainWindow: 初始化核心组件...
2025-07-22 21:29:33.061 | INFO     | app.services.account_service:__init__:44 - 账户服务初始化
2025-07-22 21:29:33.061 | INFO     | core.telegram.client_worker:run:65 - Telegram客户端工作线程开始运行
2025-07-22 21:29:33.062 | INFO     | core.proxy.proxy_task_manager:run:284 - 代理任务管理器线程启动
2025-07-22 21:29:33.062 | INFO     | app.controllers.account_controller:__init__:82 - 账户控制器初始化
2025-07-22 21:29:33.063 | INFO     | app.services.monitor_service:__init__:39 - 初始化监控任务服务
2025-07-22 21:29:33.063 | INFO     | core.monitor.filter:__init__:26 - 初始化消息过滤器
2025-07-22 21:29:33.063 | INFO     | core.monitor.extractor:__init__:24 - 初始化关键词提取器
2025-07-22 21:29:33.063 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-07-22 21:29:33.063 | INFO     | core.monitor.notifier:__init__:90 - 初始化通知管理器
2025-07-22 21:29:33.063 | INFO     | app.controllers.telegram_monitor_controller:__init__:64 - 初始化 Telegram 监控控制器
2025-07-22 21:29:33.064 | DEBUG    | app.controllers.telegram_monitor_controller:__init__:82 - 控制器初始化完成
2025-07-22 21:29:33.067 | INFO     | core.telegram.client_manager:__init__:66 - Telegram客户端管理器初始化
2025-07-22 21:29:33.068 | INFO     | core.telegram.user_manager:__init__:30 - Telegram用户管理器初始化
2025-07-22 21:29:33.068 | INFO     | core.telegram.group_manager:__init__:36 - Telegram群组管理器初始化
2025-07-22 21:29:33.070 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-07-22 21:29:33.071 | INFO     | core.telegram.monitor_manager:__init__:26 - Telegram监控管理器初始化
2025-07-22 21:29:33.071 | INFO     | core.telegram.telegram_service:__init__:39 - Telegram服务初始化完成
2025-07-22 21:29:33.247 | INFO     | app.controllers.convert_controller:set_account_controller:35 - 账户控制器已设置
2025-07-22 21:29:33.334 | INFO     | ui.views.monitor_view:__init__:48 - 初始化监控视图
2025-07-22 21:29:33.334 | DEBUG    | ui.views.monitor_view:_connect_signals:80 - 连接控制器信号
2025-07-22 21:29:33.514 | INFO     | ui.views.proxy_view:__init__:96 - 初始化视图，加载第一页代理数据
2025-07-22 21:29:33.571 | INFO     | ui.main_window:_setup_log_managers:457 - 已为 9 个视图创建日志管理器
2025-07-22 21:29:33.799 | INFO     | __main__:on_auth_completed:95 - 主窗口已启动
2025-07-22 21:29:33.837 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:88 - 尝试连接监听日志信号...
2025-07-22 21:29:33.838 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:90 - 监听日志信号连接成功
2025-07-22 21:29:33.846 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-22 21:29:33.850 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-22 21:29:33.856 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:145 - 加载监控任务列表 (搜索关键词: '')
2025-07-22 21:29:33.856 | DEBUG    | ui.views.monitor_view:_on_loading_started:620 - 加载中: 正在加载任务列表...
2025-07-22 21:29:33.856 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-22 21:29:33.863 | INFO     | app.services.proxy_service:refresh_proxies:712 - 刷新代理列表: offset=0, limit=20
2025-07-22 21:29:33.863 | INFO     | core.proxy.proxy_task_manager:submit_task:172 - 提交耗时任务到子线程: refresh_proxies_0_20
2025-07-22 21:29:33.863 | DEBUG    | app.services.proxy_service:get_proxies:500 - 获取代理列表: offset=0, limit=20
2025-07-22 21:29:33.863 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-22 21:29:33.864 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-22 21:29:33.867 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-22 21:29:33.890 | INFO     | app.controllers.invite_controller:get_invite_tasks:38 - 获取邀请任务列表, status=None
2025-07-22 21:29:33.890 | INFO     | app.services.invite_service:get_invite_tasks:121 - 获取邀请任务列表, status=None
2025-07-22 21:29:33.891 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-22 21:29:33.891 | INFO     | app.services.invite_service:task_initialize:39 - 初始化邀请服务，重置运行中的任务为暂停状态
2025-07-22 21:29:33.892 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-22 21:29:33.895 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-22 21:29:33.896 | INFO     | core.proxy.proxy_task_manager:task_wrapper:199 - 任务 refresh_proxies_0_20 完成
2025-07-22 21:29:33.897 | INFO     | utils.vip_checker:start_checking:37 - 开始VIP状态检查
2025-07-22 21:29:33.897 | DEBUG    | utils.vip_checker:_check_vip_status:61 - 正在检查VIP状态...
2025-07-22 21:29:33.897 | INFO     | core.auth.api_service:verify_vip:425 - 会员验证接口调用
2025-07-22 21:29:34.020 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-22 21:29:34.027 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-22 21:29:34.101 | INFO     | app.services.invite_service:task_initialize:48 - 成功重置 0 个任务状态
2025-07-22 21:29:34.102 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-22 21:29:34.104 | DEBUG    | ui.views.monitor_view:_refresh_task_list:175 - 刷新任务列表，共0个任务
2025-07-22 21:29:34.104 | INFO     | ui.views.monitor_view:_refresh_task_list:189 - 没有找到监控任务
2025-07-22 21:29:34.104 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-22 21:29:34.106 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-22 21:29:34.111 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-22 21:29:34.113 | INFO     | app.services.account_service:get_all_groups:98 - 获取所有账户分组成功, 共 2 个
2025-07-22 21:29:34.113 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-22 21:29:34.115 | DEBUG    | ui.views.account_view:_on_groups_loaded:519 - 分组加载完成: 2个分组
2025-07-22 21:29:34.129 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-22 21:29:34.132 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-22 21:29:34.191 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-22 21:29:34.193 | INFO     | app.services.account_service:get_all_accounts:292 - 获取所有账户成功, 共 2 个
2025-07-22 21:29:34.194 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-22 21:29:34.196 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-07-22 21:29:34.205 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-22 21:29:34.206 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-22 21:29:34.207 | DEBUG    | ui.views.account_view:_on_accounts_loaded:652 - 账户加载完成: 2个账户
2025-07-22 21:29:34.228 | DEBUG    | ui.views.account_view:_update_group_counts:188 - 分组计数已更新: {2: 2}
2025-07-22 21:29:34.232 | INFO     | ui.views.account_view:_auto_login_accounts:707 - 开始调用控制器进行 2 个账户的自动登录。
2025-07-22 21:29:34.232 | INFO     | app.services.account_service:batch_auto_login:1212 - 服务层：开始批量自动登录 2 个账户, 最大并发: 5
2025-07-22 21:29:34.232 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-22 21:29:34.240 | INFO     | app.controllers.telegram_monitor_controller:_load_total_users_count_internal:191 - 成功加载总体统计数据: 总用户数 23, 今日采集 0, 日均采集 23, 运行天数 1
2025-07-22 21:29:34.241 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:156 - 成功加载 0 个任务
2025-07-22 21:29:34.241 | DEBUG    | ui.views.monitor_view:_on_loading_finished:625 - 加载完成
2025-07-22 21:29:34.253 | INFO     | app.services.account_service:get_all_accounts:292 - 获取所有账户成功, 共 2 个
2025-07-22 21:29:34.253 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-22 21:29:34.253 | INFO     | app.services.account_service:batch_auto_login:1251 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-07-22 21:29:34.254 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-22 21:29:34.257 | DEBUG    | ui.views.account_view:_update_group_counts:188 - 分组计数已更新: {2: 2}
2025-07-22 21:29:34.257 | DEBUG    | ui.views.account_view:_init_cache:155 - 账户缓存初始化完成，共 2 个账户
2025-07-22 21:29:34.262 | DEBUG    | ui.views.account_view:_on_accounts_loaded:652 - 账户加载完成: 2个账户
2025-07-22 21:29:34.290 | DEBUG    | ui.views.account_view:_update_group_counts:188 - 分组计数已更新: {2: 2}
2025-07-22 21:29:34.296 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-22 21:29:34.301 | INFO     | core.auth.api_service:verify_vip:438 - 会员验证成功
2025-07-22 21:29:34.301 | INFO     | utils.vip_checker:_check_vip_status:76 - VIP状态有效
2025-07-22 21:29:34.305 | INFO     | app.services.account_service:batch_auto_login:1251 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-07-22 21:29:34.305 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-22 21:29:34.307 | INFO     | app.services.account_service:batch_auto_login:1321 - 服务层：准备调用核心层处理 2 个账户的自动登录。
2025-07-22 21:29:34.307 | INFO     | app.services.account_service:batch_auto_login:1331 - 服务层：设置核心层任务超时为 120 秒。
2025-07-22 21:29:34.307 | INFO     | core.telegram.client_manager:batch_auto_login:1230 - 开始批量自动登录, 共 2 个账户, 最大并发 5
2025-07-22 21:29:34.308 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:271 - 创建客户端: H:\PyProject\TeleTest\APPDATA\sessions\+***********, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-22 21:29:34.308 | DEBUG    | core.telegram.client_manager:get_proxy:96 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-22 21:29:34.309 | DEBUG    | core.telegram.client_manager:get_proxy:143 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-07-22 21:29:34.316 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:271 - 创建客户端: H:\PyProject\TeleTest\APPDATA\sessions\+***********, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-22 21:29:34.316 | DEBUG    | core.telegram.client_manager:get_proxy:96 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-22 21:29:34.316 | DEBUG    | core.telegram.client_manager:get_proxy:143 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-07-22 21:29:34.339 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-22 21:29:34.417 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-22 21:29:34.434 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-07-22 21:29:34.436 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-07-22 21:29:34.438 | INFO     | ui.views.proxy_view:_load_initial_data:1044 - 初始数据加载完成，共 1 个代理
2025-07-22 21:29:34.470 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-22 21:29:38.153 | INFO     | core.telegram.client_manager:_connect_client:219 - 连接成功!
2025-07-22 21:29:39.511 | INFO     | core.telegram.client_manager:_verify_client_authorization:1205 - 账户已授权: +***********
2025-07-22 21:29:40.974 | INFO     | core.telegram.client_manager:_connect_client:219 - 连接成功!
2025-07-22 21:29:42.956 | INFO     | core.telegram.client_manager:_verify_client_authorization:1205 - 账户已授权: +***********
2025-07-22 21:29:44.963 | INFO     | core.telegram.client_manager:batch_auto_login:1379 - 批量登录完成: 总计 2, 成功 2, 失败 0
2025-07-22 21:29:45.339 | INFO     | app.services.account_service:batch_auto_login:1352 - 服务层：核心层登录完成: 成功 2, 失败 0 / 2
2025-07-22 21:29:45.339 | INFO     | app.services.account_service:_process_batch_login_results:1786 - 开始处理批量登录结果，共 2 个账户
2025-07-22 21:29:45.339 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-22 21:29:45.344 | INFO     | data.repositories.account_repo:update_account:394 - 更新账户成功: 2
2025-07-22 21:29:45.344 | INFO     | app.services.account_service:_process_batch_login_results:1822 - 已更新账户 +*********** 的用户信息
2025-07-22 21:29:45.352 | INFO     | data.repositories.account_repo:update_account:394 - 更新账户成功: 1
2025-07-22 21:29:45.352 | INFO     | app.services.account_service:_process_batch_login_results:1822 - 已更新账户 +*********** 的用户信息
2025-07-22 21:29:45.358 | INFO     | app.services.account_service:_process_batch_login_results:1845 - 批量登录结果处理完成，数据库已更新
2025-07-22 21:29:45.358 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-22 21:29:45.358 | INFO     | app.controllers.account_controller:auto_login_accounts:640 - 批量登录完成，触发账户数据刷新
2025-07-22 21:30:33.022 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-22 21:31:33.022 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-22 21:32:33.024 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-22 21:33:33.034 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-22 21:33:56.791 | INFO     | ui.main_window:closeEvent:466 - MainWindow: 接收到关闭事件
2025-07-22 21:33:56.804 | INFO     | ui.main_window:_cleanup_before_quit:308 - MainWindow: 执行清理资源...
2025-07-22 21:33:56.805 | INFO     | core.telegram.client_worker:stop:108 - 正在停止Telegram客户端工作线程
2025-07-22 21:33:56.811 | INFO     | core.telegram.client_manager:cleanup_async:1150 - 开始清理资源并断开所有连接
2025-07-22 21:33:56.811 | INFO     | core.telegram.client_manager:disconnect_all_clients:735 - 正在断开所有客户端连接
2025-07-22 21:33:56.811 | INFO     | core.telegram.client_manager:_safe_disconnect:756 - 正在断开客户端连接: +***********
2025-07-22 21:33:56.811 | INFO     | core.telegram.client_manager:_safe_disconnect:756 - 正在断开客户端连接: +***********
2025-07-22 21:33:56.823 | INFO     | core.telegram.client_manager:_safe_disconnect:760 - 断开客户端连接成功: +***********
2025-07-22 21:33:56.824 | INFO     | core.telegram.client_manager:_safe_disconnect:760 - 断开客户端连接成功: +***********
2025-07-22 21:33:56.824 | INFO     | core.telegram.client_manager:cleanup_async:1169 - 资源清理完成
2025-07-22 21:33:57.320 | INFO     | core.telegram.client_manager:cleanup_async:1150 - 开始清理资源并断开所有连接
2025-07-22 21:33:57.320 | INFO     | core.telegram.client_manager:disconnect_all_clients:735 - 正在断开所有客户端连接
2025-07-22 21:33:57.320 | INFO     | core.telegram.client_manager:cleanup_async:1169 - 资源清理完成
2025-07-22 21:33:57.832 | INFO     | core.telegram.client_worker:run:97 - Telegram客户端工作线程已退出
2025-07-22 21:33:57.834 | INFO     | core.telegram.client_worker:stop:122 - Telegram客户端工作线程已停止
2025-07-22 21:33:57.834 | INFO     | ui.main_window:_cleanup_before_quit:320 - TelegramClientWorker 已停止。
2025-07-22 21:33:57.834 | INFO     | ui.main_window:_cleanup_before_quit:324 - MainWindow 清理完成
2025-07-22 21:33:57.849 | INFO     | __main__:main:109 - 应用程序已正常退出
2025-07-22 22:18:12.315 | INFO     | utils.logger:init_logger:98 - 日志系统初始化完成，默认级别: DEBUG，日志目录: H:\PyProject\TeleTest\APPDATA\logs
2025-07-22 22:18:19.641 | INFO     | data.database:init_database:31 - 初始化数据库连接: H:\PyProject\TeleTest\APPDATA\database\telegram_manager.db
2025-07-22 22:18:19.692 | DEBUG    | data.database:init_database:58 - 创建数据库表
2025-07-22 22:18:19.717 | INFO     | data.database:init_database:61 - 数据库初始化完成
2025-07-22 22:18:23.396 | DEBUG    | app.controllers.auth_controller:initialize_software:43 - 控制器: 初始化软件 app_id=1000, version=1.2.7
2025-07-22 22:18:23.396 | DEBUG    | core.auth.api_service:init_software:55 - 发送初始化请求: https://server.xile188.com/api/user/1000/windowns10/1.2.7/ini, params={}
2025-07-22 22:18:23.621 | DEBUG    | core.auth.api_service:init_software:66 - 初始化数据解密成功: {'new_content': '- 新增功能\n  - 1.新增图片上传功能\n  - 2.新增软件文档\n  - 3.新增软件自动更新，后续更新会自动检测版本更新，无需再下载更新包\n  - 4.新增设置账户信息及发信控制\n- 修复bug\n  - 删除监听任务时，不删除对应任务采集到的数据。\n  - 监听设置群无法发送到目标群\n  - 导出监听数据时应当导出单个任务而非所有数据\n- 优化记录\n  - 优化发信规则，直接调取符合要求的账户发送，无账户时才需要等待延时。\n  - 完善软件设置', 'new_url': None, 'notice': {'id': '2', 'content': '公告内容测试3', 'time': '**********'}}
2025-07-22 22:18:23.640 | INFO     | app.controllers.auth_controller:login:61 - 控制器: 用户登录 account=demo01
2025-07-22 22:18:26.944 | INFO     | core.auth.api_service:login:127 - 用户登录: account=demo01
2025-07-22 22:18:27.229 | INFO     | core.auth.api_service:get_user_info:347 - 获取用户信息
2025-07-22 22:18:27.464 | INFO     | core.auth.api_service:get_user_info:375 - 获取用户信息成功
2025-07-22 22:18:27.517 | INFO     | __main__:on_auth_completed:91 - 用户登录成功，正在启动主窗口...
2025-07-22 22:18:27.624 | INFO     | core.telegram.client_worker:__init__:61 - Telegram客户端工作线程初始化
2025-07-22 22:18:27.624 | INFO     | utils.vip_checker:initialize:25 - VIP检查器已初始化，等待登录后开始检查
2025-07-22 22:18:27.625 | INFO     | ui.main_window:_initialize_core_components:113 - MainWindow: 初始化核心组件...
2025-07-22 22:18:27.625 | INFO     | core.telegram.client_worker:run:65 - Telegram客户端工作线程开始运行
2025-07-22 22:18:27.626 | INFO     | app.services.account_service:__init__:44 - 账户服务初始化
2025-07-22 22:18:27.626 | INFO     | core.proxy.proxy_task_manager:run:284 - 代理任务管理器线程启动
2025-07-22 22:18:27.626 | INFO     | app.controllers.account_controller:__init__:82 - 账户控制器初始化
2025-07-22 22:18:27.627 | INFO     | app.services.monitor_service:__init__:39 - 初始化监控任务服务
2025-07-22 22:18:27.627 | INFO     | core.monitor.filter:__init__:26 - 初始化消息过滤器
2025-07-22 22:18:27.627 | INFO     | core.monitor.extractor:__init__:24 - 初始化关键词提取器
2025-07-22 22:18:27.627 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-07-22 22:18:27.628 | INFO     | core.telegram.client_manager:__init__:66 - Telegram客户端管理器初始化
2025-07-22 22:18:27.628 | INFO     | core.monitor.notifier:__init__:90 - 初始化通知管理器
2025-07-22 22:18:27.628 | INFO     | core.telegram.user_manager:__init__:30 - Telegram用户管理器初始化
2025-07-22 22:18:27.628 | INFO     | app.controllers.telegram_monitor_controller:__init__:64 - 初始化 Telegram 监控控制器
2025-07-22 22:18:27.629 | INFO     | core.telegram.group_manager:__init__:36 - Telegram群组管理器初始化
2025-07-22 22:18:27.629 | DEBUG    | app.controllers.telegram_monitor_controller:__init__:82 - 控制器初始化完成
2025-07-22 22:18:27.630 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-07-22 22:18:27.632 | INFO     | core.telegram.monitor_manager:__init__:26 - Telegram监控管理器初始化
2025-07-22 22:18:27.632 | INFO     | core.telegram.telegram_service:__init__:39 - Telegram服务初始化完成
2025-07-22 22:18:27.989 | INFO     | app.controllers.convert_controller:set_account_controller:35 - 账户控制器已设置
2025-07-22 22:18:28.090 | INFO     | ui.views.monitor_view:__init__:48 - 初始化监控视图
2025-07-22 22:18:28.090 | DEBUG    | ui.views.monitor_view:_connect_signals:80 - 连接控制器信号
2025-07-22 22:18:28.416 | INFO     | ui.views.proxy_view:__init__:96 - 初始化视图，加载第一页代理数据
2025-07-22 22:18:28.503 | INFO     | ui.main_window:_setup_log_managers:457 - 已为 9 个视图创建日志管理器
2025-07-22 22:18:28.801 | INFO     | __main__:on_auth_completed:95 - 主窗口已启动
2025-07-22 22:18:28.865 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:88 - 尝试连接监听日志信号...
2025-07-22 22:18:28.866 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:90 - 监听日志信号连接成功
2025-07-22 22:18:28.950 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-22 22:18:28.954 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-22 22:18:28.961 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:145 - 加载监控任务列表 (搜索关键词: '')
2025-07-22 22:18:28.962 | DEBUG    | ui.views.monitor_view:_on_loading_started:620 - 加载中: 正在加载任务列表...
2025-07-22 22:18:28.962 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-22 22:18:28.971 | INFO     | app.services.proxy_service:refresh_proxies:712 - 刷新代理列表: offset=0, limit=20
2025-07-22 22:18:28.971 | INFO     | core.proxy.proxy_task_manager:submit_task:172 - 提交耗时任务到子线程: refresh_proxies_0_20
2025-07-22 22:18:28.971 | DEBUG    | app.services.proxy_service:get_proxies:500 - 获取代理列表: offset=0, limit=20
2025-07-22 22:18:28.971 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-22 22:18:28.972 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-22 22:18:28.974 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-22 22:18:28.977 | INFO     | app.controllers.invite_controller:get_invite_tasks:38 - 获取邀请任务列表, status=None
2025-07-22 22:18:28.978 | INFO     | app.services.invite_service:get_invite_tasks:121 - 获取邀请任务列表, status=None
2025-07-22 22:18:28.989 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-22 22:18:29.001 | INFO     | app.services.invite_service:task_initialize:39 - 初始化邀请服务，重置运行中的任务为暂停状态
2025-07-22 22:18:29.002 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-22 22:18:29.013 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-22 22:18:29.014 | INFO     | utils.vip_checker:start_checking:37 - 开始VIP状态检查
2025-07-22 22:18:29.014 | INFO     | core.proxy.proxy_task_manager:task_wrapper:199 - 任务 refresh_proxies_0_20 完成
2025-07-22 22:18:29.015 | DEBUG    | utils.vip_checker:_check_vip_status:61 - 正在检查VIP状态...
2025-07-22 22:18:29.015 | INFO     | core.auth.api_service:verify_vip:425 - 会员验证接口调用
2025-07-22 22:18:29.179 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-22 22:18:29.179 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-22 22:18:29.295 | INFO     | app.services.invite_service:task_initialize:48 - 成功重置 0 个任务状态
2025-07-22 22:18:29.296 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-22 22:18:29.297 | DEBUG    | ui.views.monitor_view:_refresh_task_list:175 - 刷新任务列表，共0个任务
2025-07-22 22:18:29.299 | INFO     | ui.views.monitor_view:_refresh_task_list:189 - 没有找到监控任务
2025-07-22 22:18:29.300 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-22 22:18:29.305 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-22 22:18:29.310 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-22 22:18:29.312 | INFO     | app.services.account_service:get_all_groups:98 - 获取所有账户分组成功, 共 2 个
2025-07-22 22:18:29.312 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-22 22:18:29.314 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-22 22:18:29.318 | DEBUG    | ui.views.account_view:_on_groups_loaded:519 - 分组加载完成: 2个分组
2025-07-22 22:18:29.342 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-22 22:18:29.434 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-22 22:18:29.439 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-22 22:18:29.440 | INFO     | app.services.account_service:get_all_accounts:292 - 获取所有账户成功, 共 2 个
2025-07-22 22:18:29.440 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-22 22:18:29.442 | INFO     | app.controllers.telegram_monitor_controller:_load_total_users_count_internal:191 - 成功加载总体统计数据: 总用户数 23, 今日采集 0, 日均采集 23, 运行天数 1
2025-07-22 22:18:29.442 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:156 - 成功加载 0 个任务
2025-07-22 22:18:29.442 | DEBUG    | ui.views.monitor_view:_on_loading_finished:625 - 加载完成
2025-07-22 22:18:29.444 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-07-22 22:18:29.459 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-22 22:18:29.460 | DEBUG    | ui.views.account_view:_on_accounts_loaded:652 - 账户加载完成: 2个账户
2025-07-22 22:18:29.492 | DEBUG    | ui.views.account_view:_update_group_counts:188 - 分组计数已更新: {2: 2}
2025-07-22 22:18:29.498 | INFO     | ui.views.account_view:_auto_login_accounts:707 - 开始调用控制器进行 2 个账户的自动登录。
2025-07-22 22:18:29.498 | INFO     | app.services.account_service:batch_auto_login:1212 - 服务层：开始批量自动登录 2 个账户, 最大并发: 5
2025-07-22 22:18:29.499 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-22 22:18:29.505 | INFO     | core.auth.api_service:verify_vip:438 - 会员验证成功
2025-07-22 22:18:29.505 | INFO     | utils.vip_checker:_check_vip_status:76 - VIP状态有效
2025-07-22 22:18:29.520 | INFO     | app.services.account_service:batch_auto_login:1251 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-07-22 22:18:29.521 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-22 22:18:29.522 | INFO     | app.services.account_service:get_all_accounts:292 - 获取所有账户成功, 共 2 个
2025-07-22 22:18:29.523 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-22 22:18:29.526 | DEBUG    | ui.views.account_view:_update_group_counts:188 - 分组计数已更新: {2: 2}
2025-07-22 22:18:29.526 | DEBUG    | ui.views.account_view:_init_cache:155 - 账户缓存初始化完成，共 2 个账户
2025-07-22 22:18:29.535 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-22 22:18:29.536 | DEBUG    | ui.views.account_view:_on_accounts_loaded:652 - 账户加载完成: 2个账户
2025-07-22 22:18:29.571 | DEBUG    | ui.views.account_view:_update_group_counts:188 - 分组计数已更新: {2: 2}
2025-07-22 22:18:29.587 | INFO     | app.services.account_service:batch_auto_login:1251 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-07-22 22:18:29.587 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-22 22:18:29.590 | INFO     | app.services.account_service:batch_auto_login:1321 - 服务层：准备调用核心层处理 2 个账户的自动登录。
2025-07-22 22:18:29.590 | INFO     | app.services.account_service:batch_auto_login:1331 - 服务层：设置核心层任务超时为 120 秒。
2025-07-22 22:18:29.591 | INFO     | core.telegram.client_manager:batch_auto_login:1230 - 开始批量自动登录, 共 2 个账户, 最大并发 5
2025-07-22 22:18:29.591 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:271 - 创建客户端: H:\PyProject\TeleTest\APPDATA\sessions\+***********, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-22 22:18:29.591 | DEBUG    | core.telegram.client_manager:get_proxy:96 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-22 22:18:29.593 | DEBUG    | core.telegram.client_manager:get_proxy:143 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-07-22 22:18:29.602 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:271 - 创建客户端: H:\PyProject\TeleTest\APPDATA\sessions\+***********, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-22 22:18:29.602 | DEBUG    | core.telegram.client_manager:get_proxy:96 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-22 22:18:29.603 | DEBUG    | core.telegram.client_manager:get_proxy:143 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-07-22 22:18:29.643 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-22 22:18:29.793 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-22 22:18:29.808 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-07-22 22:18:29.811 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-07-22 22:18:29.813 | INFO     | ui.views.proxy_view:_load_initial_data:1044 - 初始数据加载完成，共 1 个代理
2025-07-22 22:18:29.867 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-22 22:18:36.822 | INFO     | core.telegram.client_manager:_connect_client:219 - 连接成功!
2025-07-22 22:18:40.364 | INFO     | core.telegram.client_manager:_verify_client_authorization:1205 - 账户已授权: +***********
2025-07-22 22:18:42.419 | INFO     | core.telegram.client_manager:_connect_client:219 - 连接成功!
2025-07-22 22:18:43.944 | INFO     | core.telegram.client_manager:_verify_client_authorization:1205 - 账户已授权: +***********
2025-07-22 22:18:45.948 | INFO     | core.telegram.client_manager:batch_auto_login:1379 - 批量登录完成: 总计 2, 成功 2, 失败 0
2025-07-22 22:18:46.726 | INFO     | app.services.account_service:batch_auto_login:1352 - 服务层：核心层登录完成: 成功 2, 失败 0 / 2
2025-07-22 22:18:46.726 | INFO     | app.services.account_service:_process_batch_login_results:1786 - 开始处理批量登录结果，共 2 个账户
2025-07-22 22:18:46.726 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-22 22:18:46.735 | INFO     | data.repositories.account_repo:update_account:394 - 更新账户成功: 1
2025-07-22 22:18:46.736 | INFO     | app.services.account_service:_process_batch_login_results:1822 - 已更新账户 +*********** 的用户信息
2025-07-22 22:18:46.744 | INFO     | data.repositories.account_repo:update_account:394 - 更新账户成功: 2
2025-07-22 22:18:46.744 | INFO     | app.services.account_service:_process_batch_login_results:1822 - 已更新账户 +*********** 的用户信息
2025-07-22 22:18:46.751 | INFO     | app.services.account_service:_process_batch_login_results:1845 - 批量登录结果处理完成，数据库已更新
2025-07-22 22:18:46.751 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-22 22:18:46.751 | INFO     | app.controllers.account_controller:auto_login_accounts:640 - 批量登录完成，触发账户数据刷新
2025-07-22 22:19:15.643 | INFO     | ui.main_window:closeEvent:466 - MainWindow: 接收到关闭事件
2025-07-22 22:19:15.656 | INFO     | ui.main_window:_cleanup_before_quit:308 - MainWindow: 执行清理资源...
2025-07-22 22:19:15.657 | INFO     | core.telegram.client_worker:stop:108 - 正在停止Telegram客户端工作线程
2025-07-22 22:19:15.666 | INFO     | core.telegram.client_manager:cleanup_async:1150 - 开始清理资源并断开所有连接
2025-07-22 22:19:15.666 | INFO     | core.telegram.client_manager:disconnect_all_clients:735 - 正在断开所有客户端连接
2025-07-22 22:19:15.666 | INFO     | core.telegram.client_manager:_safe_disconnect:756 - 正在断开客户端连接: +***********
2025-07-22 22:19:15.666 | INFO     | core.telegram.client_manager:_safe_disconnect:756 - 正在断开客户端连接: +***********
2025-07-22 22:19:15.679 | INFO     | core.telegram.client_manager:_safe_disconnect:760 - 断开客户端连接成功: +***********
2025-07-22 22:19:15.679 | INFO     | core.telegram.client_manager:_safe_disconnect:760 - 断开客户端连接成功: +***********
2025-07-22 22:19:15.680 | INFO     | core.telegram.client_manager:cleanup_async:1169 - 资源清理完成
2025-07-22 22:19:16.172 | INFO     | core.telegram.client_manager:cleanup_async:1150 - 开始清理资源并断开所有连接
2025-07-22 22:19:16.172 | INFO     | core.telegram.client_manager:disconnect_all_clients:735 - 正在断开所有客户端连接
2025-07-22 22:19:16.172 | INFO     | core.telegram.client_manager:cleanup_async:1169 - 资源清理完成
2025-07-22 22:19:16.673 | INFO     | core.telegram.client_worker:run:97 - Telegram客户端工作线程已退出
2025-07-22 22:19:16.674 | INFO     | core.telegram.client_worker:stop:122 - Telegram客户端工作线程已停止
2025-07-22 22:19:16.674 | INFO     | ui.main_window:_cleanup_before_quit:320 - TelegramClientWorker 已停止。
2025-07-22 22:19:16.674 | INFO     | ui.main_window:_cleanup_before_quit:324 - MainWindow 清理完成
2025-07-22 22:19:16.694 | INFO     | __main__:main:109 - 应用程序已正常退出
