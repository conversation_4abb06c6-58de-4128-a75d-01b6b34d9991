2025-07-23 15:11:14.678 | INFO     | utils.logger:init_logger:98 - 日志系统初始化完成，默认级别: DEBUG，日志目录: H:\PyProject\TeleTest\APPDATA\logs
2025-07-23 15:11:15.781 | INFO     | data.database:init_database:31 - 初始化数据库连接: H:\PyProject\TeleTest\APPDATA\database\telegram_manager.db
2025-07-23 15:11:15.797 | DEBUG    | data.database:init_database:58 - 创建数据库表
2025-07-23 15:11:15.806 | INFO     | data.database:init_database:61 - 数据库初始化完成
2025-07-23 15:11:16.390 | DEBUG    | app.controllers.auth_controller:initialize_software:43 - 控制器: 初始化软件 app_id=1000, version=1.2.7
2025-07-23 15:11:16.391 | DEBUG    | core.auth.api_service:init_software:55 - 发送初始化请求: https://server.xile188.com/api/user/1000/windowns10/1.2.7/ini, params={}
2025-07-23 15:11:16.649 | DEBUG    | core.auth.api_service:init_software:66 - 初始化数据解密成功: {'new_content': '- 新增功能\n  - 1.新增图片上传功能\n  - 2.新增软件文档\n  - 3.新增软件自动更新，后续更新会自动检测版本更新，无需再下载更新包\n  - 4.新增设置账户信息及发信控制\n- 修复bug\n  - 删除监听任务时，不删除对应任务采集到的数据。\n  - 监听设置群无法发送到目标群\n  - 导出监听数据时应当导出单个任务而非所有数据\n- 优化记录\n  - 优化发信规则，直接调取符合要求的账户发送，无账户时才需要等待延时。\n  - 完善软件设置', 'new_url': None, 'notice': {'id': '2', 'content': '公告内容测试3', 'time': '**********'}}
2025-07-23 15:11:16.658 | INFO     | app.controllers.auth_controller:login:61 - 控制器: 用户登录 account=demo01
2025-07-23 15:11:19.472 | INFO     | core.auth.api_service:login:127 - 用户登录: account=demo01
2025-07-23 15:11:19.680 | INFO     | core.auth.api_service:get_user_info:347 - 获取用户信息
2025-07-23 15:11:19.923 | INFO     | core.auth.api_service:get_user_info:375 - 获取用户信息成功
2025-07-23 15:11:19.930 | INFO     | __main__:on_auth_completed:91 - 用户登录成功，正在启动主窗口...
2025-07-23 15:11:19.952 | INFO     | core.telegram.client_worker:__init__:61 - Telegram客户端工作线程初始化
2025-07-23 15:11:19.953 | INFO     | utils.vip_checker:initialize:25 - VIP检查器已初始化，等待登录后开始检查
2025-07-23 15:11:19.953 | INFO     | ui.main_window:_initialize_core_components:113 - MainWindow: 初始化核心组件...
2025-07-23 15:11:19.954 | INFO     | app.services.account_service:__init__:45 - 账户服务初始化
2025-07-23 15:11:19.954 | INFO     | app.controllers.account_controller:__init__:83 - 账户控制器初始化
2025-07-23 15:11:19.954 | INFO     | core.telegram.client_worker:run:65 - Telegram客户端工作线程开始运行
2025-07-23 15:11:19.954 | INFO     | core.proxy.proxy_task_manager:run:284 - 代理任务管理器线程启动
2025-07-23 15:11:19.955 | INFO     | app.services.monitor_service:__init__:39 - 初始化监控任务服务
2025-07-23 15:11:19.955 | INFO     | core.monitor.filter:__init__:26 - 初始化消息过滤器
2025-07-23 15:11:19.955 | INFO     | core.monitor.extractor:__init__:24 - 初始化关键词提取器
2025-07-23 15:11:19.956 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-07-23 15:11:19.956 | INFO     | core.monitor.notifier:__init__:90 - 初始化通知管理器
2025-07-23 15:11:19.956 | INFO     | app.controllers.telegram_monitor_controller:__init__:64 - 初始化 Telegram 监控控制器
2025-07-23 15:11:19.956 | DEBUG    | app.controllers.telegram_monitor_controller:__init__:82 - 控制器初始化完成
2025-07-23 15:11:19.959 | INFO     | core.telegram.client_manager:__init__:66 - Telegram客户端管理器初始化
2025-07-23 15:11:19.959 | INFO     | core.telegram.user_manager:__init__:30 - Telegram用户管理器初始化
2025-07-23 15:11:19.960 | INFO     | core.telegram.group_manager:__init__:36 - Telegram群组管理器初始化
2025-07-23 15:11:19.960 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-07-23 15:11:19.960 | INFO     | core.telegram.monitor_manager:__init__:26 - Telegram监控管理器初始化
2025-07-23 15:11:19.960 | INFO     | core.telegram.telegram_service:__init__:39 - Telegram服务初始化完成
2025-07-23 15:11:20.092 | INFO     | app.controllers.convert_controller:set_account_controller:35 - 账户控制器已设置
2025-07-23 15:11:20.187 | INFO     | ui.views.monitor_view:__init__:48 - 初始化监控视图
2025-07-23 15:11:20.188 | DEBUG    | ui.views.monitor_view:_connect_signals:80 - 连接控制器信号
2025-07-23 15:11:20.360 | INFO     | ui.views.proxy_view:__init__:96 - 初始化视图，加载第一页代理数据
2025-07-23 15:11:20.416 | INFO     | ui.main_window:_setup_log_managers:457 - 已为 9 个视图创建日志管理器
2025-07-23 15:11:20.620 | INFO     | __main__:on_auth_completed:95 - 主窗口已启动
2025-07-23 15:11:20.668 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:88 - 尝试连接监听日志信号...
2025-07-23 15:11:20.669 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:90 - 监听日志信号连接成功
2025-07-23 15:11:20.704 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-23 15:11:20.708 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-23 15:11:20.713 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:145 - 加载监控任务列表 (搜索关键词: '')
2025-07-23 15:11:20.714 | DEBUG    | ui.views.monitor_view:_on_loading_started:620 - 加载中: 正在加载任务列表...
2025-07-23 15:11:20.714 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-23 15:11:20.720 | INFO     | app.services.proxy_service:refresh_proxies:712 - 刷新代理列表: offset=0, limit=20
2025-07-23 15:11:20.720 | INFO     | core.proxy.proxy_task_manager:submit_task:172 - 提交耗时任务到子线程: refresh_proxies_0_20
2025-07-23 15:11:20.720 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-23 15:11:20.720 | DEBUG    | app.services.proxy_service:get_proxies:500 - 获取代理列表: offset=0, limit=20
2025-07-23 15:11:20.721 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-23 15:11:20.723 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-23 15:11:20.746 | INFO     | app.controllers.invite_controller:get_invite_tasks:38 - 获取邀请任务列表, status=None
2025-07-23 15:11:20.746 | INFO     | app.services.invite_service:get_invite_tasks:121 - 获取邀请任务列表, status=None
2025-07-23 15:11:20.746 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-23 15:11:20.747 | INFO     | app.services.invite_service:task_initialize:39 - 初始化邀请服务，重置运行中的任务为暂停状态
2025-07-23 15:11:20.747 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-23 15:11:20.751 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-23 15:11:20.752 | INFO     | core.proxy.proxy_task_manager:task_wrapper:199 - 任务 refresh_proxies_0_20 完成
2025-07-23 15:11:20.753 | INFO     | utils.vip_checker:start_checking:37 - 开始VIP状态检查
2025-07-23 15:11:20.753 | DEBUG    | utils.vip_checker:_check_vip_status:61 - 正在检查VIP状态...
2025-07-23 15:11:20.754 | INFO     | core.auth.api_service:verify_vip:425 - 会员验证接口调用
2025-07-23 15:11:20.894 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-23 15:11:20.898 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-23 15:11:20.968 | INFO     | app.services.invite_service:task_initialize:48 - 成功重置 0 个任务状态
2025-07-23 15:11:20.969 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-23 15:11:20.969 | DEBUG    | ui.views.monitor_view:_refresh_task_list:175 - 刷新任务列表，共0个任务
2025-07-23 15:11:20.969 | INFO     | ui.views.monitor_view:_refresh_task_list:189 - 没有找到监控任务
2025-07-23 15:11:20.970 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-23 15:11:20.971 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-23 15:11:20.974 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-23 15:11:20.976 | INFO     | app.services.account_service:get_all_groups:99 - 获取所有账户分组成功, 共 2 个
2025-07-23 15:11:20.976 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-23 15:11:20.979 | DEBUG    | ui.views.account_view:_on_groups_loaded:519 - 分组加载完成: 2个分组
2025-07-23 15:11:20.991 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-23 15:11:20.996 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-23 15:11:21.042 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-23 15:11:21.061 | INFO     | app.controllers.telegram_monitor_controller:_load_total_users_count_internal:191 - 成功加载总体统计数据: 总用户数 23, 今日采集 0, 日均采集 23, 运行天数 1
2025-07-23 15:11:21.061 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:156 - 成功加载 0 个任务
2025-07-23 15:11:21.061 | DEBUG    | ui.views.monitor_view:_on_loading_finished:625 - 加载完成
2025-07-23 15:11:21.062 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-23 15:11:21.067 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-07-23 15:11:21.082 | INFO     | app.services.account_service:get_all_accounts:333 - 获取所有账户成功, 共 2 个
2025-07-23 15:11:21.083 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-23 15:11:21.084 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-23 15:11:21.085 | DEBUG    | ui.views.account_view:_on_accounts_loaded:652 - 账户加载完成: 2个账户
2025-07-23 15:11:21.110 | DEBUG    | ui.views.account_view:_update_group_counts:188 - 分组计数已更新: {2: 2}
2025-07-23 15:11:21.115 | INFO     | ui.views.account_view:_auto_login_accounts:707 - 开始调用控制器进行 2 个账户的自动登录。
2025-07-23 15:11:21.115 | INFO     | app.services.account_service:batch_auto_login:1366 - 服务层：开始批量自动登录 2 个账户, 最大并发: 5
2025-07-23 15:11:21.115 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-23 15:11:21.130 | INFO     | core.auth.api_service:verify_vip:438 - 会员验证成功
2025-07-23 15:11:21.130 | INFO     | utils.vip_checker:_check_vip_status:76 - VIP状态有效
2025-07-23 15:11:21.132 | INFO     | app.services.account_service:batch_auto_login:1406 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-07-23 15:11:21.132 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-23 15:11:21.135 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-23 15:11:21.144 | INFO     | app.services.account_service:batch_auto_login:1406 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-07-23 15:11:21.144 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-23 15:11:21.148 | INFO     | app.services.account_service:batch_auto_login:1476 - 服务层：准备调用核心层处理 2 个账户的自动登录。
2025-07-23 15:11:21.148 | INFO     | app.services.account_service:batch_auto_login:1486 - 服务层：设置核心层任务超时为 120 秒。
2025-07-23 15:11:21.148 | INFO     | core.telegram.client_manager:batch_auto_login:1230 - 开始批量自动登录, 共 2 个账户, 最大并发 5
2025-07-23 15:11:21.149 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:271 - 创建客户端: H:\PyProject\TeleTest\APPDATA\sessions\+***********, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-23 15:11:21.149 | DEBUG    | core.telegram.client_manager:get_proxy:96 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-23 15:11:21.149 | DEBUG    | core.telegram.client_manager:get_proxy:143 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-07-23 15:11:21.150 | INFO     | app.services.account_service:get_all_accounts:333 - 获取所有账户成功, 共 2 个
2025-07-23 15:11:21.150 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-23 15:11:21.153 | DEBUG    | ui.views.account_view:_update_group_counts:188 - 分组计数已更新: {2: 2}
2025-07-23 15:11:21.153 | DEBUG    | ui.views.account_view:_init_cache:155 - 账户缓存初始化完成，共 2 个账户
2025-07-23 15:11:21.159 | DEBUG    | ui.views.account_view:_on_accounts_loaded:652 - 账户加载完成: 2个账户
2025-07-23 15:11:21.164 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:271 - 创建客户端: H:\PyProject\TeleTest\APPDATA\sessions\+***********, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-23 15:11:21.165 | DEBUG    | core.telegram.client_manager:get_proxy:96 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-23 15:11:21.165 | DEBUG    | core.telegram.client_manager:get_proxy:143 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-07-23 15:11:21.190 | DEBUG    | ui.views.account_view:_update_group_counts:188 - 分组计数已更新: {2: 2}
2025-07-23 15:11:21.214 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-23 15:11:21.283 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-23 15:11:21.293 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-07-23 15:11:21.294 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-07-23 15:11:21.296 | INFO     | ui.views.proxy_view:_load_initial_data:1044 - 初始数据加载完成，共 1 个代理
2025-07-23 15:11:21.321 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-23 15:11:27.557 | INFO     | core.telegram.client_manager:_connect_client:219 - 连接成功!
2025-07-23 15:11:30.349 | INFO     | core.telegram.client_manager:_verify_client_authorization:1205 - 账户已授权: +***********
2025-07-23 15:11:33.023 | INFO     | core.telegram.client_manager:_connect_client:219 - 连接成功!
2025-07-23 15:11:34.301 | INFO     | core.telegram.client_manager:_verify_client_authorization:1205 - 账户已授权: +***********
2025-07-23 15:11:36.317 | INFO     | core.telegram.client_manager:batch_auto_login:1379 - 批量登录完成: 总计 2, 成功 2, 失败 0
2025-07-23 15:11:37.166 | INFO     | app.services.account_service:batch_auto_login:1507 - 服务层：核心层登录完成: 成功 2, 失败 0 / 2
2025-07-23 15:11:37.166 | INFO     | app.services.account_service:_process_batch_login_results:2142 - 开始处理批量登录结果，共 2 个账户
2025-07-23 15:11:37.166 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-23 15:11:37.172 | INFO     | data.repositories.account_repo:update_account:394 - 更新账户成功: 2
2025-07-23 15:11:37.173 | INFO     | app.services.account_service:_process_batch_login_results:2178 - 已更新账户 +*********** 的用户信息
2025-07-23 15:11:37.179 | INFO     | data.repositories.account_repo:update_account:394 - 更新账户成功: 1
2025-07-23 15:11:37.179 | INFO     | app.services.account_service:_process_batch_login_results:2178 - 已更新账户 +*********** 的用户信息
2025-07-23 15:11:37.187 | INFO     | app.services.account_service:_process_batch_login_results:2201 - 批量登录结果处理完成，数据库已更新
2025-07-23 15:11:37.187 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-23 15:11:37.187 | INFO     | app.controllers.account_controller:auto_login_accounts:642 - 批量登录完成，触发账户数据刷新
2025-07-23 15:11:46.874 | INFO     | app.services.account_service:batch_update_profiles:645 - 批量更新账户资料: 2 个
2025-07-23 15:11:46.875 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-23 15:11:46.966 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-23 15:11:46.966 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-23 15:11:46.970 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-23 15:11:46.970 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-23 15:11:46.976 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-23 15:11:46.976 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-23 15:11:47.000 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-23 15:11:49.032 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-23 15:11:49.092 | INFO     | app.services.account_service:get_all_accounts:333 - 获取所有账户成功, 共 2 个
2025-07-23 15:11:49.092 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-23 15:11:49.093 | DEBUG    | ui.views.account_view:_on_accounts_loaded:652 - 账户加载完成: 2个账户
2025-07-23 15:11:49.113 | DEBUG    | ui.views.account_view:_update_group_counts:188 - 分组计数已更新: {2: 2}
2025-07-23 15:12:19.912 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-23 15:12:33.447 | INFO     | app.services.account_service:batch_update_profiles:645 - 批量更新账户资料: 2 个
2025-07-23 15:12:33.448 | INFO     | app.services.account_service:_batch_update_with_username_template:2023 - 批量更新包含用户名模板的账户资料: 2 个
2025-07-23 15:12:33.448 | INFO     | app.services.account_service:_generate_batch_unique_usernames:1859 - 开始为 2 个账户批量生成唯一用户名
2025-07-23 15:12:33.448 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-23 15:12:33.457 | INFO     | app.services.account_service:_generate_batch_unique_usernames:1871 - 数据库中现有 2 个用户名
2025-07-23 15:12:33.457 | DEBUG    | app.services.account_service:_generate_batch_unique_usernames:1910 - 账户 1 生成用户名: tianyi20707 (尝试 1 次)
2025-07-23 15:12:33.457 | DEBUG    | app.services.account_service:_generate_batch_unique_usernames:1910 - 账户 2 生成用户名: tianyi49295 (尝试 1 次)
2025-07-23 15:12:33.457 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-23 15:12:33.460 | INFO     | app.services.account_service:_generate_batch_unique_usernames:1916 - 批量用户名生成完成: 成功 2/2 个，总尝试 2 次
2025-07-23 15:12:33.460 | INFO     | app.services.account_service:_batch_update_with_username_template:2044 - 成功为 2 个账户生成唯一用户名
2025-07-23 15:12:33.460 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-23 15:12:33.463 | INFO     | app.services.account_service:update_account_profile:546 - 更新账户资料: 1
2025-07-23 15:12:33.463 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-23 15:12:33.467 | INFO     | core.telegram.user_manager:update_profile:89 - 正在更新用户资料: +***********
2025-07-23 15:13:03.477 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-23 15:13:03.478 | WARNING  | app.services.account_service:_batch_update_with_username_template:2089 - 账户 1 (+***********) 更新失败: 更新Telegram资料失败: 任务结果获取超时
2025-07-23 15:13:03.480 | INFO     | app.services.account_service:update_account_profile:546 - 更新账户资料: 2
2025-07-23 15:13:03.480 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-23 15:13:03.483 | INFO     | core.telegram.user_manager:update_profile:89 - 正在更新用户资料: +***********
2025-07-23 15:13:19.912 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-23 15:13:33.653 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-23 15:13:33.654 | WARNING  | app.services.account_service:_batch_update_with_username_template:2089 - 账户 2 (+***********) 更新失败: 更新Telegram资料失败: 任务结果获取超时
2025-07-23 15:13:33.654 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-23 15:13:49.750 | INFO     | core.telegram.user_manager:update_profile:130 - 更新用户资料成功: +***********
2025-07-23 15:14:01.907 | INFO     | ui.main_window:closeEvent:466 - MainWindow: 接收到关闭事件
2025-07-23 15:14:01.929 | INFO     | ui.main_window:_cleanup_before_quit:308 - MainWindow: 执行清理资源...
2025-07-23 15:14:01.929 | INFO     | core.telegram.client_worker:stop:108 - 正在停止Telegram客户端工作线程
2025-07-23 15:14:01.936 | INFO     | core.telegram.client_manager:cleanup_async:1150 - 开始清理资源并断开所有连接
2025-07-23 15:14:01.936 | INFO     | core.telegram.client_manager:disconnect_all_clients:735 - 正在断开所有客户端连接
2025-07-23 15:14:01.936 | INFO     | core.telegram.client_manager:_safe_disconnect:756 - 正在断开客户端连接: +***********
2025-07-23 15:14:01.936 | INFO     | core.telegram.client_manager:_safe_disconnect:756 - 正在断开客户端连接: +***********
2025-07-23 15:14:01.937 | WARNING  | core.telegram.client_worker:task_wrapper:167 - 任务被取消 [id=2]
2025-07-23 15:14:01.950 | INFO     | core.telegram.client_manager:_safe_disconnect:760 - 断开客户端连接成功: +***********
2025-07-23 15:14:01.951 | INFO     | core.telegram.client_manager:_safe_disconnect:760 - 断开客户端连接成功: +***********
2025-07-23 15:14:01.951 | INFO     | core.telegram.client_manager:cleanup_async:1169 - 资源清理完成
2025-07-23 15:14:02.452 | INFO     | core.telegram.client_manager:cleanup_async:1150 - 开始清理资源并断开所有连接
2025-07-23 15:14:02.452 | INFO     | core.telegram.client_manager:disconnect_all_clients:735 - 正在断开所有客户端连接
2025-07-23 15:14:02.452 | INFO     | core.telegram.client_manager:cleanup_async:1169 - 资源清理完成
2025-07-23 15:14:02.956 | INFO     | core.telegram.client_worker:run:97 - Telegram客户端工作线程已退出
2025-07-23 15:14:02.957 | INFO     | core.telegram.client_worker:stop:122 - Telegram客户端工作线程已停止
2025-07-23 15:14:02.957 | INFO     | ui.main_window:_cleanup_before_quit:320 - TelegramClientWorker 已停止。
2025-07-23 15:14:02.957 | INFO     | ui.main_window:_cleanup_before_quit:324 - MainWindow 清理完成
2025-07-23 15:14:02.974 | INFO     | __main__:main:109 - 应用程序已正常退出
2025-07-23 17:20:25.453 | INFO     | utils.logger:init_logger:98 - 日志系统初始化完成，默认级别: DEBUG，日志目录: H:\PyProject\TeleTest\APPDATA\logs
2025-07-23 17:20:27.211 | INFO     | data.database:init_database:31 - 初始化数据库连接: H:\PyProject\TeleTest\APPDATA\database\telegram_manager.db
2025-07-23 17:20:27.243 | DEBUG    | data.database:init_database:58 - 创建数据库表
2025-07-23 17:20:27.261 | INFO     | data.database:init_database:61 - 数据库初始化完成
2025-07-23 17:20:28.636 | DEBUG    | app.controllers.auth_controller:initialize_software:43 - 控制器: 初始化软件 app_id=1000, version=1.2.7
2025-07-23 17:20:28.636 | DEBUG    | core.auth.api_service:init_software:55 - 发送初始化请求: https://server.xile188.com/api/user/1000/windowns10/1.2.7/ini, params={}
2025-07-23 17:20:29.145 | DEBUG    | core.auth.api_service:init_software:66 - 初始化数据解密成功: {'new_content': '- 新增功能\n  - 1.新增图片上传功能\n  - 2.新增软件文档\n  - 3.新增软件自动更新，后续更新会自动检测版本更新，无需再下载更新包\n  - 4.新增设置账户信息及发信控制\n- 修复bug\n  - 删除监听任务时，不删除对应任务采集到的数据。\n  - 监听设置群无法发送到目标群\n  - 导出监听数据时应当导出单个任务而非所有数据\n- 优化记录\n  - 优化发信规则，直接调取符合要求的账户发送，无账户时才需要等待延时。\n  - 完善软件设置', 'new_url': None, 'notice': {'id': '2', 'content': '公告内容测试3', 'time': '**********'}}
2025-07-23 17:20:29.156 | INFO     | app.controllers.auth_controller:login:61 - 控制器: 用户登录 account=demo01
2025-07-23 17:20:32.228 | INFO     | core.auth.api_service:login:127 - 用户登录: account=demo01
2025-07-23 17:20:32.820 | INFO     | core.auth.api_service:get_user_info:347 - 获取用户信息
2025-07-23 17:20:33.068 | INFO     | core.auth.api_service:get_user_info:375 - 获取用户信息成功
2025-07-23 17:20:33.075 | INFO     | __main__:on_auth_completed:91 - 用户登录成功，正在启动主窗口...
2025-07-23 17:20:33.108 | INFO     | core.telegram.client_worker:__init__:61 - Telegram客户端工作线程初始化
2025-07-23 17:20:33.108 | INFO     | utils.vip_checker:initialize:25 - VIP检查器已初始化，等待登录后开始检查
2025-07-23 17:20:33.108 | INFO     | ui.main_window:_initialize_core_components:113 - MainWindow: 初始化核心组件...
2025-07-23 17:20:33.108 | INFO     | core.telegram.client_worker:run:65 - Telegram客户端工作线程开始运行
2025-07-23 17:20:33.109 | INFO     | app.services.account_service:__init__:45 - 账户服务初始化
2025-07-23 17:20:33.109 | INFO     | app.controllers.account_controller:__init__:83 - 账户控制器初始化
2025-07-23 17:20:33.109 | INFO     | app.services.monitor_service:__init__:39 - 初始化监控任务服务
2025-07-23 17:20:33.110 | INFO     | core.proxy.proxy_task_manager:run:284 - 代理任务管理器线程启动
2025-07-23 17:20:33.110 | INFO     | core.monitor.filter:__init__:26 - 初始化消息过滤器
2025-07-23 17:20:33.110 | INFO     | core.monitor.extractor:__init__:24 - 初始化关键词提取器
2025-07-23 17:20:33.110 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-07-23 17:20:33.110 | INFO     | core.monitor.notifier:__init__:90 - 初始化通知管理器
2025-07-23 17:20:33.110 | INFO     | app.controllers.telegram_monitor_controller:__init__:64 - 初始化 Telegram 监控控制器
2025-07-23 17:20:33.111 | DEBUG    | app.controllers.telegram_monitor_controller:__init__:82 - 控制器初始化完成
2025-07-23 17:20:33.113 | INFO     | core.telegram.client_manager:__init__:66 - Telegram客户端管理器初始化
2025-07-23 17:20:33.114 | INFO     | core.telegram.user_manager:__init__:30 - Telegram用户管理器初始化
2025-07-23 17:20:33.114 | INFO     | core.telegram.group_manager:__init__:36 - Telegram群组管理器初始化
2025-07-23 17:20:33.114 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-07-23 17:20:33.114 | INFO     | core.telegram.monitor_manager:__init__:26 - Telegram监控管理器初始化
2025-07-23 17:20:33.115 | INFO     | core.telegram.telegram_service:__init__:39 - Telegram服务初始化完成
2025-07-23 17:20:33.264 | INFO     | app.controllers.convert_controller:set_account_controller:35 - 账户控制器已设置
2025-07-23 17:20:33.368 | INFO     | ui.views.monitor_view:__init__:48 - 初始化监控视图
2025-07-23 17:20:33.368 | DEBUG    | ui.views.monitor_view:_connect_signals:80 - 连接控制器信号
2025-07-23 17:20:33.541 | INFO     | ui.views.proxy_view:__init__:96 - 初始化视图，加载第一页代理数据
2025-07-23 17:20:33.598 | INFO     | ui.main_window:_setup_log_managers:457 - 已为 9 个视图创建日志管理器
2025-07-23 17:20:33.810 | INFO     | __main__:on_auth_completed:95 - 主窗口已启动
2025-07-23 17:20:33.853 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:88 - 尝试连接监听日志信号...
2025-07-23 17:20:33.853 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:90 - 监听日志信号连接成功
2025-07-23 17:20:33.862 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-23 17:20:33.866 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-23 17:20:33.871 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:145 - 加载监控任务列表 (搜索关键词: '')
2025-07-23 17:20:33.871 | DEBUG    | ui.views.monitor_view:_on_loading_started:620 - 加载中: 正在加载任务列表...
2025-07-23 17:20:33.871 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-23 17:20:33.877 | INFO     | app.services.proxy_service:refresh_proxies:712 - 刷新代理列表: offset=0, limit=20
2025-07-23 17:20:33.877 | INFO     | core.proxy.proxy_task_manager:submit_task:172 - 提交耗时任务到子线程: refresh_proxies_0_20
2025-07-23 17:20:33.877 | DEBUG    | app.services.proxy_service:get_proxies:500 - 获取代理列表: offset=0, limit=20
2025-07-23 17:20:33.878 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-23 17:20:33.878 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-23 17:20:33.881 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-23 17:20:33.904 | INFO     | app.controllers.invite_controller:get_invite_tasks:38 - 获取邀请任务列表, status=None
2025-07-23 17:20:33.904 | INFO     | app.services.invite_service:get_invite_tasks:121 - 获取邀请任务列表, status=None
2025-07-23 17:20:33.904 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-23 17:20:33.905 | INFO     | app.services.invite_service:task_initialize:39 - 初始化邀请服务，重置运行中的任务为暂停状态
2025-07-23 17:20:33.905 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-23 17:20:33.910 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-23 17:20:33.910 | INFO     | utils.vip_checker:start_checking:37 - 开始VIP状态检查
2025-07-23 17:20:33.910 | DEBUG    | utils.vip_checker:_check_vip_status:61 - 正在检查VIP状态...
2025-07-23 17:20:33.910 | INFO     | core.auth.api_service:verify_vip:425 - 会员验证接口调用
2025-07-23 17:20:33.911 | INFO     | core.proxy.proxy_task_manager:task_wrapper:199 - 任务 refresh_proxies_0_20 完成
2025-07-23 17:20:34.036 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-23 17:20:34.043 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-23 17:20:34.120 | DEBUG    | ui.views.monitor_view:_refresh_task_list:175 - 刷新任务列表，共0个任务
2025-07-23 17:20:34.121 | INFO     | ui.views.monitor_view:_refresh_task_list:189 - 没有找到监控任务
2025-07-23 17:20:34.121 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-23 17:20:34.123 | INFO     | app.services.invite_service:task_initialize:48 - 成功重置 0 个任务状态
2025-07-23 17:20:34.123 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-23 17:20:34.125 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-23 17:20:34.128 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-23 17:20:34.129 | INFO     | app.services.account_service:get_all_groups:99 - 获取所有账户分组成功, 共 2 个
2025-07-23 17:20:34.129 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-23 17:20:34.130 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-23 17:20:34.133 | DEBUG    | ui.views.account_view:_on_groups_loaded:519 - 分组加载完成: 2个分组
2025-07-23 17:20:34.146 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-23 17:20:34.208 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-23 17:20:34.208 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-23 17:20:34.213 | INFO     | app.controllers.telegram_monitor_controller:_load_total_users_count_internal:191 - 成功加载总体统计数据: 总用户数 23, 今日采集 0, 日均采集 23, 运行天数 1
2025-07-23 17:20:34.213 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:156 - 成功加载 0 个任务
2025-07-23 17:20:34.213 | DEBUG    | ui.views.monitor_view:_on_loading_finished:625 - 加载完成
2025-07-23 17:20:34.214 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-07-23 17:20:34.235 | INFO     | app.services.account_service:get_all_accounts:333 - 获取所有账户成功, 共 2 个
2025-07-23 17:20:34.235 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-23 17:20:34.237 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-23 17:20:34.238 | DEBUG    | ui.views.account_view:_on_accounts_loaded:652 - 账户加载完成: 2个账户
2025-07-23 17:20:34.263 | DEBUG    | ui.views.account_view:_update_group_counts:188 - 分组计数已更新: {2: 2}
2025-07-23 17:20:34.267 | INFO     | ui.views.account_view:_auto_login_accounts:707 - 开始调用控制器进行 2 个账户的自动登录。
2025-07-23 17:20:34.267 | INFO     | app.services.account_service:batch_auto_login:1411 - 服务层：开始批量自动登录 2 个账户, 最大并发: 5
2025-07-23 17:20:34.267 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-23 17:20:34.273 | INFO     | core.auth.api_service:verify_vip:438 - 会员验证成功
2025-07-23 17:20:34.273 | INFO     | utils.vip_checker:_check_vip_status:76 - VIP状态有效
2025-07-23 17:20:34.291 | INFO     | app.services.account_service:batch_auto_login:1451 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-07-23 17:20:34.292 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-23 17:20:34.295 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-23 17:20:34.302 | INFO     | app.services.account_service:batch_auto_login:1451 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-07-23 17:20:34.303 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-23 17:20:34.306 | INFO     | app.services.account_service:batch_auto_login:1521 - 服务层：准备调用核心层处理 2 个账户的自动登录。
2025-07-23 17:20:34.306 | INFO     | core.telegram.client_manager:batch_auto_login:1230 - 开始批量自动登录, 共 2 个账户, 最大并发 5
2025-07-23 17:20:34.306 | INFO     | app.services.account_service:batch_auto_login:1531 - 服务层：设置核心层任务超时为 120 秒。
2025-07-23 17:20:34.307 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:271 - 创建客户端: H:\PyProject\TeleTest\APPDATA\sessions\+***********, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-23 17:20:34.307 | DEBUG    | core.telegram.client_manager:get_proxy:96 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-23 17:20:34.307 | DEBUG    | core.telegram.client_manager:get_proxy:143 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-07-23 17:20:34.309 | INFO     | app.services.account_service:get_all_accounts:333 - 获取所有账户成功, 共 2 个
2025-07-23 17:20:34.309 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-23 17:20:34.311 | DEBUG    | ui.views.account_view:_update_group_counts:188 - 分组计数已更新: {2: 2}
2025-07-23 17:20:34.311 | DEBUG    | ui.views.account_view:_init_cache:155 - 账户缓存初始化完成，共 2 个账户
2025-07-23 17:20:34.317 | DEBUG    | ui.views.account_view:_on_accounts_loaded:652 - 账户加载完成: 2个账户
2025-07-23 17:20:34.323 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:271 - 创建客户端: H:\PyProject\TeleTest\APPDATA\sessions\+***********, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-23 17:20:34.323 | DEBUG    | core.telegram.client_manager:get_proxy:96 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-23 17:20:34.323 | DEBUG    | core.telegram.client_manager:get_proxy:143 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-07-23 17:20:34.348 | DEBUG    | ui.views.account_view:_update_group_counts:188 - 分组计数已更新: {2: 2}
2025-07-23 17:20:34.364 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-23 17:20:34.436 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-23 17:20:34.470 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-07-23 17:20:34.471 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-07-23 17:20:34.473 | INFO     | ui.views.proxy_view:_load_initial_data:1044 - 初始数据加载完成，共 1 个代理
2025-07-23 17:20:34.478 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-23 17:20:37.620 | INFO     | core.telegram.client_manager:_connect_client:219 - 连接成功!
2025-07-23 17:20:38.420 | INFO     | core.telegram.client_manager:_verify_client_authorization:1205 - 账户已授权: +***********
2025-07-23 17:20:39.303 | INFO     | core.telegram.client_manager:_connect_client:219 - 连接成功!
2025-07-23 17:20:40.318 | INFO     | core.telegram.client_manager:_verify_client_authorization:1205 - 账户已授权: +***********
2025-07-23 17:20:42.461 | INFO     | core.telegram.client_manager:batch_auto_login:1379 - 批量登录完成: 总计 2, 成功 2, 失败 0
2025-07-23 17:20:43.319 | INFO     | app.services.account_service:batch_auto_login:1552 - 服务层：核心层登录完成: 成功 2, 失败 0 / 2
2025-07-23 17:20:43.320 | INFO     | app.services.account_service:_process_batch_login_results:2187 - 开始处理批量登录结果，共 2 个账户
2025-07-23 17:20:43.320 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-23 17:20:43.327 | INFO     | data.repositories.account_repo:update_account:394 - 更新账户成功: 1
2025-07-23 17:20:43.327 | INFO     | app.services.account_service:_process_batch_login_results:2223 - 已更新账户 +*********** 的用户信息
2025-07-23 17:20:43.338 | INFO     | data.repositories.account_repo:update_account:394 - 更新账户成功: 2
2025-07-23 17:20:43.338 | INFO     | app.services.account_service:_process_batch_login_results:2223 - 已更新账户 +*********** 的用户信息
2025-07-23 17:20:43.347 | INFO     | app.services.account_service:_process_batch_login_results:2246 - 批量登录结果处理完成，数据库已更新
2025-07-23 17:20:43.347 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-23 17:20:43.347 | INFO     | app.controllers.account_controller:auto_login_accounts:642 - 批量登录完成，触发账户数据刷新
2025-07-23 17:21:20.435 | INFO     | app.services.account_service:batch_update_profiles:677 - 批量更新账户资料: 2 个
2025-07-23 17:21:20.435 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-23 17:21:20.447 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-23 17:21:20.447 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-23 17:21:20.450 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-23 17:21:20.450 | INFO     | app.services.account_service:batch_update_profiles:730 - 批量头像上传，使用 180 秒超时
2025-07-23 17:21:20.450 | INFO     | app.services.account_service:batch_update_profiles:734 - 开始批量更新 2 个账户的Telegram资料，超时设置: 180秒
2025-07-23 17:21:20.451 | INFO     | core.telegram.user_manager:batch_update_profiles:148 - 开始批量更新用户资料, 共 2 个账户
2025-07-23 17:21:20.453 | INFO     | core.telegram.user_manager:update_profile:89 - 正在更新用户资料: +***********
2025-07-23 17:21:33.073 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-23 17:21:54.244 | INFO     | core.telegram.user_manager:update_profile:130 - 更新用户资料成功: +***********
2025-07-23 17:21:54.245 | INFO     | core.telegram.user_manager:update_profile:89 - 正在更新用户资料: +***********
2025-07-23 17:22:33.069 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-23 17:22:42.550 | INFO     | core.telegram.user_manager:update_profile:130 - 更新用户资料成功: +***********
2025-07-23 17:22:42.550 | INFO     | core.telegram.user_manager:batch_update_profiles:192 - 批量更新用户资料完成: 总计 2, 成功 2, 失败 0
2025-07-23 17:22:42.583 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-23 17:22:42.595 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-23 17:22:42.596 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-23 17:22:42.617 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-23 17:22:44.642 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-23 17:22:44.719 | INFO     | app.services.account_service:get_all_accounts:333 - 获取所有账户成功, 共 2 个
2025-07-23 17:22:44.719 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-23 17:22:44.721 | DEBUG    | ui.views.account_view:_on_accounts_loaded:652 - 账户加载完成: 2个账户
2025-07-23 17:22:44.741 | DEBUG    | ui.views.account_view:_update_group_counts:188 - 分组计数已更新: {2: 2}
2025-07-23 17:23:33.070 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-23 17:24:33.068 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-23 17:25:33.074 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-23 17:26:33.067 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-23 17:26:56.696 | INFO     | ui.main_window:closeEvent:466 - MainWindow: 接收到关闭事件
2025-07-23 17:26:56.706 | INFO     | ui.main_window:_cleanup_before_quit:308 - MainWindow: 执行清理资源...
2025-07-23 17:26:56.707 | INFO     | core.telegram.client_worker:stop:108 - 正在停止Telegram客户端工作线程
2025-07-23 17:26:56.715 | INFO     | core.telegram.client_manager:cleanup_async:1150 - 开始清理资源并断开所有连接
2025-07-23 17:26:56.716 | INFO     | core.telegram.client_manager:disconnect_all_clients:735 - 正在断开所有客户端连接
2025-07-23 17:26:56.716 | INFO     | core.telegram.client_manager:_safe_disconnect:756 - 正在断开客户端连接: +***********
2025-07-23 17:26:56.716 | INFO     | core.telegram.client_manager:_safe_disconnect:756 - 正在断开客户端连接: +***********
2025-07-23 17:26:56.855 | INFO     | core.telegram.client_manager:_safe_disconnect:760 - 断开客户端连接成功: +***********
2025-07-23 17:26:56.855 | INFO     | core.telegram.client_manager:_safe_disconnect:760 - 断开客户端连接成功: +***********
2025-07-23 17:26:56.855 | INFO     | core.telegram.client_manager:cleanup_async:1169 - 资源清理完成
2025-07-23 17:26:57.230 | INFO     | core.telegram.client_manager:cleanup_async:1150 - 开始清理资源并断开所有连接
2025-07-23 17:26:57.230 | INFO     | core.telegram.client_manager:disconnect_all_clients:735 - 正在断开所有客户端连接
2025-07-23 17:26:57.230 | INFO     | core.telegram.client_manager:cleanup_async:1169 - 资源清理完成
2025-07-23 17:26:57.743 | INFO     | core.telegram.client_worker:run:97 - Telegram客户端工作线程已退出
2025-07-23 17:26:57.744 | INFO     | core.telegram.client_worker:stop:122 - Telegram客户端工作线程已停止
2025-07-23 17:26:57.744 | INFO     | ui.main_window:_cleanup_before_quit:320 - TelegramClientWorker 已停止。
2025-07-23 17:26:57.744 | INFO     | ui.main_window:_cleanup_before_quit:324 - MainWindow 清理完成
2025-07-23 17:26:57.763 | INFO     | __main__:main:109 - 应用程序已正常退出
2025-07-23 17:27:20.328 | INFO     | utils.logger:init_logger:98 - 日志系统初始化完成，默认级别: DEBUG，日志目录: H:\PyProject\TeleTest\APPDATA\logs
2025-07-23 17:27:21.323 | INFO     | data.database:init_database:31 - 初始化数据库连接: H:\PyProject\TeleTest\APPDATA\database\telegram_manager.db
2025-07-23 17:27:21.339 | DEBUG    | data.database:init_database:58 - 创建数据库表
2025-07-23 17:27:21.350 | INFO     | data.database:init_database:61 - 数据库初始化完成
2025-07-23 17:27:22.447 | DEBUG    | app.controllers.auth_controller:initialize_software:43 - 控制器: 初始化软件 app_id=1000, version=1.2.7
2025-07-23 17:27:22.447 | DEBUG    | core.auth.api_service:init_software:55 - 发送初始化请求: https://server.xile188.com/api/user/1000/windowns10/1.2.7/ini, params={}
2025-07-23 17:27:22.734 | DEBUG    | core.auth.api_service:init_software:66 - 初始化数据解密成功: {'new_content': '- 新增功能\n  - 1.新增图片上传功能\n  - 2.新增软件文档\n  - 3.新增软件自动更新，后续更新会自动检测版本更新，无需再下载更新包\n  - 4.新增设置账户信息及发信控制\n- 修复bug\n  - 删除监听任务时，不删除对应任务采集到的数据。\n  - 监听设置群无法发送到目标群\n  - 导出监听数据时应当导出单个任务而非所有数据\n- 优化记录\n  - 优化发信规则，直接调取符合要求的账户发送，无账户时才需要等待延时。\n  - 完善软件设置', 'new_url': None, 'notice': {'id': '2', 'content': '公告内容测试3', 'time': '**********'}}
2025-07-23 17:27:22.742 | INFO     | app.controllers.auth_controller:login:61 - 控制器: 用户登录 account=demo01
2025-07-23 17:27:25.565 | INFO     | core.auth.api_service:login:127 - 用户登录: account=demo01
2025-07-23 17:27:25.803 | INFO     | core.auth.api_service:get_user_info:347 - 获取用户信息
2025-07-23 17:27:25.987 | INFO     | core.auth.api_service:get_user_info:375 - 获取用户信息成功
2025-07-23 17:27:25.994 | INFO     | __main__:on_auth_completed:91 - 用户登录成功，正在启动主窗口...
2025-07-23 17:27:26.020 | INFO     | core.telegram.client_worker:__init__:61 - Telegram客户端工作线程初始化
2025-07-23 17:27:26.020 | INFO     | utils.vip_checker:initialize:25 - VIP检查器已初始化，等待登录后开始检查
2025-07-23 17:27:26.020 | INFO     | ui.main_window:_initialize_core_components:113 - MainWindow: 初始化核心组件...
2025-07-23 17:27:26.021 | INFO     | app.services.account_service:__init__:45 - 账户服务初始化
2025-07-23 17:27:26.021 | INFO     | core.telegram.client_worker:run:65 - Telegram客户端工作线程开始运行
2025-07-23 17:27:26.022 | INFO     | core.proxy.proxy_task_manager:run:284 - 代理任务管理器线程启动
2025-07-23 17:27:26.022 | INFO     | app.controllers.account_controller:__init__:83 - 账户控制器初始化
2025-07-23 17:27:26.023 | INFO     | app.services.monitor_service:__init__:39 - 初始化监控任务服务
2025-07-23 17:27:26.023 | INFO     | core.monitor.filter:__init__:26 - 初始化消息过滤器
2025-07-23 17:27:26.023 | INFO     | core.monitor.extractor:__init__:24 - 初始化关键词提取器
2025-07-23 17:27:26.023 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-07-23 17:27:26.024 | INFO     | core.monitor.notifier:__init__:90 - 初始化通知管理器
2025-07-23 17:27:26.024 | INFO     | app.controllers.telegram_monitor_controller:__init__:64 - 初始化 Telegram 监控控制器
2025-07-23 17:27:26.024 | DEBUG    | app.controllers.telegram_monitor_controller:__init__:82 - 控制器初始化完成
2025-07-23 17:27:26.025 | INFO     | core.telegram.client_manager:__init__:66 - Telegram客户端管理器初始化
2025-07-23 17:27:26.025 | INFO     | core.telegram.user_manager:__init__:30 - Telegram用户管理器初始化
2025-07-23 17:27:26.028 | INFO     | core.telegram.group_manager:__init__:36 - Telegram群组管理器初始化
2025-07-23 17:27:26.028 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-07-23 17:27:26.028 | INFO     | core.telegram.monitor_manager:__init__:26 - Telegram监控管理器初始化
2025-07-23 17:27:26.028 | INFO     | core.telegram.telegram_service:__init__:39 - Telegram服务初始化完成
2025-07-23 17:27:26.169 | INFO     | app.controllers.convert_controller:set_account_controller:35 - 账户控制器已设置
2025-07-23 17:27:26.260 | INFO     | ui.views.monitor_view:__init__:48 - 初始化监控视图
2025-07-23 17:27:26.260 | DEBUG    | ui.views.monitor_view:_connect_signals:80 - 连接控制器信号
2025-07-23 17:27:26.438 | INFO     | ui.views.proxy_view:__init__:96 - 初始化视图，加载第一页代理数据
2025-07-23 17:27:26.494 | INFO     | ui.main_window:_setup_log_managers:457 - 已为 9 个视图创建日志管理器
2025-07-23 17:27:26.699 | INFO     | __main__:on_auth_completed:95 - 主窗口已启动
2025-07-23 17:27:26.753 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:88 - 尝试连接监听日志信号...
2025-07-23 17:27:26.753 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:90 - 监听日志信号连接成功
2025-07-23 17:27:26.760 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-23 17:27:26.764 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-23 17:27:26.770 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:145 - 加载监控任务列表 (搜索关键词: '')
2025-07-23 17:27:26.770 | DEBUG    | ui.views.monitor_view:_on_loading_started:620 - 加载中: 正在加载任务列表...
2025-07-23 17:27:26.770 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-23 17:27:26.778 | INFO     | app.services.proxy_service:refresh_proxies:712 - 刷新代理列表: offset=0, limit=20
2025-07-23 17:27:26.778 | INFO     | core.proxy.proxy_task_manager:submit_task:172 - 提交耗时任务到子线程: refresh_proxies_0_20
2025-07-23 17:27:26.778 | DEBUG    | app.services.proxy_service:get_proxies:500 - 获取代理列表: offset=0, limit=20
2025-07-23 17:27:26.778 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-23 17:27:26.778 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-23 17:27:26.782 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-23 17:27:26.804 | INFO     | app.controllers.invite_controller:get_invite_tasks:38 - 获取邀请任务列表, status=None
2025-07-23 17:27:26.804 | INFO     | app.services.invite_service:get_invite_tasks:121 - 获取邀请任务列表, status=None
2025-07-23 17:27:26.804 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-23 17:27:26.804 | INFO     | app.services.invite_service:task_initialize:39 - 初始化邀请服务，重置运行中的任务为暂停状态
2025-07-23 17:27:26.806 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-23 17:27:26.808 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-23 17:27:26.809 | INFO     | core.proxy.proxy_task_manager:task_wrapper:199 - 任务 refresh_proxies_0_20 完成
2025-07-23 17:27:26.810 | INFO     | utils.vip_checker:start_checking:37 - 开始VIP状态检查
2025-07-23 17:27:26.810 | DEBUG    | utils.vip_checker:_check_vip_status:61 - 正在检查VIP状态...
2025-07-23 17:27:26.810 | INFO     | core.auth.api_service:verify_vip:425 - 会员验证接口调用
2025-07-23 17:27:27.029 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-23 17:27:27.030 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-23 17:27:27.092 | DEBUG    | ui.views.monitor_view:_refresh_task_list:175 - 刷新任务列表，共0个任务
2025-07-23 17:27:27.092 | INFO     | ui.views.monitor_view:_refresh_task_list:189 - 没有找到监控任务
2025-07-23 17:27:27.092 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-23 17:27:27.094 | INFO     | app.services.invite_service:task_initialize:48 - 成功重置 0 个任务状态
2025-07-23 17:27:27.094 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-23 17:27:27.099 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-23 17:27:27.102 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-23 17:27:27.104 | INFO     | app.services.account_service:get_all_groups:99 - 获取所有账户分组成功, 共 2 个
2025-07-23 17:27:27.104 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-23 17:27:27.104 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-23 17:27:27.109 | DEBUG    | ui.views.account_view:_on_groups_loaded:519 - 分组加载完成: 2个分组
2025-07-23 17:27:27.122 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-23 17:27:27.178 | INFO     | core.auth.api_service:verify_vip:438 - 会员验证成功
2025-07-23 17:27:27.178 | INFO     | utils.vip_checker:_check_vip_status:76 - VIP状态有效
2025-07-23 17:27:27.187 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-23 17:27:27.188 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-23 17:27:27.194 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-07-23 17:27:27.199 | INFO     | app.controllers.telegram_monitor_controller:_load_total_users_count_internal:191 - 成功加载总体统计数据: 总用户数 23, 今日采集 0, 日均采集 23, 运行天数 1
2025-07-23 17:27:27.199 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:156 - 成功加载 0 个任务
2025-07-23 17:27:27.199 | DEBUG    | ui.views.monitor_view:_on_loading_finished:625 - 加载完成
2025-07-23 17:27:27.217 | INFO     | app.services.account_service:get_all_accounts:333 - 获取所有账户成功, 共 2 个
2025-07-23 17:27:27.217 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-23 17:27:27.219 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-23 17:27:27.219 | DEBUG    | ui.views.account_view:_on_accounts_loaded:652 - 账户加载完成: 2个账户
2025-07-23 17:27:27.238 | DEBUG    | ui.views.account_view:_update_group_counts:188 - 分组计数已更新: {2: 2}
2025-07-23 17:27:27.243 | INFO     | ui.views.account_view:_auto_login_accounts:707 - 开始调用控制器进行 2 个账户的自动登录。
2025-07-23 17:27:27.243 | INFO     | app.services.account_service:batch_auto_login:1411 - 服务层：开始批量自动登录 2 个账户, 最大并发: 5
2025-07-23 17:27:27.243 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-23 17:27:27.261 | INFO     | app.services.account_service:batch_auto_login:1451 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-07-23 17:27:27.262 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-23 17:27:27.266 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-23 17:27:27.276 | INFO     | app.services.account_service:batch_auto_login:1451 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-07-23 17:27:27.276 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-23 17:27:27.279 | INFO     | app.services.account_service:get_all_accounts:333 - 获取所有账户成功, 共 2 个
2025-07-23 17:27:27.279 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-23 17:27:27.280 | INFO     | app.services.account_service:batch_auto_login:1521 - 服务层：准备调用核心层处理 2 个账户的自动登录。
2025-07-23 17:27:27.280 | INFO     | app.services.account_service:batch_auto_login:1531 - 服务层：设置核心层任务超时为 120 秒。
2025-07-23 17:27:27.280 | INFO     | core.telegram.client_manager:batch_auto_login:1230 - 开始批量自动登录, 共 2 个账户, 最大并发 5
2025-07-23 17:27:27.281 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:271 - 创建客户端: H:\PyProject\TeleTest\APPDATA\sessions\+***********, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-23 17:27:27.281 | DEBUG    | core.telegram.client_manager:get_proxy:96 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-23 17:27:27.281 | DEBUG    | core.telegram.client_manager:get_proxy:143 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-07-23 17:27:27.284 | DEBUG    | ui.views.account_view:_update_group_counts:188 - 分组计数已更新: {2: 2}
2025-07-23 17:27:27.284 | DEBUG    | ui.views.account_view:_init_cache:155 - 账户缓存初始化完成，共 2 个账户
2025-07-23 17:27:27.290 | DEBUG    | ui.views.account_view:_on_accounts_loaded:652 - 账户加载完成: 2个账户
2025-07-23 17:27:27.296 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:271 - 创建客户端: H:\PyProject\TeleTest\APPDATA\sessions\+***********, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-23 17:27:27.296 | DEBUG    | core.telegram.client_manager:get_proxy:96 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-23 17:27:27.296 | DEBUG    | core.telegram.client_manager:get_proxy:143 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-07-23 17:27:27.321 | DEBUG    | ui.views.account_view:_update_group_counts:188 - 分组计数已更新: {2: 2}
2025-07-23 17:27:27.342 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-23 17:27:27.416 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-23 17:27:27.437 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-07-23 17:27:27.438 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-07-23 17:27:27.440 | INFO     | ui.views.proxy_view:_load_initial_data:1044 - 初始数据加载完成，共 1 个代理
2025-07-23 17:27:27.474 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-23 17:27:33.899 | INFO     | core.telegram.client_manager:_connect_client:219 - 连接成功!
2025-07-23 17:27:35.655 | INFO     | core.telegram.client_manager:_verify_client_authorization:1205 - 账户已授权: +***********
2025-07-23 17:27:37.834 | INFO     | core.telegram.client_manager:_connect_client:219 - 连接成功!
2025-07-23 17:27:40.455 | INFO     | core.telegram.client_manager:_verify_client_authorization:1205 - 账户已授权: +***********
2025-07-23 17:27:42.449 | INFO     | core.telegram.client_manager:batch_auto_login:1379 - 批量登录完成: 总计 2, 成功 2, 失败 0
2025-07-23 17:27:43.309 | INFO     | app.services.account_service:batch_auto_login:1552 - 服务层：核心层登录完成: 成功 2, 失败 0 / 2
2025-07-23 17:27:43.309 | INFO     | app.services.account_service:_process_batch_login_results:2187 - 开始处理批量登录结果，共 2 个账户
2025-07-23 17:27:43.310 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-23 17:27:43.315 | INFO     | data.repositories.account_repo:update_account:394 - 更新账户成功: 1
2025-07-23 17:27:43.315 | INFO     | app.services.account_service:_process_batch_login_results:2223 - 已更新账户 +*********** 的用户信息
2025-07-23 17:27:43.320 | INFO     | data.repositories.account_repo:update_account:394 - 更新账户成功: 2
2025-07-23 17:27:43.320 | INFO     | app.services.account_service:_process_batch_login_results:2223 - 已更新账户 +*********** 的用户信息
2025-07-23 17:27:43.325 | INFO     | app.services.account_service:_process_batch_login_results:2246 - 批量登录结果处理完成，数据库已更新
2025-07-23 17:27:43.326 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-23 17:27:43.326 | INFO     | app.controllers.account_controller:auto_login_accounts:642 - 批量登录完成，触发账户数据刷新
2025-07-23 17:28:25.976 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-23 17:28:37.448 | INFO     | app.services.account_service:update_account_profile:546 - 更新账户资料: 2
2025-07-23 17:28:37.448 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-23 17:28:37.461 | INFO     | app.services.account_service:_generate_unique_username:1883 - 生成唯一用户名成功: tianyier00 (尝试 1/5)
2025-07-23 17:28:37.461 | INFO     | app.services.account_service:update_account_profile:614 - 开始更新账户 +*********** 的Telegram资料，超时设置: 30秒
2025-07-23 17:28:37.462 | INFO     | core.telegram.user_manager:update_profile:89 - 正在更新用户资料: +***********
2025-07-23 17:28:39.601 | INFO     | core.telegram.user_manager:update_profile:130 - 更新用户资料成功: +***********
2025-07-23 17:28:40.478 | INFO     | data.repositories.account_repo:update_account:394 - 更新账户成功: 2
2025-07-23 17:28:40.484 | INFO     | app.services.account_service:update_account_profile:643 - 更新账户 +*********** 每日消息限制为: 29
2025-07-23 17:28:40.488 | INFO     | app.services.account_service:update_account_profile:654 - 更新账户 +*********** 每日邀请限制为: 29
2025-07-23 17:28:40.493 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-23 17:28:42.517 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-23 17:28:42.581 | INFO     | app.services.account_service:get_all_accounts:333 - 获取所有账户成功, 共 2 个
2025-07-23 17:28:42.581 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-23 17:28:42.583 | DEBUG    | ui.views.account_view:_on_accounts_loaded:652 - 账户加载完成: 2个账户
2025-07-23 17:28:42.604 | DEBUG    | ui.views.account_view:_update_group_counts:188 - 分组计数已更新: {2: 2}
2025-07-23 17:29:25.977 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
