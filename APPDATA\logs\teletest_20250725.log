2025-07-25 10:19:54.930 | INFO     | utils.logger:init_logger:98 - 日志系统初始化完成，默认级别: DEBUG，日志目录: H:\PyProject\TeleTest\APPDATA\logs
2025-07-25 10:19:57.956 | INFO     | data.database:init_database:31 - 初始化数据库连接: H:\PyProject\TeleTest\APPDATA\database\telegram_manager.db
2025-07-25 10:19:57.993 | DEBUG    | data.database:init_database:58 - 创建数据库表
2025-07-25 10:19:58.011 | INFO     | data.database:init_database:61 - 数据库初始化完成
2025-07-25 10:19:59.563 | DEBUG    | app.controllers.auth_controller:initialize_software:43 - 控制器: 初始化软件 app_id=1000, version=1.3.7
2025-07-25 10:19:59.564 | DEBUG    | core.auth.api_service:init_software:55 - 发送初始化请求: https://server.xile188.com/api/user/1000/windowns10/1.3.7/ini, params={}
2025-07-25 10:20:00.035 | DEBUG    | core.auth.api_service:init_software:66 - 初始化数据解密成功: {'new_content': None, 'new_url': None, 'notice': {'id': '2', 'content': '公告内容测试3', 'time': '**********'}}
2025-07-25 10:20:00.051 | INFO     | app.controllers.auth_controller:login:61 - 控制器: 用户登录 account=demo01
2025-07-25 10:20:03.337 | INFO     | core.auth.api_service:login:127 - 用户登录: account=demo01
2025-07-25 10:20:03.876 | INFO     | core.auth.api_service:get_user_info:347 - 获取用户信息
2025-07-25 10:20:04.385 | INFO     | core.auth.api_service:get_user_info:375 - 获取用户信息成功
2025-07-25 10:20:04.392 | INFO     | __main__:on_auth_completed:91 - 用户登录成功，正在启动主窗口...
2025-07-25 10:20:04.422 | INFO     | core.telegram.client_worker:__init__:61 - Telegram客户端工作线程初始化
2025-07-25 10:20:04.423 | INFO     | utils.vip_checker:initialize:25 - VIP检查器已初始化，等待登录后开始检查
2025-07-25 10:20:04.424 | INFO     | ui.main_window:_initialize_core_components:113 - MainWindow: 初始化核心组件...
2025-07-25 10:20:04.424 | INFO     | core.telegram.client_worker:run:65 - Telegram客户端工作线程开始运行
2025-07-25 10:20:04.425 | INFO     | app.services.account_service:__init__:45 - 账户服务初始化
2025-07-25 10:20:04.425 | INFO     | core.proxy.proxy_task_manager:run:284 - 代理任务管理器线程启动
2025-07-25 10:20:04.426 | INFO     | app.controllers.account_controller:__init__:83 - 账户控制器初始化
2025-07-25 10:20:04.427 | INFO     | app.services.monitor_service:__init__:39 - 初始化监控任务服务
2025-07-25 10:20:04.427 | INFO     | core.monitor.filter:__init__:26 - 初始化消息过滤器
2025-07-25 10:20:04.427 | INFO     | core.telegram.client_manager:__init__:66 - Telegram客户端管理器初始化
2025-07-25 10:20:04.427 | INFO     | core.monitor.extractor:__init__:24 - 初始化关键词提取器
2025-07-25 10:20:04.428 | INFO     | core.telegram.user_manager:__init__:30 - Telegram用户管理器初始化
2025-07-25 10:20:04.428 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-07-25 10:20:04.428 | INFO     | core.telegram.group_manager:__init__:36 - Telegram群组管理器初始化
2025-07-25 10:20:04.429 | INFO     | core.monitor.notifier:__init__:90 - 初始化通知管理器
2025-07-25 10:20:04.429 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-07-25 10:20:04.429 | INFO     | app.controllers.telegram_monitor_controller:__init__:64 - 初始化 Telegram 监控控制器
2025-07-25 10:20:04.430 | INFO     | core.telegram.monitor_manager:__init__:26 - Telegram监控管理器初始化
2025-07-25 10:20:04.430 | DEBUG    | app.controllers.telegram_monitor_controller:__init__:82 - 控制器初始化完成
2025-07-25 10:20:04.430 | INFO     | core.telegram.telegram_service:__init__:39 - Telegram服务初始化完成
2025-07-25 10:20:04.699 | INFO     | app.controllers.convert_controller:set_account_controller:35 - 账户控制器已设置
2025-07-25 10:20:04.965 | INFO     | ui.views.monitor_view:__init__:48 - 初始化监控视图
2025-07-25 10:20:04.966 | DEBUG    | ui.views.monitor_view:_connect_signals:80 - 连接控制器信号
2025-07-25 10:20:05.700 | INFO     | ui.views.proxy_view:__init__:96 - 初始化视图，加载第一页代理数据
2025-07-25 10:20:05.790 | INFO     | ui.main_window:_setup_log_managers:457 - 已为 9 个视图创建日志管理器
2025-07-25 10:20:06.174 | INFO     | __main__:on_auth_completed:95 - 主窗口已启动
2025-07-25 10:20:06.277 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:88 - 尝试连接监听日志信号...
2025-07-25 10:20:06.279 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:90 - 监听日志信号连接成功
2025-07-25 10:20:06.288 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-25 10:20:06.295 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-25 10:20:06.300 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:145 - 加载监控任务列表 (搜索关键词: '')
2025-07-25 10:20:06.301 | DEBUG    | ui.views.monitor_view:_on_loading_started:620 - 加载中: 正在加载任务列表...
2025-07-25 10:20:06.301 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-25 10:20:06.314 | INFO     | app.services.proxy_service:refresh_proxies:712 - 刷新代理列表: offset=0, limit=20
2025-07-25 10:20:06.315 | INFO     | core.proxy.proxy_task_manager:submit_task:172 - 提交耗时任务到子线程: refresh_proxies_0_20
2025-07-25 10:20:06.315 | DEBUG    | app.services.proxy_service:get_proxies:500 - 获取代理列表: offset=0, limit=20
2025-07-25 10:20:06.315 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-25 10:20:06.316 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-25 10:20:06.331 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-25 10:20:06.356 | INFO     | app.controllers.invite_controller:get_invite_tasks:38 - 获取邀请任务列表, status=None
2025-07-25 10:20:06.359 | INFO     | app.services.invite_service:get_invite_tasks:121 - 获取邀请任务列表, status=None
2025-07-25 10:20:06.360 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-25 10:20:06.360 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-25 10:20:06.361 | INFO     | app.services.invite_service:task_initialize:39 - 初始化邀请服务，重置运行中的任务为暂停状态
2025-07-25 10:20:06.362 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-25 10:20:06.362 | INFO     | core.proxy.proxy_task_manager:task_wrapper:199 - 任务 refresh_proxies_0_20 完成
2025-07-25 10:20:06.365 | INFO     | utils.vip_checker:start_checking:37 - 开始VIP状态检查
2025-07-25 10:20:06.366 | DEBUG    | utils.vip_checker:_check_vip_status:61 - 正在检查VIP状态...
2025-07-25 10:20:06.366 | INFO     | core.auth.api_service:verify_vip:425 - 会员验证接口调用
2025-07-25 10:20:06.508 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-25 10:20:06.511 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-25 10:20:06.520 | DEBUG    | ui.views.monitor_view:_refresh_task_list:175 - 刷新任务列表，共0个任务
2025-07-25 10:20:06.521 | INFO     | ui.views.monitor_view:_refresh_task_list:189 - 没有找到监控任务
2025-07-25 10:20:06.521 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-25 10:20:06.524 | INFO     | app.services.invite_service:task_initialize:48 - 成功重置 0 个任务状态
2025-07-25 10:20:06.525 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-25 10:20:06.526 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-25 10:20:06.531 | INFO     | app.services.account_service:get_all_groups:99 - 获取所有账户分组成功, 共 2 个
2025-07-25 10:20:06.532 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-25 10:20:06.532 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-25 10:20:06.535 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-25 10:20:06.538 | DEBUG    | ui.views.account_view:_on_groups_loaded:519 - 分组加载完成: 2个分组
2025-07-25 10:20:06.567 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-25 10:20:06.757 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-25 10:20:06.760 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-25 10:20:06.766 | INFO     | app.controllers.telegram_monitor_controller:_load_total_users_count_internal:191 - 成功加载总体统计数据: 总用户数 23, 今日采集 0, 日均采集 23, 运行天数 1
2025-07-25 10:20:06.767 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:156 - 成功加载 0 个任务
2025-07-25 10:20:06.767 | DEBUG    | ui.views.monitor_view:_on_loading_finished:625 - 加载完成
2025-07-25 10:20:06.768 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-07-25 10:20:06.795 | INFO     | app.services.account_service:get_all_accounts:333 - 获取所有账户成功, 共 2 个
2025-07-25 10:20:06.795 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-25 10:20:06.798 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-25 10:20:06.799 | DEBUG    | ui.views.account_view:_on_accounts_loaded:652 - 账户加载完成: 2个账户
2025-07-25 10:20:06.834 | DEBUG    | ui.views.account_view:_update_group_counts:188 - 分组计数已更新: {2: 2}
2025-07-25 10:20:06.840 | INFO     | ui.views.account_view:_auto_login_accounts:707 - 开始调用控制器进行 2 个账户的自动登录。
2025-07-25 10:20:06.840 | INFO     | app.services.account_service:batch_auto_login:1411 - 服务层：开始批量自动登录 2 个账户, 最大并发: 5
2025-07-25 10:20:06.841 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-25 10:20:06.847 | INFO     | core.auth.api_service:verify_vip:438 - 会员验证成功
2025-07-25 10:20:06.848 | INFO     | utils.vip_checker:_check_vip_status:76 - VIP状态有效
2025-07-25 10:20:06.864 | INFO     | app.services.account_service:batch_auto_login:1451 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-07-25 10:20:06.865 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-25 10:20:06.867 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-25 10:20:06.875 | INFO     | app.services.account_service:batch_auto_login:1451 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-07-25 10:20:06.875 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-25 10:20:06.880 | INFO     | app.services.account_service:batch_auto_login:1521 - 服务层：准备调用核心层处理 2 个账户的自动登录。
2025-07-25 10:20:06.881 | INFO     | app.services.account_service:batch_auto_login:1531 - 服务层：设置核心层任务超时为 120 秒。
2025-07-25 10:20:06.881 | INFO     | core.telegram.client_manager:batch_auto_login:1230 - 开始批量自动登录, 共 2 个账户, 最大并发 5
2025-07-25 10:20:06.883 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:271 - 创建客户端: H:\PyProject\TeleTest\APPDATA\sessions\+***********, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-25 10:20:06.886 | DEBUG    | core.telegram.client_manager:get_proxy:96 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-25 10:20:06.887 | INFO     | app.services.account_service:get_all_accounts:333 - 获取所有账户成功, 共 2 个
2025-07-25 10:20:06.888 | DEBUG    | core.telegram.client_manager:get_proxy:143 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-07-25 10:20:06.888 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-25 10:20:06.893 | DEBUG    | ui.views.account_view:_update_group_counts:188 - 分组计数已更新: {2: 2}
2025-07-25 10:20:06.893 | DEBUG    | ui.views.account_view:_init_cache:155 - 账户缓存初始化完成，共 2 个账户
2025-07-25 10:20:06.895 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:271 - 创建客户端: H:\PyProject\TeleTest\APPDATA\sessions\+***********, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-25 10:20:06.898 | DEBUG    | core.telegram.client_manager:get_proxy:96 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-25 10:20:06.902 | DEBUG    | ui.views.account_view:_on_accounts_loaded:652 - 账户加载完成: 2个账户
2025-07-25 10:20:06.902 | DEBUG    | core.telegram.client_manager:get_proxy:143 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-07-25 10:20:06.942 | DEBUG    | ui.views.account_view:_update_group_counts:188 - 分组计数已更新: {2: 2}
2025-07-25 10:20:06.972 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-25 10:20:07.087 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-25 10:20:07.096 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-07-25 10:20:07.099 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-07-25 10:20:07.101 | INFO     | ui.views.proxy_view:_load_initial_data:1044 - 初始数据加载完成，共 1 个代理
2025-07-25 10:20:07.137 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-25 10:20:09.950 | INFO     | core.telegram.client_manager:_connect_client:219 - 连接成功!
2025-07-25 10:20:11.958 | INFO     | core.telegram.client_manager:_verify_client_authorization:1205 - 账户已授权: +***********
2025-07-25 10:20:17.512 | INFO     | core.telegram.client_manager:_connect_client:219 - 连接成功!
2025-07-25 10:20:18.446 | INFO     | core.telegram.client_manager:_verify_client_authorization:1205 - 账户已授权: +***********
2025-07-25 10:20:20.466 | INFO     | core.telegram.client_manager:batch_auto_login:1379 - 批量登录完成: 总计 2, 成功 2, 失败 0
2025-07-25 10:20:20.944 | INFO     | app.services.account_service:batch_auto_login:1552 - 服务层：核心层登录完成: 成功 2, 失败 0 / 2
2025-07-25 10:20:20.945 | INFO     | app.services.account_service:_process_batch_login_results:2187 - 开始处理批量登录结果，共 2 个账户
2025-07-25 10:20:20.945 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-25 10:20:20.969 | INFO     | data.repositories.account_repo:update_account:394 - 更新账户成功: 1
2025-07-25 10:20:20.970 | INFO     | app.services.account_service:_process_batch_login_results:2223 - 已更新账户 +*********** 的用户信息
2025-07-25 10:20:20.979 | INFO     | data.repositories.account_repo:update_account:394 - 更新账户成功: 2
2025-07-25 10:20:20.980 | INFO     | app.services.account_service:_process_batch_login_results:2223 - 已更新账户 +*********** 的用户信息
2025-07-25 10:20:20.999 | INFO     | app.services.account_service:_process_batch_login_results:2246 - 批量登录结果处理完成，数据库已更新
2025-07-25 10:20:21.004 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-25 10:20:21.005 | INFO     | app.controllers.account_controller:auto_login_accounts:642 - 批量登录完成，触发账户数据刷新
2025-07-25 10:20:30.565 | INFO     | ui.main_window:closeEvent:466 - MainWindow: 接收到关闭事件
2025-07-25 10:20:30.585 | INFO     | ui.main_window:_cleanup_before_quit:308 - MainWindow: 执行清理资源...
2025-07-25 10:20:30.589 | INFO     | core.telegram.client_worker:stop:108 - 正在停止Telegram客户端工作线程
2025-07-25 10:20:30.595 | INFO     | core.telegram.client_manager:cleanup_async:1150 - 开始清理资源并断开所有连接
2025-07-25 10:20:30.595 | INFO     | core.telegram.client_manager:disconnect_all_clients:735 - 正在断开所有客户端连接
2025-07-25 10:20:30.597 | INFO     | core.telegram.client_manager:_safe_disconnect:756 - 正在断开客户端连接: +***********
2025-07-25 10:20:30.598 | INFO     | core.telegram.client_manager:_safe_disconnect:756 - 正在断开客户端连接: +***********
2025-07-25 10:20:30.614 | INFO     | core.telegram.client_manager:_safe_disconnect:760 - 断开客户端连接成功: +***********
2025-07-25 10:20:30.615 | INFO     | core.telegram.client_manager:_safe_disconnect:760 - 断开客户端连接成功: +***********
2025-07-25 10:20:30.616 | INFO     | core.telegram.client_manager:cleanup_async:1169 - 资源清理完成
2025-07-25 10:20:31.102 | INFO     | core.telegram.client_manager:cleanup_async:1150 - 开始清理资源并断开所有连接
2025-07-25 10:20:31.103 | INFO     | core.telegram.client_manager:disconnect_all_clients:735 - 正在断开所有客户端连接
2025-07-25 10:20:31.103 | INFO     | core.telegram.client_manager:cleanup_async:1169 - 资源清理完成
2025-07-25 10:20:31.603 | INFO     | core.telegram.client_worker:run:97 - Telegram客户端工作线程已退出
2025-07-25 10:20:31.605 | INFO     | core.telegram.client_worker:stop:122 - Telegram客户端工作线程已停止
2025-07-25 10:20:31.605 | INFO     | ui.main_window:_cleanup_before_quit:320 - TelegramClientWorker 已停止。
2025-07-25 10:20:31.605 | INFO     | ui.main_window:_cleanup_before_quit:324 - MainWindow 清理完成
2025-07-25 10:20:31.623 | INFO     | __main__:main:109 - 应用程序已正常退出
2025-07-25 17:42:01.738 | INFO     | utils.logger:init_logger:98 - 日志系统初始化完成，默认级别: DEBUG，日志目录: H:\PyProject\TeleTest\APPDATA\logs
2025-07-25 17:42:04.007 | INFO     | data.database:init_database:31 - 初始化数据库连接: H:\PyProject\TeleTest\APPDATA\database\telegram_manager.db
2025-07-25 17:42:04.035 | DEBUG    | data.database:init_database:58 - 创建数据库表
2025-07-25 17:42:04.048 | INFO     | data.database:init_database:61 - 数据库初始化完成
2025-07-25 17:42:05.696 | DEBUG    | app.controllers.auth_controller:initialize_software:43 - 控制器: 初始化软件 app_id=1000, version=1.3.7
2025-07-25 17:42:05.696 | DEBUG    | core.auth.api_service:init_software:55 - 发送初始化请求: https://server.xile188.com/api/user/1000/windowns10/1.3.7/ini, params={}
2025-07-25 17:42:06.247 | DEBUG    | core.auth.api_service:init_software:66 - 初始化数据解密成功: {'new_content': None, 'new_url': None, 'notice': {'id': '2', 'content': '公告内容测试3', 'time': '**********'}}
2025-07-25 17:42:06.259 | INFO     | app.controllers.auth_controller:login:61 - 控制器: 用户登录 account=demo01
2025-07-25 17:42:09.231 | INFO     | core.auth.api_service:login:127 - 用户登录: account=demo01
2025-07-25 17:42:09.775 | INFO     | core.auth.api_service:get_user_info:347 - 获取用户信息
2025-07-25 17:42:10.277 | INFO     | core.auth.api_service:get_user_info:375 - 获取用户信息成功
2025-07-25 17:42:10.286 | INFO     | __main__:on_auth_completed:91 - 用户登录成功，正在启动主窗口...
2025-07-25 17:42:10.325 | INFO     | core.telegram.client_worker:__init__:61 - Telegram客户端工作线程初始化
2025-07-25 17:42:10.325 | INFO     | utils.vip_checker:initialize:25 - VIP检查器已初始化，等待登录后开始检查
2025-07-25 17:42:10.325 | INFO     | ui.main_window:_initialize_core_components:113 - MainWindow: 初始化核心组件...
2025-07-25 17:42:10.326 | INFO     | core.telegram.client_worker:run:65 - Telegram客户端工作线程开始运行
2025-07-25 17:42:10.327 | INFO     | app.services.account_service:__init__:45 - 账户服务初始化
2025-07-25 17:42:10.327 | INFO     | core.proxy.proxy_task_manager:run:284 - 代理任务管理器线程启动
2025-07-25 17:42:10.328 | INFO     | app.controllers.account_controller:__init__:83 - 账户控制器初始化
2025-07-25 17:42:10.328 | INFO     | app.services.monitor_service:__init__:39 - 初始化监控任务服务
2025-07-25 17:42:10.328 | INFO     | core.telegram.client_manager:__init__:66 - Telegram客户端管理器初始化
2025-07-25 17:42:10.328 | INFO     | core.monitor.filter:__init__:26 - 初始化消息过滤器
2025-07-25 17:42:10.329 | INFO     | core.telegram.user_manager:__init__:30 - Telegram用户管理器初始化
2025-07-25 17:42:10.329 | INFO     | core.monitor.extractor:__init__:24 - 初始化关键词提取器
2025-07-25 17:42:10.329 | INFO     | core.telegram.group_manager:__init__:36 - Telegram群组管理器初始化
2025-07-25 17:42:10.329 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-07-25 17:42:10.329 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-07-25 17:42:10.329 | INFO     | core.monitor.notifier:__init__:90 - 初始化通知管理器
2025-07-25 17:42:10.329 | INFO     | core.telegram.monitor_manager:__init__:26 - Telegram监控管理器初始化
2025-07-25 17:42:10.330 | INFO     | app.controllers.telegram_monitor_controller:__init__:64 - 初始化 Telegram 监控控制器
2025-07-25 17:42:10.330 | INFO     | core.telegram.telegram_service:__init__:39 - Telegram服务初始化完成
2025-07-25 17:42:10.330 | DEBUG    | app.controllers.telegram_monitor_controller:__init__:82 - 控制器初始化完成
2025-07-25 17:42:10.483 | INFO     | app.controllers.convert_controller:set_account_controller:35 - 账户控制器已设置
2025-07-25 17:42:10.568 | INFO     | ui.views.monitor_view:__init__:48 - 初始化监控视图
2025-07-25 17:42:10.568 | DEBUG    | ui.views.monitor_view:_connect_signals:80 - 连接控制器信号
2025-07-25 17:42:10.741 | INFO     | ui.views.proxy_view:__init__:96 - 初始化视图，加载第一页代理数据
2025-07-25 17:42:10.795 | INFO     | ui.main_window:_setup_log_managers:457 - 已为 9 个视图创建日志管理器
2025-07-25 17:42:11.003 | INFO     | __main__:on_auth_completed:95 - 主窗口已启动
2025-07-25 17:42:11.051 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:88 - 尝试连接监听日志信号...
2025-07-25 17:42:11.051 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:90 - 监听日志信号连接成功
2025-07-25 17:42:11.061 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-25 17:42:11.065 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-25 17:42:11.069 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:145 - 加载监控任务列表 (搜索关键词: '')
2025-07-25 17:42:11.070 | DEBUG    | ui.views.monitor_view:_on_loading_started:620 - 加载中: 正在加载任务列表...
2025-07-25 17:42:11.070 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-25 17:42:11.076 | INFO     | app.services.proxy_service:refresh_proxies:712 - 刷新代理列表: offset=0, limit=20
2025-07-25 17:42:11.077 | INFO     | core.proxy.proxy_task_manager:submit_task:172 - 提交耗时任务到子线程: refresh_proxies_0_20
2025-07-25 17:42:11.078 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-25 17:42:11.078 | DEBUG    | app.services.proxy_service:get_proxies:500 - 获取代理列表: offset=0, limit=20
2025-07-25 17:42:11.079 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-25 17:42:11.084 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-25 17:42:11.107 | INFO     | app.controllers.invite_controller:get_invite_tasks:38 - 获取邀请任务列表, status=None
2025-07-25 17:42:11.107 | INFO     | app.services.invite_service:get_invite_tasks:121 - 获取邀请任务列表, status=None
2025-07-25 17:42:11.108 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-25 17:42:11.111 | INFO     | app.services.invite_service:task_initialize:39 - 初始化邀请服务，重置运行中的任务为暂停状态
2025-07-25 17:42:11.112 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-25 17:42:11.112 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-25 17:42:11.114 | INFO     | core.proxy.proxy_task_manager:task_wrapper:199 - 任务 refresh_proxies_0_20 完成
2025-07-25 17:42:11.117 | INFO     | utils.vip_checker:start_checking:37 - 开始VIP状态检查
2025-07-25 17:42:11.118 | DEBUG    | utils.vip_checker:_check_vip_status:61 - 正在检查VIP状态...
2025-07-25 17:42:11.118 | INFO     | core.auth.api_service:verify_vip:425 - 会员验证接口调用
2025-07-25 17:42:11.319 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-25 17:42:11.319 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-25 17:42:11.325 | DEBUG    | ui.views.monitor_view:_refresh_task_list:175 - 刷新任务列表，共0个任务
2025-07-25 17:42:11.325 | INFO     | ui.views.monitor_view:_refresh_task_list:189 - 没有找到监控任务
2025-07-25 17:42:11.325 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-25 17:42:11.327 | INFO     | app.services.invite_service:task_initialize:48 - 成功重置 0 个任务状态
2025-07-25 17:42:11.327 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-25 17:42:11.328 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-25 17:42:11.331 | INFO     | app.services.account_service:get_all_groups:99 - 获取所有账户分组成功, 共 2 个
2025-07-25 17:42:11.331 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-25 17:42:11.336 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-25 17:42:11.337 | DEBUG    | ui.views.account_view:_on_groups_loaded:519 - 分组加载完成: 2个分组
2025-07-25 17:42:11.350 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-25 17:42:11.354 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-25 17:42:11.471 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-25 17:42:11.472 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-25 17:42:11.477 | INFO     | app.controllers.telegram_monitor_controller:_load_total_users_count_internal:191 - 成功加载总体统计数据: 总用户数 23, 今日采集 0, 日均采集 23, 运行天数 1
2025-07-25 17:42:11.477 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:156 - 成功加载 0 个任务
2025-07-25 17:42:11.478 | DEBUG    | ui.views.monitor_view:_on_loading_finished:625 - 加载完成
2025-07-25 17:42:11.479 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-07-25 17:42:11.496 | INFO     | app.services.account_service:get_all_accounts:333 - 获取所有账户成功, 共 2 个
2025-07-25 17:42:11.497 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-25 17:42:11.498 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-25 17:42:11.500 | DEBUG    | ui.views.account_view:_on_accounts_loaded:652 - 账户加载完成: 2个账户
2025-07-25 17:42:11.522 | DEBUG    | ui.views.account_view:_update_group_counts:188 - 分组计数已更新: {2: 2}
2025-07-25 17:42:11.526 | INFO     | ui.views.account_view:_auto_login_accounts:707 - 开始调用控制器进行 2 个账户的自动登录。
2025-07-25 17:42:11.526 | INFO     | app.services.account_service:batch_auto_login:1411 - 服务层：开始批量自动登录 2 个账户, 最大并发: 5
2025-07-25 17:42:11.527 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-25 17:42:11.531 | INFO     | core.auth.api_service:verify_vip:438 - 会员验证成功
2025-07-25 17:42:11.532 | INFO     | utils.vip_checker:_check_vip_status:76 - VIP状态有效
2025-07-25 17:42:11.542 | INFO     | app.services.account_service:batch_auto_login:1451 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-07-25 17:42:11.543 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-25 17:42:11.545 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-25 17:42:11.552 | INFO     | app.services.account_service:batch_auto_login:1451 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-07-25 17:42:11.553 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-25 17:42:11.556 | INFO     | app.services.account_service:batch_auto_login:1521 - 服务层：准备调用核心层处理 2 个账户的自动登录。
2025-07-25 17:42:11.557 | INFO     | core.telegram.client_manager:batch_auto_login:1230 - 开始批量自动登录, 共 2 个账户, 最大并发 5
2025-07-25 17:42:11.557 | INFO     | app.services.account_service:batch_auto_login:1531 - 服务层：设置核心层任务超时为 120 秒。
2025-07-25 17:42:11.558 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:271 - 创建客户端: H:\PyProject\TeleTest\APPDATA\sessions\+***********, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-25 17:42:11.559 | DEBUG    | core.telegram.client_manager:get_proxy:96 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-25 17:42:11.560 | DEBUG    | core.telegram.client_manager:get_proxy:143 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-07-25 17:42:11.562 | INFO     | app.services.account_service:get_all_accounts:333 - 获取所有账户成功, 共 2 个
2025-07-25 17:42:11.562 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-25 17:42:11.564 | DEBUG    | ui.views.account_view:_update_group_counts:188 - 分组计数已更新: {2: 2}
2025-07-25 17:42:11.564 | DEBUG    | ui.views.account_view:_init_cache:155 - 账户缓存初始化完成，共 2 个账户
2025-07-25 17:42:11.569 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:271 - 创建客户端: H:\PyProject\TeleTest\APPDATA\sessions\+***********, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-25 17:42:11.570 | DEBUG    | ui.views.account_view:_on_accounts_loaded:652 - 账户加载完成: 2个账户
2025-07-25 17:42:11.570 | DEBUG    | core.telegram.client_manager:get_proxy:96 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-25 17:42:11.572 | DEBUG    | core.telegram.client_manager:get_proxy:143 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-07-25 17:42:11.598 | DEBUG    | ui.views.account_view:_update_group_counts:188 - 分组计数已更新: {2: 2}
2025-07-25 17:42:11.620 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-25 17:42:11.695 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-25 17:42:11.705 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-07-25 17:42:11.707 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-07-25 17:42:11.709 | INFO     | ui.views.proxy_view:_load_initial_data:1044 - 初始数据加载完成，共 1 个代理
2025-07-25 17:42:11.734 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-25 17:42:13.518 | INFO     | ui.main_window:closeEvent:466 - MainWindow: 接收到关闭事件
2025-07-25 17:42:13.542 | INFO     | ui.main_window:_cleanup_before_quit:308 - MainWindow: 执行清理资源...
2025-07-25 17:42:13.545 | INFO     | core.telegram.client_worker:stop:108 - 正在停止Telegram客户端工作线程
2025-07-25 17:42:13.547 | INFO     | core.telegram.client_worker:_graceful_shutdown:456 - 等待 1 个耗时任务完成...
2025-07-25 17:42:14.273 | INFO     | core.telegram.client_manager:_connect_client:219 - 连接成功!
2025-07-25 17:42:15.102 | INFO     | core.telegram.client_manager:_verify_client_authorization:1205 - 账户已授权: +***********
2025-07-25 20:23:50.366 | INFO     | utils.logger:init_logger:98 - 日志系统初始化完成，默认级别: DEBUG，日志目录: H:\PyProject\TeleTest\APPDATA\logs
2025-07-25 20:24:19.846 | INFO     | utils.logger:init_logger:98 - 日志系统初始化完成，默认级别: DEBUG，日志目录: H:\PyProject\TeleTest\APPDATA\logs
2025-07-25 20:24:19.863 | INFO     | core.proxy.proxy_validator:validate_proxy:68 - 正在验证代理 socks5://127.0.0.1:1080
2025-07-25 20:24:19.868 | WARNING  | core.proxy.proxy_validator:validate_proxy:100 - 使用https://web.telegram.org验证代理失败: socks5://127.0.0.1:1080, 错误: 'tuple' object has no attribute 'transport'
2025-07-25 20:24:19.872 | WARNING  | core.proxy.proxy_validator:validate_proxy:100 - 使用https://core.telegram.org验证代理失败: socks5://127.0.0.1:1080, 错误: 'tuple' object has no attribute 'transport'
2025-07-25 20:24:19.874 | WARNING  | core.proxy.proxy_validator:validate_proxy:104 - 代理验证失败: socks5://127.0.0.1:1080
2025-07-25 20:25:39.928 | INFO     | utils.logger:init_logger:98 - 日志系统初始化完成，默认级别: DEBUG，日志目录: H:\PyProject\TeleTest\APPDATA\logs
2025-07-25 20:25:39.944 | INFO     | core.proxy.proxy_validator:validate_proxy:44 - 正在验证代理 socks5://127.0.0.1:1080
2025-07-25 20:25:39.947 | WARNING  | core.proxy.proxy_validator:validate_proxy:95 - 使用https://web.telegram.org验证代理失败: socks5://127.0.0.1:1080, 错误: [WinError 10035] 无法立即完成一个非阻止性套接字操作。
2025-07-25 20:25:39.948 | WARNING  | core.proxy.proxy_validator:validate_proxy:95 - 使用https://core.telegram.org验证代理失败: socks5://127.0.0.1:1080, 错误: [WinError 10035] 无法立即完成一个非阻止性套接字操作。
2025-07-25 20:25:39.949 | WARNING  | core.proxy.proxy_validator:validate_proxy:99 - 代理验证失败: socks5://127.0.0.1:1080
