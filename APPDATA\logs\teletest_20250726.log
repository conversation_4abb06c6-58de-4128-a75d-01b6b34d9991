2025-07-26 11:19:25.129 | INFO     | utils.logger:init_logger:98 - 日志系统初始化完成，默认级别: DEBUG，日志目录: H:\PyProject\TeleTest\APPDATA\logs
2025-07-26 11:19:34.605 | INFO     | utils.logger:init_logger:98 - 日志系统初始化完成，默认级别: DEBUG，日志目录: H:\PyProject\TeleTest\APPDATA\logs
2025-07-26 11:19:52.535 | INFO     | utils.logger:init_logger:98 - 日志系统初始化完成，默认级别: DEBUG，日志目录: H:\PyProject\TeleTest\APPDATA\logs
2025-07-26 11:19:55.467 | INFO     | data.database:init_database:31 - 初始化数据库连接: H:\PyProject\TeleTest\APPDATA\database\telegram_manager.db
2025-07-26 11:19:55.582 | DEBUG    | data.database:init_database:58 - 创建数据库表
2025-07-26 11:19:55.729 | INFO     | data.database:init_database:61 - 数据库初始化完成
2025-07-26 11:19:57.092 | DEBUG    | app.controllers.auth_controller:initialize_software:43 - 控制器: 初始化软件 app_id=1000, version=1.3.7
2025-07-26 11:19:57.093 | DEBUG    | core.auth.api_service:init_software:55 - 发送初始化请求: https://server.xile188.com/api/user/1000/windowns10/1.3.7/ini, params={}
2025-07-26 11:19:57.572 | DEBUG    | core.auth.api_service:init_software:66 - 初始化数据解密成功: {'new_content': None, 'new_url': None, 'notice': {'id': '2', 'content': '公告内容测试3', 'time': '**********'}}
2025-07-26 11:19:57.580 | INFO     | app.controllers.auth_controller:login:61 - 控制器: 用户登录 account=demo01
2025-07-26 11:20:00.663 | INFO     | core.auth.api_service:login:127 - 用户登录: account=demo01
2025-07-26 11:20:01.211 | INFO     | core.auth.api_service:get_user_info:347 - 获取用户信息
2025-07-26 11:20:01.406 | INFO     | core.auth.api_service:get_user_info:375 - 获取用户信息成功
2025-07-26 11:20:01.616 | INFO     | __main__:on_auth_completed:91 - 用户登录成功，正在启动主窗口...
2025-07-26 11:20:01.639 | INFO     | core.telegram.client_worker:__init__:61 - Telegram客户端工作线程初始化
2025-07-26 11:20:01.640 | INFO     | utils.vip_checker:initialize:25 - VIP检查器已初始化，等待登录后开始检查
2025-07-26 11:20:01.640 | INFO     | ui.main_window:_initialize_core_components:113 - MainWindow: 初始化核心组件...
2025-07-26 11:20:01.640 | INFO     | core.telegram.client_worker:run:65 - Telegram客户端工作线程开始运行
2025-07-26 11:20:01.642 | INFO     | app.services.account_service:__init__:45 - 账户服务初始化
2025-07-26 11:20:01.642 | INFO     | core.proxy.proxy_task_manager:run:284 - 代理任务管理器线程启动
2025-07-26 11:20:01.642 | INFO     | app.controllers.account_controller:__init__:83 - 账户控制器初始化
2025-07-26 11:20:01.643 | INFO     | app.services.monitor_service:__init__:37 - 初始化监控任务服务
2025-07-26 11:20:01.643 | INFO     | core.telegram.client_manager:__init__:66 - Telegram客户端管理器初始化
2025-07-26 11:20:01.643 | INFO     | core.monitor.filter:__init__:26 - 初始化消息过滤器
2025-07-26 11:20:01.644 | INFO     | core.telegram.user_manager:__init__:30 - Telegram用户管理器初始化
2025-07-26 11:20:01.644 | INFO     | core.monitor.extractor:__init__:24 - 初始化关键词提取器
2025-07-26 11:20:01.645 | INFO     | core.telegram.group_manager:__init__:36 - Telegram群组管理器初始化
2025-07-26 11:20:01.645 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-07-26 11:20:01.645 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-07-26 11:20:01.645 | INFO     | core.monitor.notifier:__init__:90 - 初始化通知管理器
2025-07-26 11:20:01.646 | INFO     | core.telegram.monitor_manager:__init__:26 - Telegram监控管理器初始化
2025-07-26 11:20:01.646 | INFO     | app.controllers.telegram_monitor_controller:__init__:64 - 初始化 Telegram 监控控制器
2025-07-26 11:20:01.646 | INFO     | core.telegram.telegram_service:__init__:39 - Telegram服务初始化完成
2025-07-26 11:20:01.646 | DEBUG    | app.controllers.telegram_monitor_controller:__init__:82 - 控制器初始化完成
2025-07-26 11:20:01.785 | INFO     | app.controllers.convert_controller:set_account_controller:35 - 账户控制器已设置
2025-07-26 11:20:01.872 | INFO     | ui.views.monitor_view:__init__:48 - 初始化监控视图
2025-07-26 11:20:01.873 | DEBUG    | ui.views.monitor_view:_connect_signals:80 - 连接控制器信号
2025-07-26 11:20:02.053 | INFO     | ui.views.proxy_view:__init__:96 - 初始化视图，加载第一页代理数据
2025-07-26 11:20:02.126 | INFO     | ui.main_window:_setup_log_managers:457 - 已为 9 个视图创建日志管理器
2025-07-26 11:20:02.331 | INFO     | __main__:on_auth_completed:95 - 主窗口已启动
2025-07-26 11:20:02.392 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:88 - 尝试连接监听日志信号...
2025-07-26 11:20:02.393 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:90 - 监听日志信号连接成功
2025-07-26 11:20:02.400 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-26 11:20:02.404 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-26 11:20:02.410 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:145 - 加载监控任务列表 (搜索关键词: '')
2025-07-26 11:20:02.410 | DEBUG    | ui.views.monitor_view:_on_loading_started:620 - 加载中: 正在加载任务列表...
2025-07-26 11:20:02.411 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-26 11:20:02.417 | INFO     | app.services.proxy_service:refresh_proxies:712 - 刷新代理列表: offset=0, limit=20
2025-07-26 11:20:02.417 | INFO     | core.proxy.proxy_task_manager:submit_task:172 - 提交耗时任务到子线程: refresh_proxies_0_20
2025-07-26 11:20:02.418 | DEBUG    | app.services.proxy_service:get_proxies:500 - 获取代理列表: offset=0, limit=20
2025-07-26 11:20:02.418 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-26 11:20:02.418 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-26 11:20:02.423 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-26 11:20:02.447 | INFO     | app.controllers.invite_controller:get_invite_tasks:38 - 获取邀请任务列表, status=None
2025-07-26 11:20:02.447 | INFO     | app.services.invite_service:get_invite_tasks:121 - 获取邀请任务列表, status=None
2025-07-26 11:20:02.447 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-26 11:20:02.450 | INFO     | app.services.invite_service:task_initialize:39 - 初始化邀请服务，重置运行中的任务为暂停状态
2025-07-26 11:20:02.451 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-26 11:20:02.451 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-26 11:20:02.453 | INFO     | core.proxy.proxy_task_manager:task_wrapper:199 - 任务 refresh_proxies_0_20 完成
2025-07-26 11:20:02.455 | INFO     | utils.vip_checker:start_checking:37 - 开始VIP状态检查
2025-07-26 11:20:02.455 | DEBUG    | utils.vip_checker:_check_vip_status:61 - 正在检查VIP状态...
2025-07-26 11:20:02.455 | INFO     | core.auth.api_service:verify_vip:425 - 会员验证接口调用
2025-07-26 11:20:02.587 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-26 11:20:02.594 | INFO     | app.services.invite_service:task_initialize:48 - 成功重置 0 个任务状态
2025-07-26 11:20:02.594 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-26 11:20:02.596 | DEBUG    | ui.views.monitor_view:_refresh_task_list:175 - 刷新任务列表，共0个任务
2025-07-26 11:20:02.596 | INFO     | ui.views.monitor_view:_refresh_task_list:189 - 没有找到监控任务
2025-07-26 11:20:02.597 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-26 11:20:02.599 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-26 11:20:02.599 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-26 11:20:02.602 | INFO     | app.services.account_service:get_all_groups:99 - 获取所有账户分组成功, 共 2 个
2025-07-26 11:20:02.602 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-26 11:20:02.603 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-26 11:20:02.606 | DEBUG    | ui.views.account_view:_on_groups_loaded:519 - 分组加载完成: 2个分组
2025-07-26 11:20:02.618 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-26 11:20:02.622 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-26 11:20:02.690 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-26 11:20:02.690 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-26 11:20:02.696 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-07-26 11:20:02.709 | INFO     | app.controllers.telegram_monitor_controller:_load_total_users_count_internal:191 - 成功加载总体统计数据: 总用户数 23, 今日采集 0, 日均采集 23, 运行天数 1
2025-07-26 11:20:02.709 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:156 - 成功加载 0 个任务
2025-07-26 11:20:02.709 | DEBUG    | ui.views.monitor_view:_on_loading_finished:625 - 加载完成
2025-07-26 11:20:02.715 | INFO     | app.services.account_service:get_all_accounts:333 - 获取所有账户成功, 共 2 个
2025-07-26 11:20:02.715 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-26 11:20:02.717 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-26 11:20:02.719 | DEBUG    | ui.views.account_view:_on_accounts_loaded:652 - 账户加载完成: 2个账户
2025-07-26 11:20:02.741 | DEBUG    | ui.views.account_view:_update_group_counts:188 - 分组计数已更新: {2: 2}
2025-07-26 11:20:02.745 | INFO     | ui.views.account_view:_auto_login_accounts:707 - 开始调用控制器进行 2 个账户的自动登录。
2025-07-26 11:20:02.746 | INFO     | app.services.account_service:batch_auto_login:1411 - 服务层：开始批量自动登录 2 个账户, 最大并发: 5
2025-07-26 11:20:02.746 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-26 11:20:02.751 | INFO     | core.auth.api_service:verify_vip:438 - 会员验证成功
2025-07-26 11:20:02.752 | INFO     | utils.vip_checker:_check_vip_status:76 - VIP状态有效
2025-07-26 11:20:02.762 | INFO     | app.services.account_service:batch_auto_login:1451 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-07-26 11:20:02.762 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-26 11:20:02.765 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-26 11:20:02.772 | INFO     | app.services.account_service:batch_auto_login:1451 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-07-26 11:20:02.773 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-26 11:20:02.775 | INFO     | app.services.account_service:batch_auto_login:1521 - 服务层：准备调用核心层处理 2 个账户的自动登录。
2025-07-26 11:20:02.775 | INFO     | app.services.account_service:batch_auto_login:1531 - 服务层：设置核心层任务超时为 120 秒。
2025-07-26 11:20:02.775 | INFO     | core.telegram.client_manager:batch_auto_login:1230 - 开始批量自动登录, 共 2 个账户, 最大并发 5
2025-07-26 11:20:02.777 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:271 - 创建客户端: H:\PyProject\TeleTest\APPDATA\sessions\+***********, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-26 11:20:02.777 | DEBUG    | core.telegram.client_manager:get_proxy:96 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-26 11:20:02.779 | DEBUG    | core.telegram.client_manager:get_proxy:143 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-07-26 11:20:02.780 | INFO     | app.services.account_service:get_all_accounts:333 - 获取所有账户成功, 共 2 个
2025-07-26 11:20:02.781 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-26 11:20:02.784 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:271 - 创建客户端: H:\PyProject\TeleTest\APPDATA\sessions\+***********, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-26 11:20:02.784 | DEBUG    | ui.views.account_view:_update_group_counts:188 - 分组计数已更新: {2: 2}
2025-07-26 11:20:02.785 | DEBUG    | core.telegram.client_manager:get_proxy:96 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-26 11:20:02.785 | DEBUG    | ui.views.account_view:_init_cache:155 - 账户缓存初始化完成，共 2 个账户
2025-07-26 11:20:02.785 | DEBUG    | core.telegram.client_manager:get_proxy:143 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-07-26 11:20:02.790 | DEBUG    | ui.views.account_view:_on_accounts_loaded:652 - 账户加载完成: 2个账户
2025-07-26 11:20:02.828 | DEBUG    | ui.views.account_view:_update_group_counts:188 - 分组计数已更新: {2: 2}
2025-07-26 11:20:02.850 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-26 11:20:02.931 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-26 11:20:02.936 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-07-26 11:20:02.938 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-07-26 11:20:02.939 | INFO     | ui.views.proxy_view:_load_initial_data:1044 - 初始数据加载完成，共 1 个代理
2025-07-26 11:20:02.966 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-26 11:20:06.777 | INFO     | app.services.account_service:refresh_account_info:841 - 刷新账户信息: 2
2025-07-26 11:20:06.778 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-26 11:20:06.789 | INFO     | core.telegram.user_manager:get_user_info:41 - 正在获取用户信息: +***********
2025-07-26 11:20:06.789 | WARNING  | core.telegram.user_manager:get_user_info:47 - 获取用户信息失败, 找不到客户端: +***********
2025-07-26 11:20:06.943 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-26 11:20:08.057 | INFO     | core.telegram.client_manager:_connect_client:219 - 连接成功!
2025-07-26 11:20:08.121 | INFO     | core.telegram.client_manager:_connect_client:219 - 连接成功!
2025-07-26 11:20:09.357 | INFO     | core.telegram.client_manager:_verify_client_authorization:1205 - 账户已授权: +***********
2025-07-26 11:20:09.413 | INFO     | core.telegram.client_manager:_verify_client_authorization:1205 - 账户已授权: +***********
2025-07-26 11:20:11.399 | INFO     | core.telegram.client_manager:batch_auto_login:1379 - 批量登录完成: 总计 2, 成功 2, 失败 0
2025-07-26 11:20:11.786 | INFO     | app.services.account_service:batch_auto_login:1552 - 服务层：核心层登录完成: 成功 2, 失败 0 / 2
2025-07-26 11:20:11.786 | INFO     | app.services.account_service:_process_batch_login_results:2187 - 开始处理批量登录结果，共 2 个账户
2025-07-26 11:20:11.787 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-26 11:20:11.791 | INFO     | data.repositories.account_repo:update_account:394 - 更新账户成功: 1
2025-07-26 11:20:11.792 | INFO     | app.services.account_service:_process_batch_login_results:2223 - 已更新账户 +*********** 的用户信息
2025-07-26 11:20:11.799 | INFO     | data.repositories.account_repo:update_account:394 - 更新账户成功: 2
2025-07-26 11:20:11.800 | INFO     | app.services.account_service:_process_batch_login_results:2223 - 已更新账户 +*********** 的用户信息
2025-07-26 11:20:11.809 | INFO     | app.services.account_service:_process_batch_login_results:2246 - 批量登录结果处理完成，数据库已更新
2025-07-26 11:20:11.809 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-26 11:20:11.810 | INFO     | app.controllers.account_controller:auto_login_accounts:642 - 批量登录完成，触发账户数据刷新
2025-07-26 11:20:12.769 | INFO     | app.services.account_service:refresh_account_info:841 - 刷新账户信息: 2
2025-07-26 11:20:12.769 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-26 11:20:12.781 | INFO     | core.telegram.user_manager:get_user_info:41 - 正在获取用户信息: +***********
2025-07-26 11:20:14.197 | INFO     | core.telegram.user_manager:get_user_info:66 - 获取用户信息成功: +***********
2025-07-26 11:20:14.798 | INFO     | data.repositories.account_repo:update_account:394 - 更新账户成功: 2
2025-07-26 11:20:14.814 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-26 11:20:14.820 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-26 11:20:14.861 | INFO     | ui.views.account_view:_update_single_account_display:337 - 已实时更新账户显示: +***********
2025-07-26 11:20:14.862 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-26 11:20:17.244 | INFO     | ui.views.convert_view:update_proxy_settings:514 - 代理设置已更新: {'type': 'system'}
2025-07-26 11:20:17.622 | INFO     | ui.views.convert_view:update_proxy_settings:514 - 代理设置已更新: {'type': 'none'}
2025-07-26 11:20:17.657 | INFO     | app.services.account_service:get_proxy_ips:1291 - 获取有效代理IP列表（包含绑定计数）
2025-07-26 11:20:17.658 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-26 11:20:17.666 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-26 11:20:17.667 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-26 11:20:17.671 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-26 11:20:17.673 | INFO     | app.services.account_service:get_proxy_ips:1343 - 成功获取 1 个有效代理（已附带绑定计数）
2025-07-26 11:20:17.674 | INFO     | app.controllers.account_controller:get_proxy_ips:569 - 获取到1个有效代理IP
2025-07-26 11:20:17.675 | INFO     | ui.views.convert_view:log_message:1417 - 已加载 1 个代理IP
2025-07-26 11:20:18.245 | INFO     | ui.views.convert_view:update_proxy_settings:514 - 代理设置已更新: {'type': 'none'}
2025-07-26 11:20:18.943 | INFO     | ui.views.convert_view:update_proxy_settings:514 - 代理设置已更新: {'type': 'system'}
2025-07-26 11:20:21.397 | INFO     | ui.views.monitor_view:open_add_task_dialog:135 - 打开添加任务对话框 
2025-07-26 11:20:21.491 | INFO     | ui.views.add_monitor_view:_load_initial_data:111 - 加载初始数据
2025-07-26 11:20:21.549 | INFO     | ui.views.add_monitor_view:_load_account_groups:400 - 加载账户分组
2025-07-26 11:20:21.549 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-26 11:20:21.551 | INFO     | ui.views.add_monitor_view:_load_accounts_by_group:417 - 根据分组ID加载账户: -1
2025-07-26 11:20:21.551 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-26 11:20:21.719 | INFO     | app.services.account_service:get_all_accounts:333 - 获取所有账户成功, 共 2 个
2025-07-26 11:20:21.720 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-26 11:20:21.722 | INFO     | ui.views.add_monitor_view:_load_accounts_by_group:442 - 已加载 2 个账户到列表
2025-07-26 11:20:21.722 | DEBUG    | ui.views.account_view:_on_accounts_loaded:652 - 账户加载完成: 2个账户
2025-07-26 11:20:21.742 | DEBUG    | ui.views.account_view:_update_group_counts:188 - 分组计数已更新: {2: 2}
2025-07-26 11:20:21.754 | INFO     | app.services.account_service:get_all_groups:99 - 获取所有账户分组成功, 共 2 个
2025-07-26 11:20:21.754 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-26 11:20:21.755 | DEBUG    | ui.views.account_view:_on_groups_loaded:519 - 分组加载完成: 2个分组
2025-07-26 11:20:21.756 | INFO     | ui.views.add_monitor_view:_on_group_index_changed:460 - 当前分组id:-1
2025-07-26 11:20:21.757 | INFO     | ui.views.add_monitor_view:_load_account_groups:409 - 加载账户分组数据: [{'id': 2, 'name': '主账户', 'description': '', 'account_count': 2}, {'id': 1, 'name': '营销', 'description': '', 'account_count': 0}]
2025-07-26 11:20:21.757 | INFO     | ui.views.add_monitor_view:_load_accounts_by_group:417 - 根据分组ID加载账户: -1
2025-07-26 11:20:21.757 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-26 11:20:21.764 | INFO     | app.services.account_service:get_all_accounts:333 - 获取所有账户成功, 共 2 个
2025-07-26 11:20:21.764 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-26 11:20:21.766 | INFO     | ui.views.add_monitor_view:_load_accounts_by_group:442 - 已加载 2 个账户到列表
2025-07-26 11:20:21.767 | DEBUG    | ui.views.account_view:_on_accounts_loaded:652 - 账户加载完成: 2个账户
2025-07-26 11:20:21.787 | DEBUG    | ui.views.account_view:_update_group_counts:188 - 分组计数已更新: {2: 2}
2025-07-26 11:20:38.273 | INFO     | ui.main_window:closeEvent:466 - MainWindow: 接收到关闭事件
2025-07-26 11:20:40.538 | INFO     | ui.main_window:_cleanup_before_quit:308 - MainWindow: 执行清理资源...
2025-07-26 11:20:40.539 | INFO     | core.telegram.client_worker:stop:108 - 正在停止Telegram客户端工作线程
2025-07-26 11:20:40.551 | INFO     | core.telegram.client_manager:cleanup_async:1150 - 开始清理资源并断开所有连接
2025-07-26 11:20:40.552 | INFO     | core.telegram.client_manager:disconnect_all_clients:735 - 正在断开所有客户端连接
2025-07-26 11:20:40.552 | INFO     | core.telegram.client_manager:_safe_disconnect:756 - 正在断开客户端连接: +***********
2025-07-26 11:20:40.554 | INFO     | core.telegram.client_manager:_safe_disconnect:756 - 正在断开客户端连接: +***********
2025-07-26 11:20:40.563 | INFO     | core.telegram.client_manager:_safe_disconnect:760 - 断开客户端连接成功: +***********
2025-07-26 11:20:40.564 | INFO     | core.telegram.client_manager:_safe_disconnect:760 - 断开客户端连接成功: +***********
2025-07-26 11:20:40.565 | INFO     | core.telegram.client_manager:cleanup_async:1169 - 资源清理完成
2025-07-26 11:20:41.057 | INFO     | core.telegram.client_manager:cleanup_async:1150 - 开始清理资源并断开所有连接
2025-07-26 11:20:41.057 | INFO     | core.telegram.client_manager:disconnect_all_clients:735 - 正在断开所有客户端连接
2025-07-26 11:20:41.058 | INFO     | core.telegram.client_manager:cleanup_async:1169 - 资源清理完成
2025-07-26 11:20:41.560 | INFO     | core.telegram.client_worker:run:97 - Telegram客户端工作线程已退出
2025-07-26 11:20:41.560 | INFO     | core.telegram.client_worker:stop:122 - Telegram客户端工作线程已停止
2025-07-26 11:20:41.561 | INFO     | ui.main_window:_cleanup_before_quit:320 - TelegramClientWorker 已停止。
2025-07-26 11:20:41.561 | INFO     | ui.main_window:_cleanup_before_quit:324 - MainWindow 清理完成
2025-07-26 11:20:41.577 | INFO     | __main__:main:109 - 应用程序已正常退出
2025-07-26 11:23:24.748 | INFO     | utils.logger:init_logger:98 - 日志系统初始化完成，默认级别: DEBUG，日志目录: H:\PyProject\TeleTest\APPDATA\logs
2025-07-26 11:23:25.825 | INFO     | data.database:init_database:31 - 初始化数据库连接: H:\PyProject\TeleTest\APPDATA\database\telegram_manager.db
2025-07-26 11:23:25.841 | DEBUG    | data.database:init_database:58 - 创建数据库表
2025-07-26 11:23:25.852 | INFO     | data.database:init_database:61 - 数据库初始化完成
2025-07-26 11:23:27.029 | DEBUG    | app.controllers.auth_controller:initialize_software:43 - 控制器: 初始化软件 app_id=1000, version=1.3.7
2025-07-26 11:23:27.031 | DEBUG    | core.auth.api_service:init_software:55 - 发送初始化请求: https://server.xile188.com/api/user/1000/windowns10/1.3.7/ini, params={}
2025-07-26 11:23:27.264 | DEBUG    | core.auth.api_service:init_software:66 - 初始化数据解密成功: {'new_content': None, 'new_url': None, 'notice': {'id': '2', 'content': '公告内容测试3', 'time': '**********'}}
2025-07-26 11:23:27.273 | INFO     | app.controllers.auth_controller:login:61 - 控制器: 用户登录 account=demo01
2025-07-26 11:23:30.249 | INFO     | core.auth.api_service:login:127 - 用户登录: account=demo01
2025-07-26 11:23:30.490 | INFO     | core.auth.api_service:get_user_info:347 - 获取用户信息
2025-07-26 11:23:30.688 | INFO     | core.auth.api_service:get_user_info:375 - 获取用户信息成功
2025-07-26 11:23:30.695 | INFO     | __main__:on_auth_completed:91 - 用户登录成功，正在启动主窗口...
2025-07-26 11:23:30.731 | INFO     | core.telegram.client_worker:__init__:61 - Telegram客户端工作线程初始化
2025-07-26 11:23:30.732 | INFO     | utils.vip_checker:initialize:25 - VIP检查器已初始化，等待登录后开始检查
2025-07-26 11:23:30.732 | INFO     | ui.main_window:_initialize_core_components:113 - MainWindow: 初始化核心组件...
2025-07-26 11:23:30.733 | INFO     | core.telegram.client_worker:run:65 - Telegram客户端工作线程开始运行
2025-07-26 11:23:30.736 | INFO     | core.proxy.proxy_task_manager:run:284 - 代理任务管理器线程启动
2025-07-26 11:23:30.736 | INFO     | app.services.account_service:__init__:45 - 账户服务初始化
2025-07-26 11:23:30.737 | INFO     | core.telegram.client_manager:__init__:66 - Telegram客户端管理器初始化
2025-07-26 11:23:30.737 | INFO     | app.controllers.account_controller:__init__:83 - 账户控制器初始化
2025-07-26 11:23:30.738 | INFO     | core.telegram.user_manager:__init__:30 - Telegram用户管理器初始化
2025-07-26 11:23:30.738 | INFO     | app.services.monitor_service:__init__:37 - 初始化监控任务服务
2025-07-26 11:23:30.739 | INFO     | core.telegram.group_manager:__init__:36 - Telegram群组管理器初始化
2025-07-26 11:23:30.739 | INFO     | core.monitor.filter:__init__:26 - 初始化消息过滤器
2025-07-26 11:23:30.739 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-07-26 11:23:30.739 | INFO     | core.monitor.extractor:__init__:24 - 初始化关键词提取器
2025-07-26 11:23:30.740 | INFO     | core.telegram.monitor_manager:__init__:26 - Telegram监控管理器初始化
2025-07-26 11:23:30.740 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-07-26 11:23:30.740 | INFO     | core.telegram.telegram_service:__init__:39 - Telegram服务初始化完成
2025-07-26 11:23:30.740 | INFO     | core.monitor.notifier:__init__:90 - 初始化通知管理器
2025-07-26 11:23:30.741 | INFO     | app.controllers.telegram_monitor_controller:__init__:64 - 初始化 Telegram 监控控制器
2025-07-26 11:23:30.741 | DEBUG    | app.controllers.telegram_monitor_controller:__init__:82 - 控制器初始化完成
2025-07-26 11:23:30.909 | INFO     | app.controllers.convert_controller:set_account_controller:35 - 账户控制器已设置
2025-07-26 11:23:31.012 | INFO     | ui.views.monitor_view:__init__:48 - 初始化监控视图
2025-07-26 11:23:31.013 | DEBUG    | ui.views.monitor_view:_connect_signals:80 - 连接控制器信号
2025-07-26 11:23:31.211 | INFO     | ui.views.proxy_view:__init__:96 - 初始化视图，加载第一页代理数据
2025-07-26 11:23:31.289 | INFO     | ui.main_window:_setup_log_managers:457 - 已为 9 个视图创建日志管理器
2025-07-26 11:23:31.565 | INFO     | __main__:on_auth_completed:95 - 主窗口已启动
2025-07-26 11:23:31.611 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:88 - 尝试连接监听日志信号...
2025-07-26 11:23:31.611 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:90 - 监听日志信号连接成功
2025-07-26 11:23:31.620 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-26 11:23:31.623 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-26 11:23:31.627 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:145 - 加载监控任务列表 (搜索关键词: '')
2025-07-26 11:23:31.628 | DEBUG    | ui.views.monitor_view:_on_loading_started:620 - 加载中: 正在加载任务列表...
2025-07-26 11:23:31.628 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-26 11:23:31.637 | INFO     | app.services.proxy_service:refresh_proxies:712 - 刷新代理列表: offset=0, limit=20
2025-07-26 11:23:31.638 | INFO     | core.proxy.proxy_task_manager:submit_task:172 - 提交耗时任务到子线程: refresh_proxies_0_20
2025-07-26 11:23:31.638 | DEBUG    | app.services.proxy_service:get_proxies:500 - 获取代理列表: offset=0, limit=20
2025-07-26 11:23:31.638 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-26 11:23:31.639 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-26 11:23:31.645 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-26 11:23:31.669 | INFO     | app.controllers.invite_controller:get_invite_tasks:38 - 获取邀请任务列表, status=None
2025-07-26 11:23:31.670 | INFO     | app.services.invite_service:get_invite_tasks:121 - 获取邀请任务列表, status=None
2025-07-26 11:23:31.670 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-26 11:23:31.674 | INFO     | app.services.invite_service:task_initialize:39 - 初始化邀请服务，重置运行中的任务为暂停状态
2025-07-26 11:23:31.674 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-26 11:23:31.675 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-26 11:23:31.677 | INFO     | core.proxy.proxy_task_manager:task_wrapper:199 - 任务 refresh_proxies_0_20 完成
2025-07-26 11:23:31.679 | INFO     | utils.vip_checker:start_checking:37 - 开始VIP状态检查
2025-07-26 11:23:31.679 | DEBUG    | utils.vip_checker:_check_vip_status:61 - 正在检查VIP状态...
2025-07-26 11:23:31.679 | INFO     | core.auth.api_service:verify_vip:425 - 会员验证接口调用
2025-07-26 11:23:31.795 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-26 11:23:31.801 | INFO     | app.services.invite_service:task_initialize:48 - 成功重置 0 个任务状态
2025-07-26 11:23:31.802 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-26 11:23:31.803 | DEBUG    | ui.views.monitor_view:_refresh_task_list:175 - 刷新任务列表，共0个任务
2025-07-26 11:23:31.804 | INFO     | ui.views.monitor_view:_refresh_task_list:189 - 没有找到监控任务
2025-07-26 11:23:31.804 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-26 11:23:31.807 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-26 11:23:31.810 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-26 11:23:31.812 | INFO     | app.services.account_service:get_all_groups:99 - 获取所有账户分组成功, 共 2 个
2025-07-26 11:23:31.813 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-26 11:23:31.814 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-26 11:23:31.818 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-26 11:23:31.819 | DEBUG    | ui.views.account_view:_on_groups_loaded:519 - 分组加载完成: 2个分组
2025-07-26 11:23:31.834 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-26 11:23:31.911 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-26 11:23:31.912 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-26 11:23:31.917 | INFO     | app.controllers.telegram_monitor_controller:_load_total_users_count_internal:191 - 成功加载总体统计数据: 总用户数 23, 今日采集 0, 日均采集 23, 运行天数 1
2025-07-26 11:23:31.917 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:156 - 成功加载 0 个任务
2025-07-26 11:23:31.917 | DEBUG    | ui.views.monitor_view:_on_loading_finished:625 - 加载完成
2025-07-26 11:23:31.919 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-07-26 11:23:31.936 | INFO     | app.services.account_service:get_all_accounts:333 - 获取所有账户成功, 共 2 个
2025-07-26 11:23:31.936 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-26 11:23:31.940 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-26 11:23:31.941 | DEBUG    | ui.views.account_view:_on_accounts_loaded:652 - 账户加载完成: 2个账户
2025-07-26 11:23:31.962 | DEBUG    | ui.views.account_view:_update_group_counts:188 - 分组计数已更新: {2: 2}
2025-07-26 11:23:31.966 | INFO     | ui.views.account_view:_auto_login_accounts:707 - 开始调用控制器进行 2 个账户的自动登录。
2025-07-26 11:23:31.966 | INFO     | app.services.account_service:batch_auto_login:1411 - 服务层：开始批量自动登录 2 个账户, 最大并发: 5
2025-07-26 11:23:31.967 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-26 11:23:31.973 | INFO     | core.auth.api_service:verify_vip:438 - 会员验证成功
2025-07-26 11:23:31.974 | INFO     | utils.vip_checker:_check_vip_status:76 - VIP状态有效
2025-07-26 11:23:31.984 | INFO     | app.services.account_service:batch_auto_login:1451 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-07-26 11:23:31.984 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-26 11:23:31.986 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-26 11:23:31.993 | INFO     | app.services.account_service:batch_auto_login:1451 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-07-26 11:23:31.994 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-26 11:23:31.998 | INFO     | app.services.account_service:batch_auto_login:1521 - 服务层：准备调用核心层处理 2 个账户的自动登录。
2025-07-26 11:23:31.998 | INFO     | app.services.account_service:batch_auto_login:1531 - 服务层：设置核心层任务超时为 120 秒。
2025-07-26 11:23:31.998 | INFO     | core.telegram.client_manager:batch_auto_login:1230 - 开始批量自动登录, 共 2 个账户, 最大并发 5
2025-07-26 11:23:31.999 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:271 - 创建客户端: H:\PyProject\TeleTest\APPDATA\sessions\+***********, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-26 11:23:32.000 | DEBUG    | core.telegram.client_manager:get_proxy:96 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-26 11:23:32.000 | INFO     | app.services.account_service:get_all_accounts:333 - 获取所有账户成功, 共 2 个
2025-07-26 11:23:32.001 | DEBUG    | core.telegram.client_manager:get_proxy:143 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-07-26 11:23:32.001 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-26 11:23:32.006 | DEBUG    | ui.views.account_view:_update_group_counts:188 - 分组计数已更新: {2: 2}
2025-07-26 11:23:32.007 | DEBUG    | ui.views.account_view:_init_cache:155 - 账户缓存初始化完成，共 2 个账户
2025-07-26 11:23:32.007 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:271 - 创建客户端: H:\PyProject\TeleTest\APPDATA\sessions\+***********, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-26 11:23:32.008 | DEBUG    | core.telegram.client_manager:get_proxy:96 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-26 11:23:32.009 | DEBUG    | core.telegram.client_manager:get_proxy:143 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-07-26 11:23:32.012 | DEBUG    | ui.views.account_view:_on_accounts_loaded:652 - 账户加载完成: 2个账户
2025-07-26 11:23:32.041 | DEBUG    | ui.views.account_view:_update_group_counts:188 - 分组计数已更新: {2: 2}
2025-07-26 11:23:32.065 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-26 11:23:32.200 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-26 11:23:32.217 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-07-26 11:23:32.220 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-07-26 11:23:32.222 | INFO     | ui.views.proxy_view:_load_initial_data:1044 - 初始数据加载完成，共 1 个代理
2025-07-26 11:23:32.249 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-26 11:23:35.940 | INFO     | core.telegram.client_manager:_connect_client:219 - 连接成功!
2025-07-26 11:23:36.829 | INFO     | core.telegram.client_manager:_connect_client:219 - 连接成功!
2025-07-26 11:23:37.189 | INFO     | core.telegram.client_manager:_verify_client_authorization:1205 - 账户已授权: +***********
2025-07-26 11:23:37.689 | INFO     | core.telegram.client_manager:_verify_client_authorization:1205 - 账户已授权: +***********
2025-07-26 11:23:39.677 | INFO     | core.telegram.client_manager:batch_auto_login:1379 - 批量登录完成: 总计 2, 成功 2, 失败 0
2025-07-26 11:23:40.054 | INFO     | app.services.account_service:batch_auto_login:1552 - 服务层：核心层登录完成: 成功 2, 失败 0 / 2
2025-07-26 11:23:40.054 | INFO     | app.services.account_service:_process_batch_login_results:2187 - 开始处理批量登录结果，共 2 个账户
2025-07-26 11:23:40.054 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-26 11:23:40.059 | INFO     | data.repositories.account_repo:update_account:394 - 更新账户成功: 2
2025-07-26 11:23:40.059 | INFO     | app.services.account_service:_process_batch_login_results:2223 - 已更新账户 +*********** 的用户信息
2025-07-26 11:23:40.064 | INFO     | data.repositories.account_repo:update_account:394 - 更新账户成功: 1
2025-07-26 11:23:40.065 | INFO     | app.services.account_service:_process_batch_login_results:2223 - 已更新账户 +*********** 的用户信息
2025-07-26 11:23:40.072 | INFO     | app.services.account_service:_process_batch_login_results:2246 - 批量登录结果处理完成，数据库已更新
2025-07-26 11:23:40.072 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-26 11:23:40.073 | INFO     | app.controllers.account_controller:auto_login_accounts:642 - 批量登录完成，触发账户数据刷新
2025-07-26 11:23:46.732 | INFO     | ui.main_window:closeEvent:466 - MainWindow: 接收到关闭事件
2025-07-26 11:23:46.742 | INFO     | ui.main_window:_cleanup_before_quit:308 - MainWindow: 执行清理资源...
2025-07-26 11:23:46.743 | INFO     | core.telegram.client_worker:stop:108 - 正在停止Telegram客户端工作线程
2025-07-26 11:23:46.752 | INFO     | core.telegram.client_manager:cleanup_async:1150 - 开始清理资源并断开所有连接
2025-07-26 11:23:46.752 | INFO     | core.telegram.client_manager:disconnect_all_clients:735 - 正在断开所有客户端连接
2025-07-26 11:23:46.753 | INFO     | core.telegram.client_manager:_safe_disconnect:756 - 正在断开客户端连接: +***********
2025-07-26 11:23:46.753 | INFO     | core.telegram.client_manager:_safe_disconnect:756 - 正在断开客户端连接: +***********
2025-07-26 11:23:46.764 | INFO     | core.telegram.client_manager:_safe_disconnect:760 - 断开客户端连接成功: +***********
2025-07-26 11:23:46.764 | INFO     | core.telegram.client_manager:_safe_disconnect:760 - 断开客户端连接成功: +***********
2025-07-26 11:23:46.765 | INFO     | core.telegram.client_manager:cleanup_async:1169 - 资源清理完成
2025-07-26 11:23:47.262 | INFO     | core.telegram.client_manager:cleanup_async:1150 - 开始清理资源并断开所有连接
2025-07-26 11:23:47.262 | INFO     | core.telegram.client_manager:disconnect_all_clients:735 - 正在断开所有客户端连接
2025-07-26 11:23:47.263 | INFO     | core.telegram.client_manager:cleanup_async:1169 - 资源清理完成
2025-07-26 11:23:47.777 | INFO     | core.telegram.client_worker:run:97 - Telegram客户端工作线程已退出
2025-07-26 11:23:47.778 | INFO     | core.telegram.client_worker:stop:122 - Telegram客户端工作线程已停止
2025-07-26 11:23:47.778 | INFO     | ui.main_window:_cleanup_before_quit:320 - TelegramClientWorker 已停止。
2025-07-26 11:23:47.778 | INFO     | ui.main_window:_cleanup_before_quit:324 - MainWindow 清理完成
2025-07-26 11:23:47.795 | INFO     | __main__:main:109 - 应用程序已正常退出
