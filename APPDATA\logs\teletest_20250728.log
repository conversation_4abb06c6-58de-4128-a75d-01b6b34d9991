2025-07-28 10:30:34.782 | INFO     | utils.logger:init_logger:98 - 日志系统初始化完成，默认级别: DEBUG，日志目录: H:\PyProject\TeleTest-bak\APPDATA\logs
2025-07-28 10:31:26.932 | INFO     | utils.logger:init_logger:98 - 日志系统初始化完成，默认级别: DEBUG，日志目录: H:\PyProject\TeleTest-bak\APPDATA\logs
2025-07-28 10:31:27.964 | INFO     | data.database:init_database:31 - 初始化数据库连接: H:\PyProject\TeleTest-bak\APPDATA\database\telegram_manager.db
2025-07-28 10:31:27.978 | CRITICAL | __main__:main:112 - 程序启动失败: No module named 'aiosqlite'
2025-07-28 10:33:34.363 | INFO     | utils.logger:init_logger:98 - 日志系统初始化完成，默认级别: DEBUG，日志目录: H:\PyProject\TeleTest-bak\APPDATA\logs
2025-07-28 10:33:35.413 | INFO     | data.database:init_database:31 - 初始化数据库连接: H:\PyProject\TeleTest-bak\APPDATA\database\telegram_manager.db
2025-07-28 10:33:35.429 | DEBUG    | data.database:init_database:58 - 创建数据库表
2025-07-28 10:33:35.438 | INFO     | data.database:init_database:61 - 数据库初始化完成
2025-07-28 10:33:36.599 | DEBUG    | app.controllers.auth_controller:initialize_software:43 - 控制器: 初始化软件 app_id=1000, version=1.3.7
2025-07-28 10:33:36.599 | DEBUG    | core.auth.api_service:init_software:55 - 发送初始化请求: https://server.xile188.com/api/user/1000/windowns10/1.3.7/ini, params={}
2025-07-28 10:33:37.018 | DEBUG    | core.auth.api_service:init_software:66 - 初始化数据解密成功: {'new_content': None, 'new_url': None, 'notice': {'id': '2', 'content': '公告内容测试3', 'time': '**********'}}
2025-07-28 10:33:37.025 | INFO     | app.controllers.auth_controller:login:61 - 控制器: 用户登录 account=demo01
2025-07-28 10:33:40.083 | INFO     | core.auth.api_service:login:127 - 用户登录: account=demo01
2025-07-28 10:33:40.630 | INFO     | core.auth.api_service:get_user_info:347 - 获取用户信息
2025-07-28 10:33:40.854 | INFO     | core.auth.api_service:get_user_info:375 - 获取用户信息成功
2025-07-28 10:33:40.861 | INFO     | __main__:on_auth_completed:91 - 用户登录成功，正在启动主窗口...
2025-07-28 10:33:40.884 | INFO     | core.telegram.client_worker:__init__:61 - Telegram客户端工作线程初始化
2025-07-28 10:33:40.884 | INFO     | utils.vip_checker:initialize:25 - VIP检查器已初始化，等待登录后开始检查
2025-07-28 10:33:40.884 | INFO     | ui.main_window:_initialize_core_components:113 - MainWindow: 初始化核心组件...
2025-07-28 10:33:40.884 | INFO     | app.services.account_service:__init__:45 - 账户服务初始化
2025-07-28 10:33:40.885 | INFO     | app.controllers.account_controller:__init__:83 - 账户控制器初始化
2025-07-28 10:33:40.885 | INFO     | core.telegram.client_worker:run:65 - Telegram客户端工作线程开始运行
2025-07-28 10:33:40.885 | INFO     | core.proxy.proxy_task_manager:run:284 - 代理任务管理器线程启动
2025-07-28 10:33:40.885 | INFO     | app.services.monitor_service:__init__:37 - 初始化监控任务服务
2025-07-28 10:33:40.885 | INFO     | core.monitor.filter:__init__:26 - 初始化消息过滤器
2025-07-28 10:33:40.886 | INFO     | core.monitor.extractor:__init__:24 - 初始化关键词提取器
2025-07-28 10:33:40.886 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-07-28 10:33:40.886 | INFO     | core.monitor.notifier:__init__:90 - 初始化通知管理器
2025-07-28 10:33:40.886 | INFO     | app.controllers.telegram_monitor_controller:__init__:64 - 初始化 Telegram 监控控制器
2025-07-28 10:33:40.886 | DEBUG    | app.controllers.telegram_monitor_controller:__init__:82 - 控制器初始化完成
2025-07-28 10:33:40.888 | INFO     | core.telegram.client_manager:__init__:66 - Telegram客户端管理器初始化
2025-07-28 10:33:40.888 | INFO     | core.telegram.user_manager:__init__:30 - Telegram用户管理器初始化
2025-07-28 10:33:40.889 | INFO     | core.telegram.group_manager:__init__:36 - Telegram群组管理器初始化
2025-07-28 10:33:40.889 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-07-28 10:33:40.889 | INFO     | core.telegram.monitor_manager:__init__:26 - Telegram监控管理器初始化
2025-07-28 10:33:40.889 | INFO     | core.telegram.telegram_service:__init__:39 - Telegram服务初始化完成
2025-07-28 10:33:41.019 | INFO     | app.controllers.convert_controller:set_account_controller:35 - 账户控制器已设置
2025-07-28 10:33:41.112 | INFO     | ui.views.monitor_view:__init__:48 - 初始化监控视图
2025-07-28 10:33:41.113 | DEBUG    | ui.views.monitor_view:_connect_signals:80 - 连接控制器信号
2025-07-28 10:33:41.295 | INFO     | ui.views.proxy_view:__init__:96 - 初始化视图，加载第一页代理数据
2025-07-28 10:33:41.350 | INFO     | ui.main_window:_setup_log_managers:457 - 已为 9 个视图创建日志管理器
2025-07-28 10:33:41.554 | INFO     | __main__:on_auth_completed:95 - 主窗口已启动
2025-07-28 10:33:41.651 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:88 - 尝试连接监听日志信号...
2025-07-28 10:33:41.651 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:90 - 监听日志信号连接成功
2025-07-28 10:33:41.663 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-28 10:33:41.667 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-28 10:33:41.671 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:145 - 加载监控任务列表 (搜索关键词: '')
2025-07-28 10:33:41.672 | DEBUG    | ui.views.monitor_view:_on_loading_started:620 - 加载中: 正在加载任务列表...
2025-07-28 10:33:41.672 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-28 10:33:41.678 | INFO     | app.services.proxy_service:refresh_proxies:712 - 刷新代理列表: offset=0, limit=20
2025-07-28 10:33:41.678 | INFO     | core.proxy.proxy_task_manager:submit_task:172 - 提交耗时任务到子线程: refresh_proxies_0_20
2025-07-28 10:33:41.679 | DEBUG    | app.services.proxy_service:get_proxies:500 - 获取代理列表: offset=0, limit=20
2025-07-28 10:33:41.679 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-28 10:33:41.679 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-28 10:33:41.682 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-28 10:33:41.706 | INFO     | app.controllers.invite_controller:get_invite_tasks:38 - 获取邀请任务列表, status=None
2025-07-28 10:33:41.706 | INFO     | app.services.invite_service:get_invite_tasks:121 - 获取邀请任务列表, status=None
2025-07-28 10:33:41.706 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-28 10:33:41.708 | INFO     | app.services.invite_service:task_initialize:39 - 初始化邀请服务，重置运行中的任务为暂停状态
2025-07-28 10:33:41.709 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-28 10:33:41.710 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-28 10:33:41.711 | INFO     | core.proxy.proxy_task_manager:task_wrapper:199 - 任务 refresh_proxies_0_20 完成
2025-07-28 10:33:41.713 | INFO     | utils.vip_checker:start_checking:37 - 开始VIP状态检查
2025-07-28 10:33:41.713 | DEBUG    | utils.vip_checker:_check_vip_status:61 - 正在检查VIP状态...
2025-07-28 10:33:41.713 | INFO     | core.auth.api_service:verify_vip:425 - 会员验证接口调用
2025-07-28 10:33:41.913 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-28 10:33:41.914 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-28 10:33:41.916 | INFO     | app.services.invite_service:task_initialize:48 - 成功重置 0 个任务状态
2025-07-28 10:33:41.916 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-28 10:33:41.917 | DEBUG    | ui.views.monitor_view:_refresh_task_list:175 - 刷新任务列表，共0个任务
2025-07-28 10:33:41.917 | INFO     | ui.views.monitor_view:_refresh_task_list:189 - 没有找到监控任务
2025-07-28 10:33:41.917 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-28 10:33:41.921 | INFO     | app.services.account_service:get_all_groups:99 - 获取所有账户分组成功, 共 2 个
2025-07-28 10:33:41.921 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-28 10:33:41.923 | DEBUG    | ui.views.account_view:_on_groups_loaded:519 - 分组加载完成: 2个分组
2025-07-28 10:33:41.936 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-28 10:33:41.943 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-28 10:33:41.943 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-28 10:33:41.948 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-28 10:33:41.950 | INFO     | app.controllers.telegram_monitor_controller:_load_total_users_count_internal:191 - 成功加载总体统计数据: 总用户数 23, 今日采集 0, 日均采集 23, 运行天数 1
2025-07-28 10:33:41.950 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:156 - 成功加载 0 个任务
2025-07-28 10:33:41.950 | DEBUG    | ui.views.monitor_view:_on_loading_finished:625 - 加载完成
2025-07-28 10:33:41.959 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-28 10:33:41.961 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-28 10:33:42.009 | INFO     | app.services.account_service:get_all_accounts:333 - 获取所有账户成功, 共 2 个
2025-07-28 10:33:42.009 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-28 10:33:42.011 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-07-28 10:33:42.021 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-28 10:33:42.022 | DEBUG    | ui.views.account_view:_on_accounts_loaded:652 - 账户加载完成: 2个账户
2025-07-28 10:33:42.043 | DEBUG    | ui.views.account_view:_update_group_counts:188 - 分组计数已更新: {2: 2}
2025-07-28 10:33:42.046 | INFO     | ui.views.account_view:_auto_login_accounts:707 - 开始调用控制器进行 2 个账户的自动登录。
2025-07-28 10:33:42.046 | INFO     | app.services.account_service:batch_auto_login:1411 - 服务层：开始批量自动登录 2 个账户, 最大并发: 5
2025-07-28 10:33:42.046 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-28 10:33:42.059 | INFO     | app.services.account_service:batch_auto_login:1451 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-07-28 10:33:42.059 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-28 10:33:42.061 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-28 10:33:42.067 | INFO     | app.services.account_service:batch_auto_login:1451 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-07-28 10:33:42.067 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-28 10:33:42.069 | INFO     | app.services.account_service:batch_auto_login:1521 - 服务层：准备调用核心层处理 2 个账户的自动登录。
2025-07-28 10:33:42.070 | INFO     | app.services.account_service:batch_auto_login:1531 - 服务层：设置核心层任务超时为 120 秒。
2025-07-28 10:33:42.070 | INFO     | core.telegram.client_manager:batch_auto_login:1230 - 开始批量自动登录, 共 2 个账户, 最大并发 5
2025-07-28 10:33:42.070 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:271 - 创建客户端: H:\PyProject\TeleTest-bak\APPDATA\sessions\+***********, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-28 10:33:42.070 | DEBUG    | core.telegram.client_manager:get_proxy:96 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-28 10:33:42.071 | DEBUG    | core.telegram.client_manager:get_proxy:143 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-07-28 10:33:42.073 | INFO     | app.services.account_service:get_all_accounts:333 - 获取所有账户成功, 共 2 个
2025-07-28 10:33:42.073 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-28 10:33:42.076 | INFO     | core.auth.api_service:verify_vip:438 - 会员验证成功
2025-07-28 10:33:42.076 | INFO     | utils.vip_checker:_check_vip_status:76 - VIP状态有效
2025-07-28 10:33:42.077 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:271 - 创建客户端: H:\PyProject\TeleTest-bak\APPDATA\sessions\+***********, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-28 10:33:42.077 | DEBUG    | ui.views.account_view:_update_group_counts:188 - 分组计数已更新: {2: 2}
2025-07-28 10:33:42.077 | DEBUG    | core.telegram.client_manager:get_proxy:96 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-28 10:33:42.077 | DEBUG    | ui.views.account_view:_init_cache:155 - 账户缓存初始化完成，共 2 个账户
2025-07-28 10:33:42.078 | DEBUG    | core.telegram.client_manager:get_proxy:143 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-07-28 10:33:42.082 | DEBUG    | ui.views.account_view:_on_accounts_loaded:652 - 账户加载完成: 2个账户
2025-07-28 10:33:42.109 | DEBUG    | ui.views.account_view:_update_group_counts:188 - 分组计数已更新: {2: 2}
2025-07-28 10:33:42.136 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-28 10:33:42.227 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-28 10:33:42.243 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-07-28 10:33:42.245 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-07-28 10:33:42.247 | INFO     | ui.views.proxy_view:_load_initial_data:1044 - 初始数据加载完成，共 1 个代理
2025-07-28 10:33:42.278 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-28 10:33:45.915 | INFO     | core.telegram.client_manager:_connect_client:219 - 连接成功!
2025-07-28 10:33:46.706 | INFO     | core.telegram.client_manager:_verify_client_authorization:1205 - 账户已授权: +***********
2025-07-28 10:33:46.811 | INFO     | core.telegram.client_manager:_connect_client:219 - 连接成功!
2025-07-28 10:33:47.977 | INFO     | core.telegram.client_manager:_verify_client_authorization:1205 - 账户已授权: +***********
2025-07-28 10:33:49.995 | INFO     | core.telegram.client_manager:batch_auto_login:1379 - 批量登录完成: 总计 2, 成功 2, 失败 0
2025-07-28 10:33:50.103 | INFO     | app.services.account_service:batch_auto_login:1552 - 服务层：核心层登录完成: 成功 2, 失败 0 / 2
2025-07-28 10:33:50.103 | INFO     | app.services.account_service:_process_batch_login_results:2187 - 开始处理批量登录结果，共 2 个账户
2025-07-28 10:33:50.103 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-28 10:33:50.108 | INFO     | data.repositories.account_repo:update_account:394 - 更新账户成功: 2
2025-07-28 10:33:50.108 | INFO     | app.services.account_service:_process_batch_login_results:2223 - 已更新账户 +*********** 的用户信息
2025-07-28 10:33:50.113 | INFO     | data.repositories.account_repo:update_account:394 - 更新账户成功: 1
2025-07-28 10:33:50.113 | INFO     | app.services.account_service:_process_batch_login_results:2223 - 已更新账户 +*********** 的用户信息
2025-07-28 10:33:50.118 | INFO     | app.services.account_service:_process_batch_login_results:2246 - 批量登录结果处理完成，数据库已更新
2025-07-28 10:33:50.119 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-28 10:33:50.119 | INFO     | app.controllers.account_controller:auto_login_accounts:642 - 批量登录完成，触发账户数据刷新
2025-07-28 10:34:04.620 | INFO     | ui.main_window:closeEvent:466 - MainWindow: 接收到关闭事件
2025-07-28 10:34:04.637 | INFO     | ui.main_window:_cleanup_before_quit:308 - MainWindow: 执行清理资源...
2025-07-28 10:34:04.637 | INFO     | core.telegram.client_worker:stop:108 - 正在停止Telegram客户端工作线程
2025-07-28 10:34:04.644 | INFO     | core.telegram.client_manager:cleanup_async:1150 - 开始清理资源并断开所有连接
2025-07-28 10:34:04.644 | INFO     | core.telegram.client_manager:disconnect_all_clients:735 - 正在断开所有客户端连接
2025-07-28 10:34:04.645 | INFO     | core.telegram.client_manager:_safe_disconnect:756 - 正在断开客户端连接: +***********
2025-07-28 10:34:04.645 | INFO     | core.telegram.client_manager:_safe_disconnect:756 - 正在断开客户端连接: +***********
2025-07-28 10:34:04.654 | INFO     | core.telegram.client_manager:_safe_disconnect:760 - 断开客户端连接成功: +***********
2025-07-28 10:34:04.654 | INFO     | core.telegram.client_manager:_safe_disconnect:760 - 断开客户端连接成功: +***********
2025-07-28 10:34:04.654 | INFO     | core.telegram.client_manager:cleanup_async:1169 - 资源清理完成
2025-07-28 10:34:05.165 | INFO     | core.telegram.client_manager:cleanup_async:1150 - 开始清理资源并断开所有连接
2025-07-28 10:34:05.165 | INFO     | core.telegram.client_manager:disconnect_all_clients:735 - 正在断开所有客户端连接
2025-07-28 10:34:05.165 | INFO     | core.telegram.client_manager:cleanup_async:1169 - 资源清理完成
2025-07-28 10:34:05.673 | INFO     | core.telegram.client_worker:run:97 - Telegram客户端工作线程已退出
2025-07-28 10:34:05.673 | INFO     | core.telegram.client_worker:stop:122 - Telegram客户端工作线程已停止
2025-07-28 10:34:05.674 | INFO     | ui.main_window:_cleanup_before_quit:320 - TelegramClientWorker 已停止。
2025-07-28 10:34:05.674 | INFO     | ui.main_window:_cleanup_before_quit:324 - MainWindow 清理完成
2025-07-28 10:34:05.691 | INFO     | __main__:main:109 - 应用程序已正常退出
2025-07-28 10:36:49.957 | INFO     | utils.logger:init_logger:98 - 日志系统初始化完成，默认级别: DEBUG，日志目录: H:\PyProject\TeleTest-bak\APPDATA\logs
2025-07-28 10:36:51.073 | INFO     | data.database:init_database:31 - 初始化数据库连接: H:\PyProject\TeleTest-bak\APPDATA\database\telegram_manager.db
2025-07-28 10:36:51.088 | DEBUG    | data.database:init_database:58 - 创建数据库表
2025-07-28 10:36:51.101 | INFO     | data.database:init_database:61 - 数据库初始化完成
2025-07-28 10:36:52.130 | DEBUG    | app.controllers.auth_controller:initialize_software:43 - 控制器: 初始化软件 app_id=1000, version=1.3.7
2025-07-28 10:36:52.130 | DEBUG    | core.auth.api_service:init_software:55 - 发送初始化请求: https://server.xile188.com/api/user/1000/windowns10/1.3.7/ini, params={}
2025-07-28 10:36:52.370 | DEBUG    | core.auth.api_service:init_software:66 - 初始化数据解密成功: {'new_content': None, 'new_url': None, 'notice': {'id': '2', 'content': '公告内容测试3', 'time': '**********'}}
2025-07-28 10:36:52.377 | INFO     | app.controllers.auth_controller:login:61 - 控制器: 用户登录 account=demo01
2025-07-28 10:36:55.401 | INFO     | core.auth.api_service:login:127 - 用户登录: account=demo01
2025-07-28 10:36:55.608 | INFO     | core.auth.api_service:get_user_info:347 - 获取用户信息
2025-07-28 10:36:55.902 | INFO     | core.auth.api_service:get_user_info:375 - 获取用户信息成功
2025-07-28 10:36:55.908 | INFO     | __main__:on_auth_completed:91 - 用户登录成功，正在启动主窗口...
2025-07-28 10:36:55.930 | INFO     | core.telegram.client_worker:__init__:61 - Telegram客户端工作线程初始化
2025-07-28 10:36:55.930 | INFO     | utils.vip_checker:initialize:25 - VIP检查器已初始化，等待登录后开始检查
2025-07-28 10:36:55.931 | INFO     | ui.main_window:_initialize_core_components:113 - MainWindow: 初始化核心组件...
2025-07-28 10:36:55.931 | INFO     | core.telegram.client_worker:run:65 - Telegram客户端工作线程开始运行
2025-07-28 10:36:55.932 | INFO     | app.services.account_service:__init__:45 - 账户服务初始化
2025-07-28 10:36:55.932 | INFO     | core.proxy.proxy_task_manager:run:284 - 代理任务管理器线程启动
2025-07-28 10:36:55.933 | INFO     | app.controllers.account_controller:__init__:83 - 账户控制器初始化
2025-07-28 10:36:55.933 | INFO     | core.telegram.client_manager:__init__:66 - Telegram客户端管理器初始化
2025-07-28 10:36:55.934 | INFO     | app.services.monitor_service:__init__:37 - 初始化监控任务服务
2025-07-28 10:36:55.934 | INFO     | core.telegram.user_manager:__init__:30 - Telegram用户管理器初始化
2025-07-28 10:36:55.934 | INFO     | core.monitor.filter:__init__:26 - 初始化消息过滤器
2025-07-28 10:36:55.935 | INFO     | core.telegram.group_manager:__init__:36 - Telegram群组管理器初始化
2025-07-28 10:36:55.935 | INFO     | core.monitor.extractor:__init__:24 - 初始化关键词提取器
2025-07-28 10:36:55.935 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-07-28 10:36:55.935 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-07-28 10:36:55.936 | INFO     | core.telegram.monitor_manager:__init__:26 - Telegram监控管理器初始化
2025-07-28 10:36:55.936 | INFO     | core.monitor.notifier:__init__:90 - 初始化通知管理器
2025-07-28 10:36:55.936 | INFO     | core.telegram.telegram_service:__init__:39 - Telegram服务初始化完成
2025-07-28 10:36:55.936 | INFO     | app.controllers.telegram_monitor_controller:__init__:64 - 初始化 Telegram 监控控制器
2025-07-28 10:36:55.937 | DEBUG    | app.controllers.telegram_monitor_controller:__init__:82 - 控制器初始化完成
2025-07-28 10:36:56.067 | INFO     | app.controllers.convert_controller:set_account_controller:35 - 账户控制器已设置
2025-07-28 10:36:56.154 | INFO     | ui.views.monitor_view:__init__:48 - 初始化监控视图
2025-07-28 10:36:56.154 | DEBUG    | ui.views.monitor_view:_connect_signals:80 - 连接控制器信号
2025-07-28 10:36:56.321 | INFO     | ui.views.proxy_view:__init__:96 - 初始化视图，加载第一页代理数据
2025-07-28 10:36:56.373 | INFO     | ui.main_window:_setup_log_managers:457 - 已为 9 个视图创建日志管理器
2025-07-28 10:36:56.570 | INFO     | __main__:on_auth_completed:95 - 主窗口已启动
2025-07-28 10:36:56.618 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:88 - 尝试连接监听日志信号...
2025-07-28 10:36:56.618 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:90 - 监听日志信号连接成功
2025-07-28 10:36:56.626 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-28 10:36:56.632 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-28 10:36:56.638 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:145 - 加载监控任务列表 (搜索关键词: '')
2025-07-28 10:36:56.639 | DEBUG    | ui.views.monitor_view:_on_loading_started:620 - 加载中: 正在加载任务列表...
2025-07-28 10:36:56.639 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-28 10:36:56.647 | INFO     | app.services.proxy_service:refresh_proxies:712 - 刷新代理列表: offset=0, limit=20
2025-07-28 10:36:56.647 | INFO     | core.proxy.proxy_task_manager:submit_task:172 - 提交耗时任务到子线程: refresh_proxies_0_20
2025-07-28 10:36:56.648 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-28 10:36:56.648 | DEBUG    | app.services.proxy_service:get_proxies:500 - 获取代理列表: offset=0, limit=20
2025-07-28 10:36:56.649 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-28 10:36:56.680 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-28 10:36:56.685 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-28 10:36:56.696 | INFO     | app.controllers.invite_controller:get_invite_tasks:38 - 获取邀请任务列表, status=None
2025-07-28 10:36:56.696 | INFO     | core.proxy.proxy_task_manager:task_wrapper:199 - 任务 refresh_proxies_0_20 完成
2025-07-28 10:36:56.696 | INFO     | app.services.invite_service:get_invite_tasks:121 - 获取邀请任务列表, status=None
2025-07-28 10:36:56.697 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-28 10:36:56.698 | INFO     | app.services.invite_service:task_initialize:39 - 初始化邀请服务，重置运行中的任务为暂停状态
2025-07-28 10:36:56.698 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-28 10:36:56.702 | INFO     | utils.vip_checker:start_checking:37 - 开始VIP状态检查
2025-07-28 10:36:56.702 | DEBUG    | utils.vip_checker:_check_vip_status:61 - 正在检查VIP状态...
2025-07-28 10:36:56.702 | INFO     | core.auth.api_service:verify_vip:425 - 会员验证接口调用
2025-07-28 10:36:56.899 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-28 10:36:56.903 | DEBUG    | ui.views.monitor_view:_refresh_task_list:175 - 刷新任务列表，共0个任务
2025-07-28 10:36:56.904 | INFO     | ui.views.monitor_view:_refresh_task_list:189 - 没有找到监控任务
2025-07-28 10:36:56.904 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-28 10:36:56.905 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-28 10:36:56.906 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-28 10:36:56.907 | INFO     | app.services.invite_service:task_initialize:48 - 成功重置 0 个任务状态
2025-07-28 10:36:56.907 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-28 10:36:56.912 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-28 10:36:56.914 | INFO     | app.services.account_service:get_all_groups:99 - 获取所有账户分组成功, 共 2 个
2025-07-28 10:36:56.914 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-28 10:36:56.915 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-28 10:36:56.918 | DEBUG    | ui.views.account_view:_on_groups_loaded:519 - 分组加载完成: 2个分组
2025-07-28 10:36:56.932 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-28 10:36:56.988 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-28 10:36:56.991 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-28 10:36:56.994 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-07-28 10:36:57.006 | INFO     | app.controllers.telegram_monitor_controller:_load_total_users_count_internal:191 - 成功加载总体统计数据: 总用户数 23, 今日采集 0, 日均采集 23, 运行天数 1
2025-07-28 10:36:57.007 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:156 - 成功加载 0 个任务
2025-07-28 10:36:57.007 | DEBUG    | ui.views.monitor_view:_on_loading_finished:625 - 加载完成
2025-07-28 10:36:57.017 | INFO     | app.services.account_service:get_all_accounts:333 - 获取所有账户成功, 共 2 个
2025-07-28 10:36:57.017 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-28 10:36:57.019 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-28 10:36:57.020 | DEBUG    | ui.views.account_view:_on_accounts_loaded:652 - 账户加载完成: 2个账户
2025-07-28 10:36:57.042 | DEBUG    | ui.views.account_view:_update_group_counts:188 - 分组计数已更新: {2: 2}
2025-07-28 10:36:57.045 | INFO     | ui.views.account_view:_auto_login_accounts:707 - 开始调用控制器进行 2 个账户的自动登录。
2025-07-28 10:36:57.046 | INFO     | app.services.account_service:batch_auto_login:1411 - 服务层：开始批量自动登录 2 个账户, 最大并发: 5
2025-07-28 10:36:57.046 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-28 10:36:57.052 | INFO     | core.auth.api_service:verify_vip:438 - 会员验证成功
2025-07-28 10:36:57.053 | INFO     | utils.vip_checker:_check_vip_status:76 - VIP状态有效
2025-07-28 10:36:57.063 | INFO     | app.services.account_service:batch_auto_login:1451 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-07-28 10:36:57.063 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-28 10:36:57.065 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-28 10:36:57.073 | INFO     | app.services.account_service:batch_auto_login:1451 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-07-28 10:36:57.073 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-28 10:36:57.076 | INFO     | app.services.account_service:batch_auto_login:1521 - 服务层：准备调用核心层处理 2 个账户的自动登录。
2025-07-28 10:36:57.076 | INFO     | core.telegram.client_manager:batch_auto_login:1230 - 开始批量自动登录, 共 2 个账户, 最大并发 5
2025-07-28 10:36:57.076 | INFO     | app.services.account_service:batch_auto_login:1531 - 服务层：设置核心层任务超时为 120 秒。
2025-07-28 10:36:57.077 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:271 - 创建客户端: H:\PyProject\TeleTest-bak\APPDATA\sessions\+***********, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-28 10:36:57.078 | DEBUG    | core.telegram.client_manager:get_proxy:96 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-28 10:36:57.079 | DEBUG    | core.telegram.client_manager:get_proxy:143 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-07-28 10:36:57.079 | INFO     | app.services.account_service:get_all_accounts:333 - 获取所有账户成功, 共 2 个
2025-07-28 10:36:57.079 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-28 10:36:57.083 | DEBUG    | ui.views.account_view:_update_group_counts:188 - 分组计数已更新: {2: 2}
2025-07-28 10:36:57.084 | DEBUG    | ui.views.account_view:_init_cache:155 - 账户缓存初始化完成，共 2 个账户
2025-07-28 10:36:57.084 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:271 - 创建客户端: H:\PyProject\TeleTest-bak\APPDATA\sessions\+***********, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-28 10:36:57.086 | DEBUG    | core.telegram.client_manager:get_proxy:96 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-28 10:36:57.088 | DEBUG    | ui.views.account_view:_on_accounts_loaded:652 - 账户加载完成: 2个账户
2025-07-28 10:36:57.089 | DEBUG    | core.telegram.client_manager:get_proxy:143 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-07-28 10:36:57.118 | DEBUG    | ui.views.account_view:_update_group_counts:188 - 分组计数已更新: {2: 2}
2025-07-28 10:36:57.141 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-28 10:36:57.214 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-28 10:36:57.223 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-07-28 10:36:57.226 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-07-28 10:36:57.228 | INFO     | ui.views.proxy_view:_load_initial_data:1044 - 初始数据加载完成，共 1 个代理
2025-07-28 10:36:57.267 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-28 10:37:29.653 | INFO     | core.telegram.client_manager:_connect_client:219 - 连接成功!
2025-07-28 10:37:35.468 | INFO     | core.telegram.client_manager:_verify_client_authorization:1205 - 账户已授权: +***********
2025-07-28 10:37:49.221 | INFO     | ui.main_window:closeEvent:466 - MainWindow: 接收到关闭事件
2025-07-28 10:37:49.232 | INFO     | ui.main_window:_cleanup_before_quit:308 - MainWindow: 执行清理资源...
2025-07-28 10:37:49.232 | INFO     | core.telegram.client_worker:stop:108 - 正在停止Telegram客户端工作线程
2025-07-28 10:37:49.235 | INFO     | core.telegram.client_worker:_graceful_shutdown:456 - 等待 1 个耗时任务完成...
2025-07-28 10:38:20.537 | INFO     | core.telegram.client_manager:_connect_client:219 - 连接成功!
2025-07-28 10:38:21.826 | INFO     | core.telegram.client_manager:_verify_client_authorization:1205 - 账户已授权: +***********
2025-07-28 10:38:23.836 | INFO     | core.telegram.client_manager:batch_auto_login:1379 - 批量登录完成: 总计 2, 成功 2, 失败 0
2025-07-28 10:38:23.839 | INFO     | core.telegram.client_manager:cleanup_async:1150 - 开始清理资源并断开所有连接
2025-07-28 10:38:23.840 | INFO     | core.telegram.client_manager:disconnect_all_clients:735 - 正在断开所有客户端连接
2025-07-28 10:38:23.840 | INFO     | core.telegram.client_manager:_safe_disconnect:756 - 正在断开客户端连接: +***********
2025-07-28 10:38:23.840 | INFO     | core.telegram.client_manager:_safe_disconnect:756 - 正在断开客户端连接: +***********
2025-07-28 10:38:23.849 | INFO     | core.telegram.client_manager:_safe_disconnect:760 - 断开客户端连接成功: +***********
2025-07-28 10:38:23.850 | INFO     | core.telegram.client_manager:_safe_disconnect:760 - 断开客户端连接成功: +***********
2025-07-28 10:38:23.850 | INFO     | core.telegram.client_manager:cleanup_async:1169 - 资源清理完成
2025-07-28 10:38:24.350 | INFO     | core.telegram.client_manager:cleanup_async:1150 - 开始清理资源并断开所有连接
2025-07-28 10:38:24.350 | INFO     | core.telegram.client_manager:disconnect_all_clients:735 - 正在断开所有客户端连接
2025-07-28 10:38:24.351 | INFO     | core.telegram.client_manager:cleanup_async:1169 - 资源清理完成
2025-07-28 10:38:24.864 | INFO     | core.telegram.client_worker:run:97 - Telegram客户端工作线程已退出
2025-07-28 10:38:24.865 | INFO     | core.telegram.client_worker:stop:122 - Telegram客户端工作线程已停止
2025-07-28 10:38:24.865 | INFO     | ui.main_window:_cleanup_before_quit:320 - TelegramClientWorker 已停止。
2025-07-28 10:38:24.866 | INFO     | ui.main_window:_cleanup_before_quit:324 - MainWindow 清理完成
2025-07-28 10:38:24.884 | INFO     | __main__:main:109 - 应用程序已正常退出
2025-07-28 10:45:08.191 | INFO     | utils.logger:init_logger:98 - 日志系统初始化完成，默认级别: DEBUG，日志目录: H:\PyProject\TeleTest-bak\APPDATA\logs
2025-07-28 10:45:09.455 | INFO     | data.database:init_database:31 - 初始化数据库连接: H:\PyProject\TeleTest-bak\APPDATA\database\telegram_manager.db
2025-07-28 10:45:09.473 | DEBUG    | data.database:init_database:58 - 创建数据库表
2025-07-28 10:45:09.486 | INFO     | data.database:init_database:61 - 数据库初始化完成
2025-07-28 10:45:10.572 | DEBUG    | app.controllers.auth_controller:initialize_software:43 - 控制器: 初始化软件 app_id=1000, version=1.3.7
2025-07-28 10:45:10.572 | DEBUG    | core.auth.api_service:init_software:55 - 发送初始化请求: https://server.xile188.com/api/user/1000/windowns10/1.3.7/ini, params={}
2025-07-28 10:45:11.018 | DEBUG    | core.auth.api_service:init_software:66 - 初始化数据解密成功: {'new_content': None, 'new_url': None, 'notice': {'id': '2', 'content': '公告内容测试3', 'time': '**********'}}
2025-07-28 10:45:11.026 | INFO     | app.controllers.auth_controller:login:61 - 控制器: 用户登录 account=demo01
2025-07-28 10:45:13.983 | INFO     | core.auth.api_service:login:127 - 用户登录: account=demo01
2025-07-28 10:45:14.182 | INFO     | core.auth.api_service:get_user_info:347 - 获取用户信息
2025-07-28 10:45:14.401 | INFO     | core.auth.api_service:get_user_info:375 - 获取用户信息成功
2025-07-28 10:45:14.408 | INFO     | __main__:on_auth_completed:91 - 用户登录成功，正在启动主窗口...
2025-07-28 10:45:14.433 | INFO     | core.telegram.client_worker:__init__:61 - Telegram客户端工作线程初始化
2025-07-28 10:45:14.434 | INFO     | utils.vip_checker:initialize:25 - VIP检查器已初始化，等待登录后开始检查
2025-07-28 10:45:14.435 | INFO     | ui.main_window:_initialize_core_components:113 - MainWindow: 初始化核心组件...
2025-07-28 10:45:14.435 | INFO     | core.telegram.client_worker:run:65 - Telegram客户端工作线程开始运行
2025-07-28 10:45:14.436 | INFO     | app.services.account_service:__init__:45 - 账户服务初始化
2025-07-28 10:45:14.436 | INFO     | core.proxy.proxy_task_manager:run:284 - 代理任务管理器线程启动
2025-07-28 10:45:14.437 | INFO     | app.controllers.account_controller:__init__:83 - 账户控制器初始化
2025-07-28 10:45:14.437 | INFO     | core.telegram.client_manager:__init__:66 - Telegram客户端管理器初始化
2025-07-28 10:45:14.438 | INFO     | app.services.monitor_service:__init__:37 - 初始化监控任务服务
2025-07-28 10:45:14.438 | INFO     | core.telegram.user_manager:__init__:30 - Telegram用户管理器初始化
2025-07-28 10:45:14.438 | INFO     | core.monitor.filter:__init__:26 - 初始化消息过滤器
2025-07-28 10:45:14.439 | INFO     | core.telegram.group_manager:__init__:36 - Telegram群组管理器初始化
2025-07-28 10:45:14.439 | INFO     | core.monitor.extractor:__init__:24 - 初始化关键词提取器
2025-07-28 10:45:14.439 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-07-28 10:45:14.439 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-07-28 10:45:14.440 | INFO     | core.telegram.monitor_manager:__init__:26 - Telegram监控管理器初始化
2025-07-28 10:45:14.440 | INFO     | core.monitor.notifier:__init__:90 - 初始化通知管理器
2025-07-28 10:45:14.440 | INFO     | core.telegram.telegram_service:__init__:39 - Telegram服务初始化完成
2025-07-28 10:45:14.440 | INFO     | app.controllers.telegram_monitor_controller:__init__:64 - 初始化 Telegram 监控控制器
2025-07-28 10:45:14.441 | DEBUG    | app.controllers.telegram_monitor_controller:__init__:82 - 控制器初始化完成
2025-07-28 10:45:14.578 | INFO     | app.controllers.convert_controller:set_account_controller:35 - 账户控制器已设置
2025-07-28 10:45:14.669 | INFO     | ui.views.monitor_view:__init__:48 - 初始化监控视图
2025-07-28 10:45:14.670 | DEBUG    | ui.views.monitor_view:_connect_signals:80 - 连接控制器信号
2025-07-28 10:45:14.841 | INFO     | ui.views.proxy_view:__init__:96 - 初始化视图，加载第一页代理数据
2025-07-28 10:45:14.894 | INFO     | ui.main_window:_setup_log_managers:457 - 已为 9 个视图创建日志管理器
2025-07-28 10:45:15.102 | INFO     | __main__:on_auth_completed:95 - 主窗口已启动
2025-07-28 10:45:15.154 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:88 - 尝试连接监听日志信号...
2025-07-28 10:45:15.155 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:90 - 监听日志信号连接成功
2025-07-28 10:45:15.163 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-28 10:45:15.167 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-28 10:45:15.172 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:145 - 加载监控任务列表 (搜索关键词: '')
2025-07-28 10:45:15.172 | DEBUG    | ui.views.monitor_view:_on_loading_started:620 - 加载中: 正在加载任务列表...
2025-07-28 10:45:15.173 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-28 10:45:15.179 | INFO     | app.services.proxy_service:refresh_proxies:712 - 刷新代理列表: offset=0, limit=20
2025-07-28 10:45:15.179 | INFO     | core.proxy.proxy_task_manager:submit_task:172 - 提交耗时任务到子线程: refresh_proxies_0_20
2025-07-28 10:45:15.180 | DEBUG    | app.services.proxy_service:get_proxies:500 - 获取代理列表: offset=0, limit=20
2025-07-28 10:45:15.180 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-28 10:45:15.180 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-28 10:45:15.184 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-28 10:45:15.209 | INFO     | app.controllers.invite_controller:get_invite_tasks:38 - 获取邀请任务列表, status=None
2025-07-28 10:45:15.209 | INFO     | app.services.invite_service:get_invite_tasks:121 - 获取邀请任务列表, status=None
2025-07-28 10:45:15.211 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-28 10:45:15.212 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-28 10:45:15.212 | INFO     | app.services.invite_service:task_initialize:39 - 初始化邀请服务，重置运行中的任务为暂停状态
2025-07-28 10:45:15.213 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-28 10:45:15.213 | INFO     | core.proxy.proxy_task_manager:task_wrapper:199 - 任务 refresh_proxies_0_20 完成
2025-07-28 10:45:15.215 | INFO     | utils.vip_checker:start_checking:37 - 开始VIP状态检查
2025-07-28 10:45:15.215 | DEBUG    | utils.vip_checker:_check_vip_status:61 - 正在检查VIP状态...
2025-07-28 10:45:15.216 | INFO     | core.auth.api_service:verify_vip:425 - 会员验证接口调用
2025-07-28 10:45:15.423 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-28 10:45:15.424 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-28 10:45:15.430 | INFO     | app.services.invite_service:task_initialize:48 - 成功重置 0 个任务状态
2025-07-28 10:45:15.431 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-28 10:45:15.432 | DEBUG    | ui.views.monitor_view:_refresh_task_list:175 - 刷新任务列表，共0个任务
2025-07-28 10:45:15.433 | INFO     | ui.views.monitor_view:_refresh_task_list:189 - 没有找到监控任务
2025-07-28 10:45:15.433 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-28 10:45:15.438 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-28 10:45:15.442 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-28 10:45:15.443 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-28 10:45:15.444 | INFO     | app.services.account_service:get_all_groups:99 - 获取所有账户分组成功, 共 2 个
2025-07-28 10:45:15.445 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-28 10:45:15.512 | DEBUG    | ui.views.account_view:_on_groups_loaded:519 - 分组加载完成: 2个分组
2025-07-28 10:45:15.523 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-28 10:45:15.534 | ERROR    | data.repositories.account_repo:get_all_accounts:353 - 获取所有账户失败: (sqlite3.OperationalError) no such column: accounts.login_status
[SQL: SELECT accounts.id, accounts.phone, accounts.session_file, accounts.first_name, accounts.last_name, accounts.username, accounts.bio, accounts.profile_photo, accounts.is_active, accounts.is_connected, accounts.login_status, accounts.has_2fa, accounts.last_connected, accounts.last_active, accounts.created_at, accounts.updated_at, accounts.proxy_id, accounts.proxy_type, accounts.group_id 
FROM accounts]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-28 10:45:15.535 | INFO     | app.services.account_service:get_all_accounts:334 - 获取所有账户成功, 共 0 个
2025-07-28 10:45:15.535 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-28 10:45:15.537 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-28 10:45:15.537 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-28 10:45:15.538 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-28 10:45:15.539 | DEBUG    | ui.views.account_view:_on_accounts_loaded:652 - 账户加载完成: 0个账户
2025-07-28 10:45:15.539 | DEBUG    | ui.views.account_view:_update_group_counts:188 - 分组计数已更新: {}
2025-07-28 10:45:15.539 | INFO     | ui.views.account_view:_auto_login_accounts:672 - 自动登录条件不满足：无控制器或无账户。
2025-07-28 10:45:15.546 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-07-28 10:45:15.551 | INFO     | app.controllers.telegram_monitor_controller:_load_total_users_count_internal:191 - 成功加载总体统计数据: 总用户数 23, 今日采集 0, 日均采集 23, 运行天数 1
2025-07-28 10:45:15.552 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:156 - 成功加载 0 个任务
2025-07-28 10:45:15.552 | DEBUG    | ui.views.monitor_view:_on_loading_finished:625 - 加载完成
2025-07-28 10:45:15.560 | ERROR    | data.repositories.account_repo:get_all_accounts:353 - 获取所有账户失败: (sqlite3.OperationalError) no such column: accounts.login_status
[SQL: SELECT accounts.id, accounts.phone, accounts.session_file, accounts.first_name, accounts.last_name, accounts.username, accounts.bio, accounts.profile_photo, accounts.is_active, accounts.is_connected, accounts.login_status, accounts.has_2fa, accounts.last_connected, accounts.last_active, accounts.created_at, accounts.updated_at, accounts.proxy_id, accounts.proxy_type, accounts.group_id 
FROM accounts]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-28 10:45:15.562 | INFO     | app.services.account_service:get_all_accounts:334 - 获取所有账户成功, 共 0 个
2025-07-28 10:45:15.563 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-28 10:45:15.565 | DEBUG    | ui.views.account_view:_update_group_counts:188 - 分组计数已更新: {}
2025-07-28 10:45:15.566 | DEBUG    | ui.views.account_view:_init_cache:155 - 账户缓存初始化完成，共 0 个账户
2025-07-28 10:45:15.572 | DEBUG    | ui.views.account_view:_on_accounts_loaded:652 - 账户加载完成: 0个账户
2025-07-28 10:45:15.573 | DEBUG    | ui.views.account_view:_update_group_counts:188 - 分组计数已更新: {}
2025-07-28 10:45:15.573 | INFO     | ui.views.account_view:_auto_login_accounts:672 - 自动登录条件不满足：无控制器或无账户。
2025-07-28 10:45:15.591 | INFO     | core.auth.api_service:verify_vip:438 - 会员验证成功
2025-07-28 10:45:15.591 | INFO     | utils.vip_checker:_check_vip_status:76 - VIP状态有效
2025-07-28 10:45:15.617 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-28 10:45:15.704 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-28 10:45:15.739 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-28 10:45:15.759 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-07-28 10:45:15.761 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-07-28 10:45:15.765 | INFO     | ui.views.proxy_view:_load_initial_data:1044 - 初始数据加载完成，共 1 个代理
2025-07-28 10:46:14.395 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-28 10:47:14.394 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-28 10:47:47.309 | INFO     | utils.logger:init_logger:98 - 日志系统初始化完成，默认级别: DEBUG，日志目录: H:\PyProject\TeleTest-bak\APPDATA\logs
2025-07-28 10:47:48.499 | INFO     | data.database:init_database:31 - 初始化数据库连接: H:\PyProject\TeleTest-bak\APPDATA\database\telegram_manager.db
2025-07-28 10:47:48.514 | DEBUG    | data.database:init_database:58 - 创建数据库表
2025-07-28 10:47:48.528 | INFO     | data.database:init_database:61 - 数据库初始化完成
2025-07-28 10:47:49.327 | DEBUG    | app.controllers.auth_controller:initialize_software:43 - 控制器: 初始化软件 app_id=1000, version=1.3.7
2025-07-28 10:47:49.328 | DEBUG    | core.auth.api_service:init_software:55 - 发送初始化请求: https://server.xile188.com/api/user/1000/windowns10/1.3.7/ini, params={}
2025-07-28 10:47:49.779 | DEBUG    | core.auth.api_service:init_software:66 - 初始化数据解密成功: {'new_content': None, 'new_url': None, 'notice': {'id': '2', 'content': '公告内容测试3', 'time': '**********'}}
2025-07-28 10:47:49.787 | INFO     | app.controllers.auth_controller:login:61 - 控制器: 用户登录 account=demo01
2025-07-28 10:47:52.768 | INFO     | core.auth.api_service:login:127 - 用户登录: account=demo01
2025-07-28 10:47:53.028 | INFO     | core.auth.api_service:get_user_info:347 - 获取用户信息
2025-07-28 10:47:53.232 | INFO     | core.auth.api_service:get_user_info:375 - 获取用户信息成功
2025-07-28 10:47:53.240 | INFO     | __main__:on_auth_completed:91 - 用户登录成功，正在启动主窗口...
2025-07-28 10:47:53.265 | INFO     | core.telegram.client_worker:__init__:61 - Telegram客户端工作线程初始化
2025-07-28 10:47:53.265 | INFO     | utils.vip_checker:initialize:25 - VIP检查器已初始化，等待登录后开始检查
2025-07-28 10:47:53.265 | INFO     | ui.main_window:_initialize_core_components:113 - MainWindow: 初始化核心组件...
2025-07-28 10:47:53.266 | INFO     | core.telegram.client_worker:run:65 - Telegram客户端工作线程开始运行
2025-07-28 10:47:53.267 | INFO     | app.services.account_service:__init__:45 - 账户服务初始化
2025-07-28 10:47:53.267 | INFO     | core.proxy.proxy_task_manager:run:284 - 代理任务管理器线程启动
2025-07-28 10:47:53.267 | INFO     | app.controllers.account_controller:__init__:83 - 账户控制器初始化
2025-07-28 10:47:53.268 | INFO     | app.services.monitor_service:__init__:37 - 初始化监控任务服务
2025-07-28 10:47:53.268 | INFO     | core.telegram.client_manager:__init__:66 - Telegram客户端管理器初始化
2025-07-28 10:47:53.269 | INFO     | core.monitor.filter:__init__:26 - 初始化消息过滤器
2025-07-28 10:47:53.269 | INFO     | core.telegram.user_manager:__init__:30 - Telegram用户管理器初始化
2025-07-28 10:47:53.269 | INFO     | core.monitor.extractor:__init__:24 - 初始化关键词提取器
2025-07-28 10:47:53.270 | INFO     | core.telegram.group_manager:__init__:36 - Telegram群组管理器初始化
2025-07-28 10:47:53.270 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-07-28 10:47:53.270 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-07-28 10:47:53.270 | INFO     | core.monitor.notifier:__init__:90 - 初始化通知管理器
2025-07-28 10:47:53.271 | INFO     | core.telegram.monitor_manager:__init__:26 - Telegram监控管理器初始化
2025-07-28 10:47:53.271 | INFO     | app.controllers.telegram_monitor_controller:__init__:64 - 初始化 Telegram 监控控制器
2025-07-28 10:47:53.271 | INFO     | core.telegram.telegram_service:__init__:39 - Telegram服务初始化完成
2025-07-28 10:47:53.272 | DEBUG    | app.controllers.telegram_monitor_controller:__init__:82 - 控制器初始化完成
2025-07-28 10:47:53.410 | INFO     | app.controllers.convert_controller:set_account_controller:35 - 账户控制器已设置
2025-07-28 10:47:53.500 | INFO     | ui.views.monitor_view:__init__:48 - 初始化监控视图
2025-07-28 10:47:53.500 | DEBUG    | ui.views.monitor_view:_connect_signals:80 - 连接控制器信号
2025-07-28 10:47:53.674 | INFO     | ui.views.proxy_view:__init__:96 - 初始化视图，加载第一页代理数据
2025-07-28 10:47:53.728 | INFO     | ui.main_window:_setup_log_managers:457 - 已为 9 个视图创建日志管理器
2025-07-28 10:47:53.937 | INFO     | __main__:on_auth_completed:95 - 主窗口已启动
2025-07-28 10:47:53.992 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:88 - 尝试连接监听日志信号...
2025-07-28 10:47:53.992 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:90 - 监听日志信号连接成功
2025-07-28 10:47:54.000 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-28 10:47:54.005 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-28 10:47:54.009 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:145 - 加载监控任务列表 (搜索关键词: '')
2025-07-28 10:47:54.010 | DEBUG    | ui.views.monitor_view:_on_loading_started:620 - 加载中: 正在加载任务列表...
2025-07-28 10:47:54.010 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-28 10:47:54.016 | INFO     | app.services.proxy_service:refresh_proxies:712 - 刷新代理列表: offset=0, limit=20
2025-07-28 10:47:54.016 | INFO     | core.proxy.proxy_task_manager:submit_task:172 - 提交耗时任务到子线程: refresh_proxies_0_20
2025-07-28 10:47:54.017 | DEBUG    | app.services.proxy_service:get_proxies:500 - 获取代理列表: offset=0, limit=20
2025-07-28 10:47:54.017 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-28 10:47:54.017 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-28 10:47:54.030 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-28 10:47:54.065 | INFO     | app.controllers.invite_controller:get_invite_tasks:38 - 获取邀请任务列表, status=None
2025-07-28 10:47:54.068 | INFO     | app.services.invite_service:get_invite_tasks:121 - 获取邀请任务列表, status=None
2025-07-28 10:47:54.068 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-28 10:47:54.069 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-28 10:47:54.070 | INFO     | app.services.invite_service:task_initialize:39 - 初始化邀请服务，重置运行中的任务为暂停状态
2025-07-28 10:47:54.070 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-28 10:47:54.071 | INFO     | core.proxy.proxy_task_manager:task_wrapper:199 - 任务 refresh_proxies_0_20 完成
2025-07-28 10:47:54.074 | INFO     | utils.vip_checker:start_checking:37 - 开始VIP状态检查
2025-07-28 10:47:54.076 | DEBUG    | utils.vip_checker:_check_vip_status:61 - 正在检查VIP状态...
2025-07-28 10:47:54.077 | INFO     | core.auth.api_service:verify_vip:425 - 会员验证接口调用
2025-07-28 10:47:54.301 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-28 10:47:54.302 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-28 10:47:54.307 | INFO     | app.services.invite_service:task_initialize:48 - 成功重置 0 个任务状态
2025-07-28 10:47:54.307 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-28 10:47:54.307 | DEBUG    | ui.views.monitor_view:_refresh_task_list:175 - 刷新任务列表，共0个任务
2025-07-28 10:47:54.308 | INFO     | ui.views.monitor_view:_refresh_task_list:189 - 没有找到监控任务
2025-07-28 10:47:54.308 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-28 10:47:54.310 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-28 10:47:54.314 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-28 10:47:54.315 | INFO     | app.services.account_service:get_all_groups:99 - 获取所有账户分组成功, 共 2 个
2025-07-28 10:47:54.316 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-28 10:47:54.316 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-28 10:47:54.319 | DEBUG    | ui.views.account_view:_on_groups_loaded:519 - 分组加载完成: 2个分组
2025-07-28 10:47:54.335 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-28 10:47:54.397 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-28 10:47:54.398 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-28 10:47:54.403 | INFO     | app.controllers.telegram_monitor_controller:_load_total_users_count_internal:191 - 成功加载总体统计数据: 总用户数 23, 今日采集 0, 日均采集 23, 运行天数 1
2025-07-28 10:47:54.403 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:156 - 成功加载 0 个任务
2025-07-28 10:47:54.403 | DEBUG    | ui.views.monitor_view:_on_loading_finished:625 - 加载完成
2025-07-28 10:47:54.404 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-07-28 10:47:54.421 | INFO     | app.services.account_service:get_all_accounts:334 - 获取所有账户成功, 共 2 个
2025-07-28 10:47:54.422 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-28 10:47:54.427 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-28 10:47:54.428 | DEBUG    | ui.views.account_view:_on_accounts_loaded:652 - 账户加载完成: 2个账户
2025-07-28 10:47:54.450 | DEBUG    | ui.views.account_view:_update_group_counts:188 - 分组计数已更新: {2: 2}
2025-07-28 10:47:54.453 | INFO     | app.services.account_service:update_account:382 - 更新账户信息: 1
2025-07-28 10:47:54.454 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-28 10:47:54.471 | INFO     | data.repositories.account_repo:update_account:397 - 更新账户成功: 1
2025-07-28 10:47:54.484 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-28 10:47:54.484 | INFO     | app.services.account_service:update_account:382 - 更新账户信息: 2
2025-07-28 10:47:54.485 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-28 10:47:54.496 | INFO     | data.repositories.account_repo:update_account:397 - 更新账户成功: 2
2025-07-28 10:47:54.498 | INFO     | app.services.account_service:get_all_accounts:334 - 获取所有账户成功, 共 2 个
2025-07-28 10:47:54.499 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-28 10:47:54.503 | DEBUG    | ui.views.account_view:_update_group_counts:188 - 分组计数已更新: {2: 2}
2025-07-28 10:47:54.503 | DEBUG    | ui.views.account_view:_init_cache:155 - 账户缓存初始化完成，共 2 个账户
2025-07-28 10:47:54.507 | DEBUG    | ui.views.account_view:_on_accounts_loaded:652 - 账户加载完成: 2个账户
2025-07-28 10:47:54.533 | DEBUG    | ui.views.account_view:_update_group_counts:188 - 分组计数已更新: {2: 2}
2025-07-28 10:47:54.537 | INFO     | app.services.account_service:update_account:382 - 更新账户信息: 1
2025-07-28 10:47:54.538 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-28 10:47:54.544 | INFO     | core.auth.api_service:verify_vip:438 - 会员验证成功
2025-07-28 10:47:54.544 | INFO     | utils.vip_checker:_check_vip_status:76 - VIP状态有效
2025-07-28 10:47:54.553 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-28 10:47:54.580 | INFO     | ui.views.account_view:_set_accounts_login_status:736 - 已将 2 个账户状态设置为: logging_in
2025-07-28 10:47:54.580 | INFO     | ui.views.account_view:_auto_login_accounts:710 - 开始调用控制器进行 2 个账户的自动登录。
2025-07-28 10:47:54.581 | INFO     | app.services.account_service:batch_auto_login:1413 - 服务层：开始批量自动登录 2 个账户, 最大并发: 5
2025-07-28 10:47:54.581 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-28 10:47:54.587 | INFO     | data.repositories.account_repo:update_account:397 - 更新账户成功: 1
2025-07-28 10:47:54.591 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-28 10:47:54.593 | INFO     | app.services.account_service:update_account:382 - 更新账户信息: 2
2025-07-28 10:47:54.593 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-28 10:47:54.596 | INFO     | app.services.account_service:batch_auto_login:1453 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-07-28 10:47:54.597 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-28 10:47:54.599 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-28 10:47:54.602 | INFO     | data.repositories.account_repo:update_account:397 - 更新账户成功: 2
2025-07-28 10:47:54.604 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-28 10:47:54.628 | INFO     | ui.views.account_view:_set_accounts_login_status:736 - 已将 2 个账户状态设置为: logging_in
2025-07-28 10:47:54.628 | INFO     | ui.views.account_view:_auto_login_accounts:710 - 开始调用控制器进行 2 个账户的自动登录。
2025-07-28 10:47:54.629 | INFO     | app.services.account_service:batch_auto_login:1413 - 服务层：开始批量自动登录 2 个账户, 最大并发: 5
2025-07-28 10:47:54.629 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-28 10:47:54.632 | INFO     | app.services.account_service:batch_auto_login:1453 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-07-28 10:47:54.633 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-28 10:47:54.635 | INFO     | app.services.account_service:batch_auto_login:1523 - 服务层：准备调用核心层处理 2 个账户的自动登录。
2025-07-28 10:47:54.636 | INFO     | app.services.account_service:batch_auto_login:1533 - 服务层：设置核心层任务超时为 120 秒。
2025-07-28 10:47:54.636 | INFO     | core.telegram.client_manager:batch_auto_login:1230 - 开始批量自动登录, 共 2 个账户, 最大并发 5
2025-07-28 10:47:54.636 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:271 - 创建客户端: H:\PyProject\TeleTest-bak\APPDATA\sessions\+***********, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-28 10:47:54.637 | INFO     | app.services.account_service:batch_auto_login:1453 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-07-28 10:47:54.637 | DEBUG    | core.telegram.client_manager:get_proxy:96 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-28 10:47:54.637 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-28 10:47:54.638 | DEBUG    | core.telegram.client_manager:get_proxy:143 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-07-28 10:47:54.639 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-28 10:47:54.643 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:271 - 创建客户端: H:\PyProject\TeleTest-bak\APPDATA\sessions\+***********, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-28 10:47:54.644 | DEBUG    | core.telegram.client_manager:get_proxy:96 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-28 10:47:54.644 | DEBUG    | core.telegram.client_manager:get_proxy:143 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-07-28 10:47:54.646 | INFO     | app.services.account_service:batch_auto_login:1453 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-07-28 10:47:54.647 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-28 10:47:54.649 | INFO     | app.services.account_service:batch_auto_login:1523 - 服务层：准备调用核心层处理 2 个账户的自动登录。
2025-07-28 10:47:54.650 | INFO     | app.services.account_service:batch_auto_login:1533 - 服务层：设置核心层任务超时为 120 秒。
2025-07-28 10:47:54.653 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-28 10:47:54.665 | INFO     | core.telegram.client_manager:batch_auto_login:1230 - 开始批量自动登录, 共 2 个账户, 最大并发 5
2025-07-28 10:47:54.666 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:271 - 创建客户端: H:\PyProject\TeleTest-bak\APPDATA\sessions\+***********, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-28 10:47:54.667 | DEBUG    | core.telegram.client_manager:get_proxy:96 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-28 10:47:54.667 | DEBUG    | core.telegram.client_manager:get_proxy:143 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-07-28 10:47:54.668 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:271 - 创建客户端: H:\PyProject\TeleTest-bak\APPDATA\sessions\+***********, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-28 10:47:54.669 | DEBUG    | core.telegram.client_manager:get_proxy:96 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-28 10:47:54.669 | DEBUG    | core.telegram.client_manager:get_proxy:143 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-07-28 10:47:54.739 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-28 10:47:54.744 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-07-28 10:47:54.746 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-07-28 10:47:54.748 | INFO     | ui.views.proxy_view:_load_initial_data:1044 - 初始数据加载完成，共 1 个代理
2025-07-28 10:47:54.785 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-28 10:48:03.193 | ERROR    | core.telegram.client_manager:_connect_client:254 - 连接时发生未知错误: database is locked
2025-07-28 10:48:03.199 | INFO     | core.telegram.client_manager:_connect_client:219 - 连接成功!
2025-07-28 10:48:08.729 | ERROR    | core.telegram.client_manager:_connect_client:254 - 连接时发生未知错误: database is locked
2025-07-28 10:48:14.266 | ERROR    | core.telegram.client_manager:_create_and_connect_client:286 - 创建并连接客户端失败: H:\PyProject\TeleTest-bak\APPDATA\sessions\+***********, 错误: database is locked
2025-07-28 10:48:19.823 | ERROR    | core.telegram.client_manager:_create_and_connect_client:286 - 创建并连接客户端失败: H:\PyProject\TeleTest-bak\APPDATA\sessions\+***********, 错误: database is locked
2025-07-28 10:48:19.830 | INFO     | core.telegram.client_manager:_connect_client:219 - 连接成功!
2025-07-28 10:48:20.677 | INFO     | core.telegram.client_manager:_verify_client_authorization:1205 - 账户已授权: +***********
2025-07-28 10:48:20.741 | INFO     | core.telegram.client_manager:_verify_client_authorization:1205 - 账户已授权: +***********
2025-07-28 10:48:21.817 | INFO     | core.telegram.client_manager:batch_auto_login:1379 - 批量登录完成: 总计 2, 成功 0, 失败 2
2025-07-28 10:48:22.643 | INFO     | app.services.account_service:batch_auto_login:1554 - 服务层：核心层登录完成: 成功 0, 失败 2 / 2
2025-07-28 10:48:22.643 | INFO     | app.services.account_service:_process_batch_login_results:2189 - 开始处理批量登录结果，共 2 个账户
2025-07-28 10:48:22.644 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-28 10:48:22.648 | INFO     | data.repositories.account_repo:update_account:397 - 更新账户成功: 1
2025-07-28 10:48:22.648 | INFO     | app.services.account_service:_process_batch_login_results:2243 - 已将账户 +*********** 标记为无法登录
2025-07-28 10:48:22.655 | INFO     | data.repositories.account_repo:update_account:397 - 更新账户成功: 2
2025-07-28 10:48:22.655 | INFO     | app.services.account_service:_process_batch_login_results:2243 - 已将账户 +*********** 标记为无法登录
2025-07-28 10:48:22.662 | INFO     | app.services.account_service:_process_batch_login_results:2251 - 批量登录结果处理完成，数据库已更新
2025-07-28 10:48:22.662 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-28 10:48:22.663 | INFO     | app.controllers.account_controller:auto_login_accounts:642 - 批量登录完成，触发账户数据刷新
2025-07-28 10:48:22.766 | INFO     | core.telegram.client_manager:batch_auto_login:1379 - 批量登录完成: 总计 2, 成功 2, 失败 0
2025-07-28 10:48:23.664 | INFO     | app.services.account_service:batch_auto_login:1554 - 服务层：核心层登录完成: 成功 2, 失败 0 / 2
2025-07-28 10:48:23.664 | INFO     | app.services.account_service:_process_batch_login_results:2189 - 开始处理批量登录结果，共 2 个账户
2025-07-28 10:48:23.665 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-28 10:48:23.669 | INFO     | data.repositories.account_repo:update_account:397 - 更新账户成功: 2
2025-07-28 10:48:23.669 | INFO     | app.services.account_service:_process_batch_login_results:2226 - 已更新账户 +*********** 的用户信息
2025-07-28 10:48:23.674 | INFO     | data.repositories.account_repo:update_account:397 - 更新账户成功: 1
2025-07-28 10:48:23.675 | INFO     | app.services.account_service:_process_batch_login_results:2226 - 已更新账户 +*********** 的用户信息
2025-07-28 10:48:23.680 | INFO     | app.services.account_service:_process_batch_login_results:2251 - 批量登录结果处理完成，数据库已更新
2025-07-28 10:48:23.681 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-28 10:48:23.681 | INFO     | app.controllers.account_controller:auto_login_accounts:642 - 批量登录完成，触发账户数据刷新
2025-07-28 10:48:53.221 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-07-28 10:49:15.343 | INFO     | ui.main_window:closeEvent:466 - MainWindow: 接收到关闭事件
2025-07-28 10:49:15.354 | INFO     | ui.main_window:_cleanup_before_quit:308 - MainWindow: 执行清理资源...
2025-07-28 10:49:15.357 | INFO     | core.telegram.client_worker:stop:108 - 正在停止Telegram客户端工作线程
2025-07-28 10:49:15.362 | INFO     | core.telegram.client_manager:cleanup_async:1150 - 开始清理资源并断开所有连接
2025-07-28 10:49:15.362 | INFO     | core.telegram.client_manager:disconnect_all_clients:735 - 正在断开所有客户端连接
2025-07-28 10:49:15.362 | INFO     | core.telegram.client_manager:_safe_disconnect:756 - 正在断开客户端连接: +***********
2025-07-28 10:49:15.363 | INFO     | core.telegram.client_manager:_safe_disconnect:756 - 正在断开客户端连接: +***********
2025-07-28 10:49:15.374 | INFO     | core.telegram.client_manager:_safe_disconnect:760 - 断开客户端连接成功: +***********
2025-07-28 10:49:15.374 | INFO     | core.telegram.client_manager:_safe_disconnect:760 - 断开客户端连接成功: +***********
2025-07-28 10:49:15.375 | INFO     | core.telegram.client_manager:cleanup_async:1169 - 资源清理完成
2025-07-28 10:49:15.872 | INFO     | core.telegram.client_manager:cleanup_async:1150 - 开始清理资源并断开所有连接
2025-07-28 10:49:15.872 | INFO     | core.telegram.client_manager:disconnect_all_clients:735 - 正在断开所有客户端连接
2025-07-28 10:49:15.873 | INFO     | core.telegram.client_manager:cleanup_async:1169 - 资源清理完成
2025-07-28 10:49:16.384 | INFO     | core.telegram.client_worker:run:97 - Telegram客户端工作线程已退出
2025-07-28 10:49:16.385 | INFO     | core.telegram.client_worker:stop:122 - Telegram客户端工作线程已停止
2025-07-28 10:49:16.385 | INFO     | ui.main_window:_cleanup_before_quit:320 - TelegramClientWorker 已停止。
2025-07-28 10:49:16.385 | INFO     | ui.main_window:_cleanup_before_quit:324 - MainWindow 清理完成
2025-07-28 10:49:16.403 | INFO     | __main__:main:109 - 应用程序已正常退出
2025-07-28 16:15:13.172 | INFO     | utils.logger:init_logger:98 - 日志系统初始化完成，默认级别: DEBUG，日志目录: H:\PyProject\TeleTest-bak\APPDATA\logs
2025-07-28 16:15:15.270 | INFO     | data.database:init_database:31 - 初始化数据库连接: H:\PyProject\TeleTest-bak\APPDATA\database\telegram_manager.db
2025-07-28 16:15:15.299 | DEBUG    | data.database:init_database:58 - 创建数据库表
2025-07-28 16:15:15.318 | INFO     | data.database:init_database:61 - 数据库初始化完成
2025-07-28 16:15:15.330 | INFO     | data.database:init_database:31 - 初始化数据库连接: H:\PyProject\TeleTest-bak\APPDATA\database\telegram_manager.db
2025-07-28 16:15:15.335 | DEBUG    | data.database:init_database:58 - 创建数据库表
2025-07-28 16:15:15.351 | INFO     | data.database:init_database:61 - 数据库初始化完成
2025-07-28 16:15:16.576 | DEBUG    | app.controllers.auth_controller:initialize_software:43 - 控制器: 初始化软件 app_id=1000, version=1.3.7
2025-07-28 16:15:16.577 | DEBUG    | core.auth.api_service:init_software:55 - 发送初始化请求: https://server.xile188.com/api/user/1000/windowns10/1.3.7/ini, params={}
2025-07-28 16:15:17.255 | DEBUG    | core.auth.api_service:init_software:66 - 初始化数据解密成功: {'new_content': None, 'new_url': None, 'notice': {'id': '2', 'content': '公告内容测试3', 'time': '**********'}}
2025-07-28 16:15:17.264 | INFO     | app.controllers.auth_controller:login:61 - 控制器: 用户登录 account=demo01
2025-07-28 16:15:20.328 | INFO     | core.auth.api_service:login:127 - 用户登录: account=demo01
2025-07-28 16:15:20.712 | INFO     | core.auth.api_service:get_user_info:347 - 获取用户信息
2025-07-28 16:15:20.938 | INFO     | core.auth.api_service:get_user_info:375 - 获取用户信息成功
2025-07-28 16:15:20.945 | INFO     | __main__:on_auth_completed:50 - 用户登录成功，正在启动主窗口...
2025-07-28 16:15:20.977 | INFO     | core.telegram.client_worker:__init__:61 - Telegram客户端工作线程初始化
2025-07-28 16:15:20.978 | INFO     | utils.vip_checker:initialize:25 - VIP检查器已初始化，等待登录后开始检查
2025-07-28 16:15:20.978 | INFO     | ui.main_window:_initialize_core_components:113 - MainWindow: 初始化核心组件...
2025-07-28 16:15:20.978 | INFO     | core.telegram.client_worker:run:65 - Telegram客户端工作线程开始运行
2025-07-28 16:15:20.979 | INFO     | app.services.account_service:__init__:45 - 账户服务初始化
2025-07-28 16:15:20.980 | INFO     | core.proxy.proxy_task_manager:run:284 - 代理任务管理器线程启动
2025-07-28 16:15:20.980 | INFO     | app.controllers.account_controller:__init__:83 - 账户控制器初始化
2025-07-28 16:15:20.981 | INFO     | core.telegram.client_manager:__init__:66 - Telegram客户端管理器初始化
2025-07-28 16:15:20.981 | INFO     | app.services.monitor_service:__init__:37 - 初始化监控任务服务
2025-07-28 16:15:20.981 | INFO     | core.telegram.user_manager:__init__:30 - Telegram用户管理器初始化
2025-07-28 16:15:20.981 | INFO     | core.monitor.filter:__init__:26 - 初始化消息过滤器
2025-07-28 16:15:20.982 | INFO     | core.telegram.group_manager:__init__:36 - Telegram群组管理器初始化
2025-07-28 16:15:20.982 | INFO     | core.monitor.extractor:__init__:24 - 初始化关键词提取器
2025-07-28 16:15:20.982 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-07-28 16:15:20.982 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-07-28 16:15:20.983 | INFO     | core.telegram.monitor_manager:__init__:26 - Telegram监控管理器初始化
2025-07-28 16:15:20.983 | INFO     | core.monitor.notifier:__init__:90 - 初始化通知管理器
2025-07-28 16:15:20.983 | INFO     | core.telegram.telegram_service:__init__:39 - Telegram服务初始化完成
2025-07-28 16:15:20.983 | INFO     | app.controllers.telegram_monitor_controller:__init__:64 - 初始化 Telegram 监控控制器
2025-07-28 16:15:20.984 | DEBUG    | app.controllers.telegram_monitor_controller:__init__:82 - 控制器初始化完成
2025-07-28 16:15:21.167 | INFO     | app.controllers.convert_controller:set_account_controller:35 - 账户控制器已设置
2025-07-28 16:15:21.257 | INFO     | ui.views.monitor_view:__init__:48 - 初始化监控视图
2025-07-28 16:15:21.258 | DEBUG    | ui.views.monitor_view:_connect_signals:80 - 连接控制器信号
2025-07-28 16:15:21.431 | INFO     | ui.views.proxy_view:__init__:96 - 初始化视图，加载第一页代理数据
2025-07-28 16:15:21.485 | INFO     | ui.main_window:_setup_log_managers:457 - 已为 9 个视图创建日志管理器
2025-07-28 16:15:21.695 | INFO     | __main__:on_auth_completed:53 - 主窗口已启动
2025-07-28 16:15:21.744 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:88 - 尝试连接监听日志信号...
2025-07-28 16:15:21.745 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:90 - 监听日志信号连接成功
2025-07-28 16:15:21.755 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-28 16:15:21.759 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-28 16:15:21.763 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:145 - 加载监控任务列表 (搜索关键词: '')
2025-07-28 16:15:21.764 | DEBUG    | ui.views.monitor_view:_on_loading_started:620 - 加载中: 正在加载任务列表...
2025-07-28 16:15:21.764 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-28 16:15:21.770 | INFO     | app.services.proxy_service:refresh_proxies:712 - 刷新代理列表: offset=0, limit=20
2025-07-28 16:15:21.771 | INFO     | core.proxy.proxy_task_manager:submit_task:172 - 提交耗时任务到子线程: refresh_proxies_0_20
2025-07-28 16:15:21.771 | DEBUG    | app.services.proxy_service:get_proxies:500 - 获取代理列表: offset=0, limit=20
2025-07-28 16:15:21.772 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-28 16:15:21.772 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-28 16:15:21.775 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-28 16:15:21.799 | INFO     | app.controllers.invite_controller:get_invite_tasks:38 - 获取邀请任务列表, status=None
2025-07-28 16:15:21.800 | INFO     | app.services.invite_service:get_invite_tasks:121 - 获取邀请任务列表, status=None
2025-07-28 16:15:21.800 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-28 16:15:21.803 | INFO     | app.services.invite_service:task_initialize:39 - 初始化邀请服务，重置运行中的任务为暂停状态
2025-07-28 16:15:21.804 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-28 16:15:21.804 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-28 16:15:21.806 | INFO     | core.proxy.proxy_task_manager:task_wrapper:199 - 任务 refresh_proxies_0_20 完成
2025-07-28 16:15:21.809 | INFO     | utils.vip_checker:start_checking:37 - 开始VIP状态检查
2025-07-28 16:15:21.809 | DEBUG    | utils.vip_checker:_check_vip_status:61 - 正在检查VIP状态...
2025-07-28 16:15:21.810 | INFO     | core.auth.api_service:verify_vip:425 - 会员验证接口调用
2025-07-28 16:15:22.092 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-28 16:15:22.093 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-28 16:15:22.099 | DEBUG    | ui.views.monitor_view:_refresh_task_list:175 - 刷新任务列表，共0个任务
2025-07-28 16:15:22.100 | INFO     | ui.views.monitor_view:_refresh_task_list:189 - 没有找到监控任务
2025-07-28 16:15:22.100 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-28 16:15:22.101 | INFO     | app.services.invite_service:task_initialize:48 - 成功重置 0 个任务状态
2025-07-28 16:15:22.101 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-28 16:15:22.103 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-28 16:15:22.109 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-28 16:15:22.111 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-28 16:15:22.112 | INFO     | app.services.account_service:get_all_groups:99 - 获取所有账户分组成功, 共 2 个
2025-07-28 16:15:22.113 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-28 16:15:22.164 | DEBUG    | ui.views.account_view:_on_groups_loaded:519 - 分组加载完成: 2个分组
2025-07-28 16:15:22.178 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-28 16:15:22.190 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-28 16:15:22.190 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-28 16:15:22.193 | INFO     | app.controllers.telegram_monitor_controller:_load_total_users_count_internal:191 - 成功加载总体统计数据: 总用户数 23, 今日采集 0, 日均采集 23, 运行天数 1
2025-07-28 16:15:22.193 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:156 - 成功加载 0 个任务
2025-07-28 16:15:22.194 | DEBUG    | ui.views.monitor_view:_on_loading_finished:625 - 加载完成
2025-07-28 16:15:22.195 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-07-28 16:15:22.216 | INFO     | app.services.account_service:get_all_accounts:333 - 获取所有账户成功, 共 2 个
2025-07-28 16:15:22.216 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-28 16:15:22.219 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-28 16:15:22.220 | DEBUG    | ui.views.account_view:_on_accounts_loaded:652 - 账户加载完成: 2个账户
2025-07-28 16:15:22.241 | DEBUG    | ui.views.account_view:_update_group_counts:188 - 分组计数已更新: {2: 2}
2025-07-28 16:15:22.245 | INFO     | ui.views.account_view:_auto_login_accounts:707 - 开始调用控制器进行 2 个账户的自动登录。
2025-07-28 16:15:22.246 | INFO     | app.services.account_service:batch_auto_login:1411 - 服务层：开始批量自动登录 2 个账户, 最大并发: 5
2025-07-28 16:15:22.246 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-28 16:15:22.261 | INFO     | app.services.account_service:batch_auto_login:1451 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-07-28 16:15:22.262 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-28 16:15:22.264 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-28 16:15:22.271 | INFO     | app.services.account_service:batch_auto_login:1451 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-07-28 16:15:22.272 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-28 16:15:22.275 | INFO     | app.services.account_service:batch_auto_login:1521 - 服务层：准备调用核心层处理 2 个账户的自动登录。
2025-07-28 16:15:22.276 | INFO     | app.services.account_service:batch_auto_login:1531 - 服务层：设置核心层任务超时为 120 秒。
2025-07-28 16:15:22.276 | INFO     | core.telegram.client_manager:batch_auto_login:1230 - 开始批量自动登录, 共 2 个账户, 最大并发 5
2025-07-28 16:15:22.278 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:271 - 创建客户端: H:\PyProject\TeleTest-bak\APPDATA\sessions\+***********, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-28 16:15:22.278 | INFO     | app.services.account_service:get_all_accounts:333 - 获取所有账户成功, 共 2 个
2025-07-28 16:15:22.279 | DEBUG    | core.telegram.client_manager:get_proxy:96 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-28 16:15:22.279 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-28 16:15:22.279 | DEBUG    | core.telegram.client_manager:get_proxy:143 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-07-28 16:15:22.282 | DEBUG    | ui.views.account_view:_update_group_counts:188 - 分组计数已更新: {2: 2}
2025-07-28 16:15:22.283 | DEBUG    | ui.views.account_view:_init_cache:155 - 账户缓存初始化完成，共 2 个账户
2025-07-28 16:15:22.289 | DEBUG    | ui.views.account_view:_on_accounts_loaded:652 - 账户加载完成: 2个账户
2025-07-28 16:15:22.290 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:271 - 创建客户端: H:\PyProject\TeleTest-bak\APPDATA\sessions\+***********, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-28 16:15:22.292 | DEBUG    | core.telegram.client_manager:get_proxy:96 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-07-28 16:15:22.294 | DEBUG    | core.telegram.client_manager:get_proxy:143 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-07-28 16:15:22.318 | DEBUG    | ui.views.account_view:_update_group_counts:188 - 分组计数已更新: {2: 2}
2025-07-28 16:15:22.342 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-28 16:15:22.416 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-28 16:15:22.520 | INFO     | core.auth.api_service:verify_vip:438 - 会员验证成功
2025-07-28 16:15:22.521 | INFO     | utils.vip_checker:_check_vip_status:76 - VIP状态有效
2025-07-28 16:15:22.548 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-07-28 16:15:22.550 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-07-28 16:15:22.552 | INFO     | ui.views.proxy_view:_load_initial_data:1044 - 初始数据加载完成，共 1 个代理
2025-07-28 16:15:22.603 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-28 16:15:25.756 | INFO     | core.telegram.client_manager:_connect_client:219 - 连接成功!
2025-07-28 16:15:26.924 | INFO     | core.telegram.client_manager:_verify_client_authorization:1205 - 账户已授权: +***********
2025-07-28 16:15:30.386 | INFO     | core.telegram.client_manager:_connect_client:219 - 连接成功!
2025-07-28 16:15:30.634 | INFO     | ui.main_window:closeEvent:466 - MainWindow: 接收到关闭事件
2025-07-28 16:15:30.650 | INFO     | ui.main_window:_cleanup_before_quit:308 - MainWindow: 执行清理资源...
2025-07-28 16:15:30.652 | INFO     | core.telegram.client_worker:stop:108 - 正在停止Telegram客户端工作线程
2025-07-28 16:15:30.654 | INFO     | core.telegram.client_worker:_graceful_shutdown:456 - 等待 1 个耗时任务完成...
2025-07-28 16:15:32.000 | INFO     | core.telegram.client_manager:_verify_client_authorization:1205 - 账户已授权: +***********
2025-07-28 16:15:34.013 | INFO     | core.telegram.client_manager:batch_auto_login:1379 - 批量登录完成: 总计 2, 成功 2, 失败 0
2025-07-28 16:15:34.019 | INFO     | core.telegram.client_manager:cleanup_async:1150 - 开始清理资源并断开所有连接
2025-07-28 16:15:34.019 | INFO     | core.telegram.client_manager:disconnect_all_clients:735 - 正在断开所有客户端连接
2025-07-28 16:15:34.020 | INFO     | core.telegram.client_manager:_safe_disconnect:756 - 正在断开客户端连接: +***********
2025-07-28 16:15:34.020 | INFO     | core.telegram.client_manager:_safe_disconnect:756 - 正在断开客户端连接: +***********
2025-07-28 16:15:34.029 | INFO     | core.telegram.client_manager:_safe_disconnect:760 - 断开客户端连接成功: +***********
2025-07-28 16:15:34.029 | INFO     | core.telegram.client_manager:_safe_disconnect:760 - 断开客户端连接成功: +***********
2025-07-28 16:15:34.030 | INFO     | core.telegram.client_manager:cleanup_async:1169 - 资源清理完成
2025-07-28 16:15:34.543 | INFO     | core.telegram.client_manager:cleanup_async:1150 - 开始清理资源并断开所有连接
2025-07-28 16:15:34.544 | INFO     | core.telegram.client_manager:disconnect_all_clients:735 - 正在断开所有客户端连接
2025-07-28 16:15:34.544 | INFO     | core.telegram.client_manager:cleanup_async:1169 - 资源清理完成
2025-07-28 16:15:35.057 | INFO     | core.telegram.client_worker:run:97 - Telegram客户端工作线程已退出
2025-07-28 16:15:35.058 | INFO     | core.telegram.client_worker:stop:122 - Telegram客户端工作线程已停止
2025-07-28 16:15:35.058 | INFO     | ui.main_window:_cleanup_before_quit:320 - TelegramClientWorker 已停止。
2025-07-28 16:15:35.059 | INFO     | ui.main_window:_cleanup_before_quit:324 - MainWindow 清理完成
2025-07-28 16:15:35.075 | INFO     | __main__:main:114 - 应用程序已正常退出
