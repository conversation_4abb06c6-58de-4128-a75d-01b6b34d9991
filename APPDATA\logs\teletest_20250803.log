2025-08-03 11:10:36.102 | INFO     | utils.logger:init_logger:98 - 日志系统初始化完成，默认级别: DEBUG，日志目录: H:\PyProject\TeleTest-bak\APPDATA\logs
2025-08-03 11:10:38.378 | INFO     | data.database:init_database:31 - 初始化数据库连接: H:\PyProject\TeleTest-bak\APPDATA\database\telegram_manager.db
2025-08-03 11:10:38.416 | DEBUG    | data.database:init_database:58 - 创建数据库表
2025-08-03 11:10:38.436 | INFO     | data.database:init_database:61 - 数据库初始化完成
2025-08-03 11:10:38.441 | INFO     | data.database:init_database:31 - 初始化数据库连接: H:\PyProject\TeleTest-bak\APPDATA\database\telegram_manager.db
2025-08-03 11:10:38.445 | DEBUG    | data.database:init_database:58 - 创建数据库表
2025-08-03 11:10:38.462 | INFO     | data.database:init_database:61 - 数据库初始化完成
2025-08-03 11:10:40.306 | DEBUG    | app.controllers.auth_controller:initialize_software:43 - 控制器: 初始化软件 app_id=1000, version=1.3.7
2025-08-03 11:10:40.306 | DEBUG    | core.auth.api_service:init_software:55 - 发送初始化请求: https://server.xile188.com/api/user/1000/windowns10/1.3.7/ini, params={}
2025-08-03 11:10:40.801 | DEBUG    | core.auth.api_service:init_software:66 - 初始化数据解密成功: {'new_content': None, 'new_url': None, 'notice': {'id': '2', 'content': '公告内容测试3', 'time': '**********'}}
2025-08-03 11:10:40.811 | INFO     | app.controllers.auth_controller:login:61 - 控制器: 用户登录 account=demo01
2025-08-03 11:10:43.858 | INFO     | core.auth.api_service:login:127 - 用户登录: account=demo01
2025-08-03 11:10:44.066 | INFO     | core.auth.api_service:get_user_info:347 - 获取用户信息
2025-08-03 11:10:44.293 | INFO     | core.auth.api_service:get_user_info:375 - 获取用户信息成功
2025-08-03 11:10:44.300 | INFO     | __main__:on_auth_completed:50 - 用户登录成功，正在启动主窗口...
2025-08-03 11:10:44.324 | INFO     | core.telegram.client_worker:__init__:61 - Telegram客户端工作线程初始化
2025-08-03 11:10:44.325 | INFO     | utils.vip_checker:initialize:25 - VIP检查器已初始化，等待登录后开始检查
2025-08-03 11:10:44.325 | INFO     | ui.main_window:_initialize_core_components:113 - MainWindow: 初始化核心组件...
2025-08-03 11:10:44.325 | INFO     | core.telegram.client_worker:run:65 - Telegram客户端工作线程开始运行
2025-08-03 11:10:44.326 | INFO     | app.services.account_service:__init__:45 - 账户服务初始化
2025-08-03 11:10:44.327 | INFO     | core.proxy.proxy_task_manager:run:284 - 代理任务管理器线程启动
2025-08-03 11:10:44.327 | INFO     | app.controllers.account_controller:__init__:83 - 账户控制器初始化
2025-08-03 11:10:44.328 | INFO     | core.telegram.client_manager:__init__:66 - Telegram客户端管理器初始化
2025-08-03 11:10:44.328 | INFO     | app.services.monitor_service:__init__:37 - 初始化监控任务服务
2025-08-03 11:10:44.329 | INFO     | core.telegram.user_manager:__init__:30 - Telegram用户管理器初始化
2025-08-03 11:10:44.329 | INFO     | core.monitor.filter:__init__:26 - 初始化消息过滤器
2025-08-03 11:10:44.329 | INFO     | core.telegram.group_manager:__init__:36 - Telegram群组管理器初始化
2025-08-03 11:10:44.330 | INFO     | core.monitor.extractor:__init__:24 - 初始化关键词提取器
2025-08-03 11:10:44.330 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-08-03 11:10:44.330 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-08-03 11:10:44.330 | INFO     | core.telegram.monitor_manager:__init__:26 - Telegram监控管理器初始化
2025-08-03 11:10:44.331 | INFO     | core.monitor.notifier:__init__:90 - 初始化通知管理器
2025-08-03 11:10:44.331 | INFO     | core.telegram.telegram_service:__init__:39 - Telegram服务初始化完成
2025-08-03 11:10:44.331 | INFO     | app.controllers.telegram_monitor_controller:__init__:64 - 初始化 Telegram 监控控制器
2025-08-03 11:10:44.332 | DEBUG    | app.controllers.telegram_monitor_controller:__init__:82 - 控制器初始化完成
2025-08-03 11:10:44.498 | INFO     | app.controllers.convert_controller:set_account_controller:35 - 账户控制器已设置
2025-08-03 11:10:44.606 | INFO     | ui.views.monitor_view:__init__:48 - 初始化监控视图
2025-08-03 11:10:44.606 | DEBUG    | ui.views.monitor_view:_connect_signals:80 - 连接控制器信号
2025-08-03 11:10:44.811 | INFO     | ui.views.proxy_view:__init__:96 - 初始化视图，加载第一页代理数据
2025-08-03 11:10:44.873 | INFO     | ui.main_window:_setup_log_managers:457 - 已为 9 个视图创建日志管理器
2025-08-03 11:10:45.099 | INFO     | __main__:on_auth_completed:53 - 主窗口已启动
2025-08-03 11:10:45.161 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:88 - 尝试连接监听日志信号...
2025-08-03 11:10:45.162 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:90 - 监听日志信号连接成功
2025-08-03 11:10:45.171 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-08-03 11:10:45.175 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-08-03 11:10:45.179 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:145 - 加载监控任务列表 (搜索关键词: '')
2025-08-03 11:10:45.180 | DEBUG    | ui.views.monitor_view:_on_loading_started:620 - 加载中: 正在加载任务列表...
2025-08-03 11:10:45.180 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-08-03 11:10:45.188 | INFO     | app.services.proxy_service:refresh_proxies:712 - 刷新代理列表: offset=0, limit=20
2025-08-03 11:10:45.188 | INFO     | core.proxy.proxy_task_manager:submit_task:172 - 提交耗时任务到子线程: refresh_proxies_0_20
2025-08-03 11:10:45.189 | DEBUG    | app.services.proxy_service:get_proxies:500 - 获取代理列表: offset=0, limit=20
2025-08-03 11:10:45.189 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-08-03 11:10:45.189 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-08-03 11:10:45.192 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-08-03 11:10:45.206 | INFO     | app.controllers.invite_controller:get_invite_tasks:38 - 获取邀请任务列表, status=None
2025-08-03 11:10:45.220 | INFO     | app.services.invite_service:get_invite_tasks:121 - 获取邀请任务列表, status=None
2025-08-03 11:10:45.221 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-08-03 11:10:45.222 | INFO     | app.services.invite_service:task_initialize:39 - 初始化邀请服务，重置运行中的任务为暂停状态
2025-08-03 11:10:45.224 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-08-03 11:10:45.226 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-08-03 11:10:45.229 | INFO     | core.proxy.proxy_task_manager:task_wrapper:199 - 任务 refresh_proxies_0_20 完成
2025-08-03 11:10:45.230 | INFO     | utils.vip_checker:start_checking:37 - 开始VIP状态检查
2025-08-03 11:10:45.231 | DEBUG    | utils.vip_checker:_check_vip_status:61 - 正在检查VIP状态...
2025-08-03 11:10:45.231 | INFO     | core.auth.api_service:verify_vip:425 - 会员验证接口调用
2025-08-03 11:10:45.365 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-08-03 11:10:45.366 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-08-03 11:10:45.370 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-08-03 11:10:45.376 | INFO     | app.services.invite_service:task_initialize:48 - 成功重置 0 个任务状态
2025-08-03 11:10:45.376 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-08-03 11:10:45.377 | DEBUG    | ui.views.monitor_view:_refresh_task_list:175 - 刷新任务列表，共0个任务
2025-08-03 11:10:45.378 | INFO     | ui.views.monitor_view:_refresh_task_list:189 - 没有找到监控任务
2025-08-03 11:10:45.378 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-08-03 11:10:45.381 | INFO     | app.services.account_service:get_all_groups:99 - 获取所有账户分组成功, 共 2 个
2025-08-03 11:10:45.381 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-08-03 11:10:45.382 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-08-03 11:10:45.386 | DEBUG    | ui.views.account_view:_on_groups_loaded:519 - 分组加载完成: 2个分组
2025-08-03 11:10:45.401 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-08-03 11:10:45.403 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-08-03 11:10:45.406 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-08-03 11:10:45.473 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-08-03 11:10:45.492 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-08-03 11:10:45.497 | INFO     | app.controllers.telegram_monitor_controller:_load_total_users_count_internal:191 - 成功加载总体统计数据: 总用户数 23, 今日采集 0, 日均采集 23, 运行天数 1
2025-08-03 11:10:45.497 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:156 - 成功加载 0 个任务
2025-08-03 11:10:45.497 | DEBUG    | ui.views.monitor_view:_on_loading_finished:625 - 加载完成
2025-08-03 11:10:45.511 | INFO     | app.services.account_service:get_all_accounts:333 - 获取所有账户成功, 共 2 个
2025-08-03 11:10:45.512 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-08-03 11:10:45.514 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-08-03 11:10:45.515 | DEBUG    | ui.views.account_view:_on_accounts_loaded:652 - 账户加载完成: 2个账户
2025-08-03 11:10:45.542 | DEBUG    | ui.views.account_view:_update_group_counts:188 - 分组计数已更新: {2: 2}
2025-08-03 11:10:45.547 | INFO     | ui.views.account_view:_auto_login_accounts:707 - 开始调用控制器进行 2 个账户的自动登录。
2025-08-03 11:10:45.547 | INFO     | app.services.account_service:batch_auto_login:1411 - 服务层：开始批量自动登录 2 个账户, 最大并发: 5
2025-08-03 11:10:45.547 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-08-03 11:10:45.555 | INFO     | app.services.account_service:batch_auto_login:1451 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-08-03 11:10:45.556 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-08-03 11:10:45.558 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-08-03 11:10:45.570 | INFO     | app.services.account_service:batch_auto_login:1451 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-08-03 11:10:45.570 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-08-03 11:10:45.573 | INFO     | app.services.account_service:batch_auto_login:1521 - 服务层：准备调用核心层处理 2 个账户的自动登录。
2025-08-03 11:10:45.573 | INFO     | app.services.account_service:batch_auto_login:1531 - 服务层：设置核心层任务超时为 120 秒。
2025-08-03 11:10:45.574 | INFO     | core.telegram.client_manager:batch_auto_login:1230 - 开始批量自动登录, 共 2 个账户, 最大并发 5
2025-08-03 11:10:45.574 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:271 - 创建客户端: H:\PyProject\TeleTest-bak\APPDATA\sessions\+***********, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-08-03 11:10:45.575 | DEBUG    | core.telegram.client_manager:get_proxy:96 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-08-03 11:10:45.575 | DEBUG    | core.telegram.client_manager:get_proxy:143 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-08-03 11:10:45.576 | INFO     | app.services.account_service:get_all_accounts:333 - 获取所有账户成功, 共 2 个
2025-08-03 11:10:45.576 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-08-03 11:10:45.580 | DEBUG    | ui.views.account_view:_update_group_counts:188 - 分组计数已更新: {2: 2}
2025-08-03 11:10:45.581 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:271 - 创建客户端: H:\PyProject\TeleTest-bak\APPDATA\sessions\+***********, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-08-03 11:10:45.581 | DEBUG    | ui.views.account_view:_init_cache:155 - 账户缓存初始化完成，共 2 个账户
2025-08-03 11:10:45.581 | DEBUG    | core.telegram.client_manager:get_proxy:96 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-08-03 11:10:45.583 | DEBUG    | core.telegram.client_manager:get_proxy:143 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-08-03 11:10:45.588 | DEBUG    | ui.views.account_view:_on_accounts_loaded:652 - 账户加载完成: 2个账户
2025-08-03 11:10:45.616 | DEBUG    | ui.views.account_view:_update_group_counts:188 - 分组计数已更新: {2: 2}
2025-08-03 11:10:45.624 | WARNING  | core.auth.api_service:verify_vip:440 - 会员验证失败: 验证失败
2025-08-03 11:10:45.624 | ERROR    | utils.vip_checker:_check_vip_status:81 - VIP验证失败: 验证失败
2025-08-03 11:10:54.655 | WARNING  | core.telegram.client_manager:_connect_client:236 - 连接失败: 网络错误 Connection to Telegram failed 2 time(s)
2025-08-03 11:10:54.735 | WARNING  | core.telegram.client_manager:_connect_client:236 - 连接失败: 网络错误 Connection to Telegram failed 2 time(s)
2025-08-03 11:11:01.579 | INFO     | ui.main_window:closeEvent:466 - MainWindow: 接收到关闭事件
2025-08-03 11:11:01.598 | INFO     | ui.main_window:_cleanup_before_quit:308 - MainWindow: 执行清理资源...
2025-08-03 11:11:01.605 | INFO     | core.telegram.client_worker:stop:108 - 正在停止Telegram客户端工作线程
2025-08-03 11:11:01.617 | INFO     | core.telegram.client_worker:_graceful_shutdown:456 - 等待 1 个耗时任务完成...
2025-08-03 11:11:04.738 | WARNING  | core.telegram.client_manager:_connect_client:236 - 连接失败: 网络错误 Connection to Telegram failed 2 time(s)
2025-08-03 11:11:04.832 | WARNING  | core.telegram.client_manager:_connect_client:236 - 连接失败: 网络错误 Connection to Telegram failed 2 time(s)
2025-08-03 11:11:14.818 | WARNING  | core.telegram.client_manager:_connect_client:236 - 连接失败: 网络错误 Connection to Telegram failed 2 time(s)
2025-08-03 11:11:14.882 | WARNING  | core.telegram.client_manager:_connect_client:236 - 连接失败: 网络错误 Connection to Telegram failed 2 time(s)
2025-08-03 11:11:16.885 | INFO     | core.telegram.client_manager:batch_auto_login:1379 - 批量登录完成: 总计 2, 成功 0, 失败 2
2025-08-03 11:11:16.891 | ERROR    | data.repositories.message_repo:get_task_target_stats:187 - 获取任务3目标统计失败: Cannot switch to a different thread
	Current:  <greenlet.greenlet object at 0x000001F5848C1AC0 (otid=0x000001F582DB3D20) current active started main>
	Expected: <greenlet.greenlet object at 0x000001F5A27DC740 (otid=0x000001F5A27B0CF0) suspended active started main>
2025-08-03 11:11:16.892 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-08-03 11:11:16.900 | ERROR    | ui.views.send_msg_view:load_tasks:50 - 加载任务列表失败: coroutine ignored GeneratorExit
