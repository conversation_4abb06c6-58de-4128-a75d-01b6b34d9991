2025-08-05 17:11:57.275 | INFO     | utils.logger:init_logger:98 - 日志系统初始化完成，默认级别: DEBUG，日志目录: H:\PyProject\TeleTest-bak\APPDATA\logs
2025-08-05 17:11:59.483 | INFO     | data.database:init_database:31 - 初始化数据库连接: H:\PyProject\TeleTest-bak\APPDATA\database\telegram_manager.db
2025-08-05 17:11:59.517 | DEBUG    | data.database:init_database:58 - 创建数据库表
2025-08-05 17:11:59.532 | INFO     | data.database:init_database:61 - 数据库初始化完成
2025-08-05 17:11:59.548 | INFO     | data.database:init_database:31 - 初始化数据库连接: H:\PyProject\TeleTest-bak\APPDATA\database\telegram_manager.db
2025-08-05 17:11:59.552 | DEBUG    | data.database:init_database:58 - 创建数据库表
2025-08-05 17:11:59.566 | INFO     | data.database:init_database:61 - 数据库初始化完成
2025-08-05 17:12:01.022 | DEBUG    | app.controllers.auth_controller:initialize_software:43 - 控制器: 初始化软件 app_id=1000, version=1.3.7
2025-08-05 17:12:01.023 | DEBUG    | core.auth.api_service:init_software:55 - 发送初始化请求: https://server.xile188.com/api/user/1000/windowns10/1.3.7/ini, params={}
2025-08-05 17:12:01.418 | DEBUG    | core.auth.api_service:init_software:66 - 初始化数据解密成功: {'new_content': None, 'new_url': None, 'notice': {'id': '2', 'content': '公告内容测试3', 'time': '**********'}}
2025-08-05 17:12:01.427 | INFO     | app.controllers.auth_controller:login:61 - 控制器: 用户登录 account=demo01
2025-08-05 17:12:04.518 | INFO     | core.auth.api_service:login:127 - 用户登录: account=demo01
2025-08-05 17:12:04.851 | INFO     | core.auth.api_service:get_user_info:347 - 获取用户信息
2025-08-05 17:12:05.086 | INFO     | core.auth.api_service:get_user_info:375 - 获取用户信息成功
2025-08-05 17:12:05.103 | INFO     | __main__:on_auth_completed:50 - 用户登录成功，正在启动主窗口...
2025-08-05 17:12:05.127 | INFO     | core.telegram.client_worker:__init__:61 - Telegram客户端工作线程初始化
2025-08-05 17:12:05.128 | INFO     | utils.vip_checker:initialize:25 - VIP检查器已初始化，等待登录后开始检查
2025-08-05 17:12:05.128 | INFO     | ui.main_window:_initialize_core_components:113 - MainWindow: 初始化核心组件...
2025-08-05 17:12:05.128 | INFO     | core.telegram.client_worker:run:65 - Telegram客户端工作线程开始运行
2025-08-05 17:12:05.129 | INFO     | app.services.account_service:__init__:45 - 账户服务初始化
2025-08-05 17:12:05.130 | INFO     | core.proxy.proxy_task_manager:run:284 - 代理任务管理器线程启动
2025-08-05 17:12:05.130 | INFO     | app.controllers.account_controller:__init__:83 - 账户控制器初始化
2025-08-05 17:12:05.131 | INFO     | app.services.monitor_service:__init__:37 - 初始化监控任务服务
2025-08-05 17:12:05.131 | INFO     | core.telegram.client_manager:__init__:66 - Telegram客户端管理器初始化
2025-08-05 17:12:05.131 | INFO     | core.monitor.filter:__init__:26 - 初始化消息过滤器
2025-08-05 17:12:05.132 | INFO     | core.telegram.user_manager:__init__:30 - Telegram用户管理器初始化
2025-08-05 17:12:05.132 | INFO     | core.monitor.extractor:__init__:24 - 初始化关键词提取器
2025-08-05 17:12:05.132 | INFO     | core.telegram.group_manager:__init__:36 - Telegram群组管理器初始化
2025-08-05 17:12:05.132 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-08-05 17:12:05.133 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-08-05 17:12:05.133 | INFO     | core.monitor.notifier:__init__:90 - 初始化通知管理器
2025-08-05 17:12:05.133 | INFO     | core.telegram.monitor_manager:__init__:26 - Telegram监控管理器初始化
2025-08-05 17:12:05.134 | INFO     | app.controllers.telegram_monitor_controller:__init__:64 - 初始化 Telegram 监控控制器
2025-08-05 17:12:05.134 | INFO     | core.telegram.telegram_service:__init__:39 - Telegram服务初始化完成
2025-08-05 17:12:05.134 | DEBUG    | app.controllers.telegram_monitor_controller:__init__:82 - 控制器初始化完成
2025-08-05 17:12:05.275 | INFO     | app.controllers.convert_controller:set_account_controller:35 - 账户控制器已设置
2025-08-05 17:12:05.368 | INFO     | ui.views.monitor_view:__init__:48 - 初始化监控视图
2025-08-05 17:12:05.368 | DEBUG    | ui.views.monitor_view:_connect_signals:80 - 连接控制器信号
2025-08-05 17:12:05.553 | INFO     | ui.views.proxy_view:__init__:96 - 初始化视图，加载第一页代理数据
2025-08-05 17:12:05.628 | INFO     | ui.main_window:_setup_log_managers:457 - 已为 9 个视图创建日志管理器
2025-08-05 17:12:05.836 | INFO     | __main__:on_auth_completed:53 - 主窗口已启动
2025-08-05 17:12:05.913 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:88 - 尝试连接监听日志信号...
2025-08-05 17:12:05.913 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:90 - 监听日志信号连接成功
2025-08-05 17:12:05.923 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-08-05 17:12:05.928 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-08-05 17:12:05.933 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:145 - 加载监控任务列表 (搜索关键词: '')
2025-08-05 17:12:05.933 | DEBUG    | ui.views.monitor_view:_on_loading_started:620 - 加载中: 正在加载任务列表...
2025-08-05 17:12:05.934 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-08-05 17:12:05.940 | INFO     | app.services.proxy_service:refresh_proxies:712 - 刷新代理列表: offset=0, limit=20
2025-08-05 17:12:05.941 | INFO     | core.proxy.proxy_task_manager:submit_task:172 - 提交耗时任务到子线程: refresh_proxies_0_20
2025-08-05 17:12:05.941 | DEBUG    | app.services.proxy_service:get_proxies:500 - 获取代理列表: offset=0, limit=20
2025-08-05 17:12:05.941 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-08-05 17:12:05.942 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-08-05 17:12:05.946 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-08-05 17:12:05.971 | INFO     | app.controllers.invite_controller:get_invite_tasks:38 - 获取邀请任务列表, status=None
2025-08-05 17:12:05.972 | INFO     | app.services.invite_service:get_invite_tasks:121 - 获取邀请任务列表, status=None
2025-08-05 17:12:05.972 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-08-05 17:12:05.975 | INFO     | app.services.invite_service:task_initialize:39 - 初始化邀请服务，重置运行中的任务为暂停状态
2025-08-05 17:12:05.975 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-08-05 17:12:05.976 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-08-05 17:12:05.978 | INFO     | core.proxy.proxy_task_manager:task_wrapper:199 - 任务 refresh_proxies_0_20 完成
2025-08-05 17:12:05.980 | INFO     | utils.vip_checker:start_checking:37 - 开始VIP状态检查
2025-08-05 17:12:05.980 | DEBUG    | utils.vip_checker:_check_vip_status:61 - 正在检查VIP状态...
2025-08-05 17:12:05.980 | INFO     | core.auth.api_service:verify_vip:425 - 会员验证接口调用
2025-08-05 17:12:06.198 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-08-05 17:12:06.198 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-08-05 17:12:06.198 | DEBUG    | ui.views.monitor_view:_refresh_task_list:175 - 刷新任务列表，共0个任务
2025-08-05 17:12:06.198 | INFO     | ui.views.monitor_view:_refresh_task_list:189 - 没有找到监控任务
2025-08-05 17:12:06.198 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-08-05 17:12:06.213 | INFO     | app.services.invite_service:task_initialize:48 - 成功重置 0 个任务状态
2025-08-05 17:12:06.213 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-08-05 17:12:06.216 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-08-05 17:12:06.220 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-08-05 17:12:06.222 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-08-05 17:12:06.258 | INFO     | app.services.account_service:get_all_groups:99 - 获取所有账户分组成功, 共 2 个
2025-08-05 17:12:06.258 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-08-05 17:12:06.283 | DEBUG    | ui.views.account_view:_on_groups_loaded:519 - 分组加载完成: 2个分组
2025-08-05 17:12:06.303 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-08-05 17:12:06.309 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-08-05 17:12:06.314 | INFO     | app.controllers.telegram_monitor_controller:_load_total_users_count_internal:191 - 成功加载总体统计数据: 总用户数 23, 今日采集 0, 日均采集 23, 运行天数 1
2025-08-05 17:12:06.314 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:156 - 成功加载 0 个任务
2025-08-05 17:12:06.315 | DEBUG    | ui.views.monitor_view:_on_loading_finished:625 - 加载完成
2025-08-05 17:12:06.318 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-08-05 17:12:06.322 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-08-05 17:12:06.343 | WARNING  | core.auth.api_service:verify_vip:440 - 会员验证失败: 验证失败
2025-08-05 17:12:06.344 | ERROR    | utils.vip_checker:_check_vip_status:81 - VIP验证失败: 验证失败
2025-08-05 17:12:06.395 | ERROR    | data.repositories.message_repo:get_task_target_stats:187 - 获取任务6目标统计失败: coroutine ignored GeneratorExit
2025-08-05 17:12:06.396 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-08-05 17:12:06.397 | ERROR    | ui.views.send_msg_view:load_tasks:50 - 加载任务列表失败: (sqlite3.OperationalError) no active connection
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-08-05 17:12:38.423 | ERROR    | data.repositories.invite_task_repo:get_invite_limit:206 - 获取账户邀请限制失败, account_phone=+***********: coroutine ignored GeneratorExit
2025-08-05 17:12:38.424 | ERROR    | app.services.account_service:_get_account_limits:300 - 获取账户 +*********** 限制信息失败: coroutine ignored GeneratorExit
2025-08-05 17:12:38.424 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-08-05 17:12:38.424 | ERROR    | app.services.account_service:get_all_accounts:336 - 获取账户列表异常: (sqlite3.OperationalError) no active connection
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-08-05 17:12:57.573 | INFO     | ui.main_window:closeEvent:466 - MainWindow: 接收到关闭事件
2025-08-05 17:12:57.573 | INFO     | ui.main_window:_cleanup_before_quit:308 - MainWindow: 执行清理资源...
2025-08-05 17:12:57.573 | INFO     | core.telegram.client_worker:stop:108 - 正在停止Telegram客户端工作线程
2025-08-05 17:12:57.594 | INFO     | core.telegram.client_manager:cleanup_async:1150 - 开始清理资源并断开所有连接
2025-08-05 17:12:57.594 | INFO     | core.telegram.client_manager:disconnect_all_clients:735 - 正在断开所有客户端连接
2025-08-05 17:12:57.595 | INFO     | core.telegram.client_manager:cleanup_async:1169 - 资源清理完成
2025-08-05 17:12:58.103 | INFO     | core.telegram.client_manager:cleanup_async:1150 - 开始清理资源并断开所有连接
2025-08-05 17:12:58.104 | INFO     | core.telegram.client_manager:disconnect_all_clients:735 - 正在断开所有客户端连接
2025-08-05 17:12:58.105 | INFO     | core.telegram.client_manager:cleanup_async:1169 - 资源清理完成
2025-08-05 17:12:58.622 | INFO     | core.telegram.client_worker:run:97 - Telegram客户端工作线程已退出
2025-08-05 17:12:58.623 | INFO     | core.telegram.client_worker:stop:122 - Telegram客户端工作线程已停止
2025-08-05 17:12:58.623 | INFO     | ui.main_window:_cleanup_before_quit:320 - TelegramClientWorker 已停止。
2025-08-05 17:12:58.624 | INFO     | ui.main_window:_cleanup_before_quit:324 - MainWindow 清理完成
2025-08-05 17:12:58.641 | INFO     | __main__:main:114 - 应用程序已正常退出
2025-08-05 17:13:17.592 | INFO     | utils.logger:init_logger:98 - 日志系统初始化完成，默认级别: DEBUG，日志目录: H:\PyProject\TeleTest-bak\APPDATA\logs
2025-08-05 17:13:18.733 | INFO     | data.database:init_database:31 - 初始化数据库连接: H:\PyProject\TeleTest-bak\APPDATA\database\telegram_manager.db
2025-08-05 17:13:18.747 | DEBUG    | data.database:init_database:58 - 创建数据库表
2025-08-05 17:13:18.762 | INFO     | data.database:init_database:61 - 数据库初始化完成
2025-08-05 17:13:18.767 | INFO     | data.database:init_database:31 - 初始化数据库连接: H:\PyProject\TeleTest-bak\APPDATA\database\telegram_manager.db
2025-08-05 17:13:18.769 | DEBUG    | data.database:init_database:58 - 创建数据库表
2025-08-05 17:13:18.779 | INFO     | data.database:init_database:61 - 数据库初始化完成
2025-08-05 17:13:19.489 | DEBUG    | app.controllers.auth_controller:initialize_software:43 - 控制器: 初始化软件 app_id=1000, version=1.3.7
2025-08-05 17:13:19.490 | DEBUG    | core.auth.api_service:init_software:55 - 发送初始化请求: https://server.xile188.com/api/user/1000/windowns10/1.3.7/ini, params={}
2025-08-05 17:13:19.722 | DEBUG    | core.auth.api_service:init_software:66 - 初始化数据解密成功: {'new_content': None, 'new_url': None, 'notice': {'id': '2', 'content': '公告内容测试3', 'time': '**********'}}
2025-08-05 17:13:19.729 | INFO     | app.controllers.auth_controller:login:61 - 控制器: 用户登录 account=demo01
2025-08-05 17:13:22.672 | INFO     | core.auth.api_service:login:127 - 用户登录: account=demo01
2025-08-05 17:13:22.918 | INFO     | core.auth.api_service:get_user_info:347 - 获取用户信息
2025-08-05 17:13:23.144 | INFO     | core.auth.api_service:get_user_info:375 - 获取用户信息成功
2025-08-05 17:13:23.151 | INFO     | __main__:on_auth_completed:50 - 用户登录成功，正在启动主窗口...
2025-08-05 17:13:23.173 | INFO     | core.telegram.client_worker:__init__:61 - Telegram客户端工作线程初始化
2025-08-05 17:13:23.173 | INFO     | utils.vip_checker:initialize:25 - VIP检查器已初始化，等待登录后开始检查
2025-08-05 17:13:23.174 | INFO     | ui.main_window:_initialize_core_components:113 - MainWindow: 初始化核心组件...
2025-08-05 17:13:23.174 | INFO     | core.telegram.client_worker:run:65 - Telegram客户端工作线程开始运行
2025-08-05 17:13:23.176 | INFO     | app.services.account_service:__init__:45 - 账户服务初始化
2025-08-05 17:13:23.176 | INFO     | core.proxy.proxy_task_manager:run:284 - 代理任务管理器线程启动
2025-08-05 17:13:23.176 | INFO     | app.controllers.account_controller:__init__:83 - 账户控制器初始化
2025-08-05 17:13:23.177 | INFO     | app.services.monitor_service:__init__:37 - 初始化监控任务服务
2025-08-05 17:13:23.177 | INFO     | core.telegram.client_manager:__init__:66 - Telegram客户端管理器初始化
2025-08-05 17:13:23.177 | INFO     | core.monitor.filter:__init__:26 - 初始化消息过滤器
2025-08-05 17:13:23.178 | INFO     | core.telegram.user_manager:__init__:30 - Telegram用户管理器初始化
2025-08-05 17:13:23.178 | INFO     | core.monitor.extractor:__init__:24 - 初始化关键词提取器
2025-08-05 17:13:23.178 | INFO     | core.telegram.group_manager:__init__:36 - Telegram群组管理器初始化
2025-08-05 17:13:23.178 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-08-05 17:13:23.178 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-08-05 17:13:23.179 | INFO     | core.monitor.notifier:__init__:90 - 初始化通知管理器
2025-08-05 17:13:23.179 | INFO     | core.telegram.monitor_manager:__init__:26 - Telegram监控管理器初始化
2025-08-05 17:13:23.179 | INFO     | app.controllers.telegram_monitor_controller:__init__:64 - 初始化 Telegram 监控控制器
2025-08-05 17:13:23.179 | INFO     | core.telegram.telegram_service:__init__:39 - Telegram服务初始化完成
2025-08-05 17:13:23.180 | DEBUG    | app.controllers.telegram_monitor_controller:__init__:82 - 控制器初始化完成
2025-08-05 17:13:23.311 | INFO     | app.controllers.convert_controller:set_account_controller:35 - 账户控制器已设置
2025-08-05 17:13:23.393 | INFO     | ui.views.monitor_view:__init__:48 - 初始化监控视图
2025-08-05 17:13:23.393 | DEBUG    | ui.views.monitor_view:_connect_signals:80 - 连接控制器信号
2025-08-05 17:13:23.554 | INFO     | ui.views.proxy_view:__init__:96 - 初始化视图，加载第一页代理数据
2025-08-05 17:13:23.604 | INFO     | ui.main_window:_setup_log_managers:457 - 已为 9 个视图创建日志管理器
2025-08-05 17:13:23.798 | INFO     | __main__:on_auth_completed:53 - 主窗口已启动
2025-08-05 17:13:23.844 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:88 - 尝试连接监听日志信号...
2025-08-05 17:13:23.845 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:90 - 监听日志信号连接成功
2025-08-05 17:13:23.853 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-08-05 17:13:23.857 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-08-05 17:13:23.862 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:145 - 加载监控任务列表 (搜索关键词: '')
2025-08-05 17:13:23.862 | DEBUG    | ui.views.monitor_view:_on_loading_started:620 - 加载中: 正在加载任务列表...
2025-08-05 17:13:23.862 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-08-05 17:13:23.870 | INFO     | app.services.proxy_service:refresh_proxies:712 - 刷新代理列表: offset=0, limit=20
2025-08-05 17:13:23.870 | INFO     | core.proxy.proxy_task_manager:submit_task:172 - 提交耗时任务到子线程: refresh_proxies_0_20
2025-08-05 17:13:23.871 | DEBUG    | app.services.proxy_service:get_proxies:500 - 获取代理列表: offset=0, limit=20
2025-08-05 17:13:23.871 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-08-05 17:13:23.871 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-08-05 17:13:23.876 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-08-05 17:13:23.897 | INFO     | app.controllers.invite_controller:get_invite_tasks:38 - 获取邀请任务列表, status=None
2025-08-05 17:13:23.898 | INFO     | app.services.invite_service:get_invite_tasks:121 - 获取邀请任务列表, status=None
2025-08-05 17:13:23.901 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-08-05 17:13:23.902 | INFO     | app.services.invite_service:task_initialize:39 - 初始化邀请服务，重置运行中的任务为暂停状态
2025-08-05 17:13:23.902 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-08-05 17:13:23.902 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-08-05 17:13:23.904 | INFO     | core.proxy.proxy_task_manager:task_wrapper:199 - 任务 refresh_proxies_0_20 完成
2025-08-05 17:13:23.906 | INFO     | utils.vip_checker:start_checking:37 - 开始VIP状态检查
2025-08-05 17:13:23.906 | DEBUG    | utils.vip_checker:_check_vip_status:61 - 正在检查VIP状态...
2025-08-05 17:13:23.907 | INFO     | core.auth.api_service:verify_vip:425 - 会员验证接口调用
2025-08-05 17:13:24.103 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-08-05 17:13:24.103 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-08-05 17:13:24.114 | DEBUG    | ui.views.monitor_view:_refresh_task_list:175 - 刷新任务列表，共0个任务
2025-08-05 17:13:24.115 | INFO     | ui.views.monitor_view:_refresh_task_list:189 - 没有找到监控任务
2025-08-05 17:13:24.115 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-08-05 17:13:24.116 | INFO     | app.services.invite_service:task_initialize:48 - 成功重置 0 个任务状态
2025-08-05 17:13:24.116 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-08-05 17:13:24.117 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-08-05 17:13:24.119 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-08-05 17:13:24.122 | INFO     | app.services.account_service:get_all_groups:99 - 获取所有账户分组成功, 共 2 个
2025-08-05 17:13:24.122 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-08-05 17:13:24.122 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-08-05 17:13:24.167 | DEBUG    | ui.views.account_view:_on_groups_loaded:519 - 分组加载完成: 2个分组
2025-08-05 17:13:24.167 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-08-05 17:13:24.202 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-08-05 17:13:24.203 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-08-05 17:13:24.207 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-08-05 17:13:24.218 | INFO     | app.controllers.telegram_monitor_controller:_load_total_users_count_internal:191 - 成功加载总体统计数据: 总用户数 23, 今日采集 0, 日均采集 23, 运行天数 1
2025-08-05 17:13:24.218 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:156 - 成功加载 0 个任务
2025-08-05 17:13:24.219 | DEBUG    | ui.views.monitor_view:_on_loading_finished:625 - 加载完成
2025-08-05 17:13:24.228 | INFO     | app.services.account_service:get_all_accounts:333 - 获取所有账户成功, 共 2 个
2025-08-05 17:13:24.229 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-08-05 17:13:24.231 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-08-05 17:13:24.232 | DEBUG    | ui.views.account_view:_on_accounts_loaded:652 - 账户加载完成: 2个账户
2025-08-05 17:13:24.251 | DEBUG    | ui.views.account_view:_update_group_counts:188 - 分组计数已更新: {2: 2}
2025-08-05 17:13:24.254 | INFO     | ui.views.account_view:_auto_login_accounts:707 - 开始调用控制器进行 2 个账户的自动登录。
2025-08-05 17:13:24.254 | INFO     | app.services.account_service:batch_auto_login:1557 - 服务层：开始批量自动登录 2 个账户, 最大并发: 5
2025-08-05 17:13:24.255 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-08-05 17:13:24.262 | INFO     | core.auth.api_service:verify_vip:438 - 会员验证成功
2025-08-05 17:13:24.262 | INFO     | utils.vip_checker:_check_vip_status:76 - VIP状态有效
2025-08-05 17:13:24.273 | INFO     | app.services.account_service:batch_auto_login:1597 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-08-05 17:13:24.274 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-08-05 17:13:24.276 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-08-05 17:13:24.282 | INFO     | app.services.account_service:batch_auto_login:1597 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:1080
2025-08-05 17:13:24.283 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-08-05 17:13:24.284 | INFO     | app.services.account_service:get_all_accounts:333 - 获取所有账户成功, 共 2 个
2025-08-05 17:13:24.284 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-08-05 17:13:24.287 | INFO     | app.services.account_service:batch_auto_login:1667 - 服务层：准备调用核心层处理 2 个账户的自动登录。
2025-08-05 17:13:24.288 | INFO     | core.telegram.client_manager:batch_auto_login:1230 - 开始批量自动登录, 共 2 个账户, 最大并发 5
2025-08-05 17:13:24.288 | INFO     | app.services.account_service:batch_auto_login:1677 - 服务层：设置核心层任务超时为 120 秒。
2025-08-05 17:13:24.288 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:271 - 创建客户端: H:\PyProject\TeleTest-bak\APPDATA\sessions\+***********, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-08-05 17:13:24.289 | DEBUG    | ui.views.account_view:_update_group_counts:188 - 分组计数已更新: {2: 2}
2025-08-05 17:13:24.289 | DEBUG    | core.telegram.client_manager:get_proxy:96 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-08-05 17:13:24.290 | DEBUG    | ui.views.account_view:_init_cache:155 - 账户缓存初始化完成，共 2 个账户
2025-08-05 17:13:24.290 | DEBUG    | core.telegram.client_manager:get_proxy:143 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-08-05 17:13:24.295 | DEBUG    | ui.views.account_view:_on_accounts_loaded:652 - 账户加载完成: 2个账户
2025-08-05 17:13:24.301 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:271 - 创建客户端: H:\PyProject\TeleTest-bak\APPDATA\sessions\+***********, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-08-05 17:13:24.302 | DEBUG    | core.telegram.client_manager:get_proxy:96 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 1080}
2025-08-05 17:13:24.302 | DEBUG    | core.telegram.client_manager:get_proxy:143 - get_proxy: 使用 ip_pool 代理 127.0.0.1:1080，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 1080)
2025-08-05 17:13:24.322 | DEBUG    | ui.views.account_view:_update_group_counts:188 - 分组计数已更新: {2: 2}
2025-08-05 17:13:24.347 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-08-05 17:13:24.414 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-08-05 17:13:24.429 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-08-05 17:13:24.432 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-08-05 17:13:24.434 | INFO     | ui.views.proxy_view:_load_initial_data:1044 - 初始数据加载完成，共 1 个代理
2025-08-05 17:13:24.455 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-08-05 17:13:33.406 | WARNING  | core.telegram.client_manager:_connect_client:236 - 连接失败: 网络错误 Connection to Telegram failed 2 time(s)
2025-08-05 17:13:33.407 | WARNING  | core.telegram.client_manager:_connect_client:236 - 连接失败: 网络错误 Connection to Telegram failed 2 time(s)
2025-08-05 17:13:43.500 | WARNING  | core.telegram.client_manager:_connect_client:236 - 连接失败: 网络错误 Connection to Telegram failed 2 time(s)
2025-08-05 17:13:43.500 | WARNING  | core.telegram.client_manager:_connect_client:236 - 连接失败: 网络错误 Connection to Telegram failed 2 time(s)
2025-08-05 17:13:48.357 | INFO     | app.controllers.proxy_controller:validate_proxy:163 - 开始验证代理 ID: 1
2025-08-05 17:13:48.357 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-08-05 17:13:48.410 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-08-05 17:13:48.411 | INFO     | app.services.proxy_service:validate_proxy:297 - 验证代理ID: 1
2025-08-05 17:13:48.411 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-08-05 17:13:48.414 | INFO     | core.proxy.proxy_core_service:validate_proxy:64 - 验证代理: 127.0.0.1:1080
2025-08-05 17:13:48.414 | INFO     | core.proxy.proxy_validator:validate_proxy:43 - 正在验证代理 socks5://127.0.0.1:1080
2025-08-05 17:13:50.615 | WARNING  | core.proxy.proxy_validator:validate_proxy:71 - 使用https://web.telegram.org验证代理失败: socks5://127.0.0.1:1080, 错误: [Errno 22] Couldn't connect to proxy 127.0.0.1:1080 [远程计算机拒绝网络连接。]
2025-08-05 17:13:53.656 | WARNING  | core.telegram.client_manager:_connect_client:236 - 连接失败: 网络错误 Connection to Telegram failed 2 time(s)
2025-08-05 17:13:53.657 | WARNING  | core.telegram.client_manager:_connect_client:236 - 连接失败: 网络错误 Connection to Telegram failed 2 time(s)
2025-08-05 17:13:55.658 | INFO     | core.telegram.client_manager:batch_auto_login:1379 - 批量登录完成: 总计 2, 成功 0, 失败 2
2025-08-05 17:13:56.281 | WARNING  | core.proxy.proxy_validator:validate_proxy:71 - 使用https://core.telegram.org验证代理失败: socks5://127.0.0.1:1080, 错误: 
2025-08-05 17:13:56.281 | WARNING  | core.proxy.proxy_validator:validate_proxy:75 - 代理验证失败: socks5://127.0.0.1:1080
2025-08-05 17:13:56.285 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-08-05 17:13:56.285 | INFO     | app.controllers.proxy_controller:validate_proxy:180 - 代理 ID: 1 验证结果: 无效, 响应时间: Nonems
2025-08-05 17:13:56.296 | INFO     | ui.views.proxy_view:load_proxies_by_page:525 - 加载代理页面: 1
2025-08-05 17:13:56.297 | INFO     | app.services.proxy_service:refresh_proxies:712 - 刷新代理列表: offset=0, limit=20
2025-08-05 17:13:56.297 | INFO     | core.proxy.proxy_task_manager:submit_task:172 - 提交耗时任务到子线程: refresh_proxies_0_20
2025-08-05 17:13:56.298 | DEBUG    | app.services.proxy_service:get_proxies:500 - 获取代理列表: offset=0, limit=20
2025-08-05 17:13:56.298 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-08-05 17:13:56.298 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-08-05 17:13:56.309 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-08-05 17:13:56.310 | INFO     | core.proxy.proxy_task_manager:task_wrapper:199 - 任务 refresh_proxies_0_20 完成
2025-08-05 17:13:56.327 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-08-05 17:13:56.328 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-08-05 17:13:56.340 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-08-05 17:13:56.343 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-08-05 17:13:56.346 | INFO     | ui.views.proxy_view:load_proxies_by_page:538 - 页面 1 加载完成，显示 1 条记录，共 1 条
2025-08-05 17:13:56.411 | INFO     | app.services.account_service:batch_auto_login:1698 - 服务层：核心层登录完成: 成功 0, 失败 2 / 2
2025-08-05 17:13:56.416 | INFO     | app.services.account_service:_process_batch_login_results:2333 - 开始处理批量登录结果，共 2 个账户
2025-08-05 17:13:56.417 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-08-05 17:13:56.428 | INFO     | data.repositories.account_repo:update_account:394 - 更新账户成功: 2
2025-08-05 17:13:56.428 | INFO     | app.services.account_service:_process_batch_login_results:2384 - 已将账户 +*********** 标记为无法登录
2025-08-05 17:13:56.440 | INFO     | data.repositories.account_repo:update_account:394 - 更新账户成功: 1
2025-08-05 17:13:56.441 | INFO     | app.services.account_service:_process_batch_login_results:2384 - 已将账户 +*********** 标记为无法登录
2025-08-05 17:13:56.446 | INFO     | app.services.account_service:_process_batch_login_results:2392 - 批量登录结果处理完成，数据库已更新
2025-08-05 17:13:56.446 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-08-05 17:13:56.447 | INFO     | app.controllers.account_controller:auto_login_accounts:642 - 批量登录完成，触发账户数据刷新
2025-08-05 17:14:01.063 | INFO     | ui.views.proxy_view:_on_table_double_clicked:1172 - 已复制代理配置: socks5://127.0.0.1:1080
2025-08-05 17:14:23.144 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-08-05 17:14:25.967 | INFO     | ui.views.proxy_view:_on_add_proxy:451 - 用户开始添加代理: IP类型=socks5(远程), IP文本='127.0.0.1'
2025-08-05 17:14:25.968 | DEBUG    | ui.views.proxy_view:_on_add_proxy:459 - 固定端口: 7897
2025-08-05 17:14:25.968 | DEBUG    | ui.views.proxy_view:_on_add_proxy:473 - 用户凭据: username=, password=None
2025-08-05 17:14:25.968 | INFO     | app.services.proxy_service:add_proxies_from_text:879 - 从文本添加代理: IP文本='127.0.0.1', 端口范围=7897-7897
2025-08-05 17:14:25.969 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-08-05 17:14:25.973 | WARNING  | app.services.proxy_service:add_proxies_from_text:904 - 代理IP 127.0.0.1 已存在，跳过添加
2025-08-05 17:14:25.974 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-08-05 17:14:33.228 | INFO     | app.services.proxy_service:delete_all_invalid_proxies:951 - 批量删除所有失效代理
2025-08-05 17:14:33.228 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-08-05 17:14:33.345 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-08-05 17:14:33.346 | INFO     | app.services.proxy_service:refresh_proxies:712 - 刷新代理列表: offset=0, limit=20
2025-08-05 17:14:33.346 | INFO     | core.proxy.proxy_task_manager:submit_task:172 - 提交耗时任务到子线程: refresh_proxies_0_20
2025-08-05 17:14:33.347 | DEBUG    | app.services.proxy_service:get_proxies:500 - 获取代理列表: offset=0, limit=20
2025-08-05 17:14:33.347 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-08-05 17:14:33.347 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-08-05 17:14:33.352 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-08-05 17:14:33.353 | INFO     | core.proxy.proxy_task_manager:task_wrapper:199 - 任务 refresh_proxies_0_20 完成
2025-08-05 17:14:33.500 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-08-05 17:14:33.502 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-08-05 17:14:33.506 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-08-05 17:14:33.508 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-08-05 17:14:33.516 | INFO     | ui.views.proxy_view:load_proxies_by_page:525 - 加载代理页面: 1
2025-08-05 17:14:33.516 | INFO     | app.services.proxy_service:refresh_proxies:712 - 刷新代理列表: offset=0, limit=20
2025-08-05 17:14:33.517 | INFO     | core.proxy.proxy_task_manager:submit_task:172 - 提交耗时任务到子线程: refresh_proxies_0_20
2025-08-05 17:14:33.517 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-08-05 17:14:33.517 | DEBUG    | app.services.proxy_service:get_proxies:500 - 获取代理列表: offset=0, limit=20
2025-08-05 17:14:33.518 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-08-05 17:14:33.524 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-08-05 17:14:33.526 | INFO     | core.proxy.proxy_task_manager:task_wrapper:199 - 任务 refresh_proxies_0_20 完成
2025-08-05 17:14:33.533 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-08-05 17:14:33.534 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-08-05 17:14:33.541 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-08-05 17:14:33.543 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-08-05 17:14:33.546 | INFO     | ui.views.proxy_view:load_proxies_by_page:538 - 页面 1 加载完成，显示 0 条记录，共 0 条
2025-08-05 17:14:35.852 | INFO     | ui.views.proxy_view:_on_add_proxy:451 - 用户开始添加代理: IP类型=socks5(远程), IP文本='127.0.0.1'
2025-08-05 17:14:35.852 | DEBUG    | ui.views.proxy_view:_on_add_proxy:459 - 固定端口: 7897
2025-08-05 17:14:35.852 | DEBUG    | ui.views.proxy_view:_on_add_proxy:473 - 用户凭据: username=, password=None
2025-08-05 17:14:35.853 | INFO     | app.services.proxy_service:add_proxies_from_text:879 - 从文本添加代理: IP文本='127.0.0.1', 端口范围=7897-7897
2025-08-05 17:14:35.853 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-08-05 17:14:35.866 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-08-05 17:14:35.866 | INFO     | app.services.proxy_service:refresh_proxies:712 - 刷新代理列表: offset=0, limit=20
2025-08-05 17:14:35.866 | INFO     | core.proxy.proxy_task_manager:submit_task:172 - 提交耗时任务到子线程: refresh_proxies_0_20
2025-08-05 17:14:35.867 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-08-05 17:14:35.867 | DEBUG    | app.services.proxy_service:get_proxies:500 - 获取代理列表: offset=0, limit=20
2025-08-05 17:14:35.868 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-08-05 17:14:35.873 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-08-05 17:14:35.875 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-08-05 17:14:35.875 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-08-05 17:14:35.876 | INFO     | core.proxy.proxy_task_manager:task_wrapper:199 - 任务 refresh_proxies_0_20 完成
2025-08-05 17:14:35.879 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-08-05 17:14:35.881 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-08-05 17:14:35.885 | INFO     | app.services.proxy_service:validate_proxies:756 - 开始验证指定代理，共 1 个
2025-08-05 17:14:35.886 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-08-05 17:14:35.911 | INFO     | core.proxy.proxy_core_service:validate_proxies_stream:159 - 批量验证代理(流式): 1个
2025-08-05 17:14:35.912 | INFO     | core.proxy.proxy_validator:validate_proxy:43 - 正在验证代理 socks5://127.0.0.1:7897
2025-08-05 17:14:36.952 | INFO     | core.proxy.proxy_validator:validate_proxy:68 - 代理验证成功: socks5://127.0.0.1:7897, 耗时: 1040.32ms
2025-08-05 17:14:36.964 | INFO     | core.proxy.proxy_core_service:validate_proxies_stream:224 - 批量代理验证完成，结果: 1/1 个有效
2025-08-05 17:14:36.964 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-08-05 17:14:36.964 | INFO     | app.services.proxy_service:refresh_proxies:712 - 刷新代理列表: offset=0, limit=20
2025-08-05 17:14:36.965 | INFO     | core.proxy.proxy_task_manager:submit_task:172 - 提交耗时任务到子线程: refresh_proxies_0_20
2025-08-05 17:14:36.965 | DEBUG    | app.services.proxy_service:get_proxies:500 - 获取代理列表: offset=0, limit=20
2025-08-05 17:14:36.966 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-08-05 17:14:36.966 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-08-05 17:14:36.978 | INFO     | ui.views.proxy_view:load_proxies_by_page:525 - 加载代理页面: 1
2025-08-05 17:14:36.978 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-08-05 17:14:36.978 | INFO     | app.services.proxy_service:refresh_proxies:712 - 刷新代理列表: offset=0, limit=20
2025-08-05 17:14:36.980 | INFO     | app.services.proxy_service:refresh_proxies:719 - 刷新任务已在运行，跳过
2025-08-05 17:14:36.980 | DEBUG    | app.services.proxy_service:get_proxies:500 - 获取代理列表: offset=0, limit=20
2025-08-05 17:14:36.980 | INFO     | core.proxy.proxy_task_manager:task_wrapper:199 - 任务 refresh_proxies_0_20 完成
2025-08-05 17:14:36.980 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-08-05 17:14:36.982 | INFO     | ui.views.proxy_view:load_proxies_by_page:525 - 加载代理页面: 1
2025-08-05 17:14:36.982 | INFO     | app.services.proxy_service:refresh_proxies:712 - 刷新代理列表: offset=0, limit=20
2025-08-05 17:14:36.982 | INFO     | core.proxy.proxy_task_manager:submit_task:172 - 提交耗时任务到子线程: refresh_proxies_0_20
2025-08-05 17:14:36.982 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-08-05 17:14:36.982 | DEBUG    | app.services.proxy_service:get_proxies:500 - 获取代理列表: offset=0, limit=20
2025-08-05 17:14:36.982 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-08-05 17:14:36.988 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-08-05 17:14:36.991 | INFO     | core.proxy.proxy_task_manager:task_wrapper:199 - 任务 refresh_proxies_0_20 完成
2025-08-05 17:14:37.002 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-08-05 17:14:37.003 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-08-05 17:14:37.004 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-08-05 17:14:37.006 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-08-05 17:14:37.007 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-08-05 17:14:37.012 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-08-05 17:14:37.017 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-08-05 17:14:37.021 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-08-05 17:14:37.022 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-08-05 17:14:37.025 | INFO     | ui.views.proxy_view:load_proxies_by_page:538 - 页面 1 加载完成，显示 1 条记录，共 1 条
2025-08-05 17:14:37.053 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-08-05 17:14:37.064 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-08-05 17:14:37.069 | INFO     | ui.views.proxy_view:load_proxies_by_page:525 - 加载代理页面: 1
2025-08-05 17:14:37.071 | INFO     | app.services.proxy_service:refresh_proxies:712 - 刷新代理列表: offset=0, limit=20
2025-08-05 17:14:37.071 | INFO     | core.proxy.proxy_task_manager:submit_task:172 - 提交耗时任务到子线程: refresh_proxies_0_20
2025-08-05 17:14:37.072 | DEBUG    | app.services.proxy_service:get_proxies:500 - 获取代理列表: offset=0, limit=20
2025-08-05 17:14:37.072 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-08-05 17:14:37.072 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-08-05 17:14:37.078 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-08-05 17:14:37.081 | INFO     | core.proxy.proxy_task_manager:task_wrapper:199 - 任务 refresh_proxies_0_20 完成
2025-08-05 17:14:37.088 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-08-05 17:14:37.091 | INFO     | ui.views.proxy_view:load_proxies_by_page:538 - 页面 1 加载完成，显示 1 条记录，共 1 条
2025-08-05 17:14:37.152 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-08-05 17:14:37.160 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-08-05 17:14:37.182 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-08-05 17:14:37.184 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-08-05 17:14:37.187 | INFO     | ui.views.proxy_view:load_proxies_by_page:538 - 页面 1 加载完成，显示 1 条记录，共 1 条
2025-08-05 17:14:37.189 | INFO     | ui.views.proxy_view:_on_add_proxy:509 - 已清空输入框，代理添加和验证流程完成
2025-08-05 17:14:44.113 | INFO     | ui.main_window:closeEvent:466 - MainWindow: 接收到关闭事件
2025-08-05 17:14:44.127 | INFO     | ui.main_window:_cleanup_before_quit:308 - MainWindow: 执行清理资源...
2025-08-05 17:14:44.129 | INFO     | core.telegram.client_worker:stop:108 - 正在停止Telegram客户端工作线程
2025-08-05 17:14:44.139 | INFO     | core.telegram.client_manager:cleanup_async:1150 - 开始清理资源并断开所有连接
2025-08-05 17:14:44.140 | INFO     | core.telegram.client_manager:disconnect_all_clients:735 - 正在断开所有客户端连接
2025-08-05 17:14:44.140 | INFO     | core.telegram.client_manager:cleanup_async:1169 - 资源清理完成
2025-08-05 17:14:44.646 | INFO     | core.telegram.client_manager:cleanup_async:1150 - 开始清理资源并断开所有连接
2025-08-05 17:14:44.647 | INFO     | core.telegram.client_manager:disconnect_all_clients:735 - 正在断开所有客户端连接
2025-08-05 17:14:44.647 | INFO     | core.telegram.client_manager:cleanup_async:1169 - 资源清理完成
2025-08-05 17:14:45.147 | INFO     | core.telegram.client_worker:run:97 - Telegram客户端工作线程已退出
2025-08-05 17:14:45.147 | INFO     | core.telegram.client_worker:stop:122 - Telegram客户端工作线程已停止
2025-08-05 17:14:45.147 | INFO     | ui.main_window:_cleanup_before_quit:320 - TelegramClientWorker 已停止。
2025-08-05 17:14:45.147 | INFO     | ui.main_window:_cleanup_before_quit:324 - MainWindow 清理完成
2025-08-05 17:14:45.166 | INFO     | __main__:main:114 - 应用程序已正常退出
2025-08-05 17:15:00.896 | INFO     | utils.logger:init_logger:98 - 日志系统初始化完成，默认级别: DEBUG，日志目录: H:\PyProject\TeleTest-bak\APPDATA\logs
2025-08-05 17:15:02.047 | INFO     | data.database:init_database:31 - 初始化数据库连接: H:\PyProject\TeleTest-bak\APPDATA\database\telegram_manager.db
2025-08-05 17:15:02.066 | DEBUG    | data.database:init_database:58 - 创建数据库表
2025-08-05 17:15:02.077 | INFO     | data.database:init_database:61 - 数据库初始化完成
2025-08-05 17:15:02.088 | INFO     | data.database:init_database:31 - 初始化数据库连接: H:\PyProject\TeleTest-bak\APPDATA\database\telegram_manager.db
2025-08-05 17:15:02.090 | DEBUG    | data.database:init_database:58 - 创建数据库表
2025-08-05 17:15:02.099 | INFO     | data.database:init_database:61 - 数据库初始化完成
2025-08-05 17:15:02.643 | DEBUG    | app.controllers.auth_controller:initialize_software:43 - 控制器: 初始化软件 app_id=1000, version=1.3.7
2025-08-05 17:15:02.651 | DEBUG    | core.auth.api_service:init_software:55 - 发送初始化请求: https://server.xile188.com/api/user/1000/windowns10/1.3.7/ini, params={}
2025-08-05 17:15:02.917 | DEBUG    | core.auth.api_service:init_software:66 - 初始化数据解密成功: {'new_content': None, 'new_url': None, 'notice': {'id': '2', 'content': '公告内容测试3', 'time': '**********'}}
2025-08-05 17:15:02.924 | INFO     | app.controllers.auth_controller:login:61 - 控制器: 用户登录 account=demo01
2025-08-05 17:15:05.854 | INFO     | core.auth.api_service:login:127 - 用户登录: account=demo01
2025-08-05 17:15:06.058 | INFO     | core.auth.api_service:get_user_info:347 - 获取用户信息
2025-08-05 17:15:06.253 | INFO     | core.auth.api_service:get_user_info:375 - 获取用户信息成功
2025-08-05 17:15:06.259 | INFO     | __main__:on_auth_completed:50 - 用户登录成功，正在启动主窗口...
2025-08-05 17:15:06.282 | INFO     | core.telegram.client_worker:__init__:61 - Telegram客户端工作线程初始化
2025-08-05 17:15:06.283 | INFO     | utils.vip_checker:initialize:25 - VIP检查器已初始化，等待登录后开始检查
2025-08-05 17:15:06.283 | INFO     | ui.main_window:_initialize_core_components:113 - MainWindow: 初始化核心组件...
2025-08-05 17:15:06.283 | INFO     | core.telegram.client_worker:run:65 - Telegram客户端工作线程开始运行
2025-08-05 17:15:06.284 | INFO     | app.services.account_service:__init__:45 - 账户服务初始化
2025-08-05 17:15:06.284 | INFO     | core.proxy.proxy_task_manager:run:284 - 代理任务管理器线程启动
2025-08-05 17:15:06.285 | INFO     | app.controllers.account_controller:__init__:83 - 账户控制器初始化
2025-08-05 17:15:06.286 | INFO     | app.services.monitor_service:__init__:37 - 初始化监控任务服务
2025-08-05 17:15:06.286 | INFO     | core.monitor.filter:__init__:26 - 初始化消息过滤器
2025-08-05 17:15:06.286 | INFO     | core.telegram.client_manager:__init__:66 - Telegram客户端管理器初始化
2025-08-05 17:15:06.286 | INFO     | core.monitor.extractor:__init__:24 - 初始化关键词提取器
2025-08-05 17:15:06.287 | INFO     | core.telegram.user_manager:__init__:30 - Telegram用户管理器初始化
2025-08-05 17:15:06.287 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-08-05 17:15:06.287 | INFO     | core.telegram.group_manager:__init__:36 - Telegram群组管理器初始化
2025-08-05 17:15:06.287 | INFO     | core.monitor.notifier:__init__:90 - 初始化通知管理器
2025-08-05 17:15:06.288 | INFO     | core.telegram.message_manager:__init__:29 - Telegram消息管理器初始化
2025-08-05 17:15:06.288 | INFO     | app.controllers.telegram_monitor_controller:__init__:64 - 初始化 Telegram 监控控制器
2025-08-05 17:15:06.288 | INFO     | core.telegram.monitor_manager:__init__:26 - Telegram监控管理器初始化
2025-08-05 17:15:06.288 | DEBUG    | app.controllers.telegram_monitor_controller:__init__:82 - 控制器初始化完成
2025-08-05 17:15:06.288 | INFO     | core.telegram.telegram_service:__init__:39 - Telegram服务初始化完成
2025-08-05 17:15:06.421 | INFO     | app.controllers.convert_controller:set_account_controller:35 - 账户控制器已设置
2025-08-05 17:15:06.502 | INFO     | ui.views.monitor_view:__init__:48 - 初始化监控视图
2025-08-05 17:15:06.502 | DEBUG    | ui.views.monitor_view:_connect_signals:80 - 连接控制器信号
2025-08-05 17:15:06.671 | INFO     | ui.views.proxy_view:__init__:96 - 初始化视图，加载第一页代理数据
2025-08-05 17:15:06.722 | INFO     | ui.main_window:_setup_log_managers:457 - 已为 9 个视图创建日志管理器
2025-08-05 17:15:06.919 | INFO     | __main__:on_auth_completed:53 - 主窗口已启动
2025-08-05 17:15:06.966 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:88 - 尝试连接监听日志信号...
2025-08-05 17:15:06.967 | DEBUG    | app.controllers.telegram_monitor_controller:_connect_service_signals:90 - 监听日志信号连接成功
2025-08-05 17:15:06.977 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-08-05 17:15:06.980 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-08-05 17:15:06.985 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:145 - 加载监控任务列表 (搜索关键词: '')
2025-08-05 17:15:06.985 | DEBUG    | ui.views.monitor_view:_on_loading_started:620 - 加载中: 正在加载任务列表...
2025-08-05 17:15:06.986 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-08-05 17:15:06.992 | INFO     | app.services.proxy_service:refresh_proxies:712 - 刷新代理列表: offset=0, limit=20
2025-08-05 17:15:06.992 | INFO     | core.proxy.proxy_task_manager:submit_task:172 - 提交耗时任务到子线程: refresh_proxies_0_20
2025-08-05 17:15:06.993 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-08-05 17:15:06.993 | DEBUG    | app.services.proxy_service:get_proxies:500 - 获取代理列表: offset=0, limit=20
2025-08-05 17:15:06.994 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-08-05 17:15:07.003 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-08-05 17:15:07.020 | INFO     | app.controllers.invite_controller:get_invite_tasks:38 - 获取邀请任务列表, status=None
2025-08-05 17:15:07.021 | INFO     | app.services.invite_service:get_invite_tasks:121 - 获取邀请任务列表, status=None
2025-08-05 17:15:07.022 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-08-05 17:15:07.023 | INFO     | app.services.invite_service:task_initialize:39 - 初始化邀请服务，重置运行中的任务为暂停状态
2025-08-05 17:15:07.024 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-08-05 17:15:07.024 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-08-05 17:15:07.025 | INFO     | core.proxy.proxy_task_manager:task_wrapper:199 - 任务 refresh_proxies_0_20 完成
2025-08-05 17:15:07.027 | INFO     | utils.vip_checker:start_checking:37 - 开始VIP状态检查
2025-08-05 17:15:07.028 | DEBUG    | utils.vip_checker:_check_vip_status:61 - 正在检查VIP状态...
2025-08-05 17:15:07.028 | INFO     | core.auth.api_service:verify_vip:425 - 会员验证接口调用
2025-08-05 17:15:07.235 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-08-05 17:15:07.236 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-08-05 17:15:07.241 | DEBUG    | ui.views.monitor_view:_refresh_task_list:175 - 刷新任务列表，共0个任务
2025-08-05 17:15:07.242 | INFO     | ui.views.monitor_view:_refresh_task_list:189 - 没有找到监控任务
2025-08-05 17:15:07.242 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-08-05 17:15:07.243 | INFO     | app.services.invite_service:task_initialize:48 - 成功重置 0 个任务状态
2025-08-05 17:15:07.244 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-08-05 17:15:07.244 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-08-05 17:15:07.249 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-08-05 17:15:07.252 | INFO     | app.services.account_service:get_all_groups:99 - 获取所有账户分组成功, 共 2 个
2025-08-05 17:15:07.252 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-08-05 17:15:07.254 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-08-05 17:15:07.256 | DEBUG    | ui.views.account_view:_on_groups_loaded:519 - 分组加载完成: 2个分组
2025-08-05 17:15:07.269 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-08-05 17:15:07.334 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-08-05 17:15:07.340 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-08-05 17:15:07.344 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-08-05 17:15:07.356 | INFO     | app.controllers.telegram_monitor_controller:_load_total_users_count_internal:191 - 成功加载总体统计数据: 总用户数 23, 今日采集 0, 日均采集 23, 运行天数 1
2025-08-05 17:15:07.356 | INFO     | app.controllers.telegram_monitor_controller:load_tasks:156 - 成功加载 0 个任务
2025-08-05 17:15:07.356 | DEBUG    | ui.views.monitor_view:_on_loading_finished:625 - 加载完成
2025-08-05 17:15:07.371 | INFO     | app.services.account_service:get_all_accounts:333 - 获取所有账户成功, 共 2 个
2025-08-05 17:15:07.372 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-08-05 17:15:07.379 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-08-05 17:15:07.381 | DEBUG    | ui.views.account_view:_on_accounts_loaded:652 - 账户加载完成: 2个账户
2025-08-05 17:15:07.402 | DEBUG    | ui.views.account_view:_update_group_counts:188 - 分组计数已更新: {2: 2}
2025-08-05 17:15:07.407 | INFO     | ui.views.account_view:_auto_login_accounts:707 - 开始调用控制器进行 2 个账户的自动登录。
2025-08-05 17:15:07.407 | INFO     | app.services.account_service:batch_auto_login:1557 - 服务层：开始批量自动登录 2 个账户, 最大并发: 5
2025-08-05 17:15:07.408 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-08-05 17:15:07.415 | INFO     | core.auth.api_service:verify_vip:438 - 会员验证成功
2025-08-05 17:15:07.415 | INFO     | utils.vip_checker:_check_vip_status:76 - VIP状态有效
2025-08-05 17:15:07.420 | INFO     | app.services.account_service:batch_auto_login:1597 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:7897
2025-08-05 17:15:07.420 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-08-05 17:15:07.422 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-08-05 17:15:07.428 | INFO     | app.services.account_service:batch_auto_login:1597 - 服务层：账户 +*********** 使用IP池代理ID 1, 详情: socks5 127.0.0.1:7897
2025-08-05 17:15:07.428 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-08-05 17:15:07.430 | INFO     | app.services.account_service:batch_auto_login:1667 - 服务层：准备调用核心层处理 2 个账户的自动登录。
2025-08-05 17:15:07.431 | INFO     | app.services.account_service:batch_auto_login:1677 - 服务层：设置核心层任务超时为 120 秒。
2025-08-05 17:15:07.431 | INFO     | core.telegram.client_manager:batch_auto_login:1230 - 开始批量自动登录, 共 2 个账户, 最大并发 5
2025-08-05 17:15:07.431 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:271 - 创建客户端: H:\PyProject\TeleTest-bak\APPDATA\sessions\+***********, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 7897}
2025-08-05 17:15:07.432 | DEBUG    | core.telegram.client_manager:get_proxy:96 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 7897}
2025-08-05 17:15:07.432 | INFO     | app.services.account_service:get_all_accounts:333 - 获取所有账户成功, 共 2 个
2025-08-05 17:15:07.432 | DEBUG    | core.telegram.client_manager:get_proxy:143 - get_proxy: 使用 ip_pool 代理 127.0.0.1:7897，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 7897)
2025-08-05 17:15:07.432 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-08-05 17:15:07.435 | DEBUG    | ui.views.account_view:_update_group_counts:188 - 分组计数已更新: {2: 2}
2025-08-05 17:15:07.436 | DEBUG    | ui.views.account_view:_init_cache:155 - 账户缓存初始化完成，共 2 个账户
2025-08-05 17:15:07.437 | DEBUG    | core.telegram.client_manager:_create_and_connect_client:271 - 创建客户端: H:\PyProject\TeleTest-bak\APPDATA\sessions\+***********, 代理: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 7897}
2025-08-05 17:15:07.439 | DEBUG    | core.telegram.client_manager:get_proxy:96 - get_proxy 收到代理配置字典: {'type': 'ip_pool', 'proxy_type': 'socks5', 'ip': '127.0.0.1', 'port': 7897}
2025-08-05 17:15:07.441 | DEBUG    | core.telegram.client_manager:get_proxy:143 - get_proxy: 使用 ip_pool 代理 127.0.0.1:7897，转换结果: (<ProxyType.SOCKS5: 2>, '127.0.0.1', 7897)
2025-08-05 17:15:07.442 | DEBUG    | ui.views.account_view:_on_accounts_loaded:652 - 账户加载完成: 2个账户
2025-08-05 17:15:07.467 | DEBUG    | ui.views.account_view:_update_group_counts:188 - 分组计数已更新: {2: 2}
2025-08-05 17:15:07.488 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-08-05 17:15:07.557 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-08-05 17:15:07.568 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-08-05 17:15:07.570 | DEBUG    | ui.views.proxy_view:_update_service_buttons:981 - 更新服务按钮状态: 服务运行状态=False
2025-08-05 17:15:07.573 | INFO     | ui.views.proxy_view:_load_initial_data:1044 - 初始数据加载完成，共 1 个代理
2025-08-05 17:15:07.610 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-08-05 17:15:09.560 | INFO     | core.telegram.client_manager:_connect_client:219 - 连接成功!
2025-08-05 17:15:09.767 | INFO     | core.telegram.client_manager:_connect_client:219 - 连接成功!
2025-08-05 17:15:10.306 | INFO     | core.telegram.client_manager:_verify_client_authorization:1205 - 账户已授权: +***********
2025-08-05 17:15:10.536 | INFO     | core.telegram.client_manager:_verify_client_authorization:1205 - 账户已授权: +***********
2025-08-05 17:15:12.553 | INFO     | core.telegram.client_manager:batch_auto_login:1379 - 批量登录完成: 总计 2, 成功 2, 失败 0
2025-08-05 17:15:13.442 | INFO     | app.services.account_service:batch_auto_login:1698 - 服务层：核心层登录完成: 成功 2, 失败 0 / 2
2025-08-05 17:15:13.442 | INFO     | app.services.account_service:_process_batch_login_results:2333 - 开始处理批量登录结果，共 2 个账户
2025-08-05 17:15:13.442 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-08-05 17:15:13.442 | INFO     | data.repositories.account_repo:update_account:394 - 更新账户成功: 1
2025-08-05 17:15:13.442 | INFO     | app.services.account_service:_process_batch_login_results:2369 - 已更新账户 +*********** 的用户信息
2025-08-05 17:15:13.456 | INFO     | data.repositories.account_repo:update_account:394 - 更新账户成功: 2
2025-08-05 17:15:13.456 | INFO     | app.services.account_service:_process_batch_login_results:2369 - 已更新账户 +*********** 的用户信息
2025-08-05 17:15:13.462 | INFO     | app.services.account_service:_process_batch_login_results:2392 - 批量登录结果处理完成，数据库已更新
2025-08-05 17:15:13.462 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-08-05 17:15:13.462 | INFO     | app.controllers.account_controller:auto_login_accounts:642 - 批量登录完成，触发账户数据刷新
2025-08-05 17:15:18.078 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-08-05 17:15:18.081 | INFO     | app.services.account_service:get_accounts_by_group:365 - 获取分组 2 的账户成功, 共 2 个
2025-08-05 17:15:18.081 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-08-05 17:15:18.095 | DEBUG    | ui.views.account_view:_on_accounts_loaded:652 - 账户加载完成: 2个账户
2025-08-05 17:15:19.016 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-08-05 17:15:19.026 | INFO     | app.services.account_service:get_all_accounts:333 - 获取所有账户成功, 共 2 个
2025-08-05 17:15:19.026 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-08-05 17:15:19.028 | DEBUG    | ui.views.account_view:_on_accounts_loaded:652 - 账户加载完成: 2个账户
2025-08-05 17:15:19.047 | DEBUG    | ui.views.account_view:_update_group_counts:188 - 分组计数已更新: {2: 2}
2025-08-05 17:16:06.244 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-08-05 17:17:06.239 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-08-05 17:18:06.251 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-08-05 17:19:06.261 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-08-05 17:20:06.259 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-08-05 17:21:06.262 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-08-05 17:22:06.271 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-08-05 17:23:06.286 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-08-05 17:24:06.275 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-08-05 17:25:06.273 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-08-05 17:26:06.285 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-08-05 17:27:06.274 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
2025-08-05 17:28:06.272 | DEBUG    | core.auth.api_service:heartbeat:310 - 发送心跳
