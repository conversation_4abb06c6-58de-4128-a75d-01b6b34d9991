2025-06-11 10:25:10.332 | ERROR    | core.telegram.client_manager:_connect_client:217 - 认证密钥错误 (AuthKeyError): The authorization key (session file) was used under two different IP addresses simultaneously, and can no longer be used. Use the same session exclusively, or use different sessions (caused by InvokeWithLayerRequest(InitConnectionRequest(GetConfigRequest)))
2025-06-11 10:27:02.465 | ERROR    | app.services.account_service:add_account:264 - 添加账户异常: 'str' object has no attribute 'get'
2025-06-11 10:27:02.465 | ERROR    | app.controllers.account_controller:complete_login:519 - 账户添加失败: +***********, 原因: 添加账户异常: 'str' object has no attribute 'get'
2025-06-11 10:27:02.472 | ERROR    | ui.views.account_view:_on_account_added:262 - 账户添加失败: 添加账户异常: 'str' object has no attribute 'get'
2025-06-11 10:27:23.278 | ERROR    | core.telegram.client_manager:_connect_client:217 - 认证密钥错误 (AuthKeyError): The authorization key (session file) was used under two different IP addresses simultaneously, and can no longer be used. Use the same session exclusively, or use different sessions (caused by InvokeWithLayerRequest(InitConnectionRequest(GetConfigRequest)))
2025-06-11 10:29:58.990 | ERROR    | app.services.account_service:add_account:261 - 添加账户数据库操作异常: (sqlite3.IntegrityError) UNIQUE constraint failed: accounts.phone
[SQL: INSERT INTO accounts (phone, session_file, first_name, last_name, username, bio, profile_photo, is_active, is_connected, has_2fa, last_connected, last_active, created_at, updated_at, proxy_id, proxy_type, group_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]
[parameters: ('+***********', 'h:\\PyProject\\TeleTest\\APPDATA\\sessions\\+***********.session', '共赢2', '联盟', 'bangqiwang', '', '', 1, 1, 0, '2025-06-11 10:29:58.960678', None, '2025-06-11 10:29:58.974017', '2025-06-11 10:29:58.974017', 1, 'ip_pool', 1)]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-06-11 10:29:58.992 | ERROR    | app.controllers.account_controller:complete_login:519 - 账户添加失败: +***********, 原因: 添加账户数据库异常: (sqlite3.IntegrityError) UNIQUE constraint failed: accounts.phone
[SQL: INSERT INTO accounts (phone, session_file, first_name, last_name, username, bio, profile_photo, is_active, is_connected, has_2fa, last_connected, last_active, created_at, updated_at, proxy_id, proxy_type, group_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]
[parameters: ('+***********', 'h:\\PyProject\\TeleTest\\APPDATA\\sessions\\+***********.session', '共赢2', '联盟', 'bangqiwang', '', '', 1, 1, 0, '2025-06-11 10:29:58.960678', None, '2025-06-11 10:29:58.974017', '2025-06-11 10:29:58.974017', 1, 'ip_pool', 1)]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-06-11 10:29:58.999 | ERROR    | ui.views.account_view:_on_account_added:262 - 账户添加失败: 添加账户数据库异常: (sqlite3.IntegrityError) UNIQUE constraint failed: accounts.phone
[SQL: INSERT INTO accounts (phone, session_file, first_name, last_name, username, bio, profile_photo, is_active, is_connected, has_2fa, last_connected, last_active, created_at, updated_at, proxy_id, proxy_type, group_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]
[parameters: ('+***********', 'h:\\PyProject\\TeleTest\\APPDATA\\sessions\\+***********.session', '共赢2', '联盟', 'bangqiwang', '', '', 1, 1, 0, '2025-06-11 10:29:58.960678', None, '2025-06-11 10:29:58.974017', '2025-06-11 10:29:58.974017', 1, 'ip_pool', 1)]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-06-11 10:29:59.059 | ERROR    | app.services.account_service:add_account:261 - 添加账户数据库操作异常: (sqlite3.IntegrityError) UNIQUE constraint failed: accounts.phone
[SQL: INSERT INTO accounts (phone, session_file, first_name, last_name, username, bio, profile_photo, is_active, is_connected, has_2fa, last_connected, last_active, created_at, updated_at, proxy_id, proxy_type, group_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]
[parameters: ('+***********', 'h:\\PyProject\\TeleTest\\APPDATA\\sessions\\+***********.session', '共赢2', '联盟', 'bangqiwang', '', '', 1, 1, 0, '2025-06-11 10:29:58.958032', None, '2025-06-11 10:29:58.972019', '2025-06-11 10:29:58.972019', 1, 'ip_pool', 1)]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-06-11 10:29:59.061 | ERROR    | app.controllers.account_controller:complete_login:519 - 账户添加失败: +***********, 原因: 添加账户数据库异常: (sqlite3.IntegrityError) UNIQUE constraint failed: accounts.phone
[SQL: INSERT INTO accounts (phone, session_file, first_name, last_name, username, bio, profile_photo, is_active, is_connected, has_2fa, last_connected, last_active, created_at, updated_at, proxy_id, proxy_type, group_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]
[parameters: ('+***********', 'h:\\PyProject\\TeleTest\\APPDATA\\sessions\\+***********.session', '共赢2', '联盟', 'bangqiwang', '', '', 1, 1, 0, '2025-06-11 10:29:58.958032', None, '2025-06-11 10:29:58.972019', '2025-06-11 10:29:58.972019', 1, 'ip_pool', 1)]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-06-11 10:29:59.069 | ERROR    | ui.views.account_view:_on_account_added:262 - 账户添加失败: 添加账户数据库异常: (sqlite3.IntegrityError) UNIQUE constraint failed: accounts.phone
[SQL: INSERT INTO accounts (phone, session_file, first_name, last_name, username, bio, profile_photo, is_active, is_connected, has_2fa, last_connected, last_active, created_at, updated_at, proxy_id, proxy_type, group_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]
[parameters: ('+***********', 'h:\\PyProject\\TeleTest\\APPDATA\\sessions\\+***********.session', '共赢2', '联盟', 'bangqiwang', '', '', 1, 1, 0, '2025-06-11 10:29:58.958032', None, '2025-06-11 10:29:58.972019', '2025-06-11 10:29:58.972019', 1, 'ip_pool', 1)]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-06-11 10:35:40.448 | ERROR    | core.telegram.client_worker:task_wrapper:176 - 任务异常 [id=15]: object tuple can't be used in 'await' expression
2025-06-11 10:36:02.549 | ERROR    | core.telegram.client_worker:task_wrapper:176 - 任务异常 [id=59]: object tuple can't be used in 'await' expression
2025-06-11 10:36:17.256 | ERROR    | core.telegram.client_worker:task_wrapper:176 - 任务异常 [id=61]: object tuple can't be used in 'await' expression
2025-06-11 10:36:17.411 | ERROR    | core.telegram.client_worker:task_wrapper:176 - 任务异常 [id=62]: object tuple can't be used in 'await' expression
2025-06-11 10:36:17.549 | ERROR    | core.telegram.client_worker:task_wrapper:176 - 任务异常 [id=63]: object tuple can't be used in 'await' expression
2025-06-11 10:49:21.607 | ERROR    | core.telegram.client_worker:task_wrapper:176 - 任务异常 [id=714]: object tuple can't be used in 'await' expression
2025-06-11 10:49:21.759 | ERROR    | core.telegram.client_worker:task_wrapper:176 - 任务异常 [id=715]: object tuple can't be used in 'await' expression
