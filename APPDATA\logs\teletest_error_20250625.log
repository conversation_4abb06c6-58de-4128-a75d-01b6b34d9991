2025-06-25 10:56:37.673 | ERROR    | app.controllers.send_msg_controller:start_task:153 - 启动任务 1 失败: 任务没有待处理的目标 [Task ID: 1]
2025-06-25 10:56:48.633 | ERROR    | app.controllers.send_msg_controller:start_task:153 - 启动任务 1 失败: 任务没有待处理的目标 [Task ID: 1]
2025-06-25 10:56:53.588 | ERROR    | app.controllers.send_msg_controller:start_task:153 - 启动任务 1 失败: 任务没有待处理的目标 [Task ID: 1]
2025-06-25 11:10:41.527 | ERROR    | app.controllers.send_msg_controller:start_task:153 - 启动任务 2 失败: 任务没有待处理的目标 [Task ID: 2]
2025-06-25 11:10:46.225 | ERROR    | app.controllers.send_msg_controller:start_task:153 - 启动任务 2 失败: 任务没有待处理的目标 [Task ID: 2]
2025-06-25 11:18:28.794 | ERROR    | core.telegram.message_manager:send_image_text_message:80 - 账户 +*********** 向 ddv6_com 发送图文消息失败: 'list' object has no attribute 'id'
2025-06-25 11:19:31.954 | ERROR    | core.telegram.message_manager:send_image_text_message:80 - 账户 +*********** 向 ddv6_com 发送图文消息失败: 'list' object has no attribute 'id'
2025-06-25 11:25:19.175 | ERROR    | core.telegram.message_manager:send_image_text_message:80 - 账户 +88807194657 向 ddv6_com 发送图文消息失败: 'list' object has no attribute 'id'
2025-06-25 11:42:04.359 | ERROR    | core.telegram.message_manager:send_image_text_message:80 - 账户 +88807194657 向 ddv6_com 发送图文消息失败: 'list' object has no attribute 'id'
2025-06-25 15:07:19.847 | ERROR    | core.telegram.client_worker:task_wrapper:174 - 任务异常 [id=3]: object tuple can't be used in 'await' expression
2025-06-25 15:07:19.986 | ERROR    | core.telegram.client_worker:task_wrapper:174 - 任务异常 [id=4]: object tuple can't be used in 'await' expression
2025-06-25 15:07:20.143 | ERROR    | core.telegram.client_worker:task_wrapper:174 - 任务异常 [id=5]: object tuple can't be used in 'await' expression
2025-06-25 15:41:18.135 | ERROR    | core.telegram.client_worker:task_wrapper:174 - 任务异常 [id=4]: object tuple can't be used in 'await' expression
2025-06-25 15:41:18.289 | ERROR    | core.telegram.client_worker:task_wrapper:174 - 任务异常 [id=5]: object tuple can't be used in 'await' expression
2025-06-25 15:41:31.916 | ERROR    | core.telegram.client_worker:task_wrapper:174 - 任务异常 [id=28]: object tuple can't be used in 'await' expression
2025-06-25 15:42:06.773 | ERROR    | core.telegram.client_worker:task_wrapper:174 - 任务异常 [id=30]: object tuple can't be used in 'await' expression
2025-06-25 15:42:16.564 | ERROR    | core.telegram.client_worker:task_wrapper:174 - 任务异常 [id=43]: object tuple can't be used in 'await' expression
2025-06-25 15:45:12.174 | ERROR    | core.telegram.client_worker:task_wrapper:174 - 任务异常 [id=3]: object tuple can't be used in 'await' expression
2025-06-25 15:45:32.719 | ERROR    | core.telegram.client_worker:task_wrapper:174 - 任务异常 [id=30]: object tuple can't be used in 'await' expression
2025-06-25 21:34:44.984 | ERROR    | data.repositories.message_repo:get_task_target_stats:187 - 获取任务6目标统计失败: coroutine ignored GeneratorExit
2025-06-25 21:34:44.985 | ERROR    | ui.views.send_msg_view:load_tasks:50 - 加载任务列表失败: (sqlite3.OperationalError) no active connection
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-25 21:36:55.872 | ERROR    | utils.client_http:post:94 - 网络请求异常: [Errno 1] [SSL: APPLICATION_DATA_AFTER_CLOSE_NOTIFY] application data after close notify (_ssl.c:2702)
2025-06-25 21:38:33.375 | ERROR    | utils.client_http:post:94 - 网络请求异常: [Errno None] Can not write request body for https://server.xile188.com/api/user/1000/windowns10/1.2.7/vip
2025-06-25 21:39:03.745 | ERROR    | data.repositories.proxy_repo:find_by_id:134 - 根据ID查询代理出错: Cannot switch to a different thread
	Current:  <greenlet.greenlet object at 0x000001AF7D48AC00 (otid=0x000001AF3C6E9230) current active started main>
	Expected: <greenlet.greenlet object at 0x000001AF594BC9C0 (otid=0x000001AF59463ED0) suspended active started main>
Traceback (most recent call last):

  File "H:\PyProject\TeleTest\script\Lib\site-packages\sqlalchemy\util\_concurrency_py3k.py", line 196, in greenlet_spawn
    value = await result
                  └ <coroutine object Connection.create_function at 0x000001AF3BBE0BF0>

GeneratorExit


During handling of the above exception, another exception occurred:


Traceback (most recent call last):

  File "h:\PyProject\TeleTest\core\telegram\client_worker.py", line 87, in run
    self._loop.run_forever()
    │    │     └ <function ProactorEventLoop.run_forever at 0x000001AF5818BBA0>
    │    └ <ProactorEventLoop running=True closed=False debug=False>
    └ <core.telegram.client_worker.TelegramClientWorker(0x1af7bd40500) at 0x000001AF7D494FC0>

  File "D:\Program Files\python11\Lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "D:\Program Files\python11\Lib\asyncio\base_events.py", line 607, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x000001AF580D67A0>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "D:\Program Files\python11\Lib\asyncio\base_events.py", line 1922, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x000001AF58000E00>
    └ <Handle <TaskStepMethWrapper object at 0x000001AF3BBA3490>()>
  File "D:\Program Files\python11\Lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle <TaskStepMethWrapper object at 0x000001AF3BBA3490>()>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle <TaskStepMethWrapper object at 0x000001AF3BBA3490>()>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle <TaskStepMethWrapper object at 0x000001AF3BBA3490>()>

  File "h:\PyProject\TeleTest\core\telegram\client_worker.py", line 464, in _graceful_shutdown
    self._telegram_service.cleanup()
    │    │                 └ <function TelegramService.cleanup at 0x000001AF7A9D1300>
    │    └ <core.telegram.telegram_service.TelegramService object at 0x000001AF7D440D50>
    └ <core.telegram.client_worker.TelegramClientWorker(0x1af7bd40500) at 0x000001AF7D494FC0>

  File "h:\PyProject\TeleTest\core\telegram\telegram_service.py", line 443, in cleanup
    self._client_manager.cleanup()
    │    │               └ <function TelegramClientManager.cleanup at 0x000001AF7A9BE0C0>
    │    └ <core.telegram.client_manager.TelegramClientManager(0x1af7b9cd010) at 0x000001AF39A51CC0>
    └ <core.telegram.telegram_service.TelegramService object at 0x000001AF7D440D50>

  File "h:\PyProject\TeleTest\core\telegram\client_manager.py", line 1128, in cleanup
    cleanup_task.add_done_callback(
    │            └ <method 'add_done_callback' of '_asyncio.Task' objects>
    └ <Task pending name='Task-344' coro=<TelegramClientManager.cleanup_async() running at h:\PyProject\TeleTest\core\telegram\clie...

  File "D:\Program Files\python11\Lib\warnings.py", line 537, in _warn_unawaited_coroutine
    warn(msg, category=RuntimeWarning, stacklevel=2, source=coro)
    │    │                                                  └ <coroutine object TelegramClientManager.cleanup_async at 0x000001AF39B1CD60>
    │    └ "coroutine 'TelegramClientManager.cleanup_async' was never awaited"
    └ <built-in function warn>
  File "D:\Program Files\python11\Lib\warnings.py", line 112, in _showwarnmsg
    _showwarnmsg_impl(msg)
    │                 └ <warnings.WarningMessage object at 0x000001AF3C756A90>
    └ <function _showwarnmsg_impl at 0x000001AF56436660>
  File "D:\Program Files\python11\Lib\warnings.py", line 28, in _showwarnmsg_impl
    text = _formatwarnmsg(msg)
           │              └ <warnings.WarningMessage object at 0x000001AF3C756A90>
           └ <function _formatwarnmsg at 0x000001AF56436840>
  File "D:\Program Files\python11\Lib\warnings.py", line 128, in _formatwarnmsg
    return _formatwarnmsg_impl(msg)
           │                   └ <warnings.WarningMessage object at 0x000001AF3C756A90>
           └ <function _formatwarnmsg_impl at 0x000001AF56436700>
  File "D:\Program Files\python11\Lib\warnings.py", line 56, in _formatwarnmsg_impl
    import tracemalloc
  File "<frozen importlib._bootstrap>", line 1178, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1149, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 690, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 936, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1069, in get_code
  File "<frozen importlib._bootstrap_external>", line 729, in _compile_bytecode

> File "h:\PyProject\TeleTest\data\repositories\proxy_repo.py", line 129, in find_by_id
    result = await self.session.execute(
                   │    │       └ <function AsyncSession.execute at 0x000001AF59EF02C0>
                   │    └ <sqlalchemy.ext.asyncio.session.AsyncSession object at 0x000001AF3C701110>
                   └ <data.repositories.proxy_repo.ProxyRepository object at 0x000001AF3C6F6E50>

  File "H:\PyProject\TeleTest\script\Lib\site-packages\sqlalchemy\ext\asyncio\session.py", line 463, in execute
    result = await greenlet_spawn(
                   └ <function greenlet_spawn at 0x000001AF594E1940>
  File "H:\PyProject\TeleTest\script\Lib\site-packages\sqlalchemy\util\_concurrency_py3k.py", line 201, in greenlet_spawn
    result = context.throw(*sys.exc_info())
             │       │      │   └ <built-in function exc_info>
             │       │      └ <module 'sys' (built-in)>
             │       └ <method 'throw' of 'greenlet.greenlet' objects>
             └ <_AsyncIoGreenlet object at 0x000001AF3C701240 (otid=0x000001AF59463ED0) suspended active started>

greenlet.error: Cannot switch to a different thread
	Current:  <greenlet.greenlet object at 0x000001AF7D48AC00 (otid=0x000001AF3C6E9230) current active started main>
	Expected: <greenlet.greenlet object at 0x000001AF594BC9C0 (otid=0x000001AF59463ED0) suspended active started main>
2025-06-25 21:39:03.777 | ERROR    | app.services.account_service:batch_auto_login:1228 - 服务层：账户 +*********** 查询代理ID 1 时出错: Method 'close()' can't be called here; method '_connection_for_bind()' is already in progress and this would cause an unexpected state change to <SessionTransactionState.CLOSED: 5> (Background on this error at: https://sqlalche.me/e/20/isce)
2025-06-25 21:39:03.779 | ERROR    | app.controllers.account_controller:auto_login_accounts:590 - 批量自动登录异常: coroutine ignored GeneratorExit
2025-06-25 21:39:03.781 | ERROR    | data.repositories.message_repo:get_task_target_stats:187 - 获取任务6目标统计失败: Cannot switch to a different thread
	Current:  <greenlet.greenlet object at 0x000001AF7D48AC00 (otid=0x000001AF3C6E9230) current active started main>
	Expected: <greenlet.greenlet object at 0x000001AF594BC9C0 (otid=0x000001AF59463ED0) suspended active started main>
2025-06-25 21:39:03.788 | ERROR    | ui.views.send_msg_view:load_tasks:50 - 加载任务列表失败: coroutine ignored GeneratorExit
2025-06-25 21:39:55.317 | ERROR    | utils.client_http:post:94 - 网络请求异常: [Errno 1] [SSL: APPLICATION_DATA_AFTER_CLOSE_NOTIFY] application data after close notify (_ssl.c:2702)
2025-06-25 21:41:05.844 | ERROR    | utils.client_http:post:94 - 网络请求异常: [Errno 1] [SSL: APPLICATION_DATA_AFTER_CLOSE_NOTIFY] application data after close notify (_ssl.c:2702)
2025-06-25 21:43:26.546 | ERROR    | utils.client_http:post:94 - 网络请求异常: [Errno 1] [SSL: APPLICATION_DATA_AFTER_CLOSE_NOTIFY] application data after close notify (_ssl.c:2702)
2025-06-25 21:44:04.364 | ERROR    | data.repositories.message_repo:get_task_target_stats:187 - 获取任务2目标统计失败: coroutine ignored GeneratorExit
2025-06-25 21:44:04.366 | ERROR    | ui.views.send_msg_view:load_tasks:50 - 加载任务列表失败: (sqlite3.OperationalError) no active connection
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-25 21:44:30.142 | ERROR    | utils.client_http:post:94 - 网络请求异常: [Errno 1] [SSL: APPLICATION_DATA_AFTER_CLOSE_NOTIFY] application data after close notify (_ssl.c:2702)
2025-06-25 21:49:35.612 | ERROR    | utils.client_http:post:94 - 网络请求异常: [Errno 1] [SSL: APPLICATION_DATA_AFTER_CLOSE_NOTIFY] application data after close notify (_ssl.c:2702)
