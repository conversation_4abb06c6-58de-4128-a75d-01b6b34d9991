2025-06-27 17:21:56.190 | ERROR    | utils.client_http:post:94 - 网络请求异常: [Errno 1] [SSL: APPLICATION_DATA_AFTER_CLOSE_NOTIFY] application data after close notify (_ssl.c:2702)
2025-06-27 17:23:11.217 | ERROR    | utils.client_http:post:94 - 网络请求异常: [Errno 1] [SSL: APPLICATION_DATA_AFTER_CLOSE_NOTIFY] application data after close notify (_ssl.c:2702)
2025-06-27 17:29:39.386 | ERROR    | utils.client_http:post:94 - 网络请求异常: [Errno 1] [SSL: APPLICATION_DATA_AFTER_CLOSE_NOTIFY] application data after close notify (_ssl.c:2702)
2025-06-27 17:37:11.631 | ERROR    | utils.client_http:post:94 - 网络请求异常: [Errno 1] [SSL: APPLICATION_DATA_AFTER_CLOSE_NOTIFY] application data after close notify (_ssl.c:2702)
2025-06-27 17:41:30.192 | ERROR    | utils.vip_checker:_check_vip_status:78 - VIP状态检查出现异常: 'int' object has no attribute 'get'
Traceback (most recent call last):

  File "h:\PyProject\TeleTest\main.py", line 118, in <module>
    main()
    └ <function main at 0x000002329D7FA480>

  File "h:\PyProject\TeleTest\main.py", line 109, in main
    event_loop.run_forever()
    │          └ <function _QEventLoop.run_forever at 0x00000232F9133560>
    └ <QIOCPEventLoop running=True closed=False debug=False>

  File "H:\PyProject\TeleTest\script\Lib\site-packages\qasync\__init__.py", line 377, in run_forever
    rslt = self.__app.exec()
           └ <QIOCPEventLoop running=True closed=False debug=False>
  File "H:\PyProject\TeleTest\script\Lib\site-packages\qasync\__init__.py", line 279, in timerEvent
    handle._run()
    │      └ <function Handle._run at 0x00000232F8880E00>
    └ <Handle Task.task_wakeup(<Task finishe...> result=None>)>
  File "D:\Program Files\python11\Lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle Task.task_wakeup(<Task finishe...> result=None>)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle Task.task_wakeup(<Task finishe...> result=None>)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle Task.task_wakeup(<Task finishe...> result=None>)>

> File "h:\PyProject\TeleTest\utils\vip_checker.py", line 61, in _check_vip_status
    is_vip = data.get('is_vip', True)  # 默认为True，兼容老接口
             └ 1751017289

AttributeError: 'int' object has no attribute 'get'
2025-06-27 19:15:16.684 | ERROR    | utils.vip_checker:_check_vip_status:92 - VIP状态检查出现异常: 'int' object has no attribute 'get'
Traceback (most recent call last):

  File "h:\PyProject\TeleTest\main.py", line 118, in <module>
    main()
    └ <function main at 0x0000025F44A8E2A0>

  File "h:\PyProject\TeleTest\main.py", line 109, in main
    event_loop.run_forever()
    │          └ <function _QEventLoop.run_forever at 0x0000025F20473560>
    └ <QIOCPEventLoop running=True closed=False debug=False>

  File "H:\PyProject\TeleTest\script\Lib\site-packages\qasync\__init__.py", line 377, in run_forever
    rslt = self.__app.exec()
           └ <QIOCPEventLoop running=True closed=False debug=False>
  File "H:\PyProject\TeleTest\script\Lib\site-packages\qasync\__init__.py", line 279, in timerEvent
    handle._run()
    │      └ <function Handle._run at 0x0000025F1FBC0E00>
    └ <Handle Task.task_wakeup(<Task finishe...> result=None>)>
  File "D:\Program Files\python11\Lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle Task.task_wakeup(<Task finishe...> result=None>)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle Task.task_wakeup(<Task finishe...> result=None>)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle Task.task_wakeup(<Task finishe...> result=None>)>

  File "h:\PyProject\TeleTest\utils\vip_checker.py", line 54, in _on_timer_timeout
    await self._check_vip_status()
          │    └ <function VipChecker._check_vip_status at 0x0000025F314FBB00>
          └ <utils.vip_checker.VipChecker(0x25f41fbc7b0) at 0x0000025F3150C940>

> File "h:\PyProject\TeleTest\utils\vip_checker.py", line 75, in _check_vip_status
    is_vip = data.get('is_vip', True)  # 默认为True，兼容老接口
             └ 1751022916

AttributeError: 'int' object has no attribute 'get'
2025-06-27 19:19:11.249 | ERROR    | utils.vip_checker:_check_vip_status:92 - VIP状态检查出现异常: 'int' object has no attribute 'get'
Traceback (most recent call last):

  File "D:\Program Files\JetBrains\PyCharm 2025.1.1\plugins\python-ce\helpers\pydev\pydevd.py", line 2270, in <module>
    main()
    └ <function main at 0x0000018E850DC680>

  File "D:\Program Files\JetBrains\PyCharm 2025.1.1\plugins\python-ce\helpers\pydev\pydevd.py", line 2252, in main
    globals = debugger.run(setup['file'], None, None, is_module)
              │        │   │                          └ False
              │        │   └ {'port': 42492, 'vm_type': None, 'client': '127.0.0.1', 'server': False, 'DEBUG_RECORD_SOCKET_READS': False, 'multiproc': Fal...
              │        └ <function PyDB.run at 0x0000018E850DB420>
              └ <__main__.PyDB object at 0x0000018EF1BBA890>

  File "D:\Program Files\JetBrains\PyCharm 2025.1.1\plugins\python-ce\helpers\pydev\pydevd.py", line 1563, in run
    return self._exec(is_module, entry_point_fn, module_name, file, globals, locals)
           │    │     │          │               │            │     │        └ {'__name__': '__main__', '__doc__': '\nTelegram多账户管理系统 - 主程序入口\n', '__package__': '', '__loader__': <_frozen_importlib_extern...
           │    │     │          │               │            │     └ {'__name__': '__main__', '__doc__': '\nTelegram多账户管理系统 - 主程序入口\n', '__package__': '', '__loader__': <_frozen_importlib_extern...
           │    │     │          │               │            └ 'H:\\PyProject\\TeleTest\\main.py'
           │    │     │          │               └ None
           │    │     │          └ ''
           │    │     └ False
           │    └ <function PyDB._exec at 0x0000018E850DB4C0>
           └ <__main__.PyDB object at 0x0000018EF1BBA890>

  File "D:\Program Files\JetBrains\PyCharm 2025.1.1\plugins\python-ce\helpers\pydev\pydevd.py", line 1570, in _exec
    pydev_imports.execfile(file, globals, locals)  # execute the script
    │             │        │     │        └ {'__name__': '__main__', '__doc__': '\nTelegram多账户管理系统 - 主程序入口\n', '__package__': '', '__loader__': <_frozen_importlib_extern...
    │             │        │     └ {'__name__': '__main__', '__doc__': '\nTelegram多账户管理系统 - 主程序入口\n', '__package__': '', '__loader__': <_frozen_importlib_extern...
    │             │        └ 'H:\\PyProject\\TeleTest\\main.py'
    │             └ <function execfile at 0x0000018E84CF9F80>
    └ <module '_pydev_bundle.pydev_imports' from 'D:\\Program Files\\JetBrains\\PyCharm 2025.1.1\\plugins\\python-ce\\helpers\\pyde...

  File "D:\Program Files\JetBrains\PyCharm 2025.1.1\plugins\python-ce\helpers\pydev\_pydev_imps\_pydev_execfile.py", line 18, in execfile
    exec(compile(contents+"\n", file, 'exec'), glob, loc)
                 │              │              │     └ {'__name__': '__main__', '__doc__': '\nTelegram多账户管理系统 - 主程序入口\n', '__package__': '', '__loader__': <_frozen_importlib_extern...
                 │              │              └ {'__name__': '__main__', '__doc__': '\nTelegram多账户管理系统 - 主程序入口\n', '__package__': '', '__loader__': <_frozen_importlib_extern...
                 │              └ 'H:\\PyProject\\TeleTest\\main.py'
                 └ '#!/usr/bin/env python\n# -*- coding: utf-8 -*-\n\n"""\nTelegram多账户管理系统 - 主程序入口\n"""\nimport sys\nimport os\nimport asyncio\n...

  File "H:\PyProject\TeleTest\main.py", line 118, in <module>
    main()
    └ <function main at 0x0000018E99083D80>

  File "H:\PyProject\TeleTest\main.py", line 109, in main
    event_loop.run_forever()
    │          └ <function _QEventLoop.run_forever at 0x0000018E854947C0>
    └ <QIOCPEventLoop running=True closed=False debug=False>

  File "H:\PyProject\TeleTest\script\Lib\site-packages\qasync\__init__.py", line 377, in run_forever
    rslt = self.__app.exec()
           └ <QIOCPEventLoop running=True closed=False debug=False>
  File "H:\PyProject\TeleTest\script\Lib\site-packages\qasync\__init__.py", line 279, in timerEvent
    handle._run()
    │      └ <function Handle._run at 0x0000018E84F9BC40>
    └ <Handle Task.task_wakeup(<Task finishe...> result=None>)>
  File "D:\Program Files\python11\Lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle Task.task_wakeup(<Task finishe...> result=None>)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle Task.task_wakeup(<Task finishe...> result=None>)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle Task.task_wakeup(<Task finishe...> result=None>)>

  File "H:\PyProject\TeleTest\ui\main_window.py", line 293, in on_login_succeeded
    await vip_checker.start_checking()
          │           └ <function VipChecker.start_checking at 0x0000018E85B394E0>
          └ <utils.vip_checker.VipChecker(0x18e87078450) at 0x0000018E85B2F180>

  File "H:\PyProject\TeleTest\utils\vip_checker.py", line 42, in start_checking
    await self._on_timer_timeout()
          │    └ <function VipChecker._on_timer_timeout at 0x0000018E85B39620>
          └ <utils.vip_checker.VipChecker(0x18e87078450) at 0x0000018E85B2F180>

  File "H:\PyProject\TeleTest\utils\vip_checker.py", line 54, in _on_timer_timeout
    await self._check_vip_status()
          │    └ <function VipChecker._check_vip_status at 0x0000018E85B396C0>
          └ <utils.vip_checker.VipChecker(0x18e87078450) at 0x0000018E85B2F180>

> File "H:\PyProject\TeleTest\utils\vip_checker.py", line 75, in _check_vip_status
    is_vip = data.get('is_vip', True)  # 默认为True，兼容老接口
             └ 1751023150

AttributeError: 'int' object has no attribute 'get'
2025-06-27 19:19:14.757 | ERROR    | data.repositories.message_repo:get_task_target_stats:187 - 获取任务4目标统计失败: Cannot switch to a different thread
	Current:  <greenlet.greenlet object at 0x0000018ED71B7C80 (otid=0x0000018ED71E5470) current active started main>
	Expected: <greenlet.greenlet object at 0x0000018E856EC540 (otid=0x0000018E8568B810) suspended active started main>
2025-06-27 19:19:14.765 | ERROR    | ui.views.send_msg_view:load_tasks:50 - 加载任务列表失败: coroutine ignored GeneratorExit
2025-06-27 19:20:26.268 | ERROR    | utils.vip_checker:_check_vip_status:92 - VIP状态检查出现异常: 'int' object has no attribute 'get'
Traceback (most recent call last):

  File "D:\Program Files\JetBrains\PyCharm 2025.1.1\plugins\python-ce\helpers\pydev\pydevd.py", line 2270, in <module>
    main()
    └ <function main at 0x000001B88701C680>

  File "D:\Program Files\JetBrains\PyCharm 2025.1.1\plugins\python-ce\helpers\pydev\pydevd.py", line 2252, in main
    globals = debugger.run(setup['file'], None, None, is_module)
              │        │   │                          └ False
              │        │   └ {'port': 42604, 'vm_type': None, 'client': '127.0.0.1', 'server': False, 'DEBUG_RECORD_SOCKET_READS': False, 'multiproc': Fal...
              │        └ <function PyDB.run at 0x000001B88701B420>
              └ <__main__.PyDB object at 0x000001B8F3B8A890>

  File "D:\Program Files\JetBrains\PyCharm 2025.1.1\plugins\python-ce\helpers\pydev\pydevd.py", line 1563, in run
    return self._exec(is_module, entry_point_fn, module_name, file, globals, locals)
           │    │     │          │               │            │     │        └ {'__name__': '__main__', '__doc__': '\nTelegram多账户管理系统 - 主程序入口\n', '__package__': '', '__loader__': <_frozen_importlib_extern...
           │    │     │          │               │            │     └ {'__name__': '__main__', '__doc__': '\nTelegram多账户管理系统 - 主程序入口\n', '__package__': '', '__loader__': <_frozen_importlib_extern...
           │    │     │          │               │            └ 'H:\\PyProject\\TeleTest\\main.py'
           │    │     │          │               └ None
           │    │     │          └ ''
           │    │     └ False
           │    └ <function PyDB._exec at 0x000001B88701B4C0>
           └ <__main__.PyDB object at 0x000001B8F3B8A890>

  File "D:\Program Files\JetBrains\PyCharm 2025.1.1\plugins\python-ce\helpers\pydev\pydevd.py", line 1570, in _exec
    pydev_imports.execfile(file, globals, locals)  # execute the script
    │             │        │     │        └ {'__name__': '__main__', '__doc__': '\nTelegram多账户管理系统 - 主程序入口\n', '__package__': '', '__loader__': <_frozen_importlib_extern...
    │             │        │     └ {'__name__': '__main__', '__doc__': '\nTelegram多账户管理系统 - 主程序入口\n', '__package__': '', '__loader__': <_frozen_importlib_extern...
    │             │        └ 'H:\\PyProject\\TeleTest\\main.py'
    │             └ <function execfile at 0x000001B886C39F80>
    └ <module '_pydev_bundle.pydev_imports' from 'D:\\Program Files\\JetBrains\\PyCharm 2025.1.1\\plugins\\python-ce\\helpers\\pyde...

  File "D:\Program Files\JetBrains\PyCharm 2025.1.1\plugins\python-ce\helpers\pydev\_pydev_imps\_pydev_execfile.py", line 18, in execfile
    exec(compile(contents+"\n", file, 'exec'), glob, loc)
                 │              │              │     └ {'__name__': '__main__', '__doc__': '\nTelegram多账户管理系统 - 主程序入口\n', '__package__': '', '__loader__': <_frozen_importlib_extern...
                 │              │              └ {'__name__': '__main__', '__doc__': '\nTelegram多账户管理系统 - 主程序入口\n', '__package__': '', '__loader__': <_frozen_importlib_extern...
                 │              └ 'H:\\PyProject\\TeleTest\\main.py'
                 └ '#!/usr/bin/env python\n# -*- coding: utf-8 -*-\n\n"""\nTelegram多账户管理系统 - 主程序入口\n"""\nimport sys\nimport os\nimport asyncio\n...

  File "H:\PyProject\TeleTest\main.py", line 118, in <module>
    main()
    └ <function main at 0x000001B89AFD7EC0>

  File "H:\PyProject\TeleTest\main.py", line 109, in main
    event_loop.run_forever()
    │          └ <function _QEventLoop.run_forever at 0x000001B8873D4860>
    └ <QIOCPEventLoop running=True closed=False debug=False>

  File "H:\PyProject\TeleTest\script\Lib\site-packages\qasync\__init__.py", line 377, in run_forever
    rslt = self.__app.exec()
           └ <QIOCPEventLoop running=True closed=False debug=False>
  File "H:\PyProject\TeleTest\script\Lib\site-packages\qasync\__init__.py", line 279, in timerEvent
    handle._run()
    │      └ <function Handle._run at 0x000001B886EDBC40>
    └ <Handle Task.task_wakeup(<Task finishe...> result=None>)>
  File "D:\Program Files\python11\Lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle Task.task_wakeup(<Task finishe...> result=None>)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle Task.task_wakeup(<Task finishe...> result=None>)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle Task.task_wakeup(<Task finishe...> result=None>)>

  File "H:\PyProject\TeleTest\ui\main_window.py", line 293, in on_login_succeeded
    await vip_checker.start_checking()
          │           └ <function VipChecker.start_checking at 0x000001B887A79620>
          └ <utils.vip_checker.VipChecker(0x1b8997c56b0) at 0x000001B887A6B700>

  File "H:\PyProject\TeleTest\utils\vip_checker.py", line 42, in start_checking
    await self._on_timer_timeout()
          │    └ <function VipChecker._on_timer_timeout at 0x000001B887A79760>
          └ <utils.vip_checker.VipChecker(0x1b8997c56b0) at 0x000001B887A6B700>

  File "H:\PyProject\TeleTest\utils\vip_checker.py", line 54, in _on_timer_timeout
    await self._check_vip_status()
          │    └ <function VipChecker._check_vip_status at 0x000001B887A79800>
          └ <utils.vip_checker.VipChecker(0x1b8997c56b0) at 0x000001B887A6B700>

> File "H:\PyProject\TeleTest\utils\vip_checker.py", line 75, in _check_vip_status
    is_vip = data.get('is_vip', True)  # 默认为True，兼容老接口
             └ 1751023226

AttributeError: 'int' object has no attribute 'get'
2025-06-27 19:20:31.184 | ERROR    | data.repositories.message_repo:get_task_target_stats:187 - 获取任务4目标统计失败: Cannot switch to a different thread
	Current:  <greenlet.greenlet object at 0x000001B8D9177FC0 (otid=0x000001B8D9149CE0) current active started main>
	Expected: <greenlet.greenlet object at 0x000001B887624700 (otid=0x000001B8875CB810) suspended active started main>
2025-06-27 19:20:31.188 | ERROR    | ui.views.send_msg_view:load_tasks:50 - 加载任务列表失败: coroutine ignored GeneratorExit
2025-06-27 19:24:08.104 | ERROR    | utils.vip_checker:_check_vip_status:92 - VIP状态检查出现异常: 'int' object has no attribute 'get'
Traceback (most recent call last):

  File "D:\Program Files\JetBrains\PyCharm 2025.1.1\plugins\python-ce\helpers\pydev\pydevd.py", line 2270, in <module>
    main()
    └ <function main at 0x00000200398BC680>

  File "D:\Program Files\JetBrains\PyCharm 2025.1.1\plugins\python-ce\helpers\pydev\pydevd.py", line 2252, in main
    globals = debugger.run(setup['file'], None, None, is_module)
              │        │   │                          └ False
              │        │   └ {'port': 42798, 'vm_type': None, 'client': '127.0.0.1', 'server': False, 'DEBUG_RECORD_SOCKET_READS': False, 'multiproc': Fal...
              │        └ <function PyDB.run at 0x00000200398BB420>
              └ <__main__.PyDB object at 0x000002002639A890>

  File "D:\Program Files\JetBrains\PyCharm 2025.1.1\plugins\python-ce\helpers\pydev\pydevd.py", line 1563, in run
    return self._exec(is_module, entry_point_fn, module_name, file, globals, locals)
           │    │     │          │               │            │     │        └ {'__name__': '__main__', '__doc__': '\nTelegram多账户管理系统 - 主程序入口\n', '__package__': '', '__loader__': <_frozen_importlib_extern...
           │    │     │          │               │            │     └ {'__name__': '__main__', '__doc__': '\nTelegram多账户管理系统 - 主程序入口\n', '__package__': '', '__loader__': <_frozen_importlib_extern...
           │    │     │          │               │            └ 'H:\\PyProject\\TeleTest\\main.py'
           │    │     │          │               └ None
           │    │     │          └ ''
           │    │     └ False
           │    └ <function PyDB._exec at 0x00000200398BB4C0>
           └ <__main__.PyDB object at 0x000002002639A890>

  File "D:\Program Files\JetBrains\PyCharm 2025.1.1\plugins\python-ce\helpers\pydev\pydevd.py", line 1570, in _exec
    pydev_imports.execfile(file, globals, locals)  # execute the script
    │             │        │     │        └ {'__name__': '__main__', '__doc__': '\nTelegram多账户管理系统 - 主程序入口\n', '__package__': '', '__loader__': <_frozen_importlib_extern...
    │             │        │     └ {'__name__': '__main__', '__doc__': '\nTelegram多账户管理系统 - 主程序入口\n', '__package__': '', '__loader__': <_frozen_importlib_extern...
    │             │        └ 'H:\\PyProject\\TeleTest\\main.py'
    │             └ <function execfile at 0x00000200394D9F80>
    └ <module '_pydev_bundle.pydev_imports' from 'D:\\Program Files\\JetBrains\\PyCharm 2025.1.1\\plugins\\python-ce\\helpers\\pyde...

  File "D:\Program Files\JetBrains\PyCharm 2025.1.1\plugins\python-ce\helpers\pydev\_pydev_imps\_pydev_execfile.py", line 18, in execfile
    exec(compile(contents+"\n", file, 'exec'), glob, loc)
                 │              │              │     └ {'__name__': '__main__', '__doc__': '\nTelegram多账户管理系统 - 主程序入口\n', '__package__': '', '__loader__': <_frozen_importlib_extern...
                 │              │              └ {'__name__': '__main__', '__doc__': '\nTelegram多账户管理系统 - 主程序入口\n', '__package__': '', '__loader__': <_frozen_importlib_extern...
                 │              └ 'H:\\PyProject\\TeleTest\\main.py'
                 └ '#!/usr/bin/env python\n# -*- coding: utf-8 -*-\n\n"""\nTelegram多账户管理系统 - 主程序入口\n"""\nimport sys\nimport os\nimport asyncio\n...

  File "H:\PyProject\TeleTest\main.py", line 118, in <module>
    main()
    └ <function main at 0x000002004D857F60>

  File "H:\PyProject\TeleTest\main.py", line 109, in main
    event_loop.run_forever()
    │          └ <function _QEventLoop.run_forever at 0x0000020039C74900>
    └ <QIOCPEventLoop running=True closed=False debug=False>

  File "H:\PyProject\TeleTest\script\Lib\site-packages\qasync\__init__.py", line 377, in run_forever
    rslt = self.__app.exec()
           └ <QIOCPEventLoop running=True closed=False debug=False>
  File "H:\PyProject\TeleTest\script\Lib\site-packages\qasync\__init__.py", line 279, in timerEvent
    handle._run()
    │      └ <function Handle._run at 0x000002003977BC40>
    └ <Handle Task.task_wakeup(<Task finishe...> result=None>)>
  File "D:\Program Files\python11\Lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle Task.task_wakeup(<Task finishe...> result=None>)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle Task.task_wakeup(<Task finishe...> result=None>)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle Task.task_wakeup(<Task finishe...> result=None>)>

  File "H:\PyProject\TeleTest\ui\main_window.py", line 293, in on_login_succeeded
    await vip_checker.start_checking()
          │           └ <function VipChecker.start_checking at 0x000002003A3196C0>
          └ <utils.vip_checker.VipChecker(0x2004bf02f50) at 0x000002003A30B4C0>

  File "H:\PyProject\TeleTest\utils\vip_checker.py", line 42, in start_checking
    await self._on_timer_timeout()
          │    └ <function VipChecker._on_timer_timeout at 0x000002003A319800>
          └ <utils.vip_checker.VipChecker(0x2004bf02f50) at 0x000002003A30B4C0>

  File "H:\PyProject\TeleTest\utils\vip_checker.py", line 54, in _on_timer_timeout
    await self._check_vip_status()
          │    └ <function VipChecker._check_vip_status at 0x000002003A3198A0>
          └ <utils.vip_checker.VipChecker(0x2004bf02f50) at 0x000002003A30B4C0>

> File "H:\PyProject\TeleTest\utils\vip_checker.py", line 75, in _check_vip_status
    is_vip = data.get('is_vip', True)  # 默认为True，兼容老接口
             └ 1751023357

AttributeError: 'int' object has no attribute 'get'
2025-06-27 19:24:16.372 | ERROR    | ui.main_window:on_login_succeeded:309 - 检查VIP状态时出错: 'bool' object has no attribute 'get'
2025-06-27 19:25:37.090 | ERROR    | utils.vip_checker:_check_vip_status:89 - VIP验证失败: 验证失败
2025-06-27 19:25:39.986 | ERROR    | data.repositories.message_repo:get_task_target_stats:187 - 获取任务4目标统计失败: Cannot switch to a different thread
	Current:  <greenlet.greenlet object at 0x000001F43BD00DC0 (otid=0x000001F43BD244B0) current active started main>
	Expected: <greenlet.greenlet object at 0x000001F46B244900 (otid=0x000001F46B1EB810) suspended active started main>
2025-06-27 19:25:39.989 | ERROR    | ui.views.send_msg_view:load_tasks:50 - 加载任务列表失败: coroutine ignored GeneratorExit
2025-06-27 22:04:52.866 | ERROR    | utils.vip_checker:_check_vip_status:104 - VIP验证失败: 验证失败
2025-06-27 22:05:17.209 | ERROR    | ui.main_window:on_login_succeeded:309 - 检查VIP状态时出错: 'bool' object has no attribute 'get'
2025-06-27 22:07:27.949 | ERROR    | utils.vip_checker:_check_vip_status:104 - VIP验证失败: 验证失败
2025-06-27 22:11:26.585 | ERROR    | utils.vip_checker:_check_vip_status:107 - VIP状态检查出现异常: 'code'
Traceback (most recent call last):

  File "D:\Program Files\JetBrains\PyCharm 2025.1.1\plugins\python-ce\helpers\pydev\pydevd.py", line 2270, in <module>
    main()
    └ <function main at 0x0000026F47BCC680>

  File "D:\Program Files\JetBrains\PyCharm 2025.1.1\plugins\python-ce\helpers\pydev\pydevd.py", line 2252, in main
    globals = debugger.run(setup['file'], None, None, is_module)
              │        │   │                          └ False
              │        │   └ {'port': 56437, 'vm_type': None, 'client': '127.0.0.1', 'server': False, 'DEBUG_RECORD_SOCKET_READS': False, 'multiproc': Fal...
              │        └ <function PyDB.run at 0x0000026F47BCB420>
              └ <__main__.PyDB object at 0x0000026F346FA890>

  File "D:\Program Files\JetBrains\PyCharm 2025.1.1\plugins\python-ce\helpers\pydev\pydevd.py", line 1563, in run
    return self._exec(is_module, entry_point_fn, module_name, file, globals, locals)
           │    │     │          │               │            │     │        └ {'__name__': '__main__', '__doc__': '\nTelegram多账户管理系统 - 主程序入口\n', '__package__': '', '__loader__': <_frozen_importlib_extern...
           │    │     │          │               │            │     └ {'__name__': '__main__', '__doc__': '\nTelegram多账户管理系统 - 主程序入口\n', '__package__': '', '__loader__': <_frozen_importlib_extern...
           │    │     │          │               │            └ 'H:\\PyProject\\TeleTest\\main.py'
           │    │     │          │               └ None
           │    │     │          └ ''
           │    │     └ False
           │    └ <function PyDB._exec at 0x0000026F47BCB4C0>
           └ <__main__.PyDB object at 0x0000026F346FA890>

  File "D:\Program Files\JetBrains\PyCharm 2025.1.1\plugins\python-ce\helpers\pydev\pydevd.py", line 1570, in _exec
    pydev_imports.execfile(file, globals, locals)  # execute the script
    │             │        │     │        └ {'__name__': '__main__', '__doc__': '\nTelegram多账户管理系统 - 主程序入口\n', '__package__': '', '__loader__': <_frozen_importlib_extern...
    │             │        │     └ {'__name__': '__main__', '__doc__': '\nTelegram多账户管理系统 - 主程序入口\n', '__package__': '', '__loader__': <_frozen_importlib_extern...
    │             │        └ 'H:\\PyProject\\TeleTest\\main.py'
    │             └ <function execfile at 0x0000026F477E9F80>
    └ <module '_pydev_bundle.pydev_imports' from 'D:\\Program Files\\JetBrains\\PyCharm 2025.1.1\\plugins\\python-ce\\helpers\\pyde...

  File "D:\Program Files\JetBrains\PyCharm 2025.1.1\plugins\python-ce\helpers\pydev\_pydev_imps\_pydev_execfile.py", line 18, in execfile
    exec(compile(contents+"\n", file, 'exec'), glob, loc)
                 │              │              │     └ {'__name__': '__main__', '__doc__': '\nTelegram多账户管理系统 - 主程序入口\n', '__package__': '', '__loader__': <_frozen_importlib_extern...
                 │              │              └ {'__name__': '__main__', '__doc__': '\nTelegram多账户管理系统 - 主程序入口\n', '__package__': '', '__loader__': <_frozen_importlib_extern...
                 │              └ 'H:\\PyProject\\TeleTest\\main.py'
                 └ '#!/usr/bin/env python\n# -*- coding: utf-8 -*-\n\n"""\nTelegram多账户管理系统 - 主程序入口\n"""\nimport sys\nimport os\nimport asyncio\n...

  File "H:\PyProject\TeleTest\main.py", line 118, in <module>
    main()
    └ <function main at 0x0000026F5BB80C20>

  File "H:\PyProject\TeleTest\main.py", line 109, in main
    event_loop.run_forever()
    │          └ <function _QEventLoop.run_forever at 0x0000026F47FB4900>
    └ <QIOCPEventLoop running=True closed=False debug=False>

  File "H:\PyProject\TeleTest\script\Lib\site-packages\qasync\__init__.py", line 377, in run_forever
    rslt = self.__app.exec()
           └ <QIOCPEventLoop running=True closed=False debug=False>
  File "H:\PyProject\TeleTest\script\Lib\site-packages\qasync\__init__.py", line 279, in timerEvent
    handle._run()
    │      └ <function Handle._run at 0x0000026F47A8BC40>
    └ <Handle Task.task_wakeup(<Task finishe...> result=None>)>
  File "D:\Program Files\python11\Lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle Task.task_wakeup(<Task finishe...> result=None>)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle Task.task_wakeup(<Task finishe...> result=None>)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle Task.task_wakeup(<Task finishe...> result=None>)>

  File "H:\PyProject\TeleTest\ui\main_window.py", line 293, in on_login_succeeded
    await vip_checker.start_checking()
          │           └ <function VipChecker.start_checking at 0x0000026F48629760>
          └ <utils.vip_checker.VipChecker(0x26f5a42cca0) at 0x0000026F5B53C880>

  File "H:\PyProject\TeleTest\utils\vip_checker.py", line 42, in start_checking
    await self._check_vip_status()
          │    └ <function VipChecker._check_vip_status at 0x0000026F48629940>
          └ <utils.vip_checker.VipChecker(0x26f5a42cca0) at 0x0000026F5B53C880>

> File "H:\PyProject\TeleTest\utils\vip_checker.py", line 76, in _check_vip_status
    if result['code'] == 200 and result['msg']=='验证成功':
       │                         └ {'success': False, 'message': '验证失败'}
       └ {'success': False, 'message': '验证失败'}

KeyError: 'code'
2025-06-27 22:12:13.072 | ERROR    | data.repositories.message_repo:get_all_tasks:54 - 获取任务列表失败: Cannot switch to a different thread
	Current:  <greenlet.greenlet object at 0x0000026F1D666100 (otid=0x0000026F1D5C6580) current active started main>
	Expected: <greenlet.greenlet object at 0x0000026F481D4C80 (otid=0x0000026F4817B810) suspended active started main>
2025-06-27 22:12:13.072 | ERROR    | ui.views.send_msg_view:load_global_stats:987 - 加载全局统计数据失败: Method 'close()' can't be called here; method '_connection_for_bind()' is already in progress and this would cause an unexpected state change to <SessionTransactionState.CLOSED: 5> (Background on this error at: https://sqlalche.me/e/20/isce)
2025-06-27 22:12:58.063 | ERROR    | utils.vip_checker:_check_vip_status:87 - VIP验证结果解析出错: 'code'
Traceback (most recent call last):

  File "D:\Program Files\JetBrains\PyCharm 2025.1.1\plugins\python-ce\helpers\pydev\pydevd.py", line 2270, in <module>
    main()
    └ <function main at 0x00000216B9B6C680>

  File "D:\Program Files\JetBrains\PyCharm 2025.1.1\plugins\python-ce\helpers\pydev\pydevd.py", line 2252, in main
    globals = debugger.run(setup['file'], None, None, is_module)
              │        │   │                          └ False
              │        │   └ {'port': 56592, 'vm_type': None, 'client': '127.0.0.1', 'server': False, 'DEBUG_RECORD_SOCKET_READS': False, 'multiproc': Fal...
              │        └ <function PyDB.run at 0x00000216B9B6B420>
              └ <__main__.PyDB object at 0x00000216A675A890>

  File "D:\Program Files\JetBrains\PyCharm 2025.1.1\plugins\python-ce\helpers\pydev\pydevd.py", line 1563, in run
    return self._exec(is_module, entry_point_fn, module_name, file, globals, locals)
           │    │     │          │               │            │     │        └ {'__name__': '__main__', '__doc__': '\nTelegram多账户管理系统 - 主程序入口\n', '__package__': '', '__loader__': <_frozen_importlib_extern...
           │    │     │          │               │            │     └ {'__name__': '__main__', '__doc__': '\nTelegram多账户管理系统 - 主程序入口\n', '__package__': '', '__loader__': <_frozen_importlib_extern...
           │    │     │          │               │            └ 'H:\\PyProject\\TeleTest\\main.py'
           │    │     │          │               └ None
           │    │     │          └ ''
           │    │     └ False
           │    └ <function PyDB._exec at 0x00000216B9B6B4C0>
           └ <__main__.PyDB object at 0x00000216A675A890>

  File "D:\Program Files\JetBrains\PyCharm 2025.1.1\plugins\python-ce\helpers\pydev\pydevd.py", line 1570, in _exec
    pydev_imports.execfile(file, globals, locals)  # execute the script
    │             │        │     │        └ {'__name__': '__main__', '__doc__': '\nTelegram多账户管理系统 - 主程序入口\n', '__package__': '', '__loader__': <_frozen_importlib_extern...
    │             │        │     └ {'__name__': '__main__', '__doc__': '\nTelegram多账户管理系统 - 主程序入口\n', '__package__': '', '__loader__': <_frozen_importlib_extern...
    │             │        └ 'H:\\PyProject\\TeleTest\\main.py'
    │             └ <function execfile at 0x00000216B9789F80>
    └ <module '_pydev_bundle.pydev_imports' from 'D:\\Program Files\\JetBrains\\PyCharm 2025.1.1\\plugins\\python-ce\\helpers\\pyde...

  File "D:\Program Files\JetBrains\PyCharm 2025.1.1\plugins\python-ce\helpers\pydev\_pydev_imps\_pydev_execfile.py", line 18, in execfile
    exec(compile(contents+"\n", file, 'exec'), glob, loc)
                 │              │              │     └ {'__name__': '__main__', '__doc__': '\nTelegram多账户管理系统 - 主程序入口\n', '__package__': '', '__loader__': <_frozen_importlib_extern...
                 │              │              └ {'__name__': '__main__', '__doc__': '\nTelegram多账户管理系统 - 主程序入口\n', '__package__': '', '__loader__': <_frozen_importlib_extern...
                 │              └ 'H:\\PyProject\\TeleTest\\main.py'
                 └ '#!/usr/bin/env python\n# -*- coding: utf-8 -*-\n\n"""\nTelegram多账户管理系统 - 主程序入口\n"""\nimport sys\nimport os\nimport asyncio\n...

  File "H:\PyProject\TeleTest\main.py", line 118, in <module>
    main()
    └ <function main at 0x00000216CDB404A0>

  File "H:\PyProject\TeleTest\main.py", line 109, in main
    event_loop.run_forever()
    │          └ <function _QEventLoop.run_forever at 0x00000216B9F84900>
    └ <QIOCPEventLoop running=True closed=False debug=False>

  File "H:\PyProject\TeleTest\script\Lib\site-packages\qasync\__init__.py", line 377, in run_forever
    rslt = self.__app.exec()
           └ <QIOCPEventLoop running=True closed=False debug=False>
  File "H:\PyProject\TeleTest\script\Lib\site-packages\qasync\__init__.py", line 279, in timerEvent
    handle._run()
    │      └ <function Handle._run at 0x00000216B9A2BC40>
    └ <Handle Task.task_wakeup(<Task finishe...> result=None>)>
  File "D:\Program Files\python11\Lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle Task.task_wakeup(<Task finishe...> result=None>)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle Task.task_wakeup(<Task finishe...> result=None>)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle Task.task_wakeup(<Task finishe...> result=None>)>

  File "H:\PyProject\TeleTest\ui\main_window.py", line 293, in on_login_succeeded
    await vip_checker.start_checking()
          │           └ <function VipChecker.start_checking at 0x00000216BA5C9760>
          └ <utils.vip_checker.VipChecker(0x216cc39eda0) at 0x00000216BA5BB900>

  File "H:\PyProject\TeleTest\utils\vip_checker.py", line 42, in start_checking
    await self._check_vip_status()
          │    └ <function VipChecker._check_vip_status at 0x00000216BA5C9940>
          └ <utils.vip_checker.VipChecker(0x216cc39eda0) at 0x00000216BA5BB900>

> File "H:\PyProject\TeleTest\utils\vip_checker.py", line 76, in _check_vip_status
    if result['code'] == 200 and result['msg']=='验证成功':
       │                         └ {'success': False, 'message': '验证失败'}
       └ {'success': False, 'message': '验证失败'}

KeyError: 'code'
2025-06-27 22:13:10.464 | ERROR    | data.repositories.message_repo:get_task_target_stats:187 - 获取任务1目标统计失败: Cannot switch to a different thread
	Current:  <greenlet.greenlet object at 0x00000216B9FD2D00 (otid=0x000002168E39F1E0) current active started main>
	Expected: <greenlet.greenlet object at 0x00000216BA184D00 (otid=0x00000216BA12B810) suspended active started main>
2025-06-27 22:13:10.471 | ERROR    | ui.views.send_msg_view:load_tasks:50 - 加载任务列表失败: coroutine ignored GeneratorExit
2025-06-27 22:13:29.753 | ERROR    | utils.vip_checker:_check_vip_status:87 - VIP验证结果解析出错: 'code'
Traceback (most recent call last):

  File "H:\PyProject\TeleTest\main.py", line 118, in <module>
    main()
    └ <function main at 0x00000279141FEFC0>

  File "H:\PyProject\TeleTest\main.py", line 109, in main
    event_loop.run_forever()
    │          └ <function _QEventLoop.run_forever at 0x000002797F78EB60>
    └ <QIOCPEventLoop running=True closed=False debug=False>

  File "H:\PyProject\TeleTest\script\Lib\site-packages\qasync\__init__.py", line 377, in run_forever
    rslt = self.__app.exec()
           └ <QIOCPEventLoop running=True closed=False debug=False>
  File "H:\PyProject\TeleTest\script\Lib\site-packages\qasync\__init__.py", line 279, in timerEvent
    handle._run()
    │      └ <function Handle._run at 0x000002797F32C900>
    └ <Handle Task.task_wakeup(<Task finishe...> result=None>)>
  File "D:\Program Files\python11\Lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle Task.task_wakeup(<Task finishe...> result=None>)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle Task.task_wakeup(<Task finishe...> result=None>)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle Task.task_wakeup(<Task finishe...> result=None>)>

  File "H:\PyProject\TeleTest\ui\main_window.py", line 293, in on_login_succeeded
    await vip_checker.start_checking()
          │           └ <function VipChecker.start_checking at 0x0000027901480720>
          └ <utils.vip_checker.VipChecker(0x27910f253d0) at 0x0000027901479C80>

  File "H:\PyProject\TeleTest\utils\vip_checker.py", line 42, in start_checking
    await self._check_vip_status()
          │    └ <function VipChecker._check_vip_status at 0x0000027901480900>
          └ <utils.vip_checker.VipChecker(0x27910f253d0) at 0x0000027901479C80>

> File "H:\PyProject\TeleTest\utils\vip_checker.py", line 76, in _check_vip_status
    if result['code'] == 200 and result['msg']=='验证成功':
       │                         └ {'success': False, 'message': '验证失败'}
       └ {'success': False, 'message': '验证失败'}

KeyError: 'code'
2025-06-27 22:25:24.173 | ERROR    | utils.vip_checker:_check_vip_status:85 - VIP状态检查出现异常: 'message'
Traceback (most recent call last):

  File "D:\Program Files\JetBrains\PyCharm 2025.1.1\plugins\python-ce\helpers\pydev\pydevd.py", line 2270, in <module>
    main()
    └ <function main at 0x00000162B25EC680>

  File "D:\Program Files\JetBrains\PyCharm 2025.1.1\plugins\python-ce\helpers\pydev\pydevd.py", line 2252, in main
    globals = debugger.run(setup['file'], None, None, is_module)
              │        │   │                          └ False
              │        │   └ {'port': 57741, 'vm_type': None, 'client': '127.0.0.1', 'server': False, 'DEBUG_RECORD_SOCKET_READS': False, 'multiproc': Fal...
              │        └ <function PyDB.run at 0x00000162B25EB420>
              └ <__main__.PyDB object at 0x000001629F1FA890>

  File "D:\Program Files\JetBrains\PyCharm 2025.1.1\plugins\python-ce\helpers\pydev\pydevd.py", line 1563, in run
    return self._exec(is_module, entry_point_fn, module_name, file, globals, locals)
           │    │     │          │               │            │     │        └ {'__name__': '__main__', '__doc__': '\nTelegram多账户管理系统 - 主程序入口\n', '__package__': '', '__loader__': <_frozen_importlib_extern...
           │    │     │          │               │            │     └ {'__name__': '__main__', '__doc__': '\nTelegram多账户管理系统 - 主程序入口\n', '__package__': '', '__loader__': <_frozen_importlib_extern...
           │    │     │          │               │            └ 'H:\\PyProject\\TeleTest\\main.py'
           │    │     │          │               └ None
           │    │     │          └ ''
           │    │     └ False
           │    └ <function PyDB._exec at 0x00000162B25EB4C0>
           └ <__main__.PyDB object at 0x000001629F1FA890>

  File "D:\Program Files\JetBrains\PyCharm 2025.1.1\plugins\python-ce\helpers\pydev\pydevd.py", line 1570, in _exec
    pydev_imports.execfile(file, globals, locals)  # execute the script
    │             │        │     │        └ {'__name__': '__main__', '__doc__': '\nTelegram多账户管理系统 - 主程序入口\n', '__package__': '', '__loader__': <_frozen_importlib_extern...
    │             │        │     └ {'__name__': '__main__', '__doc__': '\nTelegram多账户管理系统 - 主程序入口\n', '__package__': '', '__loader__': <_frozen_importlib_extern...
    │             │        └ 'H:\\PyProject\\TeleTest\\main.py'
    │             └ <function execfile at 0x00000162B2209F80>
    └ <module '_pydev_bundle.pydev_imports' from 'D:\\Program Files\\JetBrains\\PyCharm 2025.1.1\\plugins\\python-ce\\helpers\\pyde...

  File "D:\Program Files\JetBrains\PyCharm 2025.1.1\plugins\python-ce\helpers\pydev\_pydev_imps\_pydev_execfile.py", line 18, in execfile
    exec(compile(contents+"\n", file, 'exec'), glob, loc)
                 │              │              │     └ {'__name__': '__main__', '__doc__': '\nTelegram多账户管理系统 - 主程序入口\n', '__package__': '', '__loader__': <_frozen_importlib_extern...
                 │              │              └ {'__name__': '__main__', '__doc__': '\nTelegram多账户管理系统 - 主程序入口\n', '__package__': '', '__loader__': <_frozen_importlib_extern...
                 │              └ 'H:\\PyProject\\TeleTest\\main.py'
                 └ '#!/usr/bin/env python\n# -*- coding: utf-8 -*-\n\n"""\nTelegram多账户管理系统 - 主程序入口\n"""\nimport sys\nimport os\nimport asyncio\n...

  File "H:\PyProject\TeleTest\main.py", line 118, in <module>
    main()
    └ <function main at 0x00000162C6508680>

  File "H:\PyProject\TeleTest\main.py", line 109, in main
    event_loop.run_forever()
    │          └ <function _QEventLoop.run_forever at 0x00000162B29C4900>
    └ <QIOCPEventLoop running=True closed=False debug=False>

  File "H:\PyProject\TeleTest\script\Lib\site-packages\qasync\__init__.py", line 377, in run_forever
    rslt = self.__app.exec()
           └ <QIOCPEventLoop running=True closed=False debug=False>
  File "H:\PyProject\TeleTest\script\Lib\site-packages\qasync\__init__.py", line 279, in timerEvent
    handle._run()
    │      └ <function Handle._run at 0x00000162B24ABC40>
    └ <Handle Task.task_wakeup(<Task finishe...> result=None>)>
  File "D:\Program Files\python11\Lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle Task.task_wakeup(<Task finishe...> result=None>)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle Task.task_wakeup(<Task finishe...> result=None>)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle Task.task_wakeup(<Task finishe...> result=None>)>

  File "H:\PyProject\TeleTest\ui\main_window.py", line 293, in on_login_succeeded
    await vip_checker.start_checking()
          │           └ <function VipChecker.start_checking at 0x00000162B2FE5B20>
          └ <utils.vip_checker.VipChecker(0x162c4cf66d0) at 0x00000162B27B3580>

  File "H:\PyProject\TeleTest\utils\vip_checker.py", line 42, in start_checking
    await self._check_vip_status()
          │    └ <function VipChecker._check_vip_status at 0x00000162B2FE5D00>
          └ <utils.vip_checker.VipChecker(0x162c4cf66d0) at 0x00000162B27B3580>

> File "H:\PyProject\TeleTest\utils\vip_checker.py", line 63, in _check_vip_status
    result = await self._controller.verify_vip()
                   │    │           └ <function AuthController.verify_vip at 0x00000162B2F653A0>
                   │    └ <app.controllers.auth_controller.AuthController(0x162c4e60640) at 0x00000162C65ABC00>
                   └ <utils.vip_checker.VipChecker(0x162c4cf66d0) at 0x00000162B27B3580>

  File "H:\PyProject\TeleTest\app\controllers\auth_controller.py", line 190, in verify_vip
    return await self._auth_service.verify_vip()
                 │    │             └ <function AuthService.verify_vip at 0x00000162B2F64C20>
                 │    └ <app.services.auth_service.AuthService(0x162c4e60900) at 0x00000162C64FA480>
                 └ <app.controllers.auth_controller.AuthController(0x162c4e60640) at 0x00000162C65ABC00>

  File "H:\PyProject\TeleTest\app\services\auth_service.py", line 123, in verify_vip
    return await self._service.verify_vip()
                 │    │        └ <function ApiService.verify_vip at 0x00000162B2F64040>
                 │    └ <core.auth.api_service.ApiService object at 0x00000162B2F60C50>
                 └ <app.services.auth_service.AuthService(0x162c4e60900) at 0x00000162C64FA480>

  File "H:\PyProject\TeleTest\core\auth\api_service.py", line 439, in verify_vip
    return {"success": True, "data": result['time'],'message':result['message']}
                                     │                        └ {'code': 200, 'msg': '验证成功', 'time': 1751034323, 'sign': '89e916c3a27f51a8cfbe9e1920a3539d', 'run': {'ms': 3.95, 'ram': '125....
                                     └ {'code': 200, 'msg': '验证成功', 'time': 1751034323, 'sign': '89e916c3a27f51a8cfbe9e1920a3539d', 'run': {'ms': 3.95, 'ram': '125....

KeyError: 'message'
2025-06-27 22:26:12.809 | ERROR    | data.repositories.proxy_repo:find_by_id:134 - 根据ID查询代理出错: coroutine ignored GeneratorExit
Traceback (most recent call last):

  File "H:\PyProject\TeleTest\utils\vip_checker.py", line 63, in _check_vip_status
    result = await self._controller.verify_vip()
                   │    │           └ <function AuthController.verify_vip at 0x00000162B2F653A0>
                   │    └ <app.controllers.auth_controller.AuthController(0x162c4e60640) at 0x00000162C65ABC00>
                   └ <utils.vip_checker.VipChecker(0x162c4cf66d0) at 0x00000162B27B3580>

  File "H:\PyProject\TeleTest\app\controllers\auth_controller.py", line 190, in verify_vip
    return await self._auth_service.verify_vip()
                 │    │             └ <function AuthService.verify_vip at 0x00000162B2F64C20>
                 │    └ <app.services.auth_service.AuthService(0x162c4e60900) at 0x00000162C64FA480>
                 └ <app.controllers.auth_controller.AuthController(0x162c4e60640) at 0x00000162C65ABC00>

  File "H:\PyProject\TeleTest\app\services\auth_service.py", line 123, in verify_vip
    return await self._service.verify_vip()
                 │    │        └ <function ApiService.verify_vip at 0x00000162B2F64040>
                 │    └ <core.auth.api_service.ApiService object at 0x00000162B2F60C50>
                 └ <app.services.auth_service.AuthService(0x162c4e60900) at 0x00000162C64FA480>

  File "H:\PyProject\TeleTest\core\auth\api_service.py", line 439, in verify_vip
    return {"success": True, "data": result['time'],'message':result['message']}
                                     │                        └ {'code': 200, 'msg': '验证成功', 'time': 1751034323, 'sign': '89e916c3a27f51a8cfbe9e1920a3539d', 'run': {'ms': 3.95, 'ram': '125....
                                     └ {'code': 200, 'msg': '验证成功', 'time': 1751034323, 'sign': '89e916c3a27f51a8cfbe9e1920a3539d', 'run': {'ms': 3.95, 'ram': '125....

KeyError: 'message'


During handling of the above exception, another exception occurred:


Traceback (most recent call last):

  File "D:\Program Files\python11\Lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle <TaskStepMethWrapper object at 0x00000162C6592110>()>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle <TaskStepMethWrapper object at 0x00000162C6592110>()>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle <TaskStepMethWrapper object at 0x00000162C6592110>()>

RuntimeError: Cannot enter into task <Task pending name='Task-623' coro=<AuthService._on_heartbeat_timer() running at H:\PyProject\TeleTest\app\services\auth_service.py:85> cb=[asyncSlot.<locals>._error_handler() at H:\PyProject\TeleTest\script\Lib\site-packages\qasync\__init__.py:778]> while another task <Task pending name='Task-27' coro=<MainWindow.on_login_succeeded() running at H:\PyProject\TeleTest\ui\main_window.py:293> cb=[asyncSlot.<locals>._error_handler() at H:\PyProject\TeleTest\script\Lib\site-packages\qasync\__init__.py:778]> is being executed.


During handling of the above exception, another exception occurred:


Traceback (most recent call last):

  File "D:\Program Files\JetBrains\PyCharm 2025.1.1\plugins\python-ce\helpers\pydev\pydevd.py", line 2270, in <module>
    main()
    └ <function main at 0x00000162B25EC680>

  File "D:\Program Files\JetBrains\PyCharm 2025.1.1\plugins\python-ce\helpers\pydev\pydevd.py", line 2252, in main
    globals = debugger.run(setup['file'], None, None, is_module)
              │        │   │                          └ False
              │        │   └ {'port': 57741, 'vm_type': None, 'client': '127.0.0.1', 'server': False, 'DEBUG_RECORD_SOCKET_READS': False, 'multiproc': Fal...
              │        └ <function PyDB.run at 0x00000162B25EB420>
              └ <__main__.PyDB object at 0x000001629F1FA890>

  File "D:\Program Files\JetBrains\PyCharm 2025.1.1\plugins\python-ce\helpers\pydev\pydevd.py", line 1563, in run
    return self._exec(is_module, entry_point_fn, module_name, file, globals, locals)
           │    │     │          │               │            │     │        └ {'__name__': '__main__', '__doc__': '\nTelegram多账户管理系统 - 主程序入口\n', '__package__': '', '__loader__': <_frozen_importlib_extern...
           │    │     │          │               │            │     └ {'__name__': '__main__', '__doc__': '\nTelegram多账户管理系统 - 主程序入口\n', '__package__': '', '__loader__': <_frozen_importlib_extern...
           │    │     │          │               │            └ 'H:\\PyProject\\TeleTest\\main.py'
           │    │     │          │               └ None
           │    │     │          └ ''
           │    │     └ False
           │    └ <function PyDB._exec at 0x00000162B25EB4C0>
           └ <__main__.PyDB object at 0x000001629F1FA890>

  File "D:\Program Files\JetBrains\PyCharm 2025.1.1\plugins\python-ce\helpers\pydev\pydevd.py", line 1570, in _exec
    pydev_imports.execfile(file, globals, locals)  # execute the script
    │             │        │     │        └ {'__name__': '__main__', '__doc__': '\nTelegram多账户管理系统 - 主程序入口\n', '__package__': '', '__loader__': <_frozen_importlib_extern...
    │             │        │     └ {'__name__': '__main__', '__doc__': '\nTelegram多账户管理系统 - 主程序入口\n', '__package__': '', '__loader__': <_frozen_importlib_extern...
    │             │        └ 'H:\\PyProject\\TeleTest\\main.py'
    │             └ <function execfile at 0x00000162B2209F80>
    └ <module '_pydev_bundle.pydev_imports' from 'D:\\Program Files\\JetBrains\\PyCharm 2025.1.1\\plugins\\python-ce\\helpers\\pyde...

  File "D:\Program Files\JetBrains\PyCharm 2025.1.1\plugins\python-ce\helpers\pydev\_pydev_imps\_pydev_execfile.py", line 18, in execfile
    exec(compile(contents+"\n", file, 'exec'), glob, loc)
                 │              │              │     └ {'__name__': '__main__', '__doc__': '\nTelegram多账户管理系统 - 主程序入口\n', '__package__': '', '__loader__': <_frozen_importlib_extern...
                 │              │              └ {'__name__': '__main__', '__doc__': '\nTelegram多账户管理系统 - 主程序入口\n', '__package__': '', '__loader__': <_frozen_importlib_extern...
                 │              └ 'H:\\PyProject\\TeleTest\\main.py'
                 └ '#!/usr/bin/env python\n# -*- coding: utf-8 -*-\n\n"""\nTelegram多账户管理系统 - 主程序入口\n"""\nimport sys\nimport os\nimport asyncio\n...

  File "H:\PyProject\TeleTest\main.py", line 118, in <module>
    main()
    └ <function main at 0x00000162C6508680>

  File "H:\PyProject\TeleTest\main.py", line 109, in main
    event_loop.run_forever()
    │          └ <function _QEventLoop.run_forever at 0x00000162B29C4900>
    └ <QIOCPEventLoop running=True closed=False debug=False>

  File "H:\PyProject\TeleTest\script\Lib\site-packages\qasync\__init__.py", line 377, in run_forever
    rslt = self.__app.exec()
           └ <QIOCPEventLoop running=True closed=False debug=False>
  File "H:\PyProject\TeleTest\script\Lib\site-packages\qasync\__init__.py", line 279, in timerEvent
    handle._run()
    │      └ <function Handle._run at 0x00000162B24ABC40>
    └ <Handle Task.task_wakeup(<Task finishe...> result=None>)>
  File "D:\Program Files\python11\Lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle Task.task_wakeup(<Task finishe...> result=None>)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle Task.task_wakeup(<Task finishe...> result=None>)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle Task.task_wakeup(<Task finishe...> result=None>)>

  File "H:\PyProject\TeleTest\ui\main_window.py", line 293, in on_login_succeeded
    await vip_checker.start_checking()
          │           └ <function VipChecker.start_checking at 0x00000162B2FE5B20>
          └ <utils.vip_checker.VipChecker(0x162c4cf66d0) at 0x00000162B27B3580>

  File "H:\PyProject\TeleTest\utils\vip_checker.py", line 42, in start_checking
    await self._check_vip_status()
          │    └ <function VipChecker._check_vip_status at 0x00000162B2FE5D00>
          └ <utils.vip_checker.VipChecker(0x162c4cf66d0) at 0x00000162B27B3580>

  File "H:\PyProject\TeleTest\utils\vip_checker.py", line 88, in _check_vip_status
    self.vip_expired.emit(self._expire_message)
    │    │           │    │    └ "VIP验证出错: 'message'"
    │    │           │    └ <utils.vip_checker.VipChecker(0x162c4cf66d0) at 0x00000162B27B3580>
    │    │           └ <method 'emit' of 'PySide6.QtCore.SignalInstance' objects>
    │    └ <PySide6.QtCore.SignalInstance vip_expired(QString) at 0x00000162C5F8FF30>
    └ <utils.vip_checker.VipChecker(0x162c4cf66d0) at 0x00000162B27B3580>

  File "H:\PyProject\TeleTest\ui\main_window.py", line 93, in on_vip_expired
    ).exec()

  File "H:\PyProject\TeleTest\script\Lib\site-packages\qasync\__init__.py", line 279, in timerEvent
    handle._run()
    │      └ <function Handle._run at 0x00000162B24ABC40>
    └ <Handle <TaskStepMethWrapper object at 0x00000162C6592110>()>
  File "D:\Program Files\python11\Lib\asyncio\events.py", line 94, in _run
    self._loop.call_exception_handler(context)
    │    │                            └ {'message': 'Exception in callback <TaskStepMethWrapper object at 0x00000162C6592110>()', 'exception': RuntimeError("Cannot e...
    │    └ <member '_loop' of 'Handle' objects>
    └ <Handle <TaskStepMethWrapper object at 0x00000162C6592110>()>
  File "H:\PyProject\TeleTest\script\Lib\site-packages\qasync\__init__.py", line 678, in call_exception_handler
    self.default_exception_handler(context)
    │    │                         └ {'message': 'Exception in callback <TaskStepMethWrapper object at 0x00000162C6592110>()', 'exception': RuntimeError("Cannot e...
    │    └ <function _QEventLoop.default_exception_handler at 0x00000162B29C5580>
    └ <QIOCPEventLoop running=True closed=False debug=False>
  File "H:\PyProject\TeleTest\script\Lib\site-packages\qasync\__init__.py", line 673, in default_exception_handler
    self.__log_error("\n".join(log_lines), exc_info=exc_info)
    │                          │                    └ (<class 'RuntimeError'>, RuntimeError("Cannot enter into task <Task pending name='Task-623' coro=<AuthService._on_heartbeat_t...
    │                          └ ['Exception in callback <TaskStepMethWrapper object at 0x00000162C6592110>()', 'handle: <Handle <TaskStepMethWrapper object a...
    └ <QIOCPEventLoop running=True closed=False debug=False>
  File "H:\PyProject\TeleTest\script\Lib\site-packages\qasync\__init__.py", line 736, in __log_error
    cls._logger.error(*args, **kwds)
    │   │       │      │       └ {'exc_info': (<class 'RuntimeError'>, RuntimeError("Cannot enter into task <Task pending name='Task-623' coro=<AuthService._o...
    │   │       │      └ ('Exception in callback <TaskStepMethWrapper object at 0x00000162C6592110>()\nhandle: <Handle <TaskStepMethWrapper object at ...
    │   │       └ <function Logger.error at 0x00000162B2389080>
    │   └ <Logger qasync._QEventLoop (WARNING)>
    └ <class 'qasync.QIOCPEventLoop'>
  File "D:\Program Files\python11\Lib\logging\__init__.py", line 1518, in error
    self._log(ERROR, msg, args, **kwargs)
    │    │    │      │    │       └ {'exc_info': (<class 'RuntimeError'>, RuntimeError("Cannot enter into task <Task pending name='Task-623' coro=<AuthService._o...
    │    │    │      │    └ ()
    │    │    │      └ 'Exception in callback <TaskStepMethWrapper object at 0x00000162C6592110>()\nhandle: <Handle <TaskStepMethWrapper object at 0...
    │    │    └ 40
    │    └ <function Logger._log at 0x00000162B23894E0>
    └ <Logger qasync._QEventLoop (WARNING)>
  File "D:\Program Files\python11\Lib\logging\__init__.py", line 1634, in _log
    self.handle(record)
    │    │      └ <LogRecord: qasync._QEventLoop, 40, H:\PyProject\TeleTest\script\Lib\site-packages\qasync\__init__.py, 736, "Exception in cal...
    │    └ <function Logger.handle at 0x00000162B2389580>
    └ <Logger qasync._QEventLoop (WARNING)>
  File "D:\Program Files\python11\Lib\logging\__init__.py", line 1644, in handle
    self.callHandlers(record)
    │    │            └ <LogRecord: qasync._QEventLoop, 40, H:\PyProject\TeleTest\script\Lib\site-packages\qasync\__init__.py, 736, "Exception in cal...
    │    └ <function Logger.callHandlers at 0x00000162B2389800>
    └ <Logger qasync._QEventLoop (WARNING)>
  File "D:\Program Files\python11\Lib\logging\__init__.py", line 1714, in callHandlers
    lastResort.handle(record)
    │          │      └ <LogRecord: qasync._QEventLoop, 40, H:\PyProject\TeleTest\script\Lib\site-packages\qasync\__init__.py, 736, "Exception in cal...
    │          └ <function Handler.handle at 0x00000162B23476A0>
    └ <_StderrHandler <stderr> (WARNING)>
  File "D:\Program Files\python11\Lib\logging\__init__.py", line 978, in handle
    self.emit(record)
    │    │    └ <LogRecord: qasync._QEventLoop, 40, H:\PyProject\TeleTest\script\Lib\site-packages\qasync\__init__.py, 736, "Exception in cal...
    │    └ <function StreamHandler.emit at 0x00000162B2347C40>
    └ <_StderrHandler <stderr> (WARNING)>
  File "D:\Program Files\python11\Lib\logging\__init__.py", line 1110, in emit
    msg = self.format(record)
          │    │      └ <LogRecord: qasync._QEventLoop, 40, H:\PyProject\TeleTest\script\Lib\site-packages\qasync\__init__.py, 736, "Exception in cal...
          │    └ <function Handler.format at 0x00000162B2347560>
          └ <_StderrHandler <stderr> (WARNING)>
  File "D:\Program Files\python11\Lib\logging\__init__.py", line 953, in format
    return fmt.format(record)
           │   │      └ <LogRecord: qasync._QEventLoop, 40, H:\PyProject\TeleTest\script\Lib\site-packages\qasync\__init__.py, 736, "Exception in cal...
           │   └ <function Formatter.format at 0x00000162B23467A0>
           └ <logging.Formatter object at 0x00000162B23732D0>
  File "D:\Program Files\python11\Lib\logging\__init__.py", line 695, in format
    record.exc_text = self.formatException(record.exc_info)
    │      │          │    │               │      └ (<class 'RuntimeError'>, RuntimeError("Cannot enter into task <Task pending name='Task-623' coro=<AuthService._on_heartbeat_t...
    │      │          │    │               └ <LogRecord: qasync._QEventLoop, 40, H:\PyProject\TeleTest\script\Lib\site-packages\qasync\__init__.py, 736, "Exception in cal...
    │      │          │    └ <function Formatter.formatException at 0x00000162B2346520>
    │      │          └ <logging.Formatter object at 0x00000162B23732D0>
    │      └ None
    └ <LogRecord: qasync._QEventLoop, 40, H:\PyProject\TeleTest\script\Lib\site-packages\qasync\__init__.py, 736, "Exception in cal...
  File "D:\Program Files\python11\Lib\logging\__init__.py", line 645, in formatException
    traceback.print_exception(ei[0], ei[1], tb, None, sio)
    │         │               │      │      │         └ <_io.StringIO object at 0x00000162849369E0>
    │         │               │      │      └ <traceback object at 0x0000016282CD8B80>
    │         │               │      └ (<class 'RuntimeError'>, RuntimeError("Cannot enter into task <Task pending name='Task-623' coro=<AuthService._on_heartbeat_t...
    │         │               └ (<class 'RuntimeError'>, RuntimeError("Cannot enter into task <Task pending name='Task-623' coro=<AuthService._on_heartbeat_t...
    │         └ <function print_exception at 0x000001629F4E82C0>
    └ <module 'traceback' from 'D:\\Program Files\\python11\\Lib\\traceback.py'>
  File "D:\Program Files\python11\Lib\traceback.py", line 125, in print_exception
    te.print(file=file, chain=chain)
    │  │          │           └ True
    │  │          └ <_io.StringIO object at 0x00000162849369E0>
    │  └ <function TracebackException.print at 0x000001629F4EA200>
    └ <traceback.TracebackException object at 0x0000016281B35FD0>
  File "D:\Program Files\python11\Lib\traceback.py", line 979, in print
    for line in self.format(chain=chain):
        │       │    │            └ True
        │       │    └ <function TracebackException.format at 0x000001629F4EA160>
        │       └ <traceback.TracebackException object at 0x0000016281B35FD0>
        └ '  File "H:\\PyProject\\TeleTest\\utils\\vip_checker.py", line 63, in _check_vip_status\n    result = await self._controller....
  File "D:\Program Files\python11\Lib\traceback.py", line 916, in format
    yield from _ctx.emit(exc.stack.format())
               │    │    │   │     └ <function StackSummary.format at 0x000001629F4E94E0>
               │    │    │   └ [<FrameSummary file H:\PyProject\TeleTest\utils\vip_checker.py, line 63 in _check_vip_status>, <FrameSummary file H:\PyProjec...
               │    │    └ <traceback.TracebackException object at 0x0000016285918A50>
               │    └ <function _ExceptionPrintContext.emit at 0x000001629F4E9C60>
               └ <traceback._ExceptionPrintContext object at 0x0000016285918B50>

  File "D:\Program Files\JetBrains\PyCharm 2025.1.1\plugins\python-ce\helpers\pydev\_pydevd_bundle\pydevd_collect_try_except_info.py", line 163, in collect_return_info
    for instruction in _iter_instructions(co):
                       │                  └ <code object format at 0x000001629F406740, file "D:\Program Files\python11\Lib\traceback.py", line 874>
                       └ <function _iter_instructions at 0x00000162B2209760>

  File "D:\Program Files\JetBrains\PyCharm 2025.1.1\plugins\python-ce\helpers\pydev\_pydevd_bundle\pydevd_collect_try_except_info.py", line 137, in _iter_instructions
    iter_in = list(iter_in)
                   └ Bytecode(<code object format at 0x000001629F406740, file "D:\Program Files\python11\Lib\traceback.py", line 874>)

  File "D:\Program Files\python11\Lib\dis.py", line 451, in _get_instructions_bytes
    positions = Positions(*next(co_positions, ()))
                │               └ <positions_iterator object at 0x00000162847DA650>
                └ <class 'dis.Positions'>
  File "<string>", line 1, in <lambda>

> File "H:\PyProject\TeleTest\data\repositories\proxy_repo.py", line 129, in find_by_id
    result = await self.session.execute(
                   │    │       └ <function AsyncSession.execute at 0x00000162B383B420>
                   │    └ <sqlalchemy.ext.asyncio.session.AsyncSession object at 0x00000162848D4A50>
                   └ <data.repositories.proxy_repo.ProxyRepository object at 0x0000016284850C50>

  File "H:\PyProject\TeleTest\script\Lib\site-packages\sqlalchemy\ext\asyncio\session.py", line 463, in execute
    result = await greenlet_spawn(
                   └ <function greenlet_spawn at 0x00000162B2BA4FE0>

RuntimeError: coroutine ignored GeneratorExit
2025-06-27 22:26:12.833 | ERROR    | app.services.account_service:batch_auto_login:1228 - 服务层：账户 +*********** 查询代理ID 1 时出错: (sqlite3.OperationalError) no active connection
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-27 22:26:12.842 | ERROR    | app.controllers.account_controller:auto_login_accounts:590 - 批量自动登录异常: coroutine ignored GeneratorExit
2025-06-27 22:26:38.126 | ERROR    | utils.vip_checker:_check_vip_status:87 - VIP状态检查出现异常: 'message'
Traceback (most recent call last):

  File "D:\Program Files\JetBrains\PyCharm 2025.1.1\plugins\python-ce\helpers\pydev\pydevd.py", line 2270, in <module>
    main()
    └ <function main at 0x000001E3EA22C680>

  File "D:\Program Files\JetBrains\PyCharm 2025.1.1\plugins\python-ce\helpers\pydev\pydevd.py", line 2252, in main
    globals = debugger.run(setup['file'], None, None, is_module)
              │        │   │                          └ False
              │        │   └ {'port': 57877, 'vm_type': None, 'client': '127.0.0.1', 'server': False, 'DEBUG_RECORD_SOCKET_READS': False, 'multiproc': Fal...
              │        └ <function PyDB.run at 0x000001E3EA22B420>
              └ <__main__.PyDB object at 0x000001E3EA12DB90>

  File "D:\Program Files\JetBrains\PyCharm 2025.1.1\plugins\python-ce\helpers\pydev\pydevd.py", line 1563, in run
    return self._exec(is_module, entry_point_fn, module_name, file, globals, locals)
           │    │     │          │               │            │     │        └ {'__name__': '__main__', '__doc__': '\nTelegram多账户管理系统 - 主程序入口\n', '__package__': '', '__loader__': <_frozen_importlib_extern...
           │    │     │          │               │            │     └ {'__name__': '__main__', '__doc__': '\nTelegram多账户管理系统 - 主程序入口\n', '__package__': '', '__loader__': <_frozen_importlib_extern...
           │    │     │          │               │            └ 'H:\\PyProject\\TeleTest\\main.py'
           │    │     │          │               └ None
           │    │     │          └ ''
           │    │     └ False
           │    └ <function PyDB._exec at 0x000001E3EA22B4C0>
           └ <__main__.PyDB object at 0x000001E3EA12DB90>

  File "D:\Program Files\JetBrains\PyCharm 2025.1.1\plugins\python-ce\helpers\pydev\pydevd.py", line 1570, in _exec
    pydev_imports.execfile(file, globals, locals)  # execute the script
    │             │        │     │        └ {'__name__': '__main__', '__doc__': '\nTelegram多账户管理系统 - 主程序入口\n', '__package__': '', '__loader__': <_frozen_importlib_extern...
    │             │        │     └ {'__name__': '__main__', '__doc__': '\nTelegram多账户管理系统 - 主程序入口\n', '__package__': '', '__loader__': <_frozen_importlib_extern...
    │             │        └ 'H:\\PyProject\\TeleTest\\main.py'
    │             └ <function execfile at 0x000001E3E9E49F80>
    └ <module '_pydev_bundle.pydev_imports' from 'D:\\Program Files\\JetBrains\\PyCharm 2025.1.1\\plugins\\python-ce\\helpers\\pyde...

  File "D:\Program Files\JetBrains\PyCharm 2025.1.1\plugins\python-ce\helpers\pydev\_pydev_imps\_pydev_execfile.py", line 18, in execfile
    exec(compile(contents+"\n", file, 'exec'), glob, loc)
                 │              │              │     └ {'__name__': '__main__', '__doc__': '\nTelegram多账户管理系统 - 主程序入口\n', '__package__': '', '__loader__': <_frozen_importlib_extern...
                 │              │              └ {'__name__': '__main__', '__doc__': '\nTelegram多账户管理系统 - 主程序入口\n', '__package__': '', '__loader__': <_frozen_importlib_extern...
                 │              └ 'H:\\PyProject\\TeleTest\\main.py'
                 └ '#!/usr/bin/env python\n# -*- coding: utf-8 -*-\n\n"""\nTelegram多账户管理系统 - 主程序入口\n"""\nimport sys\nimport os\nimport asyncio\n...

  File "H:\PyProject\TeleTest\main.py", line 118, in <module>
    main()
    └ <function main at 0x000001E3FE1B7EC0>

  File "H:\PyProject\TeleTest\main.py", line 109, in main
    event_loop.run_forever()
    │          └ <function _QEventLoop.run_forever at 0x000001E3EA5E4900>
    └ <QIOCPEventLoop running=True closed=False debug=False>

  File "H:\PyProject\TeleTest\script\Lib\site-packages\qasync\__init__.py", line 377, in run_forever
    rslt = self.__app.exec()
           └ <QIOCPEventLoop running=True closed=False debug=False>
  File "H:\PyProject\TeleTest\script\Lib\site-packages\qasync\__init__.py", line 279, in timerEvent
    handle._run()
    │      └ <function Handle._run at 0x000001E3EA0EBC40>
    └ <Handle Task.task_wakeup(<Task finishe...> result=None>)>
  File "D:\Program Files\python11\Lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle Task.task_wakeup(<Task finishe...> result=None>)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle Task.task_wakeup(<Task finishe...> result=None>)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle Task.task_wakeup(<Task finishe...> result=None>)>

  File "H:\PyProject\TeleTest\ui\main_window.py", line 293, in on_login_succeeded
    await vip_checker.start_checking()
          │           └ <function VipChecker.start_checking at 0x000001E3EAC89800>
          └ <utils.vip_checker.VipChecker(0x1e3fcb9d510) at 0x000001E3EAC7B4C0>

  File "H:\PyProject\TeleTest\utils\vip_checker.py", line 42, in start_checking
    await self._check_vip_status()
          │    └ <function VipChecker._check_vip_status at 0x000001E3EAC899E0>
          └ <utils.vip_checker.VipChecker(0x1e3fcb9d510) at 0x000001E3EAC7B4C0>

> File "H:\PyProject\TeleTest\utils\vip_checker.py", line 64, in _check_vip_status
    result = await self._controller.verify_vip()
                   │    │           └ <function AuthController.verify_vip at 0x000001E3EAC11080>
                   │    └ <app.controllers.auth_controller.AuthController(0x1e3fcf29e50) at 0x000001E3FE74B040>
                   └ <utils.vip_checker.VipChecker(0x1e3fcb9d510) at 0x000001E3EAC7B4C0>

  File "H:\PyProject\TeleTest\app\controllers\auth_controller.py", line 190, in verify_vip
    return await self._auth_service.verify_vip()
                 │    │             └ <function AuthService.verify_vip at 0x000001E3EAC10900>
                 │    └ <app.services.auth_service.AuthService(0x1e3fcf29c70) at 0x000001E3FE1C7B00>
                 └ <app.controllers.auth_controller.AuthController(0x1e3fcf29e50) at 0x000001E3FE74B040>

  File "H:\PyProject\TeleTest\app\services\auth_service.py", line 123, in verify_vip
    return await self._service.verify_vip()
                 │    │        └ <function ApiService.verify_vip at 0x000001E3EAC07CE0>
                 │    └ <core.auth.api_service.ApiService object at 0x000001E3EAC0DF90>
                 └ <app.services.auth_service.AuthService(0x1e3fcf29c70) at 0x000001E3FE1C7B00>

  File "H:\PyProject\TeleTest\core\auth\api_service.py", line 439, in verify_vip
    return {"success": True, "data": result['time'],'message':result['message']}
                                     │                        └ {'code': 200, 'msg': '验证成功', 'time': 1751034397, 'sign': 'c9b998b664052a3713417692f4fe3fe0', 'run': {'ms': 2.73, 'ram': '125....
                                     └ {'code': 200, 'msg': '验证成功', 'time': 1751034397, 'sign': 'c9b998b664052a3713417692f4fe3fe0', 'run': {'ms': 2.73, 'ram': '125....

KeyError: 'message'
2025-06-27 22:50:30.373 | ERROR    | utils.vip_checker:_check_vip_status:82 - VIP验证失败: 验证失败
2025-06-27 22:50:34.307 | ERROR    | ui.main_window:on_login_succeeded:309 - 检查VIP状态时出错: 'bool' object has no attribute 'get'
2025-06-27 22:51:29.149 | ERROR    | data.repositories.message_repo:get_task_target_stats:187 - 获取任务6目标统计失败: coroutine ignored GeneratorExit
2025-06-27 22:51:29.149 | ERROR    | ui.views.send_msg_view:load_tasks:50 - 加载任务列表失败: (sqlite3.OperationalError) no active connection
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-27 22:52:08.293 | ERROR    | utils.vip_checker:_check_vip_status:82 - VIP验证失败: 验证失败
2025-06-27 22:52:37.759 | ERROR    | ui.main_window:on_login_succeeded:310 - 检查VIP状态时出错: 'bool' object has no attribute 'get'
2025-06-27 22:53:07.031 | ERROR    | data.repositories.message_repo:get_task_target_stats:187 - 获取任务6目标统计失败: coroutine ignored GeneratorExit
2025-06-27 22:53:07.032 | ERROR    | ui.views.send_msg_view:load_tasks:50 - 加载任务列表失败: (sqlite3.OperationalError) no active connection
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-27 22:53:27.745 | ERROR    | utils.vip_checker:_check_vip_status:82 - VIP验证失败: 验证失败
2025-06-27 22:53:57.843 | ERROR    | data.repositories.message_repo:get_task_target_stats:187 - 获取任务6目标统计失败: coroutine ignored GeneratorExit
2025-06-27 22:53:57.844 | ERROR    | ui.views.send_msg_view:load_tasks:50 - 加载任务列表失败: (sqlite3.OperationalError) no active connection
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-27 22:55:41.723 | ERROR    | utils.vip_checker:_check_vip_status:81 - VIP验证失败: 验证失败
2025-06-27 22:56:33.648 | ERROR    | data.repositories.message_repo:get_task_target_stats:187 - 获取任务6目标统计失败: coroutine ignored GeneratorExit
2025-06-27 22:56:33.649 | ERROR    | ui.views.send_msg_view:load_tasks:50 - 加载任务列表失败: (sqlite3.OperationalError) no active connection
(Background on this error at: https://sqlalche.me/e/20/e3q8)
