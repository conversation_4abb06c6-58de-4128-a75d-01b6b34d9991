2025-07-28 10:31:27.978 | CRITICAL | __main__:main:112 - 程序启动失败: No module named 'aiosqlite'
2025-07-28 10:45:15.534 | ERROR    | data.repositories.account_repo:get_all_accounts:353 - 获取所有账户失败: (sqlite3.OperationalError) no such column: accounts.login_status
[SQL: SELECT accounts.id, accounts.phone, accounts.session_file, accounts.first_name, accounts.last_name, accounts.username, accounts.bio, accounts.profile_photo, accounts.is_active, accounts.is_connected, accounts.login_status, accounts.has_2fa, accounts.last_connected, accounts.last_active, accounts.created_at, accounts.updated_at, accounts.proxy_id, accounts.proxy_type, accounts.group_id 
FROM accounts]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-28 10:45:15.560 | ERROR    | data.repositories.account_repo:get_all_accounts:353 - 获取所有账户失败: (sqlite3.OperationalError) no such column: accounts.login_status
[SQL: SELECT accounts.id, accounts.phone, accounts.session_file, accounts.first_name, accounts.last_name, accounts.username, accounts.bio, accounts.profile_photo, accounts.is_active, accounts.is_connected, accounts.login_status, accounts.has_2fa, accounts.last_connected, accounts.last_active, accounts.created_at, accounts.updated_at, accounts.proxy_id, accounts.proxy_type, accounts.group_id 
FROM accounts]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-28 10:48:03.193 | ERROR    | core.telegram.client_manager:_connect_client:254 - 连接时发生未知错误: database is locked
2025-07-28 10:48:08.729 | ERROR    | core.telegram.client_manager:_connect_client:254 - 连接时发生未知错误: database is locked
2025-07-28 10:48:14.266 | ERROR    | core.telegram.client_manager:_create_and_connect_client:286 - 创建并连接客户端失败: H:\PyProject\TeleTest-bak\APPDATA\sessions\+***********, 错误: database is locked
2025-07-28 10:48:19.823 | ERROR    | core.telegram.client_manager:_create_and_connect_client:286 - 创建并连接客户端失败: H:\PyProject\TeleTest-bak\APPDATA\sessions\+***********, 错误: database is locked
