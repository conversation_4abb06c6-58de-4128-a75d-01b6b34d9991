# 多用户管理重构指南

## 重构概述

基于新任务执行器的多用户管理系统重构，实现了：

1. **统一的用户状态管理**
2. **高效的客户端池管理**
3. **完整的操作历史记录**
4. **任务化的用户操作**
5. **实时状态监控**

## 架构对比

### 重构前（原有架构）
```
ClientManager (客户端管理器)
├── 直接管理TelegramClient实例
├── 同步操作和信号量控制
├── 分散的状态管理
└── 简单的错误处理

UserManager (用户管理器)
├── 依赖ClientManager
├── 直接调用Telegram API
├── 缺乏操作历史
└── 有限的并发控制
```

### 重构后（新架构）
```
NewUserManager (新用户管理器)
├── UserStateManager (用户状态管理器)
│   ├── 用户信息注册表
│   ├── 状态跟踪和变更通知
│   └── 操作历史记录
├── ClientPool (客户端池)
│   ├── 客户端生命周期管理
│   ├── 连接池优化
│   └── 自动重连机制
└── TaskService (任务管理服务)
    ├── 任务化的用户操作
    ├── 并发控制和调度
    └── 错误处理和重试
```

## 核心组件说明

### 1. UserStateManager (用户状态管理器)

**功能：**
- 用户信息注册和管理
- 状态跟踪（在线、离线、登录中等）
- 操作历史记录
- 活跃任务管理

**使用示例：**
```python
from core.telegram.user_state_manager import user_state_manager, UserStatus

# 注册用户
user_info = user_state_manager.register_user("***********", proxy=proxy_config)

# 获取用户信息
user = user_state_manager.get_user("***********")

# 获取在线用户
online_users = user_state_manager.get_users_by_status(UserStatus.ONLINE)

# 获取操作历史
history = user_state_manager.get_operation_history("***********", limit=50)
```

### 2. ClientPool (客户端池)

**功能：**
- 客户端创建和管理
- 连接池优化
- 自动健康检查
- 资源清理

**使用示例：**
```python
from core.telegram.client_pool import client_pool

# 获取客户端（自动创建）
client = await client_pool.get_client("***********", auto_create=True, proxy=proxy)

# 断开客户端
await client_pool.disconnect_client("***********", "手动断开")

# 获取客户端信息
info = client_pool.get_client_info("***********")
```

### 3. NewUserManager (新用户管理器)

**功能：**
- 统一的用户操作接口
- 任务化的用户管理
- 批量操作支持
- 实时状态通知

**使用示例：**
```python
from core.telegram.new_user_manager import new_user_manager

# 用户登录
task_id = await new_user_manager.start_login("***********", proxy=proxy_config)

# 提交验证码
task_id = await new_user_manager.submit_verification_code("***********", "123456")

# 批量登录
accounts = [
    {"phone": "***********", "proxy": proxy1},
    {"phone": "***********", "proxy": proxy2}
]
task_id = await new_user_manager.batch_login(accounts, max_concurrent=3)

# 获取用户统计
stats = new_user_manager.get_user_statistics()
```

## 迁移指南

### 1. 替换ClientManager调用

**原有代码：**
```python
# 旧的ClientManager使用方式
from core.telegram.client_manager import client_manager

# 登录用户
success, message = await client_manager.login_user(phone, proxy)

# 获取客户端
client = client_manager.get_client(phone)
```

**新代码：**
```python
# 新的NewUserManager使用方式
from core.telegram.new_user_manager import new_user_manager

# 登录用户（异步任务）
task_id = await new_user_manager.start_login(phone, proxy)

# 获取客户端
from core.telegram.client_pool import client_pool
client = await client_pool.get_client(phone)
```

### 2. 替换UserManager调用

**原有代码：**
```python
# 旧的UserManager使用方式
from core.telegram.user_manager import UserManager

user_manager = UserManager(client_manager)
success, user_info = await user_manager.get_user_info(phone)
```

**新代码：**
```python
# 新的任务化方式
from core.telegram.new_user_manager import new_user_manager

# 刷新用户信息（异步任务）
task_id = await new_user_manager.refresh_user_info(phone)

# 获取用户信息（同步）
user_info = new_user_manager.get_user(phone)
```

### 3. 信号连接更新

**原有代码：**
```python
# 连接ClientManager信号
client_manager.notify.connect(self.on_notify)
```

**新代码：**
```python
# 连接NewUserManager信号
new_user_manager.user_login_completed.connect(self.on_login_completed)
new_user_manager.user_status_changed.connect(self.on_status_changed)
new_user_manager.user_operation_completed.connect(self.on_operation_completed)
```

## 高级功能

### 1. 用户状态监控

```python
# 连接状态变更信号
def on_user_status_changed(phone: str, old_status: str, new_status: str):
    print(f"用户 {phone} 状态变更: {old_status} -> {new_status}")

new_user_manager.user_status_changed.connect(on_user_status_changed)
```

### 2. 批量操作进度监控

```python
# 连接批量操作进度信号
def on_batch_progress(current: int, total: int, message: str):
    print(f"批量操作进度: {current}/{total} - {message}")

new_user_manager.batch_operation_progress.connect(on_batch_progress)
```

### 3. 操作历史分析

```python
# 获取用户登录历史
from core.telegram.user_state_manager import UserOperation

login_history = new_user_manager.get_operation_history(
    phone="***********",
    operation=UserOperation.LOGIN,
    limit=10
)

for record in login_history:
    print(f"{record.timestamp}: {record.operation.value} - {'成功' if record.success else '失败'}")
```

### 4. 客户端健康监控

```python
# 获取所有客户端状态
clients_info = new_user_manager.get_all_clients_info()

for phone, info in clients_info.items():
    print(f"用户 {phone}:")
    print(f"  连接状态: {info['is_connected']}")
    print(f"  最后活动: {info['last_activity']}")
    print(f"  连接次数: {info['connection_count']}")
```

## 性能优化

### 1. 并发控制

```python
# 批量操作时控制并发数
await new_user_manager.batch_login(accounts, max_concurrent=5)
await new_user_manager.batch_refresh_info(phones, max_concurrent=10)
```

### 2. 资源清理

```python
# 定期清理不活跃用户
await new_user_manager.cleanup_inactive_users(days=7)

# 手动断开用户连接
await new_user_manager.disconnect_user("***********", "资源清理")
```

### 3. 统计监控

```python
# 获取全局统计
global_stats = new_user_manager.get_user_statistics()
print(f"总用户数: {global_stats['total_users']}")
print(f"在线用户: {global_stats['status_distribution']['online']}")
print(f"活跃任务: {global_stats['active_tasks_count']}")
```

## 错误处理

### 1. 任务失败处理

```python
def on_task_failed(task_id: str, error: str):
    print(f"任务 {task_id} 失败: {error}")
    
    # 获取任务详情
    task_info = task_service.get_task_status(task_id)
    if task_info:
        print(f"任务名称: {task_info['name']}")
        print(f"任务分类: {task_info['category']}")

task_service.task_failed.connect(on_task_failed)
```

### 2. 用户状态异常处理

```python
def on_user_status_changed(phone: str, old_status: str, new_status: str):
    if new_status == "error":
        user = new_user_manager.get_user(phone)
        if user and user.last_error:
            print(f"用户 {phone} 出现错误: {user.last_error}")
            
            # 可以尝试重新连接
            asyncio.create_task(new_user_manager.start_login(phone, user.proxy))

new_user_manager.user_status_changed.connect(on_user_status_changed)
```

## 总结

重构后的多用户管理系统具有以下优势：

1. **更好的架构分离**：状态管理、客户端管理、任务调度各司其职
2. **更强的并发能力**：基于APScheduler的任务调度，支持高并发操作
3. **更完善的监控**：实时状态跟踪、操作历史、性能统计
4. **更好的错误处理**：任务级别的错误处理和重试机制
5. **更易于扩展**：模块化设计，新功能易于添加

通过这次重构，多用户管理功能变得更加强大、稳定和易于维护。
