# Telegram消息发送助手

这是一个基于PySide6的Telegram消息发送工具，提供群发消息、定时发送等功能。

## 主要功能

- 创建和管理消息发送任务
- 支持群消息和好友消息群发
- 多种时间模式（定时、间隔、单次等）
- 支持文本、图片等多种消息类型
- 任务状态监控和控制
- 自定义随机字符、表情等消息内容

## 技术架构

本项目采用分层架构设计：

- **UI层**：基于PySide6和QFluentWidgets的界面组件
- **应用层**：控制器和服务组件，连接UI和核心业务层
- **核心层**：业务逻辑实现，如任务调度和消息发送
- **数据层**：数据访问和存储操作

## 安装依赖

```bash
pip install -r requirements.txt
```

## 运行应用

```bash
python main.py
```

## 目录结构

```
├── app/                 # 应用层
│   ├── controllers/     # 控制器
│   └── services/        # 服务
├── core/                # 核心业务层
├── data/                # 数据层
│   ├── models/          # 数据模型
│   ├── repositories/    # 数据仓储
│   └── storage/         # 数据存储
├── ui/                  # 界面层
│   ├── designer/        # UI设计文件
│   ├── dialogs/         # 对话框组件
│   └── views/           # 视图组件
├── utils/               # 工具类
├── main.py              # 主程序入口
└── README.md            # 项目说明
```

## 功能截图

(此处可放置功能截图)

## 配置说明

数据存储路径: `data/storage/`
- 消息任务存储: `data/storage/message_tasks/`
- 账号信息存储: `data/storage/accounts/`
- 群组信息存储: `data/storage/groups/`
- 联系人信息存储: `data/storage/contacts/`

## 开发扩展

如需扩展新功能，请遵循项目的分层架构原则：
1. UI层不应直接访问数据层
2. 应用层通过核心层访问数据
3. 新增UI组件应放在对应目录
4. 使用utils.logger进行日志记录 