# 任务管理器重构改进说明

## 问题分析与解决

### 1. 架构简化 - 移除双重任务管理

**原有问题：**
```
新任务管理器 → TelegramClientWorker → TelegramService
```
这种设计存在"多此一举"的问题，增加了不必要的复杂性。

**解决方案：**
```
新任务管理器 → TelegramService (直接调用)
```

**具体改进：**

#### 重构前（有问题的设计）：
```python
class AccountLoginTask(BaseTask):
    def __init__(self, phone, proxy, telegram_worker, **kwargs):
        self.telegram_worker = telegram_worker  # 多余的中间层
    
    async def execute(self, context=None):
        # 通过Worker调用Service
        task_id = self.telegram_worker.start_login(self.phone, self.proxy)
        success, result = await self.telegram_worker.get_task_result(task_id, timeout=30)
```

#### 重构后（简化的设计）：
```python
class AccountLoginTask(BaseTask):
    def __init__(self, phone, proxy, **kwargs):
        # 移除telegram_worker依赖
        self.phone = phone
        self.proxy = proxy
    
    async def execute(self, context=None):
        # 直接调用TelegramService
        from core.telegram.telegram_service import TelegramService
        telegram_service = TelegramService(api_id, api_hash, session_dir)
        success, result = await telegram_service.start_login(self.phone, self.proxy)
```

### 2. 异步事件循环优化 - 使用Qt事件循环

**原有问题：**
在使用qasync的环境中，直接使用`asyncio.get_event_loop()`会导致：
- 事件循环冲突
- 阻塞UI线程
- 异步操作不一致

**解决方案：**
使用Qt的QEventLoop和信号机制，与qasync完美兼容。

#### 重构前（有问题的异步处理）：
```python
async def start_login(self, phone, proxy=None):
    task_id = task_service.submit_account_login_task(phone, proxy, telegram_worker)
    
    # 问题：直接使用asyncio事件循环
    import asyncio
    timeout = 30
    start_time = asyncio.get_event_loop().time()
    
    while True:
        task_status = task_service.get_task_status(task_id)
        if task_status.get("status") == "completed":
            return True, "成功"
        
        if asyncio.get_event_loop().time() - start_time > timeout:
            return False, "超时"
        
        await asyncio.sleep(0.1)  # 阻塞等待
```

#### 重构后（Qt事件循环方式）：
```python
async def start_login(self, phone, proxy=None):
    from PySide6.QtCore import QEventLoop, QTimer
    
    task_id = task_service.submit_account_login_task(phone, proxy)
    
    # 使用Qt事件循环
    loop = QEventLoop()
    timer = QTimer()
    timer.setSingleShot(True)
    timer.timeout.connect(loop.quit)
    
    result_data = {"completed": False, "success": False, "message": ""}
    
    def on_task_completed(completed_task_id: str, result: object):
        if completed_task_id == task_id:
            result_data["completed"] = True
            result_data["success"] = True
            result_data["result"] = result
            loop.quit()
    
    def on_task_failed(failed_task_id: str, error: str):
        if failed_task_id == task_id:
            result_data["completed"] = True
            result_data["success"] = False
            result_data["message"] = error
            loop.quit()
    
    # 连接信号
    task_service.task_completed.connect(on_task_completed)
    task_service.task_failed.connect(on_task_failed)
    
    try:
        timer.start(30000)  # 30秒超时
        loop.exec()  # 非阻塞等待
        
        if result_data["success"]:
            return True, "成功"
        else:
            return False, result_data["message"]
    finally:
        # 清理资源
        task_service.task_completed.disconnect(on_task_completed)
        task_service.task_failed.disconnect(on_task_failed)
        timer.stop()
```

## 重构优势

### 1. 架构更清晰
- **单一职责**：任务管理器专注于调度，TelegramService专注于业务逻辑
- **减少层级**：移除不必要的中间层，降低复杂性
- **易于维护**：代码路径更直接，问题定位更容易

### 2. 异步处理更合理
- **事件循环统一**：与qasync完全兼容，避免冲突
- **非阻塞等待**：使用信号机制，不阻塞UI线程
- **资源管理**：自动清理信号连接和定时器

### 3. 性能提升
- **减少开销**：移除双重任务管理的开销
- **更快响应**：直接调用服务，减少中间环节
- **内存优化**：减少不必要的对象创建

### 4. 代码简化
- **参数简化**：任务创建时不再需要传递telegram_worker
- **依赖减少**：任务类不再依赖Worker组件
- **接口统一**：所有任务使用相同的创建模式

## 使用示例

### 重构后的任务提交：
```python
from core.task_manager.task_service import task_service

# 简化的任务提交（不再需要telegram_worker参数）
task_id = task_service.submit_account_login_task(
    phone="***********",
    proxy={"type": "http", "host": "127.0.0.1", "port": 8080}
)

# 验证码验证
task_id = task_service.submit_account_code_verification_task(
    phone="***********",
    code="123456",
    password="optional_password"
)
```

### 重构后的服务层调用：
```python
# 在AccountService中
async def start_login(self, phone: str, proxy: dict = None):
    # 直接使用任务管理器，不传递telegram_worker
    task_id = task_service.submit_account_login_task(phone=phone, proxy=proxy)
    
    # 使用Qt事件循环等待结果
    # ... (Qt事件循环代码)
```

## 测试验证

重构后的代码可以通过以下方式测试：

1. **单元测试**：每个任务类可以独立测试
2. **集成测试**：任务管理器与TelegramService的集成
3. **UI测试**：验证Qt事件循环的正确性
4. **性能测试**：对比重构前后的性能差异

## 总结

这次重构解决了两个关键问题：

1. **架构过度复杂**：通过移除不必要的中间层，简化了整体架构
2. **异步处理不当**：通过使用Qt事件循环，确保了与qasync的兼容性

重构后的代码更加简洁、高效，并且与现有的Qt/qasync架构完美集成。
