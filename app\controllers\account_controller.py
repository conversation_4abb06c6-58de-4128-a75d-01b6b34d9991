#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
账户控制器
处理与UI层的交互，将UI事件转发给服务层，并将服务层的结果返回给UI层
"""

from typing import List, Dict, Any, Optional, Tuple, Union, Callable

from PySide6.QtCore import QObject, Signal, Slot, Property, QTimer
from config import config
import os
from utils.logger import get_logger
from app.services.account_service import AccountService


class AccountController(QObject):
    """账户控制器，处理用户界面与服务层的交互"""
    
    # ================ 信号定义 ================
    
    # 账户分组相关信号
    groups_loaded = Signal(list)  # 分组加载完成 (groups)
    group_created = Signal(dict, str)  # 分组创建结果 (group_dict, message)
    group_updated = Signal(int, bool, str)  # 分组更新结果 (group_id, success, message)
    group_deleted = Signal(int, bool, str)  # 分组删除结果 (group_id, success, message)
    
    # 账户相关信号
    accounts_loaded = Signal(list)  # 账户加载完成 (accounts)
    accounts_refresh_requested = Signal()  # 请求刷新账户数据
    account_added = Signal(dict, str)  # 账户添加结果 (account_dict, message)
    account_updated = Signal(int, bool, str)  # 账户更新结果 (account_id, success, message)
    account_deleted = Signal(int, bool, str)  # 账户删除结果 (account_id, success, message)
    accounts_batch_deleted = Signal(int, int, str)  # 批量删除结果 (success_count, fail_count, message)
    
    # 账户资料更新相关信号
    profile_updated = Signal(int, bool, str)  # 资料更新结果 (account_id, success, message)
    profiles_batch_updated = Signal(int, int, str)  # 批量更新结果 (success_count, fail_count, message)
    
    # 账户刷新相关信号
    account_refreshed = Signal(int, bool, str)  # 账户刷新结果 (account_id, success, message)
    accounts_batch_refreshed = Signal(int, int, str)  # 批量刷新结果 (success_count, fail_count, message)
    
    # 登录相关信号
    login_code_sent = Signal(str, bool, str)  # 验证码发送结果 (phone, success, message)
    login_code_verified = Signal(str, bool, object)  # 验证码验证结果 (phone, success, result/message)
    login_password_verified = Signal(str, bool, object)  # 密码验证结果 (phone, success, result/message)
    
    # Session导入相关信号
    session_imported = Signal(str, bool, object)  # Session导入结果 (path, success, account/message)
    sessions_batch_imported = Signal(int, int, str)  # 批量导入结果 (success_count, fail_count, message)
    # 新增信号，用于通知UI层有一批新的账户数据可供刷新主列表
    new_accounts_added_to_main_list = Signal(list)  # list of new account dicts
    
    # 代理相关信号
    proxy_ips_loaded = Signal(list)  # 代理IP加载完成 (proxy_ips)
    proxy_usage_updated = Signal(int, bool)  # 代理使用次数更新结果 (proxy_id, success)
    
    # 其他操作信号
    operation_failed = Signal(str)  # 操作失败通用信号 (error_message)
    loading_started = Signal(str)  # 开始加载 (message)
    loading_finished = Signal()  # 加载完成
    
    # 批量操作相关信号
    batch_operation_progress = Signal(int, int, str)  # 批量操作进度 (current, total, message)
    batch_operation_completed = Signal(dict)  # 批量操作完成 (result_dict)
    
    notify = Signal(str, object, str)
    
    def __init__(self, account_service: AccountService):
        """初始化账户控制器

        Args:
            account_service: 账户服务实例
        """
        super().__init__()
        self._account_service = account_service
        self._account_service.notify.connect(self.notify.emit)
        # 连接 AccountService 的新信号
        self._account_service.batch_accounts_added.connect(self._on_service_batch_accounts_added)
        self._logger = get_logger("app.controllers.account")
        self._logger.info("账户控制器初始化")
        self._current_proxy_id = None
        self._current_proxy_type = None
    
    # ================ 账户分组管理 ================
    
    @Slot()
    async def load_all_groups(self):
        """加载所有账户分组"""
        self.loading_started.emit("正在加载分组...")
        try:
            groups = await self._account_service.get_all_groups()
            self.groups_loaded.emit(groups)
            return groups
        except Exception as e:
            self._logger.error(f"加载分组异常: {e}")
            self.operation_failed.emit(f"加载分组失败: {str(e)}")
            return []
    
    async def get_account_groups(self):
        """获取所有账户分组（别名方法，功能同get_all_groups）

        Returns:
            分组列表
        """
        return await self._account_service.get_all_groups()
    
    async def get_all_groups(self):
        """获取所有账户分组

        Returns:
            分组列表
        """
        return await self._account_service.get_all_groups()
    
    @Slot(str, str, result = bool)
    async def create_group(self, name: str, description: str = ""):
        """创建账户分组

        Args:
            name: 分组名称
            description: 分组描述

        Returns:
            操作是否成功
        """
        self.loading_started.emit("正在创建分组...")
        self._logger.info(f"请求创建分组: {name}")
        try:
            success, result = await self._account_service.create_group(name, description)
            self._logger.info(f"创建分组结果: success={success}, result={result}")
            if success:
                # 将分组对象转换为字典
                group_dict = {
                    'id': result.id,
                    'name': result.name,
                    'description': result.description,
                    'account_count': 0
                }
                self.group_created.emit(group_dict, "创建分组成功")
            else:
                self.group_created.emit({}, result)
            
            return success
        except Exception as e:
            self._logger.error(f"创建分组异常: {e}")
            self.operation_failed.emit(f"创建分组失败: {str(e)}")
            return False
        finally:
            self.loading_finished.emit()
    
    @Slot(int, str, str, result = bool)
    async def update_group(self, group_id: int, name: str = None, description: str = None):
        """更新账户分组

        Args:
            group_id: 分组ID
            name: 新的分组名称，为None则不更新
            description: 新的分组描述，为None则不更新

        Returns:
            操作是否成功
        """
        self.loading_started.emit("正在更新分组...")
        try:
            success, message = await self._account_service.update_group(group_id, name, description)
            self.group_updated.emit(group_id, success, message)
            return success
        except Exception as e:
            self._logger.error(f"更新分组异常: {e}")
            self.operation_failed.emit(f"更新分组失败: {str(e)}")
            return False
        finally:
            self.loading_finished.emit()
    
    @Slot(int, int, result = bool)
    async def delete_group(self, group_id: int, reassign_to: int = None):
        """删除账户分组

        Args:
            group_id: 要删除的分组ID
            reassign_to: 可选的重新分配的分组ID

        Returns:
            操作是否成功
        """
        self.loading_started.emit("正在删除分组...")
        try:
            success, message = await self._account_service.delete_group(group_id, reassign_to)
            self.group_deleted.emit(group_id, success, message)
            return success
        except Exception as e:
            self._logger.error(f"删除分组异常: {e}")
            self.operation_failed.emit(f"删除分组失败: {str(e)}")
            return False
        finally:
            self.loading_finished.emit()
    
    # ================ 账户管理 ================
    
    @Slot(bool)
    async def load_all_accounts(self, active_only: bool = False):
        """加载所有账户

        Args:
            active_only: 是否只加载活跃账户
        """
        self.loading_started.emit("正在加载账户...")
        try:
            accounts = await self._account_service.get_all_accounts(active_only)
            self.accounts_loaded.emit(accounts)
            return accounts
        except Exception as e:
            self._logger.error(f"加载账户异常: {e}")
            self.operation_failed.emit(f"加载账户失败: {str(e)}")
            return []
        finally:
            self.loading_finished.emit()
    
    @Slot(int)
    async def load_group_accounts(self, group_id: int):
        """加载指定分组的所有账户

        Args:
            group_id: 分组ID
        """
        self.loading_started.emit("正在加载分组账户...")
        try:
            accounts = await self._account_service.get_accounts_by_group(group_id)
            self.accounts_loaded.emit(accounts)
        except Exception as e:
            self._logger.error(f"加载分组账户异常: {e}")
            self.operation_failed.emit(f"加载分组账户失败: {str(e)}")
        finally:
            self.loading_finished.emit()
    
    @Slot(int, bool, result = bool)
    async def delete_account(self, account_id: int, delete_session: bool = True):
        """删除账户

        Args:
            account_id: 账户ID
            delete_session: 是否同时删除session文件

        Returns:
            操作是否成功
        """
        self.loading_started.emit("正在删除账户...")
        try:
            success, message = await self._account_service.delete_account(account_id, delete_session)
            self.account_deleted.emit(account_id, success, message)
            return success
        except Exception as e:
            self._logger.error(f"删除账户异常: {e}")
            self.operation_failed.emit(f"删除账户失败: {str(e)}")
            return False
        finally:
            self.loading_finished.emit()
    
    @Slot(list, bool)
    async def batch_delete_accounts(self, account_ids: List[int], delete_session: bool = True):
        """批量删除账户

        Args:
            account_ids: 账户ID列表
            delete_session: 是否同时删除session文件
        """
        self.loading_started.emit(f"正在批量删除账户({len(account_ids)}个)...")
        try:
            success_count, fail_count, message = await self._account_service.batch_delete_accounts(account_ids,
                                                                                                   delete_session)
            self.accounts_batch_deleted.emit(success_count, fail_count, message)
        except Exception as e:
            self._logger.error(f"批量删除账户异常: {e}")
            self.operation_failed.emit(f"批量删除账户失败: {str(e)}")
        finally:
            self.loading_finished.emit()
    
    @Slot(int, 'QVariantMap', result = bool)
    async def update_account_profile(self, account_id: int, profile_data: Dict[str, Any]):
        """更新账户资料

        Args:
            account_id: 账户ID
            profile_data: 资料数据，可包含first_name, last_name, username, bio, profile_photo, group_id

        Returns:
            操作是否成功
        """
        self.loading_started.emit("正在更新账户资料...")
        try:
            success, message = await self._account_service.update_account_profile(account_id, **profile_data)
            self.profile_updated.emit(account_id, success, message)
            return success
        except Exception as e:
            self._logger.error(f"更新账户资料异常: {e}")
            self.operation_failed.emit(f"更新账户资料失败: {str(e)}")
            return False
        finally:
            self.loading_finished.emit()
    
    @Slot(list, 'QVariantMap')
    async def batch_update_profiles(self, account_ids: List[int], profile_data: Dict[str, Any]):
        """批量更新账户资料

        Args:
            account_ids: 账户ID列表
            profile_data: 资料数据，可包含first_name, last_name, username, bio, profile_photo, group_id

        Returns:
            Tuple[int, int, str]: (成功数量, 失败数量, 消息)
        """
        self.loading_started.emit(f"正在批量更新账户资料({len(account_ids)}个)...")
        try:
            success_count, fail_count, message = await self._account_service.batch_update_profiles(account_ids,
                                                                                                   **profile_data)
            self.profiles_batch_updated.emit(success_count, fail_count, message)
            return success_count, fail_count, message
        except Exception as e:
            self._logger.error(f"批量更新账户资料异常: {e}")
            self.operation_failed.emit(f"批量更新账户资料失败: {str(e)}")
            return 0, len(account_ids), f"批量更新异常: {str(e)}"
        finally:
            self.loading_finished.emit()
    
    @Slot(int, 'QVariantMap', result = bool)
    async def update_account(self, account_id: int, update_data: Dict[str, Any]):
        """更新账户信息（兼容旧接口）

        Args:
            account_id: 账户ID
            update_data: 更新数据

        Returns:
            操作是否成功
        """
        self.loading_started.emit("正在更新账户信息...")
        try:
            success, message = await self._account_service.update_account(account_id, **update_data)
            self.account_updated.emit(account_id, success, message)
            return success
        except Exception as e:
            self._logger.error(f"更新账户信息异常: {e}")
            self.operation_failed.emit(f"更新账户信息失败: {str(e)}")
            return False
        finally:
            self.loading_finished.emit()
    
    @Slot(int, result = bool)
    async def refresh_account_info(self, account_id: int):
        """刷新账户信息

        Args:
            account_id: 账户ID

        Returns:
            操作是否成功
        """
        self.loading_started.emit("正在刷新账户信息...")
        try:
            success, message = await self._account_service.refresh_account_info(account_id)
            self.account_refreshed.emit(account_id, success, message)
            return success
        except Exception as e:
            self._logger.error(f"刷新账户信息异常: {e}")
            self.operation_failed.emit(f"刷新账户信息失败: {str(e)}")
            return False
        finally:
            self.loading_finished.emit()
    
    @Slot(list)
    async def batch_refresh_accounts(self, account_ids: List[int]):
        """批量刷新账户信息

        Args:
            account_ids: 账户ID列表
        """
        self.loading_started.emit(f"正在批量刷新账户信息({len(account_ids)}个)...")
        try:
            success_count, fail_count, message = await self._account_service.batch_refresh_accounts(account_ids)
            self.accounts_batch_refreshed.emit(success_count, fail_count, message)
        except Exception as e:
            self._logger.error(f"批量刷新账户信息异常: {e}")
            self.operation_failed.emit(f"批量刷新账户信息失败: {str(e)}")
        finally:
            self.loading_finished.emit()
    
    # ================ 账户登录相关 ================
    
    @Slot(str, dict, result = bool)
    async def start_login(self, phone: str, proxy: dict = None):
        """开始登录过程

        Args:
            phone: 手机号
            proxy: 代理配置

        Returns:
            操作是否成功
        """
        self.loading_started.emit("正在发送验证码...")
        try:
            # 保存代理设置
            if proxy:
                self._current_proxy_id = proxy.get('id')
                self._current_proxy_type = proxy.get('type')
            else:
                self._current_proxy_id = None
                self._current_proxy_type = None
            
            success, message = await self._account_service.start_login(phone, proxy)
            self.login_code_sent.emit(phone, success, message)
            return success
        except Exception as e:
            self._logger.error(f"开始登录异常: {e}")
            self.operation_failed.emit(f"开始登录失败: {str(e)}")
            return False
        finally:
            self.loading_finished.emit()
    
    @Slot(str, str, result = bool)
    async def submit_code(self, phone: str, code: str, password: str = None):
        """提交验证码

        Args:
            phone: 手机号
            code: 验证码
            password: 两步验证密码
            group_id: 分组ID

        Returns:
            操作是否成功
        """
        self.loading_started.emit("正在验证验证码...")
        try:
            success, result = await self._account_service.submit_code(phone, code, password)
            self.login_code_verified.emit(phone, success, result)
            return success
        except Exception as e:
            self._logger.error(f"提交验证码异常: {e}")
            self.operation_failed.emit(f"提交验证码失败: {str(e)}")
            return False
        finally:
            self.loading_finished.emit()
    
    @Slot(str, int, int, result = bool)
    async def import_session(self, session_path: str, proxy_id: int = None, group_id: int = None):
        """导入session文件

        Args:
            session_path: session文件路径
            proxy_id: 代理ID
            group_id: 分组ID

        Returns:
            操作是否成功
        """
        self.loading_started.emit("正在导入session...")
        try:
            success, result = await self._account_service.import_session(session_path, proxy_id, group_id)
            self.session_imported.emit(session_path, success, result)
            return success
        except Exception as e:
            self._logger.error(f"导入session异常: {e}")
            self.operation_failed.emit(f"导入session失败: {str(e)}")
            return False
        finally:
            self.loading_finished.emit()
    
    @Slot(list, int)
    async def batch_import_sessions(self, session_files: List[Dict[str, Any]], max_concurrent: int = 5):
        """批量导入session文件

        Args:
            session_files: session文件信息列表，每项包含 {path, proxy_id, group_id}
            max_concurrent: 最大并发数
        """
        self.loading_started.emit(f"正在批量导入session({len(session_files)}个)...")
        try:
            # 注意：AccountService 的 batch_import_sessions 现在除了返回统计数据，
            # 还会通过 batch_accounts_added 信号发送详细的账户数据
            success_count, fail_count, message = await self._account_service.batch_import_sessions(session_files,
                                                                                                   max_concurrent)
            # sessions_batch_imported 用于 ImportSessionsView 的最终摘要信息
            self.sessions_batch_imported.emit(success_count, fail_count, message)
        except Exception as e:
            self._logger.error(f"批量导入session异常: {e}")
            self.operation_failed.emit(f"批量导入session失败: {str(e)}")
        finally:
            self.loading_finished.emit()
    
    # 新增槽函数，用于处理来自 AccountService 的 batch_accounts_added 信号
    @Slot(list)
    def _on_service_batch_accounts_added(self, new_accounts_data: List[Dict[str, Any]]):
        """
        当AccountService完成一批账户的添加后，此槽被调用。
        它将转发这些新账户的数据，以便主账户列表等UI可以更新。
        """
        if new_accounts_data:
            self._logger.info(f"控制器：接收到 {len(new_accounts_data)} 个新添加账户的数据，准备通知UI更新主列表。")
            self.new_accounts_added_to_main_list.emit(new_accounts_data)
        else:
            self._logger.info("控制器：收到批处理账户添加通知，但无新账户数据。")
    
    # ================ 便捷方法 ================
    
    @Slot(str, int, str, int, result = bool)
    async def complete_login(self, phone: str, proxy_id: int = None, proxy_type: str = None, group_id: int = None):
        """完成登录过程后添加账户到数据库

        Args:
            phone: 手机号
            proxy_id: 代理ID
            proxy_type: 代理类型 ('ip_pool', 'system', 'none')
            group_id: 分组ID

        Returns:
            操作是否成功
        """
        self.loading_started.emit("正在完成登录...")
        try:
            # 获取用户信息
            self._logger.info(f"正在完成登录: {phone}, 代理ID: {proxy_id}, 代理类型: {proxy_type}, 分组ID: {group_id}")
            success, result = await self._account_service.get_user_info(phone)
            if not success:
                self.operation_failed.emit(f"获取用户信息失败: {result}")
                return False
            
            # 获取session文件名 (不是完整路径)
            session_filename = f"{phone}.session"
            self._logger.info(f"自动保存session文件: {session_filename}")
            
            # 添加账户到数据库
            success, account_or_message = await self._account_service.add_account(
                phone = phone,
                session_file = session_filename,  # 只传文件名
                user_info = result[1],
                proxy_id = proxy_id,
                proxy_type = proxy_type,
                group_id = group_id
            )
            
            if success:
                account_dict = account_or_message.to_dict() if hasattr(account_or_message, 'to_dict') else {}
                self.account_added.emit(account_dict, "账户添加成功")
                self._logger.info(f"账户添加成功: {phone}")
            else:
                self.account_added.emit({}, account_or_message)
                self._logger.error(f"账户添加失败: {phone}, 原因: {account_or_message}")
            
            return success
        except Exception as e:
            self._logger.error(f"完成登录异常: {e}", exc_info = True)
            self.operation_failed.emit(f"完成登录失败: {str(e)}")
            return False
        finally:
            self.loading_finished.emit()
    
    # ================ 代理管理方法 ================
    
    @Slot()
    async def get_proxy_ips(self):
        """获取有效代理IP列表"""
        self.loading_started.emit("正在获取代理IP列表...")
        try:
            proxy_ips = await self._account_service.get_proxy_ips()
            self._logger.info(f"获取到{len(proxy_ips)}个有效代理IP")
            self.proxy_ips_loaded.emit(proxy_ips)
            return proxy_ips
        except Exception as e:
            self._logger.error(f"获取代理IP列表异常: {e}")
            self.operation_failed.emit(f"获取代理IP列表失败: {str(e)}")
            return []
        finally:
            self.loading_finished.emit()
    
    @Slot(str, int, result = int)
    async def find_proxy_id_by_ip_port(self, ip: str, port: int):
        """根据IP和端口查找代理ID

        Args:
            ip: IP地址
            port: 端口号

        Returns:
            代理ID，如果未找到则返回None
        """
        try:
            proxy_ips = await self._account_service.get_proxy_ips()
            for proxy in proxy_ips:
                if proxy['ip'] == ip and proxy['port'] == port:
                    return proxy['id']
            return None
        except Exception as e:
            self._logger.error(f"查找代理ID异常: {e}")
            return None
    
    @Slot(int, result = bool)
    async def increment_proxy_usage(self, proxy_id: int):
        """增加代理使用次数

        Args:
            proxy_id: 代理ID

        Returns:
            是否成功
        """
        try:
            self._logger.info(f"增加代理使用次数: ID={proxy_id}")
            success = await self._account_service.increment_proxy_usage(proxy_id)
            self.proxy_usage_updated.emit(proxy_id, success)
            return success
        except Exception as e:
            self._logger.error(f"增加代理使用次数异常: {e}")
            return False
    
    async def auto_login_accounts(self, accounts: List[Dict[str, Any]], max_concurrent: int = 5):
        """批量自动登录账户

        Args:
            accounts: 账户列表
            max_concurrent: 最大并发数
        """
        self.loading_started.emit(f"正在批量登录账户({len(accounts)}个)...")
        try:
            result = await self._account_service.batch_auto_login(accounts, max_concurrent)
            
            # 转发进度信号
            if 'progress' in result:
                self.batch_operation_progress.emit(
                    result['progress']['current'],
                    result['progress']['total'],
                    result['progress']['message']
                )
            
            # 发送完成信号
            self.batch_operation_completed.emit(result)
            
            # 批量登录完成后，触发账户数据刷新以显示最新的用户信息
            self._logger.info("批量登录完成，触发账户数据刷新")
            self.accounts_refresh_requested.emit()
        
        except Exception as e:
            self._logger.error(f"批量自动登录异常: {e}")
            self.operation_failed.emit(f"批量自动登录失败: {str(e)}")
        finally:
            self.loading_finished.emit()
    
    # ================ 账户监控相关方法 ================
    
    @Slot(str, result = bool)
    async def check_login_status(self, phone: str) -> bool:
        """检查账户是否登录

        Args:
            phone: 手机号

        Returns:
            是否登录
        """
        self._logger.info(f"检查账户登录状态: {phone}")
        try:
            return await self._account_service.check_login_status(phone)
        except Exception as e:
            self._logger.error(f"检查账户登录状态异常: {e}")
            self.operation_failed.emit(f"检查账户登录状态失败: {str(e)}")
            return False
    
    @Slot(str, result = bool)
    async def check_connection_status(self, phone: str) -> bool:
        """检查账户连接状态

        Args:
            phone: 手机号

        Returns:
            是否连接
        """
        self._logger.info(f"检查账户连接状态: {phone}")
        try:
            return await self._account_service.check_connection_status(phone)
        except Exception as e:
            self._logger.error(f"检查账户连接状态异常: {e}")
            self.operation_failed.emit(f"检查账户连接状态失败: {str(e)}")
            return False
    
    @Slot(str, str, result = 'QVariantList')
    async def get_account_groups_by_type(self, phone: str, group_type: str = None) -> List[Dict[str, Any]]:
        """获取指定账户的群组或频道列表

        Args:
            phone: 手机号
            group_type: 类型，'group' 表示群组，'channel' 表示频道，None 表示全部

        Returns:
            群组/频道列表
        """
        self._logger.info(f"获取账户 {phone} 的{group_type or '所有'}对话列表")
        try:
            self.loading_started.emit(f"正在获取{group_type or '所有'}对话列表...")
            groups = await self._account_service.get_account_groups_by_type(phone, group_type)
            return groups
        except Exception as e:
            self._logger.error(f"获取账户对话列表异常: {e}")
            self.operation_failed.emit(f"获取账户对话列表失败: {str(e)}")
            return []
        finally:
            self.loading_finished.emit()
    
    @Slot(int, result = 'QVariantList')
    async def get_accounts_by_group(self, group_id: int):
        """通过群组ID获取该群组下的账户

        Args:
            group_name_or_id: 群组名称或ID

        Returns:
            该群组下的账户列表
        """
        self._logger.info(f"通过群组名或ID获取账户: {group_id}")
        try:
            self.loading_started.emit(f"正在获取群组账户...")
            accounts = await self._account_service.get_accounts_by_group(group_id)
            return accounts
        except Exception as e:
            self._logger.error(f"获取群组账户异常: {e}")
            self.operation_failed.emit(f"获取群组账户失败: {str(e)}")
            return []
        finally:
            self.loading_finished.emit()
