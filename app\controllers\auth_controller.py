#!/usr/bin/python3
# -*- coding: utf-8 -*-
"""
认证控制器模块 - 提供UI层与认证服务层之间的桥梁
"""

import asyncio
from typing import Dict, Any, Optional, Tuple, Callable
from PySide6.QtCore import QObject, Signal

from app.services.auth_service import AuthService
from utils.logger import get_logger




class AuthController(QObject):
    """认证控制器类，处理UI层与认证服务层之间的桥梁"""
    
    init_software_finished = Signal(bool, dict)

    def __init__(self, auth_service: 'AuthService'):
        """初始化认证控制器"""
        super().__init__()
        self._auth_service = auth_service
        # 获取模块日志记录器
        self._logger = get_logger("app.controllers.auth_controller")
    
    async def initialize_software(self, domain: str, app_id: str, key: str, ver_index: str, version: str) -> <PERSON><PERSON>[bool, Dict[str, Any]]:
        """
        初始化软件配置
        
        Args:
            domain: API域名
            app_id: 应用ID
            key: 密钥
            ver_index: 版本索引
            version: 版本号
            
        Returns:
            初始化结果元组 (成功标志, 结果数据)
        """
        self._logger.debug(f"控制器: 初始化软件 app_id={app_id}, version={version}")
        try:
            return await self._auth_service.do_init_software(domain, app_id, key, ver_index, version)
        except Exception as e:
            self._logger.error(f"软件初始化失败: {e}")
            return False, {"error": str(e)}
    
    async def login(self, account: str, password: str) -> Dict[str, Any]:
        """
        用户登录
        
        Args:
            account: 账号
            password: 密码
            
        Returns:
            登录结果
        """
        self._logger.info(f"控制器: 用户登录 account={account}")
        try:
            result = await self._auth_service.login(account, password)
            print(result)
            if result.get("success"):
                # 登录成功后启动心跳任务
                await self._auth_service.get_user_info()
                self._auth_service.start_auto_heartbeat()
            return result
        except Exception as e:
            self._logger.error(f"登录失败: {e}")
            return {"success": False, "message": str(e)}
    
    async def logout(self) -> Dict[str, Any]:
        """
        用户登出
        
        Returns:
            登出结果
        """
        self._logger.info("控制器: 用户登出")
        try:
            # 停止心跳任务
            self._auth_service.stop_auto_heartbeat()
            return await self._auth_service.logout()
        except Exception as e:
            self._logger.error(f"登出失败: {e}")
            return {"success": False, "message": str(e)}
    
    async def register(self, account: str, password: str, code: str="", invid: str = "", udid: str = "") -> Dict[str, Any]:
        """
        用户注册
        
        Args:
            account: 账号（手机号或邮箱）
            password: 密码
            code: 验证码
            invid: 邀请人ID
            udid: 机器码
            
        Returns:
            注册结果
        """
        self._logger.info(f"控制器: 用户注册 account={account}")
        try:
            return await self._auth_service.register(account, password, code, invid, udid)
        except Exception as e:
            self._logger.error(f"注册失败: {e}")
            return {"success": False, "message": str(e)}
    
    async def recharge(self, card_code: str) -> Dict[str, Any]:
        """
        卡密充值
        
        Args:
            card_code: 充值卡密
            
        Returns:
            充值结果
        """
        self._logger.info("控制器: 卡密充值")
        try:
            return await self._auth_service.recharge(card_code)
        except Exception as e:
            self._logger.error(f"充值失败: {e}")
            return {"success": False, "message": str(e)}
    
    async def get_user_info(self) -> Dict[str, Any]:
        """
        获取用户信息
        
        Returns:
            用户信息
        """
        self._logger.info("控制器: 获取用户信息")
        try:
            return await self._auth_service.get_user_info()
        except Exception as e:
            self._logger.error(f"获取用户信息失败: {e}")
            return {"success": False, "message": str(e)}
    
    def get_token(self) -> str:
        """
        获取当前用户的token
        
        Returns:
            用户token
        """
        return self._auth_service.get_token()
    
    def get_user_id(self) -> str:
        """
        获取当前用户的ID
        
        Returns:
            用户ID
        """
        return self._auth_service.get_user_id()
    
    def get_username(self) -> str:
        """
        获取当前用户的用户名
        
        Returns:
            用户名
        """
        return self._auth_service.get_username()
    
    async def is_logged_in(self) -> bool:
        """
        检查用户是否已登录
        
        Returns:
            是否已登录
        """
        return await self._auth_service.is_logged_in()

    def save_credentials(self, email, password, remember_password, auto_login):
        self._auth_service.save_credentials(email, password, remember_password, auto_login)

    def load_credentials(self):
        return self._auth_service.load_credentials()

    async def kami_topup(self, kami: str) -> Dict[str, Any]:
        """卡密充值"""
        return await self._auth_service.kami_topup(kami)

    async def verify_vip(self) -> Dict[str, Any]:
        """会员到期检测"""
        return await self._auth_service.verify_vip()

