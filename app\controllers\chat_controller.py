#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
聊天控制器
处理与UI层的交互，将UI事件转发给服务层，并将服务层的结果返回给UI层
"""

from typing import List, Dict, Any, Optional, Tuple

from PySide6.QtCore import QObject, Signal, Slot

from utils.logger import get_logger
from app.services.chat_service import ChatService


class ChatController(QObject):
    """聊天控制器，处理用户界面与服务层的交互"""
    
    # 信号定义
    chats_loaded = Signal(list)  # 聊天加载完成 (chats)
    chats_synced = Signal(int, int, str)  # 聊天同步结果 (new_count, update_count, message)
    monitor_status_changed = Signal(str, int, bool, bool, str)  # 监控状态变更 (phone, chat_id, success, message)
    
    operation_failed = Signal(str)  # 操作失败通用信号 (error_message)
    loading_started = Signal(str)  # 开始加载 (message)
    loading_finished = Signal()  # 加载完成
    
    notify = Signal(str, object, str)
    
    def __init__(self, chat_service: ChatService):
        """初始化聊天控制器
        
        Args:
            chat_service: 聊天服务实例
        """
        super().__init__()
        self._chat_service = chat_service
        self._chat_service.notify.connect(self.notify.emit)
        self._logger = get_logger("app.controllers.chat")
        self._logger.info("聊天控制器初始化")
    
    @Slot(str)
    async def sync_account_chats(self, phone: str):
        """同步账户的所有聊天到数据库
        
        Args:
            phone: 账户手机号
        """
        self.loading_started.emit(f"正在同步账户 {phone} 的聊天信息...")
        try:
            new_count, update_count, message = await self._chat_service.sync_account_chats(phone)
            self.chats_synced.emit(new_count, update_count, message)
        except Exception as e:
            self._logger.error(f"同步聊天异常: {e}")
            self.operation_failed.emit(f"同步聊天失败: {str(e)}")
        finally:
            self.loading_finished.emit()
    
    @Slot(str, str, bool)
    async def load_account_chats(self, phone: str, chat_type: str = None, only_member: bool = True):
        """加载账户的聊天列表
        
        Args:
            phone: 账户手机号
            chat_type: 聊天类型 ('group', 'supergroup', 'channel')，None表示所有类型
            only_member: 是否只返回当前是成员的聊天
        """
        self.loading_started.emit(f"正在加载账户 {phone} 的聊天列表...")
        try:
            chats = await self._chat_service.get_account_chats(phone, chat_type, only_member)
            self.chats_loaded.emit(chats)
        except Exception as e:
            self._logger.error(f"加载聊天列表异常: {e}")
            self.operation_failed.emit(f"加载聊天列表失败: {str(e)}")
        finally:
            self.loading_finished.emit()
    
    @Slot(str, str, bool)
    async def load_monitored_chats(self, phone: str = None, monitor_type: str = "all", only_member: bool = True):
        """加载被监控的聊天
        
        Args:
            phone: 账户手机号，None表示所有账户
            monitor_type: 监控类型 ("user", "keyword", "all")
            only_member: 是否只返回当前是成员的聊天
        """
        self.loading_started.emit(f"正在加载监控聊天...")
        try:
            chats = await self._chat_service.get_monitored_chats(phone, monitor_type, only_member)
            self.chats_loaded.emit(chats)
        except Exception as e:
            self._logger.error(f"加载监控聊天异常: {e}")
            self.operation_failed.emit(f"加载监控聊天失败: {str(e)}")
        finally:
            self.loading_finished.emit()
    
    @Slot(str, int, bool, bool)
    async def set_chat_monitor_status(self, phone: str, chat_id: int, user_monitor: bool = None, keyword_monitor: bool = None):
        """设置聊天的监控状态
        
        Args:
            phone: 账户手机号
            chat_id: 聊天ID
            user_monitor: 是否启用用户监控
            keyword_monitor: 是否启用关键词监控
        """
        self.loading_started.emit("正在设置监控状态...")
        try:
            success, message = await self._chat_service.set_chat_monitor_status(phone, chat_id, user_monitor, keyword_monitor)
            self.monitor_status_changed.emit(phone, chat_id, success, message)
        except Exception as e:
            self._logger.error(f"设置监控状态异常: {e}")
            self.operation_failed.emit(f"设置监控状态失败: {str(e)}")
        finally:
            self.loading_finished.emit()
