#!/usr/bin/env python
# -*- coding: utf-8 -*-

from typing import List, Dict, Any, Optional, Tuple
from PySide6.QtCore import QObject, Signal, Slot
from utils.logger import get_logger
from app.services.convert_service import ConvertService
from app.controllers.account_controller import AccountController


class ConvertController(QObject):
    """转换控制器，处理Tdata和Session格式转换相关的业务逻辑"""
    
    # 转换相关信号
    conversion_started = Signal(str)  # 转换开始 (message)
    conversion_progress = Signal(int, int, str)  # 转换进度 (current, total, message)
    conversion_completed = Signal(list)  # 转换完成 (results)
    conversion_failed = Signal(str)  # 转换失败 (error_message)
    
    # 代理相关信号
    proxy_ips_loaded = Signal(list)  # 代理IP加载完成 (proxy_ips)
    
    # 账户导入相关信号
    accounts_imported = Signal(int, int, str)  # 账户导入结果 (success_count, fail_count, message)
    
    def __init__(self, parent = None):
        super().__init__(parent)
        self._logger = get_logger(__name__)
        self._convert_service = ConvertService()
        self._account_controller = None  # 延迟初始化
    
    def set_account_controller(self, account_controller: AccountController):
        """设置账户控制器"""
        self._account_controller = account_controller
        self._logger.info("账户控制器已设置")
    
    @Slot(result = list)
    async def get_proxy_ips(self) -> List[Dict[str, Any]]:
        """获取代理IP池列表

        Returns:
            代理IP列表
        """
        try:
            if not self._account_controller:
                self._logger.error("账户控制器未初始化")
                return []
            
            # 使用账户控制器获取代理IP
            proxy_ips = await self._account_controller.get_proxy_ips()
            self.proxy_ips_loaded.emit(proxy_ips)
            return proxy_ips
        
        except Exception as e:
            self._logger.error(f"获取代理IP失败: {e}")
            return []
    
    @Slot(list, str, str, dict, dict, result = list)
    async def batch_convert(self,
                            files: List[str],
                            output_dir: str,
                            convert_type: str,
                            proxy_config: Dict[str, Any],
                            password_info: Dict[str, Any]) -> List[Dict[str, Any]]:
        """批量转换文件

        Args:
            files: 文件路径列表
            output_dir: 输出目录
            convert_type: 转换类型，"session_to_tdata" 或 "tdata_to_session"
            proxy_config: 代理配置
            password_info: 密码信息

        Returns:
            转换结果列表
        """
        try:
            self.conversion_started.emit(f"开始{convert_type}转换，共 {len(files)} 个文件")
            
            # 创建进度回调函数
            def progress_callback(current: int, total: int, message: str):
                self.conversion_progress.emit(current, total, message)
            
            # 调用服务层进行转换
            results = await self._convert_service.batch_convert(
                files, output_dir, convert_type, proxy_config, password_info, progress_callback
            )
            
            self.conversion_completed.emit(results)
            return results
        
        except Exception as e:
            error_msg = f"批量转换失败: {str(e)}"
            self._logger.error(error_msg)
            self.conversion_failed.emit(error_msg)
            return []
    
    @Slot(list, dict, result = bool)
    async def import_converted_accounts(self,
                                        conversion_results: List[Dict[str, Any]],
                                        import_config: Dict[str, Any]) -> bool:
        """将转换后的账户导入到账户管理

        Args:
            conversion_results: 转换结果列表
            import_config: 导入配置，包含代理设置和分组信息

        Returns:
            导入是否成功
        """
        try:
            if not self._account_controller:
                self._logger.error("账户控制器未初始化，无法导入账户")
                return False
            
            # 过滤出成功转换的session文件
            successful_sessions = []
            for result in conversion_results:
                if (result.get("status") == "成功" and
                        result.get("output_path") and
                        result.get("output_path").endswith(".session")):
                    session_info = {
                        "session_file": result["output_path"],
                        "proxy_config": import_config.get("proxy_config"),
                        "group_id": import_config.get("group_id"),
                        "row_index": len(successful_sessions)  # UI使用
                    }
                    successful_sessions.append(session_info)
            
            if not successful_sessions:
                self._logger.warning("没有成功转换的session文件可以导入")
                return False
            
            self._logger.info(f"准备导入 {len(successful_sessions)} 个session文件到账户管理")
            
            # 调用账户控制器进行批量导入
            await self._account_controller.batch_import_sessions(successful_sessions)
            
            return True
        
        except Exception as e:
            error_msg = f"导入转换后的账户失败: {str(e)}"
            self._logger.error(error_msg)
            self.conversion_failed.emit(error_msg)
            return False
    
    def _normalize_proxy_config(self, proxy_config: Dict[str, Any]) -> Optional[Tuple]:
        """规范化代理配置为converter可用的格式

        Args:
            proxy_config: 代理配置字典

        Returns:
            代理配置元组或None
        """
        if not proxy_config or proxy_config.get("type") == "none":
            return None
        
        if proxy_config.get("type") == "system":
            try:
                from utils.get_system_proxy import get_win_proxy_info
                system_proxy = get_win_proxy_info()
                if system_proxy:
                    proxy_tuple = ("socks5", system_proxy['ip'], system_proxy['port'])
                    self._logger.info(f"获取到系统代理: {system_proxy['ip']}:{system_proxy['port']}")
                    return proxy_tuple
                else:
                    self._logger.warning("未检测到系统代理设置")
                    return None
            except Exception as e:
                self._logger.error(f"读取系统代理失败: {e}")
                return None
        
        if proxy_config.get("type") == "ip_pool":
            # IP池代理
            proxy_type = proxy_config.get("proxy_type", "socks5")
            ip = proxy_config.get("ip")
            port = proxy_config.get("port")
            username = proxy_config.get("username")
            password = proxy_config.get("password")
            
            if not ip or not port:
                self._logger.warning("IP池代理配置不完整")
                return None
            
            if username and password:
                return (proxy_type, ip, port, username, password)
            else:
                return (proxy_type, ip, port)
        
        if proxy_config.get("type") == "custom":
            # 自定义代理
            proxy_type = proxy_config.get("proxy_type", "socks5")
            ip = proxy_config.get("ip")
            port = proxy_config.get("port")
            username = proxy_config.get("username")
            password = proxy_config.get("password")
            
            if not ip or not port:
                self._logger.warning("自定义代理配置不完整")
                return None
            
            if username and password:
                return (proxy_type, ip, port, username, password)
            else:
                return (proxy_type, ip, port)
        
        return None
