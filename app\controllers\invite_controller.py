from typing import List, Dict, Optional, Any, Union
from app.services.invite_service import InviteService
from core.telegram.telegram_service import TelegramService
from utils.logger import get_logger
from PySide6.QtCore import QObject, Signal

class InviteController(QObject):
    """邀请任务控制器"""
    task_stats_updated = Signal(str, int, int, int)  # 任务ID, success, failed, pending
    task_progress_updated = Signal(str, int, str)  # 任务ID, 进度, 状态
    
    def __init__(self, invite_service: InviteService, telegram_service: TelegramService = None):
        super().__init__()
        self._invite_service = invite_service
        self._telegram_service = telegram_service
        self._logger = get_logger("app.controllers.invite")
        self._invite_service.task_stats_changed.connect(self._on_task_stats_changed)
        self._invite_service.task_progress_changed.connect(self._on_task_progress_changed)
    async def task_initialize(self):
        await self._invite_service.task_initialize()
    def _on_task_stats_changed(self, task_id, success, failed, pending):
        self.task_stats_updated.emit(str(task_id), success, failed, pending)
    
    def _on_task_progress_changed(self, task_id, progress, status):
        self.task_progress_updated.emit(str(task_id), progress, status)
    
    async def create_invite_task(self, task_data: Dict[str, Any]) -> Dict[str, Any]:
        """创建邀请任务"""
        self._logger.info(f"创建邀请任务: {task_data.get('task_name')}")
        try:
            return await self._invite_service.create_invite_task(task_data)
        except Exception as e:
            self._logger.error(f"创建邀请任务失败: {str(e)}")
            raise
    
    async def get_invite_tasks(self, status: Optional[str] = None) -> List[Dict[str, Any]]:
        """获取邀请任务列表"""
        self._logger.info(f"获取邀请任务列表, status={status}")
        try:
            return await self._invite_service.get_invite_tasks(status)
        except Exception as e:
            self._logger.error(f"获取邀请任务列表失败: {str(e)}")
            raise
    
    async def get_invite_task(self, task_id: int) -> Optional[Dict[str, Any]]:
        """获取邀请任务详情"""
        self._logger.info(f"获取邀请任务详情, task_id={task_id}")
        try:
            return await self._invite_service.get_invite_task(task_id)
        except Exception as e:
            self._logger.error(f"获取邀请任务详情失败, task_id={task_id}: {str(e)}")
            raise
    
    async def update_invite_task(self, task_id: int, task_data: Dict[str, Any]) -> bool:
        """更新邀请任务"""
        self._logger.info(f"更新邀请任务, task_id={task_id}")
        try:
            return await self._invite_service.update_invite_task(task_id, task_data)
        except Exception as e:
            self._logger.error(f"更新邀请任务失败, task_id={task_id}: {str(e)}")
            raise
    
    async def delete_invite_task(self, task_id: int) -> tuple:
        """删除邀请任务"""
        self._logger.info(f"删除邀请任务, task_id={task_id}")
        try:
            return await self._invite_service.delete_invite_task(task_id)
        except Exception as e:
            self._logger.error(f"删除邀请任务失败, task_id={task_id}: {str(e)}")
            return False, f"删除失败: {str(e)}"
    
    async def start_invite_task(self, task_id: int) -> bool:
        """开始执行邀请任务"""
        self._logger.info(f"开始执行邀请任务, task_id={task_id}")
        try:
            return await self._invite_service.start_invite_task(task_id)
        except Exception as e:
            self._logger.error(f"开始执行邀请任务失败, task_id={task_id}: {str(e)}")
            raise
    
    async def stop_invite_task(self, task_id: int) -> bool:
        """停止邀请任务"""
        self._logger.info(f"停止邀请任务, task_id={task_id}")
        try:
            return await self._invite_service.stop_invite_task(task_id)
        except Exception as e:
            self._logger.error(f"停止邀请任务失败, task_id={task_id}: {str(e)}")
            raise
    
    async def check_invite_limit(self, account_phone: str) -> Dict[str, Any]:
        """检查账户邀请限制"""
        self._logger.info(f"检查账户邀请限制, account_phone={account_phone}")
        try:
            return await self._invite_service.check_invite_limit(account_phone)
        except Exception as e:
            self._logger.error(f"检查账户邀请限制失败, account_phone={account_phone}: {str(e)}")
            raise
    
    async def update_invite_limit(self, account_phone: str, max_daily_limit: int) -> Dict[str, Any]:
        """更新账户邀请限制"""
        self._logger.info(f"更新账户邀请限制, account_phone={account_phone}, max_daily_limit={max_daily_limit}")
        try:
            return await self._invite_service.update_invite_limit(account_phone, max_daily_limit)
        except Exception as e:
            self._logger.error(f"更新账户邀请限制失败, account_phone={account_phone}: {str(e)}")
            raise
    
    async def increment_invite_count(self, account_phone: str, count: int = 1) -> Dict[str, Any]:
        """增加账户邀请计数"""
        self._logger.info(f"增加账户邀请计数, account_phone={account_phone}, count={count}")
        try:
            return await self._invite_service.increment_invite_count(account_phone, count)
        except Exception as e:
            self._logger.error(f"增加账户邀请计数失败, account_phone={account_phone}: {str(e)}")
            raise
    
    async def get_monitor_tasks_with_user_count(self) -> list:
        return await self._invite_service.get_monitor_tasks_with_user_count()
    

    
  
    
 
 
    


