#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
代理控制器
负责处理UI事件，调用服务层方法，更新UI状态
"""

import asyncio
from typing import List, Dict, Any, Optional, Tuple, Union
from datetime import datetime

from PySide6.QtCore import QObject, Signal, Slot

from app.services.proxy_service import ProxyService
from utils.logger import get_logger

class ProxySignals(QObject):
    """代理信号类"""
    
    # 操作结果信号
    operation_result = Signal(bool, str)  # (是否成功, 消息)
    
    # 代理列表更新信号
    proxies_updated = Signal(list, int)  # (代理列表, 总数)
    
    # 代理验证结果信号
    proxy_validated = Signal(int, bool, float)  # (代理ID, 是否有效, 响应时间)
    
    # 服务状态变更信号
    service_status_changed = Signal(bool)  # (是否运行)
    
    # 验证任务进度信号
    validation_progress = Signal(int, int)  # (当前进度, 总数)
    
    # 验证任务完成信号
    validation_completed = Signal(int, int)  # (有效数量, 总数)

class ProxyController(QObject):
    """代理控制器
    
    负责以下功能:
    - 处理UI事件
    - 调用服务层方法
    - 更新UI状态
    - 错误处理和信号发送
    """
    
    def __init__(self, proxy_service: 'ProxyService'):
        """初始化控制器
        
        Args:
            proxy_service: 代理服务
        """
        super().__init__()
        self.proxy_service = proxy_service
        self.signals = ProxySignals()
        self._logger = get_logger("app.controllers.proxy")
        
        # 当前分页信息
        self._current_offset = 0
        self._current_limit = 20
        
        # 当前验证任务状态
        self._validating_all = False
        self._validated_count = 0
        self._total_proxies = 0
    
    async def add_proxy(self, host: str, port: int, username: str, password: str, proxy_type: str, is_local: bool):
        """添加单个代理
        
        Args:
            host: 代理主机地址
            port: 代理端口
            username: 用户名
            password: 密码
            proxy_type: 代理类型
            is_local: 是否本地代理
            
        Returns:
            添加成功返回代理ID，失败返回None
        """
        try:
            # 调用服务层添加代理
            proxy = await self.proxy_service.add_proxy(
                host=host,
                port=port,
                username=username,
                password=password,
                proxy_type=proxy_type,
                is_local=is_local
            )
            
            if proxy:
                # 刷新代理列表
                await self.refresh_proxies()
                self.signals.operation_result.emit(True, f"成功添加代理: {host}:{port}")
                # 返回代理ID，方便后续验证
                return proxy.id
            else:
                self.signals.operation_result.emit(False, f"添加代理失败: {host}:{port}")
                return None
        except Exception as e:
            self._logger.exception(f"添加代理时出错: {e}")
            self.signals.operation_result.emit(False, f"添加代理异常: {str(e)}")
            return None

    async def add_ip_range(self, start_ip: str, end_ip: str, port_start: int, port_end: int, 
                          username: str, password: str, proxy_type: str, is_local: bool):
        """添加IP范围
        
        Args:
            start_ip: 起始IP
            end_ip: 结束IP
            port_start: 起始端口
            port_end: 结束端口
            username: 用户名
            password: 密码
            proxy_type: 代理类型
            is_local: 是否本地代理
        """
        try:
            # 调用服务层添加IP范围
            result, added_proxy_ids = await self.proxy_service.add_ip_range(
                start_ip=start_ip,
                end_ip=end_ip,
                port_start=port_start,
                port_end=port_end,
                username=username,
                password=password,
                proxy_type=proxy_type,
                is_local=is_local
            )
            
            if result > 0:
                # 刷新代理列表
                await self.refresh_proxies()
                self.signals.operation_result.emit(True, f"成功添加 {result} 个代理")
                
                # 验证新添加的代理
                if added_proxy_ids:
                    await self.validate_proxies(added_proxy_ids)
                
                return True
            else:
                self.signals.operation_result.emit(False, "添加代理范围失败")
                return False
        except Exception as e:
            self._logger.exception(f"添加IP范围时出错: {e}")
            self.signals.operation_result.emit(False, f"添加IP范围异常: {str(e)}")
            return False
    
    async def validate_proxy(self, proxy_id: int):
        """验证单个代理
        
        Args:
            proxy_id: 代理ID
            
        Returns:
            bool: 验证是否成功完成
        """
        try:
            self._logger.info(f"开始验证代理 ID: {proxy_id}")
            
            # 获取代理模型
            proxy = await self.proxy_service.find_by_id(proxy_id)
            if not proxy:
                self._logger.warning(f"验证失败：未找到ID为{proxy_id}的代理")
                return False
            
            # 调用服务层验证代理
            is_valid, response_time = await self.proxy_service.validate_proxy(proxy_id)
            
            # 发送验证结果信号
            self.signals.proxy_validated.emit(proxy_id, is_valid, response_time or 0)
            
            # 更新验证进度（对于单个验证，设置为1/1完成）
            self.signals.validation_progress.emit(1, 1)
            
            self._logger.info(f"代理 ID: {proxy_id} 验证结果: {'有效' if is_valid else '无效'}, 响应时间: {response_time}ms")
            return True
        except Exception as e:
            self._logger.exception(f"验证代理 ID: {proxy_id} 时出错: {e}")
            # 发送验证失败信号
            self.signals.proxy_validated.emit(proxy_id, False, 0)
            self.signals.validation_progress.emit(1, 1)
            return False
    
    async def validate_all_proxies(self):
        """验证所有代理"""
        try:
            # 避免重复验证
            if self._validating_all:
                self.signals.operation_result.emit(False, "验证任务已在进行中")
                return False
                
            self._validating_all = True
            self._validated_count = 0
            
            # 获取代理总数
            self._total_proxies = await self.proxy_service.get_proxy_count()
            
            if self._total_proxies == 0:
                self.signals.operation_result.emit(False, "没有代理需要验证")
                self._validating_all = False
                return False
            
            # 发送开始验证信号
            self.signals.operation_result.emit(True, f"开始验证 {self._total_proxies} 个代理")
            
            # 调用服务层验证所有代理（耗时任务，已在服务层放入子线程）
            valid_count = 0
            
            async for proxy_id, is_valid, response_time in self.proxy_service.validate_all_proxies():
                # 更新进度
                self._validated_count += 1
                
                # 统计有效代理
                if is_valid:
                    valid_count += 1
                
                # 发送验证结果信号
                self.signals.proxy_validated.emit(proxy_id, is_valid, response_time if response_time else 0)
                
                # 发送进度信号
                self.signals.validation_progress.emit(self._validated_count, self._total_proxies)
            
            # 发送验证完成信号
            self.signals.validation_completed.emit(valid_count, self._total_proxies)
            self.signals.operation_result.emit(True, f"验证完成: {valid_count}/{self._total_proxies} 个有效")
            
            # 刷新代理列表
            await self.refresh_proxies()
            
            self._validating_all = False
            return True
        except Exception as e:
            self._logger.exception(f"验证所有代理时出错: {e}")
            self.signals.operation_result.emit(False, f"验证代理异常: {str(e)}")
            self._validating_all = False
            return False
    
    async def delete_proxy(self, proxy_id: int =None):
        """删除单个代理
        
        Args:
            proxy_id: 代理ID
        """
        try:
            # 调用服务层删除代理
            result = await self.proxy_service.delete_proxy(proxy_id)
            
            if result:
                # 刷新代理列表
                await self.refresh_proxies()
                self.signals.operation_result.emit(True, "成功删除代理")
                return True
            else:
                self.signals.operation_result.emit(False, "删除代理失败")
                return False
        except Exception as e:
            self._logger.exception(f"删除代理时出错: {e}")
            self.signals.operation_result.emit(False, f"删除代理异常: {str(e)}")
            return False
    
    async def delete_all_proxies(self):
        """删除所有代理"""
        try:
            # 调用服务层删除所有代理
            result = await self.proxy_service.delete_all_proxies()
            
            if result:
                # 刷新代理列表
                await self.refresh_proxies()
                self.signals.operation_result.emit(True, "成功删除所有代理")
                return True
            else:
                self.signals.operation_result.emit(False, "删除所有代理失败")
                return False
        except Exception as e:
            self._logger.exception(f"删除所有代理时出错: {e}")
            self.signals.operation_result.emit(False, f"删除所有代理异常: {str(e)}")
            return False
    
    async def set_proxy_active(self, proxy_id: int, is_active: bool):
        """设置代理激活状态
        
        Args:
            proxy_id: 代理ID
            is_active: 是否激活
        """
        try:
            # 调用服务层设置代理激活状态
            result = await self.proxy_service.set_proxy_active(proxy_id, is_active)
            
            if result:
                # 刷新代理列表
                await self.refresh_proxies()
                self.signals.operation_result.emit(True, f"成功{'激活' if is_active else '禁用'}代理")
                return True
            else:
                self.signals.operation_result.emit(False, f"{'激活' if is_active else '禁用'}代理失败")
                return False
        except Exception as e:
            self._logger.exception(f"设置代理激活状态时出错: {e}")
            self.signals.operation_result.emit(False, f"设置代理状态异常: {str(e)}")
            return False
    
    async def refresh_proxies(self, offset: int = None, limit: int = None):
        """刷新代理列表
        
        Args:
            offset: 偏移量
            limit: 限制数量
            
        Returns:
            Tuple[List[ProxyModel], int]: (代理列表, 总数)
        """
        try:
            # 使用指定的分页参数或当前分页参数
            offset = self._current_offset if offset is None else offset
            limit = self._current_limit if limit is None else limit
            
            # 更新当前分页参数
            self._current_offset = offset
            self._current_limit = limit
            
            # 调用服务层刷新代理列表
            await self.proxy_service.refresh_proxies(offset, limit)
            
            # 获取代理列表和总数
            proxies = await self.proxy_service.get_proxies(offset, limit)
            total = await self.proxy_service.get_proxy_count()
            
            # 发送代理列表更新信号
            self.signals.proxies_updated.emit(proxies, total)
            
            return proxies, total
        except Exception as e:
            self._logger.exception(f"刷新代理列表时出错: {e}")
            self.signals.operation_result.emit(False, f"刷新代理列表异常: {str(e)}")
            return [], 0
    
    async def start_service(self):
        """启动代理服务"""
        try:
            # 调用服务层启动服务（耗时任务，已在服务层放入子线程）
            result = await self.proxy_service.start_service()
            
            if result:
                self.signals.operation_result.emit(True, "代理服务启动成功")
                self.signals.service_status_changed.emit(True)
                return True
            else:
                self.signals.operation_result.emit(False, "代理服务启动失败")
                self.signals.service_status_changed.emit(False)
                return False
        except Exception as e:
            self._logger.exception(f"启动代理服务时出错: {e}")
            self.signals.operation_result.emit(False, f"启动代理服务异常: {str(e)}")
            self.signals.service_status_changed.emit(False)
            return False
    
    async def stop_service(self):
        """停止代理服务"""
        try:
            # 调用服务层停止服务（耗时任务，已在服务层放入子线程）
            result = await self.proxy_service.stop_service()
            
            if result:
                self.signals.operation_result.emit(True, "代理服务停止成功")
                self.signals.service_status_changed.emit(False)
                return True
            else:
                self.signals.operation_result.emit(False, "代理服务停止失败")
                self.signals.service_status_changed.emit(True)
                return False
        except Exception as e:
            self._logger.exception(f"停止代理服务时出错: {e}")
            self.signals.operation_result.emit(False, f"停止代理服务异常: {str(e)}")
            return False
    
    async def check_service_status(self) -> bool:
        """异步检查服务状态
        
        Returns:
            bool: 服务是否运行中
        """
        try:
            # 调用服务层异步检查服务状态
            status = await self.proxy_service.async_check_service_status()
            self.signals.service_status_changed.emit(status)
            return status
        except Exception as e:
            self._logger.exception(f"异步检查服务状态时出错: {e}")
            self.signals.operation_result.emit(False, f"检查服务状态异常: {str(e)}")
            return False
            
    async def restart_service(self):
        """重启代理服务"""
        try:
            # 调用服务层重启服务（耗时任务，已在服务层放入子线程）
            result = await self.proxy_service.restart_service()
            
            if result:
                self.signals.operation_result.emit(True, "代理服务重启成功")
                self.signals.service_status_changed.emit(True)
                return True
            else:
                self.signals.operation_result.emit(False, "代理服务重启失败")
                # 异步检查服务状态
                status = await self.proxy_service.async_check_service_status()
                self.signals.service_status_changed.emit(status)
                return False
        except Exception as e:
            self._logger.exception(f"重启代理服务时出错: {e}")
            self.signals.operation_result.emit(False, f"重启代理服务异常: {str(e)}")
            return False
    
    def get_service_status(self) -> bool:
        """获取代理服务状态
        
        Returns:
            bool: 服务是否运行中
        """
        try:
            # 调用服务层获取服务状态
            status = self.proxy_service.get_service_status()
            self.signals.service_status_changed.emit(status)
            return status
        except Exception as e:
            self._logger.exception(f"获取服务状态时出错: {e}")
            self.signals.operation_result.emit(False, f"获取服务状态异常: {str(e)}")
            return False
    
    async def remove_service(self):
        """彻底移除代理服务"""
        try:
            # 调用服务层移除服务
            result = self.proxy_service.remove_service()
            
            if result:
                self.signals.operation_result.emit(True, "代理服务移除成功")
                self.signals.service_status_changed.emit(False)
                return True
            else:
                self.signals.operation_result.emit(False, "代理服务移除失败")
                return False
        except Exception as e:
            self._logger.exception(f"移除代理服务时出错: {e}")
            self.signals.operation_result.emit(False, f"移除代理服务异常: {str(e)}")
            return False
    
    async def validate_proxies(self, proxy_ids: List[int]):
        """验证指定的代理列表
        
        Args:
            proxy_ids: 代理ID列表
            
        Returns:
            bool: 验证是否成功完成
        """
        try:
            # 避免重复验证
            if self._validating_all:
                self.signals.operation_result.emit(False, "验证任务已在进行中")
                return False
            
            self._validating_all = True
            self._validated_count = 0
            
            # 获取要验证的代理数量
            self._total_proxies = len(proxy_ids)
            
            if self._total_proxies == 0:
                self.signals.operation_result.emit(False, "没有代理需要验证")
                self._validating_all = False
                return False
            
            # 发送开始验证信号
            self.signals.operation_result.emit(True, f"开始验证 {self._total_proxies} 个代理")
            
            # 调用服务层验证指定代理
            valid_count = 0
            
            async for proxy_id, is_valid, response_time in self.proxy_service.validate_proxies(proxy_ids):
                # 更新进度
                self._validated_count += 1
                
                # 统计有效代理
                if is_valid:
                    valid_count += 1
                
                # 发送验证结果信号
                self.signals.proxy_validated.emit(proxy_id, is_valid, response_time if response_time else 0)
                
                # 发送进度信号
                self.signals.validation_progress.emit(self._validated_count, self._total_proxies)
            
            # 发送验证完成信号
            self.signals.validation_completed.emit(valid_count, self._total_proxies)
            self.signals.operation_result.emit(True, f"验证完成: {valid_count}/{self._total_proxies} 个有效")
            
            # 刷新代理列表
            await self.refresh_proxies()
            
            self._validating_all = False
            return True
        except Exception as e:
            self._logger.exception(f"验证指定代理时出错: {e}")
            self.signals.operation_result.emit(False, f"验证代理异常: {str(e)}")
            self._validating_all = False
            return False
    
    async def has_local_proxies(self) -> bool:
        """检查是否存在本地代理
        
        Returns:
            bool: 是否存在本地代理
        """
        try:
            return await self.proxy_service.has_local_proxies()
        except Exception as e:
            self._logger.exception(f"检查本地代理时出错: {e}")
            self.signals.operation_result.emit(False, f"检查本地代理状态异常: {str(e)}")
            return False
    
    async def add_proxies_from_text(self, ip_text: str, port_start: int, port_end: int,
                                username: str, password: str, proxy_type: str, is_local: bool) -> Tuple[bool, List[int]]:
        """从文本添加多个代理
        
        Args:
            ip_text: IP文本，可能包含多行
            port_start: 起始端口
            port_end: 结束端口
            username: 用户名
            password: 密码
            proxy_type: 代理类型
            is_local: 是否本地代理
            
        Returns:
            Tuple[bool, List[int]]: (是否成功, 新添加的代理ID列表)
        """
        try:
            # 调用服务层添加代理
            count, proxy_ids = await self.proxy_service.add_proxies_from_text(
                ip_text=ip_text,
                port_start=port_start,
                port_end=port_end,
                username=username,
                password=password,
                proxy_type=proxy_type,
                is_local=is_local
            )
            
            if count > 0:
                # 刷新代理列表
                await self.refresh_proxies()
                self.signals.operation_result.emit(True, f"成功添加 {count} 个代理")
                return True, proxy_ids
            else:
                self.signals.operation_result.emit(False, "添加代理失败")
                return False, []
                
        except ValueError as e:
            # IP格式错误等验证错误
            self._logger.error(f"添加代理失败: {e}")
            self.signals.operation_result.emit(False, str(e))
            return False, []
        except Exception as e:
            self._logger.exception(f"添加代理失败: {e}")
            self.signals.operation_result.emit(False, f"添加代理异常: {str(e)}")
            return False, []
    
    async def delete_all_invalid_proxies(self):
        """删除所有失效代理"""
        try:
            count = await self.proxy_service.delete_all_invalid_proxies()
            if count > 0:
                await self.refresh_proxies()
                self.signals.operation_result.emit(True, f"成功删除{count}个失效代理")
                return True
            else:
                self.signals.operation_result.emit(False, "没有失效代理可删除")
                return False
        except Exception as e:
            self._logger.exception(f"删除所有失效代理时出错: {e}")
            self.signals.operation_result.emit(False, f"删除所有失效代理异常: {str(e)}")
            return False
    