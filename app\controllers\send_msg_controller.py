import asyncio
import datetime # Added import for datetime used in create_new_task
from typing import Dict, List, Optional, Any, Callable, Union, Tuple

from PySide6.QtCore import QObject, Signal, Slot
from qasync import asyncSlot
from app.services.message_sending_service import MessageSendingService
from data.models.message import MessageTaskStatus

from utils.logger import get_logger

class SendMessageController(QObject):
    """消息发送功能的控制器，连接UI和Service层"""
    
    # 定义信号
    task_created = Signal(dict)       # 创建任务成功信号
    task_updated = Signal(dict)       # 更新任务信号
    task_deleted = Signal(str)        # 删除任务信号
    task_status_changed = Signal(str, str)  # 任务状态变更信号
    account_limit_updated = Signal(str)  # 账户发送限额更新信号
    task_progress_updated = Signal(str, int, int, int)  # task_id, processed, success, failed

    def __init__(self, message_service: MessageSendingService, parent: Optional[QObject] = None):
        super().__init__(parent)
        self._service = message_service
        self._logger = get_logger("controllers.send_msg")
        
        # 连接服务层信号
        if self._service:
            self._connect_service_signals()
        else:
            self._logger.warning("未提供MessageSendingService，UI将无法正常工作")
    
    def _connect_service_signals(self):
        """连接服务层的信号"""
        # 任务创建信号
        self._service.task_created.connect(self._on_task_created)
        # 任务更新信号
        self._service.task_updated.connect(self._on_task_updated)
        # 任务删除信号
        self._service.task_deleted.connect(self._on_task_deleted)
        # 任务状态变更信号
        self._service.task_status_changed.connect(self._on_task_status_changed)
        # 任务进度更新信号
        self._service.task_progress_updated.connect(self._on_task_progress_updated)
    
    def _on_task_created(self, task_data: dict):
        """处理任务创建事件"""
        self._logger.info(f"收到任务创建事件: {task_data.get('id')}")
        # 转发信号给UI
        self.task_created.emit(task_data)
    
    def _on_task_updated(self, task_data: dict):
        """处理任务更新事件"""
        self._logger.debug(f"收到任务更新事件: {task_data.get('id')}")
        # 转发信号给UI
        self.task_updated.emit(task_data)
    
    def _on_task_deleted(self, task_id: str):
        """处理任务删除事件"""
        self._logger.info(f"收到任务删除事件: {task_id}")
        # 转发信号给UI
        self.task_deleted.emit(task_id)
    
    def _on_task_status_changed(self, task_id: str, status: str):
        """处理任务状态变更事件"""
        self._logger.info(f"收到任务状态变更事件: {task_id} -> {status}")
        # 转发信号给UI
        self.task_status_changed.emit(task_id, status)
    
    def _on_task_progress_updated(self, task_id: str, processed: int, success: int, failed: int):
        """处理任务进度更新事件"""
        self._logger.debug(f"收到任务进度更新事件: {task_id}, 已处理: {processed}, 成功: {success}, 失败: {failed}")
        # 转发信号给UI
        self.task_progress_updated.emit(task_id, processed, success, failed)
    

    async def get_all_tasks(self, page: int = 1, per_page: int = 50) -> List[Dict]:
        """获取所有消息任务
        
        Args:
            page: 页码
            per_page: 每页数量
            
        Returns:
            任务列表
        """
        self._logger.debug(f"获取所有任务: 页码={page}, 每页={per_page}")
        
        try:
            tasks = await self._service.get_all_tasks(page, per_page)
            return tasks
        except Exception as e:
            error_msg = f"获取任务列表失败: {str(e)}"
            self._logger.error(error_msg, exc_info=True)
            return []

    async def get_task_by_id(self, task_id: str) -> Optional[Dict]:
        """根据ID获取任务
        
        Args:
            task_id: 任务ID
            
        Returns:
            任务信息
        """
        self._logger.debug(f"根据ID获取任务: {task_id}")
        
        try:
            task = await self._service.get_task_by_id(task_id)
            return task
        except Exception as e:
            error_msg = f"获取任务 {task_id} 失败: {str(e)}"
            self._logger.error(error_msg, exc_info=True)
            return None
  


    async def delete_task(self, task_id: str) -> bool:
        """删除任务
        
        Args:
            task_id: 任务ID
            
        Returns:
            操作是否成功
        """
        self._logger.info(f"删除任务: {task_id}")
        
        try:
            success = await self._service.delete_task(task_id)
            return success
        except Exception as e:
            error_msg = f"删除任务 {task_id} 失败: {str(e)}"
            self._logger.error(error_msg, exc_info=True)
            return False
    

    async def start_task(self, task_id: str) -> Tuple[bool, str]:
        """启动任务
        
        Args:
            task_id: 任务ID
            
        Returns:
            Tuple[bool, str]: (操作是否成功, 错误消息)
        """
        self._logger.info(f"启动任务: {task_id}")
        
        try:
            success, error_msg = await self._service.start_task(int(task_id))
            if not success:
                self._logger.error(f"启动任务 {task_id} 失败: {error_msg}")
            return success, error_msg
        except Exception as e:
            error_msg = f"启动任务 {task_id} 失败: {str(e)}"
            self._logger.error(error_msg, exc_info=True)
            return False, error_msg

 



    
    async def create_task(self, task_data: dict) -> dict:
        """创建新的消息发送任务（使用完整的任务数据字典）
        
        Args:
            task_data: 完整的任务数据字典，包含所有必要信息
            
        Returns:
            创建的任务信息
        """
        self._logger.info(f"创建任务: {task_data.get('task_name')}")
        
        try:
            # 直接将完整任务数据传递给服务层
            task = await self._service.create_task(task_data)
            
            self._logger.info(f"任务创建成功: ID={task.get('id')}, 名称={task_data.get('task_name')}")
            return task
            
        except Exception as e:
            error_msg = f"创建任务失败: {str(e)}"
            self._logger.error(error_msg, exc_info=True)
            raise

    async def pause_task(self, task_id: str) -> bool:
        """暂停任务
        Args:
            task_id: 任务ID
        Returns:
            操作是否成功
        """
        self._logger.info(f"暂停任务: {task_id}")
        try:
            await self.update_task_status(str(task_id), MessageTaskStatus.PAUSED.value)
            return True
        except Exception as e:
            self._logger.error(f"暂停任务 {task_id} 失败: {str(e)}", exc_info=True)
            return False

    async def update_task_status(self, task_id: str, status: str) -> bool:
        """更新任务状态
        Args:
            task_id: 任务ID
            status: 新状态
        Returns:
            bool: 更新是否成功
        """
        self._logger.info(f"更新任务状态: {task_id} -> {status}")
        try:
            # 如果传入的是枚举，取其值
            if isinstance(status, MessageTaskStatus):
                status_value = status.value
            else:
                status_value = status
            success = await self._service.update_task_status(task_id, status_value)
            return success
        except Exception as e:
            self._logger.error(f"更新任务状态 {task_id} 失败: {str(e)}", exc_info=True)
            return False

    async def get_monitor_tasks(self) -> list:
        """
        获取所有监听任务及其用户数量
        Returns:
            List[Dict]: [{id, name, user_count, ...}]
        """
        return await self._service.get_monitor_tasks()

    async def get_all_tasks_with_stats(self) -> List[Dict]:
        return await self._service.get_all_tasks_with_stats()

    async def get_global_stats(self) -> dict:
        """获取全局统计数据，便于UI统计卡片调用"""
        return await self._service.get_global_stats()
