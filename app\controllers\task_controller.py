#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
任务控制器模块
负责处理UI和任务服务之间的交互
"""

import asyncio
from typing import Dict, List, Any, Optional, Tuple, Union, Callable
from datetime import datetime

from PySide6.QtCore import QObject, Signal, Slot, Qt

from app.services.task_service import get_task_service, TaskService
from core.task_manager import TaskPriority
from utils.logger import get_logger


class TaskController(QObject):
    """
    任务控制器类
    负责处理UI和任务服务之间的交互
    """
    
    # 信号定义
    task_added = Signal(str, dict)  # task_id, task_info
    task_removed = Signal(str)  # task_id
    task_status_changed = Signal(str, str)  # task_id, status
    task_progress_updated = Signal(str, int, int, dict)  # task_id, current, total, extra_info
    task_log = Signal(str, str, str)  # task_id, message, level
    
    def __init__(self):
        """初始化任务控制器"""
        super().__init__()
        self._logger = get_logger("app.controllers.task_controller")
        self._task_service = get_task_service()
        
        if not self._task_service:
            self._logger.error("任务服务未初始化")
            return
        
        # 连接任务服务信号
        self._connect_task_service_signals()
        
        self._logger.info("任务控制器已初始化")
    
    def _connect_task_service_signals(self):
        """连接任务服务信号"""
        if not self._task_service:
            return
        
        self._task_service.task_added.connect(self._on_task_added)
        self._task_service.task_removed.connect(self._on_task_removed)
        self._task_service.task_status_changed.connect(self._on_task_status_changed)
        self._task_service.task_progress_updated.connect(self._on_task_progress_updated)
        self._task_service.task_log.connect(self._on_task_log)
    
    def _on_task_added(self, task_id: str, task_info: dict):
        """处理任务添加事件"""
        self.task_added.emit(task_id, task_info)
    
    def _on_task_removed(self, task_id: str):
        """处理任务移除事件"""
        self.task_removed.emit(task_id)
    
    def _on_task_status_changed(self, task_id: str, status: str):
        """处理任务状态变化事件"""
        self.task_status_changed.emit(task_id, status)
    
    def _on_task_progress_updated(self, task_id: str, current: int, total: int, extra_info: dict):
        """处理任务进度更新事件"""
        self.task_progress_updated.emit(task_id, current, total, extra_info)
    
    def _on_task_log(self, task_id: str, message: str, level: str):
        """处理任务日志事件"""
        self.task_log.emit(task_id, message, level)
    
    @Slot(int, int, bool, result=str)
    def create_message_sending_task(self, task_db_id: int, batch_size: int = 100, auto_start: bool = False) -> str:
        """
        创建消息群发任务
        
        Args:
            task_db_id: 数据库中的任务ID
            batch_size: 每批处理的目标数量
            auto_start: 是否自动启动任务
            
        Returns:
            str: 任务ID
        """
        if not self._task_service:
            self._logger.error("任务服务未初始化，无法创建任务")
            return ""
        
        # 使用事件循环运行异步方法
        loop = asyncio.get_event_loop()
        task_id = loop.run_until_complete(
            self._task_service.create_message_sending_task(
                task_db_id, batch_size, auto_start, TaskPriority.NORMAL
            )
        )
        
        return task_id
    
    @Slot(str, result=bool)
    def start_task(self, task_id: str) -> bool:
        """
        启动任务
        
        Args:
            task_id: 任务ID
            
        Returns:
            bool: 是否成功启动
        """
        if not self._task_service:
            self._logger.error("任务服务未初始化，无法启动任务")
            return False
        
        loop = asyncio.get_event_loop()
        return loop.run_until_complete(self._task_service.start_task(task_id))
    
    @Slot(str, result=bool)
    def pause_task(self, task_id: str) -> bool:
        """
        暂停任务
        
        Args:
            task_id: 任务ID
            
        Returns:
            bool: 是否成功暂停
        """
        if not self._task_service:
            self._logger.error("任务服务未初始化，无法暂停任务")
            return False
        
        loop = asyncio.get_event_loop()
        return loop.run_until_complete(self._task_service.pause_task(task_id))
    
    @Slot(str, result=bool)
    def resume_task(self, task_id: str) -> bool:
        """
        恢复任务
        
        Args:
            task_id: 任务ID
            
        Returns:
            bool: 是否成功恢复
        """
        if not self._task_service:
            self._logger.error("任务服务未初始化，无法恢复任务")
            return False
        
        loop = asyncio.get_event_loop()
        return loop.run_until_complete(self._task_service.resume_task(task_id))
    
    @Slot(str, result=bool)
    def stop_task(self, task_id: str) -> bool:
        """
        停止任务
        
        Args:
            task_id: 任务ID
            
        Returns:
            bool: 是否成功停止
        """
        if not self._task_service:
            self._logger.error("任务服务未初始化，无法停止任务")
            return False
        
        loop = asyncio.get_event_loop()
        return loop.run_until_complete(self._task_service.stop_task(task_id))
    
    @Slot(str, result=bool)
    def remove_task(self, task_id: str) -> bool:
        """
        移除任务
        
        Args:
            task_id: 任务ID
            
        Returns:
            bool: 是否成功移除
        """
        if not self._task_service:
            self._logger.error("任务服务未初始化，无法移除任务")
            return False
        
        loop = asyncio.get_event_loop()
        return loop.run_until_complete(self._task_service.remove_task(task_id))
    
    @Slot()
    def stop_all_tasks(self) -> None:
        """停止所有任务"""
        if not self._task_service:
            self._logger.error("任务服务未初始化，无法停止所有任务")
            return
        
        loop = asyncio.get_event_loop()
        loop.run_until_complete(self._task_service.stop_all_tasks())
    
    @Slot(str, result='QVariant')
    def get_task_info(self, task_id: str) -> Optional[Dict[str, Any]]:
        """
        获取任务信息
        
        Args:
            task_id: 任务ID
            
        Returns:
            Optional[Dict[str, Any]]: 任务信息字典，如果不存在则返回None
        """
        if not self._task_service:
            self._logger.error("任务服务未初始化，无法获取任务信息")
            return None
        
        return self._task_service.get_task_info(task_id)
    
    @Slot(result='QVariantList')
    def get_all_tasks(self) -> List[Dict[str, Any]]:
        """
        获取所有任务信息
        
        Returns:
            List[Dict[str, Any]]: 所有任务信息的列表
        """
        if not self._task_service:
            self._logger.error("任务服务未初始化，无法获取所有任务")
            return []
        
        return self._task_service.get_all_tasks()
    
    @Slot(result='QVariantList')
    def get_running_tasks(self) -> List[Dict[str, Any]]:
        """
        获取所有正在运行的任务信息
        
        Returns:
            List[Dict[str, Any]]: 正在运行的任务信息列表
        """
        if not self._task_service:
            self._logger.error("任务服务未初始化，无法获取运行中任务")
            return []
        
        return self._task_service.get_running_tasks()
    
    @Slot(result='QVariant')
    def get_task_count(self) -> Dict[str, int]:
        """
        获取各状态任务的数量
        
        Returns:
            Dict[str, int]: 各状态任务数量
        """
        if not self._task_service:
            self._logger.error("任务服务未初始化，无法获取任务数量")
            return {"total": 0}
        
        return self._task_service.get_task_count()


# 创建全局任务控制器实例
task_controller = TaskController() 