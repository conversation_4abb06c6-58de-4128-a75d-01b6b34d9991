"""
Telegram 监控页面控制器模块
"""
from PySide6.QtCore import QObject, Slot, Signal, QTimer
from PySide6.QtWidgets import QFrame, QLabel, QHBoxLayout, QListWidgetItem, QMessageBox
from qasync import asyncSlot
from qfluentwidgets import ToolButton, StrongBodyLabel, FluentIcon as FIF, InfoBar, InfoBarPosition
from typing import Dict, Any, List, Optional, Tuple, Union
import datetime
import traceback
import asyncio

from app.services.monitor_service import MonitorTaskService
from utils.logger import get_logger
from app.controllers.account_controller import AccountController


class TelegramMonitorController(QObject):
    """
    Telegram 监控页面的控制器。
    负责处理任务列表显示、任务操作（添加、删除、修改）、数据显示、日志记录等。
    """
    # ================ 信号定义 ================
    
    # 状态和操作结果信号
    loading_started = Signal(str)  # 开始加载数据 (message)
    loading_finished = Signal()    # 数据加载完成
    operation_success = Signal(str, str)  # 操作成功 (title, message)
    operation_failed = Signal(str)  # 操作失败 (message)
    
    # 监听日志相关信号
    log_received = Signal(str, str)  # 接收监听日志 (message, level)
    
    # 任务列表相关信号
    tasks_loaded = Signal(list)  # 任务列表加载完成 (task_list)
    task_added = Signal(dict, str)  # 任务添加结果 (task_dict, message)
    task_updated = Signal(str, bool, str)  # 任务更新结果 (task_id, success, message)
    task_deleted = Signal(str, bool, str)  # 任务删除结果 (task_id, success, message)
    
    # 详情页面相关信号
    task_details_loaded = Signal(str, dict)  # 任务详情加载完成 (task_id, details)
    task_users_loaded = Signal(list, dict)  # 任务用户数据加载完成 (users_data, pagination_info)
    all_users_loaded = Signal(list, dict)  # 所有用户数据加载完成 (users_data, pagination_info)
    task_selection_changed = Signal(str)  # 任务选择改变 (task_id)
    
    # 统计数据信号
    total_users_count_loaded = Signal(dict)  # 总用户数量统计数据加载完成 (stats_data)
    task_users_stats_loaded = Signal(dict)  # 任务用户统计数据加载完成 (stats_data)
    
    # 导出相关信号
    export_completed = Signal(bool, str)  # 导出完成 (success, message)
    
    def __init__(self, parent=None, monitor_task_service: MonitorTaskService = None, account_controller: AccountController = None):
        """
        初始化 TelegramMonitorController。

        Args:
            parent (QObject, optional): 父对象。 Defaults to None.
            monitor_task_service (MonitorTaskService, optional): 监控任务服务实例.
            account_controller (AccountController, optional): 账户控制器实例.
        """
        super().__init__(parent)
        self._logger = get_logger("app.controllers.telegram_monitor_controller")
        self._logger.info("初始化 Telegram 监控控制器")
        
        if monitor_task_service is None:
            self._logger.warning("MonitorTaskService 未提供，将使用默认实例。")
            # In a real scenario, you might want to raise an error or ensure it's always provided.
            # For now, let's assume it's provided from MainWindow correctly.
            # self._task_service = MonitorTaskService() # Fallback, but ideally should be injected
        self._task_service = monitor_task_service
        self._account_controller = account_controller # Store account_controller if needed for task creation context

        self._current_selected_task_id = None
        self._current_page = 1
        self._current_page_size = 10
        self._current_search_term = ""
        
        # 使用QTimer延迟连接信号，确保服务实例和信号源已完全初始化
        QTimer.singleShot(100, self._connect_service_signals)
        
        self._logger.debug("控制器初始化完成")

    def _connect_service_signals(self):
        """延迟连接服务层信号，确保信号源已完全初始化"""
        try:
            if self._task_service:
                self._logger.debug("尝试连接监听日志信号...")
                self._task_service.ListenLog.connect(self._on_listen_log_received)
                self._logger.debug("监听日志信号连接成功")
            else:
                self._logger.warning("无法连接监听日志信号：_task_service为None")
        except Exception as e:
            self._logger.error(f"连接监听日志信号失败: {str(e)}", exc_info=True)
            # 即使信号连接失败，也不影响其他功能

    # 监听日志处理方法
    def _on_listen_log_received(self, message: str):
        """
        处理服务层发来的监听日志
        
        Args:
            message: 日志消息
        """
        self._logger.debug(f"收到监听日志: {message}")
        self.log_received.emit(message, "info")
    
    @Slot()
    def add_task(self):
        """向视图层通知需要打开添加任务对话框"""
        self._logger.info("发出添加任务请求")
        # 视图层 (MonitorView) 会创建 AddMonitorView 对话框
        # AddMonitorView 将会调用本 Controller 的 submit_new_task 方法
        # 此处无需直接操作对话框
    
    @Slot(str)
    def edit_task(self, task_id: str):
        """
        发出编辑任务请求
        
        Args:
            task_id: 要编辑的任务ID
        """
        self._logger.info(f"发出编辑任务请求: 任务ID {task_id}")
        # 此功能尚未实现
    
    @Slot()
    async def delete_current_task(self):
        """删除当前选中的任务"""
        if not self._current_selected_task_id:
            self.operation_failed.emit("请先选择一个任务再删除")
            return
            
        await self.delete_task(self._current_selected_task_id)

    @Slot(str)
    @asyncSlot()
    async def load_tasks(self, search_term: str = ""):
        """
        加载监控任务列表。
        
        Args:
            search_term: 可选的搜索关键词
        """
        self._logger.info(f"加载监控任务列表 (搜索关键词: '{search_term}')")
        self.loading_started.emit("正在加载任务列表...")
        
        try:
            # 从服务层获取任务列表
            tasks = await self._task_service.get_all_tasks(search_term)
            self.tasks_loaded.emit(tasks)
            
            # 加载总体统计数据（使用特殊版本，不发送loading_finished信号）
            await self._load_total_users_count_internal()
            
            self._logger.info(f"成功加载 {len(tasks)} 个任务")
        except Exception as e:
            self._logger.error(f"加载任务列表失败: {str(e)}", exc_info=True)
            self.operation_failed.emit(f"加载任务列表失败: {str(e)}")
        finally:
            self.loading_finished.emit()
    
    @Slot()
    async def load_total_users_count(self):
        """
        加载所有任务的总体统计数据
        供视图直接调用的公共方法
        """
        self._logger.info("加载总体统计数据")
        self.loading_started.emit("正在加载统计数据...")
        
        try:
            await self._load_total_users_count_internal()
        finally:
            self.loading_finished.emit()

    async def _load_total_users_count_internal(self):
        """
        内部版本的总体统计数据加载功能，不发送加载完成信号
        """
        try:
            if not self._task_service:
                self.operation_failed.emit("任务服务未初始化，无法获取统计数据")
                return
                
            # 获取完整的全局统计数据
            stats = await self._task_service.get_global_stats()
            
            # 发送包含完整统计数据的信号
            self.total_users_count_loaded.emit(stats)
            self._logger.info(f"成功加载总体统计数据: 总用户数 {stats.get('total_users')}, 今日采集 {stats.get('today_users')}, 日均采集 {stats.get('avg_daily')}, 运行天数 {stats.get('running_days')}")
        except Exception as e:
            self._logger.error(f"加载总体统计数据失败: {str(e)}", exc_info=True)
            self.operation_failed.emit(f"加载总体统计数据失败: {str(e)}")

    @Slot(str)
    async def delete_task(self, task_id: str):
        """
        删除指定的任务
        
        Args:
            task_id: 要删除的任务ID
        """
        if not task_id:
            return
            
        self._logger.info(f"删除任务: ID {task_id}")
        
        try:
            # 调用服务层删除任务
            success, message =await self._task_service.delete_task(task_id)
            
            if success:
                # 如果删除的是当前选中的任务，清除选择状态
                if task_id == self._current_selected_task_id:
                    self._current_selected_task_id = None
                
                self._logger.info(f"任务已删除: {task_id}")
                self.task_deleted.emit(task_id, True, message)
                self.load_tasks()  # 重新加载任务列表
            else:
                self._logger.warning(f"删除任务失败: {message}")
                self.task_deleted.emit(task_id, False, message)
                
        except Exception as e:
            self._logger.error(f"删除任务异常: {str(e)}", exc_info=True)
            self.operation_failed.emit(f"删除任务异常: {str(e)}")

    @Slot(str)
    async def select_task(self, task_id: str):
        """
        选择任务
        
        Args:
            task_id: 要选择的任务ID
        """
        self._logger.info(f"选择任务: {task_id}")
        if task_id == self._current_selected_task_id:
            return  # 已经选中了该任务，不执行任何操作
            
        self._current_selected_task_id = task_id
        self.task_selection_changed.emit(task_id)
        
        if task_id:
            await self.load_task_details(task_id)
            # 不再加载全局总用户数量
            # await self.load_total_users_count() 

    @Slot(str)
    async def load_task_details(self, task_id: str):
        """
        加载指定任务的详细信息
        
        Args:
            task_id: 任务ID
        """
        if not task_id:
            return
            
        self.loading_started.emit("正在加载任务详情...")
        self._logger.info(f"加载任务详情: ID {task_id}")
        
        try:
            # 从服务层获取任务详情（异步调用）
            task_detail = await self._task_service.get_task_detail(task_id)
            
            if task_detail:
                # 获取任务统计数据 - 通过服务层获取
                if self._task_service:
                    stats = await self._task_service.get_task_stats(task_id)
                    self._logger.debug(f"获取到任务 {task_id} 的统计数据: {stats}")
                    
                    # 添加到任务详情中
                    if not "stats" in task_detail:
                        task_detail["stats"] = stats
                
                # 发送信号通知任务详情加载完成
                self.task_details_loaded.emit(task_id, task_detail)
                
                # 重置分页并加载第一页用户数据
                self._current_page = 1
                self._current_search_term = ""
                await self.load_task_users(task_id, 1, self._current_page_size)
                
                # 记录日志
                task_name = task_detail.get("name", task_id)
                self._logger.info(f"成功加载任务详情: {task_name}")
            else:
                self.operation_failed.emit(f"无法加载任务详情: 任务ID {task_id}")
        except Exception as e:
            self._logger.error(f"加载任务详情失败: {str(e)}", exc_info=True)
            self.operation_failed.emit(f"加载任务详情失败: {str(e)}")
        finally:
            self.loading_finished.emit()

    async def get_task_data(self, task_id: str) -> Optional[Dict[str, Any]]:
        """
        获取任务数据用于编辑
        
        Args:
            task_id: 任务ID
            
        Returns:
            任务数据字典，未找到返回None
        """
        self._logger.info(f"获取任务数据: ID {task_id}")
        
        try:
            if not self._task_service:
                self._logger.warning("任务服务未初始化，无法获取任务数据")
                return None
                
            # 使用异步方法获取任务详情
            task_data = await self._task_service.get_task_by_id(task_id)
            
            if task_data:
                self._logger.info(f"成功获取任务数据: {task_id}")
                return task_data
            else:
                self._logger.warning(f"未找到任务数据: {task_id}")
                self.operation_failed.emit(f"未找到任务数据: {task_id}")
                return None
        except Exception as e:
            self._logger.error(f"获取任务数据异常: {str(e)}", exc_info=True)
            self.operation_failed.emit(f"获取任务数据异常: {str(e)}")
            return None

    def _clear_task_details(self):
        """清空右侧任务详情区域"""
        self._logger.debug("清空任务详情区域")
        # 不再直接操作UI，而是发送信号通知视图层
    
    def _update_stats_cards(self, stats: dict):
        """
        更新统计信息卡片
        
        Args:
            stats: 统计数据字典
        """
        # This method was part of the original thought process but seems to belong to the View.
        # The controller should emit data, and the view should update itself.
        # Keeping it here if it's called internally or was planned for specific controller logic.
        # For now, assume task_details_loaded signal carries this data to the view.
        self._logger.debug(f"Controller: _update_stats_cards called with {stats}. This should ideally be handled by the View.")

    def _update_users_table(self, users_data: list):
        """
        更新用户数据表格
        
        Args:
            users_data: 用户数据列表
        """
        # Similar to _update_stats_cards, this is view-specific logic.
        # The controller's role is to provide data via signals.
        self._logger.debug(f"Controller: _update_users_table called with {len(users_data)} users. This should be handled by the View.")

    def _update_pagination_controls(self, current_page: int, total_pages: int):
        """
        更新分页控件
        
        Args:
            current_page: 当前页码
            total_pages: 总页数
        """
        # View-specific logic.
        self._logger.debug(f"Controller: _update_pagination_controls called. Current: {current_page}, Total: {total_pages}. Should be View's role.")

    @Slot()
    async def _on_search_tasks(self):
        """
        处理任务列表搜索。
        """
        # This slot is likely connected to a UI element in the view.
        # The view should get the text and call a controller method like self.load_tasks(search_term)
        # For now, assuming this is a direct slot.
        # search_term = self.searchBox.text().strip() # Controller doesn't have UI elements like searchBox
        # Instead, this method should accept search_term as an argument if called from view.
        # Let's assume load_tasks is called with the search term from the view.
        self._logger.debug("Controller: _on_search_tasks. Task search should be initiated by View calling load_tasks(term).")

    @Slot(str, int, int, str)
    async def load_task_users(self, task_id: str, page: int = 1, page_size: int = 10, search_term: str = ""):
        """
        加载任务的用户数据
        
        Args:
            task_id: 任务ID
            page: 页码，默认为1
            page_size: 每页显示数量，默认为10
            search_term: 搜索关键词，默认为空
        """
        if not task_id:
            return
            
        self.loading_started.emit(f"正在加载第 {page} 页数据...")
        self._logger.info(f"加载任务用户数据: ID {task_id}, 页码 {page}, 每页 {page_size} 条")
        
        try:
            # 保存当前的分页信息
            self._current_page = page
            self._current_page_size = page_size
            self._current_search_term = search_term
            
            # 从服务层获取数据
            page_data = await self._task_service.get_task_users(
                task_id,
                page=page,
                page_size=page_size,
                search_term=search_term
            )
            
            if page_data:
                users_data = page_data.get("items", [])
                pagination_info = {
                    "current_page": page_data.get("current_page", 1),
                    "total_pages": page_data.get("total_pages", 1),
                    "total_items": page_data.get("total_items", 0),
                    "page_size": page_size
                }
                
                # 发送信号通知用户数据加载完成
                self.task_users_loaded.emit(users_data, pagination_info)
                self._logger.info(f"成功加载第 {page} 页用户数据，共 {len(users_data)} 条")
            else:
                self.operation_failed.emit(f"无法加载任务用户数据: 任务ID {task_id}")
        except Exception as e:
            self._logger.error(f"加载任务用户数据失败: {str(e)}", exc_info=True)
            self.operation_failed.emit(f"加载任务用户数据失败: {str(e)}")
        finally:
            self.loading_finished.emit()

    @Slot(int)
    async def go_to_page(self, page: int):
        """
        跳转到指定页码
        
        Args:
            page: 页码
        """
        if not self._current_selected_task_id or page <= 0:
            return
            
        self._logger.debug(f"跳转到第 {page} 页")
        await self.load_task_users(
            self._current_selected_task_id,
            page,
            self._current_page_size,
            self._current_search_term
        )

    @Slot()
    @asyncSlot()
    async def go_to_previous_page(self):
        """跳转到上一页（同步版本，供按钮点击连接）"""
        if not self._current_selected_task_id or self._current_page <= 1:
            return
        
        # 使用QTimer.singleShot执行异步操作
        await self._go_to_previous_page_async()

    @Slot()
    @asyncSlot()
    async def go_to_next_page(self):
        """跳转到下一页（同步版本，供按钮点击连接）"""
        if not self._current_selected_task_id:
            return
        
        # 使用QTimer.singleShot执行异步操作
        await self._go_to_next_page_async()

    async def _go_to_previous_page_async(self):
        """跳转到上一页的异步实现"""
        if not self._current_selected_task_id or self._current_page <= 1:
            return
        
        await self.go_to_page(self._current_page - 1)

    async def _go_to_next_page_async(self):
        """跳转到下一页的异步实现"""
        if not self._current_selected_task_id:
            return
        
        await self.go_to_page(self._current_page + 1)

    @Slot()
    @asyncSlot()
    async def go_to_previous_page_async(self):
        """跳转到上一页（异步版本，保持向后兼容）"""
        if not self._current_selected_task_id or self._current_page <= 1:
            return
        
        await self.go_to_page(self._current_page - 1)

    @Slot()
    @asyncSlot()
    async def go_to_next_page_async(self):
        """跳转到下一页（异步版本，保持向后兼容）"""
        if not self._current_selected_task_id:
            return
        
        await self.go_to_page(self._current_page + 1)

    @Slot(int)
    @asyncSlot()
    async def change_page_size(self, page_size: int):
        """
        更改每页显示数量
        
        Args:
            page_size: 每页显示数量
        """
        if not self._current_selected_task_id or page_size <= 0:
            return
            
        self._logger.debug(f"更改每页显示数量: {page_size}")
        self._current_page_size = page_size
        await self.load_task_users(
            self._current_selected_task_id,
            1,  # 切换每页数量时，默认跳到第一页
            page_size,
            self._current_search_term
        )

    @Slot(str)
    @asyncSlot()
    async def search_task_users(self, search_term: str):
        """
        搜索任务用户数据
        
        Args:
            search_term: 搜索关键词
        """
        if not self._current_selected_task_id:
            return
            
        self._logger.debug(f"搜索任务用户数据: {search_term}")
        await self.load_task_users(
            self._current_selected_task_id,
            1,  # 搜索时，默认跳到第一页
            self._current_page_size,
            search_term
        )

    @Slot(str, str)
    async def export_task_data(self, task_id: str, file_path: str):
        """
        导出任务数据
        
        Args:
            task_id: 任务ID
            file_path: 导出文件路径
        """
        if not task_id or not file_path:
            return
            
        self.loading_started.emit("正在导出数据...")
        self._logger.info(f"导出任务数据: ID {task_id}, 文件路径 {file_path}")
        
        try:
            # 调用服务层导出数据
            success, message = await self._task_service.export_task_data(task_id, file_path)
            
            if success:
                self._logger.info(f"数据导出成功: {file_path}")
                self.operation_success.emit("导出成功", f"数据已导出到 {file_path}")
            else:
                self._logger.error(f"数据导出失败: {message}")
                self.operation_failed.emit(f"导出失败: {message}")
        except Exception as e:
            self._logger.error(f"导出数据异常: {str(e)}", exc_info=True)
            self.operation_failed.emit(f"导出异常: {str(e)}")
        finally:
            self.loading_finished.emit()

    @Slot(dict)
    async def submit_new_task(self, task_data: Dict[str, Any]) -> Tuple[bool, Union[Dict[str, Any], str]]:
        """
        接收来自 AddMonitorView 的新任务数据并创建任务。
        
        Args:
            task_data: 任务配置数据字典
            
        Returns:
            Tuple[bool, Union[Dict[str, Any], str]]: (成功标志, 成功返回任务信息字典，失败返回错误消息)
        """
        self._logger.info(f"控制器: 收到新任务提交数据: {task_data.get('name', '未知任务')}")
        
        # 验证监控群组是否都有关联账户
        monitored_chats = task_data.get("monitored_chats", [])
        for chat in monitored_chats:
            if not chat.get("account_phone"):
                return False, "每个监控的群组/频道必须关联一个账户。"
                
        self.loading_started.emit(f"正在创建新任务...")
        
        try:
            if not self._task_service:
                msg = "任务服务未初始化。"
                self.operation_failed.emit(msg)
                self.loading_finished.emit()
                return False, msg

            success, result_or_error = await self._task_service.create_and_start_task(task_data)
            
            if success:
                new_task_dict = result_or_error
                self.task_added.emit(new_task_dict, "任务已成功创建并启动。")
                self.operation_success.emit("任务创建成功", f"任务 '{task_data.get('name', '')}' 已创建并启动。")
                return True, new_task_dict
            else:
                error_message = str(result_or_error)
                self.task_added.emit({}, f"创建任务失败: {error_message}")
                self.operation_failed.emit(f"创建任务失败: {error_message}")
                return False, error_message
                
        except Exception as e:
            self._logger.error(f"控制器: 提交新任务时发生错误: {str(e)}", exc_info=True)
            msg = f"创建任务时出现异常: {str(e)}"
            self.operation_failed.emit(msg)
            return False, msg
        finally:
            self.loading_finished.emit()

    @Slot(str, dict)
    async def submit_updated_task(self, task_id: str, task_data: Dict[str, Any]) -> Tuple[bool, Union[Dict[str, Any], str]]:
        """
        接收来自 AddMonitorView 的任务更新数据并更新任务。
        """
        self._logger.info(f"控制器: 收到任务更新提交数据: ID {task_id}, 名称 {task_data.get('name', '未知任务')}")
        
        # 验证监控群组是否都有关联账户
        monitored_chats = task_data.get("monitored_chats", [])
        for chat in monitored_chats:
            if not chat.get("account_phone"):
                return False, "每个监控的群组/频道必须关联一个账户。"
                
        self.loading_started.emit(f"正在更新任务 {task_id}...")
        try:
            if not self._task_service:
                msg = "任务服务未初始化。"
                self.operation_failed.emit(msg)
                self.loading_finished.emit()
                return False, msg

            success, result_or_error = await self._task_service.update_task_config(task_id, task_data)
            
            if success:
                updated_task_dict = result_or_error
                self.task_updated.emit(task_id, True, "任务已成功更新。") 
                self.operation_success.emit("任务更新成功", f"任务 '{task_data.get('name', '')}' (ID: {task_id}) 已更新。")
                return True, updated_task_dict
            else:
                error_message = str(result_or_error)
                self.task_updated.emit(task_id, False, f"更新任务失败: {error_message}")
                self.operation_failed.emit(f"更新任务失败: {error_message}")
                return False, error_message
        except Exception as e:
            self._logger.error(f"控制器: 提交更新任务时发生错误 (ID: {task_id}): {str(e)}", exc_info=True)
            msg = f"更新任务时出现异常: {str(e)}"
            self.operation_failed.emit(msg)
            return False, msg
        finally:
            self.loading_finished.emit()

    async def start_task_by_id(self, task_id: str):
        """启动指定的监控任务"""
        if not task_id:
            self.operation_failed.emit("无效的任务ID。")
            return

        self._logger.info(f"请求启动任务: {task_id}")
        self.loading_started.emit(f"正在启动任务 {task_id}...")
        try:
            if not self._task_service:
                self.operation_failed.emit("任务服务未初始化。")
                self.loading_finished.emit()
                return
            
            # 直接调用服务层的异步方法
            success, message = await self._task_service.start_task_monitoring_by_id(task_id)
            if success:
                self.operation_success.emit("任务启动成功", message)
                self.load_tasks() # Refresh list to show updated status
            else:
                self.operation_failed.emit(f"启动任务失败: {message}")
        except Exception as e:
            self._logger.error(f"启动任务 {task_id} 时发生错误: {str(e)}", exc_info=True)
            self.operation_failed.emit(f"启动任务时出现异常: {str(e)}")
        finally:
            self.loading_finished.emit()

    async def stop_task_by_id(self, task_id: str):
        """停止指定的监控任务"""
        if not task_id:
            self.operation_failed.emit("无效的任务ID。")
            return

        self._logger.info(f"请求停止任务: {task_id}")
        self.loading_started.emit(f"正在停止任务 {task_id}...")
        try:
            if not self._task_service:
                self.operation_failed.emit("任务服务未初始化。")
                self.loading_finished.emit()
                return

            # 直接调用服务层的异步方法
            success, message = await self._task_service.stop_task(task_id)
            if success:
                self.operation_success.emit("任务停止成功", message)
                self.load_tasks() # Refresh list to show updated status
            else:
                self.operation_failed.emit(f"停止任务失败: {message}")
        except Exception as e:
            self._logger.error(f"停止任务 {task_id} 时发生错误: {str(e)}", exc_info=True)
            self.operation_failed.emit(f"停止任务时出现异常: {str(e)}")
        finally:
            self.loading_finished.emit()

    @Slot(int, int, str)
    @asyncSlot()
    async def load_all_users(self, page: int = 1, page_size: int = 10, search_term: str = ""):
        """
        加载所有用户数据
        
        Args:
            page: 页码，默认为1
            page_size: 每页显示数量，默认为10
            search_term: 搜索关键词，默认为空
        """
        self.loading_started.emit("正在加载所有用户数据...")
        self._logger.info(f"加载所有用户数据: 页码 {page}, 每页 {page_size} 条, 搜索词: '{search_term}'")
        
        try:
            # 从服务层获取所有用户数据
            users_data = await self._task_service.get_all_users(page, page_size, search_term)
            
            if users_data:
                users_list = users_data.get("items", [])
                pagination_info = {
                    "current_page": users_data.get("current_page", 1),
                    "total_pages": users_data.get("total_pages", 1),
                    "total_items": users_data.get("total_items", 0),
                    "page_size": page_size
                }
                
                self._logger.info(f"成功加载所有用户数据，第 {page} 页，共 {len(users_list)} 条，总计 {pagination_info['total_items']} 条")
                
                # 发送信号通知用户数据加载完成
                self.all_users_loaded.emit(users_list, pagination_info)
                
                # 清除当前选中的任务
                self._current_selected_task_id = None
                self.task_selection_changed.emit("")
                
                # 额外加载全局统计数据，更新总用户计数
                await self._load_total_users_count_internal()
            else:
                self._logger.error("从服务层获取所有用户数据失败，返回为空")
                self.operation_failed.emit("无法加载所有用户数据，服务层返回空数据")
                self.all_users_loaded.emit([], {"current_page": 1, "total_pages": 1, "total_items": 0, "page_size": page_size})
        except Exception as e:
            self._logger.error(f"加载所有用户数据失败: {str(e)}", exc_info=True)
            self.operation_failed.emit(f"加载所有用户数据失败: {str(e)}")
            self.all_users_loaded.emit([], {"current_page": 1, "total_pages": 1, "total_items": 0, "page_size": page_size})
        finally:
            self.loading_finished.emit()

  
