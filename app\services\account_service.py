#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
账户服务
负责处理与Telegram账户管理相关的业务逻辑，协调数据层和核心层
"""

import os
import asyncio
import datetime
import re
import random
import string
from typing import List, Dict, Any, Optional, Tuple, Union
from sqlalchemy import select, func
from config import config
from utils.logger import get_logger
from data.models.account import AccountModel, AccountGroupModel
from data.repositories.account_repo import AccountRepository
from core.telegram.client_worker import TelegramClientWorker
from data.database import get_session
from PySide6.QtCore import QObject, Signal
from data.repositories.proxy_repo import ProxyRepository


class AccountService(QObject):
    """账户服务类"""
    
    notify = Signal(str, object, str)
    batch_accounts_added = Signal(list)  # List of new account dicts
    
    def __init__(self, telegram_worker: TelegramClientWorker):
        """初始化账户服务

        Args:
            db_session: 数据库会话
            telegram_worker: Telegram客户端工作线程
        """
        super().__init__()
        self._telegram_worker = telegram_worker
        self._telegram_worker.notify.connect(self.notify.emit)
        self._logger = get_logger("app.services.account")
        self._logger.info("账户服务初始化")
    
    # ==================== 账户分组管理 ====================
    
    async def create_group(self, name: str, description: str = None) -> Tuple[bool, Union[AccountGroupModel, str]]:
        """创建账户分组

        Args:
            name: 分组名称
            description: 分组描述

        Returns:
            (成功标志, 分组对象或错误消息)
        """
        self._logger.info(f"创建账户分组: {name}")
        try:
            # 检查名称是否为空
            if not name or name.strip() == '':
                return False, "分组名称不能为空"
            
            # 使用异步上下文管理器获取会话
            async with get_session() as session:
                try:
                    account_repo = AccountRepository(session = session)
                    # 创建分组
                    group = await account_repo.create_group(name, description)
                    if not group:
                        return False, "分组名称已存在或创建失败"
                    
                    # 提交事务
                    await session.commit()
                    await session.refresh(group)
                    return True, group
                except Exception as e:
                    # 回滚事务
                    await session.rollback()
                    self._logger.error(f"创建账户分组数据库操作异常: {e}")
                    return False, f"创建分组数据库异常: {str(e)}"
        except Exception as e:
            self._logger.error(f"创建账户分组异常: {e}")
            return False, f"创建分组异常: {str(e)}"
    
    async def get_all_groups(self) -> List[Dict[str, Any]]:
        """获取所有账户分组

        Returns:
            分组列表，每个分组以字典形式表示
        """
        try:
            # 使用异步上下文管理器获取会话
            async with get_session() as session:
                account_repo = AccountRepository(session = session)
                # 获取分组及其账户数量
                groups = await account_repo.get_groups_with_account_count()
                self._logger.info(f"获取所有账户分组成功, 共 {len(groups)} 个")
                return groups
        except Exception as e:
            self._logger.error(f"获取账户分组异常: {e}")
            return []
    
    async def update_group(self, group_id: int, name: str = None, description: str = None) -> Tuple[bool, str]:
        """更新账户分组

        Args:
            group_id: 分组ID
            name: 新的分组名称，None表示不更新
            description: 新的分组描述，None表示不更新

        Returns:
            (成功标志, 成功或错误消息)
        """
        self._logger.info(f"更新账户分组: {group_id}")
        try:
            # 检查参数
            if name is not None and name.strip() == '':
                return False, "分组名称不能为空"
            
            # 使用异步上下文管理器获取会话
            async with get_session() as session:
                try:
                    account_repo = AccountRepository(session = session)
                    # 更新分组
                    success = await account_repo.update_group(group_id, name, description)
                    if not success:
                        return False, "分组不存在或名称重复"
                    
                    # 提交事务
                    await session.commit()
                    return True, "更新分组成功"
                except Exception as e:
                    # 回滚事务
                    await session.rollback()
                    self._logger.error(f"更新账户分组数据库操作异常: {e}")
                    return False, f"更新分组数据库异常: {str(e)}"
        except Exception as e:
            self._logger.error(f"更新账户分组异常: {e}")
            return False, f"更新分组异常: {str(e)}"
    
    async def delete_group(self, group_id: int, reassign_to: int = None) -> Tuple[bool, str]:
        """删除账户分组

        Args:
            group_id: 要删除的分组ID
            reassign_to: 可选的重新分配的分组ID

        Returns:
            (成功标志, 成功或错误消息)
        """
        self._logger.info(f"删除账户分组: {group_id}, 重新分配到: {reassign_to}")
        try:
            # 使用异步上下文管理器获取会话
            async with get_session() as session:
                try:
                    account_repo = AccountRepository(session = session)
                    # 删除分组
                    success = await account_repo.delete_group(group_id, reassign_to)
                    if not success:
                        return False, "分组不存在或删除失败"
                    
                    # 提交事务
                    await session.commit()
                    return True, "删除分组成功"
                except Exception as e:
                    # 回滚事务
                    await session.rollback()
                    self._logger.error(f"删除账户分组数据库操作异常: {e}")
                    return False, f"删除分组数据库异常: {str(e)}"
        except Exception as e:
            self._logger.error(f"删除账户分组异常: {e}")
            return False, f"删除分组异常: {str(e)}"
    
    # ==================== 账户管理 ====================
    
    async def add_account(self, phone: str, session_file: str, user_info: Dict[str, Any],
                          proxy_id: int = None, proxy_type: str = None, group_id: int = None) -> Tuple[
        bool, Union[AccountModel, str]]:
        """添加新账户

        Args:
            phone: 手机号
            session_file: session文件路径
            user_info: 用户信息字典
            proxy_id: 代理ID (如果proxy_type是'ip_pool', 则为IP池中具体IP的ID)
            proxy_type: 代理类型 ('ip_pool', 'system', 'none')
            group_id: 分组ID

        Returns:
            (成功标志, 账户对象或错误消息)
        """
        self._logger.info(f"准备添加账户: {phone}, 传入代理类型: {proxy_type}, 传入代理ID: {proxy_id}")
        
        # 根据代理类型规范化 proxy_id 和 proxy_type 用于数据库存储
        final_db_proxy_id = None
        final_db_proxy_type = "none"  # 默认为不使用代理
        
        if proxy_type == "ip_pool":
            if proxy_id is not None:
                final_db_proxy_type = "ip_pool"
                final_db_proxy_id = proxy_id
            else:
                self._logger.warning(f"账户 {phone} 指定代理类型为 'ip_pool'，但未提供 proxy_id。将按不使用代理处理。")
                # final_db_proxy_type 保持 "none", final_db_proxy_id 保持 None
        elif proxy_type == "system":
            final_db_proxy_type = "system"
            # final_db_proxy_id 保持 None
            if proxy_id is not None:
                self._logger.warning(
                    f"账户 {phone} 指定代理类型为 'system'，但提供了 proxy_id ({proxy_id})。proxy_id 将被忽略。")
        elif proxy_type == "none":
            # final_db_proxy_type 保持 "none", final_db_proxy_id 保持 None
            if proxy_id is not None:
                self._logger.warning(
                    f"账户 {phone} 指定代理类型为 'none'，但提供了 proxy_id ({proxy_id})。proxy_id 将被忽略。")
        elif proxy_type is not None:  # 处理未明确识别的 proxy_type 字符串
            self._logger.warning(f"账户 {phone} 提供了未知的代理类型 '{proxy_type}'。将按不使用代理处理。")
            # final_db_proxy_type 保持 "none", final_db_proxy_id 保持 None
        
        # 如果 proxy_type 本身为 None，则也会使用默认的 "none" 和 None
        
        self._logger.info(
            f"规范化后添加账户: {phone}, 数据库代理类型: {final_db_proxy_type}, 数据库代理ID: {final_db_proxy_id}")
        # 处理 session_file 路径，只保存文件名
        session_filename = os.path.basename(session_file)
        try:
            # 提取用户信息
            first_name = user_info.get('first_name', '')
            last_name = user_info.get('last_name', '')
            username = user_info.get('username', '')
            bio = user_info.get('bio', '')
            profile_photo = user_info.get('profile_photo', '')
            has_2fa = user_info.get('has_2fa', False)
            
            # 使用异步上下文管理器获取会话
            async with get_session() as session:
                try:
                    account_repo = AccountRepository(session = session)
                    # 创建账户
                    account = await account_repo.create_account(
                        phone = phone,
                        session_file = session_filename,
                        first_name = first_name,
                        last_name = last_name,
                        username = username,
                        bio = bio,
                        profile_photo = profile_photo,
                        is_active = True,
                        is_connected = True,  # 登录成功，状态为已连接
                        has_2fa = has_2fa,
                        last_connected = datetime.datetime.now(),  # 更新最后连接时间
                        proxy_id = final_db_proxy_id,  # 使用规范化后的 proxy_id
                        proxy_type = final_db_proxy_type,  # 使用规范化后的 proxy_type
                        group_id = group_id
                    )
                    
                    if not account:
                        return False, "手机号已存在或创建失败"
                    
                    # 提交事务
                    await session.commit()
                    await session.refresh(account)
                    return True, account
                except Exception as e:
                    # 回滚事务
                    await session.rollback()
                    self._logger.error(f"添加账户数据库操作异常: {e}")
                    return False, f"添加账户数据库异常: {str(e)}"
        except Exception as e:
            self._logger.error(f"添加账户异常: {e}")
            return False, f"添加账户异常: {str(e)}"
    
    async def _get_account_limits(self, session, phone: str) -> Dict[str, Any]:
        """获取账户的限制信息

        Args:
            session: 数据库会话
            phone: 账户手机号

        Returns:
            包含限制信息的字典
        """
        limits = {}
        try:
            # 获取消息限制
            from data.repositories.account_send_limit_repo import AccountSendLimitRepository
            send_limit_repo = AccountSendLimitRepository(session)
            send_limit = await send_limit_repo.get_by_phone(phone)
            limits['daily_msg_limit'] = send_limit.max_daily_limit if send_limit else 10
            
            # 获取邀请限制
            from data.repositories.invite_task_repo import InviteTaskRepository
            invite_repo = InviteTaskRepository(session)
            invite_limit = await invite_repo.get_invite_limit(phone)
            limits['daily_invite_limit'] = invite_limit.max_daily_limit if invite_limit else 10
        
        except Exception as e:
            self._logger.error(f"获取账户 {phone} 限制信息失败: {e}")
            # 设置默认值
            limits['daily_msg_limit'] = 10
            limits['daily_invite_limit'] = 10
        
        return limits
    
    async def get_all_accounts(self, active_only: bool = False) -> List[Dict[str, Any]]:
        """获取所有账户

        Args:
            active_only: 是否只返回活跃账户

        Returns:
            账户列表，每个账户以字典形式表示
        """
        try:
            # 使用异步上下文管理器获取会话
            async with get_session() as session:
                account_repo = AccountRepository(session = session)
                accounts = await account_repo.get_all_accounts(active_only)
                # 转换为字典列表并添加限制信息
                result = []
                for account in accounts:
                    try:
                        account_dict = account.to_dict()
                        # 添加限制信息
                        limits = await self._get_account_limits(session, account.phone)
                        account_dict.update(limits)
                        result.append(account_dict)
                    except Exception as e:
                        self._logger.error(f"转换账户数据失败: {e}")
                        continue
                self._logger.info(f"获取所有账户成功, 共 {len(result)} 个")
                return result
        except Exception as e:
            self._logger.error(f"获取账户列表异常: {e}")
            return []
    
    async def get_accounts_by_group(self, group_id: int) -> List[Dict[str, Any]]:
        """获取指定分组的所有账户

        Args:
            group_id: 分组ID

        Returns:
            账户列表，每个账户以字典形式表示
        """
        try:
            # 使用异步上下文管理器获取会话
            async with get_session() as session:
                account_repo = AccountRepository(session = session)
                accounts = await account_repo.get_accounts_by_group(group_id)
                # 转换为字典列表并添加限制信息
                result = []
                for account in accounts:
                    try:
                        account_dict = account.to_dict()
                        # 添加限制信息
                        limits = await self._get_account_limits(session, account.phone)
                        account_dict.update(limits)
                        result.append(account_dict)
                    except Exception as e:
                        self._logger.error(f"转换账户数据失败: {e}")
                        continue
                self._logger.info(f"获取分组 {group_id} 的账户成功, 共 {len(result)} 个")
                return result
        except Exception as e:
            self._logger.error(f"获取分组账户异常: {e}")
            return []
    
    async def update_account(self, account_id: int, **kwargs) -> Tuple[bool, str]:
        """更新账户信息

        Args:
            account_id: 账户ID
            **kwargs: 要更新的字段

        Returns:
            (成功标志, 成功或错误消息)
        """
        self._logger.info(f"更新账户信息: {account_id}")
        try:
            # 使用异步上下文管理器获取会话
            async with get_session() as session:
                try:
                    account_repo = AccountRepository(session = session)
                    success = await account_repo.update_account(account_id, **kwargs)
                    if not success:
                        return False, "账户不存在或更新失败"
                    
                    # 提交事务
                    await session.commit()
                    return True, "更新账户成功"
                except Exception as e:
                    # 回滚事务
                    await session.rollback()
                    self._logger.error(f"更新账户数据库操作异常: {e}")
                    return False, f"更新账户数据库异常: {str(e)}"
        except Exception as e:
            self._logger.error(f"更新账户异常: {e}")
            return False, f"更新账户异常: {str(e)}"
    
    async def delete_account(self, account_id: int, delete_session: bool = True) -> Tuple[bool, str]:
        """删除账户

        Args:
            account_id: 账户ID
            delete_session: 是否同时删除session文件

        Returns:
            (成功标志, 成功或错误消息)
        """
        self._logger.info(f"删除账户: {account_id}, 删除session: {delete_session}")
        try:
            # 使用异步上下文管理器获取会话
            async with get_session() as session:
                try:
                    account_repo = AccountRepository(session = session)
                    # 获取账户
                    account = await account_repo.get_account(account_id)
                    if not account:
                        return False, "账户不存在"
                    
                    # 断开客户端连接
                    if account.phone:
                        task_id = self._telegram_worker.disconnect_client(account.phone)
                        # 等待操作完成
                        await   self._telegram_worker.get_task_result(task_id, timeout = 5)
                    
                    # 删除账户
                    success, session_filename = await account_repo.delete_account(account_id, delete_session)
                    if not success:
                        return False, "删除账户失败"
                    
                    # 提交事务
                    await session.commit()
                    
                    # 删除session文件
                    # if delete_session and session_file and os.path.exists(session_file):
                    #     try:
                    #         os.remove(session_file)
                    #         self._logger.info(f"删除session文件: {session_file}")
                    #     except Exception as e:
                    #         self._logger.warning(f"删除session文件失败: {e}")
                    # 删除session文件 - 需要获取完整路径
                    if delete_session and session_filename:
                        try:
                            # 从配置获取 sessions 目录
                            sessions_path = config.get("sessions_path")
                            full_session_path = os.path.join(sessions_path, session_filename)
                            
                            if os.path.exists(full_session_path):
                                os.remove(full_session_path)
                                self._logger.info(f"删除session文件: {full_session_path}")
                        except Exception as e:
                            self._logger.warning(f"删除session文件失败: {e}")
                    return True, "删除账户成功"
                except Exception as e:
                    # 回滚事务
                    await session.rollback()
                    self._logger.error(f"删除账户数据库操作异常: {e}")
                    return False, f"删除账户数据库异常: {str(e)}"
        except Exception as e:
            self._logger.error(f"删除账户异常: {e}")
            return False, f"删除账户异常: {str(e)}"
    
    async def batch_delete_accounts(self, account_ids: List[int], delete_session: bool = True) -> Tuple[int, int, str]:
        """批量删除账户

        Args:
            account_ids: 账户ID列表
            delete_session: 是否同时删除session文件

        Returns:
            (成功数量, 失败数量, 消息)
        """
        self._logger.info(f"批量删除账户: {len(account_ids)} 个, 删除session: {delete_session}")
        try:
            success_count = 0
            fail_count = 0
            session_files_to_delete = []
            
            # 先断开所有客户端连接
            for account_id in account_ids:
                async with get_session() as session:
                    account_repo = AccountRepository(session = session)
                    account = await account_repo.get_account(account_id)
                    if account and account.phone:
                        self._telegram_worker.disconnect_client(account.phone)
            
            # 删除账户
            async with get_session() as session:
                try:
                    account_repo = AccountRepository(session = session)
                    delete_results = await account_repo.delete_accounts(account_ids, delete_session)
                    
                    # 提交事务
                    await session.commit()
                    
                    # 处理删除的session文件
                    success_count = len(delete_results)
                    fail_count = len(account_ids) - success_count
                    
                    # 收集需要删除的session文件
                    for account_id, session_file in delete_results:
                        if session_file and os.path.exists(session_file):
                            session_files_to_delete.append(session_file)
                    
                    # 删除session文件
                    if delete_session:
                        for session_file in session_files_to_delete:
                            try:
                                os.remove(session_file)
                                self._logger.info(f"删除session文件: {session_file}")
                            except Exception as e:
                                self._logger.warning(f"删除session文件失败: {e}")
                    
                    return success_count, fail_count, f"批量删除完成: 成功 {success_count} 个, 失败 {fail_count} 个"
                except Exception as e:
                    # 回滚事务
                    await session.rollback()
                    self._logger.error(f"批量删除账户数据库操作异常: {e}")
                    return 0, len(account_ids), f"批量删除账户数据库异常: {str(e)}"
        except Exception as e:
            self._logger.error(f"批量删除账户异常: {e}")
            return 0, len(account_ids), f"批量删除异常: {str(e)}"
    
    async def update_account_profile(self, account_id: int, **kwargs) -> Tuple[bool, str]:
        """更新账户资料

        Args:
            account_id: 账户ID
            **kwargs: 要更新的资料字段，可包括：
                first_name: 名字
                last_name: 姓氏
                username: 用户名
                bio: 个人简介
                profile_photo: 头像文件路径
                group_id: 分组ID
                daily_msg_limit: 每日消息限制
                daily_invite_limit: 每日邀请限制

        Returns:
            (成功标志, 成功或错误消息)
        """
        self._logger.info(f"更新账户资料: {account_id}")
        try:
            # 使用异步上下文管理器获取会话
            async with get_session() as session:
                account_repo = AccountRepository(session = session)
                # 获取账户
                account = await account_repo.get_account(account_id)
                if not account:
                    return False, "账户不存在"
                
                # 获取传入的资料字段
                profile_fields = {}
                db_fields = {}
                
                # 处理分组ID单独更新数据库
                if 'group_id' in kwargs:
                    db_fields['group_id'] = kwargs.pop('group_id')
                
                # 提取限制字段，单独处理
                daily_msg_limit = kwargs.pop('daily_msg_limit', None)
                daily_invite_limit = kwargs.pop('daily_invite_limit', None)
                
                # 处理用户名标签（如果包含标签，生成唯一用户名）
                username_template = kwargs.get('username')
                if username_template and self._has_username_tags(username_template):
                    # 生成唯一用户名，最多重试5次
                    unique_username = await self._generate_unique_username(username_template, account_repo,
                                                                           max_retries = 5)
                    if not unique_username:
                        return False, "无法生成唯一用户名，请尝试其他模板"
                    kwargs['username'] = unique_username
                
                # 筛选出需要通过Telegram API更新的字段
                valid_profile_fields = {'first_name', 'last_name', 'username', 'bio', 'profile_photo'}
                for field, value in kwargs.items():
                    if field in valid_profile_fields:
                        # 对头像文件进行额外验证
                        if field == 'profile_photo' and value:
                            if not os.path.exists(value):
                                return False, f"头像文件不存在: {value}"

                            # 检查文件大小（限制为10MB）
                            file_size = os.path.getsize(value)
                            max_size = 10 * 1024 * 1024  # 10MB
                            if file_size > max_size:
                                return False, f"头像文件过大: {file_size / (1024*1024):.1f}MB，最大支持10MB"

                            # 检查文件类型
                            allowed_extensions = {'.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp'}
                            file_ext = os.path.splitext(value)[1].lower()
                            if file_ext not in allowed_extensions:
                                return False, f"不支持的头像文件格式: {file_ext}，支持的格式: {', '.join(allowed_extensions)}"

                        profile_fields[field] = value
                
                # 先尝试更新Telegram资料，成功后再更新数据库
                telegram_success = False
                if profile_fields and account.phone:
                    # 根据是否包含头像上传来决定超时时间
                    if 'profile_photo' in profile_fields and profile_fields['profile_photo']:
                        # 头像上传需要更长时间
                        timeout = config.get("telegram_avatar_upload_timeout", 90)
                        self._logger.info(f"检测到头像上传，使用 {timeout} 秒超时")
                    else:
                        # 普通资料更新
                        timeout = config.get("telegram_profile_update_timeout", 30)

                    # 使用工作线程更新资料
                    self._logger.info(f"开始更新账户 {account.phone} 的Telegram资料，超时设置: {timeout}秒")
                    task_id = self._telegram_worker.update_profile(account.phone, **profile_fields)
                    success, result = await self._telegram_worker.get_task_result(task_id, timeout=timeout)

                    if not success:
                        if "任务结果获取超时" in str(result):
                            if 'profile_photo' in profile_fields:
                                return False, f"更新Telegram资料失败: 头像上传超时，请检查网络连接或尝试使用更小的图片文件"
                            else:
                                return False, f"更新Telegram资料失败: 操作超时，请稍后重试"
                        return False, f"更新Telegram资料失败: {result}"
                    
                    telegram_success = True
                    # Telegram更新成功，将这些字段添加到数据库更新列表
                    for field, value in profile_fields.items():
                        db_fields[field] = value
                
                # 更新数据库记录（只有在Telegram更新成功或没有Telegram字段时才更新）
                if db_fields:
                    success = await account_repo.update_account(account_id, **db_fields)
                    if not success:
                        return False, "更新数据库记录失败"
                
                # 处理每日消息限制
                if daily_msg_limit is not None:
                    from data.repositories.account_send_limit_repo import AccountSendLimitRepository
                    send_limit_repo = AccountSendLimitRepository(session)
                    try:
                        await send_limit_repo.set_max_limit(account.phone, daily_msg_limit)
                        self._logger.info(f"更新账户 {account.phone} 每日消息限制为: {daily_msg_limit}")
                    except Exception as e:
                        self._logger.error(f"更新每日消息限制失败: {e}")
                        return False, f"更新每日消息限制失败: {str(e)}"
                
                # 处理每日邀请限制
                if daily_invite_limit is not None:
                    from data.repositories.invite_task_repo import InviteTaskRepository
                    invite_repo = InviteTaskRepository(session)
                    try:
                        await invite_repo.create_or_update_invite_limit(account.phone, daily_invite_limit)
                        self._logger.info(f"更新账户 {account.phone} 每日邀请限制为: {daily_invite_limit}")
                    except Exception as e:
                        self._logger.error(f"更新每日邀请限制失败: {e}")
                        return False, f"更新每日邀请限制失败: {str(e)}"
                
                # 提交事务
                await session.commit()
                
                return True, "更新账户资料成功"
        except Exception as e:
            self._logger.error(f"更新账户资料异常: {e}")
            return False, f"更新账户资料异常: {str(e)}"
    
    async def batch_update_profiles(self, account_ids: List[int], **kwargs) -> Tuple[int, int, str]:
        """批量更新账户资料

        Args:
            account_ids: 账户ID列表
            **kwargs: 要更新的资料字段

        Returns:
            (成功数量, 失败数量, 消息)
        """
        self._logger.info(f"批量更新账户资料: {len(account_ids)} 个")
        
        # 检查账户ID列表是否为空
        if not account_ids:
            self._logger.warning("批量更新失败: 账户ID列表为空")
            return 0, 0, "批量更新失败: 没有选择任何账户或账户ID无效"
        
        try:
            # 验证批量更新数据
            is_valid, error_msg = await self._validate_batch_update_data(**kwargs)
            if not is_valid:
                return 0, len(account_ids), f"数据验证失败: {error_msg}"
            
            # 检查是否包含用户名模板，如果是则转为逐个处理
            username_template = kwargs.get('username')
            if username_template and self._has_username_tags(username_template):
                return await self._batch_update_with_username_template(account_ids, username_template, **kwargs)
            
            # 获取账户信息
            accounts = []
            for account_id in account_ids:
                async with get_session() as session:
                    account_repo = AccountRepository(session = session)
                    account = await account_repo.get_account(account_id)
                    if account and account.phone:
                        accounts.append({
                            'id': account.id,
                            'phone': account.phone
                        })
            
            # 分离Telegram字段和数据库字段
            group_id = kwargs.pop('group_id', None)
            
            # 提取限制字段，单独处理
            daily_msg_limit = kwargs.pop('daily_msg_limit', None)
            daily_invite_limit = kwargs.pop('daily_invite_limit', None)
            
            # 筛选Telegram API字段
            telegram_fields = {}
            valid_profile_fields = {'first_name', 'last_name', 'username', 'bio', 'profile_photo'}
            for field, value in kwargs.items():
                if field in valid_profile_fields:
                    telegram_fields[field] = value
            
            # 先尝试批量更新Telegram资料
            telegram_success = False
            success_count = 0
            fail_count = 0
            
            if accounts and telegram_fields:
                # 根据是否包含头像上传来决定超时时间
                if 'profile_photo' in telegram_fields and telegram_fields['profile_photo']:
                    timeout = config.get("telegram_avatar_upload_timeout", 90) * 2  # 批量操作需要更长时间
                    self._logger.info(f"批量头像上传，使用 {timeout} 秒超时")
                else:
                    timeout = config.get("telegram_batch_operation_timeout", 120)

                self._logger.info(f"开始批量更新 {len(accounts)} 个账户的Telegram资料，超时设置: {timeout}秒")
                task_id = self._telegram_worker.batch_update_profiles(accounts, **telegram_fields)
                success, result = await self._telegram_worker.get_task_result(task_id, timeout=timeout)

                if not success:
                    if "任务结果获取超时" in str(result):
                        if 'profile_photo' in telegram_fields:
                            return 0, len(account_ids), f"批量更新Telegram资料失败: 头像上传超时，请检查网络连接或尝试使用更小的图片文件"
                        else:
                            return 0, len(account_ids), f"批量更新Telegram资料失败: 操作超时，请稍后重试"
                    return 0, len(account_ids), f"批量更新Telegram资料失败: {result}"
                
                telegram_success = True
                # 获取操作结果统计
                success_count = result.get('success', 0)
                fail_count = result.get('failed', 0)
            
            # 准备数据库更新字段
            db_fields = {}
            
            # 添加分组ID（如果有）
            if group_id is not None:
                db_fields['group_id'] = group_id
            
            # 如果Telegram更新成功，将Telegram字段也添加到数据库更新
            # 如果没有Telegram字段或Telegram更新失败，仍然可以更新数据库字段
            if telegram_success:
                db_fields.update(telegram_fields)
            elif telegram_fields and not accounts:
                # 如果没有有效的账户进行Telegram更新，但有Telegram字段，也添加到数据库更新
                db_fields.update(telegram_fields)
            
            if db_fields:
                async with get_session() as session:
                    account_repo = AccountRepository(session = session)
                    db_success, db_fail = await account_repo.update_accounts_profile(account_ids, **db_fields)
                    
                    # 提交事务
                    await session.commit()
                    
                    # 如果没有进行Telegram API操作，使用数据库更新的统计
                    if not telegram_success:
                        success_count = db_success
                        fail_count = db_fail
            
            # 处理限制字段的批量更新
            if daily_msg_limit is not None or daily_invite_limit is not None:
                async with get_session() as session:
                    # 获取所有账户的手机号
                    account_repo = AccountRepository(session = session)
                    limit_success_count = 0
                    limit_fail_count = 0
                    
                    for account_id in account_ids:
                        account = await account_repo.get_account(account_id)
                        if not account or not account.phone:
                            limit_fail_count += 1
                            continue
                        
                        try:
                            # 处理每日消息限制
                            if daily_msg_limit is not None:
                                from data.repositories.account_send_limit_repo import AccountSendLimitRepository
                                send_limit_repo = AccountSendLimitRepository(session)
                                await send_limit_repo.set_max_limit(account.phone, daily_msg_limit)
                            
                            # 处理每日邀请限制
                            if daily_invite_limit is not None:
                                from data.repositories.invite_task_repo import InviteTaskRepository
                                invite_repo = InviteTaskRepository(session)
                                await invite_repo.create_or_update_invite_limit(account.phone, daily_invite_limit)
                            
                            limit_success_count += 1
                        except Exception as e:
                            self._logger.error(f"更新账户 {account.phone} 限制失败: {e}")
                            limit_fail_count += 1
                    
                    # 提交限制更新事务
                    await session.commit()
                    
                    # 如果只更新限制字段（没有其他字段），使用限制更新的统计
                    if not db_fields and not telegram_fields:
                        success_count = limit_success_count
                        fail_count = limit_fail_count
                    else:
                        # 合并统计结果（取最小成功数，因为所有字段都需要成功才算成功）
                        if limit_fail_count > 0:
                            # 如果有限制字段更新失败，调整总体统计
                            actual_success = min(success_count, limit_success_count)
                            actual_fail = len(account_ids) - actual_success
                            success_count = actual_success
                            fail_count = actual_fail
            
            return success_count, fail_count, f"批量更新完成: 成功 {success_count} 个, 失败 {fail_count} 个"
        except Exception as e:
            self._logger.error(f"批量更新账户资料异常: {e}")
            return 0, len(account_ids), f"批量更新异常: {str(e)}"
    
    async def refresh_account_info(self, account_id: int) -> Tuple[bool, str]:
        """刷新账户信息

        Args:
            account_id: 账户ID

        Returns:
            (成功标志, 成功或错误消息)
        """
        self._logger.info(f"刷新账户信息: {account_id}")
        try:
            # 使用异步上下文管理器获取会话
            async with get_session() as session:
                account_repo = AccountRepository(session = session)
                # 获取账户
                account = await account_repo.get_account(account_id)
                if not account:
                    return False, "账户不存在"
                
                if not account.phone:
                    return False, "账户未设置手机号"
                
                # 获取用户信息
                task_id = self._telegram_worker.get_user_info(account.phone)
                # success, result = await self._telegram_worker.get_task_result(task_id, timeout=30)
                worker_success_status, worker_payload = await self._telegram_worker.get_task_result(task_id,
                                                                                                    timeout = 30)
                
                actual_op_success = worker_success_status
                actual_op_data = worker_payload
                
                # 防御性代码：处理 worker 可能返回嵌套元组的情况
                # 例如，worker 返回 (True, (manager_success, manager_data))
                if worker_success_status and isinstance(worker_payload, tuple) and len(
                        worker_payload) == 2 and isinstance(worker_payload[0], bool):
                    actual_op_success = worker_payload[0]  # 这是 manager 操作的成功状态
                    actual_op_data = worker_payload[1]  # 这是 manager 操作的数据 (dict 或 str)
                
                if not actual_op_success:
                    # actual_op_data 在这里应该是错误消息字符串
                    return False, f"获取用户信息失败: {actual_op_data}"
                
                # 到这里，actual_op_data 应该是用户信息字典
                if not isinstance(actual_op_data, dict):
                    self._logger.error(
                        f"获取用户信息成功，但结果数据类型不正确 (账户ID: {account_id})。期望字典，得到: {type(actual_op_data)} 内容: {actual_op_data}")
                    return False, "获取用户信息成功但数据格式错误"
                
                # 更新数据库记录
                update_fields = {
                    'first_name': actual_op_data.get('first_name', ''),
                    'last_name': actual_op_data.get('last_name', ''),
                    'username': actual_op_data.get('username', ''),
                    'bio': actual_op_data.get('bio', ''),
                    'is_connected': True,  # 既然获取用户信息成功，说明连接是正常的
                    'last_connected': datetime.datetime.now(),  # 更新最后连接时间
                    'has_2fa': actual_op_data.get('has_2fa', account.has_2fa)  # 保留之前的2FA状态，除非API明确返回
                }
                
                # 如果API返回了is_active状态，则更新
                if 'is_active' in actual_op_data:
                    update_fields['is_active'] = actual_op_data['is_active']
                
                # 提交更新到数据库
                update_success_db = await account_repo.update_account(account_id, **update_fields)
                if not update_success_db:
                    return False, "更新数据库记录失败"
                
                await session.commit()  # 确保提交更改
                return True, "刷新账户信息成功"
        except Exception as e:
            self._logger.error(f"刷新账户信息异常: {e}", exc_info = True)  # 添加 exc_info=True 获取更详细的堆栈跟踪
            return False, f"刷新账户信息异常: {str(e)}"
    
    async def batch_refresh_accounts(self, account_ids: List[int]) -> Tuple[int, int, str]:
        """批量刷新账户信息

        Args:
            account_ids: 账户ID列表

        Returns:
            (成功数量, 失败数量, 消息)
        """
        self._logger.info(f"批量刷新账户信息: {len(account_ids)} 个")
        success_count = 0
        fail_count = 0
        
        for account_id in account_ids:
            success, _ = await self.refresh_account_info(account_id)
            if success:
                success_count += 1
            else:
                fail_count += 1
        
        return success_count, fail_count, f"批量刷新完成: 成功 {success_count} 个, 失败 {fail_count} 个"
    
    # ==================== 账户登录相关 ====================
    
    async def start_login(self, phone: str, proxy: dict = None) -> Tuple[bool, str]:
        """开始登录过程 - 使用新的任务管理器（异步方式）

        Args:
            phone: 手机号
            proxy: 代理配置

        Returns:
            (成功标志, 成功或错误消息)
        """
        self._logger.info(f"开始登录过程: {phone}, 代理: {proxy}")
        try:
            # 使用新的任务管理器
            from core.task_manager.task_service import task_service
            from PySide6.QtCore import QEventLoop, QTimer

            # 提交账户登录任务（不再传递telegram_worker）
            task_id = task_service.submit_account_login_task(
                phone=phone,
                proxy=proxy
            )

            # 使用Qt事件循环等待任务完成
            loop = QEventLoop()
            timer = QTimer()
            timer.setSingleShot(True)
            timer.timeout.connect(loop.quit)

            result_data = {"completed": False, "success": False, "message": ""}

            def on_task_completed(completed_task_id: str, result: object):
                if completed_task_id == task_id:
                    result_data["completed"] = True
                    result_data["success"] = True
                    result_data["result"] = result
                    loop.quit()

            def on_task_failed(failed_task_id: str, error: str):
                if failed_task_id == task_id:
                    result_data["completed"] = True
                    result_data["success"] = False
                    result_data["message"] = error
                    loop.quit()

            # 连接信号
            task_service.task_completed.connect(on_task_completed)
            task_service.task_failed.connect(on_task_failed)

            try:
                # 设置30秒超时
                timer.start(30000)
                loop.exec()

                if not result_data["completed"]:
                    task_service.cancel_task(task_id)
                    return False, "登录超时"

                if result_data["success"]:
                    result = result_data.get("result", {})
                    if result.get("status") == "already_logged_in":
                        return True, "已登录"
                    return True, "验证码已发送"
                else:
                    return False, f"登录失败: {result_data['message']}"

            finally:
                # 断开信号连接
                task_service.task_completed.disconnect(on_task_completed)
                task_service.task_failed.disconnect(on_task_failed)
                timer.stop()

        except Exception as e:
            self._logger.error(f"开始登录过程异常: {e}")
            return False, f"开始登录过程异常: {str(e)}"
    
    async def submit_code(self, phone: str, code: str, password: str = None):
        """提交验证码 - 使用新的任务管理器（Qt事件循环方式）

        Args:
            phone: 手机号
            code: 验证码
            password: 两步验证密码（可选）

        Returns:
            (成功标志, 成功消息/用户信息或错误消息)
        """
        self._logger.info(f"提交验证码: {phone}")
        try:
            # 使用新的任务管理器
            from core.task_manager.task_service import task_service
            from PySide6.QtCore import QEventLoop, QTimer

            # 提交验证码验证任务
            task_id = task_service.submit_account_code_verification_task(
                phone=phone,
                code=code,
                password=password
            )

            # 使用Qt事件循环等待任务完成
            loop = QEventLoop()
            timer = QTimer()
            timer.setSingleShot(True)
            timer.timeout.connect(loop.quit)

            result_data = {"completed": False, "success": False, "message": ""}

            def on_task_completed(completed_task_id: str, result: object):
                if completed_task_id == task_id:
                    result_data["completed"] = True
                    result_data["success"] = True
                    result_data["result"] = result
                    loop.quit()

            def on_task_failed(failed_task_id: str, error: str):
                if failed_task_id == task_id:
                    result_data["completed"] = True
                    result_data["success"] = False
                    result_data["message"] = error
                    loop.quit()

            # 连接信号
            task_service.task_completed.connect(on_task_completed)
            task_service.task_failed.connect(on_task_failed)

            try:
                # 设置30秒超时
                timer.start(30000)
                loop.exec()

                if not result_data["completed"]:
                    task_service.cancel_task(task_id)
                    return False, "验证码验证超时"

                if result_data["success"]:
                    result = result_data.get("result", {})
                    return True, result.get("user_info")
                else:
                    return False, f"验证码验证失败: {result_data['message']}"

            finally:
                # 断开信号连接
                task_service.task_completed.disconnect(on_task_completed)
                task_service.task_failed.disconnect(on_task_failed)
                timer.stop()

        except Exception as e:
            self._logger.error(f"提交验证码异常: {e}")
            return False, f"提交验证码异常: {str(e)}"
    
    async def submit_password(self, phone: str, password: str) -> Tuple[bool, Union[str, Dict[str, Any]]]:
        """提交两步验证密码 - 使用新的任务管理器（Qt事件循环方式）

        Args:
            phone: 手机号
            password: 两步验证密码

        Returns:
            (成功标志, 成功消息/用户信息或错误消息)
        """
        self._logger.info(f"提交两步验证密码: {phone}")
        try:
            # 使用新的任务管理器
            from core.task_manager.task_service import task_service
            from PySide6.QtCore import QEventLoop, QTimer

            # 提交密码验证任务
            task_id = task_service.submit_account_password_verification_task(
                phone=phone,
                password=password
            )

            # 使用Qt事件循环等待任务完成
            loop = QEventLoop()
            timer = QTimer()
            timer.setSingleShot(True)
            timer.timeout.connect(loop.quit)

            result_data = {"completed": False, "success": False, "message": ""}

            def on_task_completed(completed_task_id: str, result: object):
                if completed_task_id == task_id:
                    result_data["completed"] = True
                    result_data["success"] = True
                    result_data["result"] = result
                    loop.quit()

            def on_task_failed(failed_task_id: str, error: str):
                if failed_task_id == task_id:
                    result_data["completed"] = True
                    result_data["success"] = False
                    result_data["message"] = error
                    loop.quit()

            # 连接信号
            task_service.task_completed.connect(on_task_completed)
            task_service.task_failed.connect(on_task_failed)

            try:
                # 设置30秒超时
                timer.start(30000)
                loop.exec()

                if not result_data["completed"]:
                    task_service.cancel_task(task_id)
                    return False, "密码验证超时"

                if result_data["success"]:
                    result = result_data.get("result", {})
                    return True, result.get("user_info")
                else:
                    return False, f"密码验证失败: {result_data['message']}"

            finally:
                # 断开信号连接
                task_service.task_completed.disconnect(on_task_completed)
                task_service.task_failed.disconnect(on_task_failed)
                timer.stop()

        except Exception as e:
            self._logger.error(f"提交密码异常: {e}")
            return False, f"提交密码异常: {str(e)}"
    
    async def import_session(self, session_path: str, proxy_id: int = None, group_id: int = None) -> Tuple[
        bool, Union[AccountModel, str]]:
        """导入session文件

        Args:
            session_path: session文件路径
            proxy_id: 代理ID (特指IP池中某个IP的数据库ID)
            group_id: 分组ID

        Returns:
            (成功标志, 账户对象或错误消息)
        """
        self._logger.info(f"导入session文件: {session_path}, 传入IP池代理ID: {proxy_id}, 分组ID: {group_id}")
        
        telethon_proxy_config = None  # 用于传递给 TelegramClientManager
        db_proxy_type_for_add_account = None
        db_proxy_id_for_add_account = None
        
        if proxy_id is not None:
            # 如果提供了 proxy_id，说明用户希望使用IP池中的特定IP
            async with get_session() as session:
                from data.repositories.proxy_repo import ProxyRepository
                proxy_repo = ProxyRepository(session)
                proxy_model = await proxy_repo.find_by_id(proxy_id)  # Assume this returns ProxyModel or None
                
                if proxy_model:
                    self._logger.info(
                        f"为 session {session_path} 找到代理ID {proxy_id} 的详情: {proxy_model.ip}:{proxy_model.port}")
                    db_proxy_type_for_add_account = "ip_pool"
                    db_proxy_id_for_add_account = proxy_id
                    
                    telethon_proxy_config = {
                        'type': 'ip_pool',  # none,system,ip_pool
                        'proxy_type': proxy_model.type,  # 'socks5', 'http', etc.
                        'ip': proxy_model.ip,
                        'port': proxy_model.port,
                        'username': proxy_model.username,
                        'password': proxy_model.password,
                    }
                    # 清理Telethon代理配置中的空值
                    telethon_proxy_config = {k: v for k, v in telethon_proxy_config.items() if
                                             v is not None and v != ''}
                    if not telethon_proxy_config.get('username'):  # Telethon需要None而不是空字符串
                        telethon_proxy_config.pop('username', None)
                        telethon_proxy_config.pop('password', None)
                else:
                    self._logger.warning(
                        f"导入session时，提供的代理ID {proxy_id} 在数据库中未找到。将不使用代理进行连接。")
                    # db_proxy_type_for_add_account 保持 None, add_account 会处理为 "none"
        else:
            self._logger.info(f"导入session {session_path} 时未提供IP池代理ID，将不使用代理进行连接。")
            # db_proxy_type_for_add_account 保持 None, add_account 会处理为 "none"
        
        try:
            # 导入session
            task_id = self._telegram_worker.import_session(session_path, telethon_proxy_config)
            success, result = await self._telegram_worker.get_task_result(task_id, timeout = 30)
            
            if not success:
                return False, f"导入session失败: {result}"
            
            # 获取用户信息
            user_info = result
            phone = user_info.get('phone', '')
            
            if not phone:
                return False, "获取手机号失败"
            
            # 添加账户到数据库
            add_success, account = await self.add_account(
                phone = phone,
                session_file = session_path,
                user_info = user_info,
                proxy_id = db_proxy_id_for_add_account,  # 传递给add_account
                proxy_type = db_proxy_type_for_add_account,  # 传递给add_account
                group_id = group_id
            )
            
            if not add_success:
                return False, f"添加账户到数据库失败: {account}"
            
            return True, account
        except Exception as e:
            self._logger.error(f"导入session异常: {e}")
            return False, f"导入session异常: {str(e)}"
    
    async def batch_import_sessions(self, session_files_from_ui: List[Dict[str, Any]], max_concurrent: int = 5) -> \
    Tuple[int, int, str]:
        """
        批量导入session文件 (重构版，不在此查询代理详情)
        Args:
            session_files_from_ui: 来自UI的session文件信息列表。格式示例:
                [{
                    'session_file': 'path/to/session1.session',
                    'proxy_config': {'id': 1, 'type': 'ip_pool', 'proxy_type': 'socks5', 'ip':'...', 'port':...}, # 或 {'type': 'system'} 或 None
                    'group_id': 1,
                    'row_index': 0 # UI 使用，服务层透传给控制器，控制器再通知UI
                }, ...]
            max_concurrent: 最大并发数
        Returns:
            (成功数量, 失败数量, 消息字符串)
        """
        self._logger.info(
            f"服务层(重构)：开始批量导入 {len(session_files_from_ui)} 个session文件，最大并发: {max_concurrent}")
        
        params_for_worker = []  # 参数列表给 TelegramClientWorker
        # 存储辅助信息，用于处理worker返回结果
        auxiliary_data_map = {}
        
        # --- 开始处理UI传入数据，准备参数 ---
        for ui_file_info in session_files_from_ui:
            path = ui_file_info.get('session_file')
            ui_proxy_config = ui_file_info.get('proxy_config')
            group_id = ui_file_info.get('group_id')
            row_index = ui_file_info.get('row_index')
            
            if not path:
                self._logger.warning(f"服务层：跳过无效条目，路径为空: {ui_file_info}")
                self.notify.emit(f"导入通知_{row_index}", {"message": "文件路径无效", "success": False}, "error")
                continue
            
            telethon_proxy_arg = None  # 用于传递给核心层连接
            db_proxy_id_for_add = None  # 用于存入数据库
            db_proxy_type_for_add = "none"  # 用于存入数据库 (元类型)
            
            if ui_proxy_config:
                ui_meta_type = ui_proxy_config.get('type')
                
                if ui_meta_type == 'ip_pool':
                    # 直接从 ui_proxy_config 获取所有信息
                    proxy_db_id = ui_proxy_config.get('id')
                    actual_proxy_protocol = ui_proxy_config.get('proxy_type')  # e.g., 'socks5', 'http'
                    ip = ui_proxy_config.get('ip')
                    port = ui_proxy_config.get('port')
                    username = ui_proxy_config.get('username')
                    password = ui_proxy_config.get('password')
                    
                    # 检查必要信息是否存在
                    if proxy_db_id is not None and actual_proxy_protocol and ip and port is not None:
                        telethon_proxy_arg = {
                            'type': 'ip_pool',  # none,system,ip_pool
                            'proxy_type': actual_proxy_protocol,
                            'ip': ip,
                            'port': port,
                        }
                        # 仅在提供时添加用户名/密码
                        if username:
                            telethon_proxy_arg['username'] = username
                            # 通常密码只有在有用户名时才有意义
                            if password:
                                telethon_proxy_arg['password'] = password
                        
                        db_proxy_id_for_add = proxy_db_id
                        db_proxy_type_for_add = "ip_pool"
                        self._logger.info(
                            f"服务层：准备使用 IP 池代理 {ip}:{port} (ID: {proxy_db_id}) for session {path}")
                    else:
                        self._logger.warning(
                            f"服务层：IP池代理配置不完整 {ui_proxy_config} for session {path}。将不使用代理。")
                        self.notify.emit(f"导入通知_{row_index}", {"message": "IP池代理配置不完整", "success": False},
                                         "warning")
                        # 保持默认的 "none" 和 None
                
                elif ui_meta_type == 'system':
                    telethon_proxy_arg = {'type': 'system'}
                    db_proxy_type_for_add = "system"
                    # db_proxy_id_for_add 保持 None
                    self._logger.info(f"服务层：准备使用系统代理 for session {path}")
                
                # else: ui_meta_type is 'none' or unknown, 保持默认 "none" 和 None
            
            # --- 准备完成，添加到列表 ---
            params_for_worker.append({
                'path': path,
                'proxy': telethon_proxy_arg  # 这是给 TelegramClientManager 使用的
            })
            auxiliary_data_map[path] = {
                'group_id': group_id,
                'db_proxy_id': db_proxy_id_for_add,
                'db_proxy_type': db_proxy_type_for_add,
                'row_index': row_index
            }
        # --- 参数准备循环结束 ---
        
        if not params_for_worker:
            self._logger.info("服务层：没有有效的session文件需要处理。")
            return 0, len(session_files_from_ui), "没有有效的session文件进行导入。"
        
        # --- 调用核心层进行批量导入 ---
        self._logger.info(f"服务层：准备调用核心层处理 {len(params_for_worker)} 个session。")
        task_id = self._telegram_worker.batch_import_sessions(params_for_worker, max_concurrent)
        
        # --- 等待并处理核心层返回结果 ---
        core_success, core_result_data = await self._telegram_worker.get_task_result(task_id, timeout = None)  # 可设置超时
        
        if not core_success:
            self._logger.error(f"服务层：核心层批量导入任务失败: {core_result_data}")
            # 通知UI失败
            for path_info in params_for_worker:
                aux_data = auxiliary_data_map.get(path_info['path'])
                if aux_data:
                    self.notify.emit(f"导入通知_{aux_data['row_index']}",
                                     {"message": f"核心处理错误: {core_result_data}", "success": False}, "error")
            return 0, len(params_for_worker), f"核心层处理失败: {core_result_data}"
        
        self._logger.info(f"服务层：核心层返回结果: {core_result_data}")
        accounts_added_count = 0
        accounts_failed_count = 0
        core_details = core_result_data.get('details', [])
        
        successfully_added_accounts_data = []  # 用于收集成功添加的账户数据
        
        for item_result in core_details:
            path = item_result.get('path')
            imported_successfully = item_result.get('success')
            user_info_or_error = item_result.get('result')
            
            aux_data = auxiliary_data_map.get(path)
            if not aux_data:
                self._logger.warning(f"服务层：处理核心层返回时找不到路径 {path} 的辅助数据。")
                # 这种情况理论上不应发生，除非核心层返回了未请求的路径
                # 暂时将其计入失败
                accounts_failed_count += 1
                continue
            
            row_idx_for_notify = aux_data['row_index']
            
            if imported_successfully:
                user_info = user_info_or_error
                phone = user_info.get('phone')
                if phone:
                    # 使用核心层返回的session文件路径，可能已被移动
                    final_session_path = user_info.get('session_file', path)
                    
                    add_db_success, acc_or_msg = await self.add_account(
                        phone = phone,
                        session_file = final_session_path,
                        user_info = user_info,
                        proxy_id = aux_data['db_proxy_id'],
                        proxy_type = aux_data['db_proxy_type'],
                        group_id = aux_data['group_id']
                    )
                    if add_db_success:
                        accounts_added_count += 1
                        account_dict = acc_or_msg.to_dict()  # 获取账户字典
                        successfully_added_accounts_data.append(account_dict)  # 收集数据
                        self.notify.emit(f"导入通知_{row_idx_for_notify}",
                                         {"message": "成功",
                                          "success": True,
                                          "account_data": account_dict},  # 传递完整的账户字典
                                         "success")
                    else:
                        accounts_failed_count += 1
                        self._logger.error(f"服务层：成功导入 {path} 但存库失败: {acc_or_msg}")
                        self.notify.emit(f"导入通知_{row_idx_for_notify}",
                                         {"message": f"存库失败: {acc_or_msg}", "success": False}, "error")
                else:
                    accounts_failed_count += 1
                    self._logger.error(f"服务层：成功导入 {path} 但无电话号码。")
                    self.notify.emit(f"导入通知_{row_idx_for_notify}", {"message": "无电话号码", "success": False},
                                     "error")
            else:
                accounts_failed_count += 1
                self._logger.warning(f"服务层：核心层导入 {path} 失败: {user_info_or_error}")
                self.notify.emit(f"导入通知_{row_idx_for_notify}",
                                 {"message": f"导入失败: {user_info_or_error}", "success": False}, "error")
        # --- 结果处理循环结束 ---
        
        # 在方法末尾，发出包含所有成功添加账户数据的信号
        if successfully_added_accounts_data:
            self.batch_accounts_added.emit(successfully_added_accounts_data)
        
        final_message = f"批量导入完成: {accounts_added_count} 个成功添加至数据库, {accounts_failed_count} 个失败。"
        # 完善失败计数逻辑: 考虑核心层报告的失败数和add_account失败数
        # total_failed = core_result_data.get('failed', 0) # 核心层的失败数
        # (可以根据需要添加更复杂的失败合并逻辑)
        
        self._logger.info(f"服务层：{final_message}")
        return accounts_added_count, accounts_failed_count, final_message
    
    # ==================== 代理相关功能 ====================
    
    async def get_proxy_ips(self) -> List[Dict[str, Any]]:
        """获取当前所有有效的代理IP列表，并附带账户绑定计数"""
        self._logger.info("获取有效代理IP列表（包含绑定计数）")
        try:
            
            active_proxies_basic_info = []
            async with get_session() as common_session:
                app_proxy_repo = ProxyRepository(common_session)
                proxies, _ = await app_proxy_repo.find_all(active_only = True, valid_only = True, limit = 500)
                active_proxies_basic_info = [
                    {
                        "id": p.id,
                        "ip": p.host,
                        "port": p.port,
                        "username": p.username,
                        "password": p.password,
                        "proxy_type": p.proxy_type
                    }
                    for p in proxies
                ]
            
            if not active_proxies_basic_info:
                self._logger.info("没有活动的代理IP返回。")
                return []
            
            proxy_ids = [p['id'] for p in active_proxies_basic_info if p.get('id') is not None]
            
            proxy_usage_counts = {}
            if proxy_ids:
                # 2. 获取这些代理ID的账户使用计数 (从 AccountRepository)
                async with get_session() as account_session:  # New session for account_repo
                    # account_repo = AccountRepository(session=account_session) # This is already imported
                    # 直接构建查询，无需修改 AccountRepository
                    stmt = (
                        select(
                            AccountModel.proxy_id,
                            func.count(AccountModel.id).label("count")
                        )
                        .where(AccountModel.proxy_id.in_(proxy_ids))
                        .group_by(AccountModel.proxy_id)
                    )
                    result = await account_session.execute(stmt)
                    for row in result.all():
                        if row.proxy_id is not None:  # Ensure proxy_id is not None before using as key
                            proxy_usage_counts[row.proxy_id] = row.count
            
            # 3. 合并计数到代理信息中
            proxies_with_counts = []
            for proxy_info in active_proxies_basic_info:
                proxy_id = proxy_info.get('id')
                # 使用从数据库查询到的真实绑定数
                proxy_info['account_count'] = proxy_usage_counts.get(proxy_id, 0)
                proxies_with_counts.append(proxy_info)
            
            self._logger.info(f"成功获取 {len(proxies_with_counts)} 个有效代理（已附带绑定计数）")
            return proxies_with_counts
        
        except Exception as e:
            self._logger.error(f"获取代理IP列表（含计数）异常: {e}", exc_info = True)
            return []
    
    async def increment_proxy_usage(self, proxy_id: int) -> bool:
        """增加代理使用次数

        Args:
            proxy_id: 代理ID

        Returns:
            是否成功增加
        """
        self._logger.info(f"增加代理使用次数: ID={proxy_id}")
        try:
            # 导入代理服务
            from app.services.proxy_service import ProxyService
            from data.repositories.proxy_repo import ProxyRepository
            from data.database import get_session
            
            # 使用异步上下文管理器获取会话
            async with get_session() as session:
                proxy_repo = ProxyRepository(session)
                proxy_service = ProxyService(proxy_repo)
                # 增加使用次数
                result = await proxy_service.increment_proxy_usage(proxy_id)
                return result
        except Exception as e:
            self._logger.error(f"增加代理使用次数异常: {e}")
            return False
    
    async def get_user_info(self, phone: str) -> Tuple[bool, Union[Dict[str, Any], str]]:
        """获取用户信息

        Args:
            phone: 手机号

        Returns:
            (成功标志, 用户信息字典或错误消息)
        """
        self._logger.info(f"获取用户信息: {phone}")
        try:
            # 获取用户信息
            task_id = self._telegram_worker.get_user_info(phone)
            success, result = await self._telegram_worker.get_task_result(task_id, timeout = 30)
            
            if not success:
                return False, f"获取用户信息失败: {result}"
            
            # 返回用户信息
            return True, result
        except Exception as e:
            self._logger.error(f"获取用户信息异常: {e}")
            return False, f"获取用户信息异常: {str(e)}"
    
    async def batch_auto_login(self, accounts: List[Dict[str, Any]], max_concurrent: int = 5) -> Dict[str, Any]:
        """批量自动登录账户

        Args:
            accounts: 账户列表, 每项包含 {phone, proxy_type, proxy_id (if proxy_type=='ip_pool')}
            max_concurrent: 最大并发数

        Returns:
            登录结果统计
        """
        self._logger.info(f"服务层：开始批量自动登录 {len(accounts)} 个账户, 最大并发: {max_concurrent}")
        
        # 检查telegram_worker是否为None
        if self._telegram_worker is None:
            error_msg = "服务层：telegram_worker未初始化，无法执行批量自动登录"
            self._logger.error(error_msg)
            self.notify.emit("批量登录错误", {"message": error_msg, "success": False}, "error")
            return {
                'total': len(accounts),
                'success': 0,
                'failed': len(accounts),
                'error': error_msg,
                'details': [{'phone': acc.get('phone', 'unknown'), 'success': False, 'error': 'telegram_worker未初始化'}
                            for acc in accounts]
            }
        
        tasks_for_worker = []
        
        for account_data_from_view in accounts:
            phone = account_data_from_view.get('phone')
            proxy_type_from_view = account_data_from_view.get('proxy_type')
            proxy_id_from_view = account_data_from_view.get('proxy_id')  # This is the DB ID of the proxy entry
            
            if not phone:
                self._logger.warning(f"服务层：跳过无手机号的账户数据: {account_data_from_view}")
                # Consider emitting a notify signal for this specific case if useful for UI
                continue
            
            proxy_config_for_worker = None  # Default to no proxy
            
            if proxy_type_from_view == 'ip_pool':
                if proxy_id_from_view is not None:
                    try:
                        async with get_session() as session:
                            # Import ProxyRepository locally to avoid circular dependency issues at module level
                            from data.repositories.proxy_repo import ProxyRepository
                            proxy_repo = ProxyRepository(session)
                            proxy_model = await proxy_repo.find_by_id(proxy_id_from_view)
                            
                            if proxy_model:
                                self._logger.info(f"服务层：账户 {phone} 使用IP池代理ID {proxy_id_from_view}, "
                                                  f"详情: {proxy_model.proxy_type} {proxy_model.host}:{proxy_model.port}")
                                proxy_config_for_worker = {
                                    'type': 'ip_pool',  # Meta-type for ClientManager
                                    'proxy_type': proxy_model.proxy_type,  # Actual protocol ('socks5', 'http', etc.)
                                    'ip': proxy_model.host,
                                    'port': proxy_model.port,
                                    'username': proxy_model.username,
                                    'password': proxy_model.password
                                }
                                # Clean up None username/password for Telethon if needed
                                if not proxy_config_for_worker.get('username'):
                                    proxy_config_for_worker.pop('username', None)
                                    proxy_config_for_worker.pop('password', None)
                            else:
                                error_msg = f"代理ID {proxy_id_from_view} 在数据库中未找到。"
                                self._logger.warning(f"服务层：账户 {phone} {error_msg}")
                                self.notify.emit(f"自动登录警告_{phone}",
                                                 {"message": error_msg + " 将不使用代理。", "success": False},
                                                 "warning")
                                # proxy_config_for_worker remains None
                    
                    except Exception as e:
                        error_msg = f"查询代理ID {proxy_id_from_view} 时出错: {e}"
                        self._logger.error(f"服务层：账户 {phone} {error_msg}", exc_info = True)
                        self.notify.emit(f"自动登录错误_{phone}",
                                         {"message": error_msg + " 将不使用代理。", "success": False},
                                         "error")
                        # proxy_config_for_worker remains None
                else:
                    warn_msg = "类型为 'ip_pool' 但缺少 proxy_id。"
                    self._logger.warning(f"服务层：账户 {phone} {warn_msg}")
                    self.notify.emit(f"自动登录警告_{phone}",
                                     {"message": warn_msg + " 将不使用代理。", "success": False},
                                     "warning")
                    # proxy_config_for_worker remains None
            
            elif proxy_type_from_view == 'system':
                self._logger.info(f"服务层：账户 {phone} 使用系统代理。")
                proxy_config_for_worker = {'type': 'system'}
            
            elif proxy_type_from_view == 'none' or proxy_type_from_view is None:
                self._logger.info(f"服务层：账户 {phone} 不使用代理。")
                # proxy_config_for_worker remains None
            
            else:  # Unknown proxy type
                warn_msg = f"未知的代理类型 '{proxy_type_from_view}'。"
                self._logger.warning(f"服务层：账户 {phone} {warn_msg}")
                self.notify.emit(f"自动登录警告_{phone}",
                                 {"message": warn_msg + " 将不使用代理。", "success": False},
                                 "warning")
                # proxy_config_for_worker remains None
            
            tasks_for_worker.append({
                'phone': phone,
                'proxy': proxy_config_for_worker
            })
        
        if not tasks_for_worker:
            self._logger.info("服务层：没有有效账户可供自动登录。")
            return {
                'total': len(accounts),
                'success': 0,
                'failed': len(accounts),
                'error': '没有有效账户处理',
                'details': []
            }
        
        try:
            # 调用核心层的批量登录功能
            self._logger.info(f"服务层：准备调用核心层处理 {len(tasks_for_worker)} 个账户的自动登录。")
            task_id = self._telegram_worker.batch_auto_login(tasks_for_worker, max_concurrent)
            # get_task_result的超时时间应与帐户数量相匹配
            # 例如，每个帐户平均30秒，加上一些缓冲。
            # 如果max_current为5，并且我们有20个帐户，则可能需要（20/5）*typical_login_time。
            # 让我们使用一个慷慨的超时，例如，每个帐户60秒+30秒。
            # 对于非常大的批次，这可能需要动态调整或非常长的静态超时。
            # 考虑worker.py中的get_task_result可能需要自己处理此超时。
            
            timeout_duration = 60 + (len(tasks_for_worker) * 30)  # 30s per account, 60s base
            self._logger.info(f"服务层：设置核心层任务超时为 {timeout_duration} 秒。")
            success, result = await self._telegram_worker.get_task_result(task_id, timeout = timeout_duration)
            
            if not success:
                self._logger.error(f"服务层：核心层批量自动登录失败: {result}")
                # Propagate a general failure if the whole batch task failed at core level
                return {
                    'total': len(tasks_for_worker),
                    'success': 0,
                    'failed': len(tasks_for_worker),
                    'error': f"核心层处理失败: {result}",
                    'details': []  # Or try to construct partial failure details if possible
                }
            
            # result from worker is expected to be: {'total', 'success', 'failed', 'details': [...]}
            # We can directly return this if the format matches.
            # Add progress information if not already included by the worker,
            # though worker's `notify` signal during its `batch_auto_login` is preferred for granular progress.
            if 'total' in result and 'success' in result and 'failed' in result:
                # Construct a summary message for the service layer log / final return
                summary_message = f"核心层登录完成: 成功 {result['success']}, 失败 {result['failed']} / {result['total']}"
                self._logger.info(f"服务层：{summary_message}")
                
                # 处理登录结果，更新数据库中的用户信息
                await self._process_batch_login_results(result.get('details', []))
                
                # The worker should emit detailed progress via notify.
                # The service might emit a final summary notify here if needed, or rely on controller.
            else:
                self._logger.warning(f"服务层：核心层返回结果格式不符合预期: {result}")
                # Fallback if format is unexpected
                return {
                    'total': len(tasks_for_worker),
                    'success': 0,
                    'failed': len(tasks_for_worker),
                    'error': '核心层返回结果格式不符',
                    'details': [{'phone': acc['phone'], 'success': False, 'error': '核心结果格式问题'} for acc in
                                tasks_for_worker]
                }
            
            return result  # Return the result from the worker directly
        
        except asyncio.TimeoutError:
            error_msg = "服务层：批量自动登录超时。"
            self._logger.error(error_msg)
            # Emit notify for overall timeout
            self.notify.emit("批量登录超时", {"message": error_msg, "success": False}, "error")
            return {
                'total': len(tasks_for_worker),
                'success': 0,
                'failed': len(tasks_for_worker),
                'error': error_msg,
                'details': [{'phone': acc['phone'], 'success': False, 'error': '服务层超时'} for acc in
                            tasks_for_worker]
            }
        except Exception as e:
            error_msg = f"服务层：批量自动登录时发生异常: {e}"
            self._logger.error(error_msg, exc_info = True)
            self.notify.emit("批量登录异常", {"message": error_msg, "success": False}, "error")
            return {
                'total': len(tasks_for_worker),
                'success': 0,
                'failed': len(tasks_for_worker),
                'error': str(e),
                'details': [{'phone': acc['phone'], 'success': False, 'error': str(e)} for acc in tasks_for_worker]
            }
            
            # ==================== 账户监控功能 ====================
    
    async def check_login_status(self, phone: str) -> bool:
        """检查账户是否登录

        Args:
            phone: 手机号

        Returns:
            是否登录
        """
        self._logger.info(f"检查账户登录状态: {phone}")
        try:
            async with get_session() as session:
                account_repo = AccountRepository(session = session)
                # 根据手机号查询账户
                account = await account_repo.get_account_by_phone(phone)
                if not account:
                    self._logger.warning(f"账户不存在: {phone}")
                    return False
                
                # 检查是否为活跃状态
                return account.is_active
        except Exception as e:
            self._logger.error(f"检查账户登录状态异常: {e}")
            return False
    
    async def check_connection_status(self, phone: str) -> bool:
        """检查账户连接状态

        Args:
            phone: 手机号

        Returns:
            是否连接
        """
        self._logger.info(f"检查账户连接状态: {phone}")
        try:
            # 首先检查数据库中的连接状态
            async with get_session() as session:
                account_repo = AccountRepository(session = session)
                account = await account_repo.get_account_by_phone(phone)
                if not account:
                    self._logger.warning(f"账户不存在: {phone}")
                    return False
                
                db_connection_status = account.is_connected
            
            # 然后检查实际的客户端连接状态
            task_id = self._telegram_worker.check_connection(phone)
            success, connection_status = await self._telegram_worker.get_task_result(task_id, timeout = 10)
            
            if success:
                # 如果状态不同步，更新数据库
                if connection_status != db_connection_status:
                    async with get_session() as session:
                        account_repo = AccountRepository(session = session)
                        await account_repo.update_account_by_phone(phone, is_connected = connection_status)
                        await session.commit()
                
                return connection_status
            else:
                self._logger.warning(f"检查客户端连接状态失败: {connection_status}")
                return db_connection_status  # 返回数据库中的状态
        except Exception as e:
            self._logger.error(f"检查账户连接状态异常: {e}")
            return False
    
    async def get_account_groups_by_type(self, phone: str, group_type: str = None) -> List[Dict[str, Any]]:
        """获取指定账户的群组或频道列表

        Args:
            phone: 手机号
            group_type: 类型，'group' 表示群组，'channel' 表示频道，None 表示全部

        Returns:
            群组/频道列表
        """
        self._logger.info(f"获取账户 {phone} 的{group_type or '所有'}对话列表")
        try:
            
            # 调用核心层获取对话列表，使用较短的超时时间
            task_id = self._telegram_worker.get_dialogs(phone, group_type)
            self._logger.info(f"开始等待获取对话列表结果，任务ID: {task_id}")
            
            try:
                # 使用15秒超时，比原来的30秒更短
                success, result = await self._telegram_worker.get_task_result(task_id, timeout = 30)
            except asyncio.TimeoutError:
                self._logger.error(f"获取对话列表超时 (15秒)，任务ID: {task_id}")
                self.notify.emit("获取对话列表失败", {"message": "获取对话列表超时，请稍后再试"}, "error")
                return []
            
            if not success:
                self._logger.error(f"获取对话列表失败: {result}")
                return []
            
            # 如果结果是字符串，表示出错
            if isinstance(result, str):
                self._logger.error(f"获取对话列表错误: {result}")
                return []
            
            self._logger.info(f"获取对话列表成功: {len(result)} 个")
            
            # 过滤掉用户对话，只保留群组和频道
            filtered_result = [item for item in result if item.get('type') in ('group', 'channel')]
            self._logger.info(f"过滤后的群组/频道: {len(filtered_result)} 个")
            
            return filtered_result
        
        except Exception as e:
            self._logger.exception(f"获取账户对话列表异常: {e}")
            return []
    
    # ================ 用户名处理功能 ================
    
    def _has_username_tags(self, username: str) -> bool:
        """检查用户名是否包含标签

        Args:
            username: 用户名字符串

        Returns:
            是否包含标签
        """
        tag_patterns = [
            r'\{数字\d+-\d+\}',
            r'\{字母\d+-\d+\}',
            r'\{数字字母\d+-\d+\}'
        ]
        
        for pattern in tag_patterns:
            if re.search(pattern, username):
                return True
        return False
    
    def _parse_username_tags(self, username_template: str, enhance_randomness: bool = False) -> str:
        """解析用户名模板中的标签，生成唯一用户名

        Args:
            username_template: 包含标签的用户名模板
            enhance_randomness: 是否增强随机性（用于批量生成时减少冲突）

        Returns:
            解析后的用户名字符串
        """
        if not username_template:
            return ""
        
        result = username_template
        
        # 解析 {数字1-3} 标签 - 支持多个标签
        digit_pattern = r'\{数字(\d+)-(\d+)\}'
        while re.search(digit_pattern, result):
            match = re.search(digit_pattern, result)
            if match:
                min_len, max_len = int(match.group(1)), int(match.group(2))
                # 确保范围合理
                min_len = max(1, min_len)
                max_len = min(10, max_len)  # 限制最大长度
                if min_len > max_len:
                    min_len, max_len = max_len, min_len
                
                # 增强随机性：倾向于使用更长的长度
                if enhance_randomness and max_len > min_len:
                    # 70%概率使用较长长度
                    if random.random() < 0.7:
                        length = random.randint(max(min_len, max_len - 1), max_len)
                    else:
                        length = random.randint(min_len, max_len)
                else:
                    length = random.randint(min_len, max_len)
                
                random_digits = ''.join(random.choices(string.digits, k = length))
                # 替换第一个匹配的标签
                result = re.sub(digit_pattern, random_digits, result, count = 1)
        
        # 解析 {字母1-5} 标签 - 支持多个标签
        letter_pattern = r'\{字母(\d+)-(\d+)\}'
        while re.search(letter_pattern, result):
            match = re.search(letter_pattern, result)
            if match:
                min_len, max_len = int(match.group(1)), int(match.group(2))
                # 确保范围合理
                min_len = max(1, min_len)
                max_len = min(10, max_len)  # 限制最大长度
                if min_len > max_len:
                    min_len, max_len = max_len, min_len
                
                # 增强随机性：倾向于使用更长的长度
                if enhance_randomness and max_len > min_len:
                    if random.random() < 0.7:
                        length = random.randint(max(min_len, max_len - 1), max_len)
                    else:
                        length = random.randint(min_len, max_len)
                else:
                    length = random.randint(min_len, max_len)
                
                random_letters = ''.join(random.choices(string.ascii_lowercase, k = length))
                # 替换第一个匹配的标签
                result = re.sub(letter_pattern, random_letters, result, count = 1)
        
        # 解析 {数字字母1-6} 标签 - 支持多个标签
        mixed_pattern = r'\{数字字母(\d+)-(\d+)\}'
        while re.search(mixed_pattern, result):
            match = re.search(mixed_pattern, result)
            if match:
                min_len, max_len = int(match.group(1)), int(match.group(2))
                # 确保范围合理
                min_len = max(1, min_len)
                max_len = min(15, max_len)  # 限制最大长度
                if min_len > max_len:
                    min_len, max_len = max_len, min_len
                
                # 增强随机性：倾向于使用更长的长度
                if enhance_randomness and max_len > min_len:
                    if random.random() < 0.7:
                        length = random.randint(max(min_len, max_len - 1), max_len)
                    else:
                        length = random.randint(min_len, max_len)
                else:
                    length = random.randint(min_len, max_len)
                
                chars = string.ascii_lowercase + string.digits
                random_mixed = ''.join(random.choices(chars, k = length))
                # 替换第一个匹配的标签
                result = re.sub(mixed_pattern, random_mixed, result, count = 1)
        
        return result
    
    def _validate_username(self, username: str) -> Tuple[bool, str]:
        """验证用户名格式

        Args:
            username: 用户名字符串

        Returns:
            (是否有效, 错误消息)
        """
        if not username:
            return True, ""  # 空用户名是允许的
        
        # 检查长度
        if len(username) < 5:
            return False, "用户名长度不能少于5个字符"
        if len(username) > 32:
            return False, "用户名长度不能超过32个字符"
        
        # 检查字符
        if not re.match(r'^[a-zA-Z0-9_]+$', username):
            return False, "用户名只能包含字母、数字和下划线"
        
        # 检查开头
        if username[0].isdigit():
            return False, "用户名不能以数字开头"
        
        return True, ""
    
    async def _generate_unique_username(self, username_template: str, account_repo: AccountRepository,
                                        max_retries: int = 5) -> Optional[str]:
        """生成唯一用户名，处理冲突重试

        Args:
            username_template: 用户名模板
            account_repo: 账户仓库实例
            max_retries: 最大重试次数

        Returns:
            生成的唯一用户名，如果失败返回None
        """
        for attempt in range(max_retries):
            # 生成用户名
            username = self._parse_username_tags(username_template)
            
            # 验证用户名格式
            is_valid, error_msg = self._validate_username(username)
            if not is_valid:
                self._logger.warning(f"生成的用户名格式无效: {username}, 错误: {error_msg}")
                continue
            
            # 检查用户名是否已存在
            try:
                existing_account = await account_repo.get_account_by_username(username)
                if not existing_account:
                    # 用户名不存在，可以使用
                    self._logger.info(f"生成唯一用户名成功: {username} (尝试 {attempt + 1}/{max_retries})")
                    return username
                else:
                    self._logger.info(f"用户名已存在: {username}, 重新生成 (尝试 {attempt + 1}/{max_retries})")
            except Exception as e:
                self._logger.error(f"检查用户名唯一性时发生错误: {e}")
                continue
        
        self._logger.error(f"无法生成唯一用户名，已尝试 {max_retries} 次")
        return None
    
    async def _generate_batch_unique_usernames(self, account_ids: List[int], username_template: str) -> Dict[int, str]:
        """批量生成唯一用户名，避免重复和冲突

        Args:
            account_ids: 账户ID列表
            username_template: 用户名模板

        Returns:
            账户ID到用户名的映射字典
        """
        self._logger.info(f"开始为 {len(account_ids)} 个账户批量生成唯一用户名")
        
        username_mappings = {}
        generated_usernames = set()  # 记录本次生成的用户名，避免内部重复
        max_total_attempts = len(account_ids) * 50  # 总体最大尝试次数
        current_attempts = 0
        
        async with get_session() as session:
            account_repo = AccountRepository(session = session)
            
            # 预先获取数据库中所有现有用户名，提高检查效率
            existing_usernames = await self._get_all_existing_usernames(account_repo)
            self._logger.info(f"数据库中现有 {len(existing_usernames)} 个用户名")
            
            # 检测用户名模板的随机性
            template_randomness = self._estimate_template_randomness(username_template)
            use_fallback_strategy = len(account_ids) > 10 and template_randomness < 1000
            
            if use_fallback_strategy:
                self._logger.info(f"检测到用户名模板随机性较低 (约{template_randomness}种组合)，启用后备策略")
            
            for account_id in account_ids:
                username_generated = False
                account_attempts = 0
                max_account_attempts = 100  # 单个账户最大尝试次数
                
                while not username_generated and account_attempts < max_account_attempts and current_attempts < max_total_attempts:
                    current_attempts += 1
                    account_attempts += 1
                    
                    # 生成候选用户名（启用增强随机性）
                    candidate_username = self._parse_username_tags(username_template, enhance_randomness = True)
                    
                    # 如果尝试次数较多且使用后备策略，添加随机后缀
                    if use_fallback_strategy and account_attempts > 20:
                        suffix = ''.join(random.choices(string.ascii_lowercase + string.digits, k = 3))
                        candidate_username = f"{candidate_username}_{suffix}"
                    
                    # 验证用户名格式
                    is_valid, error_msg = self._validate_username(candidate_username)
                    if not is_valid:
                        continue
                    
                    # 检查是否与现有用户名冲突（包括数据库和本次生成的）
                    if (candidate_username not in existing_usernames and
                            candidate_username not in generated_usernames):
                        # 用户名可用，记录映射
                        username_mappings[account_id] = candidate_username
                        generated_usernames.add(candidate_username)
                        username_generated = True
                        
                        self._logger.debug(
                            f"账户 {account_id} 生成用户名: {candidate_username} (尝试 {account_attempts} 次)")
                
                if not username_generated:
                    self._logger.warning(f"账户 {account_id} 无法生成唯一用户名 (尝试 {account_attempts} 次)")
        
        self._logger.info(
            f"批量用户名生成完成: 成功 {len(username_mappings)}/{len(account_ids)} 个，总尝试 {current_attempts} 次")
        
        if len(username_mappings) == 0:
            raise Exception("无法为任何账户生成唯一用户名，请检查用户名模板或数据库中的现有用户名")
        
        return username_mappings
    
    async def _get_all_existing_usernames(self, account_repo: AccountRepository) -> set:
        """获取数据库中所有现有的用户名

        Args:
            account_repo: 账户仓库实例

        Returns:
            现有用户名的集合
        """
        try:
            # 获取所有账户
            accounts = await account_repo.get_all_accounts(active_only = False)
            usernames = set()
            
            for account in accounts:
                if account.username:
                    usernames.add(account.username)
            
            return usernames
        except Exception as e:
            self._logger.error(f"获取现有用户名失败: {e}")
            return set()
    
    def _estimate_template_randomness(self, username_template: str) -> int:
        """估算用户名模板的随机性（可能的组合数量）

        Args:
            username_template: 用户名模板

        Returns:
            估算的可能组合数量
        """
        if not username_template:
            return 1
        
        total_combinations = 1
        
        # 分析数字标签
        digit_pattern = r'\{数字(\d+)-(\d+)\}'
        digit_matches = re.findall(digit_pattern, username_template)
        for min_len, max_len in digit_matches:
            min_len, max_len = int(min_len), int(max_len)
            min_len = max(1, min_len)
            max_len = min(10, max_len)
            if min_len > max_len:
                min_len, max_len = max_len, min_len
            
            # 计算该标签的组合数
            tag_combinations = 0
            for length in range(min_len, max_len + 1):
                tag_combinations += 10 ** length  # 10^length 种数字组合
            total_combinations *= tag_combinations
        
        # 分析字母标签
        letter_pattern = r'\{字母(\d+)-(\d+)\}'
        letter_matches = re.findall(letter_pattern, username_template)
        for min_len, max_len in letter_matches:
            min_len, max_len = int(min_len), int(max_len)
            min_len = max(1, min_len)
            max_len = min(10, max_len)
            if min_len > max_len:
                min_len, max_len = max_len, min_len
            
            # 计算该标签的组合数
            tag_combinations = 0
            for length in range(min_len, max_len + 1):
                tag_combinations += 26 ** length  # 26^length 种字母组合
            total_combinations *= tag_combinations
        
        # 分析混合标签
        mixed_pattern = r'\{数字字母(\d+)-(\d+)\}'
        mixed_matches = re.findall(mixed_pattern, username_template)
        for min_len, max_len in mixed_matches:
            min_len, max_len = int(min_len), int(max_len)
            min_len = max(1, min_len)
            max_len = min(15, max_len)
            if min_len > max_len:
                min_len, max_len = max_len, min_len
            
            # 计算该标签的组合数
            tag_combinations = 0
            for length in range(min_len, max_len + 1):
                tag_combinations += 36 ** length  # 36^length 种数字字母组合
            total_combinations *= tag_combinations
        
        return min(total_combinations, 1000000)  # 限制最大值，避免溢出
    
    async def _batch_update_with_username_template(self, account_ids: List[int], username_template: str, **kwargs) -> \
    Tuple[int, int, str]:
        """批量更新包含用户名模板的账户资料

        Args:
            account_ids: 账户ID列表
            username_template: 用户名模板
            **kwargs: 其他要更新的资料字段

        Returns:
            (成功数量, 失败数量, 消息)
        """
        self._logger.info(f"批量更新包含用户名模板的账户资料: {len(account_ids)} 个")
        
        # 检查账户ID列表是否为空
        if not account_ids:
            self._logger.warning("批量更新失败: 账户ID列表为空")
            return 0, 0, "批量更新失败: 没有选择任何账户或账户ID无效"
        
        success_count = 0
        fail_count = 0
        failed_accounts = []
        
        # 提取限制字段，单独处理
        daily_msg_limit = kwargs.pop('daily_msg_limit', None)
        daily_invite_limit = kwargs.pop('daily_invite_limit', None)
        
        # 移除用户名字段，因为要单独处理
        kwargs.pop('username', None)
        
        # 使用改进的批量用户名生成策略
        try:
            username_mappings = await self._generate_batch_unique_usernames(account_ids, username_template)
            self._logger.info(f"成功为 {len(username_mappings)} 个账户生成唯一用户名")
        except Exception as e:
            self._logger.error(f"批量生成用户名失败: {e}")
            return 0, len(account_ids), f"批量生成用户名失败: {str(e)}"
        
        # 使用单个session处理所有账户，提高性能
        async with get_session() as session:
            account_repo = AccountRepository(session = session)
            
            for account_id in account_ids:
                try:
                    # 获取账户信息
                    account = await account_repo.get_account(account_id)
                    if not account:
                        self._logger.warning(f"账户 {account_id} 不存在")
                        fail_count += 1
                        failed_accounts.append(account_id)
                        continue
                    
                    # 获取为该账户生成的用户名
                    unique_username = username_mappings.get(account_id)
                    if not unique_username:
                        self._logger.warning(f"账户 {account_id} ({account.phone}) 没有生成用户名")
                        fail_count += 1
                        failed_accounts.append(account_id)
                        continue
                    
                    # 准备单个账户的更新数据
                    single_update_data = kwargs.copy()
                    single_update_data['username'] = unique_username
                    
                    # 添加限制字段
                    if daily_msg_limit is not None:
                        single_update_data['daily_msg_limit'] = daily_msg_limit
                    if daily_invite_limit is not None:
                        single_update_data['daily_invite_limit'] = daily_invite_limit
                    
                    # 调用单个账户更新
                    success, message = await self.update_account_profile(account_id, **single_update_data)
                    if success:
                        success_count += 1
                        self._logger.info(f"账户 {account_id} ({account.phone}) 更新成功，用户名: {unique_username}")
                    else:
                        fail_count += 1
                        failed_accounts.append(account_id)
                        self._logger.warning(f"账户 {account_id} ({account.phone}) 更新失败: {message}")
                
                except Exception as e:
                    self._logger.error(f"处理账户 {account_id} 时发生错误: {str(e)}")
                    fail_count += 1
                    failed_accounts.append(account_id)
        
        if success_count > 0:
            message = f"批量更新完成: 成功 {success_count} 个, 失败 {fail_count} 个"
            if failed_accounts:
                message += f" (失败账户: {failed_accounts})"
            return success_count, fail_count, message
        else:
            return 0, fail_count, f"批量更新失败: 所有账户都无法生成唯一用户名"
    
    async def _validate_batch_update_data(self, **kwargs) -> Tuple[bool, str]:
        """验证批量更新数据的有效性

        Args:
            **kwargs: 要更新的字段

        Returns:
            (是否有效, 错误消息)
        """
        # 验证用户名模板
        username_template = kwargs.get('username')
        if username_template and self._has_username_tags(username_template):
            # 检查标签格式是否正确
            if not re.search(r'\{(数字|字母|数字字母)\d+-\d+\}', username_template):
                return False, "用户名模板格式错误，请使用正确的标签格式"
        
        # 验证头像路径
        profile_photo = kwargs.get('profile_photo')
        if profile_photo and not os.path.exists(profile_photo):
            return False, f"头像文件不存在: {profile_photo}"
        
        # 验证限制值
        daily_msg_limit = kwargs.get('daily_msg_limit')
        if daily_msg_limit is not None and (daily_msg_limit < 0 or daily_msg_limit > 1000):
            return False, "每日消息限制必须在0-1000之间"
        
        daily_invite_limit = kwargs.get('daily_invite_limit')
        if daily_invite_limit is not None and (daily_invite_limit < 0 or daily_invite_limit > 500):
            return False, "每日邀请限制必须在0-500之间"
        
        return True, ""
    
    async def _process_batch_login_results(self, login_details: List[Dict[str, Any]]):
        """处理批量登录结果，更新数据库中的用户信息

        Args:
            login_details: 登录详情列表，每项包含 {phone, success, result/error}
        """
        self._logger.info(f"开始处理批量登录结果，共 {len(login_details)} 个账户")
        
        # 使用异步上下文管理器获取会话
        async with get_session() as session:
            try:
                account_repo = AccountRepository(session = session)
                
                for detail in login_details:
                    phone = detail.get('phone', '')
                    success = detail.get('success', False)
                    
                    if not phone:
                        continue
                    
                    try:
                        # 根据手机号查找账户
                        account = await account_repo.get_account_by_phone(phone)
                        if not account:
                            self._logger.warning(f"未找到手机号为 {phone} 的账户")
                            continue
                        
                        if success:
                            # 登录成功，更新用户信息
                            result = detail.get('result', {})
                            if isinstance(result, dict):
                                update_fields = {
                                    'first_name': result.get('first_name', ''),
                                    'last_name': result.get('last_name', ''),
                                    'username': result.get('username', ''),
                                    'bio': result.get('bio', ''),
                                    'is_connected': True,  # 登录成功，状态为已连接
                                    'last_connected': datetime.datetime.now()  # 更新最后连接时间
                                }
                                
                                # 更新账户信息
                                await account_repo.update_account(account.id, **update_fields)
                                self._logger.info(f"已更新账户 {phone} 的用户信息")
                            else:
                                # 即使没有详细用户信息，也要更新连接状态
                                update_fields = {
                                    'is_connected': True,
                                    'last_connected': datetime.datetime.now()
                                }
                                await account_repo.update_account(account.id, **update_fields)
                                self._logger.info(f"已更新账户 {phone} 的连接状态")
                        else:
                            # 登录失败，更新连接状态为无法登录
                            update_fields = {
                                'is_connected': False
                            }
                            await account_repo.update_account(account.id, **update_fields)
                            self._logger.info(f"已将账户 {phone} 标记为无法登录")
                    
                    except Exception as e:
                        self._logger.error(f"处理账户 {phone} 登录结果时发生错误: {e}")
                        continue
                
                # 提交事务
                await session.commit()
                self._logger.info("批量登录结果处理完成，数据库已更新")
            
            except Exception as e:
                # 回滚事务
                await session.rollback()
                self._logger.error(f"处理批量登录结果时发生异常: {e}")
                raise
