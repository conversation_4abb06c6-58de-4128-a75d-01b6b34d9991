#!/usr/bin/python3
# -*- coding: utf-8 -*-
"""
认证服务模块 - 提供软件授权、用户登录注册等功能的服务层
"""

from typing import Dict, Any, Optional, Tuple
from PySide6.QtCore import QObject, Signal, QTimer, QSettings
from qasync import asyncSlot
from core.auth.api_service import api_service,ApiService
from utils.logger import get_logger
from utils.hardware import hardware_collector
import os
from frameConf.setting import CONFIG_FOLDER




class AuthService(QObject):

    """认证服务类，提供应用层的认证相关功能（全部异步）"""
    init_software_finished = Signal(bool, dict)  # (success, result)
    heartbeat_failed = Signal(str)  # 可选：心跳失败信号

    def __init__(self):
        super().__init__()
        # 获取模块日志记录器
        self._logger = get_logger("app.services.auth_service")
        self._service:ApiService = api_service
        self._heartbeat_timer = QTimer(self)
        self._heartbeat_timer.setInterval(60 * 1000)  # 60秒
        self._heartbeat_timer.timeout.connect(self._on_heartbeat_timer)
        self._heartbeat_running = False
        self.udid = None
    def get_udid(self):
        if not self.udid:
            self.udid = hardware_collector.get_machine_code()
        
 
    async def do_init_software(self, domain, app_id, key, ver_index, version):
        """异步初始化软件，完成后发射信号"""
        return await self._service.init_software(domain, app_id, key, ver_index, version)
           
    
    async def login(self,account:str,password:str)->Dict[str,Any]:
        """用户登录"""
        self.get_udid()
        return await self._service.login(account,password,uuid=self.udid)
    
    async def logout(self)->Dict[str,Any]:
        """用户登出"""
        return await self._service.logout()
    
    async def register(self,account:str,password:str,code:str,invid:str="",udid:str="")->Dict[str,Any]:
        """用户注册"""
        self.get_udid()
        return await self._service.register(account,password,code,invid,udid=self.udid)
    
    async def recharge(self,code:str)->Dict[str,Any]:
        """卡密充值"""
        return await self._service.recharge(code)
    
    async def get_user_info(self)->Dict[str,Any]:
        """获取用户信息"""
        return await self._service.get_user_info()

    async def heartbeat(self)->Dict[str,Any]:
        """心跳"""
        return await self._service.heartbeat()
    
    async def get_software_info(self)->Dict[str,Any]:
        """获取软件信息"""
        return await self._service.get_software_info()
    
    def start_auto_heartbeat(self):
        if not self._heartbeat_running:
            self._heartbeat_timer.start()
            self._heartbeat_running = True

    def stop_auto_heartbeat(self):
        if self._heartbeat_running:
            self._heartbeat_timer.stop()
            self._heartbeat_running = False

    @asyncSlot()
    async def _on_heartbeat_timer(self):
        result = await self.heartbeat()
        if not result.get("success"):
            self._logger.warning(f"心跳失败: {result.get('message', '未知错误')}")
            self.heartbeat_failed.emit(result.get("message", "心跳失败"))

    async def heartbeat(self) -> Dict[str, Any]:
        """心跳"""
        return await self._service.heartbeat()

    def get_settings(self):
        # QSettings存储在APPDATA目录
        settings_path = os.path.join(CONFIG_FOLDER, 'auth.ini')
        return QSettings(settings_path, QSettings.IniFormat)

    def save_credentials(self, email, password, remember_password, auto_login):
        settings = self.get_settings()
        settings.setValue('auth/email', email)
        settings.setValue('auth/password', password)
        settings.setValue('auth/remember_password', remember_password)
        settings.setValue('auth/auto_login', auto_login)
        settings.sync()

    def load_credentials(self):
        settings = self.get_settings()
        email = settings.value('auth/email', '')
        password = settings.value('auth/password', '')
        remember_password = settings.value('auth/remember_password', False, type=bool)
        auto_login = settings.value('auth/auto_login', False, type=bool)
        return email, password, remember_password, auto_login
    
    async def kami_topup(self, kami: str) -> Dict[str, Any]:
        """卡密充值"""
        return await self._service.kami_topup(kami)

    async def verify_vip(self) -> Dict[str, Any]:
        """会员到期检测"""
        return await self._service.verify_vip()



    