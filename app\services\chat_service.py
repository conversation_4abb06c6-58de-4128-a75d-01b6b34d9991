#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
聊天服务
负责处理与Telegram聊天、群组和频道相关的业务逻辑
"""

import datetime
from typing import List, Dict, Any, Optional, Tuple, Union

from PySide6.QtCore import QObject, Signal

from utils.logger import get_logger
from data.models.chat import ChatModel
from data.repositories.chat_repo import ChatRepository
from data.database import get_session
from core.telegram.client_worker import TelegramClientWorker


class ChatService(QObject):
    """聊天服务类，处理群组和频道相关的业务逻辑"""
    
    notify = Signal(str, object, str)
    
    def __init__(self, telegram_worker: TelegramClientWorker):
        """初始化聊天服务
        
        Args:
            telegram_worker: Telegram客户端工作线程
        """
        super().__init__()
        self._telegram_worker = telegram_worker
        self._telegram_worker.notify.connect(self.notify.emit)
        self._logger = get_logger("app.services.chat")
        self._logger.info("聊天服务初始化")
    
    async def sync_account_chats(self, phone: str) -> Tuple[int, int, str]:
        """同步账户的所有聊天（群组和频道）到数据库
        
        Args:
            phone: 账户手机号
            
        Returns:
            (新增数量, 更新数量, 消息)
        """
        self._logger.info(f"开始同步账户 {phone} 的聊天信息")
        
        try:
            # 检查账户连接状态
            task_id = self._telegram_worker.check_connection(phone)
            connected, result = await self._telegram_worker.get_task_result(task_id, timeout=10)
            
            if not connected:
                self._logger.warning(f"账户 {phone} 未连接，无法同步聊天信息")
                return 0, 0, "账户未连接，请先登录"
            
            # 获取所有对话
            task_id = self._telegram_worker.get_dialogs(phone)
            success, dialogs = await self._telegram_worker.get_task_result(task_id, timeout=60)
            
            if not success:
                self._logger.error(f"获取对话失败: {dialogs}")
                return 0, 0, f"获取对话失败: {dialogs}"
            
            # 同步到数据库
            new_count = 0
            update_count = 0
            
            async with get_session() as session:
                chat_repo = ChatRepository(session=session)
                
                for dialog in dialogs:
                    chat_id = dialog.get('id')
                    if not chat_id:
                        continue
                    
                    chat_type = dialog.get('type')
                    # 只处理群组和频道
                    if chat_type not in ['group', 'supergroup', 'channel']:
                        continue
                    
                    # 检查是否已存在
                    existing = await chat_repo.get_chat(phone, chat_id)
                    
                    # 准备数据
                    chat_data = {
                        'access_hash': dialog.get('access_hash'),
                        'chat_type': chat_type,
                        'title': dialog.get('title', ''),
                        'username': dialog.get('username', ''),
                        'participants_count': dialog.get('members_count', 0),
                        'is_creator': dialog.get('is_creator', False),
                        'is_admin': dialog.get('is_admin', False),
                        'is_member': True  # 如果能获取到对话，则当前用户肯定是成员
                    }
                    
                    if existing:
                        # 更新现有记录
                        await chat_repo.update_chat(phone, chat_id, **chat_data)
                        update_count += 1
                    else:
                        # 创建新记录
                        await chat_repo.create_chat(
                            phone=phone,
                            chat_id=chat_id,
                            **chat_data
                        )
                        new_count += 1
                
                # 提交事务
                await session.commit()
            
            self._logger.info(f"同步账户 {phone} 的聊天信息完成: 新增 {new_count} 个, 更新 {update_count} 个")
            return new_count, update_count, f"同步完成: 新增 {new_count} 个, 更新 {update_count} 个"
            
        except Exception as e:
            self._logger.error(f"同步账户 {phone} 的聊天信息异常: {e}")
            return 0, 0, f"同步异常: {str(e)}"
    
    async def get_account_chats(self, phone: str, chat_type: str = None, only_member: bool = True) -> List[Dict[str, Any]]:
        """获取账户的聊天列表
        
        Args:
            phone: 账户手机号
            chat_type: 聊天类型 ('group', 'supergroup', 'channel')，None表示所有类型
            only_member: 是否只返回当前是成员的聊天
            
        Returns:
            聊天列表，每项为聊天信息字典
        """
        self._logger.info(f"获取账户 {phone} 的聊天列表，类型: {chat_type or '所有'}, 仅成员: {only_member}")
        
        try:
            async with get_session() as session:
                chat_repo = ChatRepository(session=session)
                chats = await chat_repo.get_chats_by_account(phone, chat_type, only_member)
                # 转换为字典
                result = [chat.to_dict() for chat in chats]
                self._logger.info(f"获取到 {len(result)} 个聊天")
                return result
        except Exception as e:
            self._logger.error(f"获取账户 {phone} 的聊天列表异常: {e}")
            return []
    
    async def get_monitored_chats(self, phone: str = None, monitor_type: str = "all", only_member: bool = True) -> List[Dict[str, Any]]:
        """获取被监控的聊天
        
        Args:
            phone: 账户手机号，None表示所有账户
            monitor_type: 监控类型 ("user", "keyword", "all")
            only_member: 是否只返回当前是成员的聊天
            
        Returns:
            聊天列表，每项为聊天信息字典
        """
        self._logger.info(f"获取{'所有账户' if phone is None else f'账户 {phone}'} 的 {monitor_type} 监控聊天")
        
        try:
            async with get_session() as session:
                chat_repo = ChatRepository(session=session)
                
                if monitor_type == "user":
                    chats = await chat_repo.get_user_monitored_chats(phone, only_member)
                elif monitor_type == "keyword":
                    chats = await chat_repo.get_keyword_monitored_chats(phone, only_member)
                else:  # all
                    chats = await chat_repo.get_all_monitored_chats(phone, only_member)
                
                # 转换为字典
                result = [chat.to_dict() for chat in chats]
                self._logger.info(f"获取到 {len(result)} 个监控聊天")
                return result
        except Exception as e:
            self._logger.error(f"获取监控聊天列表异常: {e}")
            return []
    
    async def set_chat_monitor_status(self, phone: str, chat_id: int, user_monitor: bool = None, keyword_monitor: bool = None) -> Tuple[bool, str]:
        """设置聊天的监控状态
        
        Args:
            phone: 账户手机号
            chat_id: 聊天ID
            user_monitor: 是否启用用户监控
            keyword_monitor: 是否启用关键词监控
            
        Returns:
            (成功标志, 消息)
        """
        self._logger.info(f"设置聊天 {phone}-{chat_id} 的监控状态: 用户={user_monitor}, 关键词={keyword_monitor}")
        
        try:
            async with get_session() as session:
                try:
                    chat_repo = ChatRepository(session=session)
                    success = await chat_repo.set_monitor_status(phone, chat_id, user_monitor, keyword_monitor)
                    
                    if not success:
                        return False, "聊天不存在"
                    
                    # 提交事务
                    await session.commit()
                    return True, "设置监控状态成功"
                except Exception as e:
                    # 回滚事务
                    await session.rollback()
                    self._logger.error(f"设置监控状态数据库操作异常: {e}")
                    return False, f"设置监控状态异常: {str(e)}"
        except Exception as e:
            self._logger.error(f"设置监控状态异常: {e}")
            return False, f"设置监控状态异常: {str(e)}"
