#!/usr/bin/env python
# -*- coding: utf-8 -*-

from typing import List, Dict, Any, Optional, Tuple
from pathlib import Path
from core.converter.converter import TelegramConverter
from utils.logger import get_logger


class ConvertService:
    """转换服务，处理Tdata和Session格式转换的服务层逻辑"""
    
    def __init__(self):
        self._logger = get_logger(__name__)
    
    async def batch_convert(self,
                            files: List[str],
                            output_dir: str,
                            convert_type: str,
                            proxy_config: Dict[str, Any],
                            password_info: Dict[str, Any],
                            progress_callback: Optional[callable] = None) -> List[Dict[str, Any]]:
        """批量转换文件

        Args:
            files: 文件路径列表
            output_dir: 输出目录
            convert_type: 转换类型，"session_to_tdata" 或 "tdata_to_session"
            proxy_config: 代理配置
            password_info: 密码信息
            progress_callback: 进度回调函数

        Returns:
            转换结果列表
        """
        try:
            self._logger.info(f"开始批量转换: {convert_type}, 文件数: {len(files)}")
            
            # 规范化代理配置
            proxy_tuple = self._normalize_proxy_config(proxy_config)
            
            # 创建转换器
            converter = TelegramConverter(proxy = proxy_tuple)
            
            # 执行批量转换
            results = await converter.batch_convert(
                files, output_dir, convert_type, password_info, progress_callback
            )
            
            self._logger.info(f"批量转换完成，结果数: {len(results)}")
            return results
        
        except Exception as e:
            self._logger.error(f"批量转换失败: {e}")
            raise
    
    def _normalize_proxy_config(self, proxy_config: Dict[str, Any]) -> Optional[Tuple]:
        """规范化代理配置为converter可用的格式

        Args:
            proxy_config: 代理配置字典

        Returns:
            代理配置元组或None
        """
        if not proxy_config or proxy_config.get("type") == "none":
            self._logger.info("使用无代理模式")
            return None
        
        if proxy_config.get("type") == "system":
            self._logger.info("使用系统代理模式")
            try:
                from utils.get_system_proxy import get_win_proxy_info
                system_proxy = get_win_proxy_info()
                if system_proxy:
                    proxy_tuple = ("socks5", system_proxy['ip'], system_proxy['port'])
                    self._logger.info(f"获取到系统代理: {system_proxy['ip']}:{system_proxy['port']}")
                    return proxy_tuple
                else:
                    self._logger.warning("未检测到系统代理设置，使用无代理模式")
                    return None
            except Exception as e:
                self._logger.error(f"读取系统代理失败: {e}，使用无代理模式")
                return None
        
        if proxy_config.get("type") == "ip_pool":
            # IP池代理
            proxy_type = proxy_config.get("proxy_type", "socks5")
            ip = proxy_config.get("ip")
            port = proxy_config.get("port")
            username = proxy_config.get("username")
            password = proxy_config.get("password")
            
            if not ip or not port:
                self._logger.warning("IP池代理配置不完整，使用无代理模式")
                return None
            
            if username and password:
                proxy_tuple = (proxy_type, ip, port, username, password)
            else:
                proxy_tuple = (proxy_type, ip, port)
            
            self._logger.info(f"使用IP池代理: {proxy_type}://{ip}:{port}")
            return proxy_tuple
        
        if proxy_config.get("type") == "custom":
            # 自定义代理
            proxy_type = proxy_config.get("proxy_type", "socks5")
            ip = proxy_config.get("ip")
            port = proxy_config.get("port")
            username = proxy_config.get("username")
            password = proxy_config.get("password")
            
            if not ip or not port:
                self._logger.warning("自定义代理配置不完整，使用无代理模式")
                return None
            
            if username and password:
                proxy_tuple = (proxy_type, ip, port, username, password)
            else:
                proxy_tuple = (proxy_type, ip, port)
            
            self._logger.info(f"使用自定义代理: {proxy_type}://{ip}:{port}")
            return proxy_tuple
        
        self._logger.warning(f"未知的代理类型: {proxy_config.get('type')}")
        return None
    
    def validate_files(self, files: List[str], convert_type: str) -> Tuple[List[str], List[str]]:
        """验证文件有效性

        Args:
            files: 文件路径列表
            convert_type: 转换类型

        Returns:
            (有效文件列表, 无效文件列表)
        """
        valid_files = []
        invalid_files = []
        
        for file_path in files:
            path = Path(file_path)
            
            if convert_type == "session_to_tdata":
                # 验证session文件
                if path.exists() and path.is_file() and path.suffix == '.session':
                    valid_files.append(file_path)
                else:
                    invalid_files.append(file_path)
            
            elif convert_type == "tdata_to_session":
                # 验证tdata文件夹
                if path.exists() and path.is_dir():
                    # 检查是否包含map0或map1文件
                    map0_path = path / 'map0'
                    map1_path = path / 'map1'
                    if map0_path.exists() or map1_path.exists():
                        valid_files.append(file_path)
                    else:
                        invalid_files.append(file_path)
                else:
                    invalid_files.append(file_path)
        
        self._logger.info(f"文件验证完成: 有效 {len(valid_files)}, 无效 {len(invalid_files)}")
        return valid_files, invalid_files
    
    def prepare_import_config(self, proxy_config: Dict[str, Any], group_id: Optional[int] = None) -> Dict[str, Any]:
        """准备账户导入配置

        Args:
            proxy_config: 代理配置
            group_id: 分组ID

        Returns:
            导入配置字典
        """
        import_config = {
            "proxy_config": proxy_config,
            "group_id": group_id
        }
        
        self._logger.info(f"准备导入配置: 代理类型={proxy_config.get('type')}, 分组ID={group_id}")
        return import_config
