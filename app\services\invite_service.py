import asyncio
import random
from typing import List, Dict, Optional, Any, Tuple, Union
import time
from datetime import datetime, date

from sqlalchemy.ext.asyncio import AsyncSession
from qasync import asyncSlot
from data.database import get_session
from data.repositories.invite_task_repo import InviteTaskRepository
from data.models.invite import InviteTask, InviteRecord, InviteLimit
from core.telegram.client_worker import TelegramClientWorker
from utils.logger import get_logger
from data.repositories.monitor_repo import MonitorTaskRepository
from core.invite.invite_task_manager import InviteTaskManager
from qasync import asyncSlot
from PySide6.QtCore import Signal,QObject

class InviteService(QObject):
    """邀请任务服务"""
    task_stats_changed = Signal(int, int, int, int)  # 任务ID, success, failed, pending
    task_progress_changed = Signal(int, int, str)  # 任务ID, 进度, 状态
    
    def __init__(self, client_worker: TelegramClientWorker):
        super().__init__()
        self._task_manager = InviteTaskManager(client_worker,self)
        self._logger = get_logger("app.services.invite")
        
        # 连接信号
        self._task_manager.task_progress.connect(self._on_task_progress)
        self._task_manager.task_completed.connect(self._on_task_completed)
        self._task_manager.task_stats_changed.connect(self._on_task_stats_changed)
        
        # 初始化，重置所有运行中的任务为暂停状态
    
    async def task_initialize(self):
        """初始化，重置所有运行中的任务状态"""
        try:
            self._logger.info("初始化邀请服务，重置运行中的任务为暂停状态")
            async with get_session() as session:
                repo = InviteTaskRepository(session)
                # 查找所有运行中的任务
                running_tasks = await repo.get_all_tasks("running")
                for task in running_tasks:
                    self._logger.info(f"重置任务 {task.id} 状态: running -> paused")
                    await repo.update_task_status(task.id, "paused")
                await session.commit()
                self._logger.info(f"成功重置 {len(running_tasks)} 个任务状态")
        except Exception as e:
            self._logger.error(f"初始化邀请服务失败: {e}")
    
    async def create_invite_task(self, task_data: Dict[str, Any]) -> tuple:
        """创建邀请任务"""
        self._logger.info(f"创建邀请任务: {task_data.get('task_name')}")
        async with get_session() as session:
            try:
                repo = InviteTaskRepository(session)
                # 1. 创建任务
                task = await repo.create_task(task_data)
                # 2. 导入用户到InviteRecord
                if task_data.get("invite_type") == "custom" and task_data.get("users"):
                    for user in task_data["users"]:
                        record_data = {
                            "task_id": task.id,
                            "invitee": user,
                            "status": "pending",
                            "error_message": None
                        }
                        await repo.create_invite_record(record_data)
                elif task_data.get("invite_type") == "task" and task_data.get("monitor_task_id"):
                    # 采集用户模式，支持多个监控任务id，分批导入所有用户
                    monitor_repo = MonitorTaskRepository(session)
                    monitor_ids = task_data["monitor_task_id"]
                    if not isinstance(monitor_ids, list):
                        monitor_ids = [monitor_ids]
                    all_users = []
                    for mid in monitor_ids:
                        users = await monitor_repo.get_all_usernames_or_ids_for_task(mid)
                        all_users.extend(users)
                    # 去重（以target_id为唯一标识）
                    seen = set()
                    unique_users = []
                    for user in all_users:
                        tid = user.get("target_id")
                        if tid and tid not in seen:
                            unique_users.append(user)
                            seen.add(tid)
                    # 分批写入
                    BATCH_SIZE = 1000
                    total = len(unique_users)
                    for i in range(0, total, BATCH_SIZE):
                        batch = unique_users[i:i+BATCH_SIZE]
                        for user in batch:
                            invitee = user.get("target_id")
                            invitee_name = user.get("target_name")
                            record_data = {
                                "task_id": task.id,
                                "invitee": invitee,
                                "invitee_name": invitee_name,
                                "status": "pending",
                                "error_message": None
                            }
                            await repo.create_invite_record(record_data)
                        await asyncio.sleep(0)
                # 3. 为所有accounts创建invite_limits
                accounts = task_data.get("accounts", [])
                for acc in accounts:
                    phone = acc.get("phone") if isinstance(acc, dict) else acc
                    await repo.create_or_update_invite_limit(phone)
                # 4. 提交事务
                await session.commit()
                return True, task.to_dict()
            except Exception as e:
                self._logger.error(f"创建邀请任务失败: {str(e)}")
                await session.rollback()
                return False, str(e)

    
    async def get_invite_tasks(self, status: Optional[str] = None) -> List[Dict[str, Any]]:
        """获取邀请任务列表"""
        self._logger.info(f"获取邀请任务列表, status={status}")
        
        async with get_session() as session:
            try:
                repo = InviteTaskRepository(session)
                tasks = await repo.get_all_tasks(status)
                
                # 转换为字典列表
                result = []
                for task in tasks:
                    # 获取任务统计数据
                    stats = await repo.get_task_statistics(task.id)
                    
                    task_dict = task.to_dict()
                    task_dict["stats"] = stats
                    result.append(task_dict)
                
                return result
            except Exception as e:
                self._logger.error(f"获取邀请任务列表失败: {str(e)}")
                raise
    
    async def get_invite_task(self, task_id: int) -> Optional[Dict[str, Any]]:
        """获取邀请任务详情"""
        self._logger.info(f"获取邀请任务详情, task_id={task_id}")
        
        async with get_session() as session:
            try:
                repo = InviteTaskRepository(session)
                task = await repo.get_task_by_id(task_id)
                
                if not task:
                    return None
                
                # 获取任务统计数据
                stats = await repo.get_task_statistics(task.id)
                
      
                # 合并结果
                task_dict = task.to_dict()
                task_dict["stats"] = stats
                
                return task_dict
            except Exception as e:
                self._logger.error(f"获取邀请任务详情失败, task_id={task_id}: {str(e)}")
                raise
    
    async def update_invite_task(self, task_id: int, task_data: Dict[str, Any]) -> bool:
        """更新邀请任务"""
        self._logger.info(f"更新邀请任务, task_id={task_id}")
        
        async with get_session() as session:
            try:
                repo = InviteTaskRepository(session)
                result = await repo.update_task(task_id, task_data)
                
                # 提交事务
                await session.commit()
                
                return result
            except Exception as e:
                self._logger.error(f"更新邀请任务失败, task_id={task_id}: {str(e)}")
                await session.rollback()
                raise
    
    async def delete_invite_task(self, task_id: int) -> tuple:
        """删除邀请任务及其所有邀请记录"""
        self._logger.info(f"删除邀请任务及记录, task_id={task_id}")
        if self._task_manager.is_task_running(task_id):
            msg = f"任务 {task_id} 正在运行，不能删除，请先停止任务！"
            self._logger.warning(msg)
            return False, msg
        async with get_session() as session:
            try:
                repo = InviteTaskRepository(session)
                await repo.delete_invite_records_by_task(task_id)
                result = await repo.delete_task(task_id)
                await session.commit()
                return result, ("删除成功" if result else "任务不存在")
            except Exception as e:
                self._logger.error(f"删除邀请任务及记录失败, task_id={task_id}: {str(e)}")
                await session.rollback()
                return False, f"删除失败: {str(e)}"
    
    async def start_invite_task(self, task_id: int) -> bool:
        """开始执行邀请任务"""
        self._logger.info(f"开始执行邀请任务, task_id={task_id}")

        # 1. 获取任务数据
        task_detail = await self.get_invite_task(task_id)
        if not task_detail:
            self._logger.error(f"任务不存在, task_id={task_id}")
            return False

        # 2. 更新任务状态
        async with get_session() as session:
            repo = InviteTaskRepository(session)
            await repo.update_task_status(task_id, "running")
            await session.commit()

        # 3. 启动任务
        success = self._task_manager.start_task(task_id, task_detail)
        return success
    
    async def stop_invite_task(self, task_id: int) -> bool:
        """停止邀请任务"""
        self._logger.info(f"停止邀请任务, task_id={task_id}")
        
        if task_id not in self._task_manager._tasks:
            self._logger.warning(f"任务未在运行中, task_id={task_id}")
            return False
        
        # 停止任务
        success = self._task_manager.stop_task(task_id)
        
        if success:
            # 更新任务状态为暂停
            async with get_session() as session:
                try:
                    repo = InviteTaskRepository(session)
                    await repo.update_task_status(task_id, "paused")
                    
                    # 提交事务
                    await session.commit()
                except Exception as e:
                    self._logger.error(f"更新任务状态失败, task_id={task_id}: {str(e)}")
                    await session.rollback()
                    return False
            
            return True
        else:
            self._logger.error(f"停止任务 {task_id} 失败")
            return False
    @asyncSlot()
    async def _on_task_progress(self, task_id, progress, status):
        """处理任务进度更新"""
        self._logger.info(f"任务 {task_id} 进度更新: {progress}%, 状态: {status}")
        try:
            async with get_session() as session:
                repo = InviteTaskRepository(session)
                # 更新任务进度
                await repo.update_task(task_id, {"progress": progress})
                await session.commit()
                
            # 发射进度更新信号
            self.task_progress_changed.emit(task_id, progress, status)
        except Exception as e:
            self._logger.error(f"更新任务进度失败: {e}")
    
    @asyncSlot()
    async def  _on_task_completed(self, task_id, success, message):
        """处理任务完成事件"""
        self._logger.info(f"任务 {task_id} 完成: 成功={success}, 消息={message}")
        try:
            async with get_session() as session:
                repo = InviteTaskRepository(session)
                # 更新任务状态
                if success:
                    status = "completed"
                else:
                    # 检查消息内容，如果是被中断，则设置为暂停状态
                    status = "paused" if "被中断" in message else "failed"
                
                await repo.update_task(task_id, {
                    "status": status,
                    "message": message
                })
                await session.commit()
        except Exception as e:
            self._logger.error(f"更新任务完成状态失败: {e}")
    
    async def check_invite_limit(self, account_phone: str) -> Dict[str, Any]:
        """检查账户邀请限制"""
        self._logger.info(f"检查账户邀请限制, account_phone={account_phone}")
        
        async with get_session() as session:
            try:
                repo = InviteTaskRepository(session)
                limit = await repo.get_invite_limit(account_phone)
                
                if not limit:
                    # 如果不存在，创建默认限制
                    limit = await repo.create_or_update_invite_limit(account_phone)
                
                # 检查日期是否需要重置
                today = date.today()
                if limit.last_reset_date != today:
                    limit = await repo.create_or_update_invite_limit(account_phone, limit.max_daily_limit)
                
                return {
                    "account_phone": limit.account_phone,
                    "current_day_count": limit.current_day_count,
                    "max_daily_limit": limit.max_daily_limit,
                    "remaining": limit.max_daily_limit - limit.current_day_count,
                    "last_reset_date": limit.last_reset_date.isoformat()
                }
            except Exception as e:
                self._logger.error(f"检查账户邀请限制失败, account_phone={account_phone}: {str(e)}")
                raise
    
    async def update_invite_limit(self, account_phone: str, max_daily_limit: int) -> Dict[str, Any]:
        """更新账户邀请限制"""
        self._logger.info(f"更新账户邀请限制, account_phone={account_phone}, max_daily_limit={max_daily_limit}")
        
        async with get_session() as session:
            try:
                repo = InviteTaskRepository(session)
                limit = await repo.create_or_update_invite_limit(account_phone, max_daily_limit)
                
                await session.commit()
                
                return {
                    "account_phone": limit.account_phone,
                    "current_day_count": limit.current_day_count,
                    "max_daily_limit": limit.max_daily_limit,
                    "remaining": limit.max_daily_limit - limit.current_day_count,
                    "last_reset_date": limit.last_reset_date.isoformat()
                }
            except Exception as e:
                self._logger.error(f"更新账户邀请限制失败, account_phone={account_phone}: {str(e)}")
                await session.rollback()
                raise
    
    async def increment_invite_count(self, account_phone: str, count: int = 1) -> Dict[str, Any]:
        """增加账户邀请计数"""
        self._logger.info(f"增加账户邀请计数, account_phone={account_phone}, count={count}")
        
        async with get_session() as session:
            try:
                repo = InviteTaskRepository(session)
                success, remaining = await repo.increment_invite_count(account_phone, count)
                
                await session.commit()
                
                # 获取最新的限制信息
                limit = await repo.get_invite_limit(account_phone)
                
                return {
                    "success": success,
                    "account_phone": limit.account_phone,
                    "current_day_count": limit.current_day_count,
                    "max_daily_limit": limit.max_daily_limit,
                    "remaining": remaining,
                    "last_reset_date": limit.last_reset_date.isoformat()
                }
            except Exception as e:
                self._logger.error(f"增加账户邀请计数失败, account_phone={account_phone}: {str(e)}")
                await session.rollback()
                raise
    
    async def get_monitor_tasks_with_user_count(self) -> list:
        """获取监控任务及其用户数量"""
        self._logger.info("获取监控任务及其用户数量")
        
        async with get_session() as session:
            repo = MonitorTaskRepository(session)
            return await repo.get_all_tasks_with_user_count()
    
    async def _create_invite_record(self, task_id, invitee, status, error_message=None):
        """更新邀请记录（只允许update，不允许insert）"""
        self._logger.debug(f"创建邀请记录: 任务={task_id}, 用户={invitee}, 状态={status}")
        try:
            async with get_session() as session:
                repo = InviteTaskRepository(session)
                await repo.update_invite_record_by_task_and_invitee(task_id, invitee, status, error_message)
                await session.commit()
                return True
        except Exception as e:
            self._logger.error(f"创建邀请记录失败: {e}")
            return False
    
    def _on_task_stats_changed(self, task_id, success, failed, pending):
        self.task_stats_changed.emit(task_id, success, failed, pending)
    
    async def update_invite_record(self, task_id, invitee, status, error_message=None):
        """服务层：更新邀请记录状态"""
        self._logger.debug(f"服务层更新邀请记录: 任务={task_id}, 用户={invitee}, 状态={status}")
        try:
            async with get_session() as session:
                repo = InviteTaskRepository(session)
                await repo.update_invite_record_by_task_and_invitee(task_id, invitee, status, error_message)
                await session.commit()
                return True
        except Exception as e:
            self._logger.error(f"服务层更新邀请记录失败: {e}")
            return False
   
