import asyncio
from datetime import datetime, timezone
import random
import time
from typing import Dict, List, Optional, Tuple, Any, Callable, Union

from sqlalchemy.orm import Session
from sqlalchemy import func, select
from PySide6.QtCore import QObject, Signal

from core.telegram.client_worker import TelegramClientWorker
from data.database import get_session
from data.models.message import ( MessageTaskStatus, MessageTaskTargetLog,
                                 )
from data.repositories.message_repo import MessageTaskRepository
from utils.logger import get_logger
from utils.html_processor import HtmlProcessor
from core.services.account_limit_service import AccountLimitService

class MessageSendingService(QObject):
    """消息发送服务，连接UI和核心业务层"""

    # 定义信号
    task_updated = Signal(dict)  # 任务更新信号
    task_status_changed = Signal(str, str)  # 任务状态变更信号(task_id, status)
    task_progress_updated = Signal(str, int, int, int)  # 任务进度更新信号(task_id, processed, success, failed)
    task_created = Signal(dict)  # 创建任务成功信号
    task_deleted = Signal(str)   # 删除任务信号
    
    def __init__(self, client_worker: TelegramClientWorker, parent=None):
        """初始化消息发送服务
        
        Args:
            client_worker: Telegram客户端工作线程
            parent: 父QObject
        """
        super().__init__(parent)
        self._client_worker = client_worker
        self._logger = get_logger("app.services.message_sending")
        self._running_tasks = {}  # 存储正在运行的任务 {task_id: asyncio.Task}
        
        # 连接客户端工作线程信号
        self._client_worker.notify.connect(self._on_client_notify)
            
    def _on_client_notify(self, event, data, info_type):
        """处理客户端通知事件"""
        self._logger.debug(f"收到客户端通知: {event}, 类型: {info_type}")
        
    async def create_task(self, task_data: Dict) -> Dict:
        """创建任务，批量插入目标日志"""
        print(task_data)
        async with get_session() as session:
            repo = MessageTaskRepository(session)
            
            targets = task_data.get("targets", [])
            target_type = task_data.get("target_type", "custom")
            
            task_data_copy = task_data.copy()
            if "targets" in task_data_copy:
                task_data_copy.pop("targets")
            
            # 1. 写入主表
            task = await repo.add_task(task_data_copy)
            
            logs = []
            if target_type == "task":
                # 监听任务模式，targets为监听任务id列表
                from data.repositories.monitor_repo import MonitorTaskRepository
                monitor_repo = MonitorTaskRepository(session)
                all_users = []
                for monitor_id in targets:
                    users = await monitor_repo.get_all_usernames_or_ids_for_task(monitor_id)
                    all_users.extend(users)
                # 去重（以target_id为唯一标识）
                seen = set()
                unique_users = []
                for user in all_users:
                    tid = user.get("target_id")
                    if tid and tid not in seen:
                        unique_users.append(user)
                        seen.add(tid)
                # 分批写入
                batch_size = 1000
                for i in range(0, len(unique_users), batch_size):
                    for user in unique_users[i:i+batch_size]:
                        log_data = {
                            "task_id": task.id,
                            "target_id": user.get("target_id"),
                            "target_name": user.get("target_name"),
                            "status": "pending"
                        }
                        await repo.add_target_log(log_data)
                    await asyncio.sleep(0)  # 让出事件循环，防止UI卡顿
            else:
                # 原有逻辑
                for t in targets:
                    log_data = {
                        "task_id": task.id,
                        "target_id": t.get("id") if isinstance(t, dict) else t,
                        "target_name": t.get("name") if isinstance(t, dict) else None,
                        "status": "pending"
                    }
                    logs.append(log_data)
                batch_size = 1000
                for i in range(0, len(logs), batch_size):
                    for log in logs[i:i+batch_size]:
                        await repo.add_target_log(log)
            await session.commit()
            task_info = task.to_dict()
            self.task_created.emit(task_info)
            return task_info
    
    def _choose_message(self, task):
        """随机选择一条消息内容并处理"""
        messages = task.messages or []
        if not messages:
            return "", None, "text"
        
        # 随机选择一条消息
        msg_data = random.choice(messages)
        msg_type = msg_data.get("type", "text")
        content = msg_data.get("content", "")
        
        # 处理随机表情
        if getattr(task, "random_emoji", False):
            emojis = ["😀", "😃", "😄", "😁", "😆", "😅", "😂", "🤣", "😊", "😇", 
                      "🙂", "🙃", "😉", "😌", "😍", "🥰", "😘", "😗", "😙", "😚", 
                      "😋", "😛", "😝", "😜", "🤪", "🤨", "🧐", "🤓", "😎", "🤩", 
                      "🥳", "😏", "😒", "😞", "😔", "😟", "😕", "🙁", "☹️", "😣", 
                      "😖", "😫", "😩", "🥺", "😢", "😭", "😤", "😠", "😡", "🤬", 
                      "🤯", "😳", "🥵", "🥶", "😱", "😨", "😰", "😥", "😓", "🤗", 
                      "🤔", "🤭", "🤫", "🤥", "😶", "😐", "😑", "😬", "🙄", "😯", 
                      "😦", "😧", "😮", "😲", "🥱", "😴", "🤤", "😪", "😵", "🤐", 
                      "🥴", "🤢", "🤮", "🤧", "😷", "🤒", "🤕", "🤑", "🤠", "👍", 
                      "👎", "👊", "✊", "🤛", "🤜", "👏", "🙌", "👐", "🤲", "🤝",
                      "🏃", "💃", "🕺", "🕴️", "👯", "🧶", "🧵", "🧥", "👔", "👕", 
                      "👖", "🧣", "🧤", "🧦", "👗", "👘", "🥻", "🩱", "🩲", "🩳", 
                      "👙", "👚", "👛", "👜", "👝", "🎒", "👞", "👟", "🥾", "🥿", 
                      "👠", "👡", "🩰", "👢", "👑", "👒", "🎩", "🎓", "🧢", "⛑️", 
                      "💄", "💍", "💼", "❤️", "🧡", "💛", "💚", "💙", "💜", "🖤", 
                      "🤍", "🤎", "💔", "❣️", "💕", "💞", "💓", "💗", "💖", "💘", 
                      "💝", "💟",  "☢️", 
                      "☣️", "📴", "📳",  "✴️", "🆚",
                      "💮", "🉐", "㊙️", "㊗️", "🈴",   "🅰️", "🅱️", 
                      "🆎", "🆑", "🅾️", "🆘", "🛑", "⛔", "📛", "🚫"]
            content += " " + random.choice(emojis)
        
        # 对于图文类型，需要处理HTML内容
        if msg_type == "image_text" or "<img" in content:
            from utils.html_processor import HtmlProcessor
            processor = HtmlProcessor()
           # print(content)
            text, image_path = processor.extract_all_images_and_text(content)
           # print(text, image_path )
            return text, image_path, "image_text"
        
        # 对于纯文本，直接返回
        return content, None, msg_type

    async def pause_task(self, task_id: int):
        """暂停任务"""
        async with get_session() as session:
            repo = MessageTaskRepository(session)
            await repo.update_task(task_id, {"status": MessageTaskStatus.PAUSED.value})
            await session.commit()
            self.task_status_changed.emit(str(task_id), MessageTaskStatus.PAUSED.value)

    async def resume_task(self, task_id: int):
        """恢复任务"""
        await self.start_task(task_id)

    async def retry_failed_targets(self, task_id: int):
        """重试失败目标"""
        async with get_session() as session:
            repo = MessageTaskRepository(session)
            failed_targets = await repo.get_target_logs(task_id, status="failed")
            for t in failed_targets:
                await repo.update_target_log_status(task_id, t.target_id, "pending", None)
            await session.commit()
        await self.start_task(task_id)

    async def get_task_progress(self, task_id: int) -> Dict:
        """获取任务进度统计"""
        async with get_session() as session:
            repo = MessageTaskRepository(session)
            total = len(await repo.get_target_logs(task_id))
            success = len(await repo.get_target_logs(task_id, status="success"))
            failed = len(await repo.get_target_logs(task_id, status="failed"))
            pending = len(await repo.get_target_logs(task_id, status="pending"))
            return {
                "total": total,
                "success": success,
                "failed": failed,
                "pending": pending
            }

    async def delete_task(self, task_id: int) -> bool:
        """删除任务及其所有目标日志"""
        async with get_session() as session:
            repo = MessageTaskRepository(session)
            # 先删除所有目标日志
            await repo.delete_target_logs_by_task(task_id)
            # 再删除任务本身
            success = await repo.delete_task(task_id)
            await session.commit()
            if success:
                self.task_deleted.emit(str(task_id))
            return success

    async def delete_target_logs(self, task_id: int) -> int:
        """单独删除某个任务的所有目标日志"""
        async with get_session() as session:
            repo = MessageTaskRepository(session)
            count = await repo.delete_target_logs_by_task(task_id)
            await session.commit()
            return count
        
    async def _pending_batches(self, task_id: int, batch_size: int):
        """异步生成器，分批获取待处理目标"""
        try:
            async with get_session() as session:
                repo = MessageTaskRepository(session)
                while True:
                    task = await repo.get_task_by_id(task_id)
                    if not task or task.status != MessageTaskStatus.RUNNING.value:
                        break
                    targets = await repo.get_target_logs(task_id, status="pending")
                    if not targets:
                        break
                    yield task, targets[:batch_size]
        except GeneratorExit:
            # 正常退出生成器
            pass
        except Exception as e:
            self._logger.error(f"获取待处理目标批次时出错: {e}", exc_info=True)
            raise

    async def start_task(self, task_id: int, batch_size: int = 1000):
        """启动任务，串行执行发送
        每个任务都是独立运行的，可以同时运行多个不同的群发任务。
        每个任务都有自己的：
        - 进度计数
        - 状态管理
        - 账户使用记录
        - 目标处理队列
        Args:
            task_id: 任务ID，用于区分不同的群发任务
            batch_size: 每批处理的目标数量
        Returns:
            Tuple[bool, str]: (成功标志, 错误消息)，当成功时错误消息为空字符串
        """
        self._logger.info(f"启动群发任务 [Task ID: {task_id}]")
        # 初始化任务状态
        async with get_session() as session:
            repo = MessageTaskRepository(session)
            task = await repo.get_task_by_id(task_id)
            if not task:
                error_msg = f"未找到任务 [Task ID: {task_id}]"
                self._logger.warning(error_msg)
                return False, error_msg
            if task.status == MessageTaskStatus.RUNNING.value:
                error_msg = f"任务已在运行 [Task ID: {task_id}]"
                self._logger.info(error_msg)
                return False, error_msg
            accounts = task.accounts or []
            if not accounts:
                error_msg = f"任务没有配置可用账户 [Task ID: {task_id}]"
                self._logger.warning(error_msg)
                return False, error_msg
            targets = await repo.get_target_logs(task_id, status="pending")
            if not targets:
                error_msg = f"任务没有待处理的目标 [Task ID: {task_id}]"
                self._logger.warning(error_msg)
                return False, error_msg
            limit_service = AccountLimitService(session)
            valid_account = False
            for account_data in accounts:
                account = account_data['phone'] if isinstance(account_data, dict) and 'phone' in account_data else account_data
                can_send, _, _ = await limit_service.check_can_send(account)
                if can_send:
                    valid_account = True
                    break
            if not valid_account:
                error_msg = f"任务配置的账户当日发送限额已满 [Task ID: {task_id}]"
                self._logger.warning(error_msg)
                return False, error_msg
            # 更新任务状态为运行中
            await repo.update_task(task_id, {"status": MessageTaskStatus.RUNNING.value})
            await session.commit()
            self.task_status_changed.emit(str(task_id), MessageTaskStatus.RUNNING.value)
            # 读取历史进度
            # 兼容老数据，若无则为0
            db_success = getattr(task, "success_count", None)
            db_failed = getattr(task, "failed_count", None)
            db_total = getattr(task, "total_count", None)
            if db_success is None or db_failed is None or db_total is None:
                # 兼容旧表结构
                stats = await repo.get_task_target_stats(task_id)
                db_success = stats.get("success", 0)
                db_failed = stats.get("failed", 0)
                db_total = stats.get("total", 0)
        # 用数据库进度初始化
        total = db_total
        success = db_success
        failed = db_failed
        last_account = None
        try:
            async for task, targets in self._pending_batches(task_id, batch_size):
                self._logger.debug(f"处理任务批次 [Task ID: {task_id}], 目标数量: {len(targets)}")
                accounts = task.accounts or []
                if not accounts:
                    self._logger.warning(f"任务没有可用账户 [Task ID: {task_id}]")
                    async with get_session() as session:
                        repo = MessageTaskRepository(session)
                        await repo.update_task(task_id, {"status": MessageTaskStatus.PAUSED.value})
                        await session.commit()
                        self.task_status_changed.emit(str(task_id), MessageTaskStatus.PAUSED.value)
                    return False, "任务没有配置可用账户"
                for i, target in enumerate(targets):
                    is_last = i == len(targets) - 1
                    self._logger.debug(f"处理目标 [Task ID: {task_id}] [{i+1}/{len(targets)}] Target: {target.target_id}")
                    account_count = len(accounts)
                    found_account = False
                    selected_account = None
                    async with get_session() as session:
                        limit_service = AccountLimitService(session)
                        for j in range(account_count):
                            idx = (total + j) % account_count
                            account = accounts[idx]['phone'] if isinstance(accounts[idx], dict) and 'phone' in accounts[idx] else accounts[idx]
                            can_send, msg, _ = await limit_service.check_can_send(account)
                            if can_send:
                                found_account = True
                                selected_account = account
                                self._logger.debug(f"选择账户 [Task ID: {task_id}] Account: {account}")
                                break
                    if not found_account:
                        error_msg = f"所有账户当日限额已满，任务自动暂停 [Task ID: {task_id}]"
                        self._logger.warning(error_msg)
                        async with get_session() as session:
                            repo = MessageTaskRepository(session)
                            await repo.update_task(task_id, {"status": MessageTaskStatus.PAUSED.value})
                            await session.commit()
                        self.task_status_changed.emit(str(task_id), MessageTaskStatus.PAUSED.value)
                        return False, error_msg
                    if last_account is not None and selected_account != last_account:
                        account_switch_min = getattr(task, 'account_switch_min', 10)
                        account_switch_max = getattr(task, 'account_switch_max', 30)
                        delay_time = random.uniform(account_switch_min, account_switch_max)
                        self._logger.info(f"账户切换延时 [Task ID: {task_id}] {last_account} -> {selected_account}, 延时: {delay_time}秒")
                        await asyncio.sleep(delay_time)
                    last_account = selected_account
                    # 选择消息内容
                    message, image_path, msg_type = self._choose_message(task)
                    #print(message, image_path, msg_type )
                    # 根据消息类型选择发送方法
                    self._logger.debug(
                        f"发送消息 [Task ID: {task_id}] Account: {selected_account} -> Target: {target.target_id}, Type: {msg_type}")
                    
                    if msg_type == "image_text" and image_path:
                        # 发送图文消息
                        send_task_id = self._client_worker.send_image_text_message(selected_account, target.target_id,
                                                                                   image_path, message, 'html')
                    elif msg_type == "image" and image_path:
                        # 发送图片消息
                        send_task_id = self._client_worker.send_file_message(selected_account, target.target_id,
                                                                             image_path, message)
                    else:
                        # 默认发送文本消息
                        send_task_id = self._client_worker.send_text_message(selected_account, target.target_id,
                                                                             message, 'html')
                        
                    ok, result = await self._client_worker.get_task_result(send_task_id, timeout=30)
                    async with get_session() as session:
                        repo = MessageTaskRepository(session)
                        limit_service = AccountLimitService(session)
                        if ok:
                            await repo.update_target_log_status(task_id, target.target_id, "success")
                            await limit_service.increment_send_count(selected_account)
                            success += 1
                            self._logger.debug(f"发送成功 [Task ID: {task_id}] Target: {target.target_id}")
                        else:
                            await repo.update_target_log_status(task_id, target.target_id, "failed", str(result))
                            failed += 1
                            self._logger.warning(f"发送失败 [Task ID: {task_id}] Target: {target.target_id}, Error: {result}")
                        total += 1
                        await session.commit()
                    self.task_progress_updated.emit(str(task_id), total, success, failed)
                    if not is_last:
                        interval_min = getattr(task, 'message_interval_min', 5)
                        interval_max = getattr(task, 'message_interval_max', 10)
                        interval_time = random.uniform(interval_min, interval_max)
                        self._logger.debug(f"消息间隔延时 [Task ID: {task_id}] {interval_time}秒")
                        await asyncio.sleep(interval_time)
        except Exception as e:
            error_msg = f"任务执行出错 [Task ID: {task_id}]: {e}"
            self._logger.error(error_msg, exc_info=True)
            async with get_session() as session:
                repo = MessageTaskRepository(session)
                await repo.update_task(task_id, {"status": MessageTaskStatus.FAILED.value})
                await session.commit()
                self.task_status_changed.emit(str(task_id), MessageTaskStatus.FAILED.value)
            return False, error_msg
        async with get_session() as session:
            repo = MessageTaskRepository(session)
            pending_count = await session.scalar(
                select(func.count()).where(
                    MessageTaskTargetLog.task_id == task_id,
                    MessageTaskTargetLog.status == "pending"
                )
            )
            if pending_count == 0:
                self._logger.info(f"任务完成 [Task ID: {task_id}] 总数: {total}, 成功: {success}, 失败: {failed}")
                await repo.update_task(task_id, {"status": MessageTaskStatus.COMPLETED.value})
                await session.commit()
                self.task_status_changed.emit(str(task_id), MessageTaskStatus.COMPLETED.value)
            else:
                self._logger.info(f"任务暂停 [Task ID: {task_id}] 总数: {total}, 成功: {success}, 失败: {failed}, 待处理: {pending_count}")
                await repo.update_task(task_id, {"status": MessageTaskStatus.PAUSED.value})
                await session.commit()
                self.task_status_changed.emit(str(task_id), MessageTaskStatus.PAUSED.value)
        return True, ""

    async def get_all_tasks(self, page: int = 1, per_page: int = 50) -> list:
        """获取所有消息任务列表"""
        async with get_session() as session:
            repo = MessageTaskRepository(session)
            tasks = await repo.get_all_tasks(page, per_page)
            return [task.to_dict() for task in tasks]

    async def get_task_by_id(self, task_id: int) -> dict:
        """根据任务ID获取任务详情"""
        async with get_session() as session:
            repo = MessageTaskRepository(session)
            task = await repo.get_task_by_id(task_id)
            return task.to_dict() if task else None

    async def update_task_status(self, task_id: str, status: str) -> bool:
        """更新任务状态
        Args:
            task_id: 任务ID
            status: 新状态
        Returns:
            操作是否成功
        """
        async with get_session() as session:
            repo = MessageTaskRepository(session)
            await repo.update_task(int(task_id), {"status": status})
            await session.commit()
            self.task_status_changed.emit(str(task_id), status)
            return True

    async def send_message(account, target, message_data, task, client_worker):
        """
        account: 账户
        target: 目标
        message_data: dict, 包含 type, content, 可能还有 image_path 等
        task: 当前任务对象
        client_worker: 发送消息的worker
        """
        msg_type = message_data.get("type", "text")
        content = message_data.get("content", "")
        # 随机表情
        if getattr(task, "random_emoji", False) and msg_type == "text":
            emojis = ["😀", "😃", "😄", "😁", "😆", "😅", "😂", "🤣", "😊", "😇", 
                      "🙂", "🙃", "😉", "😌", "😍", "🥰", "😘", "😗", "😙", "😚", 
                      "😋", "😛", "😝", "😜", "🤪", "🤨", "🧐", "🤓", "😎", "🤩", 
                      "🥳", "😏", "😒", "😞", "😔", "😟", "😕", "🙁", "☹️", "😣", 
                      "😖", "😫", "😩", "🥺", "😢", "😭", "😤", "😠", "😡", "🤬", 
                      "🤯", "😳", "🥵", "🥶", "😱", "😨", "😰", "😥", "😓", "🤗", 
                      "🤔", "🤭", "🤫", "🤥", "😶", "😐", "😑", "😬", "🙄", "😯", 
                      "😦", "😧", "😮", "😲", "🥱", "😴", "🤤", "😪", "😵", "🤐", 
                      "🥴", "🤢", "🤮", "🤧", "😷", "🤒", "🤕", "🤑", "🤠", "👍", 
                      "👎", "👊", "✊", "🤛", "🤜", "👏", "🙌", "👐", "🤲", "🤝", 
                      "🙏", "✌️", "🤟", "🤘", "👌", "🤌", "🤏", "👈", "👉", "👆", 
                      "👇", "☝️", "✋", "🤚", "🖐️", "🖖", "👋", "🤙", "💪", "🦾", 
                      "🖕", "✍️", "🙇", "🤦", "🤷", "💅", "🦻", "👂", "👃", "👶", 
                      "🧒", "👦", "👧", "🧑", "👱", "👨", "🧔", "👩", "🧓", "👴", 
                      "👵", "🙍", "🙎", "🙅", "🙆", "💁", "🙋", "🧏", "🙇", "🤦", 
                      "🤷", "💆", "💇", "🧖", "🛀", "🛌", "🧘", "🧑‍🦯", "🧑‍🦼", "🧑‍🦽", 
                      "🏃", "💃", "🕺", "🕴️", "👯", "🧶", "🧵", "🧥", "👔", "👕", 
                      "👖", "🧣", "🧤", "🧦", "👗", "👘", "🥻", "🩱", "🩲", "🩳", 
                      "👙", "👚", "👛", "👜", "👝", "🎒", "👞", "👟", "🥾", "🥿", 
                      "👠", "👡", "🩰", "👢", "👑", "👒", "🎩", "🎓", "🧢", "⛑️", 
                      "💄", "💍", "💼", "❤️", "🧡", "💛", "💚", "💙", "💜", "🖤", 
                      "🤍", "🤎", "💔", "❣️", "💕", "💞", "💓", "💗", "💖", "💘", 
                      "💝", "💟", "☮️", "✝️", "☪️", "🕉️", "☸️", "✡️", "🔯", "🕎", 
                      "☯️", "☦️", "🛐", "⛎", "♈", "♉", "♊", "♋", "♌", "♍", 
                      "♎", "♏", "♐", "♑", "♒", "♓", "🆔", "⚛️", "🉑", "☢️", 
                      "☣️", "📴", "📳", "🈶", "🈚", "🈸", "🈺", "🈷️", "✴️", "🆚", 
                      "💮", "🉐", "㊙️", "㊗️", "🈴", "🈵", "🈹", "🈲", "🅰️", "🅱️", 
                      "🆎", "🆑", "🅾️", "🆘", "❌", "⭕", "🛑", "⛔", "📛", "🚫"]
            content += " " + random.choice(emojis)
        if msg_type == "text":
            send_task_id = client_worker.send_text_message(account, target, content)
        elif msg_type == "image":
            image_path = message_data.get("image_path")
            send_task_id = client_worker.send_image_message(account, target, image_path, caption=content)
        elif msg_type == "image_text":
            image_path = message_data.get("image_path")
            send_task_id = client_worker.send_image_text_message(account, target, image_path, content)
        else:
            # 默认文本
            send_task_id = client_worker.send_text_message(account, target, content)
        return send_task_id

    async def get_monitor_tasks(self) -> list:
        """
        获取所有监听任务及其用户数量
        Returns:
            List[Dict]: [{id, name, user_count, ...}]
        """
        from data.repositories.monitor_repo import MonitorTaskRepository
        async with get_session() as session:
            repo = MonitorTaskRepository(session)
            return await repo.get_all_tasks_with_user_count()

    async def get_all_tasks_with_stats(self) -> list:
        async with get_session() as session:
            repo = MessageTaskRepository(session)
            tasks = await repo.get_all_tasks()
            result = []
            for task in tasks:
                stats = await repo.get_task_target_stats(task.id)
                task_dict = task.to_dict()
                task_dict.update({
                    "success_count": stats["success"],
                    "failed_count": stats["failed"],
                    "total_count": stats["total"],
                    "today_success": stats.get("today_success", 0)
                })
                result.append(task_dict)
            return result

    async def get_global_stats(self) -> dict:
        """统计全局任务数据：总任务数、运行中、总发送、总失败、已暂停、今日发送"""
        async with get_session() as session:
            repo = MessageTaskRepository(session)
            tasks = await repo.get_all_tasks()
            total = len(tasks)
            running = sum(1 for t in tasks if t.status == MessageTaskStatus.RUNNING.value)
            paused = sum(1 for t in tasks if t.status == MessageTaskStatus.PAUSED.value)
            total_sent = 0
            total_failed = 0
            today_sent = 0
            from datetime import datetime
            today = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
            for task in tasks:
                stats = await repo.get_task_target_stats(task.id)
                total_sent += stats["success"]
                total_failed += stats["failed"]
                today_sent += stats.get("today_success", 0)
            return {
                "total": total,
                "running": running,
                "paused": paused,
                "completed": total_sent,
                "failed": total_failed,
                "today_sent": today_sent
            }
