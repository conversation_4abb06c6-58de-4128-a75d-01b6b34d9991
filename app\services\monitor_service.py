"""
监控任务服务模块
实现监控任务的业务逻辑，提供任务创建、删除、查询等功能
"""
import os
import json
import datetime
import asyncio
from typing import Dict, Any, List, Optional, Tuple, Union
from PySide6.QtCore import QObject, Signal
from telethon import events
from sqlalchemy import select, func, or_, and_

from core.telegram.client_worker import TelegramClientWorker
from utils.logger import get_logger
from data.database import get_session
from data.repositories.monitor_repo import MonitorTaskRepository
from data.repositories.account_repo import AccountRepository
from data.models.monitor import MonitorTask, MonitorMessage
from core.monitor.filter import MessageFilter
from core.monitor.extractor import KeywordExtractor
from core.monitor.notifier import NotificationManager
from core.telegram.message_manager import MessageManager


class MonitorTaskService(QObject):
    """
    监控任务服务类
    负责处理监控任务的业务逻辑
    """
    ListenLog = Signal(str)
    
    def __init__(self, telegram_worker: TelegramClientWorker = None):
        """初始化监控任务服务"""
        super().__init__()  # 调用QObject的初始化方法，确保信号能正常工作
        self._logger = get_logger("app.services.monitor_service")
        self._logger.info("初始化监控任务服务")
        
        # 初始化依赖的组件
        self._telegram_worker = telegram_worker
        self._message_filter = MessageFilter()
        self._keyword_extractor = KeywordExtractor()
        client_manager = telegram_worker.get_client_manager() if telegram_worker else None
        self._message_manager = MessageManager(client_manager)
        self._notification_manager = NotificationManager(message_manager=self._message_manager)
   
        
        # 记录运行中的任务 {task_id: {"accounts": {"phone1": {"client_identifier": str, "handler_tuples": list}, "phone2": {...}}}}
        self._running_tasks: Dict[str, Dict[str, Any]] = {} 
        
    async def get_all_tasks(self, search_term: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        获取所有监控任务，可选根据名称进行搜索。
        包含运行状态和基础统计。
        
        Returns:
            List[Dict[str, Any]]: 任务列表
        """
        try:
            async with get_session() as session:
                repo = MonitorTaskRepository(session=session)
                db_tasks = await repo.get_all_tasks(search_term=search_term)
                
                tasks = []
                for task_model in db_tasks:
                    task_dict = {
                        "id": str(task_model.id),
                        "name": task_model.name,
                        "description": task_model.description,
                        "is_active": task_model.is_active,
                        # is_running from DB might be a persisted state, 
                        # but actual runtime state is in self._running_tasks
                        "is_running": str(task_model.id) in self._running_tasks, 
                        "status_text": "运行中" if str(task_model.id) in self._running_tasks else ("已停止" if task_model.is_active else "已禁用"),
                        "created_at": task_model.created_at.strftime("%Y-%m-%d %H:%M:%S") if task_model.created_at else "",
                        "updated_at": task_model.updated_at.strftime("%Y-%m-%d %H:%M:%S") if task_model.updated_at else "",
                        # Add basic stats if available, e.g., match count
                        # "match_count": await repo.get_task_match_count(str(task_model.id)) 
                    }
                    tasks.append(task_dict)
                return tasks
        except Exception as e:
            self._logger.error(f"获取所有任务失败: {str(e)}", exc_info=True)
            return []
            
    async def _get_all_tasks_async(self) -> List[Dict[str, Any]]:
        """
        异步获取所有监控任务 (兼容旧名, 但推荐使用 get_all_tasks)
        
        Returns:
            List[Dict[str, Any]]: 任务列表
        """
        return await self.get_all_tasks()
            
    async def get_task_by_id(self, task_id: str) -> Optional[Dict[str, Any]]:
        """
        异步根据ID获取任务信息
        
        Args:
            task_id: 任务ID
            
        Returns:
            Optional[Dict[str, Any]]: 任务信息，未找到返回None
        """
        try:
            async with get_session() as session:
                repo = MonitorTaskRepository(session=session)
                task = await repo.get_task_by_id(task_id)
                
                if not task:
                    self._logger.warning(f"任务未找到: ID {task_id}")
                    return None
                    
                task_dict = {
                    "id": str(task.id),
                    "name": task.name,
                    "description": task.description,
                    "keywords": json.loads(task.keywords) if task.keywords and task.keywords != 'null' else [],
                    "ignore_keywords": json.loads(task.ignore_keywords) if task.ignore_keywords and task.ignore_keywords != 'null' else [],
                    "ignore_nicknames_rules": json.loads(task.ignore_nicknames_rules) if hasattr(task, 'ignore_nicknames_rules') and task.ignore_nicknames_rules and task.ignore_nicknames_rules != 'null' else [],
                    "monitor_new_users": task.monitor_new_users,
                    "monitor_messages": task.monitor_messages,
                    "is_active": task.is_active,
                    "is_running": str(task.id) in self._running_tasks, # Actual runtime status
                    "last_error": task.last_error if hasattr(task, 'last_error') else None,
                    "created_at": task.created_at.strftime("%Y-%m-%d %H:%M:%S") if task.created_at else "",
                    "updated_at": task.updated_at.strftime("%Y-%m-%d %H:%M:%S") if task.updated_at else ""
                }
                # Also fetch monitored chats for this task
                monitored_chats_models = await repo.get_monitored_chats_for_task(task_id)
                task_dict["monitored_chats"] = [
                    {"id": mc.id, "chat_id": mc.chat_id, "chat_title": mc.chat_title, "account_phone": mc.account_phone} 
                    for mc in monitored_chats_models
                ]
                
                return task_dict
        except Exception as e:
            self._logger.error(f"异步获取任务失败: ID {task_id}, 错误: {str(e)}", exc_info=True)
            return None
            
    async def create_and_start_task(self, task_data: Dict[str, Any]) -> Tuple[bool, Union[Dict[str, Any], str]]:
        """
        创建新的监控任务并尝试启动它。
        task_data example:
        {
            'name': "My Bitcoin Monitor",
            'description': "Monitors BTC news",
            'monitored_chats': [{"chat_id": "-************", "chat_title": "BTC News Group", "account_phone": "+**********"}, ...],
            'keywords': ["bitcoin", "btc"],
            'ignore_keywords': ["scam", "spam"],
            'ignore_nicknames_rules': ["bot", "promo"],
            'monitor_new_users': False,
            'monitor_messages': True,
            'notifications': [ { 'target_type': 'private_chat', 'target_address': 'my_user_id', ...} ]
        }
        
        Returns:
            Tuple[bool, Union[Dict[str, Any], str]]: (成功标志, 成功返回完整任务信息字典，失败返回错误消息字符串)
        """
        self._logger.info(f"服务层: 创建并启动任务，数据名: {task_data.get('name')}")
        
        task_name = task_data.get("name")
        monitored_chats_data = task_data.get("monitored_chats", [])

        if not task_name: return False, "任务名称不能为空。"
        if not monitored_chats_data: return False, "必须指定至少一个监控群组/频道。"
        
        # 检查每个monitored_chat是否都有account_phone
        for chat in monitored_chats_data:
            if not chat.get("account_phone"):
                return False, "每个监控群组/频道必须指定对应的账户。"

        session = None
        new_task_model = None
        try:
            async with get_session() as session:
                repo = MonitorTaskRepository(session=session)
                
                task_payload = {
                    "name": task_name,
                    "description": task_data.get("description", ""),
                    "keywords": json.dumps(task_data.get("keywords", [])),
                    "ignore_keywords": json.dumps(task_data.get("ignore_keywords", [])),
                    "ignore_nicknames_rules": json.dumps(task_data.get("ignore_nicknames_rules", [])),
                    "monitor_new_users": task_data.get("monitor_new_users", False),
                    "monitor_messages": task_data.get("monitor_messages", True),
                    "is_active": task_data.get("is_active", True),
                    "is_running": False, # Initially false, will be set by start_task_monitoring
                    "last_error": None,
                    # Add block_enabled and block_scope if they are part of task_data and your DB model
                    "block_enabled": task_data.get("block_enabled", False),
                    "block_scope": task_data.get("block_scope", None) if task_data.get("block_enabled", False) else None,
                }
                
                new_task_model = await repo.create_task_returning_model(task_payload)
                if not new_task_model or not new_task_model.id:
                    await session.rollback() # Rollback if model creation failed
                    return False, "数据库创建任务失败。"
                
                task_id_str = str(new_task_model.id)

                for chat_info in monitored_chats_data:
                    await repo.add_monitored_chat(
                        task_id_str, 
                        chat_info["chat_id"], 
                        chat_info.get("chat_title", "未命名"),
                        chat_info.get("account_phone", "")  # 添加账户电话到monitored_chat表
                    )
                
                # Example for notifications if you have them
                # for notif_conf in task_data.get("notifications", []):
                #    await repo.create_notification_config(task_id_str, notif_conf)

                await session.commit()
                await session.refresh(new_task_model) # Refresh to get all DB-generated fields

            # Construct task_config for starting, using data from new_task_model and task_data
            task_config_for_start = {
                "id": task_id_str,
                "name": new_task_model.name,
                "keywords": json.loads(new_task_model.keywords) if new_task_model.keywords else [],
                "ignore_keywords": json.loads(new_task_model.ignore_keywords) if new_task_model.ignore_keywords else [],
                "ignore_nicknames_rules": json.loads(new_task_model.ignore_nicknames_rules) if new_task_model.ignore_nicknames_rules else [],
                "monitored_chats": monitored_chats_data, # Use the input data that has account_phone
                "is_active": new_task_model.is_active,
                "notifications": task_data.get("notifications", []), # Pass along for processing message
                 # Add other fields needed by process_monitored_message
            }
            
            start_success, start_message = await self.start_task_monitoring(task_id_str, task_config_for_start)
            
            final_task_dict = await self.get_task_by_id(task_id_str) # Fetches full state including is_running

            if start_success:
                self._logger.info(f"服务层: 任务 {task_id_str} 创建并启动成功。")
                return True, final_task_dict 
            else:
                self._logger.warning(f"服务层: 任务 {task_id_str} 创建成功但启动失败: {start_message}")
                # final_task_dict will reflect is_running=False and last_error
                return False, f"任务成功保存到数据库，但启动监控失败: {start_message}. 任务信息: {final_task_dict if final_task_dict else '无法获取'}"

        except Exception as e:
            if session and new_task_model is None and session.is_active : # Rollback only if task creation failed before commit attempt
                await session.rollback()
            self._logger.error(f"服务层: 创建并启动任务时发生严重错误: {str(e)}", exc_info=True)
            return False, f"服务内部错误: {str(e)}"

    async def update_task_config(self, task_id: str, new_task_data: Dict[str, Any]) -> Tuple[bool, Union[Dict[str, Any], str]]:
        """
        更新现有任务的配置。如果任务正在运行，它将被停止，使用新配置更新，然后重新启动。
        new_task_data structure is similar to the one used for creating tasks,
        containing all fields that can be updated.
        """
        self._logger.info(f"服务层: 更新任务配置 ID {task_id}, 新数据名: {new_task_data.get('name')}")
        
        original_task_config = await self.get_task_by_id(task_id)
        if not original_task_config:
            return False, f"任务 ID {task_id} 未找到。"

        was_running = original_task_config.get("is_running", False)
        
        if was_running:
            self._logger.info(f"服务层: 任务 {task_id} 正在运行，将先停止。")
            stop_success, stop_msg = await self.stop_task(task_id) # This should set is_running=False in DB
            if not stop_success:
                self._logger.error(f"服务层: 停止任务 {task_id} 失败: {stop_msg}。配置更新仍将继续。")
                # Decide if you want to abort or continue. For now, continue.

        db_session = None
        try:
            async with get_session() as db_session:
                repo = MonitorTaskRepository(session=db_session)
                
                task_payload_for_db = {
                    "name": new_task_data.get("name", original_task_config.get("name")),
                    "description": new_task_data.get("description", original_task_config.get("description")),
                    "keywords": json.dumps(new_task_data.get("keywords", [])),
                    "ignore_keywords": json.dumps(new_task_data.get("ignore_keywords", [])),
                    "ignore_nicknames_rules": json.dumps(new_task_data.get("ignore_nicknames_rules", [])),
                    "monitor_new_users": new_task_data.get("monitor_new_users", original_task_config.get("monitor_new_users", False)),
                    "monitor_messages": new_task_data.get("monitor_messages", original_task_config.get("monitor_messages", True)),
                    "is_active": new_task_data.get("is_active", original_task_config.get("is_active", True)),
                    "block_enabled": new_task_data.get("block_enabled", False),
                    "block_scope": new_task_data.get("block_scope", None) if new_task_data.get("block_enabled", False) else None,
                    # is_running is managed by start/stop
                }
                await repo.update_task_fields(task_id, task_payload_for_db)

                new_monitored_chats_data = new_task_data.get("monitored_chats", [])
                await repo.delete_monitored_chats_for_task(task_id)
                for chat_info in new_monitored_chats_data:
                    await repo.add_monitored_chat(
                        task_id, 
                        chat_info["chat_id"], 
                        chat_info.get("chat_title", "未命名"),
                        chat_info.get("account_phone", "")  # 确保添加account_phone
                    )
                
                # Update notification settings if they are part of new_task_data
                # Example:
                # if "notifications" in new_task_data:
                #     await repo.delete_notification_configs_by_task(task_id)
                #     for notif_conf in new_task_data.get("notifications", []):
                #        await repo.create_notification_config(task_id, notif_conf_dict)


                await db_session.commit()
            self._logger.info(f"服务层: 任务 {task_id} 数据库配置已更新。")
        except Exception as e:
            if db_session and db_session.is_active:
                await db_session.rollback()
            self._logger.error(f"服务层: 更新任务 {task_id} 数据库配置时出错: {e}", exc_info=True)
            return False, f"数据库更新失败: {e}"

        if was_running:
            self._logger.info(f"服务层: 任务 {task_id} 原本在运行，将用新配置重新启动。")
            # The start_task_monitoring_by_id will fetch the new config internally.
            start_success, start_message = await self.start_task_monitoring_by_id(task_id)
            if not start_success:
                final_task_state_after_fail = await self.get_task_by_id(task_id)
                return False, f"配置更新成功，但重新启动任务失败: {start_message}. 任务当前状态: {final_task_state_after_fail if final_task_state_after_fail else '无法获取'}"
        
        final_task_state = await self.get_task_by_id(task_id)
        if not final_task_state:
             return False, f"任务 {task_id} 更新后无法从数据库重新获取。"
             
        self._logger.info(f"服务层: 任务 {task_id} 更新成功。运行状态: {final_task_state.get('is_running')}")
        return True, final_task_state

    async def _create_task_async(self, task_data: Dict[str, Any]) -> Union[int, str]:
        """
        旧版异步创建新的监控任务 - 将被 create_and_start_task 替代
        """
        self._logger.warning("_create_task_async is deprecated, use create_and_start_task")
        # This logic is now part of create_and_start_task
        raise NotImplementedError("_create_task_async is deprecated")

    async def start_task_monitoring_by_id(self, task_id: str) -> Tuple[bool, str]:
        """通过ID启动任务监控（服务层API，供控制器调用）
        
        Args:
            task_id: 任务ID
            
        Returns:
            Tuple[bool, str]: (成功标志, 消息)
        """
        self._logger.info(f"服务层: 启动任务监控 ID: {task_id}")
        if not self._telegram_worker:
            return False, "TelegramClientWorker 未初始化"
            
        # 通过worker线程提交任务
        worker_task_id = self._telegram_worker._add_task(self._start_task_monitoring_internal, task_id)
        try:
            # 等待结果
            success, result = await self._telegram_worker.get_task_result(worker_task_id, timeout=30)
            return success, result
        except Exception as e:
            error_msg = f"启动任务监控失败: {str(e)}"
            self._logger.error(error_msg, exc_info=True)
            return False, error_msg
            
    async def _start_task_monitoring_internal(self, task_id: str) -> Tuple[bool, str]:
        """内部方法，在worker线程中执行实际的任务启动逻辑
        
        Args:
            task_id: 任务ID
            
        Returns:
            Tuple[bool, str]: (成功标志, 消息)
        """
        # 检查任务是否已在运行
        if task_id in self._running_tasks:
            return True, f"任务 {task_id} 已在运行中。"
            
        # 获取任务配置
        task_config = await self.get_task_by_id(task_id)
        if not task_config:
            return False, f"任务 {task_id} 未找到或无法加载配置。"
        
        if not task_config.get("is_active", False):
            return False, f"任务 {task_id} 当前未激活，无法启动。"
            
        # 调用实际的监控启动逻辑
        return await self.start_task_monitoring(task_id, task_config)
        
    async def stop_task(self, task_id: str, called_internally: bool = False) -> Tuple[bool, str]:
        """
        停止监控任务 (public method called by controller)
        
        Args:
            task_id: 任务ID
            called_internally: 是否内部调用
            
        Returns:
            Tuple[bool, str]: (成功标志, 消息)
        """
        self._logger.info(f"服务层: 请求停止任务 {task_id}")
        
        # 检查任务是否存在于运行时缓存中
        if task_id not in self._running_tasks:
            async with get_session() as session_db_check:
                repo_db_check = MonitorTaskRepository(session=session_db_check)
                db_task = await repo_db_check.get_task_by_id(task_id)
                if db_task and db_task.is_running:
                    self._logger.warning(f"任务 {task_id} 不在运行时缓存，但在DB中为运行状态。将其标记为停止。")
                    await repo_db_check.update_task_fields(task_id, {"is_running": False, "last_error": "已停止 (运行时未找到)"})
                    await session_db_check.commit()
            return True, "任务已标记为停止 (之前未在运行时列表中)。"
            
        try:
            if not self._telegram_worker:
                return False, "TelegramClientWorker 未初始化"
                
            # 通过worker线程提交任务
            worker_task_id = self._telegram_worker._add_task(self.stop_task_monitoring, task_id)
            try:
                # 等待结果
                success, result = await self._telegram_worker.get_task_result(worker_task_id, timeout=30)
                return success, result
            except Exception as e:
                error_msg = f"停止任务监控失败: {str(e)}"
                self._logger.error(error_msg, exc_info=True)
                return False, error_msg
        except Exception as e:
            error_msg = f"停止任务 {task_id} 时发生异常: {str(e)}"
            self._logger.error(error_msg, exc_info=True)
            await self._update_task_error_status(task_id, f"停止操作失败: {str(e)}")
            return False, error_msg

    async def _stop_task_monitoring_internal(self, task_id: str) -> Tuple[bool, str]:
        """内部方法，在worker线程中执行实际的任务停止逻辑
        
        Args:
            task_id: 任务ID
            
        Returns:
            Tuple[bool, str]: (成功标志, 消息)
        """
        return await self.stop_task_monitoring(task_id)
        
    async def start_task_monitoring(self, task_id: str, task_config: Optional[Dict[str, Any]] = None) -> Tuple[bool, str]:
        """
        启动任务监控，注册消息事件处理器（内部使用，确保在worker线程中调用）
        
        Args:
            task_id: 任务ID
            task_config: 任务配置，可选
            
        Returns:
            Tuple[bool, str]: (成功标志, 消息)
        """
        self._logger.info(f"内部方法: 启动任务监控详细执行: {task_id}")
        self.ListenLog.emit(f"开始启动监控任务 {task_id}")
        
        # 获取任务配置（如果未提供）
        if not task_config:
            task_config = await self.get_task_by_id(task_id)
            if not task_config:
                self.ListenLog.emit(f"任务 {task_id} 未找到或无法加载配置")
                return False, f"任务 {task_id} 未找到或无法加载配置。"
                
        monitored_chats = task_config.get("monitored_chats", [])
        if not monitored_chats:
            msg = f"任务 {task_id} 没有配置任何监控群组。"
            await self._update_task_error_status(task_id, msg)
            return False, msg

        # 按账户分组
        accounts_chats = {}
        for chat_info in monitored_chats:
            account_phone = chat_info.get("account_phone")
            if not account_phone:
                continue
            accounts_chats.setdefault(account_phone, []).append(chat_info)

        if not accounts_chats:
            msg = f"任务 {task_id} 没有有效的账户-群组配置。"
            await self._update_task_error_status(task_id, msg)
            return False, msg

        # 初始化任务运行状态结构
        if task_id not in self._running_tasks:
            self._running_tasks[task_id] = {
                "accounts": {},
                "task_config_snapshot": task_config.copy()
            }

        # 定义消息事件回调函数（同步，仅参数转发到worker线程）
        def new_message_event_callback(event):
            # 通过worker线程调度
            worker_task_id = self._telegram_worker._add_task(
                self.process_monitored_message, task_id, task_config.copy(), event
            )
            # 不需要等待结果，但需要返回一个可等待的对象（Future）
            # 这样Telethon可以继续处理其他事件
            f = asyncio.Future()
            f.set_result(None)  # 立即完成Future
            return f  # 返回Future对象而不是任务ID

        # 注册事件处理器
        success_count = 0
        fail_count = 0
        error_messages = []
        
        for account_phone, chats in accounts_chats.items():
            chat_ids = []
            for chat in chats:
                try:
                    chat_ids.append(int(chat["chat_id"]))
                except Exception:
                    self._logger.error(f"任务 {task_id} 的监控群组ID无效: {chat.get('chat_id')}")
            if not chat_ids:
                self._logger.warning(f"账户 {account_phone} 没有有效的监控群组ID")
                continue
                
            # 直接调用telegram_worker的方法
            try:
                # 修改：获取任务ID，然后等待结果
                worker_task_id = self._telegram_worker.register_monitoring_handlers(
                    task_id, account_phone, chat_ids, new_message_event_callback
                )
                # 等待任务完成并获取结果
                success, msg = await self._telegram_worker.get_task_result(worker_task_id)
                if success:
                    success_count += 1
                    self._logger.info(f"账户 {account_phone} 的监控已设置，监听群组: {chat_ids}")
                    
                    # 保存处理器信息到运行时缓存
                    if task_id not in self._running_tasks:
                        self._running_tasks[task_id] = {
                            "accounts": {},
                            "task_config_snapshot": task_config.copy()
                        }
                    
                    # 确保账户信息存在
                    if account_phone not in self._running_tasks[task_id]["accounts"]:
                        self._running_tasks[task_id]["accounts"][account_phone] = {
                            "chat_ids": chat_ids,
                            "handler_tuples": []  # 这里应该存储处理器元组，但我们无法直接获取
                        }
                    else:
                        self._running_tasks[task_id]["accounts"][account_phone]["chat_ids"] = chat_ids
                else:
                    error_messages.append(msg)
                    fail_count += 1
                    self._logger.warning(f"账户 {account_phone} 注册事件失败: {msg}")
            except Exception as e:
                error_msg = f"注册事件处理器失败: {str(e)}"
                self._logger.error(error_msg, exc_info=True)
                error_messages.append(error_msg)
                fail_count += 1

        # 更新任务状态
        if success_count > 0:
            async with get_session() as session_db_update:
                repo_db_update = MonitorTaskRepository(session=session_db_update)
                last_error = None if fail_count == 0 else f"部分账户监控设置失败: {', '.join(error_messages)}"
                await repo_db_update.update_task_fields(task_id, {"is_running": True, "last_error": last_error})
                await session_db_update.commit()
            result_message = f"任务 '{task_config.get('name')}' 已启动，成功设置 {success_count} 个账户的监控"
            if fail_count > 0:
                result_message += f"，{fail_count} 个账户设置失败"
            self._logger.info(f"任务 {task_id} 监控启动结果: {result_message}")
            self.ListenLog.emit(f"监控任务已启动: {result_message}")
            return True, result_message
        else:
            await self._update_task_error_status(task_id, f"所有账户监控设置均失败: {', '.join(error_messages)}")
            self.ListenLog.emit(f"监控任务启动失败: 所有账户监控设置均失败")
            return False, f"所有账户监控设置均失败: {', '.join(error_messages)}"

    async def stop_task_monitoring(self, task_id: str) -> Tuple[bool, str]:
        """
        停止任务监控，注销所有事件处理器（内部使用，确保在worker线程中调用）
        
        Args:
            task_id: 任务ID
            
        Returns:
            Tuple[bool, str]: (成功标志, 消息)
        """
        self._logger.info(f"内部方法: 停止任务监控详细执行: {task_id}")
        self.ListenLog.emit(f"开始停止监控任务 {task_id}")
        
        if task_id not in self._running_tasks:
            # 检查数据库中是否仍标记为运行中
            async with get_session() as session:
                repo = MonitorTaskRepository(session=session)
                task = await repo.get_task_by_id(task_id)
                if task and task.is_running:
                    # 更新数据库状态
                    await repo.update_task_fields(task_id, {"is_running": False, "last_error": None})
                    await session.commit()
                    self._logger.info(f"任务 {task_id} 在运行时缓存中未找到，但已在数据库中标记为停止")
                    self.ListenLog.emit(f"任务 {task_id} 已在数据库中标记为停止")
                    return True, "任务已停止（运行时状态已清理）"
            return True, "任务未在运行"
            
        # 注销事件处理器
        unregister_success = False
        unregister_message = ""
        try:
            # 修改：获取任务ID，然后等待结果
            task_id_for_unregister = self._telegram_worker.unregister_monitoring_handlers(task_id)
            # 等待任务完成并获取结果
            unregister_success, unregister_message = await self._telegram_worker.get_task_result(task_id_for_unregister)
            if not unregister_success:
                self._logger.warning(f"注销事件处理器失败: {unregister_message}")
        except Exception as e:
            self._logger.error(f"注销事件处理器异常: {str(e)}")
            unregister_message = str(e)
            
        # 更新数据库状态 - 无论注销事件处理器是否成功，都要更新数据库状态
        try:
            async with get_session() as session:
                repo = MonitorTaskRepository(session=session)
                # 如果注销失败，记录错误信息
                last_error = None if unregister_success else f"注销事件处理器失败: {unregister_message}"
                await repo.update_task_fields(task_id, {"is_running": False, "last_error": last_error})
                await session.commit()
                self._logger.info(f"任务 {task_id} 已在数据库中标记为停止")
        except Exception as e_db:
            self._logger.error(f"更新任务 {task_id} 数据库状态失败: {e_db}")
            
        # 移除运行时缓存 - 无论注销事件处理器是否成功，都要清理运行时缓存
        if task_id in self._running_tasks:
            del self._running_tasks[task_id]
            self._logger.info(f"任务 {task_id} 已从运行时缓存中移除")
        
        self._logger.info(f"任务 {task_id} 已停止监控")
        self.ListenLog.emit(f"任务 {task_id} 已成功停止监控")
        
        if not unregister_success:
            return False, f"任务已在数据库中标记为停止，但注销事件处理器失败: {unregister_message}"
        return True, "监控已停止"

    async def _update_task_error_status(self, task_id: str, error_message: str):
        try:
            async with get_session() as session:
                repo = MonitorTaskRepository(session=session)
                await repo.update_task_fields(task_id, {"is_running": False, "last_error": error_message})
                await session.commit()
        except Exception as e_db:
            self._logger.error(f"更新任务 {task_id} 错误状态失败: {e_db}")

    async def process_monitored_message(self, task_id: str, task_config: Dict[str, Any], event: events.NewMessage.Event):
        """处理监控到的新消息事件"""
        try:
            # 提取消息和发送者信息
            message = event.message
            message_text = getattr(message, 'text', '') or getattr(message, 'caption', '') or ""
            chat_id = event.chat_id
            sender = await message.get_sender()
            
            # 构建发送者信息字典
            sender_info = await self._extract_sender_info(sender)
            
            # 记录收到的消息
            self.ListenLog.emit(f"任务[{task_config.get('name', task_id)}]: 收到来自群组 {chat_id} 用户 {sender_info.get('username', sender_info.get('id'))} 的消息: {message_text[:50]}...")
            self._logger.debug(f"任务[{task_config.get('name', task_id)}]: 收到来自群组 {chat_id} 用户 {sender_info.get('username', sender_info.get('id'))} 的消息: {message_text[:50]}...")

            # 1. 消息过滤
            if await self._should_filter_message(task_id, task_config, sender_info, message_text):
                return

            # 判断任务类型：关键词列表为空则视为群组监控类型
            is_group_monitoring = not task_config.get("keywords", [])
            
            # 初始化匹配关键词
            matched_keywords = []
            
            # 2. 关键词匹配（仅对关键词监控类型进行匹配）
            if not is_group_monitoring:
                matched_keywords = await self._match_keywords(task_id, task_config, message_text)
                if not matched_keywords:
                    return
                
                # 记录匹配成功
                self._logger.info(f"任务[{task_id}]: 消息匹配到关键词! Keywords: {matched_keywords}, Msg: {message_text[:30]}...")
                self.ListenLog.emit(f"任务[{task_id}]: 匹配关键词 {matched_keywords} - 用户: {sender_info.get('username', '未知')}, 消息: {message_text[:30]}...")
            else:
                # 群组监控类型，直接记录消息
                self._logger.info(f"任务[{task_id}]: 群组监控类型，记录消息: {message_text[:30]}...")
                self.ListenLog.emit(f"任务[{task_id}]: 群组监控类型，记录用户: {sender_info.get('username', '未知')}, 消息: {message_text[:30]}...")


            # 4. 保存匹配消息到数据库
            msg_id = await self._save_matched_message(task_id, message, chat_id, sender_info, message_text, matched_keywords)
            if not msg_id:
                self._logger.error(f"任务[{task_id}]: 保存匹配消息到数据库失败")
                self.ListenLog.emit(f"任务[{task_id}]: 保存匹配消息到数据库失败")
                
            # 5. 发送通知
            await self._send_match_notifications(task_id, task_config, event, message, chat_id, sender_info, message_text, matched_keywords)
            
        except Exception as e:
            self._logger.error(f"处理监控消息时发生错误 (Task ID: {task_id}): {str(e)}", exc_info=True)
            # 更新任务的错误状态
            await self._update_task_error_status(task_id, f"消息处理错误: {str(e)}")
            
    async def _extract_sender_info(self, sender) -> Dict[str, Any]:
        """
        提取发送者信息
        
        Args:
            sender: Telethon的用户/频道对象
            
        Returns:
            Dict[str, Any]: 包含发送者信息的字典
        """
        if not sender:
            return {"id": 0}
            
        sender_info = {
            "id": sender.id,
            "username": getattr(sender, 'username', None),
            "first_name": getattr(sender, 'first_name', None),
            "last_name": getattr(sender, 'last_name', None),
            "phone": getattr(sender, 'phone', None),
            "is_bot": getattr(sender, 'bot', False)
        }
        # 构建全名
        sender_info["full_name"] = f"{sender_info.get('first_name', '')} {sender_info.get('last_name', '')}".strip()
        return sender_info
        
    async def _should_filter_message(self, task_id: str, task_config: Dict[str, Any], sender_info: Dict[str, Any], message_text: str) -> bool:
        """
        检查消息是否应该被过滤（忽略）
        
        Args:
            task_id: 任务ID
            task_config: 任务配置
            sender_info: 发送者信息
            message_text: 消息文本
            
        Returns:
            bool: 如果应该过滤返回True，否则返回False
        """
        # 设置消息过滤器
        self._message_filter.update_filters({
            "keywords": [],
            "ignore_keywords": task_config.get("ignore_keywords", []),
            "ignore_nicknames": task_config.get("ignore_nicknames_rules", [])
        })
        
        # 检查是否应该忽略
        if await self._message_filter.should_ignore(sender_info, message_text):
            self._logger.info(f"任务[{task_id}]: 消息或用户被忽略规则过滤。User: {sender_info.get('username')}, Msg: {message_text[:30]}")
            self.ListenLog.emit(f"任务[{task_id}]: 消息被过滤 - 用户: {sender_info.get('username', '未知')}, 消息: {message_text[:30]}...")
            return True
            
        return False
        
    async def _match_keywords(self, task_id: str, task_config: Dict[str, Any], message_text: str) -> List[str]:
        """
        匹配消息中的关键词
        
        Args:
            task_id: 任务ID
            task_config: 任务配置
            message_text: 消息文本
            
        Returns:
            List[str]: 匹配到的关键词列表，如果没有匹配返回空列表
        """
        target_keywords = task_config.get("keywords", [])
        # 提取匹配的关键词
        matched_keywords = await self._keyword_extractor.extract(message_text, target_keywords)
        return matched_keywords
        
    async def _save_matched_message(self, task_id: str, message, chat_id: int, 
                                   sender_info: Dict[str, Any], message_text: str, 
                                   matched_keywords: List[str]) -> Optional[int]:
        """
        保存匹配到关键词的消息到数据库
        
        Args:
            task_id: 任务ID
            message: Telethon消息对象
            chat_id: 聊天ID
            sender_info: 发送者信息
            message_text: 消息文本
            matched_keywords: 匹配到的关键词列表
            
        Returns:
            Optional[int]: 保存的消息ID，失败返回None
        """
        try:
            async with get_session() as session:
                repo = MonitorTaskRepository(session=session)
                
                # 调用仓储层保存匹配消息
                msg_id = await repo.store_matched_message(
                    task_id=task_id,
                    tg_message_id=message.id,
                    tg_chat_id=chat_id,
                    tg_sender_id=sender_info.get("id"),
                    sender_username=sender_info.get("username"),
                    sender_fullname=sender_info.get("full_name"),
                    message_text=message_text,
                    matched_keywords_json=json.dumps(matched_keywords),
                    message_timestamp=message.date  # Telethon消息对象的日期已是datetime类型
                )
                
                # 记录保存结果
                if msg_id:
                    self._logger.info(f"任务[{task_id}]: 成功保存匹配消息到数据库，ID: {msg_id}")
                    self.ListenLog.emit(f"任务[{task_id}]: 成功保存匹配消息到数据库，ID: {msg_id}")
                
                # 提交事务
                await session.commit()
                return msg_id
                
        except Exception as e:
            self._logger.error(f"保存匹配消息到数据库失败: {str(e)}", exc_info=True)
            return None
            


    async def _send_match_notifications(self, task_id: str, task_config: Dict[str, Any], 
                                       event, message, chat_id: int, sender_info: Dict[str, Any], 
                                       message_text: str, matched_keywords: List[str]) -> None:
        """
        发送匹配通知
        
        Args:
            task_id: 任务ID
            task_config: 任务配置
            event: Telethon事件对象
            message: Telethon消息对象
            chat_id: 聊天ID
            sender_info: 发送者信息
            message_text: 消息文本
            matched_keywords: 匹配到的关键词
        """
        try:
            # 获取通知配置
            notification_configs = task_config.get("notifications", [])
            if not notification_configs:
                # 如果任务配置中没有通知配置，从数据库获取
                async with get_session() as session:
                    repo = MonitorTaskRepository(session=session)
                    notifications_models = await repo.get_notification_configs_for_task(task_id)
                    notification_configs = [nm.to_dict() for nm in notifications_models if nm.is_active]
            
            if not notification_configs:
                # 无通知配置，直接返回
                return
                
            # 准备模板数据
            template_data = {
                "task_name": task_config.get("name", "N/A"),
                "group_title": event.chat.title if event.chat else str(chat_id),
                "group_id": str(chat_id),
                "user_name": sender_info.get("full_name", sender_info.get("username", "未知用户")),
                "user_id": str(sender_info.get("id", "N/A")),
                "username": sender_info.get("username", "N/A"),
                "timestamp": message.date.strftime("%Y-%m-%d %H:%M:%S"),
                "message_text": message_text,
                "matched_keywords": ", ".join(matched_keywords),
                "message_link": f"https://t.me/c/{str(chat_id).replace('-100','',1)}/{message.id}" if str(chat_id).startswith("-100") else None
            }
            
            # 获取当前事件的账户信息（即监听到关键词的账户）
            # 在event对象中获取账户信息或从message对象的client获取
            trigger_account_phone = None
            try:
                if hasattr(event, 'client') and hasattr(event.client, 'phone'):
                    trigger_account_phone = event.client.phone
                elif hasattr(message, 'client') and hasattr(message.client, 'phone'):
                    trigger_account_phone = message.client.phone
                
                # 如果上面的方法获取不到，从监控的群组配置中查找
                if not trigger_account_phone:
                    for chat_info in task_config.get("monitored_chats", []):
                        if str(chat_info.get("chat_id", "")) == str(chat_id):
                            trigger_account_phone = chat_info.get("account_phone")
                            break
                
                self._logger.info(f"任务[{task_id}]: 触发通知的账户电话: {trigger_account_phone}")
            except Exception as e:
                self._logger.error(f"获取触发账户信息失败: {e}", exc_info=True)
            
            # 发送所有通知
            for notif_conf in notification_configs:
                if not notif_conf.get("is_active", True):
                    continue
                
                # 确定使用哪个账户发送通知
                account_phone = notif_conf.get("account_phone")
                if notif_conf.get("use_trigger_account", False) and trigger_account_phone:
                    account_phone = trigger_account_phone
                    self._logger.info(f"任务[{task_id}]: 使用触发账户 {account_phone} 发送通知")
                
                # 确定目标类型（群组或私聊）
                target_type = notif_conf.get("target_type")
                target_address = notif_conf.get("target_address")
                
                # 如果目标类型为自动判断，则根据目标地址格式判断
                if target_type == "auto":
                    # 以 - 或数字开头的可能是群组 ID
                    if target_address and (target_address.startswith("-") or target_address[0].isdigit()):
                        target_type = "group"
                    else:
                        target_type = "private"  # 默认为私聊
                    
                    self._logger.info(f"任务[{task_id}]: 自动确定目标类型为 {target_type}, 地址: {target_address}")
                    
                # 构建通知配置
                actual_notifier_config = {
                    "target_type": target_type,
                    "target_address": target_address,
                    "account_phone": account_phone,  # 可能是None，NotificationManager会处理
                    "template": notif_conf.get("template", NotificationManager.DEFAULT_MESSAGE_TEMPLATE)
                }
                
                # 发送通知
                success, msg = await self._notification_manager.send_notification(actual_notifier_config, template_data)
                if success:
                    self._logger.info(f"任务[{task_id}]: 通知已发送 via {target_type} 到 {target_address}")
                    self.ListenLog.emit(f"任务[{task_id}]: 通知已发送 via {target_type} 到 {target_address}")
                else:
                    self._logger.error(f"任务[{task_id}]: 发送通知失败 via {target_type} 到 {target_address}. 原因: {msg}")
                    self.ListenLog.emit(f"任务[{task_id}]: 发送通知失败 via {target_type} 到 {target_address}. 原因: {msg}")
                    
        except Exception as e:
            self._logger.error(f"发送通知时发生错误: {str(e)}", exc_info=True)

    async def _get_account_id_by_username_async(self, username: str) -> int:
        """
        异步根据用户名获取账号ID
        
        Args:
            username: 用户名
            
        Returns:
            int: 账号ID，未找到返回0
        """
        try:
            async with get_session() as session:
                repo = AccountRepository(session=session)
                account = await repo.find_account_by_username(username)
                
                if account:
                    return account.id
                    
                return 0
        except Exception as e:
            self._logger.error(f"异步获取账号ID失败: {username}, 错误: {str(e)}", exc_info=True)
            return 0
            
    async def delete_task(self, task_id: str) -> Tuple[bool, str]:
        """
        删除监控任务
        
        Args:
            task_id: 任务ID
            
        Returns:
            Tuple[bool, str]: (成功标志, 消息)
        """
        try:
            # 先停止任务
            if task_id in self._running_tasks:
                self.stop_task(task_id)
                
            result = await self._delete_task_async(task_id)
            return result
        except Exception as e:
            error_msg = f"删除任务异常: {str(e)}"
            self._logger.error(error_msg, exc_info=True)
            return False, error_msg
            
    async def _delete_task_async(self, task_id: str) -> Tuple[bool, str]:
        """
        异步删除监控任务
        
        Args:
            task_id: 任务ID
            
        Returns:
            Tuple[bool, str]: (成功标志, 消息)
        """
        try:
            async with get_session() as session:
                repo = MonitorTaskRepository(session=session)
                
                # 先删除关联的通知配置
                notif_success = await repo.delete_notification_configs_by_task(task_id)
                if not notif_success:
                    self._logger.warning(f"删除任务 {task_id} 的通知配置失败，但将继续删除其他相关数据")
                
                # 删除与任务相关的所有采集数据（监控消息）
                msg_success = await repo.delete_task_messages(task_id)
                if msg_success:
                    self._logger.info(f"成功删除任务 {task_id} 的所有监控消息数据")
                else:
                    self._logger.warning(f"删除任务 {task_id} 的监控消息数据失败，但将继续删除任务")
                
                # 删除任务的监控群组
                chat_success = await repo.delete_monitored_chats_for_task(task_id)
                if not chat_success:
                    self._logger.warning(f"删除任务 {task_id} 的监控群组失败，但将继续删除任务")
                
                # 最后删除任务本身
                success = await repo.delete_task(task_id)
                
                if success:
                    await session.commit()
                    self._logger.info(f"任务 {task_id} 及其所有相关数据已成功删除")
                    return True, "任务删除成功"
                else:
                    await session.rollback()
                    self._logger.warning(f"任务 {task_id} 不存在或删除失败")
                    return False, "任务不存在或删除失败"
                    
        except Exception as e:
            if session and session.is_active:
                await session.rollback()
            self._logger.error(f"异步删除任务失败: ID {task_id}, 错误: {str(e)}", exc_info=True)
            return False, f"删除任务失败: {str(e)}"
            
    def get_task_detail(self, task_id: str) -> Optional[Dict[str, Any]]:
        """
        获取任务详情，包括统计数据和用户列表 - 返回异步协程，调用者需使用await运行
        
        Args:
            task_id: 任务ID
            
        Returns:
            异步协程对象，调用者需要await获取结果
        """
        self._logger.info(f"获取任务详情: ID {task_id}")
        # 直接返回协程对象，让调用者在已有的事件循环中执行
        return self.get_task_by_id(task_id)
            
    async def _get_task_detail_async(self, task_id: str) -> Optional[Dict[str, Any]]:
        """
        异步获取任务详情 - Replaced by get_task_by_id which now includes more details
        """
        self._logger.warning("_get_task_detail_async is deprecated, use get_task_by_id which is now richer.")
        return await self.get_task_by_id(task_id)
            
    async def get_task_users(self, task_id: str, page: int = 1, page_size: int = 10, search_term: str = "") -> Optional[Dict[str, Any]]:
        """
        获取任务监控到的用户列表
        
        Args:
            task_id: 任务ID
            page: 页码
            page_size: 每页记录数
            search_term: 搜索关键词
            
        Returns:
            Optional[Dict[str, Any]]: 用户列表分页数据
        """
        try:
            users_data = await self._get_task_users_async(task_id, page, page_size, search_term)
            return users_data
        except Exception as e:
            self._logger.error(f"获取任务用户列表失败: ID {task_id}, 错误: {str(e)}", exc_info=True)
            return None
            
    async def _get_task_users_async(self, task_id: str, page: int = 1, page_size: int = 10, search_term: str = "") -> Optional[Dict[str, Any]]:
        """
        异步获取任务监控到的用户列表
        
        Args:
            task_id: 任务ID
            page: 页码
            page_size: 每页记录数
            search_term: 搜索关键词
            
        Returns:
            Optional[Dict[str, Any]]: 用户列表分页数据
        """
        try:
            async with get_session() as session:
                repo = MonitorTaskRepository(session=session)
                users_data = await repo.get_task_users(task_id, page, page_size, search_term)
                return users_data
                
        except Exception as e:
            self._logger.error(f"异步获取任务用户列表失败: ID {task_id}, 错误: {str(e)}", exc_info=True)
            return None
            
    async def export_task_data(self, task_id: str, file_path: str) -> Tuple[bool, str]:
        """
        导出任务数据
        
        Args:
            task_id: 任务ID
            file_path: 保存文件路径
            
        Returns:
            Tuple[bool, str]: (成功标志, 消息)
        """
        try:
            result =await self._export_task_data_async(task_id, file_path)
            return result
        except Exception as e:
            error_msg = f"导出任务数据异常: {str(e)}"
            self._logger.error(error_msg, exc_info=True)
            return False, error_msg
    
    async def _export_task_data_async(self, task_id: str, file_path: str) -> Tuple[bool, str]:
        """
        异步导出任务数据 (MatchedMessages)
        
        Args:
            task_id: 任务ID
            file_path: 保存文件路径
            
        Returns:
            Tuple[bool, str]: (成功标志, 消息)
        """
        try:
            async with get_session() as session:
                repo = MonitorTaskRepository(session=session)
                
                # 使用已有的 get_all_task_users 方法获取任务所有用户数据
                all_users_data = await repo.get_all_task_users(task_id)
                
                if not all_users_data:
                    return False, "任务没有匹配到任何用户数据可供导出。"
                    
                # 根据文件扩展名选择导出格式
                ext = os.path.splitext(file_path)[1].lower()
                
                export_data = []
                for user_data in all_users_data:
                    export_data.append({
                        '用户ID': user_data.get("uid", ""),
                        '用户名': user_data.get("username", ""),
                        '昵称': user_data.get("nickname", ""),
                        '匹配关键词': user_data.get("keyword", ""),
                        '任务类型': user_data.get("task_type", ""),
                        '加入日期': user_data.get("join_date", "")
                    })

                if ext == ".csv":
                    import csv
                    fieldnames = ['用户ID', '用户名', '昵称', '匹配关键词', '任务类型', '加入日期']
                    with open(file_path, 'w', newline='', encoding='utf-8-sig') as csvfile: # utf-8-sig for Excel compatibility
                        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                        writer.writeheader()
                        writer.writerows(export_data)
                    
                elif ext == ".xlsx":
                    return False,"暂时移除对xlsx支持，请使用csv格式导出"
                    #df = pandas.DataFrame(export_data)
                    #df.to_excel(file_path, index=False)
                else:
                    return False, "不支持的文件格式，请使用 .csv 或 .xlsx"
                    
                return True, f"成功导出 {len(export_data)} 条用户数据到 {file_path}"
                
        except Exception as e:
            self._logger.error(f"异步导出任务数据失败: ID {task_id}, 错误: {str(e)}", exc_info=True)
            return False, f"导出数据失败: {str(e)}"
    
    def get_accounts_by_group(self, group_name: str) -> List[Dict[str, Any]]:
        """
        根据分组获取账号列表
        
        Args:
            group_name: 分组名称
            
        Returns:
            List[Dict[str, Any]]: 账号列表
        """
        try:
            accounts = asyncio.run(self._get_accounts_by_group_async(group_name))
            return accounts
        except Exception as e:
            self._logger.error(f"获取分组账号失败: {group_name}, 错误: {str(e)}", exc_info=True)
            return []
            
    async def _get_accounts_by_group_async(self, group_name: str) -> List[Dict[str, Any]]:
        """
        异步根据分组获取账号列表
        
        Args:
            group_name: 分组名称
            
        Returns:
            List[Dict[str, Any]]: 账号列表
        """
        try:
            async with get_session() as session:
                repo = AccountRepository(session=session)
                accounts = await repo.get_accounts_by_group(group_name)
                
                # 转换为视图层所需格式
                result = []
                for account in accounts:
                    account_dict = {
                        "id": account.id,
                        "phone": account.phone,
                        "username": account.username,
                        "first_name": account.first_name,
                        "last_name": account.last_name
                    }
                    result.append(account_dict)
                    
                return result
                
        except Exception as e:
            self._logger.error(f"异步获取分组账号失败: {group_name}, 错误: {str(e)}", exc_info=True)
            return []
            
    def get_all_account_groups(self) -> List[str]:
        """
        获取所有账号分组
        
        Returns:
            List[str]: 分组名称列表
        """
        try:
            groups = asyncio.run(self._get_all_account_groups_async())
            return groups
        except Exception as e:
            self._logger.error(f"获取账号分组失败: {str(e)}", exc_info=True)
            return ["默认分组"]
            
    async def _get_all_account_groups_async(self) -> List[str]:
        """
        异步获取所有账号分组
        
        Returns:
            List[str]: 分组名称列表
        """
        try:
            async with get_session() as session:
                repo = AccountRepository(session=session)
                groups = await repo.get_all_groups()
                
                if not groups:
                    return ["默认分组"]
                    
                return groups
                
        except Exception as e:
            self._logger.error(f"异步获取账号分组失败: {str(e)}", exc_info=True)
            return ["默认分组"]
            
    def get_chats_for_account(self, account_id: int) -> List[Dict[str, Any]]:
        """
        获取账号的群组/频道列表
        
        Args:
            account_id: 账号ID
            
        Returns:
            List[Dict[str, Any]]: 群组/频道列表
        """
        try:
            chats = asyncio.run(self._get_chats_for_account_async(account_id))
            return chats
        except Exception as e:
            self._logger.error(f"获取账号群组失败: ID {account_id}, 错误: {str(e)}", exc_info=True)
            return []
            
    async def _get_chats_for_account_async(self, account_id: int) -> List[Dict[str, Any]]:
        """
        异步获取账号的群组/频道列表
        
        Args:
            account_id: 账号ID
            
        Returns:
            List[Dict[str, Any]]: 群组/频道列表
        """
        try:
            # 从Telegram获取群组/频道列表
            # 这里需要实现从Telegram获取群组/频道的逻辑
            # 临时返回模拟数据
            return [
                {"id": f"g{account_id}_1", "title": f"群组1", "is_channel": False},
                {"id": f"g{account_id}_2", "title": f"群组2", "is_channel": False},
                {"id": f"c{account_id}_1", "title": f"频道1", "is_channel": True}
            ]
            
        except Exception as e:
            self._logger.error(f"异步获取账号群组失败: ID {account_id}, 错误: {str(e)}", exc_info=True)
            return []
            
    def get_notification_groups(self) -> List[Dict[str, Any]]:
        """
        获取可用于通知的群组
        
        Returns:
            List[Dict[str, Any]]: 群组列表
        """
        try:
            # 临时返回模拟数据
            return [
                {"id": "g1", "title": "通知群组1"},
                {"id": "g2", "title": "通知群组2"}
            ]
        except Exception as e:
            self._logger.error(f"获取通知群组失败: {str(e)}", exc_info=True)
            return []
            
    def get_notification_users(self) -> List[Dict[str, Any]]:
        """
        获取可用于通知的用户
        
        Returns:
            List[Dict[str, Any]]: 用户列表
        """
        try:
            # 临时返回模拟数据
            return [
                {"id": "u1", "username": "user1"},
                {"id": "u2", "username": "user2"}
            ]
        except Exception as e:
            self._logger.error(f"获取通知用户失败: {str(e)}", exc_info=True)
            return []
            
    def get_notification_accounts(self) -> List[Dict[str, Any]]:
        """
        获取可用于发送通知的账号
        
        Returns:
            List[Dict[str, Any]]: 账号列表
        """
        try:
            accounts = asyncio.run(self._get_notification_accounts_async())
            return accounts
        except Exception as e:
            self._logger.error(f"获取通知账号失败: {str(e)}", exc_info=True)
            return []
            
    async def _get_notification_accounts_async(self) -> List[Dict[str, Any]]:
        """
        异步获取可用于发送通知的账号
        
        Returns:
            List[Dict[str, Any]]: 账号列表
        """
        try:
            async with get_session() as session:
                repo = AccountRepository(session=session)
                accounts = await repo.get_all_accounts()
                
                # 转换为视图层所需格式
                result = []
                for account in accounts:
                    if account.is_connected:  # 只返回已连接的账号
                        account_dict = {
                            "id": account.id,
                            "phone": account.phone,
                            "username": account.username,
                            "first_name": account.first_name,
                            "last_name": account.last_name
                        }
                        result.append(account_dict)
                    
                return result
                
        except Exception as e:
            self._logger.error(f"异步获取通知账号失败: {str(e)}", exc_info=True)
            return []
    
    def start_task(self, task_id: str) -> Tuple[bool, str]:
        """
        启动监控任务
        
        Args:
            task_id: 任务ID
            
        Returns:
            Tuple[bool, str]: (成功标志, 消息)
        """
        try:
            # 检查任务是否已在运行
            if task_id in self._running_tasks:
                return True, "任务已在运行中"
                
            result = asyncio.run(self._start_task_async(task_id))
            return result
        except Exception as e:
            error_msg = f"启动任务异常: {str(e)}"
            self._logger.error(error_msg, exc_info=True)
            return False, error_msg
            
    async def _start_task_async(self, task_id: str) -> Tuple[bool, str]:
        """
        旧版异步启动监控任务 - Replaced by start_task_monitoring_by_id and start_task_monitoring
        """
        self._logger.warning("_start_task_async is deprecated. Use start_task_monitoring_by_id.")
        raise NotImplementedError("_start_task_async is deprecated.")
    
    async def get_total_users_count(self) -> int:
        """
        获取所有任务的总用户数量
        
        Returns:
            int: 总用户数量
        """
        try:
            async with get_session() as session:
                repo = MonitorTaskRepository(session=session)
                total_count = await repo.get_total_users_count()
                return total_count
        except Exception as e:
            self._logger.error(f"获取总用户数量失败: {str(e)}", exc_info=True)
            return 0
            
    def get_total_users_count_sync(self) -> int:
        """
        同步获取所有任务的总用户数量
        
        Returns:
            int: 总用户数量
        """
        try:
            return asyncio.run(self.get_total_users_count())
        except Exception as e:
            self._logger.error(f"获取总用户数量失败: {str(e)}", exc_info=True)
            return 0

    async def get_task_stats(self, task_id: str) -> Dict[str, Any]:
        """
        获取单个任务的统计数据
        
        Args:
            task_id: 任务ID
            
        Returns:
            Dict[str, Any]: 统计数据字典
        """
        try:
            async with get_session() as session:
                repo = MonitorTaskRepository(session=session)
                stats = await repo.get_task_stats(task_id)
                return stats
        except Exception as e:
            self._logger.error(f"获取任务 {task_id} 统计数据失败: {str(e)}", exc_info=True)
            return {
                "total_users": 0,
                "today_users": 0,
                "avg_daily": 0,
                "running_days": 0
            }

    async def get_global_stats(self) -> Dict[str, Any]:
        """
        获取所有任务的全局统计数据
        
        Returns:
            Dict[str, Any]: 包含全局统计数据的字典
        """
        try:
            async with get_session() as session:
                repo = MonitorTaskRepository(session=session)
                
                # 获取总用户数
                total_users = await repo.get_total_users_count()
                
                # 获取今日采集用户数
                today = datetime.datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
                stmt_today = select(func.count(MonitorMessage.user_id.distinct())).where(
                    MonitorMessage.collected_at >= today
                )
                result_today = await session.execute(stmt_today)
                today_users = result_today.scalar() or 0
                
                # 获取最早的任务创建时间
                stmt_earliest = select(func.min(MonitorTask.created_at))
                result_earliest = await session.execute(stmt_earliest)
                earliest_date = result_earliest.scalar()
                
                # 计算运行天数
                running_days = 0
                if earliest_date:
                    delta = datetime.datetime.now() - earliest_date
                    running_days = max(1, delta.days)  # 至少1天
                else:
                    running_days = 1  # 默认至少1天
                
                # 计算平均每日用户数
                avg_daily = round(total_users / running_days) if running_days > 0 else 0
                
                # 返回统计数据
                return {
                    "total_users": total_users,
                    "today_users": today_users,
                    "avg_daily": avg_daily,
                    "running_days": running_days
                }
                
        except Exception as e:
            self._logger.error(f"获取全局统计数据失败: {str(e)}", exc_info=True)
            return {
                "total_users": 0,
                "today_users": 0,
                "avg_daily": 0,
                "running_days": 0
            }

    async def get_all_users(self, page: int = 1, page_size: int = 10, search_term: str = "") -> Optional[Dict[str, Any]]:
        """
        获取所有任务的用户列表
        
        Args:
            page: 页码
            page_size: 每页记录数
            search_term: 搜索关键词
            
        Returns:
            Optional[Dict[str, Any]]: 用户列表分页数据
        """
        try:
            async with get_session() as session:
                repo = MonitorTaskRepository(session=session)
                users_data = await repo.get_all_users(page, page_size, search_term)
                return users_data
        except Exception as e:
            self._logger.error(f"获取所有用户列表失败: 错误: {str(e)}", exc_info=True)
            return None
