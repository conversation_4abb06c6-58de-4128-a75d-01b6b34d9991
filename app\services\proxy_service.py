#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
代理应用服务
负责协调用户界面和核心业务逻辑，遵循分层架构设计原则
"""

from typing import List, Dict, Any, Optional, Tuple, Union, AsyncGenerator
from datetime import datetime
import os
import asyncio
import ipaddress

from PySide6.QtCore import QObject, Signal

from core.proxy.proxy_core_service import ProxyCoreService
from core.proxy.proxy_task_manager import ProxyTaskManager
from data.repositories.proxy_repo import ProxyRepository
from data.models.proxy import ProxyModel
from data.database import get_session
from utils.logger import get_logger

class ProxyService(QObject):
    """代理应用服务类
    
    负责以下功能:
    - 协调UI和核心业务逻辑
    - 转发核心服务的信号
    - 提供高层业务操作
    - 管理异步任务执行
    
    遵循分层架构:
    - 应用层：处理UI交互和业务流程
    - 核心层：处理核心业务逻辑
    - 数据层：通过仓储层访问
    """
    
    def __init__(self):
        """初始化代理应用服务"""
        super().__init__()
        self._logger = get_logger("app.services.proxy")
        
        # 获取任务管理器单例
        self.task_manager = ProxyTaskManager.instance()
        
        # 初始化核心服务
        self.core_service = ProxyCoreService()
        
        # 不再直接初始化代理仓储
        # self.proxy_repo = ProxyRepository()
    
    async def add_proxy(self, host: str, port: int, username: str, password: str, proxy_type: str, is_local: bool) -> Optional[ProxyModel]:
        """添加单个代理
        
        Args:
            host: 代理主机地址
            port: 代理端口
            username: 用户名
            password: 密码
            proxy_type: 代理类型
            is_local: 是否本地代理
            
        Returns:
            Optional[ProxyModel]: 添加成功返回代理模型，失败返回None
        """
        # 去除IP中的所有空格
        host = ''.join(host.split())
        
        self._logger.info(f"添加代理: {host}:{port}, 类型: {proxy_type}, 是否本地: {is_local}")
        
        try:
            async with get_session() as session:
                # 初始化仓储
                proxy_repo = ProxyRepository(session=session)
                
                # 检查IP是否已存在，不管是本地还是外部代理
                host_exists = await proxy_repo.check_host_exists(host)
                if host_exists:
                    self._logger.warning(f"代理IP {host} 已存在，跳过添加")
                    return None
                
                # 如果是本地代理，检查端口是否已被使用，如果被使用则自动选择一个新端口
                original_port = port
                if is_local:
                    # 查找可用端口，从原始端口开始递增尝试
                    max_attempts = 100  # 最多尝试100个端口
                    port_found = False
                    
                    for i in range(max_attempts):
                        current_port = original_port + i
                        port_exists = await proxy_repo.check_port_exists(current_port, is_local=True)
                        
                        if not port_exists:
                            # 找到可用端口
                            port = current_port
                            port_found = True
                            
                            # 如果不是原始端口，记录日志
                            if port != original_port:
                                self._logger.info(f"原始端口 {original_port} 已被使用，自动选择新端口 {port}")
                            
                            break
                    
                    if not port_found:
                        self._logger.warning(f"无法找到可用端口，已尝试 {original_port} 到 {original_port + max_attempts - 1}")
                        return None
                
                # 使用仓储的add方法添加代理
                proxy = await proxy_repo.add(
                    host=host,
                    port=port,
                    username=username if username else None,
                    password=password if password else None,
                    proxy_type=proxy_type.lower(),
                    is_local=is_local
                )
                
                # 提交事务
                await session.commit()
                
                # 如果是本地代理，需要更新配置并处理服务状态
                if is_local and proxy and proxy.id > 0:
                    # 更新配置文件
                    await self.core_service.generate_proxy_config(proxy=proxy)
                    
                    # 检查服务状态并处理
                    await self._handle_local_proxy_service()
                    
                    # 验证新添加的代理
                    self._logger.info(f"验证新添加的代理 ID: {proxy.id}")
                    await self.validate_proxy(proxy.id)
                
                return proxy
        except Exception as e:
            self._logger.exception(f"添加代理失败: {e}")
            return None
    
    async def add_ip_range(self, start_ip: str, end_ip: str, port_start: int, port_end: int, 
                          username: str, password: str, proxy_type: str, is_local: bool) -> Tuple[int, List[int]]:
        """批量添加IP范围的代理（优化版，先整理列表后批量写入）
        
        Args:
            start_ip: 起始IP
            end_ip: 结束IP
            port_start: 起始端口
            port_end: 结束端口
            username: 用户名
            password: 密码
            proxy_type: 代理类型
            is_local: 是否本地代理
        Returns:
            Tuple[int, List[int]]: (添加的代理数量, 新添加的代理ID列表)
        """
        # 去除IP中的所有空格
        start_ip = ''.join(start_ip.split())
        end_ip = ''.join(end_ip.split())
        self._logger.info(f"批量添加IP范围: {start_ip}-{end_ip}, 端口: {port_start}-{port_end}")
        task_name = f"add_ip_range_{start_ip}_{end_ip}_{port_start}_{port_end}_batch"

        async def add_ip_range_batch_task():
            try:
                nonlocal end_ip, port_start, port_end
                if not end_ip.count('.') == 3:
                    start_parts = start_ip.split('.')
                    if len(start_parts) == 4:
                        end_ip = f"{start_parts[0]}.{start_parts[1]}.{start_parts[2]}.{end_ip}"
                        self._logger.info(f"结束IP只有D段，已转换为完整IP: {end_ip}")
                start = int(ipaddress.IPv4Address(start_ip))
                end = int(ipaddress.IPv4Address(end_ip))
                if start > end:
                    start, end = end, start
                if port_start > port_end:
                    port_start, port_end = port_end, port_start
                port_range = list(range(port_start, port_end + 1))
                proxy_dicts = []
                used_ports = set()
                async with get_session() as session:
                    proxy_repo = ProxyRepository(session=session)
                    for ip_int in range(start, end + 1):
                        ip = str(ipaddress.IPv4Address(ip_int))
                        # 检查IP是否已存在
                        host_exists = await proxy_repo.check_host_exists(ip)
                        if host_exists:
                            self._logger.warning(f"代理IP {ip} 已存在，跳过添加")
                            continue
                        # 单个IP多端口
                        if start == end:
                            for port in port_range:
                                # 本地代理端口分配
                                if is_local:
                                    original_port = port
                                    max_attempts = 100
                                    for i in range(max_attempts):
                                        current_port = original_port + i
                                        if current_port in used_ports:
                                            continue
                                        port_exists = await proxy_repo.check_port_exists(current_port, is_local=True)
                                        if not port_exists:
                                            port = current_port
                                            used_ports.add(port)
                                            if port != original_port:
                                                self._logger.info(f"原始端口 {original_port} 已被使用，自动选择新端口 {port}")
                                            break
                                    else:
                                        self._logger.warning(f"无法找到可用端口，已尝试 {original_port} 到 {original_port + max_attempts - 1}")
                                        continue
                                proxy_dicts.append({
                                    "host": ip,
                                    "port": port,
                                    "username": username if username else None,
                                    "password": password if password else None,
                                    "proxy_type": proxy_type.lower(),
                                    "is_local": is_local
                                })
                        else:
                            # IP范围每个IP只加第一个端口
                            port = port_range[0]
                            if is_local:
                                original_port = port
                                max_attempts = 100
                                for i in range(max_attempts):
                                    current_port = original_port + i
                                    if current_port in used_ports:
                                        continue
                                    port_exists = await proxy_repo.check_port_exists(current_port, is_local=True)
                                    if not port_exists:
                                        port = current_port
                                        used_ports.add(port)
                                        if port != original_port:
                                            self._logger.info(f"IP {ip} 的原始端口 {original_port} 已被使用，自动选择新端口 {port}")
                                        break
                                else:
                                    self._logger.warning(f"IP {ip} 无法找到可用端口，已尝试 {original_port} 到 {original_port + max_attempts - 1}")
                                    continue
                            proxy_dicts.append({
                                "host": ip,
                                "port": port,
                                "username": username if username else None,
                                "password": password if password else None,
                                "proxy_type": proxy_type.lower(),
                                "is_local": is_local
                            })
                    # 批量写入
                    proxies = await proxy_repo.bulk_add(proxy_dicts)
                    await session.commit()
                    added_proxy_ids = [p.id for p in proxies if p.id]
                    added_count = len(added_proxy_ids)
                    # 本地代理处理
                    if is_local and added_count > 0:
                        await self.core_service.generate_proxy_config(refresh_all=True)
                        await self._handle_local_proxy_service()
                    return added_count, added_proxy_ids
            except Exception as e:
                self._logger.exception(f"批量添加IP范围失败: {e}")
                return 0, []
        future = self.task_manager.submit_task(task_name, add_ip_range_batch_task)
        if future:
            try:
                return await asyncio.wrap_future(future)
            except Exception as e:
                self._logger.exception(f"等待批量添加IP范围任务结果失败: {e}")
                return 0, []
        else:
            self._logger.warning("提交批量添加IP范围任务失败，尝试在主线程执行")
            return await add_ip_range_batch_task()
    
    async def find_by_id(self, proxy_id: int) -> Optional[ProxyModel]:
        """根据ID查找代理
        
        Args:
            proxy_id: 代理ID
            
        Returns:
            Optional[ProxyModel]: 代理模型或None
        """
        try:
            async with get_session() as session:
                # 初始化仓储
                proxy_repo = ProxyRepository(session=session)
                
                # 查找代理
                return await proxy_repo.find_by_id(proxy_id)
        except Exception as e:
            self._logger.exception(f"查找代理失败: {e}")
            return None

    async def validate_proxy(self, proxy_id: int) -> Tuple[bool, Optional[float]]:
        """验证单个代理
        
        Args:
            proxy_id: 代理ID
            
        Returns:
            Tuple[bool, Optional[float]]: (是否有效, 响应时间)
        """
        self._logger.info(f"验证代理ID: {proxy_id}")
        
        try:
            async with get_session() as session:
                # 初始化仓储
                proxy_repo = ProxyRepository(session=session)
                
                # 获取代理信息
                proxy = await proxy_repo.find_by_id(proxy_id)
                
                if not proxy:
                    self._logger.warning(f"未找到代理ID: {proxy_id}")
                    return False, None
                
                # 单个代理验证是轻量任务，直接在主线程执行
                is_valid, response_time = await self.task_manager.run_in_main_thread(
                    self.core_service.validate_proxy,
                    proxy
                )
                
                # 使用update_validation_result方法更新验证结果
                await proxy_repo.update_validation_result(proxy_id, is_valid, response_time)
                await session.commit()
                
                return is_valid, response_time
        except Exception as e:
            self._logger.exception(f"验证代理失败: {e}")
            return False, None
    
    async def validate_all_proxies(self) -> AsyncGenerator[Tuple[int, bool, Optional[float]], None]:
        """验证所有代理
        
        Yields:
            Tuple[int, bool, Optional[float]]: (代理ID, 是否有效, 响应时间)
        """
        self._logger.info("开始验证所有代理")
        
        try:
            async with get_session() as session:
                # 初始化仓储
                proxy_repo = ProxyRepository(session=session)
                
                # 获取所有代理
                proxies, _ = await proxy_repo.find_all()
                
                if not proxies:
                    self._logger.warning("没有找到代理")
                    return
                
                # 使用核心服务的流式验证功能（耗时任务，已在核心服务放入子线程）
                async for proxy_id, is_valid, response_time in self.core_service.validate_proxies_stream(proxies):
                    # 获取代理对象
                    proxy = next((p for p in proxies if p.id == proxy_id), None)
                    
                    if proxy:
                        # 更新代理状态
                        await proxy_repo.update_validation_result(
                            proxy_id, 
                            is_valid, 
                            response_time
                        )
                        await session.commit()
                    
                    # 产出结果
                    yield proxy_id, is_valid, response_time
        except Exception as e:
            self._logger.exception(f"验证所有代理失败: {e}")
            return
    
    async def delete_proxy(self, proxy_id: int) -> bool:
        """删除单个代理
        
        Args:
            proxy_id: 代理ID
            
        Returns:
            bool: 是否删除成功
        """
        self._logger.info(f"删除代理ID: {proxy_id}")
        
        try:
            async with get_session() as session:
                # 初始化仓储
                proxy_repo = ProxyRepository(session=session)
                
                # 获取代理信息
                proxy = await proxy_repo.find_by_id(proxy_id)
                
                if not proxy:
                    self._logger.warning(f"未找到代理ID: {proxy_id}")
                    return False
                
                # 如果是本地代理，需要先从配置文件中删除
                is_local = proxy.is_local
                
                # 删除代理
                result = await proxy_repo.delete(proxy_id)
                
                # 提交事务
                await session.commit()
                
                # 如果是本地代理，需要更新配置并重启服务
                if is_local and result:
                    # 从配置文件中删除代理
                    self.core_service.config_manager.update_config(proxy, delete=True)
                    
                    # 如果服务正在运行，重启服务以应用新配置
                    if self.get_service_status():
                        await self.restart_service()
                
                return result
        except Exception as e:
            self._logger.exception(f"删除代理失败: {e}")
            return False
    
    async def delete_all_proxies(self) -> bool:
        """删除所有代理
        
        Returns:
            bool: 是否删除成功
        """
        self._logger.info("删除所有代理")
        
        try:
            async with get_session() as session:
                # 初始化仓储
                proxy_repo = ProxyRepository(session=session)
                
                # 删除所有代理
                result = await proxy_repo.delete_all()
                
                # 提交事务
                await session.commit()
                
                # 重置配置文件
                await self.core_service.generate_proxy_config(refresh_all=True)
                
                # 如果服务正在运行，重启服务以应用新配置
                if self.get_service_status():
                    await self.restart_service()
                
                return result > 0
        except Exception as e:
            self._logger.exception(f"删除所有代理失败: {e}")
            return False
    
    async def set_proxy_active(self, proxy_id: int, is_active: bool) -> bool:
        """设置代理激活状态
        
        Args:
            proxy_id: 代理ID
            is_active: 是否激活
            
        Returns:
            bool: 是否设置成功
        """
        self._logger.info(f"设置代理ID: {proxy_id} 激活状态: {is_active}")
        
        try:
            async with get_session() as session:
                # 初始化仓储
                proxy_repo = ProxyRepository(session=session)
                
                # 获取代理信息
                proxy = await proxy_repo.find_by_id(proxy_id)
                
                if not proxy:
                    self._logger.warning(f"未找到代理ID: {proxy_id}")
                    return False
                
                # 更新激活状态
                proxy.is_active = is_active
                
                # 保存更新
                await proxy_repo.update(proxy_id, is_active=is_active)
                
                # 提交事务
                await session.commit()
                
                # 如果是本地代理，需要更新配置并可能重启服务
                if proxy.is_local:
                    # 更新所有配置
                    await self.core_service.generate_proxy_config(refresh_all=True)
                    
                    # 如果服务正在运行，重启服务以应用新配置
                    if self.get_service_status():
                        await self.restart_service()
                
                return True
        except Exception as e:
            self._logger.exception(f"设置代理激活状态失败: {e}")
            return False
    
    async def get_proxies(self, offset: int = 0, limit: int = 20) -> List[ProxyModel]:
        """获取代理列表（分页）
        
        Args:
            offset: 偏移量
            limit: 限制数量
            
        Returns:
            List[ProxyModel]: 代理列表
        """
        self._logger.debug(f"获取代理列表: offset={offset}, limit={limit}")
        
        try:
            async with get_session() as session:
                # 初始化仓储
                proxy_repo = ProxyRepository(session=session)
                
                # 获取分页代理列表
                proxies, _ = await proxy_repo.find_all(offset=offset, limit=limit)
                return proxies
        except Exception as e:
            self._logger.exception(f"获取代理列表失败: {e}")
            return []
    
    async def get_proxy_count(self) -> int:
        """获取代理总数
        
        Returns:
            int: 代理总数
        """
        try:
            async with get_session() as session:
                # 初始化仓储
                proxy_repo = ProxyRepository(session=session)
                
                # 获取代理总数
                _, count = await proxy_repo.find_all(offset=0, limit=1)
                return count
        except Exception as e:
            self._logger.exception(f"获取代理总数失败: {e}")
            return 0
    
    async def start_service(self) -> bool:
        """启动代理服务
        
        Returns:
            bool: 是否启动成功
        """
        self._logger.info("启动代理服务")
        
        # 启动服务是耗时任务，提交到子线程处理
        task_name = "start_service"
        
        # 创建异步任务
        async def start_service_task():
            try:
                # 确保配置文件已生成
                await self.core_service.generate_proxy_config(refresh_all=True)
                
                # 启动服务
                result = await self.core_service.start_proxy_service()
                
                return result
            except Exception as e:
                self._logger.exception(f"启动代理服务失败: {e}")
                return False
        
        # 提交到子线程执行
        future = self.task_manager.submit_task(task_name, start_service_task)
        
        if future:
            try:
                # 等待任务完成
                return await asyncio.wrap_future(future)
            except Exception as e:
                self._logger.exception(f"等待启动服务任务结果失败: {e}")
                return False
        else:
            # 如果任务提交失败，直接在主线程执行（不推荐，但作为备选）
            self._logger.warning("提交启动服务任务失败，尝试在主线程执行")
            return await start_service_task()
    
    async def stop_service(self) -> bool:
        """停止代理服务
        
        Returns:
            bool: 是否停止成功
        """
        self._logger.info("停止代理服务")
        
        # 停止服务是耗时任务，提交到子线程处理
        task_name = "stop_service"
        
        # 创建异步任务
        async def stop_service_task():
            try:
                # 停止服务
                result = await self.core_service.stop_proxy_service()
                
                return result
            except Exception as e:
                self._logger.exception(f"停止代理服务失败: {e}")
                return False
        
        # 提交到子线程执行
        future = self.task_manager.submit_task(task_name, stop_service_task)
        
        if future:
            try:
                # 等待任务完成
                return await asyncio.wrap_future(future)
            except Exception as e:
                self._logger.exception(f"等待停止服务任务结果失败: {e}")
                return False
        else:
            # 如果任务提交失败，直接在主线程执行（不推荐，但作为备选）
            self._logger.warning("提交停止服务任务失败，尝试在主线程执行")
            return await stop_service_task()
    
    async def restart_service(self) -> bool:
        """重启代理服务
        
        Returns:
            bool: 是否重启成功
        """
        self._logger.info("重启代理服务")
        
        # 重启服务是耗时任务，提交到子线程处理
        task_name = "restart_service"
        
        # 创建异步任务
        async def restart_service_task():
            try:
                # 确保配置文件已生成
                await self.core_service.generate_proxy_config(refresh_all=True)
                
                # 重启服务
                result = await self.core_service.restart_proxy_service()
                
                return result
            except Exception as e:
                self._logger.exception(f"重启代理服务失败: {e}")
                return False
        
        # 提交到子线程执行
        future = self.task_manager.submit_task(task_name, restart_service_task)
        
        if future:
            try:
                # 等待任务完成
                return await asyncio.wrap_future(future)
            except Exception as e:
                self._logger.exception(f"等待重启服务任务结果失败: {e}")
                return False
        else:
            # 如果任务提交失败，直接在主线程执行（不推荐，但作为备选）
            self._logger.warning("提交重启服务任务失败，尝试在主线程执行")
            return await restart_service_task()
    
    def get_service_status(self) -> bool:
        """获取服务状态
        
        Returns:
            bool: 服务是否运行中
        """
        try:
            # 获取服务状态
            return self.core_service.is_service_running()
        except Exception as e:
            self._logger.exception(f"获取服务状态失败: {e}")
            return False
            
    async def async_check_service_status(self) -> bool:
        """异步检查服务状态
        
        此方法用于需要实时检查服务状态的场合，比如启动、停止操作后
        
        Returns:
            bool: 服务是否运行中
        """
        try:
            # 异步检查服务状态
            return await self.core_service.async_check_service_status()
        except Exception as e:
            self._logger.exception(f"异步检查服务状态失败: {e}")
            return False
    
    def remove_service(self) -> bool:
        """彻底移除代理服务
        
        Returns:
            bool: 是否移除成功
        """
        self._logger.info("彻底移除代理服务")
        
        try:
            # 移除服务
            result = self.core_service.remove_proxy_service()
            
            return result
        except Exception as e:
            self._logger.exception(f"移除代理服务失败: {e}")
            return False

    def is_task_running(self, task_name: str) -> bool:
        """检查任务是否正在运行
        
        Args:
            task_name: 任务名称
            
        Returns:
            bool: 是否正在运行
        """
        return self.task_manager.is_task_running(task_name)
    
    async def refresh_proxies(self, offset: int = 0, limit: int = 20):
        """刷新代理列表（分页）
        
        Args:
            offset: 偏移量
            limit: 限制数量
        """
        self._logger.info(f"刷新代理列表: offset={offset}, limit={limit}")
        
        # 获取代理总数和列表的任务名称
        task_name = f"refresh_proxies_{offset}_{limit}"
        
        # 如果任务已在运行，不重复执行
        if self.is_task_running(task_name):
            self._logger.info(f"刷新任务已在运行，跳过")
            return
            
        # 使用异步任务获取代理列表和总数
        async def fetch_proxies():
            try:
                async with get_session() as session:
                    # 初始化仓储
                    proxy_repo = ProxyRepository(session=session)
                    
                    # 使用find_all方法获取代理列表和总数
                    proxies, count = await proxy_repo.find_all(
                        offset=offset, 
                        limit=limit
                    )
                    
                    return proxies, count
            except Exception as e:
                self._logger.exception(f"获取代理列表失败: {e}")
                return [], 0
                
        # 提交任务到任务管理器
        future = self.task_manager.submit_task(task_name, fetch_proxies)
        
        # 任务提交失败时直接执行
        if not future:
            await fetch_proxies()
    
    async def validate_proxies(self, proxy_ids: List[int]) -> AsyncGenerator[Tuple[int, bool, Optional[float]], None]:
        """验证指定的代理列表
        
        Args:
            proxy_ids: 代理ID列表
            
        Yields:
            Tuple[int, bool, Optional[float]]: (代理ID, 是否有效, 响应时间)
        """
        self._logger.info(f"开始验证指定代理，共 {len(proxy_ids)} 个")
        
        try:
            async with get_session() as session:
                # 初始化仓储
                proxy_repo = ProxyRepository(session=session)
                
                # 获取指定ID的代理
                proxies = []
                for proxy_id in proxy_ids:
                    proxy = await proxy_repo.find_by_id(proxy_id)
                    if proxy:
                        proxies.append(proxy)
                
                if not proxies:
                    self._logger.warning("没有找到指定的代理")
                    return
                
                # 使用核心服务的流式验证功能
                async for proxy_id, is_valid, response_time in self.core_service.validate_proxies_stream(proxies):
                    # 更新代理状态
                    await proxy_repo.update_validation_result(
                        proxy_id, 
                        is_valid, 
                        response_time
                    )
                    await session.commit()
                    
                    # 产出结果
                    yield proxy_id, is_valid, response_time
        except Exception as e:
            self._logger.exception(f"验证指定代理失败: {e}")
            return

    async def has_local_proxies(self) -> bool:
        """检查是否存在本地代理
        
        Returns:
            bool: 是否存在本地代理
        """
        try:
            async with get_session() as session:
                # 初始化仓储
                proxy_repo = ProxyRepository(session=session)
                
                # 查询本地代理数量
                local_count = await proxy_repo.count_local_proxies()
                
                return local_count > 0
        except Exception as e:
            self._logger.exception(f"检查本地代理时出错: {e}")
            return False

    async def _handle_local_proxy_service(self):
        """处理本地代理服务状态
        
        根据当前服务状态决定是启动还是重启服务
        """
        try:
            # 检查服务状态
            is_running = self.get_service_status()
            
            if is_running:
                # 如果服务正在运行，重启服务以应用新配置
                self._logger.info("服务正在运行，重启服务以应用新配置")
                await self.restart_service()
            else:
                # 如果服务未运行，启动服务
                self._logger.info("服务未运行，启动服务")
                await self.start_service()
        except Exception as e:
            self._logger.exception(f"处理本地代理服务状态失败: {e}")
            raise

    def _parse_ip_lines(self, ip_text: str) -> List[Tuple[str, str]]:
        """解析多行IP文本，返回IP范围列表
        
        Args:
            ip_text: IP文本，可能包含多行
        
        Returns:
            List[Tuple[str, str]]: IP范围列表，每个元素为(起始IP, 结束IP)元组
        """
        ip_ranges = []
        try:
            # 先分行，再去除每行所有空格
            lines = [line.strip().replace(' ', '') for line in ip_text.splitlines() if line.strip()]
            for line in lines:
                # 检查IP格式
                if not all(c.isdigit() or c in '-.' for c in line):
                    raise ValueError(f"IP地址只能包含数字和连字符(-.)，错误行: {line}")
                # 检查是否是IP范围
                parts = line.split("-")
                if len(parts) == 2:
                    start_ip = parts[0].strip()
                    end_ip = parts[1].strip()
                    if not end_ip.count('.') == 3:
                        start_parts = start_ip.split('.')
                        if len(start_parts) == 4:
                            end_ip = f"{start_parts[0]}.{start_parts[1]}.{start_parts[2]}.{end_ip}"
                else:
                    start_ip = end_ip = line
                ip_ranges.append((start_ip, end_ip))
            return ip_ranges
        except Exception as e:
            self._logger.exception(f"解析IP文本失败: {e}")
            raise ValueError(f"解析IP文本失败: {str(e)}")

    async def add_proxies_from_text(self, ip_text: str, port_start: int, port_end: int,
                                  username: str, password: str, proxy_type: str, is_local: bool) -> Tuple[int, List[int]]:
        """从文本添加多个代理（一次性批量写入，端口唯一）
        
        Args:
            ip_text: IP文本，可能包含多行
            port_start: 起始端口
            port_end: 结束端口
            username: 用户名
            password: 密码
            proxy_type: 代理类型
            is_local: 是否本地代理
        Returns:
            Tuple[int, List[int]]: (添加的代理数量, 新添加的代理ID列表)
        """
        self._logger.info(f"从文本添加代理: IP文本='{ip_text}', 端口范围={port_start}-{port_end}")
        try:
            ip_ranges = self._parse_ip_lines(ip_text)
            # 展开所有IP
            all_ips = []
            for start_ip, end_ip in ip_ranges:
                if is_local and not start_ip:
                    start_ip = end_ip = "0.0.0.0"
                start = int(ipaddress.IPv4Address(start_ip))
                end = int(ipaddress.IPv4Address(end_ip))
                if start > end:
                    start, end = end, start
                for ip_int in range(start, end + 1):
                    ip = str(ipaddress.IPv4Address(ip_int))
                    all_ips.append(ip)
            # 分配端口，确保每个IP端口唯一
            port = port_start
            port_max = port_end
            proxy_dicts = []
            used_ports = set()
            async with get_session() as session:
                proxy_repo = ProxyRepository(session=session)
                for ip in all_ips:
                    # 查重IP
                    if await proxy_repo.check_host_exists(ip):
                        self._logger.warning(f"代理IP {ip} 已存在，跳过添加")
                        continue
                    # 分配端口，确保唯一且未被占用
                    max_attempts = 1000
                    for _ in range(max_attempts):
                        if port > port_max:
                            port = port_start
                        if port in used_ports:
                            port += 1
                            continue
                        port_exists = await proxy_repo.check_port_exists(port, is_local=True) if is_local else False
                        if not port_exists:
                            used_ports.add(port)
                            break
                        port += 1
                    else:
                        self._logger.warning(f"IP {ip} 无法找到可用端口，跳过")
                        continue
                    proxy_dicts.append({
                        "host": ip,
                        "port": port,
                        "username": username if username else None,
                        "password": password if password else None,
                        "proxy_type": proxy_type.lower(),
                        "is_local": is_local
                    })
                    port += 1
                proxies = await proxy_repo.bulk_add(proxy_dicts)
                await session.commit()
                added_proxy_ids = [p.id for p in proxies if p.id]
                added_count = len(added_proxy_ids)
                if is_local and added_count > 0:
                    await self.core_service.generate_proxy_config(refresh_all=True)
                    await self._handle_local_proxy_service()
                return added_count, added_proxy_ids
        except ValueError as e:
            self._logger.error(f"添加代理失败: {e}")
            raise
        except Exception as e:
            self._logger.exception(f"添加代理失败: {e}")
            return 0, []

    async def delete_all_invalid_proxies(self) -> int:
        """
        删除所有失效代理，返回删除数量
        如果有本地代理被删，重启服务
        """
        self._logger.info("批量删除所有失效代理")
        try:
            async with get_session() as session:
                proxy_repo = ProxyRepository(session=session)
                # 查找所有失效代理
                invalid_proxies = await proxy_repo.find_invalid_proxies()
                if not invalid_proxies:
                    return 0
                has_local = False
                for proxy in invalid_proxies:
                    if proxy.is_local:
                        has_local = True
                        self.core_service.config_manager.update_config(proxy, delete=True)
                    await proxy_repo.delete(proxy.id)
                await session.commit()
                # 统一刷新配置和重启服务
                if has_local and self.get_service_status():
                    await self.core_service.generate_proxy_config(refresh_all=True)
                    await self.restart_service()
                return len(invalid_proxies)
        except Exception as e:
            self._logger.exception(f"批量删除失效代理失败: {e}")
            return 0
