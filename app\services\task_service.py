#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
任务服务模块
负责连接UI和任务管理器，提供任务管理功能
"""

import asyncio
import uuid
from typing import Dict, List, Any, Optional, Tuple, Union, Callable
from datetime import datetime

from PySide6.QtCore import QObject, Signal, Slot

from core.task_manager import TaskBase, TaskStatus, TaskPriority, task_manager
from core.task_manager.tasks import MessageSendingTask
from core.telegram.client_manager import TelegramClientManager
from utils.logger import get_logger


class TaskService(QObject):
    """
    任务服务类
    作为UI和任务管理器之间的桥梁
    """
    
    # 信号定义
    task_added = Signal(str, dict)  # task_id, task_info
    task_removed = Signal(str)  # task_id
    task_status_changed = Signal(str, str)  # task_id, status
    task_progress_updated = Signal(str, int, int, dict)  # task_id, current, total, extra_info
    task_log = Signal(str, str, str)  # task_id, message, level
    
    def __init__(self, client_manager: TelegramClientManager = None):
        """
        初始化任务服务
        
        Args:
            client_manager: Telegram客户端管理器
        """
        super().__init__()
        self._logger = get_logger("app.services.task_service")
        self._client_manager = client_manager
        
        # 连接任务管理器信号
        self._connect_task_manager_signals()
        
        # 启动任务调度器
        asyncio.create_task(task_manager.start_scheduler())
        
        self._logger.info("任务服务已初始化")
    
    def _connect_task_manager_signals(self):
        """连接任务管理器信号"""
        task_manager.task_added.connect(self._on_task_added)
        task_manager.task_removed.connect(self._on_task_removed)
        task_manager.task_status_changed.connect(self._on_task_status_changed)
        task_manager.task_progress_updated.connect(self._on_task_progress_updated)
        task_manager.task_log.connect(self._on_task_log)
    
    def _on_task_added(self, task_id: str):
        """处理任务添加事件"""
        task = task_manager.get_task(task_id)
        if task:
            self.task_added.emit(task_id, task.to_dict())
    
    def _on_task_removed(self, task_id: str):
        """处理任务移除事件"""
        self.task_removed.emit(task_id)
    
    def _on_task_status_changed(self, task_id: str, status: str):
        """处理任务状态变化事件"""
        self.task_status_changed.emit(task_id, status)
    
    def _on_task_progress_updated(self, task_id: str, current: int, total: int, extra_info: dict):
        """处理任务进度更新事件"""
        self.task_progress_updated.emit(task_id, current, total, extra_info)
    
    def _on_task_log(self, task_id: str, message: str, level: str):
        """处理任务日志事件"""
        self.task_log.emit(task_id, message, level)
    
    async def create_message_sending_task(self, task_db_id: int, batch_size: int = 100, 
                                         auto_start: bool = False, priority: TaskPriority = TaskPriority.NORMAL) -> str:
        """
        创建消息群发任务
        
        Args:
            task_db_id: 数据库中的任务ID
            batch_size: 每批处理的目标数量
            auto_start: 是否自动启动任务
            priority: 任务优先级
            
        Returns:
            str: 任务ID
        """
        if not self._client_manager:
            self._logger.error("未配置客户端管理器，无法创建消息群发任务")
            return ""
        
        task_id = str(uuid.uuid4())
        task = MessageSendingTask(
            task_id=task_id,
            task_db_id=task_db_id,
            client_manager=self._client_manager,
            batch_size=batch_size,
            priority=priority
        )
        
        self._logger.info(f"创建消息群发任务: {task_id} [DB ID: {task_db_id}]")
        return await task_manager.add_task(task, auto_start)
    
    async def start_task(self, task_id: str) -> bool:
        """
        启动任务
        
        Args:
            task_id: 任务ID
            
        Returns:
            bool: 是否成功启动
        """
        self._logger.info(f"启动任务: {task_id}")
        return await task_manager.start_task(task_id)
    
    async def pause_task(self, task_id: str) -> bool:
        """
        暂停任务
        
        Args:
            task_id: 任务ID
            
        Returns:
            bool: 是否成功暂停
        """
        self._logger.info(f"暂停任务: {task_id}")
        return await task_manager.pause_task(task_id)
    
    async def resume_task(self, task_id: str) -> bool:
        """
        恢复任务
        
        Args:
            task_id: 任务ID
            
        Returns:
            bool: 是否成功恢复
        """
        self._logger.info(f"恢复任务: {task_id}")
        return await task_manager.resume_task(task_id)
    
    async def stop_task(self, task_id: str) -> bool:
        """
        停止任务
        
        Args:
            task_id: 任务ID
            
        Returns:
            bool: 是否成功停止
        """
        self._logger.info(f"停止任务: {task_id}")
        return await task_manager.stop_task(task_id)
    
    async def remove_task(self, task_id: str) -> bool:
        """
        移除任务
        
        Args:
            task_id: 任务ID
            
        Returns:
            bool: 是否成功移除
        """
        self._logger.info(f"移除任务: {task_id}")
        return await task_manager.remove_task(task_id)
    
    async def stop_all_tasks(self) -> None:
        """停止所有任务"""
        self._logger.info("停止所有任务")
        await task_manager.stop_all_tasks()
    
    def get_task_info(self, task_id: str) -> Optional[Dict[str, Any]]:
        """
        获取任务信息
        
        Args:
            task_id: 任务ID
            
        Returns:
            Optional[Dict[str, Any]]: 任务信息字典，如果不存在则返回None
        """
        task = task_manager.get_task(task_id)
        if task:
            return task.to_dict()
        return None
    
    def get_all_tasks(self) -> List[Dict[str, Any]]:
        """
        获取所有任务信息
        
        Returns:
            List[Dict[str, Any]]: 所有任务信息的列表
        """
        return [task.to_dict() for task in task_manager.get_all_tasks().values()]
    
    def get_running_tasks(self) -> List[Dict[str, Any]]:
        """
        获取所有正在运行的任务信息
        
        Returns:
            List[Dict[str, Any]]: 正在运行的任务信息列表
        """
        return [task.to_dict() for task in task_manager.get_running_tasks().values()]
    
    def get_task_count(self) -> Dict[str, int]:
        """
        获取各状态任务的数量
        
        Returns:
            Dict[str, int]: 各状态任务数量
        """
        return task_manager.get_task_count()
    
    async def cleanup(self):
        """清理资源"""
        self._logger.info("清理任务服务资源")
        await task_manager.stop_all_tasks()
        await task_manager.stop_scheduler()


# 创建全局任务服务实例
task_service = None

def init_task_service(client_manager: TelegramClientManager = None) -> TaskService:
    """
    初始化任务服务
    
    Args:
        client_manager: Telegram客户端管理器
        
    Returns:
        TaskService: 任务服务实例
    """
    global task_service
    if task_service is None:
        task_service = TaskService(client_manager)
    return task_service

def get_task_service() -> Optional[TaskService]:
    """
    获取任务服务实例
    
    Returns:
        Optional[TaskService]: 任务服务实例，如果未初始化则返回None
    """
    return task_service 