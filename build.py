import os
import multiprocessing


# https://blog.csdn.net/qq_25262697/article/details/129302819
# https://www.cnblogs.com/happylee666/articles/16158458.html
args = [
    'nuitka',
    #'--standalone', # 独立模式
    '--onefile', # 单文件模式
    '--windows-console-mode=disable',    # 禁用控制台，修改过，如果出错调整这里 新版--windows-console-mode=disable，需要测试 
    '--plugin-enable=pyside6,upx',
    r'--upx-binary=H:\PyProject\TeleTest\upx\upx.exe',
    '--include-qt-plugins=sensible,styles',
    '--msvc=latest',
    '--show-memory',
    '--show-progress',
    '--windows-icon-from-ico=ui/resources/images/logo.png',
    #'--follow-imports', #导入
    # 精确控制模块导入
    
    '--follow-import-to=core,ui,utils,data,app,frameConf,asyncio,wmi,aiohttp_socks,'
    'opentele,loguru,datetime,dotenv,Crypto,pathlib,shutil,qasync,sqlalchemy,telethon,ntplib,dotenv',
    '--include-package=ntplib',
    '--include-package=python_socks', # 排除python_socks包
    "--include-package=aiohttp", # 包含aiohttp包
    '--include-package=aiohttp_socks', # 包含aiohttp_socks包
    '--include-package=sqlalchemy',
    '--include-package=telethon',
    '--include-package=opentele',
    '--include-package=qasync',   # 包含qasync包
    '--include-package=asyncio',  # 包含asyncio包
    '--include-package=datetime',


    '--include-package=PySide6.QtCore,PySide6.QtGui,PySide6.QtWidgets,PySide6.QtSvg',  # 只包含必要的Qt模块

    '--nofollow-import-to=PySide6.QtSql,PySide6.QtPrintSupport,PySide6.QtWebEngineWidgets,PySide6.QtPdf',  # 排除不需要的Qt模块
    


    
    # 加速编译的关键选项
    #'--low-memory',  # 低内存模式，减少内存使用
    '--remove-output',  # 删除中间文件
    
    # 优化编译过程
    '--lto=no',  # 关闭链接时优化，加快编译速度
    '--jobs=' + str(multiprocessing.cpu_count()),  # 使用所有CPU核心

    
    # 其他优化 
    '--disable-dll-dependency-cache',  # 禁用DLL缓存，加快编译
    # '--assume-yes-for-downloads',  # 自动下载依赖，避免中断
    #'--quiet',  # 减少输出信息
    
    # 输出目录
    '--output-dir=dist/teletool7',
    'main.py',
]
import time    
start = time.time()
os.system(' '.join(args))
 
print("打包完成，耗时：{}分".format(int(time.time() - start)/60))
