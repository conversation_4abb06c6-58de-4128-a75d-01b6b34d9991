"""
配置管理模块 - 提供统一的配置访问接口
负责读取.env配置文件，管理APPDATA目录下的持久化存储
"""

import os
import sys
import pathlib
import platform
from typing import Any, Dict, Optional, List
from dotenv import load_dotenv

class Config:
    """配置管理类，单例模式"""
    _instance = None
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(Config, cls).__new__(cls)
            cls._instance._initialize()
        return cls._instance
    
    def _initialize(self) -> None:
        """初始化配置"""
        # 加载.env文件
        load_dotenv()
        
        # 设置应用数据目录
        self.app_name = "TelegramManager"
        self._set_app_data_dir()
        
        # 确保必要的目录存在
        self._ensure_directories()
        
        # 加载配置
        self._config = {}
        self._load_config()
        
    # def _set_app_data_dir(self) -> None:
    #     """设置应用数据目录，根据不同操作系统"""
    #     system = platform.system()
        
    #     if system == "Windows":
    #         app_data = os.environ.get("APPDATA")
    #         self.app_data_dir = pathlib.Path(app_data) / self.app_name
    #     elif system == "Darwin":  # macOS
    #         app_data = pathlib.Path.home() / "Library" / "Application Support"
    #         self.app_data_dir = app_data / self.app_name
    #     else:  # Linux和其他系统
    #         app_data = pathlib.Path.home() / ".config"
    #         self.app_data_dir = app_data / self.app_name
    #     print(self.app_data_dir)

    def _set_app_data_dir(self) -> None:
        """设置应用数据目录，放在项目文件夹下的APPDATA目录"""
        # 获取应用根目录 - 通用方案
        # 首先尝试获取当前执行文件的目录
        base_dir = pathlib.Path(sys.argv[0]).resolve().parent
        # 设置项目根目录和应用数据目录
        self.project_root = base_dir.absolute()
        self.app_data_dir = self.project_root / "APPDATA"
        print(f"应用数据目录: {self.app_data_dir}")
        
    def _ensure_directories(self) -> None:
        """确保必要的目录存在"""
        # 应用数据根目录
        self.app_data_dir.mkdir(parents=True, exist_ok=True)
        
        # Sessions目录
        self.sessions_dir = self.app_data_dir / "sessions"
        self.sessions_dir.mkdir(exist_ok=True)
        
        # 数据库目录
        self.db_dir = self.app_data_dir / "database"
        self.db_dir.mkdir(exist_ok=True)

        # 日志目录
        self.logs_dir = self.app_data_dir / "logs"
        self.logs_dir.mkdir(exist_ok=True)
    
    def _load_config(self) -> None:
        """从环境变量加载配置"""
        # Telegram API配置
        api_id_str = os.environ.get("API_ID")
        self._config["api_id"] = int(api_id_str) if api_id_str else None
        self._config["api_hash"] = os.environ.get("API_HASH")
        
        # 数据库配置
        db_path = os.environ.get("DATABASE_PATH")
        if db_path:
            self._config["database_path"] = db_path
        else:
            self._config["database_path"] = str(self.db_dir / "telegram_manager.db")
        #3proxy配置文件
        self._config['3proxy_cfg'] =str(self.app_data_dir / "3proxy.cfg")
        self._config['proxy_log'] =str(self.logs_dir / "3proxy.log")
        # 3proxy可执行文件路径 - 修改为使用项目根目录
        self._config['proxy_exe'] = str(self.project_root / "3proxy" / "bin64" / "3proxy.exe")
        
        # 将database_path同时赋值给db_path以兼容代码
        self._config["db_path"] = self._config["database_path"]
        
        # 路径配置
        self._config["sessions_path"] = str(self.sessions_dir)
        self._config["logs_path"] = str(self.logs_dir)
        
        # 日志配置
        self._config["log_level"] = os.environ.get("LOG_LEVEL", "INFO")
        self._config["log_rotation"] = os.environ.get("LOG_ROTATION", "10 MB")
        self._config["log_retention"] = os.environ.get("LOG_RETENTION", "30 days")
        
        # 模块日志级别配置
        self._config["module_log_levels"] = {
            "core.telegram": os.environ.get("LOG_LEVEL_TELEGRAM", "INFO"),
            "data.database": os.environ.get("LOG_LEVEL_DATABASE", "WARNING"),
            "app.controllers": os.environ.get("LOG_LEVEL_CONTROLLERS", "INFO")
        }
        
        # 代理验证配置
        self._config["proxy_check_timeout"] = int(os.environ.get("PROXY_CHECK_TIMEOUT", "10"))
        # 代理验证测试URL列表
        proxy_test_urls = os.environ.get("PROXY_TEST_URLS", "https://web.telegram.org,https://core.telegram.org")
        self._config["proxy_test_urls"] = [url.strip() for url in proxy_test_urls.split(",")]
        # 批量验证时每批次的代理数量
        self._config["proxy_batch_size"] = int(os.environ.get("PROXY_BATCH_SIZE", "10"))
        
        # 其他配置
        self._config["client_connection_timeout"] = int(os.environ.get("CLIENT_CONNECTION_TIMEOUT", "30"))
        self._config["inactive_client_disconnect_time"] = int(os.environ.get("INACTIVE_CLIENT_DISCONNECT_TIME", "600"))

        # Telegram操作超时配置
        self._config["telegram_profile_update_timeout"] = int(os.environ.get("TELEGRAM_PROFILE_UPDATE_TIMEOUT", "30"))
        self._config["telegram_avatar_upload_timeout"] = int(os.environ.get("TELEGRAM_AVATAR_UPLOAD_TIMEOUT", "90"))
        self._config["telegram_batch_operation_timeout"] = int(os.environ.get("TELEGRAM_BATCH_OPERATION_TIMEOUT", "120"))
    
        #同时登录的最大数量
        self._config["max_login_count"] = int(os.environ.get("MAX_LOGIN_COUNT", "5"))
    def get(self, key: str, default: Any = None) -> Any:
        """获取配置项"""
        return self._config.get(key, default)
    
    def set(self, key: str, value: Any) -> None:
        """设置配置项"""
        self._config[key] = value
    
    def get_all(self) -> Dict[str, Any]:
        """获取所有配置"""
        return self._config.copy()
    
    @property
    def database_url(self) -> str:
        """获取数据库URL"""
        return f"sqlite:///{self.get('database_path')}"
    
    @property
    def api_id(self) -> Optional[int]:
        """获取API ID"""
        return self.get("api_id")
    
    @property
    def api_hash(self) -> Optional[str]:
        """获取API Hash"""
        return self.get("api_hash")
    @property
    def proxy_cfg(self) -> str:
        return  self.get("3proxy_cfg")
    
    @property
    def proxy_log(self) -> str:
        return  self.get("proxy_log")

    @property
    def proxy_exe(self) -> str:
        """获取3proxy可执行文件路径"""
        return self.get("proxy_exe")

    @property
    def sessions_path(self) -> str:
        """获取会话路径"""
        return self.get("sessions_path")
    @property
    def max_login_count(self) -> int:
        """获取同时登录的最大数量"""
        return self.get("max_login_count", 5)
    @property
    def proxy_test_urls(self) -> List[str]:
        """获取代理测试URL列表"""
        return self.get("proxy_test_urls", ["http://httpbin.org/ip"])
        
    @property
    def proxy_batch_size(self) -> int:
        """获取代理批量验证的批次大小"""
        return self.get("proxy_batch_size", 10)

    def get_log_config(self) -> Dict[str, Any]:
        """获取日志配置"""
        return {
            "log_dir": self.get("logs_path"),
            "default_level": self.get("log_level"),
            "rotation": self.get("log_rotation"),
            "retention": self.get("log_retention")
        }
    
    def get_module_log_levels(self) -> Dict[str, str]:
        """获取模块日志级别配置"""
        return self.get("module_log_levels", {})

# 创建全局配置实例
config = Config()
'''
用法
'''
