#!/usr/bin/python3
# -*- coding: utf-8 -*-
"""
认证模块服务 - 整合其他模块提供完整的认证功能
"""

import json
import time
from typing import Dict, Any, Tuple

from utils.client_http import auth_client
from utils.crypto import default_aes, signature_util
from utils.hardware import hardware_collector
from core.auth.models import software_config,user_info,SoftwareConfig, UserInfo
from utils.logger import get_logger




class ApiService:
    """认证服务类，整合其他模块提供完整的认证功能"""
    
    def __init__(self):
        """初始化认证服务"""
        self.config:SoftwareConfig = software_config
        self.user_info:UserInfo = user_info
        # 获取模块日志记录器
        self._logger = get_logger("core.auth.service")
    
    async def init_software(self, domain: str, app_id: str, key: str, ver_index: str, version: str) -> <PERSON><PERSON>[bool, Dict[str, Any]]:
        """
        初始化软件配置
        
        Args:
            domain: API域名
            app_id: 应用ID
            key: 密钥
            ver_index: 版本索引
            version: 版本号
            
        Returns:
            初始化结果元组 (成功标志, 结果数据)
        """
      
        
        # 更新配置
        self.config.update(domain, app_id, key, ver_index, version)
        
        # 更新客户端请求头
        auth_client.update_headers(self.config.headers)
        
        # 发送初始化请求
        url = self.config.web_url + 'ini'
        params = {}
        self._logger.debug(f"发送初始化请求: {url}, params={params}")
        result = await auth_client.get(url, params)
        #self._logger.debug(result)
        if "error" in result:
            self._logger.error(f"软件初始化失败: {result['error']}")
            return False, result
        # 解密data字段
        if 'data' in result:
            try:
                data = self.decrypt_data(result['data'])
                self.config.update_from_dict(data)
                self._logger.debug(f"初始化数据解密成功: {data}")
            except Exception as e:
                self._logger.error(f"初始化数据解密失败: {e}")
        #self._logger.info(f"软件初始化成功: {result}")
        return True, result
    
    async def _prepare_encrypted_params(self, data: Dict[str, Any]) -> Dict[str, str]:
        """
        准备加密的请求参数
        
        Args:
            data: 原始参数
            
        Returns:
            加密后的参数
        """
        # 转换为查询字符串
        query_string = signature_util.dict_to_query_string(data)
        
        # 生成签名
        sign = signature_util.generate_sign(query_string, self.config.key)
        
        # 加密数据
        encrypted_data = default_aes.encode(query_string)
        
        return {
            'data': encrypted_data,
            'sign': sign
        }
    
    async def _verify_response(self, result: Dict[str, Any]) -> bool:
        """
        验证响应签名
        
        Args:
            result: 响应数据
            
        Returns:
            签名是否有效
        """
        if "error" in result:
            return False
        
        return signature_util.verify_sign(
            result['code'],
            result['time'],
            self.config.key,
            result['sign']
        )
    
    async def login(self, account: str, password: str, uuid: str) -> Dict[str, Any]:
        """
        用户登录
        
        Args:
            account: 账号
            password: 密码
            
        Returns:
            登录结果
        """
        self._logger.info(f"用户登录: account={account}")
        url = self.config.web_url + 'logon'
        time_stamp = int(time.time())
        
        # 准备请求数据
        data = {
            'account': account,
            'password': password,
            'udid': uuid,
            'time': time_stamp
        }
        
        # 加密参数
        params = await self._prepare_encrypted_params(data)
        
        # 发送请求
        result = await auth_client.post(url, params)
      
        try:
            if "error" in result:
                self._logger.error(f"登录失败: {result['error']}")
                return {"success": False, "message": result["error"]}
            
            # 验证签名
            if not await self._verify_response(result):
                self._logger.error("登录失败: 签名验证失败")
                return {"success": False, "message": "签名验证失败"}
            
            # 处理登录成功
            if result['code'] == 200 and result['msg'] == "登录成功":
                user_data = self.decrypt_data(result['data'])
                
                self.user_info.update_from_dict(user_data)
                #self._logger.info("登录成功")
                return {"success": True, "data": user_data}
        except Exception as e:
            # 登录失败
            self._logger.warning(f"登录失败: {e}")
            return {"success": False, "message": str(e)}
    
    async def logout(self) -> Dict[str, Any]:
        """
        用户登出
        
        Returns:
            登出结果
        """
        self._logger.info("用户登出")
        url = self.config.web_url + 'logout'
        time_stamp = int(time.time())
        
        # 准备请求数据
        data = {
            'token': self.user_info.token,
            'udid': hardware_collector.get_machine_code(),
            'time': time_stamp
        }
        
        # 加密参数
        params = await self._prepare_encrypted_params(data)
        
        # 发送请求
        result = await auth_client.post(url, params)
        
        if "error" in result:
            self._logger.error(f"登出失败: {result['error']}")
            return {"success": False, "message": result["error"]}
        
        # 验证签名
        if not await self._verify_response(result):
            self._logger.error("登出失败: 签名验证失败")
            return {"success": False, "message": "签名验证失败"}
        
        # 清除用户信息
        self.user_info.clear()
        
        self._logger.info("登出成功")
        return {"success": True, "message": result.get('msg', '登出成功')}
    
    async def register(self, account: str, password: str, code: str, invid: str = "", udid: str = "") -> Dict[str, Any]:
        """
        用户注册
        
        Args:
            account: 账号（手机号或邮箱）
            password: 密码
            code: 验证码
            invid: 邀请人ID
            udid: 机器码
            
        Returns:
            注册结果
        """
        self._logger.info(f"用户注册: account={account}, invid={invid}, code={code}")
        url = self.config.web_url + 'reg'
        
        # 如果未提供机器码，则生成一个
        if not udid:
            udid = hardware_collector.get_machine_code()
        
        # 准备请求数据
        data = {
            'account': account,
            'password': password,
            'code': code,
            'udid': udid,
            'time': int(time.time()),
        }
        
        # 如果有邀请码，添加到请求中
        if invid:
            data['invid'] = invid
        
        # 加密参数
        params = await self._prepare_encrypted_params(data)
        
        # 发送请求
        result = await auth_client.post(url, params)
        
        if "error" in result:
            self._logger.error(f"注册失败: {result['error']}")
            return {"success": False, "message": result["error"]}
        
        # 验证签名
        if not await self._verify_response(result):
            self._logger.error("注册失败: 签名验证失败")
            return {"success": False, "message": "签名验证失败"}
        
        self._logger.info(f"注册结果: {result['msg']}")
        return {
            "success": result['code'] == 200,
            "message": result['msg']
        }
    
    async def recharge(self, card_code: str) -> Dict[str, Any]:
        """
        卡密充值
        
        Args:
            card_code: 充值卡密
            
        Returns:
            充值结果
        """
        self._logger.info("卡密充值")
        url = self.config.web_url + 'recharge'
        
        # 准备请求数据
        data = {
            'token': self.user_info.token,
            'card': card_code,
            'udid': hardware_collector.get_machine_code(),
            'time': int(time.time()),
        }
        
        # 加密参数
        params = await self._prepare_encrypted_params(data)
        
        # 发送请求
        result = await auth_client.post(url, params)
        
        if "error" in result:
            self._logger.error(f"充值失败: {result['error']}")
            return {"success": False, "message": result["error"]}
        
        # 验证签名
        if not await self._verify_response(result):
            self._logger.error("充值失败: 签名验证失败")
            return {"success": False, "message": "签名验证失败"}
        
        self._logger.info(f"充值结果: {result['msg']}")
        return {
            "success": result['code'] == 200,
            "message": result['msg']
        }
    
    async def heartbeat(self) -> Dict[str, Any]:
        """
        心跳包，用于维持登录状态
        
        Returns:
            心跳结果
        """
        self._logger.debug("发送心跳")
        url = self.config.web_url + 'heartbeat'
        
        # 准备请求数据
        data = {
            'token': self.user_info.token,
            'udid': hardware_collector.get_machine_code(),
            'time': int(time.time()),
        }
        
        # 加密参数
        params = await self._prepare_encrypted_params(data)
        
        # 发送请求
        result = await auth_client.post(url, params)
        
        if "error" in result:
            self._logger.error(f"心跳失败: {result['error']}")
            return {"success": False, "message": result["error"]}
        
        # 验证签名
        if not await self._verify_response(result):
            self._logger.error("心跳失败: 签名验证失败")
            return {"success": False, "message": "签名验证失败"}
        
        return {
            "success": result['code'] == 200,
            "message": result.get('msg', '')
        }
    
    async def get_user_info(self) -> Dict[str, Any]:
        """
        获取用户信息
        
        Returns:
            用户信息
        """
        self._logger.info("获取用户信息")
        url = self.config.web_url + 'info'
        
        # 准备请求数据
        data = {
            'token': self.user_info.token,
            'time': int(time.time()),
        }
        try:
             # 加密参数
            params = await self._prepare_encrypted_params(data)
            
            # 发送请求
            result = await auth_client.post(url, params)
            
            if "error" in result:
                self._logger.error(f"获取用户信息失败: {result['error']}")
                return {"success": False, "message": result["error"]}
            
            # 验证签名
            if not await self._verify_response(result):
                self._logger.error("获取用户信息失败: 签名验证失败")
                return {"success": False, "message": "签名验证失败"}
            
            if result['code'] == 200:
                user_data = self.decrypt_data(result['data'])
                #self.user_info.update_from_dict(user_data)
                #self._logger.debug(user_data)
                self._logger.info("获取用户信息成功")
                return {"success": True, "data": user_data}
        except Exception as e:
            self._logger.warning(f"获取用户信息失败: {e}")
            return {"success": False, "message": str(e)}


    async def get_software_info(self)->Dict[str,Any]:
        """
        获取软件信息
        
        Returns:
            软件信息
        """
        self._logger.info("获取软件信息")
        url = self.config.web_url + 'info'
        
        # 准备请求数据
        data = {
            'token': self.user_info.token,
            'time': int(time.time()),
        }
        
        # 加密参数
        params = await self._prepare_encrypted_params(data)
        
        

    def decrypt_data(self, hex_data: str) -> dict:
        """
        解密接口返回的加密data字段
        Args:
            hex_data: 十六进制加密字符串
        Returns:
            解密后的字典
        """
        try:
            decrypted = default_aes.decode(hex_data)
            return json.loads(decrypted)
        except Exception as e:
            self._logger.error(f"解密data失败: {e}")
            return {}

    async def verify_vip(self) -> Dict[str, Any]:
        """
        会员验证接口
        URL: /vip
        POST, 加密、签名同其他接口
        参数: token
        """
        self._logger.info("会员验证接口调用")
        url = self.config.web_url + 'vip'
        data = {
            'token': self.user_info.token,
            'time': int(time.time()),
        }
        params = await self._prepare_encrypted_params(data)
        result = await auth_client.post(url, params)

        if result['code'] == 200:
            now_time =result['time']
            #data = self.decrypt_data(result['data'])
         
            self._logger.info("会员验证成功")
            return {"success": True, "data": result['time'],'message':result['msg']}
        self._logger.warning(f"会员验证失败: {result['msg']}")
        return {"success": False, "message": result['msg']}

    async def kami_topup(self, kami: str) -> Dict[str, Any]:
        """
        卡密充值接口
        URL: /kamiTopup
        POST, 加密、签名同其他接口
        参数: token, kami
        """
        self._logger.info("卡密充值接口调用")
        url = self.config.web_url + 'kamiTopup'
        data = {
            'token': self.user_info.token,
            'kami': kami,
            'time': int(time.time()),
        }
        params = await self._prepare_encrypted_params(data)
        result = await auth_client.post(url, params)
     
        if "error" in result:
            self._logger.error(f"卡密充值失败: {result['error']}")
            return {"success": False, "message": result["error"]}
        if not await self._verify_response(result):
            self._logger.error("卡密充值失败: 签名验证失败")
            return {"success": False, "message": "签名验证失败"}
        if result['code'] !=200:
            self._logger.warning(f"卡密充值失败: {result['msg']}")
            return {"success": False, "message": result['msg']}
        if result['code'] == 200:
            self._logger.info("卡密充值成功")
            return {"success": True, "data": data}
        self._logger.warning(f"卡密充值失败: {result['msg']}")
        return {"success": False, "message": result['msg']}





api_service = ApiService()
