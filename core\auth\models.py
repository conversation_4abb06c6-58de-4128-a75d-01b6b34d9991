#!/usr/bin/python3
# -*- coding: utf-8 -*-
"""
认证模块数据模型 - 定义认证相关的数据结构
"""

class SoftwareConfig:
    """软件配置类，存储软件认证相关参数"""
    
    def __init__(self):
        self.domain = ''  # webAPI地址
        self.app_id = ''  # 软件ID
        self.key = ''  # 通讯密钥
        self.ver_index = ''  # 版本索引
        self.version = ''  # 客户端版本
        self.web_url = ''  # 完整API地址
        self.server_rule = ''  # 服务端签名算法
        self.module_md5 = ''  # 模块MD5值
        self.client_id = ''  # 客户端运行ID
        self.auto_heartbeat = False  # 自动心跳
        self.notice = {}    #公告   {'id': '2', 'content': '公告内容测试3', 'time': '1747905580'}
        self.new_content = ""  #更新内容
        self.new_url = ""   #下载链接
        self.headers = {
            'Content-Type': 'application/json',
        }
    
    def update(self, domain: str, app_id: str, key: str, ver_index: str, version: str) -> None:
        """
        更新软件配置
        
        Args:
            domain: API域名
            app_id: 应用ID
            key: 密钥
            ver_index: 版本索引
            version: 版本号
        """
        self.domain = domain
        self.app_id = app_id
        self.key = key
        self.ver_index = ver_index
        self.version = version
        self.web_url = f"{domain}api/user/{app_id}/{ver_index}/{version}/"
        
    def update_from_dict(self,data:dict) -> None:
        # 兼容多种公告结构
        self.new_url = data.get('new_url', '')
        self.new_content = data.get('new_content', '')
        self.notice = data.get('notice', '')
class UserInfo:
    """用户信息类，存储当前登录用户的信息"""
    
    def __init__(self):
        self.token = ''  # 用户token
        self.uid = ''  # 用户ID
        self.phone = ''  # 手机号
        self.email = ''  # 邮箱
        self.name = ''  # 用户名
        self.account = ''   #  账号
        self.invID = ''     #  邀请人ID
        self.jifen = '' # 积分
        self.vipExpTime = ''    #  会员到期时间
        self.vipExpDate = ''    #  会员到期时间
    def update_from_dict(self, data: dict) -> None:
        """
        从字典更新用户信息
        
        Args:
            data: 包含用户信息的字典
        """
        self.token = data.get('token', '')
        data = data.get('info', '')
        
        self.uid = data.get('uid', '')
        self.name = data.get('name', '')
        self.phone = data.get('phone', '')
        self.email = data.get('email', '')
        self.account = data.get('acctno', '')
        self.invID  = data.get('invID', '')
        self.jifen = data.get('fen', '')
        self.vipExpTime = data.get('vipExpTime', '')
        self.vipExpDate = data.get('vipExpDate', '')
    def clear(self) -> None:
        """清除用户信息"""
        self.token = ''
        self.uid = ''
        self.phone = ''
        self.email = ''
        self.name = '' 


software_config = SoftwareConfig()
user_info = UserInfo()
