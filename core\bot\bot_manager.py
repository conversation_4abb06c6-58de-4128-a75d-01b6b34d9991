import asyncio
from typing import List, Optional, Dict, Any
from telethon import TelegramClient
from telethon.errors import ApiIdInvalidError, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, PhoneNumberInvalidError, rpcerrorlist

from utils.logger import get_logger
from core.entities.bot import Bo<PERSON>
from data.repositories.bot_repository import BotRepository
from data.repositories.association_repository import AssociationRepository
from data.database.db_context import get_db_session

class BotManager:
    """机器人管理业务层"""
    
    def __init__(self):
        """初始化机器人管理器"""
        self._logger = get_logger("core.bot.bot_manager")
        self._api_id = None  # 实际应用中从配置获取
        self._api_hash = None  # 实际应用中从配置获取
    
    async def verify_bot_token(self, token: str) -> Dict[str, Any]:
        """
        验证机器人Token是否有效
        
        Args:
            token: 机器人Token
            
        Returns:
            Dict[str, Any]: 验证结果，包含成功标志和消息
        """
        try:
            # 使用Telethon验证Token
            bot_client = TelegramClient('bot_session', self._api_id, self._api_hash)
            await bot_client.start(bot_token=token)
            
            # 获取机器人信息
            bot_info = await bot_client.get_me()
            
            # 关闭客户端
            await bot_client.disconnect()
            
            return {
                "success": True,
                "message": "Token验证成功",
                "data": {
                    "username": bot_info.username,
                    "first_name": bot_info.first_name,
                    "id": bot_info.id
                }
            }
        except (ApiIdInvalidError, AuthKeyError, PhoneNumberInvalidError, rpcerrorlist.ApiIdInvalidError):
            return {
                "success": False,
                "message": "无效的API ID或API Hash",
                "data": None
            }
        except Exception as e:
            self._logger.error(f"验证Token失败: {e}")
            return {
                "success": False,
                "message": f"验证失败: {str(e)}",
                "data": None
            }
    
    async def add_bot(self, bot_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        添加机器人
        
        Args:
            bot_data: 机器人数据，包含token和username
            
        Returns:
            Dict[str, Any]: 添加结果，包含成功标志和消息
        """
        try:
            bot = Bot(
                token=bot_data.get("token"),
                username=bot_data.get("username"),
                update_method=bot_data.get("update_method", "webhook")
            )
            
            # 验证Token
            verify_result = await self.verify_bot_token(bot.token)
            if not verify_result["success"]:
                return {
                    "success": False,
                    "message": verify_result["message"],
                    "data": None
                }
            
            # 检查是否已存在
            async with get_db_session() as session:
                bot_repo = BotRepository(session)
                existing_bot = await bot_repo.get_by_token(bot.token)
                
                if existing_bot:
                    return {
                        "success": False,
                        "message": "该机器人Token已存在",
                        "data": None
                    }
                
                # 添加机器人
                added_bot = await bot_repo.add(bot)
                
                return {
                    "success": True,
                    "message": "机器人添加成功",
                    "data": added_bot.bot_info
                }
        except Exception as e:
            self._logger.error(f"添加机器人失败: {e}")
            return {
                "success": False,
                "message": f"添加失败: {str(e)}",
                "data": None
            }
    
    async def delete_bot(self, bot_id: int) -> Dict[str, Any]:
        """
        删除机器人
        
        Args:
            bot_id: 机器人ID
            
        Returns:
            Dict[str, Any]: 删除结果，包含成功标志和消息
        """
        try:
            async with get_db_session() as session:
                # 删除关联
                assoc_repo = AssociationRepository(session)
                await assoc_repo.delete_by_bot_id(bot_id)
                
                # 删除机器人
                bot_repo = BotRepository(session)
                result = await bot_repo.delete(bot_id)
                
                if result:
                    return {
                        "success": True,
                        "message": "机器人删除成功",
                        "data": None
                    }
                else:
                    return {
                        "success": False,
                        "message": "未找到该机器人",
                        "data": None
                    }
        except Exception as e:
            self._logger.error(f"删除机器人失败: {e}")
            return {
                "success": False,
                "message": f"删除失败: {str(e)}",
                "data": None
            }
    
    async def update_bot(self, bot_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        更新机器人
        
        Args:
            bot_data: 机器人数据，包含id、token和username
            
        Returns:
            Dict[str, Any]: 更新结果，包含成功标志和消息
        """
        try:
            bot_id = bot_data.get("id")
            if not bot_id:
                return {
                    "success": False,
                    "message": "缺少机器人ID",
                    "data": None
                }
            
            async with get_db_session() as session:
                bot_repo = BotRepository(session)
                
                # 检查是否存在
                existing_bot = await bot_repo.get_by_id(bot_id)
                if not existing_bot:
                    return {
                        "success": False,
                        "message": "未找到该机器人",
                        "data": None
                    }
                
                # 更新数据
                existing_bot.username = bot_data.get("username", existing_bot.username)
                if "update_method" in bot_data:
                    existing_bot.update_method = bot_data.get("update_method")
                
                # 如果Token变了，需要验证
                if "token" in bot_data and bot_data["token"] != existing_bot.token:
                    verify_result = await self.verify_bot_token(bot_data["token"])
                    if not verify_result["success"]:
                        return {
                            "success": False,
                            "message": verify_result["message"],
                            "data": None
                        }
                    existing_bot.token = bot_data["token"]
                
                # 更新机器人
                updated_bot = await bot_repo.update(existing_bot)
                
                return {
                    "success": True,
                    "message": "机器人更新成功",
                    "data": updated_bot.bot_info
                }
        except Exception as e:
            self._logger.error(f"更新机器人失败: {e}")
            return {
                "success": False,
                "message": f"更新失败: {str(e)}",
                "data": None
            }
    
    async def get_all_bots(self) -> Dict[str, Any]:
        """
        获取所有机器人
        
        Returns:
            Dict[str, Any]: 结果，包含成功标志和机器人列表
        """
        try:
            async with get_db_session() as session:
                bot_repo = BotRepository(session)
                bots = await bot_repo.get_all()
                
                return {
                    "success": True,
                    "message": "获取成功",
                    "data": [bot.bot_info for bot in bots]
                }
        except Exception as e:
            self._logger.error(f"获取所有机器人失败: {e}")
            return {
                "success": False,
                "message": f"获取失败: {str(e)}",
                "data": []
            }
    
    async def get_bot_by_id(self, bot_id: int) -> Dict[str, Any]:
        """
        根据ID获取机器人
        
        Args:
            bot_id: 机器人ID
            
        Returns:
            Dict[str, Any]: 结果，包含成功标志和机器人信息
        """
        try:
            async with get_db_session() as session:
                bot_repo = BotRepository(session)
                bot = await bot_repo.get_by_id(bot_id)
                
                if bot:
                    return {
                        "success": True,
                        "message": "获取成功",
                        "data": bot.bot_info
                    }
                else:
                    return {
                        "success": False,
                        "message": "未找到该机器人",
                        "data": None
                    }
        except Exception as e:
            self._logger.error(f"获取机器人失败: {e}")
            return {
                "success": False,
                "message": f"获取失败: {str(e)}",
                "data": None
            } 