from typing import List, Optional, Dict, Any, Tuple
import asyncio

from utils.logger import get_logger
from core.entities.group import Group, EntityType
from core.entities.association import Association
from data.repositories.group_repository import GroupRepository
from data.repositories.association_repository import AssociationRepository
from data.database.db_context import get_db_session

class GroupManager:
    """群组管理业务层"""
    
    def __init__(self):
        """初始化群组管理器"""
        self._logger = get_logger("core.bot.group_manager")
    
    async def add_group(self, group_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        添加群组
        
        Args:
            group_data: 群组数据，包含entity_id, entity_type等
            
        Returns:
            Dict[str, Any]: 添加结果，包含成功标志和消息
        """
        try:
            # 验证必要字段
            if not group_data.get("entity_id"):
                return {
                    "success": False,
                    "message": "缺少实体ID",
                    "data": None
                }
            
            if not group_data.get("entity_type"):
                return {
                    "success": False,
                    "message": "缺少实体类型",
                    "data": None
                }
            
            # 创建群组实体
            group = Group(
                entity_id=group_data.get("entity_id"),
                entity_type=group_data.get("entity_type"),
                username=group_data.get("username"),
                nickname=group_data.get("nickname")
            )
            
            async with get_db_session() as session:
                group_repo = GroupRepository(session)
                
                # 检查是否已存在
                existing_group = await group_repo.get_by_entity_id(group.entity_id)
                if existing_group:
                    return {
                        "success": False,
                        "message": "该实体ID已存在",
                        "data": existing_group.group_info
                    }
                
                # 添加群组
                added_group = await group_repo.add(group)
                
                return {
                    "success": True,
                    "message": "群组添加成功",
                    "data": added_group.group_info
                }
        except Exception as e:
            self._logger.error(f"添加群组失败: {e}")
            return {
                "success": False,
                "message": f"添加失败: {str(e)}",
                "data": None
            }
    
    async def delete_group(self, group_id: int) -> Dict[str, Any]:
        """
        删除群组
        
        Args:
            group_id: 群组ID
            
        Returns:
            Dict[str, Any]: 删除结果，包含成功标志和消息
        """
        try:
            async with get_db_session() as session:
                # 删除关联
                assoc_repo = AssociationRepository(session)
                await assoc_repo.delete_by_group_id(group_id)
                
                # 删除群组
                group_repo = GroupRepository(session)
                result = await group_repo.delete(group_id)
                
                if result:
                    return {
                        "success": True,
                        "message": "群组删除成功",
                        "data": None
                    }
                else:
                    return {
                        "success": False,
                        "message": "未找到该群组",
                        "data": None
                    }
        except Exception as e:
            self._logger.error(f"删除群组失败: {e}")
            return {
                "success": False,
                "message": f"删除失败: {str(e)}",
                "data": None
            }
    
    async def update_group(self, group_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        更新群组
        
        Args:
            group_data: 群组数据，包含id等
            
        Returns:
            Dict[str, Any]: 更新结果，包含成功标志和消息
        """
        try:
            group_id = group_data.get("id")
            if not group_id:
                return {
                    "success": False,
                    "message": "缺少群组ID",
                    "data": None
                }
            
            async with get_db_session() as session:
                group_repo = GroupRepository(session)
                
                # 检查是否存在
                existing_group = await group_repo.get_by_id(group_id)
                if not existing_group:
                    return {
                        "success": False,
                        "message": "未找到该群组",
                        "data": None
                    }
                
                # 更新数据
                if "entity_id" in group_data:
                    existing_group.entity_id = group_data.get("entity_id")
                if "entity_type" in group_data:
                    existing_group.entity_type = group_data.get("entity_type")
                if "username" in group_data:
                    existing_group.username = group_data.get("username")
                if "nickname" in group_data:
                    existing_group.nickname = group_data.get("nickname")
                
                # 更新群组
                updated_group = await group_repo.update(existing_group)
                
                return {
                    "success": True,
                    "message": "群组更新成功",
                    "data": updated_group.group_info
                }
        except Exception as e:
            self._logger.error(f"更新群组失败: {e}")
            return {
                "success": False,
                "message": f"更新失败: {str(e)}",
                "data": None
            }
    
    async def get_all_groups(self) -> Dict[str, Any]:
        """
        获取所有群组
        
        Returns:
            Dict[str, Any]: 结果，包含成功标志和群组列表
        """
        try:
            async with get_db_session() as session:
                group_repo = GroupRepository(session)
                groups = await group_repo.get_all()
                
                return {
                    "success": True,
                    "message": "获取成功",
                    "data": [group.group_info for group in groups]
                }
        except Exception as e:
            self._logger.error(f"获取所有群组失败: {e}")
            return {
                "success": False,
                "message": f"获取失败: {str(e)}",
                "data": []
            }
    
    async def get_groups_by_type(self, entity_type: str) -> Dict[str, Any]:
        """
        根据类型获取群组
        
        Args:
            entity_type: 实体类型
            
        Returns:
            Dict[str, Any]: 结果，包含成功标志和群组列表
        """
        try:
            async with get_db_session() as session:
                group_repo = GroupRepository(session)
                
                try:
                    # 将字符串转换为枚举
                    type_enum = EntityType(entity_type)
                    groups = await group_repo.get_by_type(type_enum)
                    
                    return {
                        "success": True,
                        "message": "获取成功",
                        "data": [group.group_info for group in groups]
                    }
                except ValueError:
                    return {
                        "success": False,
                        "message": f"无效的实体类型: {entity_type}",
                        "data": []
                    }
        except Exception as e:
            self._logger.error(f"获取群组失败: {e}")
            return {
                "success": False,
                "message": f"获取失败: {str(e)}",
                "data": []
            }
    
    async def add_bot_group_association(self, bot_id: int, group_id: int) -> Dict[str, Any]:
        """
        添加机器人与群组的关联
        
        Args:
            bot_id: 机器人ID
            group_id: 群组ID
            
        Returns:
            Dict[str, Any]: 添加结果，包含成功标志和消息
        """
        try:
            async with get_db_session() as session:
                assoc_repo = AssociationRepository(session)
                
                # 检查是否已存在关联
                associations = await assoc_repo.get_by_bot_id(bot_id)
                for assoc in associations:
                    if assoc.group_id == group_id:
                        return {
                            "success": False,
                            "message": "该关联已存在",
                            "data": None
                        }
                
                # 创建关联
                association = Association(bot_id=bot_id, group_id=group_id)
                added_assoc = await assoc_repo.add(association)
                
                return {
                    "success": True,
                    "message": "关联添加成功",
                    "data": added_assoc.association_info
                }
        except Exception as e:
            self._logger.error(f"添加关联失败: {e}")
            return {
                "success": False,
                "message": f"添加失败: {str(e)}",
                "data": None
            }
    
    async def delete_bot_group_association(self, bot_id: int, group_id: int) -> Dict[str, Any]:
        """
        删除机器人与群组的关联
        
        Args:
            bot_id: 机器人ID
            group_id: 群组ID
            
        Returns:
            Dict[str, Any]: 删除结果，包含成功标志和消息
        """
        try:
            async with get_db_session() as session:
                assoc_repo = AssociationRepository(session)
                result = await assoc_repo.delete_by_bot_and_group(bot_id, group_id)
                
                if result:
                    return {
                        "success": True,
                        "message": "关联删除成功",
                        "data": None
                    }
                else:
                    return {
                        "success": False,
                        "message": "未找到该关联",
                        "data": None
                    }
        except Exception as e:
            self._logger.error(f"删除关联失败: {e}")
            return {
                "success": False,
                "message": f"删除失败: {str(e)}",
                "data": None
            }
    
    async def get_groups_by_bot_id(self, bot_id: int) -> Dict[str, Any]:
        """
        获取机器人关联的群组
        
        Args:
            bot_id: 机器人ID
            
        Returns:
            Dict[str, Any]: 结果，包含成功标志和群组列表
        """
        try:
            async with get_db_session() as session:
                assoc_repo = AssociationRepository(session)
                groups = await assoc_repo.get_groups_by_bot_id(bot_id)
                
                return {
                    "success": True,
                    "message": "获取成功",
                    "data": [group.group_info for group in groups]
                }
        except Exception as e:
            self._logger.error(f"获取机器人关联的群组失败: {e}")
            return {
                "success": False,
                "message": f"获取失败: {str(e)}",
                "data": []
            }
    
    async def get_bots_by_group_id(self, group_id: int) -> Dict[str, Any]:
        """
        获取群组关联的机器人
        
        Args:
            group_id: 群组ID
            
        Returns:
            Dict[str, Any]: 结果，包含成功标志和机器人列表
        """
        try:
            async with get_db_session() as session:
                assoc_repo = AssociationRepository(session)
                bots = await assoc_repo.get_bots_by_group_id(group_id)
                
                return {
                    "success": True,
                    "message": "获取成功",
                    "data": [bot.bot_info for bot in bots]
                }
        except Exception as e:
            self._logger.error(f"获取群组关联的机器人失败: {e}")
            return {
                "success": False,
                "message": f"获取失败: {str(e)}",
                "data": []
            }
    
    async def batch_add_associations(self, bot_id: int, entity_list: List[Tuple[str, str]]) -> Dict[str, Any]:
        """
        批量添加机器人与群组的关联
        
        Args:
            bot_id: 机器人ID
            entity_list: 实体列表[(entity_id, entity_type), ...]
            
        Returns:
            Dict[str, Any]: 添加结果，包含成功标志和消息
        """
        try:
            success_count = 0
            failed_count = 0
            failed_entities = []
            
            async with get_db_session() as session:
                group_repo = GroupRepository(session)
                assoc_repo = AssociationRepository(session)
                
                for entity_id, entity_type in entity_list:
                    try:
                        # 检查群组是否存在，不存在则创建
                        existing_group = await group_repo.get_by_entity_id(entity_id)
                        
                        if not existing_group:
                            # 创建新群组
                            group = Group(
                                entity_id=entity_id,
                                entity_type=entity_type
                            )
                            group = await group_repo.add(group)
                        else:
                            group = existing_group
                        
                        # 检查关联是否存在
                        associations = await assoc_repo.get_by_bot_id(bot_id)
                        exists = False
                        for assoc in associations:
                            if assoc.group_id == group.id:
                                exists = True
                                break
                        
                        if not exists:
                            # 创建关联
                            association = Association(bot_id=bot_id, group_id=group.id)
                            await assoc_repo.add(association)
                            success_count += 1
                        else:
                            # 已存在，计入失败
                            failed_count += 1
                            failed_entities.append((entity_id, "关联已存在"))
                            
                    except Exception as e:
                        failed_count += 1
                        failed_entities.append((entity_id, str(e)))
                        self._logger.error(f"添加实体 {entity_id} 失败: {e}")
                
                return {
                    "success": True,
                    "message": f"批量添加完成: 成功 {success_count} 个, 失败 {failed_count} 个",
                    "data": {
                        "success_count": success_count,
                        "failed_count": failed_count,
                        "failed_entities": failed_entities
                    }
                }
        except Exception as e:
            self._logger.error(f"批量添加关联失败: {e}")
            return {
                "success": False,
                "message": f"批量添加失败: {str(e)}",
                "data": None
            } 