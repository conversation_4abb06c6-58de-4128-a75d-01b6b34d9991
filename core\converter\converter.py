#!/usr/bin/env python
# -*- coding: utf-8 -*-

from opentele.td import TDesktop
from opentele.tl import TelegramClient
from opentele.api import API, UseCurrentSession, CreateNewSession
from pathlib import Path
from typing import Tuple, Optional, Dict, Any, List, Union
from utils.logger import get_logger
import time


class TelegramConverter:
    """Telegram会话格式转换器，用于Tdata和Session格式互转"""
    
    def __init__(self, proxy: Optional[Tuple] = None):
        """
        初始化转换器

        Args:
            proxy: 代理设置，格式为(protocol, ip, port, username, password)
                  例如("socks5", "127.0.0.1", 1080, None, None)
                  None表示禁用代理
        """
        self.proxy = proxy
        self.logger = get_logger(__name__)
        self._log_proxy_config()
    
    def set_proxy(self, protocol: str, ip: str, port: int,
                  username: Optional[str] = None, password: Optional[str] = None):
        """
        设置代理

        Args:
            protocol: 代理协议，例如"socks5"
            ip: 代理服务器IP
            port: 代理服务器端口
            username: 代理用户名（可选）
            password: 代理密码（可选）
        """
        if username and password:
            self.proxy = (protocol, ip, port, username, password)
        else:
            self.proxy = (protocol, ip, port)
        self.logger.info(f"设置代理: {self.proxy}")
        self._log_proxy_config()
    
    async def session_to_tdata(self, session_path: str, output_dir: str,
                               password_info: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        将Session转换为Tdata格式

        Args:
            session_path: .session文件路径
            output_dir: 输出目录
            password_info: 密码信息

        Returns:
            转换结果信息字典
        """
        client = None
        try:
            self.logger.info(f"开始转换Session到Tdata: {session_path}")
            
            # 验证输入参数
            session_file = Path(session_path)
            if not session_file.exists():
                raise FileNotFoundError(f"Session文件不存在: {session_path}")
            
            if not session_file.suffix == '.session':
                raise ValueError(f"文件不是.session格式: {session_path}")
            
            # 从session文件名提取电话号码
            phone = session_file.stem
            self.logger.info(f"提取的电话号码: {phone}")
            
            # 创建输出目录：导出/手机号/tdata
            output_path = Path(output_dir)
            phone_dir = output_path / phone
            tdata_dir = phone_dir / "tdata"
            tdata_dir.mkdir(parents = True, exist_ok = True)
            self.logger.info(f"创建输出目录: {tdata_dir}")
            
            # 验证session文件
            self.logger.info("验证session文件完整性")
            if not self._validate_session_file(session_file):
                raise ValueError(f"Session文件损坏或格式不正确: {session_path}")
            
            # 从 telethon.session 文件加载客户端
            self.logger.info(f"初始化Telegram客户端，代理: {self.proxy}")
            
            # 根据代理配置创建客户端
            try:
                if self.proxy:
                    client = TelegramClient(str(session_file), proxy = self.proxy)
                else:
                    client = TelegramClient(str(session_file))
                
                self.logger.info("Telegram客户端初始化成功")
            except Exception as e:
                self.logger.error(f"Telegram客户端初始化失败: {e}")
                raise e
            
            # 根据代理配置决定是否进行登录验证
            if self.proxy:
                # 有代理时进行登录验证
                self.logger.info("检测到代理配置，进行登录验证...")
                await client.connect()
                
                # 检查是否已授权
                if not await client.is_user_authorized():
                    raise Exception("Session文件未授权或已过期")
                
                # 获取用户信息以验证会话有效性
                try:
                    me = await client.get_me()
                    if me:
                        phone = me.phone if me.phone else phone  # 使用实际电话号码
                        self.logger.info(f"验证会话成功，用户: {me.first_name} {me.last_name or ''}, 电话: {phone}")
                    else:
                        self.logger.warning("无法获取用户信息，但会话似乎有效")
                except Exception as e:
                    self.logger.warning(f"获取用户信息失败，但继续转换: {e}")
            else:
                # 无代理时跳过登录验证
                self.logger.info("未配置代理，跳过登录验证（离线模式）")
            
            # 使用当前会话将 Telethon 转换为 TDesktop
            # 参考demo文件的实现方式
            try:
                self.logger.info("开始转换为TDesktop格式")
                tdesk = await client.ToTDesktop(flag = UseCurrentSession)
                self.logger.info("成功转换为TDesktop格式")
            except Exception as e:
                self.logger.error(f"ToTDesktop转换失败: {e}")
                # 尝试不使用flag参数作为备选方案
                try:
                    self.logger.info("尝试不使用flag参数进行转换")
                    tdesk = await client.ToTDesktop()
                    self.logger.info("成功转换为TDesktop格式（无flag）")
                except Exception as e2:
                    self.logger.error(f"ToTDesktop转换再次失败: {e2}")
                    raise e2
            
            # 断开客户端连接
            await client.disconnect()
            client = None
            
            # 将会话保存到输出目录
            self.logger.info(f"保存TData到: {tdata_dir}")
            tdesk.SaveTData(str(tdata_dir))
            
            # 验证保存的tdata文件
            if not self._validate_tdata_folder(tdata_dir):
                raise Exception("保存的Tdata文件夹验证失败")
            
            # 如果有密码，保存密码文件
            password_saved = False
            if password_info and "table_passwords" in password_info:
                file_passwords = password_info["table_passwords"]
                if session_path in file_passwords and file_passwords[session_path].get("password"):
                    password = file_passwords[session_path]["password"]
                    if password:
                        password_file_name = password_info.get("file_name", "Password2FA.txt")
                        password_file = tdata_dir / password_file_name
                        try:
                            password_file.write_text(password, encoding = 'utf-8')
                            self.logger.info(f"已创建密码文件: {password_file}")
                            password_saved = True
                        except Exception as e:
                            self.logger.error(f"创建密码文件失败: {e}")
            
            self.logger.info(f"Session转Tdata成功: {session_path} -> {tdata_dir}")
            
            # 获取账号信息
            account_info = {
                "phone": phone,
                "type": "Session",
                "output_path": str(tdata_dir),
                "status": "成功",
                "password_saved": password_saved
            }
            
            return account_info
        
        except Exception as e:
            # 确保客户端连接被正确关闭
            if client:
                try:
                    await client.disconnect()
                except:
                    pass
            
            error_msg = str(e)
            self.logger.error(f"Session转Tdata失败: {session_path}, 错误: {error_msg}")
            return {
                "phone": Path(session_path).stem,
                "type": "Session",
                "output_path": "",
                "status": f"失败: {error_msg}"
            }
    
    async def tdata_to_session(self, tdata_dir: str, output_dir: str, password_info: Optional[Dict[str, Any]] = None) -> \
    Dict[str, Any]:
        """
        将Tdata转换为Session格式
        简化版本，减少过多判断，让ToTelethon自动处理

        Args:
            tdata_dir: tdata文件夹路径
            output_dir: 输出目录
            password_info: 密码信息

        Returns:
            转换结果信息字典
        """
        client = None
        try:
            self.logger.info(f"开始转换Tdata到Session: {tdata_dir}")
            
            # 基本验证：确保是tdata目录
            tdata_path = Path(tdata_dir)
            if not tdata_path.exists() or not tdata_path.is_dir():
                raise FileNotFoundError(f"Tdata路径不存在或不是目录: {tdata_dir}")
            
            # 获取密码（简化逻辑）
            password = self._get_password_for_tdata(tdata_dir, password_info)
            password_source = self._get_password_source(tdata_dir, password_info)
            
            # 从tdata文件夹加载TDesktop客户端
            self.logger.info("加载TDesktop客户端...")
            tdesk = TDesktop(tdata_dir)
            
            # 检查是否已加载任何账户
            if not tdesk.isLoaded():
                raise Exception("无法加载Tdata文件夹中的账户信息")
            
            # 获取电话号码（让ToTelethon自动处理账户信息）
            phone = self._extract_phone_from_tdata(tdesk)
            
            # 创建输出session文件路径
            output_path = Path(output_dir)
            output_path.mkdir(parents = True, exist_ok = True)
            session_file = output_path / f"{phone}.session"
            
            # 删除已存在的session文件
            if session_file.exists():
                session_file.unlink()
                self.logger.info(f"删除已存在的session文件: {session_file}")
            
            # 准备转换参数
            convert_kwargs = {
                "session": str(session_file),
                "flag": UseCurrentSession
            }
            
            # 添加代理（如果有）
            if self.proxy:
                convert_kwargs["proxy"] = self.proxy
                self.logger.info(f"使用代理: {self.proxy[0]}://{self.proxy[1]}:{self.proxy[2]}")
            
            # 添加密码（如果有）
            if password:
                convert_kwargs["password"] = password
                self.logger.info(f"使用两步验证密码，来源: {password_source}")
            
            # 执行转换 - 让ToTelethon自动处理
            self.logger.info("开始转换为Telethon格式...")
            try:
                client = await tdesk.ToTelethon(**convert_kwargs)
                self.logger.info("转换成功")
            except Exception as e:
                # 简单的备选方案：不使用flag
                self.logger.warning(f"使用flag转换失败: {e}，尝试不使用flag...")
                convert_kwargs.pop("flag", None)
                client = await tdesk.ToTelethon(**convert_kwargs)
                self.logger.info("转换成功（无flag）")
            
            # 根据代理配置决定是否进行登录验证
            user_info = {}
            if self.proxy:
                # 有代理时进行登录验证
                self.logger.info("检测到代理配置，进行登录验证...")
                try:
                    await client.connect()
                    me = await client.get_me()
                    user_info = {
                        "first_name": getattr(me, 'first_name', ''),
                        "last_name": getattr(me, 'last_name', ''),
                        "username": getattr(me, 'username', ''),
                        "premium": getattr(me, 'premium', False),
                        "user_id": getattr(me, 'id', None)
                    }
                    self.logger.info(f"登录验证成功: {user_info['first_name']} {user_info['last_name']}")
                    await client.disconnect()
                except Exception as e:
                    self.logger.warning(f"登录验证失败: {e}")
                    await client.disconnect()
                    # 登录验证失败时使用默认信息
                    user_info = {
                        "first_name": "",
                        "last_name": "",
                        "username": "",
                        "premium": False,
                        "user_id": None
                    }
            else:
                # 无代理时跳过登录验证
                self.logger.info("未配置代理，跳过登录验证（离线模式）")
                user_info = {
                    "first_name": "",
                    "last_name": "",
                    "username": "",
                    "premium": False,
                    "user_id": None
                }
            
            client = None
            
            # 验证session文件
            if not session_file.exists() or session_file.stat().st_size == 0:
                raise Exception("Session文件生成失败")
            
            self.logger.info(f"Tdata转Session完成: {tdata_dir} -> {session_file}")
            
            # 返回结果
            return {
                "phone": phone,
                "type": "Tdata",
                "output_path": str(session_file),
                "status": "成功",
                "first_name": user_info.get('first_name', ''),
                "last_name": user_info.get('last_name', ''),
                "username": user_info.get('username', ''),
                "premium": user_info.get('premium', False),
                "user_id": user_info.get('user_id'),
                "password_source": password_source,
                "file_size": session_file.stat().st_size,
                "tdata_path": str(tdata_dir)
            }
        
        except Exception as e:
            # 确保客户端连接被正确关闭
            if client:
                try:
                    await client.disconnect()
                    self.logger.info("异常处理中成功断开客户端连接")
                except Exception as disconnect_error:
                    self.logger.warning(f"异常处理中断开连接失败: {disconnect_error}")
            
            error_msg = str(e)
            self.logger.error(f"Tdata转Session失败: {tdata_dir}")
            self.logger.error(f"失败原因: {error_msg}")
            
            # 尝试从tdata文件夹名称提取电话号码
            phone = "未知"
            try:
                folder_name = Path(tdata_dir).name
                if folder_name.startswith("tdata_"):
                    phone = folder_name[6:]
                elif folder_name.isdigit():
                    phone = folder_name
                else:
                    # 尝试从父目录名称提取
                    parent_name = Path(tdata_dir).parent.name
                    if parent_name.isdigit():
                        phone = parent_name
            except Exception as phone_extract_error:
                self.logger.warning(f"提取电话号码失败: {phone_extract_error}")
            
            # 构建详细的失败信息
            failure_info = {
                "phone": phone,
                "type": "Tdata",
                "output_path": "",
                "status": f"失败: {error_msg}",
                "tdata_path": str(tdata_dir),
                "error_details": {
                    "error_type": type(e).__name__,
                    "error_message": error_msg,
                    "password_attempts": locals().get('password_attempts', []),
                    "conversion_attempts": locals().get('conversion_attempts', [])
                }
            }
            
            # 记录失败的详细信息
            self.logger.error(f"转换失败详情: 路径={tdata_dir}, 电话={phone}, 错误类型={type(e).__name__}")
            
            return failure_info
    
    def _get_password_for_tdata(self, tdata_dir: str, password_info: Optional[Dict[str, Any]]) -> Optional[str]:
        """获取tdata的密码（简化版本）"""
        if not password_info:
            return None
        
        # 1. 表格密码优先
        table_passwords = password_info.get("table_passwords", {})
        if tdata_dir in table_passwords:
            password = table_passwords[tdata_dir].get("password", "").strip()
            if password:
                return password
        
        # 2. 文件识别密码
        if password_info.get("type") == "file":
            password_file_name = password_info.get("file_name", "Password2FA.txt")
            password_file = Path(tdata_dir) / password_file_name
            if password_file.exists():
                try:
                    content = password_file.read_text(encoding = 'utf-8').strip()
                    if content:
                        return content
                except Exception:
                    pass
        
        # 3. 统一密码
        elif password_info.get("type") == "unified":
            password = password_info.get("password", "").strip()
            if password:
                return password
        
        return None
    
    def _get_password_source(self, tdata_dir: str, password_info: Optional[Dict[str, Any]]) -> str:
        """获取密码来源描述"""
        if not password_info:
            return "无密码"
        
        # 检查表格密码
        table_passwords = password_info.get("table_passwords", {})
        if tdata_dir in table_passwords and table_passwords[tdata_dir].get("password", "").strip():
            return "表格设置"
        
        # 检查密码类型
        password_type = password_info.get("type", "none")
        if password_type == "file":
            password_file_name = password_info.get("file_name", "Password2FA.txt")
            return f"文件({password_file_name})"
        elif password_type == "unified":
            return "统一密码"
        
        return "无密码"
    
    def _extract_phone_from_tdata(self, tdesk) -> str:
        """从TDesktop对象中提取电话号码"""
        try:
            if hasattr(tdesk, 'accountsMain') and tdesk.accountsMain:
                account = tdesk.accountsMain[0]
                if hasattr(account, 'phone') and account.phone:
                    return str(account.phone)
            
            # 如果无法从账户中获取，返回默认值
            raise Exception("无法从tdata中提取电话号码")
        
        except Exception as e:
            # 尝试从文件夹名称提取作为备选
            self.logger.warning(f"从账户提取电话号码失败: {e}")
            return "unknown"
    
    async def batch_convert(self,
                            files: List[str],
                            output_dir: str,
                            convert_type: str,
                            password_info: Optional[Dict[str, Any]] = None,
                            progress_callback: Optional[callable] = None) -> List[Dict[str, Any]]:
        """
        批量转换文件

        Args:
            files: 文件路径列表
            output_dir: 输出目录
            convert_type: 转换类型，"session_to_tdata" 或 "tdata_to_session"
            password_info: 密码信息
            progress_callback: 进度回调函数，接收(current, total, message)参数

        Returns:
            转换结果列表
        """
        results = []
        total_files = len(files)
        
        self.logger.info(f"开始批量转换: {convert_type}, 总文件数: {total_files}")
        
        # 验证输出目录
        output_path = Path(output_dir)
        if not output_path.exists():
            try:
                output_path.mkdir(parents = True, exist_ok = True)
                self.logger.info(f"创建输出目录: {output_dir}")
            except Exception as e:
                self.logger.error(f"创建输出目录失败: {e}")
                # 为所有文件返回失败结果
                for file_path in files:
                    results.append({
                        "phone": Path(file_path).stem if Path(file_path).is_file() else "未知",
                        "type": "Session" if convert_type == "session_to_tdata" else "Tdata",
                        "output_path": "",
                        "status": f"失败: 无法创建输出目录 - {str(e)}"
                    })
                return results
        
        # 逐个处理文件
        for i, file_path in enumerate(files, 1):
            file_start_time = time.time() if 'time' in globals() else None
            try:
                # 更新进度
                file_name = Path(file_path).name
                if progress_callback:
                    progress_callback(i, total_files, f"正在处理 ({i}/{total_files}): {file_name}")
                
                self.logger.info(f"开始处理文件 {i}/{total_files}: {file_path}")
                
                path = Path(file_path)
                result = None
                
                # 预验证文件
                if convert_type == "session_to_tdata":
                    # Session转Tdata预验证
                    if not path.exists():
                        result = {
                            "phone": path.stem,
                            "type": "Session",
                            "output_path": "",
                            "status": "失败: 文件不存在"
                        }
                    elif not path.is_file():
                        result = {
                            "phone": path.stem,
                            "type": "Session",
                            "output_path": "",
                            "status": "失败: 不是文件"
                        }
                    elif path.suffix != '.session':
                        result = {
                            "phone": path.stem,
                            "type": "Session",
                            "output_path": "",
                            "status": "失败: 不是.session文件"
                        }
                    else:
                        # 执行转换
                        self.logger.info(f"开始Session转Tdata: {file_name}")
                        result = await self.session_to_tdata(file_path, output_dir, password_info)
                
                elif convert_type == "tdata_to_session":
                    # Tdata转Session预验证
                    if not path.exists():
                        result = {
                            "phone": "未知",
                            "type": "Tdata",
                            "output_path": "",
                            "status": "失败: 路径不存在"
                        }
                    elif not path.is_dir():
                        result = {
                            "phone": "未知",
                            "type": "Tdata",
                            "output_path": "",
                            "status": "失败: 不是文件夹"
                        }
                    elif not self._validate_tdata_folder(path):
                        result = {
                            "phone": "未知",
                            "type": "Tdata",
                            "output_path": "",
                            "status": "失败: 不是有效的tdata文件夹，缺少必要的标识文件"
                        }
                    else:
                        # 执行转换
                        self.logger.info(f"开始Tdata转Session: {file_name}")
                        result = await self.tdata_to_session(file_path, output_dir, password_info)
                
                if result:
                    results.append(result)
                    
                    # 记录转换结果和耗时
                    status = result.get("status", "未知")
                    elapsed_time = f"{time.time() - file_start_time:.2f}秒" if file_start_time else "未知"
                    
                    if "成功" in status:
                        self.logger.info(f"✓ 文件 {i}/{total_files} 转换成功: {file_name} (耗时: {elapsed_time})")
                        if progress_callback:
                            progress_callback(i, total_files, f"✓ 完成: {file_name}")
                    else:
                        self.logger.error(f"✗ 文件 {i}/{total_files} 转换失败: {file_name} (耗时: {elapsed_time})")
                        self.logger.error(f"失败原因: {status}")
                        if progress_callback:
                            progress_callback(i, total_files, f"✗ 失败: {file_name}")
            
            except Exception as e:
                # 处理单个文件转换时的异常
                elapsed_time = f"{time.time() - file_start_time:.2f}秒" if file_start_time else "未知"
                error_msg = f"处理文件时发生异常: {str(e)}"
                self.logger.error(f"✗ 文件 {i}/{total_files} 异常: {file_name} (耗时: {elapsed_time})")
                self.logger.error(f"异常详情: {error_msg}")
                
                result = {
                    "phone": Path(file_path).stem if Path(file_path).is_file() else "未知",
                    "type": "Session" if convert_type == "session_to_tdata" else "Tdata",
                    "output_path": "",
                    "status": f"失败: {error_msg}",
                    "error_details": {
                        "error_type": type(e).__name__,
                        "file_path": str(file_path),
                        "processing_time": elapsed_time
                    }
                }
                results.append(result)
                
                if progress_callback:
                    progress_callback(i, total_files, f"✗ 异常: {file_name}")
        
        # 统计结果
        success_count = sum(1 for r in results if "成功" in r.get("status", ""))
        fail_count = total_files - success_count
        
        self.logger.info(f"批量转换完成: 成功 {success_count}, 失败 {fail_count}, 总计 {total_files}")
        
        # 最终进度更新
        if progress_callback:
            progress_callback(total_files, total_files, f"转换完成: 成功 {success_count}, 失败 {fail_count}")
        
        return results
    
    def _log_proxy_config(self):
        """记录代理配置信息"""
        if self.proxy is None:
            self.logger.info("代理配置: 禁用代理")
        elif len(self.proxy) >= 3:
            protocol, ip, port = self.proxy[:3]
            if len(self.proxy) >= 5:
                username = self.proxy[3]
                self.logger.info(f"代理配置: {protocol}://{username}@{ip}:{port}")
            else:
                self.logger.info(f"代理配置: {protocol}://{ip}:{port}")
        else:
            self.logger.warning(f"代理配置格式异常: {self.proxy}")
    
    def set_proxy_config(self, proxy_config: Optional[Dict[str, Any]]):
        """
        根据配置字典设置代理

        Args:
            proxy_config: 代理配置字典，支持以下格式:
                - {"type": "none"} - 禁用代理
                - {"type": "system"} - 系统代理（暂时等同于禁用）
                - {"type": "ip_pool", "proxy_type": "socks5", "ip": "127.0.0.1",
                   "port": 1080, "username": "user", "password": "pass"} - IP池代理
        """
        if not proxy_config or proxy_config.get("type") == "none":
            self.proxy = None
            self.logger.info("设置为禁用代理模式")
        elif proxy_config.get("type") == "system":
            try:
                from utils.get_system_proxy import get_win_proxy_info
                system_proxy = get_win_proxy_info()
                if system_proxy:
                    self.proxy = ("socks5", system_proxy['ip'], system_proxy['port'])
                    self.logger.info(f"设置为系统代理模式: socks5://{system_proxy['ip']}:{system_proxy['port']}")
                else:
                    self.proxy = None
                    self.logger.warning("未检测到系统代理设置，使用禁用代理模式")
            except Exception as e:
                self.proxy = None
                self.logger.error(f"读取系统代理失败: {e}，使用禁用代理模式")
        elif proxy_config.get("type") == "ip_pool":
            proxy_type = proxy_config.get("proxy_type", "socks5")
            ip = proxy_config.get("ip")
            port = proxy_config.get("port")
            username = proxy_config.get("username")
            password = proxy_config.get("password")
            
            if not ip or not port:
                self.logger.error("IP池代理配置不完整，回退到禁用代理")
                self.proxy = None
                return
            
            if username and password:
                self.proxy = (proxy_type, ip, port, username, password)
            else:
                self.proxy = (proxy_type, ip, port)
            
            self.logger.info(f"设置为IP池代理模式: {proxy_type}://{ip}:{port}")
        elif proxy_config.get("type") == "custom":
            proxy_type = proxy_config.get("proxy_type", "socks5")
            ip = proxy_config.get("ip")
            port = proxy_config.get("port")
            username = proxy_config.get("username")
            password = proxy_config.get("password")
            
            if not ip or not port:
                self.logger.error("自定义代理配置不完整，回退到禁用代理")
                self.proxy = None
                return
            
            if username and password:
                self.proxy = (proxy_type, ip, port, username, password)
            else:
                self.proxy = (proxy_type, ip, port)
            
            self.logger.info(f"设置为自定义代理模式: {proxy_type}://{ip}:{port}")
        else:
            self.logger.warning(f"未知的代理类型: {proxy_config.get('type')}，使用禁用代理")
            self.proxy = None
        
        self._log_proxy_config()
    
    def _validate_session_file(self, session_file: Path) -> bool:
        """
        验证session文件的完整性

        Args:
            session_file: session文件路径

        Returns:
            是否有效
        """
        try:
            # 检查文件大小
            if session_file.stat().st_size == 0:
                self.logger.error(f"Session文件为空: {session_file}")
                return False
            
            # 尝试读取session文件的基本信息
            import sqlite3
            
            # 检查是否为有效的SQLite数据库
            try:
                conn = sqlite3.connect(str(session_file))
                cursor = conn.cursor()
                
                # 检查必要的表是否存在
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
                tables = [row[0] for row in cursor.fetchall()]
                
                required_tables = ['sessions', 'entities', 'sent_files', 'update_state']
                missing_tables = [table for table in required_tables if table not in tables]
                
                if missing_tables:
                    self.logger.warning(f"Session文件缺少表: {missing_tables}")
                    # 不是致命错误，可能是旧版本的session文件
                
                # 检查sessions表中的关键数据
                cursor.execute("SELECT * FROM sessions LIMIT 1;")
                session_data = cursor.fetchone()
                
                if session_data is None:
                    self.logger.error("Session文件中没有会话数据")
                    conn.close()
                    return False
                
                # 检查关键字段是否为None
                if len(session_data) >= 6:  # 确保有足够的字段
                    dc_id, _, _, auth_key, _, user_id = session_data[:6]
                    
                    if dc_id is None:
                        self.logger.error("Session文件中dc_id为None")
                        conn.close()
                        return False
                    
                    if auth_key is None:
                        self.logger.error("Session文件中auth_key为None")
                        conn.close()
                        return False
                    
                    self.logger.info(f"Session文件验证通过: dc_id={dc_id}, user_id={user_id}")
                
                conn.close()
                return True
            
            except sqlite3.Error as e:
                self.logger.error(f"Session文件SQLite错误: {e}")
                return False
        
        except Exception as e:
            self.logger.error(f"验证session文件时出错: {e}")
            return False
    
    def _validate_tdata_folder(self, tdata_dir: Path) -> bool:
        """
        验证tdata文件夹的完整性

        Args:
            tdata_dir: tdata文件夹路径

        Returns:
            是否有效
        """
        try:
            # 检查常见的tdata标识文件
            tdata_indicators = [
                'map0', 'map1',  # 主要标识文件
                'key_datas',  # 密钥数据文件
                'settings0', 'settings1',  # 设置文件
                'usertag', 'binlog',  # 其他可能的标识文件
                'working'  # 工作状态文件
            ]
            
            found_files = []
            for indicator in tdata_indicators:
                file_path = tdata_dir / indicator
                if file_path.exists():
                    found_files.append(indicator)
            
            # 检查是否有十六进制格式的用户数据文件（通常是16位十六进制）
            try:
                for item in tdata_dir.iterdir():
                    if item.is_file():
                        # 检查文件名是否为16位十六进制格式
                        filename = item.name
                        if len(filename) == 16 and all(c in '0123456789ABCDEFabcdef' for c in filename):
                            found_files.append(f"hex_file_{filename}")
                            break
            except (PermissionError, OSError):
                pass
            
            if not found_files:
                self.logger.error(f"Tdata文件夹缺少必要文件，检查的文件: {tdata_indicators}")
                return False
            
            self.logger.info(f"Tdata文件夹验证通过，找到文件: {found_files}")
            return True
        
        except Exception as e:
            self.logger.error(f"验证tdata文件夹时出错: {e}")
            return False
