from typing import Optional, Dict, Any

class Association:
    """
    关联实体类，表示机器人与群组的关联关系
    """
    
    def __init__(self, bot_id: int, group_id: int, id: Optional[int] = None):
        """
        初始化关联实体
        
        Args:
            bot_id: 机器人ID
            group_id: 群组ID
            id: 数据库ID，可选
        """
        self.id = id
        self.bot_id = bot_id
        self.group_id = group_id
    
    @property
    def association_info(self) -> Dict[str, Any]:
        """获取关联信息"""
        return {
            "id": self.id,
            "bot_id": self.bot_id,
            "group_id": self.group_id
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Association':
        """
        从字典创建关联实体
        
        Args:
            data: 包含关联信息的字典
            
        Returns:
            Association: 关联实体
        """
        return cls(
            bot_id=data.get("bot_id"),
            group_id=data.get("group_id"),
            id=data.get("id")
        ) 