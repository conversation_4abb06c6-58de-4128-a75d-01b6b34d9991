from typing import Optional, List, Dict, Any

class Bot:
    """
    机器人实体类，表示Telegram机器人
    """
    
    def __init__(self, token: str, username: str, update_method: str = "webhook", id: Optional[int] = None):
        """
        初始化机器人实体
        
        Args:
            token: 机器人Token
            username: 机器人用户名（@开头）
            update_method: 更新方式，默认为webhook
            id: 机器人ID，可选，用于数据库映射
        """
        self.id = id
        self.token = token
        self.username = username
        self.update_method = update_method
    
    @property
    def bot_info(self) -> Dict[str, Any]:
        """获取机器人信息"""
        return {
            "id": self.id,
            "token": self.token,
            "username": self.username,
            "update_method": self.update_method
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Bot':
        """
        从字典创建机器人实体
        
        Args:
            data: 包含机器人信息的字典
            
        Returns:
            Bot: 机器人实体
        """
        return cls(
            token=data.get("token"),
            username=data.get("username"),
            update_method=data.get("update_method", "webhook"),
            id=data.get("id")
        ) 