from typing import Optional, Dict, Any
from enum import Enum

class EntityType(Enum):
    """实体类型枚举"""
    GROUP = "群组"
    CHANNEL = "频道"
    INDIVIDUAL = "个人"

class Group:
    """
    群组实体类，表示Telegram群组、频道或个人
    """
    
    def __init__(self, 
                 entity_id: str, 
                 entity_type: EntityType, 
                 username: Optional[str] = None, 
                 nickname: Optional[str] = None, 
                 id: Optional[int] = None):
        """
        初始化群组实体
        
        Args:
            entity_id: 实体ID
            entity_type: 实体类型
            username: 用户名，可选
            nickname: 昵称，可选
            id: 数据库ID，可选
        """
        self.id = id
        self.entity_id = entity_id
        self.entity_type = entity_type if isinstance(entity_type, EntityType) else EntityType(entity_type)
        self.username = username
        self.nickname = nickname
    
    @property
    def group_info(self) -> Dict[str, Any]:
        """获取群组信息"""
        return {
            "id": self.id,
            "entity_id": self.entity_id,
            "entity_type": self.entity_type.value,
            "username": self.username,
            "nickname": self.nickname
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Group':
        """
        从字典创建群组实体
        
        Args:
            data: 包含群组信息的字典
            
        Returns:
            Group: 群组实体
        """
        return cls(
            entity_id=data.get("entity_id"),
            entity_type=data.get("entity_type"),
            username=data.get("username"),
            nickname=data.get("nickname"),
            id=data.get("id")
        ) 