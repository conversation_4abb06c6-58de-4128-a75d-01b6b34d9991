import time
import asyncio
import random
from PySide6.QtCore import QObject, Signal
from data.database import get_session
from data.repositories.invite_task_repo import InviteTaskRepository
from utils.logger import get_logger
from core.telegram.client_worker import TelegramClientWorker
from typing import TYPE_CHECKING
if TYPE_CHECKING:
    from app.services.invite_service import InviteService

class InviteTaskManager(QObject):
    """邀请任务管理器，协调多个邀请任务的执行"""
    
    # 信号定义
    task_progress = Signal(int, int, str)  # 任务ID，进度，状态
    task_completed = Signal(int, bool, str)  # 任务ID，成功标志，消息
    task_stats_changed = Signal(int, int, int, int)  # 任务ID, success, failed, pending
    
    def __init__(self, client_worker: TelegramClientWorker, invite_service: 'InviteService' = None):
        super().__init__()
        self._client_worker = client_worker
        self._invite_service = invite_service  # 直接注入InviteService
        self._tasks = {}  # {task_id: task_info}
        self._logger = get_logger("core.invite.task_manager")
        
    def task_initialize(self):
        """初始化任务"""
        self._invite_service.task_initialize()
        
    def stop_task(self, task_id):
        """停止邀请任务"""
        if task_id not in self._tasks:
            return False
            
        # 发送停止信号
        task_info = self._tasks[task_id]
        task_info["status"] = "stopping"
        
        # 通知工作线程取消任务
        self._client_worker._add_task(
            self._stop_invite_task, task_id
        )
        
        return True
    
    def start_task(self, task_id, task_data):
        """启动邀请任务"""
        if task_id in self._tasks:
            return False
        print(task_data)
        # 创建任务信息
        task_info = {
            "id": task_id,
            "data": task_data,
            "status": "running",
            "progress": 0,
            "start_time": time.time()
        }
        self._tasks[task_id] = task_info
        print("任务已添加：", task_info)
        # 提交任务到客户端工作线程
        task_request_id = self._client_worker._add_task(
            self._execute_invite_task_batched, task_id, task_data
        )
        
        # 记录请求ID，用于后续查询结果
        task_info["request_id"] = task_request_id
        
        return True
    
    async def _create_invite_record(self, task_id, invitee, status, error_message=None):
        """创建邀请记录"""
        async with get_session() as session:
            repo = InviteTaskRepository(session)
            record_data = {
                "task_id": task_id,
                "invitee": invitee,
                "status": status,
                "error_message": error_message
            }
            await repo.create_invite_record(record_data)
            await session.commit()

    async def _execute_invite_task_batched(self, task_id, task_data):
        """
        分批异步执行邀请任务，防止线程卡死
        """
        self._logger.info(f"开始分批执行邀请任务 {task_id}")
        accounts = task_data.get("accounts", [])
        
        # 从任务模型获取批处理参数
        batch_size_min = task_data.get("batch_size_min", 1)
        batch_size_max = task_data.get("batch_size_max", 5)
        
        # 邀请间隔（秒）
        invite_interval_min = task_data.get("invite_interval_min", 60)  # 默认最小60秒
        invite_interval_max = task_data.get("invite_interval_max", 300)  # 默认最大300秒
        
        # 批次间隔使用最小邀请间隔的2倍，确保充分休息
        batch_interval = invite_interval_min * 2
        
        invited = 0
        failed = 0
        stopped = False
        total = None  # 总用户数，首次从数据库统计

        try:
            while True:
                # 动态计算本批次大小
                batch_size = random.randint(batch_size_min, batch_size_max)
                # 每批从数据库读取待邀请用户
                async with get_session() as session:
                    repo = InviteTaskRepository(session)
                    batch_users = await repo.get_pending_invitees(task_id, limit=batch_size)
                    if total is None:
                        # 首次统计总数
                        stats = await repo.get_task_statistics(task_id)
                        total = stats.get("pending", 0) + stats.get("success", 0) + stats.get("failed", 0)
                if not batch_users:
                    break  # 没有更多用户，任务完成

                self._logger.info(f"任务 {task_id} 本批次读取 {len(batch_users)} 个待邀请用户")

                # 检查任务是否被请求停止
                if task_id not in self._tasks or self._tasks[task_id].get("status") == "stopping":
                    self._logger.info(f"任务 {task_id} 被请求停止，终止执行")
                    stopped = True
                    break

                # 寻找可用账号
                account, remain = await self._get_available_account(accounts)
                if not account:
                    self._logger.warning(f"任务 {task_id} 所有账号都已达每日邀请上限，任务提前结束")
                    break

                # 本批次实际可邀请数量
                effective_batch = batch_users[:remain]
                if len(effective_batch) == 0:
                    self._logger.warning(f"任务 {task_id} 可用邀请配额不足，跳过批次")
                    continue

                self._logger.info(f"任务 {task_id} 开始执行本批次，本批次 {len(effective_batch)} 人")
                
                # 邀请本批次用户
                batch_result = await self._invite_batch_users(
                    task_id, effective_batch, account, task_data
                )
                invited += batch_result["invited"]
                failed += batch_result["failed"]

                # 每批次后统计最新状态并发射信号
                async with get_session() as session:
                    repo = InviteTaskRepository(session)
                    stats = await repo.get_task_statistics(task_id)
                    success_count = stats.get("success", 0)
                    failed_count = stats.get("failed", 0)
                    pending_count = stats.get("pending", 0)
                    total_count = success_count + failed_count + pending_count
                    
                    self.task_stats_changed.emit(
                        task_id,
                        success_count,
                        failed_count,
                        pending_count
                    )

                # 发射进度信号 - 基于所有已处理的用户计算进度
                if total_count > 0:
                    progress = int((success_count + failed_count) / total_count * 100)
                else:
                    progress = 0
                self.task_progress.emit(task_id, progress, "running")

                # 更新任务信息
                if task_id in self._tasks:
                    self._tasks[task_id]["progress"] = progress

                # 批次间隔，释放事件循环，避免长时间占用
                # 使用动态批次间隔，增加随机性防止检测
                actual_interval = random.randint(
                    batch_interval, 
                    batch_interval + invite_interval_min
                )
                self._logger.debug(f"任务 {task_id} 批次间隔 {actual_interval} 秒")
                await asyncio.sleep(actual_interval)

            # 任务完成或被中断
            if stopped:
                self.task_completed.emit(task_id, False, "任务被中断")
                self._logger.info(f"任务 {task_id} 被中断，已邀请：{invited}，失败：{failed}")
            else:
                self.task_completed.emit(task_id, True, f"任务完成：成功 {invited}，失败 {failed}")
                self._logger.info(f"任务 {task_id} 完成，成功：{invited}，失败：{failed}")

        except Exception as e:
            # 发生异常，也将任务标记为中断
            self._logger.error(f"任务 {task_id} 执行异常: {str(e)}")
            self.task_completed.emit(task_id, False, f"任务意外中断: {str(e)}")
        
        finally:
            # 清理任务记录
            if task_id in self._tasks:
                del self._tasks[task_id]

    async def _get_available_account(self, accounts):
        """获取可用账号及剩余可邀请数"""
        for account in accounts:
            phone = account.get("phone")
            try:
                limit_info = await self._invite_service.check_invite_limit(phone)
                remain = limit_info["max_daily_limit"] - limit_info["current_day_count"]
                if remain > 0:
                    return account, remain
            except Exception as e:
                self._logger.error(f"检查账号 {phone} 邀请限制失败: {e}")
        return None, 0

    async def _invite_batch_users(self, task_id, users, account, task_data):
        """批量邀请用户"""
        invited = 0
        failed = 0
        stopped = False
        phone = account.get("phone")
        
        # 获取邀请参数
        invite_interval_min = task_data.get("invite_interval_min", 60)
        invite_interval_max = task_data.get("invite_interval_max", 300)
        
        for record in users:
            user = record.invitee
            # 检查任务是否被终止
            if task_id not in self._tasks or self._tasks[task_id].get("status") == "stopping":
                stopped = True
                break
                
            # 邀请单个用户
            result = await self._invite_single_user(
                task_id, user, phone, task_data
            )
            
            if result == "success":
                invited += 1
            else:
                failed += 1
                
            # 每人之间邀请间隔 - 使用从模型获取的间隔
            if not stopped and users.index(record) < len(users) - 1:
                sleep_time = random.randint(invite_interval_min, invite_interval_max)
                self._logger.debug(f"邀请间隔 {sleep_time} 秒")
                await asyncio.sleep(sleep_time)
                
        return {"invited": invited, "failed": failed, "stopped": stopped}

    async def _invite_single_user(self, task_id, user, phone, task_data):
        """邀请单个用户(带重试机制)"""
        max_retries = task_data.get("max_retries", 2)  # 最大重试次数
        retry_count = 0
        group_name = task_data.get("group_name")
        while retry_count <= max_retries:
            try:
                # 1. 邀请前先确保已在群
                if group_name:
                    join_task_id = self._client_worker.join_group_if_needed(phone, group_name)
                    join_ok, join_msg = await self._client_worker.get_task_result(join_task_id)
                    if not join_ok:
                        await self._invite_service.update_invite_record(task_id, user, "failed", f"加群失败: {join_msg}")
                        self._logger.warning(f"账号{phone}加群{group_name}失败: {join_msg}")
                        return "failed"
                # 2. 正常邀请逻辑...
                if task_data.get("invite_method", "direct") == "direct":
                    # 直接邀请
                    task_req_id = self._client_worker.invite_user_to_group(
                        phone, user, task_data.get("group_name"), task_data.get("message", "")
                    )
                else:
                    # 发送邀请链接
                    task_req_id = self._client_worker.send_invite_link(
                        phone, user, task_data.get("group_invite_link"), task_data.get("message", "")
                    )
                # 获取任务结果
                success, msg = await self._client_worker.get_task_result(task_req_id)
                # 处理邀请结果
                if success:
                    await self._invite_service.increment_invite_count(phone, 1)
                    await self._invite_service.update_invite_record(task_id, user, "success", None)
                    self._logger.debug(f"邀请用户 {user} 成功")
                    return "success"
                if isinstance(msg, str) and "FloodWait" in msg:
                    await self._invite_service.update_invite_record(task_id, user, "failed", msg)
                    self._logger.warning(f"邀请用户 {user} 频率限制错误: {msg}")
                    return "failed"
                retry_count += 1
                if retry_count <= max_retries:
                    self._logger.warning(f"邀请用户 {user} 失败, 尝试重试 ({retry_count}/{max_retries}): {msg}")
                    await asyncio.sleep(2 * retry_count)
                    continue
                await self._invite_service.update_invite_record(task_id, user, "failed", msg)
                self._logger.warning(f"邀请用户 {user} 失败 (达到最大重试次数): {msg}")
                return "failed"
            except Exception as e:
                retry_count += 1
                if retry_count <= max_retries:
                    self._logger.error(f"邀请用户 {user} 异常, 尝试重试 ({retry_count}/{max_retries}): {e}")
                    await asyncio.sleep(2 * retry_count)
                    continue
                await self._invite_service.update_invite_record(task_id, user, "failed", str(e))
                self._logger.error(f"邀请用户 {user} 异常 (达到最大重试次数): {e}")
                return "failed"

    async def _stop_invite_task(self, task_id):
        """停止处理函数，清理资源"""
        self._logger.info(f"正在停止任务 {task_id}")
        # 资源清理逻辑（如需要）
        return True

    def is_task_running(self, task_id) -> bool:
        """判断任务是否正在运行"""
        return task_id in self._tasks and self._tasks[task_id].get("status") == "running"
