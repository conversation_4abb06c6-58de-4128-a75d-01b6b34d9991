#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
关键词提取模块
实现关键词提取和匹配功能
"""

import re
from typing import List, Dict, Any, Optional, Union

from utils.logger import get_logger


class KeywordExtractor:
    """
    关键词提取器
    提供文本关键词提取和匹配功能
    """
    
    def __init__(self):
        """初始化关键词提取器"""
        self._logger = get_logger("core.monitor.extractor")
        self._logger.info("初始化关键词提取器")
        
    async def extract(self, text: str, keywords: List[str]) -> List[str]:
        """
        从文本中提取匹配的关键词
        
        Args:
            text: 文本内容
            keywords: 关键词列表
            
        Returns:
            List[str]: 匹配到的关键词列表
        """
        if not text or not keywords:
            return []
            
        try:
            matched_keywords = []
            text_lower = text.lower()
            
            for keyword in keywords:
                if not keyword:
                    continue
                    
                keyword_lower = keyword.lower()
                if keyword_lower in text_lower:
                    matched_keywords.append(keyword)
                    self._logger.debug(f"提取到关键词: {keyword}")
                    
            return matched_keywords
            
        except Exception as e:
            self._logger.error(f"关键词提取失败: {str(e)}", exc_info=True)
            return []
            
    async def extract_with_context(self, text: str, keywords: List[str], context_size: int = 20) -> List[Dict[str, Any]]:
        """
        从文本中提取匹配的关键词及其上下文
        
        Args:
            text: 文本内容
            keywords: 关键词列表
            context_size: 上下文大小（关键词前后的字符数）
            
        Returns:
            List[Dict[str, Any]]: 匹配结果列表，每项包含关键词和上下文
        """
        if not text or not keywords:
            return []
            
        try:
            results = []
            text_lower = text.lower()
            
            for keyword in keywords:
                if not keyword:
                    continue
                    
                keyword_lower = keyword.lower()
                start_pos = 0
                
                # 查找所有匹配位置
                while True:
                    pos = text_lower.find(keyword_lower, start_pos)
                    if pos == -1:
                        break
                        
                    # 提取上下文
                    context_start = max(0, pos - context_size)
                    context_end = min(len(text), pos + len(keyword) + context_size)
                    
                    prefix = text[context_start:pos]
                    matched = text[pos:pos + len(keyword)]
                    suffix = text[pos + len(keyword):context_end]
                    
                    results.append({
                        "keyword": keyword,
                        "position": pos,
                        "prefix": prefix,
                        "matched": matched,
                        "suffix": suffix,
                        "context": f"{prefix}[{matched}]{suffix}"
                    })
                    
                    start_pos = pos + len(keyword)
                    self._logger.debug(f"提取到关键词: {keyword}, 位置: {pos}")
                    
            return results
            
        except Exception as e:
            self._logger.error(f"关键词提取(带上下文)失败: {str(e)}", exc_info=True)
            return []
            
    async def extract_regex(self, text: str, patterns: List[str]) -> List[Dict[str, Any]]:
        """
        使用正则表达式从文本中提取匹配内容
        
        Args:
            text: 文本内容
            patterns: 正则表达式模式列表
            
        Returns:
            List[Dict[str, Any]]: 匹配结果列表，每项包含模式和匹配内容
        """
        if not text or not patterns:
            return []
            
        try:
            results = []
            
            for pattern in patterns:
                if not pattern:
                    continue
                    
                try:
                    regex = re.compile(pattern, re.IGNORECASE)
                    matches = regex.finditer(text)
                    
                    for match in matches:
                        match_str = match.group(0)
                        results.append({
                            "pattern": pattern,
                            "matched": match_str,
                            "start": match.start(),
                            "end": match.end()
                        })
                        self._logger.debug(f"正则表达式 '{pattern}' 匹配到: {match_str}")
                        
                except re.error:
                    self._logger.warning(f"无效的正则表达式: {pattern}")
                    continue
                    
            return results
            
        except Exception as e:
            self._logger.error(f"正则表达式提取失败: {str(e)}", exc_info=True)
            return []
    
    async def analyze_keyword_frequency(self, text: str, keywords: List[str]) -> Dict[str, int]:
        """
        分析文本中关键词的出现频率
        
        Args:
            text: 文本内容
            keywords: 关键词列表
            
        Returns:
            Dict[str, int]: 关键词出现频率字典
        """
        if not text or not keywords:
            return {}
            
        try:
            frequency = {}
            text_lower = text.lower()
            
            for keyword in keywords:
                if not keyword:
                    continue
                    
                keyword_lower = keyword.lower()
                count = text_lower.count(keyword_lower)
                
                if count > 0:
                    frequency[keyword] = count
                    self._logger.debug(f"关键词 '{keyword}' 出现频率: {count}")
                    
            return frequency
            
        except Exception as e:
            self._logger.error(f"分析关键词频率失败: {str(e)}", exc_info=True)
            return {}
    
    async def extract_patterns(self, text: str, patterns: List[str]) -> Dict[str, List[str]]:
        """从文本中提取正则表达式匹配结果
        
        Args:
            text: 待提取的文本
            patterns: 正则表达式列表
            
        Returns:
            匹配结果，格式为 {pattern: [匹配项]}
        """
        if not text or not patterns:
            return {}
        
        results = {}
        
        for pattern_str in patterns:
            try:
                pattern = re.compile(pattern_str, re.IGNORECASE)
                matches = pattern.findall(text)
                if matches:
                    results[pattern_str] = matches
            except re.error as e:
                self._logger.error(f"正则表达式 '{pattern_str}' 错误: {e}")
                
        return results
    
    async def analyze_text(self, text: str) -> Dict[str, Any]:
        """分析文本内容
        
        Args:
            text: 待分析的文本
            
        Returns:
            分析结果
        """
        if not text:
            return {
                "char_count": 0,
                "word_count": 0,
                "line_count": 0,
                "has_url": False,
                "has_email": False,
                "has_phone": False,
                "language_hint": "unknown"
            }
            
        # 基本统计
        char_count = len(text)
        word_count = len(text.split())
        line_count = len(text.splitlines())
        
        # 检查是否包含URL
        url_pattern = r'https?://(?:[-\w.]|(?:%[\da-fA-F]{2}))+'
        has_url = bool(re.search(url_pattern, text))
        
        # 检查是否包含邮箱
        email_pattern = r'[\w\.-]+@[\w\.-]+'
        has_email = bool(re.search(email_pattern, text))
        
        # 检查是否包含电话号码
        phone_pattern = r'(?:\+\d{1,3})?[-.\s]?\(?\d{1,4}\)?[-.\s]?\d{1,4}[-.\s]?\d{1,4}'
        has_phone = bool(re.search(phone_pattern, text))
        
        # 简单语言检测
        language_hint = self._detect_language(text)
        
        return {
            "char_count": char_count,
            "word_count": word_count,
            "line_count": line_count,
            "has_url": has_url,
            "has_email": has_email,
            "has_phone": has_phone,
            "language_hint": language_hint
        }
    
    def _detect_language(self, text: str) -> str:
        """简单检测文本语言
        
        Args:
            text: 待检测的文本
            
        Returns:
            检测到的语言
        """
        # 简单的语言检测逻辑
        # 这里只做一个非常基础的检测，实际项目中可以使用更复杂的算法或库
        
        # 检查中文字符
        if any('\u4e00' <= char <= '\u9fff' for char in text):
            return "chinese"
            
        # 检查俄文字符
        if any('\u0400' <= char <= '\u04FF' for char in text):
            return "russian"
            
        # 检查阿拉伯字符
        if any('\u0600' <= char <= '\u06FF' for char in text):
            return "arabic"
            
        # 默认为英文或其他拉丁字母语言
        return "latin" 