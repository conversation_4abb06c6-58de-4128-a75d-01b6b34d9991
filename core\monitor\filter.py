#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
消息过滤器
实现消息过滤功能，包括关键词过滤和昵称过滤
"""

import re
from typing import List, Optional, Dict, Any, Union, Set

from telethon.tl.types import User, Channel, Chat

from utils.logger import get_logger


class MessageFilter:
    """
    消息过滤器
    提供关键词匹配、用户过滤等功能
    """
    
    def __init__(self):
        """初始化消息过滤器"""
        self._logger = get_logger("core.monitor.filter")
        self._logger.info("初始化消息过滤器")
        
        # 初始化过滤规则
        self.keywords: Set[str] = set()  # 监控关键词
        self.ignore_keywords: Set[str] = set()  # 忽略关键词
        self.ignore_nicknames: Set[str] = set()  # 忽略昵称
        
    def update_keywords(self, keywords: List[str]) -> None:
        """更新监控关键词
        
        Args:
            keywords: 关键词列表
        """
        self.keywords = set(keywords)
        self._logger.debug(f"更新监控关键词: {self.keywords}")
        
    def update_ignore_keywords(self, ignore_keywords: List[str]) -> None:
        """更新忽略关键词
        
        Args:
            ignore_keywords: 忽略关键词列表
        """
        self.ignore_keywords = set(ignore_keywords)
        self._logger.debug(f"更新忽略关键词: {self.ignore_keywords}")
        
    def update_ignore_nicknames(self, ignore_nicknames: List[str]) -> None:
        """更新忽略昵称
        
        Args:
            ignore_nicknames: 忽略昵称列表
        """
        self.ignore_nicknames = set(ignore_nicknames)
        self._logger.debug(f"更新忽略昵称: {self.ignore_nicknames}")
    
    def update_filters(self, task_data: Dict[str, Any]) -> None:
        """根据任务数据更新过滤规则
        
        Args:
            task_data: 任务数据
        """
        # 更新监控关键词
        if "keywords" in task_data:
            self.update_keywords(task_data["keywords"])
            
        # 更新忽略关键词
        if "ignore_keywords" in task_data:
            self.update_ignore_keywords(task_data["ignore_keywords"])
            
        # 更新忽略昵称
        if "ignore_nicknames" in task_data:
            self.update_ignore_nicknames(task_data["ignore_nicknames"])
    
    def contains_keywords(self, text: Optional[str]) -> List[str]:
        """检查文本是否包含监控关键词
        
        Args:
            text: 待检查文本
            
        Returns:
            匹配的关键词列表
        """
        if not text or not self.keywords:
            return []
        
        # 转为小写进行不区分大小写匹配
        text_lower = text.lower()
        matched_keywords = []
        
        for keyword in self.keywords:
            if keyword.lower() in text_lower:
                matched_keywords.append(keyword)
                
        return matched_keywords
    
    def should_ignore_by_keywords(self, text: Optional[str]) -> bool:
        """根据忽略关键词判断是否应该忽略文本
        
        Args:
            text: 待检查文本
            
        Returns:
            是否应该忽略
        """
        if not text or not self.ignore_keywords:
            return False
        
        # 转为小写进行不区分大小写匹配
        text_lower = text.lower()
        
        for keyword in self.ignore_keywords:
            if keyword.lower() in text_lower:
                self._logger.debug(f"文本包含忽略关键词 '{keyword}'，忽略消息")
                return True
                
        return False
    
    def should_ignore_user(self, user: Union[User, Dict[str, Any]]) -> bool:
        """根据忽略昵称判断是否应该忽略用户
        
        Args:
            user: 用户对象或用户数据字典
            
        Returns:
            是否应该忽略
        """
        if not self.ignore_nicknames:
            return False
        
        # 获取用户昵称
        if isinstance(user, User):
            first_name = user.first_name or ""
            last_name = user.last_name or ""
            username = user.username or ""
        elif isinstance(user, dict):
            first_name = user.get("first_name", "") or ""
            last_name = user.get("last_name", "") or ""
            username = user.get("username", "") or ""
        else:
            self._logger.warning(f"未知的用户类型: {type(user)}")
            return False
        
        # 构建用户完整昵称
        full_name = f"{first_name} {last_name}".strip()
        
        # 检查昵称是否匹配忽略规则
        for nickname_pattern in self.ignore_nicknames:
            pattern_lower = nickname_pattern.lower()
            
            # 检查用户名
            if username and pattern_lower in username.lower():
                self._logger.debug(f"用户名 '{username}' 匹配忽略规则 '{nickname_pattern}'，忽略用户")
                return True
                
            # 检查全名
            if full_name and pattern_lower in full_name.lower():
                self._logger.debug(f"用户昵称 '{full_name}' 匹配忽略规则 '{nickname_pattern}'，忽略用户")
                return True
        
        return False

    async def should_ignore_user_by_pattern(self, user: Dict[str, Any], ignore_nicknames: List[str]) -> bool:
        """
        判断用户是否应该被忽略
        
        Args:
            user: 用户信息字典
            ignore_nicknames: 忽略昵称关键词列表
            
        Returns:
            bool: 是否应该忽略
        """
        if not user or not ignore_nicknames:
            return False
            
        try:
            # 获取用户名和昵称
            username = user.get("username", "").lower()
            first_name = user.get("first_name", "").lower()
            last_name = user.get("last_name", "").lower()
            
            # 组合全名
            full_name = f"{first_name} {last_name}".strip()
            
            for nickname_pattern in ignore_nicknames:
                if not nickname_pattern:
                    continue
                    
                pattern_lower = nickname_pattern.lower()
                
                # 匹配用户名
                if username and pattern_lower in username:
                    self._logger.debug(f"用户名匹配忽略昵称: {nickname_pattern}")
                    return True
                    
                # 匹配昵称
                if full_name and pattern_lower in full_name:
                    self._logger.debug(f"昵称匹配忽略昵称: {nickname_pattern}")
                    return True
                    
            return False
            
        except Exception as e:
            self._logger.error(f"判断用户忽略失败: {str(e)}", exc_info=True)
            return False

    async def should_ignore(self, user: Union[User, Dict[str, Any]], text: Optional[str]) -> bool:
        """综合判断是否应该忽略消息
        
        Args:
            user: 用户对象或用户数据字典
            text: 消息文本
            
        Returns:
            是否应该忽略
        """
        # 检查是否应该忽略用户
        if self.should_ignore_user(user):
            return True
            
        # 检查是否应该忽略消息文本
        if text and self.should_ignore_by_keywords(text):
            return True
            
        return False

    async def should_ignore_message(self, message_text: str, ignore_keywords: List[str]) -> bool:
        """
        判断消息是否应该被忽略
        
        Args:
            message_text: 消息文本内容
            ignore_keywords: 忽略关键词列表
            
        Returns:
            bool: 是否应该忽略
        """
        if not message_text or not ignore_keywords:
            return False
            
        try:
            # 转换为小写进行匹配
            text_lower = message_text.lower()
            
            for keyword in ignore_keywords:
                if not keyword:
                    continue
                    
                keyword_lower = keyword.lower()
                if keyword_lower in text_lower:
                    self._logger.debug(f"消息匹配忽略关键词: {keyword}")
                    return True
                    
            return False
            
        except Exception as e:
            self._logger.error(f"判断消息忽略失败: {str(e)}", exc_info=True)
            return False
            
    async def match_message_keywords(self, message_text: str, keywords: List[str]) -> List[str]:
        """
        匹配消息中的关键词
        
        Args:
            message_text: 消息文本内容
            keywords: 关键词列表
            
        Returns:
            List[str]: 匹配到的关键词列表
        """
        if not message_text or not keywords:
            return []
            
        try:
            matched_keywords = []
            text_lower = message_text.lower()
            
            for keyword in keywords:
                if not keyword:
                    continue
                    
                keyword_lower = keyword.lower()
                if keyword_lower in text_lower:
                    matched_keywords.append(keyword)
                    self._logger.debug(f"消息匹配关键词: {keyword}")
                    
            return matched_keywords
            
        except Exception as e:
            self._logger.error(f"匹配消息关键词失败: {str(e)}", exc_info=True)
            return []
            
    async def match_regex_keywords(self, message_text: str, regex_patterns: List[str]) -> List[str]:
        """
        使用正则表达式匹配消息中的关键词
        
        Args:
            message_text: 消息文本内容
            regex_patterns: 正则表达式模式列表
            
        Returns:
            List[str]: 匹配到的模式列表
        """
        if not message_text or not regex_patterns:
            return []
            
        try:
            matched_patterns = []
            
            for pattern in regex_patterns:
                if not pattern:
                    continue
                    
                try:
                    regex = re.compile(pattern, re.IGNORECASE)
                    if regex.search(message_text):
                        matched_patterns.append(pattern)
                        self._logger.debug(f"消息匹配正则表达式: {pattern}")
                except re.error:
                    self._logger.warning(f"无效的正则表达式: {pattern}")
                    continue
                    
            return matched_patterns
            
        except Exception as e:
            self._logger.error(f"正则匹配消息关键词失败: {str(e)}", exc_info=True)
            return [] 