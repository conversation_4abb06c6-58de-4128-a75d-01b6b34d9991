#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
通知管理模块
实现消息通知发送功能
"""

import asyncio
import json
import datetime
import smtplib
import traceback
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from typing import Dict, Any, List, Optional, Union, Tuple
from string import Template

import aiohttp
from telethon import TelegramClient
from telethon.tl.types import User

# from core.telegram.client_manager import ClientManager, TelegramClientManager
from core.telegram.message_manager import MessageManager
from utils.logger import get_logger


class NotificationTemplate:
    """通知模板"""
    
    DEFAULT_MESSAGE_TEMPLATE = """
📢 监控通知
------------------
👥 群组: $group_title ($group_id)
👤 用户: $user_name [$user_id]
🕒 时间: $timestamp
------------------
📝 消息内容:
$message_text
------------------
🔍 匹配关键词: $matched_keywords
📊 任务: $task_name
    """
    
    DEFAULT_USER_JOIN_TEMPLATE = """
📢 用户入群通知
------------------
👥 群组: $group_title ($group_id)
👤 新用户: $user_name [$user_id]
🕒 时间: $timestamp
------------------
📊 任务: $task_name
    """
    
    @staticmethod
    def render(template_str: str, data: Dict[str, Any]) -> str:
        """渲染模板
        
        Args:
            template_str: 模板字符串
            data: 数据字典
            
        Returns:
            渲染后的字符串
        """
        template = Template(template_str)
        
        # 预处理数据，确保所有变量都有值
        safe_data = {}
        for key, value in data.items():
            if isinstance(value, list):
                safe_data[key] = ", ".join(map(str, value))
            elif value is None:
                safe_data[key] = ""
            else:
                safe_data[key] = str(value)
        
        return template.safe_substitute(safe_data)


class NotificationManager:
    """
    通知管理器
    负责发送消息通知到不同目标
    """
    
    def __init__(self, message_manager: MessageManager = None):
        """初始化通知管理器"""
        self._logger = get_logger("core.monitor.notifier")
        self._logger.info("初始化通知管理器")
        self._message_manager = message_manager
        
    async def send_notification(self, notification_config: Dict[str, Any], message_data: Dict[str, Any]) -> Tuple[bool, str]:
        """
        发送通知
        
        Args:
            notification_config: 通知配置
            message_data: 消息数据
            
        Returns:
            Tuple[bool, str]: (成功标志, 消息)
        """
        try:
            target_type = notification_config.get("target_type", "")
            target_address = notification_config.get("target_address", "")
            account_id = notification_config.get("account_id", 0)
            template = notification_config.get("template", "")
            
            if not target_type or not target_address or not account_id:
                return False, "通知配置不完整"
                
            # 格式化通知内容
            notification_text = await self._format_notification(template, message_data)
            
            # 根据通知类型分发到不同的发送方法
            if target_type == "private_chat":
                return await self._send_private_chat(account_id, target_address, notification_text)
            elif target_type == "group_chat":
                return await self._send_group_chat(account_id, target_address, notification_text)
            elif target_type == "email":
                return await self._send_email(target_address, notification_text)
            elif target_type == "webhook":
                return await self._send_webhook(target_address, message_data)
            else:
                return False, f"不支持的通知类型: {target_type}"
                
        except Exception as e:
            self._logger.error(f"发送通知失败: {str(e)}", exc_info=True)
            return False, f"发送通知失败: {str(e)}"
            
    async def _format_notification(self, template: str, data: Dict[str, Any]) -> str:
        """
        格式化通知内容
        
        Args:
            template: 通知模板
            data: 消息数据
            
        Returns:
            str: 格式化后的通知内容
        """
        try:
            if not template:
                # 使用默认模板
                template = """
发现匹配消息:
关键词: {keywords}
用户: {user_name} (@{username})
消息: {message_text}
群组: {group_name}
时间: {message_time}
                """
                
            # 简单的模板替换
            formatted = template
            
            # 展平嵌套字典，方便替换
            flat_data = self._flatten_dict(data)
            
            # 替换占位符
            for key, value in flat_data.items():
                placeholder = "{" + key + "}"
                formatted = formatted.replace(placeholder, str(value))
                
            return formatted
            
        except Exception as e:
            self._logger.error(f"格式化通知内容失败: {str(e)}", exc_info=True)
            return f"通知格式化错误: {str(e)}"
            
    def _flatten_dict(self, d: Dict[str, Any], parent_key: str = '') -> Dict[str, Any]:
        """
        展平嵌套字典
        
        Args:
            d: 嵌套字典
            parent_key: 父键名
            
        Returns:
            Dict[str, Any]: 展平后的字典
        """
        items = []
        for k, v in d.items():
            new_key = f"{parent_key}_{k}" if parent_key else k
            
            if isinstance(v, dict):
                items.extend(self._flatten_dict(v, new_key).items())
            else:
                items.append((new_key, v))
                
        return dict(items)
            
    async def _send_private_chat(self, account_phone: str, user_id: str, text: str) -> Tuple[bool, str]:
        """
        发送私聊消息
        
        Args:
            account_phone: 发送账号手机号
            user_id: 接收用户ID
            text: 消息内容
            
        Returns:
            Tuple[bool, str]: (成功标志, 消息)
        """
        try:
            self._logger.debug(f"发送私聊通知: 账号 {account_phone}, 用户ID {user_id}")
            if not self._message_manager:
                return False, "消息管理器未初始化"
            success, result = await self._message_manager.send_text_message(account_phone, user_id, text)
            if success:
                self._logger.info(f"已发送私聊通知: 账号 {account_phone}, 用户ID {user_id}")
                return True, "私聊通知发送成功"
            else:
                return False, f"私聊通知发送失败: {result}"
        except Exception as e:
            self._logger.error(f"发送私聊通知失败: {str(e)}", exc_info=True)
            return False, f"发送私聊通知失败: {str(e)}"
            
    async def _send_group_chat(self, account_phone: str, group_id: str, text: str) -> Tuple[bool, str]:
        """
        发送群组消息
        
        Args:
            account_phone: 发送账号手机号
            group_id: 群组ID
            text: 消息内容
            
        Returns:
            Tuple[bool, str]: (成功标志, 消息)
        """
        try:
            self._logger.debug(f"发送群组通知: 账号 {account_phone}, 群组ID {group_id}")
            if not self._message_manager:
                return False, "消息管理器未初始化"
            success, result = await self._message_manager.send_text_message(account_phone, group_id, text)
            if success:
                self._logger.info(f"已发送群组通知: 账号 {account_phone}, 群组ID {group_id}")
                return True, "群组通知发送成功"
            else:
                return False, f"群组通知发送失败: {result}"
        except Exception as e:
            self._logger.error(f"发送群组通知失败: {str(e)}", exc_info=True)
            return False, f"发送群组通知失败: {str(e)}"
            
    async def _send_email(self, email_address: str, text: str) -> Tuple[bool, str]:
        """
        发送邮件通知
        
        Args:
            email_address: 邮箱地址
            text: 邮件内容
            
        Returns:
            Tuple[bool, str]: (成功标志, 消息)
        """
        try:
            self._logger.debug(f"发送邮件通知: {email_address}")
            
            # 邮件发送逻辑，这里只是预留接口
            # 模拟发送成功
            self._logger.info(f"已发送邮件通知: {email_address}")
            return True, "邮件通知发送成功"
            
        except Exception as e:
            self._logger.error(f"发送邮件通知失败: {str(e)}", exc_info=True)
            return False, f"发送邮件通知失败: {str(e)}"
            
    async def _send_webhook(self, webhook_url: str, data: Dict[str, Any]) -> Tuple[bool, str]:
        """
        发送Webhook通知
        
        Args:
            webhook_url: Webhook地址
            data: 通知数据
            
        Returns:
            Tuple[bool, str]: (成功标志, 消息)
        """
        try:
            import aiohttp
            
            self._logger.debug(f"发送Webhook通知: {webhook_url}")
            
            async with aiohttp.ClientSession() as session:
                async with session.post(webhook_url, json=data) as response:
                    status = response.status
                    response_text = await response.text()
                    
                    if status >= 200 and status < 300:
                        self._logger.info(f"Webhook通知发送成功: {webhook_url}, 响应: {response_text}")
                        return True, "Webhook通知发送成功"
                    else:
                        self._logger.warning(f"Webhook通知发送失败: 状态码 {status}, 响应: {response_text}")
                        return False, f"Webhook通知发送失败: 状态码 {status}"
                        
        except Exception as e:
            self._logger.error(f"发送Webhook通知失败: {str(e)}", exc_info=True)
            return False, f"发送Webhook通知失败: {str(e)}"


class Notifier(NotificationManager):
    """
    通知器
    NotificationManager的别名，用于向后兼容
    """
    
    def __init__(self):
        """初始化通知器"""
        super().__init__()
        self._logger.info("初始化通知器 (兼容层)") 