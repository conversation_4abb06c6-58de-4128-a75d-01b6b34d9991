#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
3proxy服务管理器
负责3proxy服务的启动、停止、重启、状态检测等
"""
import os
import asyncio
import subprocess
from typing import Tuple, Optional
from contextlib import asynccontextmanager

from logging import getLogger

class Proxy3ProxyManager:
    """3proxy服务管理类，负责3proxy服务的生命周期管理"""
    
    def __init__(self, config_path: Optional[str] = None, proxy_exe_path: Optional[str] = None):
        """
        初始化3proxy管理器
        
        Args:
            config_path: 3proxy配置文件完整路径
            proxy_exe_path: 3proxy可执行文件完整路径
        """
        self._logger = getLogger("core.proxy.proxy_3proxy_manager")
        self.is_running = False
        self.process = None
        self.config_path = config_path
        # 3proxy可执行文件路径
        self.proxy_exe = proxy_exe_path if proxy_exe_path else ("3proxy.exe" if os.name == 'nt' else "3proxy")
        print(self.proxy_exe)
    
    async def __aenter__(self):
        """异步上下文管理器入口"""
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器退出，确保资源清理"""
        await self.stop_service(silent=True)
    
    async def _run_command(self, command: str) -> Tuple[str, bool]:
        """
        执行命令并返回输出
        
        Args:
            command: 要执行的命令

        Returns:
            Tuple[str, bool]: (命令输出, 是否成功)
        """
        try:
            process = await asyncio.create_subprocess_shell(
                command,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE,
                shell=True
            )
            stdout, stderr = await process.communicate()
            
            # 尝试不同编码解码输出
            encodings = ['gbk', 'utf-8'] if os.name == 'nt' else ['utf-8', 'gbk']
            for encoding in encodings:
                try:
                    stdout_str = stdout.decode(encoding, errors='ignore')
                    stderr_str = stderr.decode(encoding, errors='ignore')
                    break
                except Exception:
                    continue
            
            output = stdout_str + stderr_str
            success = process.returncode == 0
            return output, success
        except Exception as e:
            self._logger.exception(f"执行命令失败: {command}, 错误: {e}")
            return str(e), False
    
    async def install_service(self) -> Tuple[bool, str]:
        """
        安装3proxy服务
        
        Returns:
            Tuple[bool, str]: (是否成功, 结果信息或失败原因)
        """
        if os.name != 'nt':
            error_msg = "安装服务功能仅支持Windows系统"
            self._logger.warning(error_msg)
            return False, error_msg
            
        # 检查可执行文件是否存在
        if not os.path.exists(self.proxy_exe):
            error_msg = f"3proxy可执行文件不存在: {self.proxy_exe}"
            self._logger.error(error_msg)
            return False, error_msg
            
        # 检查配置文件是否存在
        if not os.path.exists(self.config_path):
            error_msg = f"3proxy配置文件不存在: {self.config_path}"
            self._logger.error(error_msg)
            return False, error_msg
            
        try:
            # 安装服务
            install_cmd = f'"{self.proxy_exe}" --install "{self.config_path}"'
            self._logger.info(f"安装3proxy服务: {install_cmd}")
            output, success = await self._run_command(install_cmd)
            
            if success or "成功" in output or "success" in output.lower():
                self._logger.info("3proxy服务安装成功")
                return True, "服务安装成功"
            else:
                error_msg = f"安装3proxy服务失败: {output}"
                self._logger.warning(error_msg)
                return False, error_msg
        except Exception as e:
            error_msg = f"安装3proxy服务时出错: {e}"
            self._logger.exception(error_msg)
            return False, error_msg
    
    async def uninstall_service(self) -> Tuple[bool, str]:
        """
        卸载3proxy服务
        
        Returns:
            Tuple[bool, str]: (是否成功, 结果信息或失败原因)
        """
        if os.name != 'nt':
            error_msg = "卸载服务功能仅支持Windows系统"
            self._logger.warning(error_msg)
            return False, error_msg
            
        # 检查可执行文件是否存在
        if not os.path.exists(self.proxy_exe):
            error_msg = f"3proxy可执行文件不存在: {self.proxy_exe}"
            self._logger.error(error_msg)
            return False, error_msg
            
        try:
            # 先停止服务
            output, _ = await self._run_command("sc query 3proxy")
            if "3proxy" in output and "STOPPED" not in output:
                self._logger.info("停止3proxy服务...")
                await self._run_command("net stop 3proxy")
                await asyncio.sleep(1)  # 等待服务停止
                
            # 卸载服务
            remove_cmd = f'"{self.proxy_exe}" --remove'
            self._logger.info(f"卸载3proxy服务: {remove_cmd}")
            output, success = await self._run_command(remove_cmd)
            
            if success or "成功" in output or "success" in output.lower() or "服务已标记为删除" in output:
                self._logger.info("3proxy服务卸载成功")
                return True, "服务卸载成功"
            else:
                error_msg = f"卸载3proxy服务失败: {output}"
                self._logger.warning(error_msg)
                return False, error_msg
        except Exception as e:
            error_msg = f"卸载3proxy服务时出错: {e}"
            self._logger.exception(error_msg)
            return False, error_msg
    
    async def check_service_status(self) -> bool:
        """
        检查3proxy服务运行状态
        
        Returns:
            bool: 服务是否正在运行
        """
        self._logger.debug("检查3proxy服务状态")
        
        try:
            # 检查是否作为Windows服务运行
            if os.name == 'nt':
                try:
                    # 添加超时处理
                    task = asyncio.create_task(self._run_command("sc query 3proxy"))
                    output, _ = await asyncio.wait_for(task, timeout=3.0)
                    if "RUNNING" in output:
                        self._logger.debug("3proxy作为Windows服务运行中")
                        self.is_running = True
                        return True
                except asyncio.TimeoutError:
                    self._logger.warning("检查服务状态超时，假定服务未运行")
                    return False
            
            # 检查进程是否运行
            if self.process and self.process.poll() is None:
                self._logger.debug("3proxy作为进程运行中")
                self.is_running = True
                return True
                
            # 检查系统中是否有3proxy进程
            try:
                if os.name == 'nt':
                    task = asyncio.create_task(self._run_command('tasklist | findstr 3proxy'))
                    output, _ = await asyncio.wait_for(task, timeout=3.0)
                else:
                    task = asyncio.create_task(self._run_command('ps aux | grep 3proxy | grep -v grep'))
                    output, _ = await asyncio.wait_for(task, timeout=3.0)
                
                if "3proxy" in output and not output.strip().startswith("INFO"):
                    self._logger.debug("在系统进程中发现3proxy")
                    self.is_running = True
                    return True
            except asyncio.TimeoutError:
                self._logger.warning("检查进程超时，假定服务未运行")
                return False
                
            self._logger.debug("3proxy服务未运行")
            self.is_running = False
            return False
            
        except Exception as e:
            self._logger.error(f"检查服务状态出错: {e}")
            # 出错时假设服务未运行
            self.is_running = False
            return False
    
    async def is_service_installed(self) -> bool:
        """
        检查3proxy服务是否已安装
        
        Returns:
            bool: 是否已安装
        """
        if os.name != 'nt':
            # 非Windows系统不支持服务安装
            return False
            
        output, _ = await self._run_command("sc query 3proxy")
        return "3proxy" in output and "1060" not in output and "服务名称不存在" not in output and "specified service does not exist" not in output.lower()
    
    async def check_service_config(self) -> bool:
        """
        检查3proxy服务配置是否正确
        
        Returns:
            bool: 配置是否正确
        """
        if os.name != 'nt' or not await self.is_service_installed():
            return False
            
        try:
            # 获取服务二进制路径
            output, _ = await self._run_command("sc qc 3proxy")
            
            # 检查配置文件路径是否在服务命令行中
            config_path_base = os.path.basename(self.config_path)
            return config_path_base in output
        except Exception as e:
            self._logger.error(f"检查服务配置出错: {e}")
            return False
    
    async def start_service(self) -> Tuple[bool, str]:
        """
        启动3proxy服务
        
        Returns:
            Tuple[bool, str]: (是否成功, 结果信息或失败原因)
        """
        # 先检查服务是否已经在运行
        if await self.check_service_status():
            self._logger.info("3proxy服务已经在运行中")
            return True, "服务已经在运行中"
            
        # 检查可执行文件是否存在
        if os.name == 'nt' and not os.path.exists(self.proxy_exe):
            error_msg = f"3proxy可执行文件不存在: {self.proxy_exe}"
            self._logger.error(error_msg)
            return False, error_msg
            
        # 检查配置文件是否存在
        if not os.path.exists(self.config_path):
            error_msg = f"3proxy配置文件不存在: {self.config_path}"
            self._logger.error(error_msg)
            return False, error_msg
        
        # Windows系统下作为服务运行
        if os.name == 'nt':
            try:
                # 检查服务是否已安装
                is_installed = await self.is_service_installed()
                is_config_correct = await self.check_service_config()
                
                # 如果服务已安装但配置不正确，需要重新安装
                if is_installed and not is_config_correct:
                    self._logger.info("3proxy服务已安装但配置不正确，需要重新安装")
                    # 卸载服务
                    await self.uninstall_service()
                    is_installed = False
                
                # 如果服务未安装，安装服务
                if not is_installed:
                    self._logger.info("3proxy服务未安装，开始安装")
                    try:
                        # 添加超时处理
                        install_task = asyncio.create_task(self.install_service())
                        install_result, install_msg = await asyncio.wait_for(install_task, timeout=20.0)
                    except asyncio.TimeoutError:
                        self._logger.error("安装服务超时，继续尝试启动")
                        install_result, install_msg = False, "安装服务超时"
                    
                    if not install_result:
                        self._logger.warning(f"安装服务失败: {install_msg}，尝试以进程方式启动")
                        # 不返回错误，而是继续尝试以进程方式启动
                
                # 尝试启动服务
                self._logger.info("启动3proxy服务")
                try:
                    # 添加超时处理
                    start_task = asyncio.create_task(self._run_command("net start 3proxy"))
                    output, success = await asyncio.wait_for(start_task, timeout=10.0)
                except asyncio.TimeoutError:
                    self._logger.error("启动服务命令超时，尝试以进程方式启动")
                    output, success = "", False
                
                if success or "成功" in output or "successfully" in output.lower():
                    self._logger.info("3proxy服务启动成功")
                    self.is_running = True
                    return True, "服务启动成功"
                else:
                    # 处理服务已经启动的情况
                    if "1056" in output or "已经启动" in output or "already running" in output.lower():
                        self._logger.info("3proxy服务已经在运行中")
                        self.is_running = True
                        return True, "服务已经在运行中"
                        
                    self._logger.error(f"启动服务失败: {output}，尝试以进程方式启动")
                    # 不返回错误，而是继续尝试以进程方式启动
                    
            except Exception as e:
                self._logger.exception(f"作为Windows服务启动3proxy失败: {e}")
                # 不返回错误，而是继续尝试以进程方式启动
                
        # 如果无法作为服务启动或者是Unix系统，尝试以进程方式启动
        try:
            self._logger.info("尝试以进程方式启动3proxy")
            
            # 构建启动命令和参数
            if os.name == 'nt':
                # Windows系统
                self.process = subprocess.Popen(
                    [self.proxy_exe, self.config_path],
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    creationflags=subprocess.CREATE_NO_WINDOW
                )
            else:
                # 类Unix系统
                self.process = await asyncio.create_subprocess_exec(
                    self.proxy_exe, self.config_path,
                    stdout=asyncio.subprocess.PIPE,
                    stderr=asyncio.subprocess.PIPE
                )
            
            # 等待片刻确认进程已启动
            await asyncio.sleep(1)
            
            # 检查进程是否存活
            if os.name == 'nt':
                if self.process.poll() is not None:
                    stderr = self.process.stderr.read().decode("utf-8", errors="ignore")
                    error_msg = f"3proxy启动失败: {stderr}"
                    self._logger.error(error_msg)
                    return False, error_msg
            else:
                if self.process.returncode is not None:
                    stderr = await self.process.stderr.read()
                    stderr = stderr.decode("utf-8", errors="ignore")
                    error_msg = f"3proxy启动失败: {stderr}"
                    self._logger.error(error_msg)
                    return False, error_msg
            
            self.is_running = True
            self._logger.info("3proxy服务已启动(进程模式)")
            return True, "服务启动成功(进程模式)"
            
        except Exception as e:
            error_msg = f"启动3proxy服务时出错: {e}"
            self._logger.exception(error_msg)
            return False, error_msg
    
    async def reload_config(self) -> Tuple[bool, str]:
        """
        重新加载配置文件，无需重启服务
        
        Returns:
            Tuple[bool, str]: (是否成功, 结果信息或失败原因)
        """
        # 检查服务是否在运行
        if not await self.check_service_status():
            self._logger.warning("服务未运行，无法重载配置")
            return False, "服务未运行，请先启动服务"
            
        # 检查配置文件是否存在
        if not os.path.exists(self.config_path):
            error_msg = f"配置文件不存在: {self.config_path}"
            self._logger.error(error_msg)
            return False, error_msg
            
        try:
            if os.name == 'nt':
                # Windows系统下通过重启服务来重载配置
                # 先停止服务
                self._logger.info("重载配置：停止服务")
                stop_output, stop_success = await self._run_command("net stop 3proxy")
                
                if not stop_success and "1062" not in stop_output and "并未启动" not in stop_output:
                    error_msg = f"停止服务失败: {stop_output}"
                    self._logger.error(error_msg)
                    return False, error_msg
                
                # 短暂等待确保服务完全停止
                await asyncio.sleep(1)
                
                # 启动服务
                self._logger.info("重载配置：启动服务")
                start_output, start_success = await self._run_command("net start 3proxy")
                
                if start_success or "成功" in start_output or "successfully" in start_output.lower():
                    self._logger.info("配置重载成功")
                    return True, "配置重载成功"
                else:
                    error_msg = f"启动服务失败: {start_output}"
                    self._logger.error(error_msg)
                    return False, error_msg
            else:
                # Unix系统可以发送HUP信号重载配置
                self._logger.info("向3proxy进程发送HUP信号以重载配置")
                output, success = await self._run_command("pkill -HUP -f 3proxy")
                
                if success:
                    self._logger.info("配置重载成功")
                    return True, "配置重载成功"
                else:
                    error_msg = f"重载配置失败: {output}"
                    self._logger.error(error_msg)
                    return False, error_msg
        except Exception as e:
            error_msg = f"重载配置时出错: {e}"
            self._logger.exception(error_msg)
            return False, error_msg
        
    async def stop_service(self, silent: bool = False) -> Tuple[bool, str]:
        """
        停止3proxy服务，但不删除服务注册
        
        Args:
            silent: 是否静默模式，不抛出异常
            
        Returns:
            Tuple[bool, str]: (是否成功, 结果信息或失败原因)
            
        Raises:
            RuntimeError: 非静默模式下停止失败时抛出
        """
        self._logger.info("停止3proxy服务")
        success = False
        error_msg = None
        
        # Windows系统先尝试停止服务
        if os.name == 'nt':
            try:
                # 检查服务是否存在
                output, _ = await self._run_command("sc query 3proxy")
                if "3proxy" in output:
                    # 停止服务
                    self._logger.info("停止Windows 3proxy服务")
                    output, _ = await self._run_command("sc stop 3proxy")
                    
                    # 检查服务状态
                    check_output, _ = await self._run_command("sc query 3proxy")
                    if "STOPPED" in check_output or "1062" in output or "RUNNING" not in check_output:
                        success = True
                        # 注意：不再删除服务
                    else:
                        self._logger.warning("3proxy服务停止失败，将尝试终止进程")
                        error_msg = "服务停止失败，将尝试终止进程"
            except Exception as e:
                self._logger.warning(f"停止Windows服务时出错: {e}")
                error_msg = str(e)
        
        # 如果服务未停止，则尝试终止进程
        if not success:
            try:
                # 终止系统中的3proxy进程
                if os.name == 'nt':
                    output, _ = await self._run_command('taskkill /f /im 3proxy.exe')
                else:
                    output, _ = await self._run_command('pkill -f 3proxy')
                
                self._logger.debug(f"终止进程命令结果: {output}")
                
                # 终止当前对象持有的进程
                if self.process:
                    try:
                        if os.name == 'nt':
                            if self.process.poll() is None:
                                self.process.terminate()
                                await asyncio.sleep(1)
                                if self.process.poll() is None:
                                    self.process.kill()
                        else:
                            self.process.terminate()
                            await asyncio.sleep(1)
                            if self.process.returncode is None:
                                self.process.kill()
                    except Exception as e:
                        self._logger.warning(f"终止进程对象时出错: {e}")
                
                # 检查进程是否已终止
                await asyncio.sleep(1)
                if await self.check_service_status():
                    error_msg = "无法终止3proxy进程"
                    self._logger.warning(error_msg)
                else:
                    self._logger.info("3proxy进程已终止")
                    success = True
            except Exception as e:
                error_msg = f"终止3proxy进程时出错: {e}"
                self._logger.warning(error_msg)
        
        # 更新状态
        self.is_running = await self.check_service_status()
        
        # 非静默模式下失败时抛出异常
        if not silent and not success and error_msg:
            raise RuntimeError(f"停止3proxy服务失败: {error_msg}")
            
        return (success, "服务已停止" if success else f"停止服务失败: {error_msg}")
    
    async def restart_service(self, silent: bool = False) -> Tuple[bool, str]:
        """
        重启3proxy服务
        
        Args:
            silent: 是否静默模式，不抛出异常
            
        Returns:
            Tuple[bool, str]: (是否成功, 结果信息或失败原因)
        """
        self._logger.info("重启3proxy服务")
        
        # 检查配置文件是否存在
        if not os.path.exists(self.config_path):
            error_msg = f"3proxy配置文件不存在: {self.config_path}"
            self._logger.error(error_msg)
            return False, error_msg
        
        # 先检查服务是否在运行并且是否已安装
        is_running = await self.check_service_status()
        is_installed = await self.is_service_installed()
        is_config_correct = await self.check_service_config()
        
        # 如果服务已安装但配置不正确，需要更新配置
        if is_installed and not is_config_correct:
            self._logger.info("3proxy服务配置不正确，需要更新配置")
            # 卸载服务后重新安装
            await self.uninstall_service()
            is_installed = False
        
        # 优先尝试使用热重载配置
        if is_running:
            self._logger.info("服务正在运行，尝试热重载配置")
            reload_result, reload_msg = await self.reload_config()
            if reload_result:
                self._logger.info("服务配置热重载成功")
                return True, "服务配置热重载成功"
            else:
                self._logger.warning(f"配置热重载失败: {reload_msg}，尝试通过重启服务方式更新配置")
        
        # 如果热重载失败或服务未运行，尝试通过重启服务方式更新配置
        try:
            # 停止服务
            stop_result, stop_msg = await self.stop_service(silent=True)
            if not stop_result:
                self._logger.warning(f"停止服务时出错: {stop_msg}，将继续尝试启动")
            
            # 等待一段时间确保服务完全停止
            await asyncio.sleep(1)
        
            # 启动服务
            start_result, start_msg = await self.start_service()
            if start_result:
                self._logger.info("3proxy服务重启成功")
                return True, "服务重启成功"
            elif "已经启动" in start_msg or "already" in start_msg.lower():
                # 如果服务已经在运行，也视为成功
                self._logger.info("3proxy服务已经在运行中")
                return True, "服务已经在运行中"
            else:
                error_msg = f"3proxy服务重启失败: {start_msg}"
                self._logger.error(error_msg)
                return False, error_msg
        except Exception as e:
            error_msg = f"重启3proxy服务时出错: {e}"
            self._logger.exception(error_msg)
            return False, error_msg
    
    @asynccontextmanager
    async def service_session(self):
        """
        服务会话上下文管理器，确保服务在会话结束后正确关闭
        
        使用示例:
        ```
        async with proxy_manager.service_session():
            # 在这里使用3proxy服务
            pass
        # 退出后服务会自动关闭
        ```
        """
        try:
            await self.start_service()
            yield self
        finally:
            await self.stop_service(silent=True)

if __name__ == "__main__":
    import asyncio
    import sys
    
    async def main():
        try:
            # 示例：传入自定义的exe路径和配置文件路径
            proxy_exe_path = r"D:\3proxy\bin64\3proxy.exe"  # 确保指向.exe文件
            config_path = r"D:\3proxy\bin64\3proxy.cfg"
            
            print(f"使用3proxy可执行文件: {proxy_exe_path}")
            print(f"使用配置文件: {config_path}")
            
            # 创建3proxy管理器实例，传入自定义路径
            manager = Proxy3ProxyManager(config_path=config_path, proxy_exe_path=proxy_exe_path)
            
            # 菜单循环
            while True:
                print("\n===== 3Proxy 服务管理 =====")
                print("1. 启动服务")
                print("2. 停止服务")
                print("3. 重启服务")
                print("4. 检查状态")
                print("5. 安装服务")
                print("6. 卸载服务")
                print("0. 退出")
                
                try:
                    choice = input("\n请输入选项 (0-6): ").strip()
                    
                    if choice == "0":
                        print("正在退出...")
                        # 确保退出前停止服务
                        await manager.stop_service(silent=True)
                        break
                        
                    elif choice == "1":  # 启动服务
                        print("正在启动3proxy服务...")
                        success, message = await manager.start_service()
                        print(f"启动结果: {'成功' if success else '失败'}")
                        print(f"详细信息: {message}")
                    
                    elif choice == "2":  # 停止服务
                        print("正在停止3proxy服务...")
                        success, message = await manager.stop_service()
                        print(f"停止结果: {'成功' if success else '失败'}")
                        print(f"详细信息: {message}")
                    
                    elif choice == "3":  # 重启服务
                        print("正在重启3proxy服务...")
                        success, message = await manager.restart_service()
                        print(f"重启结果: {'成功' if success else '失败'}")
                        print(f"详细信息: {message}")
                    
                    elif choice == "4":  # 检查状态
                        print("正在检查3proxy服务状态...")
                        is_running = await manager.check_service_status()
                        print(f"服务状态: {'正在运行' if is_running else '未运行'}")
                    
                    elif choice == "5":  # 安装服务
                        print("正在安装3proxy服务...")
                        success, message = await manager.install_service()
                        print(f"安装结果: {'成功' if success else '失败'}")
                        print(f"详细信息: {message}")
                    
                    elif choice == "6":  # 卸载服务
                        print("正在卸载3proxy服务...")
                        success, message = await manager.uninstall_service()
                        print(f"卸载结果: {'成功' if success else '失败'}")
                        print(f"详细信息: {message}")
                    
                    else:
                        print("无效选项，请重新输入")
                    
                    # 每次操作后暂停一下，让用户看清结果
                    print("\n按Enter键继续...")
                    input()
                    
                except Exception as e:
                    print(f"操作出错: {e}")
                    print("\n按Enter键继续...")
                    input()
        except Exception as e:
            print(f"程序运行出错: {e}")
            return 1
        return 0
    
    # 获取或创建事件循环
    try:
        loop = asyncio.get_event_loop()
    except RuntimeError:
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
    
    # 运行主函数
    exit_code = loop.run_until_complete(main())
    sys.exit(exit_code) 