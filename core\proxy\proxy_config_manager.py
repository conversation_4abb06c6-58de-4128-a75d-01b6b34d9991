#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
3proxy配置文件管理器
负责3proxy配置文件的生成、增量更新、删除等
"""
import os
from typing import List, Optional, Dict, Any
from config import config
from utils.logger import get_logger
from data.models.proxy import ProxyModel

class ProxyConfigManager:
    def __init__(self):
        self._logger = get_logger("core.proxy.config_manager")
        self.config_path = config.proxy_cfg
        self.log_path = config.proxy_log
        
    def _ensure_config_dir_exists(self):
        """确保配置文件目录存在"""
        os.makedirs(os.path.dirname(self.config_path), exist_ok=True)
        
    def _write_base_config(self, file_obj):
        """写入基本配置
        
        Args:
            file_obj: 文件对象
        """
        file_obj.write("nserver 8.8.8.8\n")
        file_obj.write("nserver 8.8.4.4\n")
        file_obj.write("nscache 65536\n")
        file_obj.write("timeouts 1 5 30 60 180 1800 15 600\n")
        file_obj.write("auth none\n")
        file_obj.write("service\n")  # 在Windows上作为服务启动
        file_obj.write(f"log {self.log_path} D\n")
        file_obj.write("rotate 2\n")
        file_obj.write("logformat \"- +_L%t.%. %N.%p %E %U %C:%c %R:%r %O %I %h %T\"\n\n")
        
    def _generate_proxy_line(self, proxy: ProxyModel) -> str:
        """生成代理配置行
        
        Args:
            proxy: 代理模型
            
        Returns:
            配置行字符串
        """
        if proxy.proxy_type.lower() == "socks5":
            auth_part = f"-a:{proxy.username}:{proxy.password} " if proxy.username and proxy.password else ""
            return f"socks -na {auth_part}-i0.0.0.0 -p{proxy.port} -e{proxy.host}\n"
        elif proxy.proxy_type.lower() in ["http", "https"]:
            auth_part = f"-a:{proxy.username}:{proxy.password} " if proxy.username and proxy.password else ""
            return f"proxy {auth_part}-i0.0.0.0 -p{proxy.port} -e{proxy.host}\n"
        return ""

    def generate_config(self, proxies: List[ProxyModel]) -> Optional[str]:
        """生成3proxy配置文件
        Args:
            proxies: 代理列表
        Returns:
            配置文件路径或None
        """
        try:
            self._ensure_config_dir_exists()
            
            local_proxies = [p for p in proxies if p.is_local]
            if not local_proxies:
                self._logger.warning("没有找到本地代理配置")
                return None
                
            with open(self.config_path, 'w') as f:
                # 写入基本配置
                self._write_base_config(f)
                
                # 写入代理配置
                for proxy in local_proxies:
                    config_line = self._generate_proxy_line(proxy)
                    if config_line:
                        f.write(config_line)
                        
            return self.config_path
        except Exception as e:
            self._logger.exception(f"生成3proxy配置文件时出错: {e}")
            return None

    def update_config(self, proxy: Optional[ProxyModel] = None, delete: bool = False) -> bool:
        """增量更新配置文件
        Args:
            proxy: 要添加或删除的代理信息，None表示只确保基本配置存在
            delete: 是否删除代理配置行
        Returns:
            是否成功更新
        """
        try:
            self._ensure_config_dir_exists()
            
            # 如果配置文件不存在，创建基本配置
            if not os.path.exists(self.config_path):
                with open(self.config_path, 'w') as f:
                    self._write_base_config(f)
                    
            # 如果只是确保基本配置存在，到此为止
            if proxy is None:
                return True
                
            # 删除代理配置行
            if delete:
                with open(self.config_path, 'r') as f:
                    lines = f.readlines()
                    
                # 构建匹配模式：同时匹配IP和端口
                ip_port_pattern = f"-{proxy.host} -p{proxy.port}"
                filtered_lines = []
                removed = False
                
                for line in lines:
                    line_stripped = line.strip()
                    if (line_stripped.startswith("socks ") or line_stripped.startswith("proxy ")) and ip_port_pattern in line_stripped:
                        removed = True
                        self._logger.debug(f"移除配置行: {line_stripped}")
                        continue
                    filtered_lines.append(line)
                    
                if removed:
                    with open(self.config_path, 'w') as f:
                        f.writelines(filtered_lines)
                    self._logger.info(f"成功从配置中移除代理: {proxy.host}:{proxy.port}")
                    return True
                else:
                    self._logger.warning(f"未找到要删除的代理配置: {proxy.host}:{proxy.port}")
                    return False
            else:
                # 添加代理配置行前检查是否已存在
                port_pattern = f"-p{proxy.port}"
                with open(self.config_path, 'r') as f:
                    for line in f:
                        if (line.strip().startswith("socks ") or line.strip().startswith("proxy ")) and port_pattern in line:
                            self._logger.warning(f"代理端口{proxy.port}的配置已存在，跳过添加")
                            return False
                
                # 生成并添加配置行
                config_line = self._generate_proxy_line(proxy)
                if not config_line:
                    self._logger.warning(f"无法为代理生成配置行: {proxy.host}:{proxy.port}")
                    return False
                    
                with open(self.config_path, 'a') as f:
                    f.write(config_line)
                    
                self._logger.info(f"成功添加代理配置: {proxy.host}:{proxy.port}")
                return True
                
        except Exception as e:
            self._logger.exception(f"更新配置文件时出错: {e}")
            return False
            
    def reset_config(self) -> bool:
        """重置配置文件，只保留基本配置
        
        Returns:
            是否成功重置
        """
        try:
            self._ensure_config_dir_exists()
            
            with open(self.config_path, 'w') as f:
                self._write_base_config(f)
                
            self._logger.info("配置文件已重置为基本配置")
            return True
        except Exception as e:
            self._logger.exception(f"重置配置文件时出错: {e}")
            return False 