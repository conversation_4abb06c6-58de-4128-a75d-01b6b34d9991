#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
代理核心服务模块
提供代理IP验证、3proxy服务管理等底层功能
遵循分层架构设计原则
"""


import asyncio
from typing import List, Dict, Any, Optional, Tuple, Union, AsyncGenerator

from PySide6.QtCore import QObject, Signal

from data.repositories.proxy_repo import ProxyRepository
from data.models.proxy import ProxyModel
from data.database import get_session
from utils.logger import get_logger
from config import config
from core.proxy.proxy_3proxy_manager import Proxy3ProxyManager
from core.proxy.proxy_config_manager import ProxyConfigManager
from core.proxy.proxy_validator import ProxyValidator


class ProxyCoreService(QObject):
    """代理核心服务
    
    负责以下功能:
    - 代理IP验证
    - 3proxy服务管理
    - 配置文件生成
    """
    
    def __init__(self):
        """初始化代理服务"""
        super().__init__()
        self._logger = get_logger("core.proxy.service")
        
        # 初始化3proxy管理器
        self.proxy_manager = Proxy3ProxyManager(
            config_path=config.proxy_cfg,
            proxy_exe_path=config.proxy_exe
        )
        
        # 初始化配置管理器
        self.config_manager = ProxyConfigManager()
        
        # 初始化验证器
        self.validator = ProxyValidator()
        
        # 服务状态
        self._is_running = False
        
    async def validate_proxy(self, proxy: ProxyModel) -> Tuple[bool, Optional[float]]:
        """验证代理是否有效
        
        Args:
            proxy: 代理模型
            
        Returns:
            Tuple[bool, Optional[float]]: (是否有效, 响应时间)
        """
        self._logger.info(f"验证代理: {proxy.host}:{proxy.port}")
        
        try:
            # 使用验证器验证代理
            is_valid, response_time = await self.validator.validate_proxy(proxy)
            
            return is_valid, response_time
        except Exception as e:
            self._logger.exception(f"验证代理失败: {e}")
            return False, None
    
    async def validate_proxies(self, proxies: List[ProxyModel]) -> Dict[int, Tuple[bool, Optional[float]]]:
        """批量验证代理
        
        耗时任务，适合在子线程执行
        
        Args:
            proxies: 代理列表
            
        Returns:
            Dict[int, Tuple[bool, Optional[float]]]: {代理ID: (是否有效, 响应时间)}
        """
        self._logger.info(f"批量验证代理: {len(proxies)}个")
        
        try:
            # 批量验证结果
            results = {}
            
            if not proxies:
                self._logger.info("没有找到需要验证的代理")
                return results
                
            self._logger.info(f"开始批量验证代理，共 {len(proxies)} 个")
            
            # 从配置中获取批量大小和并发数
            batch_size = getattr(config, 'proxy_batch_size', 10)
            max_concurrent = getattr(config, 'proxy_max_concurrent', 10)
            
            # 创建信号量控制并发
            semaphore = asyncio.Semaphore(max_concurrent)
            
            # 包装验证方法，使用信号量控制并发
            async def validate_with_semaphore(proxy: ProxyModel):
                async with semaphore:
                    return await self.validator.validate_proxy(proxy)
            
            # 分批处理
            total_proxies = len(proxies)
            
            for i in range(0, total_proxies, batch_size):
                # 获取当前批次的代理
                batch_proxies = proxies[i:i+batch_size]
                
                # 创建任务列表
                tasks = [validate_with_semaphore(proxy) for proxy in batch_proxies]
                
                # 并发执行验证任务
                batch_results = await asyncio.gather(*tasks, return_exceptions=True)
                
                # 处理结果
                for idx, result in enumerate(batch_results):
                    proxy = batch_proxies[idx]
                    
                    # 处理可能的异常
                    if isinstance(result, Exception):
                        self._logger.error(f"验证代理时出错: {proxy.get_connection_string()}, 错误: {str(result)}")
                        results[proxy.id] = (False, None)
                    else:
                        is_valid, response_time = result
                        results[proxy.id] = (is_valid, response_time)
                
                # 批次之间添加短暂延迟，避免过度请求
                if i + batch_size < total_proxies:
                    await asyncio.sleep(0.5)
            
            # 统计有效代理数量
            valid_count = sum(1 for r in results.values() if r[0])
            self._logger.info(f"批量代理验证完成，结果: {valid_count}/{len(results)} 个有效")
            
            return results
        except Exception as e:
            self._logger.exception(f"批量验证代理失败: {e}")
            return {}
    
    async def validate_proxies_stream(self, proxies: List[ProxyModel]) -> AsyncGenerator[Tuple[int, bool, Optional[float]], None]:
        """批量验证代理并实时返回结果
        
        耗时任务，适合在子线程执行
        
        Args:
            proxies: 代理列表
            
        Yields:
            Tuple[int, bool, Optional[float]]: (代理ID, 是否有效, 响应时间)
        """
        self._logger.info(f"批量验证代理(流式): {len(proxies)}个")
        
        try:
            if not proxies:
                self._logger.info("没有找到需要验证的代理")
                return
                
            # 从配置中获取批量大小和并发数
            batch_size = getattr(config, 'proxy_batch_size', 1)
            max_concurrent = getattr(config, 'proxy_max_concurrent', 1)
            
            # 创建信号量控制并发
            semaphore = asyncio.Semaphore(max_concurrent)
            
            # 分批处理
            total_proxies = len(proxies)
            valid_count = 0
            
            for i in range(0, total_proxies, batch_size):
                # 获取当前批次的代理
                batch_proxies = proxies[i:i+batch_size]
                
                # 创建任务字典，使用代理ID作为键
                tasks = {}
                for proxy in batch_proxies:
                    # 使用信号量包装验证任务
                    async def validate_task(p=proxy):
                        async with semaphore:
                            return await self.validator.validate_proxy(p)
                    
                    # 创建任务
                    tasks[proxy.id] = asyncio.create_task(validate_task())
                
                # 等待所有任务完成或第一个任务完成
                pending = set(tasks.values())
                while pending:
                    # 等待任务完成
                    done, pending = await asyncio.wait(
                        pending, 
                        return_when=asyncio.FIRST_COMPLETED
                    )
                    
                    # 处理已完成的任务
                    for task in done:
                        # 找到对应的代理ID
                        proxy_id = next(pid for pid, t in tasks.items() if t == task)
                        
                        try:
                            # 获取结果
                            is_valid, response_time = task.result()
                            
                            # 统计有效代理
                            if is_valid:
                                valid_count += 1
                                
                            # 产出结果
                            yield proxy_id, is_valid, response_time
                        except Exception as e:
                            self._logger.error(f"验证代理ID {proxy_id} 时出错: {str(e)}")
                            yield proxy_id, False, None
                
                # 批次之间添加短暂延迟，避免过度请求
                if i + batch_size < total_proxies:
                    await asyncio.sleep(0.5)
            
            self._logger.info(f"批量代理验证完成，结果: {valid_count}/{total_proxies} 个有效")
        except Exception as e:
            self._logger.exception(f"批量验证代理(流式)失败: {e}")
            return
    
    async def generate_proxy_config(self, proxy: Optional[ProxyModel] = None, refresh_all: bool = False) -> bool:
        """生成代理配置文件
        
        轻量任务，适合在主线程执行
        
        Args:
            proxy: 单个代理模型，用于增量更新
            refresh_all: 是否刷新所有配置
            
        Returns:
            bool: 是否成功
        """
        self._logger.info("生成代理配置文件")
        
        try:
            if refresh_all:
                # 获取所有本地代理
                async with get_session() as session:
                    # 初始化仓储
                    proxy_repo = ProxyRepository(session=session)
                    
                    # 获取所有本地代理
                    local_proxies = await proxy_repo.get_local_proxies()
                    
                    # 生成完整配置文件
                    config_path = self.config_manager.generate_config(local_proxies)
                    
                    return config_path is not None
            elif proxy is not None:
                # 增量更新配置
                if proxy.is_local:
                    result = self.config_manager.update_config(proxy)
                    return result
                else:
                    # 非本地代理不需要更新配置
                    return True
            else:
                # 确保基本配置存在
                return self.config_manager.update_config()
        except Exception as e:
            self._logger.exception(f"生成代理配置文件失败: {e}")
            return False
    
    async def start_proxy_service(self) -> bool:
        """启动代理服务
        
        耗时任务，适合在子线程执行
        
        Returns:
            bool: 是否成功
        """
        self._logger.info("启动代理服务")
        
        try:
            # 确保配置文件存在
            await self.generate_proxy_config(refresh_all=True)
            
            # 启动服务
            result, message = await self.proxy_manager.start_service()
            
            if result:
                self._is_running = True
                self._logger.info(f"代理服务启动成功: {message}")
            else:
                self._logger.error(f"代理服务启动失败: {message}")
                
            return result
        except Exception as e:
            self._logger.exception(f"启动代理服务失败: {e}")
            return False
    
    async def stop_proxy_service(self) -> bool:
        """停止代理服务
        
        耗时任务，适合在子线程执行
        
        Returns:
            bool: 是否成功
        """
        self._logger.info("停止代理服务")
        
        try:
            # 停止服务
            result, message = await self.proxy_manager.stop_service()
            
            if result:
                self._is_running = False
                self._logger.info(f"代理服务停止成功: {message}")
            else:
                self._logger.error(f"代理服务停止失败: {message}")
                
            return result
        except Exception as e:
            self._logger.exception(f"停止代理服务失败: {e}")
            return False
    
    async def restart_proxy_service(self) -> bool:
        """重启代理服务
        
        耗时任务，适合在子线程执行
        
        Returns:
            bool: 是否成功
        """
        self._logger.info("重启代理服务")
        
        try:
            # 确保配置文件存在
            await self.generate_proxy_config(refresh_all=True)
            
            # 重启服务
            result, message = await self.proxy_manager.restart_service()
            
            if result:
                self._is_running = True
                self._logger.info(f"代理服务重启成功: {message}")
            else:
                self._logger.error(f"代理服务重启失败: {message}")
                
            return result
        except Exception as e:
            self._logger.exception(f"重启代理服务失败: {e}")
            return False
    
    def is_service_running(self) -> bool:
        """检查服务是否运行中
        
        Returns:
            bool: 是否运行中
        """
        try:
            # 使用_is_running缓存的状态，避免每次都异步检查
            return self._is_running
        except Exception as e:
            self._logger.exception(f"检查服务状态失败: {e}")
            return False
            
    async def async_check_service_status(self) -> bool:
        """异步检查服务状态
        
        Returns:
            bool: 是否运行中
        """
        try:
            # 调用3proxy管理器检查服务状态
            is_running = await self.proxy_manager.check_service_status()
            
            self._is_running = is_running
            return is_running
        except Exception as e:
            self._logger.exception(f"异步检查服务状态失败: {e}")
            return self._is_running
    
    def remove_proxy_service(self) -> bool:
        """彻底移除代理服务
        
        Returns:
            bool: 是否成功
        """
        self._logger.info("彻底移除代理服务")
        
        try:
            # 调用3proxy管理器卸载服务
            loop = asyncio.get_event_loop()
            result, message = loop.run_until_complete(self.proxy_manager.uninstall_service())
            
            if result:
                self._is_running = False
                self._logger.info(f"代理服务移除成功: {message}")
            else:
                self._logger.error(f"代理服务移除失败: {message}")
                
            return result
        except Exception as e:
            self._logger.exception(f"移除代理服务失败: {e}")
            return False
        