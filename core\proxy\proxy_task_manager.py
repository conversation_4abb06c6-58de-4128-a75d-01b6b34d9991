#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
代理任务管理器
负责处理代理相关的异步任务，使用单例QThread模式管理耗时操作

架构说明:
- 任务管理器(ProxyTaskManager): 提供异步任务执行的基础设施
- 核心服务层(ProxyCoreService): 包含业务逻辑和批量处理算法
- 验证器(ProxyValidator): 专注于单个代理验证的核心功能

任务流程:
1. 应用层调用核心服务层的方法
2. 核心服务层根据任务性质决定:
   - 轻量任务: 直接在主线程执行
   - 耗时任务: 通过任务管理器提交到子线程
3. 任务管理器负责线程管理和结果返回
"""

import asyncio
import traceback
from typing import Dict, Any, Optional, Callable, Coroutine, List
from concurrent.futures import Future

from PySide6.QtCore import QObject, Signal, Slot, QThread, QMutex

from utils.logger import get_logger

# 全局单例实例
_instance = None
_instance_mutex = QMutex()

class ProxyTaskManager(QThread):
    """代理任务管理器
    
    设计为单例模式的QThread，负责:
    - 创建和管理异步事件循环
    - 提交和跟踪耗时异步任务
    - 返回任务执行结果
    
    使用指南:
    1. 耗时任务（如批量验证代理、启动/停止服务等）应放入子线程
    2. 轻量任务（如单个代理验证、配置更新等）可直接在主线程执行
    
    使用示例:
    ```python
    # 获取单例实例
    task_manager = ProxyTaskManager.instance()
    
    # 提交耗时任务到子线程
    future = task_manager.submit_task("batch_validate", validate_multiple_proxies, proxies)
    
    # 轻量任务直接在主线程异步执行
    result = await task_manager.run_in_main_thread(validate_single_proxy, proxy)
    ```
    """
    
    # 任务完成信号
    task_completed = Signal(str, object)  # (task_name, result)
    
    # 任务错误信号
    task_error = Signal(str, str)  # (task_name, error_message)
    
    # 耗时任务类型列表（应放入子线程）
    HEAVY_TASKS = [
        "batch_validate",      # 批量验证代理
        "validate_all",        # 验证所有代理
        "add_ip_range",        # 批量添加IP范围
        "start_service",       # 启动代理服务
        "stop_service",        # 停止代理服务
        "restart_service",     # 重启代理服务
        "remove_service",      # 移除代理服务
        "refresh_all_config",  # 刷新所有配置
    ]
    
    @staticmethod
    def instance():
        """获取全局单例实例
        
        Returns:
            ProxyTaskManager: 单例实例
        """
        global _instance
        
        _instance_mutex.lock()
        try:
            if _instance is None:
                _instance = ProxyTaskManager()
                _instance.start()
            elif not _instance.isRunning():
                # 如果线程已停止，重新启动
                _instance.start()
                
            return _instance
        finally:
            _instance_mutex.unlock()
    
    def __init__(self):
        """初始化任务管理器，私有构造函数"""
        super().__init__()
        self._logger = get_logger("proxy_task_manager")
        
        # 运行标志
        self._running = True
        
        # 事件循环
        self._event_loop = None
        
        # 正在运行的任务
        self._running_tasks = {}
        
        # 任务Future字典
        self._futures = {}
    
    def get_event_loop(self) -> Optional[asyncio.AbstractEventLoop]:
        """获取子线程事件循环
        
        Returns:
            Optional[asyncio.AbstractEventLoop]: 事件循环或None
        """
        return self._event_loop
    
    async def run_in_main_thread(self, coro_func: Callable, *args, **kwargs):
        """在主线程中执行轻量级异步任务
        
        Args:
            coro_func: 协程函数
            *args: 位置参数
            **kwargs: 关键字参数
            
        Returns:
            Any: 任务结果
        """
        try:
            # 直接在主线程事件循环中执行
            return await coro_func(*args, **kwargs)
        except Exception as e:
            self._logger.exception(f"主线程任务执行失败: {e}")
            raise
    
    def is_heavy_task(self, task_name: str) -> bool:
        """判断任务是否为耗时任务
        
        Args:
            task_name: 任务名称
            
        Returns:
            bool: 是否为耗时任务
        """
        # 检查任务名是否包含已知的耗时任务前缀
        return any(task_name.startswith(prefix) for prefix in self.HEAVY_TASKS)
    
    def submit_task(self, task_name: str, coro_func: Callable, *args, **kwargs) -> Optional[Future]:
        """提交异步任务到子线程
        
        适用于耗时任务，如批量验证代理、启动/停止服务等
        
        Args:
            task_name: 任务名称
            coro_func: 协程函数
            *args: 位置参数
            **kwargs: 关键字参数
            
        Returns:
            Optional[Future]: Future对象或None
        """
        if self.is_task_running(task_name):
            self._logger.warning(f"任务 {task_name} 已在运行，忽略本次提交")
            return None
        
        self._logger.info(f"提交耗时任务到子线程: {task_name}")
        
        # 检查线程是否运行
        if not self.isRunning():
            self._logger.warning("任务管理器线程未运行，正在启动")
            self.start()
        
        # 检查事件循环
        if self._event_loop is None:
            self._logger.error("事件循环未初始化，任务提交失败")
            return None
        
        # 标记任务为运行状态
        self._running_tasks[task_name] = True
        
        # 创建Future对象
        future = Future()
        self._futures[task_name] = future
        
        # 创建协程
        async def task_wrapper():
            try:
                # 执行任务
                result = await coro_func(*args, **kwargs)
                
                # 发送完成信号
                self.task_completed.emit(task_name, result)
                self._logger.info(f"任务 {task_name} 完成")
                
                # 设置Future结果
                if task_name in self._futures and not self._futures[task_name].done():
                    self._futures[task_name].set_result(result)
                    del self._futures[task_name]
                
                return result
            except Exception as e:
                # 获取异常堆栈
                error_msg = f"{str(e)}\n{traceback.format_exc()}"
                
                # 发送错误信号
                self.task_error.emit(task_name, error_msg)
                self._logger.error(f"任务 {task_name} 出错: {error_msg}")
                
                # 设置Future异常
                if task_name in self._futures and not self._futures[task_name].done():
                    self._futures[task_name].set_exception(e)
                    del self._futures[task_name]
                
                return None
            finally:
                # 标记任务为完成状态
                if task_name in self._running_tasks:
                    del self._running_tasks[task_name]
        
        # 提交任务到事件循环
        asyncio.run_coroutine_threadsafe(task_wrapper(), self._event_loop)
        
        return future
    
    def submit_batch_tasks(self, base_name: str, coro_func: Callable, items: List[Any], 
                          batch_size: int = 10, **kwargs) -> List[Future]:
        """批量提交任务，并控制并发数
        
        Args:
            base_name: 任务基础名称
            coro_func: 协程函数
            items: 要处理的项目列表
            batch_size: 批处理大小
            **kwargs: 其他关键字参数
            
        Returns:
            List[Future]: Future对象列表
        """
        futures = []
        total = len(items)
        
        self._logger.info(f"批量提交任务: {base_name}, 总数: {total}, 批次大小: {batch_size}")
        
        # 分批处理
        for i in range(0, total, batch_size):
            batch = items[i:i+batch_size]
            batch_name = f"{base_name}_batch_{i//batch_size}"
            
            # 创建批处理任务
            async def process_batch(batch_items):
                results = []
                for item in batch_items:
                    # 处理单个项目
                    result = await coro_func(item, **kwargs)
                    results.append(result)
                return results
            
            # 提交批处理任务
            future = self.submit_task(batch_name, process_batch, batch)
            if future:
                futures.append(future)
        
        return futures
    
    def is_task_running(self, task_name: str) -> bool:
        """检查任务是否正在运行
        
        Args:
            task_name: 任务名称
            
        Returns:
            bool: 是否正在运行
        """
        return task_name in self._running_tasks
    
    def run(self):
        """线程主函数，创建事件循环并处理异步任务"""
        self._logger.info("代理任务管理器线程启动")
        
        # 创建新的事件循环
        self._event_loop = asyncio.new_event_loop()
        asyncio.set_event_loop(self._event_loop)
        
        try:
            # 保持事件循环运行
            while self._running:
                # 每次运行一个周期的事件循环
                self._event_loop.run_until_complete(asyncio.sleep(0.1))
        except Exception as e:
            self._logger.exception(f"任务管理器线程异常: {e}")
        finally:
            # 关闭事件循环
            self._event_loop.close()
            self._event_loop = None
            self._logger.info("代理任务管理器线程退出")
    
    def stop(self):
        """停止任务管理器"""
        self._logger.info("停止任务管理器")
        
        # 设置运行标志为False
        self._running = False
        
        # 等待线程结束
        if self.isRunning():
            self.wait(5000)
    
    def __del__(self):
        """析构函数"""
        self.stop()
        