#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
代理IP验证器
负责代理IP的验证操作
"""
import time
import asyncio
import aiohttp
from aiohttp_socks import ProxyConnector
from typing import Tuple, Optional
from utils.logger import get_logger
from config import config
from data.models.proxy import ProxyModel

class ProxyValidator:
    """代理验证器
    
    专注于单个代理IP验证的核心功能
    """
    
    def __init__(self):
        """初始化验证器"""
        self._logger = get_logger("core.proxy.validator")
        
        # 从配置中读取设置
        self.test_urls = config.proxy_test_urls
        self.timeout_seconds = getattr(config, 'proxy_timeout_seconds', 5)

    async def validate_proxy(self, proxy: ProxyModel) -> Tuple[bool, Optional[float]]:
        """验证单个代理是否有效
        
        Args:
            proxy: 代理模型
            
        Returns:
            Tuple[bool, Optional[float]]: (是否有效, 响应时间(毫秒))
        """
        if not proxy:
            return False, None
            
        conn_str = proxy.get_connection_string()
        self._logger.info(f"正在验证代理 {conn_str}")
        
        # 创建代理连接器，禁用SSL验证
        connector = ProxyConnector.from_url(conn_str, ssl=False)
        
        # 设置超时
        timeout = aiohttp.ClientTimeout(total=self.timeout_seconds)
        
        # 创建会话，禁用SSL验证
        async with aiohttp.ClientSession(connector=connector, timeout=timeout, trust_env=True) as session:
            # 尝试多个测试URL
            for service_url in self.test_urls:
                try:
                    # 记录开始时间
                    start_time = time.time()
                    
                    # 发送请求，禁用SSL验证
                    async with session.get(service_url, ssl=False) as response:
                        # 记录结束时间
                        end_time = time.time()
                        
                        # 检查响应状态
                        if response.status == 200:
                            # 计算响应时间（毫秒）
                            response_time = (end_time - start_time) * 1000
                            self._logger.info(f"代理验证成功: {conn_str}, 耗时: {response_time:.2f}ms")
                            return True, response_time
                except Exception as e:
                    self._logger.warning(f"使用{service_url}验证代理失败: {conn_str}, 错误: {str(e)}")
                    continue
                    
        # 所有测试URL都失败
        self._logger.warning(f"代理验证失败: {conn_str}")
        return False, None 