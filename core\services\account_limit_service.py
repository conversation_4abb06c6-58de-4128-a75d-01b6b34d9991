#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
账户每日发信限制业务服务
"""

import datetime
from typing import Tuple, Optional, List, Dict
from sqlalchemy.ext.asyncio import AsyncSession

from data.models.account import AccountModel
from data.repositories.account_send_limit_repo import AccountSendLimitRepository
from utils.logger import get_logger
from data.models.account_limit import AccountSendLimit


class AccountLimitService:
    """账户每日发信限制业务服务"""
    
    def __init__(self, session: AsyncSession):
        """初始化账户发送限制服务"""
        self._session = session
        self._repo = AccountSendLimitRepository(session)
        self._logger = get_logger("core.services.account_limit")
    
    async def check_can_send(self, account_phone: str) -> Tuple[bool, str, Optional[AccountSendLimit]]:
        """
        检查账户今日是否还能发信
        Returns: (可发, 消息, 限制对象)
        """
        limit = await self._repo.get_by_phone(account_phone)
        from datetime import date
        today = date.today()
        if not limit:
            limit = await self._repo.create(account_phone)
        if limit.last_reset_date < today:
            limit = await self._repo.reset_count(account_phone)
        if limit.current_day_count >= limit.max_daily_limit:
            return False, f"账户今日已达上限({limit.current_day_count}/{limit.max_daily_limit})", limit
        return True, f"账户今日已发{limit.current_day_count}/{limit.max_daily_limit}", limit

    async def increment_send_count(self, account_phone: str, increment: int = 1) -> AccountSendLimit:
        """
        增加账户今日已发信数量
        """
        return await self._repo.update_count(account_phone, increment)

    async def reset_send_count(self, account_phone: str) -> AccountSendLimit:
        """
        重置账户今日已发信数量
        """
        return await self._repo.reset_count(account_phone)

    async def set_max_daily_limit(self, account_phone: str, max_limit: int) -> AccountSendLimit:
        """
        设置账户每日最大限制
        """
        return await self._repo.set_max_limit(account_phone, max_limit)

    async def get_limit_info(self, account_phone: str) -> Optional[AccountSendLimit]:
        """
        获取账户限制信息
        """
        return await self._repo.get_by_phone(account_phone)

    async def get_all_limits(self) -> List[AccountSendLimit]:
        """
        获取所有账户的限制信息
        """
        from sqlalchemy.future import select
        result = await self._session.execute(select(AccountSendLimit))
        return result.scalars().all()

    async def get_stats(self) -> Dict:
        """
        获取全局统计信息
        """
        all_limits = await self.get_all_limits()
        total = len(all_limits)
        total_sent = sum(l.current_day_count for l in all_limits)
        total_limit = sum(l.max_daily_limit for l in all_limits)
        reached = sum(1 for l in all_limits if l.current_day_count >= l.max_daily_limit)
        available = total - reached
        return {
            "total_accounts": total,
            "total_sent_today": total_sent,
            "total_limit": total_limit,
            "reached_limit_accounts": reached,
            "available_accounts": available,
            "usage_percentage": (total_sent / total_limit * 100) if total_limit else 0
        }
    