#!/usr/bin/env python
# -*- coding: utf-8 -*-

import asyncio
from typing import Dict, Any, Optional, Callable, Union
from datetime import datetime, timedelta
from apscheduler.schedulers.asyncio import AsyncIOScheduler
from apscheduler.jobstores.sqlalchemy import SQLAlchemyJobStore
from apscheduler.executors.asyncio import AsyncIOExecutor
from apscheduler.events import EVENT_JOB_EXECUTED, EVENT_JOB_ERROR
from PySide6.QtCore import QObject, Signal

from utils.logger import get_logger
from frameConf.setting import DATABASE_URL

logger = get_logger(__name__)

class TelegramTaskScheduler(QObject):
    """统一的Telegram任务调度器，基于APScheduler"""
    
    # 任务完成信号
    task_completed = Signal(str, object)  # (task_id, result)
    task_error = Signal(str, str)  # (task_id, error_message)
    task_started = Signal(str)  # (task_id)
    
    def __init__(self):
        super().__init__()
        self._setup_scheduler()
        self._running_tasks: Dict[str, Any] = {}
        
    def _setup_scheduler(self):
        """配置调度器"""
        jobstores = {
            'default': SQLAlchemyJobStore(url=DATABASE_URL)
        }
        executors = {
            'default': AsyncIOExecutor()
        }
        job_defaults = {
            'coalesce': False,
            'max_instances': 3
        }
        
        self.scheduler = AsyncIOScheduler(
            jobstores=jobstores,
            executors=executors,
            job_defaults=job_defaults
        )
        
        # 添加事件监听器
        self.scheduler.add_listener(self._job_executed, EVENT_JOB_EXECUTED)
        self.scheduler.add_listener(self._job_error, EVENT_JOB_ERROR)
        
    async def start(self):
        """启动调度器"""
        if not self.scheduler.running:
            self.scheduler.start()
            logger.info("任务调度器已启动")
    
    async def shutdown(self):
        """关闭调度器"""
        if self.scheduler.running:
            self.scheduler.shutdown(wait=True)
            logger.info("任务调度器已关闭")
    
    def _job_executed(self, event):
        """任务执行完成回调"""
        task_id = event.job_id
        result = event.retval
        self.task_completed.emit(task_id, result)
        self._running_tasks.pop(task_id, None)
        logger.info(f"任务 {task_id} 执行完成")
    
    def _job_error(self, event):
        """任务执行错误回调"""
        task_id = event.job_id
        error_msg = str(event.exception)
        self.task_error.emit(task_id, error_msg)
        self._running_tasks.pop(task_id, None)
        logger.error(f"任务 {task_id} 执行失败: {error_msg}")
    
    async def _task_wrapper(self, task_id: str, func: Callable, *args, **kwargs):
        """任务包装器，处理信号发送和异常"""
        try:
            self.task_started.emit(task_id)
            self._running_tasks[task_id] = {
                'func': func.__name__,
                'started_at': datetime.now()
            }
            
            if asyncio.iscoroutinefunction(func):
                result = await func(*args, **kwargs)
            else:
                result = func(*args, **kwargs)
            
            return result
        except Exception as e:
            logger.error(f"任务 {task_id} 执行异常: {e}")
            raise
    
    def add_immediate_task(self, task_id: str, func: Callable, *args, **kwargs) -> str:
        """添加立即执行的任务（短时任务）"""
        job = self.scheduler.add_job(
            self._task_wrapper,
            'date',
            args=[task_id, func] + list(args),
            kwargs=kwargs,
            id=task_id,
            replace_existing=True
        )
        logger.info(f"添加立即任务: {task_id}")
        return job.id
    
    def add_delayed_task(self, task_id: str, func: Callable, delay_seconds: int, *args, **kwargs) -> str:
        """添加延迟执行的任务"""
        run_date = datetime.now() + timedelta(seconds=delay_seconds)
        job = self.scheduler.add_job(
            self._task_wrapper,
            'date',
            run_date=run_date,
            args=[task_id, func] + list(args),
            kwargs=kwargs,
            id=task_id,
            replace_existing=True
        )
        logger.info(f"添加延迟任务: {task_id}, 延迟: {delay_seconds}秒")
        return job.id
    
    def add_interval_task(self, task_id: str, func: Callable, interval_seconds: int, *args, **kwargs) -> str:
        """添加间隔执行的任务"""
        job = self.scheduler.add_job(
            self._task_wrapper,
            'interval',
            seconds=interval_seconds,
            args=[task_id, func] + list(args),
            kwargs=kwargs,
            id=task_id,
            replace_existing=True
        )
        logger.info(f"添加间隔任务: {task_id}, 间隔: {interval_seconds}秒")
        return job.id
    
    def add_cron_task(self, task_id: str, func: Callable, cron_expr: str, *args, **kwargs) -> str:
        """添加定时任务（cron表达式）"""
        # 解析cron表达式
        parts = cron_expr.split()
        if len(parts) == 5:
            minute, hour, day, month, day_of_week = parts
        else:
            raise ValueError("cron表达式格式错误")
        
        job = self.scheduler.add_job(
            self._task_wrapper,
            'cron',
            minute=minute,
            hour=hour,
            day=day,
            month=month,
            day_of_week=day_of_week,
            args=[task_id, func] + list(args),
            kwargs=kwargs,
            id=task_id,
            replace_existing=True
        )
        logger.info(f"添加定时任务: {task_id}, cron: {cron_expr}")
        return job.id
    
    def remove_task(self, task_id: str) -> bool:
        """移除任务"""
        try:
            self.scheduler.remove_job(task_id)
            self._running_tasks.pop(task_id, None)
            logger.info(f"移除任务: {task_id}")
            return True
        except Exception as e:
            logger.error(f"移除任务失败 {task_id}: {e}")
            return False
    
    def pause_task(self, task_id: str) -> bool:
        """暂停任务"""
        try:
            self.scheduler.pause_job(task_id)
            logger.info(f"暂停任务: {task_id}")
            return True
        except Exception as e:
            logger.error(f"暂停任务失败 {task_id}: {e}")
            return False
    
    def resume_task(self, task_id: str) -> bool:
        """恢复任务"""
        try:
            self.scheduler.resume_job(task_id)
            logger.info(f"恢复任务: {task_id}")
            return True
        except Exception as e:
            logger.error(f"恢复任务失败 {task_id}: {e}")
            return False
    
    def get_task_info(self, task_id: str) -> Optional[Dict]:
        """获取任务信息"""
        try:
            job = self.scheduler.get_job(task_id)
            if job:
                return {
                    'id': job.id,
                    'name': job.name,
                    'next_run_time': job.next_run_time,
                    'trigger': str(job.trigger)
                }
        except Exception as e:
            logger.error(f"获取任务信息失败 {task_id}: {e}")
        return None
    
    def get_all_tasks(self) -> list:
        """获取所有任务"""
        jobs = self.scheduler.get_jobs()
        return [
            {
                'id': job.id,
                'name': job.name,
                'next_run_time': job.next_run_time,
                'trigger': str(job.trigger)
            }
            for job in jobs
        ]