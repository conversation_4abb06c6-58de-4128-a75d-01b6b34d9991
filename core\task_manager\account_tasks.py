#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
账户相关任务执行器 - 重构版本

基于新架构的账户任务实现，集成：
1. 用户状态管理
2. 客户端池管理
3. 操作历史记录
4. 错误处理和重试
"""

import asyncio
from typing import Dict, Any, Optional, List, Tuple
from .task_executor import BaseTask, TaskCategory, TaskPriority
from core.telegram.user_state_manager import user_state_manager, UserStatus, UserOperation
from core.telegram.client_pool import client_pool
from utils.logger import get_logger

logger = get_logger(__name__)


class AccountLoginTask(BaseTask):
    """账户登录任务 - 集成用户状态管理"""

    def __init__(self,
                 phone: str,
                 proxy: Dict[str, Any] = None,
                 **kwargs):
        super().__init__(
            name=f"账户登录_{phone}",
            category=TaskCategory.ACCOUNT,
            priority=TaskPriority.HIGH,
            description=f"登录账户 {phone}",
            **kwargs
        )
        self.phone = phone
        self.proxy = proxy

    async def execute(self, context: Dict[str, Any] = None) -> Dict[str, Any]:
        """执行账户登录 - 集成用户状态管理"""
        try:
            self.log(f"开始登录账户: {self.phone}")

            # 注册用户到状态管理器
            user_state_manager.register_user(self.phone, proxy=self.proxy)
            user_state_manager.add_active_task(self.phone, self.task_id)

            self.update_progress(10, 100, "初始化客户端...")

            # 使用客户端池获取客户端
            client = await client_pool.get_client(self.phone, auto_create=True, proxy=self.proxy)
            if not client:
                raise Exception("无法创建或获取客户端")

            self.update_progress(30, 100, "检查授权状态...")

            # 检查是否已经授权
            is_authorized = await client.is_user_authorized()

            if is_authorized:
                self.update_progress(80, 100, "账户已授权")

                # 获取用户信息
                try:
                    me = await client.get_me()
                    user_state_manager.update_user_info(
                        phone=self.phone,
                        user_id=me.id,
                        username=me.username,
                        first_name=me.first_name,
                        last_name=me.last_name
                    )
                except Exception as e:
                    self.log(f"获取用户信息失败: {e}", "warning")

                # 更新状态为在线
                user_state_manager.update_user_status(self.phone, UserStatus.ONLINE)

                # 记录登录操作
                user_state_manager.record_operation(
                    phone=self.phone,
                    operation=UserOperation.LOGIN,
                    success=True,
                    details={"status": "already_authorized"},
                    task_id=self.task_id
                )

                self.update_progress(100, 100, "账户已登录")
                self.log("账户已经登录")

                return {
                    "success": True,
                    "status": "already_logged_in",
                    "phone": self.phone,
                    "message": "账户已登录"
                }

            else:
                self.update_progress(50, 100, "发送验证码...")
                user_state_manager.update_user_status(self.phone, UserStatus.LOGGING_IN)

                # 发送验证码
                await client.send_code_request(self.phone)

                self.update_progress(100, 100, "验证码发送成功")
                self.log("验证码发送成功，等待用户输入")

                return {
                    "success": True,
                    "status": "code_sent",
                    "phone": self.phone,
                    "message": "验证码已发送"
                }

        except Exception as e:
            error_msg = str(e)
            self.log(f"登录失败: {error_msg}", "error")

            # 更新用户状态为登录失败
            user_state_manager.update_user_status(
                self.phone, UserStatus.LOGIN_FAILED, {"error": error_msg}
            )

            # 记录失败操作
            user_state_manager.record_operation(
                phone=self.phone,
                operation=UserOperation.LOGIN,
                success=False,
                error_message=error_msg,
                task_id=self.task_id
            )

            raise
        finally:
            # 移除活跃任务
            user_state_manager.remove_active_task(self.phone, self.task_id)


class AccountCodeVerificationTask(BaseTask):
    """账户验证码验证任务 - 直接调用TelegramService"""

    def __init__(self,
                 phone: str,
                 code: str,
                 password: str = None,
                 **kwargs):
        super().__init__(
            name=f"验证码验证_{phone}",
            category=TaskCategory.ACCOUNT,
            priority=TaskPriority.HIGH,
            description=f"验证账户 {phone} 的验证码",
            **kwargs
        )
        self.phone = phone
        self.code = code
        self.password = password

    async def execute(self, context: Dict[str, Any] = None) -> Dict[str, Any]:
        """执行验证码验证 - 直接调用TelegramService"""
        try:
            self.log(f"开始验证账户 {self.phone} 的验证码")
            self.update_progress(20, 100, "提交验证码...")

            # 直接使用TelegramService
            from core.telegram.telegram_service import TelegramService
            from config import config

            telegram_service = TelegramService(
                api_id=config.get("api_id"),
                api_hash=config.get("api_hash"),
                session_dir=config.get("sessions_path")
            )

            self.update_progress(50, 100, "验证验证码...")

            # 直接调用验证码提交方法
            success, result = await telegram_service.submit_code(self.phone, self.code, self.password)

            self.update_progress(80, 100, "验证码验证完成")

            if not success:
                raise Exception(f"验证码验证失败: {result}")

            self.update_progress(100, 100, "验证成功")
            self.log("验证码验证成功")

            return {
                "success": True,
                "phone": self.phone,
                "user_info": result,
                "message": "验证码验证成功"
            }

        except Exception as e:
            self.log(f"验证码验证失败: {str(e)}", "error")
            raise


class AccountPasswordVerificationTask(BaseTask):
    """账户密码验证任务（两步验证） - 直接调用TelegramService"""

    def __init__(self,
                 phone: str,
                 password: str,
                 **kwargs):
        super().__init__(
            name=f"密码验证_{phone}",
            category=TaskCategory.ACCOUNT,
            priority=TaskPriority.HIGH,
            description=f"验证账户 {phone} 的两步验证密码",
            **kwargs
        )
        self.phone = phone
        self.password = password

    async def execute(self, context: Dict[str, Any] = None) -> Dict[str, Any]:
        """执行密码验证 - 直接调用TelegramService"""
        try:
            self.log(f"开始验证账户 {self.phone} 的两步验证密码")
            self.update_progress(20, 100, "提交密码...")

            # 直接使用TelegramService
            from core.telegram.telegram_service import TelegramService
            from config import config

            telegram_service = TelegramService(
                api_id=config.get("api_id"),
                api_hash=config.get("api_hash"),
                session_dir=config.get("sessions_path")
            )

            self.update_progress(50, 100, "验证密码...")

            # 直接调用密码验证方法
            success, result = await telegram_service.submit_password(self.phone, self.password)

            self.update_progress(80, 100, "密码验证完成")

            if not success:
                raise Exception(f"密码验证失败: {result}")

            self.update_progress(100, 100, "密码验证成功")
            self.log("两步验证密码验证成功")

            return {
                "success": True,
                "phone": self.phone,
                "user_info": result,
                "message": "密码验证成功"
            }

        except Exception as e:
            self.log(f"密码验证失败: {str(e)}", "error")
            raise


class AccountInfoRefreshTask(BaseTask):
    """账户信息刷新任务 - 直接调用TelegramService"""

    def __init__(self,
                 phone: str,
                 **kwargs):
        super().__init__(
            name=f"刷新账户信息_{phone}",
            category=TaskCategory.ACCOUNT,
            priority=TaskPriority.NORMAL,
            description=f"刷新账户 {phone} 的信息",
            **kwargs
        )
        self.phone = phone

    async def execute(self, context: Dict[str, Any] = None) -> Dict[str, Any]:
        """执行账户信息刷新 - 直接调用TelegramService"""
        try:
            self.log(f"开始刷新账户 {self.phone} 的信息")
            self.update_progress(20, 100, "获取用户信息...")

            # 直接使用TelegramService
            from core.telegram.telegram_service import TelegramService
            from config import config

            telegram_service = TelegramService(
                api_id=config.get("api_id"),
                api_hash=config.get("api_hash"),
                session_dir=config.get("sessions_path")
            )

            self.update_progress(50, 100, "连接Telegram...")

            # 直接调用获取用户信息方法
            success, result = await telegram_service.get_user_info(self.phone)

            self.update_progress(80, 100, "信息获取完成")

            if not success:
                raise Exception(f"获取用户信息失败: {result}")

            self.update_progress(100, 100, "信息刷新成功")
            self.log("账户信息刷新成功")

            return {
                "success": True,
                "phone": self.phone,
                "user_info": result,
                "message": "账户信息刷新成功"
            }

        except Exception as e:
            self.log(f"账户信息刷新失败: {str(e)}", "error")
            raise


class BatchAccountLoginTask(BaseTask):
    """批量账户登录任务 - 直接调用TelegramService"""

    def __init__(self,
                 accounts: List[Dict[str, Any]],
                 max_concurrent: int = 3,
                 **kwargs):
        super().__init__(
            name=f"批量登录_{len(accounts)}个账户",
            category=TaskCategory.ACCOUNT,
            priority=TaskPriority.NORMAL,
            description=f"批量登录 {len(accounts)} 个账户",
            **kwargs
        )
        self.accounts = accounts
        self.max_concurrent = max_concurrent

    async def execute(self, context: Dict[str, Any] = None) -> Dict[str, Any]:
        """执行批量账户登录 - 直接调用TelegramService"""
        try:
            self.log(f"开始批量登录 {len(self.accounts)} 个账户")
            self.update_progress(0, len(self.accounts), "准备批量登录...")

            results = []
            semaphore = asyncio.Semaphore(self.max_concurrent)

            async def login_single_account(account_info):
                async with semaphore:
                    try:
                        phone = account_info.get('phone')
                        proxy = account_info.get('proxy')

                        self.log(f"登录账户: {phone}")

                        # 创建单个登录任务
                        login_task = AccountLoginTask(
                            phone=phone,
                            proxy=proxy
                        )

                        result = await login_task.execute()
                        results.append({
                            "phone": phone,
                            "success": True,
                            "result": result
                        })

                        # 更新进度
                        current_progress = len(results)
                        self.update_progress(current_progress, len(self.accounts),
                                           f"已完成 {current_progress}/{len(self.accounts)}")

                    except Exception as e:
                        results.append({
                            "phone": account_info.get('phone'),
                            "success": False,
                            "error": str(e)
                        })
                        self.log(f"账户 {account_info.get('phone')} 登录失败: {str(e)}", "error")

            # 并发执行登录
            tasks = [login_single_account(account) for account in self.accounts]
            await asyncio.gather(*tasks, return_exceptions=True)

            success_count = sum(1 for r in results if r["success"])
            fail_count = len(results) - success_count

            self.update_progress(len(self.accounts), len(self.accounts),
                               f"批量登录完成: 成功 {success_count}, 失败 {fail_count}")

            self.log(f"批量登录完成: 成功 {success_count} 个, 失败 {fail_count} 个")

            return {
                "success": True,
                "total": len(self.accounts),
                "success_count": success_count,
                "fail_count": fail_count,
                "results": results,
                "message": f"批量登录完成: 成功 {success_count}, 失败 {fail_count}"
            }

        except Exception as e:
            self.log(f"批量登录失败: {str(e)}", "error")
            raise
