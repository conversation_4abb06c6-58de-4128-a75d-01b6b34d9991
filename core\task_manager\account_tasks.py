#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
账户相关任务执行器

实现账户登录、验证、管理等相关的具体任务执行逻辑
"""

import asyncio
from typing import Dict, Any, Optional, List, Tuple
from datetime import datetime

from .task_executor import BaseTask, TaskCategory, TaskPriority
from core.telegram.client_worker import TelegramClientWorker
from utils.logger import get_logger

logger = get_logger(__name__)


class AccountLoginTask(BaseTask):
    """账户登录任务"""
    
    def __init__(self, 
                 phone: str, 
                 proxy: Dict[str, Any] = None,
                 telegram_worker: TelegramClientWorker = None,
                 **kwargs):
        super().__init__(
            name=f"账户登录_{phone}",
            category=TaskCategory.ACCOUNT,
            priority=TaskPriority.HIGH,
            description=f"登录账户 {phone}",
            **kwargs
        )
        self.phone = phone
        self.proxy = proxy
        self.telegram_worker = telegram_worker
        
    async def execute(self, context: Dict[str, Any] = None) -> Dict[str, Any]:
        """执行账户登录"""
        try:
            self.log(f"开始登录账户: {self.phone}")
            self.update_progress(10, 100, "发送验证码...")
            
            if not self.telegram_worker:
                raise ValueError("TelegramClientWorker 未提供")
            
            # 发送验证码
            task_id = self.telegram_worker.start_login(self.phone, self.proxy)
            success, result = await self.telegram_worker.get_task_result(task_id, timeout=30)
            
            self.update_progress(50, 100, "验证码发送完成")
            
            if not success:
                raise Exception(f"发送验证码失败: {result}")
            
            if result == "already_authorized":
                self.update_progress(100, 100, "账户已登录")
                self.log("账户已经登录")
                return {
                    "success": True,
                    "status": "already_logged_in",
                    "phone": self.phone,
                    "message": "账户已登录"
                }
            
            self.update_progress(100, 100, "验证码发送成功")
            self.log("验证码发送成功，等待用户输入")
            
            return {
                "success": True,
                "status": "code_sent",
                "phone": self.phone,
                "message": "验证码已发送"
            }
            
        except Exception as e:
            self.log(f"登录失败: {str(e)}", "error")
            raise


class AccountCodeVerificationTask(BaseTask):
    """账户验证码验证任务"""
    
    def __init__(self, 
                 phone: str, 
                 code: str,
                 password: str = None,
                 telegram_worker: TelegramClientWorker = None,
                 **kwargs):
        super().__init__(
            name=f"验证码验证_{phone}",
            category=TaskCategory.ACCOUNT,
            priority=TaskPriority.HIGH,
            description=f"验证账户 {phone} 的验证码",
            **kwargs
        )
        self.phone = phone
        self.code = code
        self.password = password
        self.telegram_worker = telegram_worker
        
    async def execute(self, context: Dict[str, Any] = None) -> Dict[str, Any]:
        """执行验证码验证"""
        try:
            self.log(f"开始验证账户 {self.phone} 的验证码")
            self.update_progress(20, 100, "提交验证码...")
            
            if not self.telegram_worker:
                raise ValueError("TelegramClientWorker 未提供")
            
            # 提交验证码
            task_id = self.telegram_worker.submit_code(self.phone, self.code, self.password)
            success, result = await self.telegram_worker.get_task_result(task_id, timeout=30)
            
            self.update_progress(80, 100, "验证码验证完成")
            
            if not success:
                raise Exception(f"验证码验证失败: {result}")
            
            self.update_progress(100, 100, "验证成功")
            self.log("验证码验证成功")
            
            return {
                "success": True,
                "phone": self.phone,
                "user_info": result,
                "message": "验证码验证成功"
            }
            
        except Exception as e:
            self.log(f"验证码验证失败: {str(e)}", "error")
            raise


class AccountPasswordVerificationTask(BaseTask):
    """账户密码验证任务（两步验证）"""
    
    def __init__(self, 
                 phone: str, 
                 password: str,
                 telegram_worker: TelegramClientWorker = None,
                 **kwargs):
        super().__init__(
            name=f"密码验证_{phone}",
            category=TaskCategory.ACCOUNT,
            priority=TaskPriority.HIGH,
            description=f"验证账户 {phone} 的两步验证密码",
            **kwargs
        )
        self.phone = phone
        self.password = password
        self.telegram_worker = telegram_worker
        
    async def execute(self, context: Dict[str, Any] = None) -> Dict[str, Any]:
        """执行密码验证"""
        try:
            self.log(f"开始验证账户 {self.phone} 的两步验证密码")
            self.update_progress(20, 100, "提交密码...")
            
            if not self.telegram_worker:
                raise ValueError("TelegramClientWorker 未提供")
            
            # 提交密码
            task_id = self.telegram_worker.submit_password(self.phone, self.password)
            success, result = await self.telegram_worker.get_task_result(task_id, timeout=30)
            
            self.update_progress(80, 100, "密码验证完成")
            
            if not success:
                raise Exception(f"密码验证失败: {result}")
            
            self.update_progress(100, 100, "密码验证成功")
            self.log("两步验证密码验证成功")
            
            return {
                "success": True,
                "phone": self.phone,
                "user_info": result,
                "message": "密码验证成功"
            }
            
        except Exception as e:
            self.log(f"密码验证失败: {str(e)}", "error")
            raise


class AccountInfoRefreshTask(BaseTask):
    """账户信息刷新任务"""
    
    def __init__(self, 
                 phone: str,
                 telegram_worker: TelegramClientWorker = None,
                 **kwargs):
        super().__init__(
            name=f"刷新账户信息_{phone}",
            category=TaskCategory.ACCOUNT,
            priority=TaskPriority.NORMAL,
            description=f"刷新账户 {phone} 的信息",
            **kwargs
        )
        self.phone = phone
        self.telegram_worker = telegram_worker
        
    async def execute(self, context: Dict[str, Any] = None) -> Dict[str, Any]:
        """执行账户信息刷新"""
        try:
            self.log(f"开始刷新账户 {self.phone} 的信息")
            self.update_progress(20, 100, "获取用户信息...")
            
            if not self.telegram_worker:
                raise ValueError("TelegramClientWorker 未提供")
            
            # 获取用户信息
            task_id = self.telegram_worker.get_user_info(self.phone)
            success, result = await self.telegram_worker.get_task_result(task_id, timeout=30)
            
            self.update_progress(80, 100, "信息获取完成")
            
            if not success:
                raise Exception(f"获取用户信息失败: {result}")
            
            self.update_progress(100, 100, "信息刷新成功")
            self.log("账户信息刷新成功")
            
            return {
                "success": True,
                "phone": self.phone,
                "user_info": result,
                "message": "账户信息刷新成功"
            }
            
        except Exception as e:
            self.log(f"账户信息刷新失败: {str(e)}", "error")
            raise


class BatchAccountLoginTask(BaseTask):
    """批量账户登录任务"""
    
    def __init__(self, 
                 accounts: List[Dict[str, Any]],
                 telegram_worker: TelegramClientWorker = None,
                 max_concurrent: int = 3,
                 **kwargs):
        super().__init__(
            name=f"批量登录_{len(accounts)}个账户",
            category=TaskCategory.ACCOUNT,
            priority=TaskPriority.NORMAL,
            description=f"批量登录 {len(accounts)} 个账户",
            **kwargs
        )
        self.accounts = accounts
        self.telegram_worker = telegram_worker
        self.max_concurrent = max_concurrent
        
    async def execute(self, context: Dict[str, Any] = None) -> Dict[str, Any]:
        """执行批量账户登录"""
        try:
            self.log(f"开始批量登录 {len(self.accounts)} 个账户")
            self.update_progress(0, len(self.accounts), "准备批量登录...")
            
            if not self.telegram_worker:
                raise ValueError("TelegramClientWorker 未提供")
            
            results = []
            semaphore = asyncio.Semaphore(self.max_concurrent)
            
            async def login_single_account(account_info):
                async with semaphore:
                    try:
                        phone = account_info.get('phone')
                        proxy = account_info.get('proxy')
                        
                        self.log(f"登录账户: {phone}")
                        
                        # 创建单个登录任务
                        login_task = AccountLoginTask(
                            phone=phone,
                            proxy=proxy,
                            telegram_worker=self.telegram_worker
                        )
                        
                        result = await login_task.execute()
                        results.append({
                            "phone": phone,
                            "success": True,
                            "result": result
                        })
                        
                        # 更新进度
                        current_progress = len(results)
                        self.update_progress(current_progress, len(self.accounts), 
                                           f"已完成 {current_progress}/{len(self.accounts)}")
                        
                    except Exception as e:
                        results.append({
                            "phone": account_info.get('phone'),
                            "success": False,
                            "error": str(e)
                        })
                        self.log(f"账户 {account_info.get('phone')} 登录失败: {str(e)}", "error")
            
            # 并发执行登录
            tasks = [login_single_account(account) for account in self.accounts]
            await asyncio.gather(*tasks, return_exceptions=True)
            
            success_count = sum(1 for r in results if r["success"])
            fail_count = len(results) - success_count
            
            self.update_progress(len(self.accounts), len(self.accounts), 
                               f"批量登录完成: 成功 {success_count}, 失败 {fail_count}")
            
            self.log(f"批量登录完成: 成功 {success_count} 个, 失败 {fail_count} 个")
            
            return {
                "success": True,
                "total": len(self.accounts),
                "success_count": success_count,
                "fail_count": fail_count,
                "results": results,
                "message": f"批量登录完成: 成功 {success_count}, 失败 {fail_count}"
            }
            
        except Exception as e:
            self.log(f"批量登录失败: {str(e)}", "error")
            raise
