#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
重构后的任务管理器 - 支持APScheduler的强大任务调度系统

主要特性：
1. 任务调度管理器与执行逻辑分离
2. 支持定时任务、立即执行、间隔执行、延迟执行
3. 完整的任务生命周期管理
4. 实时日志记录和状态跟踪
5. 任务持久化和恢复
6. 与UI层的信号集成
"""

import asyncio
import uuid
from typing import Dict, Any, Optional, Callable, List, Union, Type
from datetime import datetime, timedelta
from abc import ABC, abstractmethod
from enum import Enum
import json
import traceback

from PySide6.QtCore import QObject, Signal, QThread, QMutex, QTimer
from apscheduler.schedulers.asyncio import AsyncIOScheduler
from apscheduler.jobstores.sqlalchemy import SQLAlchemyJobStore
from apscheduler.executors.asyncio import AsyncIOExecutor
from apscheduler.events import EVENT_JOB_EXECUTED, EVENT_JOB_ERROR, EVENT_JOB_SUBMITTED, EVENT_JOB_ADDED, EVENT_JOB_REMOVED

from utils.logger import get_logger
from frameConf.setting import DATABASE_URL

logger = get_logger(__name__)

class TaskType(Enum):
    """任务类型"""
    IMMEDIATE = "immediate"      # 立即执行
    DELAYED = "delayed"         # 延迟执行
    SCHEDULED = "scheduled"     # 定时执行（cron）
    INTERVAL = "interval"       # 间隔执行

class TaskStatus(Enum):
    """任务状态"""
    PENDING = "pending"         # 等待执行
    RUNNING = "running"         # 正在执行
    COMPLETED = "completed"     # 执行完成
    FAILED = "failed"          # 执行失败
    CANCELLED = "cancelled"     # 已取消
    PAUSED = "paused"          # 已暂停

class TaskPriority(Enum):
    """任务优先级"""
    LOW = 1
    NORMAL = 2
    HIGH = 3
    URGENT = 4

class TaskCategory(Enum):
    """任务分类"""
    ACCOUNT = "account"         # 账户相关任务
    MESSAGE = "message"         # 消息相关任务
    MONITOR = "monitor"         # 监控相关任务
    INVITE = "invite"          # 邀请相关任务
    SYSTEM = "system"          # 系统相关任务

class TaskLogEntry:
    """任务日志条目"""

    def __init__(self, level: str, message: str, timestamp: datetime = None):
        self.level = level
        self.message = message
        self.timestamp = timestamp or datetime.now()

    def to_dict(self) -> Dict[str, Any]:
        return {
            "level": self.level,
            "message": self.message,
            "timestamp": self.timestamp.isoformat()
        }

class BaseTask(ABC):
    """任务基类 - 重构版本"""

    def __init__(self,
                 task_id: str = None,
                 name: str = "",
                 category: TaskCategory = TaskCategory.SYSTEM,
                 priority: TaskPriority = TaskPriority.NORMAL,
                 description: str = "",
                 **kwargs):
        self.task_id = task_id or str(uuid.uuid4())
        self.name = name
        self.category = category
        self.priority = priority
        self.description = description
        self.status = TaskStatus.PENDING
        self.created_at = datetime.now()
        self.started_at = None
        self.completed_at = None
        self.progress = 0
        self.total = 100
        self.logs: List[TaskLogEntry] = []
        self.result = None
        self.error = None
        self.metadata = kwargs

        # 任务执行上下文
        self.context: Dict[str, Any] = {}

        # 进度回调函数
        self._progress_callback: Optional[Callable] = None
        self._log_callback: Optional[Callable] = None

    @abstractmethod
    async def execute(self, context: Dict[str, Any] = None) -> Any:
        """执行任务的具体逻辑"""
        pass

    def set_progress_callback(self, callback: Callable):
        """设置进度回调函数"""
        self._progress_callback = callback

    def set_log_callback(self, callback: Callable):
        """设置日志回调函数"""
        self._log_callback = callback

    def log(self, message: str, level: str = "info"):
        """记录任务日志"""
        log_entry = TaskLogEntry(level, message)
        self.logs.append(log_entry)

        # 调用日志回调
        if self._log_callback:
            self._log_callback(self.task_id, log_entry.timestamp.isoformat(), level, message)

        # 记录到系统日志
        logger_func = getattr(logger, level, logger.info)
        logger_func(f"[Task {self.task_id}] {message}")

    def update_progress(self, current: int, total: int = None, message: str = ""):
        """更新任务进度"""
        self.progress = current
        if total is not None:
            self.total = total

        # 调用进度回调
        if self._progress_callback:
            self._progress_callback(self.task_id, current, self.total, message)

        if message:
            self.log(f"进度更新: {current}/{self.total} - {message}")

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "task_id": self.task_id,
            "name": self.name,
            "category": self.category.value,
            "priority": self.priority.value,
            "description": self.description,
            "status": self.status.value,
            "progress": self.progress,
            "total": self.total,
            "created_at": self.created_at.isoformat(),
            "started_at": self.started_at.isoformat() if self.started_at else None,
            "completed_at": self.completed_at.isoformat() if self.completed_at else None,
            "result": self.result,
            "error": self.error,
            "metadata": self.metadata,
            "logs": [log.to_dict() for log in self.logs]
        }

class TaskScheduler(QObject):
    """任务调度器 - 负责任务的调度和管理"""

    # 信号定义
    task_submitted = Signal(str, dict)  # task_id, task_info
    task_started = Signal(str, dict)    # task_id, task_info
    task_progress = Signal(str, int, int, str)  # task_id, current, total, message
    task_completed = Signal(str, object)  # task_id, result
    task_failed = Signal(str, str)      # task_id, error_message
    task_cancelled = Signal(str)        # task_id
    task_log = Signal(str, str, str, str)  # task_id, timestamp, level, message
    task_status_changed = Signal(str, str)  # task_id, new_status

    def __init__(self):
        super().__init__()
        self._setup_scheduler()
        self._tasks: Dict[str, BaseTask] = {}
        self._running = False
        self._mutex = QMutex()

        # 任务统计
        self._task_stats = {
            "total": 0,
            "pending": 0,
            "running": 0,
            "completed": 0,
            "failed": 0,
            "cancelled": 0
        }

    def _setup_scheduler(self):
        """配置APScheduler"""
        jobstores = {
            'default': SQLAlchemyJobStore(url=DATABASE_URL, tablename='apscheduler_jobs')
        }
        executors = {
            'default': AsyncIOExecutor(max_workers=20)  # 增加最大工作线程数
        }
        job_defaults = {
            'coalesce': False,
            'max_instances': 10,
            'misfire_grace_time': 30
        }

        self.scheduler = AsyncIOScheduler(
            jobstores=jobstores,
            executors=executors,
            job_defaults=job_defaults,
            timezone='Asia/Shanghai'  # 设置时区
        )

        # 添加事件监听
        self.scheduler.add_listener(self._on_job_executed, EVENT_JOB_EXECUTED)
        self.scheduler.add_listener(self._on_job_error, EVENT_JOB_ERROR)
        self.scheduler.add_listener(self._on_job_submitted, EVENT_JOB_SUBMITTED)
        self.scheduler.add_listener(self._on_job_added, EVENT_JOB_ADDED)
        self.scheduler.add_listener(self._on_job_removed, EVENT_JOB_REMOVED)

    async def start(self):
        """启动调度器"""
        if not self.scheduler.running:
            self.scheduler.start()
            self._running = True
            logger.info("任务调度器已启动")

    async def shutdown(self):
        """关闭调度器"""
        if self.scheduler.running:
            self.scheduler.shutdown(wait=True)
            self._running = False
            logger.info("任务调度器已关闭")

    def is_running(self) -> bool:
        """检查调度器是否运行中"""
        return self._running and self.scheduler.running

    def submit_immediate_task(self, task: BaseTask, context: Dict[str, Any] = None) -> str:
        """提交立即执行任务"""
        return self._submit_task(task, TaskType.IMMEDIATE, context=context)

    def submit_delayed_task(self, task: BaseTask, delay_seconds: int, context: Dict[str, Any] = None) -> str:
        """提交延迟执行任务"""
        return self._submit_task(task, TaskType.DELAYED, delay_seconds=delay_seconds, context=context)

    def submit_scheduled_task(self, task: BaseTask, cron_expr: str, context: Dict[str, Any] = None) -> str:
        """提交定时任务（cron表达式）"""
        return self._submit_task(task, TaskType.SCHEDULED, cron_expr=cron_expr, context=context)

    def submit_interval_task(self, task: BaseTask, interval_seconds: int, context: Dict[str, Any] = None) -> str:
        """提交间隔执行任务"""
        return self._submit_task(task, TaskType.INTERVAL, interval_seconds=interval_seconds, context=context)

    async def _execute_task_wrapper(self, task_id: str):
        """任务执行包装器"""
        task = self._tasks.get(task_id)
        if not task:
            logger.error(f"任务不存在: {task_id}")
            return

        try:
            # 更新任务状态为运行中
            old_status = task.status
            task.status = TaskStatus.RUNNING
            task.started_at = datetime.now()
            self._update_task_stats(old_status, TaskStatus.RUNNING)

            # 发送开始信号
            task_info = task.to_dict()
            self.task_started.emit(task_id, task_info)
            self.task_status_changed.emit(task_id, TaskStatus.RUNNING.value)

            # 执行任务
            task.log("任务开始执行")
            result = await task.execute(task.context)

            # 任务完成
            old_status = task.status
            task.status = TaskStatus.COMPLETED
            task.completed_at = datetime.now()
            task.result = result
            task.log("任务执行完成")
            self._update_task_stats(old_status, TaskStatus.COMPLETED)

            self.task_completed.emit(task_id, result)
            self.task_status_changed.emit(task_id, TaskStatus.COMPLETED.value)

        except Exception as e:
            # 任务失败
            old_status = task.status
            task.status = TaskStatus.FAILED
            task.completed_at = datetime.now()
            task.error = str(e)
            task.log(f"任务执行失败: {str(e)}", "error")
            self._update_task_stats(old_status, TaskStatus.FAILED)

            self.task_failed.emit(task_id, str(e))
            self.task_status_changed.emit(task_id, TaskStatus.FAILED.value)

            # 记录详细错误信息
            logger.exception(f"任务执行异常: {task_id}")
            task.log(f"详细错误: {traceback.format_exc()}", "error")

    def _update_task_stats(self, old_status: TaskStatus, new_status: TaskStatus):
        """更新任务统计"""
        if old_status != new_status:
            if old_status != TaskStatus.PENDING:  # 新任务不需要减少旧状态计数
                self._task_stats[old_status.value] = max(0, self._task_stats[old_status.value] - 1)
            self._task_stats[new_status.value] += 1

    def _on_task_progress(self, task_id: str, current: int, total: int, message: str):
        """任务进度回调"""
        self.task_progress.emit(task_id, current, total, message)

    def _on_task_log(self, task_id: str, timestamp: str, level: str, message: str):
        """任务日志回调"""
        self.task_log.emit(task_id, timestamp, level, message)

    def _submit_task(self, task: BaseTask, task_type: TaskType, **kwargs) -> str:
        """内部任务提交方法"""
        self._mutex.lock()
        try:
            # 设置任务回调
            task.set_progress_callback(self._on_task_progress)
            task.set_log_callback(self._on_task_log)

            # 存储任务
            self._tasks[task.task_id] = task
            self._update_task_stats(task.status, TaskStatus.PENDING)

            # 发送任务提交信号
            task_info = task.to_dict()
            task_info["type"] = task_type.value
            self.task_submitted.emit(task.task_id, task_info)

            context = kwargs.get('context', {})
            task.context.update(context)

            # 根据任务类型添加到调度器
            if task_type == TaskType.IMMEDIATE:
                job = self.scheduler.add_job(
                    self._execute_task_wrapper,
                    'date',
                    args=[task.task_id],
                    id=task.task_id,
                    replace_existing=True
                )
            elif task_type == TaskType.DELAYED:
                run_date = datetime.now() + timedelta(seconds=kwargs['delay_seconds'])
                job = self.scheduler.add_job(
                    self._execute_task_wrapper,
                    'date',
                    run_date=run_date,
                    args=[task.task_id],
                    id=task.task_id,
                    replace_existing=True
                )
            elif task_type == TaskType.SCHEDULED:
                # 解析cron表达式 (分 时 日 月 周)
                cron_parts = kwargs['cron_expr'].split()
                if len(cron_parts) != 5:
                    raise ValueError("Cron表达式格式错误，应为: 分 时 日 月 周")

                job = self.scheduler.add_job(
                    self._execute_task_wrapper,
                    'cron',
                    minute=cron_parts[0],
                    hour=cron_parts[1],
                    day=cron_parts[2],
                    month=cron_parts[3],
                    day_of_week=cron_parts[4],
                    args=[task.task_id],
                    id=task.task_id,
                    replace_existing=True
                )
            elif task_type == TaskType.INTERVAL:
                job = self.scheduler.add_job(
                    self._execute_task_wrapper,
                    'interval',
                    seconds=kwargs['interval_seconds'],
                    args=[task.task_id],
                    id=task.task_id,
                    replace_existing=True
                )

            logger.info(f"任务已提交: {task.name} [ID: {task.task_id}] [类型: {task_type.value}]")
            return task.task_id

        except Exception as e:
            logger.error(f"提交任务失败: {str(e)}")
            raise
        finally:
            self._mutex.unlock()

    def cancel_task(self, task_id: str) -> bool:
        """取消任务"""
        try:
            self.scheduler.remove_job(task_id)
            task = self._tasks.get(task_id)
            if task:
                old_status = task.status
                task.status = TaskStatus.CANCELLED
                task.log("任务已取消")
                self._update_task_stats(old_status, TaskStatus.CANCELLED)

            self.task_cancelled.emit(task_id)
            self.task_status_changed.emit(task_id, TaskStatus.CANCELLED.value)
            logger.info(f"任务已取消: {task_id}")
            return True
        except Exception as e:
            logger.error(f"取消任务失败: {task_id}, 错误: {str(e)}")
            return False
    
    def submit_immediate_task(self, task: BaseTask, context: Dict[str, Any] = None) -> str:
        """提交立即执行任务"""
        return self._submit_task(task, TaskType.IMMEDIATE, context=context)
    
    def submit_delayed_task(self, task: BaseTask, delay_seconds: int, context: Dict[str, Any] = None) -> str:
        """提交延迟执行任务"""
        return self._submit_task(task, TaskType.DELAYED, delay_seconds=delay_seconds, context=context)
    
    def submit_scheduled_task(self, task: BaseTask, cron_expr: str, context: Dict[str, Any] = None) -> str:
        """提交定时任务"""
        return self._submit_task(task, TaskType.SCHEDULED, cron_expr=cron_expr, context=context)
    
    def submit_interval_task(self, task: BaseTask, interval_seconds: int, context: Dict[str, Any] = None) -> str:
        """提交间隔执行任务"""
        return self._submit_task(task, TaskType.INTERVAL, interval_seconds=interval_seconds, context=context)
    
    def _submit_task(self, task: BaseTask, task_type: TaskType, **kwargs) -> str:
        """内部任务提交方法"""
        self._mutex.lock()
        try:
            self._tasks[task.task_id] = task
            
            # 发送任务提交信号
            task_info = {
                "name": task.name,
                "type": task_type.value,
                "priority": task.priority.value,
                "created_at": task.created_at.isoformat(),
                "metadata": task.metadata
            }
            self.task_submitted.emit(task.task_id, task_info)
            
            context = kwargs.get('context', {})
            
            if task_type == TaskType.IMMEDIATE:
                job = self.scheduler.add_job(
                    self._execute_task_wrapper,
                    'date',
                    args=[task.task_id, context],
                    id=task.task_id,
                    replace_existing=True
                )
            elif task_type == TaskType.DELAYED:
                run_date = datetime.now() + timedelta(seconds=kwargs['delay_seconds'])
                job = self.scheduler.add_job(
                    self._execute_task_wrapper,
                    'date',
                    run_date=run_date,
                    args=[task.task_id, context],
                    id=task.task_id,
                    replace_existing=True
                )
            elif task_type == TaskType.SCHEDULED:
                # 解析cron表达式 (分 时 日 月 周)
                cron_parts = kwargs['cron_expr'].split()
                if len(cron_parts) != 5:
                    raise ValueError("Cron表达式格式错误，应为: 分 时 日 月 周")
                
                job = self.scheduler.add_job(
                    self._execute_task_wrapper,
                    'cron',
                    minute=cron_parts[0],
                    hour=cron_parts[1],
                    day=cron_parts[2],
                    month=cron_parts[3],
                    day_of_week=cron_parts[4],
                    args=[task.task_id, context],
                    id=task.task_id,
                    replace_existing=True
                )
            elif task_type == TaskType.INTERVAL:
                job = self.scheduler.add_job(
                    self._execute_task_wrapper,
                    'interval',
                    seconds=kwargs['interval_seconds'],
                    args=[task.task_id, context],
                    id=task.task_id,
                    replace_existing=True
                )
            
            logger.info(f"任务已提交: {task.name} [ID: {task.task_id}] [类型: {task_type.value}]")
            return task.task_id
            
        finally:
            self._mutex.unlock()
    
    async def _execute_task_wrapper(self, task_id: str, context: Dict[str, Any] = None):
        """任务执行包装器"""
        task = self._tasks.get(task_id)
        if not task:
            logger.error(f"任务不存在: {task_id}")
            return
        
        try:
            # 更新任务状态
            task.status = TaskStatus.RUNNING
            task.started_at = datetime.now()
            
            # 发送开始信号
            task_info = {
                "name": task.name,
                "status": task.status.value,
                "started_at": task.started_at.isoformat(),
                "priority": task.priority.value
            }
            self.task_started.emit(task_id, task_info)
            
            # 执行任务
            task.log("任务开始执行")
            result = await task.execute(context or {})
            
            # 任务完成
            task.status = TaskStatus.COMPLETED
            task.completed_at = datetime.now()
            task.result = result
            task.log("任务执行完成")
            
            self.task_completed.emit(task_id, result)
            
        except Exception as e:
            # 任务失败
            task.status = TaskStatus.FAILED
            task.completed_at = datetime.now()
            task.error = str(e)
            task.log(f"任务执行失败: {str(e)}", "error")
            
            self.task_failed.emit(task_id, str(e))
            logger.exception(f"任务执行异常: {task_id}")

    def get_task_status(self, task_id: str) -> Optional[Dict]:
        """获取任务状态"""
        task = self._tasks.get(task_id)
        if not task:
            return None
        return task.to_dict()

    def get_all_tasks(self) -> Dict[str, Dict]:
        """获取所有任务状态"""
        return {task_id: task.to_dict() for task_id, task in self._tasks.items()}

    def get_tasks_by_category(self, category: TaskCategory) -> Dict[str, Dict]:
        """根据分类获取任务"""
        return {
            task_id: task.to_dict()
            for task_id, task in self._tasks.items()
            if task.category == category
        }

    def get_tasks_by_status(self, status: TaskStatus) -> Dict[str, Dict]:
        """根据状态获取任务"""
        return {
            task_id: task.to_dict()
            for task_id, task in self._tasks.items()
            if task.status == status
        }

    def get_task_stats(self) -> Dict[str, int]:
        """获取任务统计信息"""
        return self._task_stats.copy()

    def clear_completed_tasks(self) -> int:
        """清理已完成的任务"""
        completed_tasks = [
            task_id for task_id, task in self._tasks.items()
            if task.status in [TaskStatus.COMPLETED, TaskStatus.FAILED, TaskStatus.CANCELLED]
        ]

        for task_id in completed_tasks:
            del self._tasks[task_id]

        # 重新计算统计
        self._recalculate_stats()

        logger.info(f"已清理 {len(completed_tasks)} 个已完成的任务")
        return len(completed_tasks)

    def _recalculate_stats(self):
        """重新计算任务统计"""
        self._task_stats = {
            "total": len(self._tasks),
            "pending": 0,
            "running": 0,
            "completed": 0,
            "failed": 0,
            "cancelled": 0
        }

        for task in self._tasks.values():
            self._task_stats[task.status.value] += 1

    # APScheduler事件处理器
    def _on_job_executed(self, event):
        """任务执行完成事件"""
        logger.debug(f"调度器任务执行完成: {event.job_id}")

    def _on_job_error(self, event):
        """任务执行错误事件"""
        logger.error(f"调度器任务执行错误: {event.job_id}, 异常: {event.exception}")

    def _on_job_submitted(self, event):
        """任务提交事件"""
        logger.debug(f"任务已提交到调度器: {event.job_id}")

    def _on_job_added(self, event):
        """任务添加事件"""
        logger.debug(f"任务已添加到调度器: {event.job_id}")

    def _on_job_removed(self, event):
        """任务移除事件"""
        logger.debug(f"任务已从调度器移除: {event.job_id}")


class TaskExecutor(QThread):
    """任务执行器线程 - 包装TaskScheduler"""

    def __init__(self):
        super().__init__()
        self.scheduler = TaskScheduler()
        self._running = False
        self._event_loop = None

        # 转发信号
        self.task_submitted = self.scheduler.task_submitted
        self.task_started = self.scheduler.task_started
        self.task_progress = self.scheduler.task_progress
        self.task_completed = self.scheduler.task_completed
        self.task_failed = self.scheduler.task_failed
        self.task_cancelled = self.scheduler.task_cancelled
        self.task_log = self.scheduler.task_log
        self.task_status_changed = self.scheduler.task_status_changed

    def run(self):
        """线程运行方法"""
        self._event_loop = asyncio.new_event_loop()
        asyncio.set_event_loop(self._event_loop)

        try:
            self._event_loop.run_until_complete(self._async_run())
        except Exception as e:
            logger.error(f"任务执行器运行异常: {e}")
        finally:
            self._event_loop.close()

    async def _async_run(self):
        """异步运行方法"""
        await self.scheduler.start()
        self._running = True
        logger.info("任务执行器已启动")

        try:
            while self._running:
                await asyncio.sleep(0.1)
        except Exception as e:
            logger.error(f"任务执行器异步运行异常: {e}")
        finally:
            await self.scheduler.shutdown()
            logger.info("任务执行器已关闭")

    def stop_executor(self):
        """停止执行器"""
        self._running = False
        if self._event_loop and not self._event_loop.is_closed():
            self._event_loop.call_soon_threadsafe(self._event_loop.stop)

    # 代理方法到调度器
    def submit_immediate_task(self, task: BaseTask, context: Dict[str, Any] = None) -> str:
        return self.scheduler.submit_immediate_task(task, context)

    def submit_delayed_task(self, task: BaseTask, delay_seconds: int, context: Dict[str, Any] = None) -> str:
        return self.scheduler.submit_delayed_task(task, delay_seconds, context)

    def submit_scheduled_task(self, task: BaseTask, cron_expr: str, context: Dict[str, Any] = None) -> str:
        return self.scheduler.submit_scheduled_task(task, cron_expr, context)

    def submit_interval_task(self, task: BaseTask, interval_seconds: int, context: Dict[str, Any] = None) -> str:
        return self.scheduler.submit_interval_task(task, interval_seconds, context)

    def cancel_task(self, task_id: str) -> bool:
        return self.scheduler.cancel_task(task_id)

    def get_task_status(self, task_id: str) -> Optional[Dict]:
        return self.scheduler.get_task_status(task_id)

    def get_all_tasks(self) -> Dict[str, Dict]:
        return self.scheduler.get_all_tasks()

    def get_task_stats(self) -> Dict[str, int]:
        return self.scheduler.get_task_stats()


# 全局任务执行器实例
task_executor = TaskExecutor()
