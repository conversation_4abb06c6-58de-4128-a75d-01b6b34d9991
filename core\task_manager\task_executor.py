#!/usr/bin/env python
# -*- coding: utf-8 -*-

import asyncio
import uuid
from typing import Dict, Any, Optional, Callable, List, Union
from datetime import datetime, timedelta
from abc import ABC, abstractmethod
from enum import Enum
import json

from PySide6.QtCore import QObject, Signal, QThread, QMutex
from apscheduler.schedulers.asyncio import AsyncIOScheduler
from apscheduler.jobstores.sqlalchemy import SQLAlchemyJobStore
from apscheduler.executors.asyncio import AsyncIOExecutor
from apscheduler.events import EVENT_JOB_EXECUTED, EVENT_JOB_ERROR, EVENT_JOB_SUBMITTED

from utils.logger import get_logger
from frameConf.setting import DATABASE_URL

logger = get_logger(__name__)

class TaskType(Enum):
    """任务类型"""
    IMMEDIATE = "immediate"      # 立即执行
    DELAYED = "delayed"         # 延迟执行
    SCHEDULED = "scheduled"     # 定时执行（cron）
    INTERVAL = "interval"       # 间隔执行

class TaskStatus(Enum):
    """任务状态"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"
    PAUSED = "paused"

class TaskPriority(Enum):
    """任务优先级"""
    LOW = 1
    NORMAL = 2
    HIGH = 3
    URGENT = 4

class BaseTask(ABC):
    """任务基类"""
    
    def __init__(self, task_id: str, name: str, priority: TaskPriority = TaskPriority.NORMAL, **kwargs):
        self.task_id = task_id
        self.name = name
        self.priority = priority
        self.status = TaskStatus.PENDING
        self.created_at = datetime.now()
        self.started_at = None
        self.completed_at = None
        self.progress = 0
        self.total = 100
        self.logs = []
        self.result = None
        self.error = None
        self.metadata = kwargs
        
    @abstractmethod
    async def execute(self, context: Dict[str, Any] = None) -> Any:
        """执行任务的具体逻辑"""
        pass
    
    def log(self, message: str, level: str = "info"):
        """记录任务日志"""
        log_entry = {
            "timestamp": datetime.now(),
            "level": level,
            "message": message
        }
        self.logs.append(log_entry)
        logger_func = getattr(logger, level, logger.info)
        logger_func(f"[Task {self.task_id}] {message}")
    
    def update_progress(self, current: int, total: int = None, message: str = ""):
        """更新任务进度"""
        self.progress = current
        if total is not None:
            self.total = total
        if message:
            self.log(f"进度更新: {current}/{self.total} - {message}")

class TaskExecutor(QThread):
    """统一任务执行器"""
    
    # 信号定义
    task_submitted = Signal(str, dict)  # task_id, task_info
    task_started = Signal(str, dict)    # task_id, task_info
    task_progress = Signal(str, int, int, str)  # task_id, current, total, message
    task_completed = Signal(str, object)  # task_id, result
    task_failed = Signal(str, str)      # task_id, error_message
    task_cancelled = Signal(str)        # task_id
    task_log = Signal(str, str, str, str)  # task_id, timestamp, level, message
    
    def __init__(self):
        super().__init__()
        self._setup_scheduler()
        self._tasks: Dict[str, BaseTask] = {}
        self._running = False
        self._event_loop = None
        self._mutex = QMutex()
        
    def _setup_scheduler(self):
        """配置APScheduler"""
        jobstores = {
            'default': SQLAlchemyJobStore(url=DATABASE_URL, tablename='apscheduler_jobs')
        }
        executors = {
            'default': AsyncIOExecutor()
        }
        job_defaults = {
            'coalesce': False,
            'max_instances': 10,
            'misfire_grace_time': 30
        }
        
        self.scheduler = AsyncIOScheduler(
            jobstores=jobstores,
            executors=executors,
            job_defaults=job_defaults
        )
        
        # 添加事件监听
        self.scheduler.add_listener(self._on_job_executed, EVENT_JOB_EXECUTED)
        self.scheduler.add_listener(self._on_job_error, EVENT_JOB_ERROR)
        self.scheduler.add_listener(self._on_job_submitted, EVENT_JOB_SUBMITTED)
    
    def run(self):
        """线程运行方法"""
        self._event_loop = asyncio.new_event_loop()
        asyncio.set_event_loop(self._event_loop)
        
        try:
            self._event_loop.run_until_complete(self._async_run())
        except Exception as e:
            logger.error(f"任务执行器运行异常: {e}")
        finally:
            self._event_loop.close()
    
    async def _async_run(self):
        """异步运行方法"""
        self.scheduler.start()
        self._running = True
        logger.info("任务执行器已启动")
        
        try:
            while self._running:
                await asyncio.sleep(0.1)
        except Exception as e:
            logger.error(f"任务执行器异步运行异常: {e}")
        finally:
            self.scheduler.shutdown(wait=True)
            logger.info("任务执行器已关闭")
    
    def stop_executor(self):
        """停止执行器"""
        self._running = False
        if self._event_loop and not self._event_loop.is_closed():
            self._event_loop.call_soon_threadsafe(self._event_loop.stop)
    
    def submit_immediate_task(self, task: BaseTask, context: Dict[str, Any] = None) -> str:
        """提交立即执行任务"""
        return self._submit_task(task, TaskType.IMMEDIATE, context=context)
    
    def submit_delayed_task(self, task: BaseTask, delay_seconds: int, context: Dict[str, Any] = None) -> str:
        """提交延迟执行任务"""
        return self._submit_task(task, TaskType.DELAYED, delay_seconds=delay_seconds, context=context)
    
    def submit_scheduled_task(self, task: BaseTask, cron_expr: str, context: Dict[str, Any] = None) -> str:
        """提交定时任务"""
        return self._submit_task(task, TaskType.SCHEDULED, cron_expr=cron_expr, context=context)
    
    def submit_interval_task(self, task: BaseTask, interval_seconds: int, context: Dict[str, Any] = None) -> str:
        """提交间隔执行任务"""
        return self._submit_task(task, TaskType.INTERVAL, interval_seconds=interval_seconds, context=context)
    
    def _submit_task(self, task: BaseTask, task_type: TaskType, **kwargs) -> str:
        """内部任务提交方法"""
        self._mutex.lock()
        try:
            self._tasks[task.task_id] = task
            
            # 发送任务提交信号
            task_info = {
                "name": task.name,
                "type": task_type.value,
                "priority": task.priority.value,
                "created_at": task.created_at.isoformat(),
                "metadata": task.metadata
            }
            self.task_submitted.emit(task.task_id, task_info)
            
            context = kwargs.get('context', {})
            
            if task_type == TaskType.IMMEDIATE:
                job = self.scheduler.add_job(
                    self._execute_task_wrapper,
                    'date',
                    args=[task.task_id, context],
                    id=task.task_id,
                    replace_existing=True
                )
            elif task_type == TaskType.DELAYED:
                run_date = datetime.now() + timedelta(seconds=kwargs['delay_seconds'])
                job = self.scheduler.add_job(
                    self._execute_task_wrapper,
                    'date',
                    run_date=run_date,
                    args=[task.task_id, context],
                    id=task.task_id,
                    replace_existing=True
                )
            elif task_type == TaskType.SCHEDULED:
                # 解析cron表达式 (分 时 日 月 周)
                cron_parts = kwargs['cron_expr'].split()
                if len(cron_parts) != 5:
                    raise ValueError("Cron表达式格式错误，应为: 分 时 日 月 周")
                
                job = self.scheduler.add_job(
                    self._execute_task_wrapper,
                    'cron',
                    minute=cron_parts[0],
                    hour=cron_parts[1],
                    day=cron_parts[2],
                    month=cron_parts[3],
                    day_of_week=cron_parts[4],
                    args=[task.task_id, context],
                    id=task.task_id,
                    replace_existing=True
                )
            elif task_type == TaskType.INTERVAL:
                job = self.scheduler.add_job(
                    self._execute_task_wrapper,
                    'interval',
                    seconds=kwargs['interval_seconds'],
                    args=[task.task_id, context],
                    id=task.task_id,
                    replace_existing=True
                )
            
            logger.info(f"任务已提交: {task.name} [ID: {task.task_id}] [类型: {task_type.value}]")
            return task.task_id
            
        finally:
            self._mutex.unlock()
    
    async def _execute_task_wrapper(self, task_id: str, context: Dict[str, Any] = None):
        """任务执行包装器"""
        task = self._tasks.get(task_id)
        if not task:
            logger.error(f"任务不存在: {task_id}")
            return
        
        try:
            # 更新任务状态
            task.status = TaskStatus.RUNNING
            task.started_at = datetime.now()
            
            # 发送开始信号
            task_info = {
                "name": task.name,
                "status": task.status.value,
                "started_at": task.started_at.isoformat(),
                "priority": task.priority.value
            }
            self.task_started.emit(task_id, task_info)
            
            # 执行任务
            task.log("任务开始执行")
            result = await task.execute(context or {})
            
            # 任务完成
            task.status = TaskStatus.COMPLETED
            task.completed_at = datetime.now()
            task.result = result
            task.log("任务执行完成")
            
            self.task_completed.emit(task_id, result)
            
        except Exception as e:
            # 任务失败
            task.status = TaskStatus.FAILED
            task.completed_at = datetime.now()
            task.error = str(e)
            task.log(f"任务执行失败: {str(e)}", "error")
            
            self.task_failed.emit(task_id, str(e))
            logger.exception(f"任务执行异常: {task_id}")
    
    def _on_job_executed(self, event):
        """任务执行完成事件"""
        logger.debug(f"调度器任务执行完成: {event.job_id}")
    
    def _on_job_error(self, event):
        """任务执行错误事件"""
        logger.error(f"调度器任务执行错误: {event.job_id}, 异常: {event.exception}")
    
    def _on_job_submitted(self, event):
        """任务提交事件"""
        logger.debug(f"任务已提交到调度器: {event.job_id}")
    
    def get_task_status(self, task_id: str) -> Optional[Dict]:
        """获取任务状态"""
        task = self._tasks.get(task_id)
        if not task:
            return None
        
        return {
            "task_id": task.task_id,
            "name": task.name,
            "status": task.status.value,
            "priority": task.priority.value,
            "progress": task.progress,
            "total": task.total,
            "created_at": task.created_at.isoformat(),
            "started_at": task.started_at.isoformat() if task.started_at else None,
            "completed_at": task.completed_at.isoformat() if task.completed_at else None,
            "result": task.result,
            "error": task.error,
            "logs": [
                {
                    "timestamp": log["timestamp"].isoformat(),
                    "level": log["level"],
                    "message": log["message"]
                }
                for log in task.logs
            ]
        }
    
    def cancel_task(self, task_id: str) -> bool:
        """取消任务"""
        try:
            self.scheduler.remove_job(task_id)
            task = self._tasks.get(task_id)
            if task:
                task.status = TaskStatus.CANCELLED
                task.log("任务已取消")
            
            self.task_cancelled.emit(task_id)
            logger.info(f"任务已取消: {task_id}")
            return True
        except Exception as e:
            logger.error(f"取消任务失败: {task_id}, 错误: {str(e)}")
            return False
    
    def get_all_tasks(self) -> Dict[str, Dict]:
        """获取所有任务状态"""
        return {task_id: self.get_task_status(task_id) for task_id in self._tasks.keys()}

# 全局任务执行器实例
task_executor = TaskExecutor()