 #!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
重构后的任务管理服务

提供统一的任务管理接口，支持：
1. 任务提交和管理
2. 任务状态监控
3. 任务日志记录
4. 与UI层的信号集成
"""

from typing import Dict, Any, Optional, List, Type
from PySide6.QtCore import QObject, Signal

from core.task_manager.task_executor import (
    task_executor, BaseTask, TaskPriority, TaskCategory, TaskStatus, TaskType
)
from core.task_manager.account_tasks import (
    AccountLoginTask, AccountCodeVerificationTask, AccountPasswordVerificationTask,
    AccountInfoRefreshTask, BatchAccountLoginTask
)
from utils.logger import get_logger

logger = get_logger(__name__)

class TaskService(QObject):
    """任务管理服务 - 提供统一的任务管理接口"""

    # 信号转发
    task_submitted = Signal(str, dict)
    task_started = Signal(str, dict)
    task_progress = Signal(str, int, int, str)
    task_completed = Signal(str, object)
    task_failed = Signal(str, str)
    task_cancelled = Signal(str)
    task_log = Signal(str, str, str, str)
    task_status_changed = Signal(str, str)

    def __init__(self):
        super().__init__()
        self._logger = get_logger("core.task_manager.service")
        self._connect_executor_signals()

    def _connect_executor_signals(self):
        """连接执行器信号"""
        task_executor.task_submitted.connect(self.task_submitted.emit)
        task_executor.task_started.connect(self.task_started.emit)
        task_executor.task_progress.connect(self.task_progress.emit)
        task_executor.task_completed.connect(self.task_completed.emit)
        task_executor.task_failed.connect(self.task_failed.emit)
        task_executor.task_cancelled.connect(self.task_cancelled.emit)
        task_executor.task_log.connect(self.task_log.emit)
        task_executor.task_status_changed.connect(self.task_status_changed.emit)

    async def start_service(self):
        """启动任务服务"""
        if not task_executor.isRunning():
            task_executor.start()
            self._logger.info("任务服务已启动")

    def stop_service(self):
        """停止任务服务"""
        task_executor.stop_executor()
        self._logger.info("任务服务已停止")

    # ==================== 任务提交接口 ====================

    def submit_immediate_task(self, task: BaseTask, context: Dict[str, Any] = None) -> str:
        """提交立即执行任务"""
        return task_executor.submit_immediate_task(task, context)

    def submit_delayed_task(self, task: BaseTask, delay_seconds: int, context: Dict[str, Any] = None) -> str:
        """提交延迟执行任务"""
        return task_executor.submit_delayed_task(task, delay_seconds, context)

    def submit_scheduled_task(self, task: BaseTask, cron_expr: str, context: Dict[str, Any] = None) -> str:
        """提交定时任务"""
        return task_executor.submit_scheduled_task(task, cron_expr, context)

    def submit_interval_task(self, task: BaseTask, interval_seconds: int, context: Dict[str, Any] = None) -> str:
        """提交间隔执行任务"""
        return task_executor.submit_interval_task(task, interval_seconds, context)

    # ==================== 账户相关任务接口 ====================

    def submit_account_login_task(self, phone: str, proxy: Dict[str, Any] = None,
                                 telegram_worker=None, context: Dict[str, Any] = None) -> str:
        """提交账户登录任务"""
        task = AccountLoginTask(
            phone=phone,
            proxy=proxy,
            telegram_worker=telegram_worker
        )
        return self.submit_immediate_task(task, context)

    def submit_account_code_verification_task(self, phone: str, code: str, password: str = None,
                                            telegram_worker=None, context: Dict[str, Any] = None) -> str:
        """提交账户验证码验证任务"""
        task = AccountCodeVerificationTask(
            phone=phone,
            code=code,
            password=password,
            telegram_worker=telegram_worker
        )
        return self.submit_immediate_task(task, context)

    def submit_account_password_verification_task(self, phone: str, password: str,
                                                 telegram_worker=None, context: Dict[str, Any] = None) -> str:
        """提交账户密码验证任务"""
        task = AccountPasswordVerificationTask(
            phone=phone,
            password=password,
            telegram_worker=telegram_worker
        )
        return self.submit_immediate_task(task, context)

    def submit_account_info_refresh_task(self, phone: str, telegram_worker=None,
                                       context: Dict[str, Any] = None) -> str:
        """提交账户信息刷新任务"""
        task = AccountInfoRefreshTask(
            phone=phone,
            telegram_worker=telegram_worker
        )
        return self.submit_immediate_task(task, context)

    def submit_batch_account_login_task(self, accounts: List[Dict[str, Any]], max_concurrent: int = 3,
                                      telegram_worker=None, context: Dict[str, Any] = None) -> str:
        """提交批量账户登录任务"""
        task = BatchAccountLoginTask(
            accounts=accounts,
            max_concurrent=max_concurrent,
            telegram_worker=telegram_worker
        )
        return self.submit_immediate_task(task, context)

    # ==================== 任务管理接口 ====================

    def cancel_task(self, task_id: str) -> bool:
        """取消任务"""
        return task_executor.cancel_task(task_id)

    def get_task_status(self, task_id: str) -> Optional[Dict]:
        """获取任务状态"""
        return task_executor.get_task_status(task_id)

    def get_all_tasks(self) -> Dict[str, Dict]:
        """获取所有任务"""
        return task_executor.get_all_tasks()

    def get_tasks_by_category(self, category: str) -> Dict[str, Dict]:
        """根据分类获取任务"""
        try:
            task_category = TaskCategory(category)
            return task_executor.scheduler.get_tasks_by_category(task_category)
        except ValueError:
            self._logger.error(f"无效的任务分类: {category}")
            return {}

    def get_tasks_by_status(self, status: str) -> Dict[str, Dict]:
        """根据状态获取任务"""
        try:
            task_status = TaskStatus(status)
            return task_executor.scheduler.get_tasks_by_status(task_status)
        except ValueError:
            self._logger.error(f"无效的任务状态: {status}")
            return {}

    def get_task_stats(self) -> Dict[str, int]:
        """获取任务统计信息"""
        return task_executor.get_task_stats()

    def clear_completed_tasks(self) -> int:
        """清理已完成的任务"""
        return task_executor.scheduler.clear_completed_tasks()

    # ==================== 定时任务接口 ====================

    def schedule_account_login(self, phone: str, cron_expr: str, proxy: Dict[str, Any] = None,
                              telegram_worker=None, context: Dict[str, Any] = None) -> str:
        """定时账户登录"""
        task = AccountLoginTask(
            phone=phone,
            proxy=proxy,
            telegram_worker=telegram_worker
        )
        return self.submit_scheduled_task(task, cron_expr, context)

    def schedule_account_info_refresh(self, phone: str, cron_expr: str, telegram_worker=None,
                                    context: Dict[str, Any] = None) -> str:
        """定时刷新账户信息"""
        task = AccountInfoRefreshTask(
            phone=phone,
            telegram_worker=telegram_worker
        )
        return self.submit_scheduled_task(task, cron_expr, context)

    def schedule_batch_account_refresh(self, accounts: List[str], cron_expr: str,
                                     telegram_worker=None, context: Dict[str, Any] = None) -> str:
        """定时批量刷新账户信息"""
        # 这里可以创建一个批量刷新任务类
        # 暂时使用现有的批量登录任务作为示例
        account_list = [{"phone": phone} for phone in accounts]
        task = BatchAccountLoginTask(
            accounts=account_list,
            telegram_worker=telegram_worker
        )
        return self.submit_scheduled_task(task, cron_expr, context)


# 全局任务服务实例
task_service = TaskService()