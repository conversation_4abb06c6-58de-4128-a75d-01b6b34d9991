 #!/usr/bin/env python
# -*- coding: utf-8 -*-

from typing import Dict, Any, Optional, List
from PySide6.QtCore import QObject, Signal

from core.task_manager.task_executor import task_executor, BaseTask, TaskPriority
from utils.logger import get_logger

logger = get_logger(__name__)

class TaskService(QObject):
    """任务管理服务 - 提供统一的任务管理接口"""
    
    # 信号转发
    task_submitted = Signal(str, dict)
    task_started = Signal(str, dict)
    task_progress = Signal(str, int, int, str)
    task_completed = Signal(str, object)
    task_failed = Signal(str, str)
    task_cancelled = Signal(str)
    task_log = Signal(str, str, str, str)
    
    def __init__(self):
        super().__init__()
        self._logger = get_logger("core.task_manager.service")
        self._connect_executor_signals()
        
    def _connect_executor_signals(self):
        """连接执行器信号"""
        task_executor.task_submitted.connect(self.task_submitted.emit)
        task_executor.task_started.connect(self.task_started.emit)
        task_executor.task_progress.connect(self.task_progress.emit)
        task_executor.task_completed.connect(self.task_completed.emit)
        task_executor.task_failed.connect(self.task_failed.emit)
        task_executor.task_cancelled.connect(self.task_cancelled.emit)
        task_executor.task_log.connect(self.task_log.emit)
    
    async def start_service(self):
        """启动任务服务"""
        if not task_executor.isRunning():
            task_executor.start()
            self._logger.info("任务服务已启动")
    
    def stop_service(self):
        """停止任务服务"""
        task_executor.stop_executor()
        self._logger.info("任务服务已停止")
    
    def submit_immediate_task(self, task: BaseTask, context: Dict[str, Any] = None) -> str:
        """提交立即执行任务"""
        return task_executor.submit_immediate_task(task, context)
    
    def submit_delayed_task(self, task: BaseTask, delay_seconds: int, context: Dict[str, Any] = None) -> str:
        """提交延迟执行任务"""
        return task_executor.submit_delayed_task(task, delay_seconds, context)
    
    def submit_scheduled_task(self, task: BaseTask, cron_expr: str, context: Dict[str, Any] = None) -> str:
        """提交定时任务"""
        return task_executor.submit_scheduled_task(task, cron_expr, context)
    
    def submit_interval_task(self, task: BaseTask, interval_seconds: int, context: Dict[str, Any] = None) -> str:
        """提交间隔执行任务"""
        return task_executor.submit_interval_task(task, interval_seconds, context)
    
    def get_task_status(self, task_id: str) -> Optional[Dict]:
        """获取任务状态"""
        return task_executor.get_task_status(task_id)
    
    def cancel_task(self, task_id: str) -> bool:
        """取消任务"""
        return task_executor.cancel_task(task_id)
    
    def get_all_tasks(self) -> Dict[str, Dict]:
        """获取所有任务"""
        return task_executor.get_all_tasks()

# 全局任务服务实例
task_service = TaskService()