#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Telegram客户端池管理器

基于新任务执行器的客户端池管理，提供：
1. 客户端生命周期管理
2. 连接池优化
3. 自动重连机制
4. 资源清理
"""

import os
import asyncio
from typing import Dict, Optional, Tuple, Any,List
from datetime import datetime, timedelta

from telethon import TelegramClient
from telethon.errors import AuthKeyError, FloodWaitError

from PySide6.QtCore import QObject, Signal, QTimer

from utils.get_system_proxy import GetProxy
from utils.logger import get_logger
from config import config
from .user_state_manager import user_state_manager, UserStatus

logger = get_logger(__name__)


class ClientPool(QObject):
    """Telegram客户端池管理器"""
    
    # 信号定义
    client_connected = Signal(str, dict)  # phone, client_info
    client_disconnected = Signal(str, str)  # phone, reason
    client_error = Signal(str, str)  # phone, error_message
    
    def __init__(self, api_id: int = None, api_hash: str = None, session_dir: str = None):
        super().__init__()
        self._api_id = api_id or config.get("api_id")
        self._api_hash = api_hash or config.get("api_hash")
        self._session_dir = session_dir or config.get("sessions_path")
        
        # 确保session目录存在
        os.makedirs(self._session_dir, exist_ok=True)
        
        # 客户端池 {phone: client}
        self._clients: Dict[str, TelegramClient] = {}
        
        # 客户端元数据 {phone: metadata}
        self._client_metadata: Dict[str, Dict[str, Any]] = {}
        
        # 设置定时器进行健康检查
        self._health_check_timer = QTimer()
        self._health_check_timer.timeout.connect(self._perform_health_check)
        self._health_check_timer.start(60000)  # 每分钟检查一次
        
        logger.info("客户端池管理器已初始化")
    
    def get_session_path(self, phone: str) -> str:
        """获取session文件路径"""
        if not os.path.dirname(phone) and not os.path.isabs(phone):
            return os.path.join(self._session_dir, f"{phone}.session")
        return phone
    
    def get_proxy_config(self, proxy: Dict[str, Any] = None) -> Optional[Dict]:
        """获取代理配置"""
        if not proxy:
            return None
            
        try:
            proxy_type = proxy.get('type', '').lower()
            
            if proxy_type == 'none' or not proxy_type:
                return None
            elif proxy_type == 'system':
                proxy_instance = GetProxy(type="system")
                return proxy_instance.get_proxy()
            elif proxy_type in ['socks5', 'http', 'socks4']:
                ip = proxy.get('ip')
                port = proxy.get('port')
                username = proxy.get('username')
                password = proxy.get('password')
                
                if not ip or port is None:
                    logger.warning(f"代理配置缺少必要参数: {proxy}")
                    return None
                
                proxy_instance = GetProxy(
                    type=proxy_type,
                    ip=ip,
                    port=port,
                    username=username,
                    password=password
                )
                return proxy_instance.get_proxy()
            else:
                logger.warning(f"未知的代理类型: {proxy_type}")
                return None
                
        except Exception as e:
            logger.error(f"处理代理配置失败: {e}")
            return None
    
    async def create_client(self, phone: str, proxy: Dict[str, Any] = None) -> Tuple[bool, str]:
        """创建客户端"""
        try:
            session_path = self.get_session_path(phone)
            proxy_config = self.get_proxy_config(proxy)
            
            # 更新用户状态
            user_state_manager.update_user_status(phone, UserStatus.CONNECTING)
            
            client = TelegramClient(
                session=session_path,
                api_id=self._api_id,
                api_hash=self._api_hash,
                proxy=proxy_config,
                connection_retries=2,
                retry_delay=1,
                auto_reconnect=True
            )
            
            # 连接客户端
            await client.connect()
            
            if not client.is_connected():
                error_msg = "客户端连接失败"
                user_state_manager.update_user_status(phone, UserStatus.ERROR, {"error": error_msg})
                return False, error_msg
            
            # 保存客户端和元数据
            self._clients[phone] = client
            self._client_metadata[phone] = {
                "created_at": datetime.now(),
                "last_activity": datetime.now(),
                "proxy": proxy,
                "session_path": session_path,
                "connection_count": 1
            }
            
            # 检查授权状态
            is_authorized = await client.is_user_authorized()
            
            if is_authorized:
                user_state_manager.update_user_status(phone, UserStatus.ONLINE)
                
                # 获取用户信息并更新
                try:
                    me = await client.get_me()
                    user_state_manager.update_user_info(
                        phone=phone,
                        user_id=me.id,
                        username=me.username,
                        first_name=me.first_name,
                        last_name=me.last_name,
                        is_connected=True,
                        is_authorized=True,
                        session_file=session_path
                    )
                except Exception as e:
                    logger.warning(f"获取用户信息失败: {phone}, {e}")
            else:
                user_state_manager.update_user_status(phone, UserStatus.OFFLINE)
            
            # 发送连接成功信号
            client_info = {
                "phone": phone,
                "is_authorized": is_authorized,
                "session_path": session_path,
                "proxy": proxy
            }
            self.client_connected.emit(phone, client_info)
            
            logger.info(f"客户端创建成功: {phone}, 授权状态: {is_authorized}")
            return True, ""
            
        except AuthKeyError as e:
            error_msg = f"认证密钥错误: {e}"
            user_state_manager.update_user_status(phone, UserStatus.ERROR, {"error": error_msg})
            self.client_error.emit(phone, error_msg)
            logger.error(f"客户端创建失败: {phone}, {error_msg}")
            return False, error_msg
            
        except Exception as e:
            error_msg = f"创建客户端失败: {e}"
            user_state_manager.update_user_status(phone, UserStatus.ERROR, {"error": error_msg})
            self.client_error.emit(phone, error_msg)
            logger.error(f"客户端创建异常: {phone}, {error_msg}")
            return False, error_msg
    
    async def get_client(self, phone: str, auto_create: bool = True, proxy: Dict[str, Any] = None) -> Optional[TelegramClient]:
        """获取客户端实例"""
        # 如果客户端已存在且连接正常
        if phone in self._clients:
            client = self._clients[phone]
            if client.is_connected():
                # 更新活动时间
                if phone in self._client_metadata:
                    self._client_metadata[phone]["last_activity"] = datetime.now()
                return client
            else:
                # 客户端存在但未连接，尝试重连
                try:
                    await client.connect()
                    if client.is_connected():
                        self._client_metadata[phone]["last_activity"] = datetime.now()
                        self._client_metadata[phone]["connection_count"] += 1
                        return client
                except Exception as e:
                    logger.warning(f"客户端重连失败: {phone}, {e}")
                    # 移除无效客户端
                    await self._remove_client(phone)
        
        # 如果需要自动创建客户端
        if auto_create:
            success, error = await self.create_client(phone, proxy)
            if success:
                return self._clients.get(phone)
            else:
                logger.error(f"自动创建客户端失败: {phone}, {error}")
        
        return None
    
    async def disconnect_client(self, phone: str, reason: str = "手动断开") -> bool:
        """断开客户端连接"""
        if phone not in self._clients:
            logger.warning(f"客户端不存在: {phone}")
            return False
        
        try:
            client = self._clients[phone]
            if client.is_connected():
                await client.disconnect()
            
            # 更新用户状态
            user_state_manager.update_user_status(phone, UserStatus.DISCONNECTED)
            
            # 发送断开连接信号
            self.client_disconnected.emit(phone, reason)
            
            logger.info(f"客户端已断开: {phone}, 原因: {reason}")
            return True
            
        except Exception as e:
            logger.error(f"断开客户端连接失败: {phone}, {e}")
            return False
    
    async def _remove_client(self, phone: str):
        """移除客户端（内部方法）"""
        if phone in self._clients:
            try:
                client = self._clients[phone]
                if client.is_connected():
                    await client.disconnect()
            except Exception as e:
                logger.error(f"断开客户端连接失败: {phone}, {e}")
            finally:
                del self._clients[phone]
                if phone in self._client_metadata:
                    del self._client_metadata[phone]
                
                # 更新用户状态
                user_state_manager.update_user_status(phone, UserStatus.OFFLINE)
    
    async def cleanup_client(self, phone: str) -> bool:
        """清理客户端资源"""
        success = await self.disconnect_client(phone, "资源清理")
        await self._remove_client(phone)
        return success
    
    async def cleanup_all_clients(self):
        """清理所有客户端"""
        logger.info("开始清理所有客户端")
        
        phones = list(self._clients.keys())
        for phone in phones:
            try:
                await self.cleanup_client(phone)
            except Exception as e:
                logger.error(f"清理客户端失败: {phone}, {e}")
        
        logger.info("所有客户端清理完成")
    
    def _perform_health_check(self):
        """执行健康检查（定时器回调）"""
        asyncio.create_task(self._async_health_check())
    
    async def _async_health_check(self):
        """异步健康检查"""
        try:
            current_time = datetime.now()
            inactive_threshold = timedelta(minutes=30)  # 30分钟无活动视为不活跃
            
            inactive_clients = []
            
            for phone, metadata in self._client_metadata.items():
                last_activity = metadata.get("last_activity", current_time)
                if current_time - last_activity > inactive_threshold:
                    inactive_clients.append(phone)
            
            # 清理不活跃的客户端
            for phone in inactive_clients:
                logger.info(f"清理不活跃客户端: {phone}")
                await self.cleanup_client(phone)
                
        except Exception as e:
            logger.error(f"健康检查失败: {e}")
    
    def get_client_info(self, phone: str) -> Optional[Dict[str, Any]]:
        """获取客户端信息"""
        if phone not in self._clients:
            return None
        
        client = self._clients[phone]
        metadata = self._client_metadata.get(phone, {})
        
        return {
            "phone": phone,
            "is_connected": client.is_connected(),
            "created_at": metadata.get("created_at"),
            "last_activity": metadata.get("last_activity"),
            "connection_count": metadata.get("connection_count", 0),
            "session_path": metadata.get("session_path"),
            "proxy": metadata.get("proxy")
        }
    
    def get_all_clients_info(self) -> Dict[str, Dict[str, Any]]:
        """获取所有客户端信息"""
        return {
            phone: self.get_client_info(phone) 
            for phone in self._clients.keys()
        }
    
    def get_connected_clients(self) -> List[str]:
        """获取已连接的客户端列表"""
        return [
            phone for phone, client in self._clients.items() 
            if client.is_connected()
        ]


# 全局客户端池实例
client_pool = ClientPool()
