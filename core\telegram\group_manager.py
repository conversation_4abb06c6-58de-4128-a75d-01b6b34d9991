#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Telegram群组管理模块
负责获取对话列表、邀请用户、加入群组等群组管理功能
"""

from typing import Dict, List, Optional, Tuple, Union, Any

from telethon.tl import types, functions
from telethon.errors import (
    ChatAdminRequiredError, UserPrivacyRestrictedError, 
    FloodWaitError, UserBannedInChannelError, PeerFloodError, 
    UsernameInvalidError, UsernameNotOccupiedError,
    UserAlreadyParticipantError
)
from typing import TYPE_CHECKING
if TYPE_CHECKING:
    from core.telegram.client_manager import TelegramClientManager


from utils.logger import get_logger

class GroupManager:
    """Telegram群组管理器"""
    
    def __init__(self, client_manager: 'TelegramClientManager'):
        """初始化群组管理器
        
        Args:
            client_manager: TelegramClientManager实例，用于获取客户端
        """
        self._client_manager = client_manager
        self._logger = get_logger("core.telegram.group_manager")
        self._logger.info("Telegram群组管理器初始化")
    
    async def get_dialogs(self, phone: str, dialog_type: str = None) -> Union[List[Dict[str, Any]], str]:
        """获取客户端的对话列表
        
        Args:
            phone: 手机号
            dialog_type: 对话类型，'group' 或 'channel'，None表示全部
            
        Returns:
            对话列表，每个对话以字典形式表示，失败则返回错误信息
        """
        self._logger.info(f"获取客户端对话列表: {phone}, 类型: {dialog_type or '全部'}")
        
        # 检查客户端是否存在
        client = self._client_manager.get_client(phone)
        if not client:
            error_msg = f"客户端不存在: {phone}"
            self._logger.warning(error_msg)
            return error_msg
            
        # 检查连接状态
        if not client.is_connected():
            try:
                # 尝试连接客户端
                self._logger.info(f"尝试连接客户端: {phone}")
                await client.connect()
                if not client.is_connected():
                    error_msg = f"无法连接客户端: {phone}"
                    self._logger.warning(error_msg)
                    return error_msg
            except Exception as e:
                error_msg = f"连接客户端异常: {phone}, {e}"
                self._logger.error(error_msg)
                return error_msg
        
        try:
            # 获取对话列表
            dialogs = await client.get_dialogs(ignore_migrated=True)
            
            # 直接处理对话信息
            result = []
            for dialog in dialogs:
                entity = dialog.entity
                if isinstance(entity, (types.Channel, types.Chat)):
                    # 直接从entity获取所有需要的信息
                    dialog_info = {
                        'id': entity.id,
                        'title': entity.title or dialog.title or '未命名',
                        'username': getattr(entity, 'username', ''),
                        'type': 'group' if isinstance(entity, types.Chat) or (isinstance(entity, types.Channel) and entity.megagroup) else 'channel',
                        'members_count': getattr(entity, 'participants_count', 0),
                        'is_admin': getattr(entity, 'admin_rights', False) or getattr(entity, 'creator', False)
                    }
                    
                    # 根据类型过滤
                    if dialog_type is None or dialog_info['type'] == dialog_type:
                        result.append(dialog_info)
            
            self._logger.info(f"获取对话列表成功: {phone}, 共 {len(result)} 个符合条件的对话")
            return result
            
        except Exception as e:
            error_msg = f"获取对话列表异常: {phone}, {e}"
            self._logger.error(error_msg)
            return error_msg
    
    async def invite_user_to_group(self, phone: str, username: str, group_name: str, message: Optional[str] = None) -> Tuple[bool, Union[str, Dict]]:
        """邀请用户到群组
        
        Args:
            phone: 账户手机号
            username: 被邀请用户的用户名或ID
            group_name: 群组用户名或ID
            message: 邀请附言
            
        Returns:
            (成功标志, 结果消息或错误信息)
        """
        self._logger.info(f"账户 {phone} 尝试邀请 {username} 到群组 {group_name}")
        
        # 1. 检查客户端是否存在并连接
        client = self._client_manager.get_client(phone)
        if not client:
            return False, f"客户端 {phone} 不存在"
        
        if not client.is_connected():
            try:
                await client.connect()
                if not client.is_connected():
                    return False, f"客户端 {phone} 无法连接"
            except Exception as e:
                return False, f"连接客户端时出错: {str(e)}"
        
        try:
            # self._logger.info(f"已邀请 {username} 进入频道 {group_name}")
            # return True, "邀请成功"
            # 获取聊天实体
            chat_entity = await client.get_entity(group_name)
            # 获取用户实体
            user_entity = await client.get_entity(username)
                    
            # 4. 邀请用户到群组
             # 判断类型，分别邀请
            if isinstance(chat_entity, types.Channel):
                self._logger.debug(f"使用InviteToChannelRequest拉人{chat_entity},{user_entity}")
                await client(functions.channels.InviteToChannelRequest(
                    channel=chat_entity,
                    users=[user_entity]
                ))
                self._logger.info(f"已邀请 {username} 进入频道 {group_name}")
                return True, "邀请成功"
            elif isinstance(chat_entity, types.Chat):
                self._logger.debug(f"使用AddChatUserRequest拉人{chat_entity.id},,,{user_entity.id}")
                await client(functions.messages.AddChatUserRequest(
                    chat_id=chat_entity,
                    user_id=user_entity,
                    fwd_limit=10
                ))
                self._logger.info(f"已邀请 {username} 进入群组 {group_name}")
                return True, "邀请成功"
            else:
                self._logger.info(f"无效聊天类型: {group_name}")
                return False, "无效聊天类型"
        except ValueError:
            self._logger.error(f"无效用户名: {username}")
            return False, "无效用户名"
        except ChatAdminRequiredError:
            self._logger.error(f"没有管理员权限: {username}")
            return False, "没有管理员权限"
        except UserPrivacyRestrictedError:
            self._logger.error(f"用户隐私限制: {username}")
            return False, "用户隐私限制"
        except FloodWaitError as e:
            self._logger.error(f"请求过快，请稍后再试: {e}")
            return False, f"请求过快，需等待 {e.seconds} 秒"
        except UsernameInvalidError:
            self._logger.error(f"无效用户名: {username}")
            return False, "无效用户名"
        except UsernameNotOccupiedError:
            self._logger.error(f"用户名未被占用: {username}")
            return False, "用户名未被占用"
        except PeerFloodError:
            self._logger.error(f"请求过多，请稍后再试: {username}")
            return False, "请求过多"
        except UserBannedInChannelError:
            self._logger.error(f"用户已被封禁: {username}")
            return False, "用户已被封禁"
        except UserAlreadyParticipantError:
            self._logger.error(f"用户已是频道成员: {username}")
            return False, "用户已是频道成员"
        except Exception as e:
            self._logger.error(f"邀请用户时发生异常: {str(e)}")
            return False, f"邀请异常: {str(e)}"



    async def send_invite_link(self, phone: str, username: str, invite_link: str, message: Optional[str] = None) -> Tuple[bool, Union[str, Dict]]:
        """通过消息发送邀请链接
        
        Args:
            phone: 账户手机号
            username: 被邀请用户的用户名或ID
            invite_link: 群组邀请链接
            message: 邀请消息文本
            
        Returns:
            (成功标志, 结果消息或错误信息)
        """
        self._logger.info(f"账户 {phone} 尝试发送邀请链接给 {username}")
        
        # 1. 检查客户端是否存在并连接
        client = self._client_manager.get_client(phone)
        if not client:
            return False, f"客户端 {phone} 不存在"
        
        if not client.is_connected():
            try:
                await client.connect()
                if not client.is_connected():
                    return False, f"客户端 {phone} 无法连接"
            except Exception as e:
                return False, f"连接客户端时出错: {str(e)}"
        
        try:
            # 2. 获取用户实体
            try:
                if username.startswith('+'):  # 如果是手机号
                    user_entity = await client.get_entity(username)
                else:  # 如果是用户名
                    user_entity = await client.get_entity(username.lstrip('@'))
            except Exception as e:
                return False, f"找不到用户 {username}: {str(e)}"
            
            # 3. 构建消息文本
            if not message:
                message = f"你好，我想邀请你加入我们的群组: {invite_link}"
            else:
                # 确保消息中包含邀请链接
                if invite_link not in message:
                    message = f"{message}\n\n{invite_link}"
            
            # 4. 发送消息
            try:
                sent_message = await client.send_message(
                    entity=user_entity,
                    message=message
                )
                self._logger.info(f"成功发送邀请链接给 {username}")
                return True, "邀请链接已发送"
            except Exception as e:
                error_msg = str(e)
                if "FLOOD_WAIT" in error_msg:
                    # 提取等待时间
                    import re
                    wait_time = re.search(r'(\d+)', error_msg)
                    if wait_time:
                        return False, f"FloodWait: {wait_time.group(1)}"
                    return False, f"FloodWait: unknown"
                return False, f"发送邀请链接失败: {error_msg}"
                
        except Exception as e:
            self._logger.error(f"发送邀请链接时发生异常: {str(e)}")
            return False, f"发送邀请链接异常: {str(e)}"

    async def join_group_if_needed(self, phone: str, group_name: str) -> Tuple[bool, str]:
        """如果账号未加入目标群，则自动加入
        
        Args:
            phone: 操作账号
            group_name: 群组用户名或ID
            
        Returns:
            (是否已在群/加入成功, 错误信息)
        """
        client = self._client_manager.get_client(phone)
        if not client:
            return False, f"客户端 {phone} 不存在"
            
        if not client.is_connected():
            try:
                await client.connect()
                if not client.is_connected():
                    return False, f"客户端 {phone} 无法连接"
            except Exception as e:
                return False, f"连接客户端时出错: {str(e)}"
                
        try:
            # 检查是否已在群中
            dialogs = await client.get_dialogs()
            in_group = False
            for dialog in dialogs:
                entity = dialog.entity
                if hasattr(entity, 'username') and entity.username == group_name:
                    in_group = True
                    break
                if hasattr(entity, 'id') and str(entity.id) == str(group_name):
                    in_group = True
                    break
                    
            if in_group:
                return True, "已在群中"
                
            # 未在群中，尝试加入
            group_entity = await client.get_entity(group_name)
            await client(functions.channels.JoinChannelRequest(group_entity))
            return True, "已加入群"
            
        except Exception as e:
            return False, f"加群失败: {str(e)}" 
