#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Telegram消息管理模块
负责发送文本消息、媒体文件、转发消息等功能
"""

import os
from typing import Dict, List, Optional, Tuple, Union, Any, Callable

from telethon import TelegramClient
from telethon.tl import types, functions
from telethon.errors import FloodWaitError, UserDeactivatedBanError

from utils.logger import get_logger

class MessageManager:
    """Telegram消息管理器"""
    
    def __init__(self, client_manager):
        """初始化消息管理器
        
        Args:
            client_manager: TelegramClientManager实例，用于获取客户端
        """
        self._client_manager = client_manager
        self._logger = get_logger("core.telegram.message_manager")
        self._logger.info("Telegram消息管理器初始化")
    
    async def send_image_text_message(
            self,
            phone: str,
            chat_id: Union[int, str],
            image_path: str,
            text: str,
            parse_mode: Optional[str] = 'html'
    ) -> Tuple[bool, Union[types.Message, str]]:
        """发送图文消息（图片+文本）

        Args:
            phone: 账户手机号
            chat_id: 目标对话ID
            image_path: list,图片路径
            text: 消息文本
            parse_mode: 文本解析模式

        Returns:
            (成功标志, Telethon Message 对象或错误消息)
        """
        self._logger.info(f"账户 {phone} 准备向 {chat_id} 发送图文消息")
        client = self._client_manager.get_client(phone)
        if not client or not client.is_connected():
            error_msg = f"客户端 {phone} 未连接或不存在"
            self._logger.warning(error_msg)
            return False, error_msg
        
        try:
            # 尝试将 chat_id 转换为 entity
            target_entity = await client.get_entity(chat_id)
            # 发送图片消息，带上文本作为caption
            sent_message = await client.send_file(
                target_entity,
                file = image_path,
                caption = text,
                parse_mode = parse_mode
            )
            self._logger.info(f"账户 {phone} 成功向 {chat_id} 发送图文消息 ID: {sent_message.id}")
            return True, sent_message
        except FloodWaitError as e:
            error_msg = f"发送图文消息被限制 (FloodWaitError): {phone} -> {chat_id}, 等待 {e.seconds} 秒"
            self._logger.error(error_msg)
            return False, f"FloodWait: {e.seconds}"
        except UserDeactivatedBanError as e:
            error_msg = f"账户 {phone} 已被封禁 (UserDeactivatedBanError)，无法发送图文消息至 {chat_id}: {e}"
            self._logger.error(error_msg)
            return False, "account_banned"
        except Exception as e:
            error_msg = f"账户 {phone} 向 {chat_id} 发送图文消息失败: {e}"
            self._logger.error(error_msg, exc_info = True)
            return False, str(e)
    
    async def send_text_message(self, phone: str, chat_id: Union[int, str], text: str, parse_mode: Optional[str] = 'html') -> Tuple[bool, Union[types.Message, str]]:
        """发送文本消息
        
        Args:
            phone: 账户手机号
            chat_id: 目标对话ID (可以是int或username)
            text: 消息文本
            parse_mode: 'md' for Markdown, 'html' for HTML
            
        Returns:
            (成功标志, Telethon Message 对象或错误消息)
        """
        self._logger.info(f"账户 {phone} 准备向 {chat_id} 发送文本消息{text}")
        client = self._client_manager.get_client(phone)
        if not client or not client.is_connected():
            error_msg = f"客户端 {phone} 未连接或不存在"
            self._logger.warning(error_msg)
            return False, error_msg

        try:
            # self._logger.info(f"账户 {phone} 成功向 {chat_id} 发送{text}消息 ，解析模式: {parse_mode}")
            # return True, "sent_message"
            # 尝试将 chat_id 转换为 entity
            target_entity = await client.get_entity(chat_id)
            sent_message = await client.send_message(target_entity, text, parse_mode=parse_mode)
            self._logger.info(f"账户 {phone} 成功向 {chat_id} 发送消息 ID: {sent_message.id}，解析模式: {parse_mode}")
            return True, sent_message
        except FloodWaitError as e:
            error_msg = f"发送消息被限制 (FloodWaitError): {phone} -> {chat_id}, 等待 {e.seconds} 秒"
            self._logger.error(error_msg)
            # 可以考虑将 FloodWaitError 特殊处理，例如返回等待时间
            return False, f"FloodWait: {e.seconds}"
        except UserDeactivatedBanError as e:
            error_msg = f"账户 {phone} 已被封禁 (UserDeactivatedBanError)，无法发送消息至 {chat_id}: {e}"
            self._logger.error(error_msg)
            # 特殊处理封号情况
            return False, "account_banned"
        except Exception as e:
            error_msg = f"账户 {phone}向 {chat_id} 发送文本消息失败: {e}"
            self._logger.error(error_msg, exc_info=True)
            return False, str(e)


    async def forward_messages(
        self, 
        phone: str, 
        chat_id: Union[int, str], 
        from_chat_id: Union[int, str], 
        message_ids: List[int], 
        drop_author: bool = False
    ) -> Tuple[bool, Union[List[types.Message], str]]:
        """转发消息
        
        Args:
            phone: 账户手机号
            chat_id: 目标对话ID
            from_chat_id: 原始消息所在对话ID
            message_ids: 要转发的消息ID列表
            drop_author: 是否隐藏原始发送者 (silent forwarding)
            
        Returns:
            (成功标志, 成功时是 Telethon Message 对象列表, 失败时是错误消息)
        """
        self._logger.info(f"账户 {phone} 准备从 {from_chat_id} 向 {chat_id} 转发消息 IDs: {message_ids}")
        client = self._client_manager.get_client(phone)
        if not client or not client.is_connected():
            error_msg = f"客户端 {phone} 未连接或不存在"
            self._logger.warning(error_msg)
            return False, error_msg

        if not message_ids:
            return False, "没有提供消息ID进行转发"

        try:
            target_entity = await client.get_entity(chat_id)
            source_entity = await client.get_entity(from_chat_id)
            
            sent_messages = await client.forward_messages(
                entity=target_entity,
                messages=message_ids,
                from_peer=source_entity,
                silent=drop_author # 'silent' in Telethon often means drop_author/notifications
            )
            # client.forward_messages 可以返回单个消息或消息列表
            if not isinstance(sent_messages, list):
                sent_messages = [sent_messages]

            sent_message_ids = [m.id for m in sent_messages if m]
            self._logger.info(f"账户 {phone} 成功从 {from_chat_id} 向 {chat_id} 转发消息, 新消息 IDs: {sent_message_ids}")
            return True, sent_messages
        except FloodWaitError as e:
            error_msg = f"转发消息被限制 (FloodWaitError): {phone} -> {chat_id}, 等待 {e.seconds} 秒"
            self._logger.error(error_msg)
            return False, f"FloodWait: {e.seconds}"
        except UserDeactivatedBanError as e:
            error_msg = f"账户 {phone} 已被封禁 (UserDeactivatedBanError)，无法转发消息至 {chat_id}: {e}"
            self._logger.error(error_msg)
            return False, "account_banned"
        except Exception as e:
            # 检查错误是否表明某些消息无法转发
            # 例如，"MessageIDsInvalidError" 或每条消息的特定错误
            # 为简单起见，将任何错误视为批处理失败。
            error_msg = f"账户 {phone} 从 {from_chat_id} 向 {chat_id} 转发消息 IDs {message_ids} 失败: {e}"
            self._logger.error(error_msg, exc_info=True)
            return False, str(e)

    async def batch_send_messages(
        self,
        phone: str,
        chat_ids: List[Union[int, str]],
        message_content: Dict[str, Any],
        delay: int = 5,
        max_per_session: int = 20
    ) -> Dict[str, Any]:
        """批量发送消息到多个聊天
        
        Args:
            phone: 账户手机号
            chat_ids: 目标聊天ID列表
            message_content: 消息内容字典，包含类型(text/file)和内容
            delay: 发送间隔延迟(秒)
            max_per_session: 单次会话最大发送数量
            
        Returns:
            发送结果统计
        """
        import asyncio
        
        self._logger.info(f"账户 {phone} 开始批量发送消息到 {len(chat_ids)} 个聊天")
        
        client = self._client_manager.get_client(phone)
        if not client or not client.is_connected():
            return {
                'success': 0,
                'failed': len(chat_ids),
                'total': len(chat_ids),
                'error': f"客户端 {phone} 未连接或不存在",
                'details': []
            }
        
        results = {
            'success': 0,
            'failed': 0,
            'total': len(chat_ids),
            'details': []
        }
        
        # 检查消息类型
        msg_type = message_content.get('type', 'text')
        
        for i, chat_id in enumerate(chat_ids):
            # 发送进度通知
            self._client_manager.notify.emit(
                "发送进度", 
                f"发送至 {chat_id} ({i+1}/{len(chat_ids)})", 
                "info"
            )
            
            try:
                success = False
                error_msg = ""
                
                # 根据消息类型发送
                if msg_type == 'text':
                    text = message_content.get('text', '')
                    parse_mode = message_content.get('parse_mode')
                    success, result = await self.send_text_message(phone, chat_id, text, parse_mode)
                elif msg_type == 'file':
                    file_path = message_content.get('file_path', '')
                    caption = message_content.get('caption', '')
                    success, result = await self.send_file_message(
                        phone, 
                        chat_id, 
                        file_path, 
                        caption=caption,
                        parse_mode=message_content.get('parse_mode')
                    )
                else:
                    error_msg = f"不支持的消息类型: {msg_type}"
                    success = False
                
                # 记录结果
                if success:
                    results['success'] += 1
                    detail = {'chat_id': chat_id, 'success': True}
                else:
                    results['failed'] += 1
                    error_msg = result if isinstance(result, str) else "发送失败"
                    detail = {'chat_id': chat_id, 'success': False, 'error': error_msg}
                
                results['details'].append(detail)
                
                # 处理FloodWait错误
                if isinstance(result, str) and result.startswith("FloodWait:"):
                    try:
                        wait_seconds = int(result.split(":")[1].strip())
                        self._logger.warning(f"遇到FloodWait，等待 {wait_seconds} 秒")
                        self._client_manager.notify.emit(
                            "发送延迟", 
                            f"遇到发送限制，等待 {wait_seconds} 秒", 
                            "warning"
                        )
                        await asyncio.sleep(wait_seconds + 1)  # 额外等待1秒
                    except Exception as e:
                        self._logger.error(f"处理FloodWait等待时出错: {e}")
                        await asyncio.sleep(30)  # 默认等待30秒
                
                # 每发送一定数量消息后暂停一下
                if (i + 1) % max_per_session == 0 and i < len(chat_ids) - 1:
                    pause_time = delay * 3  # 批次间隔是正常延迟的3倍
                    self._logger.info(f"已发送 {max_per_session} 条消息，暂停 {pause_time} 秒")
                    self._client_manager.notify.emit(
                        "发送暂停", 
                        f"批次暂停 {pause_time} 秒", 
                        "info"
                    )
                    await asyncio.sleep(pause_time)
                elif i < len(chat_ids) - 1:
                    # 消息间的正常延迟
                    await asyncio.sleep(delay)
                
            except Exception as e:
                self._logger.error(f"向 {chat_id} 发送消息时出错: {e}")
                results['failed'] += 1
                results['details'].append({
                    'chat_id': chat_id,
                    'success': False,
                    'error': str(e)
                })
                await asyncio.sleep(delay)  # 出错后仍然等待
        
        self._logger.info(f"批量发送完成: 成功 {results['success']}/{results['total']}，失败 {results['failed']}")
        self._client_manager.notify.emit(
            "发送完成", 
            f"批量发送完成: 成功 {results['success']}/{results['total']}，失败 {results['failed']}", 
            "info"
        )
        
        return results 
