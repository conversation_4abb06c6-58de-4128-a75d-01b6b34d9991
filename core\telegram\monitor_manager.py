#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Telegram监控管理模块
负责注册监控事件处理器、管理事件回调等功能
"""

from typing import Dict, List, Tuple, Callable, Any, Optional

from telethon import events

from utils.logger import get_logger

class MonitorManager:
    """Telegram监控管理器"""
    
    def __init__(self, client_manager):
        """初始化监控管理器
        
        Args:
            client_manager: TelegramClientManager实例，用于获取客户端
        """
        self._client_manager = client_manager
        self._logger = get_logger("core.telegram.monitor_manager")
        self._logger.info("Telegram监控管理器初始化")
        
        # 存储事件处理器 {task_id: {phone: [handler_tuples]}}
        self._event_handlers: Dict[str, Dict[str, List[Tuple[Callable, events.NewMessage]]]] = {}
    
    def register_monitoring_handlers(self, task_id: str, account_phone: str, chat_ids: List[int], callback: Callable) -> Tuple[bool, str]:
        """注册监控事件处理器
        
        Args:
            task_id: 任务ID
            account_phone: 账户手机号
            chat_ids: 要监控的群组ID列表
            callback: 消息回调函数
            
        Returns:
            (成功标志, 消息)
        """
        try:
            client = self._client_manager.get_client(account_phone)
            if not client:
                return False, f"找不到账户 {account_phone} 的客户端"
                
            if not client.is_connected():
                return False, f"账户 {account_phone} 的客户端未连接"
                
            # 创建事件处理器
            handler_tuple = (callback, events.NewMessage(chats=chat_ids))
            client.add_event_handler(*handler_tuple)
            
            # 记录处理器信息
            if task_id not in self._event_handlers:
                self._event_handlers[task_id] = {}
            if account_phone not in self._event_handlers[task_id]:
                self._event_handlers[task_id][account_phone] = []
                
            self._event_handlers[task_id][account_phone].append(handler_tuple)
            
            self._logger.info(f"任务 {task_id} 为账户 {account_phone} 注册了 {len(chat_ids)} 个群组的监控")
            return True, "事件处理器注册成功"
            
        except Exception as e:
            error_msg = f"注册事件处理器失败: {str(e)}"
            self._logger.error(error_msg, exc_info=True)
            return False, error_msg
            
    def unregister_monitoring_handlers(self, task_id: str) -> Tuple[bool, str]:
        """注销任务的所有事件处理器
        
        Args:
            task_id: 任务ID
            
        Returns:
            (成功标志, 消息)
        """
        try:
            if task_id not in self._event_handlers:
                return True, "任务没有注册的事件处理器"
                
            fail_count = 0
            for account_phone, handlers in self._event_handlers[task_id].items():
                client = self._client_manager.get_client(account_phone)
                if client:
                    for handler_tuple in handlers:
                        try:
                            callback, event_builder = handler_tuple
                            client.remove_event_handler(callback, event_builder)
                        except Exception as e:
                            self._logger.error(f"移除事件处理器失败: {account_phone}, {str(e)}")
                            fail_count += 1
                else:
                    self._logger.warning(f"注销事件处理器时找不到客户端: {account_phone}")
                    fail_count += len(handlers)
                            
            # 清理记录
            del self._event_handlers[task_id]
            
            if fail_count > 0:
                return False, f"部分事件处理器移除失败: {fail_count} 个"
            return True, "所有事件处理器已移除"
            
        except Exception as e:
            error_msg = f"注销事件处理器失败: {str(e)}"
            self._logger.error(error_msg, exc_info=True)
            return False, error_msg
    
    def register_custom_event_handler(self, task_id: str, account_phone: str, event_type, filter_func: Optional[Callable] = None, callback: Callable = None) -> Tuple[bool, str]:
        """注册自定义事件处理器
        
        Args:
            task_id: 任务ID
            account_phone: 账户手机号
            event_type: 事件类型，如 events.NewMessage, events.UserUpdate 等
            filter_func: 事件过滤函数
            callback: 事件回调函数
            
        Returns:
            (成功标志, 消息)
        """
        try:
            client = self._client_manager.get_client(account_phone)
            if not client:
                return False, f"找不到账户 {account_phone} 的客户端"
                
            if not client.is_connected():
                return False, f"账户 {account_phone} 的客户端未连接"
            
            # 创建事件构建器
            event_builder = event_type()
            if filter_func:
                event_builder = event_builder.filter(filter_func)
                
            # 注册事件处理器
            handler_tuple = (callback, event_builder)
            client.add_event_handler(*handler_tuple)
            
            # 记录处理器信息
            if task_id not in self._event_handlers:
                self._event_handlers[task_id] = {}
            if account_phone not in self._event_handlers[task_id]:
                self._event_handlers[task_id][account_phone] = []
                
            self._event_handlers[task_id][account_phone].append(handler_tuple)
            
            self._logger.info(f"任务 {task_id} 为账户 {account_phone} 注册了自定义事件处理器: {event_type.__name__}")
            return True, "自定义事件处理器注册成功"
            
        except Exception as e:
            error_msg = f"注册自定义事件处理器失败: {str(e)}"
            self._logger.error(error_msg, exc_info=True)
            return False, error_msg
    
    def list_active_monitors(self) -> Dict[str, Any]:
        """获取所有活跃的监控任务列表
        
        Returns:
            监控任务信息字典
        """
        result = {}
        
        for task_id, accounts in self._event_handlers.items():
            task_info = {
                'task_id': task_id,
                'accounts': []
            }
            
            for account_phone, handlers in accounts.items():
                account_info = {
                    'phone': account_phone,
                    'handlers': len(handlers),
                    'client_connected': self._client_manager.is_client_connected(account_phone)
                }
                task_info['accounts'].append(account_info)
                
            result[task_id] = task_info
            
        return result
        
    def get_task_monitors(self, task_id: str) -> Dict[str, Any]:
        """获取指定任务的监控信息
        
        Args:
            task_id: 任务ID
            
        Returns:
            任务监控信息字典
        """
        if task_id not in self._event_handlers:
            return {
                'task_id': task_id,
                'exists': False,
                'accounts': []
            }
            
        task_info = {
            'task_id': task_id,
            'exists': True,
            'accounts': []
        }
        
        for account_phone, handlers in self._event_handlers[task_id].items():
            handler_info = []
            for callback, event_builder in handlers:
                handler_info.append({
                    'type': event_builder.__class__.__name__,
                    'callback': callback.__name__ if hasattr(callback, '__name__') else str(callback)
                })
                
            account_info = {
                'phone': account_phone,
                'handlers': handler_info,
                'client_connected': self._client_manager.is_client_connected(account_phone)
            }
            task_info['accounts'].append(account_info)
            
        return task_info
    
    def clear_all_monitors(self) -> Tuple[bool, str]:
        """清除所有监控任务
        
        Returns:
            (成功标志, 消息)
        """
        try:
            task_ids = list(self._event_handlers.keys())
            fail_count = 0
            
            for task_id in task_ids:
                success, msg = self.unregister_monitoring_handlers(task_id)
                if not success:
                    fail_count += 1
                    self._logger.warning(f"清除任务 {task_id} 的监控失败: {msg}")
            
            if fail_count > 0:
                return False, f"部分监控任务清除失败: {fail_count}/{len(task_ids)}"
            return True, f"所有监控任务已清除: {len(task_ids)} 个任务"
            
        except Exception as e:
            error_msg = f"清除所有监控任务失败: {str(e)}"
            self._logger.error(error_msg, exc_info=True)
            return False, error_msg 