#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
新用户管理器 - 基于任务执行器的重构版本

整合了：
1. 用户状态管理
2. 客户端池管理
3. 任务调度
4. 操作历史
"""

from typing import Dict, List, Optional, Any, Tuple
from PySide6.QtCore import QObject, Signal

from core.task_manager.task_service import task_service
from .user_state_manager import user_state_manager, UserStatus, UserOperation, UserInfo
from .client_pool import client_pool
from utils.logger import get_logger

logger = get_logger(__name__)


class NewUserManager(QObject):
    """新用户管理器 - 基于任务执行器"""
    
    # 信号定义
    user_login_started = Signal(str)  # phone
    user_login_completed = Signal(str, bool, str)  # phone, success, message
    user_status_changed = Signal(str, str, str)  # phone, old_status, new_status
    user_operation_completed = Signal(str, str, bool, dict)  # phone, operation, success, details
    batch_operation_progress = Signal(int, int, str)  # current, total, message
    
    def __init__(self):
        super().__init__()
        self._setup_connections()
        logger.info("新用户管理器已初始化")
    
    def _setup_connections(self):
        """设置信号连接"""
        # 连接用户状态管理器信号
        user_state_manager.user_status_changed.connect(self.user_status_changed.emit)
        user_state_manager.user_operation_completed.connect(self.user_operation_completed.emit)
        
        # 连接任务服务信号
        task_service.task_completed.connect(self._on_task_completed)
        task_service.task_failed.connect(self._on_task_failed)
    
    # ==================== 用户登录相关 ====================
    
    async def start_login(self, phone: str, proxy: Dict[str, Any] = None) -> str:
        """开始用户登录流程
        
        Args:
            phone: 手机号
            proxy: 代理配置
            
        Returns:
            任务ID
        """
        logger.info(f"开始用户登录: {phone}")
        
        # 发送登录开始信号
        self.user_login_started.emit(phone)
        
        # 提交登录任务
        task_id = task_service.submit_account_login_task(
            phone=phone,
            proxy=proxy
        )
        
        return task_id
    
    async def submit_verification_code(self, phone: str, code: str, password: str = None) -> str:
        """提交验证码
        
        Args:
            phone: 手机号
            code: 验证码
            password: 两步验证密码（可选）
            
        Returns:
            任务ID
        """
        logger.info(f"提交验证码: {phone}")
        
        task_id = task_service.submit_account_code_verification_task(
            phone=phone,
            code=code,
            password=password
        )
        
        return task_id
    
    async def submit_password(self, phone: str, password: str) -> str:
        """提交两步验证密码
        
        Args:
            phone: 手机号
            password: 两步验证密码
            
        Returns:
            任务ID
        """
        logger.info(f"提交两步验证密码: {phone}")
        
        task_id = task_service.submit_account_password_verification_task(
            phone=phone,
            password=password
        )
        
        return task_id
    
    # ==================== 用户信息管理 ====================
    
    def register_user(self, phone: str, **kwargs) -> UserInfo:
        """注册用户"""
        return user_state_manager.register_user(phone, **kwargs)
    
    def get_user(self, phone: str) -> Optional[UserInfo]:
        """获取用户信息"""
        return user_state_manager.get_user(phone)
    
    def get_all_users(self) -> Dict[str, UserInfo]:
        """获取所有用户"""
        return user_state_manager.get_all_users()
    
    def get_users_by_status(self, status: UserStatus) -> List[UserInfo]:
        """根据状态获取用户"""
        return user_state_manager.get_users_by_status(status)
    
    def get_online_users(self) -> List[UserInfo]:
        """获取在线用户"""
        return self.get_users_by_status(UserStatus.ONLINE)
    
    def get_offline_users(self) -> List[UserInfo]:
        """获取离线用户"""
        return self.get_users_by_status(UserStatus.OFFLINE)
    
    # ==================== 用户操作管理 ====================
    
    async def refresh_user_info(self, phone: str) -> str:
        """刷新用户信息
        
        Args:
            phone: 手机号
            
        Returns:
            任务ID
        """
        logger.info(f"刷新用户信息: {phone}")
        
        task_id = task_service.submit_account_info_refresh_task(phone=phone)
        return task_id
    
    async def update_user_profile(self, phone: str, **profile_data) -> str:
        """更新用户资料
        
        Args:
            phone: 手机号
            **profile_data: 资料数据
            
        Returns:
            任务ID
        """
        logger.info(f"更新用户资料: {phone}")
        
        # 这里需要创建一个用户资料更新任务
        # 暂时使用信息刷新任务作为占位符
        task_id = task_service.submit_account_info_refresh_task(phone=phone)
        return task_id
    
    async def disconnect_user(self, phone: str, reason: str = "手动断开") -> bool:
        """断开用户连接"""
        logger.info(f"断开用户连接: {phone}, 原因: {reason}")
        
        success = await client_pool.disconnect_client(phone, reason)
        
        if success:
            user_state_manager.record_operation(
                phone=phone,
                operation=UserOperation.DISCONNECT,
                success=True,
                details={"reason": reason}
            )
        
        return success
    
    # ==================== 批量操作 ====================
    
    async def batch_login(self, accounts: List[Dict[str, Any]], max_concurrent: int = 3) -> str:
        """批量登录用户
        
        Args:
            accounts: 账户列表，每项包含 {phone, proxy, ...}
            max_concurrent: 最大并发数
            
        Returns:
            任务ID
        """
        logger.info(f"开始批量登录 {len(accounts)} 个账户")
        
        task_id = task_service.submit_batch_account_login_task(
            accounts=accounts,
            max_concurrent=max_concurrent
        )
        
        return task_id
    
    async def batch_refresh_info(self, phones: List[str], max_concurrent: int = 5) -> List[str]:
        """批量刷新用户信息
        
        Args:
            phones: 手机号列表
            max_concurrent: 最大并发数
            
        Returns:
            任务ID列表
        """
        logger.info(f"开始批量刷新 {len(phones)} 个用户信息")
        
        task_ids = []
        
        # 使用信号量控制并发
        import asyncio
        semaphore = asyncio.Semaphore(max_concurrent)
        
        async def refresh_single(phone: str):
            async with semaphore:
                task_id = await self.refresh_user_info(phone)
                task_ids.append(task_id)
                
                # 发送进度信号
                current = len(task_ids)
                self.batch_operation_progress.emit(current, len(phones), f"已提交 {current}/{len(phones)}")
        
        # 并发提交任务
        tasks = [refresh_single(phone) for phone in phones]
        await asyncio.gather(*tasks)
        
        return task_ids
    
    # ==================== 统计和监控 ====================
    
    def get_user_statistics(self, phone: str = None) -> Dict[str, Any]:
        """获取用户统计信息"""
        return user_state_manager.get_user_statistics(phone)
    
    def get_operation_history(self, phone: str = None, operation: UserOperation = None, 
                            limit: int = 100) -> List:
        """获取操作历史"""
        return user_state_manager.get_operation_history(phone, operation, limit)
    
    def get_active_tasks(self, phone: str) -> set:
        """获取用户的活跃任务"""
        return user_state_manager.get_active_tasks(phone)
    
    def get_client_info(self, phone: str) -> Optional[Dict[str, Any]]:
        """获取客户端信息"""
        return client_pool.get_client_info(phone)
    
    def get_all_clients_info(self) -> Dict[str, Dict[str, Any]]:
        """获取所有客户端信息"""
        return client_pool.get_all_clients_info()
    
    # ==================== 任务事件处理 ====================
    
    def _on_task_completed(self, task_id: str, result: Any):
        """任务完成事件处理"""
        # 获取任务信息
        task_info = task_service.get_task_status(task_id)
        if not task_info:
            return
        
        task_name = task_info.get("name", "")
        category = task_info.get("category", "")
        
        # 处理账户相关任务
        if category == "account":
            self._handle_account_task_completed(task_id, task_info, result)
    
    def _on_task_failed(self, task_id: str, error: str):
        """任务失败事件处理"""
        # 获取任务信息
        task_info = task_service.get_task_status(task_id)
        if not task_info:
            return
        
        task_name = task_info.get("name", "")
        category = task_info.get("category", "")
        
        # 处理账户相关任务失败
        if category == "account":
            self._handle_account_task_failed(task_id, task_info, error)
    
    def _handle_account_task_completed(self, task_id: str, task_info: Dict, result: Any):
        """处理账户任务完成"""
        task_name = task_info.get("name", "")
        
        if "登录" in task_name and isinstance(result, dict):
            phone = result.get("phone")
            if phone:
                success = result.get("success", False)
                message = result.get("message", "")
                self.user_login_completed.emit(phone, success, message)
    
    def _handle_account_task_failed(self, task_id: str, task_info: Dict, error: str):
        """处理账户任务失败"""
        task_name = task_info.get("name", "")
        
        if "登录" in task_name:
            # 从任务名称中提取手机号（简单实现）
            parts = task_name.split("_")
            if len(parts) > 1:
                phone = parts[1]
                self.user_login_completed.emit(phone, False, error)
    
    # ==================== 清理和维护 ====================
    
    async def cleanup_inactive_users(self, days: int = 7):
        """清理不活跃用户"""
        logger.info(f"开始清理 {days} 天内不活跃的用户")
        
        # 清理操作记录
        user_state_manager.cleanup_old_records(days)
        
        # 清理客户端
        await client_pool.cleanup_all_clients()
        
        logger.info("用户清理完成")


# 全局新用户管理器实例
new_user_manager = NewUserManager()
