#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Telegram服务门面类
作为统一管理各个模块的入口点
"""

from typing import Dict, List, Optional, Tuple, Any, Union, Callable

from utils.logger import get_logger
from .client_manager import TelegramClientManager
from .user_manager import UserManager
from .group_manager import GroupManager
from .message_manager import MessageManager
from .monitor_manager import MonitorManager

class TelegramService:
    """Telegram服务门面类，统一管理各个功能模块"""
    
    def __init__(self, api_id: int, api_hash: str, session_dir: str = None):
        """初始化Telegram服务
        
        Args:
            api_id: Telegram API ID
            api_hash: Telegram API Hash
            session_dir: Session文件存储目录
        """
        # 初始化客户端管理器（核心模块）
        self._client_manager = TelegramClientManager(api_id, api_hash, session_dir)
        
        # 初始化各功能模块
        self._user_manager = UserManager(self._client_manager)
        self._group_manager = GroupManager(self._client_manager)
        self._message_manager = MessageManager(self._client_manager)
        self._monitor_manager = MonitorManager(self._client_manager)
        
        self._logger = get_logger("core.telegram.telegram_service")
        self._logger.info("Telegram服务初始化完成")
    
    # =============================================
    # 客户端管理功能（代理到ClientManager）
    # =============================================
    
    async def start_login(self, phone: str, proxy: Dict = None) -> Tuple[bool, str]:
        """开始登录过程
        
        Args:
            phone: 手机号
            proxy: 代理设置
            
        Returns:
            (成功标志, 结果消息)
        """
        return await self._client_manager.start_login(phone, proxy)
    
    async def submit_code(self, phone: str, code: str, password: str = None):
        """提交验证码
        
        Args:
            phone: 手机号
            code: 验证码
            password: 两步验证密码
            
        Returns:
            (成功标志, 结果消息)
        """
        return await self._client_manager.submit_code(phone, code, password)
    
    async def import_session(self, session_path: str, proxy: Dict = None) -> Tuple[bool, Union[Dict[str, Any], str]]:
        """导入session文件
        
        Args:
            session_path: session文件路径
            proxy: 代理设置
            
        Returns:
            (成功标志, 结果消息)
        """
        return await self._client_manager.import_session(session_path, proxy)
    
    async def batch_import_sessions(self, session_connection_details_list: List[Dict[str, Any]], max_concurrent: int = 5):
        """批量导入session文件
        
        Args:
            session_connection_details_list: session文件信息列表
            max_concurrent: 最大并发数
            
        Returns:
            导入结果统计
        """
        return await self._client_manager.batch_import_sessions(session_connection_details_list, max_concurrent)
    
    async def connect_client(self, phone: str, proxy: Dict = None) -> Tuple[bool, str]:
        """连接客户端
        
        Args:
            phone: 手机号
            proxy: 代理设置
            
        Returns:
            (成功标志, 结果消息)
        """
        return await self._client_manager.connect_client(phone, proxy)
    
    async def disconnect_client(self, phone: str) -> bool:
        """断开客户端连接
        
        Args:
            phone: 手机号
            
        Returns:
            操作是否成功
        """
        return await self._client_manager.disconnect_client(phone)
    
    async def disconnect_all_clients(self):
        """断开所有客户端连接"""
        await self._client_manager.disconnect_all_clients()
    
    async def logout_client(self, phone: str) -> bool:
        """登出客户端
        
        Args:
            phone: 手机号
            
        Returns:
            操作是否成功
        """
        return await self._client_manager.logout_client(phone)
    
    async def check_connection(self, phone: str) -> bool:
        """检查客户端连接状态
        
        Args:
            phone: 手机号
            
        Returns:
            连接状态
        """
        return await self._client_manager.check_connection(phone)
    
    def get_active_clients(self) -> List[str]:
        """获取所有活跃的客户端手机号列表
        
        Returns:
            活跃客户端的手机号列表
        """
        return self._client_manager.get_active_clients()
    
    def is_client_connected(self, phone: str) -> bool:
        """检查客户端是否已连接
        
        Args:
            phone: 手机号
            
        Returns:
            客户端是否已连接
        """
        return self._client_manager.is_client_connected(phone)
    
    def get_client(self, phone: str):
        """获取指定手机号的客户端实例
        
        Args:
            phone: 手机号
            
        Returns:
            客户端实例或None
        """
        return self._client_manager.get_client(phone)
    
    # =============================================
    # 用户管理功能（代理到UserManager）
    # =============================================
    
    async def get_user_info(self, phone: str) -> Tuple[bool, Union[Dict[str, Any], str]]:
        """获取用户信息
        
        Args:
            phone: 手机号
            
        Returns:
            (成功标志, 用户信息或错误消息)
        """
        return await self._user_manager.get_user_info(phone)
    
    async def update_profile(self, phone: str, **kwargs) -> Tuple[bool, Union[Dict[str, Any], str]]:
        """更新用户资料
        
        Args:
            phone: 手机号
            **kwargs: 要更新的资料字段
            
        Returns:
            (成功标志, 更新后的用户信息或错误消息)
        """
        return await self._user_manager.update_profile(phone, **kwargs)
    
    async def batch_update_profiles(self, accounts: List[Dict[str, Any]], **kwargs) -> Dict[str, Any]:
        """批量更新用户资料
        
        Args:
            accounts: 账户信息列表
            **kwargs: 要更新的资料字段模板
            
        Returns:
            更新结果统计
        """
        return await self._user_manager.batch_update_profiles(accounts, **kwargs)
    
    # =============================================
    # 群组管理功能（代理到GroupManager）
    # =============================================
    
    async def get_dialogs(self, phone: str, dialog_type: str = None) -> Union[List[Dict[str, Any]], str]:
        """获取对话列表
        
        Args:
            phone: 手机号
            dialog_type: 对话类型
            
        Returns:
            对话列表或错误消息
        """
        return await self._group_manager.get_dialogs(phone, dialog_type)
    
    async def invite_user_to_group(self, phone: str, username: str, group_name: str, message: Optional[str] = None) -> Tuple[bool, Union[str, Dict]]:
        """邀请用户到群组
        
        Args:
            phone: 账户手机号
            username: 被邀请用户的用户名
            group_name: 群组名称
            message: 邀请消息
            
        Returns:
            (成功标志, 结果消息)
        """
        return await self._group_manager.invite_user_to_group(phone, username, group_name, message)
    
    async def send_invite_link(self, phone: str, username: str, invite_link: str, message: Optional[str] = None) -> Tuple[bool, Union[str, Dict]]:
        """发送邀请链接
        
        Args:
            phone: 账户手机号
            username: 被邀请用户的用户名
            invite_link: 邀请链接
            message: 邀请消息
            
        Returns:
            (成功标志, 结果消息)
        """
        return await self._group_manager.send_invite_link(phone, username, invite_link, message)
    
    async def join_group_if_needed(self, phone: str, group_name: str) -> Tuple[bool, str]:
        """加入群组（如果尚未加入）
        
        Args:
            phone: 手机号
            group_name: 群组名称
            
        Returns:
            (成功标志, 结果消息)
        """
        return await self._group_manager.join_group_if_needed(phone, group_name)
    
    # =============================================
    # 消息管理功能（代理到MessageManager）
    # =============================================
    
    async def send_text_message(self, phone: str, chat_id: Union[int, str], text: str, parse_mode: Optional[str] = None) -> Tuple[bool, Union[Any, str]]:
        """发送文本消息
        
        Args:
            phone: 手机号
            chat_id: 聊天ID
            text: 消息文本
            parse_mode: 解析模式
            
        Returns:
            (成功标志, 结果消息)
        """
        return await self._message_manager.send_text_message(phone, chat_id, text, parse_mode)
    
    async def send_image_text_message(self, phone: str, chat_id: Union[int, str], image_path: str, text: str,
                                      parse_mode: Optional[str] = 'html') -> Tuple[bool, Union[Any, str]]:
        """发送图文消息（图片+文本）

        Args:
            phone: 手机号
            chat_id: 聊天ID
            image_path: 图片路径
            text: 消息文本
            parse_mode: 解析模式

        Returns:
            (成功标志, 结果消息)
        """
        return await self._message_manager.send_image_text_message(phone, chat_id, image_path, text, parse_mode)
    async def send_file_message(self, phone: str, chat_id: Union[int, str], file_path: str, caption: Optional[str] = None, media_type = None, progress_callback: Optional[Callable] = None, file_name: Optional[str] = None, parse_mode: Optional[str] = None) -> Tuple[bool, Union[Any, str]]:
        """发送文件消息
        
        Args:
            phone: 手机号
            chat_id: 聊天ID
            file_path: 文件路径
            caption: 说明文字
            media_type: 媒体类型
            progress_callback: 进度回调
            file_name: 文件名
            parse_mode: 解析模式
            
        Returns:
            (成功标志, 结果消息)
        """
        return await self._message_manager.send_file_message(phone, chat_id, file_path, caption, media_type, progress_callback, file_name, parse_mode)
    
    async def forward_messages(self, phone: str, chat_id: Union[int, str], from_chat_id: Union[int, str], message_ids: List[int], drop_author: bool = False) -> Tuple[bool, Union[List[Any], str]]:
        """转发消息
        
        Args:
            phone: 手机号
            chat_id: 目标聊天ID
            from_chat_id: 源聊天ID
            message_ids: 消息ID列表
            drop_author: 是否隐藏作者
            
        Returns:
            (成功标志, 结果消息)
        """
        return await self._message_manager.forward_messages(phone, chat_id, from_chat_id, message_ids, drop_author)
    
    async def batch_send_messages(self, phone: str, chat_ids: List[Union[int, str]], message_content: Dict[str, Any], delay: int = 5, max_per_session: int = 20) -> Dict[str, Any]:
        """批量发送消息
        
        Args:
            phone: 手机号
            chat_ids: 聊天ID列表
            message_content: 消息内容
            delay: 延迟时间
            max_per_session: 每批次最大发送数
            
        Returns:
            发送结果统计
        """
        return await self._message_manager.batch_send_messages(phone, chat_ids, message_content, delay, max_per_session)
    
    # =============================================
    # 监控管理功能（代理到MonitorManager）
    # =============================================
    
    def register_monitoring_handlers(self, task_id: str, account_phone: str, chat_ids: List[int], callback: Callable) -> Tuple[bool, str]:
        """注册监控处理器
        
        Args:
            task_id: 任务ID
            account_phone: 账户手机号
            chat_ids: 聊天ID列表
            callback: 回调函数
            
        Returns:
            (成功标志, 结果消息)
        """
        return self._monitor_manager.register_monitoring_handlers(task_id, account_phone, chat_ids, callback)
    
    def unregister_monitoring_handlers(self, task_id: str) -> Tuple[bool, str]:
        """注销监控处理器
        
        Args:
            task_id: 任务ID
            
        Returns:
            (成功标志, 结果消息)
        """
        return self._monitor_manager.unregister_monitoring_handlers(task_id)
    
    def register_custom_event_handler(self, task_id: str, account_phone: str, event_type, filter_func: Optional[Callable] = None, callback: Callable = None) -> Tuple[bool, str]:
        """注册自定义事件处理器
        
        Args:
            task_id: 任务ID
            account_phone: 账户手机号
            event_type: 事件类型
            filter_func: 过滤函数
            callback: 回调函数
            
        Returns:
            (成功标志, 结果消息)
        """
        return self._monitor_manager.register_custom_event_handler(task_id, account_phone, event_type, filter_func, callback)
    
    def list_active_monitors(self) -> Dict[str, Any]:
        """获取所有活跃的监控任务
        
        Returns:
            监控任务信息
        """
        return self._monitor_manager.list_active_monitors()
    
    def get_task_monitors(self, task_id: str) -> Dict[str, Any]:
        """获取指定任务的监控信息
        
        Args:
            task_id: 任务ID
            
        Returns:
            任务监控信息
        """
        return self._monitor_manager.get_task_monitors(task_id)
    
    def clear_all_monitors(self) -> Tuple[bool, str]:
        """清除所有监控任务
        
        Returns:
            (成功标志, 结果消息)
        """
        return self._monitor_manager.clear_all_monitors()
    
    # =============================================
    # 自动登录相关方法（代理到ClientManager）
    # =============================================
    
    async def batch_auto_login(self, accounts: List[Dict[str, Any]], max_concurrent: int = 5) -> Dict[str, Any]:
        """批量自动登录账户
        
        Args:
            accounts: 账户列表,每项包含 {phone, proxy} 信息
            max_concurrent: 最大并发数
            
        Returns:
            登录结果统计
        """
        return await self._client_manager.batch_auto_login(accounts, max_concurrent)
    
    # =============================================
    # 通用功能
    # =============================================
    
    def cleanup(self):
        """清理所有资源"""
        self._monitor_manager.clear_all_monitors()
        self._client_manager.cleanup() 
