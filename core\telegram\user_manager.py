#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Telegram用户管理模块
负责用户信息获取、更新等功能
"""

import os
import time
from typing import Dict, List, Optional, Tuple, Any, Union

from telethon import TelegramClient, utils
from telethon.tl import types, functions
from telethon.errors import FloodWaitError, UserDeactivatedBanError

from utils.logger import get_logger

class UserManager:
    """Telegram用户管理器"""
    
    def __init__(self, client_manager):
        """初始化用户管理器
        
        Args:
            client_manager: TelegramClientManager实例，用于获取客户端
        """
        self._client_manager = client_manager
        self._logger = get_logger("core.telegram.user_manager")
        self._logger.info("Telegram用户管理器初始化")
        
    async def get_user_info(self, phone: str) -> <PERSON><PERSON>[bool, Union[Dict[str, Any], str]]:
        """获取用户信息
        
        Args:
            phone: 手机号
            
        Returns:
            成功返回(True, user_info)，失败返回(False, error_message)
        """
        self._logger.info(f"正在获取用户信息: {phone}")
        
        client = self._client_manager.get_client(phone)
        if not client:
            error_msg = f"找不到客户端: {phone}"
            self._client_manager.notify.emit("获取用户信息失败", f"{phone}获取用户信息失败,找不到客户端!", "error")
            self._logger.warning(f"获取用户信息失败, {error_msg}")
            return False, error_msg
        
        if not client.is_connected():
            try:
                # 尝试连接客户端
                self._logger.info(f"尝试连接客户端: {phone}")
                await client.connect()
                if not client.is_connected():
                    error_msg = f"无法连接客户端: {phone}"
                    self._logger.warning(error_msg)
                    return False, error_msg
            except Exception as e:
                error_msg = f"连接客户端异常: {phone}, {e}"
                self._logger.error(error_msg)
                return False, error_msg
        try:            
            user_info = await self._get_user_info(client)
            self._client_manager.notify.emit("用户信息更新", f"{phone}用户信息更新成功", "success")
            self._logger.info(f"获取用户信息成功: {phone}")
            return True, user_info
        except Exception as e:
            error_msg = f"获取用户信息失败: {str(e)}"
            self._client_manager.notify.emit("获取用户信息失败", f"{phone}获取用户信息失败,错误信息:{error_msg}", "error")
            self._logger.error(f"获取用户信息错误: {phone}, {error_msg}")
            return False, error_msg
    
    async def update_profile(self, phone: str, **kwargs) -> Tuple[bool, Union[Dict[str, Any], str]]:
        """更新用户资料
        
        Args:
            phone: 手机号
            **kwargs: 要更新的资料字段，可包括：
                first_name: 名字
                last_name: 姓氏
                username: 用户名
                bio: 个人简介
                profile_photo: 头像文件路径
            
        Returns:
            成功返回(True, updated_info)，失败返回(False, error_message)
        """
        self._logger.info(f"正在更新用户资料: {phone}")
        
        client = self._client_manager.get_client(phone)
        if not client:
            error_msg = f"找不到客户端: {phone}"
            self._client_manager.notify.emit("更新用户资料失败", phone, error_msg)
            self._logger.warning(f"更新用户资料失败, {error_msg}")
            return False, error_msg
        
        try:
            # 更新基本资料（名字、姓氏、简介）
            update_fields = {}
            valid_fields = {'first_name', 'last_name'}
            for field in valid_fields:
                if field in kwargs:
                    update_fields[field] = kwargs[field]

            # 处理bio字段，Telegram API使用'about'而不是'bio'
            if 'bio' in kwargs:
                update_fields['about'] = kwargs['bio']

            if update_fields:
                await client(functions.account.UpdateProfileRequest(**update_fields))

            # 单独处理用户名更新
            if 'username' in kwargs:
                username = kwargs['username']
                # 如果用户名为空字符串，传递空字符串来清除用户名
                await client(functions.account.UpdateUsernameRequest(username=username or ""))
            
            # 更新头像
            if 'profile_photo' in kwargs and kwargs['profile_photo']:
                photo_path = kwargs['profile_photo']
                if os.path.exists(photo_path):
                    await client(functions.photos.UploadProfilePhotoRequest(
                        file=await client.upload_file(photo_path)
                    ))
            
            # 获取更新后的用户信息
            user_info = await self._get_user_info(client)
            self._client_manager.notify.emit("资料更新成功", f"{phone}资料更新成功", "success")
            self._logger.info(f"更新用户资料成功: {phone}")
            return True, user_info
        except Exception as e:
            error_msg = f"更新用户资料失败: {str(e)}"
            self._client_manager.notify.emit("更新用户资料失败", f"{phone}更新用户资料失败,错误信息:{error_msg}", "error")
            self._logger.error(f"更新用户资料错误: {phone}, {error_msg}")
            return False, error_msg
    
    async def batch_update_profiles(self, accounts: List[Dict[str, Any]], **kwargs) -> Dict[str, Any]:
        """批量更新用户资料
        
        Args:
            accounts: 账户信息列表，每项包含 {phone, ...}
            **kwargs: 要更新的资料字段模板
            
        Returns:
            操作结果统计
        """
        self._logger.info(f"开始批量更新用户资料, 共 {len(accounts)} 个账户")
        
        results = {
            'total': len(accounts),
            'success': 0,
            'failed': 0,
            'details': []
        }
        
        for i, account in enumerate(accounts):
            phone = account['phone']
            
            # 处理随机标签
            update_fields = self._process_template_fields(kwargs.copy(), phone)
            
            self._client_manager.notify.emit("批量操作进度", f"正在更新: {phone}", "info")
            
            try:
                success, result = await self.update_profile(phone, **update_fields)
                
                detail = {
                    'phone': phone,
                    'success': success,
                    'fields': update_fields
                }
                
                if success:
                    results['success'] += 1
                else:
                    results['failed'] += 1
                    detail['error'] = result
                
                results['details'].append(detail)
            except Exception as e:
                self._logger.error(f"更新用户资料异常: {phone}, {str(e)}")
                results['failed'] += 1
                results['details'].append({
                    'phone': phone,
                    'success': False,
                    'fields': update_fields,
                    'error': str(e)
                })
        
        self._client_manager.notify.emit("批量操作完成", f"批量更新用户资料完成: 总计 {results['total']}, 成功 {results['success']}, 失败 {results['failed']}", "info")
        self._logger.info(f"批量更新用户资料完成: 总计 {results['total']}, 成功 {results['success']}, 失败 {results['failed']}")
        
        return results
    
    async def _get_user_info(self, client: TelegramClient) -> Dict[str, Any]:
        """获取用户详细信息
        
        Args:
            client: Telegram客户端实例
            
        Returns:
            用户信息字典
        """
        me = await client.get_me()
        
        # 提取基本用户信息
        user_info = self._extract_user_info(me)
        
        # 获取用户全名
        if hasattr(me, 'phone'):
            user_info['phone'] = me.phone
        
        # 获取其他额外信息
        try:
            # 获取用户状态
            user_full = await client(functions.users.GetFullUserRequest(me.id))
            if hasattr(user_full, 'about'):
                user_info['bio'] = user_full.about
        except Exception as e:
            self._logger.warning(f"获取用户额外信息失败: {str(e)}")
        
        return user_info
    
    @staticmethod
    def _extract_user_info(user) -> Dict[str, Any]:
        """从用户对象中提取信息
        
        Args:
            user: Telegram用户对象
            
        Returns:
            用户信息字典
        """
        if user is None:
            # 用户为None，返回空信息
            return {
                'id': 0,
                'first_name': '',
                'last_name': '',
                'username': '',
                'phone': '',
                'bot': False,
                'verified': False,
                'restricted': False,
                'photo': False,
                'status_type': 'unknown'
            }
        
        user_info = {
            'id': getattr(user, 'id', 0),
            'first_name': getattr(user, 'first_name', '') or '',
            'last_name': getattr(user, 'last_name', '') or '',
            'username': getattr(user, 'username', '') or '',
            'phone': getattr(user, 'phone', '') or '',
            'bot': getattr(user, 'bot', False),
            'verified': getattr(user, 'verified', False),
            'restricted': getattr(user, 'restricted', False),
            'photo': bool(getattr(user, 'photo', None)),
            'status': getattr(user, 'status', None)
        }
        
        # 处理最后在线时间
        if hasattr(user, 'status') and user.status is not None:
            try:
                if isinstance(user.status, types.UserStatusOnline):
                    user_info['status_type'] = 'online'
                elif isinstance(user.status, types.UserStatusOffline):
                    user_info['status_type'] = 'offline'
                    user_info['last_online'] = user.status.was_online.isoformat()
                elif isinstance(user.status, types.UserStatusRecently):
                    user_info['status_type'] = 'recently'
                elif isinstance(user.status, types.UserStatusLastWeek):
                    user_info['status_type'] = 'last_week'
                elif isinstance(user.status, types.UserStatusLastMonth):
                    user_info['status_type'] = 'last_month'
                else:
                    user_info['status_type'] = 'unknown'
            except Exception:
                user_info['status_type'] = 'unknown'
        else:
            user_info['status_type'] = 'unknown'
        
        return user_info
    
    def _process_template_fields(self, template: Dict[str, Any], seed: str) -> Dict[str, Any]:
        """处理模板字段中的随机标签
        
        Args:
            template: 模板字段
            seed: 用于随机种子的字符串
            
        Returns:
            处理后的字段字典
        """
        import re
        import random
        import string
        
        # 设置种子以确保同一账户的相同标签生成相同结果，不同账户生成不同结果
        random.seed(seed + str(time.time()))
        
        def replace_random_tag(match):
            tag_type = match.group(1)
            length = match.group(2)
            
            if '-' in length:
                min_len, max_len = map(int, length.split('-'))
                length = random.randint(min_len, max_len)
            else:
                length = int(length)
            
            if tag_type.lower() == '随机字符':
                chars = string.ascii_letters + string.digits
                return ''.join(random.choice(chars) for _ in range(length))
            elif tag_type.lower() == '随机数字':
                return ''.join(random.choice(string.digits) for _ in range(length))
            elif tag_type.lower() == '随机字母':
                return ''.join(random.choice(string.ascii_letters) for _ in range(length))
            else:
                return match.group(0)
        
        result = {}
        
        for key, value in template.items():
            if isinstance(value, str):
                # 匹配类似 {随机字符3} 或 {随机数字3-5} 的格式
                pattern = r'\{(随机字符|随机数字|随机字母)(\d+(?:-\d+)?)\}'
                result[key] = re.sub(pattern, replace_random_tag, value)
            else:
                result[key] = value
        
        return result 
