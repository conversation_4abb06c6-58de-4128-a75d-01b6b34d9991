#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
用户状态管理器

基于新任务执行器的用户状态管理，提供：
1. 用户状态跟踪
2. 连接状态管理
3. 用户操作历史
4. 状态变更通知
"""

from typing import Dict, List, Optional, Any, Set
from datetime import datetime
from enum import Enum
from dataclasses import dataclass, field

from PySide6.QtCore import QObject, Signal

from utils.logger import get_logger

logger = get_logger(__name__)


class UserStatus(Enum):
    """用户状态枚举"""
    OFFLINE = "offline"           # 离线
    CONNECTING = "connecting"     # 连接中
    ONLINE = "online"            # 在线
    LOGGING_IN = "logging_in"    # 登录中
    LOGIN_FAILED = "login_failed" # 登录失败
    DISCONNECTED = "disconnected" # 已断开
    BANNED = "banned"            # 被封禁
    ERROR = "error"              # 错误状态


class UserOperation(Enum):
    """用户操作类型"""
    LOGIN = "login"
    LOGOUT = "logout"
    CONNECT = "connect"
    DISCONNECT = "disconnect"
    UPDATE_PROFILE = "update_profile"
    SEND_MESSAGE = "send_message"
    JOIN_GROUP = "join_group"
    LEAVE_GROUP = "leave_group"


@dataclass
class UserInfo:
    """用户信息数据类"""
    phone: str
    user_id: Optional[int] = None
    username: Optional[str] = None
    first_name: Optional[str] = None
    last_name: Optional[str] = None
    bio: Optional[str] = None
    status: UserStatus = UserStatus.OFFLINE
    last_seen: Optional[datetime] = None
    created_at: datetime = field(default_factory=datetime.now)
    updated_at: datetime = field(default_factory=datetime.now)
    
    # 连接信息
    is_connected: bool = False
    is_authorized: bool = False
    session_file: Optional[str] = None
    proxy: Optional[Dict[str, Any]] = None
    
    # 统计信息
    login_count: int = 0
    last_login: Optional[datetime] = None
    error_count: int = 0
    last_error: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "phone": self.phone,
            "user_id": self.user_id,
            "username": self.username,
            "first_name": self.first_name,
            "last_name": self.last_name,
            "bio": self.bio,
            "status": self.status.value,
            "last_seen": self.last_seen.isoformat() if self.last_seen else None,
            "created_at": self.created_at.isoformat(),
            "updated_at": self.updated_at.isoformat(),
            "is_connected": self.is_connected,
            "is_authorized": self.is_authorized,
            "session_file": self.session_file,
            "proxy": self.proxy,
            "login_count": self.login_count,
            "last_login": self.last_login.isoformat() if self.last_login else None,
            "error_count": self.error_count,
            "last_error": self.last_error
        }


@dataclass
class UserOperationRecord:
    """用户操作记录"""
    phone: str
    operation: UserOperation
    timestamp: datetime
    success: bool
    details: Optional[Dict[str, Any]] = None
    error_message: Optional[str] = None
    task_id: Optional[str] = None


class UserStateManager(QObject):
    """用户状态管理器"""
    
    # 信号定义
    user_status_changed = Signal(str, str, str)  # phone, old_status, new_status
    user_connected = Signal(str, dict)  # phone, user_info
    user_disconnected = Signal(str, str)  # phone, reason
    user_operation_completed = Signal(str, str, bool, dict)  # phone, operation, success, details
    
    def __init__(self):
        super().__init__()
        self._users: Dict[str, UserInfo] = {}
        self._operation_history: List[UserOperationRecord] = []
        self._active_tasks: Dict[str, Set[str]] = {}  # phone -> set of task_ids
        
    def register_user(self, phone: str, **kwargs) -> UserInfo:
        """注册用户"""
        if phone in self._users:
            user_info = self._users[phone]
            # 更新现有用户信息
            for key, value in kwargs.items():
                if hasattr(user_info, key):
                    setattr(user_info, key, value)
            user_info.updated_at = datetime.now()
        else:
            # 创建新用户
            user_info = UserInfo(phone=phone, **kwargs)
            self._users[phone] = user_info
            
        logger.info(f"用户已注册: {phone}")
        return user_info
    
    def get_user(self, phone: str) -> Optional[UserInfo]:
        """获取用户信息"""
        return self._users.get(phone)
    
    def get_all_users(self) -> Dict[str, UserInfo]:
        """获取所有用户"""
        return self._users.copy()
    
    def get_users_by_status(self, status: UserStatus) -> List[UserInfo]:
        """根据状态获取用户"""
        return [user for user in self._users.values() if user.status == status]
    
    def update_user_status(self, phone: str, new_status: UserStatus, details: Dict[str, Any] = None):
        """更新用户状态"""
        user_info = self._users.get(phone)
        if not user_info:
            logger.warning(f"用户不存在: {phone}")
            return
            
        old_status = user_info.status
        user_info.status = new_status
        user_info.updated_at = datetime.now()
        
        # 更新相关字段
        if new_status == UserStatus.ONLINE:
            user_info.is_connected = True
            user_info.is_authorized = True
            user_info.last_seen = datetime.now()
        elif new_status in [UserStatus.OFFLINE, UserStatus.DISCONNECTED]:
            user_info.is_connected = False
        elif new_status == UserStatus.LOGIN_FAILED:
            user_info.error_count += 1
            if details and 'error' in details:
                user_info.last_error = details['error']
        
        # 发送状态变更信号
        self.user_status_changed.emit(phone, old_status.value, new_status.value)
        
        logger.info(f"用户状态更新: {phone} {old_status.value} -> {new_status.value}")
    
    def update_user_info(self, phone: str, **kwargs):
        """更新用户信息"""
        user_info = self._users.get(phone)
        if not user_info:
            logger.warning(f"用户不存在: {phone}")
            return
            
        for key, value in kwargs.items():
            if hasattr(user_info, key):
                setattr(user_info, key, value)
        
        user_info.updated_at = datetime.now()
        logger.debug(f"用户信息已更新: {phone}")
    
    def record_operation(self, phone: str, operation: UserOperation, success: bool, 
                        details: Dict[str, Any] = None, error_message: str = None, 
                        task_id: str = None):
        """记录用户操作"""
        record = UserOperationRecord(
            phone=phone,
            operation=operation,
            timestamp=datetime.now(),
            success=success,
            details=details,
            error_message=error_message,
            task_id=task_id
        )
        
        self._operation_history.append(record)
        
        # 发送操作完成信号
        self.user_operation_completed.emit(
            phone, operation.value, success, details or {}
        )
        
        # 更新用户统计
        user_info = self._users.get(phone)
        if user_info:
            if operation == UserOperation.LOGIN and success:
                user_info.login_count += 1
                user_info.last_login = datetime.now()
            elif not success:
                user_info.error_count += 1
                user_info.last_error = error_message
        
        logger.info(f"操作记录: {phone} {operation.value} {'成功' if success else '失败'}")
    
    def add_active_task(self, phone: str, task_id: str):
        """添加活跃任务"""
        if phone not in self._active_tasks:
            self._active_tasks[phone] = set()
        self._active_tasks[phone].add(task_id)
    
    def remove_active_task(self, phone: str, task_id: str):
        """移除活跃任务"""
        if phone in self._active_tasks:
            self._active_tasks[phone].discard(task_id)
            if not self._active_tasks[phone]:
                del self._active_tasks[phone]
    
    def get_active_tasks(self, phone: str) -> Set[str]:
        """获取用户的活跃任务"""
        return self._active_tasks.get(phone, set()).copy()
    
    def get_operation_history(self, phone: str = None, operation: UserOperation = None, 
                            limit: int = 100) -> List[UserOperationRecord]:
        """获取操作历史"""
        history = self._operation_history
        
        # 按条件过滤
        if phone:
            history = [r for r in history if r.phone == phone]
        if operation:
            history = [r for r in history if r.operation == operation]
        
        # 按时间倒序排列并限制数量
        history.sort(key=lambda x: x.timestamp, reverse=True)
        return history[:limit]
    
    def get_user_statistics(self, phone: str = None) -> Dict[str, Any]:
        """获取用户统计信息"""
        if phone:
            user_info = self._users.get(phone)
            if not user_info:
                return {}
            
            return {
                "phone": phone,
                "status": user_info.status.value,
                "login_count": user_info.login_count,
                "error_count": user_info.error_count,
                "last_login": user_info.last_login.isoformat() if user_info.last_login else None,
                "active_tasks": len(self.get_active_tasks(phone)),
                "created_at": user_info.created_at.isoformat(),
                "updated_at": user_info.updated_at.isoformat()
            }
        else:
            # 全局统计
            total_users = len(self._users)
            status_counts = {}
            for status in UserStatus:
                status_counts[status.value] = len(self.get_users_by_status(status))
            
            return {
                "total_users": total_users,
                "status_distribution": status_counts,
                "total_operations": len(self._operation_history),
                "active_tasks_count": sum(len(tasks) for tasks in self._active_tasks.values())
            }
    
    def cleanup_old_records(self, days: int = 30):
        """清理旧的操作记录"""
        cutoff_date = datetime.now() - timedelta(days=days)
        old_count = len(self._operation_history)
        
        self._operation_history = [
            record for record in self._operation_history 
            if record.timestamp > cutoff_date
        ]
        
        cleaned_count = old_count - len(self._operation_history)
        if cleaned_count > 0:
            logger.info(f"已清理 {cleaned_count} 条旧操作记录")


# 全局用户状态管理器实例
user_state_manager = UserStateManager()
