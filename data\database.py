#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
数据库连接与初始化模块
"""
from pathlib import Path
from typing import Optional, AsyncGenerator
from contextlib import asynccontextmanager

from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine, async_sessionmaker
from sqlalchemy.pool import NullPool

from config import config
from utils.logger import get_logger, exception_handler

# 获取数据库日志记录器
logger = get_logger("data.database")

# 全局数据库连接引擎和会话工厂
_engine = None
_session_factory = None

@exception_handler
async def init_database():
    """初始化数据库连接和模型"""
    global _engine, _session_factory
    
    # 获取数据库路径
    db_path = config.get("db_path")
    logger.info(f"初始化数据库连接: {db_path}")
    
    # 确保数据库目录存在
    Path(db_path).parent.mkdir(parents=True, exist_ok=True)
    
    # 构建数据库URI (使用SQLite异步驱动)
    db_uri = f"sqlite+aiosqlite:///{db_path}"
    
    # 创建异步引擎
    _engine = create_async_engine(
        db_uri,
        echo=False,  # 设置为True可以查看SQL语句
        poolclass=NullPool,  # 非池化连接，适用于不同线程访问
        connect_args={"check_same_thread": False}  # SQLite特有设置
    )
    
    # 创建会话工厂
    _session_factory = async_sessionmaker(
        _engine,
        class_=AsyncSession,
        expire_on_commit=False
    )
    
    # 创建所有表 (如果不存在)
    # 从models模块导入所有模型
    from data.models import Base
    async with _engine.begin() as conn:
        logger.debug("创建数据库表")
        await conn.run_sync(Base.metadata.create_all)
    
    logger.info("数据库初始化完成")

async def get_db_session() -> AsyncSession:
    """获取数据库会话
    
    直接返回一个AsyncSession实例，而不是生成器
    
    Returns:
        AsyncSession: 新的数据库会话
    """
    if _session_factory is None:
        logger.warning("数据库尚未初始化，正在初始化...")
        await init_database()
    
    logger.debug("创建新的数据库会话")
    session = _session_factory()
    return session

async def commit_session(session: AsyncSession) -> bool:
    """提交数据库会话
    
    Args:
        session: 数据库会话
        
    Returns:
        bool: 是否成功提交
    """
    try:
        await session.commit()
        logger.debug("数据库会话提交成功")
        return True
    except Exception as e:
        logger.error(f"数据库会话提交失败: {e}")
        await session.rollback()
        logger.debug("已回滚数据库会话")
        return False
    
async def close_session(session: AsyncSession) -> None:
    """关闭数据库会话
    
    Args:
        session: 数据库会话
    """
    try:
        await session.close()
        logger.debug("数据库会话已关闭")
    except Exception as e:
        logger.error(f"关闭数据库会话时出错: {e}")

@asynccontextmanager
async def get_session() -> AsyncGenerator[AsyncSession, None]:
    """获取数据库会话
    
    Returns:
        AsyncGenerator[AsyncSession, None]: 数据库会话生成器
    """
    if _session_factory is None:
        logger.warning("数据库尚未初始化，正在初始化...")
        await init_database()
    
    logger.debug("创建新的数据库会话")
    session = _session_factory()
    try:
        yield session
    finally:
        logger.debug("关闭数据库会话")
        await session.close()

@asynccontextmanager
async def get_connection():
    """获取数据库连接
    
    Returns:
        AsyncGenerator: 数据库连接生成器
    """
    global _engine
    if _engine is None:
        logger.warning("数据库引擎尚未初始化，正在初始化...")
        await init_database()
    
    logger.debug("获取数据库连接")
    async with _engine.connect() as conn:
        yield conn 