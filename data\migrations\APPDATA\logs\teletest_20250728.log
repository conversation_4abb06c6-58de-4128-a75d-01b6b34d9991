2025-07-28 10:44:12.882 | INFO     | utils.logger:init_logger:98 - 日志系统初始化完成，默认级别: DEBUG，日志目录: H:\PyProject\TeleTest-bak\data\migrations\APPDATA\logs
2025-07-28 10:44:12.883 | INFO     | __main__:main:66 - 开始执行数据库迁移：添加login_status字段
2025-07-28 10:44:12.884 | WARNING  | data.database:get_session:118 - 数据库尚未初始化，正在初始化...
2025-07-28 10:44:12.884 | INFO     | data.database:init_database:31 - 初始化数据库连接: H:\PyProject\TeleTest-bak\data\migrations\APPDATA\database\telegram_manager.db
2025-07-28 10:44:12.938 | DEBUG    | data.database:init_database:58 - 创建数据库表
2025-07-28 10:44:12.993 | INFO     | data.database:init_database:61 - 数据库初始化完成
2025-07-28 10:44:12.993 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-28 10:44:12.995 | INFO     | __main__:add_login_status_field:36 - login_status字段已存在，跳过迁移
2025-07-28 10:44:12.996 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-28 10:44:12.996 | INFO     | __main__:main:70 - 数据库迁移完成
2025-07-28 10:44:40.606 | INFO     | utils.logger:init_logger:98 - 日志系统初始化完成，默认级别: DEBUG，日志目录: H:\PyProject\TeleTest-bak\data\migrations\APPDATA\logs
2025-07-28 10:44:40.607 | INFO     | __main__:main:70 - 开始执行数据库迁移：添加login_status字段
2025-07-28 10:44:40.608 | WARNING  | data.database:get_session:118 - 数据库尚未初始化，正在初始化...
2025-07-28 10:44:40.608 | INFO     | data.database:init_database:31 - 初始化数据库连接: H:\PyProject\TeleTest-bak\data\migrations\APPDATA\database\telegram_manager.db
2025-07-28 10:44:40.658 | DEBUG    | data.database:init_database:58 - 创建数据库表
2025-07-28 10:44:40.667 | INFO     | data.database:init_database:61 - 数据库初始化完成
2025-07-28 10:44:40.668 | DEBUG    | data.database:get_session:121 - 创建新的数据库会话
2025-07-28 10:44:40.671 | INFO     | __main__:add_login_status_field:40 - login_status字段已存在，跳过迁移
2025-07-28 10:44:40.671 | DEBUG    | data.database:get_session:126 - 关闭数据库会话
2025-07-28 10:44:40.672 | INFO     | __main__:main:74 - 数据库迁移完成
