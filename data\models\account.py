#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
账户数据模型
定义Telegram账户相关的数据库模型
"""

from typing import Optional
from datetime import datetime,timezone

from sqlalchemy import Column, Integer, String, Boolean, DateTime, ForeignKey, Text, Date
from sqlalchemy.orm import relationship

from data.models import Base


class AccountGroupModel(Base):
    """账户分组模型"""
    
    __tablename__ = "account_groups"
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    name = Column(String(50), nullable=False, unique=True)
    description = Column(String(255), nullable=True)
    created_at = Column(DateTime, default=datetime.now())
    updated_at = Column(DateTime, default=datetime.now(), onupdate=datetime.now())
    
    # 关系
    accounts = relationship("AccountModel", back_populates="group")
    
    def __repr__(self) -> str:
        """字符串表示"""
        return f"<AccountGroup(id={self.id}, name={self.name})>"
    
    def to_dict(self) -> dict:
        """将模型转换为字典"""
        return {
            "id": self.id,
            "name": self.name,
            "description": self.description,
            "created_at": self.created_at.isoformat(),
            "updated_at": self.updated_at.isoformat(),
            "accounts_count": len(self.accounts) if self.accounts else 0
        }


class AccountModel(Base):
    """Telegram账户模型"""
    
    __tablename__ = "accounts"
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    phone = Column(String(20), unique=True, nullable=False, index=True)
    session_file = Column(String(255), nullable=False)
    first_name = Column(String(100), nullable=True)
    last_name = Column(String(100), nullable=True)
    username = Column(String(100), nullable=True, index=True)
    bio = Column(Text, nullable=True)
    profile_photo = Column(String(255), nullable=True)
    is_active = Column(Boolean, default=True)
    is_connected = Column(Boolean, default=False)
    has_2fa = Column(Boolean, default=False)
    last_connected = Column(DateTime, default=None, nullable=True)
    last_active = Column(DateTime, default=None, nullable=True)
    created_at = Column(DateTime, default=datetime.now())
    updated_at = Column(DateTime, default=datetime.now(), onupdate=datetime.now())
    proxy_id = Column(Integer, ForeignKey("proxies.id"), nullable=True)
    proxy_type = Column(String(20), nullable=True)  # 代理类型：'ip_pool', 'system', 'none'
    group_id = Column(Integer, ForeignKey("account_groups.id"), nullable=True)
    
    # 关系
    proxy = relationship("ProxyModel", back_populates="accounts")
    group = relationship("AccountGroupModel", back_populates="accounts")
    
    def __repr__(self) -> str:
        """字符串表示"""
        return f"<Account(id={self.id}, phone={self.phone}, username={self.username})>"
    
    def to_dict(self) -> dict:
        """将模型转换为字典"""
        return {
            "id": self.id,
            "phone": self.phone,
            "session_file": self.session_file,
            "first_name": self.first_name,
            "last_name": self.last_name,
            "username": self.username,
            "bio": self.bio,
            "profile_photo": self.profile_photo,
            "is_active": self.is_active,
            "is_connected": self.is_connected,
            "has_2fa": self.has_2fa,
            "last_connected": self.last_connected.isoformat() if self.last_connected else None,
            "last_active": self.last_active.isoformat() if self.last_active else None,
            "created_at": self.created_at.isoformat(),
            "updated_at": self.updated_at.isoformat(),
            "proxy_id": self.proxy_id,
            "proxy_type": self.proxy_type,
            "group_id": self.group_id
        } 
