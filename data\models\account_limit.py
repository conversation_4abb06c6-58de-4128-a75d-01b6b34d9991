#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
账户限制相关模型
"""
from datetime import datetime, timezone
from sqlalchemy import Column, Integer, String, Date, DateTime
from data.models import Base

class AccountSendLimit(Base):
    """账户发送限额记录，用于限制账户每日发送消息的数量"""
    __tablename__ = "account_send_limits"
    id = Column(Integer, primary_key=True, autoincrement=True)
    account_phone = Column(String, nullable=False, unique=True, index=True)
    last_reset_date = Column(Date, nullable=False)  # 最后重置日期
    current_day_count = Column(Integer, default=0)  # 今日已发送次数
    max_daily_limit = Column(Integer, default=10)  # 每日最大发送限制
    created_at = Column(DateTime, default=datetime.now())
    updated_at = Column(DateTime, default=datetime.now(), onupdate=datetime.now()) 
