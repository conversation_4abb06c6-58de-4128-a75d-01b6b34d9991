#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
聊天数据模型
定义Telegram聊天、群组和频道相关的数据库模型
"""

from typing import Optional
from datetime import datetime, timezone

from sqlalchemy import Column, Integer, String, Boolean, DateTime, Text, ForeignKey
from sqlalchemy.orm import relationship

from data.models import Base


class ChatModel(Base):
    """Telegram聊天模型，包括群组、超级群组和频道"""
    
    __tablename__ = "user_chats"
    
    phone = Column(String(255), primary_key=True, nullable=False)  # 账户手机号，主键
    chat_id = Column(Integer, primary_key=True, nullable=False)  # 聊天ID，主键
    access_hash = Column(Integer, nullable=True)  # 访问哈希值
    chat_type = Column(String(20), nullable=True)  # 聊天类型 ('group', 'supergroup', 'channel')
    title = Column(String(255), nullable=True)  # 群组或频道标题
    username = Column(String(100), nullable=True)  # 群组或频道用户名(如果有)
    participants_count = Column(Integer, nullable=True)  # 成员数量
    is_creator = Column(Boolean, default=False)  # 是否为创建者
    is_admin = Column(Boolean, default=False)  # 是否为管理员
    is_member = Column(Boolean, default=True)  # 当前用户是否仍在该群组中
    last_synced_at = Column(DateTime, default=datetime.now())  # 最后同步时间
    created_at = Column(DateTime, default=datetime.now())  # 记录创建时间
    updated_at = Column(DateTime, default=datetime.now(), onupdate=datetime.now())  # 记录更新时间
    
    # 监控相关字段
    is_user_monitored = Column(Boolean, default=False)  # 是否监控用户
    is_keyword_monitored = Column(Boolean, default=False)  # 是否监控关键词
    
    def __repr__(self) -> str:
        """字符串表示
        
        Returns:
            str: 对象的字符串表示
        """
        return f"<Chat(phone={self.phone}, chat_id={self.chat_id}, title={self.title})>"
    
    def to_dict(self) -> dict:
        """将模型转换为字典
        
        Returns:
            dict: 包含聊天信息的字典
        """
        return {
            "phone": self.phone,  # 账户手机号
            "chat_id": self.chat_id,  # 聊天ID
            "access_hash": self.access_hash,  # 访问哈希值
            "chat_type": self.chat_type,  # 聊天类型
            "title": self.title,  # 标题
            "username": self.username,  # 用户名
            "participants_count": self.participants_count,  # 成员数量
            "is_creator": self.is_creator,  # 是否为创建者
            "is_admin": self.is_admin,  # 是否为管理员
            "is_member": self.is_member,  # 是否仍在群组中
            "last_synced_at": self.last_synced_at.isoformat() if self.last_synced_at else None,  # 最后同步时间
            "created_at": self.created_at.isoformat(),  # 创建时间
            "updated_at": self.updated_at.isoformat(),  # 更新时间
            "is_user_monitored": self.is_user_monitored,  # 是否监控用户
            "is_keyword_monitored": self.is_keyword_monitored,  # 是否监控关键词
        }

