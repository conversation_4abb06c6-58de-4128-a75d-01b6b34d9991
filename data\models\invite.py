from sqlalchemy import Column, Integer, String, <PERSON><PERSON><PERSON>, JSO<PERSON>, DateTime, ForeignKey, Table, Text, Date
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from data.models import Base
from data.models.account import AccountModel
from datetime import datetime, timezone

class InviteTaskStatus:
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    PAUSED = "paused"

class InviteTask(Base):
    """邀请任务主表"""
    __tablename__ = "invite_tasks"
    id = Column(Integer, primary_key=True, autoincrement=True)
    task_name = Column(String(100), nullable=False)
    accounts = Column(JSON, nullable=False)  # 发起人账号（支持多账号）
    group_name = Column(String(100), nullable=True)  # 群组名称
    group_invite_link = Column(String(255), nullable=True)  # 群组邀请链接
    status = Column(String(20), default=InviteTaskStatus.PENDING)
    message = Column(String(255), nullable=True)  # 邀请附言
    invite_interval_min = Column(Integer, default=60, comment="最小邀请间隔(秒)")
    invite_interval_max = Column(Integer, default=300, comment="最大邀请间隔(秒)")
    invite_type = Column(String(20), default="custom", comment="邀请方式(custom/task)")
    batch_size_min = Column(Integer, default=1, comment="每次最小邀请数量")
    batch_size_max = Column(Integer, default=5, comment="每次最大邀请数量")
    progress = Column(Integer, default=0, comment="任务进度(0-100)")
    

    created_at = Column(DateTime, default=datetime.now())
    updated_at = Column(DateTime, default=datetime.now(), onupdate=datetime.now())

    def to_dict(self):
        return {
            "id": self.id,
            "task_name": self.task_name,
            "accounts": self.accounts,
            "group_name":self.group_name,
            "status": self.status,
            "message": self.message,
            "invite_interval_min": self.invite_interval_min,
            "invite_interval_max": self.invite_interval_max,
            "invite_type": self.invite_type,
            "batch_size_min": self.batch_size_min,
            "batch_size_max": self.batch_size_max,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None
        }

class InviteRecord(Base):
    """邀请记录表，记录每个被邀请人的状态"""
    __tablename__ = "invite_records"
    id = Column(Integer, primary_key=True, autoincrement=True)
    task_id = Column(Integer, ForeignKey("invite_tasks.id"), nullable=False, index=True)
    invitee = Column(String(100), nullable=False, index=True)  # 被邀请人手机号/邮箱
    invitee_name = Column(String(100), nullable=True)
    status = Column(String(20), default="pending", index=True)  # pending/success/failed
    error_message = Column(Text, nullable=True)
    processed_at = Column(DateTime, nullable=True)
    created_at = Column(DateTime, default=datetime.now())
    updated_at = Column(DateTime, default=datetime.now(), onupdate=datetime.now())

    task = relationship("InviteTask", backref="invite_records")

    def to_dict(self):
        return {
            "id": self.id,
            "task_id": self.task_id,
            "invitee": self.invitee,
            "invitee_name": self.invitee_name,
            "status": self.status,
            "error_message": self.error_message,
            "processed_at": self.processed_at.isoformat() if self.processed_at else None,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None
        }

class InviteLimit(Base):
    """账户每日邀请次数限制表"""
    __tablename__ = "invite_limits"
    id = Column(Integer, primary_key=True, autoincrement=True)
    account_phone = Column(String, nullable=False, unique=True, index=True)
    last_reset_date = Column(Date, nullable=False)  # 最后重置日期
    current_day_count = Column(Integer, default=0)  # 今日已邀请次数
    max_daily_limit = Column(Integer, default=10)  # 每日最大邀请限制
    created_at = Column(DateTime, default=datetime.now())
    updated_at = Column(DateTime, default=datetime.now(), onupdate=datetime.now())
