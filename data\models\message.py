from sqlalchemy import <PERSON><PERSON><PERSON>, Column, Integer, String, DateTime, JSON, ForeignKey, Text
from datetime import datetime, timezone
from data.models import Base
from enum import Enum


class MessageTaskStatus(Enum):
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    PAUSED = "paused"


class MessageTask(Base):
    """消息任务主表，包含多条消息内容、目标类型、允许账户等"""
    __tablename__ = "message_tasks"
    id = Column(Integer, primary_key=True, autoincrement=True)
    task_name = Column(String(100), nullable=False)
    messages = Column(JSON, nullable=False)  # 多条消息内容 [{"type": "text", "content": "..."}]
    target_type = Column(String(20), nullable=False)  # users/groups/channels
    accounts = Column(JSON, nullable=False)  # 允许发送的账户手机号/ID列表
    random_emoji = Column(Boolean, default=False)  # 是否随机发送表情
    status = Column(String(20), default="pending")  # pending/running/completed/failed/paused
    
    schedule_type = Column(String(20), default="immediate") # immediate/scheduled
    scheduled_at = Column(DateTime, nullable=True) # 定时发送时间
    
    # 账户切换时间范围(秒)
    account_switch_min = Column(Integer, default=60)  # 最小切换时间
    account_switch_max = Column(Integer, default=300)  # 最大切换时间
    
    # 消息间隔时间范围(秒)
    message_interval_min = Column(Integer, default=5)  # 最小消息间隔
    message_interval_max = Column(Integer, default=30)  # 最大消息间隔

    created_at = Column(DateTime, default=datetime.now())   # 创建时间
    updated_at = Column(DateTime, default=datetime.now(), onupdate=datetime.now()) # 更新时间

    def to_dict(self):
        return {
            "id": self.id,
            "task_name": self.task_name,
            "messages": self.messages,
            "target_type": self.target_type,
            "accounts": self.accounts,
            "status": self.status,
            "random_emoji": self.random_emoji,
            "schedule_type": self.schedule_type,
            "scheduled_at": self.scheduled_at.isoformat() if self.scheduled_at else None,
            "account_switch_min": self.account_switch_min,
            "account_switch_max": self.account_switch_max,
            "message_interval_min": self.message_interval_min,
            "message_interval_max": self.message_interval_max,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None
        }

class MessageTaskTargetLog(Base):
    """任务目标&极简日志表，记录每个任务-目标的发送状态"""
    __tablename__ = "message_task_target_logs"
    id = Column(Integer, primary_key=True, autoincrement=True)
    task_id = Column(Integer, ForeignKey("message_tasks.id"), nullable=False, index=True)
    target_id = Column(String(100), nullable=False, index=True)  # 用户/群/频道ID
    target_name = Column(String(100), nullable=True)
    status = Column(String(20), default="pending", index=True)  # pending/success/failed
    error_message = Column(Text, nullable=True)
    processed_at = Column(DateTime, nullable=True)
    created_at = Column(DateTime, default=datetime.now())
    updated_at = Column(DateTime, default=datetime.now(), onupdate=datetime.now())

    def to_dict(self):
        return {
            "id": self.id,
            "task_id": self.task_id,
            "target_id": self.target_id,
            "target_name": self.target_name,
            "status": self.status,
            "error_message": self.error_message,
            "processed_at": self.processed_at.isoformat() if self.processed_at else None,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None
        }
