#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
监控任务数据模型
定义监控任务、监控消息、通知配置等数据结构
"""

import json
from typing import List, Optional, Dict, Any
from datetime import timezone,datetime

from sqlalchemy import Column, Integer, String, Boolean, DateTime, ForeignKey, Text, Table, JSON
from sqlalchemy.orm import relationship

from data.models import Base


# 账户与监控任务的多对多关系表
account_monitor_tasks = Table(
    "account_monitor_tasks",
    Base.metadata,
    Column("account_id", Integer, ForeignKey("accounts.id"), primary_key=True),
    Column("monitor_task_id", Integer, ForeignKey("monitor_tasks.id"), primary_key=True)
)

class MonitorTask(Base):
    """监控任务表"""
    __tablename__ = "monitor_tasks"
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    name = Column(String(100), nullable=False, comment="任务名称")
    description = Column(String(255), nullable=True, comment="任务描述")
    # 不再直接存储单一群组，而是通过MonitoredChat关联多个群组
    group_id = Column(String(50), nullable=True, comment="群组ID (旧版兼容)")
    group_title = Column(String(100), nullable=True, comment="群组标题 (旧版兼容)")
    keywords = Column(Text, nullable=True, comment="关键词列表（JSON格式）")
    ignore_keywords = Column(Text, nullable=True, comment="忽略关键词列表（JSON格式）")
    ignore_nicknames_rules = Column(Text, nullable=True, comment="忽略昵称规则列表（JSON格式）")
    monitor_new_users = Column(Boolean, default=False, comment="是否监控新用户")
    monitor_messages = Column(Boolean, default=True, comment="是否监控消息")
    is_active = Column(Boolean, default=True, comment="是否启用")
    is_running = Column(Boolean, default=False, comment="是否正在运行")
    last_error = Column(Text, nullable=True, comment="最后错误信息")
    block_enabled = Column(Boolean, default=False, comment="是否启用屏蔽")
    block_scope = Column(String(20), nullable=True, comment="屏蔽范围（all/user/msg）")
    created_at = Column(DateTime, default=datetime.now(), comment="创建时间")
    updated_at = Column(DateTime, default=datetime.now(), onupdate=datetime.now(), comment="更新时间")
    
    # 关联关系
    messages = relationship("MonitorMessage", back_populates="task", cascade="all, delete-orphan")
    notification_configs = relationship("NotificationConfig", back_populates="task", cascade="all, delete-orphan")
    monitored_chats = relationship("MonitoredChat", back_populates="task", cascade="all, delete-orphan")
    
    def __repr__(self):
        return f"<MonitorTask(id={self.id}, name='{self.name}')>"


class MonitorMessage(Base):
    """监控消息表"""
    __tablename__ = "monitor_messages"
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    task_id = Column(Integer, ForeignKey("monitor_tasks.id"), nullable=False, comment="所属任务ID")
    message_id = Column(String(50), nullable=False, comment="Telegram消息ID")
    chat_id = Column(String(50), nullable=False, comment="群组ID")
    user_id = Column(String(50), nullable=False, comment="用户ID")
    username = Column(String(100), nullable=True, comment="用户名")
    first_name = Column(String(100), nullable=True, comment="用户名字")
    last_name = Column(String(100), nullable=True, comment="用户姓")
    text = Column(Text, nullable=True, comment="消息内容")
    matched_keywords = Column(Text, nullable=True, comment="匹配的关键词（JSON格式）")
    message_type = Column(String(20), default="text", comment="消息类型（文本、媒体等）")
    collected_at = Column(DateTime, default=datetime.now(), comment="采集时间")
    
    # 关联关系
    task = relationship("MonitorTask", back_populates="messages")
    
    def __repr__(self):
        return f"<MonitorMessage(id={self.id}, task_id={self.task_id}, user_id='{self.user_id}')>"


class NotificationConfig(Base):
    """通知配置表"""
    __tablename__ = "notification_configs"
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    task_id = Column(Integer, ForeignKey("monitor_tasks.id"), nullable=False, comment="所属任务ID")
    target_type = Column(String(20), nullable=False, comment="通知目标类型（private_chat/group_chat/email/webhook）")
    target_address = Column(String(255), nullable=False, comment="通知目标地址")
    account_id = Column(Integer, nullable=True, comment="发送通知使用的账户ID")
    template = Column(Text, nullable=True, comment="通知模板")
    is_active = Column(Boolean, default=True, comment="是否启用")
    created_at = Column(DateTime, default=datetime.now(), comment="创建时间")
    updated_at = Column(DateTime, default=datetime.now(), onupdate=datetime.now(), comment="更新时间")
    
    # 关联关系
    task = relationship("MonitorTask", back_populates="notification_configs")
    
    def __repr__(self):
        return f"<NotificationConfig(id={self.id}, task_id={self.task_id}, target_type='{self.target_type}')>"


class MonitoredChat(Base):
    """监控聊天表，存储任务监控的群组/频道信息，支持多账户监控"""
    __tablename__ = "monitored_chats"
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    task_id = Column(Integer, ForeignKey("monitor_tasks.id"), nullable=False, comment="所属任务ID")
    chat_id = Column(String(50), nullable=False, comment="聊天ID")
    chat_title = Column(String(100), nullable=True, comment="聊天标题")
    account_phone = Column(String(50), nullable=True, comment="关联的账户手机号")
    created_at = Column(DateTime, default=datetime.now(), comment="创建时间")
    
    # 关联关系
    task = relationship("MonitorTask", back_populates="monitored_chats")
    
    def __repr__(self):
        return f"<MonitoredChat(id={self.id}, task_id={self.task_id}, chat_id='{self.chat_id}', account_phone='{self.account_phone}')>"

