#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
代理IP数据模型
定义代理IP相关的数据库模型
"""

from datetime import  datetime
from typing import Optional, List

from sqlalchemy import Column, Integer, String, Boolean, DateTime, Float
from sqlalchemy.orm import relationship

from data.models import Base


class ProxyModel(Base):
    """代理IP模型"""
    
    __tablename__ = "proxies"
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    host = Column(String(255), nullable=False)
    port = Column(Integer, nullable=False)
    username = Column(String(100), nullable=True)
    password = Column(String(100), nullable=True)
    proxy_type = Column(String(10), default="socks5")  # socks5, http, https
    is_active = Column(Boolean, default=True)
    is_local = Column(Boolean, default=False)  # 是否是本地代理
    last_checked = Column(DateTime, nullable=True)
    is_valid = Column(<PERSON><PERSON>an, default=False)
    response_time = Column(Float, nullable=True)  # 毫秒
    success_count = Column(Integer, default=0)
    failure_count = Column(Integer, default=0)
    created_at = Column(DateTime, default=lambda: datetime.now())
    updated_at = Column(DateTime, default=lambda: datetime.now(), onupdate=lambda: datetime.now())
    
    # 关系
    accounts = relationship("AccountModel", back_populates="proxy")
    
    def __repr__(self) -> str:
        """字符串表示"""
        auth = f"{self.username}:***@" if self.username else ""
        return f"<Proxy(id={self.id}, {self.proxy_type}://{auth}{self.host}:{self.port})>"
    
    def to_dict(self) -> dict:
        """将模型转换为字典"""
        return {
            "id": self.id,
            "host": self.host,
            "port": self.port,
            "username": self.username,
            "password": self.password,
            "proxy_type": self.proxy_type,
            "is_active": self.is_active,
            "is_local": self.is_local,
            "last_checked": self.last_checked.isoformat() if self.last_checked else None,
            "is_valid": self.is_valid,
            "response_time": self.response_time,
            "success_count": self.success_count,
            "failure_count": self.failure_count,
            "created_at": self.created_at.isoformat(),
            "updated_at": self.updated_at.isoformat()
        }
    
    def get_connection_string(self) -> str:
        """获取代理连接字符串"""
        auth = f"{self.username}:{self.password}@" if self.username and self.password else ""
        return f"{self.proxy_type}://{auth}{self.host}:{self.port}"
    
    def get_telethon_proxy(self) -> tuple:
        """获取Telethon格式的代理配置"""
        proxy_type = {
            "socks5": 2,  # SOCKS5
            "http": 1,    # HTTP
            "https": 1    # HTTP
        }.get(self.proxy_type, 2)  # 默认SOCKS5
        
        return (
            proxy_type,
            self.host,
            self.port,
            self.username,
            self.password
        )
    
    def to_telethon_dict(self) -> dict:
        """获取Telethon格式的代理字典
        
        Returns:
            dict: Telethon格式的代理配置字典
        """
        proxy_type = {
            "socks5": "socks5",  # SOCKS5
            "http": "http",      # HTTP
            "https": "http"      # HTTP (Telethon使用http键处理https)
        }.get(self.proxy_type, "socks5")  # 默认SOCKS5
        
        proxy_dict = {
            "proxy_type": proxy_type,
            "addr": self.host,
            "port": self.port
        }
        
        # 如果有用户名和密码，则添加到字典
        if self.username and self.password:
            proxy_dict["username"] = self.username
            proxy_dict["password"] = self.password
            
        return proxy_dict 