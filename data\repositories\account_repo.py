#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
账户数据仓库
提供对账户和账户分组的数据访问操作
"""

import os
import datetime
from typing import List, Optional, Dict, Tuple, Any

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import func, and_, or_, not_, desc, asc, select
from sqlalchemy.future import select

from data.models.account import AccountModel, AccountGroupModel
from utils.logger import get_logger

from data.database import get_connection, get_session

class AccountRepository:
    """账户数据仓库"""
    
    def __init__(self, session: AsyncSession=None):
        """初始化，使用传入的数据库会话"""
        self._session = session
        self._logger = get_logger("data.repositories.account")
    
    
    # ==================== 账户分组操作 ====================
    
    async def create_group(self, name: str, description: str = None) -> Optional[AccountGroupModel]:
        """创建账户分组
        
        Args:
            name: 分组名称
            description: 分组描述
            
        Returns:
            成功则返回创建的分组对象，否则返回None
        """
        # 检查名称是否已存在
        stmt = select(AccountGroupModel).where(AccountGroupModel.name == name)
        result = await self._session.execute(stmt)
        existing = result.scalars().first()
        
        if existing:
            self._logger.warning(f"分组名称已存在: {name}")
            return None
        
        # 创建新分组
        group = AccountGroupModel(name=name, description=description)
        self._session.add(group)
        self._logger.info(f"创建账户分组成功: {name}")
        return group
    
    async def get_group(self, group_id: int) -> Optional[AccountGroupModel]:
        """获取指定ID的账户分组
        
        Args:
            group_id: 分组ID
            
        Returns:
            成功则返回分组对象，不存在则返回None
        """
        stmt = select(AccountGroupModel).where(AccountGroupModel.id == group_id)
        result = await self._session.execute(stmt)
        return result.scalars().first()
    
    async def get_group_by_name(self, name: str) -> Optional[AccountGroupModel]:
        """根据名称获取账户分组
        
        Args:
            name: 分组名称
            
        Returns:
            成功则返回分组对象，不存在则返回None
        """
        stmt = select(AccountGroupModel).where(AccountGroupModel.name == name)
        result = await self._session.execute(stmt)
        return result.scalars().first()
    
    async def get_all_groups(self) -> List[AccountGroupModel]:
        """获取所有账户分组
        
        Returns:
            所有分组对象的列表
        """
        stmt = select(AccountGroupModel).order_by(AccountGroupModel.name)
        result = await self._session.execute(stmt)
        return result.scalars().all()
    
    async def update_group(self, group_id: int, name: str = None, description: str = None) -> bool:
        """更新账户分组
        
        Args:
            group_id: 分组ID
            name: 新的分组名称，为None则不更新
            description: 新的分组描述，为None则不更新
            
        Returns:
            操作是否成功
        """
        stmt = select(AccountGroupModel).where(AccountGroupModel.id == group_id)
        result = await self._session.execute(stmt)
        group = result.scalars().first()
        
        if not group:
            self._logger.warning(f"更新账户分组失败，ID不存在: {group_id}")
            return False
        
        # 更新字段
        if name is not None:
            # 检查名称是否重复
            stmt = select(AccountGroupModel).where(
                AccountGroupModel.name == name,
                AccountGroupModel.id != group_id
            )
            result = await self._session.execute(stmt)
            existing = result.scalars().first()
            
            if existing:
                self._logger.warning(f"分组名称已存在: {name}")
                return False
            group.name = name
        
        if description is not None:
            group.description = description
        
        self._logger.info(f"更新账户分组成功: {group_id}")
        return True
    
    async def delete_group(self, group_id: int, reassign_to: int = None) -> bool:
        """删除账户分组
        
        Args:
            group_id: 要删除的分组ID
            reassign_to: 可选的重新分配的分组ID，为None则将账户移出分组
            
        Returns:
            操作是否成功
        """
        # 获取要删除的分组
        stmt = select(AccountGroupModel).where(AccountGroupModel.id == group_id)
        result = await self._session.execute(stmt)
        group = result.scalars().first()
        
        if not group:
            self._logger.warning(f"删除账户分组失败，ID不存在: {group_id}")
            return False
        
        # 处理分组内的账户
        if reassign_to is not None:
            # 确认目标分组存在
            stmt = select(AccountGroupModel).where(AccountGroupModel.id == reassign_to)
            result = await self._session.execute(stmt)
            target_group = result.scalars().first()
            
            if not target_group:
                self._logger.warning(f"目标分组不存在，无法重新分配: {reassign_to}")
                return False
            
            # 更新账户的分组
            stmt = select(AccountModel).where(AccountModel.group_id == group_id)
            result = await self._session.execute(stmt)
            accounts = result.scalars().all()
            
            for account in accounts:
                account.group_id = reassign_to
            
            self._logger.info(f"将分组 {group_id} 中的账户重新分配到分组 {reassign_to}")
        else:
            # 将账户移出分组
            stmt = select(AccountModel).where(AccountModel.group_id == group_id)
            result = await self._session.execute(stmt)
            accounts = result.scalars().all()
            
            for account in accounts:
                account.group_id = None
            
            self._logger.info(f"将分组 {group_id} 中的账户移出分组")
        
        # 删除分组
        await self._session.delete(group)
        self._logger.info(f"删除账户分组成功: {group_id}")
        return True
    
    async def get_groups_with_account_count(self) -> List[Dict[str, Any]]:
        """获取所有分组及其账户数量
        
        Returns:
            包含分组信息和账户数量的字典列表
        """
        # 获取所有分组
        stmt = select(AccountGroupModel).order_by(AccountGroupModel.name)
        result = await self._session.execute(stmt)
        groups = result.scalars().all()
        
        # 获取每个分组的账户数量
        result_list = []
        for group in groups:
            # 统计该分组下的账户数量
            stmt = select(func.count(AccountModel.id)).where(AccountModel.group_id == group.id)
            count_result = await self._session.execute(stmt)
            count = count_result.scalar() or 0
            
            # 构建结果字典
            result_list.append({
                'id': group.id,
                'name': group.name,
                'description': group.description,
                'account_count': count
            })
        
        return result_list
    
    # ==================== 账户操作 ====================
    
    async def create_account(self, 
                     phone: str, 
                     session_file: str, 
                     first_name: str = None, 
                     last_name: str = None,
                     username: str = None,
                     bio: str = None,
                     profile_photo: str = None,
                     is_active: bool = True,
                     is_connected: bool = False,
                     has_2fa: bool = False,
                     proxy_id: int = None,
                     proxy_type: str = None,
                     group_id: int = None,
                     last_connected: Optional[datetime.datetime] = None) -> Optional[AccountModel]:
        """创建新账户
        
        Args:
            phone: 手机号
            session_file: 会话文件路径
            first_name: 名字
            last_name: 姓氏
            username: 用户名
            bio: 个人简介
            profile_photo: 头像路径
            is_active: 是否活跃
            is_connected: 是否已连接
            has_2fa: 是否启用了两步验证
            proxy_id: 代理ID
            proxy_type: 代理类型 ('ip_pool', 'system', 'none')
            group_id: 分组ID
            last_connected: 最后连接时间
            
        Returns:
            成功则返回创建的账户对象，否则返回None
        """
        # 检查手机号是否已存在
        stmt = select(AccountModel).where(AccountModel.phone == phone)
        result = await self._session.execute(stmt)
        existing = result.scalars().first()
        
        if existing:
            self._logger.warning(f"手机号已存在: {phone}")
            return None
        
        # 创建新账户
        now = datetime.datetime.now()  # 使用datetime对象而非字符串
        account = AccountModel(
            phone=phone,
            session_file=session_file,
            first_name=first_name,
            last_name=last_name,
            username=username,
            bio=bio,
            profile_photo=profile_photo,
            is_active=is_active,
            is_connected=is_connected,
            has_2fa=has_2fa,
            created_at=now,
            updated_at=now,
            last_connected=last_connected,
            proxy_id=proxy_id,
            proxy_type=proxy_type,
            group_id=group_id
        )
        
        self._session.add(account)
        self._logger.info(f"创建账户成功: {phone}, 代理类型: {proxy_type}")
        return account
    
    async def get_account(self, account_id: int) -> Optional[AccountModel]:
        """获取指定ID的账户
        
        Args:
            account_id: 账户ID
            
        Returns:
            成功则返回账户对象，不存在则返回None
        """
        stmt = select(AccountModel).where(AccountModel.id == account_id)
        result = await self._session.execute(stmt)
        return result.scalars().first()
    
    async def get_account_by_phone(self, phone: str) -> Optional[AccountModel]:
        """根据手机号获取账户

        Args:
            phone: 手机号

        Returns:
            账户对象，如果不存在则返回None
        """
        stmt = select(AccountModel).where(AccountModel.phone == phone)
        result = await self._session.execute(stmt)
        return result.scalars().first()

    async def get_account_by_username(self, username: str) -> Optional[AccountModel]:
        """根据用户名获取账户

        Args:
            username: 用户名

        Returns:
            账户对象，如果不存在则返回None
        """
        if not username:
            return None

        stmt = select(AccountModel).where(AccountModel.username == username)
        result = await self._session.execute(stmt)
        return result.scalars().first()
    
    async def get_all_accounts(self, active_only: bool = False) -> List[AccountModel]:
        """获取所有账户
        
        Args:
            active_only: 是否只返回活跃账户
            
        Returns:
            账户对象列表
        """
        try:
            if active_only:
                stmt = select(AccountModel).where(AccountModel.is_active == True)
            else:
                stmt = select(AccountModel)
            
            result = await self._session.execute(stmt)
            return result.scalars().all()
        except Exception as e:
            self._logger.error(f"获取所有账户失败: {e}")
            return []
    
    async def get_accounts_by_group(self, group_id: int) -> List[AccountModel]:
        """获取指定分组的所有账户
        
        Args:
            group_id: 分组ID
            
        Returns:
            账户对象列表
        """
        stmt = select(AccountModel).where(AccountModel.group_id == group_id)
        result = await self._session.execute(stmt)
        return result.scalars().all()
    
    async def update_account(self, account_id: int, **kwargs) -> bool:
        """更新账户信息
        
        Args:
            account_id: 账户ID
            **kwargs: 要更新的字段
            
        Returns:
            操作是否成功
        """
        # 获取账户
        stmt = select(AccountModel).where(AccountModel.id == account_id)
        result = await self._session.execute(stmt)
        account = result.scalars().first()
        
        if not account:
            self._logger.warning(f"更新账户失败，ID不存在: {account_id}")
            return False
        
        # 更新字段
        for key, value in kwargs.items():
            if hasattr(account, key):
                setattr(account, key, value)
        
        # 更新时间戳
        # if 'is_connected' in kwargs and kwargs['is_connected']:
        #     account.last_connected = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        
        self._logger.info(f"更新账户成功: {account_id}")
        return True
    
    async def update_accounts_profile(self, account_ids: List[int], **kwargs) -> Tuple[int, int]:
        """批量更新账户资料
        
        Args:
            account_ids: 账户ID列表
            **kwargs: 要更新的资料字段
            
        Returns:
            (成功数量, 失败数量)
        """
        success_count = 0
        fail_count = 0
        
        # 过滤有效字段
        valid_fields = {
            'first_name', 'last_name', 'username', 'bio', 'profile_photo',
            'is_active', 'proxy_id', 'proxy_type', 'group_id'
        }
        
        update_data = {k: v for k, v in kwargs.items() if k in valid_fields}
        if not update_data:
            return 0, len(account_ids)
        
        # 逐个更新账户
        for account_id in account_ids:
            # 获取账户
            stmt = select(AccountModel).where(AccountModel.id == account_id)
            result = await self._session.execute(stmt)
            account = result.scalars().first()
            
            if not account:
                fail_count += 1
                continue
            
            # 更新字段
            for field, value in update_data.items():
                setattr(account, field, value)
            
            success_count += 1
        
        return success_count, fail_count
    
    async def delete_account(self, account_id: int, delete_session_file: bool = True) -> Tuple[bool, Optional[str]]:
        """删除账户
        
        Args:
            account_id: 账户ID
            delete_session_file: 是否同时删除session文件
            
        Returns:
            (操作是否成功, 如果需要删除会话文件则返回文件路径，否则返回None)
        """
        # 获取账户
        stmt = select(AccountModel).where(AccountModel.id == account_id)
        result = await self._session.execute(stmt)
        account = result.scalars().first()
        
        if not account:
            self._logger.warning(f"删除账户失败, ID不存在: {account_id}")
            return False, None
        
        session_file = None
        
        # 如果需要删除session文件，获取文件路径
        if delete_session_file and account.session_file and os.path.exists(account.session_file):
            session_file = account.session_file
        
        # 删除账户
        await self._session.delete(account)
        self._logger.info(f"删除账户成功: ID={account_id}")
        return True, session_file
    
    async def delete_accounts(self, account_ids: List[int], delete_session: bool = True) -> List[Tuple[int, Optional[str]]]:
        """批量删除账户
        
        Args:
            account_ids: 账户ID列表
            delete_session: 是否同时删除session文件
            
        Returns:
            成功删除的账户ID和会话文件路径列表 [(account_id, session_file_path)]
        """
        result_list = []
        
        for account_id in account_ids:
            success, session_file = await self.delete_account(account_id, delete_session)
            if success:
                result_list.append((account_id, session_file))
        
        return result_list
    
    async def update_account_by_phone(self, phone: str, **kwargs) -> bool:
        """根据手机号更新账户信息
        
        Args:
            phone: 手机号
            **kwargs: 要更新的字段
            
        Returns:
            是否更新成功
        """
        try:
            stmt = select(AccountModel).where(AccountModel.phone == phone)
            result = await self._session.execute(stmt)
            account = result.scalars().first()
            
            if not account:
                return False
                
            for key, value in kwargs.items():
                if hasattr(account, key):
                    setattr(account, key, value)
            
            return True
        except Exception as e:
            self._logger.error(f"更新账户信息异常: {e}")
            return False
    

