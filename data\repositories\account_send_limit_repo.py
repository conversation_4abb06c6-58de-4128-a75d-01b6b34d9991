#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
AccountSendLimitRepository
账户每日发信限制仓储，负责AccountSendLimit模型的增删查改
"""
from typing import Optional
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from data.models.account_limit import AccountSendLimit

class AccountSendLimitRepository:
    def __init__(self, session: AsyncSession):
        self.session = session

    async def get_by_phone(self, account_phone: str) -> Optional[AccountSendLimit]:
        result = await self.session.execute(
            select(AccountSendLimit).where(AccountSendLimit.account_phone == account_phone)
        )
        return result.scalar_one_or_none()

    async def create(self, account_phone: str, max_daily_limit: int = 30) -> AccountSendLimit:
        from datetime import date, datetime
        limit = AccountSendLimit(
            account_phone=account_phone,
            last_reset_date=date.today(),
            current_day_count=0,
            max_daily_limit=max_daily_limit,
            created_at=datetime.now(),
            updated_at=datetime.now()
        )
        self.session.add(limit)
        await self.session.flush()
        return limit

    async def update_count(self, account_phone: str, increment: int = 1) -> AccountSendLimit:
        from datetime import date, datetime
        limit = await self.get_by_phone(account_phone)
        today = date.today()
        if not limit:
            limit = await self.create(account_phone)
        if limit.last_reset_date < today:
            limit.last_reset_date = today
            limit.current_day_count = increment
        else:
            limit.current_day_count += increment
        if limit.current_day_count > limit.max_daily_limit:
            limit.current_day_count = limit.max_daily_limit
        limit.updated_at = datetime.now()
        await self.session.flush()
        return limit

    async def reset_count(self, account_phone: str) -> AccountSendLimit:
        from datetime import date, datetime
        limit = await self.get_by_phone(account_phone)
        today = date.today()
        if not limit:
            limit = await self.create(account_phone)
        limit.last_reset_date = today
        limit.current_day_count = 0
        limit.updated_at = datetime.now()
        await self.session.flush()
        return limit

    async def set_max_limit(self, account_phone: str, max_limit: int) -> AccountSendLimit:
        from datetime import datetime, date
        limit = await self.get_by_phone(account_phone)
        today = date.today()
        if not limit:
            limit = await self.create(account_phone, max_daily_limit=max_limit)
        else:
            limit.max_daily_limit = max_limit
            limit.updated_at = datetime.now()
        await self.session.flush()
        return limit 
