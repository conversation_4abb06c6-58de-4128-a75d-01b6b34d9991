#!/usr/bin/python3
# -*- coding: utf-8 -*-

"""
聊天数据仓库
提供对Telegram聊天、群组和频道的数据访问操作
"""

import datetime
from typing import List, Optional, Dict, Tuple, Any

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import func, and_, or_, not_, desc, asc, select
from sqlalchemy.future import select

from data.models.chat import ChatModel
from utils.logger import get_logger


class ChatRepository:
    """聊天数据仓库，提供对聊天数据的增删改查操作"""
    
    def __init__(self, session: AsyncSession=None):
        """初始化聊天数据仓库
        
        Args:
            session: 数据库会话，通过外部传入
        """
        self._session = session
        self._logger = get_logger("data.repositories.chat")
    
    async def create_chat(self, 
                    phone: str,
                    chat_id: int,
                    access_hash: int = None,
                    chat_type: str = None,
                    title: str = None,
                    username: str = None,
                    participants_count: int = None,
                    is_creator: bool = False,
                    is_admin: bool = False,
                    is_member: bool = True,
                    is_user_monitored: bool = False,
                    is_keyword_monitored: bool = False) -> Optional[ChatModel]:
        """创建或更新聊天信息
        
        Args:
            phone: Telegram账户手机号
            chat_id: 聊天ID
            access_hash: 访问哈希值
            chat_type: 聊天类型 ('group', 'supergroup', 'channel')
            title: 标题或名称
            username: 用户名
            participants_count: 参与者数量
            is_creator: 是否为创建者
            is_admin: 是否为管理员
            is_member: 用户是否仍在群组中
            is_user_monitored: 是否监控用户
            is_keyword_monitored: 是否监控关键词
            
        Returns:
            成功则返回创建的聊天对象，否则返回None
        """
        # 检查该账户下是否已存在此聊天
        stmt = select(ChatModel).where(
            ChatModel.phone == phone,
            ChatModel.chat_id == chat_id
        )
        result = await self._session.execute(stmt)
        existing = result.scalars().first()
        
        now = datetime.datetime.utcnow()
        
        if existing:
            # 更新已有记录
            existing.access_hash = access_hash if access_hash is not None else existing.access_hash
            existing.chat_type = chat_type if chat_type is not None else existing.chat_type
            existing.title = title if title is not None else existing.title
            existing.username = username if username is not None else existing.username
            existing.participants_count = participants_count if participants_count is not None else existing.participants_count
            existing.is_creator = is_creator
            existing.is_admin = is_admin
            existing.is_member = is_member
            existing.is_user_monitored = is_user_monitored
            existing.is_keyword_monitored = is_keyword_monitored
            existing.last_synced_at = now
            existing.updated_at = now
            
            self._logger.info(f"更新聊天信息: {phone} - {chat_id}")
            return existing
        
        # 创建新聊天记录
        chat = ChatModel(
            phone=phone,
            chat_id=chat_id,
            access_hash=access_hash,
            chat_type=chat_type,
            title=title,
            username=username,
            participants_count=participants_count,
            is_creator=is_creator,
            is_admin=is_admin,
            is_member=is_member,
            is_user_monitored=is_user_monitored,
            is_keyword_monitored=is_keyword_monitored,
            last_synced_at=now,
            created_at=now,
            updated_at=now
        )
        
        self._session.add(chat)
        self._logger.info(f"创建聊天记录: {phone} - {chat_id}")
        return chat
    
    async def get_chat(self, phone: str, chat_id: int) -> Optional[ChatModel]:
        """获取指定的聊天记录
        
        Args:
            phone: Telegram账户手机号
            chat_id: 聊天ID
            
        Returns:
            成功则返回聊天对象，不存在则返回None
        """
        stmt = select(ChatModel).where(
            ChatModel.phone == phone,
            ChatModel.chat_id == chat_id
        )
        result = await self._session.execute(stmt)
        return result.scalars().first()
    
    async def get_chats_by_account(self, phone: str, chat_type: str = None, is_member_only: bool = False) -> List[ChatModel]:
        """获取账户的所有聊天
        
        Args:
            phone: Telegram账户手机号
            chat_type: 可选的聊天类型过滤 ('group', 'supergroup', 'channel')
            is_member_only: 是否只返回仍然是成员的聊天
            
        Returns:
            聊天对象列表
        """
        conditions = [ChatModel.phone == phone]
        
        if chat_type:
            conditions.append(ChatModel.chat_type == chat_type)
        
        if is_member_only:
            conditions.append(ChatModel.is_member == True)
        
        stmt = select(ChatModel).where(and_(*conditions))
        result = await self._session.execute(stmt)
        return result.scalars().all()
    
    async def get_all_chats(self, is_member_only: bool = False) -> List[ChatModel]:
        """获取所有聊天记录
        
        Args:
            is_member_only: 是否只返回仍然是成员的聊天
            
        Returns:
            所有聊天对象列表
        """
        if is_member_only:
            stmt = select(ChatModel).where(ChatModel.is_member == True)
        else:
            stmt = select(ChatModel)
            
        result = await self._session.execute(stmt)
        return result.scalars().all()
    
    async def search_chats(self, 
                     phone: str = None, 
                     keyword: str = None, 
                     chat_type: str = None,
                     is_member_only: bool = False) -> List[ChatModel]:
        """搜索聊天
        
        Args:
            phone: 可选的账户手机号过滤
            keyword: 搜索关键词（标题或用户名）
            chat_type: 可选的聊天类型过滤
            is_member_only: 是否只返回仍然是成员的聊天
            
        Returns:
            匹配的聊天对象列表
        """
        conditions = []
        
        if phone:
            conditions.append(ChatModel.phone == phone)
        
        if keyword:
            conditions.append(or_(
                ChatModel.title.ilike(f'%{keyword}%'),
                ChatModel.username.ilike(f'%{keyword}%')
            ))
        
        if chat_type:
            conditions.append(ChatModel.chat_type == chat_type)
            
        if is_member_only:
            conditions.append(ChatModel.is_member == True)
        
        stmt = select(ChatModel)
        if conditions:
            stmt = stmt.where(and_(*conditions))
        
        result = await self._session.execute(stmt)
        return result.scalars().all()
    
    async def update_chat(self, 
                    phone: str, 
                    chat_id: int, 
                    **kwargs) -> bool:
        """更新聊天信息
        
        Args:
            phone: Telegram账户手机号
            chat_id: 聊天ID
            **kwargs: 要更新的字段，可包含以下字段：
                      access_hash, chat_type, title, username, participants_count, 
                      is_creator, is_admin, is_member, is_user_monitored, is_keyword_monitored
            
        Returns:
            操作是否成功
        """
        # 获取聊天记录
        stmt = select(ChatModel).where(
            ChatModel.phone == phone,
            ChatModel.chat_id == chat_id
        )
        result = await self._session.execute(stmt)
        chat = result.scalars().first()
        
        if not chat:
            self._logger.warning(f"更新聊天失败，记录不存在: {phone} - {chat_id}")
            return False
        
        # 更新字段
        for key, value in kwargs.items():
            if hasattr(chat, key):
                setattr(chat, key, value)
        
        # 更新同步时间
        chat.last_synced_at = datetime.datetime.utcnow()
        
        self._logger.info(f"更新聊天信息成功: {phone} - {chat_id}")
        return True
    
    async def update_membership_status(self, phone: str, chat_id: int, is_member: bool) -> bool:
        """更新用户在群组中的成员状态
        
        Args:
            phone: Telegram账户手机号
            chat_id: 聊天ID
            is_member: 是否仍是群组成员
            
        Returns:
            操作是否成功
        """
        # 获取聊天记录
        stmt = select(ChatModel).where(
            ChatModel.phone == phone,
            ChatModel.chat_id == chat_id
        )
        result = await self._session.execute(stmt)
        chat = result.scalars().first()
        
        if not chat:
            self._logger.warning(f"更新成员状态失败，记录不存在: {phone} - {chat_id}")
            return False
        
        # 更新成员状态
        chat.is_member = is_member
        chat.last_synced_at = datetime.datetime.utcnow()
        
        self._logger.info(f"更新成员状态: {phone} - {chat_id}, 是否为成员: {is_member}")
        return True
    
    async def delete_chat(self, phone: str, chat_id: int) -> bool:
        """删除聊天记录
        
        Args:
            phone: Telegram账户手机号
            chat_id: 聊天ID
            
        Returns:
            操作是否成功
        """
        # 获取聊天记录
        stmt = select(ChatModel).where(
            ChatModel.phone == phone,
            ChatModel.chat_id == chat_id
        )
        result = await self._session.execute(stmt)
        chat = result.scalars().first()
        
        if not chat:
            self._logger.warning(f"删除聊天失败，记录不存在: {phone} - {chat_id}")
            return False
        
        # 删除聊天记录
        await self._session.delete(chat)
        self._logger.info(f"删除聊天记录成功: {phone} - {chat_id}")
        return True
    
    async def delete_chats_by_account(self, phone: str) -> int:
        """删除账户的所有聊天记录
        
        Args:
            phone: Telegram账户手机号
            
        Returns:
            删除的记录数量
        """
        # 获取该账户的所有聊天记录
        stmt = select(ChatModel).where(
            ChatModel.phone == phone
        )
        result = await self._session.execute(stmt)
        chats = result.scalars().all()
        
        count = 0
        for chat in chats:
            await self._session.delete(chat)
            count += 1
        
        self._logger.info(f"删除账户 {phone} 的所有聊天记录: {count} 条")
        return count
    
    async def get_chat_count_by_account(self, phone: str, chat_type: str = None, is_member_only: bool = False) -> int:
        """获取账户的聊天数量
        
        Args:
            phone: Telegram账户手机号
            chat_type: 可选的聊天类型过滤
            is_member_only: 是否只计算仍是成员的聊天
            
        Returns:
            聊天数量
        """
        conditions = [ChatModel.phone == phone]
        
        if chat_type:
            conditions.append(ChatModel.chat_type == chat_type)
            
        if is_member_only:
            conditions.append(ChatModel.is_member == True)
        
        stmt = select(func.count()).where(and_(*conditions))
        result = await self._session.execute(stmt)
        return result.scalar() or 0
    
    # 监控相关方法
    async def set_monitor_status(self, 
                           phone: str, 
                           chat_id: int, 
                           is_user_monitored: bool = None, 
                           is_keyword_monitored: bool = None) -> bool:
        """设置聊天监控状态
        
        Args:
            phone: Telegram账户手机号
            chat_id: 聊天ID
            is_user_monitored: 用户监控开关
            is_keyword_monitored: 关键词监控开关
            
        Returns:
            操作是否成功
        """
        # 获取聊天记录
        stmt = select(ChatModel).where(
            ChatModel.phone == phone,
            ChatModel.chat_id == chat_id
        )
        result = await self._session.execute(stmt)
        chat = result.scalars().first()
        
        if not chat:
            self._logger.warning(f"设置监控状态失败，记录不存在: {phone} - {chat_id}")
            return False
        
        # 更新监控状态
        if is_user_monitored is not None:
            chat.is_user_monitored = is_user_monitored
        
        if is_keyword_monitored is not None:
            chat.is_keyword_monitored = is_keyword_monitored
        
        self._logger.info(f"更新监控状态: {phone} - {chat_id}, 用户监控:{chat.is_user_monitored}, 关键词监控:{chat.is_keyword_monitored}")
        return True
    
    async def get_user_monitored_chats(self, phone: str = None, is_member_only: bool = True) -> List[ChatModel]:
        """获取用户监控的聊天
        
        Args:
            phone: 可选的账户手机号过滤
            is_member_only: 是否只返回仍是成员的聊天
            
        Returns:
            用户监控的聊天列表
        """
        conditions = [ChatModel.is_user_monitored == True]
        
        if phone:
            conditions.append(ChatModel.phone == phone)
            
        if is_member_only:
            conditions.append(ChatModel.is_member == True)
        
        stmt = select(ChatModel).where(and_(*conditions))
        result = await self._session.execute(stmt)
        return result.scalars().all()
    
    async def get_keyword_monitored_chats(self, phone: str = None, is_member_only: bool = True) -> List[ChatModel]:
        """获取关键词监控的聊天
        
        Args:
            phone: 可选的账户手机号过滤
            is_member_only: 是否只返回仍是成员的聊天
            
        Returns:
            关键词监控的聊天列表
        """
        conditions = [ChatModel.is_keyword_monitored == True]
        
        if phone:
            conditions.append(ChatModel.phone == phone)
            
        if is_member_only:
            conditions.append(ChatModel.is_member == True)
        
        stmt = select(ChatModel).where(and_(*conditions))
        result = await self._session.execute(stmt)
        return result.scalars().all()
    
    async def get_all_monitored_chats(self, phone: str = None, is_member_only: bool = True) -> List[ChatModel]:
        """获取所有监控的聊天（用户或关键词）
        
        Args:
            phone: 可选的账户手机号过滤
            is_member_only: 是否只返回仍是成员的聊天
            
        Returns:
            所有被监控的聊天列表
        """
        conditions = [or_(
            ChatModel.is_user_monitored == True,
            ChatModel.is_keyword_monitored == True
        )]
        
        if phone:
            conditions.append(ChatModel.phone == phone)
            
        if is_member_only:
            conditions.append(ChatModel.is_member == True)
        
        stmt = select(ChatModel).where(and_(*conditions))
        result = await self._session.execute(stmt)
        return result.scalars().all()
        
    async def get_non_member_chats(self, phone: str = None) -> List[ChatModel]:
        """获取用户不再是成员的聊天列表
        
        Args:
            phone: 可选的账户手机号过滤
            
        Returns:
            用户不再是成员的聊天列表
        """
        conditions = [ChatModel.is_member == False]
        
        if phone:
            conditions.append(ChatModel.phone == phone)
        
        stmt = select(ChatModel).where(and_(*conditions))
        result = await self._session.execute(stmt)
        return result.scalars().all()
