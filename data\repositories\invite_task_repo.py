from typing import List, Dict, Optional, Any, Tuple
from sqlalchemy import select, update, delete, func
from sqlalchemy.ext.asyncio import AsyncSession
from datetime import datetime, date, timezone

from data.models.invite import InviteTask, InviteRecord, InviteLimit
from utils.logger import get_logger

class InviteTaskRepository:
    """邀请任务数据访问层"""
    
    def __init__(self, session: AsyncSession):
        self._session = session
        self._logger = get_logger("data.repositories.invite_task")
    
    async def create_task(self, task_data: Dict[str, Any]) -> InviteTask:
        """创建邀请任务"""
        try:
            task = InviteTask(
                task_name=task_data.get("task_name"),
                accounts=task_data.get("accounts", []),
                group_name=task_data.get("group_name"),
                group_invite_link=task_data.get("group_invite_link"),
                status=task_data.get("status", "pending"),
                message=task_data.get("message"),
                invite_interval_min=task_data.get("invite_interval_min", 60),
                invite_interval_max=task_data.get("invite_interval_max", 300),
                invite_type=task_data.get("invite_type", "custom"),
                batch_size_min=task_data.get("batch_size_min", 1),
                batch_size_max=task_data.get("batch_size_max", 5)
            )
            
            self._session.add(task)
            await self._session.flush()
            return task
        except Exception as e:
            self._logger.error(f"创建邀请任务失败: {str(e)}")
            raise
    
    async def get_task_by_id(self, task_id: int) -> Optional[InviteTask]:
        """根据ID获取任务"""
        try:
            result = await self._session.execute(
                select(InviteTask).where(InviteTask.id == task_id)
            )
            return result.scalar_one_or_none()
        except Exception as e:
            self._logger.error(f"获取任务失败, task_id={task_id}: {str(e)}")
            raise
    
    async def get_all_tasks(self, status: Optional[str] = None) -> List[InviteTask]:
        """获取所有任务，可按状态筛选"""
        try:
            query = select(InviteTask).order_by(InviteTask.created_at.desc())
            
            if status:
                query = query.where(InviteTask.status == status)
            
            result = await self._session.execute(query)
            return result.scalars().all()
        except Exception as e:
            self._logger.error(f"获取任务列表失败: {str(e)}")
            raise
    
    async def update_task(self, task_id: int, task_data: Dict[str, Any]) -> bool:
        """更新任务信息"""
        try:
            result = await self._session.execute(
                update(InviteTask)
                .where(InviteTask.id == task_id)
                .values(**task_data)
            )
            return result.rowcount > 0
        except Exception as e:
            self._logger.error(f"更新任务失败, task_id={task_id}: {str(e)}")
            raise
    
    async def update_task_status(self, task_id: int, status: str) -> bool:
        """更新任务状态"""
        try:
            result = await self._session.execute(
                update(InviteTask)
                .where(InviteTask.id == task_id)
                .values(status=status)
            )
            return result.rowcount > 0
        except Exception as e:
            self._logger.error(f"更新任务状态失败, task_id={task_id}: {str(e)}")
            raise
    
    async def delete_task(self, task_id: int) -> bool:
        """删除任务"""
        try:
            result = await self._session.execute(
                delete(InviteTask).where(InviteTask.id == task_id)
            )
            return result.rowcount > 0
        except Exception as e:
            self._logger.error(f"删除任务失败, task_id={task_id}: {str(e)}")
            raise
    

    

    
    # InviteRecord 相关操作
    
    async def create_invite_record(self, record_data: Dict[str, Any]) -> InviteRecord:
        """创建邀请记录"""
        try:
            record = InviteRecord(
                task_id=record_data.get("task_id"),
                invitee=record_data.get("invitee"), # 邀请人
                invitee_name=record_data.get("invitee_name"), # 邀请人名称
                status=record_data.get("status", "pending"), # 状态
                error_message=record_data.get("error_message") # 错误信息
            )
            
            self._session.add(record)
            await self._session.flush()
            return record
        except Exception as e:
            self._logger.error(f"创建邀请记录失败: {str(e)}")
            raise
    
    async def get_invite_records(self, task_id: int, status: Optional[str] = None) -> List[InviteRecord]:
        """获取任务的邀请记录"""
        try:
            query = select(InviteRecord).where(InviteRecord.task_id == task_id)
            
            if status:
                query = query.where(InviteRecord.status == status)
                
            query = query.order_by(InviteRecord.created_at.desc())
            
            result = await self._session.execute(query)
            return result.scalars().all()
        except Exception as e:
            self._logger.error(f"获取邀请记录失败, task_id={task_id}: {str(e)}")
            raise
    
    async def update_invite_record(self, record_id: int, status: str, error_message: Optional[str] = None) -> bool:
        """更新邀请记录状态"""
        try:
            update_data = {"status": status}
            if error_message is not None:
                update_data["error_message"] = error_message
            if status in ["success", "failed"]:
                update_data["processed_at"] = datetime.now()
                
            result = await self._session.execute(
                update(InviteRecord)
                .where(InviteRecord.id == record_id)
                .values(**update_data)
            )
            return result.rowcount > 0
        except Exception as e:
            self._logger.error(f"更新邀请记录失败, record_id={record_id}: {str(e)}")
            raise
    
    async def get_invite_record(self, task_id: int, invitee: str) -> Optional[InviteRecord]:
        """根据任务ID和被邀请人查找单条邀请记录"""
        try:
            result = await self._session.execute(
                select(InviteRecord).where(
                    InviteRecord.task_id == task_id,
                    InviteRecord.invitee == invitee
                )
            )
            return result.scalar_one_or_none()
        except Exception as e:
            self._logger.error(f"获取邀请记录失败, task_id={task_id}, invitee={invitee}: {str(e)}")
            raise

    async def update_invite_record_by_task_and_invitee(self, task_id: int, invitee: str, status: str, error_message: Optional[str] = None) -> bool:
        """根据任务ID和被邀请人更新邀请记录状态和错误信息"""
        try:
            update_data = {"status": status}
            if error_message is not None:
                update_data["error_message"] = error_message
            if status in ["success", "failed"]:
                update_data["processed_at"] = datetime.now()
            result = await self._session.execute(
                update(InviteRecord)
                .where(
                    InviteRecord.task_id == task_id,
                    InviteRecord.invitee == invitee
                )
                .values(**update_data)
            )
            return result.rowcount > 0
        except Exception as e:
            self._logger.error(f"更新邀请记录失败, task_id={task_id}, invitee={invitee}: {str(e)}")
            raise
    
    # InviteLimit 相关操作
    
    async def get_invite_limit(self, account_phone: str) -> Optional[InviteLimit]:
        """获取账户邀请限制"""
        try:
            result = await self._session.execute(
                select(InviteLimit).where(InviteLimit.account_phone == account_phone)
            )
            return result.scalar_one_or_none()
        except Exception as e:
            self._logger.error(f"获取账户邀请限制失败, account_phone={account_phone}: {str(e)}")
            raise
    
    async def create_or_update_invite_limit(self, account_phone: str, max_daily_limit: int = 20) -> InviteLimit:
        """创建或更新账户邀请限制"""
        try:
            # 先查询是否存在
            limit = await self.get_invite_limit(account_phone)
            
            today = date.today()
            
            if limit:
                # 如果存在且日期不是今天，重置计数
                if limit.last_reset_date != today:
                    await self._session.execute(
                        update(InviteLimit)
                        .where(InviteLimit.account_phone == account_phone)
                        .values(
                            last_reset_date=today,
                            current_day_count=0,
                            max_daily_limit=max_daily_limit
                        )
                    )
                # 如果只需要更新最大限制
                elif limit.max_daily_limit != max_daily_limit:
                    await self._session.execute(
                        update(InviteLimit)
                        .where(InviteLimit.account_phone == account_phone)
                        .values(max_daily_limit=max_daily_limit)
                    )
                
                # 重新查询获取最新数据
                result = await self._session.execute(
                    select(InviteLimit).where(InviteLimit.account_phone == account_phone)
                )
                return result.scalar_one()
            else:
                # 不存在则创建
                new_limit = InviteLimit(
                    account_phone=account_phone,
                    last_reset_date=today,
                    current_day_count=0,
                    max_daily_limit=max_daily_limit
                )
                self._session.add(new_limit)
                await self._session.flush()
                return new_limit
        except Exception as e:
            self._logger.error(f"创建或更新账户邀请限制失败, account_phone={account_phone}: {str(e)}")
            raise
    
    async def increment_invite_count(self, account_phone: str, count: int = 1) -> Tuple[bool, int]:
        """增加账户邀请计数，返回是否成功和剩余可邀请次数"""
        try:
            # 获取当前限制
            limit = await self.get_invite_limit(account_phone)
            
            if not limit:
                # 如果不存在，创建新记录
                limit = await self.create_or_update_invite_limit(account_phone)
            
            today = date.today()
            
            # 如果日期不是今天，重置计数
            if limit.last_reset_date != today:
                await self._session.execute(
                    update(InviteLimit)
                    .where(InviteLimit.account_phone == account_phone)
                    .values(
                        last_reset_date=today,
                        current_day_count=count
                    )
                )
                remaining = limit.max_daily_limit - count
                return True, remaining
            
            # 检查是否超出限制
            if limit.current_day_count + count > limit.max_daily_limit:
                remaining = limit.max_daily_limit - limit.current_day_count
                return False, remaining
            
            # 增加计数
            await self._session.execute(
                update(InviteLimit)
                .where(InviteLimit.account_phone == account_phone)
                .values(current_day_count=limit.current_day_count + count)
            )
            
            remaining = limit.max_daily_limit - (limit.current_day_count + count)
            return True, remaining
        except Exception as e:
            self._logger.error(f"增加账户邀请计数失败, account_phone={account_phone}: {str(e)}")
            raise
    
    async def get_task_statistics(self, task_id: int) -> Dict[str, int]:
        """获取任务统计数据"""
        try:
            # 邀请记录状态统计
            record_stats_query = select(
                InviteRecord.status,
                func.count(InviteRecord.id).label("count")
            ).where(
                InviteRecord.task_id == task_id
            ).group_by(
                InviteRecord.status
            )
            
            result = await self._session.execute(record_stats_query)
            record_stats = {row[0]: row[1] for row in result}
            
            # 默认值
            stats = {
                "total": 0,
                "pending": 0,
                "success": 0,
                "failed": 0
            }
            
            # 更新统计
            stats["pending"] = record_stats.get("pending", 0)
            stats["success"] = record_stats.get("success", 0)
            stats["failed"] = record_stats.get("failed", 0)
            stats["total"] = stats["pending"] + stats["success"] + stats["failed"]
            
            return stats
        except Exception as e:
            self._logger.error(f"获取任务统计失败, task_id={task_id}: {str(e)}")
            raise

    async def get_pending_invitees(self, task_id: int, limit: int = 100) -> list:
        """批量获取待邀请用户（status='pending'）"""
        try:
            query = select(InviteRecord).where(
                InviteRecord.task_id == task_id,
                InviteRecord.status == 'pending'
            ).order_by(InviteRecord.created_at.asc()).limit(limit)
            result = await self._session.execute(query)
            return result.scalars().all()
        except Exception as e:
            self._logger.error(f"批量获取待邀请用户失败, task_id={task_id}: {str(e)}")
            raise

    async def delete_invite_records_by_task(self, task_id: int) -> int:
        """删除某任务下所有邀请记录，返回删除数量"""
        try:
            result = await self._session.execute(
                delete(InviteRecord).where(InviteRecord.task_id == task_id)
            )
            return result.rowcount
        except Exception as e:
            self._logger.error(f"删除邀请记录失败, task_id={task_id}: {str(e)}")
            raise
