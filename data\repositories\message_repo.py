from typing import List, Optional, Dict, Any
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy import func
from datetime import datetime, timezone
from data.models.message import MessageTask, MessageTaskTargetLog, MessageTaskStatus
from utils.logger import get_logger

class MessageTaskRepository:
    def __init__(self, session: AsyncSession):
        self._session = session
        self._logger = get_logger("data.repositories.message_task")

    async def _flush(self):
        try:
            await self._session.flush()
        except Exception as e:
            self._logger.error(f"数据库刷新失败: {e}", exc_info=True)
            raise

    # --- MessageTask Methods ---
    async def add_task(self, task_data: Dict[str, Any]) -> MessageTask:
        # 确保新增的时间相关字段有默认值
        if 'account_switch_min' not in task_data:
            task_data['account_switch_min'] = 60
        if 'account_switch_max' not in task_data:
            task_data['account_switch_max'] = 300
        if 'message_interval_min' not in task_data:
            task_data['message_interval_min'] = 5
        if 'message_interval_max' not in task_data:
            task_data['message_interval_max'] = 30
            
        task = MessageTask(**task_data)
        self._session.add(task)
        await self._flush()
        self._logger.info(f"消息任务已添加, ID: {task.id}, 名称: {task.task_name}, 账户切换时间: {task.account_switch_min}-{task.account_switch_max}秒, 消息间隔: {task.message_interval_min}-{task.message_interval_max}秒")
        return task

    async def get_task_by_id(self, task_id: int) -> Optional[MessageTask]:
        try:
            stmt = select(MessageTask).where(MessageTask.id == task_id)
            result = await self._session.execute(stmt)
            return result.scalars().first()
        except Exception as e:
            self._logger.error(f"获取任务 {task_id} 失败: {e}", exc_info=True)
            return None

    async def get_all_tasks(self, page: int = 1, per_page: int = 20) -> List[MessageTask]:
        try:
            stmt = select(MessageTask).order_by(MessageTask.created_at.desc()).limit(per_page).offset((page - 1) * per_page)
            result = await self._session.execute(stmt)
            return result.scalars().all()
        except Exception as e:
            self._logger.error(f"获取任务列表失败: {e}", exc_info=True)
            return []

    async def update_task(self, task_id: int, update_data: Dict[str, Any]) -> Optional[MessageTask]:
        task = await self.get_task_by_id(task_id)
        if task:
            for key, value in update_data.items():
                setattr(task, key, value)
            task.updated_at = datetime.now()
            await self._flush()
            self._logger.info(f"消息任务已更新, ID: {task_id}, 账户切换时间: {task.account_switch_min}-{task.account_switch_max}秒, 消息间隔: {task.message_interval_min}-{task.message_interval_max}秒")
            return task
        return None

    async def delete_task(self, task_id: int) -> bool:
        task = await self.get_task_by_id(task_id)
        if task:
            await self._session.delete(task)
            await self._flush()
            self._logger.info(f"消息任务已删除, ID: {task_id}")
            return True
        return False

    # --- MessageTaskTargetLog Methods ---
    async def add_target_log(self, log_data: Dict[str, Any]) -> MessageTaskTargetLog:
        log = MessageTaskTargetLog(**log_data)
        self._session.add(log)
        await self._flush()
        #self._logger.info(f"添加目标日志: 任务{log.task_id} 目标{log.target_id}")
        return log

    async def get_target_logs(self, task_id: int, status: Optional[str] = None) -> List[MessageTaskTargetLog]:
        try:
            stmt = select(MessageTaskTargetLog).where(MessageTaskTargetLog.task_id == task_id)
            if status:
                # 支持传入枚举或字符串
                if isinstance(status, MessageTaskStatus):
                    status_value = status.value
                else:
                    status_value = status
                stmt = stmt.where(MessageTaskTargetLog.status == status_value)
            result = await self._session.execute(stmt)
            return result.scalars().all()
        except Exception as e:
            self._logger.error(f"获取任务{task_id}目标日志失败: {e}", exc_info=True)
            return []

    async def update_target_log_status(self, task_id: int, target_id: str, status: str, error_message: Optional[str] = None) -> bool:
        try:
            stmt = select(MessageTaskTargetLog).where(
                MessageTaskTargetLog.task_id == task_id,
                MessageTaskTargetLog.target_id == target_id
            )
            result = await self._session.execute(stmt)
            log = result.scalars().first()
            if log:
                # 支持传入枚举或字符串
                if isinstance(status, MessageTaskStatus):
                    log.status = status.value
                else:
                    log.status = status
                log.error_message = error_message
                log.processed_at = datetime.now()
                log.updated_at = datetime.now()
                await self._flush()
                self._logger.info(f"更新目标日志状态: 任务{task_id} 目标{target_id} 状态{log.status}")
                return True
            return False
        except Exception as e:
            self._logger.error(f"更新目标日志状态失败: {e}", exc_info=True)
            return False

    async def delete_target_logs_by_task(self, task_id: int) -> int:
        try:
            stmt = select(MessageTaskTargetLog).where(MessageTaskTargetLog.task_id == task_id)
            result = await self._session.execute(stmt)
            logs = result.scalars().all()
            count = 0
            for log in logs:
                await self._session.delete(log)
                count += 1
            await self._flush()
            self._logger.info(f"删除任务{task_id}的所有目标日志，共{count}条")
            return count
        except Exception as e:
            self._logger.error(f"删除任务{task_id}目标日志失败: {e}", exc_info=True)
            return 0

    async def get_task_target_stats(self, task_id: int) -> Dict[str, int]:
        """高性能统计任务目标各状态数量，并统计今日发送成功数"""
        try:
            success_count = await self._session.scalar(
                select(func.count()).where(
                    MessageTaskTargetLog.task_id == task_id,
                    MessageTaskTargetLog.status == "success"
                )
            )
            failed_count = await self._session.scalar(
                select(func.count()).where(
                    MessageTaskTargetLog.task_id == task_id,
                    MessageTaskTargetLog.status == "failed"
                )
            )
            pending_count = await self._session.scalar(
                select(func.count()).where(
                    MessageTaskTargetLog.task_id == task_id,
                    MessageTaskTargetLog.status == "pending"
                )
            )
            total_count = await self._session.scalar(
                select(func.count()).where(
                    MessageTaskTargetLog.task_id == task_id
                )
            )
            # 今日成功发送数
            from datetime import datetime
            from sqlalchemy import and_
            today = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
            today_success = await self._session.scalar(
                select(func.count()).where(
                    MessageTaskTargetLog.task_id == task_id,
                    MessageTaskTargetLog.status == "success",
                    MessageTaskTargetLog.updated_at >= today
                )
            )
            return {
                "success": success_count or 0,
                "failed": failed_count or 0,
                "pending": pending_count or 0,
                "total": total_count or 0,
                "today_success": today_success or 0
            }
        except Exception as e:
            self._logger.error(f"获取任务{task_id}目标统计失败: {e}", exc_info=True)
            return {"success": 0, "failed": 0, "pending": 0, "total": 0, "today_success": 0}
