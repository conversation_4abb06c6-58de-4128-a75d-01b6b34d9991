"""
监控任务仓库模块
实现监控任务相关的数据库操作
"""
import json
import datetime
import math
from typing import List, Dict, Any, Optional, Tuple, Union
from sqlalchemy import select, delete, update, func, or_, and_, desc, Text
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy.orm import aliased

from utils.logger import get_logger
from data.models.monitor import MonitorTask, MonitorMessage, NotificationConfig


class MonitorTaskRepository:
    """
    监控任务仓库
    负责监控任务相关的数据库操作
    """
    
    def __init__(self, session: AsyncSession):
        """
        初始化监控任务仓库
        
        Args:
            session: 数据库会话
        """
        self._session = session
        self._logger = get_logger("data.repositories.monitor")
        
    async def get_all_tasks(self, search_term: Optional[str] = None) -> List[MonitorTask]:
        """
        获取所有监控任务，可根据名称搜索过滤
        
        Args:
            search_term: 可选的搜索关键词，根据名称匹配
            
        Returns:
            List[MonitorTask]: 任务列表
        """
        try:
            stmt = select(MonitorTask).order_by(MonitorTask.created_at.desc())
            
            # 如果提供了搜索关键词，添加过滤条件
            if search_term:
                stmt = stmt.filter(MonitorTask.name.ilike(f"%{search_term}%"))
                
            result = await self._session.execute(stmt)
            tasks = result.scalars().all()
            return list(tasks)
        except Exception as e:
            self._logger.error(f"获取所有任务失败: {str(e)}", exc_info=True)
            return []
            
    async def get_task_by_id(self, task_id: Union[int, str]) -> Optional[MonitorTask]:
        """
        根据ID获取任务
        
        Args:
            task_id: 任务ID
            
        Returns:
            Optional[MonitorTask]: 任务对象，未找到返回None
        """
        try:
            task_id = int(task_id)
            stmt = select(MonitorTask).filter(MonitorTask.id == task_id)
            result = await self._session.execute(stmt)
            task = result.scalars().first()
            return task
        except Exception as e:
            self._logger.error(f"获取任务 ID {task_id} 失败: {str(e)}", exc_info=True)
            return None
            
    async def find_task_by_group(self, group_id: str) -> Optional[MonitorTask]:
        """
        根据群组ID查找任务
        
        Args:
            group_id: 群组ID
            
        Returns:
            Optional[MonitorTask]: 任务对象，未找到返回None
        """
        try:
            stmt = select(MonitorTask).filter(MonitorTask.group_id == group_id)
            result = await self._session.execute(stmt)
            task = result.scalars().first()
            return task
        except Exception as e:
            self._logger.error(f"查找群组 {group_id} 的任务失败: {str(e)}", exc_info=True)
            return None
            
    async def create_task(self, task_data: Dict[str, Any]) -> Union[int, None]:
        """
        创建新任务
        
        Args:
            task_data: 任务数据
            
        Returns:
            Union[int, None]: 成功返回任务ID，失败返回None
        """
        try:
            new_task = MonitorTask(
                name=task_data.get("name", ""),
                description=task_data.get("description", ""),
                group_id=task_data.get("group_id", ""),
                group_title=task_data.get("group_title", ""),
                keywords=task_data.get("keywords", "[]"),
                ignore_keywords=task_data.get("ignore_keywords", "[]"),
                ignore_nicknames=task_data.get("ignore_nicknames", "[]"),
                monitor_new_users=task_data.get("monitor_new_users", False),
                monitor_messages=task_data.get("monitor_messages", True),
                is_active=task_data.get("is_active", True),
                is_running=task_data.get("is_running", False)
            )
            
            self._session.add(new_task)
            await self._session.flush()
            
            # 获取新任务ID
            task_id = new_task.id
            self._logger.info(f"创建任务成功: ID {task_id}")
            return task_id
        except Exception as e:
            self._logger.error(f"创建任务失败: {str(e)}", exc_info=True)
            return None
            
    async def update_task(self, task_id: Union[int, str], task_data: Dict[str, Any]) -> bool:
        """
        更新任务
        
        Args:
            task_id: 任务ID
            task_data: 要更新的任务数据
            
        Returns:
            bool: 是否成功
        """
        try:
            task_id = int(task_id)
            stmt = update(MonitorTask).where(MonitorTask.id == task_id).values(**task_data)
            result = await self._session.execute(stmt)
            
            success = result.rowcount > 0
            if success:
                self._logger.info(f"更新任务成功: ID {task_id}")
            else:
                self._logger.warning(f"更新任务失败: ID {task_id} 不存在")
                
            return success
        except Exception as e:
            self._logger.error(f"更新任务 ID {task_id} 失败: {str(e)}", exc_info=True)
            return False
            
    async def delete_task(self, task_id: Union[int, str]) -> bool:
        """
        删除任务
        
        Args:
            task_id: 任务ID
            
        Returns:
            bool: 是否成功
        """
        try:
            task_id = int(task_id)
            stmt = delete(MonitorTask).where(MonitorTask.id == task_id)
            result = await self._session.execute(stmt)
            
            success = result.rowcount > 0
            if success:
                self._logger.info(f"删除任务成功: ID {task_id}")
            else:
                self._logger.warning(f"删除任务失败: ID {task_id} 不存在")
                
            return success
        except Exception as e:
            self._logger.error(f"删除任务 ID {task_id} 失败: {str(e)}", exc_info=True)
            return False
            
    async def get_task_stats(self, task_id: Union[int, str]) -> Dict[str, Any]:
        """
        获取任务统计数据
        
        Args:
            task_id: 任务ID
            
        Returns:
            Dict[str, Any]: 统计数据
        """
        try:
            task_id = int(task_id)
            
            # 获取总用户数
            stmt_total = select(func.count(MonitorMessage.user_id.distinct())).where(MonitorMessage.task_id == task_id)
            result_total = await self._session.execute(stmt_total)
            total_users = result_total.scalar() or 0
            
            # 获取今日用户数
            today = datetime.datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
            stmt_today = select(func.count(MonitorMessage.user_id.distinct())).where(
                and_(
                    MonitorMessage.task_id == task_id,
                    MonitorMessage.collected_at >= today
                )
            )
            result_today = await self._session.execute(stmt_today)
            today_users = result_today.scalar() or 0
            
            # 获取任务创建时间
            stmt_task = select(MonitorTask.created_at).where(MonitorTask.id == task_id)
            result_task = await self._session.execute(stmt_task)
            created_at = result_task.scalar()
            
            # 计算运行天数
            running_days = 0
            if created_at:
                delta = datetime.datetime.now() - created_at
                running_days = max(1, delta.days)  # 至少1天
                
            # 计算平均每日用户数
            avg_daily = round(total_users / running_days) if running_days > 0 else 0
            
            stats = {
                "total_users": total_users,
                "today_users": today_users,
                "avg_daily": avg_daily,
                "running_days": running_days
            }
            
            return stats
        except Exception as e:
            self._logger.error(f"获取任务 ID {task_id} 统计数据失败: {str(e)}", exc_info=True)
            return {
                "total_users": 0,
                "today_users": 0,
                "avg_daily": 0,
                "running_days": 0
            }
            
    async def get_task_users(self, task_id: Union[int, str], page: int = 1, page_size: int = 10, search_term: str = "") -> Dict[str, Any]:
        """
        获取任务监控到的用户列表（分页）
        
        Args:
            task_id: 任务ID
            page: 页码
            page_size: 每页记录数
            search_term: 搜索关键词
            
        Returns:
            Dict[str, Any]: 用户列表分页数据
        """
        try:
            task_id = int(task_id)
            
            # 基础查询
            base_query = select(
                MonitorMessage.user_id.label("uid"),
                MonitorMessage.username,
                MonitorMessage.first_name,
                MonitorMessage.last_name,
                MonitorMessage.matched_keywords.label("keyword"),
                MonitorMessage.message_type.label("task_type"),
                MonitorMessage.collected_at.label("join_date")
            ).where(MonitorMessage.task_id == task_id)
            
            # 添加搜索条件
            if search_term:
                search_term = f"%{search_term}%"
                base_query = base_query.where(
                    or_(
                        MonitorMessage.username.ilike(search_term),
                        MonitorMessage.first_name.ilike(search_term),
                        MonitorMessage.last_name.ilike(search_term),
                        MonitorMessage.text.ilike(search_term)
                    )
                )
                
            # 计算总记录数
            count_query = select(func.count()).select_from(base_query.subquery())
            total_count = await self._session.execute(count_query)
            total_count = total_count.scalar() or 0
            
            # 计算总页数
            total_pages = (total_count + page_size - 1) // page_size if total_count > 0 else 1
            
            # 修正页码
            page = min(max(1, page), total_pages)
            
            # 添加分页
            offset = (page - 1) * page_size
            paginated_query = base_query.order_by(MonitorMessage.collected_at.desc()).offset(offset).limit(page_size)
            
            # 执行查询
            result = await self._session.execute(paginated_query)
            rows = result.fetchall()
            
            # 构建结果列表
            items = []
            for row in rows:
                row_dict = {
                    "uid": row.uid,
                    "username": row.username or "",
                    "nickname": f"{row.first_name or ''} {row.last_name or ''}".strip(),
                    "keyword": self._format_keywords(row.keyword),
                    "task_type": row.task_type,
                    "join_date": row.join_date.strftime("%Y-%m-%d %H:%M:%S") if row.join_date else ""
                }
                items.append(row_dict)
                
            # 返回分页数据
            return {
                "items": items,
                "current_page": page,
                "total_pages": total_pages,
                "total_items": total_count,
                "page_size": page_size
            }
        except Exception as e:
            self._logger.error(f"获取任务 ID {task_id} 用户列表失败: {str(e)}", exc_info=True)
            return {
                "items": [],
                "current_page": 1,
                "total_pages": 1,
                "total_items": 0,
                "page_size": page_size
            }
            
    def _format_keywords(self, keywords_json: str) -> str:
        """
        格式化关键词JSON字符串为可读文本
        
        Args:
            keywords_json: 关键词JSON字符串
            
        Returns:
            str: 格式化后的关键词文本
        """
        try:
            if not keywords_json:
                return ""
                
            keywords = json.loads(keywords_json)
            if isinstance(keywords, list):
                return ", ".join(keywords)
            else:
                return str(keywords)
        except Exception:
            return keywords_json or ""
            
    async def get_all_task_users(self, task_id: Union[int, str]) -> List[Dict[str, Any]]:
        """
        获取任务监控到的所有用户
        
        Args:
            task_id: 任务ID
            
        Returns:
            List[Dict[str, Any]]: 用户列表
        """
        try:
            task_id = int(task_id)
            
            # 查询
            query = select(
                MonitorMessage.user_id.label("uid"),
                MonitorMessage.username,
                MonitorMessage.first_name,
                MonitorMessage.last_name,
                MonitorMessage.matched_keywords.label("keyword"),
                MonitorMessage.message_type.label("task_type"),
                MonitorMessage.collected_at.label("join_date")
            ).where(MonitorMessage.task_id == task_id).order_by(MonitorMessage.collected_at.desc())
            
            # 执行查询
            result = await self._session.execute(query)
            rows = result.fetchall()
            
            # 构建结果列表
            items = []
            for row in rows:
                row_dict = {
                    "uid": row.uid,
                    "username": row.username or "",
                    "nickname": f"{row.first_name or ''} {row.last_name or ''}".strip(),
                    "keyword": self._format_keywords(row.keyword),
                    "task_type": row.task_type,
                    "join_date": row.join_date.strftime("%Y-%m-%d %H:%M:%S") if row.join_date else ""
                }
                items.append(row_dict)
                
            return items
        except Exception as e:
            self._logger.error(f"获取任务 ID {task_id} 全部用户失败: {str(e)}", exc_info=True)
            return []
            
    async def record_message(self, message_data: Dict[str, Any]) -> Union[int, None]:
        """
        记录监控消息
        
        Args:
            message_data: 消息数据
            
        Returns:
            Union[int, None]: 成功返回消息ID，失败返回None
        """
        try:
            new_message = MonitorMessage(
                task_id=message_data.get("task_id"),
                message_id=message_data.get("message_id", ""),
                chat_id=message_data.get("chat_id", ""),
                user_id=message_data.get("user_id", ""),
                username=message_data.get("username", ""),
                first_name=message_data.get("first_name", ""),
                last_name=message_data.get("last_name", ""),
                text=message_data.get("text", ""),
                matched_keywords=json.dumps(message_data.get("matched_keywords", [])),
                message_type=message_data.get("message_type", "text"),
                collected_at=message_data.get("timestamp", datetime.datetime.now())
            )
            
            self._session.add(new_message)
            await self._session.flush()
            
            # 获取新消息ID
            message_id = new_message.id
            self._logger.info(f"记录消息成功: ID {message_id}")
            return message_id
        except Exception as e:
            self._logger.error(f"记录消息失败: {str(e)}", exc_info=True)
            return None
            
    async def get_notification_configs(self, task_id: Union[int, str]) -> List[Dict[str, Any]]:
        """
        获取任务的通知配置
        
        Args:
            task_id: 任务ID
            
        Returns:
            List[Dict[str, Any]]: 通知配置列表
        """
        try:
            task_id = int(task_id)
            
            stmt = select(NotificationConfig).filter(
                and_(
                    NotificationConfig.task_id == task_id,
                    NotificationConfig.is_active == True
                )
            )
            result = await self._session.execute(stmt)
            configs = result.scalars().all()
            
            # 构建结果列表
            items = []
            for config in configs:
                config_dict = {
                    "id": config.id,
                    "task_id": config.task_id,
                    "target_type": config.target_type,
                    "target_address": config.target_address,
                    "account_id": config.account_id,
                    "template": config.template
                }
                items.append(config_dict)
                
            return items
        except Exception as e:
            self._logger.error(f"获取任务 ID {task_id} 通知配置失败: {str(e)}", exc_info=True)
            return []
            
    async def create_notification_config(self, config_data: Dict[str, Any]) -> Union[int, None]:
        """
        创建通知配置
        
        Args:
            config_data: 通知配置数据
            
        Returns:
            Union[int, None]: 成功返回配置ID，失败返回None
        """
        try:
            new_config = NotificationConfig(
                task_id=config_data.get("task_id"),
                target_type=config_data.get("target_type", ""),
                target_address=config_data.get("target_address", ""),
                account_id=config_data.get("account_id", 0),
                template=config_data.get("template", ""),
                is_active=config_data.get("is_active", True)
            )
            
            self._session.add(new_config)
            await self._session.flush()
            
            # 获取新配置ID
            config_id = new_config.id
            self._logger.info(f"创建通知配置成功: ID {config_id}")
            return config_id
        except Exception as e:
            self._logger.error(f"创建通知配置失败: {str(e)}", exc_info=True)
            return None
            
    async def delete_notification_configs_by_task(self, task_id: Union[int, str]) -> bool:
        """
        删除任务的所有通知配置
        
        Args:
            task_id: 任务ID
            
        Returns:
            bool: 是否成功
        """
        try:
            task_id = int(task_id)
            stmt = delete(NotificationConfig).where(NotificationConfig.task_id == task_id)
            result = await self._session.execute(stmt)
            
            self._logger.info(f"删除任务 ID {task_id} 的通知配置成功")
            return True
        except Exception as e:
            self._logger.error(f"删除任务 ID {task_id} 的通知配置失败: {str(e)}", exc_info=True)
            return False
            
    async def delete_task_messages(self, task_id: Union[int, str]) -> bool:
        """
        删除任务的所有监控消息数据
        
        Args:
            task_id: 任务ID
            
        Returns:
            bool: 是否成功
        """
        try:
            task_id = int(task_id)
            stmt = delete(MonitorMessage).where(MonitorMessage.task_id == task_id)
            result = await self._session.execute(stmt)
            
            count = result.rowcount
            self._logger.info(f"删除任务 ID {task_id} 的监控消息数据成功，共删除 {count} 条记录")
            return True
        except Exception as e:
            self._logger.error(f"删除任务 ID {task_id} 的监控消息数据失败: {str(e)}", exc_info=True)
            return False

    async def create_task_returning_model(self, task_data: Dict[str, Any]) -> Optional[MonitorTask]:
        """
        创建新任务并返回模型对象
        
        Args:
            task_data: 任务数据
            
        Returns:
            Optional[MonitorTask]: 成功返回任务模型对象，失败返回None
        """
        try:
            new_task = MonitorTask(
                name=task_data.get("name", ""),
                description=task_data.get("description", ""),
                keywords=task_data.get("keywords", "[]"),
                ignore_keywords=task_data.get("ignore_keywords", "[]"),
                ignore_nicknames_rules=task_data.get("ignore_nicknames_rules", "[]"),
                monitor_new_users=task_data.get("monitor_new_users", False),
                monitor_messages=task_data.get("monitor_messages", True),
                is_active=task_data.get("is_active", True),
                is_running=task_data.get("is_running", False),
                block_enabled=task_data.get("block_enabled", False),
                block_scope=task_data.get("block_scope", None)
            )
            
            self._session.add(new_task)
            await self._session.flush()
            
            # 获取新任务ID
            self._logger.info(f"创建任务成功: ID {new_task.id}")
            return new_task
        except Exception as e:
            self._logger.error(f"创建任务失败: {str(e)}", exc_info=True)
            return None

    async def delete_monitored_chats_for_task(self, task_id: Union[int, str]) -> bool:
        """
        删除任务的所有监控群组/频道
        
        Args:
            task_id: 任务ID
            
        Returns:
            bool: 是否成功
        """
        try:
            from data.models.monitor import MonitoredChat
            
            task_id = int(task_id)
            stmt = delete(MonitoredChat).where(MonitoredChat.task_id == task_id)
            await self._session.execute(stmt)
            
            self._logger.info(f"删除任务 ID {task_id} 的监控群组成功")
            return True
        except Exception as e:
            self._logger.error(f"删除任务监控群组失败: {str(e)}", exc_info=True)
            return False

    async def add_monitored_chat(self, task_id: str, chat_id: str, chat_title: str, account_phone: str = "") -> bool:
        """
        为任务添加监控的聊天
        
        Args:
            task_id: 任务ID
            chat_id: 聊天ID
            chat_title: 聊天标题
            account_phone: 关联的账户手机号
            
        Returns:
            bool: 是否成功
        """
        try:
            from data.models.monitor import MonitoredChat
            
            new_chat = MonitoredChat(
                task_id=int(task_id),
                chat_id=chat_id,
                chat_title=chat_title,
                account_phone=account_phone
            )
            
            self._session.add(new_chat)
            # Flush here but commit in the caller
            await self._session.flush()
            
            self._logger.info(f"为任务 {task_id} 添加监控聊天: {chat_title} ({chat_id}) 关联账户: {account_phone}")
            return True
        except Exception as e:
            self._logger.error(f"为任务添加监控聊天失败: {str(e)}", exc_info=True)
            return False

    async def get_monitored_chats_for_task(self, task_id: str) -> List[Any]:
        """
        获取任务的所有监控聊天
        
        Args:
            task_id: 任务ID
            
        Returns:
            List[Any]: 监控聊天列表
        """
        try:
            from data.models.monitor import MonitoredChat
            
            stmt = select(MonitoredChat).filter(MonitoredChat.task_id == int(task_id))
            result = await self._session.execute(stmt)
            chats = result.scalars().all()
            
            return list(chats)
        except Exception as e:
            self._logger.error(f"获取任务 {task_id} 的监控聊天失败: {str(e)}", exc_info=True)
            return []

    async def update_task_fields(self, task_id: Union[int, str], fields_data: Dict[str, Any]) -> bool:
        """
        更新任务的特定字段
        
        Args:
            task_id: 任务ID
            fields_data: 要更新的字段数据
            
        Returns:
            bool: 是否成功
        """
        try:
            task_id = int(task_id)
            
            # 先获取任务对象
            task = await self.get_task_by_id(task_id)
            if not task:
                self._logger.warning(f"更新任务字段失败: ID {task_id} 不存在")
                return False
                
            # 更新字段
            for field, value in fields_data.items():
                if hasattr(task, field):
                    setattr(task, field, value)
                else:
                    self._logger.warning(f"任务没有 {field} 字段，跳过更新")
                    
            # 不调用commit，由调用者负责
            await self._session.flush()
            
            self._logger.info(f"更新任务字段成功: ID {task_id}")
            return True
        except Exception as e:
            self._logger.error(f"更新任务字段失败: ID {task_id}, 错误: {str(e)}", exc_info=True)
            return False

    async def store_matched_message(self, task_id: str, tg_message_id: int, 
                         tg_chat_id: int, tg_sender_id: int,
                         sender_username: Optional[str], sender_fullname: Optional[str],
                         message_text: str, matched_keywords_json: str,
                         message_timestamp: datetime.datetime) -> Optional[int]:
        """
        存储匹配到关键词的消息
        
        Args:
            task_id: 任务ID
            tg_message_id: Telegram消息ID
            tg_chat_id: Telegram聊天ID
            tg_sender_id: Telegram发送者ID
            sender_username: 发送者用户名
            sender_fullname: 发送者全名
            message_text: 消息内容
            matched_keywords_json: 匹配的关键词JSON字符串
            message_timestamp: 消息时间戳
            
        Returns:
            Optional[int]: 新记录的ID，失败返回None
        """
        try:
            from data.models.monitor import MonitorMessage
            
            # 转换task_id为整数
            try:
                task_id_int = int(task_id)
            except ValueError:
                self._logger.error(f"无效的任务ID: {task_id}")
                return None

            # 先检查此任务是否已存在此用户ID的记录
            user_id_str = str(tg_sender_id)
            check_stmt = select(MonitorMessage.id).where(
                and_(
                    MonitorMessage.task_id == task_id_int,
                    MonitorMessage.user_id == user_id_str
                )
            ).limit(1)
            
            result = await self._session.execute(check_stmt)
            existing_record = result.scalar()
            
            # 如果该用户已存在记录，则跳过保存
            if existing_record:
                self._logger.info(f"用户 {user_id_str} 在任务 {task_id} 中已存在记录，跳过保存")
                return existing_record  # 返回已存在记录的ID
                
            # 创建匹配消息记录
            new_message = MonitorMessage(
                task_id=task_id_int,
                message_id=str(tg_message_id),
                chat_id=str(tg_chat_id),
                user_id=user_id_str,
                username=sender_username,
                first_name=sender_fullname.split()[0] if sender_fullname and " " in sender_fullname else sender_fullname,
                last_name=sender_fullname.split(" ", 1)[1] if sender_fullname and " " in sender_fullname else None,
                text=message_text,
                matched_keywords=matched_keywords_json,
                message_type="text",  # 默认为文本类型
                collected_at=message_timestamp  # 使用传入的时间戳
            )
            
            self._session.add(new_message)
            await self._session.flush()
            
            self._logger.info(f"存储匹配消息成功: 任务ID {task_id}, 消息ID {tg_message_id}, 用户ID {user_id_str}")
            return new_message.id
        except Exception as e:
            self._logger.error(f"存储匹配消息失败: {str(e)}", exc_info=True)
            return None

    async def get_notification_configs_for_task(self, task_id: str) -> List[Any]:
        """
        获取任务的通知配置
        
        Args:
            task_id: 任务ID
            
        Returns:
            List[Any]: 通知配置列表
        """
        try:
            from data.models.monitor import NotificationConfig
            
            # 转换task_id为整数
            try:
                task_id_int = int(task_id)
            except ValueError:
                self._logger.error(f"无效的任务ID: {task_id}")
                return []
                
            stmt = select(NotificationConfig).filter(NotificationConfig.task_id == task_id_int)
            result = await self._session.execute(stmt)
            configs = result.scalars().all()
            
            return list(configs)
        except Exception as e:
            self._logger.error(f"获取任务 {task_id} 的通知配置失败: {str(e)}", exc_info=True)
            return []

    async def get_total_users_count(self) -> int:
        """
        获取所有任务的总用户数量（去重）
        
        Returns:
            int: 总用户数量
        """
        try:
            # 统计所有任务的不重复用户数量
            stmt = select(func.count(MonitorMessage.user_id.distinct()))
            result = await self._session.execute(stmt)
            total_count = result.scalar() or 0
            
            return total_count
        except Exception as e:
            self._logger.error(f"获取总用户数量失败: {str(e)}", exc_info=True)
            return 0

    async def get_all_users(self, page: int = 1, page_size: int = 10, search_term: str = "") -> Dict[str, Any]:
        """
        获取所有任务的用户列表（分页）
        
        Args:
            page: 页码
            page_size: 每页记录数
            search_term: 搜索关键词
            
        Returns:
            Dict[str, Any]: 用户列表分页数据
        """
        try:
            self._logger.info(f"获取所有用户数据: 页码 {page}, 每页 {page_size} 条, 搜索词: '{search_term}'")
            
            # 基础查询 - 使用子查询获取不同用户ID的最新记录
            subq = select(
                MonitorMessage.user_id,
                func.max(MonitorMessage.collected_at).label('max_date')
            ).group_by(MonitorMessage.user_id).subquery('latest_message')
            
            # 主查询 - 连接子查询获取最新的用户记录
            base_query = select(
                MonitorMessage.task_id,
                MonitorMessage.user_id.label("uid"),
                MonitorMessage.username,
                MonitorMessage.first_name,
                MonitorMessage.last_name,
                MonitorMessage.matched_keywords.label("keyword"),
                MonitorMessage.message_type.label("task_type"),
                MonitorMessage.collected_at.label("join_date")
            ).join(
                subq,
                and_(
                    MonitorMessage.user_id == subq.c.user_id,
                    MonitorMessage.collected_at == subq.c.max_date
                )
            )
            
            # 添加搜索条件
            if search_term:
                search_pattern = f"%{search_term}%"
                base_query = base_query.where(
                    or_(
                        MonitorMessage.username.ilike(search_pattern),
                        MonitorMessage.first_name.ilike(search_pattern),
                        MonitorMessage.last_name.ilike(search_pattern),
                        MonitorMessage.text.ilike(search_pattern)
                    )
                )
            
            # 计算总记录数
            count_query = select(func.count()).select_from(
                base_query.alias("base")
            )
            
            # 添加分页
            paginated_query = base_query.order_by(desc(MonitorMessage.collected_at))
            if page > 0 and page_size > 0:
                paginated_query = paginated_query.limit(page_size).offset((page - 1) * page_size)
            
            # 执行查询
            result = await self._session.execute(paginated_query)
            rows = result.fetchall()
            
            # 构建结果列表
            items = []
            for row in rows:
                row_dict = {
                    "uid": row.uid,
                    "username": row.username or "",
                    "nickname": f"{row.first_name or ''} {row.last_name or ''}".strip(),
                    "keyword": self._format_keywords(row.keyword),
                    "task_type": row.task_type,
                    "join_date": row.join_date.strftime("%Y-%m-%d %H:%M:%S") if hasattr(row.join_date, "strftime") else str(row.join_date)
                }
                items.append(row_dict)
            
            # 获取总记录数
            count_result = await self._session.execute(count_query)
            total_items = count_result.scalar() or 0
            
            # 记录实际返回的数据格式
            if items:
                self._logger.info(f"成功获取所有用户数据: 共 {total_items} 条，当前页 {len(items)} 条")
                self._logger.info(f"样本数据格式: {items[0] if items else 'None'}")
            else:
                self._logger.warning("查询结果为空，没有找到任何用户数据")
            
            # 返回分页数据
            result_data = {
                "items": items,
                "current_page": page,
                "page_size": page_size,
                "total_items": total_items,
                "total_pages": math.ceil(total_items / page_size) if page_size > 0 else 1
            }
            
            return result_data
            
        except Exception as e:
            self._logger.error(f"获取所有用户数据失败: {str(e)}", exc_info=True)
            return {
                "items": [],
                "current_page": page,
                "page_size": page_size,
                "total_items": 0,
                "total_pages": 1
            }

    async def get_all_tasks_with_user_count(self) -> list:
        """
        获取所有监控任务及其用户数量
        Returns:
            List[Dict]: [{id, name, ... , user_count}]
        """
        try:
            stmt = select(MonitorTask)
            result = await self._session.execute(stmt)
            tasks = result.scalars().all()
            task_list = []
            for task in tasks:
                # 查询每个任务的用户数量
                stmt_count = select(func.count(MonitorMessage.user_id.distinct())).where(MonitorMessage.task_id == task.id)
                result_count = await self._session.execute(stmt_count)
                user_count = result_count.scalar() or 0
                task_list.append({
                    'id': task.id,
                    'name': task.name,
                    'user_count': user_count,
                    'created_at': task.created_at.strftime('%Y-%m-%d %H:%M:%S') if task.created_at else '',
                    'description': getattr(task, 'description', ''),
                })
            return task_list
        except Exception as e:
            self._logger.error(f"获取所有任务及用户数失败: {str(e)}", exc_info=True)
            return []

    async def get_all_usernames_or_ids_for_task(self, task_id: int) -> list:
        """
        获取某监听任务下所有具有 username 的用户，以及 nickname。
        只返回 username 可用的用户。
        返回格式: [{"target_id": username, "target_name": nickname}, ...]
        """
        try:
            from data.models.monitor import MonitorMessage
            stmt = select(
                MonitorMessage.username,
                MonitorMessage.first_name,
                MonitorMessage.last_name
            ).where(MonitorMessage.task_id == task_id)
            result = await self._session.execute(stmt)
            rows = result.fetchall()
            targets = []
            seen = set()
            for row in rows:
                username = row.username
                nickname = f"{row.first_name or ''} {row.last_name or ''}".strip()
                if username and username not in seen:
                    targets.append({
                        "target_id": username,
                        "target_name": nickname
                    })
                    seen.add(username)
            return targets
        except Exception as e:
            self._logger.error(f"获取监听任务{task_id}用户username失败: {e}", exc_info=True)
            return [] 