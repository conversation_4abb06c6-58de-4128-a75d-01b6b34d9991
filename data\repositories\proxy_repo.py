#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
代理IP数据仓库
提供代理IP的CRUD操作和查询功能
"""

import datetime
from typing import List, Optional, Dict, Any, Tuple

from sqlalchemy import select, update, delete, and_, or_, func
from sqlalchemy.ext.asyncio import AsyncSession

from data.models.proxy import ProxyModel
from utils.logger import get_logger


class ProxyRepository:
    """代理IP数据仓库"""
    
    def __init__(self, session: AsyncSession):
        """初始化
        
        Args:
            session: 数据库会话
        """
        self.session = session
        self._logger = get_logger("data.repositories.proxy")
    
    async def add(self, host: str, port: int, username: Optional[str] = None, 
                  password: Optional[str] = None, proxy_type: str = "socks5",
                  is_local: bool = False) -> ProxyModel:
        """添加代理IP
        
        Args:
            host: 主机地址
            port: 端口
            username: 用户名
            password: 密码
            proxy_type: 代理类型，默认socks5
            is_local: 是否为本地代理，默认False
            
        Returns:
            新添加的代理IP模型
        """
        proxy = ProxyModel(
            host=host,
            port=port,
            username=username,
            password=password,
            proxy_type=proxy_type,
            is_active=True,
            is_valid=False,
            is_local=is_local
        )
        self.session.add(proxy)
        await self.session.flush()
        return proxy
    
    async def bulk_add(self, proxy_list: List[Dict[str, Any]]) -> List[ProxyModel]:
        """批量添加代理IP
        
        Args:
            proxy_list: 代理IP列表，每项包含host、port等信息
            
        Returns:
            新添加的代理IP模型列表
        """
        proxies = []
        existing_proxies_cache = {}  # 缓存已查询过的代理
        
        # 先获取所有IP:端口的组合
        host_port_pairs = [(data["host"], data["port"]) for data in proxy_list]
        unique_pairs = set(host_port_pairs)
        
        # 批量查询已存在的代理
        for host, port in unique_pairs:
            existing = await self.find_by_host_port(host, port)
            if existing:
                existing_proxies_cache[(host, port)] = existing
        
        # 添加不存在的代理
        for proxy_data in proxy_list:
            host = proxy_data["host"]
            port = proxy_data["port"]
            
            # 检查缓存中是否存在
            if (host, port) in existing_proxies_cache:
                continue
                
            proxy = ProxyModel(
                host=host,
                port=port,
                username=proxy_data.get("username"),
                password=proxy_data.get("password"),
                proxy_type=proxy_data.get("proxy_type", "socks5"),
                is_active=True,
                is_valid=False,
                is_local=proxy_data.get("is_local", False)
            )
            self.session.add(proxy)
            proxies.append(proxy)
        
        # 只在添加完所有代理后执行一次flush
        if proxies:
            try:
                await self.session.flush()
            except Exception as e:
                self._logger.exception(f"批量添加代理失败: {e}")
                # 如果flush失败，回滚并重试不使用flush
                await self.session.rollback()
                for proxy in proxies:
                    if proxy not in self.session:
                        self.session.add(proxy)
        
        return proxies
    
    async def find_by_id(self, proxy_id: int) -> Optional[ProxyModel]:
        """根据ID查询代理
        
        Args:
            proxy_id: 代理ID
            
        Returns:
            代理模型或None
        """
        try:
            result = await self.session.execute(
                select(ProxyModel).filter(ProxyModel.id == proxy_id)
            )
            return result.scalars().first()
        except Exception as e:
            self._logger.exception(f"根据ID查询代理出错: {e}")
            return None
    
    async def find_by_host_port(self, host: str, port: int, proxy_type: str = None) -> Optional[ProxyModel]:
        """根据主机和端口查询代理
        
        Args:
            host: 主机地址
            port: 端口
            proxy_type: 代理类型
            
        Returns:
            代理模型或None
        """
        try:
            # 构建查询
            query = select(ProxyModel).filter(
                ProxyModel.host == host,
                ProxyModel.port == port
            )
            
            # 应用代理类型条件
            if proxy_type:
                query = query.filter(ProxyModel.proxy_type == proxy_type)
            
            # 执行查询
            result = await self.session.execute(query)
            return result.scalars().first()
        except Exception as e:
            self._logger.exception(f"根据主机和端口查询代理出错: {e}")
            return None
    
    async def find_all(self, active_only: bool = False, valid_only: bool = False,
                      offset: int = 0, limit: int = 100) -> Tuple[List[ProxyModel], int]:
        """查询所有代理
        
        Args:
            active_only: 是否只查询激活的代理
            valid_only: 是否只查询有效的代理
            offset: 分页偏移
            limit: 分页限制
            
        Returns:
            (代理列表, 总数)
        """
        try:
            # 构建查询
            query = select(ProxyModel)
            count_query = select(func.count()).select_from(ProxyModel)
            
            # 应用条件
            if active_only:
                query = query.filter(ProxyModel.is_active == True)
                count_query = count_query.filter(ProxyModel.is_active == True)
            
            if valid_only:
                query = query.filter(ProxyModel.is_valid == True)
                count_query = count_query.filter(ProxyModel.is_valid == True)
            
            # 排序
            query = query.order_by(ProxyModel.id.desc())
            
            # 分页
            query = query.offset(offset).limit(limit)
            
            # 执行查询
            result = await self.session.execute(query)
            count_result = await self.session.execute(count_query)
            
            return result.scalars().all(), count_result.scalar()
        except Exception as e:
            self._logger.exception(f"查询所有代理出错: {e}")
            return [], 0
    
    async def update(self, proxy_id: int, **kwargs) -> bool:
        """更新代理IP信息
        
        Args:
            proxy_id: 代理IP ID
            **kwargs: 要更新的字段
            
        Returns:
            是否更新成功
        """
        # 移除不允许更新的字段
        for key in ["id", "created_at"]:
            kwargs.pop(key, None)
        
        # 自动更新updated_at
        kwargs["updated_at"] = datetime.datetime.utcnow()
        
        result = await self.session.execute(
            update(ProxyModel)
            .where(ProxyModel.id == proxy_id)
            .values(**kwargs)
        )
        
        return result.rowcount > 0
    
    async def update_validation_result(self, proxy_id: int, is_valid: bool, 
                                      response_time: Optional[float] = None) -> bool:
        """更新代理IP验证结果
        
        Args:
            proxy_id: 代理IP ID
            is_valid: 是否有效
            response_time: 响应时间(毫秒)
            
        Returns:
            是否更新成功
        """
        proxy = await self.find_by_id(proxy_id)
        if not proxy:
            return False
        
        # 更新成功/失败计数
        if is_valid:
            success_count = proxy.success_count + 1
            update_data = {
                "is_valid": True,
                "success_count": success_count,
                "response_time": response_time,
                "last_checked": datetime.datetime.utcnow()
            }
        else:
            failure_count = proxy.failure_count + 1
            update_data = {
                "is_valid": False,
                "failure_count": failure_count,
                "last_checked": datetime.datetime.utcnow()
            }
            
            # 如果连续失败超过阈值，自动禁用
            if failure_count > 5 and proxy.success_count == 0:
                update_data["is_active"] = False
        
        return await self.update(proxy_id, **update_data)
    
    async def delete(self, proxy_id: int) -> bool:
        """删除代理IP
        
        Args:
            proxy_id: 代理IP ID
            
        Returns:
            是否删除成功
        """
        result = await self.session.execute(
            delete(ProxyModel).where(ProxyModel.id == proxy_id)
        )
        
        return result.rowcount > 0
    
    async def delete_invalid(self, min_failure_count: int = 5) -> int:
        """删除无效的代理IP
        
        Args:
            min_failure_count: 最小失败次数阈值
            
        Returns:
            删除的代理IP数量
        """
        result = await self.session.execute(
            delete(ProxyModel).where(
                and_(
                    ProxyModel.is_valid == False,
                    ProxyModel.failure_count >= min_failure_count
                )
            )
        )
        
        return result.rowcount
    
    async def has_local_proxy(self) -> bool:
        """检查是否有本地代理
        
        Returns:
            是否有本地代理
        """
        try:
            query = select(func.count()).select_from(ProxyModel).filter(ProxyModel.is_local == True)
            result = await self.session.execute(query)
            count = result.scalar()
            return count > 0
        except Exception as e:
            self._logger.exception(f"检查是否有本地代理出错: {e}")
            return False
    
    async def delete_all(self) -> int:
        """删除所有代理
        
        Returns:
            删除的代理数量
        """
        try:
            result = await self.session.execute(delete(ProxyModel))
            return result.rowcount
        except Exception as e:
            self._logger.exception(f"删除所有代理出错: {e}")
            return 0
    
    async def get_local_proxies(self) -> List[ProxyModel]:
        """获取所有本地代理
        
        Returns:
            本地代理列表
        """
        try:
            result = await self.session.execute(
                select(ProxyModel)
                .filter(ProxyModel.is_local == True)
                .filter(ProxyModel.is_active == True)
            )
            return result.scalars().all()
        except Exception as e:
            self._logger.exception(f"获取本地代理出错: {e}")
            return []
    
    async def get_valid_proxies(self, proxy_type: str = None, is_local: bool = None) -> List[ProxyModel]:
        """获取有效代理
        
        Args:
            proxy_type: 代理类型
            is_local: 是否为本地代理
            
        Returns:
            有效代理列表
        """
        try:
            # 构建查询
            query = select(ProxyModel).filter(
                ProxyModel.is_valid == True,
                ProxyModel.is_active == True
            )
            
            # 应用条件
            if proxy_type:
                query = query.filter(ProxyModel.proxy_type == proxy_type)
            
            if is_local is not None:
                query = query.filter(ProxyModel.is_local == is_local)
            
            # 执行查询
            result = await self.session.execute(query)
            return result.scalars().all()
        except Exception as e:
            self._logger.exception(f"获取有效代理出错: {e}")
            return []
    
    async def check_port_exists(self, port: int, is_local: bool = True) -> bool:
        """检查指定端口是否已被本地代理使用
        
        Args:
            port: 要检查的端口
            is_local: 是否检查本地代理
            
        Returns:
            bool: 端口是否已存在
        """
        try:
            query = select(func.count()).select_from(ProxyModel).filter(
                ProxyModel.port == port
            )
            
            # 如果指定了检查本地代理
            if is_local:
                query = query.filter(ProxyModel.is_local == True)
                
            result = await self.session.execute(query)
            count = result.scalar()
            return count > 0
        except Exception as e:
            self._logger.exception(f"检查端口是否存在出错: {e}")
            return False
    
    async def check_host_exists(self, host: str) -> bool:
        """检查指定IP是否已被代理使用
        
        Args:
            host: 要检查的IP地址
            is_local: 是否检查本地代理
            
        Returns:
            bool: IP是否已存在
        """
        try:
            query = select(func.count()).select_from(ProxyModel).filter(
                ProxyModel.host == host,
            )
                
            result = await self.session.execute(query)
            count = result.scalar()
            return count > 0
        except Exception as e:
            self._logger.exception(f"检查IP是否存在出错: {e}")
            return False
    
    async def add_proxy(self, session: AsyncSession, proxy: ProxyModel) -> int:
        """添加代理IP模型
        
        Args:
            session: 数据库会话
            proxy: 代理模型
            
        Returns:
            int: 添加的代理ID，失败返回0
        """
        try:
            session.add(proxy)
            await session.flush()
            return proxy.id if proxy.id else 0
        except Exception as e:
            self._logger.exception(f"添加代理模型失败: {e}")
            return 0
    
    async def count_local_proxies(self) -> int:
        """统计本地代理数量
        
        Returns:
            int: 本地代理数量
        """
        try:
            query = select(func.count()).select_from(ProxyModel).filter(
                and_(
                    ProxyModel.is_local == True,
                    ProxyModel.is_active == True
                )
            )
            result = await self.session.execute(query)
            count = result.scalar()
            return count or 0
        except Exception as e:
            self._logger.exception(f"统计本地代理数量出错: {e}")
            return 0
    
    async def find_invalid_proxies(self) -> List[ProxyModel]:
        """查找所有失效代理（is_valid==False）"""
        result = await self.session.execute(
            select(ProxyModel).where(ProxyModel.is_valid == False)
        )
        return result.scalars().all() 