#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Telegram客户端管理器
用于管理多个Telegram客户端实例，提供账户登录、登出、连接管理等功能
"""

import os
import asyncio
import time
from typing import Dict, List, Optional, Tuple, Any, Callable, Union
from concurrent.futures import ThreadPoolExecutor

import qasync
from telethon import TelegramClient, utils, events
from telethon.tl import types, functions
from telethon.errors import (
    SessionPasswordNeededError, PhoneCodeInvalidError, 
    PhoneCodeExpiredError, FloodWaitError, AuthKeyError,
    UserDeactivatedBanError, PasswordHashInvalidError
)
from PySide6.QtCore import QObject, Signal

from utils.get_system_proxy import GetProxy
from utils.logger import get_logger
from utils.signal_bus import signal_bus
from config import config

class TelegramClientManager(QObject):
    """Telegram客户端管理器"""
    
    # 信号定义
    notify = Signal(str, object, str)  # event, data, info_type
    connection_status_changed = Signal(str, bool)  # phone, is_connected
    
    def __init__(self, api_id: int, api_hash: str, session_dir: str = None):
        """初始化Telegram客户端管理器
        
        Args:
            api_id: Telegram API ID
            api_hash: Telegram API Hash
            session_dir: Session文件存储目录，默认为当前目录
        """
        super().__init__()
        self._api_id = api_id
        self._api_hash = api_hash
        self._session_dir = session_dir or config.get("sessions_path")
        
        # 确保session目录存在
        os.makedirs(self._session_dir, exist_ok=True)
        
        # 存储活跃的客户端实例 {phone: client}
        self._clients: Dict[str, TelegramClient] = {}
        
        # 存储正在进行的登录过程 {phone: {client, phone_code_hash, ...}}
        self._login_processes: Dict[str, Dict[str, Any]] = {}
        
        # 存储事件处理器 {task_id: {phone: [handler_tuples]}}
        self._event_handlers: Dict[str, Dict[str, List[Tuple[Callable, events.NewMessage]]]] = {}
     
        # 日志记录器
        self._logger = get_logger("core.telegram.client_manager")
        self._logger.info("Telegram客户端管理器初始化")
    
    def get_session_path(self, phone: str) -> str:
        """获取指定手机号对应的session文件路径
        
        Args:
            phone: 手机号码
            
        Returns:
            session文件的完整路径
        """
        # 将手机号中的非数字字符替换为下划线，避免文件路径问题
        return os.path.join(self._session_dir, f"{phone}.session")
        
    
    def get_proxy(self, proxy: dict = None) -> dict:
        """获取代理配置，将标准字典转换为Telethon可用的格式
        
        Args:
            proxy: 标准化的代理配置字典，应包含 'type' 字段:
                   - {'type': 'socks5', 'ip': '*************', 'port': 3129}
                   - {'type': 'system'}
                   - {'type': 'none'} 或 None
            
        Returns:
            供 Telethon 使用的代理配置，或 None（表示不使用代理）
        """
        self._logger.debug(f"get_proxy 收到代理配置字典: {proxy}")
        
        # 检查是否不需要代理
        if proxy is None:
            self._logger.debug("get_proxy: 代理为 None，返回 None")
            return None
            
        # 获取代理类型
        proxy_type = proxy.get('type')
        
        # 如果代理类型为 'none' 或未指定，则不使用代理
        if proxy_type is None or proxy_type.lower() == 'none':
            self._logger.debug("get_proxy: 代理类型为 None 或 'none'，返回 None")
            return None
            
        try:
            if proxy_type.lower() == 'system':
                # 使用系统代理
                proxy_instance = GetProxy(type="system")
                telethon_proxy = proxy_instance.get_proxy()
                self._logger.debug(f"get_proxy: 使用系统代理，转换结果: {telethon_proxy}")
                return telethon_proxy
            
            elif proxy_type.lower() in ['socks5', 'http', 'socks4','ip_pool']:
                # 获取代理服务器信息
                ip = proxy.get('ip')
                port = proxy.get('port')
                username = proxy.get('username')
                password = proxy.get('password')
                
                # 验证必要参数
                if not ip or port is None:
                    self._logger.warning(f"get_proxy: 缺少必要的代理参数 ip/port: {proxy}")
                    return None
                if proxy.get('type').lower() == 'ip_pool':
                    type =proxy.get('proxy_type')
                else:
                    type = proxy_type.lower()
                # 创建代理配置
                proxy_instance = GetProxy(
                    type=type,
                    ip=ip,
                    port=port,
                    username=username,
                    password=password
                )
                telethon_proxy = proxy_instance.get_proxy()
                self._logger.debug(f"get_proxy: 使用 {proxy_type} 代理 {ip}:{port}，转换结果: {telethon_proxy}")
                return telethon_proxy
            
            else:
                self._logger.warning(f"get_proxy: 未知的代理类型 '{proxy_type}' 在字典中: {proxy}。返回 None")
                return None
                
        except Exception as e:
            self._logger.error(f"get_proxy: 处理代理字典 {proxy} 时出错: {e}")
            return None

    async def _create_client(self, session_path: str, proxy: dict = None) -> TelegramClient:
        """创建 TelegramClient 实例
        
        Args:
            session_path: session文件路径
            proxy: 代理设置
            
        Returns:
            TelegramClient 实例
        """
        
        try:
            # 获取代理配置
            proxy_config = self.get_proxy(proxy)
            
            # 创建客户端
            client = TelegramClient(
                session_path, 
                self._api_id, 
                self._api_hash, 
                proxy=proxy_config, 
                connection_retries=0,  # 禁用自动重试
                retry_delay=1,
                auto_reconnect=True
            )
            
            # 注册连接状态监控
            # Telethon 没有 DisconnectedEvent，需要使用其他方式监控连接状态
            # 可以在客户端连接后保存其引用，并通过定时任务或其他方式检查连接状态
            
            return client
        except Exception as e:
            self.notify.emit(f"创建客户端失败", f"创建Telegram客户端失败", "error")
            self._logger.error(f"创建客户端失败: {session_path}, 错误: {e}")
            raise ValueError(f"创建Telegram客户端失败: {str(e)}")

    async def _connect_client(self, client: TelegramClient, max_retries: int = 3) -> Tuple[bool, str]:
        """连接客户端
        
        Args:
            client: TelegramClient实例
            max_retries: 最大重试次数
            
        Returns:
            (成功标志, 错误消息)
        """
        for attempt in range(max_retries):
            try:
                self.notify.emit(f"客户端连接中", f"尝试连接 Telegram... (第 {attempt + 1}/{max_retries} 次)", "info")
                
                await client.connect()
                
                if client.is_connected():
                    self._logger.info(f"连接成功!")
                    self.notify.emit(f"客户端连接成功", f"连接成功!", "success")
                    return True, ""
                else:
                    self._logger.warning("连接未成功建立")
                    self.notify.emit(f"链接失败","连接未成功建立","warning")
                    if attempt < max_retries - 1:
                        await asyncio.sleep(1)
                    continue
                    
            except AuthKeyError as e:
                error_msg = f"认证密钥错误 (AuthKeyError): {e}"
                self.notify.emit("链接失败",f"认证密钥错误 (AuthKeyError): {e}",'error')
                self._logger.error(error_msg)
                return False, error_msg
                
            except ConnectionError as e:
                self._logger.warning(f"连接失败: 网络错误 {e}")
                self.notify.emit("链接失败",f"网络错误 {e}",'error')
                if attempt < max_retries - 1:
                    await asyncio.sleep(1)
                continue
                
            except TimeoutError as e:
                self._logger.warning(f"连接失败: 连接超时 {e}")
                if attempt < max_retries - 1:
                    await asyncio.sleep(1)
                continue
                
            except FloodWaitError as e:
                error_msg = f"请求被限制，需要等待 {e.seconds} 秒"
                self._logger.error(error_msg)
                return False, error_msg
                
            except Exception as e:
                self._logger.error(f"连接时发生未知错误: {e}")
                return False, str(e)
                
        return False, "达到最大重试次数,连接失败"

    async def _create_and_connect_client(self, session_path: str, proxy: dict = None) -> Tuple[bool, Union[TelegramClient, str]]:
        """创建并连接客户端的通用方法
        
        Args:
            session_path: session文件路径
            proxy: 代理设置
            
        Returns:
            (成功标志, 客户端实例或错误消息)
        """
        try:
            # 创建客户端
            self._logger.debug(f"创建客户端: {session_path}, 代理: {proxy}")
            #self.notify.emit(f"创建客户端", f"创建客户端: {session_path}, 代理: {proxy}", "info")
            client = await self._create_client(session_path, proxy)
            
            # 连接客户端(只尝试3次)
            success, error_msg = await self._connect_client(client, max_retries=3)
            
            if success:
                return True, client
            else:
                if client and client.is_connected():
                    await client.disconnect()
                return False, error_msg
                
        except Exception as e:
            self._logger.error(f"创建并连接客户端失败: {session_path}, 错误: {e}")
            return False, f"创建并连接客户端失败: {str(e)}"

    async def start_login(self, phone: str, proxy: dict = None) -> Tuple[bool, str]:
        """开始登录过程，发送验证码
        
        Args:
            phone: 手机号码
            proxy: 代理设置 (格式: {'proxy_type': 'socks5', 'ip': 'host', 'port': port, 'username': user, 'password': pass})
                  其中proxy_type可以是'socks5'、'http'或'mtproxy'
        
        Returns:
            成功发送验证码返回(True, phone_code_hash)，否则返回(False, error_message)
        """
        self.notify.emit("",f"{phone}开始登录",  "info")
        self._logger.info(f"开始登录账户: {phone}")
        
        # 创建一个新客户端实例用于登录
        session_path = self.get_session_path(phone)
  
        # 使用通用方法创建并连接客户端
        success, result = await self._create_and_connect_client(session_path, proxy)
        if not success:
            self.notify.emit(f"{phone}登录失败", result, "error")
            return False, result
            
        client = result
            
        # 检查是否已经授权
        is_authorized, auth_result = await self._verify_client_authorization(client, phone)
        if is_authorized:
            # 如果已经授权，获取用户信息并保存客户端
            self._clients[phone] = client
            self.notify.emit("登录成功", f"{phone}登录成功", "success")
            
            # 使用异步方式发送结果
            self.notify.emit("登录完成", f"登录成功: {phone}", "success")
            
            # 清理登录过程数据
            if phone in self._login_processes:
                del self._login_processes[phone]
            
            return True, "already_authorized"
        
        # 发送验证码
        self.notify.emit("验证中", f"{phone}正在请求验证码...", "info")
        try:
            send_result = await client.send_code_request(phone)
            phone_code_hash = send_result.phone_code_hash
            
            # 保存登录过程信息
            self._login_processes[phone] = {
                'client': client,
                'phone_code_hash': phone_code_hash,
                'attempts': 0,
                'proxy': proxy
            }
            
            self.notify.emit("验证码", f"已发送验证码到 {phone}，请输入", "info")
            
            return True, phone_code_hash
        except Exception as e:
            self._logger.error(f"发送验证码请求失败: {e}")
            self.notify.emit("验证码发送失败", f"发送验证码请求失败: {e}", "error")
            return False, f"发送验证码请求失败: {e}"
        

    
    async def submit_code(self, phone: str, code: str, password: str = None):
        """提交验证码
        
        Args:
            phone: 手机号
            code: 验证码
            password: 两步验证密码（可选）
            
        Returns:
            如果成功,返回(True, user_info)；
            如果需要密码,返回(False, "password_needed")；
            如果失败,返回(False, error_message)
        """
        self.notify.emit("验证中","正在提交验证码...",'info')
        login_info = self._login_processes.get(phone)
        if not login_info:
            return False, "登录会话已过期，请重新开始登录流程"
            
        login_info['attempts'] += 1
        client = login_info['client']
        phone_code_hash = login_info['phone_code_hash']
        
        try:
            # 提交验证码
            await client.sign_in(phone, code, phone_code_hash=phone_code_hash)
            
            # 登录成功
            self._clients[phone] = client
            user_info = await self._get_user_info(client)
            self.notify.emit(f"{phone}登录成功", user_info,"success")
            self._logger.info(f"登录成功: {phone}")

            # 清理登录过程数据
            del self._login_processes[phone]
            
            return True, user_info
        
        except SessionPasswordNeededError:
            # 需要两步验证密码
            if not password:
                self._logger.info(f"需要两步验证密码: {phone}")
                self.notify.emit("验证", "需要两步验证密码，请输入密码", "info")
                return False, "password_needed"
                
            # 如果提供了密码，尝试使用密码登录
            try:
                await client.sign_in(password=password)
                self._clients[phone] = client
                user_info = await self._get_user_info(client)
                self.notify.emit("登录成功", "两步验证通过，用户已成功登录", "success")
                
                # 清理登录过程数据
                del self._login_processes[phone]
                
                return True, user_info
            except PasswordHashInvalidError:
                self.notify.emit("密码错误", "两步验证密码无效", "error")
                self._logger.warning(f"两步验证密码无效: {phone}")
                self.notify.emit("密码错误", "两步验证密码无效", "error")
                return False, "密码错误"
            except Exception as e:
                error_msg = f"两步验证登录失败: {str(e)}"
                self.notify.emit("登录失败", error_msg, "error")
                self._logger.error(f"两步验证登录失败: {phone}, {error_msg}")
                self.notify.emit("登录失败", error_msg, "error")
                return False, error_msg
        
        except PhoneCodeInvalidError:
            # 验证码无效
            error_msg = "验证码无效"
            self.notify.emit("验证码无效", f"手机号 {phone}, 登录失败: {error_msg}", "error")
            
            # 如果尝试次数过多，清理登录过程
            if login_info['attempts'] >= 3:
                await client.disconnect()
                del self._login_processes[phone]
                self.notify.emit("登录失败", "验证码尝试次数过多，请重新开始登录", "error")
            
            return False, error_msg
        
        except PhoneCodeExpiredError:
            # 验证码已过期
            error_msg = "验证码已过期，请重新获取"
            self.notify.emit("登录失败", "验证码已过期，请重新获取", "error")
            signal_bus.send_result("verify", False, {"phone": phone, "message": error_msg})
            
            # 清理当前登录过程
            await client.disconnect()
            del self._login_processes[phone]
            
            return False, error_msg
        
        except Exception as e:
            # 其他错误
            error_msg = f"提交验证码失败: {str(e)}"
            self.notify.emit("登录失败", error_msg, "error")
            self._logger.error(f"验证码提交失败: {phone}, {error_msg}")
            signal_bus.send_result("verify", False, {"phone": phone, "message": error_msg})
            
            # 清理登录过程
            await client.disconnect()
            del self._login_processes[phone]
            
            return False, error_msg
    


    async def import_session(self, session_path: str, proxy: dict = None) -> Tuple[bool, Union[Dict[str, Any], str]]:
        """导入现有的session文件并尝试登录
        
        Args:
            session_path: session文件路径
            proxy: 代理设置
            
        Returns:
            成功返回(True, user_info)，失败返回(False, error_message)
        """
        self.notify.emit("开始导入session", session_path, "info")
        self._logger.info(f"正在导入session: {session_path}，代理: {proxy}")
        
        # 获取文件名作为临时标识
        session_name = os.path.basename(session_path)
        
        try:
            # 使用通用方法创建并连接客户端
            success, result = await self._create_and_connect_client(session_path, proxy)
            if not success:
                self.notify.emit("导入session失败", session_name, result)
                self._logger.warning(f"导入session失败, 连接错误: {session_path}")
                return False, result
                
            client = result
            
            # 验证客户端授权状态
            is_authorized, auth_result = await self._verify_client_authorization(client)
            if not is_authorized:
                error_msg = "session文件未授权或已失效"
                self.notify.emit("导入session失败", session_name, error_msg)
                self._logger.warning(f"导入session失败, {error_msg}: {session_path}")
                if client and client.is_connected():
                    await client.disconnect()
                return False, error_msg
            
            # 获取用户信息
            me = await client.get_me()
            
            # 如果获取不到电话号码，可能是机器人session
            if not getattr(me, 'phone', None):
                phone = f"user_{me.id}"
                self._logger.warning(f"导入的用户没有phone属性，可能是机器人: {me.id}")
            else:
                phone = me.phone
                self._logger.info(f"导入的用户phone: {phone}，{me.username}, {me.first_name}, {me.last_name}")
                
            # 将客户端保存到活跃客户端字典中
            self._clients[phone] = client
            
            # 获取用户信息（使用auth_result已经包含了基本信息）
            user_info = auth_result
            
            # 移动session文件到指定目录
            target_session_path = self.get_session_path(phone)
            if session_path != target_session_path:
                try:
                    # 确保目标目录存在
                    os.makedirs(os.path.dirname(target_session_path), exist_ok=True)
                    # 如果目标文件已存在，先删除
                    if os.path.exists(target_session_path):
                        os.remove(target_session_path)
                    # 复制文件内容，而不是移动
                    with open(session_path, 'rb') as src_file, open(target_session_path, 'wb') as dest_file:
                        # Read and write in chunks for potentially large files
                        chunk_size = 1024 * 1024 # 1MB chunk size
                        while True:
                            chunk = src_file.read(chunk_size)
                            if not chunk:
                                break
                            dest_file.write(chunk)
                    self._logger.info(f"Session文件已从 {session_path} 复制到: {target_session_path}")
                    # 更新session文件路径为标准路径
                    user_info['session_file'] = target_session_path
                except Exception as e:
                    self._logger.error(f"复制session文件从 {session_path} 到 {target_session_path} 失败: {e}")
                    # 如果复制失败，回退到使用原始路径
                    user_info['session_file'] = session_path
            else:
                # 如果路径相同，则无需操作，session_file 路径已经是目标路径
                user_info['session_file'] = session_path
            
            self.notify.emit("导入session成功", session_name, user_info)
            self._logger.info(f"导入session成功: {session_path} -> {phone}")
            
            return True, user_info
        
        except Exception as e:
            error_msg = f"导入session失败: {str(e)}"
            self.notify.emit("导入session失败", session_name, error_msg)
            self._logger.error(f"导入session错误: {session_path}, {error_msg}")
            return False, error_msg
    
    async def batch_import_sessions(self, session_connection_details_list: List[Dict[str, Any]], max_concurrent: int = 5):
        """
        批量导入session文件
        Args:
            session_connection_details_list: session文件信息列表，每项包含 {'path': str, 'proxy': Optional[Dict]} 信息。
                                          'proxy' 是Telethon可以直接使用的代理配置。
            max_concurrent: 最大并发数
        Returns:
            一个字典，包含 'total', 'success', 'failed', 和 'details' (每个session的结果)
        """
        self._logger.info(f"核心层：开始批量导入 {len(session_connection_details_list)} 个session文件，最大并发 {max_concurrent}")
        
        results = {
            'total': len(session_connection_details_list),
            'success': 0,
            'failed': 0,
            'details': [] # 每个元素将是 {'path': str, 'success': bool, 'result': Union[Dict_user_info, str_error_msg]}
        }
        
        if not session_connection_details_list:
            return results # 没有文件处理

        semaphore = asyncio.Semaphore(max_concurrent)
        
        async def import_task(session_detail: Dict[str, Any]):
            path = session_detail.get('path')
            proxy_for_connection = session_detail.get('proxy')
            
            # 确保 self.notify 在此异步上下文中可用或通过其他方式传递
            # self.notify.emit(...) 

            if not path:
                self._logger.error(f"核心层：路径为空，跳过: {session_detail}")
                return {'path': path or 'UnknownPath', 'success': False, 'result': "Session文件路径为空"}

            self.notify.emit("批量操作进度", f"核心层：正在处理 {os.path.basename(path)}", "info")
            try:
                if not os.path.exists(path):
                    raise FileNotFoundError(f"Session文件不存在: {path}")
                if not os.path.isfile(path) or not path.endswith('.session'):
                    raise ValueError(f"无效的session文件: {path}")
                
                # 调用单个导入session的方法
                async with semaphore: # 在实际API调用前获取信号量
                    single_import_success, single_import_result = await self.import_session(path, proxy_for_connection)
                
                self.notify.emit("批量操作进度", f"核心层：完成 {os.path.basename(path)} - {'成功' if single_import_success else '失败'}", "info")
                return {'path': path, 'success': single_import_success, 'result': single_import_result}
            
            except Exception as e:
                self._logger.error(f"核心层：导入 {path} 发生错误: {e}")
                self.notify.emit("批量操作进度", f"核心层：错误 {os.path.basename(path)}: {e}", "error")
                return {'path': path, 'success': False, 'result': str(e)}

        tasks = [import_task(detail) for detail in session_connection_details_list]
        
        processed_details = await asyncio.gather(*tasks, return_exceptions=False) # return_exceptions=False 会让异常直接抛出，如果需要收集部分成功，设为True

        for detail in processed_details:
            if isinstance(detail, dict) and 'success' in detail : # 确保是我们的结果字典
                results['details'].append(detail)
                if detail['success']:
                    results['success'] += 1
                else:
                    results['failed'] += 1
            else: # 可能 asyncio.gather 捕获到了未预期的异常或返回了非字典类型
                results['failed'] +=1
                # 尝试从 detail (可能是异常对象) 中获取路径信息
                path_unknown = "UnknownPathOnGatherError"
                if hasattr(detail, 'args') and detail.args and isinstance(detail.args[0], str) and '.session' in detail.args[0].lower():
                     path_unknown = detail.args[0] # 粗略尝试从异常参数获取路径
                elif isinstance(detail, dict) and 'path' in detail : # 如果是之前捕获的错误字典
                    path_unknown = detail['path']


                results['details'].append({'path': path_unknown, 'success': False, 'result': f"Gather error or unexpected result: {str(detail)}"})
                self._logger.error(f"核心层：批量导入中遇到意外结果或错误: {detail}")


        self._logger.info(f"核心层：批量导入session完成: 总计 {results['total']}, 成功 {results['success']}, 失败 {results['failed']}")
        self.notify.emit("批量操作完成", results, "info") # 发送包含所有细节的最终结果
        return results
    
    async def connect_client(self, phone: str, proxy: dict = None) -> Tuple[bool, str]:
        """连接已存在的客户端
        
        Args:
            phone: 手机号
            proxy: 代理设置
            
        Returns:
            连接成功返回(True, "")，失败返回(False, error_message)
        """
        self._logger.info(f"正在连接客户端: {phone}")
        
        # 如果客户端已经存在且已连接，直接返回成功
        if phone in self._clients:
            client = self._clients[phone]
            if client.is_connected():
                self._logger.info(f"客户端已连接: {phone}")
                self.notify.emit("客户端连接成功", f"{phone}客户端已连接", "success")
                return True, ""
            else:
                # 客户端存在但未连接，尝试重新连接
                try:
                    await client.connect()
                    is_authorized, _ = await self._verify_client_authorization(client, phone)
                    if is_authorized:
                        self.notify.emit("客户端连接成功", f"{phone}客户端已连接", "success")
                        self._logger.info(f"重新连接客户端成功: {phone}")
                        return True, ""
                    else:
                        error_msg = "客户端未授权，需要重新登录"
                        self.notify.emit("客户端连接失败", f"{phone}客户端未授权，需要重新登录", "error")
                        self._logger.warning(f"连接失败, {error_msg}: {phone}")
                        return False, error_msg
                except Exception as e:
                    error_msg = f"重新连接客户端失败: {str(e)}"
                    self.notify.emit("客户端连接失败", f"{phone}客户端连接失败", "error")
                    self._logger.error(f"重新连接失败: {phone}, {error_msg}")
                    return False, error_msg
        
        # 客户端不存在，尝试创建并连接
        try:
            session_path = self.get_session_path(phone)
            if not os.path.exists(session_path):
                error_msg = f"找不到session文件: {session_path}"
                self.notify.emit("客户端连接失败", f"{phone}找不到session文件", "error")
                self._logger.warning(f"连接失败, {error_msg}: {phone}")
                return False, error_msg
            
            # 使用通用方法创建并连接客户端
            success, result = await self._create_and_connect_client(session_path, proxy)
            if not success:
                self.notify.emit("客户端连接失败", f"{phone}客户端连接失败", "error")
                return False, result
                
            client = result
            
            # 验证客户端授权状态
            is_authorized, _ = await self._verify_client_authorization(client, phone)
            if is_authorized:
                self._clients[phone] = client
                self.notify.emit("客户端连接成功", f"{phone}客户端已连接", "success")
                self._logger.info(f"连接客户端成功: {phone}")
                return True, ""
            else:
                await client.disconnect()
                error_msg = "客户端未授权，需要重新登录"
                self.notify.emit("客户端连接失败", f"{phone}客户端未授权，需要重新登录", "error")
                self._logger.warning(f"连接失败, {error_msg}: {phone}")
                return False, error_msg
        
        except Exception as e:
            error_msg = f"连接客户端失败: {str(e)}"
            self.notify.emit("客户端连接失败", f"{phone}客户端连接失败：{error_msg}", "error")
            self._logger.error(f"连接失败: {phone}, {error_msg}")
            return False, error_msg
    
    async def disconnect_client(self, phone: str) -> bool:
        """断开客户端连接
        
        Args:
            phone: 手机号
            
        Returns:
            操作是否成功
        """
        self._logger.info(f"正在断开客户端连接: {phone}")
        
        if phone not in self._clients:
            self._logger.warning(f"找不到客户端: {phone}")
            return False
        
        client = self._clients[phone]
        try:
            await client.disconnect()
            self.notify.emit("客户端断开连接", f"{phone}客户端已断开", "warning")
            self._logger.info(f"断开客户端连接成功: {phone}")
            return True
        except Exception as e:
            self._logger.error(f"断开客户端连接失败: {phone}, {str(e)}")
            return False
    
    async def disconnect_all_clients(self):
        """断开所有客户端连接"""
        self._logger.info("正在断开所有客户端连接")
        
        disconnect_tasks = []
        
        for phone, client in list(self._clients.items()):
            try:
                # 创建断开连接的任务
                disconnect_tasks.append(asyncio.create_task(self._safe_disconnect(phone, client)))
            except Exception as e:
                self._logger.error(f"创建断开连接任务失败: {phone}, {str(e)}")
        
        # 等待所有断开连接任务完成，设置超时
        if disconnect_tasks:
            try:
                await asyncio.wait(disconnect_tasks, timeout=10)
            except Exception as e:
                self._logger.error(f"等待断开连接任务完成失败: {e}")
    
    async def _safe_disconnect(self, phone, client):
        """安全断开连接，捕获所有异常"""
        try:
            self._logger.info(f"正在断开客户端连接: {phone}")
            if client.is_connected():
                await client.disconnect()
                self.notify.emit("客户端断开连接", f"{phone}客户端已断开", "warning")
                self._logger.info(f"断开客户端连接成功: {phone}")
            else:
                self._logger.info(f"客户端已断开连接: {phone}")
        except Exception as e:
            self._logger.error(f"安全断开客户端连接失败: {phone}, {str(e)}")

    async def logout_client(self, phone: str) -> bool:
        """登出客户端
        
        Args:
            phone: 手机号
            
        Returns:
            操作是否成功
        """
        self._logger.info(f"正在登出客户端: {phone}")
        
        if phone not in self._clients:
            self._logger.warning(f"找不到客户端: {phone}")
            return False
        
        client = self._clients[phone]
        try:
            await client.log_out()
            self.notify.emit("客户端断开连接", f"{phone}客户端已断开", "info")
            del self._clients[phone]
            self._logger.info(f"登出客户端成功: {phone}")
            return True
        except Exception as e:
            self._logger.error(f"登出客户端失败: {phone}, {str(e)}")
            return False
    
    async def get_user_info(self, phone: str) -> Tuple[bool, Union[Dict[str, Any], str]]:
        """获取用户信息
        
        Args:
            phone: 手机号
            
        Returns:
            成功返回(True, user_info)，失败返回(False, error_message)
        """
        self._logger.info(f"正在获取用户信息: {phone}")
        
        if phone not in self._clients:
            error_msg = f"找不到客户端: {phone}"
            self.notify.emit("获取用户信息失败", f"{phone}获取用户信息失败,找不到客户端!", "error")
            self._logger.warning(f"获取用户信息失败, {error_msg}")
            return False, error_msg
        
        client = self._clients[phone]
        if not client.is_connected():
            try:
                # 尝试连接客户端
                self._logger.info(f"尝试连接客户端: {phone}")
                await client.connect()
                if not client.is_connected():
                    error_msg = f"无法连接客户端: {phone}"
                    self._logger.warning(error_msg)
                    return False,error_msg
            except Exception as e:
                error_msg = f"连接客户端异常: {phone}, {e}"
                self._logger.error(error_msg)
                return False, error_msg
        try:            
            user_info = await self._get_user_info(client)
            self.notify.emit("用户信息更新", f"{phone}用户信息更新成功", "success")
            self._logger.info(f"获取用户信息成功: {phone}")
            return True, user_info
        except Exception as e:
            error_msg = f"获取用户信息失败: {str(e)}"
            self.notify.emit("获取用户信息失败", f"{phone}获取用户信息失败,错误信息:{error_msg}", "error")
            self._logger.error(f"获取用户信息错误: {phone}, {error_msg}")
            return False, error_msg
    
    async def update_profile(self, phone: str, **kwargs) -> Tuple[bool, Union[Dict[str, Any], str]]:
        """更新用户资料
        
        Args:
            phone: 手机号
            **kwargs: 要更新的资料字段，可包括：
                first_name: 名字
                last_name: 姓氏
                username: 用户名
                bio: 个人简介
                profile_photo: 头像文件路径
            
        Returns:
            成功返回(True, updated_info)，失败返回(False, error_message)
        """
        self._logger.info(f"正在更新用户资料: {phone}")
        
        if phone not in self._clients:
            error_msg = f"找不到客户端: {phone}"
            self.notify.emit("更新用户资料失败", phone, error_msg)
            self._logger.warning(f"更新用户资料失败, {error_msg}")
            return False, error_msg
        
        client = self._clients[phone]
        
        try:
            # 更新基本资料（名字、姓氏、用户名、简介）
            update_fields = {}
            valid_fields = {'first_name', 'last_name', 'username', 'bio'}
            for field in valid_fields:
                if field in kwargs:
                    update_fields[field] = kwargs[field]
            
            if update_fields:
                await client(functions.account.UpdateProfileRequest(**update_fields))
            
            # 更新头像
            if 'profile_photo' in kwargs and kwargs['profile_photo']:
                photo_path = kwargs['profile_photo']
                if os.path.exists(photo_path):
                    await client(functions.photos.UploadProfilePhotoRequest(
                        file=await client.upload_file(photo_path)
                    ))
            
            # 获取更新后的用户信息
            user_info = await self._get_user_info(client)
            self.notify.emit("资料更新成功", f"{phone}资料更新成功", "success")
            self._logger.info(f"更新用户资料成功: {phone}")
            return True, user_info
        except Exception as e:
            error_msg = f"更新用户资料失败: {str(e)}"
            self.notify.emit("更新用户资料失败", f"{phone}更新用户资料失败,错误信息:{error_msg}", "error")
            self._logger.error(f"更新用户资料错误: {phone}, {error_msg}")
            return False, error_msg
    
    async def batch_update_profiles(self, accounts: List[Dict[str, Any]], **kwargs) -> Dict[str, Any]:
        """批量更新用户资料
        
        Args:
            accounts: 账户信息列表，每项包含 {phone, ...}
            **kwargs: 要更新的资料字段模板
            
        Returns:
            操作结果统计
        """
        self._logger.info(f"开始批量更新用户资料, 共 {len(accounts)} 个账户")
        
        results = {
            'total': len(accounts),
            'success': 0,
            'failed': 0,
            'details': []
        }
        
        for i, account in enumerate(accounts):
            phone = account['phone']
            
            # 处理随机标签
            update_fields = self._process_template_fields(kwargs.copy(), phone)
            
            self.notify.emit("批量操作进度", f"正在更新: {phone}", "info")
            
            try:
                success, result = await self.update_profile(phone, **update_fields)
                
                detail = {
                    'phone': phone,
                    'success': success,
                    'fields': update_fields
                }
                
                if success:
                    results['success'] += 1
                else:
                    results['failed'] += 1
                    detail['error'] = result
                
                results['details'].append(detail)
            except Exception as e:
                self._logger.error(f"更新用户资料异常: {phone}, {str(e)}")
                results['failed'] += 1
                results['details'].append({
                    'phone': phone,
                    'success': False,
                    'fields': update_fields,
                    'error': str(e)
                })
        
        self.notify.emit("批量操作完成", f"批量更新用户资料完成: 总计 {results['total']}, 成功 {results['success']}, 失败 {results['failed']}", "info")
        self._logger.info(f"批量更新用户资料完成: 总计 {results['total']}, 成功 {results['success']}, 失败 {results['failed']}")
        
        return results
    
    async def _get_user_info(self, client: TelegramClient) -> Dict[str, Any]:
        """获取用户详细信息
        
        Args:
            client: Telegram客户端实例
            
        Returns:
            用户信息字典
        """
        me = await client.get_me()
        
        # 提取基本用户信息
        user_info = self._extract_user_info(me)
        
        # 获取用户全名
        if hasattr(me, 'phone'):
            user_info['phone'] = me.phone
        
        # 获取其他额外信息
        try:
            # 获取用户状态
            user_full = await client(functions.users.GetFullUserRequest(me.id))
            if hasattr(user_full, 'about'):
                user_info['bio'] = user_full.about
        except Exception as e:
            self._logger.warning(f"获取用户额外信息失败: {str(e)}")
        
        return user_info
    
    @staticmethod
    def _extract_user_info(user) -> Dict[str, Any]:
        """从用户对象中提取信息
        
        Args:
            user: Telegram用户对象
            
        Returns:
            用户信息字典
        """
        if user is None:
            # 用户为None，返回空信息
            return {
                'id': 0,
                'first_name': '',
                'last_name': '',
                'username': '',
                'phone': '',
                'bot': False,
                'verified': False,
                'restricted': False,
                'photo': False,
                'status_type': 'unknown'
            }
        
        user_info = {
            'id': getattr(user, 'id', 0),
            'first_name': getattr(user, 'first_name', '') or '',
            'last_name': getattr(user, 'last_name', '') or '',
            'username': getattr(user, 'username', '') or '',
            'phone': getattr(user, 'phone', '') or '',
            'bot': getattr(user, 'bot', False),
            'verified': getattr(user, 'verified', False),
            'restricted': getattr(user, 'restricted', False),
            'photo': bool(getattr(user, 'photo', None)),
            'status': getattr(user, 'status', None)
        }
        
        # 处理最后在线时间
        if hasattr(user, 'status') and user.status is not None:
            try:
                if isinstance(user.status, types.UserStatusOnline):
                    user_info['status_type'] = 'online'
                elif isinstance(user.status, types.UserStatusOffline):
                    user_info['status_type'] = 'offline'
                    user_info['last_online'] = user.status.was_online.isoformat()
                elif isinstance(user.status, types.UserStatusRecently):
                    user_info['status_type'] = 'recently'
                elif isinstance(user.status, types.UserStatusLastWeek):
                    user_info['status_type'] = 'last_week'
                elif isinstance(user.status, types.UserStatusLastMonth):
                    user_info['status_type'] = 'last_month'
                else:
                    user_info['status_type'] = 'unknown'
            except Exception:
                user_info['status_type'] = 'unknown'
        else:
            user_info['status_type'] = 'unknown'
        
        return user_info
    
    def _process_template_fields(self, template: Dict[str, Any], seed: str) -> Dict[str, Any]:
        """处理模板字段中的随机标签
        
        Args:
            template: 模板字段
            seed: 用于随机种子的字符串
            
        Returns:
            处理后的字段字典
        """
        import re
        import random
        import string
        
        # 设置种子以确保同一账户的相同标签生成相同结果，不同账户生成不同结果
        random.seed(seed + str(time.time()))
        
        def replace_random_tag(match):
            tag_type = match.group(1)
            length = match.group(2)
            
            if '-' in length:
                min_len, max_len = map(int, length.split('-'))
                length = random.randint(min_len, max_len)
            else:
                length = int(length)
            
            if tag_type.lower() == '随机字符':
                chars = string.ascii_letters + string.digits
                return ''.join(random.choice(chars) for _ in range(length))
            elif tag_type.lower() == '随机数字':
                return ''.join(random.choice(string.digits) for _ in range(length))
            elif tag_type.lower() == '随机字母':
                return ''.join(random.choice(string.ascii_letters) for _ in range(length))
            else:
                return match.group(0)
        
        result = {}
        
        for key, value in template.items():
            if isinstance(value, str):
                # 匹配类似 {随机字符3} 或 {随机数字3-5} 的格式
                pattern = r'\{(随机字符|随机数字|随机字母)(\d+(?:-\d+)?)\}'
                result[key] = re.sub(pattern, replace_random_tag, value)
            else:
                result[key] = value
        
        return result
    
    def is_client_connected(self, phone: str) -> bool:
        """检查客户端是否已连接
        
        Args:
            phone: 手机号
            
        Returns:
            客户端是否已连接
        """
        if phone not in self._clients:
            return False
        
        client = self._clients[phone]
        return client.is_connected()
    
    def get_active_clients(self) -> List[str]:
        """获取所有活跃的客户端手机号列表
        
        Returns:
            活跃客户端的手机号列表
        """
        return [phone for phone, client in self._clients.items() if client.is_connected()]
    
    def get_client(self, phone: str) -> Optional[TelegramClient]:
        """根据手机号获取客户端实例
        
        Args:
            phone: 手机号
            
        Returns:
            TelegramClient实例，如果不存在返回None
        """
        return self._clients.get(phone)
    
    def __del__(self):
        """析构函数，确保在对象销毁时清理资源"""
        try:
            # 使用事件循环运行清理方法
            if asyncio.get_event_loop().is_running():
                asyncio.create_task(self.cleanup_async())
            else:
                self._logger.warning("事件循环未运行，可能无法正确清理资源")
        except Exception as e:
            self._logger.error(f"析构函数中清理资源失败: {e}")
    
    def cleanup(self):
        """清理资源，断开所有连接"""
        # 使用qasync运行异步清理操作
        try:
            qasync.run(self.cleanup_async())
        except RuntimeError:
            # 如果事件循环已运行，创建任务
            if asyncio.get_event_loop().is_running():
                asyncio.create_task(self.cleanup_async())
            else:
                self._logger.error("无法清理资源：事件循环未运行")
    
    async def cleanup_async(self):
        """异步清理所有资源"""
        self._logger.info("开始清理资源并断开所有连接")
        
        # 断开所有客户端连接
        await self.disconnect_all_clients()
        
        # 清空客户端字典
        self._clients.clear()
        
        # 清空登录过程
        for login_info in self._login_processes.values():
            if 'client' in login_info and login_info['client']:
                try:
                    if login_info['client'].is_connected():
                        await login_info['client'].disconnect()
                except Exception as e:
                    self._logger.error(f"断开登录过程客户端连接失败: {e}")
        
        self._login_processes.clear()
        
        self._logger.info("资源清理完成")

    async def _send_progress_async(self, title: str, message: str, status: str):
        """异步发送进度信息，避免阻塞主流程
        
        Args:
            title: 进度标题
            message: 进度消息
            status: 状态类型 (info, warning, error, success)
        """
        try:
            from utils.signal_bus import signal_bus
            signal_bus.send_progress(title, message, status)
        except Exception as e:
            # 避免因为发送进度信息失败而影响主流程
            self._logger.error(f"发送进度信息失败: {e}")
            pass 

    async def _verify_client_authorization(self, client: TelegramClient, phone: str = None) -> Tuple[bool, Union[Dict[str, Any], str]]:
        """验证客户端授权状态的通用方法
        
        Args:
            client: Telegram客户端实例
            phone: 手机号（可选，用于日志和信号）
            
        Returns:
            (授权状态, 用户信息或错误消息)
        """
        try:
            # 检查客户端是否已授权
            if await client.is_user_authorized():
                # 获取用户信息
                user_info = await self._get_user_info(client)
                
                # 如果提供了手机号，记录日志和发送信号
                if phone:
                    self._logger.info(f"账户已授权: {phone}")
                    
                return True, user_info
            else:
                error_msg = "客户端未授权，需要重新登录"
                if phone:
                    self._logger.warning(f"账户未授权: {phone}")
                
                return False, error_msg
                
        except Exception as e:
            error_msg = f"验证客户端授权失败: {str(e)}"
            self._logger.error(error_msg)
            return False, error_msg 

    async def batch_auto_login(self, accounts: List[Dict[str, Any]], max_concurrent: int = 5) -> Dict[str, Any]:
        """批量自动登录账户
        
        Args:
            accounts: 账户列表,每项包含 {phone, proxy} 信息
            max_concurrent: 最大并发数
            
        Returns:
            登录结果统计
        """
        self._logger.info(f"开始批量自动登录, 共 {len(accounts)} 个账户, 最大并发 {max_concurrent}")
        
        results = {
            'total': len(accounts),
            'success': 0,
            'failed': 0,
            'details': []
        }
        
        # 创建信号量控制并发
        semaphore = asyncio.Semaphore(max_concurrent)
        
        # 使用队列管理任务
        queue = asyncio.Queue()
        for account in accounts:
            await queue.put(account)
            
        # 完成计数
        completed = 0
        total = len(accounts)
        
        # 任务列表，用于跟踪和取消
        worker_tasks = []
        
        async def login_worker():
            nonlocal completed
            
            try:
                while not queue.empty():
                    try:
                        account = await queue.get()
                        phone = account.get('phone', '')
                        proxy = account.get('proxy')
                        
                        async with semaphore:
                            self.notify.emit("批量操作进度", f"正在登录: {phone}", "info")
                            
                            try:
                                # 检查是否已经连接
                                if phone in self._clients and self._clients[phone].is_connected():
                                    is_authorized, auth_result = await self._verify_client_authorization(self._clients[phone], phone)
                                    if is_authorized:
                                        results['success'] += 1
                                        results['details'].append({
                                            'phone': phone,
                                            'success': True,
                                            'result': auth_result
                                        })
                                        completed += 1
                                        queue.task_done()
                                        continue
                                
                                # 创建并连接客户端(只尝试一次)
                                session_path = self.get_session_path(phone)
                                success, result = await self._create_and_connect_client(session_path, proxy)
                                
                                if success:
                                    client = result
                                    is_authorized, auth_result = await self._verify_client_authorization(client, phone)
                                    
                                    if is_authorized:
                                        self._clients[phone] = client
                                        results['success'] += 1
                                        results['details'].append({
                                            'phone': phone,
                                            'success': True,
                                            'result': auth_result
                                        })
                                    else:
                                        # 确保断开未授权的客户端
                                        if client and client.is_connected():
                                            await client.disconnect()
                                        results['failed'] += 1
                                        results['details'].append({
                                            'phone': phone,
                                            'success': False,
                                            'error': "账户未授权"
                                        })
                                else:
                                    results['failed'] += 1
                                    results['details'].append({
                                        'phone': phone,
                                        'success': False,
                                        'error': result
                                    })
                                    
                            except asyncio.CancelledError:
                                # 正确处理取消请求
                                self._logger.info(f"登录任务 {phone} 被取消")
                                # 保存部分结果
                                results['failed'] += 1
                                results['details'].append({
                                    'phone': phone,
                                    'success': False,
                                    'error': "任务被取消"
                                })
                                # 重新引发取消异常
                                raise
                            except Exception as e:
                                results['failed'] += 1
                                results['details'].append({
                                    'phone': phone,
                                    'success': False,
                                    'error': str(e)
                                })
                                self._logger.error(f"登录账户异常: {phone}, 错误: {e}")
                                
                            finally:
                                # 短暂等待避免请求过快
                                await asyncio.sleep(2)  # 增加等待时间到2秒
                                completed += 1
                                self.notify.emit("批量操作进度", f"批量操作进度:已完成: {completed}/{total}", "info")
                                queue.task_done()
                    except asyncio.CancelledError:
                        # 处理工作者协程被取消的情况
                        self._logger.info("登录工作协程被取消")
                        break
                    except Exception as e:
                        self._logger.error(f"登录工作协程异常: {e}")
                        if queue.qsize() > 0:
                            queue.task_done()  # 确保完成当前任务
            except asyncio.CancelledError:
                self._logger.info("登录工作协程被取消")
                raise
            except Exception as e:
                self._logger.error(f"登录工作协程未捕获的异常: {e}")
                
        try:
            # 启动工作协程
            workers_count = min(max_concurrent, len(accounts))
            worker_tasks = [asyncio.create_task(login_worker()) for _ in range(workers_count)]
            
            # 等待队列处理完成，带超时控制
            try:
                await asyncio.wait_for(queue.join(), timeout=60 * 5)  # 设置5分钟超时
            except asyncio.TimeoutError:
                self._logger.warning("批量登录队列处理超时")
                results['error'] = "批量登录处理超时"
            
            # 取消工作协程
            for task in worker_tasks:
                if not task.done():
                    task.cancel()
            
            # 等待所有工作协程处理完成
            if worker_tasks:
                await asyncio.wait(worker_tasks, return_when=asyncio.ALL_COMPLETED)
            
            self.notify.emit("批量操作完成", f"批量登录完成: 总计 {results['total']}, 成功 {results['success']}, 失败 {results['failed']}", "success")
            self._logger.info(f"批量登录完成: 总计 {results['total']}, 成功 {results['success']}, 失败 {results['failed']}")
            
            return results
            
        except asyncio.CancelledError:
            self._logger.info("批量登录主任务被取消")
            # 取消所有工作协程
            for task in worker_tasks:
                if not task.done():
                    task.cancel()
            
            # 等待所有工作协程处理完成，但设置短超时
            if worker_tasks:
                try:
                    await asyncio.wait(worker_tasks, timeout=5)
                except Exception as e:
                    self._logger.error(f"等待工作协程取消时出错: {e}")
            
            # 返回部分结果
            results['error'] = "操作被取消"
            self.notify.emit("操作取消", "批量登录操作被取消", "warning")
            return results
            
        except Exception as e:
            error_msg = f"批量登录过程发生异常: {str(e)}"
            self._logger.error(error_msg)
            results['error'] = error_msg
            
            # 取消所有工作协程
            for task in worker_tasks:
                if not task.done():
                    task.cancel()
            
            # 等待所有工作协程处理完成，但设置短超时
            if worker_tasks:
                try:
                    await asyncio.wait(worker_tasks, timeout=5)
                except Exception as e:
                    self._logger.error(f"等待工作协程取消时出错: {e}")
            
            self.notify.emit("操作异常", f"批量登录过程发生异常: {str(e)}", "error")
            return results

    async def check_connection(self, phone: str) -> bool:
        """检查客户端连接状态
        
        Args:
            phone: 手机号
            
        Returns:
            连接状态：True 表示已连接，False 表示未连接
        """
        self._logger.debug(f"客户端列表{self._clients}")
        # 检查客户端是否存在
        if phone not in self._clients:
            self._logger.warning(f"客户端不存在: {phone}")
            return False
            
        client = self._clients[phone]
        
        try:
            # 检查连接状态
            is_connected = client.is_connected()
            
            # 如果客户端显示为已连接，进一步验证是否可用
            if is_connected:
                # 尝试执行一个轻量级操作来验证连接的实际状态
                try:
                    # 获取 ping 时间来验证连接
                    await asyncio.wait_for(client.get_me(), timeout=5.0)
                    self._logger.info(f"客户端 {phone} 已连接且可用")
                    return True
                except asyncio.TimeoutError:
                    self._logger.warning(f"客户端 {phone} 连接已超时，可能不可用")
                    return False
                except Exception as e:
                    self._logger.warning(f"客户端 {phone} 连接验证失败: {e}")
                    return False
            else:
                self._logger.info(f"客户端 {phone} 未连接")
                return False
                
        except Exception as e:
            self._logger.error(f"检查客户端连接状态异常: {phone}, {e}")
            return False
    
    async def get_dialogs(self, phone: str, dialog_type: str = None) -> Union[List[Dict[str, Any]], str]:
        """获取客户端的对话列表
        
        Args:
            phone: 手机号
            dialog_type: 对话类型，'group' 或 'channel'，None表示全部
            
        Returns:
            对话列表，每个对话以字典形式表示，失败则返回错误信息
        """
        self._logger.info(f"获取客户端对话列表: {phone}, 类型: {dialog_type or '全部'}")
        
        # 检查客户端是否存在
        if phone not in self._clients:
            error_msg = f"客户端不存在: {phone}"
            self._logger.warning(error_msg)
            return error_msg
            
        client = self._clients[phone]
        
        # 检查连接状态
        if not client.is_connected():
            try:
                # 尝试连接客户端
                self._logger.info(f"尝试连接客户端: {phone}")
                await client.connect()
                if not client.is_connected():
                    error_msg = f"无法连接客户端: {phone}"
                    self._logger.warning(error_msg)
                    return error_msg
            except Exception as e:
                error_msg = f"连接客户端异常: {phone}, {e}"
                self._logger.error(error_msg)
                return error_msg
        
        try:
            # 获取对话列表
            dialogs = await client.get_dialogs(ignore_migrated=True)
            
            # 直接处理对话信息
            result = []
            for dialog in dialogs:
                entity = dialog.entity
                if isinstance(entity, (types.Channel, types.Chat)):
                    # 直接从entity获取所有需要的信息
                    dialog_info = {
                        'id': entity.id,
                        'title': entity.title or dialog.title or '未命名',
                        'username': getattr(entity, 'username', ''),
                        'type': 'group' if isinstance(entity, types.Chat) or (isinstance(entity, types.Channel) and entity.megagroup) else 'channel',
                        'members_count': getattr(entity, 'participants_count', 0),
                        'is_admin': getattr(entity, 'admin_rights', False) or getattr(entity, 'creator', False)
                    }
                    
                    # 根据类型过滤
                    if dialog_type is None or dialog_info['type'] == dialog_type:
                        result.append(dialog_info)
            
            self._logger.info(f"获取对话列表成功: {phone}, 共 {len(result)} 个符合条件的对话")
            return result
            
        except Exception as e:
            error_msg = f"获取对话列表异常: {phone}, {e}"
            self._logger.error(error_msg)
            return error_msg

    async def _handle_disconnection(self, client):
        """处理客户端断开连接事件
        
        Args:
            client: 断开连接的客户端
        """
        # 查找断开连接的客户端对应的手机号
        phone = None
        for p, c in self._clients.items():
            if c == client:
                phone = p
                break
                
        if not phone:
            self._logger.warning("无法确定断开连接的客户端对应的手机号")
            return
            
        self._logger.info(f"检测到客户端断开连接: {phone}")
        
        # 发送断开连接通知
        self.notify.emit("连接断开", f"账户 {phone} 的Telegram客户端连接已断开", "warning")
        self.connection_status_changed.emit(phone, False)
        
        # 尝试重新连接
        try:
            self._logger.info(f"尝试重新连接客户端: {phone}")
            await client.connect()
            
            if client.is_connected():
                self._logger.info(f"客户端重新连接成功: {phone}")
                self.notify.emit("连接恢复", f"账户 {phone} 的Telegram客户端已重新连接", "success")
                self.connection_status_changed.emit(phone, True)
            else:
                self._logger.warning(f"客户端重新连接失败: {phone}")
        except Exception as e:
            self._logger.error(f"重新连接客户端时出错: {phone}, {e}")
            self.notify.emit("连接错误", f"账户 {phone} 重连失败: {str(e)}", "error")

    def register_monitoring_handlers(self, task_id: str, account_phone: str, chat_ids: List[int], callback: Callable) -> Tuple[bool, str]:
        """注册监控事件处理器
        
        Args:
            task_id: 任务ID
            account_phone: 账户手机号
            chat_ids: 要监控的群组ID列表
            callback: 消息回调函数
            
        Returns:
            (成功标志, 消息)
        """
        try:
            if account_phone not in self._clients:
                return False, f"找不到账户 {account_phone} 的客户端"
                
            client = self._clients[account_phone]
            if not client.is_connected():
                return False, f"账户 {account_phone} 的客户端未连接"
                
            # 创建事件处理器
            handler_tuple = (callback, events.NewMessage(chats=chat_ids))
            client.add_event_handler(*handler_tuple)
            
            # 记录处理器信息
            if task_id not in self._event_handlers:
                self._event_handlers[task_id] = {}
            if account_phone not in self._event_handlers[task_id]:
                self._event_handlers[task_id][account_phone] = []
                
            self._event_handlers[task_id][account_phone].append(handler_tuple)
            
            self._logger.info(f"任务 {task_id} 为账户 {account_phone} 注册了 {len(chat_ids)} 个群组的监控")
            return True, "事件处理器注册成功"
            
        except Exception as e:
            error_msg = f"注册事件处理器失败: {str(e)}"
            self._logger.error(error_msg, exc_info=True)
            return False, error_msg
            
    def unregister_monitoring_handlers(self, task_id: str) -> Tuple[bool, str]:
        """注销任务的所有事件处理器
        
        Args:
            task_id: 任务ID
            
        Returns:
            (成功标志, 消息)
        """
        try:
            if task_id not in self._event_handlers:
                return True, "任务没有注册的事件处理器"
                
            fail_count = 0
            for account_phone, handlers in self._event_handlers[task_id].items():
                if account_phone in self._clients:
                    client = self._clients[account_phone]
                    for handler_tuple in handlers:
                        try:
                            callback, event_builder = handler_tuple
                            client.remove_event_handler(callback, event_builder)
                        except Exception as e:
                            self._logger.error(f"移除事件处理器失败: {account_phone}, {str(e)}")
                            fail_count += 1
                            
            # 清理记录
            del self._event_handlers[task_id]
            
            if fail_count > 0:
                return False, f"部分事件处理器移除失败: {fail_count} 个"
            return True, "所有事件处理器已移除"
            
        except Exception as e:
            error_msg = f"注销事件处理器失败: {str(e)}"
            self._logger.error(error_msg, exc_info=True)
            return False, error_msg

    async def send_text_message(self, phone: str, chat_id: Union[int, str], text: str, parse_mode: Optional[str] = None) -> Tuple[bool, Union[types.Message, str]]:
        """发送文本消息
        
        Args:
            phone: 账户手机号
            chat_id: 目标对话ID (可以是int或username)
            text: 消息文本
            parse_mode: 'md' for Markdown, 'html' for HTML
            
        Returns:
            (成功标志, Telethon Message 对象或错误消息)
        """
        self._logger.info(f"账户 {phone} 准备向 {chat_id} 发送文本消息")
        client = self.get_client(phone)
        if not client or not client.is_connected():
            error_msg = f"客户端 {phone} 未连接或不存在"
            self._logger.warning(error_msg)
            return False, error_msg

        try:
            # 尝试将 chat_id 转换为 entity
            target_entity = await client.get_entity(chat_id)
            sent_message = await client.send_message(target_entity, text, parse_mode=parse_mode)
            self._logger.info(f"账户 {phone} 成功向 {chat_id} 发送消息 ID: {sent_message.id}")
            return True, sent_message
        except FloodWaitError as e:
            error_msg = f"发送消息被限制 (FloodWaitError): {phone} -> {chat_id}, 等待 {e.seconds} 秒"
            self._logger.error(error_msg)
            # 可以考虑将 FloodWaitError 特殊处理，例如返回等待时间
            return False, f"FloodWait: {e.seconds}"
        except UserDeactivatedBanError as e:
            error_msg = f"账户 {phone} 已被封禁 (UserDeactivatedBanError)，无法发送消息至 {chat_id}: {e}"
            self._logger.error(error_msg)
            # 特殊处理封号情况
            return False, "account_banned"
        except Exception as e:
            error_msg = f"账户 {phone}向 {chat_id} 发送文本消息失败: {e}"
            self._logger.error(error_msg, exc_info=True)
            return False, str(e)

    async def send_file_message(
        self, 
        phone: str, 
        chat_id: Union[int, str], 
        file_path: str, 
        caption: Optional[str] = None, 
        media_type = None, # Forward ref, actual type from message_model
        progress_callback: Optional[Callable] = None,
        file_name: Optional[str] = None,
        parse_mode: Optional[str] = None
    ) -> Tuple[bool, Union[types.Message, str]]:
        """发送文件/媒体消息
        
        Args:
            phone: 账户手机号
            chat_id: 目标对话ID
            file_path: 本地文件路径
            caption: 媒体说明
            media_type: 媒体类型 (MediaType.PHOTO, MediaType.VIDEO, etc.)
            progress_callback: 上传进度回调
            file_name: 自定义文件名 (主要用于documents)
            parse_mode: 标题的解析模式 ('md' 或 'html')

        Returns:
            (成功标志, Telethon Message 对象或错误消息)
        """
        self._logger.info(f"账户 {phone} 准备向 {chat_id} 发送文件: {file_path} (类型: {media_type})")
        client = self.get_client(phone)
        if not client or not client.is_connected():
            error_msg = f"客户端 {phone} 未连接或不存在"
            self._logger.warning(error_msg)
            return False, error_msg

        if not os.path.exists(file_path):
            error_msg = f"文件不存在: {file_path}"
            self._logger.error(error_msg)
            return False, error_msg

        attributes = []
        if media_type: # Check if media_type is provided and valid before using it
            # Dynamically import MediaType to avoid circular dependency at module load time
            from data.models.message import MediaType as MessageModelMediaType

            if media_type == MessageModelMediaType.VIDEO:
                # For videos, you might want to add attributes like duration, w, h if known
                # This usually requires a library like ffmpeg to extract metadata
                # attributes.append(types.DocumentAttributeVideo(duration=0, w=0, h=0, supports_streaming=True))
                pass # Example: Telethon usually infers this
            elif media_type == MessageModelMediaType.AUDIO:
                # attributes.append(types.DocumentAttributeAudio(duration=0, title="Audio Title", performer="Performer"))
                pass
            elif media_type == MessageModelMediaType.VOICE:
                 attributes.append(types.DocumentAttributeAudio(voice=True, duration=0)) # duration will be filled by TG

        # Ensure file_name is used if provided, especially for documents
        doc_attributes = attributes
        if file_name and (media_type is None or media_type == MessageModelMediaType.DOCUMENT):
             doc_attributes.append(types.DocumentAttributeFilename(file_name=file_name))


        try:
            target_entity = await client.get_entity(chat_id)
            # force_document=True for some types if you want them sent as generic files
            # Using client.send_file for simplicity, it handles most types automatically
            sent_message = await client.send_file(
                target_entity,
                file_path,
                caption=caption,
                progress_callback=progress_callback,
                attributes=doc_attributes if doc_attributes else None, # Pass attributes if any
                parse_mode=parse_mode,
                # voice_note= (media_type == MessageModelMediaType.VOICE), # For voice notes
                # video_note= (media_type == MessageModelMediaType.ROUND_VIDEO_NOTE) # If you add round video
            )
            self._logger.info(f"账户 {phone} 成功向 {chat_id} 发送文件 {file_path}, 消息 ID: {sent_message.id}")
            return True, sent_message
        except FloodWaitError as e:
            error_msg = f"发送文件被限制 (FloodWaitError): {phone} -> {chat_id}, 等待 {e.seconds} 秒"
            self._logger.error(error_msg)
            return False, f"FloodWait: {e.seconds}"
        except UserDeactivatedBanError as e:
            error_msg = f"账户 {phone} 已被封禁 (UserDeactivatedBanError)，无法发送文件至 {chat_id}: {e}"
            self._logger.error(error_msg)
            return False, "account_banned"
        except Exception as e:
            error_msg = f"账户 {phone}向 {chat_id} 发送文件 {file_path} 失败: {e}"
            self._logger.error(error_msg, exc_info=True)
            return False, str(e)

    async def forward_messages(
        self, 
        phone: str, 
        chat_id: Union[int, str], 
        from_chat_id: Union[int, str], 
        message_ids: List[int], 
        drop_author: bool = False
    ) -> Tuple[bool, Union[List[types.Message], str]]:
        """转发消息
        
        Args:
            phone: 账户手机号
            chat_id: 目标对话ID
            from_chat_id: 原始消息所在对话ID
            message_ids: 要转发的消息ID列表
            drop_author: 是否隐藏原始发送者 (silent forwarding)
            
        Returns:
            (成功标志, 成功时是 Telethon Message 对象列表, 失败时是错误消息)
        """
        self._logger.info(f"账户 {phone} 准备从 {from_chat_id} 向 {chat_id} 转发消息 IDs: {message_ids}")
        client = self.get_client(phone)
        if not client or not client.is_connected():
            error_msg = f"客户端 {phone} 未连接或不存在"
            self._logger.warning(error_msg)
            return False, error_msg

        if not message_ids:
            return False, "没有提供消息ID进行转发"

        try:
            target_entity = await client.get_entity(chat_id)
            source_entity = await client.get_entity(from_chat_id)
            
            sent_messages = await client.forward_messages(
                entity=target_entity,
                messages=message_ids,
                from_peer=source_entity,
                silent=drop_author # 'silent' in Telethon often means drop_author/notifications
            )
            # client.forward_messages can return a single message or a list
            if not isinstance(sent_messages, list):
                sent_messages = [sent_messages]

            sent_message_ids = [m.id for m in sent_messages if m]
            self._logger.info(f"账户 {phone} 成功从 {from_chat_id} 向 {chat_id} 转发消息, 新消息 IDs: {sent_message_ids}")
            return True, sent_messages
        except FloodWaitError as e:
            error_msg = f"转发消息被限制 (FloodWaitError): {phone} -> {chat_id}, 等待 {e.seconds} 秒"
            self._logger.error(error_msg)
            return False, f"FloodWait: {e.seconds}"
        except UserDeactivatedBanError as e:
            error_msg = f"账户 {phone} 已被封禁 (UserDeactivatedBanError)，无法转发消息至 {chat_id}: {e}"
            self._logger.error(error_msg)
            return False, "account_banned"
        except Exception as e:
            # Check if the error indicates some messages could not be forwarded
            # e.g. "MessageIDsInvalidError" or specific errors per message
            # For simplicity, treating any error as a failure for the batch.
            error_msg = f"账户 {phone} 从 {from_chat_id} 向 {chat_id} 转发消息 IDs {message_ids} 失败: {e}"
            self._logger.error(error_msg, exc_info=True)
            return False, str(e)
