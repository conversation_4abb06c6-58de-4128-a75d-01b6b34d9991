#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Telegram客户端工作线程
单独线程运行异步Telegram客户端操作，避免阻塞主UI线程
"""

import os
import asyncio
import threading
import time
from typing import Dict, List, Optional, Any, Callable, Union

import qasync
from PySide6.QtCore import QObject, Signal, QThread, QMetaObject, Qt, Slot, QMutex, QWaitCondition

from utils.logger import get_logger
from core.telegram.client_manager import TelegramClientManager
from frameConf.setting import API_ID, API_HASH

class TelegramClientWorker(QThread):
    """Telegram客户端工作线程，运行一个asyncio事件循环处理所有Telegram异步操作"""
    
    notify = Signal(str, object, str)
    
    def __init__(self, api_id: int = API_ID, api_hash: str = API_HASH, session_dir: str = None):
        """初始化Telegram客户端工作线程
        
        Args:
            api_id: Telegram API ID
            api_hash: Telegram API Hash
            session_dir: Session文件存储目录
        """
        super().__init__()
        
        # 初始化配置
        self._api_id = api_id
        self._api_hash = api_hash
        self._session_dir = session_dir
        
        # 事件循环和客户端管理器
        self._loop = None
        self._client_manager = None
        
        # 线程控制
        self._mutex = QMutex()
        self._condition = QWaitCondition()
        self._running = False
        self._started = False
        
        # 任务队列和结果缓存
        self._tasks = []
        self._results = {}
        self._task_id = 0
        
        # 日志记录器
        self._logger = get_logger("core.telegram.client_worker")
        self._logger.info("Telegram客户端工作线程初始化")
    
    def run(self):
        """线程主函数，创建事件循环和客户端管理器"""
        self._logger.info("Telegram客户端工作线程开始运行")
        
        # 创建新的事件循环
        self._loop = asyncio.new_event_loop()
        asyncio.set_event_loop(self._loop)
        
        # 创建客户端管理器
        self._client_manager = TelegramClientManager(
            api_id=self._api_id, 
            api_hash=self._api_hash, 
            session_dir=self._session_dir
        )
        
        # 连接信号
        self._connect_signals()
        
        # 设置运行标志
        self._mutex.lock()
        self._running = True
        self._started = True
        self._condition.wakeAll()
        self._mutex.unlock()
        
        # 运行事件循环
        try:
            self._loop.run_forever()
        except Exception as e:
            self._logger.error(f"事件循环异常: {e}")
        finally:
            # 清理资源
            try:
                pending_tasks = asyncio.all_tasks(self._loop)
                if pending_tasks:
                    # 取消所有待处理任务
                    for task in pending_tasks:
                        task.cancel()
                    # 等待任务取消完成
                    self._loop.run_until_complete(asyncio.gather(*pending_tasks, return_exceptions=True))
                
                # 断开所有客户端连接
                self._loop.run_until_complete(self._client_manager.disconnect_all_clients())
                
                # 关闭事件循环
                self._loop.run_until_complete(self._loop.shutdown_asyncgens())
                self._loop.close()
            except Exception as e:
                self._logger.error(f"清理资源异常: {e}")
        
        self._logger.info("Telegram客户端工作线程已退出")
    
    def _connect_signals(self):
        """连接客户端管理器的信号到本类的信号"""
        self._client_manager.notify.connect(self.notify.emit)
    
    def wait_for_started(self):
        """等待线程启动完成"""
        self._mutex.lock()
        if not self._started:
            self._condition.wait(self._mutex)
        self._mutex.unlock()
    
    def stop(self):
        """停止线程"""
        self._logger.info("正在停止Telegram客户端工作线程")
        
        self._mutex.lock()
        self._running = False
        self._mutex.unlock()
        
        # 停止事件循环
        if self._loop and self._loop.is_running():
            self._loop.call_soon_threadsafe(self._loop.stop)
        
        # 等待线程结束
        self.wait()
        self._logger.info("Telegram客户端工作线程已停止")
    
    def _add_task(self, coro_func, *args, **kwargs):
        """添加异步任务到事件循环
        
        Args:
            coro_func: 协程函数
            *args: 协程函数参数
            **kwargs: 协程函数关键字参数
            
        Returns:
            任务ID
        """
        self.wait_for_started()
        
        # 生成任务ID
        self._mutex.lock()
        self._task_id += 1
        task_id = self._task_id
        self._mutex.unlock()
        
        # 创建任务包装函数
        async def task_wrapper():
            try:
                result = await coro_func(*args, **kwargs)
                # 保存结果
                self._mutex.lock()
                self._results[task_id] = (True, result)
                self._mutex.unlock()
                return result
            except Exception as e:
                self._logger.error(f"任务异常 [id={task_id}]: {e}")
                # 保存异常
                self._mutex.lock()
                self._results[task_id] = (False, str(e))
                self._mutex.unlock()
                return None
        
        # 提交任务到事件循环
        asyncio.run_coroutine_threadsafe(task_wrapper(), self._loop)
        return task_id
    
    async def get_task_result(self, task_id, timeout=None):
        """获取任务结果
        
        Args:
            task_id: 任务ID
            timeout: 超时时间（秒），None表示一直等待
            
        Returns:
            (success, result) 元组，success表示是否成功，result为结果或错误消息
        """
        start_time = time.monotonic()
        check_interval = 0.1  # 初始检查间隔
        max_interval = 1.0  # 最大检查间隔
        
        try:
            while timeout is None or (time.monotonic() - start_time) < timeout:
                # 使用锁检查结果
                self._mutex.lock()
                if task_id in self._results:
                    result = self._results.pop(task_id)
                    self._mutex.unlock()
                    return result
                self._mutex.unlock()
                
                # 指数退避策略，逐渐增加等待时间
                check_interval = min(check_interval * 1.5, max_interval)
                await asyncio.sleep(check_interval)
                
            # 超时
            return False, "任务结果获取超时"
        except Exception as e:
            return False, f"获取任务结果异常: {str(e)}"
    
    # ================ 账户登录相关方法 ================
    
    @Slot(str, dict)
    def start_login(self, phone, proxy=None):
        """开始登录过程，发送验证码"""
        return self._add_task(self._client_manager.start_login, phone, proxy)
    
    @Slot(str, str)
    def submit_code(self, phone, code,password):
        """提交验证码"""
        return self._add_task(self._client_manager.submit_code, phone, code,password)
    

    
    # ================ Session导入相关方法 ================
    
    @Slot(str, dict)
    def import_session(self, session_path, proxy=None):
        """导入单个session文件"""
        return self._add_task(self._client_manager.import_session, session_path, proxy)
    
    @Slot(list, int)
    def batch_import_sessions(self, session_files, max_concurrent=5):
        """批量导入session文件"""
        return self._add_task(self._client_manager.batch_import_sessions, session_files, max_concurrent)
    
    # ================ 客户端连接管理相关方法 ================
    
    @Slot(str, dict)
    def connect_client(self, phone, proxy=None):
        """连接客户端"""
        return self._add_task(self._client_manager.connect_client, phone, proxy)
    
    @Slot(str)
    def disconnect_client(self, phone):
        """断开客户端连接"""
        return self._add_task(self._client_manager.disconnect_client, phone)
    
    @Slot()
    def disconnect_all_clients(self):
        """断开所有客户端连接"""
        return self._add_task(self._client_manager.disconnect_all_clients)
    
    @Slot(str)
    def logout_client(self, phone):
        """登出客户端"""
        return self._add_task(self._client_manager.logout_client, phone)
    
    # ================ 用户信息相关方法 ================
    
    @Slot(str)
    def get_user_info(self, phone):
        """获取用户信息"""
        return self._add_task(self._client_manager.get_user_info, phone)
    
    # ================ 用户资料更新相关方法 ================
    
    def update_profile(self, phone, **kwargs):
        """更新用户资料"""
        return self._add_task(self._client_manager.update_profile, phone, **kwargs)
    
    def batch_update_profiles(self, accounts, **kwargs):
        """批量更新用户资料"""
        return self._add_task(self._client_manager.batch_update_profiles, accounts, **kwargs)
    
    # ================ 状态查询相关方法 ================
    
    @Slot(str, result=bool)
    def is_client_connected(self, phone):
        """检查客户端是否已连接（同步方法）"""
        # 这个方法需要在工作线程中执行才能获取准确结果
        if self._client_manager:
            return self._client_manager.is_client_connected(phone)
        return False
    
    @Slot(result=list)
    def get_active_clients(self):
        """获取所有活跃的客户端手机号列表（同步方法）"""
        # 这个方法需要在工作线程中执行才能获取准确结果
        if self._client_manager:
            return self._client_manager.get_active_clients()
        return []
    
    def batch_auto_login(self, accounts: List[Dict[str, Any]], max_concurrent: int = 5):
        """批量自动登录账户
        
        Args:
            accounts: 账户列表,每项包含 {phone, proxy} 信息
            max_concurrent: 最大并发数
            
        Returns:
            任务ID
        """
        # 检查_client_manager是否已初始化
        if self._client_manager is None:
            self._logger.error("批量自动登录失败：_client_manager未初始化")
            return self._add_task(lambda: (False, "client_manager未初始化，请确保TelegramClientWorker已正确启动"))
            
        return self._add_task(self._client_manager.batch_auto_login, accounts, max_concurrent)
    
    def check_connection(self, phone: str) -> str:
        """检查客户端连接状态
        
        Args:
            phone: 手机号
            
        Returns:
            任务ID
        """
        task_id = self._add_task(self._client_manager.check_connection, phone)
        return task_id
    
    def get_dialogs(self, phone: str, dialog_type: str = None) -> str:
        """获取对话列表
        
        Args:
            phone: 手机号
            dialog_type: 对话类型，'group' 或 'channel'，None表示全部
            
        Returns:
            任务ID
        """
        task_id = self._add_task(self._client_manager.get_dialogs, phone, dialog_type)
        return task_id
    
    def register_monitoring_handlers(self, task_id: str, account_phone: str, chat_ids: List[int], callback: Callable) -> str:
        """注册监控事件处理器
        
        Args:
            task_id: 任务ID
            account_phone: 账户手机号
            chat_ids: 要监控的群组ID列表
            callback: 消息回调函数
            
        Returns:
            任务ID
        """
        return self._add_task(self._client_manager.register_monitoring_handlers, task_id, account_phone, chat_ids, callback)
        
    def unregister_monitoring_handlers(self, task_id: str) -> str:
        """注销任务的所有事件处理器
        
        Args:
            task_id: 任务ID
            
        Returns:
            任务ID
        """
        return self._add_task(self._client_manager.unregister_monitoring_handlers, task_id)

    # ================ 消息发送相关方法 ================_MESSAGE_SENDING_METHODS

    def send_text_message(self, phone: str, chat_id: Union[int, str], text: str, parse_mode: Optional[str] = None) -> int:
        """异步发送文本消息
        Args:
            phone: 账户手机号
            chat_id: 目标对话ID
            text: 消息文本
            parse_mode: 'md' or 'html'
        Returns:
            任务ID，用于后续获取结果
        """
        if self._client_manager is None:
            self._logger.error("发送文本消息失败：_client_manager未初始化")
            # 返回一个立即失败的任务的ID
            async def failed_task(): return False, "client_manager未初始化"
            return self._add_task(failed_task)
        return self._add_task(self._client_manager.send_text_message, phone, chat_id, text, parse_mode)

    def send_file_message(self, phone: str, chat_id: Union[int, str], file_path: str, caption: Optional[str] = None, media_type: Optional[Any] = None, progress_callback: Optional[Callable] = None, file_name: Optional[str] = None, parse_mode: Optional[str] = None) -> int:
        """异步发送文件/媒体消息
        Args:
            phone: 账户手机号
            chat_id: 目标对话ID
            file_path: 文件路径
            caption: 媒体说明
            media_type: 来自 data.models.message.MediaType 的枚举值
            progress_callback: 进度回调
            file_name: 自定义文件名
            parse_mode: 标题解析模式
        Returns:
            任务ID
        """
        if self._client_manager is None:
            self._logger.error("发送文件消息失败：_client_manager未初始化")
            async def failed_task(): return False, "client_manager未初始化"
            return self._add_task(failed_task)
        return self._add_task(self._client_manager.send_file_message, phone, chat_id, file_path, caption, media_type, progress_callback, file_name, parse_mode)

    def forward_messages(self, phone: str, chat_id: Union[int, str], from_chat_id: Union[int, str], message_ids: List[int], drop_author: bool = False) -> int:
        """异步转发消息
        Args:
            phone: 账户手机号
            chat_id: 目标对话ID
            from_chat_id: 源对话ID
            message_ids: 要转发的消息ID列表
            drop_author: 是否移除作者信息
        Returns:
            任务ID
        """
        if self._client_manager is None:
            self._logger.error("转发消息失败：_client_manager未初始化")
            async def failed_task(): return False, "client_manager未初始化"
            return self._add_task(failed_task)
        return self._add_task(self._client_manager.forward_messages, phone, chat_id, from_chat_id, message_ids, drop_author) 