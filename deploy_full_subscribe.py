import os
from pathlib import Path
from shutil import copy, copytree
from distutils.sysconfig import get_python_lib
import sys
import time


# https://blog.csdn.net/qq_25262697/article/details/129302819
# https://www.cnblogs.com/happylee666/articles/16158458.html
args = [
    'nuitka',
    '--standalone',
    #'--windows-disable-console',   #注释掉则开启cmd窗口
    '--follow-import-to=app,core,data,frameConf,ui,utils',
    '--plugin-enable=pyside6,numpy,upx',
    '--upx-binary=upx/upx.exe',
    '--include-qt-plugins=sensible,styles',
    #'--msvc=latest',
    '--mingw64', #未下载，msvc和mingw64只能启动一个
    '--show-memory',
    '--show-progress',
     '--windows-icon-from-ico=ui/resources/images/logo.png',
    '--include-module=app',
    '--nofollow-import-to=numpy,scipy,PIL,pywin,colorthief,pycryptodome',
    '--follow-import-to=qfluentwidgets,telethon,app,core,data,frameConf,ui,utils',
    '--output-dir=dist/tele2',
    'main.py',
]
print(' '.join(args))
start = time.time()
os.system(' '.join(args))


# copy site-packages to dist folder
dist_folder = Path("dist/tele2/tele2.dist")
site_packages = Path(get_python_lib())

copied_libs = [
    "numpy", "numpy.libs", "scipy", "scipy.libs",
    "PIL", "pycryptodome", "urllib3", "colorthief.py",
]

for src in copied_libs:
    src = site_packages / src
    dist = dist_folder / src.name

    print(f"Coping site-packages `{src}` to `{dist}`")

    if src.is_file():
        copy(src, dist)
    else:
        copytree(src, dist)


# copy standard library
copied_files = ["ctypes", "hashlib.py", "email", "http", "hmac.py", "random.py", "secrets.py", "uuid.py"]
for file in copied_files:
    src = site_packages.parent / file
    dist = dist_folder / src.name

    print(f"Coping stand library `{src}` to `{dist}`")

    if src.is_file():
        copy(src, dist)
    else:
        copytree(src, dist)


# copy pyd
suffix = ".pyd" if sys.platform == "win32" else ".so"
copied_dlls = ["_uuid"]
for dll in copied_dlls:
    src = site_packages.parent.parent / "DLLs" / (dll + suffix)
    dist = dist_folder / src.name

    print(f"Coping stand library `{src}` to `{dist}`")
    copy(src, dist)


print("打包完成，耗时：{}分".format(int(time.time() - start)/60))