# 🚀 TeleTool - 专业的Telegram营销与管理工具

<div align="center">
  <img src="../../_media/logo.png" alt="TeleTool Logo" width="180">
  <br>
  <h3>高效 · 稳定 · 安全</h3>


  <p>
    <a href="#功能特点">功能特点</a> •
    <a href="#软件下载">软件下载</a> •
    <a href="#使用文档">使用文档</a> •
    <a href="#常见问题">常见问题</a>
  </p>
</div>

## 📱 产品概述

**TeleTool**  是一套**面向**的 Telegram 营销工具，由**天壹财团**的TG营销工具开发团队，涵盖**IP池管理**、**账号管理**、**群发**、**私信**、**拉群**、**炒群**、**监听**、**~~智能养号~~**、**~~消息转发~~**等模块，适用于各类 TG 营销场景。

无论你是个人推广者，还是团队运营者，**TG-Tool** 都能为你提供稳定、高效、灵活的 Telegram 全链路操作能力。

## ✨ 功能特点

<div class="features">
  <div class="feature-item">
    <h3>🔐 多账户管理</h3>
    <p>支持批量导入、分组管理数百个Telegram账号，一键切换，高效操作</p>
  </div>

  <div class="feature-item">
    <h3>🌐 代理IP池</h3>
    <p>内置智能代理分配系统，支持ISP/住宅代理，静态/动态IP，有效防控风险</p>
  </div>

  <div class="feature-item">
    <h3>👥 用户监听</h3>
    <p>实时监控目标群组活跃用户，捕获潜在客户，支持关键词触发和自动回复</p>
  </div>

  <div class="feature-item">
    <h3>📢 消息群发</h3>
    <p>支持文本、图片、视频等多媒体内容群发，可设置发送间隔，模拟真人操作</p>
  </div>

  <div class="feature-item">
    <h3>👋 拉人入群</h3>
    <p>智能拉人系统，批量邀请用户加入目标群组，支持分批次执行，降低封号风险</p>
  </div>

  <div class="feature-item">
    <h3>⏱️ 定时转发</h3>
    <p>设置定时任务，自动转发内容至多个群组或频道，解放双手，提高效率</p>
  </div>
</div>

## 🖥️ 软件界面

<div align="center">
  <img src="./_media/login.png" alt="TeleTool Screenshot" width="30%">
  <img src="./_media/accounts.png" alt="TeleTool Screenshot" width="30%">
  <img src="./_media/proxy.png" alt="TeleTool Screenshot" width="30%">
  <img src="./_media/monitor.png" alt="TeleTool Screenshot" width="30%">
  <img src="./_media/send.png" alt="TeleTool Screenshot" width="30%">
  <img src="./_media/invite.png" alt="TeleTool Screenshot" width="30%">
</div>

## 📥 软件下载

| 版本 | 系统要求 | 下载链接 |
|:----:|:--------|:-------:|
| v1.2.7 | Windows 10/11(64bit) | [立即下载](https://share.feijipan.com/s/wZMmcGxF) |
| v1.2.6 | macOS 10.15+ | 待支持|
| v1.2.6 | Linux | 待支持 |

## 🛡️ 安全检测报告

#### 微步云检测结果✅

| 检测类型 | 检测值 |
|---------|--------|
| SHA256 | 10cf3505fe0302bc4e58d55534b4d833b6e8c2c8d0e8e83c19647c1410804b7a |
| MD5 | c55c33b556608f279a02bda029147296 |
| SHA1 | be612919fe5c090399943cd5ba5ddcd21642a104 |

#### 360沙箱检测结果✅

| 检测类型 | 检测值 |
|---------|--------|
| SHA256 | 4a5edbb5d1fbeb09ed311e67c1563d2e68c908e5f8d23e2c31601787d2723178 |
| MD5 | 9f858fb3efc915552ac587b7db32c189 |
| SHA1 | 3321a19a2d9f77618e80c5927b4f9ddc820a1247 |

<div align="center">
  <img src="./assets/image-20250606213334855.png" alt="检测结果1" style="width:45%;">
  <img src="./assets/image-20250606213615043.png" alt="检测结果2" style="width:45%;">
</div>

## 📚 推荐阅读路径

（根据你的用途）选择文档阅读即可

| 你是... | 建议先看这几篇内容👇 |
|---------|----------|
| **初次接触 TG-TOOL** | [账号导入 & 系统设置说明](#) |
| **做 TG 私信营销的用户** | [私信模块](#) |
| **采集用户信息 / 获客** | [采集群组、频道各层级用户信息](#)|
| **想批量投放内容 / 群发广告** | [自动群发 模块对比与实操技巧](#) |
| **想拉群，将目标客户聚集进群** | [拉群、强拉，多策略优化](#) |

## 🔗 相关链接

- [官方网站](https://www.example.com)
- [视频教程](https://www.example.com/tutorials)
- [技术支持](https://www.example.com/support)

<style>
.buttons {
  display: flex;
  justify-content: center;
  margin: 30px 0;
}
.download-btn, .doc-btn {
  padding: 12px 24px;
  margin: 0 10px;
  border-radius: 30px;
  font-weight: bold;
  text-decoration: none;
  transition: all 0.3s;
}
.download-btn {
  background-color: #0088cc;
  color: white;
}
.download-btn:hover {
  background-color: #006699;
  transform: translateY(-2px);
}
.doc-btn {
  background-color: #f8f9fa;
  color: #333;
  border: 2px solid #ddd;
}
.doc-btn:hover {
  background-color: #e9ecef;
  transform: translateY(-2px);
}
.features {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 20px;
  margin: 30px 0;
}
.feature-item {
  background-color: #f8f9fa;
  border-radius: 10px;
  padding: 20px;
  transition: all 0.3s;
}
.feature-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 20px rgba(0,0,0,0.1);
}
.feature-item h3 {
  color: #0088cc;
  margin-top: 0;
}
.guide-table {
    width: 100%;
    border-collapse: collapse;
    margin: 20px 0;
    background-color: #fff;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.guide-table th, .guide-table td {
    padding: 15px;
    text-align: left;
    border-bottom: 1px solid #eee;
}

.guide-table th {
    background-color: #f8f9fa;
    font-weight: 600;
    color: #2c3e50;
}

.guide-table tr:hover {
    background-color: #f5f5f5;
}

.guide-table td a {
    color: #42b983;
    text-decoration: none;
}

.guide-table td a:hover {
    text-decoration: underline;
}
</style>
