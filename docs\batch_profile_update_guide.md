# 批量账户资料设置功能指南

## 功能概述

批量账户资料设置功能允许您一次性为多个Telegram账户设置相同的资料信息，同时支持用户名标签功能来确保每个账户的用户名都是唯一的。

## 主要特性

### 1. 批量设置支持的字段
- **姓名**: 姓氏和名字
- **用户名**: 支持标签模板生成唯一用户名
- **个人简介**: Bio信息
- **头像**: 统一设置头像图片
- **分组**: 批量分配到指定分组
- **代理设置**: 统一代理配置
- **消息限制**: 每日消息发送限制
- **邀请限制**: 每日邀请人数限制

### 2. 用户名标签功能

#### 支持的标签类型
- `{数字1-3}`: 生成1-3位随机数字
- `{字母1-5}`: 生成1-5位随机小写字母
- `{数字字母1-6}`: 生成1-6位随机数字字母组合

#### 标签使用示例
```
模板: user{数字1-3}
生成: user123, user45, user7

模板: test{字母2-4}
生成: testab, testxyz, testqw

模板: name{数字字母1-5}
生成: name1a2b, namexy9, namea1

模板: bot{数字1-2}_{字母1-3}
生成: bot12_abc, bot5_xy, bot89_def
```

## 使用步骤

### 1. 打开批量编辑对话框
1. 在账户管理页面选择多个账户
2. 点击"批量编辑"按钮
3. 进入批量编辑模式

### 2. 设置资料信息
1. **基本信息**:
   - 填写姓氏、名字
   - 设置个人简介
   - 选择头像文件

2. **用户名设置**:
   - 直接输入固定用户名（所有账户将使用相同用户名，可能冲突）
   - 使用标签模板（推荐）：
     - 点击标签按钮快速插入
     - 手动输入标签格式
     - 预览生成效果

3. **限制设置**:
   - 设置每日消息限制（0表示不限制）
   - 设置每日邀请限制（0表示不限制）

4. **代理设置**:
   - 选择代理类型
   - 配置代理参数

### 3. 执行批量更新
1. 检查设置信息
2. 点击"保存"按钮
3. 系统自动处理：
   - 验证数据有效性
   - 为每个账户生成唯一用户名
   - 批量更新Telegram资料
   - 更新数据库记录
   - 设置限制信息

## 高级功能

### 1. 用户名冲突处理
- 系统自动检测用户名冲突
- 最多重试10次生成唯一用户名
- 失败时跳过该账户并记录错误

### 2. 错误处理和重试
- 网络错误自动重试
- Telegram API限制处理
- 详细的错误日志记录

### 3. 进度监控
- 实时显示更新进度
- 成功/失败统计
- 详细的操作日志

## 最佳实践

### 1. 用户名模板设计
```
推荐模板:
- company{数字1-3}          # 公司账户
- user{数字2-4}_{字母1-2}   # 用户账户
- bot{数字字母1-5}          # 机器人账户
- test{数字1-2}             # 测试账户
```

### 2. 批量操作建议
- 一次处理的账户数量建议不超过50个
- 使用有意义的用户名前缀
- 设置合理的限制值
- 定期备份账户数据

### 3. 错误排查
1. **用户名生成失败**:
   - 检查模板格式是否正确
   - 尝试增加随机字符长度
   - 使用更复杂的模板组合

2. **Telegram更新失败**:
   - 检查网络连接
   - 验证账户登录状态
   - 检查代理设置

3. **限制设置失败**:
   - 确认限制值在有效范围内
   - 检查数据库连接状态

## 技术实现

### 1. 架构设计
```
UI层 (profile_edit_dialog.py)
  ↓ 收集用户输入
控制器层 (account_controller.py)
  ↓ 调用服务
服务层 (account_service.py)
  ↓ 处理业务逻辑
  ├─ 用户名标签解析
  ├─ 唯一性检查
  ├─ Telegram API调用
  └─ 数据库更新
数据层 (repositories)
  ↓ 数据持久化
```

### 2. 关键算法
- **标签解析**: 正则表达式匹配和替换
- **唯一性检查**: 数据库查询验证
- **冲突重试**: 指数退避重试机制
- **批量处理**: 事务管理和错误隔离

## 注意事项

1. **用户名限制**:
   - 长度: 5-32个字符
   - 字符: 只能包含字母、数字和下划线
   - 开头: 不能以数字开头
   - 唯一性: 全局唯一

2. **API限制**:
   - Telegram API有频率限制
   - 大批量操作可能需要分批处理
   - 网络异常时会自动重试

3. **数据安全**:
   - 操作前建议备份数据
   - 重要操作会记录详细日志
   - 支持操作回滚（部分功能）

## 故障排除

### 常见问题
1. **Q: 用户名生成失败怎么办？**
   A: 检查模板格式，增加随机字符长度，或使用更复杂的组合。

2. **Q: 批量更新部分失败？**
   A: 查看详细日志，针对失败账户单独处理。

3. **Q: 限制设置不生效？**
   A: 检查数据库连接，确认限制值在有效范围内。

### 联系支持
如遇到技术问题，请提供：
- 操作步骤描述
- 错误日志信息
- 账户数量和模板信息
- 系统环境信息
