<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <title>TeleTool-软件使用文档</title>
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
  <meta property="og:description" name="viewport"
    content="width=device-width,height=device-height,initial-scale=1,maximum-scale=1,user-scalable=no">
  <meta property="og:title"
    content="TeleTool-软件使用文档" name="keywords">
  <meta property="og:description"
    content="TeleTool-软件使用文档"
    name="description">
  <meta property="og:description"
    content="TeleTool-软件使用文档">

  <!-- themes : dark.css buble.css dolphin.css pure.css -->
  <link rel="stylesheet" href="//unpkg.com/docsify/lib/themes/vue.css">
  <!-- 添加折叠侧边栏样式 -->
  <link rel="stylesheet" href="//cdn.jsdelivr.net/npm/docsify-sidebar-collapse/dist/sidebar.min.css" />
  <!-- <link rel="stylesheet" href="//cdn.jsdelivr.net/npm/docsify@4/lib/themes/vue.css"> -->
  <!-- <link href="https://cdn.bootcdn.net/ajax/libs/docsify/4.13.1/themes/vue.css" rel="stylesheet"> -->

  <!-- 自定义样式 -->
  <style>
    /* 表格样式优化 */
    .markdown-section table {
      display: table;
      width: 100%;
      margin: 1em 0;
      border-collapse: collapse;
    }
    
    .markdown-section table th,
    .markdown-section table td {
      padding: 12px 15px;
      text-align: left;
      border: 1px solid #e6e1f4;  /* 浅紫色边框 */
      min-width: 100px;
    }
    
    /* 表格hover效果 */
    .markdown-section table tr:hover {
      background-color: #f8f6ff;  /* 浅紫色背景 */
    }
    
    /* 表格标题样式 */
    .markdown-section table th {
      background-color: #6f42c1;  /* 紫色表头 */
      color: white;  /* 表头文字白色 */
      font-weight: bold;
      white-space: nowrap;
    }

    /* 表格内容样式 */
    .markdown-section table td {
      word-break: break-word;
    }

    /* 表格奇数行背景 */
    .markdown-section table tr:nth-child(odd) {
      background-color: #fbfaff;  /* 极浅紫色背景 */
    }

    /* 响应式布局 */
    @media screen and (max-width: 768px) {
      .markdown-section table {
        display: block;
        overflow-x: auto;
      }
    }
  </style>
</head>

<body>
  <div id="app">
  </div>
  <!-- <script>
    window.$docsify = {
      name: '',
      repo: 'ssh://git@*.git'
    }
  </script> -->
  <script>
    window.$docsify = {
      el: '#app',
      // repo: 'docsifyjs/docsify',
      basePath: './',
      maxLevel: 3,        // 修改为3，与subMaxLevel保持一致
      loadNavbar: true,  
      mergeNavbar: false,
      onlyCover: true,
      coverpage: {
        '/': '_coverpage.md',
      },
      auto2top: true,
      loadSidebar: true,
      autoHeader: false,  // 关闭自动添加标题
      alias: {
        '/_sidebar.md': '/_sidebar.md',
      },
      subMaxLevel: 3,     // 保持为3
      sidebarDisplayLevel: 1,
      collapse: {
        accordion: true,  // 折叠侧边栏
        expandable: false // 展开侧边栏
        
      },

      // 文档目录配置
      toc: {
        scope: '.markdown-section',
        headings: 'h2, h3, h4',  // 只显示 h2-h4 的标题
        title: '目录',
      },

      search: {
        maxAge: 86400000, // 过期时间，单位毫秒，默认一天
        paths: 'auto',    // 改为 auto，自动搜索所有路径
        // Localization
        placeholder: {
          '/zh-cn/': '搜索',
          '/': 'Type to search'
        },

        // Localization
        noData: {
          '/zh-cn/': '找不到结果',
          '/': 'No Results'
        },

        // 搜索标题的最大层级, 1 - 6
        depth: 6,        // 增加搜索深度

        hideOtherSidebarContent: false, // 是否隐藏其他侧边栏内容

        // 避免搜索索引冲突
        // 同一域下的多个网站之间
        namespace: 'tg-tool-docs',

        // 使用不同的索引作为路径前缀（namespaces）
        pathNamespaces: ['/zh-cn']
      },

      // 给每个页面的末尾加上 footer
      plugins: [
        function (hook) {
          var footer = [
            '<hr/>',
            '<footer>',
            '<span><a href="https://docs.xile188.com">TeleTool</a> &copy;2024-2025.</span>',
            '<span>由天壹财团发布 <a href="https://docs.xile188.com" target="_blank">在线文档</a>.</span>',
            '</footer>'
          ].join('');

          hook.afterEach(function (html) {
            return html + footer;
          });
        },

      ],
      count: {
        countable: true,
        position: 'top',
        margin: '10px',
        float: 'right',
        fontsize: '0.9em',
        color: 'rgb(90,90,90)',
        language: 'chinese',
        localization: {
          words: "",
          minute: ""
        },
        isExpected: true
      },
      pagination: {
        previousText: '上一章节',
        nextText: '下一章节',
      },
      copyCode: {
        buttonText: {
          '/zh-cn/': '点击复制',
          '/ru/': 'Скопировать в буфер обмена',
          '/de-de/': 'Klicken Sie zum Kopieren',
          '/es/': 'Haga clic para copiar',
          '/': 'Copy to clipboard',
        },
        errorText: {
          '/zh-cn/': '错误',
          '/ru/': 'ошибка',
          '/': 'Error',
        },
        successText: {
          '/zh-cn/': '复制',
          '/ru/': 'Скопировано',
          '/de-de/': 'Kopiert',
          '/es/': 'Copiado',
          '/': 'Copied',
        },
      },
      ads: [
        // {
        //   img: 'https://dev-skytop.oss-cn-chengdu.aliyuncs.com/shop/info/202312/2023-1229-2227-2d149722-h5.png',
        //   href: 'https://h5.10000hk.com/#/?userSn=7LXJ7Rg5NBnZbNSkoKhdyrlpXb0PeosbYR06jVbxIBN'
        // },
      ]
    }

  </script>
  <!-- Docsify v4 -->
  <!-- <script src="//cdn.jsdelivr.net/npm/docsify@4"></script> -->
  <!-- <script src="https://cdn.bootcdn.net/ajax/libs/docsify/4.13.1/docsify.js"></script> -->
  <script src="//unpkg.com/docsify/lib/docsify.min.js"></script>
  <!-- 插件 -->
  <script src="//unpkg.com/docsify/lib/plugins/search.min.js"></script>
  <script src="//unpkg.com/docsify/lib/plugins/emoji.min.js"></script>
  <script src="//unpkg.com/docsify/lib/plugins/zoom-image.min.js"></script>
  <!-- w -->
  <script src="//unpkg.com/docsify/lib/plugins/external-script.min.js"></script>
  <!-- 分页插件 -->
  <script src="//unpkg.com/docsify-pagination/dist/docsify-pagination.min.js"></script>
  <!-- 代码高亮 -->
  <script src="https://cdn.bootcdn.net/ajax/libs/prism/9000.0.1/components/prism-actionscript.min.js"></script>
  <!-- 自定义广告 -->
  <script src="https://unpkg.com/docsify-ads@1.0.4/dist/docsify-ads.min.js"></script>
  <!-- 字数统计 -->
  <script src="//unpkg.com/docsify-count/dist/countable.js"></script>
  <!-- 代码复制 -->
  <script src="https://unpkg.com/docsify-copy-code"></script>
  <!-- 折叠侧边栏 -->
  <script src="//cdn.jsdelivr.net/npm/docsify-sidebar-collapse/dist/docsify-sidebar-collapse.min.js"></script>


  <!-- 离线模式 -->
  <!-- <script>
    if (typeof navigator.serviceWorker !== 'undefined') {
      navigator.serviceWorker.register('sw.js')
    }
  </script> -->
</body>

</html>