## 📥 批量导入session

> [!NOTE]
> Session是Telegram客户端的登录会话数据，包含了账号的认证信息。通过导入session文件，可以快速完成账号登录，无需重新进行手机验证码认证。

## 💾 tdata转session

> [!TIP]
> tdata是Telegram Desktop客户端的数据文件夹，可以通过工具将tdata转换为session文件。具体转换方法请参考专门的转换工具说明。

## ⚙️ 导入设置说明

### 🌐 代理设置

- **🚫 不使用代理**：直接使用本地网络连接，要确保本地网络【未开启VPN】能访问[www.telegram.org](https://www.telegram.org/)官网，才可正常使用账户
- **🔄 系统代理**：使用系统全局代理设置，开启VPN或设置了代理ip，会自动读取系统设置的vpn脚本或代理ip进行登录
- **📊 IP池**：从预设的IP池中选择代理,需要先在IP池添加本地代理IP或远程socks5代理且检验有效才显示IP。

### 👥 用户分组
从已创建的分组中选择，用于批量管理账号。如果没有合适的分组，可以先创建新分组。

### ⚡ 并发设置
- **🔢 同时登录数**：设置并发登录的账号数量，避免同时登录过多账号导致风控
- **💡 建议值**：3-5个账号同时登录为宜

## 📋 session导入流程

1. 📂 点击导入session文件按钮，选择单个或多个需要导入的session文件

   ![image-20250606233559086](./assets/image-20250606233559086.png)

2. ⚙️ 选择代理设置和选择用户分组

   ![image-*****************](./assets/image-*****************.png)

3. 🔄 设置并发登录数

   ![image-*****************](./assets/image-*****************.png)

4. ▶️ 点击开始登录按钮进行批量登录

   

5. ⏳ 等待导入完成

   ![image-*****************](./assets/image-*****************.png)

   ✅ 导入成功或失败会有提醒，完成后可关闭该窗口或导入新一批的session账户。

   

<div align="center">
    <img src="/zh-cn/account/assets/image-*****************.png" width="45%" />
</div>

> [!IMPORTANT]
> - 🔒 导入过程中请保持网络稳定
> - ✔️ 请确保session文件格式正确
> - ⚠️ 如遇导入失败，请检查代理设置是否正常