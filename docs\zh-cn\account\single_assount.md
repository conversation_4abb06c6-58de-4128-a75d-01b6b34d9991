## 📱 单个账户登录

> [!NOTE]
> 本节介绍如何使用手机号和验证码登录单个 Telegram 账户。

## 🌐 代理设置

代理设置提供以下三种方式：
- **🚫不使用代理**：直接使用本地网络连接，要确保本地网络【未开启VPN】能访问[www.telegram.org](https://www.telegram.org/)官网，才可正常使用账户
- **🔄系统代理**：使用系统全局代理设置，开启VPN或设置了代理ip，会自动读取系统设置的vpn脚本或代理ip进行登录
- **📊IP池**：从预设的IP池中选择代理,需要先在IP池添加本地代理IP或远程socks5代理且检验有效才显示IP。

## 📲 登录步骤
1. **设置代理IP**
   - 根据上面的代理ip设置提醒，设置好您所需要的代理IP类型
   ![image-20250606234733899](./assets/image-20250606234733899.png)​	

2. **输入手机号**
   - 请输入完整的手机号（包含国际区号）
   
   - 例如：+86 138xxxx xxxx
   
     ![image-20250606235031986](./assets/image-20250606235031986.png)
   
3. **验证码确认**
   - 发送验证码后，等待以下任一方式接收：
     - Telegram 客户端接收
     
     - 手机短信接收
     
       ![image-20250606235056761](./assets/image-20250606235056761.png)
   
4. **两步验证**
   - 如果账户开启了两步验证，需要输入密码
   
   - 如未开启，此步骤可跳过
   
     ![image-20250606235342506](./assets/image-20250606235342506.png)
   
5. **完成登录**
   
   - 验证通过后，系统将自动保存登录状态
   - 登录成功后可进行后续操作
   
   ![image-20250606235409149](./assets/image-20250606235409149.png)

> [!IMPORTANT]
> 请确保您使用的手机号已注册 Telegram 账户，并且在24小时内未频繁登录，以避免触发风控。