# telegram群组消息关键词监听

## 功能简介
**注意** 当前版本（1.2.6）通知有bug，未生效，下个版本修复。
关键词监听是 TG-Tool 的核心功能之一，能够帮助您实时监控多个群组中的消息，当发现包含您设置的关键词的消息时，系统会立即捕获并通知您。这对于市场调研、品牌监控、客户挖掘和竞品分析都非常有价值。

<img src="/_media/monitor.png" alt="群组监听界面" width="800" style="display: block; margin: 20px auto;" />

## 核心优势

- **多群组并行监听**：同时监控多个目标群组，无需手动切换
- **自定义关键词**：支持设置多组关键词，可精确匹配或模糊匹配
- **实时捕获**：消息一出现即被记录，不会遗漏重要信息
- **数据导出**：支持将监听结果导出为Excel或CSV格式，方便后续分析
- **自动回复**：可设置关键词触发后的自动回复内容（高级版功能）
- **定时监听**：支持设置监听时间段，优化资源使用

## 操作步骤

### 1. 添加监听任务

1. 在软件左侧导航栏中，点击"消息监听" 
2. 点击右上角"添加任务"按钮
<img src="/zh-cn/collection/img.png" alt="添加任务" width="600" style="display: block; margin: 10px auto;" />
3. 填写任务名称（建议使用有意义的名称以便于管理）
4. 选择监听任务类型为 **关键词监听**
<img src="/zh-cn/collection/img_6.png" alt="选择监听类型" width="600" style="display: block; margin: 10px auto;" />

### 2. 选择监听账号

1. 从账号分组中选择用于监听的TG账号分组
2. 建议选择活跃度较高、未被限制的账号进行监听
3. 不同群组可以使用不同账号进行监听，分散风险
4. 单任务支持多个账户同时监听多个群组
<img src="/zh-cn/collection/img_2.png" alt="选择监听账号" width="600" style="display: block; margin: 10px auto;" />
5. 根据需求选择不同群组类型
6. 根据需求勾选不同群组或选中所有群组

### 3. 添加目标群组

1. 点击"添加群组"按钮
1. 选择需要添加到监听列表的群组【支持多个群组，多个账户】

<img src="/zh-cn/collection/assets/image-20250611103455963.png" alt="添加目标群组" width="600" style="display: block; margin: 10px auto;" />


### 4. 设置监听关键词
<img src="/zh-cn/collection/img_7.png" alt="设置关键词" width="600" style="display: block; margin: 10px auto;" />
1. 在关键词设置区域，输入需要监听的关键词
2. 每行输入一个关键词或词组
3. 支持以下匹配模式：
   - 精确匹配：完全匹配关键词
   - 模糊匹配：包含关键词即匹配
   - 正则匹配：使用正则表达式匹配（高级用户）

<img src="/zh-cn/collection/img_8.png" alt="关键词设置" width="600" style="display: block; margin: 10px auto;" />

### 5. 设置监听规则（可选）

1. 监听时间：可设置特定时间段进行监听，节省资源
2. 消息类型：可选择仅监听文本消息、图片消息或所有类型
3. 发送者过滤：可设置只监听特定用户或排除特定用户

### 6. 设置通知方式（可选）

1. 实时通知：新消息即时在软件中显示
2. 邮件通知：将重要关键词匹配结果发送到指定邮箱（高级版）
3. 自动回复：设置关键词触发后的自动回复内容（高级版）

### 7. 启动监听

1. 点击"保存并启动"按钮
2. 系统会自动连接到目标群组并开始监听
3. 监听状态会在任务列表中显示

<img src="/zh-cn/collection/img_9.png" alt="启动监听" width="600" style="display: block; margin: 10px auto;" />
启动/暂停，编辑任务

## 监听结果查看

1. 在"关键词监听"页面，可查看所有监听任务的状态
2. 点击任务名称，进入详情页查看匹配到的消息
3. 监听结果包含以下信息：
   - 发送时间
   - 发送者信息（用户名、ID）
   - 消息内容
   - 匹配的关键词
   - 所在群组
<img src="/zh-cn/collection/img_10.png" alt="监听结果" width="600" style="display: block; margin: 10px auto;" />

## 数据导出

1. 在采集结果页面，点击"导出数据"按钮

   <img src="/zh-cn/collection/assets/image-20250611103752176.png" alt="导出数据" width="600" style="display: block; margin: 10px auto;" />

2. 选择导出位置（保存为CSV表格格式）

   <img src="/zh-cn/collection/assets/image-20250611103823166.png" alt="选择导出位置" width="600" style="display: block; margin: 10px auto;" />

3. 系统将生成数据文件并提供下载

4. 使用excel打开并筛选用户
<img src="/zh-cn/collection/img_3.png" alt="数据筛选" width="600" style="display: block; margin: 10px auto;" />

## 使用场景

### 市场情报收集

监听行业相关群组中的关键词，及时获取市场动态、竞品信息和用户需求，为决策提供依据。

### 获客和销售线索

监听潜在客户可能发出的需求信息，如"求推荐"、"哪里有卖"等关键词，快速找到销售机会。



### 品牌口碑监控

设置品牌名称、产品名称作为关键词，实时了解用户对品牌的评价和反馈。

### 竞品分析

监听竞争对手品牌名称，了解市场对竞争对手的评价和竞争对手的动态。

## 实战案例

### 案例一：加密货币交易机会捕捉

某交易团队设置了"买币"、"卖币"、"换U"、"OTC"等关键词进行监听，每天能捕获数十条交易需求，大大提高了获客效率。

### 案例二：品牌舆情监控

某品牌营销团队设置了自己的品牌名称和产品名称作为关键词，实时监控用户评价，及时发现并处理负面信息，有效保护了品牌形象。



## 注意事项

1. 请遵守Telegram使用条款，不要使用本功能进行骚扰或违法活动
2. 监听账号应适当活跃，完全不发言的账号可能会被限制
3. 单个账号不建议同时监听过多群组（建议不超过10个）
4. 关键词设置不宜过多，建议每个任务不超过50个关键词
5. 定期导出重要数据，避免数据丢失

## 常见问题

**Q: 为什么我的监听任务显示"连接中断"？**  
A: 可能是账号被限制、网络问题或代理IP问题，请检查账号状态和网络连接。

**Q: 监听会消耗大量流量吗？**  
A: 纯文本监听流量消耗较小，但如果监听的群组消息量大且包含媒体内容，会消耗较多流量。

**Q: 如何提高关键词匹配的准确性？**  
A: 可以使用更精确的关键词组合，或利用正则表达式进行高级匹配。

**Q: 自动回复功能会增加账号风险吗？**  
A: 是的，频繁的自动回复可能增加账号被限制的风险，建议设置合理的回复间隔和随机延迟。

