<style>
.category-title {
    color: #6f42c1;
    font-size: 24px;
    font-weight: bold;
    margin: 20px 0;
    padding: 10px 0;
    border-bottom: 2px solid #6f42c1;
}

table {
    width: 100%;
    border-collapse: collapse;
    margin: 20px 0;
    background-color: #fff;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

th, td {
    padding: 12px 15px;
    text-align: left;
    border-bottom: 1px solid #eee;
}

th {
    background-color: #f8f9fa;
    font-weight: 600;
    color: #2c3e50;
}

tr:hover {
    background-color: #f5f5f5;
}

td strong {
    color: #42b983;
}

td:first-child {
    font-weight: 500;
}
</style>

<div class="category-title">💬 账号类型区别</div>

在 Telegram 批量营销领域，账号的格式并不只有一种，而是根据登录方式和用途划分为两种主流形态：

- **协议号（session + json）**
- **直登号（Tdata）**

很多刚入行的新手会搞混这两者的区别，下面我们从多个维度来做一一解释。

------

| 类型 | 文件结构说明 |
| --- | --- |
| 协议号 | 两个文件：`.session` 文件 + `.json` 配置文件 |
| 直登号 | 一个文件夹：Tdata 文件夹，包含多个加密数据文件 |

------

| 类型 | 使用平台 | 登录方式说明 |
| --- | --- | --- |
| 协议号 | **TG-WAVE**、API类软件 | 使用 session 文件建立会话，自动登录 |
| 直登号 | Telegram PC 客户端 | 拷贝 Tdata 文件夹即可直接免验证登录 |

------

### 🔁 三、能否互转

**可以互转**：

- TG-WAVE 软件提供了**【格式互转工具】**，支持 session + json 和 Tdata 之间的双向转换
- 但**不建议频繁反复互转**，容易出现文件损坏或账号状态异常

------

| 类型 | 风控表现 | 特别说明 |
| --- | --- | --- |
| 协议号 | **可控性更强** | 可用于检测状态、批量管理、编程控制 |
| 直登号 | **更依赖客户端环境** | 适合用于短期登录任务或人工操作，适配性差 |

------
### 🧰 五、适用场景总结
| 场景类型 | 推荐账号格式 | 原因说明 |
| --- | --- | --- |
| TG-WAVE 群发/私信 | ✅ 协议号 | 支持批量控制、自动轮换、程序调用 |
| 手动操作/临时登录 | ✅ 直登号 | 快速免验证登录，适用于人手控号 |
| 多工具平台协同 | 协议号 + Tdata | 配合使用，根据用途转换格式 |

------

### 🧠 小结一句话

> **协议号更适合自动化，直登号更适合人工控。**

如果你使用的是 **TG-TOOL** 软件，那你应该**重点使用协议号**，而不是直登号。