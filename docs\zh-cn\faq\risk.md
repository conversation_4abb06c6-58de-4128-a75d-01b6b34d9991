# ❗关于账号“双向/冻结”风险说明

近期行业内出现大量账号**上号即冻、被双向，未操作即冻、被双向**的现象，经过我们与多方客户和同行反复沟通，结合大量实测，总结以下几点经验性判断：

------

#### ⚠️ 常见触发原因

1. **使用 @SpamBot 检测频率高**
   - 多次调用 @SpamBot 接口检测账号状态，反而容易被系统识别为异常行为，**诱发双向/冻结**。
2. **接入低质量机器人筛号**
   - 市面上大量筛号机器人底层接口与账号注册协议不一致，**底层协议老旧、参数混乱**，登录即触发冻结。
   - ✅ 建议使用结构规范、更新及时的**正规筛号工具**。
3. **控端底层问题**
   - 某些控（不管是协议控还是直登控）**底层兼容性差**，容易出现上号后无操作仍被冻结。
   - 已有实测记录：同一时间段上控，**未操作账号也遭遇批量冻结**。
4. **VPN节点、代理IP被污染，导致登录环境污染**
   - 使用共享VPN节点、代理IP，其他用户的滥用导致环境污染造成风控。
   - 独享代理节点但长期固定没变化，登录账号过多导致风控连坐。
5. **手动登录相对安全**
   - 人工手动打码登录，相对来说更为稳定。
   - 但也受号段、地区、IP影响，**不能完全避免冻结**。

------

#### ✅ 建议总结

> **冻结≠操作问题，更多是环境和接口问题。**

- 有些号**上号那一刻就已经决定了命运**，即便什么也不做，也可能被TG系统判定异常。
- 请重视 **登录环境、代理IP、控端底层、筛号工具** 等综合因素。

------

