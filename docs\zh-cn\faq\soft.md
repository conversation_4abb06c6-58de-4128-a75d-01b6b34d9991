# ConnectionError: [<PERSON><PERSON>no 11003] failed 问题说明与解答
## ❓ 出现原因
这是因为 TG-TOOL 启动时需要连接验证服务器，用于校验授权信息。

报错 [Errno 11003] failed 通常表示：
本地网络无法正常访问 TG-TOOL 的授权服务器。

不是软件本身问题，也不是账号、代理设置问题，是本机网络环境导致连接失败。

## 🎯 常见导致原因
本地网络存在防火墙或运营商限制，拦截了授权服务器的访问。

所使用的代理、VPS、云服务器屏蔽了部分端口或DNS请求。

某些公司网络、学校网络对外连接有严格限制。

少数情况下，本地DNS解析异常（比如污染或错误解析）也可能导致。

## ✅ 解决方案
1.换成正常的家庭网络或手机热点试一下，不要用公司、学校、办公楼的封闭网络。

2.更换 DNS 解析服务，推荐使用公共 DNS，比如：
    
    8.8.8.8（Google）
    
    1.1.1.1（Cloudflare）

3.关闭代理/VPN直接运行软件，有些代理会拦截授权服务器访问。

4.如果在服务器上使用，建议切换本地电脑运行测试，排除服务器自身防火墙问题。

5.联系你的网络提供方，确认是否存在国际访问屏蔽（特别是俄罗斯、欧美方向的连接）。

可以正常连接到授权码窗口出现，输入授权码并正式进入软件后，即可打开VPN使用。
