# 拉人入群功能

## 📝 功能介绍

拉人入群功能允许您使用 Telegram 账号批量邀请用户加入指定群组。通过合理配置，可以实现高效的群组成员扩充。

<div style="text-align: center;">
    <img src="/zh-cn/group/img_2.png" alt="功能介绍" style="width: 40%; border-radius: 10px; margin: 20px auto;" />
</div>

## ⚙️ 功能配置

| 配置项 | 说明 |
|--------|------|
| 操作账号 | 选择用于拉人的 Telegram 账号 |
| 目标群组 | 选择要拉人进入的目标群组 |
| 用户列表 | 需要邀请的用户名单 |
| 拉人间隔 | 每次邀请操作之间的时间间隔（秒） |
| 并发线程 | 同时进行邀请操作的线程数 |

## 🚀 使用步骤
1. 点击左侧导航"拉群">创建新任务
<div style="text-align: center;">
    <img src="/zh-cn/group/img_3.png" alt="创建任务" style="width: 40%; border-radius: 10px; margin: 20px auto;" />
</div>

2. 选择操作账号：
   - 从已登录账号列表中选择
   - 确保账号有拉人权限
   - 账号状态正常无风控
<div style="text-align: center;">
    <img src="/zh-cn/group/img_4.png" alt="选择账号" style="width: 40%; border-radius: 10px; margin: 20px auto;" />
</div>

3. 设置目标群组：
   - 选择要拉人的群组
   - 确认群组权限设置正确
   - 检查群组是否允许邀请新成员
<div style="text-align: center;">
    <img src="/zh-cn/group/img_5.png" alt="设置群组" style="width: 40%; border-radius: 10px; margin: 20px auto;" />
</div>
   
4. 导入用户列表：
   - 支持文件导入
   - 可直接输入用户名
   - 确保用户名格式正确
<div style="text-align: center;">
    <img src="/zh-cn/group/img_6.png" alt="导入方式1" style="width: 40%; border-radius: 10px; margin: 20px auto; display: inline-block;" />
    <img src="/zh-cn/group/img_7.png" alt="导入方式2" style="width: 40%; border-radius: 10px; margin: 20px auto; display: inline-block;" />
</div>

5. 配置拉人参数：
   - 设置合理的拉人间隔（建议 30-50 秒）
   - 调整并发线程数（建议 3-5 个）
<div style="text-align: center;">
    <img src="/zh-cn/group/img_8.png" alt="参数设置1" style="width: 40%; border-radius: 10px; margin: 20px auto; display: inline-block;" />
    <img src="/zh-cn/group/img_9.png" alt="参数设置2" style="width: 40%; border-radius: 10px; margin: 20px auto; display: inline-block;" />
</div>

6. 保存任务
   - 配置完成后点击保存按钮保存任务
<div style="text-align: center;">
    <img src="/zh-cn/group/img_10.png" alt="保存任务" style="width: 40%; border-radius: 10px; margin: 20px auto;" />
</div>

7. 启动任务：
   - 点击"开始拉人"按钮
   - 观察任务执行状态
   - 监控成功率
<div style="text-align: center;">
    <img src="/zh-cn/group/img_11.png" alt="启动任务" style="width: 40%; border-radius: 10px; margin: 20px auto;" />
</div>

## ⚠️ 注意事项

1. 群组要求：
   - 确保群组允许邀请新成员
   - 群组权重要足够
   - 群组活跃度要合适

2. 账号限制：
   - 注意账号的拉人权限
   - 遵守每日拉人限额
   - 避免触发风控机制

3. 操作建议：
   - 控制单次拉人数量
   - 合理设置时间间隔
   - 避免频繁大量操作

## 💡 使用技巧

1. 提高成功率：
   - 使用优质账号
   - 选择合适的群组
   - 合理控制拉人节奏

2. 避免风控：
   - 不要使用新注册账号
   - 保持账号活跃度
   - 遵守 Telegram 规则

3. 群组优化：
   - 维护群组活跃度
   - 完善群组资料
   - 保持正常互动

## 🛡️ 安全建议

1. 账号养护：
   - 定期检查账号状态
   - 保持正常社交行为
   - 避免异常操作

2. 群组管理：
   - 控制群组质量
   - 维护群组秩序
   - 及时清理违规内容

3. 操作规范：
   - 遵守平台规则
   - 避免恶意行为
   - 保持合理使用频率，当前设置单账户每日最多可拉入20人，后续版本(>1.2)可以单独或批量设置
