## 🏠 添加本地代理

> [!IMPORTANT]
> 
> 🖥️ 本地代理由 3proxy 提供支持，软件本地代理功能只针对 3proxy 提供界面快捷操作自动化。需将软件所在目录的 3proxy 目录添加到 Windows 安全排除文件夹才可运行 3proxy。
> 
> 3proxy 属于 GitHub 开源软件，可放心使用，可把 3proxy 目录下的 3proxy 相关文件替换为 GitHub 下的 3proxy 项目文件。项目地址：[https://github.com/3proxy/3proxy](https://github.com/3proxy/3proxy)
> 
> ⚠️ 当前项目搭建的所有代理 IP 仅支持 socks5 协议。
> 
> 3proxy 作用：将您的多个公网 IP 绑定到内网网卡，可使用相对应的公网 IP 绑定 Telegram 账户，实现 1IP 1账户 或 1IP 多账户。
> 
> ✅ 有效降低账户被封风险，提高账户稳定性。

<div class="card">
  <div class="card-header">
    <span class="icon">🛡️</span>
    <span class="title">安全检测报告</span>
  </div>
  <div class="card-body">
    <a href="#/README?id=🛡%ef%b8%8f-安全检测报告">查看软件完整安全检测报告 →</a>
  </div>
</div>

## 🛡️ 将软件文件夹添加到 Windows 安全排除文件夹

---
**获取需要Windows 安全排除文件夹**


<div align="center">
  <img src="/zh-cn/proxy/assets/image-20250606211842259.png" alt="注册界面2" style="width:45%;">
</div>

以当前文件夹为例，我们可以添加F:\wm\teletool或F:\wm\teletool\3proxy

添加方式请查看下方文档：

<div class="card-container">
  <div class="card">
    <div class="card-header">
      <span class="icon">💻</span>
      <span class="title">Windows 10 教程</span>
    </div>
    <div class="card-body">
      <a href="win10.md">查看Win10系统添加排除文件夹教程 →</a>
    </div>
  </div>

  <div class="card">
    <div class="card-header">
      <span class="icon">💻</span>
      <span class="title">Windows 11 教程</span>
    </div>
    <div class="card-body">
      <a href="win11.md">查看Win11系统添加排除文件夹教程 →</a>
    </div>
  </div>
</div>

<style>
.card-container {
  display: flex;
  gap: 20px;
  margin: 20px 0;
}

.card {
  background: #ffffff;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  margin: 10px 0;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  overflow: hidden;
  flex: 1;
}

.card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0,0,0,0.2);
}

.card-header {
  background: #f8f9fa;
  padding: 12px 16px;
  border-bottom: 1px solid #eee;
  display: flex;
  align-items: center;
}

.card-header .icon {
  margin-right: 8px;
  font-size: 1.2em;
}

.card-header .title {
  font-weight: 600;
  color: #2c3e50;
}

.card-body {
  padding: 16px;
}

.card-body a {
  color: #0088cc;
  text-decoration: none;
  display: block;
}

.card-body a:hover {
  color: #006699;
  text-decoration: underline;
}
</style>

## 🌐 添加服务器公网 IP

---
>代理ip格式：可一行一个，使用英文字符-链接多个ip段，
>如***********-10，代表D段生成10个ip,***********,*********** .....***********0
>也可以完整IP段***********-************  则表示生成30个IP
>*********** 则表示一个IP

```
***********
*********-10
*********-**********
```
代理类型选中本地代理，ip输入框输入服务器公网ip或ip段，支持多行，端口选择为随机端口范围，自动为ip绑定不同端口进行代理转发


<div align="center">
  <img src="zh-cn/proxy/assets/image-20250606215616864.png" alt="注册界面3" style="width:45%;">
</div>


点击添加按钮后请等待程序自动检测IP有效性【会自动重载配置再检测，不放心也可等检测完成后重启3proxy再次检测】

<div align="center">
  <img src="zh-cn/proxy/assets/image-20250606215823599.png" alt="注册界面3" style="width:45%;">
</div>


## 🔍 检测代理有效性

---
当检测完成后，我们可以清晰看到有效IP


<div align="center">
  <img src="zh-cn/proxy/assets/image-20250606220522341.png" alt="注册界面3" style="width:45%;">
</div>
就可以进行下一步绑定代理IP到

## 🔗 绑定 IP

---
参考登录单个账户或批量导入账户的设置，里面会有详细介绍。


## ⚙️ 启动 3proxy
---
- 3proxy 以服务模式启动
- 软件启动时判断有本地代理会自动启动 3proxy
- 软件添加新的本地公网 IP 时，会自动重载配置
- 您也可以手动启动或关闭 3proxy，关闭后代理 IP 将无法生效


