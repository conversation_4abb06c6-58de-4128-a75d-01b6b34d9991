
## 🌐 添加远程代理

> [!IMPORTANT]
> 
> ⚠️ 远程代理只支持 socks5 代理模式，其他模式理论上支持，但不建议使用，socks5 稳定性更高，后续也只会统一维护管理 socks5 代理。
> 
> 🌍 远程代理无需启动 3proxy 即可使用。

---

## 🛒 获取远程代理
- 购买或使用多 IP 服务器搭建远程代理 IP 提供给软件绑定

- 我们这里以IP 127.0.0.1 端口 1080 用户名：无  密码：无为例

  

---

## ➕ 添加代理
- 每次只能添加一个远程代理 IP

- 输入框填写 IP 地址，选择单个端口并输入端口信息，如有用户名和密码请输入用户名和密码，无用户名和密码则保持为空

- 点击添加代理 IP 按钮，等待自动验证代理 IP 有效性

- 我们这里以IP 127.0.0.1 端口 1080 用户名：无  密码：无为例

  代理ip类型选择为外部socks5 ,ip输入框输入127.0.0.1 端口选择固定端口 改为1080，用户名和密码有则输入，无则默认。

  ![image-20250606221911523](./assets/image-20250606221911523.png)

  然后等待自动检测有效性结束，即可使用。

  ![image-20250606221947440](./assets/image-20250606221947440.png)

---

## 📤 导出代理IP给其他电脑使用
    双击需要给远程使用的代理ip，即可复制该行IP到剪贴板。
    例：socks5://127.0.0.1:1080