1. - Win10系统中，有些软件，在运行的时候，可能会被Windows安全中心拦截，导致无法正常使用，这篇文章是本站给大家带来的Win10安全中心添加排除项方法。

     ![Win10,安全中心,排除项,白名单步骤](./../../_media/1-22111214040J18.jpg)

     
     Win11安全中心设置白名单
      

     1、首先，按键盘上的【 Win + i 】组合键，打开Windows 设置窗口，搜索框查找设置，或直接找到并点击【更新和安全(Windows 更新、恢复、备份)】；
      

     ![Win10,安全中心,排除项,白名单步骤](./../../_media/1-22111214040N49.jpg)

     2、更新和安全主页设置窗口，左侧边栏点击【Windows 安全中心】，右侧找到，并点击【打开 Windows 安全中心】；
      

     ![Win10,安全中心,排除项,白名单步骤](./../../_media/1-22111214040I13.jpg)

     3、Windows 安全中心窗口，左侧点击【病毒和威胁防护】，并在右侧，找到并点击【病毒和威胁防护设置】；

     ![Win10,安全中心,排除项,白名单步骤](./../../_media/1-22111214040V39.jpg)

     
     4、‘病毒和威胁防护设置”窗口中，找到并点击【排除项】；
     排除项说明：Microsoft Defender 防病毒不会扫描已排除的项目。已排除的项目可能包含使你的设备易受攻击的威胁。

     ![Win10,安全中心,排除项,白名单步骤](./../../_media/1-22111214040U03.jpg)

     
     5、排除项窗口中，找到并点击【添加排除项】，并根据实际情况选择需排除项的具体类型；

     ![Win10,安全中心,排除项,白名单步骤](./../../_media/1-22111214040QZ.jpg)

     
     6、最后，找到需排除项的具体位置，并点击【选择文件夹】即可（这里以文件夹类型为例）；

     ![Win10,安全中心,排除项,白名单步骤](./../../_media/1-2211121404092T.jpg)