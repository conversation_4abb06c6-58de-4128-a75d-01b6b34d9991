# 批量发送消息

## 📝 功能介绍

批量发送消息功能允许您同时向多个用户或群组发送相同的消息内容。通过简单的设置，可以实现高效的消息群发。


## ⚙️ 使用说明

| 设置项  | 说明                                 |
|------|------------------------------------|
| 发送账号 | 选择要用于发送消息的 Telegram 账号             |
| 发送对象 | 选择消息接收者（可以是用户或群组，支持自定义或多个采集任务对应用户） |
| 消息内容 | 输入要发送的文本消息 （支持多条随机）                |
| 发送间隔 | 设置每条消息之间的时间间隔（秒）                   |
| 发送线程 | 设置同时发送的线程数量                        |
| 发信限制 | 默认每账户每日10条额度（后面支持账户设置里自行调整）        |

## 🚀 操作步骤
<div style="text-align: center;">
   <img src="/zh-cn/send/img_2.png" alt="操作步骤" style="width: 40%; border-radius: 20px; margin: 20px auto;" />
</div>

1. 选择发送账号：从已登录的账号列表中选择要使用的账号
   <div style="text-align: center;">
      <img src="/zh-cn/send/img_4.png" alt="选择账号" style="width: 40%; border-radius: 20px; margin: 20px auto;" />
   </div>
   通过账户分组批量选择用于发信的账户或自行勾选该任务用于发信的账户

2. 导入发送对象：可以通过文件导入或直接输入目标用户/群组
   <div style="text-align: center;">
      <img src="/zh-cn/send/img_5.png" alt="导入方式1" style="width: 40%; border-radius: 20px; margin: 20px auto; display: inline-block;" />
      <img src="/zh-cn/send/img_6.png" alt="导入方式2" style="width: 40%; border-radius: 20px; margin: 20px auto; display: inline-block;" />
   </div>
   支持直接发送给指定任务采集到的用户，支持导入用户或输入用户自定义发送目标。

3. 编写消息内容：在消息框中输入要发送的内容
   <div style="text-align: center;">
      <img src="/zh-cn/send/img_3.png" alt="编写消息" style="width: 40%; border-radius: 20px; margin: 20px auto;" />
   </div>

4. 设置发送参数：
   - 调整发送间隔（建议 30-50 秒）
   - 调整账户间隔（每次切换账户间隔）
   - 尾部随机表情（防止检测同账户发送垃圾信息）
   <div style="text-align: center;">
      <img src="/zh-cn/send/img_7.png" alt="发送参数" style="width: 40%; border-radius: 20px; margin: 20px auto;" />
   </div>

5. 保存后点击"开始发送"按钮启动群发任务
   <div style="text-align: center;">
      <img src="/zh-cn/send/img_8.png" alt="开始发送" style="width: 40%; border-radius: 20px; margin: 20px auto;" />
   </div>

6. 发送结果
   <div style="text-align: center;">
      <img src="/zh-cn/send/img_9.png" alt="发送结果1" style="width: 40%; border-radius: 20px; margin: 20px auto;" />
      <img src="/zh-cn/send/img_10.png" alt="发送结果2" style="width: 40%; border-radius: 20px; margin: 20px auto;" />
   </div>

## ⚠️ 注意事项

1. 为避免触发 Telegram 风控，建议：
   - 合理设置发送间隔，不要过于频繁
   - 控制线程数量，避免并发过高
   - 单次群发数量不要过大
   - 单账户每日不超过30条
2. 使用前请确保：
   - 发送账号状态正常，无风控
   - 目标用户/群组可以接收消息
   - 消息内容符合 Telegram 规范

## 💡 使用技巧

1. 首次使用建议：
   - 先用少量目标测试
   - 观察发送成功率
   - 根据反馈调整参数
2. 提高发送成功率：
   - 使用匹配目标地区的号段
   - 避免使用高风险号段
   - 保持账号活跃度

## 🛡️ 安全建议

1. 定期检查账号状态
2. 避免发送违规内容
3. 不要频繁更换发送设备和 IP
4. 保持正常的使用行为模式
