# 本地代理模式
> [!IMPORTANT]
>
> 本地代理模式由3proxy提供支持，支持将多ip服务器的公网IP绑定到不同的telegram账户，保证IP稳定优质，账户使用ip稳定，适合高质量账户使用。

## 📣 功能简介

该模块用于批量管理 Telegram 自动化任务所使用的代理 IP，支持快速导入、检测、筛选与清理操作，是高频群发、私信和采集任务中**保障账号安全与任务稳定的关键配置**。

适用于以下场景：

- 给不同账号绑定独立代理，防止同源 IP 被封
- 检测代理是否可用，自动筛选高质量线路
- 清理无效或失效的代理，保持代理池健康
- 批量导入并绑定账号，简化初始化配置流程

## 本地代理和远程代理的区别

| 本地代理    | 远程代理    |
| ----------- | ----------- |
| ✅socks5支持 | ✅socks5支持 |
| ✅IP长期稳定 | ✅IP长期稳定 |
