from PySide6.QtWidgets import QWidget, QTableWidgetItem, QHBoxLayout, QMenu
from PySide6.QtCore import Signal, Qt, QTimer
from PySide6.QtGui import QColor
from functools import partial
from qfluentwidgets import ToolButton, FluentIcon, InfoBar, InfoBarPosition, Action, CheckBox
from ui.account_manage_ui import AccountManageUI

class AccountManager(AccountManageUI):
    """账户管理逻辑处理类，处理UI中的逻辑部分"""
    
    def __init__(self, parent=None):
        super().__init__(parent)  # 初始化UI组件
        self.setObjectName('AccountManager')
        
        # 存储当前显示的账户数据 (由 controller 更新)
        self._current_accounts_data = [] 
        # 缓存分组账户数量 {group_id: count}
        self._group_counts = {}
        # 记录正在刷新的账户及其刷新按钮
        self._refreshing_accounts = {} # {phone: refresh_button}
        
        # 连接信号
        self.connect_manager_signals()
    
    def connect_manager_signals(self):
        """连接账户管理器的信号"""
        # 连接账户上下文菜单信号
        self.show_account_menu_requested.connect(self.create_account_context_menu)
        
        # 确保刷新按钮点击信号已连接
        self.refresh_all_btn.clicked.connect(self.on_refresh_all_accounts)
        # 连接批量编辑按钮信号
        self.batch_edit_btn.clicked.connect(self.on_batch_edit)
    
    def create_account_context_menu(self, phone, global_pos):
        """创建账户右键菜单"""
        # 获取当前账户已在的分组 ID 列表
        account = self._find_account_in_current_data(phone)
        if not account:
            return
            
        current_group_ids = {g.id for g in account.groups} if hasattr(account, 'groups') and account.groups else set()

        menu = QMenu(self)
        group_submenu = QMenu("添加到分组", menu)
        remove_group_submenu = QMenu("从分组移除", menu)
        
        # 获取所有分组（从分组项字典中获取）
        all_groups = {gid: item.text_label.text().split(' (')[0] 
                     for gid, item in self.group_items.items() if gid != -1}
        
        has_assignable_groups = False
        for group_id, group_name in all_groups.items():
            if group_id not in current_group_ids:
                assign_action = Action(group_name)
                # 使用 lambda 捕获 phone 和 group_id
                assign_action.triggered.connect(lambda checked=False, p=phone, gid=group_id: 
                                             self.assign_account_to_group_requested.emit(p, gid))
                group_submenu.addAction(assign_action)
                has_assignable_groups = True
                
        if not has_assignable_groups:
            no_assign_action = Action("没有可添加的分组")
            no_assign_action.setEnabled(False)
            group_submenu.addAction(no_assign_action)
            
        has_removable_groups = False
        for group_id, group_name in all_groups.items():
            if group_id in current_group_ids:
                remove_action = Action(group_name)
                # 使用 lambda 捕获 phone 和 group_id
                remove_action.triggered.connect(lambda checked=False, p=phone, gid=group_id: 
                                             self.remove_account_from_group_requested.emit(p, gid))
                remove_group_submenu.addAction(remove_action)
                has_removable_groups = True
            
        if not has_removable_groups:
            no_remove_action = Action("不在任何分组中")
            no_remove_action.setEnabled(False)
            remove_group_submenu.addAction(no_remove_action)

        menu.addMenu(group_submenu)
        if has_removable_groups: # 只有在至少一个分组中时才显示移除菜单
            menu.addMenu(remove_group_submenu)
            
        # 添加其他操作（如编辑、刷新单个账号等）
        menu.addSeparator()
        
        # 刷新按钮
        refresh_action = Action('刷新状态')
        refresh_action.triggered.connect(lambda: self.refresh_account_requested.emit(phone))
        menu.addAction(refresh_action)
        
        # 验证按钮 (当账号状态不是online时显示)
        if hasattr(account, 'status') and account.status not in ['online', 'active']:
            verify_action = Action('验证账号')
            verify_action.triggered.connect(lambda: self.verify_account_requested.emit(phone))
            menu.addAction(verify_action)
        
        # 编辑功能
        edit_action = Action('编辑账号信息')
        edit_action.triggered.connect(lambda: self.edit_account_requested.emit(phone))
        menu.addAction(edit_action)
        
        # 删除
        menu.addSeparator()
        delete_action = Action('删除账号')
        delete_action.triggered.connect(lambda: self.on_delete_user(phone))
        menu.addAction(delete_action)
        
        menu.exec(global_pos)
    
    def update_user_table(self, users):
        """根据当前筛选条件更新用户表格"""
        # 缓存数据，用于右键菜单查找
        self._current_accounts_data = users
        
        # 清空表格前，记录哪些账户正在刷新中
        refreshing_phones = list(self._refreshing_accounts.keys())
        
        self.table.setRowCount(len(users))
        
        # 如果是显示所有账户，更新分组计数
        if self.current_filter_group_id == -1:
            # 这里不需要清空，_update_group_counts会重新计算
            self._update_group_counts(users)
        
        # 导入datetime用于计算活跃时间
        from datetime import datetime, timedelta
        now = datetime.now()
        
        # 更新表格内容        
        for row, user in enumerate(users):
            # 手机号
            self.table.setItem(row, 0, QTableWidgetItem(user.phone))
            
            # 用户名/昵称
            username_display = ""
            if hasattr(user, 'username') and user.username:
                username_display = f"@{user.username}"
            else:
                username_display = "(未设置用户名)"
            
            fullname = f"{getattr(user, 'first_name', '')} {getattr(user, 'last_name', '')}".strip()
            if not fullname:
                fullname = "未设置昵称"
            
            display_name = f"{username_display} | {fullname}"
            self.table.setItem(row, 1, QTableWidgetItem(display_name))
            
            # 状态 (设置失效为红色)
            status_item = QTableWidgetItem(user.status)
            if user.status and '失效' in user.status:
                 status_item.setForeground(QColor('red')) # 设置文字颜色为红色
            self.table.setItem(row, 2, status_item)
            
            # 活跃时间
            active_days = "1天"  # 默认至少为1天
            if hasattr(user, 'first_login_time') and user.first_login_time:
                try:
                    # 计算从first_login_time到现在的天数
                    days_diff = (now - user.first_login_time).days
                    if days_diff < 1:
                        active_days = "1天"  # 不足1天按1天计算
                    else:
                        active_days = f"{days_diff}天"
                except Exception:
                    pass  # 如果计算出错，使用默认值
            
            self.table.setItem(row, 3, QTableWidgetItem(active_days))
            
            # 所属分组
            group_names = ", ".join([g.name for g in user.groups]) if hasattr(user, 'groups') and user.groups else "无"
            self.table.setItem(row, 4, QTableWidgetItem(group_names))
            
            # --- 新的操作列按钮 ---
            op_widget = QWidget()
            op_layout = QHBoxLayout(op_widget)
            op_layout.setContentsMargins(2, 2, 2, 2) # 稍微给点边距
            op_layout.setSpacing(5)

            # 刷新按钮
            refresh_btn = ToolButton(FluentIcon.SYNC)
            refresh_btn.setToolTip("刷新状态")
            refresh_btn.clicked.connect(partial(self.on_refresh_account, user.phone))
            
            # 如果账户正在刷新中，禁用刷新按钮
            if user.phone in refreshing_phones:
                refresh_btn.setEnabled(False)
                self._refreshing_accounts[user.phone] = refresh_btn  # 更新按钮引用

            # 修改按钮 (待实现功能)
            edit_btn = ToolButton(FluentIcon.EDIT)
            edit_btn.setToolTip("修改账号信息 (待实现)")
            edit_btn.clicked.connect(partial(self.on_edit_account, user.phone))

            # 验证按钮 (条件显示)
            verify_btn = ToolButton(FluentIcon.ACCEPT_MEDIUM) # 或者其他合适的图标
            verify_btn.setToolTip("验证账号")
            verify_btn.clicked.connect(partial(self.on_verify_account, user.phone))
            # 仅在状态不是 'online' 时显示验证按钮
            verify_btn.setVisible(user.status not in ['online', 'active'])
            
            # 删除按钮
            delete_btn = ToolButton(FluentIcon.DELETE)
            delete_btn.setToolTip("删除账号")
            delete_btn.clicked.connect(partial(self.on_delete_user, user.phone))

            op_layout.addWidget(refresh_btn)
            op_layout.addWidget(edit_btn)
            op_layout.addWidget(verify_btn)
            op_layout.addStretch()
            op_layout.addWidget(delete_btn)

            self.table.setCellWidget(row, 5, op_widget) # 添加到合并后的第 5 列
            # --- 结束：新的操作列按钮 ---
        
        # 更新当前视图分组的计数
        if self.current_filter_group_id != -1 and self.current_filter_group_id in self.group_items:
            group_item = self.group_items[self.current_filter_group_id]
            group_name = group_item.text_label.text().split(' (')[0]
            group_item.text_label.setText(f"{group_name} ({len(users)})")

    def _update_group_counts(self, accounts):
        """仅更新分组计数缓存，不更新UI
        这个方法被控制器调用，确保在切换到筛选视图前，分组计数已正确计算
        """
        # 清空之前的分组计数缓存
        self._group_counts = {}
        # 统计每个分组的账户数量
        for user in accounts:
            if hasattr(user, 'groups') and user.groups:  # 确保用户有groups属性且不为空
                for group in user.groups:
                    self._group_counts[group.id] = self._group_counts.get(group.id, 0) + 1
        
        # 打印调试信息
        print(f"已更新分组计数: {self._group_counts}")
        
        # 同时更新分组按钮上的显示
        all_item = self.group_items.get(-1)
        if all_item:
            all_item.text_label.setText(f"所有账号 ({len(accounts)})")
            
        # 更新其他分组项的计数显示
        for group_id, group_item in self.group_items.items():
            if group_id != -1:  # 排除"所有账号"项
                group_name = group_item.text_label.text().split(' (')[0]  # 获取纯分组名
                count = self._group_counts.get(group_id, 0)
                group_item.text_label.setText(f"{group_name} ({count})")
    
    # --- 新的操作按钮处理方法 ---
    def on_refresh_account(self, phone):
        """处理刷新按钮点击"""
        print(f"请求刷新账号: {phone}")
        # 找到当前行的刷新按钮
        for row in range(self.table.rowCount()):
            if self.table.item(row, 0) and self.table.item(row, 0).text() == phone:
                op_widget = self.table.cellWidget(row, 5)
                if op_widget:
                    # 获取刷新按钮 (第一个按钮)
                    refresh_btn = op_widget.layout().itemAt(0).widget()
                    if refresh_btn:
                        # 禁用按钮并保存引用
                        refresh_btn.setEnabled(False)
                        self._refreshing_accounts[phone] = refresh_btn
                        break
        
        # 发送刷新信号
        self.refresh_account_requested.emit(phone)

    def enable_refresh_button(self, phone, enable=True):
        """启用或禁用账号的刷新按钮
        
        Args:
            phone: 手机号
            enable: True为启用，False为禁用
        """
        if phone in self._refreshing_accounts:
            refresh_btn = self._refreshing_accounts.pop(phone) if enable else self._refreshing_accounts[phone]
            if refresh_btn and refresh_btn.isEnabled() != enable:
                refresh_btn.setEnabled(enable)
                print(f"账号 {phone} 的刷新按钮已{'启用' if enable else '禁用'}")
        else:
            # 如果找不到按钮引用，尝试在表格中查找
            self._find_and_enable_refresh_button(phone, enable)

    def _find_and_enable_refresh_button(self, phone, enable=True):
        """在表格中查找并启用/禁用刷新按钮"""
        for row in range(self.table.rowCount()):
            if self.table.item(row, 0) and self.table.item(row, 0).text() == phone:
                op_widget = self.table.cellWidget(row, 5)
                if op_widget:
                    # 获取刷新按钮 (第一个按钮)
                    refresh_btn = op_widget.layout().itemAt(0).widget()
                    if refresh_btn:
                        refresh_btn.setEnabled(enable)
                        if not enable:
                            self._refreshing_accounts[phone] = refresh_btn
                        print(f"通过查找表格{'启用' if enable else '禁用'}了账号 {phone} 的刷新按钮")
                        break

    def on_edit_account(self, phone):
        """处理编辑用户按钮点击
        
        在表格中点击编辑按钮时调用
        """
        # 发出编辑信号
        self.edit_account_requested.emit(phone)
        
        # 显示开发中提示
        InfoBar.info(
            title='功能开发中',
            content="用户编辑功能已经可用",
            orient=Qt.Horizontal,
            isClosable=True,
            position=InfoBarPosition.TOP,
            duration=3000,
            parent=self
        )

    def on_verify_account(self, phone):
        """处理验证按钮点击"""
        print(f"请求验证账号: {phone}")
        self.verify_account_requested.emit(phone)
        
    def _find_account_in_current_data(self, phone):
        """从当前账户数据中查找特定手机号的账户"""
        return next((acc for acc in self._current_accounts_data if acc.phone == phone), None) 

    def on_refresh_all_accounts(self):
        """处理全部刷新按钮点击"""
        InfoBar.info(
            title='正在刷新',
            content="正在刷新所有账号状态，请稍候...",
            orient=Qt.Horizontal, isClosable=True,
            position=InfoBarPosition.TOP, duration=3000, parent=self
        )
        
        # 遍历当前显示的所有账户
        for account in self._current_accounts_data:
            if hasattr(account, 'phone'):
                # 发送刷新信号
                self.refresh_account_requested.emit(account.phone)
                # 禁用刷新按钮
                self._find_and_disable_refresh_button(account.phone)
    
    def _find_and_disable_refresh_button(self, phone):
        """在表格中查找并禁用刷新按钮"""
        for row in range(self.table.rowCount()):
            if self.table.item(row, 0) and self.table.item(row, 0).text() == phone:
                op_widget = self.table.cellWidget(row, 5)
                if op_widget:
                    # 获取刷新按钮 (第一个按钮)
                    refresh_btn = op_widget.layout().itemAt(0).widget()
                    if refresh_btn:
                        refresh_btn.setEnabled(False)
                        self._refreshing_accounts[phone] = refresh_btn
                        break 

    def on_batch_edit(self):
        """处理批量编辑按钮点击"""
        # 获取选中的账户
        selected_accounts = self.get_selected_accounts()
        
        if not selected_accounts:
            # 如果没有选中任何账户，显示提示
            InfoBar.warning(
                title='未选择账户',
                content="请先选择要编辑的账户",
                orient=Qt.Horizontal,
                isClosable=True,
                position=InfoBarPosition.TOP,
                duration=3000,
                parent=self
            )
            return
        
        # 发出批量编辑信号
        self.batch_edit_clicked.emit()
        
    def get_selected_accounts(self):
        """获取表格中选中的账户
        
        Returns:
            list: 选中的账户对象列表
        """
        selected_accounts = []
        
        # 遍历表格中所有行，检查第一列是否有选中状态
        for row in range(self.table.rowCount()):
            checkbox_cell = self.table.cellWidget(row, 0)
            if checkbox_cell and isinstance(checkbox_cell, QWidget):
                # 获取checkbox
                checkbox = None
                for i in range(checkbox_cell.layout().count()):
                    item = checkbox_cell.layout().itemAt(i).widget()
                    if isinstance(item, CheckBox):
                        checkbox = item
                        break
                
                if checkbox and checkbox.isChecked():
                    # 找到对应的账户对象
                    phone_item = self.table.item(row, 0)
                    if phone_item:
                        phone = phone_item.text()
                        account = self._find_account_in_current_data(phone)
                        if account:
                            selected_accounts.append(account)
        
        return selected_accounts 