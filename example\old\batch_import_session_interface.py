from PySide6.QtWidgets import QTableWidgetItem, QWidget, QHBoxLayout, QVBoxLayout, QDialog
from PySide6.QtCore import Signal, Qt
from PySide6.QtWidgets import QFileDialog
from ui.dialog.batch_import_session_ui import BatchImportSessionUI
from qfluentwidgets import CheckBox, ComboBox, MessageBox, InfoBar, InfoBarPosition, FluentIcon, getIconColor
from PySide6.QtGui import QIcon, QColor, QBrush
from pathlib import Path
from PySide6.QtWidgets import QApplication

class BatchImportSessionDialog(QDialog):
    """批量导入Session对话框，仅负责界面展示和简单逻辑处理，复杂逻辑由控制器处理"""
    
    # 定义信号
    import_sessions_signal = Signal(list, int)  # sessions列表, 最大并发数
    
    def __init__(self, parent=None, logger=None):
        super().__init__(parent)
        self.logger = logger
        self.ui = BatchImportSessionUI()
        self.setWindowTitle("批量导入Session")
        self.resize(1000, 700)
        
        # 使用垂直布局设置UI
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.addWidget(self.ui)
        self.setLayout(layout)
        
        # 数据存储
        self.session_files = []  # 选中的session文件列表
        self.proxy_list = []     # 可用代理列表
        self.group_list = []     # 可用分组列表
        
        # 用于管理IP绑定统计和代理映射
        self.ip_binding_count = {}  # 格式: {ip_port: count}
        self.session_proxy_map = {}  # 格式: {session_file: proxy_config}
        
        # 存储UI组件引用
        self.row_checkboxes = []    # 每行的复选框
        self.row_proxy_combos = []  # 每行的主代理类型下拉框
        self.row_ip_combos = []     # 每行的IP池选择下拉框
        self.row_group_combos = []  # 每行的分组下拉框
        
        # 默认选中全选按钮
        self.ui.select_all_checkbox.setChecked(True)
        
        # 连接信号
        self.connect_signals()
    
    def connect_signals(self):
        """连接所有信号"""
        self.ui.import_button.clicked.connect(self.select_session_files)
        self.ui.refresh_proxy_button.clicked.connect(self.refresh_proxy_list)
        self.ui.refresh_groups_button.clicked.connect(self.refresh_groups_list)
        self.ui.select_all_checkbox.stateChanged.connect(self.toggle_select_all)
        self.ui.login_button.clicked.connect(self.login_selected_sessions)
        self.ui.cancel_button.clicked.connect(self.close)
    
    def show_info(self, title, content):
        """显示信息通知"""
        InfoBar.success(
            title=title,
            content=content,
            orient=Qt.Horizontal,
            isClosable=True,
            position=InfoBarPosition.TOP,
            duration=2000,
            parent=self
        )
    
    def show_warning(self, title, content):
        """显示警告通知"""
        InfoBar.warning(
            title=title,
            content=content,
            orient=Qt.Horizontal,
            isClosable=True,
            position=InfoBarPosition.TOP,
            duration=3000,
            parent=self
        )
    
    # ================ 文件选择相关 ================
    
    def select_session_files(self):
        """选择session文件"""
        files, _ = QFileDialog.getOpenFileNames(self, "选择Session文件", "", "Session Files (*.session)")
        if files:
            # 处理Windows路径格式
            processed_files = []
            for file in files:
                # 替换正斜杠为反斜杠(Windows风格)
                processed_file = file.replace('/', '\\') if '/' in file else file
                processed_files.append(processed_file)
                
            self.session_files = processed_files
            self.update_session_table()
            self.show_info("文件选择", f"已选择 {len(files)} 个Session文件")
    
    # ================ 表格相关 ================
    
    def update_session_table(self):
        """更新会话表格"""
        # 清空表格
        self.ui.session_table.setRowCount(0)
        
        # 暂停全选按钮信号
        self.ui.select_all_checkbox.blockSignals(True)
        
        # 清空组件引用列表
        self.row_checkboxes = []
        self.row_proxy_combos = []
        self.row_ip_combos = []
        self.row_group_combos = []
        
        # 当前全选状态
        all_checked = self.ui.select_all_checkbox.isChecked()
        
        # 添加行
        for file in self.session_files:
            self._add_session_row(file, all_checked)
        
        # 调整列宽
        self.ui.adjust_table_column_widths()
        
        # 恢复全选按钮信号
        self.ui.select_all_checkbox.blockSignals(False)
    
    def _add_session_row(self, file, checked=True):
        """添加一行会话数据"""
        row = self.ui.add_table_row()
        filename = Path(file).name
        
        # 添加复选框
        checkbox_container, checkbox = self.ui.create_checkbox_cell(checked)
        checkbox.stateChanged.connect(self.update_session_combo_items)
        self.row_checkboxes.append(checkbox)
        self.ui.session_table.setCellWidget(row, 0, checkbox_container)
        
        # 添加文件名
        self.ui.set_session_filename(row, filename, file)
        
        # 添加代理选择
        proxy_container, proxy_combo, ip_combo = self.ui.create_proxy_selection_cell()
        self.row_proxy_combos.append(proxy_combo)
        self.row_ip_combos.append(ip_combo)
        
        # 设置当前代理状态
        current_proxy = self.session_proxy_map.get(file)
        if current_proxy:
            if current_proxy.get('type') == 'system':
                proxy_combo.setCurrentIndex(1)
                ip_combo.setVisible(False)
            elif current_proxy.get('type') == 'ip_pool':
                proxy_combo.setCurrentIndex(2)
                ip_combo.setVisible(True)
                # 更新IP池选项
                self.update_ip_combo(ip_combo, file)
            else:
                proxy_combo.setCurrentIndex(0)
                ip_combo.setVisible(False)
        else:
            proxy_combo.setCurrentIndex(0)
            ip_combo.setVisible(False)
        
        # 连接信号
        proxy_combo.currentIndexChanged.connect(
            lambda idx, row=row: self.on_proxy_type_changed(row, idx)
        )
        ip_combo.currentIndexChanged.connect(
            lambda idx, row=row: self.on_ip_selection_changed(row, idx)
        )
        
        self.ui.session_table.setCellWidget(row, 2, proxy_container)
        
        # 添加分组选择
        group_container, group_combo = self.ui.create_group_selection_cell(self.group_list)
        self.row_group_combos.append(group_combo)
        self.ui.session_table.setCellWidget(row, 3, group_container)
        
        # 添加状态
        status_item = self.ui.create_status_cell("待登录", "#3296fa")
        self.ui.session_table.setItem(row, 4, status_item)
    
    def update_session_combo_items(self):
        """当单个复选框状态改变时，检查是否需要更新全选按钮"""
        if not self.row_checkboxes:
            return
            
        all_checked = all(cb.isChecked() for cb in self.row_checkboxes if cb)
        
        if self.ui.select_all_checkbox.isChecked() != all_checked:
            self.ui.select_all_checkbox.blockSignals(True)
            self.ui.select_all_checkbox.setChecked(all_checked)
            self.ui.select_all_checkbox.blockSignals(False)
    
    def resizeEvent(self, event):
        """当对话框大小改变时，调整表格列宽"""
        super().resizeEvent(event)
        self.ui.adjust_table_column_widths()
    
    # ================ 代理相关 ================
    
    def update_ip_combo(self, ip_combo, session_file):
        """更新IP池下拉框"""
        if not ip_combo:
            return
        
        # 保存当前选中的索引
        current_index = ip_combo.currentIndex()
        current_data = ip_combo.itemData(current_index) if current_index >= 0 else None
        
        # 清空combobox
        ip_combo.clear()
        
        # 添加空选项
        ip_combo.addItem("选择代理", {"ip": "", "port": ""})
        
        # 添加可用IP
        for proxy in self.proxy_list:
            proxy_key = f"{proxy['ip']}:{proxy['port']}"
            binding_count = self.ip_binding_count.get(proxy_key, 0)
            ip_combo.addItem(f"{proxy['ip']}:{proxy['port']} (已绑定:{binding_count})", 
                             {"ip": proxy['ip'], "port": proxy['port']})
        
        # 恢复选中状态
        if current_data and current_data.get('ip'):
            current_key = f"{current_data['ip']}:{current_data['port']}"
            for i in range(ip_combo.count()):
                combo_data = ip_combo.itemData(i)
                if combo_data and f"{combo_data['ip']}:{combo_data['port']}" == current_key:
                    ip_combo.setCurrentIndex(i)
                    break
        else:
            # 检查会话文件是否已有关联的代理
            current_proxy = self.session_proxy_map.get(session_file)
            if current_proxy and current_proxy.get('type') == 'ip_pool' and current_proxy.get('ip'):
                current_key = f"{current_proxy['ip']}:{current_proxy['port']}"
                for i in range(ip_combo.count()):
                    combo_data = ip_combo.itemData(i)
                    if combo_data and f"{combo_data['ip']}:{combo_data['port']}" == current_key:
                        ip_combo.setCurrentIndex(i)
                        break
        
        # 更新选择的会话文件对应的代理IP
        if session_file and ip_combo.currentIndex() > 0:
            selected_data = ip_combo.itemData(ip_combo.currentIndex())
            if selected_data and selected_data.get('ip'):
                proxy_config = {
                    'type': 'ip_pool',
                    'ip': selected_data.get('ip'),
                    'port': selected_data.get('port'),
                    'username': selected_data.get('username', ''),
                    'password': selected_data.get('password', '')
                }
                self.session_proxy_map[session_file] = proxy_config
    
    def on_proxy_type_changed(self, row, index):
        """处理代理类型更改事件"""
        if row >= len(self.row_ip_combos) or row >= len(self.session_files):
            return
        
        session_file = self.session_files[row]
        ip_combo = self.row_ip_combos[row]
        
        # 阻断IP下拉框信号，防止级联触发
        ip_combo.blockSignals(True)
        
        # 获取旧的代理配置
        old_proxy = self.session_proxy_map.get(session_file)
        
        # 解除旧代理的绑定关系
        if old_proxy and old_proxy.get('type') == 'ip_pool':
            self.unbind_ip(old_proxy.get('ip'), old_proxy.get('port'))
        
        # 根据代理类型处理
        if index == 2:  # IP池
            ip_combo.setVisible(True)
            
            # 如果IP下拉框为空，则初始化选项
            if ip_combo.count() == 0:
                ip_combo.addItem("选择代理IP", {"ip": "", "port": ""})
                
                for proxy in self.proxy_list:
                    proxy_key = f"{proxy['ip']}:{proxy['port']}"
                    binding_count = self.ip_binding_count.get(proxy_key, 0)
                    ip_combo.addItem(f"{proxy['ip']}:{proxy['port']} (已绑定:{binding_count})", 
                                   {"ip": proxy['ip'], "port": proxy['port']})
            
            # 默认选择第一项
            ip_combo.setCurrentIndex(0)
            
            # 移除当前session的代理配置
            if session_file in self.session_proxy_map:
                del self.session_proxy_map[session_file]
        else:
            # 隐藏IP下拉框
            ip_combo.setVisible(False)
            
            # 更新代理配置
            if index == 1:  # 系统代理
                self.session_proxy_map[session_file] = {'type': 'system'}
            else:  # 无代理
                if session_file in self.session_proxy_map:
                    del self.session_proxy_map[session_file]
        
        # 恢复IP下拉框信号
        ip_combo.blockSignals(False)
    
    def on_ip_selection_changed(self, row, index):
        """处理IP选择更改事件"""
        if row >= len(self.row_proxy_combos) or row >= len(self.session_files):
            return
        
        session_file = self.session_files[row]
        ip_combo = self.row_ip_combos[row]
        proxy_combo = self.row_proxy_combos[row]
        
        # 确保代理类型是IP池
        if proxy_combo.currentIndex() != 2:
            return
        
        # 获取旧的代理配置并解除绑定
        old_proxy = self.session_proxy_map.get(session_file)
        if old_proxy and old_proxy.get('type') == 'ip_pool':
            self.unbind_ip(old_proxy.get('ip'), old_proxy.get('port'))
        
        # 获取新选择的IP数据
        proxy_data = ip_combo.itemData(index)
        
        # 如果选择了有效的IP
        if index > 0 and proxy_data and proxy_data.get('ip') and proxy_data.get('port'):
            # 更新代理配置并添加类型标识
            proxy_config = {
                'type': 'ip_pool',
                'ip': proxy_data.get('ip'),
                'port': proxy_data.get('port'),
                'username': proxy_data.get('username', ''),
                'password': proxy_data.get('password', '')
            }
            
            # 更新代理配置
            self.session_proxy_map[session_file] = proxy_config
            
            # 绑定新IP
            self.bind_ip(proxy_config.get('ip'), proxy_config.get('port'))
            
            # 更新IP标签
            self._update_ip_labels()
        else:
            # 未选择IP或选择了"不使用代理"，移除代理绑定
            if session_file in self.session_proxy_map:
                del self.session_proxy_map[session_file]
    
    def bind_ip(self, ip, port):
        """增加IP绑定计数"""
        if not ip or not port:
            return
            
        ip_key = f"{ip}:{port}"
        self.ip_binding_count[ip_key] = self.ip_binding_count.get(ip_key, 0) + 1
    
    def unbind_ip(self, ip, port):
        """减少IP绑定计数"""
        if not ip or not port:
            return
            
        ip_key = f"{ip}:{port}"
        if ip_key in self.ip_binding_count:
            self.ip_binding_count[ip_key] = max(0, self.ip_binding_count.get(ip_key, 0) - 1)
    
    def _update_ip_labels(self):
        """只更新所有IP下拉框的显示文本，不触发任何事件"""
        for row in range(len(self.row_ip_combos)):
            if row >= len(self.session_files):
                continue
                
            ip_combo = self.row_ip_combos[row]
            
            for i in range(ip_combo.count()):
                if i == 0:  # 跳过第一项
                    continue
                    
                combo_data = ip_combo.itemData(i)
                if combo_data and combo_data.get('ip') and combo_data.get('port'):
                    proxy_key = f"{combo_data['ip']}:{combo_data['port']}"
                    binding_count = self.ip_binding_count.get(proxy_key, 0)
                    current_text = ip_combo.itemText(i)
                    
                    # 如果是IP文本加绑定数的格式，则更新
                    if "(" in current_text:
                        base_text = current_text.split(" (")[0]
                        ip_combo.setItemText(i, f"{base_text} (已绑定:{binding_count})")
    
    def refresh_ip_combos(self):
        """刷新所有IP下拉框的状态"""
        for row in range(len(self.row_ip_combos)):
            if row >= len(self.session_files):
                continue
            
            ip_combo = self.row_ip_combos[row]
            session_file = self.session_files[row]
            
            # 暂时阻止信号触发
            ip_combo.blockSignals(True)
            
            # 记住当前选中的代理数据
            current_proxy = self.session_proxy_map.get(session_file, {})
            
            # 清空下拉框并重新填充选项
            ip_combo.clear()
            
            # 添加空选项
            ip_combo.addItem("不使用代理", {"ip": "", "port": ""})
            
            # 添加可用IP
            for proxy in self.proxy_list:
                proxy_key = f"{proxy['ip']}:{proxy['port']}"
                binding_count = self.ip_binding_count.get(proxy_key, 0)
                ip_combo.addItem(f"{proxy['ip']}:{proxy['port']} (已绑定:{binding_count})", 
                                {"ip": proxy['ip'], "port": proxy['port']})
            
            # 恢复选中状态
            if current_proxy and 'ip' in current_proxy and 'port' in current_proxy:
                current_key = f"{current_proxy['ip']}:{current_proxy['port']}"
                for i in range(ip_combo.count()):
                    item_data = ip_combo.itemData(i)
                    if item_data and f"{item_data['ip']}:{item_data['port']}" == current_key:
                        ip_combo.setCurrentIndex(i)
                        break
            else:
                # 如果没有当前选中的代理，默认选择第一项
                ip_combo.setCurrentIndex(0)
            
            # 恢复信号
            ip_combo.blockSignals(False)
    
    # ================ 对外接口方法 ================
    
    def refresh_proxy_list(self):
        """刷新代理列表 - 由控制器实现具体逻辑"""
        pass
    
    def refresh_groups_list(self):
        """刷新分组列表 - 由控制器实现具体逻辑"""
        pass
    
    def update_proxy_list(self, proxy_list):
        """更新代理列表并刷新显示"""
        self.proxy_list = proxy_list
        
        # 初始化IP绑定计数
        for proxy in self.proxy_list:
            proxy_key = f"{proxy['ip']}:{proxy['port']}"
            # 使用传入的account_count初始化IP绑定计数
            self.ip_binding_count[proxy_key] = proxy.get('account_count', 0)
        
        # 更新表格
        self.update_session_table()
        self.show_info("代理更新", f"已更新 {len(proxy_list)} 个代理")
        
        # 刷新IP下拉框
        self.refresh_ip_combos()
    
    def update_groups_list(self, group_list):
        """更新分组列表并刷新显示"""
        self.group_list = group_list
        self.update_session_table()
        self.show_info("分组更新", f"已更新 {len(group_list)} 个分组")
    
    def toggle_select_all(self, state):
        """处理全选/取消全选"""
        if not self.row_checkboxes:
            return
            
        is_checked = (state == Qt.Checked)
        
        # 遍历所有复选框并设置状态
        for checkbox in self.row_checkboxes:
            if checkbox:
                checkbox.blockSignals(True)
                checkbox.setChecked(is_checked)
                checkbox.blockSignals(False)
    
    def login_selected_sessions(self):
        """收集选中的会话信息并发送给控制器"""
        selected_sessions = []
        
        for i, checkbox in enumerate(self.row_checkboxes):
            if checkbox and checkbox.isChecked() and i < len(self.session_files):
                session_file = self.session_files[i]
                group_combo = self.row_group_combos[i]
                
                # 获取代理配置
                proxy_config = self.session_proxy_map.get(session_file)
                
                # 获取分组ID
                try:
                    group_id = int(group_combo.currentData() or -1)
                except (ValueError, TypeError):
                    group_id = -1  # 默认无分组
                
                selected_sessions.append({
                    'session_file': session_file,
                    'proxy_config': proxy_config,
                    'group_id': group_id
                })
        
        # 获取最大并发数
        max_concurrent = int(self.ui.max_concurrent_combo.currentText())
        
        # 检查是否有选中的会话
        if not selected_sessions:
            self.show_warning("无选择", "请至少选择一个Session文件")
            return
            
        # 确认对话框
        confirm = MessageBox(
            "确认导入",
            f"将导入 {len(selected_sessions)} 个Session文件，最大并发数 {max_concurrent}\n确定继续吗？",
            self
        )
        
        if confirm.exec():
            # 发送信号给控制器处理
            self.import_sessions_signal.emit(selected_sessions, max_concurrent)
            self.accept()  # 关闭对话框
