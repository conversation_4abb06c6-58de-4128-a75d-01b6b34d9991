import os
import asyncio
import logging
import time
from typing import List, Dict, Optional, Tuple, Any, Callable
from PySide6.QtCore import QObject, Signal, Slot, QThreadPool, QRunnable, QTimer
from qasync import asyncSlot

from app.models.entities.proxy import ProxyItem
from app.services.proxy_service import ProxyService


class ProxySignals(QObject):
    """代理信号类，用于在异步操作完成后发出信号"""
    
    # 代理服务状态变化信号: (是否运行, 消息)
    service_status_changed = Signal(bool, str)
    
    # 代理列表更新信号: (代理列表, 消息)
    proxies_updated = Signal(list, str)
    
    # 代理验证结果信号: (代理ID到状态的映射, 消息)
    verify_result = Signal(dict, str)
    
    # 代理添加结果信号: (成功数量, 消息)
    add_result = Signal(int, str)
    
    # 代理删除结果信号: (成功数量, 消息)
    delete_result = Signal(int, str)
    
    # 公网IP获取结果信号: (IP地址, 消息)
    public_ip_result = Signal(str, str)


class ProxyController(QObject):
    """代理控制器，连接UI和服务"""
    
    def __init__(self, model, view, logger=None, proxy_service=None):
        super().__init__()
        
        # 初始化日志组件
        self.logger = logger or logging.getLogger("ProxyController")
        
        # 初始化模型和视图
        self.proxy_model = model
        self.proxy_view = view
        
        # 初始化服务
        self.proxy_service = proxy_service or ProxyService(logger=self.logger)
        
        # 信号对象
        self.signals = ProxySignals()
        
        # 记录上次代理服务状态和检查时间
        self.last_proxy_status = False
        self.last_check_time = 0
        
        # 自动检查代理服务状态
        self.status_check_timer = QTimer(self)
        self.status_check_timer.timeout.connect(self.check_service_status)
        self.status_check_timer.start(15000)  # 延长到15秒检查一次，减少干扰
        
        # 连接视图信号
        self._connect_view_signals()
        
        # 初始化视图
        self._init_view()
    
    def _connect_view_signals(self):
        """连接视图信号"""
        if self.proxy_view:
            # 连接视图界面信号到控制器方法
            self.proxy_view.add_proxy_signal.connect(self.add_proxies)
            self.proxy_view.validate_selected_signal.connect(self.verify_proxies)
            self.proxy_view.validate_all_signal.connect(lambda: self.verify_proxies())
            self.proxy_view.delete_selected_signal.connect(self.delete_proxies)
            self.proxy_view.delete_invalid_signal.connect(self.delete_invalid_proxies)
            self.proxy_view.toggle_service_signal.connect(self.toggle_proxy_service)
            self.proxy_view.restart_signal.connect(self.restart_proxy)
            self.proxy_view.ip_type_changed_signal.connect(self._on_ip_type_changed)
            
            # 连接控制器信号到视图更新方法
            self.signals.service_status_changed.connect(self.proxy_view.update_service_status)
            self.signals.proxies_updated.connect(self.proxy_view.update_proxy_list)
            self.signals.verify_result.connect(self.proxy_view.update_verify_results)
            self.signals.add_result.connect(self.proxy_view.update_add_result)
            self.signals.delete_result.connect(self.proxy_view.update_delete_result)
            self.signals.public_ip_result.connect(self.proxy_view.update_public_ip)
    
    def _init_view(self):
        """初始化视图"""
        if self.proxy_view:
            # 加载代理列表
            self.load_proxies()
            
            # 检查服务状态
            self.check_service_status()
            
            # 启动时检查本机IP并启动服务
            self._check_local_ips_and_start_service()
    
    def _on_ip_type_changed(self, index: int):
        """IP类型变更处理
        
        Args:
            index: 下拉框索引
        """
        # 如果选择了本机公网IP，则尝试获取
        if index == 0:
            self.get_public_ip()
    
    @Slot()
    @asyncSlot()
    async def toggle_proxy_service(self):
        """切换代理服务状态（启动/停止）"""
        try:
            if self.proxy_service.is_proxy_running():
                result = await self._stop_proxy()
            else:
                result = await self._start_proxy()
                
            self.logger.info(f"切换代理服务: {result}")
        except Exception as e:
            self.logger.error(f"切换代理服务失败: {str(e)}")
            self.signals.service_status_changed.emit(False, f"操作失败: {str(e)}")
    
    async def _start_proxy(self) -> str:
        """启动代理服务
        
        Returns:
            str: 操作结果消息
        """
        try:
            # 先生成配置文件
            proxies = self.proxy_model.get_all_proxies()
            config_result = await self.proxy_service.generate_config(proxies)
            if not config_result:
                self.signals.service_status_changed.emit(False, "生成配置文件失败")
                return "生成配置文件失败"
            
            # 然后启动服务
            success, message = await self.proxy_service.start_proxy()
            self.signals.service_status_changed.emit(success, message)
            self.logger.info(f"启动代理服务: {'成功' if success else '失败'}, {message}")
            return message
        except Exception as e:
            self.logger.error(f"启动代理服务出错: {str(e)}")
            self.signals.service_status_changed.emit(False, f"启动出错: {str(e)}")
            return f"启动出错: {str(e)}"
    
    async def _stop_proxy(self) -> str:
        """停止代理服务
        
        Returns:
            str: 操作结果消息
        """
        try:
            success, message = await self.proxy_service.stop_proxy()
            self.signals.service_status_changed.emit(not success, message)
            self.logger.info(f"停止代理服务: {'成功' if success else '失败'}, {message}")
            return message
        except Exception as e:
            self.logger.error(f"停止代理服务出错: {str(e)}")
            self.signals.service_status_changed.emit(True, f"停止出错: {str(e)}")
            return f"停止出错: {str(e)}"
    
    @Slot()
    @asyncSlot()
    async def restart_proxy(self):
        """重启代理服务"""
        try:
            # 先停止服务
            await self._stop_proxy()
            
            # 再启动服务
            await self._start_proxy()
            
            self.logger.info("重启代理服务完成")
        except Exception as e:
            self.logger.error(f"重启代理服务失败: {str(e)}")
            self.signals.service_status_changed.emit(False, f"重启失败: {str(e)}")
    
    @Slot()
    @asyncSlot()
    async def check_service_status(self):
        """检查代理服务状态"""
        try:
            # 获取当前时间
            current_time = time.time()
            
            # 避免短时间内重复检查（防抖动）
            if current_time - self.last_check_time < 10:  # 至少10秒间隔
                return
                
            self.last_check_time = current_time
            
            # 使用不阻塞当前任务的方式运行
            is_running = self.proxy_service.is_proxy_running()
            
            # 只有状态变化时才发送信号
            if is_running != self.last_proxy_status:
                self.last_proxy_status = is_running
                self.signals.service_status_changed.emit(is_running, "服务状态已更新")
                
        except Exception as e:
            self.logger.error(f"检查代理服务状态失败: {str(e)}")
            # 不发送信号，避免UI更新错误
    
    @Slot(str, bool, bool, int, int, int, str, str)
    @asyncSlot()
    async def add_proxies(self, ip_text, is_local_ip, use_fixed_port, fixed_port, min_port, max_port, username, password):
        """添加代理
        
        Args:
            ip_text: IP文本，支持多行和范围
            is_local_ip: 是否为本地IP
            use_fixed_port: 是否使用固定端口
            fixed_port: 固定端口值
            min_port: 最小端口
            max_port: 最大端口
            username: 自定义用户名（仅SOCKS5模式有效）
            password: 自定义密码（仅SOCKS5模式有效）
        """
        try:
            # 发出状态更新信号
            self.signals.add_result.emit(0, "正在添加代理，请稍候...")
            
            # 组装端口范围
            port_range = (min_port, max_port)
            
            # 记录添加操作的基本信息
            proxy_type = "LOCAL" if is_local_ip else "SOCKS5"
            port_info = f"固定端口: {fixed_port}" if use_fixed_port else f"端口范围: {min_port}-{max_port}"
            self.logger.info(f"尝试添加代理，类型: {proxy_type}，{port_info}")
            
            # 解析IP范围和生成代理列表
            count, message, all_proxies = self.parse_and_add_proxies(
                ip_text, is_local_ip, use_fixed_port, fixed_port, port_range, username, password
            )
            
            if count <= 0:
                self.signals.add_result.emit(0, message)
                return
            
            # 发出结果信号
            message = f"成功添加{count}个代理"
            self.signals.add_result.emit(count, message)
            self.logger.info(f"添加代理: {message}")
            
            # 更新代理列表
            await self.load_proxies()
            
            # 根据代理类型决定后续操作
            if proxy_type == "LOCAL" and self.proxy_service.is_proxy_running():
                # 本地代理需要重启3Proxy服务
                self.signals.add_result.emit(count, f"{message}，正在重启代理服务...")
                await self.restart_proxy()
                
                # 延迟一秒确保服务已启动
                await asyncio.sleep(1)
            elif proxy_type == "LOCAL" and not self.proxy_service.is_proxy_running():
                # 本地代理但服务未启动，尝试启动服务
                self.signals.add_result.emit(count, f"{message}，正在启动代理服务...")
                await self._start_proxy()
                
                # 延迟一秒确保服务已启动
                await asyncio.sleep(1)
            elif proxy_type == "SOCKS5":
                # SOCKS5代理不需要启动3Proxy服务
                use_auth = True if username and password else False
                auth_info = "无认证" if not use_auth else "有认证"
                self.signals.add_result.emit(count, f"{message}，SOCKS5代理({auth_info})可直接使用，无需启动服务")
            
            # 无论哪种类型都验证代理
            self.signals.add_result.emit(count, f"{message}，正在验证代理...")
            
            # 仅验证新添加的代理
            new_proxy_ids = [proxy.id for proxy in all_proxies]
            await self.verify_proxies(new_proxy_ids)
                
        except Exception as e:
            self.logger.error(f"添加代理失败: {str(e)}")
            self.signals.add_result.emit(0, f"添加失败: {str(e)}")
    
    def parse_and_add_proxies(self, ip_text, is_local_ip, use_fixed_port, fixed_port, port_range, username=None, password=None):
        """解析IP文本并添加代理
        
        Args:
            ip_text: IP文本，支持多行和范围
            is_local_ip: 是否为本地IP
            use_fixed_port: 是否使用固定端口
            fixed_port: 固定端口值
            port_range: 端口范围元组 (min_port, max_port)
            username: 自定义用户名（仅SOCKS5模式有效）
            password: 自定义密码（仅SOCKS5模式有效）
            
        Returns:
            Tuple[int, str, List[ProxyItem]]: (成功数量, 消息, 添加的代理列表)
        """
        try:
            # 解析IP范围
            ip_ranges = self.proxy_model.parse_ip_range(ip_text)
            
            if not ip_ranges:
                return 0, "未找到有效的IP地址或范围", []
            
            # 设置代理类型
            proxy_type = "LOCAL" if is_local_ip else "SOCKS5"
            
            # 确定是否使用认证
            # 如果是SOCKS5代理且未提供用户名和密码，则视为无认证代理
            use_auth = True  # 默认使用认证
            if proxy_type == "SOCKS5" and not username and not password:
                use_auth = False
                self.logger.info("添加无认证SOCKS5代理")
            
            # 生成代理列表
            all_proxies = []
            for start_ip, end_ip in ip_ranges:
                # 设置端口参数
                port_param = fixed_port if use_fixed_port else None
                range_param = port_range if not use_fixed_port else None
                
                # 生成代理
                proxies = self.proxy_model.generate_proxy_from_range(
                    start_ip, end_ip,
                    port_range=range_param,
                    fixed_port=port_param,
                    proxy_type=proxy_type,
                    username=username if username else None,
                    password=password if password else None,
                    use_auth=use_auth
                )
                all_proxies.extend(proxies)
            
            if not all_proxies:
                return 0, "生成代理失败，请检查IP格式", []
            
            # 添加到数据库
            success_count = self.proxy_model.add_proxy_batch(all_proxies)
            
            if success_count > 0:
                return success_count, f"成功添加{success_count}个代理", all_proxies
            else:
                return 0, "添加代理失败", []
                
        except Exception as e:
            self.logger.error(f"解析并添加代理失败: {str(e)}")
            return 0, f"添加代理失败: {str(e)}", []
    
    @Slot()
    @asyncSlot()
    async def load_proxies(self):
        """加载所有代理"""
        try:
            proxies = self.proxy_model.get_all_proxies()
            self.signals.proxies_updated.emit(proxies, "")
            self.logger.info(f"加载代理列表: {len(proxies)}个")
        except Exception as e:
            self.logger.error(f"加载代理列表失败: {str(e)}")
            self.signals.proxies_updated.emit([], f"加载失败: {str(e)}")
    
    @Slot(list)
    @asyncSlot()
    async def verify_proxies(self, proxy_ids=None):
        """验证代理可用性
        
        Args:
            proxy_ids: 代理ID列表，为None则验证所有代理
        """
        try:
            # 获取要验证的代理
            if proxy_ids is None:
                proxies = self.proxy_model.get_all_proxies()
                self.logger.info(f"开始验证所有代理，共{len(proxies)}个")
            else:
                proxies = [self.proxy_model.get_proxy_by_id(pid) for pid in proxy_ids]
                proxies = [p for p in proxies if p is not None]
                self.logger.info(f"开始验证选中代理，共{len(proxies)}个")
            
            if not proxies:
                self.logger.warning("没有找到需要验证的代理")
                self.signals.verify_result.emit({}, "无代理需要验证")
                return
                
            results = {}
            
            # 设置验证状态为"验证中"
            for proxy in proxies:
                self.proxy_model.update_proxy_status(proxy.id, "验证中")
            
            # 先更新UI
            await self.load_proxies()
            
            # 并发验证代理，但限制并发数量以避免资源耗尽
            tasks = []
            batch_size = min(10, len(proxies))  # 最多同时验证10个代理
            
            self.logger.info(f"使用批次大小 {batch_size} 验证代理")
            
            for i in range(0, len(proxies), batch_size):
                batch = proxies[i:i+batch_size]
                batch_tasks = []
                
                for proxy in batch:
                    task = self.verify_single_proxy(proxy)
                    batch_tasks.append(task)
                
                # 等待这一批次完成
                batch_results = await asyncio.gather(*batch_tasks, return_exceptions=True)
                
                # 处理结果
                for proxy, result in zip(batch, batch_results):
                    if isinstance(result, Exception):
                        self.logger.error(f"验证代理 {proxy.ip}:{proxy.port} 时发生异常: {str(result)}")
                        results[proxy.id] = False
                    else:
                        results[proxy.id] = result
                
                # 每批次完成后更新一次UI
                self.signals.verify_result.emit(results.copy(), "验证进行中...")
                
                # 短暂延迟，避免过快发送请求
                await asyncio.sleep(0.5)
            
            # 发送验证结果信号
            valid_count = sum(1 for v in results.values() if v)
            total_count = len(results)
            self.signals.verify_result.emit(results, f"验证完成: {valid_count}/{total_count} 个代理可用")
            self.logger.info(f"代理验证完成: 共{total_count}个，有效{valid_count}个")
            
            # 更新代理列表
            await self.load_proxies()
            
        except Exception as e:
            self.logger.error(f"验证代理失败: {str(e)}")
            self.signals.verify_result.emit({}, f"验证失败: {str(e)}")
    
    async def verify_single_proxy(self, proxy: ProxyItem) -> bool:
        """验证单个代理并更新其状态
        
        Args:
            proxy: 代理项
            
        Returns:
            bool: 是否有效
        """
        try:
            # 使用服务验证连接
            is_valid = await self.proxy_service.verify_proxy_connection(proxy)
            
            # 更新代理状态
            status = "正常" if is_valid else "验证失败"
            self.proxy_model.update_proxy_status(proxy.id, status)
            
            return is_valid
        except Exception as e:
            self.logger.error(f"验证代理 {proxy.ip}:{proxy.port} 时出错: {str(e)}")
            self.proxy_model.update_proxy_status(proxy.id, "验证失败")
            return False
    
    @Slot(list)
    @asyncSlot()
    async def delete_proxies(self, proxy_ids):
        """删除代理
        
        Args:
            proxy_ids: 代理ID列表
        """
        try:
            # 获取要删除的代理类型信息
            has_local_proxy = False
            for pid in proxy_ids:
                proxy = self.proxy_model.get_proxy_by_id(pid)
                if proxy and proxy.proxy_type == "LOCAL":
                    has_local_proxy = True
                    break
            
            # 删除代理
            count = self.proxy_model.delete_proxies(proxy_ids)
            self.signals.delete_result.emit(count, f"成功删除{count}个代理")
            self.logger.info(f"删除代理: {count}个")
            
            # 更新代理列表
            await self.load_proxies()
            
            # 如果删除了本地代理，更新配置文件并重启服务
            if has_local_proxy:
                # 获取所有剩余的代理，用于重新生成配置文件
                all_proxies = self.proxy_model.get_all_proxies()
                
                # 重新生成配置文件
                config_result = await self.proxy_service.generate_config(all_proxies)
                if not config_result:
                    self.signals.delete_result.emit(count, f"警告：成功删除{count}个代理，但生成配置文件失败")
                    self.logger.error("删除代理后生成配置文件失败")
                    return
                
                # 检查服务是否正在运行
                if self.proxy_service.is_proxy_running():
                    # 检查是否还有本地代理
                    remaining_local_proxies = self.proxy_model.get_proxies_by_type("LOCAL")
                    
                    if remaining_local_proxies:
                        # 如果还有LOCAL代理，重启服务应用新配置
                        self.signals.delete_result.emit(count, f"成功删除{count}个代理，正在更新代理服务...")
                        await self.restart_proxy()
                    else:
                        # 如果没有LOCAL代理了，停止服务
                        self.signals.delete_result.emit(count, f"成功删除{count}个代理，正在停止代理服务（无剩余本地代理）...")
                        await self.stop_proxy()
                
        except Exception as e:
            self.logger.error(f"删除代理失败: {str(e)}")
            self.signals.delete_result.emit(0, f"删除失败: {str(e)}")
    
    @Slot()
    @asyncSlot()
    async def delete_invalid_proxies(self):
        """删除所有验证失败的代理"""
        try:
            # 先获取所有验证失败的本地代理
            all_proxies = self.proxy_model.get_all_proxies()
            has_invalid_local = any(p.status == "验证失败" and p.proxy_type == "LOCAL" for p in all_proxies)
            
            # 删除所有验证失败的代理
            count = self.proxy_model.delete_invalid_proxies()
            self.signals.delete_result.emit(count, f"成功删除{count}个失效代理")
            self.logger.info(f"删除失效代理: {count}个")
            
            # 更新代理列表
            await self.load_proxies()
            
            # 如果删除了本地代理，更新配置文件并重启服务
            if has_invalid_local:
                # 获取所有剩余的代理，用于重新生成配置文件
                remaining_proxies = self.proxy_model.get_all_proxies()
                
                # 重新生成配置文件
                config_result = await self.proxy_service.generate_config(remaining_proxies)
                if not config_result:
                    self.signals.delete_result.emit(count, f"警告：成功删除{count}个失效代理，但生成配置文件失败")
                    self.logger.error("删除失效代理后生成配置文件失败")
                    return
                
                # 检查服务是否正在运行
                if self.proxy_service.is_proxy_running():
                    # 检查是否还有本地代理
                    remaining_local_proxies = self.proxy_model.get_proxies_by_type("LOCAL")
                    
                    if remaining_local_proxies:
                        # 如果还有LOCAL代理，重启服务应用新配置
                        self.signals.delete_result.emit(count, f"成功删除{count}个失效代理，正在更新代理服务...")
                        await self.restart_proxy()
                    else:
                        # 如果没有LOCAL代理了，停止服务
                        self.signals.delete_result.emit(count, f"成功删除{count}个失效代理，正在停止代理服务（无剩余本地代理）...")
                        await self.stop_proxy()
                
        except Exception as e:
            self.logger.error(f"删除失效代理失败: {str(e)}")
            self.signals.delete_result.emit(0, f"删除失败: {str(e)}")
    
    @Slot()
    @asyncSlot()
    async def get_public_ip(self):
        """获取本机公网IP"""
        try:
            ip = await self.proxy_service.get_public_ip()
            if ip:
                self.signals.public_ip_result.emit(ip, "获取公网IP成功")
                self.logger.info(f"获取公网IP成功: {ip}")
            else:
                self.signals.public_ip_result.emit("", "获取公网IP失败")
                self.logger.warning("获取公网IP失败: 未返回结果")
                
        except Exception as e:
            self.logger.error(f"获取公网IP失败: {str(e)}")
            self.signals.public_ip_result.emit("", f"获取IP失败: {str(e)}")
    
    @asyncSlot()
    async def _check_local_ips_and_start_service(self):
        """启动检查：检查是否有本机公网IP，如果有则确保3proxy服务启动"""
        
        # 使用锁确保同一时间只有一个检查任务在执行
        if hasattr(self, '_check_service_lock') and self._check_service_lock.locked():
            self.logger.debug("已有启动检查任务在运行，跳过此次检查")
            return
        
        # 懒加载创建锁
        if not hasattr(self, '_check_service_lock'):
            self._check_service_lock = asyncio.Lock()
        
        # 使用非阻塞方式尝试获取锁
        if not self._check_service_lock.locked():
            async with self._check_service_lock:
                try:
                    # 创建一个Future来存储服务状态检查结果
                    loop = asyncio.get_running_loop()
                    check_future = loop.create_future()
                    
                    # 用一个独立的任务来执行实际检查
                    async def _do_startup_check():
                        try:
                            # 获取所有代理
                            proxies = self.proxy_model.get_all_proxies()
                            
                            # 检查是否有本地IP代理
                            has_local_ip = any(proxy.proxy_type == "LOCAL" for proxy in proxies)
                            
                            if has_local_ip:
                                # 检查服务是否已启动
                                service_running = await self.proxy_service.check_service_status()
                                
                                if not service_running:
                                    # 尝试启动服务
                                    try:
                                        # 使用net start命令直接启动
                                        result = await self.proxy_service._run_command("net start 3proxy")
                                        
                                        if "成功" in result or "success" in result.lower() or "started" in result.lower():
                                            self.proxy_service.is_running = True
                                            result = (True, "代理服务已自动启动")
                                            self.logger.info("启动检查：3proxy服务已自动启动")
                                        else:
                                            # 启动失败，提示用户
                                            result = (False, "代理IP池自启失败，请前往IP池手动启动代理IP再配置账号")
                                            self.logger.warning(f"启动检查：3proxy服务自启失败: {result}")
                                    except Exception as e:
                                        result = (False, f"代理IP池自启失败: {str(e)}")
                                        self.logger.error(f"启动检查：3proxy服务自启错误: {str(e)}")
                                else:
                                    self.logger.info("启动检查：3proxy服务已在运行")
                                    result = (True, "代理服务已在运行")
                            else:
                                self.logger.info("启动检查：未发现本地IP代理，无需启动服务")
                                result = None  # 不需要任何UI更新
                            
                            if not check_future.cancelled() and not check_future.done():
                                check_future.set_result(result)
                        except asyncio.CancelledError:
                            # 优雅地处理取消
                            self.logger.debug("启动检查任务被取消")
                            raise
                        except Exception as e:
                            if not check_future.cancelled() and not check_future.done():
                                check_future.set_exception(e)
                    
                    # 创建任务但不等待它完成
                    # 使用弱引用避免循环引用导致的资源泄漏
                    import weakref
                    self_weak = weakref.ref(self)
                    
                    def _done_callback(fut):
                        try:
                            # 检查self是否仍然存在
                            self_obj = self_weak()
                            if self_obj is None:
                                return
                            
                            # 如果任务完成但Future仍未被设置，取消Future
                            if not check_future.done() and not check_future.cancelled():
                                check_future.cancel()
                        except Exception as e:
                            # 使用类的日志记录器代替logging模块直接调用
                            if self_weak() is not None:
                                self_weak().logger.error(f"启动检查回调函数错误: {str(e)}")
                            else:
                                # 如果对象已被销毁，使用根日志记录器
                                logging.getLogger().error(f"启动检查回调函数错误: {str(e)}")
                    
                    # 创建任务并添加完成回调
                    check_task = loop.create_task(_do_startup_check())
                    check_task.add_done_callback(_done_callback)
                    
                    # 设置超时，避免无限等待
                    try:
                        # 最多等待5秒
                        result = await asyncio.wait_for(check_future, 5.0)
                        # 只有当需要更新UI状态时才发送信号
                        if result is not None:
                            status, message = result
                            self.signals.service_status_changed.emit(status, message)
                    except asyncio.TimeoutError:
                        self.logger.warning("启动检查超时")
                    except asyncio.CancelledError:
                        self.logger.debug("启动检查被取消")
                    except Exception as e:
                        self.logger.error(f"执行启动检查过程中出错: {str(e)}")
                    finally:
                        # 确保任务最终被取消（如果尚未完成）
                        if not check_task.done() and not check_task.cancelled():
                            check_task.cancel()
                        
                        # 确保future被适当处理
                        if not check_future.done() and not check_future.cancelled():
                            check_future.cancel()
                
                except Exception as e:
                    self.logger.error(f"启动检查失败: {str(e)}")
        else:
            self.logger.debug("无法获取启动检查锁，跳过此次检查")
    
    def get_valid_proxies(self) -> List[ProxyItem]:
        """获取所有有效代理列表，供其他页面调用
        
        Returns:
            List[ProxyItem]: 有效代理列表
        """
        return self.proxy_model.get_valid_proxies()
    
    def get_valid_proxies_by_type(self, proxy_type: str) -> List[ProxyItem]:
        """获取指定类型的有效代理，供其他页面调用
        
        Args:
            proxy_type: 代理类型，如"LOCAL"或"SOCKS5"
            
        Returns:
            List[ProxyItem]: 有效代理列表
        """
        return self.proxy_model.get_valid_proxies_by_type(proxy_type)
    
    async def stop_proxy(self):
        """停止代理服务"""
        try:
            success, message = await self.proxy_service.stop_proxy()
            if success:
                self.signals.service_status_changed.emit(False, "代理服务已停止")
                self.logger.info("代理服务已停止")
            else:
                self.signals.service_status_changed.emit(True, f"停止服务失败: {message}")
                self.logger.error(f"停止服务失败: {message}")
        except Exception as e:
            self.logger.error(f"停止代理服务错误: {str(e)}")
            self.signals.service_status_changed.emit(True, f"停止服务错误: {str(e)}") 