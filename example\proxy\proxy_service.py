import os
import subprocess
import platform
import re
import asyncio
import random
import string
import logging
import aiohttp
import json
from typing import List, Tuple, Dict, Optional, Union
import ipaddress
from pathlib import Path

# 导入aiohttp_socks用于更可靠的代理连接
try:
    from aiohttp_socks import ProxyConnector
    SOCKS_CONNECTOR_AVAILABLE = True
except ImportError:
    SOCKS_CONNECTOR_AVAILABLE = False

# 只导入需要的实体模型，不直接导入操作模型
from app.models.entities.proxy import ProxyItem
from app.common.setting import PROXY_DIR, PROXY_EXE, PROXY_CONFIG, CONFIG_FOLDER, IP_CHECK_SERVICES


class ProxyService:
    """3Proxy代理服务管理"""
    
    def __init__(self, logger=None):
        """初始化代理服务
        
        Args:
            logger: 日志记录器
        """
        # 使用settings中的路径配置
        self.proxy_dir = PROXY_DIR
        self.proxy_exe = PROXY_EXE
        self.config_path = PROXY_CONFIG
        self.app_data_dir = CONFIG_FOLDER
        
        # 确保目录存在
        #self.app_data_dir.mkdir(exist_ok=True)
        
        # 代理运行状态
        self.is_running = False
        
        # 日志
        self.logger = logger or logging.getLogger("ProxyService")
        
        # 如果aiohttp_socks可用，记录日志
        if SOCKS_CONNECTOR_AVAILABLE:
            self.logger.info("aiohttp_socks库已加载，将使用专用连接器进行代理验证")
        else:
            self.logger.warning("aiohttp_socks库不可用，将使用标准aiohttp进行代理验证")
    
    async def start_proxy(self) -> Tuple[bool, str]:
        """启动3proxy代理服务
        
        Returns:
            Tuple[bool, str]: (成功状态, 消息)
        """
        if not self.proxy_exe.exists():
            return False, f"代理程序不存在: {self.proxy_exe}"
        
        if not self.config_path.exists():
            return False, f"配置文件不存在: {self.config_path}"
        
        # 尝试启动代理
        try:
            # 安装并启动服务
            result = await self._run_command(f'"{self.proxy_exe}" --install "{self.config_path}"')
            
            if "成功" in result or "success" in result.lower() or "started" in result.lower():
                self.is_running = True
                return True, "代理服务启动成功"
                
            # 启动失败，尝试停止再启动
            await self.stop_proxy(silent=True)
            result = await self._run_command(f'"{self.proxy_exe}" --install "{self.config_path}"')
            
            if "成功" in result or "success" in result.lower() or "started" in result.lower():
                self.is_running = True
                return True, "代理服务启动成功"
            else:
                return False, f"代理服务启动失败: {result}"
                
        except Exception as e:
            self.logger.error(f"启动代理服务出错: {str(e)}")
            return False, f"启动代理服务出错: {str(e)}"
    
    async def stop_proxy(self, silent: bool = False) -> Tuple[bool, str]:
        """停止3proxy代理服务
        
        Args:
            silent: 是否静默操作，不返回错误
            
        Returns:
            Tuple[bool, str]: (成功状态, 消息)
        """
        try:
            # 停止服务
            await self._run_command("net stop 3proxy")
            
            # 删除服务
            await self._run_command(f'"{self.proxy_exe}" --remove')
            
            self.is_running = False
            return True, "代理服务已停止"
        except Exception as e:
            if silent:
                return False, ""
            else:
                self.logger.error(f"停止代理服务出错: {str(e)}")
                return False, f"停止代理服务出错: {str(e)}"
    
    async def restart_proxy(self) -> Tuple[bool, str]:
        """重启3proxy代理服务
        
        Returns:
            Tuple[bool, str]: (成功状态, 消息)
        """
        # 先停止
        await self.stop_proxy(silent=True)
        
        # 再启动
        return await self.start_proxy()
    
    async def generate_config(self, proxies: List[ProxyItem]) -> bool:
        """根据代理列表生成3proxy配置文件
        
        Args:
            proxies: 代理列表
            
        Returns:
            bool: 是否成功生成配置
        """
        try:
            # 过滤出本地IP代理（只有LOCAL类型的代理需要3proxy服务）
            local_proxies = [proxy for proxy in proxies if proxy.proxy_type == "LOCAL"]
            
            self.logger.info(f"开始生成3proxy配置文件，共有 {len(local_proxies)} 个本地IP代理")
            
            # 基础配置
            log_path = (self.app_data_dir / "3proxy.log").as_posix()
            
            config_lines = [
                "# 3proxy配置文件",
                "nserver *******",
                "# 首选 DNS 服务器",
                "nserver *******",
                "# 备用 DNS 服务器",
                "maxconn 1000",
                "# 最大连接数",
                "nscache 65536",
                "#nserver指定了DNS服务器，nscache用于DNS缓存，提高解析速度。",
                "auth none",
                "#账户认证 空",
                "service",
                "#在windows上作为服务启动",               
                "timeouts 1 5 30 60 180 1800 15 60",
                "#设置连接超时",
                f'log "{log_path}"',
                "logformat \"- +_L%t.%.  %N.%p %E %U %C:%c %R:%r %O %I %h %T\"",
                "rotate 3",
                "#设置日志文件的旋转策略",
                ""
            ]
            
            # 添加代理配置（只添加LOCAL类型代理）
            for proxy in local_proxies:               
                # 本地IP代理
                #proxy_line = f"proxy -p{proxy.port} -a -n -i{proxy.ip} -e{proxy.ip}"
                proxy_line = f"socks -na -a:{proxy.username}:{proxy.password}  -{proxy.ip} -p{proxy.port} -e{proxy.ip}"

                config_lines.append(proxy_line)
                config_lines.append("")
            
            # 写入配置文件
            self.config_path.write_text('\n'.join(config_lines), encoding='utf-8')
            return True
        except Exception as e:
            self.logger.error(f"生成配置文件失败: {str(e)}")
            return False
    
    async def verify_proxy_connection(self, proxy: ProxyItem) -> bool:
        """验证单个代理连接是否可用
        
        Args:
            proxy: 代理项
            
        Returns:
            bool: 是否可用
        """
        self.logger.info(f"验证代理连接: {proxy}")
        try:
            # 如果是LOCAL类型，先检查3proxy服务是否运行
            if proxy.proxy_type == "LOCAL" and not self.is_running:
                self.logger.warning(f"本地代理验证失败：3proxy服务未运行 {proxy.ip}:{proxy.port}")
                return False
            
            # 超时设置
            timeout = aiohttp.ClientTimeout(total=10)
            
            # 根据是否有aiohttp_socks库选择不同的连接方式
            if SOCKS_CONNECTOR_AVAILABLE:
                # 使用专用的SOCKS连接器
                if proxy.username and proxy.password:
                    # 有认证
                    connector = ProxyConnector.from_url(
                        f'socks5://{proxy.username}:{proxy.password}@{proxy.ip}:{proxy.port}'
                    )
                else:
                    # 无认证
                    connector = ProxyConnector.from_url(
                        f'socks5://{proxy.ip}:{proxy.port}'
                    )
                
                self.logger.info(f"使用ProxyConnector连接: {proxy.ip}:{proxy.port}")
                async with aiohttp.ClientSession(connector=connector, timeout=timeout) as session:
                    # 依次尝试IP检测服务
                    for service_url in IP_CHECK_SERVICES:
                        try:
                            self.logger.info(f"尝试通过代理连接到服务: {service_url}")
                            async with session.get(service_url) as response:
                                if response.status == 200:
                                    content = await response.text()
                                    self.logger.info(f"代理连接成功 (ProxyConnector): {proxy.ip}:{proxy.port}, 响应: {content}, 服务: {service_url}")
                                    return True
                        except Exception as e:
                            self.logger.warning(f"通过服务 {service_url} 验证失败: {str(e)}")
                            continue  # 继续尝试下一个服务
                            
                    self.logger.warning(f"所有IP检测服务均验证失败 (ProxyConnector): {proxy.ip}:{proxy.port}")
                    return False
            else:
                # 使用标准aiohttp的代理参数
                if proxy.username and proxy.password:
                    proxy_url = f"socks5://{proxy.username}:{proxy.password}@{proxy.ip}:{proxy.port}"
                else:
                    proxy_url = f"socks5://{proxy.ip}:{proxy.port}"
                
                self.logger.info(f"使用标准方式连接，代理URL: {proxy_url}")
                async with aiohttp.ClientSession(timeout=timeout) as session:
                    # 依次尝试IP检测服务
                    for service_url in IP_CHECK_SERVICES:
                        try:
                            self.logger.info(f"尝试通过代理连接到服务: {service_url}")
                            async with session.get(service_url, proxy=proxy_url) as response:
                                if response.status == 200:
                                    content = await response.text()
                                    self.logger.info(f"代理连接成功 (标准方式): {proxy.ip}:{proxy.port}, 响应: {content}, 服务: {service_url}")
                                    return True
                        except Exception as e:
                            self.logger.warning(f"通过服务 {service_url} 验证失败: {str(e)}")
                            continue  # 继续尝试下一个服务
                    
                    self.logger.warning(f"所有IP检测服务均验证失败 (标准方式): {proxy.ip}:{proxy.port}")
                    return False
                
        except aiohttp.ClientProxyConnectionError as e:
            self.logger.error(f"代理连接错误: {proxy.ip}:{proxy.port}, 错误: {str(e)}")
            return False
        except aiohttp.ClientConnectorError as e:
            self.logger.error(f"连接器错误: {proxy.ip}:{proxy.port}, 错误: {str(e)}")
            return False
        except asyncio.TimeoutError:
            self.logger.error(f"连接超时: {proxy.ip}:{proxy.port}")
            return False
        except Exception as e:
            self.logger.error(f"代理连接验证出错: {proxy.ip}:{proxy.port}, 错误类型: {type(e).__name__}, 错误: {str(e)}")
            return False
    
    async def get_public_ip(self) -> Optional[str]:
        """获取本机公网IP
        
        Returns:
            Optional[str]: 公网IP或None
        """
        timeout = aiohttp.ClientTimeout(total=10)
        
        async with aiohttp.ClientSession(timeout=timeout) as session:
            # 依次尝试所有IP检测服务
            for service_url in IP_CHECK_SERVICES:
                try:
                    async with session.get(service_url, timeout=timeout) as response:
                        if response.status == 200:
                            # 检查响应内容是否为IP地址或包含IP地址
                            content_type = response.headers.get('Content-Type', '')
                            if 'application/json' in content_type:
                                # JSON格式响应，如httpbin.org/ip
                                try:
                                    data = await response.json()
                                    ip = data.get('origin') or data.get('ip')
                                    if ip:
                                        self.logger.info(f"成功获取公网IP: {ip} (通过 {service_url})")
                                        return ip
                                except json.JSONDecodeError:
                                    continue
                            else:
                                # 文本格式响应，直接包含IP地址
                                text = await response.text()
                                if text and re.match(r'^(\d{1,3}\.){3}\d{1,3}$', text.strip()):
                                    self.logger.info(f"成功获取公网IP: {text.strip()} (通过 {service_url})")
                                    return text.strip()
                except Exception as e:
                    self.logger.debug(f"获取公网IP失败 {service_url}: {str(e)}")
                    continue  # 尝试下一个服务
        
        self.logger.error("所有IP获取服务均失败，无法获取公网IP")
        return None
    
    async def _run_command(self, command: str) -> str:
        """运行shell命令
        
        Args:
            command: 命令字符串
            
        Returns:
            str: 命令输出
        """
        # 使用异步执行命令
        process = await asyncio.create_subprocess_shell(
            command,
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.PIPE,
            shell=True
        )
        
        # 获取输出
        stdout, stderr = await process.communicate()
        
        # 解码输出
        if platform.system() == "Windows":
            stdout_str = stdout.decode('gbk', errors='ignore')
            stderr_str = stderr.decode('gbk', errors='ignore')
        else:
            stdout_str = stdout.decode('utf-8', errors='ignore')
            stderr_str = stderr.decode('utf-8', errors='ignore')
        
        # 返回合并的输出
        return stdout_str + stderr_str
    
    async def check_service_status(self) -> bool:
        """检查3proxy服务是否在系统中运行
        
        Returns:
            bool: 是否运行
        """
        try:
            # 使用Windows的SC命令查询服务状态
            result = await self._run_command("sc query 3proxy")
            
            # 检查服务是否正在运行
            if "RUNNING" in result:
                self.is_running = True
                return True
            else:
                self.is_running = False
                return False
        except Exception as e:
            self.logger.error(f"检查服务状态出错: {str(e)}")
            self.is_running = False
            return False
    
    def is_proxy_running(self):
        """同步检查代理服务状态（避免异步方法冲突）
        
        Returns:
            bool: 服务是否运行中
        """
        try:
            # 直接使用操作系统命令检查服务进程是否存在
            process_name = "3proxy"
            
            # 根据操作系统选择不同的检查命令
            if os.name == 'nt':  # Windows
                # 使用tasklist命令检查进程
                result = os.popen(f'tasklist /FI "IMAGENAME eq {process_name}.exe" /NH').read()
                return process_name.lower() in result.lower()
            else:  # Linux/Mac
                # 使用ps命令检查进程
                result = os.popen(f'ps -A | grep {process_name}').read()
                return process_name in result and 'grep' not in result
                
        except Exception as e:
            self.logger.error(f"同步检查代理服务状态失败: {e}")
            return False 