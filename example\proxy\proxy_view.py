import os
from typing import List, Dict, Optional, Set
from PySide6.QtWidgets import QWidget, QTableWidgetItem, QMessageBox, QVBoxLayout
from PySide6.QtCore import Qt, Slot, Signal

from qfluentwidgets import InfoBar, InfoBarPosition, ScrollArea

from ui.proxy_manager_ui import ProxyManagerUI
from app.models.entities.proxy import ProxyItem


class ProxyView(ScrollArea):
    """代理管理视图类，提供滚动功能并包含ProxyManagerUI
    
    负责:
    1. 处理UI交互逻辑
    2. 发送信号给控制器
    3. 接收控制器信号并更新UI
    """
    
    # 定义界面信号，这些信号将在控制器中被连接
    add_proxy_signal = Signal(str, bool, bool, int, int, int, str, str)  # IP文本, 是否本地IP, 是否固定端口, 固定端口, 最小端口, 最大端口, 用户名, 密码
    validate_selected_signal = Signal(list)  # 代理ID列表
    validate_all_signal = Signal()  # 无参数
    delete_selected_signal = Signal(list)  # 代理ID列表
    delete_invalid_signal = Signal()  # 无参数
    toggle_service_signal = Signal()  # 切换代理服务
    restart_signal = Signal()  # 重启代理服务
    ip_type_changed_signal = Signal(int)  # IP类型改变
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setObjectName("ProxyView")
        
        # 创建代理管理UI
        self.proxy_ui = ProxyManagerUI()
        
        # 配置ScrollArea
        self.setWidget(self.proxy_ui)
        self.setWidgetResizable(True)
        self.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        self.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        self.setViewportMargins(0, 0, 0, 0)
        
        # 存储代理ID到行索引的映射
        self.proxy_id_to_row = {}
        
        # 连接UI信号到视图信号，这些信号将由控制器处理
        self._connect_ui_signals()
        
    def _connect_ui_signals(self):
        """连接UI组件信号到处理函数"""
        # 切换服务状态
        self.proxy_ui.toggle_proxy_btn.clicked.connect(self.toggle_service_signal)
        
        # 重启服务
        self.proxy_ui.restart_btn.clicked.connect(self.restart_signal)
        
        # 添加代理
        self.proxy_ui.add_proxy_btn.clicked.connect(self._on_add_proxy_clicked)
        
        # 验证代理
        self.proxy_ui.validate_btn.clicked.connect(self._on_validate_selected_clicked)
        self.proxy_ui.validate_all_btn.clicked.connect(self.validate_all_signal)
        
        # 删除代理
        self.proxy_ui.delete_btn.clicked.connect(self._on_delete_selected_clicked)
        self.proxy_ui.delete_invalid_btn.clicked.connect(self.delete_invalid_signal)
        
        # 全选/取消全选
        self.proxy_ui.select_all_cb.clicked.connect(self._on_select_all_changed)
        
        # IP类型改变
        self.proxy_ui.ip_type_combo.currentIndexChanged.connect(self._on_ip_type_changed)
        
        # 端口选项改变
        self.proxy_ui.fixed_port_radio.toggled.connect(self._on_port_option_changed)
        self.proxy_ui.random_port_radio.toggled.connect(self._on_port_option_changed)
    
    def _on_ip_type_changed(self, index):
        """IP类型变更处理
        
        Args:
            index: 下拉框索引
        """
        # 更新用户名密码框的可见性
        is_socks5 = index == 1
        self.proxy_ui.credentials_group.setVisible(is_socks5)
        
        # 如果是SOCKS5模式，显示提示信息
        if is_socks5:
            self.proxy_ui.username_edit.setPlaceholderText("留空表示不使用认证")
            self.proxy_ui.password_edit.setPlaceholderText("留空表示不使用认证")
        
        # 更新端口设置
        self._on_port_option_changed()
        
        # 发送信号
        self.ip_type_changed_signal.emit(index)
    
    def _on_port_option_changed(self):
        """端口选项变更处理"""
        is_fixed = self.proxy_ui.fixed_port_radio.isChecked()
        self.proxy_ui.update_port_widgets_visibility(is_fixed)
    
    def _on_select_all_changed(self, checked):
        """全选/取消全选处理
        
        Args:
            checked: 是否选中
        """
        for row in range(self.proxy_ui.proxy_table.rowCount()):
            checkbox_widget = self.proxy_ui.proxy_table.cellWidget(row, 0)
            if checkbox_widget:
                checkbox = checkbox_widget.findChild(QWidget)
                if checkbox and hasattr(checkbox, 'isChecked'):
                    checkbox.setChecked(checked)
    
    def clear_table(self):
        """清空表格"""
        self.proxy_ui.proxy_table.setRowCount(0)
        self.proxy_id_to_row = {}
    
    def update_service_status(self, is_running: bool, message: str = ""):
        """更新服务状态UI
        
        Args:
            is_running: 是否运行中
            message: 状态消息
        """
        # 更新UI状态
        self.proxy_ui.set_running_style(is_running)
        
        # 显示消息
        if message:
            self.show_success_message("操作结果", message)
    
    def update_proxy_list(self, proxies: List[ProxyItem], message: str = ""):
        """更新代理列表UI
        
        Args:
            proxies: 代理列表
            message: 更新消息
        """
        # 清空表格
        self.clear_table()
        
        # 更新表格
        table = self.proxy_ui.proxy_table
        for index, proxy in enumerate(proxies):
            table.insertRow(index)
            
            # 记录ID到行的映射
            self.proxy_id_to_row[proxy.id] = index
            
            # 添加复选框
            table.setCellWidget(index, 0, self.proxy_ui.create_checkbox())
            
            # 添加数据
            data = [proxy.ip, str(proxy.port), proxy.username, proxy.password, proxy.status, proxy.proxy_type]
            for col, text in enumerate(data):
                item = QTableWidgetItem(text)
                item.setTextAlignment(Qt.AlignCenter)
                
                # 设置状态颜色
                if col == 4:  # 状态列
                    if text == "正常":
                        item.setForeground(Qt.green)
                    elif text == "验证失败":
                        item.setForeground(Qt.red)
                    else:
                        item.setForeground(Qt.gray)
                
                # 设置代理ID为隐藏数据
                if col == 0:  # IP列
                    item.setData(Qt.UserRole, proxy.id)
                
                table.setItem(index, col + 1, item)
        
        # 显示消息
        if message:
            self.show_info_message("提示", message)
    
    def update_verify_results(self, results: Dict[str, bool], message: str = ""):
        """更新验证结果UI
        
        Args:
            results: 验证结果字典 {代理ID: 是否可用}
            message: 消息
        """
        # 更新表格中代理的状态
        for proxy_id, is_valid in results.items():
            if proxy_id in self.proxy_id_to_row:
                row = self.proxy_id_to_row[proxy_id]
                
                # 设置状态文本和颜色
                status_text = "正常" if is_valid else "验证失败"
                status_item = QTableWidgetItem(status_text)
                status_item.setTextAlignment(Qt.AlignCenter)
                
                if is_valid:
                    status_item.setForeground(Qt.green)
                else:
                    status_item.setForeground(Qt.red)
                
                self.proxy_ui.proxy_table.setItem(row, 5, status_item)
        
        # 显示消息
        if message:
            self.show_success_message("验证完成", message)
    
    def update_add_result(self, count: int, message: str = ""):
        """更新添加结果UI
        
        Args:
            count: 添加数量
            message: 消息
        """
        # 清空输入框
        if count > 0:
            self.proxy_ui.ip_edit.clear()
        
        # 显示消息
        if count > 0:
            self.show_success_message("添加成功", message)
        else:
            self.show_error_message("添加失败", message)
    
    def update_delete_result(self, count: int, message: str = ""):
        """更新删除结果UI
        
        Args:
            count: 删除数量
            message: 消息
        """
        # 显示消息
        self.show_success_message("删除结果", message)
    
    def update_public_ip(self, ip: str, message: str = ""):
        """更新公网IP UI
        
        Args:
            ip: IP地址
            message: 消息
        """
        # 如果获取成功且当前选择了本机公网IP，则填入输入框
        if ip and self.proxy_ui.ip_type_combo.currentIndex() == 0:
            self.proxy_ui.ip_edit.setText(ip)
            
            self.show_success_message("IP获取成功", f"您的公网IP: {ip}")
        elif not ip and message:
            self.show_warning_message("IP获取失败", message)
    
    def _on_add_proxy_clicked(self):
        """添加代理按钮点击，验证输入并发出信号"""
        # 获取输入值
        ip_text = self.proxy_ui.ip_edit.toPlainText().strip()
        if not ip_text:
            self.show_warning_message("输入错误", "请输入IP地址")
            return
        
        # 获取IP类型
        is_local_ip = self.proxy_ui.ip_type_combo.currentIndex() == 0
        proxy_type_name = "本机公网IP" if is_local_ip else "外部SOCKS5"
        
        # 获取端口设置
        use_fixed_port = self.proxy_ui.fixed_port_radio.isChecked()
        fixed_port = self.proxy_ui.port_spin.value()
        min_port = self.proxy_ui.min_port_spin.value()
        max_port = self.proxy_ui.max_port_spin.value()
        
        # 验证端口范围
        if not use_fixed_port and min_port >= max_port:
            self.show_warning_message("输入错误", "端口范围无效，最小端口必须小于最大端口")
            return
        
        # 获取用户名密码（如果是SOCKS5模式）
        username = ""
        password = ""
        if not is_local_ip:
            username = self.proxy_ui.username_edit.text()
            password = self.proxy_ui.password_edit.text()
        
        # 显示操作提示信息
        port_info = f"固定端口 {fixed_port}" if use_fixed_port else f"端口范围 {min_port}-{max_port}"
        confirm_message = f"将添加类型为 {proxy_type_name} 的代理，{port_info}"
        
        if not is_local_ip:
            if username and password:
                confirm_message += f"，使用自定义用户名和密码"
            elif not username and not password:
                confirm_message += f"，不使用认证（无用户名和密码）"
            else:
                confirm_message += f"，将自动生成用户名和密码"
        
        if not is_local_ip:
            confirm_message += "\n\n注意: 外部SOCKS5代理不需要启动3Proxy服务即可使用"
            
        self.show_info_message("添加代理", confirm_message)
            
        # 发出添加代理信号
        self.add_proxy_signal.emit(
            ip_text, is_local_ip, use_fixed_port, 
            fixed_port, min_port, max_port,
            username, password
        )
    
    def _on_validate_selected_clicked(self):
        """验证选中代理按钮点击，获取选中项并发出信号"""
        selected_ids = self._get_selected_proxy_ids()
        
        if not selected_ids:
            self.show_warning_message("未选择代理", "请选择要验证的代理")
            return
        
        # 发出验证选中代理信号
        self.validate_selected_signal.emit(list(selected_ids))
    
    def _on_delete_selected_clicked(self):
        """删除选中代理按钮点击，确认并发出信号"""
        selected_ids = self._get_selected_proxy_ids()
        
        if not selected_ids:
            self.show_warning_message("未选择代理", "请选择要删除的代理")
            return
        
        # 确认删除
        reply = QMessageBox.question(
            self,
            "确认删除",
            f"确定要删除选中的 {len(selected_ids)} 个代理吗？",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            # 发出删除选中的代理信号
            self.delete_selected_signal.emit(list(selected_ids))
    
    def _get_selected_proxy_ids(self) -> Set[str]:
        """获取选中的代理ID集合
        
        Returns:
            Set[str]: 选中的代理ID集合
        """
        selected_ids = set()
        
        for row in range(self.proxy_ui.proxy_table.rowCount()):
            checkbox_widget = self.proxy_ui.proxy_table.cellWidget(row, 0)
            if checkbox_widget:
                checkbox = checkbox_widget.findChild(QWidget)
                
                # 检查是否选中
                if checkbox and hasattr(checkbox, 'isChecked') and checkbox.isChecked():
                    # 获取代理ID
                    ip_item = self.proxy_ui.proxy_table.item(row, 1)
                    if ip_item:
                        proxy_id = ip_item.data(Qt.UserRole)
                        if proxy_id:
                            selected_ids.add(proxy_id)
        
        return selected_ids
    
    # 消息显示辅助方法
    def show_success_message(self, title, content, duration=3000):
        """显示成功消息"""
        InfoBar.success(
            title=title,
            content=content,
            parent=self,
            position=InfoBarPosition.TOP,
            duration=duration
        )
    
    def show_info_message(self, title, content, duration=3000):
        """显示信息消息"""
        InfoBar.info(
            title=title,
            content=content,
            parent=self,
            position=InfoBarPosition.TOP,
            duration=duration
        )
    
    def show_warning_message(self, title, content, duration=3000):
        """显示警告消息"""
        InfoBar.warning(
            title=title,
            content=content,
            parent=self,
            position=InfoBarPosition.TOP,
            duration=duration
        )
    
    def show_error_message(self, title, content, duration=3000):
        """显示错误消息"""
        InfoBar.error(
            title=title,
            content=content,
            parent=self,
            position=InfoBarPosition.TOP,
            duration=duration
        ) 