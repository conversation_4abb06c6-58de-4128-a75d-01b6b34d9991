#!/usr/bin/python3
# -*- coding: utf-8 -*-
import asyncio
from PySide6.QtCore import QObject, Signal

class TelegramAccountService(QObject):
    """负责Telegram客户端的账号信息获取和连接管理"""
    
    # 定义信号
    connection_status_changed = Signal(str, bool)  # 连接状态变化信号 (phone, is_connected)
    update_profile_result = Signal(str, bool, str)  # 资料更新结果信号 (phone, success, message)
    
    def __init__(self, main_service, logger):
        super().__init__()  # 必须调用父类初始化
        self.main_service = main_service
        self.logger = logger
        # 保存客户端状态信息
        self.client_status = {}
        # 周期性检查连接状态的任务
        self.monitor_task = None
    
    async def get_me(self, phone):
        """获取当前登录用户信息"""
        client = self.main_service.clients.get(phone)
        if not client:
            return None, "客户端不存在"
        if not client.is_connected():
            return None, "客户端未连接"
        try:
            if await client.is_user_authorized():
                user = await client.get_me()
                return user, "获取成功"
            else:
                return None, "用户未授权"
        except Exception as e:
            return None, f"获取用户信息失败: {str(e)}"
    
    async def get_info(self, phone):
        """获取用户信息，uid，username，nickname等用户信息"""
        client = self.main_service.clients.get(phone)
        self.logger.info(f"客户端列表: {self.main_service.clients}")
        if not client:
            return None, "客户端不存在"
        if not client.is_connected():
            return None, "客户端未连接"
        try:
            if await client.is_user_authorized():       # 如果已授权
                me = await client.get_me()              # 获取用户信息
                return me, "获取成功"
            else:
                return None, "用户未授权"
        except Exception as e:
            # 检查是否是会话撤销错误
            if "SessionRevokedError" in str(e) or "session was revoked" in str(e).lower():
                # 从客户端字典中移除此客户端
                if phone in self.main_service.clients:
                    await client.disconnect()
                    self.main_service.clients.pop(phone, None)
                self.logger.warning(f"账号 {phone} 的会话已被撤销，需要重新登录")
                return None, "账号会话已被撤销，请重新登录"
            
            self.logger.error(f"获取用户信息失败: {str(e)}")
            return None, f"获取用户信息失败: {str(e)}"
    
    def get_logged_in_accounts(self):
        """获取已登录的账号列表(同步版本)
        
        Returns:
            list: 已登录的电话号码列表
        """
        logged_in_accounts = []
        for phone, client in self.main_service.clients.items():
            if client.is_connected():
                # 避免使用run_until_complete，改为检查连接状态
                # 直接返回已连接的客户端，不进行授权检查
                logged_in_accounts.append(phone)
        
        self.logger.info(f"找到 {len(logged_in_accounts)} 个已连接账号")
        return logged_in_accounts
    
    async def get_logged_in_accounts_async(self):
        """获取已登录的账号列表(异步版本)
        
        Returns:
            list: 已登录的电话号码列表
        """
        logged_in_accounts = []
        for phone, client in self.main_service.clients.items():
            if client.is_connected():
                try:
                    if await client.is_user_authorized():
                        logged_in_accounts.append(phone)
                except Exception as e:
                    self.logger.error(f"检查账号 {phone} 授权状态时出错: {str(e)}")
        
        self.logger.info(f"找到 {len(logged_in_accounts)} 个已登录账号")
        return logged_in_accounts
    
    async def get_all_groups_async(self, phone):
        """异步获取指定账号的所有群组
        
        Args:
            phone: 手机号码
            
        Returns:
            list: 群组列表
        """
        client = self.main_service.clients.get(phone)
        if not client or not client.is_connected():
            return []
            
        try:
            if await client.is_user_authorized():
                dialogs = await client.get_dialogs()
                # 筛选出群组类型的对话
                groups = [d for d in dialogs if d.is_group]
                return groups
            return []
        except Exception as e:
            self.logger.error(f"获取群组失败: {str(e)}")
            return []
    
    async def get_all_groups_and_channels_async(self, phone):
        """异步获取指定账号的所有群组和频道
        
        Args:
            phone: 手机号码
            
        Returns:
            tuple: (groups_and_channels, message)
                groups_and_channels: 包含两个列表的字典 {'groups': [], 'channels': []}
                message: 状态消息
        """
        client = self.main_service.clients.get(phone)
        if not client:
            return {'groups': [], 'channels': []}, "客户端不存在"
        if not client.is_connected():
            return {'groups': [], 'channels': []}, "客户端未连接"
        
        try:
            if not await client.is_user_authorized():
                return {'groups': [], 'channels': []}, "用户未授权"
                
            # 获取所有对话，包括群组、频道等
            dialogs = await client.get_dialogs()
            
            # 过滤出群组和频道
            groups = []
            channels = []
            
            for dialog in dialogs:
                if dialog.is_group:
                    group_info = {
                        'id': str(dialog.id),
                        'title': dialog.title,
                        'members_count': dialog.entity.participants_count if hasattr(dialog.entity, 'participants_count') else 0
                    }
                    groups.append(group_info)
                elif dialog.is_channel:
                    channel_info = {
                        'id': str(dialog.id),
                        'title': dialog.title,
                        'members_count': dialog.entity.participants_count if hasattr(dialog.entity, 'participants_count') else 0,
                        'username': dialog.entity.username if hasattr(dialog.entity, 'username') else None
                    }
                    channels.append(channel_info)
            
            result = {
                'groups': groups,
                'channels': channels
            }
            
            self.logger.info(f"账号 {phone} 获取到 {len(groups)} 个群组和 {len(channels)} 个频道")
            return result, "获取成功"
            
        except Exception as e:
            error_msg = f"获取群组和频道失败: {str(e)}"
            self.logger.error(error_msg)
            return {'groups': [], 'channels': []}, error_msg
    
    async def update_user_profile(self, phone, profile_data):
        """更新用户个人资料
        
        Args:
            phone: 账户电话号码
            profile_data: 包含用户资料的字典，可能包含以下字段：
                - first_name: 名
                - last_name: 姓
                - username: 用户名（Telegram可能不允许直接修改）
                - bio: 个人简介
                - avatar: 头像文件路径
                
        Returns:
            tuple: (success, message)
        """
        client = self.main_service.clients.get(phone)
        if not client:
            return False, "客户端不存在"
        
        if not client.is_connected():
            try:
                await client.connect()
            except Exception as e:
                return False, f"连接失败: {str(e)}"
        
        try:
            if not await client.is_user_authorized():
                return False, "用户未授权"
            
            # 更新个人信息
            update_info = {}
            
            # 只添加有提供的字段
            if 'first_name' in profile_data and profile_data['first_name']:
                update_info['first_name'] = profile_data['first_name']
            
            if 'last_name' in profile_data and profile_data['last_name'] is not None:
                update_info['last_name'] = profile_data['last_name']
            
            if 'about' in profile_data or 'bio' in profile_data:
                update_info['about'] = profile_data.get('about', '') or profile_data.get('bio', '')
            
            # 如果有需要更新的信息
            if update_info:
                # 使用正确的Telethon API方法更新个人资料
                from telethon import functions
                await client(functions.account.UpdateProfileRequest(**update_info))
                self.logger.info(f"已更新用户 {phone} 的资料信息")
            
            # 更新头像（如果提供）
            if 'avatar_path' in profile_data and profile_data['avatar_path']:
                try:
                    avatar_path = profile_data['avatar_path']
                    # 使用正确的Telethon API方法上传头像
                    from telethon.tl.functions.photos import UploadProfilePhotoRequest
                    await client(UploadProfilePhotoRequest(
                        await client.upload_file(avatar_path)
                    ))
                    self.logger.info(f"已更新用户 {phone} 的头像")
                except Exception as e:
                    self.logger.error(f"更新头像失败: {str(e)}")
                    return False, f"更新头像失败: {str(e)}"
            
            return True, "用户资料更新成功"
            
        except Exception as e:
            self.logger.error(f"更新用户资料失败: {str(e)}")
            return False, f"更新用户资料失败: {str(e)}"
    
    async def start_connection_monitor(self, check_interval=60):
        """启动连接状态监控
        
        Args:
            check_interval: 检查间隔(秒)
        """
        if self.monitor_task:
            self.monitor_task.cancel()
            
        self.monitor_task = asyncio.create_task(self._monitor_connections(check_interval))
        self.logger.info(f"已启动连接状态监控，检查间隔: {check_interval}秒")
    
    async def _monitor_connections(self, interval):
        """监控所有客户端的连接状态
        
        Args:
            interval: 检查间隔(秒)
        """
        try:
            while True:
                await self._check_all_connections()
                await asyncio.sleep(interval)
        except asyncio.CancelledError:
            self.logger.info("连接状态监控已停止")
        except Exception as e:
            self.logger.error(f"连接状态监控出错: {str(e)}")
    
    async def _check_all_connections(self):
        """检查所有客户端的连接状态"""
        for phone, client in list(self.main_service.clients.items()):
            # 获取之前的连接状态
            prev_status = self.client_status.get(phone, {
                'connected': False,
                'authorized': False
            })
            
            current_status = {
                'connected': client.is_connected(),
                'authorized': False
            }
            
            # 如果连接中，检查是否已授权
            if current_status['connected']:
                try:
                    current_status['authorized'] = await client.is_user_authorized()
                except Exception as e:
                    current_status['connected'] = False
                    # 检查是否是会话撤销错误
                    if "SessionRevokedError" in str(e) or "session was revoked" in str(e).lower():
                        self.logger.warning(f"账号 {phone} 的会话已被撤销，将从客户端列表中移除")
                        await client.disconnect()
                        self.main_service.clients.pop(phone, None)
                        self.connection_status_changed.emit(phone, False)
                        continue
                    self.logger.error(f"检查客户端 {phone} 授权状态出错: {str(e)}")
                    
            # 如果连接状态发生变化，发出信号
            if prev_status['connected'] != current_status['connected']:
                self.connection_status_changed.emit(phone, current_status['connected'])
                
            # 如果连接断开，尝试重新连接
            if not current_status['connected'] and prev_status['connected']:
                self.logger.warning(f"客户端 {phone} 连接已断开，尝试重新连接")
                try:
                    await client.connect()
                    # 更新连接状态
                    current_status['connected'] = client.is_connected()
                    if current_status['connected']:
                        self.logger.info(f"客户端 {phone} 重新连接成功")
                        try:
                            current_status['authorized'] = await client.is_user_authorized()
                        except Exception as e:
                            if "SessionRevokedError" in str(e) or "session was revoked" in str(e).lower():
                                self.logger.warning(f"重连后发现账号 {phone} 的会话已被撤销，将从客户端列表中移除")
                                await client.disconnect()
                                self.main_service.clients.pop(phone, None)
                                self.connection_status_changed.emit(phone, False)
                                continue
                            self.logger.error(f"重连后检查客户端 {phone} 授权状态出错: {str(e)}")
                    else:
                        self.logger.error(f"客户端 {phone} 重新连接失败")
                except Exception as e:
                    self.logger.error(f"重新连接客户端 {phone} 出错: {str(e)}")
            
            # 更新状态记录
            self.client_status[phone] = current_status 