#!/usr/bin/python3
# -*- coding: utf-8 -*-
from PySide6.QtCore import QObject, Signal
from telethon.errors import SessionPasswordNeededError, PhoneCodeInvalidError, PhoneCodeExpiredError

class TelegramAuthService(QObject):
    """负责Telegram客户端的登录和验证"""
    
    # 定义信号
    login_code_required = Signal(str)  # 需要验证码 (phone)
    password_required = Signal(str)  # 需要两步验证密码 (phone)
    login_success = Signal(str, object)  # 登录成功 (phone, user)
    login_failed = Signal(str, str)  # 登录失败 (phone, error)
    
    def __init__(self, main_service, logger):
        super().__init__()
        self.main_service = main_service
        self.logger = logger
        self.phone_code_hash = None
    
    async def send_code(self, phone):
        """发送验证码"""
        client = self.main_service.clients.get(phone)
        if not client:
            return False, "客户端不存在"
         
        try:
            if not await client.is_user_authorized():
                hash = await client.send_code_request(phone)                
                self.phone_code_hash = hash.phone_code_hash
                self.login_code_required.emit(phone)
                return True, "验证码已发送"
            return False, "用户已登录"
        except Exception as e:
            return False, str(e)
    
    async def login(self, phone, code, password=None):
        """登录验证
        
        Args:
            phone: 手机号码
            code: 验证码
            password: 两步验证密码(可选)
            
        Returns:
            (bool, Union[User, str]): (是否成功, 用户信息或错误消息)
        """
        client = self.main_service.clients.get(phone)
        if not client:
            return False, "客户端不存在"

        try:
            try:
                # 使用验证码登录
                await client.sign_in(phone, code, phone_code_hash=self.phone_code_hash)
                user = await client.get_me()
                return True, user
                
            except SessionPasswordNeededError:
                # 需要两步验证密码
                if not password:
                    return False, "需要两步验证密码"
                    
                # 使用密码登录    
                await client.sign_in(password=password)
                user = await client.get_me()
                return True, user
                
            except PhoneCodeInvalidError:
                return False, "验证码无效"
                
            except PhoneCodeExpiredError:
                return False, "验证码已过期"
                
        except Exception as e:
            return False, f"登录失败: {str(e)}" 