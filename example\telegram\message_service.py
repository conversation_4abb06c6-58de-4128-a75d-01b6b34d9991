#!/usr/bin/python3
# -*- coding: utf-8 -*-
from telethon.tl.functions.messages import GetDialogsRequest
from telethon.tl.types import InputPeerEmpty, InputMessagesFilterPhotos, InputMessagesFilterVideo

class TelegramMessageService:
    """负责Telegram客户端的消息和媒体操作"""
    
    def __init__(self, main_service, logger):
        self.main_service = main_service
        self.logger = logger
    
    async def get_dialogs(self, phone, limit=100):
        """获取所有对话"""
        client = self.main_service.clients.get(phone)
        if not client:
            return []
        return await client.get_dialogs(limit=limit)
    
    async def get_channel_messages(self, phone, channel, limit=100):
        """获取频道消息"""
        client = self.main_service.clients.get(phone)
        if not client:
            return []
        return await client.get_messages(channel, limit=limit)
    
    async def get_chat_history(self, phone, chat_id, limit=100):
        """获取私聊消息"""
        client = self.main_service.clients.get(phone)
        if not client:
            return []
        return await client.get_messages(chat_id, limit=limit)
    
    async def download_media(self, phone, message, file=None):
        """下载消息中的媒体文件"""
        client = self.main_service.clients.get(phone)
        if not client or not message.media:
            return False
        try:
            await client.download_media(message, file=file)
            return True
        except Exception:
            return False
    
    async def get_media_messages(self, phone, chat, media_filter, limit=100):
        """获取指定类型的媒体消息"""
        client = self.main_service.clients.get(phone)
        if not client:
            return []
        return await client.get_messages(
            chat,
            limit=limit,
            filter=media_filter
        )
    
    async def get_photos(self, phone, chat, limit=100):
        """获取图片消息"""
        return await self.get_media_messages(
            phone, 
            chat,
            InputMessagesFilterPhotos,
            limit
        )
    
    async def get_videos(self, phone, chat, limit=100):
        """获取视频消息"""
        return await self.get_media_messages(
            phone,
            chat,
            InputMessagesFilterVideo,
            limit
        ) 