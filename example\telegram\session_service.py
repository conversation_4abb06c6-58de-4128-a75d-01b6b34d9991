#!/usr/bin/python3
# -*- coding: utf-8 -*-
from telethon import TelegramClient
from app.common.setting import SESSION_DIR, API_ID, API_HASH
from app.utils.get_proxy import GetProxy
import os

class TelegramSessionService:
    """负责Telegram客户端会话的管理"""
    
    def __init__(self, main_service, logger):
        self.main_service = main_service  # 引用主服务
        self.logger = logger
    
    async def create_client(self, phone, api_id=None, api_hash=None, proxy=None):
        """创建新的客户端"""
        if phone in self.main_service.clients:
            return False, "该用户已登录"
            
        if not api_id or not api_hash:
            api_id = API_ID
            api_hash = API_HASH
            
        try:
            # 移除手机号中的'+'字符，避免在文件路径中出现
            safe_phone = phone.replace('+', '')
            session_path = SESSION_DIR / f"{safe_phone}.session"
            self.logger.info(f"创建客户端: {phone}, 会话路径: {session_path}")
            
            # 记录详细的代理配置信息
            if proxy:
                if isinstance(proxy, dict):
                    proxy_type = proxy.get('type', 'unknown')
                    if proxy_type == 'ip_pool':
                        self.logger.info(f"使用IP池代理: {proxy.get('ip')}:{proxy.get('port')}")
                    else:
                        self.logger.info(f"使用{proxy_type}代理: {proxy.get('ip')}:{proxy.get('port')}")
                else:
                    self.logger.info(f"使用代理配置: {proxy}")
            else:
                self.logger.info("未使用代理")
            
            # 获取代理配置
            proxy_config = self.get_proxy(proxy=proxy)
            if proxy_config:
                self.logger.info(f"解析后的代理配置: {proxy_config}")
            
            # client.connect() 和 client.start() 的主要区别:
            # 1. connect() 只建立与 Telegram 服务器的连接,不会进行登录验证
            # 2. start() 会先调用 connect(),然后自动处理登录流程(如果未登录)
            # 3. 这里使用 connect() 因为我们想手动控制登录流程,而不是自动登录
            client = TelegramClient(str(session_path), api_id, api_hash, proxy=proxy_config)
            
            # 设置连接超时回调
            client._sender._connect_timeout = 10  # 设置连接超时时间为10秒
            client._sender._connect_retries = 6   # 设置最大重试次数为6次
            
            # 初始化连接尝试次数
            self.main_service.connection_attempts[phone] = 0
            
            # 只建立连接,不进行登录
            await client.connect()
            self.main_service.clients[phone] = client
            self.logger.info(f"成功创建客户端{phone}")
            return True, "客户端创建成功"
        except Exception as e:
            self.logger.error(f"{phone}，客户端创建失败，失败原因{e}")
            if isinstance(e, TimeoutError):
                self.main_service.connection_attempts[phone] = self.main_service.connection_attempts.get(phone, 0) + 1
                self.main_service.connection_timeout.emit(phone, self.main_service.connection_attempts[phone])
            return False, str(e)
            
    async def import_session(self, session_path, phone, api_id=None, api_hash=None, proxy=None):
        """使用现有session文件创建客户端
        
        Args:
            session_path: 导入的session文件路径
            phone: 手机号码
            api_id: API ID (可选)
            api_hash: API Hash (可选)
            proxy: 代理设置 (可选)
            
        Returns:
            (bool, str): (是否成功, 消息)
        """
        if phone in self.main_service.clients:
            return False, "该用户已登录"
            
        if not api_id or not api_hash:
            api_id = API_ID
            api_hash = API_HASH
            
        try:
            # 确保session_path是字符串
            session_path_str = str(session_path)
            # 移除手机号中的'+'字符，避免在文件路径中出现
            safe_phone = phone.replace('+', '')
            
            # 直接使用字符串路径创建客户端，避免SQLiteSession对象问题
            self.logger.info(f"使用session文件路径: {session_path_str}")
            self.logger.info(f"手机号: {phone}")
            
            # 记录详细的代理配置信息
            if proxy:
                if isinstance(proxy, dict):
                    proxy_type = proxy.get('type', 'unknown')
                    if proxy_type == 'ip_pool':
                        self.logger.info(f"使用IP池代理: {proxy.get('ip')}:{proxy.get('port')}")
                    else:
                        self.logger.info(f"使用{proxy_type}代理: {proxy.get('ip')}:{proxy.get('port')}")
                else:
                    self.logger.info(f"使用代理配置: {proxy}")
            else:
                self.logger.info("未使用代理")
            
            # 获取代理配置
            proxy_config = self.get_proxy(proxy=proxy)
            if proxy_config:
                self.logger.info(f"解析后的代理配置: {proxy_config}")
            
            # 创建新的客户端实例
            client = TelegramClient(str(session_path_str), api_id, api_hash, proxy=proxy_config)
            
            # 设置连接超时回调
            client._sender._connect_timeout = 10  # 设置连接超时时间为10秒
            client._sender._connect_retries = 6   # 设置最大重试次数为6次
            
            # 初始化连接尝试次数
            self.main_service.connection_attempts[phone] = 0
            
            # 使用 connect() 而不是 start() 来手动控制连接过程
            await client.connect()
            self.main_service.clients[phone] = client
            if  client.is_connected():
                try:
                    is_authorized = await client.is_user_authorized()
                    # me = await client.get_me()
                    
                    # # 记录用户信息到日志而不是print
                    # if me:
                    #     self.logger.info(f"用户信息 - ID: {me.id}, 名称: {me.first_name} {me.last_name}, 用户名: {me.username}")
                    
                    # 只有在授权成功时保存客户端
                    if is_authorized:
                        self.main_service.clients[phone] = client
                        self.logger.info(f"客户端{phone}连接成功且已授权")
                        return True, "客户端导入并登录成功"
                    else:
                        await client.disconnect()
                        self.logger.error(f"{phone} session未授权")
                        return False, "session未授权，请重新登录"
                except Exception as e:
                    self.logger.error(f"{phone}读取认证信息失败: {str(e)}")
                    await client.disconnect()
                    return False, f"读取认证信息失败: {str(e)}"
            else:
                await client.disconnect()
                self.logger.error(f"{phone} session无效或已过期")
                return False, "session无效或已过期"
                
        except Exception as e:
            self.logger.error(f"{phone}，客户端导入失败: {str(e)}")
            if isinstance(e, TimeoutError):
                self.main_service.connection_attempts[phone] = self.main_service.connection_attempts.get(phone, 0) + 1
                self.main_service.connection_timeout.emit(phone, self.main_service.connection_attempts[phone])
            return False, str(e)
            
    def get_proxy(self, proxy):
        """获取代理配置"""
        if not proxy:
            self.logger.info("未配置代理，返回None")
            return None
            
        if isinstance(proxy, dict):
            proxy_type = proxy.get('type', 'disable')
            self.logger.info(f"配置代理类型: {proxy_type}, IP: {proxy.get('ip')}, 端口: {proxy.get('port')}")
            
            # 使用新的适配方法创建代理对象
            proxy_config = GetProxy.from_dict(proxy).get_proxy()
            
            if proxy_config:
                self.logger.info(f"成功创建代理配置: {proxy_config}")
            else:
                self.logger.warning(f"代理配置失败，返回None")
                
            return proxy_config
            
        elif proxy == "system":
            self.logger.info("使用系统代理")
            proxy = GetProxy(type="system")
            system_proxy = proxy.get_proxy()
            
            if system_proxy:
                self.logger.info(f"系统代理配置: {system_proxy}")
            else:
                self.logger.warning("无法获取系统代理配置，返回None")
                
            return system_proxy
            
        self.logger.warning(f"未知代理配置: {proxy}，返回None")
        return None
        
    async def disconnect_client(self, phone):
        """断开指定客户端连接"""
        if phone in self.main_service.clients:
            await self.main_service.clients[phone].disconnect()
            del self.main_service.clients[phone]
            return True, "已断开连接"
        return False, "客户端不存在"
        
    async def check_authorized(self, phone):
        """检查账户是否已登录
        
        Args:
            phone: 手机号码
            
        Returns:
            (bool, str): (是否已登录, 消息)
        """
        self.logger.info(f"检查账户是否已登录: {phone}")
        #self.logger.info(f"客户端: {self.main_service.clients}")
        try:
            client = self.main_service.clients.get(phone)
            if not client:
                return False, "客户端不存在"
            
            if await client.is_user_authorized():
                return True, "已登录"
            else:
                return False, "未登录"
                
        except Exception as e:
            self.logger.error(f"检查登录状态失败: {str(e)}")
            return False, f"检查登录状态失败: {str(e)}" 