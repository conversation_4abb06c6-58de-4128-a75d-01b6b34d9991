#!/usr/bin/python3
# -*- coding: utf-8 -*-
from PySide6.QtCore import QObject, Signal
from app.services.telegram.auth_service import TelegramAuthService
from app.services.telegram.session_service import TelegramSessionService
from app.services.telegram.message_service import TelegramMessageService
from app.services.telegram.account_service import TelegramAccountService

class TelegramService(QObject):
    """Telegram服务主类，负责协调其他子服务"""
    
    # 信号定义
    login_code_required = Signal(str)  # 需要验证码 (phone)
    password_required = Signal(str)  # 需要两步验证密码 (phone)
    login_success = Signal(str, object)  # 登录成功 (phone, user)
    login_failed = Signal(str, str)  # 登录失败 (phone, error)
    new_message = Signal(str, object)  # 新消息 (phone, message)
    connection_changed = Signal(str, bool)  # 连接状态变化 (phone, connected)
    connection_timeout = Signal(str, int)  # 连接超时 (phone, attempt)

    def __init__(self, logger):
        super().__init__()
        self.logger = logger
        self.clients = {}
        self.connection_attempts = {}  # 记录每个手机号的连接尝试次数
        
        # 初始化子服务
        self.session_service = TelegramSessionService(self, logger)
        self.auth_service = TelegramAuthService(self, logger)
        self.message_service = TelegramMessageService(self, logger)
        self.account_service = TelegramAccountService(self, logger)
        
        # 将主服务的信号连接到身份验证服务
        self.auth_service.login_code_required.connect(self.login_code_required)
        self.auth_service.password_required.connect(self.password_required)
        self.auth_service.login_success.connect(self.login_success)
        self.auth_service.login_failed.connect(self.login_failed)
        
        # 连接账户服务的信号
        self.account_service.connection_status_changed.connect(self.connection_changed)
    
    # 客户端管理方法（转发到会话服务）
    async def create_client(self, phone, api_id=None, api_hash=None, proxy=None):
        """创建新的客户端"""
        return await self.session_service.create_client(phone, api_id, api_hash, proxy)
        
    async def import_session(self, session_path, phone, api_id=None, api_hash=None, proxy=None):
        """使用现有session文件创建客户端"""
        return await self.session_service.import_session(session_path, phone, api_id, api_hash, proxy)
    
    async def batch_import_sessions(self, sessions_data):
        """批量导入多个会话文件，支持多账户同时登录
        
        Args:
            sessions_data: 会话数据列表，每项包含session_path、phone等信息
                
        Returns:
            dict: 每个账号的导入结果
        """
        return await self.session_service.batch_import_sessions(sessions_data)
    
    async def disconnect_client(self, phone):
        """断开指定客户端连接"""
        return await self.session_service.disconnect_client(phone)
    
    async def check_authorized(self, phone):
        """检查账户是否已登录"""
        return await self.session_service.check_authorized(phone)
    
    # 登录验证方法（转发到身份验证服务）
    async def send_code(self, phone):
        """发送验证码"""
        return await self.auth_service.send_code(phone)
    
    async def login(self, phone, code, password=None):
        """登录验证"""
        return await self.auth_service.login(phone, code, password)
    
    # 消息获取方法（转发到消息服务）
    async def get_dialogs(self, phone, limit=100):
        """获取所有对话"""
        return await self.message_service.get_dialogs(phone, limit)
    
    async def get_channel_messages(self, phone, channel, limit=100):
        """获取频道消息"""
        return await self.message_service.get_channel_messages(phone, channel, limit)
    
    async def get_chat_history(self, phone, chat_id, limit=100):
        """获取私聊消息"""
        return await self.message_service.get_chat_history(phone, chat_id, limit)
    
    # 媒体下载方法（转发到消息服务）
    async def download_media(self, phone, message, file=None):
        """下载消息中的媒体文件"""
        return await self.message_service.download_media(phone, message, file)
    
    async def get_media_messages(self, phone, chat, media_filter, limit=100):
        """获取指定类型的媒体消息"""
        return await self.message_service.get_media_messages(phone, chat, media_filter, limit)
    
    async def get_photos(self, phone, chat, limit=100):
        """获取图片消息"""
        return await self.message_service.get_photos(phone, chat, limit)
    
    async def get_videos(self, phone, chat, limit=100):
        """获取视频消息"""
        return await self.message_service.get_videos(phone, chat, limit)
    
    # 账号信息方法（转发到账号信息服务）
    async def get_me(self, phone):
        """获取当前登录用户信息"""
        return await self.account_service.get_me(phone)
    
    async def get_info(self, phone):
        """获取用户信息，uid，username，nickname等用户信息"""
        return await self.account_service.get_info(phone)
    
    def get_logged_in_accounts(self):
        """获取已登录的账号列表"""
        return self.account_service.get_logged_in_accounts()
    
    async def get_logged_in_accounts_async(self):
        """获取已登录的账号列表(异步版本)"""
        return await self.account_service.get_logged_in_accounts_async()
    
    async def get_all_groups_async(self, phone):
        """异步获取指定账号的所有群组"""
        return await self.account_service.get_all_groups_async(phone)
    
    async def get_all_groups_and_channels_async(self, phone):
        """异步获取指定账号的所有群组和频道
        
        Args:
            phone: 手机号码
            
        Returns:
            tuple: (groups_and_channels, message)
                groups_and_channels: 包含两个列表的字典 {'groups': [], 'channels': []}
                message: 状态消息
        """
        return await self.account_service.get_all_groups_and_channels_async(phone)
    
    # 连接池管理方法
    async def start_connection_monitor(self, check_interval=60):
        """启动连接状态监控"""
        await self.account_service.start_connection_monitor(check_interval)
    
    def get_client(self, phone):
        """获取指定电话号码的客户端"""
        return self.clients.get(phone)
    
    def get_all_clients(self):
        """获取所有客户端"""
        return self.clients
    
    def get_client_count(self):
        """获取客户端数量"""
        return len(self.clients)
    
    async def get_connected_client_count(self):
        """获取已连接的客户端数量"""
        count = 0
        for client in self.clients.values():
            if client.is_connected():
                count += 1
        return count
    
    async def update_user_profile(self, phone, profile_data):
        """更新用户个人资料
        
        Args:
            phone: 账户电话号码
            profile_data: 包含用户资料的字典，可能包含以下字段：
                - first_name: 名
                - last_name: 姓
                - username: 用户名（Telegram可能不允许直接修改）
                - bio: 个人简介
                - avatar: 头像文件路径
                
        Returns:
            tuple: (success, message)
        """
        return await self.account_service.update_user_profile(phone, profile_data)
        
    # 消息监控相关方法
    async def get_group_members(self, phone, group_id, limit=1000):
        """获取群组成员
        
        Args:
            phone: 账户电话号码
            group_id: 群组ID或用户名
            limit: 最大获取数量
            
        Returns:
            list: 用户列表
        """
        try:
            client = self.get_client(phone)
            if not client:
                self.logger.error(f"获取群组成员失败: 账号 {phone} 未登录")
                return []
            
            # 获取群组实体
            try:
                entity = await client.get_entity(group_id)
            except Exception as e:
                self.logger.error(f"获取群组 {group_id} 实体失败: {str(e)}")
                return []
            
            # 获取群组成员
            members = []
            try:
                async for member in client.iter_participants(entity, limit=limit):
                    members.append(member)
            except Exception as e:
                self.logger.error(f"获取群组 {group_id} 成员失败: {str(e)}")
                return []
            
            self.logger.info(f"成功获取群组 {group_id} 的 {len(members)} 名成员")
            return members
            
        except Exception as e:
            self.logger.error(f"获取群组成员时发生错误: {str(e)}")
            return []
    
    async def search_messages(self, phone, chat_id, query, limit=100):
        """在指定对话中搜索消息
        
        Args:
            phone: 账户电话号码
            chat_id: 对话ID或用户名
            query: 搜索关键词
            limit: 最大获取数量
            
        Returns:
            list: 消息列表
        """
        try:
            client = self.get_client(phone)
            if not client:
                self.logger.error(f"搜索消息失败: 账号 {phone} 未登录")
                return []
            
            # 获取对话实体
            try:
                entity = await client.get_entity(chat_id)
            except Exception as e:
                self.logger.error(f"获取对话 {chat_id} 实体失败: {str(e)}")
                return []
            
            # 搜索消息
            messages = []
            try:
                async for message in client.iter_messages(entity, search=query, limit=limit):
                    messages.append(message)
            except Exception as e:
                self.logger.error(f"在对话 {chat_id} 中搜索关键词 {query} 失败: {str(e)}")
                return []
            
            self.logger.info(f"在对话 {chat_id} 中成功搜索到 {len(messages)} 条包含 '{query}' 的消息")
            return messages
            
        except Exception as e:
            self.logger.error(f"搜索消息时发生错误: {str(e)}")
            return []
    
    async def get_user_info(self, phone, user_id):
        """获取用户信息
        
        Args:
            phone: 账户电话号码
            user_id: 用户ID或用户名
            
        Returns:
            User: 用户对象，如果找不到则返回None
        """
        try:
            client = self.get_client(phone)
            if not client:
                self.logger.error(f"获取用户信息失败: 账号 {phone} 未登录")
                return None
            
            # 获取用户实体
            try:
                user = await client.get_entity(user_id)
                return user
            except Exception as e:
                self.logger.error(f"获取用户 {user_id} 信息失败: {str(e)}")
                return None
            
        except Exception as e:
            self.logger.error(f"获取用户信息时发生错误: {str(e)}")
            return None 