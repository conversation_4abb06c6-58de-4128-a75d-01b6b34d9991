#!/usr/bin/python3
# -*- coding: utf-8 -*-
import asyncio
import os
from telethon.sync import TelegramClient
from telethon.errors.rpcerrorlist import PhoneNumberInvalidError

# --- 步骤 1: 请在这里填写您的信息 ---

# 请确保 api_id 是一个纯数字，没有任何引号。
# 错误示例: '1234567'
# 正确示例: 1234567
API_ID = 24297563  # 替换为您的 API ID

# 请确保 api_hash 是一个被引号包围的字符串。
API_HASH = '79354bc5da59358b3f268c7ecb1ce332'  # 替换为您的 API Hash

# 请确保电话号码是字符串格式，并建议使用国际区号（以'+'开头）。
PHONE_NUMBER = '+888 0719 4657'  # 替换为您的手机号, 例如: '+8612345678900'

# Session 文件的名字，您可以自定义。
SESSION_NAME = '+888 0719 4657'


# --- 核心排查逻辑 ---

async def main():
    """
    一个用于诊断 Telethon 登录问题的独立脚本。
    """
    print("--- 开始 Telethon 登录排查 ---")
    
    # --- 步骤 2: 强制类型转换与验证 ---
    # 这是解决问题的关键。即使您认为类型正确，我们也要在这里进行强制转换和验证。
    
    try:
        # 验证并转换 api_id 为整数 (int)
        api_id_int = int(API_ID)
    except (ValueError, TypeError):
        print("\n[!!!] 关键错误：API_ID 无法转换为整数！")
        print(f"      您提供的 API_ID 是: '{API_ID}' (类型: {type(API_ID)})")
        print("      请确保 API_ID 是一串纯数字，不要加任何引号。")
        return
    
    # 验证并转换 api_hash 为字符串 (str)
    api_hash_str = str(API_HASH)
    
    # 验证并转换 phone_number 为字符串 (str)
    phone_str = str(PHONE_NUMBER)
    
    # --- 步骤 3: 打印运行时变量的真实类型 ---
    # 观察这里的输出，这是判断问题所在的“铁证”。
    print("\n[信息] 检查即将使用的变量类型...")
    print(f"      Session 文件名: {SESSION_NAME}.session")
    print(f"      API ID: {api_id_int} (类型: {type(api_id_int)})")
    print(f"      API Hash: '{api_hash_str}' (类型: {type(api_hash_str)})")
    print(f"      手机号码: '{phone_str}' (类型: {type(phone_str)})")
    
    # 期望的输出:
    #      API ID: ... (类型: <class 'int'>)
    #      API Hash: '...' (类型: <class 'str'>)
    #      手机号码: '...' (类型: <class 'str'>)
    
    if not isinstance(api_id_int, int):
        print("\n[!!!] 运行时类型错误：api_id 不是整数！排查中止。")
        return
    if not isinstance(api_hash_str, str):
        print("\n[!!!] 运行时类型错误：api_hash 不是字符串！排查中止。")
        return
    if not isinstance(phone_str, str):
        print("\n[!!!] 运行时类型错误：phone 不是字符串！排查中止。")
        return
    
    print("\n[成功] 所有变量类型检查通过！")
    
    # 初始化 TelegramClient
    # 我们将客户端初始化放在 try...except 块中，以便捕获任何潜在错误
    proxy=('socks5',"127.0.0.1",1080)
    client = TelegramClient(SESSION_NAME, api_id_int, api_hash_str,proxy = proxy)
    print(client)
    try:
        print("\n[操作] 正在连接到 Telegram...")
        await client.connect()
        print("[成功] 连接成功！")
        
        if not await client.is_user_authorized():
            print("\n[操作] 用户未登录，准备发送验证码...")
            
            # --- 步骤 4: 调用 send_code_request ---
            # 这是最容易出错的地方。
            try:
                print(phone_str)
                print(type(phone_str))
                send_result = await client.send_code_request(phone_str)
                print("[成功] send_code_request 调用成功！")
                print(f"      Phone Code Hash: {send_result.phone_code_hash}")
                
                # 请求用户输入验证码和密码（如果设置了）
                code = input("      请输入您收到的 Telegram 验证码: ")
                password = input("      请输入您的两步验证密码 (如果未设置，请直接按回车): ")
                
                if password:
                    await client.sign_in(phone_str, code, password = password)
                else:
                    await client.sign_in(phone_str, code)
                
                print("\n[成功] 登录成功！")
            
            except TypeError as e:
                # 这是您遇到的核心错误
                print("\n[!!!] 捕获到核心错误: TypeError!")
                print(f"      错误信息: {e}")
                print("\n      [诊断建议]:")
                print("      1. 请再次确认上方打印的 API ID 类型是否为 <class 'int'>。这是最常见的原因。")
                print("      2. 请确认您在运行此脚本前，已经删除了旧的 .session 文件。")
                return  # 发生错误，中止脚本
            
            except PhoneNumberInvalidError:
                print("\n[!!!] 捕获到错误: 手机号码格式无效 (PhoneNumberInvalidError)")
                print("      [诊断建议]:")
                print("      1. 检查手机号码是否有拼写错误。")
                print("      2. 确保手机号使用了国际格式，例如中国的 `+86` 开头。")
                return
        
        else:
            print("\n[信息] 用户已经登录。")
        
        # 打印登录信息
        me = await client.get_me()
        print("\n--- 登录账号信息 ---")
        print(f"      用户ID: {me.id}")
        print(f"      名字: {me.first_name} {me.last_name or ''}")
        print(f"      用户名: @{me.username}")
        print("--------------------")
    
    except Exception as e:
        print(f"\n[!!!] 发生未知错误: {e}")
        print(f"      错误类型: {type(e)}")
    
    finally:
        if client.is_connected():
            print("\n[操作] 断开连接...")
            await client.disconnect()
            print("[成功] 已断开连接。")
        print("\n--- 排查结束 ---")


if __name__ == '__main__':
    # 确保在 Windows 上也能正常运行 asyncio
    if os.name == 'nt':
        asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())
    
    asyncio.run(main())
