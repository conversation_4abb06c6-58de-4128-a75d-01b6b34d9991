#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Telegram测试工具
提供对新的client模块的示例和测试
"""

import os
import sys
import asyncio
import argparse
from typing import List, Dict, Any, Optional

from utils.logger import get_logger
from tests.client.core.client_manager import TelegramClientManager
from tests.client.services.message_service import MessageService
from tests.client.services.group_service import GroupService

# 配置日志
logger = get_logger("test_telethon")

# Telegram API 配置
API_ID = 24297563
API_HASH = "79354bc5da59358b3f268c7ecb1ce332"

# 默认会话目录
DEFAULT_SESSION_DIR = r"H:\PyProject\TeleTest\APPDATA"

# 默认代理配置
DEFAULT_PROXY_CONFIG = {
    'type': 'socks5',
    'ip': '127.0.0.1',
    'port': 1080
}

async def run_telegram_client(
    session_path: str, 
    api_id: int = API_ID, 
    api_hash: str = API_HASH, 
    proxy: Optional[Dict[str, Any]] = DEFAULT_PROXY_CONFIG
):
    """运行Telegram客户端
    
    Args:
        session_path: 会话文件路径
        api_id: API ID
        api_hash: API Hash
        proxy: 代理配置
    """
    logger.info(f"初始化客户端: {session_path}")
    
    # 创建客户端管理器
    manager = TelegramClientManager(
        api_id=api_id,
        api_hash=api_hash,
        session_dir=os.path.dirname(session_path),
        proxy=proxy,
        auto_load=False  # 禁用自动加载会话
    )
    
    # 添加指定会话
    success, result = await manager.add_worker(session_path)
    
    if not success:
        logger.error(f"添加会话失败: {result}")
        return
    
    worker = result
    logger.info(f"会话已加载: {worker.user_info['username'] if worker.user_info else 'Unknown'}")
    
    # 创建消息服务和群组服务
    message_service = MessageService()
    group_service = GroupService()
    
    try:
        # 显示会话信息
        if worker.is_authorized:
            user_info = worker.user_info
            logger.info(f"已登录账号: {user_info['first_name']} {user_info['last_name']}")
            logger.info(f"用户名: @{user_info['username']}" if user_info['username'] else "无用户名")
            logger.info(f"用户ID: {user_info['id']}")
            
            # 示例：发送测试消息到自己
            success, result = await message_service.send_text_message(
                client=worker.client,
                chat_id="me",
                text="这是一条测试消息"
            )
            
            if success:
                logger.info(f"测试消息发送成功，消息ID: {result.id}")
            else:
                logger.error(f"测试消息发送失败: {result}")
                
            # 等待用户输入命令
            logger.info("客户端已启动，按 Ctrl+C 停止")
            
            # 保持运行直到用户中断
            while True:
                await asyncio.sleep(1)
                
        else:
            logger.error("会话未授权，请先登录账号")
            
    except KeyboardInterrupt:
        logger.info("用户中断，正在停止客户端")
    except Exception as e:
        logger.error(f"运行时出错: {e}")
    finally:
        # 停止客户端
        await worker.stop()
        logger.info("客户端已停止")

def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='Telegram客户端测试工具')
    
    # 会话参数
    parser.add_argument('--session', default=os.path.join(DEFAULT_SESSION_DIR, "+88807194657"), help='会话文件路径，不含.session后缀')
    
    # 代理参数
    parser.add_argument('--proxy-type', default='socks5', choices=['socks5', 'http', 'none'], help='代理类型')
    parser.add_argument('--proxy-ip', default='127.0.0.1', help='代理IP地址')
    parser.add_argument('--proxy-port', type=int, default=1080, help='代理端口')
    
    return parser.parse_args()

async def main():
    """主函数"""
    args = parse_args()
    
    # 配置代理
    proxy_config = None
    if args.proxy_type != 'none':
        proxy_config = {
            'type': args.proxy_type,
            'ip': args.proxy_ip,
            'port': args.proxy_port
        }
    
    # 运行客户端
    await run_telegram_client(
        session_path=args.session,
        proxy=proxy_config
    )

if __name__ == "__main__":
    asyncio.run(main())