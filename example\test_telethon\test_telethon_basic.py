#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Telethon基础功能测试
独立测试文件，不依赖项目中的其他模块
测试通过UID或用户名给陌生人发送消息，并获取用户信息
"""

import os
import sys
import asyncio
from typing import Dict, Any, Optional, Union, List
from telethon import TelegramClient
from telethon.tl.types import User, Channel, Chat, InputChannel, InputPeerChannel, InputPeerUser, InputPeerChat, \
    InputUser, PeerUser
from telethon.tl.functions.channels import InviteToChannelRequest
from telethon.tl.functions.messages import AddChatUserRequest, GetFullChatRequest
from telethon.errors import FloodWaitError, UserNotMutualContactError, UserPrivacyRestrictedError, UserIdInvalidError, ChatAdminRequiredError

# Telegram API 配置
API_ID = 24297563
API_HASH = "79354bc5da59358b3f268c7ecb1ce332"

# 会话目录和文件
SESSION_DIR = r"H:\PyProject\TeleTest\APPDATA"
#SESSION_FILE = "+88807194657"  # 已登录的会话文件名，不含.session后缀
SESSION_FILE = "+88806093192.session"  # 已登录的会话文件名，不含.session后缀

# 代理配置
PROXY = {
    'proxy_type': 'socks5',
    'addr': '127.0.0.1',
    'port': 1080,
    'rdns': True
}

async def get_my_info(client):
    """获取自己的所有信息
    
    Args:
        client: Telegram客户端
    
    Returns:
        包含用户信息的字典
    """
    try:
        # 获取自己的完整用户信息
        me = await client.get_me()
        print(me)
        print(type(me))
        return True
        # 构建包含所有可用信息的字典
        # user_info = {
        #     'id': me.id,
        #     'first_name': me.first_name,
        #     'last_name': me.last_name,
        #     'username': me.username,
        #     'phone': me.phone if hasattr(me, 'phone') else None,
        #     'status': me.status,
        #     'bot': me.bot,
        #     'verified': me.verified,
        #     'restricted': me.restricted,
        #     'scam': me.scam,
        #     'fake': getattr(me, 'fake', False),
        #     'premium': getattr(me, 'premium', False),
        #     'lang_code': getattr(me, 'lang_code', None),
        #     'access_hash': getattr(me, 'access_hash', None),
        #     'photo': bool(me.photo),
        #     'contact': getattr(me, 'contact', False),
        #     'mutual_contact': getattr(me, 'mutual_contact', False),
        #     'deleted': getattr(me, 'deleted', False),
        #     'bot_chat_history': getattr(me, 'bot_chat_history', False),
        #     'bot_nochats': getattr(me, 'bot_nochats', False),
        #     'bot_inline_geo': getattr(me, 'bot_inline_geo', False),
        #     'support': getattr(me, 'support', False),
        #     'min': getattr(me, 'min', False)
        # }
        
        # # 打印用户信息
        # print("当前登录用户信息:")
        # print(f"用户ID: {user_info['id']}")
        # print(f"名字: {user_info['first_name']} {user_info['last_name'] or ''}")
        # print(f"用户名: @{user_info['username']}" if user_info['username'] else "无用户名")
        # print(f"电话号码: {user_info['phone'] or '未知'}")
        # print(f"用户状态: {user_info['status']}")
        # print(f"是否为机器人: {'是' if user_info['bot'] else '否'}")
        # print(f"是否已验证: {'是' if user_info['verified'] else '否'}")
        # print(f"是否受限: {'是' if user_info['restricted'] else '否'}")
        # print(f"是否诈骗: {'是' if user_info['scam'] else '否'}")
        # print(f"是否假冒: {'是' if user_info['fake'] else '否'}")
        # print(f"是否高级用户: {'是' if user_info['premium'] else '否'}")
        # print(f"语言代码: {user_info['lang_code'] or '未知'}")
        # print(f"个人资料照片: {'有' if user_info['photo'] else '无'}")
        
        # return user_info
    
    except Exception as e:
        print(f"获取自己的信息时出错: {e}")
        return None

async def get_user_info(client, user_id_or_username):
    """获取用户信息
    
    Args:
        client: Telegram客户端
        user_id_or_username: 用户ID或用户名
    
    Returns:
        User对象或None
    """
    try:
        print(f"正在获取用户信息: {user_id_or_username}")
        user = await client.get_entity(user_id_or_username)
        if isinstance(user, User):
            return user
        else:
            print(f"获取的实体不是用户: {type(user)}")
            return None
    except Exception as e:
        print(f"获取用户信息出错: {e}")
        return None


async def send_message(client, user_id_or_username, message, parse_mode='html', max_retries=3, retry_delay=5):
    """发送消息
    
    Args:
        client: Telegram客户端
        user_id_or_username: 用户ID或用户名
        message: 消息内容
        parse_mode: 消息解析模式，默认为'html'
        max_retries: 最大重试次数
        retry_delay: 重试间隔（秒）
    
    Returns:
        发送的消息对象或None
    """
    retries = 0
    while retries < max_retries:
        try:
            print(f"正在向 {user_id_or_username} 发送消息: {message}")
            entity = await client.get_entity(user_id_or_username)
            input_entity = await client.get_input_entity(entity)
            sent_message = await client.send_message(input_entity, message, parse_mode=parse_mode)
            return sent_message
        except FloodWaitError as e:
            wait_time = e.seconds
            print(f"遇到限流，需要等待 {wait_time} 秒")
            await asyncio.sleep(wait_time)
            retries += 1
            if retries < max_retries:
                print(f"正在进行第 {retries + 1} 次重试...")
            continue
        except ValueError as e:
            print(f"直接发送消息失败: {e}")
            return None
        except Exception as e:
            print(f"发送消息时出错: {type(e).__name__}: {str(e)}")
            await asyncio.sleep(retry_delay)
            retries += 1
            if retries < max_retries:
                print(f"正在进行第 {retries + 1} 次重试...")
            continue
    
    print(f"达到最大重试次数 ({max_retries})，发送消息失败")
    return None

def print_user_info(user):
    """打印用户信息
    
    Args:
        user: User对象
    """
    if not user:
        print("无法获取用户信息")
        return
        
    print("用户信息:")
    print(f"用户ID: {user.id}")
    print(f"用户名: @{user.username}" if user.username else "无用户名")    
    print(f"名字: {user.first_name} {user.last_name or ''}")
    print(f"是否为机器人: {'是' if user.bot else '否'}")
    print(f"是否已验证: {'是' if user.verified else '否'}")
    print(f"是否受限: {'是' if user.restricted else '否'}")
    print(f"是否诈骗: {'是' if user.scam else '否'}")
    print(f"是否假冒: {'是' if getattr(user, 'fake', False) else '否'}")
    print(f"电话号码: {user.phone if hasattr(user, 'phone') and user.phone else '未知'}")
    print(f"个人资料照片: {'有' if user.photo else '无'}")

async def test_user_and_send_message(client, user_id_or_username,message,parse_mode='html'):
    """测试获取用户信息并发送消息
    
    Args:
        client: Telegram客户端
        user_id_or_username: 用户ID或用户名
    """
    # 获取用户信息
    user = await get_user_info(client, user_id_or_username)
    if user:
        print_user_info(user)
        
        # 发送消息
        sent_message = await send_message(client, user_id_or_username, message,parse_mode)
        if sent_message:
            print(f"消息发送成功，消息ID: {sent_message.id}")
        else:
            print("消息发送失败")
    else:
        print(f"无法获取用户 {user_id_or_username} 的信息，尝试直接发送消息")
        # 即使无法获取用户信息，也尝试发送消息
        message = f"你好"
        sent_message = await send_message(client, user_id_or_username, message)
        if sent_message:
            print(f"消息发送成功，消息ID: {sent_message.id}")
        else:
            print("消息发送失败")

async def invite_user_to_group(client: TelegramClient, user_id_or_username: Union[int, str], group_identifier: str) -> bool:
    """邀请用户加入群组
    
    Args:
        client: Telegram客户端
        user_id_or_username: 用户ID或用户名
        group_identifier: 群组标识符（可以是链接、用户名或ID）
    
    Returns:
        是否成功邀请
    """
    try:
        # 1. 解析群组标识符
        
        group_entity = await client.get_entity(group_identifier)
        #user_entity = await client.get_input_entity(user_id_or_username)
        user_entity = await client.get_entity(user_id_or_username)
        # 2. 获取用户实体

        
        # 3. 根据群组类型使用不同的邀请方法
        if isinstance(group_entity, Channel):
            # 超级群组或频道
            if not group_entity.megagroup and not group_entity.gigagroup:
                print("不能邀请用户加入普通频道，用户需要通过邀请链接加入")
                return False
                
            print(f"正在邀请用户加入超级群组 {group_entity.title}")
            await client(InviteToChannelRequest(
                channel=group_entity,
                users=[user_entity]
            ))
            print(f"已成功邀请用户加入超级群组")
            return True
            
        elif isinstance(group_entity, Chat):
            # 普通群组
            print(f"正在邀请用户加入普通群组 {group_entity.title}")
            await client(AddChatUserRequest(
                chat_id=group_entity.id,
                user_id=user_entity,
                fwd_limit=100  # 最多可以转发多少条消息
            ))
            print(f"已成功邀请用户加入普通群组")
            return True
            
        else:
            print(f"未知的群组类型: {type(group_entity)}")
            return False
            
    except FloodWaitError as e:
        wait_time = e.seconds
        print(f"操作过于频繁，需要等待 {wait_time} 秒后重试")
        return False
    except UserPrivacyRestrictedError:
        print(f"由于用户的隐私设置，无法邀请该用户")
        return False
    except UserNotMutualContactError:
        print(f"无法邀请非互相联系人的用户")
        return False
    except ChatAdminRequiredError:
        print(f"需要管理员权限才能邀请用户")
        return False
    except UserIdInvalidError:
        print(f"无效的用户ID")
        return False
    except Exception as e:
        print(f"邀请用户时出错: {e}")
        return False

async def main():
    """主函数"""
    print("开始Telethon基础功能测试")
    
    # 构建会话文件路径
    session_path = os.path.join(SESSION_DIR, SESSION_FILE)
    print(session_path)
    # 创建客户端
    
    # 随机设备模型列表
    device_models = [
        "iPhone 15 Pro Max",
        "Samsung Galaxy S24 Ultra", 
        "Xiaomi 14 Pro",
        "OnePlus 12",
        "Google Pixel 8 Pro",
        "Huawei Mate 60 Pro",
        "OPPO Find X7 Ultra",
        "vivo X100 Pro",
        "Realme GT5 Pro",
        "iQOO 12 Pro"
    ]
    
    # 随机选择一个设备模型
    import random
    random_device = random.choice(device_models)
    
    client = TelegramClient(
        session_path,
        API_ID, 
        API_HASH,
        proxy=PROXY,
        device_model=random_device  
    )
    try:
        # 连接并确保已登录
        await client.connect()
        
        if not await client.is_user_authorized():
            print("错误: 会话未授权，请先登录账号")
            return
            
        # 获取自己的信息
        my_info = await get_my_info(client)
        if not my_info:
            #print("获取个人信息失败")
            return
        message =  """本频道为天壹财团新接平台公示、福利待遇、奖励分红、每日更新行业动态、团队动态等。
诚邀各个级别代理携手共赢，合作洽谈联系天壹本人飞机。
⭐️天一励志：@tianyilizhi
🔗天一软件：@tianyiruanjian
💥天一动态：@tianyidongtai
🔥天一待遇：@tianyidaiyu
💬天一聊天：@tianyiliaotian
🎞天一看片：@tianyikanpian
✈️天一本人：@tianyi2024"""
        await test_user_and_send_message(client, 'ddv6_com',message,'html')
        # 测试通过用户ID发送消息
        print("\n===== 测试通过用户ID发送消息 =====")
        user_id = [**********,**********,**********,'+8617620035224',]  # Telegram官方通知账号
        #username发送成功，普通发送即可向username发送 await client.send_message(username, message),不需要实体
        #user_id
        #phone
        # for uid in user_id:
        #     await test_user_and_send_message(client, uid)
        #     await asyncio.sleep(3)
        
        # 测试邀请入群
        #print("\n===== 测试邀请入群 =====")
        #group_link = **********
        # group_link = "https://t.me/abcd135EG"
        # invalid_ids = ['TRXX2313X90','pianai5201','JoyceMeyer2020','anzhuoshoji',123456789012345,'+8617620035224',**********,**********,**********,**********,]  # 一个可能不存在的ID 
        # for uid in invalid_ids:
        #     # 邀请用户入群
        #     await invite_user_to_group(client, uid, group_link)
        #     await asyncio.sleep(5)

    except Exception as e:
        print(f"测试过程中出错: {e}")
    finally:
        # 断开连接
        await client.disconnect()
        print("测试完成，客户端已断开连接")

if __name__ == "__main__":
    asyncio.run(main()) 
