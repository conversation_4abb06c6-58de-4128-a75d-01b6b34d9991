#!/usr/bin/python3
# -*- coding: utf-8 -*-
from PySide6.QtWidgets import QApplication, QComboBox, QVBoxLayout, QWidget, QLabel
from PySide6.QtCore import Qt

app = QApplication([])

window = QWidget()
layout = QVBoxLayout()

combo = QComboBox()
combo.addItem("苹果", {"id": 1, "price": 5.8})  # 关联字典数据
combo.addItem("香蕉", {"id": 2, "price": 3.2})
combo.addItem("橙子", {"id": 3, "price": 4.5})

label = QLabel()

def on_current_index_changed():
    data = combo.currentData()
    print(data)
    if data:
        label.setText(f"选中: {combo.currentText()}, ID: {data['id']}, 价格: {data['price']}")
    else:
        label.setText("没有数据")

combo.currentIndexChanged.connect(on_current_index_changed)

layout.addWidget(combo)
layout.addWidget(label)
window.setLayout(layout)
window.show()

app.exec()
