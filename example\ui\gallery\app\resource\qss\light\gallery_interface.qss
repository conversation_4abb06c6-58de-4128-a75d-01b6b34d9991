GalleryInterface, ToolBar, #view {
    background-color: transparent;
}

QScrollArea {
    border: none;
}

ToolBar > StrongBodyLabel {
    color: black;
}

ToolBar > CaptionLabel {
    color: rgb(95, 95, 95);
}

ExampleCard {
    background-color: transparent;
}

TitleLabel,
StrongBodyLabel {
    color: black;
}

ExampleCard > #card {
    border: 1px solid rgba(0, 0, 0, 0.05);
    border-radius: 10px;
    background-color: rgba(0, 0, 0, 0.024);
}

ExampleCard > #card QLabel {
    font: 14px 'Segoe UI', 'Microsoft YaHei', 'PingFang SC';
    color: black;
}

ExampleCard> #card InfoBadge {
    font-size: 11px;
}

#sourceWidget {
    background-color: rgba(255, 255, 255, 0.667);
    border-top: 1px solid rgba(0, 0, 0, 0.05);
    border-bottom-left-radius: 10px;
    border-bottom-right-radius: 10px;
}
