<RCC>
    <qresource prefix="/gallery">
        <file>images/controls/Acrylic.png</file>
        <file>images/controls/AnimatedIcon.png</file>
        <file>images/controls/AnimatedVisualPlayer.png</file>
        <file>images/controls/AnimationInterop.png</file>
        <file>images/controls/AppBarButton.png</file>
        <file>images/controls/AppBarSeparator.png</file>
        <file>images/controls/AppBarToggleButton.png</file>
        <file>images/controls/AutomationProperties.png</file>
        <file>images/controls/AutoSuggestBox.png</file>
        <file>images/controls/Border.png</file>
        <file>images/controls/BreadcrumbBar.png</file>
        <file>images/controls/Button.png</file>
        <file>images/controls/CalendarDatePicker.png</file>
        <file>images/controls/CalendarView.png</file>
        <file>images/controls/Canvas.png</file>
        <file>images/controls/Checkbox.png</file>
        <file>images/controls/Clipboard.png</file>
        <file>images/controls/ColorPaletteResources.png</file>
        <file>images/controls/ColorPicker.png</file>
        <file>images/controls/ComboBox.png</file>
        <file>images/controls/CommandBar.png</file>
        <file>images/controls/CommandBarFlyout.png</file>
        <file>images/controls/CompactSizing.png</file>
        <file>images/controls/ConnectedAnimation.png</file>
        <file>images/controls/ContentDialog.png</file>
        <file>images/controls/CreateMultipleWindows.png</file>
        <file>images/controls/DataGrid.png</file>
        <file>images/controls/DatePicker.png</file>
        <file>images/controls/DropDownButton.png</file>
        <file>images/controls/EasingFunction.png</file>
        <file>images/controls/Expander.png</file>
        <file>images/controls/FilePicker.png</file>
        <file>images/controls/FlipView.png</file>
        <file>images/controls/Flyout.png</file>
        <file>images/controls/Grid.png</file>
        <file>images/controls/GridView.png</file>
        <file>images/controls/HyperlinkButton.png</file>
        <file>images/controls/IconElement.png</file>
        <file>images/controls/Image.png</file>
        <file>images/controls/ImplicitTransition.png</file>
        <file>images/controls/InfoBadge.png</file>
        <file>images/controls/InfoBar.png</file>
        <file>images/controls/InkCanvas.png</file>
        <file>images/controls/InkToolbar.png</file>
        <file>images/controls/InputValidation.png</file>
        <file>images/controls/ItemsRepeater.png</file>
        <file>images/controls/Line.png</file>
        <file>images/controls/ListBox.png</file>
        <file>images/controls/ListView.png</file>
        <file>images/controls/MediaPlayerElement.png</file>
        <file>images/controls/MenuBar.png</file>
        <file>images/controls/MenuFlyout.png</file>
        <file>images/controls/NavigationView.png</file>
        <file>images/controls/NumberBox.png</file>
        <file>images/controls/PageTransition.png</file>
        <file>images/controls/ParallaxView.png</file>
        <file>images/controls/PasswordBox.png</file>
        <file>images/controls/PersonPicture.png</file>
        <file>images/controls/PipsPager.png</file>
        <file>images/controls/Pivot.png</file>
        <file>images/controls/ProgressBar.png</file>
        <file>images/controls/ProgressRing.png</file>
        <file>images/controls/PullToRefresh.png</file>
        <file>images/controls/RadialGradientBrush.png</file>
        <file>images/controls/RadioButton.png</file>
        <file>images/controls/RadioButtons.png</file>
        <file>images/controls/RatingControl.png</file>
        <file>images/controls/RelativePanel.png</file>
        <file>images/controls/RepeatButton.png</file>
        <file>images/controls/RevealFocus.png</file>
        <file>images/controls/RichEditBox.png</file>
        <file>images/controls/RichTextBlock.png</file>
        <file>images/controls/ScrollViewer.png</file>
        <file>images/controls/SemanticZoom.png</file>
        <file>images/controls/Shape.png</file>
        <file>images/controls/Slider.png</file>
        <file>images/controls/Sound.png</file>
        <file>images/controls/SplitButton.png</file>
        <file>images/controls/SplitView.png</file>
        <file>images/controls/StackPanel.png</file>
        <file>images/controls/StandardUICommand.png</file>
        <file>images/controls/SwipeControl.png</file>
        <file>images/controls/TabView.png</file>
        <file>images/controls/TeachingTip.png</file>
        <file>images/controls/TextBlock.png</file>
        <file>images/controls/TextBox.png</file>
        <file>images/controls/ThemeTransition.png</file>
        <file>images/controls/TimePicker.png</file>
        <file>images/controls/TitleBar.png</file>
        <file>images/controls/ToggleButton.png</file>
        <file>images/controls/ToggleSplitButton.png</file>
        <file>images/controls/ToggleSwitch.png</file>
        <file>images/controls/ToolTip.png</file>
        <file>images/controls/TreeView.png</file>
        <file>images/controls/VariableSizedWrapGrid.png</file>
        <file>images/controls/Viewbox.png</file>
        <file>images/controls/WebView.png</file>
        <file>images/controls/XamlUICommand.png</file>
        <file>images/icons/EmojiTabSymbols_black.svg</file>
        <file>images/icons/EmojiTabSymbols_white.svg</file>
        <file>images/icons/Grid_black.svg</file>
        <file>images/icons/Grid_white.svg</file>
        <file>images/icons/Menu_black.svg</file>
        <file>images/icons/Menu_white.svg</file>
        <file>images/icons/Text_black.svg</file>
        <file>images/icons/Text_white.svg</file>
        <file>images/icons/Price_black.svg</file>
        <file>images/icons/Price_white.svg</file>
        <file>images/chidanta.jpg</file>
        <file>images/chidanta2.jpg</file>
        <file>images/chidanta3.jpg</file>
        <file>images/chidanta4.jpg</file>
        <file>images/chidanta5.jpg</file>
        <file>images/header.png</file>
        <file>images/header1.png</file>
        <file>images/kunkun.png</file>
        <file>images/logo.png</file>
        <file>images/shoko.png</file>
        <file>images/Gyro.jpg</file>
        <file>images/SBR.jpg</file>
        <file>images/Dvd.png</file>
        <file>images/Singer.png</file>
        <file>images/MusicNote.png</file>
        <file>images/Smiling_with_heart.png</file>
        <file>images/Shoko1.jpg</file>
        <file>images/Shoko2.jpg</file>
        <file>images/Shoko3.jpg</file>
        <file>images/Shoko4.jpg</file>

        <file>qss/dark/gallery_interface.qss</file>
        <file>qss/dark/home_interface.qss</file>
        <file>qss/dark/icon_interface.qss</file>
        <file>qss/dark/link_card.qss</file>
        <file>qss/dark/sample_card.qss</file>
        <file>qss/dark/setting_interface.qss</file>
        <file>qss/dark/view_interface.qss</file>
        <file>qss/dark/navigation_view_interface.qss</file>

        <file>qss/light/gallery_interface.qss</file>
        <file>qss/light/home_interface.qss</file>
        <file>qss/light/icon_interface.qss</file>
        <file>qss/light/link_card.qss</file>
        <file>qss/light/sample_card.qss</file>
        <file>qss/light/setting_interface.qss</file>
        <file>qss/light/view_interface.qss</file>
        <file>qss/light/navigation_view_interface.qss</file>

        <file>i18n/gallery.zh_CN.qm</file>
        <file>i18n/gallery.zh_HK.qm</file>

    </qresource>
</RCC>