Widget > Q<PERSON>abel {
    font: 24px 'Segoe UI', 'Microsoft YaHei';
}

StackedWidget {
    border: 1px solid rgb(29, 29, 29);
    border-right: none;
    border-bottom: none;
    border-top-left-radius: 10px;
    background-color: rgb(39, 39, 39);
}

Window {
    background-color: rgb(32, 32, 32);
}

Widget > QLabel {
    color: white;
}

CustomTitleBar {
    background-color: transparent;
}

CustomTitleBar>QLabel#titleLabel {
    background: transparent;
    font: 13px 'Segoe UI';
    padding: 0 10px;
    color: white;
}

MinimizeButton {
    qproperty-normalColor: white;
    qproperty-normalBackgroundColor: transparent;
    qproperty-hoverColor: white;
    qproperty-hoverBackgroundColor: rgba(255, 255, 255, 26);
    qproperty-pressedColor: white;
    qproperty-pressedBackgroundColor: rgba(255, 255, 255, 51)
}

MaximizeButton {
    qproperty-normalColor: white;
    qproperty-normalBackgroundColor: transparent;
    qproperty-hoverColor: white;
    qproperty-hoverBackgroundColor: rgba(255, 255, 255, 26);
    qproperty-pressedColor: white;
    qproperty-pressedBackgroundColor: rgba(255, 255, 255, 51)
}

CloseButton {
    qproperty-normalColor: white;
    qproperty-normalBackgroundColor: transparent;
}