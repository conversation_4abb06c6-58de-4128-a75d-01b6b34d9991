<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>FocusInterface</class>
 <widget class="QWidget" name="FocusInterface">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>911</width>
    <height>807</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <layout class="QHBoxLayout" name="horizontalLayout_3">
   <item>
    <layout class="QGridLayout" name="gridLayout" columnstretch="1,1,0">
     <property name="sizeConstraint">
      <enum>QLayout::SetDefaultConstraint</enum>
     </property>
     <property name="leftMargin">
      <number>20</number>
     </property>
     <property name="topMargin">
      <number>40</number>
     </property>
     <property name="rightMargin">
      <number>20</number>
     </property>
     <property name="bottomMargin">
      <number>20</number>
     </property>
     <property name="spacing">
      <number>12</number>
     </property>
     <item row="0" column="1">
      <widget class="CardWidget" name="progressCard">
       <property name="sizePolicy">
        <sizepolicy hsizetype="Expanding" vsizetype="Fixed">
         <horstretch>0</horstretch>
         <verstretch>0</verstretch>
        </sizepolicy>
       </property>
       <property name="minimumSize">
        <size>
         <width>380</width>
         <height>410</height>
        </size>
       </property>
       <property name="maximumSize">
        <size>
         <width>600</width>
         <height>410</height>
        </size>
       </property>
       <layout class="QVBoxLayout" name="verticalLayout_4">
        <property name="sizeConstraint">
         <enum>QLayout::SetDefaultConstraint</enum>
        </property>
        <item>
         <layout class="QVBoxLayout" name="verticalLayout_2" stretch="0,0">
          <property name="sizeConstraint">
           <enum>QLayout::SetMinAndMaxSize</enum>
          </property>
          <item>
           <layout class="QHBoxLayout" name="horizontalLayout_4">
            <property name="leftMargin">
             <number>5</number>
            </property>
            <item>
             <widget class="IconWidget" name="progressIcon">
              <property name="minimumSize">
               <size>
                <width>18</width>
                <height>18</height>
               </size>
              </property>
              <property name="maximumSize">
               <size>
                <width>18</width>
                <height>18</height>
               </size>
              </property>
              <property name="icon">
               <iconset resource="../resource.qrc">
                <normaloff>:/images/tips.png</normaloff>:/images/tips.png</iconset>
              </property>
             </widget>
            </item>
            <item>
             <spacer name="horizontalSpacer_8">
              <property name="orientation">
               <enum>Qt::Horizontal</enum>
              </property>
              <property name="sizeType">
               <enum>QSizePolicy::Fixed</enum>
              </property>
              <property name="sizeHint" stdset="0">
               <size>
                <width>2</width>
                <height>20</height>
               </size>
              </property>
             </spacer>
            </item>
            <item>
             <widget class="StrongBodyLabel" name="dailyProgressLabel">
              <property name="text">
               <string>每日进度</string>
              </property>
             </widget>
            </item>
            <item>
             <spacer name="horizontalSpacer_9">
              <property name="orientation">
               <enum>Qt::Horizontal</enum>
              </property>
              <property name="sizeHint" stdset="0">
               <size>
                <width>40</width>
                <height>20</height>
               </size>
              </property>
             </spacer>
            </item>
            <item alignment="Qt::AlignRight">
             <widget class="TransparentToolButton" name="editButton"/>
            </item>
           </layout>
          </item>
          <item>
           <layout class="QHBoxLayout" name="horizontalLayout_5" stretch="1,2,1">
            <property name="sizeConstraint">
             <enum>QLayout::SetMinAndMaxSize</enum>
            </property>
            <item>
             <layout class="QVBoxLayout" name="verticalLayout_5">
              <item>
               <spacer name="verticalSpacer_5">
                <property name="orientation">
                 <enum>Qt::Vertical</enum>
                </property>
                <property name="sizeHint" stdset="0">
                 <size>
                  <width>20</width>
                  <height>40</height>
                 </size>
                </property>
               </spacer>
              </item>
              <item alignment="Qt::AlignHCenter|Qt::AlignVCenter">
               <widget class="BodyLabel" name="yesterdayLabel">
                <property name="text">
                 <string>昨天</string>
                </property>
               </widget>
              </item>
              <item alignment="Qt::AlignHCenter|Qt::AlignVCenter">
               <widget class="LargeTitleLabel" name="yesterdayTimeLabel">
                <property name="text">
                 <string>3</string>
                </property>
               </widget>
              </item>
              <item alignment="Qt::AlignHCenter|Qt::AlignVCenter">
               <widget class="BodyLabel" name="minuteLabel1">
                <property name="text">
                 <string>分钟</string>
                </property>
               </widget>
              </item>
              <item>
               <spacer name="verticalSpacer_6">
                <property name="orientation">
                 <enum>Qt::Vertical</enum>
                </property>
                <property name="sizeHint" stdset="0">
                 <size>
                  <width>20</width>
                  <height>40</height>
                 </size>
                </property>
               </spacer>
              </item>
             </layout>
            </item>
            <item>
             <layout class="QVBoxLayout" name="verticalLayout_10" stretch="0,0,1,0,0,0">
              <item>
               <spacer name="verticalSpacer_15">
                <property name="orientation">
                 <enum>Qt::Vertical</enum>
                </property>
                <property name="sizeHint" stdset="0">
                 <size>
                  <width>20</width>
                  <height>40</height>
                 </size>
                </property>
               </spacer>
              </item>
              <item>
               <widget class="SubtitleLabel" name="targetLabel">
                <property name="text">
                 <string>今日计划</string>
                </property>
                <property name="alignment">
                 <set>Qt::AlignCenter</set>
                </property>
               </widget>
              </item>
              <item>
               <widget class="ProgressRing" name="progressRing">
                <property name="sizePolicy">
                 <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
                  <horstretch>1</horstretch>
                  <verstretch>1</verstretch>
                 </sizepolicy>
                </property>
                <property name="minimumSize">
                 <size>
                  <width>150</width>
                  <height>150</height>
                 </size>
                </property>
                <property name="maximumSize">
                 <size>
                  <width>220</width>
                  <height>220</height>
                 </size>
                </property>
                <property name="font">
                 <font>
                  <family>Microsoft YaHei UI</family>
                  <pointsize>10</pointsize>
                  <weight>50</weight>
                  <bold>false</bold>
                 </font>
                </property>
                <property name="maximum">
                 <number>24</number>
                </property>
                <property name="value">
                 <number>10</number>
                </property>
                <property name="alignment">
                 <set>Qt::AlignCenter</set>
                </property>
                <property name="textVisible">
                 <bool>true</bool>
                </property>
                <property name="orientation">
                 <enum>Qt::Horizontal</enum>
                </property>
                <property name="textDirection">
                 <enum>QProgressBar::TopToBottom</enum>
                </property>
                <property name="format">
                 <string>目标 %v 小时</string>
                </property>
                <property name="useAni">
                 <bool>false</bool>
                </property>
                <property name="val">
                 <double>10.000000000000000</double>
                </property>
                <property name="strokeWidth">
                 <number>15</number>
                </property>
               </widget>
              </item>
              <item>
               <spacer name="verticalSpacer_16">
                <property name="orientation">
                 <enum>Qt::Vertical</enum>
                </property>
                <property name="sizeType">
                 <enum>QSizePolicy::Fixed</enum>
                </property>
                <property name="sizeHint" stdset="0">
                 <size>
                  <width>20</width>
                  <height>3</height>
                 </size>
                </property>
               </spacer>
              </item>
              <item alignment="Qt::AlignHCenter">
               <widget class="BodyLabel" name="finishTimeLabel">
                <property name="text">
                 <string>已完成：0 分钟</string>
                </property>
               </widget>
              </item>
              <item>
               <spacer name="verticalSpacer_13">
                <property name="orientation">
                 <enum>Qt::Vertical</enum>
                </property>
                <property name="sizeHint" stdset="0">
                 <size>
                  <width>20</width>
                  <height>40</height>
                 </size>
                </property>
               </spacer>
              </item>
             </layout>
            </item>
            <item>
             <layout class="QVBoxLayout" name="verticalLayout_6">
              <item>
               <spacer name="verticalSpacer_9">
                <property name="orientation">
                 <enum>Qt::Vertical</enum>
                </property>
                <property name="sizeHint" stdset="0">
                 <size>
                  <width>20</width>
                  <height>40</height>
                 </size>
                </property>
               </spacer>
              </item>
              <item alignment="Qt::AlignHCenter|Qt::AlignVCenter">
               <widget class="BodyLabel" name="continousComplianceDayLabel">
                <property name="text">
                 <string>连续达标日</string>
                </property>
               </widget>
              </item>
              <item alignment="Qt::AlignHCenter">
               <widget class="LargeTitleLabel" name="compianceDayLabel">
                <property name="text">
                 <string>5</string>
                </property>
               </widget>
              </item>
              <item alignment="Qt::AlignHCenter">
               <widget class="BodyLabel" name="dayLabel">
                <property name="text">
                 <string>天</string>
                </property>
               </widget>
              </item>
              <item>
               <spacer name="verticalSpacer_10">
                <property name="orientation">
                 <enum>Qt::Vertical</enum>
                </property>
                <property name="sizeHint" stdset="0">
                 <size>
                  <width>20</width>
                  <height>40</height>
                 </size>
                </property>
               </spacer>
              </item>
             </layout>
            </item>
           </layout>
          </item>
         </layout>
        </item>
       </layout>
      </widget>
     </item>
     <item row="0" column="0">
      <widget class="CardWidget" name="focusCard">
       <property name="sizePolicy">
        <sizepolicy hsizetype="Expanding" vsizetype="Fixed">
         <horstretch>0</horstretch>
         <verstretch>0</verstretch>
        </sizepolicy>
       </property>
       <property name="minimumSize">
        <size>
         <width>380</width>
         <height>410</height>
        </size>
       </property>
       <property name="maximumSize">
        <size>
         <width>600</width>
         <height>410</height>
        </size>
       </property>
       <property name="styleSheet">
        <string notr="true"/>
       </property>
       <layout class="QVBoxLayout" name="verticalLayout_3">
        <property name="sizeConstraint">
         <enum>QLayout::SetDefaultConstraint</enum>
        </property>
        <item>
         <layout class="QVBoxLayout" name="verticalLayout">
          <property name="sizeConstraint">
           <enum>QLayout::SetDefaultConstraint</enum>
          </property>
          <item>
           <layout class="QHBoxLayout" name="horizontalLayout_2">
            <property name="leftMargin">
             <number>5</number>
            </property>
            <item>
             <widget class="IconWidget" name="focusCardIcon">
              <property name="minimumSize">
               <size>
                <width>20</width>
                <height>20</height>
               </size>
              </property>
              <property name="maximumSize">
               <size>
                <width>20</width>
                <height>20</height>
               </size>
              </property>
              <property name="icon">
               <iconset resource="../resource.qrc">
                <normaloff>:/images/alarms.png</normaloff>:/images/alarms.png</iconset>
              </property>
             </widget>
            </item>
            <item>
             <widget class="StrongBodyLabel" name="focusPeriodLabel">
              <property name="text">
               <string>专注时段</string>
              </property>
             </widget>
            </item>
            <item>
             <spacer name="horizontalSpacer">
              <property name="orientation">
               <enum>Qt::Horizontal</enum>
              </property>
              <property name="sizeHint" stdset="0">
               <size>
                <width>40</width>
                <height>20</height>
               </size>
              </property>
             </spacer>
            </item>
            <item>
             <widget class="TransparentToolButton" name="pinButton"/>
            </item>
            <item>
             <widget class="TransparentToolButton" name="moreButton"/>
            </item>
           </layout>
          </item>
          <item>
           <spacer name="verticalSpacer_12">
            <property name="orientation">
             <enum>Qt::Vertical</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>20</width>
              <height>40</height>
             </size>
            </property>
           </spacer>
          </item>
          <item>
           <widget class="SubtitleLabel" name="prepareFocusLabel">
            <property name="text">
             <string>准备专注</string>
            </property>
            <property name="alignment">
             <set>Qt::AlignCenter</set>
            </property>
           </widget>
          </item>
          <item>
           <layout class="QHBoxLayout" name="horizontalLayout_6">
            <property name="leftMargin">
             <number>15</number>
            </property>
            <property name="topMargin">
             <number>10</number>
            </property>
            <property name="rightMargin">
             <number>15</number>
            </property>
            <item>
             <widget class="BodyLabel" name="hintLabel">
              <property name="text">
               <string>我们将在每个会话期间关闭通知和应用警报。对于较长的会话，我们将添加简短的休息时间，以便你可以恢复精力。</string>
              </property>
              <property name="alignment">
               <set>Qt::AlignCenter</set>
              </property>
              <property name="wordWrap">
               <bool>true</bool>
              </property>
              <property name="margin">
               <number>0</number>
              </property>
              <property name="lightColor" stdset="0">
               <color>
                <red>96</red>
                <green>96</green>
                <blue>96</blue>
               </color>
              </property>
              <property name="darkColor" stdset="0">
               <color>
                <red>206</red>
                <green>206</green>
                <blue>206</blue>
               </color>
              </property>
             </widget>
            </item>
           </layout>
          </item>
          <item>
           <spacer name="verticalSpacer_2">
            <property name="orientation">
             <enum>Qt::Vertical</enum>
            </property>
            <property name="sizeType">
             <enum>QSizePolicy::Fixed</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>20</width>
              <height>20</height>
             </size>
            </property>
           </spacer>
          </item>
          <item alignment="Qt::AlignHCenter">
           <widget class="TimePicker" name="timePicker">
            <property name="secondVisible">
             <bool>true</bool>
            </property>
           </widget>
          </item>
          <item>
           <spacer name="verticalSpacer_3">
            <property name="orientation">
             <enum>Qt::Vertical</enum>
            </property>
            <property name="sizeType">
             <enum>QSizePolicy::Fixed</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>20</width>
              <height>10</height>
             </size>
            </property>
           </spacer>
          </item>
          <item alignment="Qt::AlignHCenter">
           <widget class="BodyLabel" name="bottomHintLabel">
            <property name="text">
             <string>你将没有休息时间。</string>
            </property>
            <property name="alignment">
             <set>Qt::AlignCenter</set>
            </property>
           </widget>
          </item>
          <item>
           <spacer name="verticalSpacer_11">
            <property name="orientation">
             <enum>Qt::Vertical</enum>
            </property>
            <property name="sizeType">
             <enum>QSizePolicy::Fixed</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>20</width>
              <height>10</height>
             </size>
            </property>
           </spacer>
          </item>
          <item alignment="Qt::AlignHCenter">
           <widget class="CheckBox" name="skipRelaxCheckBox">
            <property name="enabled">
             <bool>true</bool>
            </property>
            <property name="text">
             <string>跳过休息</string>
            </property>
           </widget>
          </item>
          <item>
           <spacer name="verticalSpacer_4">
            <property name="orientation">
             <enum>Qt::Vertical</enum>
            </property>
            <property name="sizeType">
             <enum>QSizePolicy::Fixed</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>20</width>
              <height>20</height>
             </size>
            </property>
           </spacer>
          </item>
          <item alignment="Qt::AlignHCenter">
           <widget class="PrimaryPushButton" name="startFocusButton">
            <property name="text">
             <string>启动专注时段</string>
            </property>
            <property name="autoDefault">
             <bool>true</bool>
            </property>
           </widget>
          </item>
          <item>
           <spacer name="verticalSpacer">
            <property name="orientation">
             <enum>Qt::Vertical</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>20</width>
              <height>40</height>
             </size>
            </property>
           </spacer>
          </item>
         </layout>
        </item>
       </layout>
      </widget>
     </item>
     <item row="1" column="0">
      <widget class="CardWidget" name="taskCard">
       <property name="minimumSize">
        <size>
         <width>370</width>
         <height>0</height>
        </size>
       </property>
       <property name="maximumSize">
        <size>
         <width>600</width>
         <height>395</height>
        </size>
       </property>
       <layout class="QVBoxLayout" name="verticalLayout_8">
        <item>
         <layout class="QVBoxLayout" name="verticalLayout_7">
          <property name="leftMargin">
           <number>8</number>
          </property>
          <item>
           <layout class="QHBoxLayout" name="horizontalLayout_7">
            <item>
             <widget class="IconWidget" name="taskCardIcon">
              <property name="minimumSize">
               <size>
                <width>18</width>
                <height>18</height>
               </size>
              </property>
              <property name="maximumSize">
               <size>
                <width>18</width>
                <height>18</height>
               </size>
              </property>
              <property name="icon">
               <iconset resource="../resource.qrc">
                <normaloff>:/images/todo.png</normaloff>:/images/todo.png</iconset>
              </property>
             </widget>
            </item>
            <item>
             <spacer name="horizontalSpacer_7">
              <property name="orientation">
               <enum>Qt::Horizontal</enum>
              </property>
              <property name="sizeType">
               <enum>QSizePolicy::Fixed</enum>
              </property>
              <property name="sizeHint" stdset="0">
               <size>
                <width>2</width>
                <height>2</height>
               </size>
              </property>
             </spacer>
            </item>
            <item>
             <widget class="StrongBodyLabel" name="taskLabel">
              <property name="text">
               <string>任务</string>
              </property>
             </widget>
            </item>
            <item>
             <spacer name="horizontalSpacer_3">
              <property name="orientation">
               <enum>Qt::Horizontal</enum>
              </property>
              <property name="sizeHint" stdset="0">
               <size>
                <width>40</width>
                <height>20</height>
               </size>
              </property>
             </spacer>
            </item>
            <item>
             <widget class="TransparentToolButton" name="addTaskButton"/>
            </item>
            <item>
             <widget class="TransparentToolButton" name="moreTaskButton"/>
            </item>
           </layout>
          </item>
          <item>
           <spacer name="verticalSpacer_8">
            <property name="orientation">
             <enum>Qt::Vertical</enum>
            </property>
            <property name="sizeType">
             <enum>QSizePolicy::Fixed</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>20</width>
              <height>3</height>
             </size>
            </property>
           </spacer>
          </item>
          <item>
           <widget class="BodyLabel" name="hintLabel_2">
            <property name="text">
             <string>为会话选择任务</string>
            </property>
            <property name="lightColor" stdset="0">
             <color>
              <red>96</red>
              <green>96</green>
              <blue>96</blue>
             </color>
            </property>
            <property name="darkColor" stdset="0">
             <color>
              <red>206</red>
              <green>206</green>
              <blue>206</blue>
             </color>
            </property>
           </widget>
          </item>
          <item>
           <layout class="QVBoxLayout" name="verticalLayout_9">
            <property name="rightMargin">
             <number>7</number>
            </property>
            <item>
             <widget class="CardWidget" name="taskCard1">
              <property name="minimumSize">
               <size>
                <width>0</width>
                <height>44</height>
               </size>
              </property>
              <property name="maximumSize">
               <size>
                <width>16777215</width>
                <height>44</height>
               </size>
              </property>
              <layout class="QHBoxLayout" name="horizontalLayout_9">
               <property name="leftMargin">
                <number>15</number>
               </property>
               <item>
                <layout class="QHBoxLayout" name="horizontalLayout_8">
                 <item>
                  <widget class="IconWidget" name="taskIcon1">
                   <property name="sizePolicy">
                    <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
                     <horstretch>16</horstretch>
                     <verstretch>16</verstretch>
                    </sizepolicy>
                   </property>
                   <property name="minimumSize">
                    <size>
                     <width>16</width>
                     <height>16</height>
                    </size>
                   </property>
                   <property name="maximumSize">
                    <size>
                     <width>16</width>
                     <height>16</height>
                    </size>
                   </property>
                  </widget>
                 </item>
                </layout>
               </item>
               <item>
                <widget class="BodyLabel" name="taskLabel1">
                 <property name="text">
                  <string>全军出鸡，誓死保卫鸽鸽！！</string>
                 </property>
                 <property name="pixelFontSize" stdset="0">
                  <number>14</number>
                 </property>
                 <property name="strikeOut" stdset="0">
                  <bool>true</bool>
                 </property>
                </widget>
               </item>
               <item>
                <spacer name="horizontalSpacer_4">
                 <property name="orientation">
                  <enum>Qt::Horizontal</enum>
                 </property>
                 <property name="sizeHint" stdset="0">
                  <size>
                   <width>40</width>
                   <height>20</height>
                  </size>
                 </property>
                </spacer>
               </item>
              </layout>
             </widget>
            </item>
            <item>
             <widget class="CardWidget" name="taskCard2">
              <property name="minimumSize">
               <size>
                <width>0</width>
                <height>44</height>
               </size>
              </property>
              <property name="maximumSize">
               <size>
                <width>16777215</width>
                <height>44</height>
               </size>
              </property>
              <layout class="QHBoxLayout" name="horizontalLayout_12">
               <property name="leftMargin">
                <number>15</number>
               </property>
               <item>
                <layout class="QHBoxLayout" name="horizontalLayout_13">
                 <item>
                  <widget class="IconWidget" name="taskIcon2">
                   <property name="sizePolicy">
                    <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
                     <horstretch>16</horstretch>
                     <verstretch>16</verstretch>
                    </sizepolicy>
                   </property>
                   <property name="minimumSize">
                    <size>
                     <width>16</width>
                     <height>16</height>
                    </size>
                   </property>
                   <property name="maximumSize">
                    <size>
                     <width>16</width>
                     <height>16</height>
                    </size>
                   </property>
                  </widget>
                 </item>
                </layout>
               </item>
               <item>
                <widget class="BodyLabel" name="taskLabel2">
                 <property name="text">
                  <string>上传我家 aiko 的 MV『シアワセ』</string>
                 </property>
                </widget>
               </item>
               <item>
                <spacer name="horizontalSpacer_6">
                 <property name="orientation">
                  <enum>Qt::Horizontal</enum>
                 </property>
                 <property name="sizeHint" stdset="0">
                  <size>
                   <width>40</width>
                   <height>20</height>
                  </size>
                 </property>
                </spacer>
               </item>
              </layout>
             </widget>
            </item>
            <item>
             <widget class="CardWidget" name="taskCard3">
              <property name="minimumSize">
               <size>
                <width>0</width>
                <height>44</height>
               </size>
              </property>
              <property name="maximumSize">
               <size>
                <width>16777215</width>
                <height>44</height>
               </size>
              </property>
              <layout class="QHBoxLayout" name="horizontalLayout_10">
               <property name="leftMargin">
                <number>15</number>
               </property>
               <item>
                <layout class="QHBoxLayout" name="horizontalLayout_11">
                 <item>
                  <widget class="IconWidget" name="taskIcon3">
                   <property name="sizePolicy">
                    <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
                     <horstretch>16</horstretch>
                     <verstretch>16</verstretch>
                    </sizepolicy>
                   </property>
                   <property name="minimumSize">
                    <size>
                     <width>16</width>
                     <height>16</height>
                    </size>
                   </property>
                   <property name="maximumSize">
                    <size>
                     <width>16</width>
                     <height>16</height>
                    </size>
                   </property>
                  </widget>
                 </item>
                </layout>
               </item>
               <item>
                <widget class="BodyLabel" name="taskLabel3">
                 <property name="text">
                  <string>下载我家 aiko 的新歌『荒れた唇は恋を失くす』</string>
                 </property>
                </widget>
               </item>
               <item>
                <spacer name="horizontalSpacer_5">
                 <property name="orientation">
                  <enum>Qt::Horizontal</enum>
                 </property>
                 <property name="sizeHint" stdset="0">
                  <size>
                   <width>40</width>
                   <height>20</height>
                  </size>
                 </property>
                </spacer>
               </item>
              </layout>
             </widget>
            </item>
           </layout>
          </item>
          <item>
           <spacer name="verticalSpacer_7">
            <property name="orientation">
             <enum>Qt::Vertical</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>20</width>
              <height>40</height>
             </size>
            </property>
           </spacer>
          </item>
         </layout>
        </item>
       </layout>
      </widget>
     </item>
     <item row="0" column="2">
      <spacer name="horizontalSpacer_2">
       <property name="orientation">
        <enum>Qt::Horizontal</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>40</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
    </layout>
   </item>
  </layout>
 </widget>
 <customwidgets>
  <customwidget>
   <class>CheckBox</class>
   <extends>QCheckBox</extends>
   <header>qfluentwidgets</header>
  </customwidget>
  <customwidget>
   <class>PushButton</class>
   <extends>QPushButton</extends>
   <header>qfluentwidgets</header>
  </customwidget>
  <customwidget>
   <class>PrimaryPushButton</class>
   <extends>PushButton</extends>
   <header>qfluentwidgets</header>
  </customwidget>
  <customwidget>
   <class>ToolButton</class>
   <extends>QToolButton</extends>
   <header>qfluentwidgets</header>
  </customwidget>
  <customwidget>
   <class>TransparentToolButton</class>
   <extends>ToolButton</extends>
   <header>qfluentwidgets</header>
  </customwidget>
  <customwidget>
   <class>IconWidget</class>
   <extends>QWidget</extends>
   <header>qfluentwidgets</header>
  </customwidget>
  <customwidget>
   <class>CardWidget</class>
   <extends>QFrame</extends>
   <header>qfluentwidgets</header>
   <container>1</container>
  </customwidget>
  <customwidget>
   <class>TimePicker</class>
   <extends>QPushButton</extends>
   <header>qfluentwidgets</header>
  </customwidget>
  <customwidget>
   <class>BodyLabel</class>
   <extends>QLabel</extends>
   <header>qfluentwidgets</header>
  </customwidget>
  <customwidget>
   <class>StrongBodyLabel</class>
   <extends>QLabel</extends>
   <header>qfluentwidgets</header>
  </customwidget>
  <customwidget>
   <class>SubtitleLabel</class>
   <extends>QLabel</extends>
   <header>qfluentwidgets</header>
  </customwidget>
  <customwidget>
   <class>LargeTitleLabel</class>
   <extends>QLabel</extends>
   <header>qfluentwidgets</header>
  </customwidget>
  <customwidget>
   <class>ProgressBar</class>
   <extends>QProgressBar</extends>
   <header>qfluentwidgets</header>
  </customwidget>
  <customwidget>
   <class>ProgressRing</class>
   <extends>ProgressBar</extends>
   <header>qfluentwidgets</header>
  </customwidget>
 </customwidgets>
 <resources>
  <include location="../resource.qrc"/>
 </resources>
 <connections/>
</ui>
