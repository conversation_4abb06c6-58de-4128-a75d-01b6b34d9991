<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>StopWatchInterface</class>
 <widget class="QWidget" name="StopWatchInterface">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>867</width>
    <height>781</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout_2">
   <item>
    <layout class="QVBoxLayout" name="verticalLayout">
     <property name="spacing">
      <number>0</number>
     </property>
     <item>
      <spacer name="verticalSpacer">
       <property name="orientation">
        <enum>Qt::Vertical</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>20</width>
         <height>40</height>
        </size>
       </property>
      </spacer>
     </item>
     <item alignment="Qt::AlignHCenter">
      <widget class="BodyLabel" name="timeLabel">
       <property name="text">
        <string>00:00:00</string>
       </property>
       <property name="lightColor" stdset="0">
        <color>
         <red>96</red>
         <green>96</green>
         <blue>96</blue>
        </color>
       </property>
       <property name="darkColor" stdset="0">
        <color>
         <red>206</red>
         <green>206</green>
         <blue>206</blue>
        </color>
       </property>
       <property name="pixelFontSize" stdset="0">
        <number>100</number>
       </property>
      </widget>
     </item>
     <item>
      <layout class="QHBoxLayout" name="horizontalLayout_3">
       <item>
        <spacer name="horizontalSpacer_4">
         <property name="orientation">
          <enum>Qt::Horizontal</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>40</width>
           <height>20</height>
          </size>
         </property>
        </spacer>
       </item>
       <item>
        <widget class="TitleLabel" name="hourLabel">
         <property name="text">
          <string>小时</string>
         </property>
         <property name="lightColor" stdset="0">
          <color>
           <red>96</red>
           <green>96</green>
           <blue>96</blue>
          </color>
         </property>
         <property name="darkColor" stdset="0">
          <color>
           <red>206</red>
           <green>206</green>
           <blue>206</blue>
          </color>
         </property>
        </widget>
       </item>
       <item>
        <spacer name="horizontalSpacer_6">
         <property name="orientation">
          <enum>Qt::Horizontal</enum>
         </property>
         <property name="sizeType">
          <enum>QSizePolicy::Fixed</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>60</width>
           <height>20</height>
          </size>
         </property>
        </spacer>
       </item>
       <item>
        <widget class="TitleLabel" name="minuteLabel">
         <property name="text">
          <string>分钟</string>
         </property>
         <property name="lightColor" stdset="0">
          <color>
           <red>96</red>
           <green>96</green>
           <blue>96</blue>
          </color>
         </property>
         <property name="darkColor" stdset="0">
          <color>
           <red>206</red>
           <green>206</green>
           <blue>206</blue>
          </color>
         </property>
        </widget>
       </item>
       <item>
        <spacer name="horizontalSpacer_7">
         <property name="orientation">
          <enum>Qt::Horizontal</enum>
         </property>
         <property name="sizeType">
          <enum>QSizePolicy::Fixed</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>90</width>
           <height>17</height>
          </size>
         </property>
        </spacer>
       </item>
       <item>
        <widget class="TitleLabel" name="secondLabel">
         <property name="text">
          <string>秒</string>
         </property>
         <property name="lightColor" stdset="0">
          <color>
           <red>96</red>
           <green>96</green>
           <blue>96</blue>
          </color>
         </property>
         <property name="darkColor" stdset="0">
          <color>
           <red>206</red>
           <green>206</green>
           <blue>206</blue>
          </color>
         </property>
        </widget>
       </item>
       <item>
        <spacer name="horizontalSpacer_5">
         <property name="orientation">
          <enum>Qt::Horizontal</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>40</width>
           <height>20</height>
          </size>
         </property>
        </spacer>
       </item>
      </layout>
     </item>
     <item>
      <spacer name="verticalSpacer_3">
       <property name="orientation">
        <enum>Qt::Vertical</enum>
       </property>
       <property name="sizeType">
        <enum>QSizePolicy::Fixed</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>20</width>
         <height>50</height>
        </size>
       </property>
      </spacer>
     </item>
     <item>
      <layout class="QHBoxLayout" name="horizontalLayout_2">
       <property name="spacing">
        <number>24</number>
       </property>
       <item>
        <spacer name="horizontalSpacer">
         <property name="orientation">
          <enum>Qt::Horizontal</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>40</width>
           <height>20</height>
          </size>
         </property>
        </spacer>
       </item>
       <item>
        <widget class="PillToolButton" name="startButton">
         <property name="minimumSize">
          <size>
           <width>68</width>
           <height>68</height>
          </size>
         </property>
         <property name="iconSize">
          <size>
           <width>21</width>
           <height>21</height>
          </size>
         </property>
         <property name="checked">
          <bool>true</bool>
         </property>
        </widget>
       </item>
       <item>
        <widget class="PillToolButton" name="flagButton">
         <property name="enabled">
          <bool>false</bool>
         </property>
         <property name="minimumSize">
          <size>
           <width>68</width>
           <height>68</height>
          </size>
         </property>
         <property name="iconSize">
          <size>
           <width>21</width>
           <height>21</height>
          </size>
         </property>
        </widget>
       </item>
       <item>
        <widget class="PillToolButton" name="restartButton">
         <property name="enabled">
          <bool>false</bool>
         </property>
         <property name="minimumSize">
          <size>
           <width>68</width>
           <height>68</height>
          </size>
         </property>
         <property name="iconSize">
          <size>
           <width>21</width>
           <height>21</height>
          </size>
         </property>
        </widget>
       </item>
       <item>
        <spacer name="horizontalSpacer_2">
         <property name="orientation">
          <enum>Qt::Horizontal</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>40</width>
           <height>20</height>
          </size>
         </property>
        </spacer>
       </item>
      </layout>
     </item>
     <item>
      <spacer name="verticalSpacer_2">
       <property name="orientation">
        <enum>Qt::Vertical</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>20</width>
         <height>40</height>
        </size>
       </property>
      </spacer>
     </item>
    </layout>
   </item>
  </layout>
 </widget>
 <customwidgets>
  <customwidget>
   <class>ToolButton</class>
   <extends>QToolButton</extends>
   <header>qfluentwidgets</header>
  </customwidget>
  <customwidget>
   <class>PillToolButton</class>
   <extends>ToggleToolButton</extends>
   <header>qfluentwidgets</header>
  </customwidget>
  <customwidget>
   <class>ToggleToolButton</class>
   <extends>ToolButton</extends>
   <header>qfluentwidgets</header>
  </customwidget>
  <customwidget>
   <class>BodyLabel</class>
   <extends>QLabel</extends>
   <header>qfluentwidgets</header>
  </customwidget>
  <customwidget>
   <class>TitleLabel</class>
   <extends>QLabel</extends>
   <header>qfluentwidgets</header>
  </customwidget>
 </customwidgets>
 <resources/>
 <connections/>
</ui>
