<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>Form</class>
 <widget class="QWidget" name="Form">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>1250</width>
    <height>809</height>
   </rect>
  </property>
  <property name="minimumSize">
   <size>
    <width>700</width>
    <height>500</height>
   </size>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <layout class="QHBoxLayout" name="horizontalLayout">
   <property name="spacing">
    <number>0</number>
   </property>
   <property name="leftMargin">
    <number>0</number>
   </property>
   <property name="topMargin">
    <number>0</number>
   </property>
   <property name="rightMargin">
    <number>0</number>
   </property>
   <property name="bottomMargin">
    <number>0</number>
   </property>
   <item>
    <widget class="QLabel" name="label">
     <property name="text">
      <string/>
     </property>
     <property name="pixmap">
      <pixmap resource="../resource.qrc">:/images/background.jpg</pixmap>
     </property>
     <property name="scaledContents">
      <bool>true</bool>
     </property>
    </widget>
   </item>
   <item>
    <widget class="QWidget" name="widget" native="true">
     <property name="sizePolicy">
      <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
       <horstretch>0</horstretch>
       <verstretch>0</verstretch>
      </sizepolicy>
     </property>
     <property name="minimumSize">
      <size>
       <width>360</width>
       <height>0</height>
      </size>
     </property>
     <property name="maximumSize">
      <size>
       <width>360</width>
       <height>16777215</height>
      </size>
     </property>
     <property name="styleSheet">
      <string notr="true">QLabel{
	font: 13px 'Microsoft YaHei'
}</string>
     </property>
     <layout class="QVBoxLayout" name="verticalLayout_2">
      <property name="spacing">
       <number>9</number>
      </property>
      <property name="leftMargin">
       <number>20</number>
      </property>
      <property name="topMargin">
       <number>20</number>
      </property>
      <property name="rightMargin">
       <number>20</number>
      </property>
      <property name="bottomMargin">
       <number>20</number>
      </property>
      <item>
       <spacer name="verticalSpacer">
        <property name="orientation">
         <enum>Qt::Vertical</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>20</width>
          <height>40</height>
         </size>
        </property>
       </spacer>
      </item>
      <item alignment="Qt::AlignHCenter">
       <widget class="QLabel" name="label_2">
        <property name="enabled">
         <bool>true</bool>
        </property>
        <property name="sizePolicy">
         <sizepolicy hsizetype="Preferred" vsizetype="Expanding">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="minimumSize">
         <size>
          <width>100</width>
          <height>100</height>
         </size>
        </property>
        <property name="maximumSize">
         <size>
          <width>100</width>
          <height>100</height>
         </size>
        </property>
        <property name="text">
         <string/>
        </property>
        <property name="pixmap">
         <pixmap resource="../resource.qrc">:/images/logo.png</pixmap>
        </property>
        <property name="scaledContents">
         <bool>true</bool>
        </property>
       </widget>
      </item>
      <item>
       <spacer name="verticalSpacer_3">
        <property name="orientation">
         <enum>Qt::Vertical</enum>
        </property>
        <property name="sizeType">
         <enum>QSizePolicy::Fixed</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>20</width>
          <height>15</height>
         </size>
        </property>
       </spacer>
      </item>
      <item>
       <layout class="QGridLayout" name="gridLayout" columnstretch="2,1">
        <property name="horizontalSpacing">
         <number>4</number>
        </property>
        <property name="verticalSpacing">
         <number>9</number>
        </property>
        <item row="1" column="0">
         <widget class="LineEdit" name="lineEdit">
          <property name="placeholderText">
           <string>ftp.example.com</string>
          </property>
          <property name="clearButtonEnabled">
           <bool>true</bool>
          </property>
         </widget>
        </item>
        <item row="0" column="0">
         <widget class="BodyLabel" name="label_3">
          <property name="text">
           <string>主机</string>
          </property>
         </widget>
        </item>
        <item row="1" column="1">
         <widget class="LineEdit" name="lineEdit_2">
          <property name="text">
           <string>21</string>
          </property>
          <property name="placeholderText">
           <string/>
          </property>
          <property name="clearButtonEnabled">
           <bool>true</bool>
          </property>
         </widget>
        </item>
        <item row="0" column="1">
         <widget class="BodyLabel" name="label_4">
          <property name="text">
           <string>端口</string>
          </property>
         </widget>
        </item>
       </layout>
      </item>
      <item>
       <widget class="BodyLabel" name="label_5">
        <property name="text">
         <string>用户名</string>
        </property>
       </widget>
      </item>
      <item>
       <widget class="LineEdit" name="lineEdit_3">
        <property name="placeholderText">
         <string><EMAIL></string>
        </property>
        <property name="clearButtonEnabled">
         <bool>true</bool>
        </property>
       </widget>
      </item>
      <item>
       <widget class="BodyLabel" name="label_6">
        <property name="text">
         <string>密码</string>
        </property>
       </widget>
      </item>
      <item>
       <widget class="LineEdit" name="lineEdit_4">
        <property name="echoMode">
         <enum>QLineEdit::Password</enum>
        </property>
        <property name="placeholderText">
         <string>••••••••••••</string>
        </property>
        <property name="clearButtonEnabled">
         <bool>true</bool>
        </property>
       </widget>
      </item>
      <item>
       <spacer name="verticalSpacer_5">
        <property name="orientation">
         <enum>Qt::Vertical</enum>
        </property>
        <property name="sizeType">
         <enum>QSizePolicy::Fixed</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>20</width>
          <height>5</height>
         </size>
        </property>
       </spacer>
      </item>
      <item>
       <widget class="CheckBox" name="checkBox">
        <property name="text">
         <string>记住密码</string>
        </property>
        <property name="checked">
         <bool>true</bool>
        </property>
       </widget>
      </item>
      <item>
       <spacer name="verticalSpacer_4">
        <property name="orientation">
         <enum>Qt::Vertical</enum>
        </property>
        <property name="sizeType">
         <enum>QSizePolicy::Fixed</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>20</width>
          <height>5</height>
         </size>
        </property>
       </spacer>
      </item>
      <item>
       <widget class="PrimaryPushButton" name="pushButton">
        <property name="text">
         <string>登录</string>
        </property>
       </widget>
      </item>
      <item>
       <spacer name="verticalSpacer_6">
        <property name="orientation">
         <enum>Qt::Vertical</enum>
        </property>
        <property name="sizeType">
         <enum>QSizePolicy::Fixed</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>20</width>
          <height>6</height>
         </size>
        </property>
       </spacer>
      </item>
      <item>
       <widget class="HyperlinkButton" name="pushButton_2">
        <property name="text">
         <string>找回密码</string>
        </property>
       </widget>
      </item>
      <item>
       <spacer name="verticalSpacer_2">
        <property name="orientation">
         <enum>Qt::Vertical</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>20</width>
          <height>40</height>
         </size>
        </property>
       </spacer>
      </item>
     </layout>
    </widget>
   </item>
  </layout>
 </widget>
 <customwidgets>
  <customwidget>
   <class>LineEdit</class>
   <extends>QLineEdit</extends>
   <header>qfluentwidgets</header>
  </customwidget>
  <customwidget>
   <class>CheckBox</class>
   <extends>QCheckBox</extends>
   <header>qfluentwidgets</header>
  </customwidget>
  <customwidget>
   <class>PrimaryPushButton</class>
   <extends>QPushButton</extends>
   <header>qfluentwidgets</header>
  </customwidget>
  <customwidget>
   <class>HyperlinkButton</class>
   <extends>QPushButton</extends>
   <header>qfluentwidgets</header>
  </customwidget>
  <customwidget>
   <class>BodyLabel</class>
   <extends>QLabel</extends>
   <header>qfluentwidgets</header>
  </customwidget>
 </customwidgets>
 <resources>
  <include location="../resource.qrc"/>
 </resources>
 <connections/>
</ui>
