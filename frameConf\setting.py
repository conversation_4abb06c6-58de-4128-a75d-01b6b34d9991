# coding: utf-8
from pathlib import Path

#from app.services.logger import Logger

# change DEBUG to False if you want to compile the code to exe
DEBUG = "__compiled__" not in globals()

YEAR = 2025
AUTHOR = "天壹财团"
APP_NAME = "Telegram Tool"
HELP_URL = "https://docs.xile188.com"
REPO_URL = "https://docs.xile188.com"
FEEDBACK_URL = "https://docs.xile188.com"
DOC_URL = "https://docs.xile188.com/#/README"

SERVER_URL = "https://server.xile188.com/"
APP_ID = "1000"
APP_KEY = "FqMsSPwgsdrp0n25FE3xaer1nQsNnyEg"
VER_INDEX = "windowns10"
VERSION = "1.3.7"

# 路径配置
CONFIG_FOLDER = Path('AppData').absolute()
CONFIG_FILE = CONFIG_FOLDER / "config.json"
DB_PATH = CONFIG_FOLDER / "telegram_manager.db"
SESSION_DIR = CONFIG_FOLDER / "sessions"
API_ID=24297563
API_HASH="79354bc5da59358b3f268c7ecb1ce332"
#LOGGER = Logger()
# import python_socks
# PROXY = (python_socks.ProxyType.SOCKS5, '127.0.0.1', 1080)

# 3proxy相关配置
PROXY_DIR = Path('3proxy/bin64').absolute()
PROXY_EXE = PROXY_DIR / "3proxy.exe"
PROXY_CONFIG = CONFIG_FOLDER / "3proxy.cfg"

# IP检测服务列表（按可靠性排序）
IP_CHECK_SERVICES = [
    "http://httpbin.org/ip",
    "http://ip.3322.net", 
    "http://myip.ipip.net",   
    "http://icanhazip.com",
    #"http://ip.sb",
    #"http://ifconfig.me",
    #"http://api.ipify.org"
]
AUTO_LOGIN_MAX_COUNT = 5
