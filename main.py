#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Telegram多账户管理系统 - 主程序入口
"""
import sys
import os
import asyncio
from PySide6.QtWidgets import QApplication
from PySide6.QtCore import QCoreApplication, Qt
from qasync import QEventLoop
# 导入配置管理和日志管理
from config import config
from utils.logger import get_logger, init_logger, set_module_level
from data.database import init_database, get_db_session

from frameConf.config import cfg
from PySide6.QtCore import QTranslator
from qfluentwidgets import FluentTranslator
from app.controllers.auth_controller import AuthController
from app.services.auth_service import AuthService
# 导入认证窗口
from ui.views.auth_view import AuthView
from ui.main_window import MainWindow   

async def init_app_modules():
    """初始化应用模块"""
    print("正在初始化数据库...")
    # 初始化数据库
    await init_database()

async def main_async():
    """异步主程序逻辑"""
    logger = get_logger("main")
    
    # 异步初始化应用模块
    await init_app_modules()
    
    auth_service = AuthService()
    auth_controller = AuthController(auth_service)
    auth_view = AuthView(auth_controller)
    
    # 创建主窗口实例但不立即显示
    main_window = None
    
    def on_auth_completed(success):
        nonlocal main_window
        if success:
            logger.info("用户登录成功，正在启动主窗口...")
            main_window = MainWindow(auth_controller)
            main_window.show()
            logger.info("主窗口已启动")
    
    auth_view.auth_completed.connect(on_auth_completed)
    auth_view.show()
    
    return auth_view, main_window

def main():
    """程序主入口"""
    # 设置应用数据目录
    
    # 初始化日志系统
    log_config = config.get_log_config()
    init_logger(
        log_dir=log_config["log_dir"],
        default_level=log_config["default_level"],
        rotation=log_config["rotation"],
        retention=log_config["retention"],
        #module_levels=module_log_levels
    )
    
    # 获取主程序日志记录器
    logger = get_logger("main")


    
    try:
        if cfg.get(cfg.dpiScale) != "Auto":
            os.environ["QT_ENABLE_HIGHDPI_SCALING"] = "0"
            os.environ["QT_SCALE_FACTOR"] = str(cfg.get(cfg.dpiScale))

        
        # 创建应用程序实例
        app = QApplication(sys.argv)
        event_loop = QEventLoop(app)
        asyncio.set_event_loop(event_loop)  # 设置异步事件循环
        #app_close_event = asyncio.Event()
        #app.aboutToQuit.connect(app_close_event.set)
        app.setAttribute(Qt.AA_DontCreateNativeWidgetSiblings)
        
        locale = cfg.get(cfg.language).value
        translator = FluentTranslator(locale)
        galleryTranslator = QTranslator()
        galleryTranslator.load(locale, "app", ".", ":/app/i18n")
        
        app.installTranslator(translator)
        app.installTranslator(galleryTranslator)
        
        app.setApplicationName("Telegram Manager")
        app.setOrganizationName("TeleManager")
        # 初始化应用模块（异步）
        proxy_controller = event_loop.run_until_complete(init_app_modules())


        # 使用异步方式初始化
        auth_view, main_window = event_loop.run_until_complete(main_async())
        
        # 运行qasync事件循环
        with event_loop:
            event_loop.run_forever()
        
        logger.info("应用程序已正常退出")
 
    except Exception as e:
        logger.critical(f"程序启动失败: {str(e)}", exc_info=True)
        raise

if __name__ == "__main__":
    main() 
