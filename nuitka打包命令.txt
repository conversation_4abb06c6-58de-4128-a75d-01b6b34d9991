用法: python.exe -m nuitka [--mode=编译模式] [--run] [选项] main_module.py

    注意：对于通用插件帮助（它们通常也有自己的
    命令行选项），请考虑查看
    '--help-plugins' 的输出。

选项:
  --help                显示此帮助信息并退出
  --version             显示版本信息和错误报告的重要详细信息，然后退出。默认关闭。
  --module              创建可导入的二进制扩展模块
                        可执行文件而不是程序。默认关闭。
  --mode=编译模式
                        编译模式。Accelerated 在您的
                        Python 安装中运行并依赖于它。Standalone
                        创建一个包含可执行文件的文件夹来运行
                        它。Onefile 创建一个单独的可执行文件来部署。App
                        是 onefile，除了在 macOS 上不使用。
                        Module 制作模块，package 还包括所有
                        子模块和子包。Dll 目前正在
                        开发中，尚未供用户使用。默认是
                        'accelerated'。
  --standalone          为输出启用独立模式。这允许您
                        将创建的二进制文件传输到其他机器而无需
                        使用现有的 Python 安装。这也
                        意味着它会变得很大。它暗示这些选项："
                        --follow-imports" 和 "--python-flag=no_site"。
                        默认关闭。
  --onefile             在独立模式之上，启用单文件模式。这
                        意味着不是文件夹，而是创建并使用压缩的可执行文件。默认关闭。
  --python-flag=标志    要使用的 Python 标志。默认是您用来
                        运行 Nuitka 的标志，这强制执行特定模式。这些是
                        标准 Python 可执行文件也存在的选项。
                        目前支持："-S"（别名 "no_site"），
                        "static_hashes"（不使用哈希随机化），
                        "no_warnings"（不给出 Python 运行时警告），
                        "-O"（别名 "no_asserts"），"no_docstrings"（不使用
                        文档字符串），"-u"（别名 "unbuffered"），"isolated"
                        （不加载外部代码），"-P"（别名 "safe_path"，
                        在模块搜索中不使用当前目录）和
                        "-m"（包模式，编译为 "package.__main__"）。
                        默认为空。
  --python-debug        是否使用调试版本。默认使用您
                        用来运行 Nuitka 的版本，很可能是非调试版本。
                        仅用于调试和测试目的。
  --python-for-scons=路径
                        使用 Python 3.4 编译时，提供用于 Scons 的
                        Python 二进制文件路径。否则 Nuitka 可以
                        使用您运行 Nuitka 的版本，或找到 Python
                        安装，例如从 Windows 注册表。在 Windows 上，
                        需要 Python 3.5 或更高版本。在非 Windows 上，
                        Python 2.6 或 2.7 也可以。
  --main=路径           如果指定一次，这将取代
                        位置参数，即要编译的文件名。
                        当多次给出时，它启用 "multidist"（见
                        用户手册），它允许您创建依赖于文件名或调用名的二进制文件。

  控制结果中模块和包的包含:
    --include-package=包名
                        包含整个包。以 Python 命名空间形式给出，
                        例如 "some_package.sub_package"，Nuitka 将
                        找到它并包含它以及在该磁盘位置下找到的所有模块
                        到它创建的二进制文件或扩展模块中，
                        并使代码可以导入它。要避免不需要的子包，例如测试，您
                        可以这样做 "--nofollow-import-to=*.tests"。
                        默认为空。
    --include-module=模块名
                        包含单个模块。以 Python 命名空间形式给出，
                        例如 "some_package.some_module"，Nuitka 将
                        找到它并将其包含在它创建的二进制文件或扩展
                        模块中，并使代码可以导入它。默认为空。
    --include-plugin-directory=模块/包
                        还包含在该目录中找到的代码，
                        将它们视为每个都作为主文件给出。
                        覆盖所有其他包含选项。您应该
                        优先使用其他按名称进行的包含选项，
                        而不是文件名，那些通过在 "sys.path" 中找到东西。
                        此选项仅适用于非常特殊的用例。可以多次给出。默认
                        为空。
    --include-plugin-files=模式
                        包含匹配模式的文件。覆盖所有
                        其他跟随选项。可以多次给出。
                        默认为空。
    --prefer-source-code
                        对于已编译的扩展模块，如果既有
                        源文件又有扩展模块，通常
                        使用扩展模块，但最好
                        从可用源代码编译模块以获得
                        最佳性能。如果不需要，有 --no-
                        prefer-source-code 来禁用相关警告。
                        默认关闭。

  控制对导入模块的跟随:
    --follow-imports    深入所有导入的模块。在独立模式下默认开启，
                        否则关闭。
    --follow-import-to=模块/包
                        如果使用，跟随到该模块，或如果是包，跟随到
                        整个包。可以多次给出。默认
                        为空。
    --nofollow-import-to=模块/包
                        即使使用也不跟随到该模块名，或如果是
                        包名，在任何情况下都不跟随到整个包，
                        覆盖所有其他选项。这也可以包含
                        模式，例如 "*.tests"。可以多次给出。
                        默认为空。
    --nofollow-imports  完全不深入任何导入的模块，
                        覆盖所有其他包含选项，不适用于
                        独立模式。默认关闭。
    --follow-stdlib     也深入从标准库导入的模块。
                        这将大大增加编译时间，
                        目前也没有经过充分测试，
                        有时不会工作。默认关闭。

  单文件选项:
    --onefile-tempdir-spec=单文件临时目录规范
                        在单文件模式下使用此作为解压缩的文件夹。
                        默认为 '{TEMP}/onefile_{PID}_{TIME}'，即用户
                        临时目录，由于是非静态的，它会被删除。
                        使用例如像
                        '{CACHE_DIR}/{COMPANY}/{PRODUCT}/{VERSION}' 这样的字符串，
                        这是一个良好的静态缓存路径，这样就不会被删除。
    --onefile-cache-mode=单文件缓存模式
                        此模式从您对规范的使用中推断。如果它
                        包含运行时依赖路径，"auto" 解析为
                        "temporary"，这将确保在执行后删除
                        解压的二进制文件，而 cached 不会
                        删除它，并在下次执行时重用其内容
                        以获得更快的启动时间。
    --onefile-child-grace-time=宽限时间毫秒
                        停止子进程时，例如由于 CTRL-C 或
                        关机等，Python 代码会收到
                        "KeyboardInterrupt"，它可以处理例如刷新
                        数据。这是在强制杀死子进程之前的时间量（毫秒）。
                        单位是毫秒，默认 5000。
    --onefile-no-compression
                        创建单文件时，禁用有效载荷的压缩。
                        这主要用于调试目的，或节省
                        时间。默认关闭。
    --onefile-as-archive
                        创建单文件时，使用存档格式，
                        可以用 "nuitka-onefile-unpack" 解压，而不是
                        只有单文件程序本身解压的流。默认关闭。
    --onefile-no-dll    创建单文件时，某些平台（目前是 Windows，
                        如果不使用缓存位置）默认
                        对 Python 代码使用 DLL 而不是可执行文件。
                        这使得它在解压文件中也使用可执行文件。默认关闭。

  数据文件:
    --include-package-data=包名
                        包含给定包名的数据文件。DLL
                        和扩展模块不是数据文件，永远不会
                        这样包含。可以使用如下所示的文件名模式。
                        包的数据文件默认不包含，但包配置可以
                        做到。这只会包含非 DLL、非扩展
                        模块，即实际的数据文件。在 ":" 后
                        可选地也可以给出文件名模式，
                        只选择匹配的文件。示例："--include-
                        package-data=package_name"（所有文件）"--include-
                        package-data=package_name:*.txt"（仅某种类型）"
                        --include-package-data=package_name:some_filename.dat
                        （具体文件）默认为空。
    --include-data-files=描述
                        在分发中按文件名包含数据文件。
                        有许多允许的形式。使用 '--include-data-
                        files=/path/to/file/*.txt=folder_name/some.txt' 它
                        将复制单个文件，如果是多个文件则会报错。
                        使用 '--include-data-
                        files=/path/to/files/*.txt=folder_name/' 它将把
                        所有匹配的文件放入该文件夹。对于递归
                        复制，有一个包含 3 个值的形式 '--include-
                        data-files=/path/to/scan=folder_name/=**/*.txt'，
                        将保留目录结构。默认为空。
    --include-data-dir=目录
                        在分发中包含完整目录的数据文件。
                        这是递归的。如果您想要非递归
                        包含，请检查带模式的 '--include-
                        data-files'。一个例子是 '--include-data-
                        dir=/path/some_dir=data/some_dir' 用于普通复制
                        整个目录。所有非代码文件都会被复制，如果
                        您想使用 '--noinclude-data-files' 选项来
                        删除它们。默认为空。
    --noinclude-data-files=模式
                        不包含匹配给定文件名模式的数据文件。
                        这是针对目标文件名，
                        不是源路径。所以要忽略来自
                        'package_name' 包数据的文件模式应该匹配为
                        'package_name/*.txt'。或者对于整个目录
                        简单地使用 'package_name'。默认为空。
    --include-onefile-external-data=模式
                        将指定的数据文件模式包含在
                        单文件二进制文件外部，而不是内部。只有
                        在 '--onefile' 编译情况下才有意义。首先
                        文件必须用其他 `--include-*data*` 选项指定为包含，
                        然后这指的是分发内部的目标路径。默认为空。
    --list-package-data=列出包数据
                        输出为给定包名找到的数据文件。
                        默认不执行。
    --include-raw-dir=目录
                        在分发中完全包含原始目录。
                        这是递归的。检查 '--include-
                        data-dir' 以使用合理的选项。默认为空。

  元数据支持:
    --include-distribution-metadata=分发名
                        包含给定分发名的元数据信息。
                        一些包检查元数据的
                        存在、版本、入口点等，如果没有给出此
                        选项，它只在编译时被识别时才工作，
                        这并不总是发生。这当然
                        只对包含在编译中的包有意义。默认为空。
    --list-distribution-metadata
                        输出所有包的分发列表及其详细信息。
                        默认不执行。

  DLL 文件:
    --noinclude-dlls=模式
                        不包含匹配给定文件名模式的 DLL 文件。
                        这是针对目标文件名，不是源
                        路径。所以要忽略包含在
                        包 'package_name' 中的 DLL 'someDLL'，应该匹配为
                        'package_name/someDLL.*'。默认为空。
    --list-package-dlls=列出包DLL
                        输出为给定包名找到的 DLL。
                        默认不执行。
    --list-package-exe=列出包EXE
                        输出为给定包名找到的 EXE。
                        默认不执行。

  控制 Nuitka 给出的警告:
    --warn-implicit-exceptions
                        启用对编译时检测到的隐式异常的警告。
    --warn-unusual-code
                        启用对编译时检测到的异常代码的警告。
    --assume-yes-for-downloads
                        如果需要，允许 Nuitka 下载外部代码，
                        例如依赖项遍历器、ccache，甚至在
                        Windows 上的 gcc。要禁用，从空设备重定向输入，
                        例如 "</dev/null" 或 "<NUL:"。默认是提示。
    --nowarn-mnemonic=助记符
                        禁用给定助记符的警告。这些是为了
                        确保您了解某些主题，
                        通常指向 Nuitka 网站。助记符是
                        URL 末尾的部分，不包括 HTML
                        后缀。可以多次给出并接受 shell
                        模式。默认为空。

  编译后立即执行:
    --run               立即执行创建的二进制文件（或导入
                        编译的模块）。默认关闭。
    --debugger          在调试器内执行，例如 "gdb" 或 "lldb" 以
                        自动获取堆栈跟踪。调试器是
                        自动选择的，除非通过 NUITKA_DEBUGGER_CHOICE
                        环境变量按名称指定。默认关闭。

  编译选择:
    --user-package-configuration-file=YAML文件名
                        用户提供的包含包配置的 Yaml 文件。
                        您可以包含 DLL、删除冗余、添加隐藏
                        依赖项。查看 Nuitka 包配置
                        手册以获取要使用的格式的完整描述。
                        可以多次给出。默认为空。
    --full-compat       强制与 CPython 绝对兼容。甚至不
                        允许与 CPython 行为的轻微偏差，
                        例如没有更好的回溯或异常
                        消息，这些实际上并不不兼容，只是
                        不同或更差。这仅用于测试，
                        *不应该*使用。
    --file-reference-choice=文件模式
                        选择 "__file__" 的值。使用
                        "runtime"（独立二进制模式和
                        模块模式的默认值），创建的二进制文件和模块使用
                        它们自己的位置来推断 "__file__" 的值。
                        包含的包假装在该位置下的
                        目录中。这允许您在部署中
                        包含数据文件。如果您只是寻求
                        加速，最好使用
                        "original" 值，其中将使用源文件位置。
                        使用 "frozen" 时使用 "<frozen
                        module_name>" 符号。出于兼容性原因，
                        "__file__" 值将始终具有 ".py" 后缀，
                        无论它实际是什么。
    --module-name-choice=模块名模式
                        选择 "__name__" 和 "__package__" 的值。
                        使用 "runtime"（模块模式的默认值），
                        创建的模块使用父包来推断
                        "__package__" 的值，以完全兼容。
                        值 "original"（其他模式的默认值）允许
                        发生更多静态优化，但对于
                        通常可以加载到任何包中的模块不兼容。

  输出选择:
    --output-filename=文件名
                        指定可执行文件的命名方式。对于
                        扩展模块没有选择，独立模式也没有，
                        使用它将是错误的。这
                        可能包括需要存在的路径信息。
                        在此平台上默认为 '<program_name>.exe'。
    --output-dir=目录
                        指定中间和最终输出文件
                        应该放在哪里。目录将填充
                        构建文件夹、分发文件夹、二进制文件等。默认为
                        当前目录。
    --remove-output     在生成模块或 exe 文件后删除构建目录。
                        默认关闭。
    --no-pyi-file       不为 Nuitka 创建的扩展模块
                        创建 '.pyi' 文件。这用于检测隐式
                        导入。默认关闭。
    --no-pyi-stubs      为 Nuitka 创建的扩展模块创建 '.pyi' 文件时
                        不使用 stubgen。它们暴露您的
                        API，但 stubgen 可能会导致问题。默认关闭。

  部署控制:
    --deployment        禁用旨在使查找兼容性
                        问题更容易的代码。这将例如阻止使用
                        "-c" 参数执行，这通常被尝试
                        运行模块的代码使用，并可能导致程序
                        一遍又一遍地启动自己。一旦您
                        部署给最终用户就禁用，对于查找典型问题，这
                        在开发期间非常有用。默认关闭。
    --no-deployment-flag=标志
                        保持部署模式，但有选择地禁用其
                        部分。部署模式的错误将输出这些
                        标识符。默认为空。

  环境控制:
    --force-runtime-environment-variable=变量规范
                        强制环境变量为给定值。
                        默认为空。

  调试功能:
    --debug             执行所有可能的自检以查找 Nuitka 中的错误，
                        不要用于生产。默认关闭。
    --no-debug-immortal-assumptions
                        禁用通常使用 "--debug" 进行的检查。对于
                        Python3.12+ 不检查已知的不朽对象
                        假设。一些 C 库会破坏它们。默认
                        如果 "--debug" 开启则进行检查。
    --no-debug-c-warnings
                        禁用通常使用 "--debug" 进行的检查。C
                        编译可能产生警告，对于某些包来说经常如此，
                        但这些并不是问题，特别是对于
                        未使用的值。
    --unstripped        在结果对象文件中保留调试信息以
                        更好地与调试器交互。默认关闭。
    --profile           启用基于 vmprof 的时间消耗分析。
                        目前不工作。默认关闭。
    --trace-execution   跟踪执行输出，在执行前输出代码行。
                        默认关闭。
    --xml=XML文件名     将内部程序结构、优化结果以 XML 形式
                        写入给定文件名。
    --experimental=标志
                        使用声明为 '实验性' 的功能。如果代码中
                        没有实验性功能，可能没有效果。
                        每个实验功能使用秘密标签（检查源代码）。
    --low-memory        尝试使用更少内存，通过减少 C
                        编译作业分叉和使用占用更少
                        内存的选项。用于嵌入式机器。在
                        内存不足问题时使用。默认关闭。
    --create-environment-from-report=从报告创建环境
                        从给定的报告文件在该不存在的路径中创建新的 virtualenv，
                        例如 '--report=compilation-
                        report.xml'。默认不执行。
    --generate-c-only   仅生成 C 源代码，不将其编译为
                        二进制文件或模块。这用于调试和代码
                        覆盖率分析，不浪费 CPU。默认
                        关闭。不要认为您可以直接使用这个。

  Nuitka 开发功能:
    --devel-missing-code-helpers
                        报告尝试但不存在的类型代码助手的警告。
                        这有助于识别改进从未使用的类型知识
                        生成代码优化的机会。默认 False。
    --devel-missing-trust
                        报告可以信任但目前不信任的导入的警告。
                        这是为了识别改进硬模块处理的机会，
                        有时这可以允许更多静态优化。
                        默认 False。
    --devel-recompile-c-only
                        这不是增量编译，而是仅用于 Nuitka
                        开发。获取现有文件并在执行 Python 步骤后
                        简单地再次将它们编译为 C。
                        允许编译编辑的 C 文件以手动调试
                        对生成源的更改。允许我们添加
                        打印、检查和打印值，但这不是
                        用户想要的。依赖于编译 Python 源
                        来确定应该查看哪些文件。
    --devel-internal-graph
                        创建优化过程内部图，不要
                        用于整个程序，只用于小测试用例。
                        默认关闭。

  后端 C 编译器选择:
    --clang             强制使用 clang。在 Windows 上这需要
                        工作的 Visual Studio 版本来依靠。
                        默认关闭。
    --mingw64           在 Windows 上强制使用 MinGW64。默认关闭，
                        除非使用带有 MinGW Python 的 MSYS2。
    --msvc=MSVC版本
                        在 Windows 上强制使用特定的 MSVC 版本。
                        允许的值例如 "14.3"（MSVC 2022）和其他
                        MSVC 版本号，指定 "list" 获取已安装
                        编译器列表，或使用 "latest"。默认
                        如果安装则使用最新的 MSVC，否则使用 MinGW64。
    --jobs=N            指定允许的并行 C 编译器
                        作业数。负值是系统 CPU 减去给定
                        值。默认为完整系统 CPU 计数，除非
                        激活低内存模式，则默认为 1。
    --lto=选择          使用链接时优化（MSVC、gcc、clang）。
                        允许的值是 "yes"、"no" 和 "auto"（当已知
                        工作时）。默认为 "auto"。
    --static-libpython=选择
                        使用 Python 的静态链接库。允许的值是
                        "yes"、"no" 和 "auto"（当已知工作时）。
                        默认为 "auto"。
    --cf-protection=保护模式
                        此选项是 gcc 特定的。对于 gcc 编译器，
                        选择 "cf-protection" 模式。默认 "auto" 是
                        使用 gcc 默认值，但您可以覆盖它，
                        例如用 "none" 值禁用它。有关详细信息，
                        请参阅 gcc 文档中的 "-fcf-protection"。

  缓存控制:
    --disable-cache=禁用缓存
                        禁用选定的缓存，指定 "all" 表示所有缓存。
                        目前允许的值是：
                        "all"、"ccache"、"bytecode"、"compression"、"dll-
                        dependencies"。可以多次给出或使用
                        逗号分隔的值。默认无。
    --clean-cache=清理缓存
                        在执行前清理给定的缓存，指定 "all"
                        表示所有缓存。目前允许的值是：
                        "all"、"ccache"、"bytecode"、"compression"、"dll-
                        dependencies"。可以多次给出或使用
                        逗号分隔的值。默认无。
    --force-dll-dependency-cache-update
                        强制更新依赖项遍历器缓存。将
                        导致创建分发文件夹的时间大大延长，
                        但在缓存可能导致错误或已知需要更新时
                        可能会使用。

  PGO 编译选择:
    --pgo-c             启用 C 级配置文件引导优化（PGO），通过
                        首先执行专用构建进行分析运行，
                        然后使用结果反馈到 C
                        编译中。注意：这是实验性的，尚未
                        与 Nuitka 的独立模式一起工作。默认
                        关闭。
    --pgo-args=PGO参数
                        在配置文件引导优化情况下要传递的参数。
                        这些在 PGO 分析运行期间传递给特殊构建的
                        可执行文件。默认
                        为空。
    --pgo-executable=PGO可执行文件
                        收集配置文件信息时要执行的命令。
                        仅在需要通过准备运行的脚本启动时使用。
                        默认使用创建的程序。

  跟踪功能:
    --report=报告文件名
                        在 XML 输出文件中报告模块、数据文件、编译、插件等
                        详细信息。这对于问题报告也非常
                        有用。这些报告例如可以
                        与 '--create-environment-from-report' 一起使用来轻松重新创建环境，
                        但包含大量信息。默认关闭。
    --report-diffable   以可比较的形式报告数据，即没有因运行而异的
                        时间或内存使用值。默认
                        关闭。
    --report-user-provided=键值
                        报告来自您的数据。这可以多次给出，
                        可以是 'key=value' 形式的任何内容，其中 key 应该
                        是标识符，例如使用 '--report-user-
                        provided=pipenv-lock-hash=64a5e4' 来跟踪一些输入
                        值。默认为空。
    --report-template=报告描述
                        通过模板报告。提供模板和输出
                        文件名 'template.rst.j2:output.rst'。对于内置
                        模板，请查看用户手册了解这些是什么。
                        可以多次给出。默认为空。
    --quiet             禁用所有信息输出，但显示警告。
                        默认关闭。
    --show-scons        使用详细信息运行 C 构建后端 Scons，
                        显示执行的命令、检测到的编译器。默认关闭。
    --no-progressbar    禁用进度条。默认关闭。
    --show-progress     过时：提供进度信息和统计。
                        禁用正常进度条。默认关闭。
    --show-memory       提供内存信息和统计。默认
                        关闭。
    --show-modules      提供包含的模块和 DLL 的信息
                        过时：您应该使用 '--report' 文件代替。
                        默认关闭。
    --show-modules-output=路径
                        '--show-modules' 的输出位置，应该是
                        文件名。默认是标准输出。
    --verbose           输出所采取行动的详细信息，特别是在
                        优化中。可能会很多。默认关闭。
    --verbose-output=路径
                        '--verbose' 的输出位置，应该是
                        文件名。默认是标准输出。

  通用操作系统控制:
    --force-stdout-spec=强制标准输出规范
                        强制程序的标准输出到此
                        位置。对于禁用控制台的程序
                        和使用 Nuitka 商业版 Windows 服务插件的程序很有用。
                        默认不激活，使用例如
                        '{PROGRAM_BASE}.out.txt'，即程序附近的文件，
                        查看用户手册获取可用值的完整列表。
    --force-stderr-spec=强制标准错误规范
                        强制程序的标准错误到此
                        位置。对于禁用控制台的程序
                        和使用 Nuitka 商业版 Windows 服务插件的程序很有用。
                        默认不激活，使用例如
                        '{PROGRAM_BASE}.err.txt'，即程序附近的文件，
                        查看用户手册获取可用值的完整列表。

  Windows 特定控制:
    --windows-console-mode=控制台模式
                        选择要使用的控制台模式。默认模式是 'force'，
                        除非程序从控制台启动，否则创建控制台窗口。
                        使用 'disable' 时完全不创建或使用控制台。
                        使用 'attach' 时将使用现有控制台进行输出。
                        使用 'hide' 时新生成的控制台将被隐藏，
                        已存在的控制台将表现得像 'force'。默认是 'force'。
    --windows-icon-from-ico=图标路径
                        添加可执行文件图标。可以为不同分辨率或
                        包含多个图标的文件多次给出。
                        在后一种情况下，您也可以用
                        #<n> 作为后缀，其中 n 是从 1 开始的整数索引，
                        指定要包含的特定图标，忽略所有其他图标。
    --windows-icon-from-exe=图标EXE路径
                        从此现有可执行文件复制可执行文件图标
                        （仅限 Windows）。
    --onefile-windows-splash-screen-image=启动画面图像
                        为 Windows 和单文件编译时，在加载应用程序时
                        显示此图像。默认关闭。
    --windows-uac-admin
                        请求 Windows 用户控制，在执行时授予管理员权限。
                        （仅限 Windows）。默认关闭。
    --windows-uac-uiaccess
                        请求 Windows 用户控制，强制仅从少数文件夹运行，
                        远程桌面访问。（仅限 Windows）。默认关闭。

  macOS 特定控制:
    --macos-create-app-bundle
                        为 macOS 编译时，创建包而不是
                        普通的二进制应用程序。这是
                        解锁禁用控制台、获得高 DPI
                        图形等的唯一方法，并暗示独立模式。默认
                        关闭。
    --macos-target-arch=MACOS目标架构
                        这应该在什么架构上运行。
                        默认和限制是运行的 Python 允许的。
                        默认是 "native"，即 Python 运行的架构。
    --macos-app-icon=图标路径
                        为应用程序包添加要使用的图标。只能
                        给出一次。如果可用，默认为 Python 图标。
    --macos-signed-app-name=MACOS签名应用名称
                        用于 macOS 签名的应用程序名称。
                        遵循 "com.YourCompany.AppName" 命名结果以获得
                        最佳结果，因为这些必须是全局唯一的，
                        并可能授予受保护的 API 访问权限。
    --macos-app-name=MACOS应用名称
                        在 macOS 包信息中使用的产品名称。
                        默认为二进制文件的基本文件名。
    --macos-app-mode=应用模式
                        应用程序包的应用程序模式。当
                        启动窗口并希望出现在 Docker 中时，
                        默认值 "gui" 是一个好选择。如果从不
                        有窗口，应用程序是 "background"
                        应用程序。对于稍后显示的 UI 元素，
                        "ui-element" 介于两者之间。应用程序
                        不会出现在 dock 中，但在稍后打开窗口时
                        可以完全访问桌面。
    --macos-prohibit-multiple-instances
                        对于应用程序包，设置标志
                        "LSMultipleInstancesProhibited" 以防止启动
                        应用程序的多个实例。默认关闭。
    --macos-sign-identity=MACOS应用版本
                        在 macOS 上签名时，默认将使用临时身份，
                        但使用此选项您可以指定
                        要使用的另一个身份。代码签名现在在 macOS 上
                        是强制性的，无法禁用。使用 "auto"
                        检测您安装的唯一身份。如果未给出，默认 "ad-
                        hoc"。
    --macos-sign-notarization
                        为公证签名时，使用来自 Apple 的适当 TeamID
                        身份，使用所需的运行时签名
                        选项，以便可以被接受。
    --macos-app-version=MACOS应用版本
                        在 macOS 包信息中使用的产品版本。
                        如果未给出，默认为 "1.0"。
    --macos-app-protected-resource=资源描述
                        请求访问 macOS 受保护资源的权限，
                        例如
                        "NSMicrophoneUsageDescription:Microphone access for
                        recording audio." 请求访问麦克风
                        并为用户提供信息文本，说明
                        为什么需要这样做。冒号前是访问权限的 OS 标识符，
                        然后是信息文本。合法
                        值可以在 https://developer.apple.com/doc
                        umentation/bundleresources/information_property_list/p
                        rotected_resources 找到，该选项可以
                        多次指定。默认为空。

  Linux 特定控制:
    --linux-icon=图标路径
                        为单文件二进制文件添加要使用的可执行文件图标。只能
                        给出一次。如果可用，默认为 Python 图标。

  二进制版本信息:
    --company-name=公司名称
                        在版本信息中使用的公司名称。
                        默认未使用。
    --product-name=产品名称
                        在版本信息中使用的产品名称。
                        默认为二进制文件的基本文件名。
    --file-version=文件版本
                        在版本信息中使用的文件版本。必须是
                        最多 4 个数字的序列，例如 1.0 或 *******，
                        不允许更多数字，不允许字符串。
                        默认未使用。
    --product-version=产品版本
                        在版本信息中使用的产品版本。与
                        文件版本相同的规则。默认未使用。
    --file-description=文件描述
                        在版本信息中使用的文件描述。
                        目前仅限 Windows。默认为二进制
                        文件名。
    --copyright=版权文本
                        在版本信息中使用的版权。目前仅限 Windows/macOS。
                        默认不存在。
    --trademarks=商标文本
                        在版本信息中使用的商标。目前仅限 Windows/macOS。
                        默认不存在。

  插件控制:
    --enable-plugins=插件名称
                        启用的插件。必须是插件名称。使用 '--plugin-
                        list' 查询完整列表并退出。默认为空。
    --disable-plugins=插件名称
                        禁用的插件。必须是插件名称。使用 '--
                        plugin-list' 查询完整列表并退出。大多数
                        标准插件不建议禁用。
                        默认为空。
    --user-plugin=路径  用户插件的文件名。可以多次给出。
                        默认为空。
    --plugin-list       显示所有可用插件的列表并退出。默认
                        关闭。
    --plugin-no-detection
                        插件可以检测它们是否可能被使用，您
                        可以通过 "--disable-plugin=plugin-
                        that-warned" 禁用警告，或者您可以使用此选项
                        完全禁用该机制，这当然也会稍微加快
                        编译速度，因为一旦您确定要使用哪些插件，
                        这个检测代码就会白白运行。默认关闭。
    --module-parameter=模块参数
                        提供模块参数。一些包要求您
                        提供额外的决定。格式目前是
                        --module-parameter=module.name-option-
                        name=value 默认为空。
    --show-source-changes=显示源代码更改
                        在编译前显示对原始 Python 文件内容的源代码更改。
                        主要用于开发插件和 Nuitka 包配置。
                        使用例如 '--show-source-changes=numpy.**' 查看
                        给定命名空间下的所有更改，或使用 '*' 查看所有内容，
                        这可能会很多。默认为空。

  交叉编译:
    --target=目标描述
                        交叉编译目标。高度实验性且正在
                        开发中，尚不应该工作。我们正在
                        开发 '--target=wasi'，目前没有其他。

  'anti-bloat' 插件选项（类别：核心）:
    --show-anti-bloat-changes
                        注释插件所做的更改。
    --noinclude-setuptools-mode=不包含SETUPTOOLS模式
                        遇到 'setuptools' 导入时该怎么办。
                        这个包可能很大且有依赖项，应该
                        绝对避免。也处理 'setuptools_scm'。
    --noinclude-pytest-mode=不包含PYTEST模式
                        遇到 'pytest' 导入时该怎么办。这个
                        包可能很大且有依赖项，应该
                        绝对避免。也处理 'nose' 导入。
    --noinclude-unittest-mode=不包含UNITTEST模式
                        遇到 unittest 导入时该怎么办。这个
                        包可能很大且有依赖项，应该
                        绝对避免。
    --noinclude-pydoc-mode=不包含PYDOC模式
                        遇到 pydoc 导入时该怎么办。这个
                        包的使用是部署中无用代码的标志，
                        应该避免。
    --noinclude-IPython-mode=不包含IPYTHON模式
                        遇到 IPython 导入时该怎么办。这个
                        包可能很大且有依赖项，应该
                        绝对避免。
    --noinclude-dask-mode=不包含DASK模式
                        遇到 'dask' 导入时该怎么办。这个
                        包可能很大且有依赖项，应该
                        绝对避免。
    --noinclude-numba-mode=不包含NUMBA模式
                        遇到 'numba' 导入时该怎么办。这个
                        包可能很大且有依赖项，目前
                        不适用于独立模式。这个包很大且有
                        依赖项，应该绝对避免。
    --noinclude-default-mode=不包含默认模式
                        这实际上为上述选项提供默认的 "warning" 值，
                        可以用来打开所有这些选项。
    --noinclude-custom-mode=自定义选择
                        遇到特定导入时该怎么办。格式是
                        模块名称，可以且应该是顶级
                        包，然后是一个选择，"error"、"warning"、
                        "nofollow"，例如 PyQt5:error。

  'playwright' 插件选项（类别：包支持）:
    --playwright-include-browser=包含浏览器
                        按名称包含的 Playwright 浏览器。可以
                        多次指定。使用 "all" 包含所有
                        已安装的浏览器，或使用 "none" 排除所有
                        浏览器。

  'spacy' 插件选项（类别：包支持）:
    --spacy-language-model=包含语言模型
                        要使用的 Spacy 语言模型。可以
                        多次指定。使用 'all' 包含所有下载的
                        模型。