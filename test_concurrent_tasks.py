#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试任务执行器的并发处理能力

验证：
1. 多个立即执行任务是否能并发运行
2. 每个任务是否返回不同的结果
3. 任务执行的独立性
"""

import sys
import asyncio
import time
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from PySide6.QtWidgets import QApplication
from PySide6.QtCore import QTimer

from core.task_manager.task_executor import BaseTask, TaskCategory, TaskPriority
from core.task_manager.task_service import task_service
from utils.logger import get_logger

logger = get_logger(__name__)


class TestConcurrentTask(BaseTask):
    """测试并发任务"""
    
    def __init__(self, task_name: str, delay_seconds: int = 1, **kwargs):
        super().__init__(
            name=f"并发测试_{task_name}",
            category=TaskCategory.SYSTEM,
            priority=TaskPriority.NORMAL,
            description=f"测试并发任务 {task_name}，延迟 {delay_seconds} 秒",
            **kwargs
        )
        self.task_name = task_name
        self.delay_seconds = delay_seconds
        self.start_time = None
        self.end_time = None
        
    async def execute(self, context=None):
        """执行测试任务"""
        self.start_time = time.time()
        self.log(f"任务 {self.task_name} 开始执行")
        self.update_progress(0, 100, "开始执行")
        
        # 模拟耗时操作
        for i in range(10):
            await asyncio.sleep(self.delay_seconds / 10)
            progress = (i + 1) * 10
            self.update_progress(progress, 100, f"执行中 {progress}%")
            self.log(f"任务 {self.task_name} 进度: {progress}%")
        
        self.end_time = time.time()
        execution_time = self.end_time - self.start_time
        
        result = {
            "task_name": self.task_name,
            "start_time": self.start_time,
            "end_time": self.end_time,
            "execution_time": execution_time,
            "delay_seconds": self.delay_seconds,
            "success": True
        }
        
        self.log(f"任务 {self.task_name} 完成，耗时: {execution_time:.2f}秒")
        return result


class ConcurrentTaskTester:
    """并发任务测试器"""
    
    def __init__(self):
        self.submitted_tasks = []
        self.completed_tasks = []
        self.failed_tasks = []
        
        # 连接信号
        task_service.task_completed.connect(self.on_task_completed)
        task_service.task_failed.connect(self.on_task_failed)
        
    def on_task_completed(self, task_id: str, result):
        """任务完成回调"""
        self.completed_tasks.append({
            "task_id": task_id,
            "result": result,
            "timestamp": time.time()
        })
        logger.info(f"任务完成: {task_id}, 结果: {result}")
        
    def on_task_failed(self, task_id: str, error: str):
        """任务失败回调"""
        self.failed_tasks.append({
            "task_id": task_id,
            "error": error,
            "timestamp": time.time()
        })
        logger.error(f"任务失败: {task_id}, 错误: {error}")
        
    async def test_concurrent_execution(self, num_tasks: int = 5):
        """测试并发执行"""
        logger.info(f"开始测试 {num_tasks} 个并发任务")
        
        # 记录开始时间
        test_start_time = time.time()
        
        # 提交多个任务
        for i in range(num_tasks):
            task = TestConcurrentTask(
                task_name=f"Task_{i+1}",
                delay_seconds=2  # 每个任务耗时2秒
            )
            
            task_id = task_service.submit_immediate_task(task)
            self.submitted_tasks.append({
                "task_id": task_id,
                "task_name": f"Task_{i+1}",
                "submit_time": time.time()
            })
            
            logger.info(f"提交任务: {task_id} (Task_{i+1})")
        
        # 等待所有任务完成
        timeout = 30  # 30秒超时
        while len(self.completed_tasks) + len(self.failed_tasks) < num_tasks:
            if time.time() - test_start_time > timeout:
                logger.error("测试超时")
                break
            await asyncio.sleep(0.1)
        
        test_end_time = time.time()
        total_test_time = test_end_time - test_start_time
        
        # 分析结果
        self.analyze_results(total_test_time)
        
    def analyze_results(self, total_test_time: float):
        """分析测试结果"""
        logger.info("=" * 50)
        logger.info("并发测试结果分析")
        logger.info("=" * 50)
        
        logger.info(f"提交任务数: {len(self.submitted_tasks)}")
        logger.info(f"完成任务数: {len(self.completed_tasks)}")
        logger.info(f"失败任务数: {len(self.failed_tasks)}")
        logger.info(f"总测试时间: {total_test_time:.2f}秒")
        
        if self.completed_tasks:
            # 分析执行时间
            execution_times = []
            start_times = []
            end_times = []
            
            for completed in self.completed_tasks:
                result = completed["result"]
                if isinstance(result, dict):
                    execution_times.append(result.get("execution_time", 0))
                    start_times.append(result.get("start_time", 0))
                    end_times.append(result.get("end_time", 0))
            
            if execution_times:
                avg_execution_time = sum(execution_times) / len(execution_times)
                min_execution_time = min(execution_times)
                max_execution_time = max(execution_times)
                
                logger.info(f"平均执行时间: {avg_execution_time:.2f}秒")
                logger.info(f"最短执行时间: {min_execution_time:.2f}秒")
                logger.info(f"最长执行时间: {max_execution_time:.2f}秒")
                
                # 检查并发性
                if start_times and end_times:
                    earliest_start = min(start_times)
                    latest_start = max(start_times)
                    earliest_end = min(end_times)
                    latest_end = max(end_times)
                    
                    concurrent_window = latest_start - earliest_start
                    total_span = latest_end - earliest_start
                    
                    logger.info(f"任务启动时间窗口: {concurrent_window:.2f}秒")
                    logger.info(f"总执行时间跨度: {total_span:.2f}秒")
                    
                    if concurrent_window < 1.0:  # 如果任务在1秒内都启动了
                        logger.info("✅ 任务确实是并发执行的")
                    else:
                        logger.warning("⚠️ 任务可能不是完全并发执行的")
                        
                    # 理论上，如果是串行执行，总时间应该是所有任务执行时间的总和
                    # 如果是并发执行，总时间应该接近单个任务的执行时间
                    theoretical_serial_time = sum(execution_times)
                    if total_test_time < theoretical_serial_time * 0.7:
                        logger.info("✅ 确认任务是并发执行的（总时间远小于串行执行时间）")
                    else:
                        logger.warning("⚠️ 任务可能是串行执行的")
        
        # 显示每个任务的详细结果
        logger.info("\n任务详细结果:")
        for completed in self.completed_tasks:
            result = completed["result"]
            if isinstance(result, dict):
                logger.info(f"  {result.get('task_name')}: "
                          f"耗时 {result.get('execution_time', 0):.2f}秒")


async def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 启动任务服务
    await task_service.start_service()
    
    # 等待任务服务完全启动
    await asyncio.sleep(1)
    
    # 创建测试器
    tester = ConcurrentTaskTester()
    
    logger.info("开始并发任务测试...")
    
    # 测试1: 5个并发任务
    await tester.test_concurrent_execution(5)
    
    # 等待一段时间再进行下一个测试
    await asyncio.sleep(2)
    
    # 重置测试器
    tester.completed_tasks.clear()
    tester.failed_tasks.clear()
    tester.submitted_tasks.clear()
    
    # 测试2: 10个并发任务
    logger.info("\n" + "="*50)
    logger.info("开始第二轮测试（10个并发任务）")
    logger.info("="*50)
    await tester.test_concurrent_execution(10)
    
    # 停止任务服务
    task_service.stop_service()
    
    logger.info("测试完成")


if __name__ == "__main__":
    # 设置事件循环策略（Windows需要）
    if sys.platform == "win32":
        asyncio.set_event_loop_policy(asyncio.WindowsProactorEventLoopPolicy())
    
    # 运行测试
    asyncio.run(main())
