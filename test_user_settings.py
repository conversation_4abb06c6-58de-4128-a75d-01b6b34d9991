#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
用户设置功能测试脚本
用于验证单个用户设置和批量用户设置功能是否正常工作
"""

import sys
import asyncio
from typing import Dict, Any, List

# 模拟测试数据
class MockAccountController:
    """模拟账户控制器"""
    
    def __init__(self):
        self.accounts = [
            {
                'id': 1,
                'phone': '+**********',
                'username': 'user1',
                'first_name': '<PERSON>',
                'last_name': '<PERSON><PERSON>',
                'bio': 'Test user 1',
                'group_id': 1,
                'proxy_type': 'none',
                'proxy_id': None,
                'daily_msg_limit': 10,
                'daily_invite_limit': 5
            },
            {
                'id': 2,
                'phone': '+**********',
                'username': 'user2',
                'first_name': '<PERSON>',
                'last_name': '<PERSON>',
                'bio': 'Test user 2',
                'group_id': 2,
                'proxy_type': 'ip_pool',
                'proxy_id': 1,
                'daily_msg_limit': 15,
                'daily_invite_limit': 8
            }
        ]
        
        self.proxy_ips = [
            {
                'id': 1,
                'ip': '*************',
                'port': 8080,
                'username': 'proxy_user',
                'password': 'proxy_pass',
                'account_count': 1
            },
            {
                'id': 2,
                'ip': '*************',
                'port': 8080,
                'username': 'proxy_user2',
                'password': 'proxy_pass2',
                'account_count': 0
            }
        ]
    
    async def update_account(self, account_id: int, update_data: Dict[str, Any]) -> bool:
        """模拟更新单个账户"""
        print(f"更新账户 {account_id}: {update_data}")
        
        # 查找账户
        account = next((acc for acc in self.accounts if acc['id'] == account_id), None)
        if not account:
            print(f"账户 {account_id} 不存在")
            return False
        
        # 更新账户信息
        for key, value in update_data.items():
            if key in account:
                account[key] = value
                print(f"  {key}: {account[key]} -> {value}")
        
        print("单个账户更新成功")
        return True
    
    async def batch_update_profiles(self, account_ids: List[int], profile_data: Dict[str, Any]) -> tuple:
        """模拟批量更新账户"""
        print(f"批量更新账户 {account_ids}: {profile_data}")
        
        success_count = 0
        fail_count = 0
        
        for account_id in account_ids:
            account = next((acc for acc in self.accounts if acc['id'] == account_id), None)
            if account:
                # 更新账户信息
                for key, value in profile_data.items():
                    if key in account:
                        account[key] = value
                        print(f"  账户 {account_id} {key}: {value}")
                success_count += 1
            else:
                print(f"账户 {account_id} 不存在")
                fail_count += 1
        
        message = f"批量更新完成: 成功 {success_count} 个, 失败 {fail_count} 个"
        print(message)
        return success_count, fail_count, message
    
    async def get_proxy_ips(self) -> List[Dict[str, Any]]:
        """模拟获取代理IP列表"""
        print("获取代理IP列表")
        return self.proxy_ips

def test_proxy_settings_processing():
    """测试代理设置处理逻辑"""
    print("=== 测试代理设置处理逻辑 ===")
    
    # 模拟 AccountView 的代理处理方法
    def process_proxy_settings(proxy_settings):
        if not proxy_settings:
            return {}
            
        proxy_type = proxy_settings.get('type')
        result = {}
        
        if proxy_type == 'none':
            result['proxy_type'] = 'none'
            result['proxy_id'] = None
        elif proxy_type == 'system':
            result['proxy_type'] = 'system'
            result['proxy_id'] = None
        elif proxy_type == 'ip_pool':
            result['proxy_type'] = 'ip_pool'
            proxy_id = proxy_settings.get('id')
            if proxy_id:
                result['proxy_id'] = proxy_id
            else:
                print("警告: IP池代理设置缺少代理ID")
                result['proxy_id'] = None
        
        return result
    
    # 测试不同的代理设置
    test_cases = [
        {"type": "none"},
        {"type": "system"},
        {"type": "ip_pool", "id": 1, "ip": "*************", "port": 8080},
        {"type": "ip_pool", "ip": "*************", "port": 8080},  # 缺少ID
        None
    ]
    
    for i, proxy_settings in enumerate(test_cases):
        print(f"\n测试用例 {i+1}: {proxy_settings}")
        result = process_proxy_settings(proxy_settings)
        print(f"处理结果: {result}")

async def test_single_user_update():
    """测试单个用户更新"""
    print("\n=== 测试单个用户更新 ===")
    
    controller = MockAccountController()
    
    # 模拟单个用户更新
    changed_data = {
        'first_name': 'Updated John',
        'bio': 'Updated bio',
        'proxy': {'type': 'ip_pool', 'id': 2, 'ip': '*************', 'port': 8080}
    }
    
    # 处理代理设置
    def process_proxy_settings(proxy_settings):
        if not proxy_settings:
            return {}
            
        proxy_type = proxy_settings.get('type')
        result = {}
        
        if proxy_type == 'none':
            result['proxy_type'] = 'none'
            result['proxy_id'] = None
        elif proxy_type == 'system':
            result['proxy_type'] = 'system'
            result['proxy_id'] = None
        elif proxy_type == 'ip_pool':
            result['proxy_type'] = 'ip_pool'
            proxy_id = proxy_settings.get('id')
            if proxy_id:
                result['proxy_id'] = proxy_id
            else:
                print("警告: IP池代理设置缺少代理ID")
                result['proxy_id'] = None
        
        return result
    
    proxy_settings = changed_data.get('proxy')
    if proxy_settings:
        changed_data.update(process_proxy_settings(proxy_settings))
        changed_data.pop('proxy', None)
    
    # 执行更新
    success = await controller.update_account(1, changed_data)
    print(f"更新结果: {'成功' if success else '失败'}")

async def test_batch_user_update():
    """测试批量用户更新"""
    print("\n=== 测试批量用户更新 ===")
    
    controller = MockAccountController()
    
    # 模拟批量用户更新
    changed_data = {
        'bio': 'Batch updated bio',
        'daily_msg_limit': 20,
        'proxy': {'type': 'system'}
    }
    
    # 处理代理设置
    def process_proxy_settings(proxy_settings):
        if not proxy_settings:
            return {}
            
        proxy_type = proxy_settings.get('type')
        result = {}
        
        if proxy_type == 'none':
            result['proxy_type'] = 'none'
            result['proxy_id'] = None
        elif proxy_type == 'system':
            result['proxy_type'] = 'system'
            result['proxy_id'] = None
        elif proxy_type == 'ip_pool':
            result['proxy_type'] = 'ip_pool'
            proxy_id = proxy_settings.get('id')
            if proxy_id:
                result['proxy_id'] = proxy_id
            else:
                print("警告: IP池代理设置缺少代理ID")
                result['proxy_id'] = None
        
        return result
    
    proxy_settings = changed_data.get('proxy')
    if proxy_settings:
        changed_data.update(process_proxy_settings(proxy_settings))
        changed_data.pop('proxy', None)
    
    # 执行批量更新
    account_ids = [1, 2]
    success_count, fail_count, message = await controller.batch_update_profiles(account_ids, changed_data)
    print(f"批量更新结果: 成功 {success_count} 个, 失败 {fail_count} 个")

async def test_proxy_ip_loading():
    """测试代理IP加载"""
    print("\n=== 测试代理IP加载 ===")
    
    controller = MockAccountController()
    proxy_ips = await controller.get_proxy_ips()
    
    print("代理IP列表:")
    for ip_info in proxy_ips:
        print(f"  ID: {ip_info['id']}, IP: {ip_info['ip']}:{ip_info['port']}, 绑定账户: {ip_info['account_count']}")

async def main():
    """主测试函数"""
    print("开始用户设置功能测试")
    
    # 测试代理设置处理
    test_proxy_settings_processing()
    
    # 测试单个用户更新
    await test_single_user_update()
    
    # 测试批量用户更新
    await test_batch_user_update()
    
    # 测试代理IP加载
    await test_proxy_ip_loading()
    
    print("\n测试完成")

if __name__ == "__main__":
    asyncio.run(main())
