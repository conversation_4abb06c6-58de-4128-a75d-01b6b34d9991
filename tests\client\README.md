# Telegram客户端管理模块

这个模块提供了一个灵活、可扩展的Telegram客户端管理解决方案，支持多账号管理、消息发送、群组管理等功能。

## 目录结构

```
client/
├── core/                      # 核心模块
│   ├── client_manager.py      # 客户端管理器
│   └── client_worker.py       # 客户端工作类
├── handlers/                  # 事件处理模块
│   └── message_handler.py     # 消息处理器
├── services/                  # 服务模块
│   ├── message_service.py     # 消息服务
│   └── group_service.py       # 群组服务
├── utils/                     # 工具模块
│   ├── cache.py              # 缓存工具
│   └── error_handler.py      # 错误处理工具
├── examples/                  # 示例模块
│   ├── message_example.py    # 消息发送示例
│   └── group_example.py      # 群组管理示例
└── main.py                    # 主程序入口
```

## 功能特点

- **多账号管理**：支持同时管理多个Telegram账号，每个账号都有独立的会话和状态
- **代理支持**：内置代理支持，包括SOCKS5和HTTP代理，适应各种网络环境
- **消息服务**：提供全面的消息发送、转发、删除等功能
- **群组管理**：支持邀请用户、获取成员、加入/离开群组等功能
- **事件处理**：灵活的事件处理机制，支持命令处理和消息过滤
- **错误处理**：专业的错误处理和重试机制，提高稳定性
- **缓存支持**：内置缓存系统，减少API调用，提高性能

## 使用方法

### 命令行工具

模块提供了一个命令行工具，可以直接使用：

```bash
# 列出所有客户端
python main.py list

# 登录客户端
python main.py login +8613800138000

# 发送消息
python main.py send username "这是一条测试消息"

# 邀请用户到群组
python main.py invite group_name user1 user2 user3

# 获取群组成员
python main.py members group_name
```

### 代码中使用

```python
import asyncio
from client.core.client_manager import TelegramClientManager
from client.services.message_service import MessageService

async def main():
    # 创建客户端管理器
    manager = TelegramClientManager(
        api_id=YOUR_API_ID,
        api_hash="YOUR_API_HASH",
        session_dir="./sessions",
        proxy={
            'type': 'socks5',
            'ip': '127.0.0.1',
            'port': 1080
        }
    )
    
    # 等待加载会话
    await asyncio.sleep(2)
    
    # 获取已授权客户端
    workers = manager.get_authorized_workers()
    if not workers:
        print("没有可用的已授权客户端")
        return
    
    # 创建消息服务
    message_service = MessageService()
    
    # 发送消息
    success, result = await message_service.send_text_message(
        client=workers[0].client,
        chat_id="username",
        text="Hello, world!"
    )
    
    if success:
        print(f"消息发送成功: {result.id}")
    else:
        print(f"消息发送失败: {result}")
    
    # 停止客户端
    await manager.stop_all()

# 运行主函数
asyncio.run(main())
```

## 示例

查看 `examples/` 目录获取更多使用示例：

- `message_example.py`: 消息发送示例
- `group_example.py`: 群组管理示例

## 安全注意事项

使用Telegram API时需注意：

1. **频率限制**：Telegram API有严格的频率限制，过快的操作会触发FloodWaitError
2. **账号安全**：过度使用API可能导致账号被限制或封禁，请合理使用
3. **代理安全**：确保使用的代理服务器安全可靠
4. **API密钥安全**：不要在公开环境中泄露API ID和API Hash

## 依赖项

- Python 3.7+
- Telethon
- python-socks (用于代理支持)
- asyncio 