#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Telegram客户端管理器
负责管理多个Telegram客户端
"""

import os
import asyncio
import glob
import re
from typing import Dict, List, Optional, Tuple, Union, Any, Set

from utils.logger import get_logger
from tests.client.core.client_worker import TelegramClientWorker
from tests.client.utils.error_handler import handle_telegram_errors, async_log_exceptions

class TelegramClientManager:
    """Telegram客户端管理器类"""
    
    def __init__(
        self, 
        api_id: int, 
        api_hash: str,
        session_dir: str,
        proxy: Optional[Dict[str, Any]] = None,
        auto_load: bool = True
    ):
        """初始化客户端管理器
        
        Args:
            api_id: Telegram API ID
            api_hash: Telegram API Hash
            session_dir: 会话文件目录
            proxy: 代理配置
            auto_load: 是否自动加载会话文件
        """
        self._api_id = api_id
        self._api_hash = api_hash
        self._session_dir = session_dir
        self._proxy = proxy
        
        # 确保会话目录存在
        os.makedirs(session_dir, exist_ok=True)
        
        # 客户端工作类字典
        self._workers: Dict[str, TelegramClientWorker] = {}
        
        # 初始化日志
        self._logger = get_logger("client.manager")
        
        # 自动加载会话
        if auto_load:
            asyncio.create_task(self._load_sessions())
            
    @property
    def worker_count(self) -> int:
        """获取工作类数量"""
        return len(self._workers)
        
    @property
    def active_worker_count(self) -> int:
        """获取活跃的工作类数量"""
        return sum(1 for worker in self._workers.values() if worker.is_connected)
        
    @property
    def authorized_worker_count(self) -> int:
        """获取已授权的工作类数量"""
        return sum(1 for worker in self._workers.values() if worker.is_authorized)
        
    def get_worker_names(self) -> List[str]:
        """获取所有工作类名称
        
        Returns:
            工作类名称列表
        """
        return list(self._workers.keys())
        
    def get_worker(self, worker_name: str) -> Optional[TelegramClientWorker]:
        """获取指定工作类
        
        Args:
            worker_name: 工作类名称
            
        Returns:
            工作类实例
        """
        return self._workers.get(worker_name)
        
    def get_active_workers(self) -> List[TelegramClientWorker]:
        """获取活跃的工作类
        
        Returns:
            活跃的工作类列表
        """
        return [worker for worker in self._workers.values() if worker.is_connected]
        
    def get_authorized_workers(self) -> List[TelegramClientWorker]:
        """获取已授权的工作类
        
        Returns:
            已授权的工作类列表
        """
        return [worker for worker in self._workers.values() if worker.is_authorized]
        
    def get_all_workers(self) -> List[TelegramClientWorker]:
        """获取所有工作类
        
        Returns:
            所有工作类列表
        """
        return list(self._workers.values())
        
    def get_worker_status(self) -> List[Dict[str, Any]]:
        """获取所有工作类状态
        
        Returns:
            工作类状态列表
        """
        result = []
        
        for name, worker in self._workers.items():
            status = {
                'name': name,
                'connected': worker.is_connected,
                'authorized': worker.is_authorized,
                'last_error': worker.last_error,
                'user_info': worker.user_info
            }
            
            result.append(status)
            
        return result
        
    async def create_worker(
        self, 
        phone_number: str, 
        session_name: Optional[str] = None
    ) -> Tuple[bool, Union[TelegramClientWorker, str]]:
        """创建新的工作类
        
        Args:
            phone_number: 手机号码
            session_name: 会话名称，不指定则使用手机号
            
        Returns:
            (成功标志, 工作类实例或错误消息)
        """
        # 规范化手机号码，移除非数字字符
        phone = re.sub(r'\D', '', phone_number)
        
        # 生成会话名称
        if not session_name:
            session_name = f"+{phone}"
            
        # 检查是否已存在
        if session_name in self._workers:
            return True, self._workers[session_name]
            
        # 会话文件路径
        session_file = os.path.join(self._session_dir, session_name)
        
        # 创建工作类
        worker = TelegramClientWorker(
            session_file=session_file,
            api_id=self._api_id,
            api_hash=self._api_hash,
            phone=phone,
            proxy=self._proxy
        )
        
        # 启动工作类
        success, message = await worker.start()
        
        if success:
            # 添加到字典
            self._workers[session_name] = worker
            self._logger.info(f"创建工作类成功: {session_name}")
            return True, worker
        else:
            self._logger.error(f"创建工作类失败: {session_name}, {message}")
            return False, message
            
    async def add_worker(
        self, 
        session_file: str, 
        phone: Optional[str] = None
    ) -> Tuple[bool, Union[TelegramClientWorker, str]]:
        """添加现有会话文件的工作类
        
        Args:
            session_file: 会话文件路径
            phone: 手机号码（可选）
            
        Returns:
            (成功标志, 工作类实例或错误消息)
        """
        # 获取会话名称
        session_name = os.path.basename(session_file)
        if session_name.endswith('.session'):
            session_name = session_name[:-8]  # 移除.session后缀
            
        # 检查是否已存在
        if session_name in self._workers:
            return True, self._workers[session_name]
            
        # 检查文件是否存在
        if not os.path.exists(f"{session_file}.session"):
            return False, f"会话文件不存在: {session_file}.session"
            
        # 创建工作类
        worker = TelegramClientWorker(
            session_file=session_file,
            api_id=self._api_id,
            api_hash=self._api_hash,
            phone=phone,
            proxy=self._proxy
        )
        
        # 启动工作类
        success, message = await worker.start()
        
        if success:
            # 添加到字典
            self._workers[session_name] = worker
            self._logger.info(f"添加工作类成功: {session_name}")
            return True, worker
        else:
            self._logger.error(f"添加工作类失败: {session_name}, {message}")
            return False, message
            
    async def remove_worker(
        self, 
        worker_name: str, 
        delete_session: bool = False
    ) -> Tuple[bool, str]:
        """移除工作类
        
        Args:
            worker_name: 工作类名称
            delete_session: 是否删除会话文件
            
        Returns:
            (成功标志, 结果消息)
        """
        # 检查是否存在
        if worker_name not in self._workers:
            return False, f"工作类不存在: {worker_name}"
            
        # 获取工作类
        worker = self._workers[worker_name]
        
        # 停止工作类
        await worker.stop()
        
        # 从字典移除
        del self._workers[worker_name]
        
        # 删除会话文件
        if delete_session:
            session_file = f"{worker._session_file}.session"
            if os.path.exists(session_file):
                try:
                    os.remove(session_file)
                    self._logger.info(f"删除会话文件: {session_file}")
                except Exception as e:
                    self._logger.error(f"删除会话文件失败: {session_file}, {e}")
                    
        self._logger.info(f"移除工作类成功: {worker_name}")
        return True, f"移除工作类成功: {worker_name}"
        
    async def start_all(self) -> Tuple[int, int]:
        """启动所有工作类
        
        Returns:
            (成功数量, 失败数量)
        """
        success_count = 0
        fail_count = 0
        
        for name, worker in self._workers.items():
            success, _ = await worker.start()
            if success:
                success_count += 1
            else:
                fail_count += 1
                
        self._logger.info(f"启动所有工作类：成功 {success_count}，失败 {fail_count}")
        return success_count, fail_count
        
    async def stop_all(self) -> Tuple[int, int]:
        """停止所有工作类
        
        Returns:
            (成功数量, 失败数量)
        """
        success_count = 0
        fail_count = 0
        
        for name, worker in self._workers.items():
            success, _ = await worker.stop()
            if success:
                success_count += 1
            else:
                fail_count += 1
                
        self._logger.info(f"停止所有工作类：成功 {success_count}，失败 {fail_count}")
        return success_count, fail_count
        
    async def check_all_sessions(self) -> Dict[str, bool]:
        """检查所有会话是否有效
        
        Returns:
            会话有效性字典
        """
        result = {}
        
        for name, worker in self._workers.items():
            success, _ = await worker.check_session()
            result[name] = success
            
        return result
        
    async def login_worker(
        self, 
        worker_name: str, 
        phone: Optional[str] = None,
        code_callback: Optional[Any] = None,
        password_callback: Optional[Any] = None
    ) -> Tuple[bool, str]:
        """登录指定工作类
        
        Args:
            worker_name: 工作类名称
            phone: 手机号码
            code_callback: 验证码回调函数
            password_callback: 两步验证密码回调函数
            
        Returns:
            (成功标志, 结果消息)
        """
        # 检查是否存在
        if worker_name not in self._workers:
            return False, f"工作类不存在: {worker_name}"
            
        # 获取工作类
        worker = self._workers[worker_name]
        
        # 登录
        return await worker.login(phone, code_callback, password_callback)
        
    @async_log_exceptions(get_logger("client.manager.error"))
    async def _load_sessions(self):
        """加载会话文件"""
        self._logger.info(f"正在加载会话文件目录: {self._session_dir}")
        
        # 查找所有会话文件
        session_files = glob.glob(os.path.join(self._session_dir, "*.session"))
        
        if not session_files:
            self._logger.info("未找到会话文件")
            return
            
        self._logger.info(f"找到 {len(session_files)} 个会话文件")
        
        # 添加会话文件
        for session_file in session_files:
            # 移除.session后缀
            base_file = session_file[:-8]
            
            # 添加工作类
            await self.add_worker(base_file)
            
        self._logger.info(f"已加载 {len(self._workers)} 个会话工作类") 