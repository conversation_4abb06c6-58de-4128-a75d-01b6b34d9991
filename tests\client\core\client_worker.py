#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Telegram客户端工作类
负责Telegram客户端的创建、连接和会话管理
"""

import os
import asyncio
import threading
import time
from typing import Dict, List, Optional, Tuple, Union, Any

import python_socks
from telethon import TelegramClient
from telethon.errors import (
    PhoneCodeInvalidError, PhoneCodeExpiredError, 
    SessionPasswordNeededError, AuthKeyError
)

from utils.logger import get_logger
from tests.client.utils.error_handler import handle_telegram_errors, async_log_exceptions

class TelegramClientWorker:
    """Telegram客户端工作类，负责单个客户端的管理"""
    
    def __init__(
        self, 
        session_file: str, 
        api_id: int, 
        api_hash: str,
        phone: Optional[str] = None,
        proxy: Optional[Dict[str, Any]] = None,
        connection_retries: int = 5,
        retry_delay: int = 5,
        auto_reconnect: bool = True
    ):
        """初始化客户端工作类
        
        Args:
            session_file: 会话文件路径
            api_id: Telegram API ID
            api_hash: Telegram API Hash
            phone: 手机号码（可选）
            proxy: 代理配置
            connection_retries: 连接重试次数
            retry_delay: 重试延迟（秒）
            auto_reconnect: 是否自动重连
        """
        self._session_file = session_file
        self._api_id = api_id
        self._api_hash = api_hash
        self._phone = phone
        self._proxy = proxy
        self._connection_retries = connection_retries
        self._retry_delay = retry_delay
        self._auto_reconnect = auto_reconnect
        
        self._client = None
        self._running = False
        self._connected = False
        self._authorized = False
        self._me = None
        self._task = None
        self._last_error = None
        self._retry_count = 0
        
        self._logger = get_logger(f"client.worker.{os.path.basename(session_file)}")
        
    @property
    def client(self) -> Optional[TelegramClient]:
        """获取客户端实例"""
        return self._client
        
    @property
    def is_connected(self) -> bool:
        """是否已连接"""
        return self._connected
        
    @property
    def is_authorized(self) -> bool:
        """是否已授权"""
        return self._authorized
        
    @property
    def last_error(self) -> Optional[str]:
        """获取最后一次错误"""
        return self._last_error
        
    @property
    def user_info(self) -> Optional[Dict[str, Any]]:
        """获取用户信息"""
        if not self._me:
            return None
            
        return {
            'id': self._me.id,
            'first_name': self._me.first_name or '',
            'last_name': self._me.last_name or '',
            'username': self._me.username or '',
            'phone': self._me.phone or '',
            'bot': getattr(self._me, 'bot', False),
            'verified': getattr(self._me, 'verified', False),
            'session': os.path.basename(self._session_file)
        }
        
    async def start(self) -> Tuple[bool, str]:
        """启动客户端
        
        Returns:
            (成功标志, 结果消息)
        """
        if self._running:
            return True, "客户端已在运行"
            
        self._running = True
        self._retry_count = 0
        
        # 创建客户端实例
        await self._create_client()
        
        # 启动客户端循环
        if self._client:
            self._task = asyncio.create_task(self._client_loop())
            return True, "客户端启动成功"
        else:
            self._running = False
            return False, "客户端创建失败"
            
    async def stop(self) -> Tuple[bool, str]:
        """停止客户端
        
        Returns:
            (成功标志, 结果消息)
        """
        if not self._running:
            return True, "客户端未运行"
            
        self._running = False
        
        # 取消任务
        if self._task:
            self._task.cancel()
            try:
                await self._task
            except asyncio.CancelledError:
                pass
            self._task = None
            
        # 断开客户端连接
        if self._client:
            try:
                await self._client.disconnect()
            except Exception as e:
                self._logger.error(f"断开客户端连接时出错: {e}")
                
        self._connected = False
        self._authorized = False
        self._client = None
        
        return True, "客户端已停止"
        
    async def restart(self) -> Tuple[bool, str]:
        """重启客户端
        
        Returns:
            (成功标志, 结果消息)
        """
        self._logger.info("重启客户端")
        
        # 停止客户端
        await self.stop()
        
        # 延迟一段时间
        await asyncio.sleep(2)
        
        # 启动客户端
        return await self.start()
        
    async def login(
        self, 
        phone: Optional[str] = None, 
        code_callback: Optional[Any] = None,
        password_callback: Optional[Any] = None
    ) -> Tuple[bool, str]:
        """登录账号
        
        Args:
            phone: 手机号码
            code_callback: 验证码回调函数
            password_callback: 两步验证密码回调函数
            
        Returns:
            (成功标志, 结果消息)
        """
        if not self._client:
            return False, "客户端未创建"
            
        if self._authorized:
            return True, "客户端已授权"
            
        if phone:
            self._phone = phone
            
        if not self._phone:
            return False, "未提供手机号码"
            
        try:
            self._logger.info(f"尝试登录账号: {self._phone}")
            
            # 确保已连接
            if not self._connected:
                await self._client.connect()
                self._connected = True
                
            # 检查是否已授权
            if await self._client.is_user_authorized():
                self._authorized = True
                self._me = await self._client.get_me()
                self._logger.info(f"账号已授权: {self._me.first_name} {self._me.last_name}")
                return True, "账号已授权"
                
            # 发送验证码
            sent_code = await self._client.send_code_request(self._phone)
            self._logger.info(f"已发送验证码到: {self._phone}")
            
            # 获取验证码
            if code_callback:
                code = await code_callback(self._phone, sent_code.phone_code_hash)
            else:
                code = input(f"输入发送到 {self._phone} 的验证码: ")
                
            # 登录账号
            try:
                await self._client.sign_in(self._phone, code, phone_code_hash=sent_code.phone_code_hash)
                
                # 登录成功
                self._authorized = True
                self._me = await self._client.get_me()
                self._logger.info(f"登录成功: {self._me.first_name} {self._me.last_name}")
                return True, "登录成功"
                
            except PhoneCodeInvalidError:
                self._last_error = "验证码无效"
                self._logger.error(self._last_error)
                return False, self._last_error
                
            except PhoneCodeExpiredError:
                self._last_error = "验证码已过期"
                self._logger.error(self._last_error)
                return False, self._last_error
                
            except SessionPasswordNeededError:
                # 需要两步验证密码
                self._logger.info("需要两步验证密码")
                
                # 获取密码
                if password_callback:
                    password = await password_callback(self._phone)
                else:
                    password = input("输入两步验证密码: ")
                    
                # 输入密码
                await self._client.sign_in(password=password)
                
                # 登录成功
                self._authorized = True
                self._me = await self._client.get_me()
                self._logger.info(f"登录成功: {self._me.first_name} {self._me.last_name}")
                return True, "登录成功"
                
        except Exception as e:
            self._last_error = f"登录失败: {str(e)}"
            self._logger.error(self._last_error)
            return False, self._last_error
            
    async def check_session(self) -> Tuple[bool, str]:
        """检查会话是否有效
        
        Returns:
            (成功标志, 结果消息)
        """
        if not self._client:
            return False, "客户端未创建"
            
        try:
            # 确保已连接
            if not self._connected:
                await self._client.connect()
                self._connected = True
                
            # 检查授权状态
            authorized = await self._client.is_user_authorized()
            
            if authorized:
                self._authorized = True
                self._me = await self._client.get_me()
                self._logger.info(f"会话有效: {self._me.first_name} {self._me.last_name}")
                return True, "会话有效"
            else:
                self._authorized = False
                self._logger.warning("会话无效，需要重新登录")
                return False, "会话无效，需要重新登录"
                
        except AuthKeyError:
            self._authorized = False
            self._last_error = "会话密钥无效，需要重新登录"
            self._logger.error(self._last_error)
            return False, self._last_error
            
        except Exception as e:
            self._last_error = f"检查会话失败: {str(e)}"
            self._logger.error(self._last_error)
            return False, self._last_error
            
    async def logout(self) -> Tuple[bool, str]:
        """登出账号
        
        Returns:
            (成功标志, 结果消息)
        """
        if not self._client:
            return False, "客户端未创建"
            
        if not self._authorized:
            return True, "客户端未授权"
            
        try:
            self._logger.info("正在登出账号")
            
            # 确保已连接
            if not self._connected:
                await self._client.connect()
                self._connected = True
                
            # 登出账号
            await self._client.log_out()
            
            self._authorized = False
            self._me = None
            
            self._logger.info("账号已登出")
            return True, "账号已登出"
            
        except Exception as e:
            self._last_error = f"登出失败: {str(e)}"
            self._logger.error(self._last_error)
            return False, self._last_error
            
    @async_log_exceptions(get_logger("client.worker.error"))
    async def _create_client(self):
        """创建客户端实例"""
        self._logger.info(f"创建客户端: {self._session_file}")
        
        try:
            # 配置代理
            proxy_config = None
            if self._proxy:
                proxy_type = self._proxy.get('type', 'socks5')
                
                if proxy_type == 'socks5':
                    proxy_config = (
                        python_socks.ProxyType.SOCKS5,
                        self._proxy.get('ip', '127.0.0.1'),
                        self._proxy.get('port', 1080),
                        self._proxy.get('username'),
                        self._proxy.get('password')
                    )
                elif proxy_type == 'http':
                    proxy_config = (
                        python_socks.ProxyType.HTTP,
                        self._proxy.get('ip', '127.0.0.1'),
                        self._proxy.get('port', 8080),
                        self._proxy.get('username'),
                        self._proxy.get('password')
                    )
                    
            # 创建客户端
            self._client = TelegramClient(
                self._session_file,
                self._api_id,
                self._api_hash,
                proxy=proxy_config,
                connection_retries=self._connection_retries
            )
            
            self._logger.info(f"客户端创建成功: {self._session_file}")
            
        except Exception as e:
            self._last_error = f"创建客户端失败: {str(e)}"
            self._logger.error(self._last_error)
            self._client = None
            
    @async_log_exceptions(get_logger("client.worker.error"))
    async def _client_loop(self):
        """客户端主循环"""
        self._logger.info("启动客户端循环")
        
        while self._running:
            try:
                # 连接客户端
                if not self._connected:
                    self._logger.info("正在连接客户端")
                    await self._client.connect()
                    self._connected = True
                    self._retry_count = 0
                    self._logger.info("客户端已连接")
                    
                # 检查授权状态
                if self._connected and not self._authorized:
                    authorized = await self._client.is_user_authorized()
                    if authorized:
                        self._authorized = True
                        self._me = await self._client.get_me()
                        self._logger.info(f"账号已授权: {self._me.first_name} {self._me.last_name}")
                    else:
                        self._logger.warning("账号未授权，需要登录")
                        
                # 保持在线
                if self._connected:
                    # 简单的在线检查，防止连接断开
                    me = await self._client.get_me()
                    if me:
                        self._me = me
                        self._authorized = True
                        
                # 等待一段时间
                await asyncio.sleep(60)
                
            except asyncio.CancelledError:
                # 任务取消
                self._logger.info("客户端循环被取消")
                break
                
            except Exception as e:
                self._last_error = f"客户端循环出错: {str(e)}"
                self._logger.error(self._last_error)
                
                # 标记为未连接
                self._connected = False
                
                # 重试策略
                if self._auto_reconnect:
                    self._retry_count += 1
                    retry_delay = min(self._retry_delay * self._retry_count, 300)  # 最多等待5分钟
                    
                    self._logger.info(f"将在 {retry_delay} 秒后尝试重新连接 (尝试 {self._retry_count}/{self._connection_retries})")
                    
                    if self._retry_count > self._connection_retries:
                        self._logger.error("超过最大重试次数，停止重连")
                        break
                        
                    await asyncio.sleep(retry_delay)
                else:
                    break
                    
        # 断开连接
        if self._client and self._connected:
            try:
                await self._client.disconnect()
            except Exception as e:
                self._logger.error(f"断开客户端连接时出错: {e}")
                
        self._connected = False
        self._logger.info("客户端循环已结束") 