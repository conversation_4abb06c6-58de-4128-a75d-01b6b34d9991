#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
群组管理示例
演示如何使用TelegramClientManager进行群组管理
"""

import os
import asyncio
import logging
from typing import List, Dict, Any

from utils.logger import get_logger
from tests.client.core.client_manager import TelegramClientManager
from tests.client.services.group_service import GroupService

# 配置日志
logger = get_logger("examples.group")

# Telegram API 配置
API_ID = 24297563
API_HASH = "79354bc5da59358b3f268c7ecb1ce332"

# 会话目录
SESSION_DIR = r"H:\PyProject\TeleTest\APPDATA"

# 代理配置
PROXY_CONFIG = {
    'type': 'socks5',
    'ip': '127.0.0.1',
    'port': 1080
}

async def invite_to_group_example(group_id: str, user_ids: List[str]):
    """邀请用户到群组示例
    
    Args:
        group_id: 群组ID或用户名
        user_ids: 用户ID或用户名列表
    """
    logger.info(f"启动邀请用户示例，群组: {group_id}，用户数量: {len(user_ids)}")
    
    # 创建客户端管理器
    manager = TelegramClientManager(
        api_id=API_ID,
        api_hash=API_HASH,
        session_dir=SESSION_DIR,
        proxy=PROXY_CONFIG
    )
    
    # 等待加载会话
    await asyncio.sleep(2)
    
    # 获取已授权客户端
    authorized_workers = manager.get_authorized_workers()
    if not authorized_workers:
        logger.error("没有可用的已授权客户端")
        await manager.stop_all()
        return
        
    # 选择第一个已授权客户端
    worker = authorized_workers[0]
    logger.info(f"使用客户端: {worker.user_info['username'] or worker.user_info['phone']}")
    
    # 创建群组服务
    group_service = GroupService()
    
    # 邀请用户到群组
    success, result = await group_service.invite_to_group(
        client=worker.client,
        group_id=group_id,
        user_ids=user_ids
    )
    
    if success:
        logger.info(f"邀请用户结果: 总计 {result['total']}，成功 {result['success']}，失败 {result['failed']}")
    else:
        logger.error(f"邀请用户失败: {result['error'] if 'error' in result else result}")
    
    # 停止所有客户端
    await manager.stop_all()
    logger.info("示例结束")
    
    return result

async def get_group_members_example(group_id: str, limit: int = 100):
    """获取群组成员示例
    
    Args:
        group_id: 群组ID或用户名
        limit: 获取成员数量限制
    """
    logger.info(f"启动获取群组成员示例，群组: {group_id}")
    
    # 创建客户端管理器
    manager = TelegramClientManager(
        api_id=API_ID,
        api_hash=API_HASH,
        session_dir=SESSION_DIR,
        proxy=PROXY_CONFIG
    )
    
    # 等待加载会话
    await asyncio.sleep(2)
    
    # 获取已授权客户端
    authorized_workers = manager.get_authorized_workers()
    if not authorized_workers:
        logger.error("没有可用的已授权客户端")
        await manager.stop_all()
        return
        
    # 选择第一个已授权客户端
    worker = authorized_workers[0]
    logger.info(f"使用客户端: {worker.user_info['username'] or worker.user_info['phone']}")
    
    # 创建群组服务
    group_service = GroupService()
    
    # 获取群组成员
    success, result = await group_service.get_group_members(
        client=worker.client,
        group_id=group_id,
        limit=limit
    )
    
    if success:
        logger.info(f"获取到 {len(result)} 个群组成员")
        
        # 打印前10个成员
        for i, member in enumerate(result[:10], 1):
            username = member['username']
            name = f"{member['first_name']} {member['last_name']}".strip()
            logger.info(f"{i}. {name} (@{username})" if username else f"{i}. {name}")
    else:
        logger.error(f"获取群组成员失败: {result}")
    
    # 停止所有客户端
    await manager.stop_all()
    logger.info("示例结束")
    
    return result

async def join_group_example(group_link: str):
    """加入群组示例
    
    Args:
        group_link: 群组链接或ID
    """
    logger.info(f"启动加入群组示例，群组: {group_link}")
    
    # 创建客户端管理器
    manager = TelegramClientManager(
        api_id=API_ID,
        api_hash=API_HASH,
        session_dir=SESSION_DIR,
        proxy=PROXY_CONFIG
    )
    
    # 等待加载会话
    await asyncio.sleep(2)
    
    # 获取已授权客户端
    authorized_workers = manager.get_authorized_workers()
    if not authorized_workers:
        logger.error("没有可用的已授权客户端")
        await manager.stop_all()
        return
        
    # 选择第一个已授权客户端
    worker = authorized_workers[0]
    logger.info(f"使用客户端: {worker.user_info['username'] or worker.user_info['phone']}")
    
    # 创建群组服务
    group_service = GroupService()
    
    # 加入群组
    success, result = await group_service.join_group(
        client=worker.client,
        group_id=group_link
    )
    
    if success:
        logger.info(f"成功加入群组: {group_link}")
    else:
        logger.error(f"加入群组失败: {result}")
    
    # 停止所有客户端
    await manager.stop_all()
    logger.info("示例结束")
    
    return success, result

async def leave_group_example(group_id: str):
    """离开群组示例
    
    Args:
        group_id: 群组ID或用户名
    """
    logger.info(f"启动离开群组示例，群组: {group_id}")
    
    # 创建客户端管理器
    manager = TelegramClientManager(
        api_id=API_ID,
        api_hash=API_HASH,
        session_dir=SESSION_DIR,
        proxy=PROXY_CONFIG
    )
    
    # 等待加载会话
    await asyncio.sleep(2)
    
    # 获取已授权客户端
    authorized_workers = manager.get_authorized_workers()
    if not authorized_workers:
        logger.error("没有可用的已授权客户端")
        await manager.stop_all()
        return
        
    # 选择第一个已授权客户端
    worker = authorized_workers[0]
    logger.info(f"使用客户端: {worker.user_info['username'] or worker.user_info['phone']}")
    
    # 创建群组服务
    group_service = GroupService()
    
    # 离开群组
    success, result = await group_service.leave_group(
        client=worker.client,
        group_id=group_id
    )
    
    if success:
        logger.info(f"成功离开群组: {group_id}")
    else:
        logger.error(f"离开群组失败: {result}")
    
    # 停止所有客户端
    await manager.stop_all()
    logger.info("示例结束")
    
    return success, result

# 运行示例
if __name__ == "__main__":
    # 邀请用户到群组示例
    # group_id = "your_group"
    # user_ids = ["user1", "user2", "user3"]
    # asyncio.run(invite_to_group_example(group_id, user_ids))
    
    # 获取群组成员示例
    # group_id = "your_group"
    # asyncio.run(get_group_members_example(group_id))
    
    # 加入群组示例
    # group_link = "https://t.me/joinchat/xxxxx"
    # asyncio.run(join_group_example(group_link))
    
    # 离开群组示例
    # group_id = "your_group"
    # asyncio.run(leave_group_example(group_id))
    
    # 默认运行获取群组成员示例
    group_id = "me"  # 默认使用自己的私聊，请修改为实际群组ID
    asyncio.run(get_group_members_example(group_id)) 