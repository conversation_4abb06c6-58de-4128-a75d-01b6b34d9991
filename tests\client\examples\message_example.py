#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
消息发送示例
演示如何使用TelegramClientManager发送消息
"""

import os
import asyncio
import logging
from typing import List, Dict, Any

from utils.logger import get_logger
from tests.client.core.client_manager import TelegramClientManager
from tests.client.services.message_service import MessageService

# 配置日志
logger = get_logger("examples.message")

# Telegram API 配置
API_ID = 24297563
API_HASH = "79354bc5da59358b3f268c7ecb1ce332"

# 会话目录
SESSION_DIR = r"H:\PyProject\TeleTest\APPDATA"

# 代理配置
PROXY_CONFIG = {
    'type': 'socks5',
    'ip': '127.0.0.1',
    'port': 1080
}

async def send_message_example():
    """发送消息示例"""
    logger.info("启动消息发送示例")
    
    # 创建客户端管理器
    manager = TelegramClientManager(
        api_id=API_ID,
        api_hash=API_HASH,
        session_dir=SESSION_DIR,
        proxy=PROXY_CONFIG
    )
    
    # 等待加载会话
    await asyncio.sleep(2)
    
    # 获取客户端状态
    statuses = manager.get_worker_status()
    logger.info(f"已加载 {len(statuses)} 个客户端")
    
    # 检查是否有可用客户端
    authorized_workers = manager.get_authorized_workers()
    if not authorized_workers:
        logger.error("没有可用的已授权客户端")
        await manager.stop_all()
        return
        
    # 选择第一个已授权客户端
    worker = authorized_workers[0]
    logger.info(f"使用客户端: {worker.user_info['username'] or worker.user_info['phone']}")
    
    # 创建消息服务
    message_service = MessageService()
    
    # 发送文本消息
    chat_id = "me"  # 发送给自己
    text = "这是一条测试消息"
    
    success, result = await message_service.send_text_message(
        client=worker.client,
        chat_id=chat_id,
        text=text
    )
    
    if success:
        logger.info(f"消息发送成功，消息ID: {result.id}")
    else:
        logger.error(f"消息发送失败: {result}")
    
    # 等待一段时间
    await asyncio.sleep(2)
    
    # 停止所有客户端
    await manager.stop_all()
    logger.info("示例结束")

# 消息批量发送示例
async def batch_send_message_example(chat_ids: List[str], message: str):
    """批量发送消息示例
    
    Args:
        chat_ids: 目标聊天ID列表
        message: 要发送的消息
    """
    logger.info(f"启动批量消息发送示例，目标数量: {len(chat_ids)}")
    
    # 创建客户端管理器
    manager = TelegramClientManager(
        api_id=API_ID,
        api_hash=API_HASH,
        session_dir=SESSION_DIR,
        proxy=PROXY_CONFIG
    )
    
    # 等待加载会话
    await asyncio.sleep(2)
    
    # 获取已授权客户端
    authorized_workers = manager.get_authorized_workers()
    if not authorized_workers:
        logger.error("没有可用的已授权客户端")
        await manager.stop_all()
        return
        
    # 创建消息服务
    message_service = MessageService()
    
    # 统计结果
    results = {
        'total': len(chat_ids),
        'success': 0,
        'failed': 0,
        'details': []
    }
    
    # 批量发送消息
    for i, chat_id in enumerate(chat_ids):
        # 选择客户端（循环使用可用客户端）
        worker_index = i % len(authorized_workers)
        worker = authorized_workers[worker_index]
        
        logger.info(f"使用客户端 {worker.user_info['username'] or worker.user_info['phone']} 发送消息到 {chat_id}")
        
        try:
            success, result = await message_service.send_text_message(
                client=worker.client,
                chat_id=chat_id,
                text=message
            )
            
            if success:
                results['success'] += 1
                detail = {
                    'chat_id': chat_id,
                    'success': True,
                    'message_id': result.id
                }
            else:
                results['failed'] += 1
                detail = {
                    'chat_id': chat_id,
                    'success': False,
                    'error': result
                }
                
            results['details'].append(detail)
            
            # 简单的延迟，避免触发限制
            await asyncio.sleep(1)
            
        except Exception as e:
            logger.error(f"发送消息到 {chat_id} 时出错: {e}")
            results['failed'] += 1
            results['details'].append({
                'chat_id': chat_id,
                'success': False,
                'error': str(e)
            })
    
    # 打印结果统计
    logger.info(f"批量发送结果: 总计 {results['total']}，成功 {results['success']}，失败 {results['failed']}")
    
    # 停止所有客户端
    await manager.stop_all()
    logger.info("批量发送示例结束")
    
    return results

# 运行示例
if __name__ == "__main__":
    # 单条消息发送示例
    asyncio.run(send_message_example())
    
    # 批量发送示例
    # targets = ["me", "username1", "username2"]
    # message = "这是一条批量测试消息"
    # asyncio.run(batch_send_message_example(targets, message)) 