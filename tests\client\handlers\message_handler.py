#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
消息事件处理模块
提供各种消息事件的处理函数
"""

import asyncio
import re
from typing import Dict, List, Callable, Any, Optional, Union

from telethon import TelegramClient, events
from telethon.tl import types

from utils.logger import get_logger

class MessageHandler:
    """消息处理器类"""
    
    def __init__(self, client: TelegramClient):
        """初始化消息处理器
        
        Args:
            client: Telegram客户端
        """
        self._client = client
        self._logger = get_logger("client.handlers.message")
        self._command_handlers: Dict[str, Callable] = {}
        self._message_handlers: List[Dict[str, Any]] = []
        self._initialized = False
        
    def register_command(self, command: str, handler: Callable, description: str = ""):
        """注册命令处理器
        
        Args:
            command: 命令名称，不含/
            handler: 处理函数
            description: 命令描述
        """
        self._command_handlers[command] = {
            "handler": handler,
            "description": description
        }
        self._logger.info(f"注册命令处理器: /{command}")
        
    def register_message_handler(
        self, 
        handler: Callable, 
        filters: Optional[Dict[str, Any]] = None, 
        description: str = ""
    ):
        """注册消息处理器
        
        Args:
            handler: 处理函数
            filters: 过滤条件，如 {'pattern': r'关键词', 'from_users': [123456]}
            description: 处理器描述
        """
        if filters is None:
            filters = {}
            
        self._message_handlers.append({
            "handler": handler,
            "filters": filters,
            "description": description
        })
        self._logger.info(f"注册消息处理器: {description}")
        
    def initialize(self):
        """初始化处理器，注册到客户端"""
        if self._initialized:
            self._logger.warning("消息处理器已经初始化")
            return
            
        # 注册命令处理器
        @self._client.on(events.NewMessage(pattern=r'/([a-zA-Z0-9_]+)(@\w+)?(\s.*)?'))
        async def command_handler(event):
            """处理命令消息"""
            # 提取命令名称
            command_match = re.match(r'/([a-zA-Z0-9_]+)(@\w+)?(\s.*)?', event.raw_text)
            if not command_match:
                return
                
            command = command_match.group(1).lower()
            bot_username = command_match.group(2)
            
            # 如果命令指定了机器人用户名，但不是给当前客户端的，则忽略
            if bot_username:
                me = await self._client.get_me()
                if me.username and f"@{me.username.lower()}" != bot_username.lower():
                    return
            
            # 提取参数
            args_text = command_match.group(3)
            args = args_text.strip().split() if args_text else []
            
            # 查找并执行命令处理函数
            if command in self._command_handlers:
                try:
                    handler_info = self._command_handlers[command]
                    handler = handler_info["handler"]
                    
                    self._logger.info(f"执行命令: /{command} {args}")
                    await handler(event, args)
                except Exception as e:
                    self._logger.error(f"执行命令 /{command} 出错: {e}")
        
        # 注册普通消息处理器
        @self._client.on(events.NewMessage())
        async def message_handler(event):
            """处理普通消息"""
            # 如果是命令，则跳过
            if event.raw_text and event.raw_text.startswith('/'):
                return
                
            # 遍历所有消息处理器
            for handler_info in self._message_handlers:
                handler = handler_info["handler"]
                filters = handler_info["filters"]
                
                # 检查过滤条件
                if self._check_filters(event, filters):
                    try:
                        self._logger.info(f"执行消息处理器: {handler_info['description']}")
                        await handler(event)
                    except Exception as e:
                        self._logger.error(f"执行消息处理器 {handler_info['description']} 出错: {e}")
        
        # 注册编辑消息处理器
        @self._client.on(events.MessageEdited())
        async def message_edited_handler(event):
            """处理编辑消息"""
            # 记录消息编辑
            chat = await event.get_chat()
            chat_title = getattr(chat, 'title', None) or f"Chat {chat.id}"
            sender = await event.get_sender()
            sender_name = getattr(sender, 'first_name', 'Unknown')
            
            self._logger.info(f"消息编辑: {chat_title} - {sender_name}: {event.raw_text}")
        
        # 注册删除消息处理器
        @self._client.on(events.MessageDeleted())
        async def message_deleted_handler(event):
            """处理删除消息"""
            # 记录消息删除
            self._logger.info(f"消息删除: IDs {event.deleted_ids}")
        
        # 注册群组事件处理器
        @self._client.on(events.ChatAction())
        async def chat_action_handler(event):
            """处理群组事件"""
            # 记录群组事件
            action_message = self._format_chat_action(event)
            if action_message:
                self._logger.info(action_message)
        
        self._initialized = True
        self._logger.info("消息处理器初始化完成")
    
    def _check_filters(self, event, filters):
        """检查消息是否符合过滤条件
        
        Args:
            event: 消息事件
            filters: 过滤条件
            
        Returns:
            是否符合条件
        """
        # 检查发送者
        if 'from_users' in filters:
            sender = event.sender_id
            if sender not in filters['from_users']:
                return False
        
        # 检查聊天
        if 'chats' in filters:
            chat_id = event.chat_id
            if chat_id not in filters['chats']:
                return False
        
        # 检查正则表达式匹配
        if 'pattern' in filters and event.raw_text:
            pattern = filters['pattern']
            if not re.search(pattern, event.raw_text):
                return False
        
        # 检查是否是群组消息
        if 'is_group' in filters:
            is_group = event.is_group
            if is_group != filters['is_group']:
                return False
        
        # 检查是否是私聊消息
        if 'is_private' in filters:
            is_private = event.is_private
            if is_private != filters['is_private']:
                return False
        
        # 检查是否包含媒体
        if 'has_media' in filters:
            has_media = event.media is not None
            if has_media != filters['has_media']:
                return False
        
        return True
    
    def _format_chat_action(self, event):
        """格式化群组事件消息
        
        Args:
            event: 群组事件
            
        Returns:
            格式化的消息
        """
        action = event.action_message
        if not action:
            return None
            
        chat = event.chat
        chat_title = getattr(chat, 'title', None) or f"Chat {chat.id}"
        
        if isinstance(action.action, types.MessageActionChatAddUser):
            users = [f"用户 {user_id}" for user_id in action.action.users]
            return f"群组 {chat_title} 添加了新成员: {', '.join(users)}"
            
        elif isinstance(action.action, types.MessageActionChatDeleteUser):
            return f"群组 {chat_title} 移除了成员: 用户 {action.action.user_id}"
            
        elif isinstance(action.action, types.MessageActionChatJoinedByLink):
            return f"用户通过邀请链接加入群组 {chat_title}"
            
        elif isinstance(action.action, types.MessageActionChannelCreate):
            return f"创建了频道 {chat_title}"
            
        elif isinstance(action.action, types.MessageActionChatCreate):
            return f"创建了群组 {chat_title}"
            
        elif isinstance(action.action, types.MessageActionChatEditTitle):
            return f"群组更名为 {action.action.title}"
            
        elif isinstance(action.action, types.MessageActionChatEditPhoto):
            return f"群组 {chat_title} 更换了头像"
            
        elif isinstance(action.action, types.MessageActionChatDeletePhoto):
            return f"群组 {chat_title} 删除了头像"
            
        else:
            return f"群组 {chat_title} 发生了动作: {type(action.action).__name__}" 