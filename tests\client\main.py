#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Telegram客户端管理主程序
提供命令行界面操作Telegram客户端
"""

import os
import sys
import asyncio
import argparse
from typing import List, Dict, Any, Optional

from utils.logger import get_logger
from tests.client.core.client_manager import TelegramClientManager
from tests.client.services.message_service import MessageService
from tests.client.services.group_service import GroupService

# 配置日志
logger = get_logger("client.main")

# Telegram API 配置
API_ID = 24297563
API_HASH = "79354bc5da59358b3f268c7ecb1ce332"

# 默认会话目录
DEFAULT_SESSION_DIR = r"H:\PyProject\TeleTest\APPDATA"

# 默认代理配置
DEFAULT_PROXY_CONFIG = {
    'type': 'socks5',
    'ip': '127.0.0.1',
    'port': 1080
}

class TelegramClientCLI:
    """Telegram客户端命令行界面"""
    
    def __init__(
        self, 
        api_id: int, 
        api_hash: str,
        session_dir: str,
        proxy: Optional[Dict[str, Any]] = None
    ):
        """初始化命令行界面
        
        Args:
            api_id: API ID
            api_hash: API Hash
            session_dir: 会话目录
            proxy: 代理配置
        """
        self.api_id = api_id
        self.api_hash = api_hash
        self.session_dir = session_dir
        self.proxy = proxy
        
        self.manager = None
        self.message_service = None
        self.group_service = None
        
    async def initialize(self):
        """初始化客户端管理器"""
        logger.info("初始化客户端管理器")
        
        self.manager = TelegramClientManager(
            api_id=self.api_id,
            api_hash=self.api_hash,
            session_dir=self.session_dir,
            proxy=self.proxy
        )
        
        # 等待加载会话
        await asyncio.sleep(2)
        
        self.message_service = MessageService()
        self.group_service = GroupService()
        
        logger.info("初始化完成")
        
    async def list_clients(self):
        """列出所有客户端"""
        if not self.manager:
            logger.error("客户端管理器未初始化")
            return
            
        statuses = self.manager.get_worker_status()
        
        print(f"\n共有 {len(statuses)} 个客户端:")
        print("=" * 60)
        
        for i, status in enumerate(statuses, 1):
            username = status['user_info']['username'] if status['user_info'] else "未知"
            phone = status['user_info']['phone'] if status['user_info'] else "未知"
            
            connected = "已连接" if status['connected'] else "未连接"
            authorized = "已授权" if status['authorized'] else "未授权"
            
            print(f"{i}. 名称: {status['name']}")
            print(f"   用户名: {username}, 手机: {phone}")
            print(f"   状态: {connected}, {authorized}")
            
            if status['last_error']:
                print(f"   最后错误: {status['last_error']}")
                
            print("-" * 60)
            
    async def login_client(self, phone_number: str):
        """登录客户端
        
        Args:
            phone_number: 手机号码
        """
        if not self.manager:
            logger.error("客户端管理器未初始化")
            return
            
        # 创建客户端
        success, result = await self.manager.create_worker(phone_number)
        
        if not success:
            logger.error(f"创建客户端失败: {result}")
            return
            
        worker = result
        
        # 检查是否已授权
        if worker.is_authorized:
            logger.info(f"客户端已授权: {worker.user_info['username'] or worker.user_info['phone']}")
            return
            
        # 定义验证码回调
        async def code_callback(phone, phone_code_hash):
            code = input(f"输入发送到 {phone} 的验证码: ")
            return code
            
        # 定义密码回调
        async def password_callback(phone):
            password = input("输入两步验证密码: ")
            return password
            
        # 登录客户端
        success, message = await worker.login(
            phone=phone_number,
            code_callback=code_callback,
            password_callback=password_callback
        )
        
        if success:
            logger.info(f"登录成功: {worker.user_info['username'] or worker.user_info['phone']}")
        else:
            logger.error(f"登录失败: {message}")
            
    async def send_message(self, chat_id: str, message: str):
        """发送消息
        
        Args:
            chat_id: 目标聊天ID
            message: 消息内容
        """
        if not self.manager:
            logger.error("客户端管理器未初始化")
            return
            
        # 获取已授权客户端
        authorized_workers = self.manager.get_authorized_workers()
        if not authorized_workers:
            logger.error("没有可用的已授权客户端")
            return
            
        # 选择第一个已授权客户端
        worker = authorized_workers[0]
        logger.info(f"使用客户端: {worker.user_info['username'] or worker.user_info['phone']}")
        
        # 发送消息
        success, result = await self.message_service.send_text_message(
            client=worker.client,
            chat_id=chat_id,
            text=message
        )
        
        if success:
            logger.info(f"消息发送成功，消息ID: {result.id}")
        else:
            logger.error(f"消息发送失败: {result}")
            
    async def invite_to_group(self, group_id: str, user_ids: List[str]):
        """邀请用户到群组
        
        Args:
            group_id: 群组ID
            user_ids: 用户ID列表
        """
        if not self.manager:
            logger.error("客户端管理器未初始化")
            return
            
        # 获取已授权客户端
        authorized_workers = self.manager.get_authorized_workers()
        if not authorized_workers:
            logger.error("没有可用的已授权客户端")
            return
            
        # 选择第一个已授权客户端
        worker = authorized_workers[0]
        logger.info(f"使用客户端: {worker.user_info['username'] or worker.user_info['phone']}")
        
        # 邀请用户到群组
        success, result = await self.group_service.invite_to_group(
            client=worker.client,
            group_id=group_id,
            user_ids=user_ids
        )
        
        if success:
            logger.info(f"邀请用户结果: 总计 {result['total']}，成功 {result['success']}，失败 {result['failed']}")
        else:
            logger.error(f"邀请用户失败: {result['error'] if 'error' in result else result}")
            
    async def get_group_members(self, group_id: str, limit: int = 100):
        """获取群组成员
        
        Args:
            group_id: 群组ID
            limit: 获取数量限制
        """
        if not self.manager:
            logger.error("客户端管理器未初始化")
            return
            
        # 获取已授权客户端
        authorized_workers = self.manager.get_authorized_workers()
        if not authorized_workers:
            logger.error("没有可用的已授权客户端")
            return
            
        # 选择第一个已授权客户端
        worker = authorized_workers[0]
        logger.info(f"使用客户端: {worker.user_info['username'] or worker.user_info['phone']}")
        
        # 获取群组成员
        success, result = await self.group_service.get_group_members(
            client=worker.client,
            group_id=group_id,
            limit=limit
        )
        
        if success:
            logger.info(f"获取到 {len(result)} 个群组成员")
            
            # 打印前10个成员
            for i, member in enumerate(result[:10], 1):
                username = member['username']
                name = f"{member['first_name']} {member['last_name']}".strip()
                print(f"{i}. {name} (@{username})" if username else f"{i}. {name}")
                
            # 保存到文件
            file_path = f"members_{group_id.replace('/', '_')}.txt"
            with open(file_path, 'w', encoding='utf-8') as f:
                for member in result:
                    username = member['username']
                    name = f"{member['first_name']} {member['last_name']}".strip()
                    line = f"{name} (@{username})" if username else name
                    f.write(f"{line}\n")
                    
            logger.info(f"已保存成员列表到文件: {file_path}")
        else:
            logger.error(f"获取群组成员失败: {result}")
            
    async def close(self):
        """关闭客户端管理器"""
        if self.manager:
            await self.manager.stop_all()
            logger.info("已关闭所有客户端")

def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='Telegram客户端管理工具')
    
    # 全局参数
    parser.add_argument('--session-dir', default=DEFAULT_SESSION_DIR, help='会话文件目录')
    parser.add_argument('--proxy-type', default='socks5', choices=['socks5', 'http', 'none'], help='代理类型')
    parser.add_argument('--proxy-ip', default='127.0.0.1', help='代理IP地址')
    parser.add_argument('--proxy-port', type=int, default=1080, help='代理端口')
    
    # 子命令
    subparsers = parser.add_subparsers(dest='command', help='子命令')
    
    # 列出客户端
    list_parser = subparsers.add_parser('list', help='列出所有客户端')
    
    # 登录客户端
    login_parser = subparsers.add_parser('login', help='登录客户端')
    login_parser.add_argument('phone', help='手机号码，包含国家代码，如 +86123456789')
    
    # 发送消息
    send_parser = subparsers.add_parser('send', help='发送消息')
    send_parser.add_argument('chat_id', help='目标聊天ID，可以是用户名或数字ID')
    send_parser.add_argument('message', help='消息内容')
    
    # 邀请用户到群组
    invite_parser = subparsers.add_parser('invite', help='邀请用户到群组')
    invite_parser.add_argument('group_id', help='群组ID，可以是用户名或数字ID')
    invite_parser.add_argument('users', nargs='+', help='用户ID列表，可以是用户名或数字ID')
    
    # 获取群组成员
    members_parser = subparsers.add_parser('members', help='获取群组成员')
    members_parser.add_argument('group_id', help='群组ID，可以是用户名或数字ID')
    members_parser.add_argument('--limit', type=int, default=200, help='获取数量限制')
    
    return parser.parse_args()

async def main():
    """主函数"""
    args = parse_args()
    
    # 配置代理
    proxy_config = None
    if args.proxy_type != 'none':
        proxy_config = {
            'type': args.proxy_type,
            'ip': args.proxy_ip,
            'port': args.proxy_port
        }
    
    # 创建客户端界面
    cli = TelegramClientCLI(
        api_id=API_ID,
        api_hash=API_HASH,
        session_dir=args.session_dir,
        proxy=proxy_config
    )
    
    # 初始化
    await cli.initialize()
    
    try:
        # 根据命令执行对应操作
        if args.command == 'list':
            await cli.list_clients()
        elif args.command == 'login':
            await cli.login_client(args.phone)
        elif args.command == 'send':
            await cli.send_message(args.chat_id, args.message)
        elif args.command == 'invite':
            await cli.invite_to_group(args.group_id, args.users)
        elif args.command == 'members':
            await cli.get_group_members(args.group_id, args.limit)
        else:
            # 默认列出客户端
            await cli.list_clients()
    finally:
        # 关闭客户端
        await cli.close()

if __name__ == "__main__":
    asyncio.run(main()) 