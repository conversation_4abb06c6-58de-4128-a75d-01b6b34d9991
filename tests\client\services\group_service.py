#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
群组服务模块
负责群组管理、成员添加、踢人等功能
"""

from typing import Dict, List, Optional, Tuple, Union, Any, Set

from telethon import TelegramClient
from telethon.tl import types, functions
from telethon.errors import (
    ChatAdminRequiredError, UserPrivacyRestrictedError, 
    UserNotMutualContactError, UserBannedInChannelError, 
    FloodWaitError, UserAlreadyParticipantError, PeerFloodError
)

from utils.logger import get_logger
from tests.client.utils.error_handler import handle_telegram_errors, retry_operation
from tests.client.utils.cache import entity_cache

class GroupService:
    """群组服务类"""
    
    def __init__(self):
        """初始化群组服务"""
        self._logger = get_logger("client.services.group")
    
    @handle_telegram_errors
    async def invite_to_group(
        self, 
        client: TelegramClient, 
        group_id: Union[int, str], 
        user_ids: Union[List[Union[int, str]], Union[int, str]],
        add_limit: int = 50
    ) -> Tuple[bool, Dict[str, Any]]:
        """邀请用户到群组
        
        Args:
            client: Telegram客户端
            group_id: 群组ID或用户名
            user_ids: 用户ID或用户名列表
            add_limit: 单次操作最大添加数量
            
        Returns:
            (成功标志, 结果统计)
        """
        # 确保user_ids是列表
        if not isinstance(user_ids, list):
            user_ids = [user_ids]
            
        if not user_ids:
            return False, {"error": "没有提供用户ID"}
            
        # 限制单次添加数量
        if len(user_ids) > add_limit:
            return False, {"error": f"单次添加用户数量不能超过 {add_limit}"}
            
        self._logger.info(f"准备邀请 {len(user_ids)} 个用户到群组 {group_id}")
        
        try:
            # 获取群组实体
            group_entity = await client.get_entity(group_id)
            
            # 统计结果
            results = {
                "total": len(user_ids),
                "success": 0,
                "failed": 0,
                "details": []
            }
            
            # 检查群组类型并使用对应方法添加用户
            for user_id in user_ids:
                try:
                    # 获取用户实体
                    user_entity = await client.get_entity(user_id)
                    
                    if isinstance(group_entity, types.Channel):
                        # 超级群组或频道
                        if group_entity.broadcast and not group_entity.megagroup:
                            # 纯频道不能添加用户，只能使用邀请链接
                            detail = {
                                "user_id": str(user_id),
                                "success": False,
                                "error": "不能直接添加用户到广播频道，请使用邀请链接"
                            }
                            results["details"].append(detail)
                            results["failed"] += 1
                            continue
                            
                        # 超级群组可以添加用户
                        await client(functions.channels.InviteToChannelRequest(
                            channel=group_entity,
                            users=[user_entity]
                        ))
                    else:
                        # 普通群组
                        await client(functions.messages.AddChatUserRequest(
                            chat_id=group_entity.id,
                            user_id=user_entity,
                            fwd_limit=100
                        ))
                        
                    # 记录成功
                    detail = {
                        "user_id": str(user_id),
                        "success": True
                    }
                    results["details"].append(detail)
                    results["success"] += 1
                    
                    # 短暂延迟，避免触发限制
                    import asyncio
                    await asyncio.sleep(1)
                    
                except UserPrivacyRestrictedError:
                    detail = {
                        "user_id": str(user_id),
                        "success": False,
                        "error": "用户隐私设置限制"
                    }
                    results["details"].append(detail)
                    results["failed"] += 1
                    
                except UserNotMutualContactError:
                    detail = {
                        "user_id": str(user_id),
                        "success": False,
                        "error": "非互相联系人"
                    }
                    results["details"].append(detail)
                    results["failed"] += 1
                    
                except UserAlreadyParticipantError:
                    detail = {
                        "user_id": str(user_id),
                        "success": False,
                        "error": "用户已经是群组成员"
                    }
                    results["details"].append(detail)
                    results["failed"] += 1
                    
                except PeerFloodError:
                    detail = {
                        "user_id": str(user_id),
                        "success": False,
                        "error": "添加用户过于频繁，触发限制"
                    }
                    results["details"].append(detail)
                    results["failed"] += 1
                    
                    # 触发洪水限制时立即退出
                    break
                    
                except Exception as e:
                    detail = {
                        "user_id": str(user_id),
                        "success": False,
                        "error": str(e)
                    }
                    results["details"].append(detail)
                    results["failed"] += 1
            
            self._logger.info(f"邀请用户完成：成功 {results['success']}，失败 {results['failed']}")
            return True, results
            
        except ChatAdminRequiredError:
            self._logger.error(f"邀请用户失败：需要管理员权限")
            return False, {"error": "需要管理员权限"}
            
        except FloodWaitError as e:
            self._logger.error(f"邀请用户被限制：需要等待 {e.seconds} 秒")
            return False, {"error": f"请求频率限制，需要等待 {e.seconds} 秒"}
            
        except Exception as e:
            self._logger.error(f"邀请用户失败：{e}")
            return False, {"error": str(e)}
    
    @handle_telegram_errors
    async def join_group(
        self, 
        client: TelegramClient, 
        group_id: Union[int, str],
        hash: str = None
    ) -> Tuple[bool, Union[Dict[str, Any], str]]:
        """加入群组
        
        Args:
            client: Telegram客户端
            group_id: 群组ID、用户名或邀请链接
            hash: 邀请链接的哈希部分
            
        Returns:
            (成功标志, 结果信息)
        """
        self._logger.info(f"准备加入群组: {group_id}")
        
        try:
            # 处理不同类型的群组标识
            if isinstance(group_id, str) and ('t.me/' in group_id or 'telegram.me/' in group_id):
                # 处理邀请链接
                if 'joinchat' in group_id:
                    # 提取邀请链接哈希
                    import re
                    match = re.search(r't(?:elegram)?\.me/(?:joinchat/)?([a-zA-Z0-9_-]+)', group_id)
                    if match:
                        invite_hash = match.group(1)
                        result = await client(functions.messages.ImportChatInviteRequest(invite_hash))
                        self._logger.info(f"成功通过邀请链接加入群组")
                        return True, {"result": "success", "updates": result}
                else:
                    # 提取公共用户名
                    import re
                    match = re.search(r't(?:elegram)?\.me/([a-zA-Z0-9_]+)', group_id)
                    if match:
                        group_id = match.group(1)
            
            # 获取群组实体
            entity = await client.get_entity(group_id)
            
            if isinstance(entity, types.Channel):
                # 加入频道或超级群组
                result = await client(functions.channels.JoinChannelRequest(
                    channel=entity
                ))
                self._logger.info(f"成功加入群组 {group_id}")
                return True, {"result": "success", "updates": result}
            else:
                # 普通群组不能直接加入，需要邀请
                return False, "普通群组需要邀请才能加入"
                
        except FloodWaitError as e:
            self._logger.error(f"加入群组被限制，需要等待 {e.seconds} 秒")
            return False, f"请求频率限制，需要等待 {e.seconds} 秒"
            
        except UserBannedInChannelError:
            self._logger.error(f"加入群组失败：用户被封禁")
            return False, "用户被封禁，无法加入该群组"
            
        except Exception as e:
            self._logger.error(f"加入群组失败：{e}")
            return False, str(e)
    
    @handle_telegram_errors
    async def leave_group(
        self, 
        client: TelegramClient, 
        group_id: Union[int, str]
    ) -> Tuple[bool, str]:
        """离开群组
        
        Args:
            client: Telegram客户端
            group_id: 群组ID或用户名
            
        Returns:
            (成功标志, 结果信息)
        """
        self._logger.info(f"准备离开群组: {group_id}")
        
        try:
            # 获取群组实体
            entity = await client.get_entity(group_id)
            
            if isinstance(entity, types.Channel):
                # 离开频道或超级群组
                await client(functions.channels.LeaveChannelRequest(
                    channel=entity
                ))
                self._logger.info(f"成功离开群组 {group_id}")
                return True, "成功离开群组"
            else:
                # 离开普通群组
                await client(functions.messages.DeleteChatUserRequest(
                    chat_id=entity.id,
                    user_id='me'
                ))
                self._logger.info(f"成功离开群组 {group_id}")
                return True, "成功离开群组"
                
        except Exception as e:
            self._logger.error(f"离开群组失败：{e}")
            return False, str(e)
    
    @handle_telegram_errors
    async def get_group_members(
        self, 
        client: TelegramClient, 
        group_id: Union[int, str],
        limit: int = 200,
        search: str = '',
        filter: str = None
    ) -> Tuple[bool, Union[List[Dict[str, Any]], str]]:
        """获取群组成员
        
        Args:
            client: Telegram客户端
            group_id: 群组ID或用户名
            limit: 获取成员数量限制
            search: 搜索关键词
            filter: 成员类型过滤器
            
        Returns:
            (成功标志, 成员列表或错误信息)
        """
        self._logger.info(f"获取群组 {group_id} 的成员列表")
        
        try:
            # 获取群组实体
            entity = await client.get_entity(group_id)
            
            # 转换过滤器类型
            filter_obj = None
            if filter == 'admin':
                filter_obj = types.ChannelParticipantsAdmins()
            elif filter == 'bot':
                filter_obj = types.ChannelParticipantsBots()
            elif filter == 'banned':
                filter_obj = types.ChannelParticipantsBanned()
            
            # 获取成员
            participants = await client.get_participants(
                entity,
                limit=limit,
                search=search,
                filter=filter_obj
            )
            
            # 转换为易于序列化的字典
            result = []
            for p in participants:
                member = {
                    'id': p.id,
                    'first_name': p.first_name or '',
                    'last_name': p.last_name or '',
                    'username': p.username or '',
                    'phone': getattr(p, 'phone', '') or '',
                    'bot': p.bot,
                    'mutual_contact': p.mutual_contact
                }
                
                result.append(member)
            
            self._logger.info(f"成功获取 {len(result)} 个群组成员")
            return True, result
            
        except Exception as e:
            self._logger.error(f"获取群组成员失败：{e}")
            return False, str(e)
    
    @handle_telegram_errors
    async def kick_from_group(
        self, 
        client: TelegramClient, 
        group_id: Union[int, str],
        user_id: Union[int, str],
        ban: bool = False
    ) -> Tuple[bool, str]:
        """从群组踢出用户
        
        Args:
            client: Telegram客户端
            group_id: 群组ID或用户名
            user_id: 用户ID或用户名
            ban: 是否封禁用户
            
        Returns:
            (成功标志, 结果信息)
        """
        self._logger.info(f"准备从群组 {group_id} 踢出用户 {user_id}")
        
        try:
            # 获取群组和用户实体
            group_entity = await client.get_entity(group_id)
            user_entity = await client.get_entity(user_id)
            
            if isinstance(group_entity, types.Channel):
                # 从频道或超级群组踢出
                rights = types.ChatBannedRights(
                    until_date=None,  # 永久
                    view_messages=True,  # 禁止查看消息
                    send_messages=True,  # 禁止发送消息
                    send_media=True,  # 禁止发送媒体
                    send_stickers=True,  # 禁止发送贴纸
                    send_gifs=True,  # 禁止发送GIF
                    send_games=True,  # 禁止发送游戏
                    send_inline=True,  # 禁止使用内联机器人
                    embed_links=True,  # 禁止嵌入链接
                    send_polls=True,  # 禁止发送投票
                    change_info=True,  # 禁止修改信息
                    invite_users=True,  # 禁止邀请用户
                    pin_messages=True,  # 禁止置顶消息
                )
                
                if ban:
                    # 封禁用户
                    await client(functions.channels.EditBannedRequest(
                        channel=group_entity,
                        participant=user_entity,
                        banned_rights=rights
                    ))
                    self._logger.info(f"成功将用户 {user_id} 封禁出群组 {group_id}")
                    return True, "成功将用户封禁出群组"
                else:
                    # 只踢出用户
                    await client(functions.channels.EditBannedRequest(
                        channel=group_entity,
                        participant=user_entity,
                        banned_rights=rights
                    ))
                    # 解除封禁
                    await client(functions.channels.EditBannedRequest(
                        channel=group_entity,
                        participant=user_entity,
                        banned_rights=types.ChatBannedRights(until_date=None)
                    ))
                    self._logger.info(f"成功将用户 {user_id} 踢出群组 {group_id}")
                    return True, "成功将用户踢出群组"
            else:
                # 从普通群组踢出
                await client(functions.messages.DeleteChatUserRequest(
                    chat_id=group_entity.id,
                    user_id=user_entity
                ))
                self._logger.info(f"成功将用户 {user_id} 踢出群组 {group_id}")
                return True, "成功将用户踢出群组"
                
        except ChatAdminRequiredError:
            self._logger.error(f"踢出用户失败：需要管理员权限")
            return False, "需要管理员权限"
            
        except Exception as e:
            self._logger.error(f"踢出用户失败：{e}")
            return False, str(e) 