#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
消息服务模块
负责消息发送、转发等功能
"""

import os
from typing import Dict, List, Optional, Tuple, Union, Any

from telethon import TelegramClient
from telethon.tl import types, functions
from telethon.errors import FloodWaitError, UserDeactivatedBanError

from utils.logger import get_logger
from tests.client.utils.error_handler import handle_telegram_errors, retry_operation
from tests.client.utils.cache import entity_cache

class MessageService:
    """消息服务类"""
    
    def __init__(self):
        """初始化消息服务"""
        self._logger = get_logger("client.services.message")
    
    @handle_telegram_errors
    async def send_text_message(
        self, 
        client: TelegramClient, 
        chat_id: Union[int, str], 
        text: str, 
        parse_mode: Optional[str] = None
    ) -> Tuple[bool, Union[types.Message, str]]:
        """发送文本消息
        
        Args:
            client: Telegram客户端
            chat_id: 目标对话ID (可以是int或username)
            text: 消息文本
            parse_mode: 'md' for Markdown, 'html' for HTML
            
        Returns:
            (成功标志, Telethon Message 对象或错误消息)
        """
        self._logger.info(f"准备发送文本消息到 {chat_id}")
        
        try:
            # 获取目标实体
            target_entity = await self._get_entity(client, chat_id)
            
            # 发送消息
            sent_message = await client.send_message(
                target_entity, 
                text, 
                parse_mode=parse_mode
            )
            
            self._logger.info(f"成功发送消息到 {chat_id}, 消息ID: {sent_message.id}")
            return True, sent_message
            
        except FloodWaitError as e:
            self._logger.error(f"发送消息被限制: {chat_id}, 等待 {e.seconds} 秒")
            raise
        except UserDeactivatedBanError as e:
            self._logger.error(f"账户已被封禁，无法发送消息: {e}")
            raise
        except Exception as e:
            self._logger.error(f"发送消息失败: {chat_id}, {e}")
            raise
    
    @handle_telegram_errors
    async def send_file_message(
        self, 
        client: TelegramClient, 
        chat_id: Union[int, str], 
        file_path: str, 
        caption: Optional[str] = None, 
        media_type: Optional[Any] = None,
        progress_callback: Optional[Any] = None,
        file_name: Optional[str] = None,
        parse_mode: Optional[str] = None
    ) -> Tuple[bool, Union[types.Message, str]]:
        """发送文件/媒体消息
        
        Args:
            client: Telegram客户端
            chat_id: 目标对话ID
            file_path: 本地文件路径
            caption: 媒体说明
            media_type: 媒体类型
            progress_callback: 上传进度回调
            file_name: 自定义文件名
            parse_mode: 标题的解析模式
            
        Returns:
            (成功标志, Telethon Message 对象或错误消息)
        """
        self._logger.info(f"准备发送文件到 {chat_id}: {file_path}")
        
        if not os.path.exists(file_path):
            return False, f"文件不存在: {file_path}"
        
        try:
            # 获取目标实体
            target_entity = await self._get_entity(client, chat_id)
            
            # 准备文件属性
            attributes = []
            if file_name:
                attributes.append(types.DocumentAttributeFilename(file_name=file_name))
            
            # 发送文件
            sent_message = await client.send_file(
                target_entity,
                file_path,
                caption=caption,
                attributes=attributes if attributes else None,
                progress_callback=progress_callback,
                parse_mode=parse_mode
            )
            
            self._logger.info(f"成功发送文件到 {chat_id}, 消息ID: {sent_message.id}")
            return True, sent_message
            
        except FloodWaitError as e:
            self._logger.error(f"发送文件被限制: {chat_id}, 等待 {e.seconds} 秒")
            raise
        except UserDeactivatedBanError as e:
            self._logger.error(f"账户已被封禁，无法发送文件: {e}")
            raise
        except Exception as e:
            self._logger.error(f"发送文件失败: {chat_id}, {e}")
            raise
    
    @handle_telegram_errors
    async def forward_messages(
        self, 
        client: TelegramClient, 
        chat_id: Union[int, str], 
        from_chat_id: Union[int, str], 
        message_ids: List[int], 
        drop_author: bool = False
    ) -> Tuple[bool, Union[List[types.Message], str]]:
        """转发消息
        
        Args:
            client: Telegram客户端
            chat_id: 目标对话ID
            from_chat_id: 原始消息所在对话ID
            message_ids: 要转发的消息ID列表
            drop_author: 是否隐藏原始发送者
            
        Returns:
            (成功标志, 成功时是 Telethon Message 对象列表, 失败时是错误消息)
        """
        self._logger.info(f"准备从 {from_chat_id} 转发消息到 {chat_id}, 消息IDs: {message_ids}")
        
        if not message_ids:
            return False, "没有提供消息ID进行转发"
        
        try:
            # 获取目标实体
            target_entity = await self._get_entity(client, chat_id)
            source_entity = await self._get_entity(client, from_chat_id)
            
            # 转发消息
            sent_messages = await client.forward_messages(
                entity=target_entity,
                messages=message_ids,
                from_peer=source_entity,
                silent=drop_author
            )
            
            # 处理返回结果
            if not isinstance(sent_messages, list):
                sent_messages = [sent_messages]
                
            sent_message_ids = [m.id for m in sent_messages if m]
            self._logger.info(f"成功转发消息, 新消息IDs: {sent_message_ids}")
            return True, sent_messages
            
        except FloodWaitError as e:
            self._logger.error(f"转发消息被限制, 等待 {e.seconds} 秒")
            raise
        except UserDeactivatedBanError as e:
            self._logger.error(f"账户已被封禁，无法转发消息: {e}")
            raise
        except Exception as e:
            self._logger.error(f"转发消息失败: {e}")
            raise
    
    @handle_telegram_errors
    async def get_message_history(
        self,
        client: TelegramClient,
        chat_id: Union[int, str],
        limit: int = 100,
        offset_id: int = 0,
        min_id: int = 0,
        max_id: int = 0
    ) -> Tuple[bool, Union[List[types.Message], str]]:
        """获取消息历史
        
        Args:
            client: Telegram客户端
            chat_id: 对话ID
            limit: 获取消息数量限制
            offset_id: 开始获取的消息ID
            min_id: 最小消息ID
            max_id: 最大消息ID
            
        Returns:
            (成功标志, 消息列表或错误消息)
        """
        self._logger.info(f"获取对话 {chat_id} 的消息历史")
        
        try:
            # 获取对话实体
            entity = await self._get_entity(client, chat_id)
            
            # 获取消息历史
            messages = await client.get_messages(
                entity,
                limit=limit,
                offset_id=offset_id,
                min_id=min_id,
                max_id=max_id
            )
            
            self._logger.info(f"成功获取 {len(messages)} 条消息历史")
            return True, messages
            
        except Exception as e:
            self._logger.error(f"获取消息历史失败: {e}")
            raise
    
    @handle_telegram_errors
    async def delete_messages(
        self,
        client: TelegramClient,
        chat_id: Union[int, str],
        message_ids: List[int],
        revoke: bool = True
    ) -> Tuple[bool, str]:
        """删除消息
        
        Args:
            client: Telegram客户端
            chat_id: 对话ID
            message_ids: 要删除的消息ID列表
            revoke: 是否对所有人撤回消息
            
        Returns:
            (成功标志, 结果消息)
        """
        self._logger.info(f"删除对话 {chat_id} 中的消息: {message_ids}")
        
        if not message_ids:
            return False, "没有提供消息ID进行删除"
        
        try:
            # 获取对话实体
            entity = await self._get_entity(client, chat_id)
            
            # 删除消息
            await client.delete_messages(
                entity,
                message_ids,
                revoke=revoke
            )
            
            self._logger.info(f"成功删除 {len(message_ids)} 条消息")
            return True, f"成功删除 {len(message_ids)} 条消息"
            
        except Exception as e:
            self._logger.error(f"删除消息失败: {e}")
            raise
    
    async def _get_entity(self, client: TelegramClient, entity_id: Union[int, str]):
        """获取实体对象，带缓存
        
        Args:
            client: Telegram客户端
            entity_id: 实体ID或用户名
            
        Returns:
            实体对象
        """
        # 缓存键
        cache_key = f"entity_{entity_id}"
        
        # 尝试从缓存获取
        entity = entity_cache.get(cache_key)
        if entity:
            return entity
        
        # 缓存未命中，获取实体
        entity = await client.get_entity(entity_id)
        
        # 缓存实体
        entity_cache.set(cache_key, entity)
        
        return entity 