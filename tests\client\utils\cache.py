#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
缓存工具模块
提供内存缓存和持久化缓存功能
"""

import time
import json
import os
import pickle
from typing import Dict, Any, Optional, Callable, TypeVar, Generic, Union
import functools
import asyncio

T = TypeVar('T')

class MemoryCache(Generic[T]):
    """内存缓存类，支持过期时间"""
    
    def __init__(self, ttl: int = 600):
        """初始化内存缓存
        
        Args:
            ttl: 缓存生存时间（秒），默认10分钟
        """
        self._cache: Dict[str, Dict[str, Any]] = {}
        self._ttl = ttl
    
    def get(self, key: str) -> Optional[T]:
        """获取缓存值
        
        Args:
            key: 缓存键
            
        Returns:
            缓存值，如果不存在或已过期返回None
        """
        if key not in self._cache:
            return None
            
        cache_data = self._cache[key]
        # 检查是否过期
        if time.time() > cache_data['expire_time']:
            del self._cache[key]
            return None
            
        return cache_data['value']
    
    def set(self, key: str, value: T, ttl: Optional[int] = None) -> None:
        """设置缓存值
        
        Args:
            key: 缓存键
            value: 缓存值
            ttl: 此项的生存时间（秒），不指定则使用默认值
        """
        expire_time = time.time() + (ttl if ttl is not None else self._ttl)
        self._cache[key] = {
            'value': value,
            'expire_time': expire_time
        }
    
    def delete(self, key: str) -> bool:
        """删除缓存项
        
        Args:
            key: 缓存键
            
        Returns:
            是否成功删除
        """
        if key in self._cache:
            del self._cache[key]
            return True
        return False
    
    def clear(self) -> None:
        """清空缓存"""
        self._cache.clear()
    
    def cleanup(self) -> int:
        """清理过期缓存
        
        Returns:
            清理的缓存项数量
        """
        current_time = time.time()
        expired_keys = [
            key for key, data in self._cache.items()
            if current_time > data['expire_time']
        ]
        
        for key in expired_keys:
            del self._cache[key]
            
        return len(expired_keys)

class PersistentCache:
    """持久化缓存类，支持将缓存保存到文件"""
    
    def __init__(self, cache_dir: str, ttl: int = 3600):
        """初始化持久化缓存
        
        Args:
            cache_dir: 缓存目录
            ttl: 缓存生存时间（秒），默认1小时
        """
        self._cache_dir = cache_dir
        self._ttl = ttl
        
        # 确保缓存目录存在
        os.makedirs(cache_dir, exist_ok=True)
    
    def _get_cache_path(self, key: str) -> str:
        """获取缓存文件路径
        
        Args:
            key: 缓存键
            
        Returns:
            缓存文件路径
        """
        # 将可能包含非法字符的键转换为安全的文件名
        safe_key = ''.join(c if c.isalnum() else '_' for c in key)
        return os.path.join(self._cache_dir, f"{safe_key}.cache")
    
    def get(self, key: str) -> Optional[Any]:
        """获取缓存值
        
        Args:
            key: 缓存键
            
        Returns:
            缓存值，如果不存在或已过期返回None
        """
        cache_path = self._get_cache_path(key)
        
        if not os.path.exists(cache_path):
            return None
            
        try:
            with open(cache_path, 'rb') as f:
                cache_data = pickle.load(f)
                
            # 检查是否过期
            if time.time() > cache_data['expire_time']:
                os.remove(cache_path)
                return None
                
            return cache_data['value']
        except Exception:
            # 如果读取出错，删除可能损坏的缓存文件
            if os.path.exists(cache_path):
                os.remove(cache_path)
            return None
    
    def set(self, key: str, value: Any, ttl: Optional[int] = None) -> None:
        """设置缓存值
        
        Args:
            key: 缓存键
            value: 缓存值
            ttl: 此项的生存时间（秒），不指定则使用默认值
        """
        cache_path = self._get_cache_path(key)
        expire_time = time.time() + (ttl if ttl is not None else self._ttl)
        
        cache_data = {
            'value': value,
            'expire_time': expire_time
        }
        
        with open(cache_path, 'wb') as f:
            pickle.dump(cache_data, f)
    
    def delete(self, key: str) -> bool:
        """删除缓存项
        
        Args:
            key: 缓存键
            
        Returns:
            是否成功删除
        """
        cache_path = self._get_cache_path(key)
        
        if os.path.exists(cache_path):
            os.remove(cache_path)
            return True
        return False
    
    def clear(self) -> None:
        """清空缓存"""
        for filename in os.listdir(self._cache_dir):
            if filename.endswith('.cache'):
                os.remove(os.path.join(self._cache_dir, filename))
    
    def cleanup(self) -> int:
        """清理过期缓存
        
        Returns:
            清理的缓存项数量
        """
        count = 0
        current_time = time.time()
        
        for filename in os.listdir(self._cache_dir):
            if not filename.endswith('.cache'):
                continue
                
            cache_path = os.path.join(self._cache_dir, filename)
            
            try:
                with open(cache_path, 'rb') as f:
                    cache_data = pickle.load(f)
                    
                if current_time > cache_data['expire_time']:
                    os.remove(cache_path)
                    count += 1
            except Exception:
                # 如果读取出错，删除可能损坏的缓存文件
                os.remove(cache_path)
                count += 1
                
        return count

def cached(cache_instance, key_func=None, ttl=None):
    """函数结果缓存装饰器
    
    Args:
        cache_instance: 缓存实例
        key_func: 缓存键生成函数，默认使用函数名和参数生成
        ttl: 缓存生存时间
        
    Returns:
        装饰器函数
    """
    def decorator(func):
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            if key_func:
                cache_key = key_func(*args, **kwargs)
            else:
                # 默认使用函数名和参数的字符串表示作为缓存键
                args_str = ','.join(str(arg) for arg in args)
                kwargs_str = ','.join(f"{k}={v}" for k, v in sorted(kwargs.items()))
                cache_key = f"{func.__name__}:{args_str}:{kwargs_str}"
                
            # 尝试从缓存获取
            cached_result = cache_instance.get(cache_key)
            if cached_result is not None:
                return cached_result
                
            # 缓存未命中，执行函数
            result = func(*args, **kwargs)
            
            # 缓存结果
            cache_instance.set(cache_key, result, ttl)
            
            return result
            
        @functools.wraps(func)
        async def async_wrapper(*args, **kwargs):
            if key_func:
                cache_key = key_func(*args, **kwargs)
            else:
                # 默认使用函数名和参数的字符串表示作为缓存键
                args_str = ','.join(str(arg) for arg in args)
                kwargs_str = ','.join(f"{k}={v}" for k, v in sorted(kwargs.items()))
                cache_key = f"{func.__name__}:{args_str}:{kwargs_str}"
                
            # 尝试从缓存获取
            cached_result = cache_instance.get(cache_key)
            if cached_result is not None:
                return cached_result
                
            # 缓存未命中，执行函数
            result = await func(*args, **kwargs)
            
            # 缓存结果
            cache_instance.set(cache_key, result, ttl)
            
            return result
            
        # 根据函数是否是协程函数选择包装器
        if asyncio.iscoroutinefunction(func):
            return async_wrapper
        else:
            return wrapper
            
    return decorator

# 创建全局缓存实例
entity_cache = MemoryCache(ttl=1800)  # 实体缓存，30分钟有效期
session_cache = MemoryCache(ttl=86400)  # 会话缓存，24小时有效期 