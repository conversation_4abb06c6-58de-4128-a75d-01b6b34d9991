#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
错误处理工具
提供异常处理装饰器和辅助函数
"""

import functools
import traceback
import logging
from typing import Callable, Any, Tuple, Union, TypeVar

# 定义类型变量
T = TypeVar('T')
R = TypeVar('R')

def log_exceptions(logger: logging.Logger) -> Callable[[Callable[..., T]], Callable[..., T]]:
    """异常日志装饰器
    
    Args:
        logger: 日志记录器
        
    Returns:
        装饰器函数
    """
    def decorator(func: Callable[..., T]) -> Callable[..., T]:
        @functools.wraps(func)
        def wrapper(*args, **kwargs) -> T:
            try:
                return func(*args, **kwargs)
            except Exception as e:
                logger.error(
                    f"函数 {func.__name__} 执行出错: {str(e)}\n"
                    f"参数: {args}, {kwargs}\n"
                    f"堆栈: {traceback.format_exc()}"
                )
                raise
        return wrapper
    return decorator

def async_log_exceptions(logger: logging.Logger) -> Callable[[Callable[..., T]], Callable[..., T]]:
    """异步函数异常日志装饰器
    
    Args:
        logger: 日志记录器
        
    Returns:
        装饰器函数
    """
    def decorator(func: Callable[..., T]) -> Callable[..., T]:
        @functools.wraps(func)
        async def wrapper(*args, **kwargs) -> T:
            try:
                return await func(*args, **kwargs)
            except Exception as e:
                logger.error(
                    f"异步函数 {func.__name__} 执行出错: {str(e)}\n"
                    f"参数: {args}, {kwargs}\n"
                    f"堆栈: {traceback.format_exc()}"
                )
                raise
        return wrapper
    return decorator

def handle_telegram_errors(func: Callable[..., Tuple[bool, R]]) -> Callable[..., Tuple[bool, Union[R, str]]]:
    """处理Telegram错误并返回统一格式
    
    Args:
        func: 原始函数
        
    Returns:
        包装后的函数
    """
    @functools.wraps(func)
    async def wrapper(*args, **kwargs) -> Tuple[bool, Union[R, str]]:
        try:
            return await func(*args, **kwargs)
        except Exception as e:
            error_type = type(e).__name__
            error_msg = str(e)
            
            # 处理特定类型的错误
            if "FloodWaitError" in error_type:
                seconds = getattr(e, 'seconds', 0)
                return False, f"请求频率限制，需要等待 {seconds} 秒"
            elif "UserDeactivatedBanError" in error_type:
                return False, "账户已被封禁"
            elif "AuthKeyError" in error_type:
                return False, "认证密钥错误，请重新登录"
            elif "PhoneCodeInvalidError" in error_type:
                return False, "验证码无效"
            elif "PhoneCodeExpiredError" in error_type:
                return False, "验证码已过期，请重新获取"
            elif "SessionPasswordNeededError" in error_type:
                return False, "需要两步验证密码"
            else:
                return False, f"操作失败: {error_type} - {error_msg}"
    return wrapper

def retry_operation(max_retries: int = 3, delay: float = 1.0):
    """带有重试的操作装饰器
    
    Args:
        max_retries: 最大重试次数
        delay: 重试延迟（秒）
        
    Returns:
        装饰器函数
    """
    def decorator(func: Callable[..., T]) -> Callable[..., T]:
        @functools.wraps(func)
        async def async_wrapper(*args, **kwargs) -> T:
            import asyncio
            last_exception = None
            
            for attempt in range(max_retries):
                try:
                    return await func(*args, **kwargs)
                except Exception as e:
                    last_exception = e
                    # 如果是最后一次尝试，直接抛出异常
                    if attempt == max_retries - 1:
                        raise
                        
                    # 计算重试延迟（指数退避）
                    retry_delay = delay * (2 ** attempt)
                    await asyncio.sleep(retry_delay)
            
            # 不应该运行到这里，但为了类型检查添加
            raise last_exception if last_exception else RuntimeError("未知错误")
            
        @functools.wraps(func)
        def sync_wrapper(*args, **kwargs) -> T:
            import time
            last_exception = None
            
            for attempt in range(max_retries):
                try:
                    return func(*args, **kwargs)
                except Exception as e:
                    last_exception = e
                    # 如果是最后一次尝试，直接抛出异常
                    if attempt == max_retries - 1:
                        raise
                        
                    # 计算重试延迟（指数退避）
                    retry_delay = delay * (2 ** attempt)
                    time.sleep(retry_delay)
            
            # 不应该运行到这里，但为了类型检查添加
            raise last_exception if last_exception else RuntimeError("未知错误")
            
        # 根据函数是否是协程函数选择包装器
        if asyncio.iscoroutinefunction(func):
            return async_wrapper
        else:
            return sync_wrapper
            
    return decorator 