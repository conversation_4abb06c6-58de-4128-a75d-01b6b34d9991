import os
from PySide6.QtGui import QPixmap, QImage
from PySide6.QtCore import Qt, QByteArray, Signal, QObject, QUrl
from PySide6.QtNetwork import QNetworkAccessManager, QNetworkRequest, QNetworkReply


class NetworkImage(QObject):
    """网络图片加载组件，将网络图片转换为PySide6可显示的QPixmap格式"""
    
    # 信号定义
    image_loaded = Signal(QPixmap)  # 图片加载成功信号
    image_error = Signal(str)       # 图片加载失败信号
    
    def __init__(self, parent=None):
        super().__init__(parent)
        # 创建网络管理器
        self._network_manager = QNetworkAccessManager(self)
        # 图片缓存
        self._image_cache = {}
    
    def load_image(self, url, use_cache=True):
        """
        异步加载网络图片并返回QPixmap对象
        
        Args:
            url (str): 图片URL
            use_cache (bool): 是否使用缓存
        """
        # 检查缓存
        if use_cache and url in self._image_cache:
            pixmap = self._image_cache[url]
            self.image_loaded.emit(pixmap)
            return
        
        # 创建网络请求
        request = QNetworkRequest(QUrl(url))
        reply = self._network_manager.get(request)
        
        # 连接信号
        reply.finished.connect(lambda: self._handle_image_loaded(reply, url, use_cache))
    
    def _handle_image_loaded(self, reply, url, use_cache):
        """处理图片加载完成"""
        if reply.error() == QNetworkReply.NetworkError.NoError:
            data = reply.readAll()
            pixmap = self._bytes_to_pixmap(data)
            
            if pixmap and not pixmap.isNull():
                if use_cache:
                    self._image_cache[url] = pixmap
                self.image_loaded.emit(pixmap)
            else:
                self.image_error.emit(f"无法解析图片内容: {url}")
        else:
            self.image_error.emit(f"图片加载失败: {reply.errorString()}")
            
        reply.deleteLater()
    
    def clear_cache(self):
        """清除图片缓存"""
        self._image_cache.clear()
    
    @staticmethod
    def _bytes_to_pixmap(data):
        """将字节数据转换为QPixmap"""
        byte_array = data if isinstance(data, QByteArray) else QByteArray(data)
        image = QImage.fromData(byte_array)
        if not image.isNull():
            return QPixmap.fromImage(image)
        return QPixmap()
