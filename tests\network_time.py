import ntplib
from datetime import datetime, timezone

def get_ntp_time(host='pool.ntp.org'):
    """
    从NTP服务器获取当前UTC时间。
    :param host: NTP服务器地址，默认为 'pool.ntp.org'
    :return: datetime对象 (UTC时间)，如果失败则返回 None
    """
    try:
        client = ntplib.NTPClient()
        response = client.request(host, version=3)
        # response.tx_time 是NTP服务器发送响应时的时间戳 (Unix timestamp)
        utc_dt = datetime.fromtimestamp(response.tx_time, timezone.utc)
        return utc_dt
    except ntplib.NTPException as e:
        print(f"NTP Error: {e}")
        return None
    except Exception as e:
        print(f"An unexpected error occurred: {e}")
        return None

if __name__ == "__main__":
    ntp_time_utc = get_ntp_time()
    if ntp_time_utc:
        print(f"NTP Time (UTC): {ntp_time_utc.strftime('%Y-%m-%d %H:%M:%S.%f %Z%z')}")

        # 如果需要转换为本地时间
        local_time = ntp_time_utc.astimezone()
        print(f"NTP Time (Local): {local_time.strftime('%Y-%m-%d %H:%M:%S.%f %Z%z')}")
    else:
        print("Failed to get NTP time.")
