import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import asyncio
from data.database import get_session
from data.repositories.account_send_limit_repo import AccountSendLimitRepository

async def test_account_send_limit_repo_all():
    async with get_session() as session:
        repo = AccountSendLimitRepository(session)
        phone = "***********"

        # 1. 创建
        limit = await repo.create(phone, max_daily_limit=5)
        print("创建限额:", limit.account_phone, limit.max_daily_limit)
        assert limit.account_phone == phone

        # 2. 查询
        limit2 = await repo.get_by_phone(phone)
        print("查询限额:", limit2.account_phone)
        assert limit2.account_phone == phone

        # 3. 更新计数
        limit3 = await repo.update_count(phone, increment=2)
        print("更新计数:", limit3.current_day_count)
        assert limit3.current_day_count == 2

        # 4. 重置
        limit4 = await repo.reset_count(phone)
        print("重置计数:", limit4.current_day_count)
        assert limit4.current_day_count == 0

        # 5. 设置最大限制
        limit5 = await repo.set_max_limit(phone, 10)
        print("设置最大限制:", limit5.max_daily_limit)
        assert limit5.max_daily_limit == 10

def main():
    asyncio.run(test_account_send_limit_repo_all())

if __name__ == "__main__":
    main()