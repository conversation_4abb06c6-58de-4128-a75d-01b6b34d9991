import requests
import socket
import urllib.parse

# 配置代理（替换成你的代理地址，如 Clash 默认是 127.0.0.1:7890）
PROXY = {
    "http": "socks5://127.0.0.1:1080",
    "https": "socks5://127.0.0.1:1080",
}

# 要测试的域名列表（可自行添加）
AUGMENT_DOMAINS = [
    "i1.api.augmentcode.com",
    "api.augmentcode.com",
    "auth.augmentcode.com",
    "www.augmentcode.com"
    # 添加更多子域名...
]

def check_dns(domain):
    """检查 DNS 解析是否走代理（对比本地和代理解析结果）"""
    try:
        # 本地 DNS 解析
        local_ip = socket.gethostbyname(domain)
        
        # 通过代理 DNS 解析（需代理支持）
        session = requests.Session()
        session.proxies = PROXY
        proxy_ip = socket.gethostbyname(domain)
        
        print(f"[DNS] {domain}: 本地解析 → {local_ip}, 代理解析 → {proxy_ip}")
        return local_ip != proxy_ip  # 如果不同，说明可能走代理
    except Exception as e:
        print(f"[DNS] {domain} 解析失败: {e}")
        return False

def check_http(domain):
    """检查 HTTP 请求是否通过代理"""
    try:
        # 不使用代理（预期失败）
        # try:
        #     requests.get(f"https://{domain}", timeout=5)
        #     print(f"[HTTP] {domain}: 未走代理（能直接访问，可能代理未生效！）")
        #     return False
        # except:
        #     pass
        
        # 使用代理（预期成功）
        session = requests.Session()
        session.proxies = PROXY
        resp = session.get(f"https://{domain}", timeout=5)
        print(f"[HTTP] {domain}: 代理访问成功（状态码: {resp.status_code}）")
        return True
    except Exception as e:
        print(f"[HTTP] {domain} 代理访问失败: {e}")
        return False

def main():
    print("=== 开始测试 AugmentCode 域名代理情况 ===")
    for domain in AUGMENT_DOMAINS:
        print(f"\n测试域名: {domain}")
        dns_ok = check_dns(domain)
        http_ok = check_http(domain)
        
        if dns_ok and http_ok:
            print(f"✅ {domain} 已正确走代理")
        else:
            print(f"❌ {domain} 代理可能未生效！")

if __name__ == "__main__":
    main()