#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
批量账户资料更新功能测试
"""

import asyncio
import pytest
from unittest.mock import AsyncMock, MagicMock

from app.services.account_service import AccountService
from data.repositories.account_repo import AccountRepository
from data.models.account import AccountModel


class TestBatchProfileUpdate:
    """批量账户资料更新测试类"""
    
    def setup_method(self):
        """测试前准备"""
        self.account_service = AccountService()
        self.account_service._telegram_worker = AsyncMock()
        
    async def test_username_tag_parsing(self):
        """测试用户名标签解析"""
        # 测试数字标签
        result1 = self.account_service._parse_username_tags("user{数字1-3}")
        assert result1.startswith("user")
        assert len(result1) >= 5  # user + 1位数字
        assert len(result1) <= 7  # user + 3位数字
        
        # 测试字母标签
        result2 = self.account_service._parse_username_tags("test{字母2-4}")
        assert result2.startswith("test")
        assert len(result2) >= 6  # test + 2位字母
        assert len(result2) <= 8  # test + 4位字母
        
        # 测试混合标签
        result3 = self.account_service._parse_username_tags("name{数字字母1-5}")
        assert result3.startswith("name")
        assert len(result3) >= 5  # name + 1位混合
        assert len(result3) <= 9  # name + 5位混合
        
        # 测试多个标签
        result4 = self.account_service._parse_username_tags("user{数字1-2}_{字母1-3}")
        assert "user" in result4
        assert "_" in result4
        
    def test_username_tag_detection(self):
        """测试用户名标签检测"""
        # 包含标签的用户名
        assert self.account_service._has_username_tags("user{数字1-3}")
        assert self.account_service._has_username_tags("test{字母2-4}")
        assert self.account_service._has_username_tags("name{数字字母1-5}")
        
        # 不包含标签的用户名
        assert not self.account_service._has_username_tags("username")
        assert not self.account_service._has_username_tags("test123")
        assert not self.account_service._has_username_tags("")
        
    def test_username_validation(self):
        """测试用户名验证"""
        # 有效用户名
        valid, msg = self.account_service._validate_username("user123")
        assert valid
        assert msg == ""
        
        valid, msg = self.account_service._validate_username("test_user")
        assert valid
        
        # 无效用户名
        valid, msg = self.account_service._validate_username("123user")  # 数字开头
        assert not valid
        assert "数字开头" in msg
        
        valid, msg = self.account_service._validate_username("usr")  # 太短
        assert not valid
        assert "长度" in msg
        
        valid, msg = self.account_service._validate_username("user@test")  # 非法字符
        assert not valid
        assert "字母、数字和下划线" in msg
        
    async def test_batch_update_data_validation(self):
        """测试批量更新数据验证"""
        # 有效数据
        valid, msg = await self.account_service._validate_batch_update_data(
            username="user{数字1-3}",
            daily_msg_limit=50,
            daily_invite_limit=20
        )
        assert valid
        assert msg == ""
        
        # 无效的消息限制
        valid, msg = await self.account_service._validate_batch_update_data(
            daily_msg_limit=1500  # 超出范围
        )
        assert not valid
        assert "消息限制" in msg
        
        # 无效的邀请限制
        valid, msg = await self.account_service._validate_batch_update_data(
            daily_invite_limit=-5  # 负数
        )
        assert not valid
        assert "邀请限制" in msg
        
    async def test_generate_unique_username(self):
        """测试生成唯一用户名"""
        # 模拟账户仓库
        mock_repo = AsyncMock(spec=AccountRepository)
        mock_repo.get_account_by_username.return_value = None  # 用户名不存在
        
        # 测试生成唯一用户名
        username = await self.account_service._generate_unique_username(
            "user{数字1-3}", mock_repo, max_retries=3
        )
        assert username is not None
        assert username.startswith("user")
        
        # 测试用户名冲突重试
        mock_repo.get_account_by_username.side_effect = [
            MagicMock(),  # 第一次返回存在的账户
            None  # 第二次返回不存在
        ]
        
        username = await self.account_service._generate_unique_username(
            "test{数字1-2}", mock_repo, max_retries=3
        )
        assert username is not None
        assert username.startswith("test")
        
    def test_username_template_examples(self):
        """测试用户名模板示例"""
        templates = [
            "user{数字1-3}",
            "test{字母2-4}",
            "name{数字字母1-5}",
            "user{数字1-2}_{字母1-3}",
            "bot{数字1-1}_{数字字母2-3}",
        ]
        
        for template in templates:
            result = self.account_service._parse_username_tags(template)
            print(f"模板: {template} -> 结果: {result}")
            
            # 验证结果不包含标签
            assert "{" not in result
            assert "}" not in result
            
            # 验证结果格式有效
            valid, msg = self.account_service._validate_username(result)
            if not valid:
                print(f"无效用户名: {result}, 错误: {msg}")


if __name__ == "__main__":
    # 运行测试
    test = TestBatchProfileUpdate()
    test.setup_method()
    
    # 运行同步测试
    test.test_username_tag_detection()
    test.test_username_validation()
    test.test_username_template_examples()
    
    # 运行异步测试
    async def run_async_tests():
        await test.test_username_tag_parsing()
        await test.test_batch_update_data_validation()
        await test.test_generate_unique_username()
    
    asyncio.run(run_async_tests())
    print("所有测试完成！")
