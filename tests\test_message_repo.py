import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import asyncio
from data.database import get_session
from data.repositories.message_repo import MessageTaskRepository
from data.models.message import MessageTaskStatus

async def test_message_repo_all():
    async with get_session() as session:
        repo = MessageTaskRepository(session)
        # 1. 新增任务
        task_data = {
            "task_name": "测试任务",
            "target_type": "users",
            "target_ids": ["1001", "1002"],
            "send_strategy": "round_robin_account_per_target",
            "interval_seconds_min": 5,
            "interval_seconds_max": 10,
            "account_interval_min": 10,
            "account_interval_max": 20,
            "status": MessageTaskStatus.PENDING.value,
            "schedule_type": "immediate",
            "scheduled_at": None
        }
        task = await repo.add_task(task_data)
        print("添加任务成功:", task.id, task.task_name)
        assert task.id is not None

        # 2. 查询单个任务
        task2 = await repo.get_task_by_id(task.id)
        print("查询任务:", task2.id, task2.task_name)
        assert task2 is not None

        # 3. 查询所有任务
        all_tasks = await repo.get_all_tasks()
        print("所有任务数量:", len(all_tasks))
        assert isinstance(all_tasks, list)

        # 4. 按状态查询
        tasks_by_status = await repo.get_tasks_by_status(MessageTaskStatus.PENDING.value)
        print("按状态查询数量:", len(tasks_by_status))
        assert isinstance(tasks_by_status, list)

        # 5. 统计任务数量
        count = await repo.count_tasks()
        print("任务总数:", count)
        assert isinstance(count, int)

        # 6. 更新任务
        await repo.update_task(task.id, {"task_name": "已更新任务"})
        updated = await repo.get_task_by_id(task.id)
        print("更新后任务名:", updated.task_name)
        assert updated.task_name == "已更新任务"

        # 7. 任务目标相关
        targets = await repo.get_task_targets(task.id)
        print("任务目标数量:", len(targets))
        assert isinstance(targets, list)

        # 8. 任务账户相关
        accounts = await repo.get_task_accounts(task.id)
        print("任务账户数量:", len(accounts))
        assert isinstance(accounts, list)

        # 9. 日志相关
        logs = await repo.get_logs_for_task(task.id)
        print("任务日志数量:", len(logs))
        assert isinstance(logs, list)

        # 10. 删除任务
        result = await repo.delete_task(task.id)
        print("删除任务:", result)
        assert result is True

def main():
    asyncio.run(test_message_repo_all())

if __name__ == "__main__":
    main()