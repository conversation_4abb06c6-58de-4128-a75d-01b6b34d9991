import sys
import os
from PySide6.QtWidgets import QApplication, QMainWindow, QLabel, QVBoxLayout, QPushButton, QWidget
from PySide6.QtCore import Qt
from network_img import NetworkImage


class TestNetworkImageWindow(QMainWindow):
    """测试网络图片组件的窗口"""
    
    def __init__(self):
        super().__init__()
        
        self.setWindowTitle("网络图片显示测试")
        self.setGeometry(100, 100, 600, 500)
        
        # 创建网络图片组件
        self.network_image = NetworkImage(self)
        self.network_image.image_loaded.connect(self.on_image_loaded)
        self.network_image.image_error.connect(self.on_image_error)
        
        # 创建UI
        self.init_ui()
        
        # 示例URL - 可以替换为其他图片URL
        self.image_url = "https://www.tingtao.org/wp-content/uploads/2018/05/qrcode.png"
        
    def init_ui(self):
        # 创建中央窗口部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建布局
        layout = QVBoxLayout(central_widget)
        
        # 创建加载按钮
        self.load_btn = QPushButton("加载网络图片")
        self.load_btn.clicked.connect(self.load_image)
        layout.addWidget(self.load_btn)
        
        self.clear_cache_btn = QPushButton("清除缓存")
        self.clear_cache_btn.clicked.connect(self.clear_cache)
        layout.addWidget(self.clear_cache_btn)
        
        # 创建图片显示标签
        self.image_label = QLabel("点击按钮加载图片")
        self.image_label.setAlignment(Qt.AlignCenter)
        self.image_label.setMinimumSize(400, 300)
        self.image_label.setStyleSheet("border: 1px solid #cccccc;")
        layout.addWidget(self.image_label)
        
        # 创建状态标签
        self.status_label = QLabel("")
        layout.addWidget(self.status_label)
    
    def load_image(self):
        """加载图片"""
        self.status_label.setText("正在加载图片...")
        self.network_image.load_image(self.image_url)
    
    def on_image_loaded(self, pixmap):
        """图片加载成功回调"""
        self.image_label.setPixmap(pixmap.scaled(
            self.image_label.size(), 
            Qt.KeepAspectRatio, 
            Qt.SmoothTransformation
        ))
        self.status_label.setText("图片加载成功")
    
    def on_image_error(self, error_msg):
        """图片加载失败回调"""
        self.status_label.setText(f"错误: {error_msg}")
        self.image_label.setText("图片加载失败")
    
    def clear_cache(self):
        """清除图片缓存"""
        self.network_image.clear_cache()
        self.status_label.setText("已清除图片缓存")
        self.image_label.clear()
        self.image_label.setText("点击按钮加载图片")


if __name__ == "__main__":
    app = QApplication(sys.argv)
    window = TestNetworkImageWindow()
    window.show()
    sys.exit(app.exec()) 