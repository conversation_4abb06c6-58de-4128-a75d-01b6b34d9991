#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
加载指示器组件
提供全局加载状态显示
"""

from PySide6.QtWidgets import QDialog, QLabel, QVBoxLayout, QApplication
from PySide6.QtCore import Qt, QSize, QTimer
from PySide6.QtGui import QMovie

# 全局加载对话框实例
_loading_dialog = None


def show_loading(parent=None, message="正在加载..."):
    """显示加载对话框
    
    Args:
        parent: 父窗口
        message: 显示的消息文本
    """
    global _loading_dialog
    
    # 如果已经存在加载对话框，先关闭它
    if _loading_dialog is not None:
        hide_loading()
    
    # 创建新的加载对话框
    _loading_dialog = LoadingDialog(parent, message)
    _loading_dialog.show()
    
    # 确保对话框立即显示
    QApplication.processEvents()


def hide_loading():
    """隐藏加载对话框"""
    global _loading_dialog
    
    if _loading_dialog is not None:
        _loading_dialog.hide()
        _loading_dialog.deleteLater()
        _loading_dialog = None


class LoadingDialog(QDialog):
    """加载对话框"""
    
    def __init__(self, parent=None, message="正在加载..."):
        """初始化
        
        Args:
            parent: 父窗口
            message: 显示的消息文本
        """
        super().__init__(parent)
        
        # 设置对话框属性
        self.setWindowFlags(Qt.Dialog | Qt.FramelessWindowHint)
        self.setAttribute(Qt.WA_TranslucentBackground)
        self.setModal(True)
        
        # 创建布局
        layout = QVBoxLayout(self)
        
        # 创建标签显示加载动画
        self.loading_label = QLabel()
        self.loading_label.setAlignment(Qt.AlignCenter)
        
        # 尝试加载动画
        try:
            # 如果没有gif资源，就使用文本代替
            self.movie = QMovie(":/resources/loading.gif")
            self.movie.setScaledSize(QSize(48, 48))
            self.loading_label.setMovie(self.movie)
            self.movie.start()
        except Exception:
            self.loading_label.setText("⏳")
            self.loading_label.setStyleSheet("font-size: 32px;")
        
        layout.addWidget(self.loading_label)
        
        # 创建标签显示消息
        self.message_label = QLabel(message)
        self.message_label.setAlignment(Qt.AlignCenter)
        self.message_label.setStyleSheet("color: #333333; font-size: 14px;")
        layout.addWidget(self.message_label)
        
        # 设置对话框样式
        self.setStyleSheet("""
            QDialog {
                background-color: rgba(255, 255, 255, 0.9);
                border-radius: 10px;
                border: 1px solid #cccccc;
            }
        """)
        
        # 设置大小
        self.setMinimumSize(200, 120)
        
        # 添加超时保护，防止长时间阻塞
        self.timer = QTimer(self)
        self.timer.setSingleShot(True)
        self.timer.timeout.connect(self.timeout_handler)
        self.timer.start(30000)  # 30秒超时
    
    def timeout_handler(self):
        """超时处理，防止长时间阻塞"""
        hide_loading()
    
    def closeEvent(self, event):
        """关闭事件处理"""
        if hasattr(self, "movie") and self.movie:
            self.movie.stop()
        if hasattr(self, "timer") and self.timer:
            self.timer.stop()
        event.accept() 