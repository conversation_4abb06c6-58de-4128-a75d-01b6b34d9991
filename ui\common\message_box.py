from PySide6.QtCore import QFile, Qt, QObject
from PySide6.QtWidgets import Q<PERSON>abel, QHBoxLayout, QFrame
from PySide6.QtGui import QGuiApplication
from qfluentwidgets import Theme, Dialog, InfoBarPosition, InfoBar, StateToolTip, LineEdit, ImageLabel, HyperlinkLabel, \
    SubtitleLabel, InfoLevel, PushButton


def set_window_center(window):
    """ set window center """
    qr = window.frameGeometry()
    cp = window.screen().availableGeometry().center()
    qr.moveCenter(cp)
    window.move(qr.topLeft())


def show_dialog(parent, content, title = '提示', callback = None, ):
    w = Dialog(title, content, parent)
    # 获取当前屏幕高度
    screen = QGuiApplication.primaryScreen().geometry()
    height = screen.height()
    if parent:
        height = parent.screen().availableGeometry().height()
    w.contentLabel.setMaximumHeight(height * 0.5)
    w.windowTitleLabel.hide()
    if not callback:
        w.yesButton.setText("确定")
        w.cancelButton.setText('取消')
        w.buttonLayout.insertWidget(0, QLabel(''))
        w.buttonLayout.setStretch(0, 1)
        w.buttonLayout.setStretch(1, 1)
    
    result = w.exec()
    if result and callback:
        callback()
    
    return result  # 返回结果：1表示确定，0表示取消


def show_toast(parent, title, content, position =  InfoBarPosition.TOP_RIGHT, duration = 1500):
    InfoBar.info(
        title = title,
        content = content,
        orient = Qt.Horizontal,
        isClosable = True,
        position = position,
        duration = duration,
        parent = parent
    )
def show_center_loading(parent, content = '请稍后...', title = '加载中'):
    parent.stateTooltip = StateToolTip(title, content, parent)
    parent.stateTooltip.setTitle(title)
    parent.stateTooltip.setContent(content)
    parent.stateTooltip.show()

# 显示加载中（右上角）
def show_top_right_loading(parent, content = '请稍后...', title = '加载中'):
    parent.stateTooltip = StateToolTip(title, content, parent)
    parent.stateTooltip.setTitle(title)
    parent.stateTooltip.setContent(content)
    parent.stateTooltip.show()
    tl_x, tl_y, width, height = parent.window().frameGeometry().getRect()
    width2 = parent.stateTooltip.width()
    height2 = parent.stateTooltip.height()
    parent.stateTooltip.move(width - width2 - 40, height - height2 - 90)


def show_loading(parent, content = '请稍后...', title = '加载中'):
    parent.stateTooltip = StateToolTip(title, content, parent)
    parent.stateTooltip.setTitle(title)
    parent.stateTooltip.setContent(content)
    parent.stateTooltip.show()
    move_loading(parent, position="default")

# 隐藏加载中
def hide_loading(parent, content = '请查看结果框', title = '操作完成'):
    if  parent.stateTooltip:
        parent.stateTooltip.setTitle(title)
        parent.stateTooltip.setContent(content)
        parent.stateTooltip.setState(True)
        parent.stateTooltip = None
        
# 把加载中的窗口移动到窗口右下角或右上角
def move_loading(parent, position="bottom_right"):
    if parent.stateTooltip:
        tl_x, tl_y, width, height = parent.window().frameGeometry().getRect()
        width2 = parent.stateTooltip.width()
        height2 = parent.stateTooltip.height()
        
        if position == "top_right":
            # 移动到右上角，保持边距
            parent.stateTooltip.move(width - width2, height - height2)
        else:
            # 默认移动到右下角
            parent.stateTooltip.move(width - width2 - 70, height - height2 - 70)


def createSuccessInfoBar(parten,  content,title="提醒", position=InfoBarPosition.TOP, duration = 2000):
    # convenient class mothod
    InfoBar.success(
        title = title,
        content = content,
        orient = Qt.Horizontal,
        isClosable = True,
        position = position,
        # position='Custom',   # NOTE: use custom info bar manager
        duration = duration,
        parent = parten
    )


def createWarningInfoBar(parten, title, content, position=InfoBarPosition.TOP, duration = 2000):
    # convenient class mothod
    InfoBar.warning(
        title = title,
        content = content,
        orient = Qt.Horizontal,
        isClosable = True,
        position = position,
        # position='Custom',   # NOTE: use custom info bar manager
        duration = duration,
        parent = parten
    )
def createInfoInfoBar(parten, title, content, position=InfoBarPosition.TOP, duration = 2000):
    # convenient class mothod
    InfoBar.info(
        title = title,
        content = content,
        orient = Qt.Horizontal,
        isClosable = True,
        position = position,
        # position='Custom',   # NOTE: use custom info bar manager
        duration = duration,
        parent = parten
    )

def createErrorInfoBar(parten, content, title="错误提醒", position=InfoBarPosition.TOP, duration = 2000):
    # convenient class mothod
    InfoBar.error(
        
        title = title,
        content = content,
        orient = Qt.Horizontal,
        isClosable = True,
        position = position,
        # position='Custom',   # NOTE: use custom info bar manager
        duration = duration,
        parent = parten
    )


