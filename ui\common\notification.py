#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
通知组件
提供系统级通知功能
"""

from PySide6.QtWidgets import QSystemTrayIcon, QApplication, QMenu
from PySide6.QtGui import QIcon, QAction


def show_notification(title, message, icon=None, timeout=5000):
    """显示系统通知
    
    Args:
        title: 通知标题
        message: 通知内容
        icon: 通知图标，默认为None使用应用图标
        timeout: 通知显示时长(毫秒)，默认5000
    
    Returns:
        是否成功显示通知
    """
    # 检查系统是否支持通知
    if not QSystemTrayIcon.isSystemTrayAvailable():
        return False
    
    # 获取应用实例
    app = QApplication.instance()
    
    # 创建或获取系统托盘图标
    tray_icon = getattr(app, '_tray_icon', None)
    if tray_icon is None:
        tray_icon = QSystemTrayIcon(app)
        if icon:
            tray_icon.setIcon(icon)
        elif app.windowIcon():
            tray_icon.setIcon(app.windowIcon())
        else:
            # 使用默认图标
            tray_icon.setIcon(QIcon.fromTheme("dialog-information"))
        
        # 添加退出菜单
        menu = QMenu()
        exit_action = QAction("退出", app)
        exit_action.triggered.connect(app.quit)
        menu.addAction(exit_action)
        tray_icon.setContextMenu(menu)
        
        # 保存到应用实例
        app._tray_icon = tray_icon
    
    # 确保托盘图标可见
    if not tray_icon.isVisible():
        tray_icon.show()
    
    # 显示通知
    tray_icon.showMessage(title, message, QSystemTrayIcon.MessageIcon.Information, timeout)
    return True 