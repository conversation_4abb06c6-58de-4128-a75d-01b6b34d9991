from PySide6.QtCore import QObject, Slot
from utils.signal_bus import signal_bus
from ui.common.message_box import show_top_right_loading, hide_loading

class ProgressMonitor(QObject):
    """
    进度监控器
    用于监听信号总线上的进度信息，并显示到UI界面
    """
    
    def __init__(self, parent_widget=None):
        """
        初始化进度监控器
        
        Args:
            parent_widget: 父窗口部件，用于显示消息
        """
        super().__init__()
        self.parent_widget = parent_widget
        self.current_operation = None
        
        # 连接信号
        self._connect_signals()
        
    def set_parent(self, parent_widget):
        """
        设置父窗口部件
        
        Args:
            parent_widget: 父窗口部件
        """
        self.parent_widget = parent_widget
        
    def _connect_signals(self):
        """连接信号"""
        # 进度信号
        signal_bus.progress_updated.connect(self.on_progress_updated)
        # 结果信号
        signal_bus.operation_result.connect(self.on_operation_result)
        
    @Slot(str, str, str)
    def on_progress_updated(self, operation: str, content: str, title: str):
        """
        处理进度更新信号
        
        Args:
            operation: 操作类型
            content: 进度内容
            title: 标题
        """
        if self.parent_widget:
            self.current_operation = operation
            show_top_right_loading(self.parent_widget, content, title)
    
    @Slot(str, bool, object)
    def on_operation_result(self, operation: str, success: bool, data=None):
        """
        处理操作结果信号
        
        Args:
            operation: 操作类型
            success: 是否成功
            data: 结果数据
        """
        if self.parent_widget and self.current_operation == operation:
            message = ""
            title = "操作完成" if success else "操作失败"
            
            if isinstance(data, dict) and "message" in data:
                message = data["message"]
            elif isinstance(data, str):
                message = data
            else:
                message = "操作已完成" if success else "操作失败，请查看日志"
                
            hide_loading(self.parent_widget, message, title)
            self.current_operation = None
            
# 全局单例
progress_monitor = ProgressMonitor() 