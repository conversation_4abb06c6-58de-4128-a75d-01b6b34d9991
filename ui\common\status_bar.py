#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
状态栏管理器组件
管理主窗口状态栏的状态显示
"""

from typing import Optional

from PySide6.QtCore import QObject, QTimer
from PySide6.QtWidgets import QLabel, QProgressBar

from utils.logger import get_logger

# 获取模块日志记录器
logger = get_logger(__name__)


class StatusBarManager(QObject):
    """状态栏管理器
    
    用于管理主窗口状态栏的状态显示
    """
    
    def __init__(self, status_label: QLabel, progress_bar: Optional[QProgressBar] = None):
        """初始化
        
        Args:
            status_label: 状态标签
            progress_bar: 进度条
        """
        super().__init__()
        self.status_label = status_label
        self.progress_bar = progress_bar
        self.timer = QTimer(self)
        self.timer.timeout.connect(self._clear_status)
        logger.debug("状态栏管理器初始化完成")
        
    def set_status(self, message: str, timeout: int = 0, busy: bool = False):
        """设置状态信息
        
        Args:
            message: 状态信息
            timeout: 自动清除超时(毫秒)，为0则不自动清除
            busy: 是否显示忙碌状态
        """
        logger.debug(f"设置状态栏信息: {message}, 超时: {timeout}ms, 忙碌: {busy}")
        
        # 设置状态文本
        self.status_label.setText(message)
        
        # 设置进度条状态
        if self.progress_bar:
            self.progress_bar.setVisible(busy)
            if busy:
                self.progress_bar.setRange(0, 0)  # 显示忙碌状态
            else:
                self.progress_bar.setRange(0, 100)
                self.progress_bar.setValue(100)
        
        # 设置超时自动清除
        if timeout > 0:
            self.timer.start(timeout)
        else:
            self.timer.stop()
    
    def clear_status(self):
        """清除状态信息"""
        self._clear_status()
    
    def _clear_status(self):
        """内部清除状态方法"""
        self.timer.stop()
        self.status_label.setText("就绪")
        logger.debug("状态栏信息已清除")
        if self.progress_bar:
            self.progress_bar.setVisible(False) 