#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Toast消息组件
提供短暂显示的提示信息，自动消失
"""

from PySide6.QtCore import Qt, QTimer
from PySide6.QtWidgets import QFrame, QVBoxLayout, QLabel

from utils.logger import get_logger

# 获取模块日志记录器
logger = get_logger(__name__)


class Toast(QFrame):
    """Toast消息组件
    
    用于显示短暂的提示信息，自动消失
    """
    
    def __init__(self, parent=None, duration: int = 2000):
        """初始化
        
        Args:
            parent: 父窗口
            duration: 显示时长(毫秒)
        """
        super().__init__(parent)
        self.duration = duration
        self.timer = QTimer(self)
        self.timer.timeout.connect(self.hide)
        
        self._init_ui()
        logger.debug("Toast组件初始化完成")
    
    def _init_ui(self):
        """初始化UI"""
        # 设置样式
        self.setStyleSheet("""
            QFrame {
                background-color: rgba(50, 50, 50, 200);
                color: white;
                border-radius: 10px;
                padding: 10px;
            }
        """)
        
        # 设置布局
        layout = QVBoxLayout(self)
        
        # 消息标签
        self.label = QLabel()
        self.label.setStyleSheet("color: white;")
        self.label.setAlignment(Qt.AlignCenter)
        layout.addWidget(self.label)
        
        # 调整大小
        self.setMinimumWidth(200)
        self.adjustSize()
        
        # 初始隐藏
        self.hide()
    
    def show_message(self, message: str):
        """显示消息
        
        Args:
            message: 消息内容
        """
        logger.debug(f"显示Toast消息: {message}")
        
        # 设置消息
        self.label.setText(message)
        
        # 调整大小并居中
        self.adjustSize()
        parent = self.parent()
        if parent:
            x = (parent.width() - self.width()) // 2
            y = parent.height() - self.height() - 50
            self.move(x, y)
        
        # 显示并启动定时器
        self.show()
        self.timer.start(self.duration)
    
    @staticmethod
    def success(parent, message: str, duration: int = 2000):
        """显示成功消息
        
        Args:
            parent: 父窗口
            message: 消息内容
            duration: 显示时长(毫秒)
        """
        toast = Toast(parent, duration)
        toast.setStyleSheet("""
            QFrame {
                background-color: rgba(46, 125, 50, 200);
                color: white;
                border-radius: 10px;
                padding: 10px;
            }
            QLabel {
                color: white;
            }
        """)
        toast.show_message(message)
        return toast
    
    @staticmethod
    def error(parent, message: str, duration: int = 3000):
        """显示错误消息
        
        Args:
            parent: 父窗口
            message: 消息内容
            duration: 显示时长(毫秒)
        """
        toast = Toast(parent, duration)
        toast.setStyleSheet("""
            QFrame {
                background-color: rgba(198, 40, 40, 200);
                color: white;
                border-radius: 10px;
                padding: 10px;
            }
            QLabel {
                color: white;
            }
        """)
        toast.show_message(message)
        return toast
    
    @staticmethod
    def info(parent, message: str, duration: int = 2000):
        """显示提示消息
        
        Args:
            parent: 父窗口
            message: 消息内容
            duration: 显示时长(毫秒)
        """
        toast = Toast(parent, duration)
        toast.setStyleSheet("""
            QFrame {
                background-color: rgba(30, 136, 229, 200);
                color: white;
                border-radius: 10px;
                padding: 10px;
            }
            QLabel {
                color: white;
            }
        """)
        toast.show_message(message)
        return toast
    
    @staticmethod
    def warning(parent, message: str, duration: int = 2500):
        """显示警告消息
        
        Args:
            parent: 父窗口
            message: 消息内容
            duration: 显示时长(毫秒)
        """
        toast = Toast(parent, duration)
        toast.setStyleSheet("""
            QFrame {
                background-color: rgba(251, 140, 0, 200);
                color: white;
                border-radius: 10px;
                padding: 10px;
            }
            QLabel {
                color: white;
            }
        """)
        toast.show_message(message)
        return toast 