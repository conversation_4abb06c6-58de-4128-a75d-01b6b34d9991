# coding: utf-8
"""
浮动日志按钮组件
提供可拖动的浮动按钮，用于打开/关闭日志窗口
"""

from PySide6.QtCore import Qt, QPoint, Signal, QPropertyAnimation, QEasingCurve, QRect, QSettings
from PySide6.QtGui import QIcon, QPainter, QColor, QFont, QPen
from PySide6.QtWidgets import QPushButton, QWidget, QGraphicsDropShadowEffect

from qfluentwidgets import FluentIcon, isDarkTheme


class FloatingButtonPositionManager:
    """浮动按钮位置管理器 - 单例模式"""
    _instance = None
    _position = None
    _settings = None
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
            cls._instance._initialize()
        return cls._instance
    
    def _initialize(self):
        """初始化位置管理器"""
        self._settings = QSettings("TeleTest", "FloatingButton")
        # 从设置中加载上次保存的位置
        saved_pos = self._settings.value("position", None)
        if saved_pos:
            self._position = QPoint(saved_pos)
        else:
            self._position = None
    
    def get_position(self) -> QPoint:
        """获取当前位置"""
        return self._position
    
    def set_position(self, position: QPoint):
        """设置位置并保存"""
        self._position = position
        self._settings.setValue("position", position)
        self._settings.sync()
    
    def has_saved_position(self) -> bool:
        """是否有保存的位置"""
        return self._position is not None


# 全局位置管理器实例
position_manager = FloatingButtonPositionManager()


class FloatingLogButton(QPushButton):
    """浮动日志按钮"""
    
    # 信号
    log_window_toggle_requested = Signal(bool)  # 请求切换日志窗口状态
    
    def __init__(self, parent: QWidget = None):
        super().__init__(parent)
        self.parent_widget = parent
        self.is_dragging = False
        self.drag_start_position = QPoint()
        self.log_window_visible = False
        
        self._setup_ui()
        self._setup_animation()
        
        # 不在构造函数中设置位置，而是在显示时设置
    
    def _setup_ui(self):
        """设置UI"""
        # 设置按钮样式
        self.setFixedSize(50, 50)
        self.setIcon(FluentIcon.DOCUMENT.icon())
        self.setIconSize(self.size() * 0.6)
        
        # 设置样式表
        self._update_style()
        
        # 添加阴影效果
        shadow = QGraphicsDropShadowEffect()
        shadow.setBlurRadius(10)
        shadow.setColor(QColor(0, 0, 0, 80))
        shadow.setOffset(2, 2)
        self.setGraphicsEffect(shadow)
        
        # 设置工具提示
        self.setToolTip("点击查看运行日志")
        
        # 连接点击事件
        self.clicked.connect(self._on_clicked)
    
    def _set_initial_position(self):
        """设置初始位置"""
        if not self.parent_widget:
            default_pos = QPoint(700, 500)
            self.move(default_pos)
            position_manager.set_position(default_pos)
            return
        
        parent_rect = self.parent_widget.rect()
        
        # 如果父窗口大小无效，延迟执行
        if parent_rect.width() <= 0 or parent_rect.height() <= 0:
            from PySide6.QtCore import QTimer
            QTimer.singleShot(300, self._set_initial_position)
            return
        
        if position_manager.has_saved_position():
            # 使用保存的位置
            saved_pos = position_manager.get_position()
            
            if (saved_pos.x() >= 0 and saved_pos.y() >= 0 and
                    saved_pos.x() + self.width() <= parent_rect.width() and
                    saved_pos.y() + self.height() <= parent_rect.height()):
                self.move(saved_pos)
                return
            else:
                # 如果保存的位置超出范围，调整到合适的位置
                self._adjust_position_to_bounds(saved_pos, parent_rect)
                return
        
        # 没有保存的位置，使用默认的右下角位置
        self.position_at_bottom_right()
    
    def _adjust_position_to_bounds(self, saved_pos: QPoint, parent_rect):
        """调整位置到父窗口边界内"""
        # 调整X坐标
        x = saved_pos.x()
        if x < 0:
            x = 0
        elif x + self.width() > parent_rect.width():
            x = parent_rect.width() - self.width()
        
        # 调整Y坐标
        y = saved_pos.y()
        if y < 0:
            y = 0
        elif y + self.height() > parent_rect.height():
            y = parent_rect.height() - self.height()
        
        # 移动到调整后的位置
        adjusted_pos = QPoint(x, y)
        self.move(adjusted_pos)
        # 保存调整后的位置
        position_manager.set_position(adjusted_pos)
    
    def _setup_animation(self):
        """设置动画"""
        self.hover_animation = QPropertyAnimation(self, b"geometry")
        self.hover_animation.setDuration(200)
        self.hover_animation.setEasingCurve(QEasingCurve.Type.OutCubic)
    
    def _update_style(self):
        """更新样式"""
        if isDarkTheme():
            bg_color = "rgba(45, 45, 45, 200)"
            hover_color = "rgba(60, 60, 60, 220)"
            border_color = "rgba(255, 255, 255, 50)"
        else:
            bg_color = "rgba(255, 255, 255, 200)"
            hover_color = "rgba(240, 240, 240, 220)"
            border_color = "rgba(0, 0, 0, 50)"
        
        style = f"""
        QPushButton {{
            background-color: {bg_color};
            border: 1px solid {border_color};
            border-radius: 25px;
            color: {'white' if isDarkTheme() else 'black'};
        }}
        QPushButton:hover {{
            background-color: {hover_color};
        }}
        QPushButton:pressed {{
            background-color: {'rgba(30, 30, 30, 240)' if isDarkTheme() else 'rgba(220, 220, 220, 240)'};
        }}
        """
        self.setStyleSheet(style)
    
    def _on_clicked(self):
        """处理点击事件"""
        self.log_window_visible = not self.log_window_visible
        self.log_window_toggle_requested.emit(self.log_window_visible)
        
        # 更新图标
        if self.log_window_visible:
            self.setIcon(FluentIcon.CLOSE.icon())
            self.setToolTip("关闭运行日志")
        else:
            self.setIcon(FluentIcon.DOCUMENT.icon())
            self.setToolTip("点击查看运行日志")
    
    def set_log_window_visible(self, visible: bool):
        """设置日志窗口可见状态（外部调用）"""
        if self.log_window_visible != visible:
            self.log_window_visible = visible
            if visible:
                self.setIcon(FluentIcon.CLOSE.icon())
                self.setToolTip("关闭运行日志")
            else:
                self.setIcon(FluentIcon.DOCUMENT.icon())
                self.setToolTip("点击查看运行日志")
    
    def mousePressEvent(self, event):
        """鼠标按下事件"""
        if event.button() == Qt.MouseButton.LeftButton:
            self.is_dragging = True
            self.drag_start_position = event.globalPosition().toPoint() - self.pos()
        super().mousePressEvent(event)
    
    def mouseMoveEvent(self, event):
        """鼠标移动事件"""
        if self.is_dragging and event.buttons() == Qt.MouseButton.LeftButton:
            # 计算新位置
            new_pos = event.globalPosition().toPoint() - self.drag_start_position
            
            # 限制在父窗口范围内
            if self.parent_widget:
                parent_rect = self.parent_widget.rect()
                button_rect = QRect(new_pos, self.size())
                
                # 确保按钮不会超出父窗口边界
                if button_rect.left() < 0:
                    new_pos.setX(0)
                elif button_rect.right() > parent_rect.right():
                    new_pos.setX(parent_rect.right() - self.width())
                
                if button_rect.top() < 0:
                    new_pos.setY(0)
                elif button_rect.bottom() > parent_rect.bottom():
                    new_pos.setY(parent_rect.bottom() - self.height())
            
            self.move(new_pos)
        super().mouseMoveEvent(event)
    
    def mouseReleaseEvent(self, event):
        """鼠标释放事件"""
        if event.button() == Qt.MouseButton.LeftButton:
            if self.is_dragging:
                # 保存当前位置
                position_manager.set_position(self.pos())
            self.is_dragging = False
        super().mouseReleaseEvent(event)
    
    def enterEvent(self, event):
        """鼠标进入事件"""
        # 添加悬停动画效果
        current_rect = self.geometry()
        hover_rect = current_rect.adjusted(-2, -2, 2, 2)
        
        self.hover_animation.setStartValue(current_rect)
        self.hover_animation.setEndValue(hover_rect)
        self.hover_animation.start()
        
        super().enterEvent(event)
    
    def leaveEvent(self, event):
        """鼠标离开事件"""
        # 恢复原始大小
        current_rect = self.geometry()
        normal_rect = current_rect.adjusted(2, 2, -2, -2)
        
        self.hover_animation.setStartValue(current_rect)
        self.hover_animation.setEndValue(normal_rect)
        self.hover_animation.start()
        
        super().leaveEvent(event)
    
    def showEvent(self, event):
        """显示事件 - 在按钮显示时设置初始位置"""
        super().showEvent(event)
        # 延迟设置位置，确保父窗口已经完全显示
        from PySide6.QtCore import QTimer
        QTimer.singleShot(100, self._set_initial_position)
    
    def show(self):
        """重写show方法，确保位置正确设置"""
        super().show()
        # 额外延迟确保位置设置
        from PySide6.QtCore import QTimer
        QTimer.singleShot(200, self._ensure_position)
    
    def _ensure_position(self):
        """确保位置正确设置"""
        current_pos = self.pos()
        # 如果位置仍然是(0,0)，强制设置到正确位置
        if current_pos.x() == 0 and current_pos.y() == 0:
            self._set_initial_position()
    
    def position_at_bottom_right(self):
        """将按钮定位到父窗口右下角"""
        if self.parent_widget:
            parent_rect = self.parent_widget.rect()
            
            # 如果父窗口大小为0，延迟执行
            if parent_rect.width() <= 0 or parent_rect.height() <= 0:
                from PySide6.QtCore import QTimer
                QTimer.singleShot(200, self.position_at_bottom_right)
                return
            
            margin = 20  # 距离边缘的边距
            x = parent_rect.right() - self.width() - margin
            y = parent_rect.bottom() - self.height() - margin
            new_pos = QPoint(x, y)
            
            self.move(new_pos)
            # 保存位置
            position_manager.set_position(new_pos)
        else:
            # 如果没有父窗口，设置一个默认位置
            default_pos = QPoint(700, 500)  # 默认位置
            self.move(default_pos)
            position_manager.set_position(default_pos)
    
    def resizeEvent(self, event):
        """窗口大小改变事件"""
        super().resizeEvent(event)
        # 当父窗口大小改变时，检查按钮位置是否仍在有效范围内
        if hasattr(self, 'parent_widget') and self.parent_widget:
            current_pos = self.pos()
            parent_rect = self.parent_widget.rect()
            
            # 如果当前位置超出父窗口范围，调整位置到边界内
            if (current_pos.x() + self.width() > parent_rect.width() or
                    current_pos.y() + self.height() > parent_rect.height() or
                    current_pos.x() < 0 or current_pos.y() < 0):
                # 调整位置到边界内，而不是重新定位到右下角
                self._adjust_position_to_bounds(current_pos, parent_rect)
            else:
                # 位置仍然有效，保持当前位置
                pass
