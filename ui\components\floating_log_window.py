# coding: utf-8
"""
浮动日志窗口组件
提供可拖动的日志显示窗口，实时显示应用运行日志
"""

import os
import re
from datetime import datetime
from pathlib import Path
from typing import Optional

from PySide6.QtCore import Qt, QPoint, Signal, QTimer, QThread, QSettings
from PySide6.QtGui import QFont, QTextCursor, QColor, QPainter, QPen
from PySide6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QPushButton,
                               QTextEdit, QLabel, QFrame, QGraphicsDropShadowEffect,
                               QComboBox, QCheckBox)

from qfluentwidgets import FluentIcon, isDarkTheme, PushButton, ComboBox, CheckBox
from config import config
from utils.logger import get_logger


class LogWindowPositionManager:
    """日志窗口位置管理器 - 单例模式"""
    _instance = None
    _settings = None
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
            cls._instance._initialize()
        return cls._instance
    
    def _initialize(self):
        """初始化位置管理器"""
        self._settings = QSettings("TeleTest", "LogWindow")
    
    def get_position(self, view_name: str) -> QPoint:
        """获取指定视图的日志窗口位置"""
        saved_pos = self._settings.value(f"position_{view_name}", None)
        if saved_pos:
            return QPoint(saved_pos)
        return None
    
    def set_position(self, view_name: str, position: QPoint):
        """设置指定视图的日志窗口位置"""
        self._settings.setValue(f"position_{view_name}", position)
        self._settings.sync()
    
    def get_size(self, view_name: str) -> tuple:
        """获取指定视图的日志窗口大小"""
        saved_size = self._settings.value(f"size_{view_name}", None)
        if saved_size:
            return (saved_size.width(), saved_size.height())
        return (600, 400)  # 默认大小
    
    def set_size(self, view_name: str, width: int, height: int):
        """设置指定视图的日志窗口大小"""
        from PySide6.QtCore import QSize
        self._settings.setValue(f"size_{view_name}", QSize(width, height))
        self._settings.sync()


# 全局日志窗口位置管理器实例
log_window_position_manager = LogWindowPositionManager()


class LogFileWatcher(QThread):
    """日志文件监控线程"""
    
    new_log_line = Signal(str)  # 新日志行信号
    
    def __init__(self, log_file_path: str):
        super().__init__()
        self.log_file_path = log_file_path
        self.is_running = False
        self.last_position = 0
    
    def run(self):
        """运行监控"""
        self.is_running = True
        
        # 如果文件存在，获取当前文件大小作为起始位置
        if os.path.exists(self.log_file_path):
            self.last_position = os.path.getsize(self.log_file_path)
        
        while self.is_running:
            try:
                if os.path.exists(self.log_file_path):
                    current_size = os.path.getsize(self.log_file_path)
                    
                    if current_size > self.last_position:
                        # 读取新增内容
                        with open(self.log_file_path, 'r', encoding = 'utf-8') as f:
                            f.seek(self.last_position)
                            new_content = f.read()
                            
                            # 按行分割并发送
                            for line in new_content.splitlines():
                                if line.strip():
                                    self.new_log_line.emit(line)
                        
                        self.last_position = current_size
                
                self.msleep(500)  # 每500ms检查一次
            
            except Exception as e:
                print(f"日志监控错误: {e}")
                self.msleep(1000)
    
    def stop(self):
        """停止监控"""
        self.is_running = False
        self.wait()


class FloatingLogWindow(QWidget):
    """浮动日志窗口"""
    
    # 信号
    window_closed = Signal()
    
    def __init__(self, parent: QWidget = None, view_name: str = "", module_filters: list = None):
        super().__init__(parent)
        self.parent_widget = parent
        self.view_name = view_name
        self.module_filters = module_filters or []  # 模块过滤器列表
        self.is_dragging = False
        self.is_resizing = False
        self.drag_start_position = QPoint()
        self.resize_start_position = QPoint()
        self.resize_start_size = None
        self.log_watcher = None
        self.logger = get_logger(f"ui.components.floating_log_window.{view_name}")
        
        self._setup_ui()
        self._setup_log_monitoring()
        self._restore_window_state()
    
    def _setup_ui(self):
        """设置UI"""
        # 设置窗口标志：无边框、置顶、独立窗口
        self.setWindowFlags(
            Qt.WindowType.FramelessWindowHint |
            Qt.WindowType.WindowStaysOnTopHint |
            Qt.WindowType.Tool  # 独立工具窗口，不会在任务栏显示
        )
        self.setAttribute(Qt.WidgetAttribute.WA_TranslucentBackground)
        
        # 设置最小和默认大小
        self.setMinimumSize(400, 300)
        default_width, default_height = log_window_position_manager.get_size(self.view_name)
        self.resize(default_width, default_height)
        
        # 主布局
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(10, 10, 10, 10)
        
        # 创建主框架
        self.main_frame = QFrame()
        self.main_frame.setObjectName("logWindow")
        main_layout.addWidget(self.main_frame)
        
        # 框架布局
        frame_layout = QVBoxLayout(self.main_frame)
        frame_layout.setContentsMargins(15, 15, 15, 15)
        frame_layout.setSpacing(10)
        
        # 标题栏
        title_layout = QHBoxLayout()
        
        # 标题
        title_text = f"{self.view_name} - 运行日志" if self.view_name else "运行日志"
        self.title_label = QLabel(title_text)
        self.title_label.setFont(QFont("Microsoft YaHei", 12, QFont.Weight.Bold))
        self.title_label.setToolTip("拖动此区域可移动窗口")
        self.title_label.setCursor(Qt.CursorShape.SizeAllCursor)  # 设置拖动光标
        title_layout.addWidget(self.title_label)
        
        title_layout.addStretch()
        
        # 控制按钮
        self.clear_btn = PushButton("清空")
        self.clear_btn.setIcon(FluentIcon.DELETE)
        self.clear_btn.clicked.connect(self._clear_logs)
        title_layout.addWidget(self.clear_btn)
        
        self.close_btn = PushButton("关闭")
        self.close_btn.setIcon(FluentIcon.CLOSE)
        self.close_btn.clicked.connect(self.close)
        title_layout.addWidget(self.close_btn)
        
        frame_layout.addLayout(title_layout)
        
        # 过滤控制栏
        filter_layout = QHBoxLayout()
        
        # 日志级别过滤
        filter_layout.addWidget(QLabel("级别:"))
        self.level_combo = ComboBox()
        self.level_combo.addItems(["全部", "DEBUG", "INFO", "WARNING", "ERROR"])
        self.level_combo.setCurrentText("全部")
        self.level_combo.currentTextChanged.connect(self._filter_logs)
        filter_layout.addWidget(self.level_combo)
        
        # 模块过滤器显示
        if self.module_filters:
            filter_layout.addWidget(QLabel("模块:"))
            modules_text = ", ".join(self.module_filters[:2])  # 只显示前两个
            if len(self.module_filters) > 2:
                modules_text += f" 等{len(self.module_filters)}个"
            module_label = QLabel(modules_text)
            module_label.setStyleSheet("color: #666; font-size: 10px;")
            filter_layout.addWidget(module_label)
        
        # 自动滚动
        self.auto_scroll_cb = CheckBox("自动滚动")
        self.auto_scroll_cb.setChecked(True)
        filter_layout.addWidget(self.auto_scroll_cb)
        
        filter_layout.addStretch()
        frame_layout.addLayout(filter_layout)
        
        # 日志显示区域
        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)
        self.log_text.setFont(QFont("Consolas", 9))
        frame_layout.addWidget(self.log_text)
        
        # 设置样式
        self._update_style()
        
        # 添加阴影效果
        shadow = QGraphicsDropShadowEffect()
        shadow.setBlurRadius(20)
        shadow.setColor(QColor(0, 0, 0, 100))
        shadow.setOffset(0, 5)
        self.main_frame.setGraphicsEffect(shadow)
    
    def _setup_log_monitoring(self):
        """设置日志监控"""
        # 获取今天的日志文件路径
        log_dir = Path(config.get("logs_path"))
        today = datetime.now().strftime('%Y%m%d')
        log_file = log_dir / f"teletest_{today}.log"
        
        if log_file.exists():
            self.log_watcher = LogFileWatcher(str(log_file))
            self.log_watcher.new_log_line.connect(self._add_log_line)
            self.log_watcher.start()
            
            # 加载现有日志内容
            self._load_existing_logs(str(log_file))
        else:
            self.log_text.append("日志文件不存在，等待日志生成...")
    
    def _load_existing_logs(self, log_file_path: str):
        """加载现有日志内容"""
        try:
            with open(log_file_path, 'r', encoding = 'utf-8') as f:
                lines = f.readlines()
                # 只显示最后100行
                recent_lines = lines[-100:] if len(lines) > 100 else lines
                
                for line in recent_lines:
                    line = line.strip()
                    if line:
                        self._add_log_line(line)
        
        except Exception as e:
            self.logger.error(f"加载日志文件失败: {e}")
            self.log_text.append(f"加载日志文件失败: {e}")
    
    def _add_log_line(self, line: str):
        """添加日志行"""
        # 检查模块过滤
        if not self._should_show_log_line(line):
            return
        
        # 解析日志级别
        level = self._extract_log_level(line)
        
        # 检查级别过滤条件
        current_filter = self.level_combo.currentText()
        if current_filter != "全部" and level != current_filter:
            return
        
        # 添加颜色格式
        formatted_line = self._format_log_line(line, level)
        
        # 添加到文本框
        self.log_text.append(formatted_line)
        
        # 自动滚动到底部
        if self.auto_scroll_cb.isChecked():
            cursor = self.log_text.textCursor()
            cursor.movePosition(QTextCursor.MoveOperation.End)
            self.log_text.setTextCursor(cursor)
    
    def _should_show_log_line(self, line: str) -> bool:
        """判断是否应该显示这行日志"""
        # 如果没有设置模块过滤器，显示所有日志
        if not self.module_filters:
            return True
        
        # 提取日志行中的模块名称
        module_name = self._extract_module_name(line)
        if not module_name:
            return False
        
        # 检查是否匹配任何一个过滤器
        for filter_pattern in self.module_filters:
            if filter_pattern in module_name:
                return True
        
        return False
    
    def _extract_module_name(self, line: str) -> str:
        """提取日志行中的模块名称"""
        # 日志格式: timestamp | level | module_name:function:line - message
        # 匹配模块名称部分
        match = re.search(r'\|\s*\w+\s*\|\s*([^:]+):', line)
        if match:
            return match.group(1).strip()
        return ""
    
    def _extract_log_level(self, line: str) -> str:
        """提取日志级别"""
        # 匹配日志格式中的级别
        match = re.search(r'\|\s*(\w+)\s*\|', line)
        if match:
            return match.group(1).strip()
        return "INFO"
    
    def _format_log_line(self, line: str, level: str) -> str:
        """格式化日志行"""
        # 根据级别添加颜色
        if level == "ERROR":
            return f'<span style="color: #ff4757;">{line}</span>'
        elif level == "WARNING":
            return f'<span style="color: #ffa502;">{line}</span>'
        elif level == "DEBUG":
            return f'<span style="color: #747d8c;">{line}</span>'
        else:
            return line
    
    def _filter_logs(self):
        """过滤日志"""
        # 重新加载日志文件并应用过滤
        self.log_text.clear()
        if self.log_watcher and os.path.exists(self.log_watcher.log_file_path):
            self._load_existing_logs(self.log_watcher.log_file_path)
    
    def _clear_logs(self):
        """清空日志显示"""
        self.log_text.clear()
    
    def _update_style(self):
        """更新样式"""
        if isDarkTheme():
            bg_color = "rgba(32, 32, 32, 240)"
            text_color = "white"
            border_color = "rgba(255, 255, 255, 50)"
        else:
            bg_color = "rgba(255, 255, 255, 240)"
            text_color = "black"
            border_color = "rgba(0, 0, 0, 50)"
        
        style = f"""
        QFrame#logWindow {{
            background-color: {bg_color};
            border: 1px solid {border_color};
            border-radius: 10px;
        }}
        QLabel {{
            color: {text_color};
        }}
        QTextEdit {{
            background-color: {'rgba(45, 45, 45, 200)' if isDarkTheme() else 'rgba(250, 250, 250, 200)'};
            border: 1px solid {border_color};
            border-radius: 5px;
            color: {text_color};
            padding: 5px;
        }}
        """
        self.setStyleSheet(style)
    
    def mousePressEvent(self, event):
        """鼠标按下事件"""
        if event.button() == Qt.MouseButton.LeftButton:
            pos = event.position()
            # 检查是否在右下角调整大小区域（15x15像素）
            if (pos.x() >= self.width() - 15 and pos.y() >= self.height() - 15):
                self.is_resizing = True
                self.resize_start_position = event.globalPosition().toPoint()
                self.resize_start_size = self.size()
            # 检查是否在标题区域内（前60像素高度为标题区域）
            elif pos.y() <= 60:
                self.is_dragging = True
                self.drag_start_position = event.globalPosition().toPoint() - self.pos()
        super().mousePressEvent(event)
    
    def mouseMoveEvent(self, event):
        """鼠标移动事件"""
        if event.buttons() == Qt.MouseButton.LeftButton:
            if self.is_resizing:
                # 调整窗口大小
                current_pos = event.globalPosition().toPoint()
                delta = current_pos - self.resize_start_position
                
                new_width = max(self.minimumWidth(), self.resize_start_size.width() + delta.x())
                new_height = max(self.minimumHeight(), self.resize_start_size.height() + delta.y())
                
                self.resize(new_width, new_height)
            
            elif self.is_dragging:
                # 移动窗口
                new_pos = event.globalPosition().toPoint() - self.drag_start_position
                
                # 获取屏幕边界，确保窗口不会完全移出屏幕
                from PySide6.QtWidgets import QApplication
                screen = QApplication.primaryScreen()
                screen_rect = screen.availableGeometry()
                
                # 限制窗口至少有一部分在屏幕内（标题栏区域）
                min_visible = 50  # 至少50像素可见
                
                # 调整X坐标
                if new_pos.x() + self.width() < min_visible:
                    new_pos.setX(min_visible - self.width())
                elif new_pos.x() > screen_rect.width() - min_visible:
                    new_pos.setX(screen_rect.width() - min_visible)
                
                # 调整Y坐标
                if new_pos.y() + min_visible < 0:
                    new_pos.setY(-min_visible)
                elif new_pos.y() > screen_rect.height() - min_visible:
                    new_pos.setY(screen_rect.height() - min_visible)
                
                self.move(new_pos)
        else:
            # 更新鼠标光标
            pos = event.position()
            if (pos.x() >= self.width() - 15 and pos.y() >= self.height() - 15):
                self.setCursor(Qt.CursorShape.SizeFDiagCursor)  # 对角线调整大小光标
            elif pos.y() <= 60:
                self.setCursor(Qt.CursorShape.SizeAllCursor)  # 移动光标
            else:
                self.setCursor(Qt.CursorShape.ArrowCursor)  # 默认光标
        
        super().mouseMoveEvent(event)
    
    def mouseReleaseEvent(self, event):
        """鼠标释放事件"""
        if event.button() == Qt.MouseButton.LeftButton:
            if self.is_dragging or self.is_resizing:
                # 保存窗口状态
                self._save_window_state()
            self.is_dragging = False
            self.is_resizing = False
        super().mouseReleaseEvent(event)
    
    def paintEvent(self, event):
        """绘制事件 - 在右下角绘制调整大小指示器"""
        super().paintEvent(event)
        
        painter = QPainter(self)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)
        
        # 绘制右下角的调整大小指示器
        resize_area_size = 15
        x = self.width() - resize_area_size
        y = self.height() - resize_area_size
        
        # 设置画笔颜色
        if isDarkTheme():
            color = QColor(200, 200, 200, 100)
        else:
            color = QColor(100, 100, 100, 100)
        
        painter.setPen(QPen(color, 1))
        
        # 绘制三条对角线
        for i in range(3):
            offset = i * 4 + 3
            painter.drawLine(
                x + offset, y + resize_area_size - 1,
                x + resize_area_size - 1, y + offset
            )
        
        painter.end()
    
    def _restore_window_state(self):
        """恢复窗口状态（位置和大小）"""
        # 恢复位置
        saved_pos = log_window_position_manager.get_position(self.view_name)
        if saved_pos:
            self.move(saved_pos)
        else:
            # 默认居中显示
            self.center_on_parent()
    
    def _save_window_state(self):
        """保存窗口状态（位置和大小）"""
        # 保存位置
        log_window_position_manager.set_position(self.view_name, self.pos())
        # 保存大小
        log_window_position_manager.set_size(self.view_name, self.width(), self.height())
    
    def resizeEvent(self, event):
        """窗口大小改变事件"""
        super().resizeEvent(event)
        # 保存新的大小
        if hasattr(self, 'view_name'):
            self._save_window_state()
    
    def center_on_parent(self):
        """在父窗口中居中显示，如果没有父窗口则在屏幕中央显示"""
        if self.parent_widget:
            parent_rect = self.parent_widget.geometry()
            x = parent_rect.x() + (parent_rect.width() - self.width()) // 2
            y = parent_rect.y() + (parent_rect.height() - self.height()) // 2
            self.move(x, y)
        else:
            # 在屏幕中央显示
            from PySide6.QtWidgets import QApplication
            screen = QApplication.primaryScreen()
            screen_rect = screen.availableGeometry()
            x = (screen_rect.width() - self.width()) // 2
            y = (screen_rect.height() - self.height()) // 2
            self.move(x, y)
    
    def closeEvent(self, event):
        """关闭事件"""
        if self.log_watcher:
            self.log_watcher.stop()
        self.window_closed.emit()
        super().closeEvent(event)
