# coding: utf-8
"""
日志管理器
管理浮动按钮和日志窗口的协调工作
"""

from typing import Optional, Dict
from PySide6.QtWidgets import QWidget
from PySide6.QtCore import QObject, Signal, QTimer

from .floating_log_button import Floating<PERSON><PERSON><PERSON><PERSON>on
from .floating_log_window import FloatingLogWindow


class GlobalLogManagerCoordinator(QObject):
    """全局日志管理器协调器 - 单例模式"""
    _instance = None
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
            cls._instance._initialized = False
        return cls._instance
    
    def __init__(self):
        if self._initialized:
            return
        super().__init__()
        self.log_managers: Dict[str, 'LogManager'] = {}
        self.current_view_key: Optional[str] = None
        self._initialized = True
    
    def register_manager(self, key: str, manager: 'LogManager'):
        """注册日志管理器"""
        self.log_managers[key] = manager
        # 如果是第一个管理器，设为当前视图
        if self.current_view_key is None:
            self.switch_to_view(key)
    
    def switch_to_view(self, view_key: str):
        """切换到指定视图"""
        if view_key not in self.log_managers:
            return
        
        # 隐藏所有浮动按钮
        for key, manager in self.log_managers.items():
            if key != view_key:
                manager.hide_button()
        
        # 显示当前视图的浮动按钮
        current_manager = self.log_managers[view_key]
        current_manager.show_button()
        
        # 确保按钮位置正确
        QTimer.singleShot(100, current_manager.ensure_button_position)
        
        self.current_view_key = view_key
    
    def cleanup_all(self):
        """清理所有管理器"""
        for manager in self.log_managers.values():
            manager.cleanup()
        self.log_managers.clear()
        self.current_view_key = None


# 全局协调器实例
global_coordinator = GlobalLogManagerCoordinator()


class LogManager(QObject):
    """日志管理器"""
    
    def __init__(self, parent_widget: QWidget, view_name: str = "", module_filters: list = None, view_key: str = ""):
        super().__init__()
        self.parent_widget = parent_widget
        self.view_name = view_name
        self.view_key = view_key
        self.module_filters = module_filters or []
        
        # 创建浮动按钮
        self.floating_button = FloatingLogButton(parent_widget)
        self.floating_button.log_window_toggle_requested.connect(self._toggle_log_window)
        
        # 日志窗口
        self.log_window: Optional[FloatingLogWindow] = None
        
        # 初始隐藏按钮，由协调器控制显示
        self.floating_button.hide()
        
        # 注册到全局协调器
        if view_key:
            global_coordinator.register_manager(view_key, self)
    
    def _toggle_log_window(self, show: bool):
        """切换日志窗口显示状态"""
        if show:
            self._show_log_window()
        else:
            self._hide_log_window()
    
    def _show_log_window(self):
        """显示日志窗口"""
        if not self.log_window:
            self.log_window = FloatingLogWindow(
                self.parent_widget,
                self.view_name,
                self.module_filters
            )
            self.log_window.window_closed.connect(self._on_log_window_closed)
        
        self.log_window.center_on_parent()
        self.log_window.show()
        self.log_window.raise_()
        self.log_window.activateWindow()
    
    def _hide_log_window(self):
        """隐藏日志窗口"""
        if self.log_window:
            self.log_window.close()
    
    def _on_log_window_closed(self):
        """日志窗口关闭时的处理"""
        self.floating_button.set_log_window_visible(False)
        if self.log_window:
            self.log_window.deleteLater()
            self.log_window = None
    
    def set_view_name(self, view_name: str):
        """设置视图名称"""
        self.view_name = view_name
        if self.log_window:
            self.log_window.view_name = view_name
            title_text = f"{view_name} - 运行日志" if view_name else "运行日志"
            self.log_window.title_label.setText(title_text)
    
    def show_button(self):
        """显示浮动按钮"""
        self.floating_button.show()
        # 延迟确保位置正确
        QTimer.singleShot(100, self.ensure_button_position)
    
    def hide_button(self):
        """隐藏浮动按钮"""
        self.floating_button.hide()
        if self.log_window:
            self.log_window.close()
    
    def ensure_button_position(self):
        """确保按钮位置正确"""
        if self.floating_button.isVisible():
            # 检查当前位置是否合理
            current_pos = self.floating_button.pos()
            if current_pos.x() == 0 and current_pos.y() == 0:
                # 位置不正确，重新设置到右下角
                self.floating_button.position_at_bottom_right()
            else:
                # 位置看起来合理，但确保在边界内
                parent_rect = self.parent_widget.rect()
                if (current_pos.x() + self.floating_button.width() > parent_rect.width() or
                        current_pos.y() + self.floating_button.height() > parent_rect.height() or
                        current_pos.x() < 0 or current_pos.y() < 0):
                    # 位置超出边界，调整位置
                    self.floating_button._adjust_position_to_bounds(current_pos, parent_rect)
    
    def cleanup(self):
        """清理资源"""
        if self.log_window:
            self.log_window.close()
        if self.floating_button:
            self.floating_button.hide()
            self.floating_button.deleteLater()
