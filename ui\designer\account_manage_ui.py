from PySide6.QtCore import Qt, Signal, QSize
from PySide6.QtWidgets import QA<PERSON><PERSON>, QWidget, QVBoxLayout, QHBoxLayout, QLabel, QSplitter
from PySide6.QtWidgets import (QTableWidget, QTableWidgetItem, QHeaderView,
                             QScrollArea, QListWidget, QListWidgetItem, QMenu, QMessageBox, QDialog, QFileDialog, QDialogButtonBox)
from PySide6.QtCore import QTimer
from PySide6.QtGui import QColor, QCursor

from qfluentwidgets import (PushButton, FlowLayout, InfoBar, InfoBarPosition,
                          TableWidget, CheckBox, ComboBox, LineEdit, 
                          TransparentToolButton, ToolButton, IconWidget, BodyLabel,
                          SubtitleLabel, TransparentPushButton, PushButton,
                          Flyout, FlyoutAnimationType, CardWidget, TitleLabel, Action,
                          FluentIcon as FIF,PrimaryPushButton,Dialog)

class GroupItem(QWidget):
    """分组项组件"""
    clicked = Signal(object)  # 点击信号，传递自身引用
    delete_clicked = Signal(object)  # 删除按钮点击信号，传递自身引用
    
    def __init__(self, text, group_id, is_all=False, parent=None):
        super().__init__(parent)
        self.group_id = group_id  # 分组ID，-1表示"所有账号"项
        self.is_all = is_all  # 是否为"所有账号"项
        self.is_selected = False  # 是否被选中
        
        self.init_ui(text)
    
    def init_ui(self, text):
        """初始化UI"""
        self.setAttribute(Qt.WidgetAttribute.WA_StyledBackground, True)
        self.setFixedHeight(36)
        
        # 整体布局
        self.layout = QHBoxLayout(self)
        self.layout.setContentsMargins(8, 4, 8, 4)
        self.layout.setSpacing(6)
        
        # 添加文本标签
        self.text_label = QLabel(text, self)
        self.text_label.setObjectName("groupItemLabel")
        
        self.layout.addWidget(self.text_label)
        self.layout.addStretch(1)
        
        # 如果不是"所有账号"项，添加删除按钮（默认隐藏）
        if not self.is_all:
            self.delete_btn = ToolButton(FIF.DELETE, self)
            self.delete_btn.setFixedSize(QSize(24, 24))
            self.delete_btn.setToolTip("删除分组")
            self.delete_btn.clicked.connect(self._on_delete_clicked)
            self.delete_btn.setVisible(False)  # 默认隐藏
            self.layout.addWidget(self.delete_btn)
        
        # 更新样式
        self.update_style()
        
        # 鼠标点击事件
        self.setCursor(Qt.CursorShape.PointingHandCursor)
    
    def update_style(self):
        """更新样式"""
        if self.is_selected:
            self.setStyleSheet("""
                QWidget {
                    background-color: #ECF2FC;
                    border-radius: 4px;
                }
                #groupItemLabel {
                    color: #0070F5;
                    font-weight: bold;
                }
            """)
        else:
            self.setStyleSheet("""
                QWidget {
                    background-color: transparent;
                    border-radius: 4px;
                }
                QWidget:hover {
                    background-color: #F5F5F5;
                }
            """)
    
    def set_selected(self, selected):
        """设置是否选中"""
        self.is_selected = selected
        self.update_style()
    
    def _on_delete_clicked(self):
        """删除按钮点击事件"""
        self.delete_clicked.emit(self)
    
    def mousePressEvent(self, event):
        """鼠标点击事件"""
        if event.button() == Qt.MouseButton.LeftButton:
            self.clicked.emit(self)
        super().mousePressEvent(event)
    
    def enterEvent(self, event):
        """鼠标进入事件"""
        if not self.is_all and hasattr(self, 'delete_btn'):
            self.delete_btn.setVisible(True)
        super().enterEvent(event)
    
    def leaveEvent(self, event):
        """鼠标离开事件"""
        if not self.is_all and hasattr(self, 'delete_btn'):
            self.delete_btn.setVisible(False)
        super().leaveEvent(event) 

# 自定义分组名称输入对话框
class GroupNameDialog(Dialog):
    def __init__(self, parent=None, current_name=""):
        super().__init__(parent)
        self.setWindowTitle("分组名称") 
        self.setMinimumWidth(300)
        
        self.name_label = SubtitleLabel("请输入分组名称:", self) # 使用 TitleLabel
        self.name_edit = LineEdit(self)
        self.name_edit.setText(current_name)
        self.name_edit.setPlaceholderText("例如：营销组, 测试组")
        
        # 使用 QDialogButtonBox 管理按钮
        self.button_box = QDialogButtonBox(self)
        self.save_button = PrimaryPushButton("保存", self)
        self.cancel_button = PushButton("取消", self)
        self.button_box.addButton(self.cancel_button, QDialogButtonBox.ButtonRole.RejectRole)
        self.button_box.addButton(self.save_button, QDialogButtonBox.ButtonRole.AcceptRole)
        
        self.layout = QVBoxLayout(self)
        self.layout.addWidget(self.name_label)
        self.layout.addWidget(self.name_edit)
        self.layout.addWidget(self.button_box)
        
        # 连接信号
        self.button_box.accepted.connect(self.accept)
        self.button_box.rejected.connect(self.reject)
        
        self.name_edit.setFocus()

    def get_name(self):
        return self.name_edit.text().strip()

# 创建一个鼠标悬停显示删除按钮的容器类
class GroupButtonContainer(QWidget):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.layout = QHBoxLayout(self)
        self.layout.setContentsMargins(0, 0, 0, 0)
        self.layout.setSpacing(2)
        self.delete_btn = None  # 删除按钮的引用
        
    def set_delete_button(self, btn):
        """设置删除按钮引用并默认隐藏"""
        self.delete_btn = btn
        if self.delete_btn:
            self.delete_btn.setVisible(False)
    
    def enterEvent(self, event):
        """鼠标进入时显示删除按钮"""
        if self.delete_btn:
            self.delete_btn.setVisible(True)
        super().enterEvent(event)
    
    def leaveEvent(self, event):
        """鼠标离开时隐藏删除按钮"""
        if self.delete_btn:
            self.delete_btn.setVisible(False)
        super().leaveEvent(event)

class AccountManageUI(QWidget):
    """账户管理页面UI组件 - 基础布局"""
    # 仅保留最基本的信号
    add_account_clicked = Signal()
    import_session_clicked = Signal()
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setObjectName('AccountManageUI')
        
        # 初始化 UI
        self.init_ui()
        self.connect_signals()

    def init_ui(self):
        """初始化UI布局"""
        self.main_layout = QVBoxLayout(self)
        self.main_layout.setSpacing(20)
        self.main_layout.setContentsMargins(30, 30, 30, 30)

        # 顶部按钮区域
        top_layout = QHBoxLayout()
        self.add_account_btn = PushButton('添加账户', self, icon=FIF.ADD)
        self.import_session_btn = PushButton('批量导入Session', self, icon=FIF.FOLDER)
        
        top_layout.addWidget(self.add_account_btn)
        top_layout.addWidget(self.import_session_btn)
        top_layout.addStretch()
        self.main_layout.addLayout(top_layout)
        
        # 中间区域：分组列表(左) + 表格(右)
        self.content_splitter = QSplitter(Qt.Orientation.Horizontal)
        self.content_splitter.setHandleWidth(1)
        self.content_splitter.setChildrenCollapsible(False)
        
        # 左侧和右侧区域的占位符 - 这些将在子类中被替换为实际组件
        self.group_container = QWidget()
        self.table_container = QWidget()
        
        # 设置分割器的两个部件
        self.content_splitter.addWidget(self.group_container)
        self.content_splitter.addWidget(self.table_container)
        
        # 设置初始大小比例 (分组:表格 = 1:4)
        self.content_splitter.setSizes([200, 800])
        
        # 将分割器添加到主布局
        self.main_layout.addWidget(self.content_splitter, 1)
    
    def connect_signals(self):
        """连接界面内部信号"""
        # 顶部按钮
        self.add_account_btn.clicked.connect(lambda: self.add_account_clicked.emit())
        self.import_session_btn.clicked.connect(lambda: self.import_session_clicked.emit())
    
    def show_info(self, title, content, type='info', position = InfoBarPosition.TOP):
        """显示消息提示"""
        
        
        if type == 'success':
            InfoBar.success(
                title=title,
                content=content,
                orient=Qt.Horizontal,
                isClosable=True,
                position=position,
                duration=2000,
                parent=self
            )
        elif type == 'error':
            InfoBar.error(
                title=title,
                content=content,
                orient=Qt.Horizontal,
                isClosable=True,
                position=position,
                duration=3000,
                parent=self
            )
        elif type == 'warning':
            InfoBar.warning(
                title=title,
                content=content,
                orient=Qt.Horizontal,
                isClosable=True,
                position=position,
                duration=3000,
                parent=self
            )
        else:  # 默认为 info
            InfoBar.info(
                title=title,
                content=content,
                orient=Qt.Horizontal,
                isClosable=True,
                position=position,
                duration=2000,
                parent=self
            ) 