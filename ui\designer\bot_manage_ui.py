'''
你是一个优秀的界面设计师，使用PySide6-Fluent-Widgets库来设计一个页面
页面名称：telegram Bot管理页，
功能：
左侧 竖框可滚动，显示Bot Token列表，下方显示添加Bot，删除Bot,每个bot token后面有删除按钮，点击该bot后，右侧显示Bot详情。

右侧为一个卡片框，框里显示bot的token,bot绑定的群组，添加群组，群组信息等，。下方为bot操作日志框，

界面要美观，现代化，添加初始虚拟数据显示，有点显示特效最好，只需要设计好组件样式，可以让其他的类继承该样式

'''
from PySide6.QtCore import Qt, QSize, Signal, QPoint, QPropertyAnimation, QEasingCurve
from PySide6.QtWidgets import QWidget, QVBoxLayout, QHBoxLayout, QFrame, QTableWidgetItem, QMessageBox
from qfluentwidgets import (ScrollArea, PushButton, TableWidget, TextEdit,
                          BodyLabel, CardWidget, IconWidget, FlowLayout,
                          InfoBar, InfoBarPosition, TitleLabel, LineEdit, 
                          SearchLineEdit, StrongBodyLabel, ToolButton,
                          TransparentToolButton, PrimaryPushButton)
from qfluentwidgets import FluentIcon as FIF
from datetime import datetime

class BotManageUI(QWidget):
    # 添加信号
    botSelected = Signal(str)  # Bot选中信号，传递Bot Token
    
    def __init__(self, parent=None):
        super().__init__(parent=parent)
        self.current_selected_bot = None  # 添加一个变量来存储当前选中的bot信息
        self.bot_widgets = {}  # 存储Bot小部件 {Bot Token: widget}
        self.setup_ui()
        self.init_widget()

    def setup_ui(self):
        # 主布局
        self.hBoxLayout = QHBoxLayout(self)
        
        # 左侧Bot列表区域
        self.botListCard = CardWidget(self)
        self.botListLayout = QVBoxLayout(self.botListCard)
        
        # Bot列表标题
        self.botListTitle = TitleLabel('Bot列表')
        self.botListTitle.setAlignment(Qt.AlignCenter)
        
        # 添加搜索框
        self.searchBox = SearchLineEdit(self)
        self.searchBox.setPlaceholderText("搜索Bot...")
        
        # Bot列表滚动区域
        self.scrollArea = ScrollArea()
        self.scrollWidget = QWidget()
        self.vBoxLayout = QVBoxLayout(self.scrollWidget)  # 改为垂直布局确保每行一个Bot
        self.vBoxLayout.setSpacing(10)  # 增加Bot间距
        self.vBoxLayout.setAlignment(Qt.AlignTop)  # 设置为顶部对齐
        
        # Bot操作按钮
        self.addBotButton = PushButton('添加Bot', self, FIF.ADD)
        self.deleteBotButton = PushButton('删除Bot', self, FIF.DELETE)
        self.buttonLayout = QHBoxLayout()
        
        # 右侧详情区域
        self.detailCard = CardWidget(self)
        self.detailLayout = QVBoxLayout(self.detailCard)
        
        # Bot详情区域
        self.usernameLabel = TitleLabel('Bot Username:')
        self.usernameEdit = LineEdit()
        self.usernameEdit.setReadOnly(True)
        
        # 群组表格标题和操作
        self.tableHeader = QHBoxLayout()
        self.groupsTitle = StrongBodyLabel("绑定的群组", self)
        self.addGroupButton = PrimaryPushButton('添加群组', self, FIF.ADD)
        
        # 群组表格
        self.groupTable = TableWidget(self)
        self.groupTable.setColumnCount(5)
        self.groupTable.setHorizontalHeaderLabels(['UID', '用户名', '昵称', '类型', '操作'])
        
        # 日志框
        self.logTitle = TitleLabel('操作日志')
        self.logEdit = TextEdit(self)
        self.logEdit.setReadOnly(True)

    def init_widget(self):
        # 设置主布局
        self.hBoxLayout.setContentsMargins(36, 36, 36, 36)
        self.hBoxLayout.setSpacing(24)
        
        # 配置左侧Bot列表
        self.botListCard.setFixedWidth(280)  # 固定宽度
        self.botListLayout.addWidget(self.botListTitle)
        self.botListLayout.addWidget(self.searchBox)
        self.scrollArea.setWidget(self.scrollWidget)
        self.scrollArea.setWidgetResizable(True)
        self.scrollArea.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        self.botListLayout.addWidget(self.scrollArea, 1)  # 添加拉伸因子，使滚动区域占据主要空间
        
        # 添加Bot操作按钮
        self.buttonLayout.addWidget(self.addBotButton)
        self.buttonLayout.addWidget(self.deleteBotButton)
        self.botListLayout.addLayout(self.buttonLayout, 0)  # 底部按钮不拉伸
        
        # 添加左侧卡片到主布局
        self.hBoxLayout.addWidget(self.botListCard)
        
        # 配置右侧详情区域
        self.detailLayout.addWidget(self.usernameLabel)
        self.detailLayout.addWidget(self.usernameEdit)
        
        # 添加群组表格标题和按钮
        self.tableHeader.addWidget(self.groupsTitle)
        self.tableHeader.addStretch(1)
        self.tableHeader.addWidget(self.addGroupButton)
        self.detailLayout.addLayout(self.tableHeader)
        
        # 添加群组表格
        self.groupTable.horizontalHeader().setStretchLastSection(True)
        self.detailLayout.addWidget(self.groupTable)
        
        # 添加日志标题和文本框
        self.detailLayout.addWidget(self.logTitle)
        self.logEdit.setMaximumHeight(200)
        self.detailLayout.addWidget(self.logEdit)
        
        # 添加右侧卡片到主布局
        self.hBoxLayout.addWidget(self.detailCard)
        
        # 设置删除按钮初始禁用状态
        self.deleteBotButton.setEnabled(False)
        
        # 连接信号
        self.deleteBotButton.clicked.connect(self.confirm_delete_bot)
        self.searchBox.textChanged.connect(self.search_bots)
        
        # 设置样式
        self.setStyleSheet("""
            QScrollArea {
                border: none;
                background-color: transparent;
            }
            CardWidget {
                background-color: rgb(251, 251, 251);
                border-radius: 10px;
            }
            TextEdit {
                border: 1px solid rgb(200, 200, 200);
                border-radius: 5px;
                padding: 5px;
            }
            QFrame.BotItem {
                background-color: white;
                border-radius: 8px;
                padding: 10px;
            }
            QFrame.SelectedBot {
                background-color: #E3F2FD;
                border-radius: 8px;
            }
        """)

    def add_bot_item(self, bot_info):
        """添加Bot项到列表, bot_info 是包含 'username' 和 'token' 的字典"""
        token = bot_info.get('token', 'Unknown Token')
        if token in self.bot_widgets:
            return  # 如果已经存在，不重复添加
            
        botWidget = QFrame()
        botWidget.setObjectName("BotItem")
        botWidget.setProperty("class", "BotItem")  # 设置CSS类名
        botWidget.setFixedHeight(70)  # 设置固定高度
        
        layout = QHBoxLayout(botWidget)
        
        # 显示 username
        username = bot_info.get('username', 'Unknown Bot')
        label = StrongBodyLabel(username) 
        label.setProperty("class", "BotLabel")
        
        # 创建修改按钮
        editBtn = ToolButton(FIF.EDIT, self)
        editBtn.setFixedSize(QSize(24, 24))
        editBtn.setCursor(Qt.PointingHandCursor)
        editBtn.setToolTip("修改Bot信息")
        editBtn.setVisible(False)  # 初始隐藏修改按钮
        
        # 创建删除按钮
        deleteBtn = ToolButton(FIF.DELETE, self)
        deleteBtn.setFixedSize(QSize(24, 24))
        deleteBtn.setCursor(Qt.PointingHandCursor)
        deleteBtn.setToolTip("删除Bot")
        deleteBtn.setVisible(False)  # 初始隐藏删除按钮
        
        # 按钮布局
        buttonLayout = QHBoxLayout()
        buttonLayout.setSpacing(4)  # 设置按钮之间的间距
        buttonLayout.setContentsMargins(0, 0, 0, 0)
        buttonLayout.addWidget(editBtn)
        buttonLayout.addWidget(deleteBtn)
        
        # 添加到布局
        layout.addWidget(label, 1)  # 1表示可伸展
        layout.addLayout(buttonLayout, 0)  # 0表示不可伸展
        layout.setContentsMargins(12, 8, 12, 8)
        
        # 存储Bot信息
        botWidget.setProperty("token", token)
        botWidget.setProperty("username", username)
        
        # 存储Bot小部件
        self.bot_widgets[token] = botWidget
        
        self.vBoxLayout.addWidget(botWidget)
        
        # 添加点击效果
        botWidget.mousePressEvent = lambda e: self.on_bot_clicked(token, username)
        
        # 添加删除按钮点击效果
        deleteBtn.clicked.connect(lambda: self.confirm_delete_bot())
        
        # 添加修改按钮点击效果
        editBtn.clicked.connect(lambda: self.edit_bot(token))
        
        return botWidget

    def on_bot_clicked(self, token, username):
        """处理Bot点击事件"""
        # 取消之前的选中状态
        self.unselect_all_bots()
        
        # 设置当前Bot为选中状态
        self.current_selected_bot = {'token': token, 'username': username}
        self.highlight_selected_bot()
        
        # 启用删除按钮
        self.deleteBotButton.setEnabled(True)
        
        # 显示成功信息
        InfoBar.success(
            title='已选择Bot',
            content=f"当前选择: {username}",
            orient=Qt.Horizontal,
            isClosable=True,
            position=InfoBarPosition.TOP,
            duration=2000,
            parent=self
        )
        
        # 发送Bot选中信号
        self.botSelected.emit(token)
        
        # 更新显示详情
        self.show_bot_detail({'token': token, 'username': username})
    
    def unselect_all_bots(self):
        """取消所有Bot的选中状态"""
        for widget in self.bot_widgets.values():
            widget.setProperty("class", "BotItem")
            widget.setStyleSheet("")
            # 隐藏所有按钮
            for btn in widget.findChildren(ToolButton):
                btn.setVisible(False)
    
    def highlight_selected_bot(self):
        """高亮显示选中的Bot"""
        if self.current_selected_bot:
            token = self.current_selected_bot.get('token')
            if token in self.bot_widgets:
                bot_widget = self.bot_widgets[token]
                bot_widget.setProperty("class", "SelectedBot")
                bot_widget.setStyleSheet("background-color: #E3F2FD; border-radius: 8px;")
                
                # 显示所有按钮
                for btn in bot_widget.findChildren(ToolButton):
                    btn.setVisible(True)

    def show_bot_detail(self, bot_info):
        """显示Bot详情"""
        username = bot_info.get('username', 'N/A')
        token = bot_info.get('token', 'N/A')
        
        self.usernameEdit.setText(username)
        
        # 清空并填充群组表格（这里可以根据实际的bot_info加载关联的群组数据）
        self.groupTable.setRowCount(0)
        
        # 示例：加载与此 bot 关联的群组数据
        self.load_group_data(token)
        
        # 更新日志框内容
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        self.logEdit.append(f"[{current_time}] 选择Bot: {username}")

    def search_bots(self, text):
        """搜索Bot"""
        for token, widget in self.bot_widgets.items():
            username = widget.property("username")
            if not text or text.lower() in username.lower() or text.lower() in token.lower():
                widget.setVisible(True)
            else:
                widget.setVisible(False)

    def confirm_delete_bot(self):
        """确认删除当前选中的Bot"""
        if not self.current_selected_bot:
            InfoBar.warning(
                title='警告',
                content="请先选择要删除的Bot",
                orient=Qt.Horizontal,
                isClosable=True,
                position=InfoBarPosition.TOP,
                duration=2000,
                parent=self
            )
            return
            
        username = self.current_selected_bot.get('username')
        
        # 显示确认对话框
        reply = QMessageBox.question(
            self,
            '确认删除',
            f"确定要删除Bot '{username}' 吗？\n删除后将无法恢复。",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            # 执行删除操作
            self.delete_bot(self.current_selected_bot.get('token'))

    def delete_bot(self, token):
        """删除指定的Bot"""
        if token in self.bot_widgets:
            # 从布局中移除控件
            botWidget = self.bot_widgets[token]
            self.vBoxLayout.removeWidget(botWidget)
            botWidget.deleteLater()
            
            # 从字典中移除
            del self.bot_widgets[token]
            
            # 清除选中状态
            if self.current_selected_bot and self.current_selected_bot.get('token') == token:
                self.current_selected_bot = None
                self.deleteBotButton.setEnabled(False)
                
                # 清空表格
                self.groupTable.setRowCount(0)
                
                # 清空用户名
                self.usernameEdit.clear()
            
            # 强制布局重新计算
            self.vBoxLayout.update()
            self.scrollWidget.updateGeometry()
            
            # 显示成功消息
            InfoBar.success(
                title='成功',
                content="Bot已删除",
                orient=Qt.Horizontal,
                isClosable=True,
                position=InfoBarPosition.TOP,
                duration=2000,
                parent=self
            )
            
            # 添加日志
            current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            self.logEdit.append(f"[{current_time}] 删除Bot: {token}")

    def edit_bot(self, token):
        """编辑Bot信息"""
        if token in self.bot_widgets:
            username = self.bot_widgets[token].property("username")
            
            # 显示信息
            InfoBar.info(
                title='编辑Bot',
                content=f"正在编辑Bot: {username}",
                orient=Qt.Horizontal,
                isClosable=True,
                position=InfoBarPosition.TOP,
                duration=2000,
                parent=self
            )
            
            # 添加日志
            current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            self.logEdit.append(f"[{current_time}] 开始编辑Bot: {username}")
            
            # 注意：实际应用中，这里应该打开Bot编辑对话框

    def load_group_data(self, token):
        """加载与指定Bot关联的群组数据"""
        # 在实际应用中，应该从数据库或API加载数据
        # 这里使用演示数据
        demo_groups = [
            {'uid': '-1001234567890', 'username': '@TestGroup1', 'nickname': '测试群组1', 'type': '聊天群'},
            {'uid': '123456789', 'username': 'some_user', 'nickname': '张三', 'type': '个人'},
            {'uid': '-1009876543210', 'username': '@MyChannel', 'nickname': '我的频道', 'type': '频道'}
        ]
        
        self.groupTable.setRowCount(len(demo_groups))
        for row, group in enumerate(demo_groups):
            self.groupTable.setItem(row, 0, QTableWidgetItem(group['uid']))
            self.groupTable.setItem(row, 1, QTableWidgetItem(group['username']))
            self.groupTable.setItem(row, 2, QTableWidgetItem(group['nickname']))
            self.groupTable.setItem(row, 3, QTableWidgetItem(group['type']))
            
            # 添加操作按钮
            deleteGroupBtn = TransparentToolButton(FIF.DELETE, self)
            deleteGroupBtn.setToolTip("删除群组")
            # deleteGroupBtn.clicked.connect(lambda checked, r=row: self.delete_group(r))
            self.groupTable.setCellWidget(row, 4, deleteGroupBtn)

    def add_demo_data(self):
        """添加演示数据"""
        # 使用字典列表存储 demo bot 信息
        demo_bots = [
            {'username': 'DemoBotAlpha', 'token': '1234567890:ABCDEF'},
            {'username': 'DemoBotBeta', 'token': '0987654321:FEDCBA'},
            {'username': 'HelperBotGamma', 'token': '1111111111:XYZ'},
            {'username': 'NotificationBot', 'token': '2222222222:NOTIF'},
            {'username': 'MarketingBot', 'token': '3333333333:MARK'}
        ]
        for bot in demo_bots:
            self.add_bot_item(bot)
            
        # 默认选中第一个Bot并显示详情
        if demo_bots:
            first_bot = demo_bots[0]
            self.on_bot_clicked(first_bot['token'], first_bot['username'])

        # 添加日志演示数据
        self.logEdit.clear()
        self.logEdit.append("2023-12-26 11:00:00 应用程序启动")
        self.logEdit.append(f"2023-12-26 11:00:05 自动选择 Bot: {demo_bots[0]['username']}")
        self.logEdit.append("2023-12-26 11:01:00 加载群组信息")

if __name__ == '__main__':
    from PySide6.QtWidgets import QApplication
    app = QApplication([])
    window = BotManageUI()
    window.show()
    app.exec()
