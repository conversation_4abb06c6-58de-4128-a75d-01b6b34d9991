<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>Form</class>
 <widget class="QWidget" name="Form">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>1012</width>
    <height>862</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout_3">
   <item>
    <widget class="StrongBodyLabel" name="StrongBodyLabel">
     <property name="text">
      <string>Tdata和Sesison互转</string>
     </property>
    </widget>
   </item>
   <item>
    <widget class="CardWidget" name="CardWidget">
     <layout class="QVBoxLayout" name="verticalLayout">
      <item>
       <layout class="QHBoxLayout" name="horizontalLayout_4">
        <item>
         <layout class="QHBoxLayout" name="horizontalLayout">
          <item>
           <widget class="QLabel" name="label_3">
            <property name="text">
             <string>互转方向</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="ComboBox" name="ComboBox"/>
          </item>
         </layout>
        </item>
        <item>
         <layout class="QHBoxLayout" name="horizontalLayout_2">
          <item>
           <widget class="QLabel" name="label_2">
            <property name="text">
             <string>密码类型</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="ComboBox" name="ComboBox_2"/>
          </item>
          <item>
           <widget class="QLabel" name="label">
            <property name="text">
             <string>默认密码</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="LineEdit" name="LineEdit"/>
          </item>
         </layout>
        </item>
       </layout>
      </item>
      <item>
       <layout class="QHBoxLayout" name="horizontalLayout_3">
        <item>
         <widget class="QLabel" name="label_4">
          <property name="text">
           <string>代理ip(可不设置，只支持Socks5)</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="LineEdit" name="LineEdit_4">
          <property name="placeholderText">
           <string>IP地址</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="LineEdit" name="LineEdit_5">
          <property name="sizePolicy">
           <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
            <horstretch>0</horstretch>
            <verstretch>0</verstretch>
           </sizepolicy>
          </property>
          <property name="placeholderText">
           <string>1080</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="LineEdit" name="LineEdit_6">
          <property name="placeholderText">
           <string>用户名，默认空</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="LineEdit" name="LineEdit_7">
          <property name="placeholderText">
           <string>密码默认无</string>
          </property>
         </widget>
        </item>
        <item>
         <spacer name="horizontalSpacer_4">
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>40</width>
            <height>20</height>
           </size>
          </property>
         </spacer>
        </item>
       </layout>
      </item>
      <item>
       <layout class="QHBoxLayout" name="horizontalLayout_9">
        <item>
         <spacer name="horizontalSpacer">
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>40</width>
            <height>20</height>
           </size>
          </property>
         </spacer>
        </item>
        <item>
         <widget class="PushButton" name="PushButton">
          <property name="text">
           <string>导入</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="PushButton" name="PushButton_3">
          <property name="text">
           <string>清空列表</string>
          </property>
         </widget>
        </item>
       </layout>
      </item>
     </layout>
    </widget>
   </item>
   <item>
    <widget class="TableWidget" name="TableWidget">
     <property name="sizePolicy">
      <sizepolicy hsizetype="Expanding" vsizetype="Preferred">
       <horstretch>0</horstretch>
       <verstretch>50</verstretch>
      </sizepolicy>
     </property>
     <column>
      <property name="text">
       <string>号码</string>
      </property>
     </column>
     <column>
      <property name="text">
       <string>类型</string>
      </property>
     </column>
     <column>
      <property name="text">
       <string>密码</string>
      </property>
     </column>
     <column>
      <property name="text">
       <string>注册时间</string>
      </property>
     </column>
     <column>
      <property name="text">
       <string>是否会员</string>
      </property>
     </column>
     <column>
      <property name="text">
       <string>转换结果</string>
      </property>
     </column>
    </widget>
   </item>
   <item>
    <widget class="CardWidget" name="CardWidget_2">
     <layout class="QHBoxLayout" name="horizontalLayout_7">
      <item>
       <layout class="QVBoxLayout" name="verticalLayout_2">
        <item>
         <layout class="QHBoxLayout" name="horizontalLayout_5">
          <item>
           <widget class="QLabel" name="label_5">
            <property name="text">
             <string>导出路径</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="LineEdit" name="LineEdit_3">
            <property name="placeholderText">
             <string>socks5://127.0.0.1:1080</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="PushButton" name="PushButton_2">
            <property name="text">
             <string>选择</string>
            </property>
           </widget>
          </item>
          <item>
           <spacer name="horizontalSpacer_3">
            <property name="orientation">
             <enum>Qt::Horizontal</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>40</width>
              <height>20</height>
             </size>
            </property>
           </spacer>
          </item>
         </layout>
        </item>
        <item>
         <layout class="QHBoxLayout" name="horizontalLayout_6">
          <item>
           <widget class="QLabel" name="label_6">
            <property name="sizePolicy">
             <sizepolicy hsizetype="Fixed" vsizetype="Preferred">
              <horstretch>0</horstretch>
              <verstretch>0</verstretch>
             </sizepolicy>
            </property>
            <property name="text">
             <string>导出格式</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="CheckBox" name="CheckBox">
            <property name="sizePolicy">
             <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
              <horstretch>0</horstretch>
              <verstretch>0</verstretch>
             </sizepolicy>
            </property>
            <property name="text">
             <string>Telethon协议</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="CheckBox" name="CheckBox_2">
            <property name="text">
             <string>Tdata直登号</string>
            </property>
           </widget>
          </item>
          <item>
           <spacer name="horizontalSpacer_2">
            <property name="orientation">
             <enum>Qt::Horizontal</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>40</width>
              <height>20</height>
             </size>
            </property>
           </spacer>
          </item>
         </layout>
        </item>
       </layout>
      </item>
      <item>
       <widget class="PrimaryPushButton" name="PrimaryPushButton">
        <property name="text">
         <string>开始转化</string>
        </property>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
   <item>
    <widget class="QGroupBox" name="groupBox">
     <property name="sizePolicy">
      <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
       <horstretch>0</horstretch>
       <verstretch>20</verstretch>
      </sizepolicy>
     </property>
     <property name="title">
      <string>操作日志</string>
     </property>
     <layout class="QHBoxLayout" name="horizontalLayout_8">
      <item>
       <widget class="QTextBrowser" name="textBrowser"/>
      </item>
     </layout>
    </widget>
   </item>
  </layout>
 </widget>
 <customwidgets>
  <customwidget>
   <class>CheckBox</class>
   <extends>QCheckBox</extends>
   <header>qfluentwidgets</header>
  </customwidget>
  <customwidget>
   <class>ComboBox</class>
   <extends>QPushButton</extends>
   <header>qfluentwidgets</header>
  </customwidget>
  <customwidget>
   <class>PushButton</class>
   <extends>QPushButton</extends>
   <header>qfluentwidgets</header>
  </customwidget>
  <customwidget>
   <class>PrimaryPushButton</class>
   <extends>PushButton</extends>
   <header>qfluentwidgets</header>
  </customwidget>
  <customwidget>
   <class>CardWidget</class>
   <extends>QFrame</extends>
   <header>qfluentwidgets</header>
   <container>1</container>
  </customwidget>
  <customwidget>
   <class>StrongBodyLabel</class>
   <extends>QLabel</extends>
   <header>qfluentwidgets</header>
  </customwidget>
  <customwidget>
   <class>LineEdit</class>
   <extends>QLineEdit</extends>
   <header>qfluentwidgets</header>
  </customwidget>
  <customwidget>
   <class>TableWidget</class>
   <extends>QTableWidget</extends>
   <header>qfluentwidgets</header>
  </customwidget>
 </customwidgets>
 <resources/>
 <connections/>
</ui>
