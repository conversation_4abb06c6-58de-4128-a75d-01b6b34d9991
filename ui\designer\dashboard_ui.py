from PySide6.QtWidgets import QWidget, QVBoxLayout, QHBoxLayout, QGridLayout, QLabel, QFrame, QSizePolicy
from PySide6.QtCore import Qt, QPropertyAnimation, QEasingCurve, QPoint, Signal
from PySide6.QtGui import QColor, QPainter, QLinearGradient, QPixmap, QIcon, QFont
from qfluentwidgets import (CardWidget, FluentIcon, SubtitleLabel, TitleLabel, StrongBodyLabel,
                          BodyLabel, setTheme, Theme, IconWidget, setFont, FluentStyleSheet,
                          InfoBadge, InfoLevel, PushButton, TransparentToolButton,PrimaryPushButton)
import logging
import os

class WelcomeBanner(CardWidget):
    """顶部欢迎横幅"""
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setObjectName("WelcomeBanner")
        self._setup_ui()

    def _setup_ui(self):
        layout = QHBoxLayout(self)
        layout.setContentsMargins(30, 20, 30, 20)
        layout.setSpacing(25)

        # 机器人插画占位符
        robot_placeholder = QLabel(self)
        robot_placeholder.setFixedSize(100, 100)
        robot_placeholder.setStyleSheet("background-color: #e0e0f0; border-radius: 50px;")
        robot_placeholder.setAlignment(Qt.AlignCenter)
        layout.addWidget(robot_placeholder)

        # 欢迎文字和状态
        text_layout = QVBoxLayout()
        text_layout.setSpacing(8)
        
        title = TitleLabel("您好，欢迎使用Telegram营销助手！", self)
        subtitle = BodyLabel("助手 已准备就绪，为您提供高效的工作服务", self)
        
        status_layout = QHBoxLayout()
        status_layout.setSpacing(10)
        # 状态指示器
        status_badge = InfoBadge.custom("Telegram营销助手 正在运行", QColor("#6C5CE7"), QColor("#6C5CE7"), self)
        status_layout.addWidget(status_badge)
        status_layout.addStretch(1)
        
        text_layout.addWidget(title)
        text_layout.addWidget(subtitle)
        text_layout.addSpacing(10)
        text_layout.addLayout(status_layout)
        text_layout.addStretch(1)

        layout.addLayout(text_layout, 1)

        # 按钮区域
        button_layout = QVBoxLayout()
        button_layout.setSpacing(10)
        button_layout.setAlignment(Qt.AlignTop)
        
        self.start_button = PrimaryPushButton("启动 Telegram", self, FluentIcon.PLAY)

     
        
        button_layout.addWidget(self.start_button)
        button_layout.addStretch(1)

        layout.addLayout(button_layout)

        # Banner 样式
        self.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Preferred)
        self.setStyleSheet("""
            #WelcomeBanner {
                background-color: rgba(245, 245, 250, 0.8);
                border-radius: 12px;
                min-height: 120px;
            }
        """)



class FunctionCard(CardWidget):
    """系统功能卡片"""
    clicked = Signal()

    def __init__(self, title: str, description: str, icon: FluentIcon, is_pro: bool = False, parent=None):
        super().__init__(parent)
        self.setObjectName("FunctionCard")
        self.is_pro = is_pro
        self._setup_ui(title, description, icon)
        self._set_style()
        self.setCursor(Qt.PointingHandCursor)

    def _setup_ui(self, title: str, description: str, icon: FluentIcon):
        self.main_layout = QHBoxLayout(self)
        self.main_layout.setContentsMargins(15, 15, 15, 15)
        self.main_layout.setSpacing(15)

        # 左侧图标
        icon_widget = IconWidget(icon, self)
        icon_widget.setFixedSize(32, 32)
        self.main_layout.addWidget(icon_widget)

        # 中间文字
        text_layout = QVBoxLayout()
        text_layout.setSpacing(2)
        title_label = StrongBodyLabel(title, self)
        desc_label = BodyLabel(description, self)
        desc_label.setStyleSheet("color: #606060;")
        text_layout.addWidget(title_label)
        text_layout.addWidget(desc_label)
        self.main_layout.addLayout(text_layout, 1)

        # PRO 标签 (如果需要)
        if self.is_pro:
            pro_badge = InfoBadge.info("PRO", self)
            self.main_layout.addWidget(pro_badge)

        # 右侧箭头
        arrow_icon = IconWidget(FluentIcon.CHEVRON_RIGHT, self)
        arrow_icon.setFixedSize(16, 16)
        self.main_layout.addWidget(arrow_icon)

    def _set_style(self):
        self.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Preferred)
        self.setBorderRadius(8)
        self.setStyleSheet("""
            #FunctionCard {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #ffffff, stop:1 #f8f9fa);
                border: 1px solid #e5e5e5;
                min-height: 100px;
            }
            #FunctionCard:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #fdfdfd, stop:1 #f0f1f2);
                border: 1px solid #d5d5d5;
            }
        """)
        
    def mouseReleaseEvent(self, event):
        if event.button() == Qt.LeftButton:
            self.clicked.emit()
        super().mouseReleaseEvent(event)


class OverviewStatCard(CardWidget):
    """系统概览统计卡片"""
    def __init__(self, title: str, value: str, icon: FluentIcon = None, parent=None):
        super().__init__(parent)
        self.setObjectName("OverviewStatCard")
        self._setup_ui(title, value, icon)
        self._set_style()

    def _setup_ui(self, title: str, value: str, icon: FluentIcon):
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 15, 20, 15)
        layout.setSpacing(5)
        layout.setAlignment(Qt.AlignTop)

        # Top row for value and icon
        top_layout = QHBoxLayout()
        top_layout.setContentsMargins(0,0,0,0)
        top_layout.setSpacing(5)
        
        # 数值 (适当字体大小)
        self.value_label = TitleLabel(value, self)
        self.value_label.setObjectName("overviewValue")
        setFont(self.value_label, 22)  # 减小字体大小，去除粗体
        top_layout.addWidget(self.value_label, 1)

        # 右上角图标 (如果提供)
        if icon:
            icon_widget = IconWidget(icon, self)
            icon_widget.setFixedSize(20, 20)
            top_layout.addWidget(icon_widget, 0, Qt.AlignTop)
            
        layout.addLayout(top_layout)
        
        # 标题 (小字体)
        self.title_label = BodyLabel(title, self)
        self.title_label.setObjectName("overviewTitle")
        layout.addWidget(self.title_label)
        layout.addStretch(1)
            

    def _set_style(self):
        self.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Preferred)
        self.setBorderRadius(8)
        self.setStyleSheet("""
            #OverviewStatCard {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #ffffff, stop:1 #f8f9fa);
                border: 1px solid #e5e5e5;
                min-height: 90px;
            }
            #OverviewStatCard:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #fdfdfd, stop:1 #f0f1f2);
            }
            QLabel#overviewValue {
                color: #333;
            }
            QLabel#overviewTitle {
                color: #666;
            }
        """)

    def update_value(self, value: str):
        self.value_label.setText(value)

class DashboardUI(QWidget):
    """数据看板UI组件"""
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setObjectName("dashboardUI")
        
        # UI组件字典，方便外部访问
        self.components = {}
        self._setup_ui()
        
    def _setup_ui(self):
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(30, 20, 30, 30)
        main_layout.setSpacing(25)

        # 1. 欢迎横幅
        welcome_banner = WelcomeBanner(self)
        self.components['welcome_banner'] = welcome_banner
        main_layout.addWidget(welcome_banner)

        # 2. 系统功能
        func_title_layout = QHBoxLayout()
        func_title_label = StrongBodyLabel("系统功能", self)
        func_title_layout.addWidget(func_title_label)
        func_title_layout.addStretch(1)
        main_layout.addLayout(func_title_layout)

        func_grid_layout = QGridLayout()
        func_grid_layout.setSpacing(15)
        
        # 添加功能卡片
        card_reset = FunctionCard("机器配置", "配置机器人发信，防封高效率", FluentIcon.PEOPLE, parent=self)
        card_update = FunctionCard("自动控制", "控制每个账户每天的活跃额度，防止封号", FluentIcon.UPDATE, parent=self)
        card_repair = FunctionCard("智能通知", "当用户搜索某些关键词，立即通知给您", FluentIcon.PEOPLE, parent=self)
        card_pool = FunctionCard("账号池管理", "智能管理多个账号，提升额度利用率", FluentIcon.PEOPLE, is_pro=True, parent=self)
        card_export = FunctionCard("用户导出", "导出采集到的用户，方便营销", FluentIcon.DOCUMENT, parent=self)
        
        # 保存卡片引用
        self.components['card_reset'] = card_reset
        self.components['card_update'] = card_update
        self.components['card_repair'] = card_repair
        self.components['card_pool'] = card_pool
        self.components['card_export'] = card_export

        func_grid_layout.addWidget(card_reset, 0, 0)
        func_grid_layout.addWidget(card_update, 0, 1)
        func_grid_layout.addWidget(card_repair, 0, 2)
        func_grid_layout.addWidget(card_pool, 1, 0)
        func_grid_layout.addWidget(card_export, 1, 1)
        
        func_grid_layout.setColumnStretch(0, 1)
        func_grid_layout.setColumnStretch(1, 1)
        func_grid_layout.setColumnStretch(2, 1)

        main_layout.addLayout(func_grid_layout)

        # 3. 系统概览
        overview_title_layout = QHBoxLayout()
        overview_title_label = StrongBodyLabel("系统概览", self)
        overview_title_layout.addWidget(overview_title_label)
        overview_title_layout.addStretch(1)
        main_layout.addLayout(overview_title_layout)

        overview_grid_layout = QGridLayout()
        overview_grid_layout.setSpacing(15)

        # 添加概览卡片
        version_card = OverviewStatCard("当前版本", "0.0.0", FluentIcon.TAG, self)
        accounts_card = OverviewStatCard("管理账号", "0", FluentIcon.PEOPLE, self)
        optimize_card = OverviewStatCard("修复状态", "未知", FluentIcon.CHECKBOX, self)
        autoupdate_card = OverviewStatCard("自动更新", "未知", FluentIcon.UPDATE, self)
        
        total_users_card = OverviewStatCard("总用户数", "0", FluentIcon.ADD_TO, self)
        active_users_card = OverviewStatCard("已登录用户", "0", FluentIcon.ACCEPT, self)
        failed_users_card = OverviewStatCard("登录失败用户", "0", FluentIcon.ADD_TO, self)
        total_pull_in_card = OverviewStatCard("总拉人入群", "0", FluentIcon.ADD_TO, self)
        
        total_message_card = OverviewStatCard("总私信次数", "0", FluentIcon.MAIL, self)
        daily_pull_in_card = OverviewStatCard("今日拉人入群", "0", FluentIcon.ADD_TO, self)
        daily_message_card = OverviewStatCard("今日私信次数", "0", FluentIcon.MAIL, self)

        # 保存卡片引用
        self.components['version_card'] = version_card
        self.components['accounts_card'] = accounts_card
        self.components['optimize_card'] = optimize_card
        self.components['autoupdate_card'] = autoupdate_card
        self.components['total_users_card'] = total_users_card
        self.components['active_users_card'] = active_users_card
        self.components['failed_users_card'] = failed_users_card
        self.components['total_pull_in_card'] = total_pull_in_card
        self.components['total_message_card'] = total_message_card
        self.components['daily_pull_in_card'] = daily_pull_in_card
        self.components['daily_message_card'] = daily_message_card

        # 添加到布局
        overview_grid_layout.addWidget(version_card, 0, 0)
        overview_grid_layout.addWidget(accounts_card, 0, 1)
        overview_grid_layout.addWidget(optimize_card, 0, 2)
        overview_grid_layout.addWidget(autoupdate_card, 0, 3)
        
        overview_grid_layout.addWidget(total_users_card, 1, 0) 
        overview_grid_layout.addWidget(active_users_card, 1, 1)
        overview_grid_layout.addWidget(failed_users_card, 1, 2)
        overview_grid_layout.addWidget(total_pull_in_card, 1, 3)

        overview_grid_layout.addWidget(total_message_card, 2, 0)
        overview_grid_layout.addWidget(daily_pull_in_card, 2, 1)
        overview_grid_layout.addWidget(daily_message_card, 2, 2)
        
        # 设置每列拉伸
        for i in range(4):
            overview_grid_layout.setColumnStretch(i, 1)

        main_layout.addLayout(overview_grid_layout)
        main_layout.addStretch(1) # 向上推

        # 设置整体背景色
        self.setStyleSheet("#dashboardUI { background-color: #f9f9f9; }") 
