import sys
from typing import Optional, List, Dict

from PySide6.QtWidgets import (
    QApplication, QWidget, QVBoxLayout, QHBoxLayout, 
    QLabel, QHeaderView, QAbstractItemView, QFrame, QPushButton,
    QTableWidget, QTableWidgetItem, QButtonGroup, QToolButton,
    QRadioButton, QSpinBox, QTextEdit, QLineEdit, QCheckBox,
    QSlider, QComboBox, QScrollArea, QSizePolicy
)
from PySide6.QtCore import Qt, Signal, Slot, QSize
from PySide6.QtGui import QColor, QIcon, QFont

class InviteUserUI(QWidget):
    """邀请用户入群界面UI组件类
    
    功能：
    1. 选择目标用户
    2. 选择目标群组
    3. 设置邀请消息
    4. 设置邀请策略
    5. 查看邀请历史和状态
    """
    
    def __init__(self, parent=None):
        super().__init__(parent)
        
        # 设置对象名称和样式
        self.setObjectName("InviteUserUI")
        self.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 1px solid #d0d0d0;
                border-radius: 6px;
                margin-top: 12px;
                padding-top: 10px;
            }
            
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px;
            }
            
            #StatsCard {
                background-color: white;
                border-radius: 8px;
                border: 1px solid #e0e0e0;
            }
            
            QLabel#TitleLabel {
                font-size: 18px;
                font-weight: bold;
            }
            
            QLabel#SubtitleLabel {
                font-size: 14px;
                font-weight: bold;
            }
            
            QLabel#StrongBodyLabel {
                font-weight: bold;
            }
            
            QPushButton#PrimaryButton {
                background-color: #2b579a;
                color: white;
                border-radius: 4px;
                padding: 6px 12px;
                font-weight: bold;
            }
            
            QPushButton#PrimaryButton:hover {
                background-color: #1b4583;
            }
            
            QFrame#Card {
                background-color: white;
                border-radius: 8px;
                border: 1px solid #e0e0e0;
            }
            
            QLabel#StatValue {
                color: #409eff;
                font-size: 24px;
                font-weight: bold;
            }
            
            QLabel#StatTitle {
                color: #666;
                font-size: 14px;
            }
        """)
        
        # 创建所有UI组件
        self._setup_ui()
        
    def _setup_ui(self):
        """创建并设置UI组件"""
        # 创建主布局
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(16, 16, 16, 16)
        main_layout.setSpacing(16)
        
        # 标题和操作按钮
        header_layout = QHBoxLayout()
        title_label = QLabel("邀请用户入群")
        title_label.setObjectName("TitleLabel")
        header_layout.addWidget(title_label)
        header_layout.addStretch()
        
        # 添加邀请按钮
        self.add_invite_btn = QPushButton("创建新邀请")
        self.add_invite_btn.setObjectName("PrimaryButton")
        header_layout.addWidget(self.add_invite_btn)
        
        main_layout.addLayout(header_layout)
        
        # 创建工作区域 - 分为左右两部分
        work_area = QHBoxLayout()
        
        # 左侧 - 用户和群组选择区域
        left_panel = QWidget()
        left_layout = QVBoxLayout(left_panel)
        left_layout.setContentsMargins(0, 0, 8, 0)
        
        # 用户选择区域
        user_card = QFrame()
        user_card.setObjectName("Card")
        user_layout = QVBoxLayout(user_card)
        
        user_header = QHBoxLayout()
        user_subtitle = QLabel("目标用户")
        user_subtitle.setObjectName("SubtitleLabel")
        user_header.addWidget(user_subtitle)
        user_header.addStretch()
        
        # 用户搜索框
        self.user_search = QLineEdit()
        self.user_search.setPlaceholderText("搜索用户...")
        self.user_search.setFixedWidth(200)
        user_header.addWidget(self.user_search)
        
        # 用户筛选按钮
        self.user_filter_btn = QToolButton()
        self.user_filter_btn.setText("筛选")
        self.user_filter_btn.setToolTip("筛选用户")
        user_header.addWidget(self.user_filter_btn)
        
        user_layout.addLayout(user_header)
        
        # 用户表格
        self.user_table = QTableWidget()
        self.user_table.setColumnCount(4)
        self.user_table.setHorizontalHeaderLabels(["选择", "用户名", "状态", "上次活跃"])
        self.user_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.user_table.horizontalHeader().setSectionResizeMode(0, QHeaderView.ResizeToContents)
        self.user_table.setEditTriggers(QAbstractItemView.NoEditTriggers)
        user_layout.addWidget(self.user_table)
        
        # 批量操作按钮
        batch_actions = QHBoxLayout()
        self.select_all_btn = QPushButton("全选")
        batch_actions.addWidget(self.select_all_btn)
        
        self.deselect_all_btn = QPushButton("取消全选")
        batch_actions.addWidget(self.deselect_all_btn)
        
        self.import_users_btn = QPushButton("导入用户")
        batch_actions.addWidget(self.import_users_btn)
        
        batch_actions.addStretch()
        
        # 显示已选用户数量
        self.selected_user_count = QLabel("已选择: 0位用户")
        batch_actions.addWidget(self.selected_user_count)
        
        user_layout.addLayout(batch_actions)
        left_layout.addWidget(user_card)
        
        # 群组选择区域
        group_card = QFrame()
        group_card.setObjectName("Card")
        group_layout = QVBoxLayout(group_card)
        
        group_header = QHBoxLayout()
        group_subtitle = QLabel("目标群组")
        group_subtitle.setObjectName("SubtitleLabel")
        group_header.addWidget(group_subtitle)
        group_header.addStretch()
        
        # 群组搜索框
        self.group_search = QLineEdit()
        self.group_search.setPlaceholderText("搜索群组...")
        self.group_search.setFixedWidth(200)
        group_header.addWidget(self.group_search)
        
        # 群组筛选按钮
        self.group_filter_btn = QToolButton()
        self.group_filter_btn.setText("筛选")
        self.group_filter_btn.setToolTip("筛选群组")
        group_header.addWidget(self.group_filter_btn)
        
        group_layout.addLayout(group_header)
        
        # 群组表格
        self.group_table = QTableWidget()
        self.group_table.setColumnCount(5)
        self.group_table.setHorizontalHeaderLabels(["选择", "群组名称", "成员数", "创建时间", "描述"])
        self.group_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.group_table.horizontalHeader().setSectionResizeMode(0, QHeaderView.ResizeToContents)
        self.group_table.setEditTriggers(QAbstractItemView.NoEditTriggers)
        group_layout.addWidget(self.group_table)
        
        # 批量操作按钮
        group_actions = QHBoxLayout()
        self.select_all_groups_btn = QPushButton("全选")
        group_actions.addWidget(self.select_all_groups_btn)
        
        self.deselect_all_groups_btn = QPushButton("取消全选")
        group_actions.addWidget(self.deselect_all_groups_btn)
        
        group_actions.addStretch()
        
        # 显示已选群组数量
        self.selected_group_count = QLabel("已选择: 0个群组")
        group_actions.addWidget(self.selected_group_count)
        
        group_layout.addLayout(group_actions)
        left_layout.addWidget(group_card)
        
        # 右侧 - 邀请设置和预览区域
        right_panel = QWidget()
        right_layout = QVBoxLayout(right_panel)
        right_layout.setContentsMargins(8, 0, 0, 0)
        
        # 邀请设置区域
        invite_settings_card = QFrame()
        invite_settings_card.setObjectName("Card")
        invite_settings_layout = QVBoxLayout(invite_settings_card)
        
        settings_subtitle = QLabel("邀请设置")
        settings_subtitle.setObjectName("SubtitleLabel")
        invite_settings_layout.addWidget(settings_subtitle)
        
        # 邀请消息
        message_layout = QVBoxLayout()
        message_layout.addWidget(QLabel("邀请消息:"))
        self.invite_message = QTextEdit()
        self.invite_message.setPlaceholderText("请输入邀请消息，该消息将发送给被邀请的用户...")
        self.invite_message.setFixedHeight(100)
        message_layout.addWidget(self.invite_message)
        
        # 默认消息模板
        templates_layout = QHBoxLayout()
        templates_layout.addWidget(QLabel("快速模板:"))
        
        self.template1_btn = QPushButton("正式邀请")
        templates_layout.addWidget(self.template1_btn)
        
        self.template2_btn = QPushButton("好友邀请")
        templates_layout.addWidget(self.template2_btn)
        
        self.template3_btn = QPushButton("活动邀请")
        templates_layout.addWidget(self.template3_btn)
        
        templates_layout.addStretch()
        message_layout.addLayout(templates_layout)
        
        invite_settings_layout.addLayout(message_layout)
        
        # 分隔线
        separator = QFrame()
        separator.setFrameShape(QFrame.HLine)
        separator.setFrameShadow(QFrame.Sunken)
        separator.setStyleSheet("background-color: #d0d0d0; max-height: 1px;")
        invite_settings_layout.addWidget(separator)
        
        # 邀请策略
        strategy_layout = QVBoxLayout()
        strategy_label = QLabel("邀请策略")
        strategy_label.setObjectName("StrongBodyLabel")
        strategy_layout.addWidget(strategy_label)
        
        # 邀请间隔
        interval_layout = QHBoxLayout()
        interval_layout.addWidget(QLabel("邀请间隔:"))
        self.invite_interval = QSpinBox()
        self.invite_interval.setRange(1, 60)
        self.invite_interval.setValue(5)
        interval_layout.addWidget(self.invite_interval)
        interval_layout.addWidget(QLabel("秒"))
        
        self.random_interval = QCheckBox("随机波动")
        self.random_interval.setChecked(True)
        interval_layout.addWidget(self.random_interval)
        
        interval_layout.addStretch()
        strategy_layout.addLayout(interval_layout)
        
        # 每群最大邀请人数
        max_layout = QHBoxLayout()
        max_layout.addWidget(QLabel("每群最大邀请:"))
        self.max_invites = QSpinBox()
        self.max_invites.setRange(1, 200)
        self.max_invites.setValue(50)
        max_layout.addWidget(self.max_invites)
        max_layout.addWidget(QLabel("人"))
        max_layout.addStretch()
        strategy_layout.addLayout(max_layout)
        
        # 失败重试
        retry_layout = QHBoxLayout()
        retry_layout.addWidget(QLabel("失败重试:"))
        self.retry_count = QSpinBox()
        self.retry_count.setRange(0, 5)
        self.retry_count.setValue(2)
        retry_layout.addWidget(self.retry_count)
        retry_layout.addWidget(QLabel("次"))
        retry_layout.addStretch()
        strategy_layout.addLayout(retry_layout)
        
        # 邀请方式选择
        invite_method_layout = QHBoxLayout()
        invite_method_layout.addWidget(QLabel("邀请方式:"))
        
        self.method_group = QButtonGroup(self)
        self.direct_invite = QRadioButton("直接邀请")
        self.link_invite = QRadioButton("邀请链接")
        self.direct_invite.setChecked(True)
        
        self.method_group.addButton(self.direct_invite, 1)
        self.method_group.addButton(self.link_invite, 2)
        
        invite_method_layout.addWidget(self.direct_invite)
        invite_method_layout.addWidget(self.link_invite)
        invite_method_layout.addStretch()
        strategy_layout.addLayout(invite_method_layout)
        
        # 完成通知
        notification_layout = QHBoxLayout()
        notification_layout.addWidget(QLabel("完成通知:"))
        self.notification_switch = QCheckBox()
        self.notification_switch.setChecked(True)
        notification_layout.addWidget(self.notification_switch)
        notification_layout.addStretch()
        strategy_layout.addLayout(notification_layout)
        
        invite_settings_layout.addLayout(strategy_layout)
        
        # 操作按钮
        actions_layout = QHBoxLayout()
        actions_layout.addStretch()
        
        self.invite_preview_btn = QPushButton("预览")
        actions_layout.addWidget(self.invite_preview_btn)
        
        self.start_invite_btn = QPushButton("开始邀请")
        self.start_invite_btn.setObjectName("PrimaryButton")
        actions_layout.addWidget(self.start_invite_btn)
        
        invite_settings_layout.addLayout(actions_layout)
        
        right_layout.addWidget(invite_settings_card)
        
        # 邀请任务统计区域
        stats_card = QFrame()
        stats_card.setObjectName("Card")
        stats_layout = QVBoxLayout(stats_card)
        
        stats_subtitle = QLabel("邀请任务统计")
        stats_subtitle.setObjectName("SubtitleLabel")
        stats_layout.addWidget(stats_subtitle)
        
        # 统计卡片区域
        stats_cards_layout = QHBoxLayout()
        
        # 用户统计卡片
        stats_cards_layout.addWidget(self._create_stats_card("待邀请用户", "0"))
        stats_cards_layout.addWidget(self._create_stats_card("已邀请成功", "0", "#19be6b"))
        stats_cards_layout.addWidget(self._create_stats_card("邀请失败", "0", "#ed4014"))
        stats_cards_layout.addWidget(self._create_stats_card("等待中", "0", "#ff9900"))
        
        stats_layout.addLayout(stats_cards_layout)
        
        # 最近任务历史
        history_label = QLabel("最近邀请历史")
        history_label.setObjectName("StrongBodyLabel")
        stats_layout.addWidget(history_label)
        
        self.history_table = QTableWidget()
        self.history_table.setColumnCount(5)
        self.history_table.setHorizontalHeaderLabels(["用户", "群组", "状态", "时间", "操作"])
        self.history_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.history_table.horizontalHeader().setSectionResizeMode(4, QHeaderView.ResizeToContents)
        self.history_table.setEditTriggers(QAbstractItemView.NoEditTriggers)
        self.history_table.setFixedHeight(180)
        stats_layout.addWidget(self.history_table)
        
        right_layout.addWidget(stats_card)
        
        # 将左右面板添加到工作区域
        work_area.addWidget(left_panel, 1)
        work_area.addWidget(right_panel, 1)
        main_layout.addLayout(work_area)
    
    def _create_stats_card(self, title: str, value: str, color: str = "#409eff") -> QWidget:
        """创建统计信息卡片"""
        card = QFrame()
        card.setObjectName("StatsCard")
        
        layout = QVBoxLayout(card)
        layout.setContentsMargins(15, 10, 15, 10)
        
        value_label = QLabel(value)
        value_label.setObjectName("StatValue")
        value_label.setAlignment(Qt.AlignCenter)
        value_label.setStyleSheet(f"color: {color}; font-size: 24px; font-weight: bold;")
        
        title_label = QLabel(title)
        title_label.setObjectName("StatTitle")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("color: #666; font-size: 14px;")
        
        layout.addWidget(value_label)
        layout.addWidget(title_label)
        
        return card


if __name__ == "__main__":
    app = QApplication(sys.argv)
    window = InviteUserUI()
    window.resize(1200, 800)
    window.show()
    sys.exit(app.exec())