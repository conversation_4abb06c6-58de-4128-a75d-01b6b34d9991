from PySide6.QtCore import Qt, QSize, Q<PERSON><PERSON>tyAnimation, QPoint, QEasingCurve
from PySide6.QtWidgets import QWidget, QVBoxLayout, QHBoxLayout, QFrame, QLabel
from qfluentwidgets import (ScrollArea, PushButton, TableWidget, TextEdit, 
                          BodyLabel, CardWidget, FlowLayout,
                          StrongBodyLabel, TextBrowser,
                          ToolButton, TitleLabel, CaptionLabel, SearchLineEdit,
                          FluentIcon, ComboBox, PrimaryPushButton,
                          TransparentToolButton)
from qfluentwidgets import FluentIcon as FIF
from datetime import datetime


class TaskItemWidget(QFrame):
    """单个监控任务项组件"""
    
    def __init__(self, task_id: str, task_name: str, is_running: bool = False, status_text: str = "已停止", parent=None):
        super().__init__(parent=parent)
        self.task_id = task_id
        self.task_name = task_name
        self.is_running = is_running
        self.status_text = status_text
        
        self.setup_ui()
        
    def setup_ui(self):
        # 设置基本样式和属性
        self.setObjectName(f"task_item_{self.task_id}")
        self.setProperty("class", "TaskItem")
        self.setFixedHeight(80)
        self.setCursor(Qt.PointingHandCursor)
        
        # 主布局
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(10, 5, 10, 5)
        main_layout.setSpacing(5)
        
        # 顶部布局 - 任务名称
        top_layout = QHBoxLayout()
        self.name_label = StrongBodyLabel(self.task_name, self)
        self.name_label.setProperty("class", "TaskLabel")
        top_layout.addWidget(self.name_label, 1)
        
        # 右侧布局 - 垂直排列的按钮
        buttons_layout = QVBoxLayout()
        buttons_layout.setSpacing(1)
        buttons_layout.setAlignment(Qt.AlignCenter)
        #buttons_layout.setContentsMargins(5, 0, 5, 0)  # 增加左右间距
        
        # 编辑按钮
        self.edit_button = ToolButton(self)
        self.edit_button.setObjectName(f"edit_button_{self.task_id}")
        self.edit_button.setIcon(FIF.EDIT.icon())
        self.edit_button.setToolTip("编辑任务")
        #self.edit_button.setFixedWidth(36)  # 设置固定宽度
        #self.edit_button.setFixedHeight(32)  # 增加高度
        
        # 启动/停止按钮
        self.start_stop_button = ToolButton(self)
        self.start_stop_button.setObjectName(f"start_stop_button_{self.task_id}")
        self.start_stop_button.setToolTip("启动/停止任务")
        #self.start_stop_button.setFixedWidth(36)  # 设置固定宽度
        #self.start_stop_button.setFixedHeight(32)  # 增加高度
        self.update_start_stop_button(self.is_running)
        
        # 添加按钮到垂直布局
        buttons_layout.addWidget(self.edit_button)
        buttons_layout.addWidget(self.start_stop_button)
        
        # 底部布局 - 状态文本
        bottom_layout = QHBoxLayout()
        
        # 状态标签
        self.status_label = QLabel(self.status_text, self)
        self.status_label.setObjectName(f"status_label_{self.task_id}")
        self.update_status(self.is_running, self.status_text)
        
        # 添加组件到底部布局
        bottom_layout.addWidget(self.status_label, 1)  # 状态标签占据剩余空间
        
        # 结合布局
        content_layout = QHBoxLayout()
        
        # 创建左侧布局包含任务名称和状态
        left_layout = QVBoxLayout()
        left_layout.addLayout(top_layout)
        left_layout.addLayout(bottom_layout)
        
        # 将左侧和按钮布局添加到内容布局
        content_layout.addLayout(left_layout, 1)  # 左侧占据主要空间
        content_layout.addLayout(buttons_layout, 0)  # 按钮不拉伸
        
        # 添加内容布局到主布局
        main_layout.addLayout(content_layout)
        
    def update_status(self, is_running: bool, status_text: str = None):
        """更新任务状态"""
        self.is_running = is_running
        if status_text:
            self.status_text = status_text
            self.status_label.setText(self.status_text)
        
        if is_running:
            self.status_label.setStyleSheet("color: green;")
        else:
            # Check for errors in status_text to color red
            if "失败" in self.status_text or "错误" in self.status_text:
                self.status_label.setStyleSheet("color: red;")
            else:
                self.status_label.setStyleSheet("color: orange;")
                
    def update_start_stop_button(self, is_running: bool):
        """更新启动/停止按钮状态"""
        self.is_running = is_running
        
        if is_running:
            self.start_stop_button.setIcon(FIF.PAUSE.icon())
            self.start_stop_button.setToolTip("停止任务")
        else:
            self.start_stop_button.setIcon(FIF.PLAY.icon())
            self.start_stop_button.setToolTip("启动任务")
    
    def set_selected(self, selected: bool):
        """设置是否被选中"""
        if selected:
            self.setProperty("class", "SelectedTask")
            self.setStyleSheet("QFrame.SelectedTask { background-color: #E3F2FD; border-radius: 8px; }")
        else:
            self.setProperty("class", "TaskItem")
            self.setStyleSheet("QFrame.TaskItem { background-color: white; border-radius: 8px; }")


class StatsCard(CardWidget):
    """统计信息卡片，带有悬停效果"""
    
    def __init__(self, title, value="0",color="#409eff", parent=None):
        super().__init__(parent=parent)
        self.color = color
        self.setup_ui(title, value)
        self.animation = QPropertyAnimation(self, b"pos")
        self.animation.setEasingCurve(QEasingCurve.Type.OutCubic)
        self.animation.setDuration(200)
        self.hover_offset = 5  # 悬停时上移的像素数
        
    def setup_ui(self, title, value):
        self.layout = QVBoxLayout(self)
        self.layout.setContentsMargins(16, 16, 16, 16)
        self.layout.setSpacing(8)
        self.layout.setAlignment(Qt.AlignCenter)
        
        # 值标签
        self.valueLabel = TitleLabel(value, self)
        self.valueLabel.setAlignment(Qt.AlignCenter)
        
        # 标题标签
        self.titleLabel = CaptionLabel(title, self)
        self.titleLabel.setAlignment(Qt.AlignCenter)
        
        self.layout.addWidget(self.valueLabel)
        self.layout.addWidget(self.titleLabel)
        
        # 设置固定大小
        self.setFixedSize(180, 100)
        
        # 设置样式
        self.setStyleSheet("""
            StatsCard {
                background-color: rgb(251, 251, 251);
                border-radius: 10px;
                border: 1px solid rgba(0, 0, 0, 0.1);
            }
        """)
        
    def setValue(self, value, has_data=False,color=None):
        """设置值"""
        self.valueLabel.setText(str(value))
        if has_data:
            self.valueLabel.setStyleSheet(f"color: {color if  color else self.color}; font-weight: bold;")
        else:
            self.valueLabel.setStyleSheet("")
            
    def enterEvent(self, event):
        """鼠标进入事件"""
        # 保存当前位置
        current_pos = self.pos()
        # 创建新位置
        new_pos = current_pos - QPoint(0, self.hover_offset)
        # 设置动画
        self.animation.setStartValue(current_pos)
        self.animation.setEndValue(new_pos)
        self.animation.start()
        # 添加阴影效果
        self.setStyleSheet("""
            StatsCard {
                background-color: rgb(251, 251, 251);
                border-radius: 10px;
                border: 1px solid rgba(0, 0, 0, 0.1);
            }
        """)
        super().enterEvent(event)
        
    def leaveEvent(self, event):
        """鼠标离开事件"""
        # 恢复原位置
        current_pos = self.pos()
        # 创建新位置
        new_pos = current_pos + QPoint(0, self.hover_offset)
        # 设置动画
        self.animation.setStartValue(current_pos)
        self.animation.setEndValue(new_pos)
        self.animation.start()
        # 移除阴影效果
        self.setStyleSheet("""
            StatsCard {
                background-color: rgb(251, 251, 251);
                border-radius: 10px;
            }
        """)
        super().leaveEvent(event)


class TelegramMonitorPage(QWidget):
    """Telegram消息监控页面"""
    
    def __init__(self, parent=None):
        super().__init__(parent=parent)
        self.selected_task = None  # 当前选中的任务ID
        self.task_widgets = {}  # 存储任务小部件 {任务ID: widget}
        self.setup_ui()
        self.init_widget()

    def setup_ui(self):
        # 主布局
        self.hBoxLayout = QHBoxLayout(self)
        
        # 左侧任务列表区域
        self.taskListCard = CardWidget(self)
        self.taskListLayout = QVBoxLayout(self.taskListCard)
        
        # 添加左侧标题
        self.taskListTitle = TitleLabel("监控任务列表", self)
        self.taskListTitle.setAlignment(Qt.AlignCenter)
        
        # 添加搜索框
        self.searchBox = SearchLineEdit(self)
        self.searchBox.setPlaceholderText("搜索任务...")
        
        # 任务列表滚动区域
        self.scrollArea = ScrollArea()
        self.scrollWidget = QWidget()
        self.flowLayout = QVBoxLayout(self.scrollWidget)  # 改为垂直布局确保每行一个任务
        self.flowLayout.setSpacing(10)  # 增加任务间距
        self.flowLayout.setAlignment(Qt.AlignTop)  # 设置为顶部对齐，确保任务始终在顶部
        
        # 任务操作按钮
        self.addTaskButton = PushButton('添加任务', self, FIF.ADD)
        self.deleteTaskButton = PushButton('删除任务', self, FIF.DELETE)
        self.buttonLayout = QHBoxLayout()
        
        # 右侧内容区域
        self.rightLayout = QVBoxLayout()
        
        # 统计信息卡片区域
        self.statsLayout = QHBoxLayout()
        
        # 创建四个统计卡片
        self.totalUsersCard = StatsCard("总采集用户", "0", self)
        self.todayUsersCard = StatsCard("今日采集", "0", self)
        self.avgDailyCard = StatsCard("日均采集", "0", self)
        self.runningDaysCard = StatsCard("运行天数", "0", self)
        
        # 右侧详情区域
        self.detailCard = CardWidget(self)
        self.detailLayout = QVBoxLayout(self.detailCard)
        # 添加全部按钮
        self.allUsersButton = PushButton("显示全部", self, FIF.PEOPLE)
        self.allUsersButton.setToolTip("显示全部用户信息")

        # 搜索和导出区域
        self.searchExportLayout = QHBoxLayout()

        
        # 表格搜索框
        self.tableSearchBox = SearchLineEdit(self)
        self.tableSearchBox.setPlaceholderText("搜索用户...")
        
        # 导出按钮
        self.exportButton = PrimaryPushButton('导出数据', self, FIF.SAVE)
        self.exportButton.setToolTip("导出用户到表格")
        # 表格标题
        self.tableTitle = StrongBodyLabel("用户数据列表", self)
        
        # 表格
        self.tableWidget = TableWidget(self)
        
        # 分页区域
        self.paginationLayout = QHBoxLayout()
        
        # 页码显示
        self.pageInfoLabel = BodyLabel("第 1 页 / 共 1 页", self)
        
        # 页码选择
        self.pageComboBox = ComboBox(self)
        self.pageComboBox.setMinimumWidth(80)
        
        # 上一页/下一页按钮
        self.prevPageButton = TransparentToolButton(FIF.LEFT_ARROW, self)
        self.nextPageButton = TransparentToolButton(FIF.RIGHT_ARROW, self)
        
        # 每页显示数量
        self.pageSizeLabel = BodyLabel("每页显示：", self)
        self.pageSizeComboBox = ComboBox(self)
        self.pageSizeComboBox.addItems(["10", "20", "50", "100"])
        self.pageSizeComboBox.setCurrentIndex(0)
        self.pageSizeComboBox.setMinimumWidth(80)
        
        # 日志框
        self.logEdit = TextBrowser(self)
        
    def init_widget(self):
        # 设置主布局
        self.hBoxLayout.setContentsMargins(36, 36, 36, 36)
        self.hBoxLayout.setSpacing(24)
        
        # 配置左侧任务列表
        self.taskListCard.setFixedWidth(280)  # 固定宽度
        self.taskListLayout.addWidget(self.taskListTitle)
        self.taskListLayout.addWidget(self.searchBox)
        self.scrollArea.setWidget(self.scrollWidget)
        self.scrollArea.setWidgetResizable(True)
        self.scrollArea.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)  # 确保滚动条按需显示
        self.taskListLayout.addWidget(self.scrollArea, 1)  # 添加拉伸因子，使滚动区域占据主要空间
        
        # 配置底部按钮
        self.buttonLayout.addWidget(self.addTaskButton)
        self.buttonLayout.addWidget(self.deleteTaskButton)
        self.taskListLayout.addLayout(self.buttonLayout, 0)  # 底部按钮不拉伸
        
        # 添加左侧卡片到主布局
        self.hBoxLayout.addWidget(self.taskListCard)
        
        # 配置右侧布局
        self.hBoxLayout.addLayout(self.rightLayout)
        
        # 添加统计卡片到统计布局
        self.statsLayout.addWidget(self.totalUsersCard)
        self.statsLayout.addWidget(self.todayUsersCard)
        self.statsLayout.addWidget(self.avgDailyCard)
        self.statsLayout.addWidget(self.runningDaysCard)
        self.statsLayout.setSpacing(20)
        self.rightLayout.addLayout(self.statsLayout)
        
        # 添加搜索和导出按钮
        self.searchExportLayout.addWidget(self.tableSearchBox)
        self.searchExportLayout.addSpacing(20)
        self.searchExportLayout.addWidget(self.allUsersButton)
        self.searchExportLayout.addSpacing(20)
        self.searchExportLayout.addWidget(self.exportButton)
        
        # 添加表格标题和搜索导出区
        self.detailLayout.addWidget(self.tableTitle)
        self.detailLayout.addLayout(self.searchExportLayout)
        
        # 配置右侧表格
        self.tableWidget.setColumnCount(6)
        self.tableWidget.setHorizontalHeaderLabels(['UID', '用户名', '昵称', '关键词', '任务类型', '入库日期'])
        self.tableWidget.horizontalHeader().setStretchLastSection(True)
        self.detailLayout.addWidget(self.tableWidget)
        
        # 配置分页区域
        self.paginationLayout.addWidget(self.prevPageButton)
        self.paginationLayout.addWidget(self.pageInfoLabel)
        self.paginationLayout.addWidget(self.pageComboBox)
        self.paginationLayout.addStretch(1)
        self.paginationLayout.addWidget(self.pageSizeLabel)
        self.paginationLayout.addWidget(self.pageSizeComboBox)
        self.paginationLayout.addWidget(self.nextPageButton)
        
        # 添加分页区域
        self.detailLayout.addLayout(self.paginationLayout)
        
        # 配置日志框
        self.logEdit.setPlaceholderText('日志信息...')
        self.logEdit.setMaximumHeight(200)
        self.detailLayout.addWidget(self.logEdit)
        
        # 添加详情卡片到右侧布局
        self.rightLayout.addWidget(self.detailCard)
        
        # 设置右侧布局比例
        self.rightLayout.setStretch(0, 0)  # 统计卡片布局
        self.rightLayout.setStretch(1, 4)  # 详情卡片
        
        # 设置删除按钮初始禁用状态
        self.deleteTaskButton.setEnabled(False)
        
        # 禁用分页按钮（初始状态）
        self.prevPageButton.setEnabled(False)
        self.nextPageButton.setEnabled(False)
        
        # 设置样式
        self.setStyleSheet("""
            QScrollArea {
                border: none;
                background-color: transparent;
            }
            CardWidget {
                background-color: rgb(251, 251, 251);
                border-radius: 10px;
            }
            TextEdit {
                border: 1px solid rgb(200, 200, 200);
                border-radius: 5px;
                padding: 5px;
            }
            QFrame.TaskItem {
                background-color: white;
                border-radius: 8px;
                padding: 10px;
            }
            QFrame.SelectedTask {
                background-color: #E3F2FD;
                border-radius: 8px;
            }
        """)

    def add_task_item(self, task_name, task_id=None):
        """添加任务项到列表 - 纯UI组件创建"""
        if not task_id:
            task_id = f"task_{len(self.task_widgets) + 1}"
            
        taskWidget = QFrame()
        taskWidget.setObjectName("TaskItem")
        taskWidget.setProperty("class", "TaskItem")  # 设置CSS类名
        taskWidget.setFixedHeight(80)  # 设置固定高度，不会因删除其他任务而拉伸
        
        layout = QVBoxLayout(taskWidget)
        
        # 创建任务标签
        label = StrongBodyLabel(task_name, self)
        label.setProperty("class", "TaskLabel")
        
        # 创建修改按钮
        editBtn = ToolButton(FIF.EDIT, self)
        editBtn.setFixedSize(QSize(24, 24))
        editBtn.setCursor(Qt.PointingHandCursor)
        editBtn.setToolTip("修改任务")
        editBtn.setVisible(False)  # 初始隐藏修改按钮
        
        # 创建删除按钮
        deleteBtn = ToolButton(FIF.DELETE, self)
        deleteBtn.setFixedSize(QSize(24, 24))
        deleteBtn.setCursor(Qt.PointingHandCursor)
        deleteBtn.setToolTip("删除任务")
        deleteBtn.setVisible(False)  # 初始隐藏删除按钮
        
        # 按钮布局
        buttonLayout = QVBoxLayout()
        buttonLayout.setSpacing(4)  # 设置按钮之间的间距
        buttonLayout.setContentsMargins(0, 0, 0, 0)
        buttonLayout.addWidget(editBtn)
        buttonLayout.addWidget(deleteBtn)
        
        # 添加到布局
        layout.addWidget(label, 1)  # 1表示可伸展
        layout.addLayout(buttonLayout, 0)  # 0表示不可伸展
        layout.setContentsMargins(12, 8, 12, 8)
        
        # 存储任务ID
        taskWidget.setProperty("task_id", task_id)
        taskWidget.setProperty("task_name", task_name)
        
        # 存储任务小部件
        self.task_widgets[task_id] = taskWidget
        
        self.flowLayout.addWidget(taskWidget)
        
        return taskWidget
    

if __name__ == '__main__':
    from PySide6.QtWidgets import QApplication
    app = QApplication([])
    window = TelegramMonitorPage()
    window.show()
    app.exec()
