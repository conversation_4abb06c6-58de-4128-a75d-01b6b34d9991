import sys
from typing import Optional, Set

from PySide6.QtWidgets import (
    QMainWindow, QApplication, QWidget, QVBoxLayout, QHBoxLayout,
    QLabel, QLineEdit, QGroupBox, QCheckBox, QHeaderView, QAbstractItemView,
    QTableWidget, QTableWidgetItem, QSplitter, QProgressBar
)
from PySide6.QtCore import Qt, QSize, Signal, Slot, QTimer, QMargins
from PySide6.QtGui import QColor, QFont

from qfluentwidgets import (
    FluentIcon, InfoBar, InfoBarPosition, ComboBox, PushButton, 
    RadioButton, SpinBox, TextEdit, LineEdit, ToggleButton,
    TitleLabel, BodyLabel, CardWidget, PrimaryPushButton, IconWidget,
    StrongBodyLabel, CheckBox, TableWidget, ProgressBar, PlainTextEdit
)


class ProxyManagerUI(QWidget):
    """代理管理界面UI组件类
    
    职责：
    1. 创建并布局UI组件
    2. 提供UI状态更新方法
    3. 不处理业务逻辑或信号连接
    """
    
    def __init__(self, parent=None):
        super().__init__(parent)
        
        # 设置对象名称和样式
        self.setObjectName("ProxyManagerUI")
        self.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 1px solid #d0d0d0;
                border-radius: 6px;
                margin-top: 12px;
                padding-top: 10px;
            }
            
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px;
            }
        """)
        
        # 创建所有UI组件
        self._setup_ui()
        
    def _setup_ui(self):
        """创建并设置UI组件"""
        # 创建主布局
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(16, 16, 16, 16)
        main_layout.setSpacing(12)
        
        # 创建上下分割区域
        splitter = QSplitter(Qt.Vertical)
        splitter.setChildrenCollapsible(False)
        
        # 创建上部控制区域容器
        top_container = QWidget()
        top_layout = QVBoxLayout(top_container)
        top_layout.setContentsMargins(0, 0, 0, 0)
        top_layout.setSpacing(12)
        
        # 创建服务状态区域
        service_card = self._create_service_card()
        top_layout.addWidget(service_card)
        
        # 创建添加代理区域
        add_proxy_card = self._create_add_proxy_card()
        top_layout.addWidget(add_proxy_card)
        
        # 创建下部代理列表区域
        list_card = self._create_proxy_list_card()
        
        # 添加到分割器
        splitter.addWidget(top_container)
        splitter.addWidget(list_card)
        splitter.setSizes([300, 500])  # 设置初始比例
        
        # 添加到主布局
        main_layout.addWidget(splitter)
        
    def _create_service_card(self) -> CardWidget:
        """创建服务状态卡片
        
        Returns:
            CardWidget: 服务状态卡片
        """
        card = CardWidget()
        layout = QVBoxLayout(card)
        
        # 标题
        title_layout = QHBoxLayout()
        icon = IconWidget(FluentIcon.SETTING)
        title = TitleLabel("代理服务控制")
        title_layout.addWidget(icon)
        title_layout.addWidget(title)
        title_layout.addStretch()
        layout.addLayout(title_layout)
        
        # 状态显示区域
        status_layout = QHBoxLayout()
        self.status_label = StrongBodyLabel("当前状态: 已停止")
        self.status_label.setStyleSheet("color: #757575; font-size: 14px;")
        status_layout.addWidget(self.status_label)
        status_layout.addStretch()
        
        # 切换服务按钮
        buttons_layout = QHBoxLayout()
        self.toggle_proxy_btn = PrimaryPushButton("启动代理")
        self.toggle_proxy_btn.setIcon(FluentIcon.POWER_BUTTON)
        
        # 重启按钮
        self.restart_btn = PushButton("重启服务")
        self.restart_btn.setIcon(FluentIcon.SYNC)
        
        # 添加按钮到布局
        buttons_layout.addWidget(self.toggle_proxy_btn)
        buttons_layout.addWidget(self.restart_btn)
        buttons_layout.addStretch()
        
        # 添加到主布局
        layout.addLayout(status_layout)
        layout.addLayout(buttons_layout)
        
        return card
    
    def _create_add_proxy_card(self) -> CardWidget:
        """创建添加代理卡片
        
        Returns:
            CardWidget: 添加代理卡片
        """
        card = CardWidget()
        layout = QVBoxLayout(card)
        
        # 标题
        title_layout = QHBoxLayout()
        icon = IconWidget(FluentIcon.ADD)
        title = TitleLabel("添加代理")
        title_layout.addWidget(icon)
        title_layout.addWidget(title)
        title_layout.addStretch()
        layout.addLayout(title_layout)
        
        # 创建单个水平布局，放置所有四个GroupBox
        all_settings_layout = QHBoxLayout()
        
        # 代理类型选择 - 卡片式设计
        type_group = QGroupBox("代理类型")
        type_layout = QVBoxLayout(type_group)
        type_layout.setSpacing(8)
        
        self.ip_type_combo = ComboBox()
        self.ip_type_combo.addItem("本机公网IP")
        self.ip_type_combo.addItem("外部SOCKS5")
        self.ip_type_combo.setCurrentIndex(0)
        self.ip_type_combo.setMinimumWidth(120)
        type_layout.addWidget(self.ip_type_combo)
        
        # 设置宽度
        type_group.setMinimumWidth(150)
        all_settings_layout.addWidget(type_group)
        
        # IP输入框 - 卡片式设计
        ip_group = QGroupBox("IP地址/范围")
        ip_layout = QVBoxLayout(ip_group)
        ip_layout.setSpacing(8)
        
        self.ip_edit = PlainTextEdit()
        self.ip_edit.setPlaceholderText("请输入IP地址或IP范围\n例如: *********** 或 ***********-************")
        self.ip_edit.setFixedHeight(60)  # 减小高度
        self.ip_edit.setStyleSheet("QTextEdit { border: 1px solid #d0d0d0; border-radius: 5px; }")
        ip_layout.addWidget(self.ip_edit)
        
        # 设置IP组的最小宽度
        ip_group.setMinimumWidth(200)
        all_settings_layout.addWidget(ip_group, 1)  # IP地址框可以伸展
        
        # 端口设置
        port_group = QGroupBox("端口设置")
        port_layout = QVBoxLayout(port_group)
        port_layout.setSpacing(8)  # 减少上下间距
        
        # 单选按钮放在同一行，靠左显示
        radio_layout = QHBoxLayout()
        self.fixed_port_radio = RadioButton("固定端口")
        self.fixed_port_radio.setChecked(True)
        self.random_port_radio = RadioButton("随机端口范围")
        radio_layout.addWidget(self.fixed_port_radio)
        radio_layout.addSpacing(15)  # 按钮之间只保留少量间距
        radio_layout.addWidget(self.random_port_radio)
        radio_layout.addStretch()  # 末尾添加stretch使整体靠左
        port_layout.addLayout(radio_layout)
        
        # 创建控件区域，减少左侧缩进让组件更紧凑
        controls_layout = QVBoxLayout()
        controls_layout.setSpacing(10)  # 控制上下间距
        
        # 固定端口输入框
        self.fixed_port_container = QWidget()
        fixed_port_layout = QHBoxLayout(self.fixed_port_container)
        fixed_port_layout.setContentsMargins(10, 5, 5, 5)  # 减少左侧缩进
        fixed_port_layout.addWidget(BodyLabel("端口:"))
        self.port_spin = SpinBox()
        self.port_spin.setRange(1024, 65535)
        self.port_spin.setValue(10000)
        fixed_port_layout.addWidget(self.port_spin)
        fixed_port_layout.addStretch()
        
        # 随机端口范围输入框
        self.random_port_container = QWidget()
        random_port_layout = QHBoxLayout(self.random_port_container)
        random_port_layout.setContentsMargins(10, 5, 5, 5)  # 减少左侧缩进
        random_port_layout.addWidget(BodyLabel("范围:"))
        self.min_port_spin = SpinBox()
        self.min_port_spin.setRange(1024, 65535)
        self.min_port_spin.setValue(10000)
        random_port_layout.addWidget(self.min_port_spin)
        random_port_layout.addWidget(BodyLabel("-"))
        self.max_port_spin = SpinBox()
        self.max_port_spin.setRange(1024, 65535)
        self.max_port_spin.setValue(20000)
        random_port_layout.addWidget(self.max_port_spin)
        random_port_layout.addStretch()
        
        # 添加输入区域
        controls_layout.addWidget(self.fixed_port_container)
        controls_layout.addWidget(self.random_port_container)
        port_layout.addLayout(controls_layout)
        
        # 设置最小宽度
        port_group.setMinimumWidth(220)
        all_settings_layout.addWidget(port_group)
        
        # 用户设置（原凭据设置）
        self.credentials_group = QGroupBox("用户设置")
        credentials_layout = QVBoxLayout(self.credentials_group)
        
        # 用户名
        username_layout = QHBoxLayout()
        username_layout.addWidget(BodyLabel("用户名:"))
        self.username_edit = LineEdit()
        self.username_edit.setPlaceholderText("可选，留空为没有用户名")
        username_layout.addWidget(self.username_edit)
        
        # 密码
        password_layout = QHBoxLayout()
        password_layout.addWidget(BodyLabel("密码:"))
        self.password_edit = LineEdit()
        self.password_edit.setPlaceholderText("可选，留空为没有用户密码")
        password_layout.addWidget(self.password_edit)
        
        credentials_layout.addLayout(username_layout)
        credentials_layout.addLayout(password_layout)
        
        # 设置最小宽度
        self.credentials_group.setMinimumWidth(200)
        all_settings_layout.addWidget(self.credentials_group)
        
        # 默认隐藏用户设置
        self.credentials_group.setVisible(False)
        
        # 添加按钮
        btn_layout = QHBoxLayout()
        self.add_proxy_btn = PrimaryPushButton("添加代理")
        self.add_proxy_btn.setIcon(FluentIcon.ADD)
        btn_layout.addWidget(self.add_proxy_btn)
        btn_layout.addStretch()
        
        # 组合布局
        layout.addLayout(all_settings_layout)
        layout.addLayout(btn_layout)
        
        # 设置初始端口可见性
        self.update_port_widgets_visibility(True)
        
        return card
    
    def _create_proxy_list_card(self) -> CardWidget:
        """创建代理列表卡片
        
        Returns:
            CardWidget: 代理列表卡片
        """
        card = CardWidget()
        layout = QVBoxLayout(card)
        
        # 标题与工具栏
        title_layout = QHBoxLayout()
        
        # 标题
        icon = IconWidget(FluentIcon.GLOBE)
        title = TitleLabel("代理列表")
        self.proxy_count_label = BodyLabel("总代理：0")
        title_layout.addWidget(icon)
        title_layout.addWidget(title)
        
        title_layout.addStretch()
        title_layout.addWidget(self.proxy_count_label)
        # 工具按钮
        self.validate_btn = PushButton("验证选中")
        self.validate_btn.setIcon(FluentIcon.ACCEPT)
        
        self.validate_all_btn = PushButton("验证全部")
        self.validate_all_btn.setIcon(FluentIcon.SYNC)
        
        self.export_btn = PushButton("导出代理")
        
        self.delete_btn = PushButton("删除选中")
        self.delete_btn.setIcon(FluentIcon.DELETE)
        self.invalid_btn = PushButton("删除失效")
        self.invalid_btn.setIcon(FluentIcon.DELETE)
        self.delete_all_btn = PushButton("删除全部")
        self.delete_all_btn.setIcon(FluentIcon.DELETE)
        # 添加按钮到布局
        title_layout.addWidget(self.validate_btn)
        title_layout.addWidget(self.validate_all_btn)
        title_layout.addWidget(self.export_btn)
        title_layout.addWidget(self.invalid_btn)
        title_layout.addWidget(self.delete_btn)
        title_layout.addWidget(self.delete_all_btn)
        
        # 添加验证进度条
        progress_layout = QHBoxLayout()
        self.validation_progress_label = QLabel("验证进度:")
        self.validation_progress_label.setVisible(False)
        self.validation_progress_bar = ProgressBar()
        self.validation_progress_bar.setRange(0, 100)
        self.validation_progress_bar.setValue(0)
        self.validation_progress_bar.setVisible(False)
        progress_layout.addWidget(self.validation_progress_label)
        progress_layout.addWidget(self.validation_progress_bar)
        progress_layout.addStretch()
        
        # 创建表格
        self.proxy_table = TableWidget()
        self.proxy_table.setEditTriggers(QAbstractItemView.NoEditTriggers)  # 不可编辑
        
        # 添加分页控制
        pagination_layout = QHBoxLayout()
        pagination_layout.setContentsMargins(0, 8, 0, 0)
        
        # 左侧：每页显示数量
        page_size_layout = QHBoxLayout()
        page_size_layout.addWidget(BodyLabel("每页显示:"))
        self.page_size_combo = ComboBox()
        self.page_size_combo.addItems(["10", "20", "50", "100"])
        self.page_size_combo.setCurrentIndex(1)  # 默认选择20
        self.page_size_combo.setFixedWidth(80)
        page_size_layout.addWidget(self.page_size_combo)
        pagination_layout.addLayout(page_size_layout)
        
        pagination_layout.addStretch()
        
        # 右侧：页码控制
        self.prev_page_btn = PushButton("上一页")
        self.prev_page_btn.setIcon(FluentIcon.LEFT_ARROW)
        self.prev_page_btn.setFixedWidth(100)
        
        self.page_info_label = BodyLabel("第 1/1 页")
        
        self.next_page_btn = PushButton("下一页")
        self.next_page_btn.setIcon(FluentIcon.RIGHT_ARROW)
        self.next_page_btn.setFixedWidth(100)
        
        pagination_layout.addWidget(self.prev_page_btn)
        pagination_layout.addWidget(self.page_info_label)
        pagination_layout.addWidget(self.next_page_btn)
        
        # 添加到主布局
        layout.addLayout(title_layout)
        layout.addLayout(progress_layout)
        layout.addWidget(self.proxy_table)
        layout.addLayout(pagination_layout)
        
        return card
    
    def update_port_widgets_visibility(self, is_fixed: bool):
        """更新端口控件可见性
        
        Args:
            is_fixed: 是否为固定端口
        """
        self.fixed_port_container.setVisible(is_fixed)
        self.random_port_container.setVisible(not is_fixed)
    
    def create_checkbox(self) -> QWidget:
        """创建用于表格的复选框
        
        Returns:
            QWidget: 复选框容器
        """
        # 创建复选框容器
        container = QWidget()
        layout = QHBoxLayout(container)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setAlignment(Qt.AlignCenter)
        
        # 创建复选框
        checkbox = ToggleButton()
        checkbox.setFixedSize(20, 20)
        layout.addWidget(checkbox)
        
        return container
    
    def update_status_label(self, is_running: bool):
        """更新状态标签
        
        Args:
            is_running: 是否运行中
        """
        if is_running:
            self.status_label.setText("当前状态: 运行中")
            self.status_label.setStyleSheet("color: #2e7d32; font-size: 14px;")
            self.set_running_style(True)
        else:
            self.status_label.setText("当前状态: 已停止")
            self.status_label.setStyleSheet("color: #757575; font-size: 14px;")
            self.set_running_style(False)
    
    def set_running_style(self, is_running: bool):
        """设置运行状态的UI样式
        
        Args:
            is_running: 是否运行中
        """
        # 更新状态标签
   
        # 更新按钮样式和文本
        if is_running:
            self.toggle_proxy_btn.setText("停止代理")
            self.toggle_proxy_btn.setIcon(FluentIcon.STOP_WATCH)
        else:
            self.toggle_proxy_btn.setText("启动代理")
            self.toggle_proxy_btn.setIcon(FluentIcon.POWER_BUTTON)

    def show_validation_progress(self, show: bool = True):
        """显示或隐藏验证进度条
        
        Args:
            show: 是否显示进度条
        """
        self.validation_progress_label.setVisible(show)
        self.validation_progress_bar.setVisible(show)
        if not show:
            self.validation_progress_bar.setValue(0)
        
    def update_validation_progress(self, current: int, total: int):
        """更新验证进度
        
        Args:
            current: 当前进度
            total: 总数
        """
        if total <= 0:
            percentage = 0
        else:
            percentage = int(current * 100 / total)
        
        self.validation_progress_bar.setValue(percentage)
        self.validation_progress_label.setText(f"验证进度: {current}/{total}")
        # 确保进度条可见
        self.show_validation_progress(True)


# 测试运行
if __name__ == "__main__":
    app = QApplication(sys.argv)
    window = ProxyManagerUI()
    window.show()
    sys.exit(app.exec()) 