import sys
from typing import Op<PERSON>, List, Dict

from PySide6.QtWidgets import (
    QMainWindow, QApplication, QWidget, QVBoxLayout, QHBoxLayout,
    QLabel, QLineEdit, QGroupBox, QCheckBox, QHeaderView, QAbstractItemView,
    QTableWidget, QTableWidgetItem, QSplitter, QFrame, QDialog, QStackedWidget,
    QScrollArea, QButtonGroup, QFileDialog, QMessageBox
)
from PySide6.QtCore import Qt, QSize, Signal, Slot, QTimer, QMargins, QDateTime
from PySide6.QtGui import QColor, QFont, QIcon

from qfluentwidgets import (
    FluentIcon, InfoBar, InfoBarPosition, ComboBox, PushButton, 
    RadioButton, SpinBox, TextEdit, LineEdit, ToggleButton,
    TitleLabel, BodyLabel, CardWidget, PrimaryPushButton, IconWidget,
    StrongBodyLabel, CheckBox, TableWidget, SwitchButton, FlowLayout,
    SubtitleLabel, MessageBoxBase, StateToolTip, 
    DateTimeEdit, TimeEdit, SearchLineEdit
)


class SendMsgUI(QWidget):
    """消息群发任务管理界面UI组件类
    
    功能：
    1. 创建群发任务
    2. 查看每个任务的运行状态
    3. 启动/停止任务
    4. 修改任务
    5. 查看任务的群发数据
    """
    
    def __init__(self, parent=None):
        super().__init__(parent)
        
        # 设置对象名称和样式
        self.setObjectName("MessageTaskManagerUI")
        self.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 1px solid #d0d0d0;
                border-radius: 6px;
                margin-top: 12px;
                padding-top: 10px;
            }
            
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px;
            }
        """)
        
        # 创建所有UI组件
        self._setup_ui()
        
    def _setup_ui(self):
        """创建并设置UI组件"""
        # 创建主布局
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(16, 16, 16, 16)
        main_layout.setSpacing(12)
        
        # 标题和添加任务按钮
        header_layout = QHBoxLayout()
        title_label = TitleLabel("消息群发任务管理")
        header_layout.addWidget(title_label)
        header_layout.addStretch()
        
        # 添加任务按钮
        self.add_task_btn = PrimaryPushButton("创建新任务")
        self.add_task_btn.setIcon(FluentIcon.ADD)
        header_layout.addWidget(self.add_task_btn)
        
        main_layout.addLayout(header_layout)
        
        # 创建任务过滤和搜索区域
        filter_layout = QHBoxLayout()
        
        # 状态过滤
        filter_layout.addWidget(BodyLabel("状态:"))
        self.status_filter = ComboBox()
        self.status_filter.addItems(["全部", "运行中", "已暂停", "已完成", "已失败"])
        self.status_filter.setCurrentIndex(0)
        filter_layout.addWidget(self.status_filter)
        
        filter_layout.addSpacing(20)
        
        # 时间过滤
        filter_layout.addWidget(BodyLabel("时间:"))
        self.time_filter = ComboBox()
        self.time_filter.addItems(["全部时间", "今天", "昨天", "本周", "本月"])
        self.time_filter.setCurrentIndex(0)
        filter_layout.addWidget(self.time_filter)
        
        filter_layout.addSpacing(20)
        
        # 任务类型过滤
        filter_layout.addWidget(BodyLabel("类型:"))
        self.type_filter = ComboBox()
        self.type_filter.addItems(["全部类型", "文本消息", "图片消息", "视频消息", "文件消息", "混合消息"])
        self.type_filter.setCurrentIndex(0)
        filter_layout.addWidget(self.type_filter)
        
        filter_layout.addStretch()
        
        # 搜索框
        self.search_box = SearchLineEdit()
        self.search_box.setPlaceholderText("搜索任务名称或ID")
        self.search_box.setFixedWidth(240)
        filter_layout.addWidget(self.search_box)
        
        main_layout.addLayout(filter_layout)
        
        # 创建表格
        self.task_table = TableWidget(self)
        self.task_table.setColumnCount(8)
        self.task_table.setHorizontalHeaderLabels([
            "ID", "任务名称", "状态", "类型", "目标数(成/败/总)", "进度", "创建时间", "操作"
        ])
        
        # 设置表格属性
        self.task_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.task_table.horizontalHeader().setSectionResizeMode(0, QHeaderView.ResizeToContents)
        self.task_table.horizontalHeader().setSectionResizeMode(2, QHeaderView.ResizeToContents)
        self.task_table.horizontalHeader().setSectionResizeMode(7, QHeaderView.ResizeToContents)
        self.task_table.setEditTriggers(QAbstractItemView.NoEditTriggers)
        self.task_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        
        main_layout.addWidget(self.task_table)
        
        # 添加统计信息区域
        stats_frame = CardWidget()
        stats_layout = QHBoxLayout(stats_frame)
        
        # 总任务数
        total_tasks_card = self._create_stats_card("总任务数", "0")
        stats_layout.addWidget(total_tasks_card)
        
        # 运行中任务
        running_tasks_card = self._create_stats_card("运行中", "0", "#2d8cf0")
        stats_layout.addWidget(running_tasks_card)
        # 暂停任务
        paused_tasks_card = self._create_stats_card("已暂停", "0", "#ff9900")
        stats_layout.addWidget(paused_tasks_card)
        
        # 已完成任务
        completed_tasks_card = self._create_stats_card("总发送", "0", "#19be6b")
        stats_layout.addWidget(completed_tasks_card)
        
        # 已失败任务
        failed_tasks_card = self._create_stats_card("总失败", "0", "#ed4014")
        stats_layout.addWidget(failed_tasks_card)
        

        # 今日发送总量
        total_sent_card = self._create_stats_card("今日发送", "0", "#9b59b6")
        stats_layout.addWidget(total_sent_card)
        
        main_layout.addWidget(stats_frame)
        
    def _create_stats_card(self, title: str, value: str, color: str = "#409eff") -> QWidget:
        """创建统计信息卡片"""
        card = QFrame()
        card.setObjectName("StatsCard")
        card.setStyleSheet(f"""
            #StatsCard {{
                background-color: white;
                border-radius: 8px;
                border: 1px solid #e0e0e0;
            }}
            #StatValue {{
                color: {color};
                font-size: 24px;
                font-weight: bold;
            }}
            #StatTitle {{
                color: #666;
                font-size: 14px;
            }}
        """)
        
        layout = QVBoxLayout(card)
        layout.setContentsMargins(15, 10, 15, 10)
        
        value_label = QLabel(value)
        value_label.setObjectName("StatValue")
        value_label.setAlignment(Qt.AlignCenter)
        
        title_label = QLabel(title)
        title_label.setObjectName("StatTitle")
        title_label.setAlignment(Qt.AlignCenter)
        
        layout.addWidget(value_label)
        layout.addWidget(title_label)
        
        return card

    def _create_control_widget(self, row_index: int) -> QWidget:
        """创建操作按钮控件"""
        widget = QWidget()
        layout = QHBoxLayout(widget)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(5)
        
        # 查看按钮
        view_btn = PushButton("查看")
        view_btn.setProperty("row", row_index)
        view_btn.setIcon(FluentIcon.VIEW)
        
        # 启停按钮 - 根据状态动态设置
        toggle_btn = PushButton("启动")
        toggle_btn.setProperty("row", row_index)
        toggle_btn.setIcon(FluentIcon.PLAY)
        
        # 编辑按钮
        edit_btn = PushButton("编辑")
        edit_btn.setProperty("row", row_index)
        edit_btn.setIcon(FluentIcon.EDIT)
        
        # 删除按钮
        delete_btn = PushButton("删除")
        delete_btn.setProperty("row", row_index)
        delete_btn.setIcon(FluentIcon.DELETE)
        
        layout.addWidget(view_btn)
        layout.addWidget(toggle_btn)
        layout.addWidget(edit_btn)
        layout.addWidget(delete_btn)
        
        return widget
        
    def update_table_data(self, tasks: List[Dict]):
        """更新表格数据"""
        self.task_table.setRowCount(0)  # 清空表格
        
        for i, task in enumerate(tasks):
            row_position = self.task_table.rowCount()
            self.task_table.insertRow(row_position)
            
            # 填充单元格数据
            self.task_table.setItem(row_position, 0, QTableWidgetItem(str(task.get("id", ""))))
            self.task_table.setItem(row_position, 1, QTableWidgetItem(task.get("name", "")))
            
            # 状态单元格
            status = task.get("status", "")
            status_item = QTableWidgetItem(status)
            if status == "运行中":
                status_item.setForeground(QColor("#2d8cf0"))
            elif status == "已完成":
                status_item.setForeground(QColor("#19be6b"))
            elif status == "已失败":
                status_item.setForeground(QColor("#ed4014"))
            elif status == "已暂停":
                status_item.setForeground(QColor("#ff9900"))
            self.task_table.setItem(row_position, 2, status_item)
            
            # 其他单元格
            self.task_table.setItem(row_position, 3, QTableWidgetItem(task.get("type", "")))
            self.task_table.setItem(row_position, 4, QTableWidgetItem(str(task.get("target_count", 0))))
            self.task_table.setItem(row_position, 5, QTableWidgetItem(str(task.get("sent_count", 0))))
            self.task_table.setItem(row_position, 6, QTableWidgetItem(task.get("create_time", "")))
            
            # 添加操作按钮
            control_widget = self._create_control_widget(row_position)
            self.task_table.setCellWidget(row_position, 7, control_widget)
    
    def update_stats(self, stats: Dict):
        """更新统计信息"""
        # 找到所有的统计卡片
        for child in self.findChildren(QFrame, "StatsCard"):
            # 查找卡片内的值标签
            value_label = child.findChild(QLabel, "StatValue")
            if not value_label:
                continue
                
            # 根据标题更新值
            title_label = child.findChild(QLabel, "StatTitle")
            if not title_label:
                continue
                
            title_text = title_label.text()
            
            # 根据标题匹配相应的统计值
            if title_text == "总任务数":
                value_label.setText(str(stats.get("total", 0)))
            elif title_text == "运行中":
                value_label.setText(str(stats.get("running", 0)))
            elif title_text == "总发送":
                value_label.setText(str(stats.get("completed", 0)))
            elif title_text == "总失败":
                value_label.setText(str(stats.get("failed", 0)))
            elif title_text == "已暂停":
                value_label.setText(str(stats.get("paused", 0)))
            elif title_text == "今日发送":
                value_label.setText(str(stats.get("today_sent", 0)))


class CreateTaskDialog(QDialog):
    """创建新任务对话框"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("创建新任务")
        self.resize(600, 700)
        
        # 初始化UI
        self._setup_ui()
        
    def _setup_ui(self):
        """设置UI组件"""
        # 使用QVBoxLayout直接在对话框上添加控件
        self.main_layout = QVBoxLayout(self)
        self.main_layout.setContentsMargins(24, 24, 24, 24)
        self.main_layout.setSpacing(16)
        
        # 任务基本信息
        self.main_layout.addWidget(SubtitleLabel("基本信息"))
        
        # 任务名称
        name_layout = QHBoxLayout()
        name_layout.addWidget(BodyLabel("任务名称:"))
        self.task_name_edit = LineEdit()
        self.task_name_edit.setPlaceholderText("请输入任务名称")
        name_layout.addWidget(self.task_name_edit)
        self.main_layout.addLayout(name_layout)
        
        # 任务类型
        type_layout = QHBoxLayout()
        type_layout.addWidget(BodyLabel("消息类型:"))
        self.task_type_combo = ComboBox()
        self.task_type_combo.addItems(["文本消息", "图片消息", "视频消息", "文件消息", "混合消息"])
        type_layout.addWidget(self.task_type_combo)
        self.main_layout.addLayout(type_layout)
        
        # 添加分割线
        separator = QFrame()
        separator.setFrameShape(QFrame.HLine)
        separator.setFrameShadow(QFrame.Sunken)
        separator.setStyleSheet("background-color: #d0d0d0; max-height: 1px;")
        self.main_layout.addWidget(separator)
        
        # 目标设置
        self.main_layout.addWidget(SubtitleLabel("目标设置"))
        
        # 目标类型选择
        target_type_layout = QHBoxLayout()
        self.target_type_group = QButtonGroup(self)
        self.group_radio = RadioButton("发送到群组")
        self.private_radio = RadioButton("发送到个人")
        self.target_type_group.addButton(self.group_radio, 1)
        self.target_type_group.addButton(self.private_radio, 2)
        self.group_radio.setChecked(True)
        
        target_type_layout.addWidget(BodyLabel("发送对象:"))
        target_type_layout.addWidget(self.group_radio)
        target_type_layout.addWidget(self.private_radio)
        target_type_layout.addStretch()
        self.main_layout.addLayout(target_type_layout)
        
        # 目标选择
        select_target_layout = QHBoxLayout()
        select_target_layout.addWidget(BodyLabel("选择目标:"))
        self.select_target_btn = PushButton("选择目标")
        self.select_target_btn.setIcon(FluentIcon.ADD)
        select_target_layout.addWidget(self.select_target_btn)
        select_target_layout.addStretch()
        self.main_layout.addLayout(select_target_layout)
        
        # 已选目标显示
        self.target_display = TextEdit()
        self.target_display.setReadOnly(True)
        self.target_display.setPlaceholderText("尚未选择任何目标")
        self.target_display.setFixedHeight(100)
        self.main_layout.addWidget(self.target_display)
        
        # 添加分割线
        separator2 = QFrame()
        separator2.setFrameShape(QFrame.HLine)
        separator2.setFrameShadow(QFrame.Sunken)
        separator2.setStyleSheet("background-color: #d0d0d0; max-height: 1px;")
        self.main_layout.addWidget(separator2)
        
        # 消息内容
        self.main_layout.addWidget(SubtitleLabel("消息内容"))
        
        # 消息编辑区域
        self.message_edit = TextEdit()
        self.message_edit.setPlaceholderText("请输入要发送的消息内容...")
        self.message_edit.setFixedHeight(100)
        self.main_layout.addWidget(self.message_edit)
        
        # 媒体附件
        media_layout = QHBoxLayout()
        media_layout.addWidget(BodyLabel("添加附件:"))
        self.add_image_btn = PushButton("添加图片")
        self.add_image_btn.setIcon(FluentIcon.PHOTO)
        self.add_video_btn = PushButton("添加视频")
        self.add_video_btn.setIcon(FluentIcon.VIDEO)
        self.add_file_btn = PushButton("添加文件")
        self.add_file_btn.setIcon(FluentIcon.DOCUMENT)
        
        media_layout.addWidget(self.add_image_btn)
        media_layout.addWidget(self.add_video_btn)
        media_layout.addWidget(self.add_file_btn)
        media_layout.addStretch()
        self.main_layout.addLayout(media_layout)
        
        # 附件预览区
        self.attachment_preview = TextEdit()
        self.attachment_preview.setReadOnly(True)
        self.attachment_preview.setPlaceholderText("尚未添加任何附件")
        self.attachment_preview.setFixedHeight(80)
        self.main_layout.addWidget(self.attachment_preview)
        
        # 添加分割线
        separator3 = QFrame()
        separator3.setFrameShape(QFrame.HLine)
        separator3.setFrameShadow(QFrame.Sunken)
        separator3.setStyleSheet("background-color: #d0d0d0; max-height: 1px;")
        self.main_layout.addWidget(separator3)
        
        # 发送设置
        self.main_layout.addWidget(SubtitleLabel("发送设置"))
        
        # 定时发送
        schedule_layout = QHBoxLayout()
        schedule_layout.addWidget(BodyLabel("定时发送:"))
        self.schedule_switch = SwitchButton(self)
        schedule_layout.addWidget(self.schedule_switch)
        schedule_layout.addStretch()
        self.main_layout.addLayout(schedule_layout)
        
        # 定时设置区域
        self.schedule_widget = QWidget()
        schedule_settings_layout = QHBoxLayout(self.schedule_widget)
        schedule_settings_layout.setContentsMargins(20, 0, 0, 0)
        
        schedule_settings_layout.addWidget(BodyLabel("发送时间:"))
        self.schedule_datetime = DateTimeEdit()
        self.schedule_datetime.setDateTime(QDateTime.currentDateTime().addSecs(3600))  # 默认1小时后
        schedule_settings_layout.addWidget(self.schedule_datetime)
        schedule_settings_layout.addStretch()
        
        self.schedule_widget.setVisible(False)  # 默认隐藏
        self.main_layout.addWidget(self.schedule_widget)
        
        # 发送间隔
        interval_layout = QHBoxLayout()
        interval_layout.addWidget(BodyLabel("发送间隔:"))
        self.interval_spinbox = SpinBox()
        self.interval_spinbox.setRange(1, 600)
        self.interval_spinbox.setValue(5)
        interval_layout.addWidget(self.interval_spinbox)
        interval_layout.addWidget(BodyLabel("秒"))
        interval_layout.addStretch()
        self.main_layout.addLayout(interval_layout)
        
        # 随机间隔
        random_interval_layout = QHBoxLayout()
        random_interval_layout.addWidget(BodyLabel("随机间隔:"))
        self.random_interval_switch = SwitchButton(self)
        random_interval_layout.addWidget(self.random_interval_switch)
        random_interval_layout.addWidget(BodyLabel("(在发送间隔基础上增加0-5秒随机延迟)"))
        random_interval_layout.addStretch()
        self.main_layout.addLayout(random_interval_layout)
        
        # 重试设置
        retry_layout = QHBoxLayout()
        retry_layout.addWidget(BodyLabel("失败重试:"))
        self.retry_spinbox = SpinBox()
        self.retry_spinbox.setRange(0, 10)
        self.retry_spinbox.setValue(3)
        retry_layout.addWidget(self.retry_spinbox)
        retry_layout.addWidget(BodyLabel("次"))
        retry_layout.addStretch()
        self.main_layout.addLayout(retry_layout)
        
        # 提醒设置
        notify_layout = QHBoxLayout()
        notify_layout.addWidget(BodyLabel("发送完成通知:"))
        self.notify_switch = SwitchButton(self)
        notify_layout.addWidget(self.notify_switch)
        notify_layout.addStretch()
        self.main_layout.addLayout(notify_layout)
        
        # 添加按钮区域
        button_layout = QHBoxLayout()
        button_layout.addStretch()
        
        self.cancelButton = PushButton("取消")
        self.cancelButton.clicked.connect(self.reject)
        
        self.yesButton = PrimaryPushButton("创建")
        self.yesButton.clicked.connect(self.accept)
        
        button_layout.addWidget(self.cancelButton)
        button_layout.addWidget(self.yesButton)
        
        self.main_layout.addLayout(button_layout)


class TaskDetailDialog(QDialog):
    """任务详情对话框"""
    
    def __init__(self, task_id: str, parent=None):
        super().__init__(parent)
        self.task_id = task_id
        self.setWindowTitle(f"任务详情 - {task_id}")
        self.resize(800, 600)
        
        # 初始化UI
        self._setup_ui()
        
    def _setup_ui(self):
        """设置UI组件"""
        # 使用QVBoxLayout直接在对话框上添加控件
        self.main_layout = QVBoxLayout(self)
        self.main_layout.setContentsMargins(24, 24, 24, 24)
        self.main_layout.setSpacing(16)
        
        # 任务信息头部
        header_layout = QHBoxLayout()
        
        # 任务ID和名称
        task_info_layout = QVBoxLayout()
        self.task_name_label = TitleLabel("任务名称")
        self.task_id_label = BodyLabel(f"ID: {self.task_id}")
        task_info_layout.addWidget(self.task_name_label)
        task_info_layout.addWidget(self.task_id_label)
        header_layout.addLayout(task_info_layout)
        
        header_layout.addStretch()
        
        # 状态和控制按钮
        control_layout = QHBoxLayout()
        self.status_label = QLabel("状态: 运行中")
        self.status_label.setStyleSheet("color: #2d8cf0; font-weight: bold;")
        control_layout.addWidget(self.status_label)
        
        self.control_btn = PushButton("暂停任务")
        self.control_btn.setIcon(FluentIcon.PAUSE)
        control_layout.addWidget(self.control_btn)
        
        header_layout.addLayout(control_layout)
        self.main_layout.addLayout(header_layout)
        
        # 添加标签页切换
        tab_layout = QHBoxLayout()
        self.info_tab_btn = PushButton("基本信息")
        self.info_tab_btn.setCheckable(True)
        self.info_tab_btn.setChecked(True)
        
        self.stats_tab_btn = PushButton("发送统计")
        self.stats_tab_btn.setCheckable(True)
        
        self.logs_tab_btn = PushButton("任务日志")
        self.logs_tab_btn.setCheckable(True)
        
        tab_layout.addWidget(self.info_tab_btn)
        tab_layout.addWidget(self.stats_tab_btn)
        tab_layout.addWidget(self.logs_tab_btn)
        tab_layout.addStretch()
        self.main_layout.addLayout(tab_layout)
        
        # 内容堆栈
        self.content_stack = QStackedWidget()
        
        # 基本信息页
        self.info_page = QWidget()
        self._setup_info_page()
        self.content_stack.addWidget(self.info_page)
        
        # 统计页
        self.stats_page = QWidget()
        self._setup_stats_page()
        self.content_stack.addWidget(self.stats_page)
        
        # 日志页
        self.logs_page = QWidget()
        self._setup_logs_page()
        self.content_stack.addWidget(self.logs_page)
        
        self.main_layout.addWidget(self.content_stack)
        
        # 添加按钮区域
        button_layout = QHBoxLayout()
        button_layout.addStretch()
        
        self.closeButton = PushButton("关闭")
        self.closeButton.clicked.connect(self.accept)
        
        button_layout.addWidget(self.closeButton)
        
        self.main_layout.addLayout(button_layout)
    
    def _setup_info_page(self):
        """设置基本信息页"""
        layout = QVBoxLayout(self.info_page)
        
        # 基本信息卡片
        info_card = CardWidget()
        info_layout = QVBoxLayout(info_card)
        
        # 基本信息字段
        fields = [
            ("创建时间:", "2023-05-01 15:30:45"),
            ("消息类型:", "文本消息"),
            ("目标类型:", "发送到群组"),
            ("目标数量:", "10个群组"),
            ("发送间隔:", "5秒"),
            ("随机间隔:", "启用"),
            ("失败重试:", "3次"),
            ("完成通知:", "启用")
        ]
        
        for label, value in fields:
            field_layout = QHBoxLayout()
            field_layout.addWidget(StrongBodyLabel(label))
            field_layout.addWidget(BodyLabel(value))
            field_layout.addStretch()
            info_layout.addLayout(field_layout)
        
        layout.addWidget(info_card)
        
        # 消息内容
        layout.addWidget(SubtitleLabel("消息内容"))
        
        message_card = CardWidget()
        message_layout = QVBoxLayout(message_card)
        self.message_display = TextEdit()
        self.message_display.setReadOnly(True)
        self.message_display.setPlainText("这是一条测试消息，用于演示群发功能。")
        message_layout.addWidget(self.message_display)
        
        layout.addWidget(message_card)
        
        # 目标列表
        layout.addWidget(SubtitleLabel("发送目标"))
        
        targets_card = CardWidget()
        targets_layout = QVBoxLayout(targets_card)
        self.targets_table = TableWidget()
        self.targets_table.setColumnCount(3)
        self.targets_table.setHorizontalHeaderLabels(["目标名称", "目标类型", "发送状态"])
        self.targets_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        
        # 添加一些示例数据
        self.targets_table.setRowCount(5)
        for i in range(5):
            self.targets_table.setItem(i, 0, QTableWidgetItem(f"群组 {i+1}"))
            self.targets_table.setItem(i, 1, QTableWidgetItem("群组"))
            status_item = QTableWidgetItem("已发送" if i < 3 else "等待中")
            status_item.setForeground(QColor("#19be6b" if i < 3 else "#ff9900"))
            self.targets_table.setItem(i, 2, status_item)
        
        targets_layout.addWidget(self.targets_table)
        layout.addWidget(targets_card)
    
    def _setup_stats_page(self):
        """设置统计页"""
        layout = QVBoxLayout(self.stats_page)
        
        # 统计卡片区域
        stats_layout = QHBoxLayout()
        
        total_card = self._create_stats_card("总目标数", "10")
        sent_card = self._create_stats_card("已发送", "6", "#19be6b")
        failed_card = self._create_stats_card("失败", "1", "#ed4014")
        waiting_card = self._create_stats_card("等待中", "3", "#ff9900")
        
        stats_layout.addWidget(total_card)
        stats_layout.addWidget(sent_card)
        stats_layout.addWidget(failed_card)
        stats_layout.addWidget(waiting_card)
        
        layout.addLayout(stats_layout)
        
        # 进度显示
        progress_card = CardWidget()
        progress_layout = QVBoxLayout(progress_card)
        progress_layout.addWidget(SubtitleLabel("发送进度"))
        
        # 这里可以放置进度图表，简单用文字代替
        progress_text = BodyLabel("已完成: 60%")
        progress_layout.addWidget(progress_text)
        
        layout.addWidget(progress_card)
        
        # 详细统计表格
        layout.addWidget(SubtitleLabel("发送详情"))
        
        stats_table = TableWidget()
        stats_table.setColumnCount(5)
        stats_table.setHorizontalHeaderLabels(["目标名称", "发送时间", "状态", "重试次数", "失败原因"])
        stats_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        
        # 添加一些示例数据
        stats_table.setRowCount(10)
        for i in range(10):
            stats_table.setItem(i, 0, QTableWidgetItem(f"目标 {i+1}"))
            stats_table.setItem(i, 1, QTableWidgetItem("2023-05-01 15:31:45"))
            
            if i < 6:
                status = "成功"
                color = "#19be6b"
            elif i == 6:
                status = "失败"
                color = "#ed4014"
            else:
                status = "等待中"
                color = "#ff9900"
                
            status_item = QTableWidgetItem(status)
            status_item.setForeground(QColor(color))
            stats_table.setItem(i, 2, status_item)
            
            stats_table.setItem(i, 3, QTableWidgetItem("0" if status != "失败" else "3"))
            stats_table.setItem(i, 4, QTableWidgetItem("" if status != "失败" else "目标不可达"))
        
        layout.addWidget(stats_table)
    
    def _setup_logs_page(self):
        """设置日志页"""
        layout = QVBoxLayout(self.logs_page)
        
        # 日志过滤
        filter_layout = QHBoxLayout()
        filter_layout.addWidget(BodyLabel("日志级别:"))
        log_level_combo = ComboBox()
        log_level_combo.addItems(["全部", "信息", "警告", "错误"])
        filter_layout.addWidget(log_level_combo)
        filter_layout.addStretch()
        
        layout.addLayout(filter_layout)
        
        # 日志显示
        log_display = TextEdit()
        log_display.setReadOnly(True)
        log_display.setStyleSheet("font-family: Consolas, Monaco, monospace; font-size: 12px;")
        
        # 添加一些示例日志
        sample_logs = [
            "[2023-05-01 15:30:45] [INFO] 任务已创建",
            "[2023-05-01 15:30:45] [INFO] 开始执行任务",
            "[2023-05-01 15:31:00] [INFO] 成功发送到 目标 1",
            "[2023-05-01 15:31:05] [INFO] 成功发送到 目标 2",
            "[2023-05-01 15:31:10] [INFO] 成功发送到 目标 3",
            "[2023-05-01 15:31:15] [INFO] 成功发送到 目标 4",
            "[2023-05-01 15:31:20] [INFO] 成功发送到 目标 5",
            "[2023-05-01 15:31:25] [INFO] 成功发送到 目标 6",
            "[2023-05-01 15:31:30] [ERROR] 发送到 目标 7 失败: 目标不可达",
            "[2023-05-01 15:31:35] [WARNING] 重试发送到 目标 7, 尝试 1/3",
            "[2023-05-01 15:31:40] [ERROR] 重试失败: 目标 7, 尝试 1/3",
            "[2023-05-01 15:31:45] [WARNING] 重试发送到 目标 7, 尝试 2/3",
            "[2023-05-01 15:31:50] [ERROR] 重试失败: 目标 7, 尝试 2/3",
            "[2023-05-01 15:31:55] [WARNING] 重试发送到 目标 7, 尝试 3/3",
            "[2023-05-01 15:32:00] [ERROR] 重试失败: 目标 7, 尝试 3/3",
            "[2023-05-01 15:32:05] [ERROR] 放弃发送到 目标 7, 已达到最大重试次数"
        ]
        log_display.setPlainText("\n".join(sample_logs))
        
        layout.addWidget(log_display)
    
    def _create_stats_card(self, title: str, value: str, color: str = "#409eff") -> QWidget:
        """创建统计信息卡片"""
        card = QFrame()
        card.setObjectName("StatsCard")
        card.setStyleSheet(f"""
            #StatsCard {{
                background-color: white;
                border-radius: 8px;
                border: 1px solid #e0e0e0;
            }}
            #StatValue {{
                color: {color};
                font-size: 24px;
                font-weight: bold;
            }}
            #StatTitle {{
                color: #666;
                font-size: 14px;
            }}
        """)
        
        layout = QVBoxLayout(card)
        layout.setContentsMargins(15, 10, 15, 10)
        
        value_label = QLabel(value)
        value_label.setObjectName("StatValue")
        value_label.setAlignment(Qt.AlignCenter)
        
        title_label = QLabel(title)
        title_label.setObjectName("StatTitle")
        title_label.setAlignment(Qt.AlignCenter)
        
        layout.addWidget(value_label)
        layout.addWidget(title_label)
        
        return card
