from PySide6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QPushButton, 
                             QLabel, QLineEdit, QTextEdit, QComboBox, QScrollArea,
                             QFrame, QTableWidget, QTableWidgetItem, QHeaderView,
                             QProgressBar, QFileDialog, QSpinBox, QDateTimeEdit,
                             QTabWidget, QGridLayout, QSizePolicy, QCheckBox)
from PySide6.QtCore import Qt, Signal, QSize, QDateTime, QTimer
from PySide6.QtGui import QIcon, QColor, QFont, QPixmap, QPainter, QPalette

from qfluentwidgets import (PushButton, ToolButton, IconWidget, TransparentToolButton, 
                          PrimaryPushButton, ComboBox, ToggleButton, InfoBar, InfoBarPosition,
                          LineEdit, TextEdit, CheckBox, SpinBox, DateTimeEdit, FluentIcon,
                          SwitchButton, CardWidget, SimpleCardWidget, ElevatedCardWidget,
                          StrongBodyLabel, BodyLabel, CaptionLabel, TitleLabel, SubtitleLabel,SmoothScrollArea,
                          TabBar, RoundMenu, Action, SearchLineEdit, ProgressBar, StateToolTip,
                          setTheme, Theme)


class StatusBadge(QFrame):
    """状态徽章组件"""
    
    def __init__(self, text="", bg_color="#E8F5E9", text_color="#388E3C", parent=None):
        super().__init__(parent)
        self.setObjectName("statusBadge")
        
        self.layout = QHBoxLayout(self)
        self.layout.setContentsMargins(8, 4, 8, 4)
        self.layout.setSpacing(4)
        
        self.label = BodyLabel(text)
        self.label.setObjectName("statusBadgeLabel")
        self.layout.addWidget(self.label)
        
        # 设置样式
        self.setStyleSheet(f"""
            QFrame#statusBadge {{
                background-color: {bg_color};
                border-radius: 6px;
                border: none;
            }}
            QLabel#statusBadgeLabel {{
                color: {text_color};
            }}
        """)
    
    def setText(self, text):
        """设置文本"""
        self.label.setText(text)


class TaskCardUI(ElevatedCardWidget):
    """任务卡片UI组件"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setObjectName("taskCard")
        
        # 设置卡片样式
        self.setMinimumHeight(120)
        self.setMaximumHeight(120)
        self.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)
        
        # 创建布局
        self.main_layout = QVBoxLayout(self)
        self.main_layout.setContentsMargins(16, 12, 16, 12)
        self.main_layout.setSpacing(8)
        
        # 创建顶部信息栏
        self.header_layout = QHBoxLayout()
        self.header_layout.setSpacing(10)
        
        # 任务名称标签
        self.name_label = StrongBodyLabel()
        self.name_label.setObjectName("taskNameLabel")
        self.name_label.setFixedWidth(150)
        
        # 任务ID标签
        self.id_label = CaptionLabel()
        self.id_label.setObjectName("taskIdLabel")
        
        # 状态标签
        self.status_label = CaptionLabel()
        self.status_label.setObjectName("statusLabel")
        
        # 添加到顶部布局
        self.header_layout.addWidget(self.name_label)
        self.header_layout.addWidget(self.id_label)
        self.header_layout.addStretch(1)
        self.header_layout.addWidget(self.status_label)
        
        # 创建进度信息栏
        self.info_layout = QHBoxLayout()
        self.info_layout.setSpacing(10)
        
        # 收件人信息
        self.recipients_label = BodyLabel()
        self.recipients_label.setObjectName("infoLabel")
        
        # 进度条
        self.progress_bar = ProgressBar()
        self.progress_bar.setTextVisible(True)
        self.progress_bar.setFixedHeight(8)
        self.progress_bar.setObjectName("taskProgress")
        
        # 进度文本
        self.progress_label = BodyLabel()
        self.progress_label.setObjectName("progressLabel")
        
        # 添加到信息布局
        self.info_layout.addWidget(self.recipients_label)
        self.info_layout.addStretch(1)
        self.info_layout.addWidget(self.progress_label)
        
        # 创建按钮布局
        self.button_layout = QHBoxLayout()
        self.button_layout.setSpacing(8)
        
        # 添加状态指示器（成功、失败、待邀）
        self.status_indicators_layout = QHBoxLayout()
        self.status_indicators_layout.setSpacing(12)
        
        # 成功状态指示器
        self.success_count = StatusBadge("成功 0", "#E8F5E9", "#388E3C")
        
        # 失败状态指示器
        self.failed_count = StatusBadge("失败 0", "#FFEBEE", "#D32F2F")
        
        # 待邀状态指示器
        self.pending_count = StatusBadge("待邀 0", "#FFF8E1", "#FFA000")
        
        # 添加状态指示器到布局
        self.status_indicators_layout.addWidget(self.success_count)
        self.status_indicators_layout.addWidget(self.failed_count)
        self.status_indicators_layout.addWidget(self.pending_count)
        
        # 创建操作按钮
        self.view_button = PushButton("查看")
        self.pause_button = PushButton("暂停")
        self.delete_button = PushButton("删除")
            
        self.view_button.setObjectName("viewButton")
        self.pause_button.setObjectName("pauseButton")
        self.delete_button.setObjectName("deleteButton")
        
        # 设置按钮样式
        for btn in [self.view_button, self.pause_button, self.delete_button]:
            btn.setFixedHeight(30)
            btn.setMinimumWidth(60)
        
        # 添加状态指示器到按钮布局
        self.button_layout.addLayout(self.status_indicators_layout)
        
        # 添加弹簧
        self.button_layout.addStretch(1)
        
        # 添加按钮到布局
        self.button_layout.addWidget(self.view_button)
        self.button_layout.addWidget(self.pause_button)
        self.button_layout.addWidget(self.delete_button)
        
        # 组合所有布局
        self.main_layout.addLayout(self.header_layout)
        self.main_layout.addWidget(self.progress_bar)
        self.main_layout.addLayout(self.info_layout)
        self.main_layout.addLayout(self.button_layout)


class SendMessageWidgetUI(QWidget):
    """消息群发任务管理UI组件"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setObjectName("sendMessageWidget")
        
        # 创建主布局
        self.main_layout = QVBoxLayout(self)
        self.main_layout.setContentsMargins(20, 20, 20, 20)
        self.main_layout.setSpacing(16)
        
        # 创建标题栏
        self.header_layout = QHBoxLayout()
        self.header_layout.setSpacing(10)
        
        self.title_label = TitleLabel("拉人入群任务管理")
        self.title_label.setObjectName("pageTitle")
        
        # 添加新任务按钮
        self.new_task_button = PrimaryPushButton("创建新任务")
        
        self.header_layout.addWidget(self.title_label)
        self.header_layout.addStretch(1)
        self.header_layout.addWidget(self.new_task_button)
        
        # 任务列表区域
        self.tasks_layout = QVBoxLayout()
        self.tasks_layout.setContentsMargins(0, 16, 0, 0)
        self.tasks_layout.setSpacing(12)
        
        # 任务过滤区域
        self.filter_frame = QFrame()
        self.filter_frame.setObjectName("filterFrame")
        self.filter_layout = QHBoxLayout(self.filter_frame)
        self.filter_layout.setContentsMargins(16, 10, 16, 10)
        
        self.status_label = BodyLabel("状态:")
        self.status_combo = ComboBox()
        self.status_combo.addItems(["所有状态", "运行中", "等待中", "已完成", "失败"])
        
        self.search_label = BodyLabel("搜索:")
        self.search_input = SearchLineEdit()
        self.search_input.setPlaceholderText("输入任务名称或ID...")
        self.search_input.setFixedWidth(200)
        
        self.refresh_button = ToolButton(FluentIcon.SYNC)
        self.refresh_button.setToolTip("刷新")
        
        self.filter_layout.addWidget(self.status_label)
        self.filter_layout.addWidget(self.status_combo)
        self.filter_layout.addStretch(1)
        self.filter_layout.addWidget(self.search_label)
        self.filter_layout.addWidget(self.search_input)
        self.filter_layout.addWidget(self.refresh_button)
        
        # 任务列表区域（滚动区域）
        self.task_scroll = SmoothScrollArea()
        self.task_scroll.setWidgetResizable(True)
        self.task_scroll.setFrameShape(QFrame.NoFrame)
        
        self.task_container = QWidget()
        self.task_container_layout = QVBoxLayout(self.task_container)
        self.task_container_layout.setContentsMargins(0, 0, 0, 0)
        self.task_container_layout.setSpacing(12)
        
        # 添加占位的拉伸空间
        self.task_container_layout.addStretch(1)
        self.task_scroll.setWidget(self.task_container)
        
        # 添加到任务列表布局
        self.tasks_layout.addWidget(self.filter_frame)
        self.tasks_layout.addWidget(self.task_scroll)
        
        # 添加组件到主布局
        self.main_layout.addLayout(self.header_layout)
        self.main_layout.addLayout(self.tasks_layout) 