# -*- coding: utf-8 -*-

################################################################################
## Form generated from reading UI file 'convert.ui'
##
## Created by: Qt User Interface Compiler version 6.6.3
##
## WARNING! All changes made in this file will be lost when recompiling UI file!
################################################################################

from PySide6.QtCore import (QCoreApplication, QDate, QDateTime, QLocale,
    QMetaObject, QObject, QPoint, QRect,
    QSize, QTime, QUrl, Qt)
from PySide6.QtGui import (QBrush, QColor, QC<PERSON>alGradient, Q<PERSON>ursor,
    QFont, QFontDatabase, QGradient, QIcon,
    QImage, Q<PERSON>eySequence, QLinearGradient, QPainter,
    QPalette, QPixmap, QRadialGradient, QTransform)
from PySide6.QtWidgets import (QApplication, QGroupBox, QHBoxLayout, QHeaderView,
    QLabel, QSizePolicy, QSpacerItem, QTableWidgetItem,
    QTextBrowser, QVBoxLayout, QWidget)

from qfluentwidgets import (CardWidget, CheckBox, ComboBox, LineEdit,
    PrimaryPushButton, PushButton, StrongBodyLabel, TableWidget)

class Ui_Form(object):
    def setupUi(self, Form):
        if not Form.objectName():
            Form.setObjectName(u"Form")
        Form.resize(1012, 862)
        self.verticalLayout_3 = QVBoxLayout(Form)
        self.verticalLayout_3.setObjectName(u"verticalLayout_3")
        self.StrongBodyLabel = StrongBodyLabel(Form)
        self.StrongBodyLabel.setObjectName(u"StrongBodyLabel")

        self.verticalLayout_3.addWidget(self.StrongBodyLabel)

        self.CardWidget = CardWidget(Form)
        self.CardWidget.setObjectName(u"CardWidget")
        self.verticalLayout = QVBoxLayout(self.CardWidget)
        self.verticalLayout.setObjectName(u"verticalLayout")
        self.horizontalLayout_4 = QHBoxLayout()
        self.horizontalLayout_4.setObjectName(u"horizontalLayout_4")
        self.horizontalLayout = QHBoxLayout()
        self.horizontalLayout.setObjectName(u"horizontalLayout")
        self.label_3 = QLabel(self.CardWidget)
        self.label_3.setObjectName(u"label_3")

        self.horizontalLayout.addWidget(self.label_3)

        self.ComboBox = ComboBox(self.CardWidget)
        self.ComboBox.setObjectName(u"ComboBox")

        self.horizontalLayout.addWidget(self.ComboBox)


        self.horizontalLayout_4.addLayout(self.horizontalLayout)

        self.horizontalLayout_2 = QHBoxLayout()
        self.horizontalLayout_2.setObjectName(u"horizontalLayout_2")
        self.label_2 = QLabel(self.CardWidget)
        self.label_2.setObjectName(u"label_2")

        self.horizontalLayout_2.addWidget(self.label_2)

        self.ComboBox_2 = ComboBox(self.CardWidget)
        self.ComboBox_2.setObjectName(u"ComboBox_2")

        self.horizontalLayout_2.addWidget(self.ComboBox_2)

        self.label = QLabel(self.CardWidget)
        self.label.setObjectName(u"label")

        self.horizontalLayout_2.addWidget(self.label)

        self.LineEdit = LineEdit(self.CardWidget)
        self.LineEdit.setObjectName(u"LineEdit")

        self.horizontalLayout_2.addWidget(self.LineEdit)


        self.horizontalLayout_4.addLayout(self.horizontalLayout_2)


        self.verticalLayout.addLayout(self.horizontalLayout_4)

        self.horizontalLayout_3 = QHBoxLayout()
        self.horizontalLayout_3.setObjectName(u"horizontalLayout_3")
        self.label_4 = QLabel(self.CardWidget)
        self.label_4.setObjectName(u"label_4")

        self.horizontalLayout_3.addWidget(self.label_4)

        self.LineEdit_4 = LineEdit(self.CardWidget)
        self.LineEdit_4.setObjectName(u"LineEdit_4")

        self.horizontalLayout_3.addWidget(self.LineEdit_4)

        self.LineEdit_5 = LineEdit(self.CardWidget)
        self.LineEdit_5.setObjectName(u"LineEdit_5")
        sizePolicy = QSizePolicy(QSizePolicy.Policy.Fixed, QSizePolicy.Policy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.LineEdit_5.sizePolicy().hasHeightForWidth())
        self.LineEdit_5.setSizePolicy(sizePolicy)

        self.horizontalLayout_3.addWidget(self.LineEdit_5)

        self.LineEdit_6 = LineEdit(self.CardWidget)
        self.LineEdit_6.setObjectName(u"LineEdit_6")

        self.horizontalLayout_3.addWidget(self.LineEdit_6)

        self.LineEdit_7 = LineEdit(self.CardWidget)
        self.LineEdit_7.setObjectName(u"LineEdit_7")

        self.horizontalLayout_3.addWidget(self.LineEdit_7)

        self.horizontalSpacer_4 = QSpacerItem(40, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.horizontalLayout_3.addItem(self.horizontalSpacer_4)


        self.verticalLayout.addLayout(self.horizontalLayout_3)

        self.horizontalLayout_9 = QHBoxLayout()
        self.horizontalLayout_9.setObjectName(u"horizontalLayout_9")
        self.horizontalSpacer = QSpacerItem(40, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.horizontalLayout_9.addItem(self.horizontalSpacer)

        self.PushButton = PushButton(self.CardWidget)
        self.PushButton.setObjectName(u"PushButton")

        self.horizontalLayout_9.addWidget(self.PushButton)

        self.PushButton_3 = PushButton(self.CardWidget)
        self.PushButton_3.setObjectName(u"PushButton_3")

        self.horizontalLayout_9.addWidget(self.PushButton_3)


        self.verticalLayout.addLayout(self.horizontalLayout_9)


        self.verticalLayout_3.addWidget(self.CardWidget)

        self.TableWidget = TableWidget(Form)
        if (self.TableWidget.columnCount() < 6):
            self.TableWidget.setColumnCount(6)
        __qtablewidgetitem = QTableWidgetItem()
        self.TableWidget.setHorizontalHeaderItem(0, __qtablewidgetitem)
        __qtablewidgetitem1 = QTableWidgetItem()
        self.TableWidget.setHorizontalHeaderItem(1, __qtablewidgetitem1)
        __qtablewidgetitem2 = QTableWidgetItem()
        self.TableWidget.setHorizontalHeaderItem(2, __qtablewidgetitem2)
        __qtablewidgetitem3 = QTableWidgetItem()
        self.TableWidget.setHorizontalHeaderItem(3, __qtablewidgetitem3)
        __qtablewidgetitem4 = QTableWidgetItem()
        self.TableWidget.setHorizontalHeaderItem(4, __qtablewidgetitem4)
        __qtablewidgetitem5 = QTableWidgetItem()
        self.TableWidget.setHorizontalHeaderItem(5, __qtablewidgetitem5)
        self.TableWidget.setObjectName(u"TableWidget")
        sizePolicy1 = QSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Preferred)
        sizePolicy1.setHorizontalStretch(0)
        sizePolicy1.setVerticalStretch(50)
        sizePolicy1.setHeightForWidth(self.TableWidget.sizePolicy().hasHeightForWidth())
        self.TableWidget.setSizePolicy(sizePolicy1)

        self.verticalLayout_3.addWidget(self.TableWidget)

        self.CardWidget_2 = CardWidget(Form)
        self.CardWidget_2.setObjectName(u"CardWidget_2")
        self.horizontalLayout_7 = QHBoxLayout(self.CardWidget_2)
        self.horizontalLayout_7.setObjectName(u"horizontalLayout_7")
        self.verticalLayout_2 = QVBoxLayout()
        self.verticalLayout_2.setObjectName(u"verticalLayout_2")
        self.horizontalLayout_5 = QHBoxLayout()
        self.horizontalLayout_5.setObjectName(u"horizontalLayout_5")
        self.label_5 = QLabel(self.CardWidget_2)
        self.label_5.setObjectName(u"label_5")

        self.horizontalLayout_5.addWidget(self.label_5)

        self.LineEdit_3 = LineEdit(self.CardWidget_2)
        self.LineEdit_3.setObjectName(u"LineEdit_3")

        self.horizontalLayout_5.addWidget(self.LineEdit_3)

        self.PushButton_2 = PushButton(self.CardWidget_2)
        self.PushButton_2.setObjectName(u"PushButton_2")

        self.horizontalLayout_5.addWidget(self.PushButton_2)

        self.horizontalSpacer_3 = QSpacerItem(40, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.horizontalLayout_5.addItem(self.horizontalSpacer_3)


        self.verticalLayout_2.addLayout(self.horizontalLayout_5)

        self.horizontalLayout_6 = QHBoxLayout()
        self.horizontalLayout_6.setObjectName(u"horizontalLayout_6")
        self.label_6 = QLabel(self.CardWidget_2)
        self.label_6.setObjectName(u"label_6")
        sizePolicy2 = QSizePolicy(QSizePolicy.Policy.Fixed, QSizePolicy.Policy.Preferred)
        sizePolicy2.setHorizontalStretch(0)
        sizePolicy2.setVerticalStretch(0)
        sizePolicy2.setHeightForWidth(self.label_6.sizePolicy().hasHeightForWidth())
        self.label_6.setSizePolicy(sizePolicy2)

        self.horizontalLayout_6.addWidget(self.label_6)

        self.CheckBox = CheckBox(self.CardWidget_2)
        self.CheckBox.setObjectName(u"CheckBox")
        sizePolicy.setHeightForWidth(self.CheckBox.sizePolicy().hasHeightForWidth())
        self.CheckBox.setSizePolicy(sizePolicy)

        self.horizontalLayout_6.addWidget(self.CheckBox)

        self.CheckBox_2 = CheckBox(self.CardWidget_2)
        self.CheckBox_2.setObjectName(u"CheckBox_2")

        self.horizontalLayout_6.addWidget(self.CheckBox_2)

        self.horizontalSpacer_2 = QSpacerItem(40, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.horizontalLayout_6.addItem(self.horizontalSpacer_2)


        self.verticalLayout_2.addLayout(self.horizontalLayout_6)


        self.horizontalLayout_7.addLayout(self.verticalLayout_2)

        self.PrimaryPushButton = PrimaryPushButton(self.CardWidget_2)
        self.PrimaryPushButton.setObjectName(u"PrimaryPushButton")

        self.horizontalLayout_7.addWidget(self.PrimaryPushButton)


        self.verticalLayout_3.addWidget(self.CardWidget_2)

        self.groupBox = QGroupBox(Form)
        self.groupBox.setObjectName(u"groupBox")
        sizePolicy3 = QSizePolicy(QSizePolicy.Policy.Preferred, QSizePolicy.Policy.Fixed)
        sizePolicy3.setHorizontalStretch(0)
        sizePolicy3.setVerticalStretch(20)
        sizePolicy3.setHeightForWidth(self.groupBox.sizePolicy().hasHeightForWidth())
        self.groupBox.setSizePolicy(sizePolicy3)
        self.horizontalLayout_8 = QHBoxLayout(self.groupBox)
        self.horizontalLayout_8.setObjectName(u"horizontalLayout_8")
        self.textBrowser = QTextBrowser(self.groupBox)
        self.textBrowser.setObjectName(u"textBrowser")

        self.horizontalLayout_8.addWidget(self.textBrowser)


        self.verticalLayout_3.addWidget(self.groupBox)


        self.retranslateUi(Form)

        QMetaObject.connectSlotsByName(Form)
    # setupUi

    def retranslateUi(self, Form):
        Form.setWindowTitle(QCoreApplication.translate("Form", u"Form", None))
        self.StrongBodyLabel.setText(QCoreApplication.translate("Form", u"Tdata\u548cSesison\u4e92\u8f6c", None))
        self.label_3.setText(QCoreApplication.translate("Form", u"\u4e92\u8f6c\u65b9\u5411", None))
        self.label_2.setText(QCoreApplication.translate("Form", u"\u5bc6\u7801\u7c7b\u578b", None))
        self.label.setText(QCoreApplication.translate("Form", u"\u9ed8\u8ba4\u5bc6\u7801", None))
        self.label_4.setText(QCoreApplication.translate("Form", u"\u4ee3\u7406ip(\u53ef\u4e0d\u8bbe\u7f6e\uff0c\u53ea\u652f\u6301Socks5)", None))
        self.LineEdit_4.setPlaceholderText(QCoreApplication.translate("Form", u"IP\u5730\u5740", None))
        self.LineEdit_5.setPlaceholderText(QCoreApplication.translate("Form", u"1080", None))
        self.LineEdit_6.setPlaceholderText(QCoreApplication.translate("Form", u"\u7528\u6237\u540d\uff0c\u9ed8\u8ba4\u7a7a", None))
        self.LineEdit_7.setPlaceholderText(QCoreApplication.translate("Form", u"\u5bc6\u7801\u9ed8\u8ba4\u65e0", None))
        self.PushButton.setText(QCoreApplication.translate("Form", u"\u5bfc\u5165", None))
        self.PushButton_3.setText(QCoreApplication.translate("Form", u"\u6e05\u7a7a\u5217\u8868", None))
        ___qtablewidgetitem = self.TableWidget.horizontalHeaderItem(0)
        ___qtablewidgetitem.setText(QCoreApplication.translate("Form", u"\u53f7\u7801", None));
        ___qtablewidgetitem1 = self.TableWidget.horizontalHeaderItem(1)
        ___qtablewidgetitem1.setText(QCoreApplication.translate("Form", u"\u7c7b\u578b", None));
        ___qtablewidgetitem2 = self.TableWidget.horizontalHeaderItem(2)
        ___qtablewidgetitem2.setText(QCoreApplication.translate("Form", u"\u5bc6\u7801", None));
        ___qtablewidgetitem3 = self.TableWidget.horizontalHeaderItem(3)
        ___qtablewidgetitem3.setText(QCoreApplication.translate("Form", u"\u6ce8\u518c\u65f6\u95f4", None));
        ___qtablewidgetitem4 = self.TableWidget.horizontalHeaderItem(4)
        ___qtablewidgetitem4.setText(QCoreApplication.translate("Form", u"\u662f\u5426\u4f1a\u5458", None));
        ___qtablewidgetitem5 = self.TableWidget.horizontalHeaderItem(5)
        ___qtablewidgetitem5.setText(QCoreApplication.translate("Form", u"\u8f6c\u6362\u7ed3\u679c", None));
        self.label_5.setText(QCoreApplication.translate("Form", u"\u5bfc\u51fa\u8def\u5f84", None))
        self.LineEdit_3.setPlaceholderText(QCoreApplication.translate("Form", u"socks5://127.0.0.1:1080", None))
        self.PushButton_2.setText(QCoreApplication.translate("Form", u"\u9009\u62e9", None))
        self.label_6.setText(QCoreApplication.translate("Form", u"\u5bfc\u51fa\u683c\u5f0f", None))
        self.CheckBox.setText(QCoreApplication.translate("Form", u"Telethon\u534f\u8bae", None))
        self.CheckBox_2.setText(QCoreApplication.translate("Form", u"Tdata\u76f4\u767b\u53f7", None))
        self.PrimaryPushButton.setText(QCoreApplication.translate("Form", u"\u5f00\u59cb\u8f6c\u5316", None))
        self.groupBox.setTitle(QCoreApplication.translate("Form", u"\u64cd\u4f5c\u65e5\u5fd7", None))
    # retranslateUi

