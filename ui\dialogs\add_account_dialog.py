from PySide6.QtCore import Qt, Signal
from PySide6.QtWidgets import QWidget, QVBoxLayout, QHBoxLayout, QApplication, QLabel, QDialog, QListWidget, QListWidgetItem, QSplitter

from qfluentwidgets import (LineEdit, PushButton, RadioButton, 
                          InfoBar, FlowLayout, TitleLabel, SubtitleLabel,
                          InfoBarPosition, BodyLabel, ComboBox, SearchLineEdit,PrimaryPushButton)
class AddAccountUI(QDialog):
    """添加账号UI组件 - 新布局，左右分区设计"""
    # 定义UI内部信号
    proxy_toggled = Signal(bool)  # 代理选项切换信号
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.init_ui()

    def init_ui(self):
        self.main_layout = QVBoxLayout(self)
        self.main_layout.setSpacing(20)
        self.main_layout.setContentsMargins(30, 30, 30, 30)

        # 标题 - 居中显示
        title_layout = QHBoxLayout()
        title = TitleLabel('添加 Telegram 账户')
        title.setFixedHeight(40)  # 设置标题的固定高度
        title_layout.addStretch(1)  # 左侧弹性空间
        title_layout.addWidget(title)
        title_layout.addStretch(1)  # 右侧弹性空间
        self.main_layout.addLayout(title_layout)

        # 创建左右分区的水平布局
        content_layout = QHBoxLayout()
        
        # ===== 左侧区域：代理设置 =====
        left_widget = QWidget()
        left_layout = QVBoxLayout(left_widget)
        left_layout.setContentsMargins(0, 0, 20, 0)
        left_layout.setAlignment(Qt.AlignTop)  # 确保左侧组件始终在顶部
        
        # 代理设置标题
        proxy_title = SubtitleLabel('代理设置')
        left_layout.addWidget(proxy_title)

        # 代理选择按钮组
        self.no_proxy_radio = RadioButton('禁用代理')
        self.system_proxy_radio = RadioButton('系统代理')
        self.proxy_ip_radio = RadioButton('代理IP池')
        
        # 设置最小宽度确保文本完整显示
        self.no_proxy_radio.setMinimumWidth(100)
        self.system_proxy_radio.setMinimumWidth(100)
        self.proxy_ip_radio.setMinimumWidth(100)
        
        self.system_proxy_radio.setChecked(True)  # 默认选中系统代理

        # 使用水平布局而不是流式布局，确保按钮始终保持在一行
        proxy_radio_layout = QHBoxLayout()
        proxy_radio_layout.addWidget(self.no_proxy_radio)
        proxy_radio_layout.addWidget(self.system_proxy_radio) 
        proxy_radio_layout.addWidget(self.proxy_ip_radio)
        proxy_radio_layout.addStretch(1)  # 添加弹性空间，防止按钮过分拉伸
        left_layout.addLayout(proxy_radio_layout)
        
        # 代理IP池选择区域
        self.proxy_ip_widget = QWidget()
        proxy_ip_layout = QVBoxLayout(self.proxy_ip_widget)
        proxy_ip_layout.setAlignment(Qt.AlignTop)  # 确保代理IP选择区域内容在顶部
        
        proxy_note = BodyLabel('从IP池中选择代理IP (优先显示未绑定账号的代理)')
        proxy_ip_layout.addWidget(proxy_note)
        
        # 搜索框
        self.ip_search = SearchLineEdit(self)
        self.ip_search.setPlaceholderText('搜索IP地址')
        proxy_ip_layout.addWidget(self.ip_search)
        
        # IP列表
        self.ip_list = QListWidget()
        self.ip_list.setMinimumHeight(200)
        proxy_ip_layout.addWidget(self.ip_list)
        
        # 刷新IP池按钮
        self.refresh_ip_btn = PushButton('刷新IP列表')
        proxy_ip_layout.addWidget(self.refresh_ip_btn)
        
        self.proxy_ip_widget.hide()
        left_layout.addWidget(self.proxy_ip_widget)
        
        # ===== 右侧区域：用户信息 =====
        right_widget = QWidget()
        right_layout = QVBoxLayout(right_widget)
        right_layout.setContentsMargins(20, 0, 0, 0)
        right_layout.setAlignment(Qt.AlignTop)  # 确保右侧组件始终在顶部
        right_layout.setSpacing(15)  # 设置组件之间的垂直间距为15像素
        
        # 用户信息标题
        user_title = SubtitleLabel('账户信息')
        right_layout.addWidget(user_title)
        right_layout.addSpacing(10)  # 标题下方额外添加一些间距
        
        # 手机号输入框
        phone_label = QLabel("手机号码")
        right_layout.addWidget(phone_label)
        self.phone_input = LineEdit()
        self.phone_input.setPlaceholderText('请输入手机号(包含国家代码)')
        self.phone_input.setFixedHeight(32)  # 设置固定高度
        right_layout.addWidget(self.phone_input)
        right_layout.addSpacing(5)  # 添加一些额外间距

        # 验证码区域
        code_label = QLabel("验证码")
        right_layout.addWidget(code_label)
        code_layout = QHBoxLayout()
        self.code_input = LineEdit()
        self.code_input.setPlaceholderText('请输入验证码')
        self.code_input.setFixedHeight(32)  # 设置固定高度
        self.send_code_btn = PushButton('发送验证码')
        self.send_code_btn.setFixedHeight(32)  # 设置固定高度
        code_layout.addWidget(self.code_input)
        code_layout.addWidget(self.send_code_btn)
        right_layout.addLayout(code_layout)
        right_layout.addSpacing(5)  # 添加一些额外间距

        # 两步验证密码
        password_label = QLabel("两步验证密码")
        right_layout.addWidget(password_label)
        self.password_input = LineEdit()
        self.password_input.setPlaceholderText('如不存在可不填')
        self.password_input.setEchoMode(LineEdit.Password)
        self.password_input.setFixedHeight(32)  # 设置固定高度
        right_layout.addWidget(self.password_input)
        right_layout.addSpacing(5)  # 添加一些额外间距
        
        # 账户分组选择
        self.group_label = QLabel("账户分组")
        right_layout.addWidget(self.group_label)
        self.group_combo = ComboBox(self)
        self.group_combo.setFixedHeight(32)  # 设置固定高度
        
        # 使用空图标解决QFluentWidgets ComboBox的图标访问问题

        self.group_combo.addItem("无分组" )  # 默认选项，ID为-1表示不分配到任何分组
        
        right_layout.addWidget(self.group_combo)
        
        # 在右侧添加底部按钮，添加弹性空间使其靠下显示
        right_layout.addStretch(1)
        
        btn_layout = QHBoxLayout()
        self.cancel_btn = PushButton('取消')
        self.login_btn = PrimaryPushButton('登录')
 
        
        # 排列按钮位置，确保登录按钮靠最右侧
        btn_layout.addStretch(1)  # 添加左侧弹性空间，将按钮推向右侧
        btn_layout.addWidget(self.cancel_btn)
        btn_layout.addSpacing(30)  # 按钮之间增加30像素的固定宽度
        btn_layout.addWidget(self.login_btn)
        # 没有右侧弹性空间，确保登录按钮靠最右
        
        right_layout.addLayout(btn_layout)
        
        # 添加左右区域到主布局
        content_layout.addWidget(left_widget, 1)
        content_layout.addWidget(right_widget, 1)
        self.main_layout.addLayout(content_layout)

        # 信号连接
        self.proxy_ip_radio.toggled.connect(self.on_proxy_ip_radio_toggled)
        self.ip_search.textChanged.connect(self.on_ip_search_changed)
    
    def on_proxy_ip_radio_toggled(self, checked):
        self.proxy_ip_widget.setVisible(checked)
        # 调整窗口大小
        self.adjustSize()
        # 确保窗口不会小于最小大小
        parent = self.parent()
        if parent and isinstance(parent, QDialog):
            if parent.width() < 700:
                parent.setMinimumWidth(700)
            if parent.height() < 500:
                parent.setMinimumHeight(500)
        # 发出信号
        self.proxy_toggled.emit(checked)
    
    def on_ip_search_changed(self, text):
        """IP搜索框文本改变时过滤IP列表"""
        for i in range(self.ip_list.count()):
            item = self.ip_list.item(i)
            if text.lower() in item.text().lower():
                item.setHidden(False)
            else:
                item.setHidden(True)
    
    def update_ip_list(self, ip_list):
        """更新IP池列表
        
        Args:
            ip_list: IP池数据列表，已按绑定账户数排序
        """
        # 清空现有列表
        self.ip_list.clear()
        
        if not ip_list:
            # 添加一个提示
            self.show_info("刷新失败","没有找到有效代理ip", "error")
            empty_item = QListWidgetItem("当前没有可用的代理IP，请前往代理管理页面添加")
            empty_item.setFlags(empty_item.flags() & ~Qt.ItemIsEnabled)  # 禁用选择
            self.ip_list.addItem(empty_item)
            return
        self.show_info("刷新成功",f"获取到{len(ip_list)}个代理IP", "success")
        # 添加所有IP项，已经按照绑定数量排序（从低到高）
        for ip_info in ip_list:
            ip = ip_info.get('ip', '')
            port = ip_info.get('port', '')
            account_count = ip_info.get('account_count', 0)
            
            # 创建显示文本，根据绑定数量显示不同文本
            if account_count == 0:
                display_text = f"{ip}:{port} [未绑定]"
            else:
                display_text = f"{ip}:{port} [已绑定: {account_count}]"
            
            # 创建列表项
            item = QListWidgetItem(display_text)
            item.setData(Qt.UserRole, ip_info)  # 存储完整的IP信息
            
            # 设置颜色和字体
            if account_count == 0:
                # 未绑定的显示绿色
                item.setForeground(Qt.darkGreen)
                # 加粗未绑定的选项
                font = item.font()
                font.setBold(True)
                item.setFont(font)
            elif account_count < 3:
                # 绑定少量的显示黑色
                item.setForeground(Qt.black)
            else:
                # 绑定多个的显示橙色
                item.setForeground(Qt.darkYellow)
            
            self.ip_list.addItem(item)
        
        # 默认选择第一个（绑定数量最少的）
        if self.ip_list.count() > 0:
            self.ip_list.setCurrentRow(0)

    def show_info(self, title, content, type='info'):
        """显示消息提示"""
        position = InfoBarPosition.TOP
        
        if type == 'success':
            InfoBar.success(
                title=title,
                content=content,
                orient=Qt.Horizontal,
                isClosable=True,
                position=position,
                duration=2000,
                parent=self
            )
        elif type == 'error':
            InfoBar.error(
                title=title,
                content=content,
                orient=Qt.Horizontal,
                isClosable=True,
                position=position,
                duration=3000,
                parent=self
            )
        else:  # 默认为 info
            InfoBar.info(
                title=title,
                content=content,
                orient=Qt.Horizontal,
                isClosable=True,
                position=position,
                duration=2000,
                parent=self
            )
    


if __name__ == '__main__':
    app = QApplication([])
    add_account_ui = AddAccountUI()
    add_account_ui.show()
    app.exec() 