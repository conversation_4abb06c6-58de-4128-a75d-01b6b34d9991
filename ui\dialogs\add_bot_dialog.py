from PySide6.QtCore import Qt, Signal
from PySide6.QtWidgets import <PERSON>D<PERSON>og, QVBoxLayout, QHBoxLayout, QFrame, QLabel
from qfluentwidgets import (LineEdit, PasswordLineEdit, PrimaryPushButton, PushButton, 
                          StrongBodyLabel, BodyLabel, CardWidget, InfoBar, InfoBarPosition,
                          FluentIcon as FIF)


class AddBotDialog(QDialog):
    """添加机器人对话框"""
    
    # 信号定义
    tokenVerified = Signal(bool, str)  # Token验证结果信号 (验证结果, 消息)
    botAdded = Signal(dict)            # 添加机器人信号 (机器人信息)
    
    def __init__(self, parent=None):
        super().__init__(parent=parent)
        self.setObjectName("AddBotDialog")
        self.setup_ui()
        self.connect_signals()

    def setup_ui(self):
        # 主布局
        self.mainLayout = QVBoxLayout(self)
        self.mainLayout.setContentsMargins(30, 20, 30, 20)
        self.mainLayout.setSpacing(15)
        
        # 标题
        self.titleLabel = StrongBodyLabel("添加 Telegram 机器人", self)
        self.titleLabel.setAlignment(Qt.AlignCenter)
        
        # 输入区域卡片
        self.inputCard = CardWidget(self)
        self.inputLayout = QVBoxLayout(self.inputCard)
        self.inputLayout.setSpacing(10)
        
        # Token 输入行
        self.tokenLayout = QHBoxLayout()
        self.tokenLabel = BodyLabel("机器人 Token:", self)
        self.tokenInput = LineEdit(self)
        self.tokenInput.setPlaceholderText("请输入机器人的 Token")
        self.tokenInput.setClearButtonEnabled(True)
        self.verifyButton = PrimaryPushButton("验证 Token", self, FIF.ACCEPT)
        self.tokenLayout.addWidget(self.tokenLabel)
        self.tokenLayout.addWidget(self.tokenInput, 1) # 可伸展
        self.tokenLayout.addWidget(self.verifyButton)
        
        # 用户名输入行
        self.usernameLayout = QHBoxLayout()
        self.usernameLabel = BodyLabel("机器人用户名:", self)
        self.usernameInput = LineEdit(self)
        self.usernameInput.setPlaceholderText("例如：@MyAwesomeBot")
        self.usernameInput.setClearButtonEnabled(True)
        self.usernameLayout.addWidget(self.usernameLabel)
        self.usernameLayout.addWidget(self.usernameInput, 1)
        
        # 更新方式显示
        self.updateMethodLayout = QHBoxLayout()
        self.updateMethodLabel = BodyLabel("更新方式:", self)
        self.updateMethodValue = StrongBodyLabel("Webhook (默认且唯一)", self)
        self.updateMethodLayout.addWidget(self.updateMethodLabel)
        self.updateMethodLayout.addWidget(self.updateMethodValue, 1)
        
        # 将输入行添加到卡片布局
        self.inputLayout.addLayout(self.tokenLayout)
        self.inputLayout.addLayout(self.usernameLayout)
        self.inputLayout.addLayout(self.updateMethodLayout)
        
        # 底部按钮区域
        self.bottomLayout = QHBoxLayout()
        self.bottomLayout.addStretch(1) # 右对齐
        self.cancelButton = PushButton("取消", self)
        self.confirmButton = PrimaryPushButton("确认添加", self)
        self.confirmButton.setEnabled(False) # 初始禁用，验证通过后启用
        self.bottomLayout.addWidget(self.cancelButton)
        self.bottomLayout.addWidget(self.confirmButton)
        
        # 将各部分添加到主布局
        self.mainLayout.addWidget(self.titleLabel)
        self.mainLayout.addWidget(self.inputCard)
        self.mainLayout.addLayout(self.bottomLayout)
        
        # 设置样式
        self.setStyleSheet("""
            #AddBotDialog {
                background-color: rgb(249, 249, 249);
            }
            CardWidget {
                padding: 15px;
            }
            BodyLabel {
                min-width: 80px; /* 标签宽度对齐 */
            }
        """)

    def connect_signals(self):
        self.verifyButton.clicked.connect(self.on_verify_token)
        self.confirmButton.clicked.connect(self.on_confirm_add)
        # self.cancelButton 通常连接到 dialog.reject() 或 window.close()
        
        # Token 输入变化时，重置确认按钮状态
        self.tokenInput.textChanged.connect(lambda: self.confirmButton.setEnabled(False))

    def on_verify_token(self):
        token = self.tokenInput.text().strip()
        if not token:
            InfoBar.warning(
                title='提示',
                content="请输入机器人 Token",
                orient=Qt.Horizontal,
                isClosable=True,
                position=InfoBarPosition.TOP,
                duration=2000,
                parent=self
            )
            return
            
        # --- 实际验证逻辑 ---
        # 这里应该是调用后端服务或 Telegram API 来验证 Token
        # 假设验证成功
        is_valid = True 
        message = "Token 验证成功！" 
        
        # 发射信号
        self.tokenVerified.emit(is_valid, message)
        
        # 处理验证结果
        if is_valid:
            InfoBar.success(
                title='成功',
                content=message,
                orient=Qt.Horizontal,
                isClosable=True,
                position=InfoBarPosition.TOP,
                duration=2000,
                parent=self
            )
            self.confirmButton.setEnabled(True) # 验证成功后启用确认按钮
            self.tokenInput.setEnabled(False) # 验证成功后禁用Token输入
            self.verifyButton.setEnabled(False) # 验证成功后禁用验证按钮
        else:
            InfoBar.error(
                title='失败',
                content=message,
                orient=Qt.Horizontal,
                isClosable=True,
                position=InfoBarPosition.TOP,
                duration=3000,
                parent=self
            )
            self.confirmButton.setEnabled(False)

    def on_confirm_add(self):
        token = self.tokenInput.text().strip()
        username = self.usernameInput.text().strip()
        
        if not username:
            InfoBar.warning(
                title='提示',
                content="请输入机器人用户名",
                orient=Qt.Horizontal,
                isClosable=True,
                position=InfoBarPosition.TOP,
                duration=2000,
                parent=self
            )
            return
            
        bot_info = {
            "token": token,
            "username": username,
            "update_method": "webhook"
        }
        
        # 发射添加信号
        self.botAdded.emit(bot_info)
        
        # --- 可以在这里关闭对话框 ---
        # self.parent().close() 或 self.window().close()

# --- 用于独立测试运行 ---
if __name__ == '__main__':
    import sys
    from PySide6.QtWidgets import QApplication, QMainWindow

    app = QApplication(sys.argv)
    
    # 创建一个主窗口来容纳对话框
    mainWindow = QMainWindow()
    mainWindow.setWindowTitle("添加机器人测试")
    mainWindow.resize(500, 300)
    
    addBotWidget = AddBotDialog()
    
    # 连接信号示例
    addBotWidget.tokenVerified.connect(lambda valid, msg: print(f"Token Verified: {valid}, Message: {msg}"))
    addBotWidget.botAdded.connect(lambda info: print(f"Bot Added: {info}"))
    addBotWidget.cancelButton.clicked.connect(mainWindow.close) # 取消按钮关闭窗口
    
    mainWindow.setCentralWidget(addBotWidget)
    mainWindow.show()
    
    sys.exit(app.exec())
