import sys
from PySide6.QtCore import Qt, Signal
from PySide6.QtWidgets import (QWidget, QHBoxLayout, QVBoxLayout, QApplication, 
                             QMainWindow, QListWidgetItem, QTableWidgetItem, QHeaderView)
from qfluentwidgets import (ListWidget, CardWidget, TitleLabel, TextEdit, RadioButton, 
                          PrimaryPushButton, TableWidget, StrongBodyLabel, BodyLabel,
                          PushButton, InfoBar, InfoBarPosition, FluentIcon as FIF,
                          TransparentToolButton, CheckBox)

# 假设的机器人数据结构
MOCK_BOTS = [
    {"id": "bot_1", "username": "@MyAwesomeBot", "token": "123..."},
    {"id": "bot_2", "username": "@AnotherCoolBot", "token": "456..."},
    {"id": "bot_3", "username": "@UtilityMasterBot", "token": "789..."},
]

# 假设的关联数据结构 {bot_id: [(entity_id, entity_type), ...]}
MOCK_ASSOCIATIONS = {
    "bot_1": [("group_123", "群组"), ("@channel_abc", "频道")],
    "bot_2": [("user_xyz", "个人")],
}


class AddGroupBotPage(QWidget):
    """添加机器人与群组/频道/个人关联的页面"""

    # 信号：当为一个机器人保存关联时发出 (bot_id, list_of_associations)
    # list_of_associations 的格式为: [(entity_id, entity_type), ...]
    associationsSaved = Signal(str, list)

    def __init__(self, parent=None):
        super().__init__(parent=parent)
        self.setObjectName("AddGroupBotPage")
        
        self.current_bot_id = None # 当前选中的机器人ID
        # 存储每个机器人原始的关联数据，用于判断是否有修改
        self.original_associations = {} 
        
        self.setup_ui()
        self.connect_signals()
        self.load_bots() # 加载机器人列表

    def setup_ui(self):
        # 主布局
        self.mainLayout = QHBoxLayout(self)
        self.mainLayout.setContentsMargins(20, 20, 20, 20)
        self.mainLayout.setSpacing(20)

        # --- 左侧机器人列表 ---
        self.leftCard = CardWidget(self)
        self.leftLayout = QVBoxLayout(self.leftCard)
        self.leftLayout.setContentsMargins(10, 10, 10, 10)
        self.leftLayout.setSpacing(10)
        
        self.botListTitle = TitleLabel("选择机器人", self)
        self.botListWidget = ListWidget(self)

        self.leftLayout.addWidget(self.botListTitle)
        self.leftLayout.addWidget(self.botListWidget)
        self.leftCard.setFixedWidth(250)

        # --- 右侧关联管理 ---
        self.rightCard = CardWidget(self)
        self.rightLayout = QVBoxLayout(self.rightCard)
        self.rightLayout.setContentsMargins(15, 15, 15, 15)
        self.rightLayout.setSpacing(15)

        self.rightTitle = TitleLabel("添加关联: 群组 / 频道 / 个人", self)

        # 输入区域
        self.inputAreaLayout = QVBoxLayout()
        self.inputAreaLayout.setSpacing(10)
        
        self.entityInputLabel = BodyLabel("输入 ID 或用户名 (每行一个):", self)
        self.entityInput = TextEdit(self)
        self.entityInput.setPlaceholderText("例如:\n-1001234567890 (群组/频道 ID)\n@my_channel (频道用户名)\n123456789 (用户 ID)")
        self.entityInput.setFixedHeight(100)

        self.typeSelectionLayout = QHBoxLayout()
        self.typeLabel = BodyLabel("类型:", self)
        self.groupRadioButton = RadioButton("群组", self)
        self.channelRadioButton = RadioButton("频道", self)
        self.individualRadioButton = RadioButton("个人", self)
        self.groupRadioButton.setChecked(True) # 默认选中群组
        
        self.typeSelectionLayout.addWidget(self.typeLabel)
        self.typeSelectionLayout.addWidget(self.groupRadioButton)
        self.typeSelectionLayout.addWidget(self.channelRadioButton)
        self.typeSelectionLayout.addWidget(self.individualRadioButton)
        self.typeSelectionLayout.addStretch(1)

        self.addButton = PrimaryPushButton("添加到列表", self, FIF.ADD)
        
        self.inputAreaLayout.addWidget(self.entityInputLabel)
        self.inputAreaLayout.addWidget(self.entityInput)
        self.inputAreaLayout.addLayout(self.typeSelectionLayout)
        self.inputAreaLayout.addWidget(self.addButton, 0, Qt.AlignRight) # 按钮右对齐

        # 待保存列表区域
        self.tableAreaLayout = QVBoxLayout()
        self.tableAreaLayout.setSpacing(10)
        
        self.tableTitle = StrongBodyLabel("待保存列表 (当前机器人)", self)
        self.associationTable = TableWidget(self)
        self.associationTable.setColumnCount(3)
        self.associationTable.setHorizontalHeaderLabels(["ID / 用户名", "类型", "操作"])
        self.associationTable.horizontalHeader().setSectionResizeMode(0, QHeaderView.ResizeMode.Stretch)
        self.associationTable.horizontalHeader().setSectionResizeMode(1, QHeaderView.ResizeMode.ResizeToContents)
        self.associationTable.horizontalHeader().setSectionResizeMode(2, QHeaderView.ResizeMode.ResizeToContents)
        self.associationTable.setMinimumHeight(200) # 给表格一个最小高度

        self.tableAreaLayout.addWidget(self.tableTitle)
        self.tableAreaLayout.addWidget(self.associationTable)

        # 保存按钮区域
        self.saveButtonLayout = QHBoxLayout()
        self.saveButton = PrimaryPushButton("保存更改", self, FIF.SAVE)
        self.saveButton.setEnabled(False) # 初始禁用
        self.saveButtonLayout.addStretch(1)
        self.saveButtonLayout.addWidget(self.saveButton)
        
        # 将各部分添加到右侧布局
        self.rightLayout.addWidget(self.rightTitle)
        self.rightLayout.addLayout(self.inputAreaLayout)
        self.rightLayout.addLayout(self.tableAreaLayout, 1) # 表格区域可伸展
        self.rightLayout.addLayout(self.saveButtonLayout)

        # 将左右卡片添加到主布局
        self.mainLayout.addWidget(self.leftCard)
        self.mainLayout.addWidget(self.rightCard, 1) # 右侧区域可伸展

        # 初始时禁用右侧所有控件
        self.rightCard.setEnabled(False)


    def connect_signals(self):
        self.botListWidget.currentItemChanged.connect(self.on_bot_selected)
        self.addButton.clicked.connect(self.on_add_entity_to_table)
        self.saveButton.clicked.connect(self.on_save_changes)
        # 表格内容变化时（添加或移除）检查是否需要启用保存按钮
        self.associationTable.model().rowsInserted.connect(self.check_save_button_state)
        self.associationTable.model().rowsRemoved.connect(self.check_save_button_state)

    def load_bots(self):
        """加载机器人列表 (模拟)"""
        self.botListWidget.clear()
        # 实际应用中，这里应从数据库或配置加载机器人
        for bot in MOCK_BOTS:
            item = QListWidgetItem(f"{bot['username']} ({bot['id']})")
            item.setData(Qt.UserRole, bot) # 将完整机器人信息存储在item中
            self.botListWidget.addItem(item)

    def on_bot_selected(self, current_item: QListWidgetItem, previous_item: QListWidgetItem):
        """当选择的机器人变化时"""
        if not current_item:
            self.current_bot_id = None
            self.rightCard.setEnabled(False)
            self.associationTable.setRowCount(0) # 清空表格
            self.saveButton.setEnabled(False)
            return

        bot_data = current_item.data(Qt.UserRole)
        self.current_bot_id = bot_data['id']
        self.rightCard.setEnabled(True) # 启用右侧控件
        
        # 加载当前机器人的关联 (模拟)
        self.load_associations_for_bot(self.current_bot_id)
        self.check_save_button_state() # 检查初始状态是否需要保存

    def load_associations_for_bot(self, bot_id):
        """加载指定机器人的关联项到表格 (模拟)"""
        self.associationTable.setRowCount(0) # 清空现有表格
        
        # 实际应用中从数据库加载
        associations = MOCK_ASSOCIATIONS.get(bot_id, [])
        
        # 存储原始数据，用于对比是否有修改
        self.original_associations[bot_id] = set((entity_id, entity_type) for entity_id, entity_type in associations)
        
        for entity_id, entity_type in associations:
            self._add_row_to_table(entity_id, entity_type)
            
        self.saveButton.setEnabled(False) # 加载完成后，默认未修改，禁用保存按钮

    def on_add_entity_to_table(self):
        """将输入框中的实体添加到待保存列表"""
        if not self.current_bot_id:
            return

        entity_text = self.entityInput.toPlainText().strip()
        if not entity_text:
            InfoBar.warning("提示", "请输入要添加的 ID 或用户名", parent=self, position=InfoBarPosition.TOP, duration=2000)
            return

        # 获取选中的类型
        selected_type = ""
        if self.groupRadioButton.isChecked():
            selected_type = "群组"
        elif self.channelRadioButton.isChecked():
            selected_type = "频道"
        elif self.individualRadioButton.isChecked():
            selected_type = "个人"
        else:
             InfoBar.warning("提示", "请选择实体类型", parent=self, position=InfoBarPosition.TOP, duration=2000)
             return
        
        added_count = 0
        lines = entity_text.splitlines()
        current_table_items = set()
        for row in range(self.associationTable.rowCount()):
             item_id = self.associationTable.item(row, 0).text()
             item_type = self.associationTable.item(row, 1).text()
             current_table_items.add((item_id, item_type))

        for line in lines:
            entity_id = line.strip()
            if entity_id:
                # 检查是否已在表格中
                if (entity_id, selected_type) not in current_table_items:
                   self._add_row_to_table(entity_id, selected_type)
                   added_count += 1
                   current_table_items.add((entity_id, selected_type)) # 更新检查集合
                else:
                    print(f"跳过重复项: {entity_id} ({selected_type})") # 可以换成 InfoBar 提示
        
        if added_count > 0:
            self.entityInput.clear() # 清空输入框
            InfoBar.success("成功", f"已添加 {added_count} 个实体到待保存列表", parent=self, position=InfoBarPosition.TOP, duration=1500)
        elif lines: # 有输入但没添加成功（全是重复）
             InfoBar.warning("提示", "输入的实体已在列表中", parent=self, position=InfoBarPosition.TOP, duration=2000)


    def _add_row_to_table(self, entity_id, entity_type):
        """向表格添加一行数据"""
        row_count = self.associationTable.rowCount()
        self.associationTable.insertRow(row_count)

        id_item = QTableWidgetItem(entity_id)
        type_item = QTableWidgetItem(entity_type)
        
        # 创建移除按钮
        remove_button = TransparentToolButton(FIF.REMOVE, self)
        remove_button.setToolTip("从列表中移除")
        # 使用 lambda 捕获当前行号
        remove_button.clicked.connect(lambda checked=False, r=row_count: self.on_remove_entity_from_table(r))

        self.associationTable.setItem(row_count, 0, id_item)
        self.associationTable.setItem(row_count, 1, type_item)
        self.associationTable.setCellWidget(row_count, 2, remove_button)

    def on_remove_entity_from_table(self, row):
        """从表格中移除指定行"""
        # 注意：由于删除会导致行号变化，直接使用传入的行号可能不准确
        # 更好的方法是找到按钮所在的行
        button = self.sender() # 获取发送信号的按钮
        if button:
            index = self.associationTable.indexAt(button.pos())
            if index.isValid():
                self.associationTable.removeRow(index.row())
            else: # Fallback if indexAt fails (less likely with direct connection)
                 print(f"无法找到按钮位置对应的行，尝试使用原始行号: {row}")
                 if row < self.associationTable.rowCount():
                      self.associationTable.removeRow(row)
        else: # Fallback if sender is None
            print(f"无法获取发送信号的按钮，尝试使用原始行号: {row}")
            if row < self.associationTable.rowCount():
                 self.associationTable.removeRow(row)


    def check_save_button_state(self):
        """检查当前表格内容与原始数据比较，决定是否启用保存按钮"""
        if not self.current_bot_id:
            self.saveButton.setEnabled(False)
            return

        current_table_items = set()
        for row in range(self.associationTable.rowCount()):
             # 添加检查确保 item 不是 None
             id_item = self.associationTable.item(row, 0)
             type_item = self.associationTable.item(row, 1)
             if id_item and type_item:
                 item_id = id_item.text()
                 item_type = type_item.text()
                 current_table_items.add((item_id, item_type))
             else:
                 print(f"警告: 在检查保存状态时，行 {row} 的单元格为空。") # 添加调试信息
             
        original_items = self.original_associations.get(self.current_bot_id, set())
        
        # 如果当前表格内容与原始数据不同，则启用保存按钮
        if current_table_items != original_items:
            self.saveButton.setEnabled(True)
        else:
            self.saveButton.setEnabled(False)


    def on_save_changes(self):
        """保存当前表格中的关联项"""
        if not self.current_bot_id:
            return

        current_associations = []
        for row in range(self.associationTable.rowCount()):
            entity_id = self.associationTable.item(row, 0).text()
            entity_type = self.associationTable.item(row, 1).text()
            current_associations.append((entity_id, entity_type))

        # --- 这里执行实际的保存逻辑 ---
        # 例如，调用后端 API 或更新数据库
        print(f"保存机器人 {self.current_bot_id} 的关联: {current_associations}")
        
        # 发射信号
        self.associationsSaved.emit(self.current_bot_id, current_associations)

        # 更新原始数据状态
        self.original_associations[self.current_bot_id] = set(current_associations)
        
        # 更新模拟数据 (仅用于演示)
        MOCK_ASSOCIATIONS[self.current_bot_id] = current_associations

        # 显示成功消息
        InfoBar.success("成功", f"已保存机器人 {self.current_bot_id} 的关联设置", parent=self, position=InfoBarPosition.TOP, duration=2000)
        
        # 保存后禁用按钮
        self.saveButton.setEnabled(False)


# --- 用于独立测试运行 ---
if __name__ == '__main__':
    app = QApplication(sys.argv)
    
    mainWindow = QMainWindow()
    mainWindow.setWindowTitle("关联机器人与群组/频道/个人")
    mainWindow.resize(900, 600)
    
    page = AddGroupBotPage()
    
    # 连接信号示例
    page.associationsSaved.connect(lambda bot_id, assoc: print(f"信号接收: Bot {bot_id} Saved Associations: {assoc}"))
    
    mainWindow.setCentralWidget(page)
    mainWindow.show()
    
    sys.exit(app.exec())