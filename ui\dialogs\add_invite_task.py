# -*- coding: utf-8 -*-

################################################################################
## Form generated from reading UI file 'invite_task_new.ui'
##
## Created by: Qt User Interface Compiler version 6.9.0
##
## WARNING! All changes made in this file will be lost when recompiling UI file!
################################################################################

from PySide6.QtCore import (QCoreApplication, QDate, QDateTime, QLocale,
    QMetaObject, QObject, QPoint, QRect,
    QSize, QTime, QUrl, Qt)
from PySide6.QtGui import (QBrush, QColor, QC<PERSON>alGradient, QCursor,
    QF<PERSON>, QFontDatabase, QGradient, QIcon,
    QImage, QKeySequence, QLinearGradient, QPainter,
    QPalette, QPixmap, QRadialGradient, QTransform)
from PySide6.QtWidgets import (QApplication, QFrame, QHBoxLayout, QListWidgetItem,
    QSizePolicy, QSpacerItem, QVBoxLayout, QWidget)

from qfluentwidgets import (BodyLabel, CaptionLabel, CardWidget, CheckBox,
    ComboBox, LineEdit, ListWidget, PlainTextEdit,
    PrimaryPushButton, PushButton, RadioButton, TextEdit,
    ToggleButton)

class Ui_Form(object):
    def setupUi(self, Form):
        if not Form.objectName():
            Form.setObjectName(u"Form")
        Form.resize(750, 500)
        self.verticalLayout_4 = QVBoxLayout(Form)
        self.verticalLayout_4.setObjectName(u"verticalLayout_4")
        self.horizontalLayout = QHBoxLayout()
        self.horizontalLayout.setObjectName(u"horizontalLayout")
        self.BodyLabel = BodyLabel(Form)
        self.BodyLabel.setObjectName(u"BodyLabel")
        sizePolicy = QSizePolicy(QSizePolicy.Policy.Fixed, QSizePolicy.Policy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.BodyLabel.sizePolicy().hasHeightForWidth())
        self.BodyLabel.setSizePolicy(sizePolicy)

        self.horizontalLayout.addWidget(self.BodyLabel)

        self.TaskName = LineEdit(Form)
        self.TaskName.setObjectName(u"TaskName")

        self.horizontalLayout.addWidget(self.TaskName)


        self.verticalLayout_4.addLayout(self.horizontalLayout)

        self.horizontalLayout_9 = QHBoxLayout()
        self.horizontalLayout_9.setObjectName(u"horizontalLayout_9")
        self.CardWidget = CardWidget(Form)
        self.CardWidget.setObjectName(u"CardWidget")
        sizePolicy1 = QSizePolicy(QSizePolicy.Policy.Preferred, QSizePolicy.Policy.Preferred)
        sizePolicy1.setHorizontalStretch(0)
        sizePolicy1.setVerticalStretch(0)
        sizePolicy1.setHeightForWidth(self.CardWidget.sizePolicy().hasHeightForWidth())
        self.CardWidget.setSizePolicy(sizePolicy1)
        self.verticalLayout = QVBoxLayout(self.CardWidget)
        self.verticalLayout.setObjectName(u"verticalLayout")
        self.horizontalLayout_2 = QHBoxLayout()
        self.horizontalLayout_2.setObjectName(u"horizontalLayout_2")
        self.CaptionLabel = CaptionLabel(self.CardWidget)
        self.CaptionLabel.setObjectName(u"CaptionLabel")
        sizePolicy.setHeightForWidth(self.CaptionLabel.sizePolicy().hasHeightForWidth())
        self.CaptionLabel.setSizePolicy(sizePolicy)

        self.horizontalLayout_2.addWidget(self.CaptionLabel)

        self.GroupName = LineEdit(self.CardWidget)
        self.GroupName.setObjectName(u"GroupName")

        self.horizontalLayout_2.addWidget(self.GroupName)


        self.verticalLayout.addLayout(self.horizontalLayout_2)

        self.horizontalLayout_3 = QHBoxLayout()
        self.horizontalLayout_3.setObjectName(u"horizontalLayout_3")
        self.CaptionLabel_2 = CaptionLabel(self.CardWidget)
        self.CaptionLabel_2.setObjectName(u"CaptionLabel_2")
        sizePolicy.setHeightForWidth(self.CaptionLabel_2.sizePolicy().hasHeightForWidth())
        self.CaptionLabel_2.setSizePolicy(sizePolicy)

        self.horizontalLayout_3.addWidget(self.CaptionLabel_2)

        self.InviteLink = LineEdit(self.CardWidget)
        self.InviteLink.setObjectName(u"InviteLink")

        self.horizontalLayout_3.addWidget(self.InviteLink)


        self.verticalLayout.addLayout(self.horizontalLayout_3)

        self.horizontalLayout_5 = QHBoxLayout()
        self.horizontalLayout_5.setObjectName(u"horizontalLayout_5")
        self.CaptionLabel_3 = CaptionLabel(self.CardWidget)
        self.CaptionLabel_3.setObjectName(u"CaptionLabel_3")
        sizePolicy.setHeightForWidth(self.CaptionLabel_3.sizePolicy().hasHeightForWidth())
        self.CaptionLabel_3.setSizePolicy(sizePolicy)

        self.horizontalLayout_5.addWidget(self.CaptionLabel_3)

        self.LineEdit_4 = LineEdit(self.CardWidget)
        self.LineEdit_4.setObjectName(u"LineEdit_4")

        self.horizontalLayout_5.addWidget(self.LineEdit_4)

        self.CaptionLabel_5 = CaptionLabel(self.CardWidget)
        self.CaptionLabel_5.setObjectName(u"CaptionLabel_5")
        sizePolicy.setHeightForWidth(self.CaptionLabel_5.sizePolicy().hasHeightForWidth())
        self.CaptionLabel_5.setSizePolicy(sizePolicy)

        self.horizontalLayout_5.addWidget(self.CaptionLabel_5)

        self.LineEdit_5 = LineEdit(self.CardWidget)
        self.LineEdit_5.setObjectName(u"LineEdit_5")

        self.horizontalLayout_5.addWidget(self.LineEdit_5)


        self.verticalLayout.addLayout(self.horizontalLayout_5)

        self.horizontalLayout_4 = QHBoxLayout()
        self.horizontalLayout_4.setObjectName(u"horizontalLayout_4")
        self.CaptionLabel_4 = CaptionLabel(self.CardWidget)
        self.CaptionLabel_4.setObjectName(u"CaptionLabel_4")
        sizePolicy.setHeightForWidth(self.CaptionLabel_4.sizePolicy().hasHeightForWidth())
        self.CaptionLabel_4.setSizePolicy(sizePolicy)

        self.horizontalLayout_4.addWidget(self.CaptionLabel_4)

        self.LineEdit_6 = LineEdit(self.CardWidget)
        self.LineEdit_6.setObjectName(u"LineEdit_6")

        self.horizontalLayout_4.addWidget(self.LineEdit_6)

        self.CaptionLabel_7 = CaptionLabel(self.CardWidget)
        self.CaptionLabel_7.setObjectName(u"CaptionLabel_7")
        sizePolicy.setHeightForWidth(self.CaptionLabel_7.sizePolicy().hasHeightForWidth())
        self.CaptionLabel_7.setSizePolicy(sizePolicy)

        self.horizontalLayout_4.addWidget(self.CaptionLabel_7)

        self.LineEdit_7 = LineEdit(self.CardWidget)
        self.LineEdit_7.setObjectName(u"LineEdit_7")

        self.horizontalLayout_4.addWidget(self.LineEdit_7)


        self.verticalLayout.addLayout(self.horizontalLayout_4)

        self.horizontalLayout_6 = QHBoxLayout()
        self.horizontalLayout_6.setObjectName(u"horizontalLayout_6")
        self.CaptionLabel_6 = CaptionLabel(self.CardWidget)
        self.CaptionLabel_6.setObjectName(u"CaptionLabel_6")
        sizePolicy.setHeightForWidth(self.CaptionLabel_6.sizePolicy().hasHeightForWidth())
        self.CaptionLabel_6.setSizePolicy(sizePolicy)

        self.horizontalLayout_6.addWidget(self.CaptionLabel_6)

        self.RadioButton_invite = RadioButton(self.CardWidget)
        self.RadioButton_invite.setObjectName(u"RadioButton_invite")

        self.horizontalLayout_6.addWidget(self.RadioButton_invite)

        self.RadioButton_link = RadioButton(self.CardWidget)
        self.RadioButton_link.setObjectName(u"RadioButton_link")

        self.horizontalLayout_6.addWidget(self.RadioButton_link)


        self.verticalLayout.addLayout(self.horizontalLayout_6)


        self.horizontalLayout_9.addWidget(self.CardWidget)

        self.CardWidget_2 = CardWidget(Form)
        self.CardWidget_2.setObjectName(u"CardWidget_2")
        sizePolicy2 = QSizePolicy(QSizePolicy.Policy.Preferred, QSizePolicy.Policy.Fixed)
        sizePolicy2.setHorizontalStretch(0)
        sizePolicy2.setVerticalStretch(0)
        sizePolicy2.setHeightForWidth(self.CardWidget_2.sizePolicy().hasHeightForWidth())
        self.CardWidget_2.setSizePolicy(sizePolicy2)
        self.verticalLayout_2 = QVBoxLayout(self.CardWidget_2)
        self.verticalLayout_2.setObjectName(u"verticalLayout_2")
        self.CaptionLabel_8 = CaptionLabel(self.CardWidget_2)
        self.CaptionLabel_8.setObjectName(u"CaptionLabel_8")
        sizePolicy.setHeightForWidth(self.CaptionLabel_8.sizePolicy().hasHeightForWidth())
        self.CaptionLabel_8.setSizePolicy(sizePolicy)

        self.verticalLayout_2.addWidget(self.CaptionLabel_8)

        self.InviteMsg = TextEdit(self.CardWidget_2)
        self.InviteMsg.setObjectName(u"InviteMsg")

        self.verticalLayout_2.addWidget(self.InviteMsg)

        self.horizontalLayout_7 = QHBoxLayout()
        self.horizontalLayout_7.setObjectName(u"horizontalLayout_7")
        self.CaptionLabel_9 = CaptionLabel(self.CardWidget_2)
        self.CaptionLabel_9.setObjectName(u"CaptionLabel_9")
        sizePolicy.setHeightForWidth(self.CaptionLabel_9.sizePolicy().hasHeightForWidth())
        self.CaptionLabel_9.setSizePolicy(sizePolicy)

        self.horizontalLayout_7.addWidget(self.CaptionLabel_9)

        self.ToggleButton = ToggleButton(self.CardWidget_2)
        self.ToggleButton.setObjectName(u"ToggleButton")

        self.horizontalLayout_7.addWidget(self.ToggleButton)

        self.ToggleButton_friend = ToggleButton(self.CardWidget_2)
        self.ToggleButton_friend.setObjectName(u"ToggleButton_friend")

        self.horizontalLayout_7.addWidget(self.ToggleButton_friend)

        self.ToggleButton_activity = ToggleButton(self.CardWidget_2)
        self.ToggleButton_activity.setObjectName(u"ToggleButton_activity")

        self.horizontalLayout_7.addWidget(self.ToggleButton_activity)


        self.verticalLayout_2.addLayout(self.horizontalLayout_7)


        self.horizontalLayout_9.addWidget(self.CardWidget_2)


        self.verticalLayout_4.addLayout(self.horizontalLayout_9)

        self.horizontalLayout_8 = QHBoxLayout()
        self.horizontalLayout_8.setObjectName(u"horizontalLayout_8")
        self.CardWidget_3 = CardWidget(Form)
        self.CardWidget_3.setObjectName(u"CardWidget_3")
        self.verticalLayout_3 = QVBoxLayout(self.CardWidget_3)
        self.verticalLayout_3.setObjectName(u"verticalLayout_3")
        self.horizontalLayout_11 = QHBoxLayout()
        self.horizontalLayout_11.setObjectName(u"horizontalLayout_11")
        self.CaptionLabel_10 = CaptionLabel(self.CardWidget_3)
        self.CaptionLabel_10.setObjectName(u"CaptionLabel_10")
        sizePolicy.setHeightForWidth(self.CaptionLabel_10.sizePolicy().hasHeightForWidth())
        self.CaptionLabel_10.setSizePolicy(sizePolicy)

        self.horizontalLayout_11.addWidget(self.CaptionLabel_10)

        self.CustomUser = RadioButton(self.CardWidget_3)
        self.CustomUser.setObjectName(u"CustomUser")

        self.horizontalLayout_11.addWidget(self.CustomUser)

        self.Collectusers = RadioButton(self.CardWidget_3)
        self.Collectusers.setObjectName(u"Collectusers")

        self.horizontalLayout_11.addWidget(self.Collectusers)

        self.ImportUser = PushButton(self.CardWidget_3)
        self.ImportUser.setObjectName(u"ImportUser")

        self.horizontalLayout_11.addWidget(self.ImportUser)

        self.horizontalSpacer = QSpacerItem(40, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.horizontalLayout_11.addItem(self.horizontalSpacer)


        self.verticalLayout_3.addLayout(self.horizontalLayout_11)

        self.PlainTextEdit = PlainTextEdit(self.CardWidget_3)
        self.PlainTextEdit.setObjectName(u"PlainTextEdit")

        self.verticalLayout_3.addWidget(self.PlainTextEdit)

        self.frame = QFrame(self.CardWidget_3)
        self.frame.setObjectName(u"frame")
        self.frame.setFrameShape(QFrame.StyledPanel)
        self.frame.setFrameShadow(QFrame.Raised)
        self.verticalLayout_5 = QVBoxLayout(self.frame)
        self.verticalLayout_5.setObjectName(u"verticalLayout_5")
        self.CaptionLabel_11 = CaptionLabel(self.frame)
        self.CaptionLabel_11.setObjectName(u"CaptionLabel_11")

        self.verticalLayout_5.addWidget(self.CaptionLabel_11)

        self.ListWidget = ListWidget(self.frame)
        QListWidgetItem(self.ListWidget)
        QListWidgetItem(self.ListWidget)
        QListWidgetItem(self.ListWidget)
        self.ListWidget.setObjectName(u"ListWidget")

        self.verticalLayout_5.addWidget(self.ListWidget)

        self.CheckBox = CheckBox(self.frame)
        self.CheckBox.setObjectName(u"CheckBox")

        self.verticalLayout_5.addWidget(self.CheckBox)


        self.verticalLayout_3.addWidget(self.frame)


        self.horizontalLayout_8.addWidget(self.CardWidget_3)

        self.CardWidget_4 = CardWidget(Form)
        self.CardWidget_4.setObjectName(u"CardWidget_4")
        self.verticalLayout_6 = QVBoxLayout(self.CardWidget_4)
        self.verticalLayout_6.setObjectName(u"verticalLayout_6")
        self.horizontalLayout_12 = QHBoxLayout()
        self.horizontalLayout_12.setObjectName(u"horizontalLayout_12")
        self.CaptionLabel_12 = CaptionLabel(self.CardWidget_4)
        self.CaptionLabel_12.setObjectName(u"CaptionLabel_12")
        sizePolicy.setHeightForWidth(self.CaptionLabel_12.sizePolicy().hasHeightForWidth())
        self.CaptionLabel_12.setSizePolicy(sizePolicy)

        self.horizontalLayout_12.addWidget(self.CaptionLabel_12)

        self.AccountBox = ComboBox(self.CardWidget_4)
        self.AccountBox.setObjectName(u"AccountBox")

        self.horizontalLayout_12.addWidget(self.AccountBox)


        self.verticalLayout_6.addLayout(self.horizontalLayout_12)

        self.AccountsListWidget = ListWidget(self.CardWidget_4)
        QListWidgetItem(self.AccountsListWidget)
        QListWidgetItem(self.AccountsListWidget)
        QListWidgetItem(self.AccountsListWidget)
        QListWidgetItem(self.AccountsListWidget)
        self.AccountsListWidget.setObjectName(u"AccountsListWidget")

        self.verticalLayout_6.addWidget(self.AccountsListWidget)

        self.CheckBoxAccounts = CheckBox(self.CardWidget_4)
        self.CheckBoxAccounts.setObjectName(u"CheckBoxAccounts")

        self.verticalLayout_6.addWidget(self.CheckBoxAccounts)


        self.horizontalLayout_8.addWidget(self.CardWidget_4)


        self.verticalLayout_4.addLayout(self.horizontalLayout_8)

        self.CardWidget_5 = CardWidget(Form)
        self.CardWidget_5.setObjectName(u"CardWidget_5")
        self.horizontalLayout_10 = QHBoxLayout(self.CardWidget_5)
        self.horizontalLayout_10.setObjectName(u"horizontalLayout_10")
        self.horizontalSpacer_2 = QSpacerItem(40, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.horizontalLayout_10.addItem(self.horizontalSpacer_2)

        self.cancelBtn = PushButton(self.CardWidget_5)
        self.cancelBtn.setObjectName(u"cancelBtn")

        self.horizontalLayout_10.addWidget(self.cancelBtn)

        self.horizontalSpacer_3 = QSpacerItem(40, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.horizontalLayout_10.addItem(self.horizontalSpacer_3)

        self.SaveBtn = PrimaryPushButton(self.CardWidget_5)
        self.SaveBtn.setObjectName(u"SaveBtn")

        self.horizontalLayout_10.addWidget(self.SaveBtn)

        self.horizontalSpacer_7 = QSpacerItem(40, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.horizontalLayout_10.addItem(self.horizontalSpacer_7)


        self.verticalLayout_4.addWidget(self.CardWidget_5)


        self.retranslateUi(Form)

        QMetaObject.connectSlotsByName(Form)
    # setupUi

    def retranslateUi(self, Form):
        Form.setWindowTitle(QCoreApplication.translate("Form", u"Form", None))
        self.BodyLabel.setText(QCoreApplication.translate("Form", u"\u4efb\u52a1\u540d\uff1a", None))
        self.CaptionLabel.setText(QCoreApplication.translate("Form", u"\u7fa4\u7ec4\u6216\u9891\u9053\u7528\u6237\u540d", None))
        self.CaptionLabel_2.setText(QCoreApplication.translate("Form", u"\u6b63\u5f0f\u9080\u8bf7\u9080\u8bf7\u94fe\u63a5", None))
        self.CaptionLabel_3.setText(QCoreApplication.translate("Form", u"\u9080\u8bf7\u95f4\u9694", None))
        self.CaptionLabel_5.setText(QCoreApplication.translate("Form", u"~", None))
        self.CaptionLabel_4.setText(QCoreApplication.translate("Form", u"\u6bcf\u6b21\u9080\u8bf7\u6570", None))
        self.CaptionLabel_7.setText(QCoreApplication.translate("Form", u"~", None))
        self.CaptionLabel_6.setText(QCoreApplication.translate("Form", u"\u9080\u8bf7\u65b9\u5f0f", None))
        self.RadioButton_invite.setText(QCoreApplication.translate("Form", u"\u76f4\u63a5\u9080\u8bf7", None))
        self.RadioButton_link.setText(QCoreApplication.translate("Form", u"\u94fe\u63a5\u9080\u8bf7", None))
        self.CaptionLabel_8.setText(QCoreApplication.translate("Form", u"\u9080\u8bf7\u6d88\u606f", None))
        self.CaptionLabel_9.setText(QCoreApplication.translate("Form", u"\u5feb\u901f\u6a21\u677f", None))
        self.ToggleButton.setText(QCoreApplication.translate("Form", u"\u6b63\u5f0f\u9080\u8bf7", None))
        self.ToggleButton_friend.setText(QCoreApplication.translate("Form", u"\u597d\u53cb\u9080\u8bf7", None))
        self.ToggleButton_activity.setText(QCoreApplication.translate("Form", u"\u6d3b\u52a8\u9080\u8bf7", None))
        self.CaptionLabel_10.setText(QCoreApplication.translate("Form", u"\u7528\u6237", None))
        self.CustomUser.setText(QCoreApplication.translate("Form", u"\u81ea\u5b9a\u4e49", None))
        self.Collectusers.setText(QCoreApplication.translate("Form", u"\u91c7\u96c6\u7528\u6237", None))
        self.ImportUser.setText(QCoreApplication.translate("Form", u"\u5bfc\u5165", None))
        self.PlainTextEdit.setPlaceholderText(QCoreApplication.translate("Form", u"\u6bcf\u884c\u4e00\u4e2a\u6216\u4f7f\u7528\u4e2d\u82f1\u6587\u9017\u53f7\u9694\u5f00\uff0c\u7528\u6237\u540d\u5217\u8868(\u793a\u4f8b: feitapp, feitapp2, feitapp3, feitapp4, feitapp5)", None))
        self.CaptionLabel_11.setText(QCoreApplication.translate("Form", u"\u91c7\u96c6\u5230\u7684\u7528\u6237", None))

        __sortingEnabled = self.ListWidget.isSortingEnabled()
        self.ListWidget.setSortingEnabled(False)
        ___qlistwidgetitem = self.ListWidget.item(0)
        ___qlistwidgetitem.setText(QCoreApplication.translate("Form", u"\u65b0\u5efa\u9879\u76ee", None));
        ___qlistwidgetitem1 = self.ListWidget.item(1)
        ___qlistwidgetitem1.setText(QCoreApplication.translate("Form", u"\u65b0\u5efa\u9879\u76ee", None));
        ___qlistwidgetitem2 = self.ListWidget.item(2)
        ___qlistwidgetitem2.setText(QCoreApplication.translate("Form", u"\u65b0\u5efa\u9879\u76ee", None));
        self.ListWidget.setSortingEnabled(__sortingEnabled)

        self.CheckBox.setText(QCoreApplication.translate("Form", u"\u5168\u9009/\u53d6\u6d88", None))
        self.CaptionLabel_12.setText(QCoreApplication.translate("Form", u"\u8d26\u6237\u5206\u7ec4", None))

        __sortingEnabled1 = self.AccountsListWidget.isSortingEnabled()
        self.AccountsListWidget.setSortingEnabled(False)
        ___qlistwidgetitem3 = self.AccountsListWidget.item(0)
        ___qlistwidgetitem3.setText(QCoreApplication.translate("Form", u"\u65b0\u5efa\u9879\u76ee", None));
        ___qlistwidgetitem4 = self.AccountsListWidget.item(1)
        ___qlistwidgetitem4.setText(QCoreApplication.translate("Form", u"\u65b0\u5efa\u9879\u76ee", None));
        ___qlistwidgetitem5 = self.AccountsListWidget.item(2)
        ___qlistwidgetitem5.setText(QCoreApplication.translate("Form", u"\u65b0\u5efa\u9879\u76ee", None));
        ___qlistwidgetitem6 = self.AccountsListWidget.item(3)
        ___qlistwidgetitem6.setText(QCoreApplication.translate("Form", u"\u65b0\u5efa\u9879\u76ee", None));
        self.AccountsListWidget.setSortingEnabled(__sortingEnabled1)

        self.CheckBoxAccounts.setText(QCoreApplication.translate("Form", u"\u5168\u9009/\u53d6\u6d88", None))
        self.cancelBtn.setText(QCoreApplication.translate("Form", u"\u53d6\u6d88", None))
        self.SaveBtn.setText(QCoreApplication.translate("Form", u"\u4fdd\u5b58", None))
    # retranslateUi

