from PySide6.QtCore import Qt, Signal, QSize, Slot
from PySide6.QtWidgets import (QWidget, QDialog, QVBoxLayout, QHBoxLayout, 
                             QCheckBox, QLabel, QLineEdit, QComboBox, 
                             QListWidget, QListWidgetItem, QPushButton,
                             QSplitter, QGroupBox, QScrollArea, QRadioButton,
                             QButtonGroup, QFrame, QStackedWidget)
from qfluentwidgets import (CardWidget, PushButton, ComboBox, LineEdit, 
                          BodyLabel, TitleLabel, PrimaryPushButton,
                          InfoBar, InfoBarPosition, CheckBox, SearchLineEdit,
                          MessageBox, SubtitleLabel, RadioButton, SwitchButton)
from qfluentwidgets import FluentIcon as FIF


class AddTaskDialogUI(QDialog):
    """任务对话框UI类，只负责UI组件的创建和布局"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("添加监控任务")
        self.resize(900, 800)
        self.setup_ui()
        self.connect_signals()  # 连接所有信号槽
        
    def setup_ui(self):
        # 主布局
        self.main_layout = QVBoxLayout(self)
        
        # 顶部任务信息区域
        self.task_info_card = CardWidget(self)
        self.task_info_layout = QVBoxLayout(self.task_info_card)
        
        # 任务名称和任务类型
        self.task_header_layout = QHBoxLayout()
        
        self.task_name_layout = QVBoxLayout()
        self.task_name_label = BodyLabel("任务名称:")
        self.task_name_edit = LineEdit(self)
        self.task_name_edit.setPlaceholderText("输入任务名称")
        self.task_name_layout.addWidget(self.task_name_label)
        self.task_name_layout.addWidget(self.task_name_edit)
        
        self.task_type_layout = QVBoxLayout()
        self.task_type_label = BodyLabel("任务类型:")
        self.task_type_combo = ComboBox(self)
        self.task_type_combo.addItems(["群组监控", "关键词监控"])
        self.task_type_layout.addWidget(self.task_type_label)
        self.task_type_layout.addWidget(self.task_type_combo)
        
        self.task_header_layout.addLayout(self.task_name_layout)
        self.task_header_layout.addLayout(self.task_type_layout)
        
        # 关键词监控设置
        self.keyword_layout = QVBoxLayout()
        self.keyword_label = BodyLabel("关键词 (多个关键词请用英文逗号分隔):")
        self.keyword_edit = LineEdit(self)
        self.keyword_edit.setPlaceholderText("例如: 比特币,BTC,Bitcoin")
        self.keyword_layout.addWidget(self.keyword_label)
        self.keyword_layout.addWidget(self.keyword_edit)
        
        # 关键词屏蔽设置
        self.block_layout = QVBoxLayout()
        self.block_title_layout = QHBoxLayout()
        self.block_label = BodyLabel("关键词屏蔽 (屏蔽包含以下关键词的消息或用户，多个用英文逗号分隔):")
        self.block_toggle = SwitchButton(self)
        self.block_title_layout.addWidget(self.block_label)
        self.block_title_layout.addWidget(self.block_toggle)
        self.block_title_layout.addStretch()
        
        self.block_edit = LineEdit(self)
        self.block_edit.setPlaceholderText("例如: 广告,推广,联系方式")
        self.block_edit.setEnabled(False)  # 默认禁用，需要通过开关启用
        
        # 屏蔽范围设置
        self.block_scope_layout = QHBoxLayout()
        self.block_scope_label = BodyLabel("屏蔽范围:")
        self.block_scope_group = QButtonGroup(self)
        self.block_message_radio = RadioButton("仅消息内容", self)
        self.block_username_radio = RadioButton("仅用户名", self)
        self.block_both_radio = RadioButton("两者都屏蔽", self)
        
        self.block_scope_group.addButton(self.block_message_radio, 1)
        self.block_scope_group.addButton(self.block_username_radio, 2)
        self.block_scope_group.addButton(self.block_both_radio, 3)
        self.block_both_radio.setChecked(True)  # 默认选中"两者都屏蔽"
        
        # 将屏蔽范围内的控件收集到一个列表中以便统一控制启用/禁用状态
        self.block_scope_widgets = [
            self.block_scope_label, self.block_message_radio, 
            self.block_username_radio, self.block_both_radio
        ]
        
        self.block_scope_layout.addWidget(self.block_scope_label)
        self.block_scope_layout.addWidget(self.block_message_radio)
        self.block_scope_layout.addWidget(self.block_username_radio)
        self.block_scope_layout.addWidget(self.block_both_radio)
        self.block_scope_layout.addStretch()
        
        # 将屏蔽范围控件设为禁用状态
        for widget in self.block_scope_widgets:
            widget.setEnabled(False)
        
        self.block_layout.addLayout(self.block_title_layout)
        self.block_layout.addWidget(self.block_edit)
        self.block_layout.addLayout(self.block_scope_layout)
        
        # 排除群组设置
        # self.exclude_layout = QVBoxLayout()
        # self.exclude_label = BodyLabel("排除群组/频道 (输入username或ID，多个用英文逗号分隔):")
        # self.exclude_edit = LineEdit(self)
        # self.exclude_edit.setPlaceholderText("例如: group_name1,channel_id2,-1001234567890")
        # self.exclude_layout.addWidget(self.exclude_label)
        # self.exclude_layout.addWidget(self.exclude_edit)
        
        # 将布局添加到任务信息卡片
        self.task_info_layout.addLayout(self.task_header_layout)
        self.task_info_layout.addLayout(self.keyword_layout)
        self.task_info_layout.addLayout(self.block_layout)
        #self.task_info_layout.addLayout(self.exclude_layout)
        
        # 主分割区域 - 账号和群组选择
        self.splitter = QSplitter(Qt.Horizontal)
        
        # 左侧账号列表
        self.account_card = CardWidget(self)
        self.account_layout = QVBoxLayout(self.account_card)
        
        self.account_title = TitleLabel("账号列表")
        
        # 添加账号分组下拉框
        self.account_group_layout = QHBoxLayout()
        self.account_group_label = BodyLabel("账号分组:")
        self.account_group_combo = ComboBox(self)
        self.account_group_combo.setPlaceholderText("选择账号分组")
        self.account_group_layout.addWidget(self.account_group_label)
        self.account_group_layout.addWidget(self.account_group_combo)
        
        self.account_search = SearchLineEdit(self)
        self.account_search.setPlaceholderText("搜索账号")
        
        self.account_list = QListWidget(self)
        self.account_list.setSelectionMode(QListWidget.SingleSelection)
        
        self.account_layout.addWidget(self.account_title)
        self.account_layout.addLayout(self.account_group_layout) # 添加分组布局
        self.account_layout.addWidget(self.account_search)
        self.account_layout.addWidget(self.account_list)
        
        # 右侧群组列表
        self.group_card = CardWidget(self)
        self.group_layout = QVBoxLayout(self.group_card)
        
        self.group_header_layout = QHBoxLayout()
        self.group_title = TitleLabel("群组/频道列表")
        self.group_search = SearchLineEdit(self)
        self.group_search.setPlaceholderText("搜索群组/频道")
        
        # 将QScrollArea和QWidget容器改为QListWidget
        self.group_list = QListWidget(self)
        self.group_list.setSelectionMode(QListWidget.NoSelection)  # 不需要选择模式，只需要勾选功能
        
        # 添加排除的群组标签
        # self.excluded_groups_label = SubtitleLabel("已排除的群组:", self)
        # self.excluded_groups_label.setVisible(False)
        #
        self.group_header_layout.addWidget(self.group_title)
        self.group_header_layout.addStretch()
        
        self.group_layout.addLayout(self.group_header_layout)
        
        # 添加群组类型过滤单选按钮
        self.group_filter_layout = QHBoxLayout()
        self.group_filter_label = BodyLabel("显示类型:", self)
        self.group_filter_group = QButtonGroup(self)
        self.all_filter_radio = RadioButton("全部", self)
        self.group_filter_radio = RadioButton("仅群组", self)
        self.channel_filter_radio = RadioButton("仅频道", self)
        
        self.group_filter_group.addButton(self.all_filter_radio, 1)
        self.group_filter_group.addButton(self.group_filter_radio, 2)
        self.group_filter_group.addButton(self.channel_filter_radio, 3)
        self.all_filter_radio.setChecked(True)  # 默认选中"全部"
        
        self.group_filter_layout.addWidget(self.group_filter_label)
        self.group_filter_layout.addWidget(self.all_filter_radio)
        self.group_filter_layout.addWidget(self.group_filter_radio)
        self.group_filter_layout.addWidget(self.channel_filter_radio)
        self.group_filter_layout.addStretch()
        
        self.group_layout.addLayout(self.group_filter_layout)
        self.group_layout.addWidget(self.group_search)
        self.group_layout.addWidget(self.group_list)  # 使用QListWidget替代QScrollArea
        
        # 将全选按钮添加到列表下方
        self.select_all_layout = QHBoxLayout()
        self.select_all_layout.setContentsMargins(0, 5, 0, 5)  # 添加上下间距
        
        # 全选按钮
        self.select_all_checkbox = CheckBox("全选/取消全选", self)
        self.select_all_checkbox.setStyleSheet("font-weight: bold;")  # 加粗文字
        
        self.select_all_layout.addWidget(self.select_all_checkbox)
        self.select_all_layout.addStretch()
        self.group_layout.addLayout(self.select_all_layout)
        
        # 添加说明标签
        self.select_all_hint = BodyLabel("(适用于当前可见的所有群组和频道)", self)
        self.select_all_hint.setStyleSheet("color: #666666; font-size: 12px;")
        self.group_layout.addWidget(self.select_all_hint)
        
        #self.group_layout.addWidget(self.excluded_groups_label)
        
        # 添加到分割器
        self.splitter.addWidget(self.account_card)
        self.splitter.addWidget(self.group_card)
        self.splitter.setSizes([300, 600])  # 设置初始比例

        # 通知设置区域
        self.notification_card = CardWidget(self)
        self.notification_layout = QVBoxLayout(self.notification_card)
        
        # 通知标题和开关
        self.notification_header = QHBoxLayout()
        self.notification_title = TitleLabel("通知设置")
        self.notification_header.addWidget(self.notification_title)
        self.notification_header.addStretch()
        self.notification_layout.addLayout(self.notification_header)

        # 是否启用通知的开关
        self.notification_toggle_layout = QHBoxLayout()
        self.notification_toggle_label = BodyLabel("启用通知:")
        self.notification_toggle = SwitchButton(self)
        self.notification_toggle_layout.addWidget(self.notification_toggle_label)
        self.notification_toggle_layout.addWidget(self.notification_toggle)
        self.notification_toggle_layout.addStretch()
        self.notification_layout.addLayout(self.notification_toggle_layout)
        
        # 添加分隔线 - 使用QFrame替代Separator
        self.notification_separator = QFrame()
        self.notification_separator.setFrameShape(QFrame.HLine)
        self.notification_separator.setFrameShadow(QFrame.Sunken)
        self.notification_layout.addWidget(self.notification_separator)
        
        # 创建通知配置容器
        self.notification_config_container = QWidget()
        self.notification_config_layout = QVBoxLayout(self.notification_config_container)
        self.notification_config_layout.setContentsMargins(0, 0, 0, 0)
        
        # 创建水平布局用于横排显示添加通知和通知列表
        self.notification_horizontal_layout = QHBoxLayout()
        
        # 通知列表区域
        self.notification_list_layout = QVBoxLayout()
        self.notification_list_label = SubtitleLabel("通知列表:")
        self.notification_list = QListWidget()
        self.notification_list.setSelectionMode(QListWidget.SingleSelection)
        self.notification_list.setMinimumHeight(200)
        
        # 通知列表操作按钮
        self.notification_list_button_layout = QHBoxLayout()
        self.remove_notification_button = PushButton("删除所选", self, FIF.DELETE)
        self.notification_list_button_layout.addStretch()
        self.notification_list_button_layout.addWidget(self.remove_notification_button)
        
        self.notification_list_layout.addWidget(self.notification_list_label)
        self.notification_list_layout.addWidget(self.notification_list)
        self.notification_list_layout.addLayout(self.notification_list_button_layout)
        
        # 添加通知区域
        self.add_notification_card = CardWidget(self)
        self.add_notification_layout = QVBoxLayout(self.add_notification_card)
        self.add_notification_label = SubtitleLabel("添加通知:")
        
        # 通知类型选择
        self.notification_type_layout = QHBoxLayout()
        self.notification_type_label = BodyLabel("通知方式:")
        self.notification_type_layout.addWidget(self.notification_type_label)
        
        # 单选按钮组
        self.notification_type_group = QButtonGroup(self)
        self.group_radio = RadioButton("发送到群组", self)
        self.private_radio = RadioButton("发送私聊", self)
        self.notification_type_group.addButton(self.group_radio, 1)
        self.notification_type_group.addButton(self.private_radio, 2)
        self.notification_type_layout.addWidget(self.group_radio)
        self.notification_type_layout.addWidget(self.private_radio)
        self.notification_type_layout.addStretch()
        
        # 添加通知类型选择到布局
        self.add_notification_layout.addWidget(self.add_notification_label)
        self.add_notification_layout.addLayout(self.notification_type_layout)
        
        # 堆叠小部件用于显示不同的通知设置
        self.notification_stack = QStackedWidget(self)
        
        # 群组通知设置
        self.group_notification_widget = QWidget()
        self.group_notification_layout = QVBoxLayout(self.group_notification_widget)
        
        # 机器人选择
        self.bot_layout = QHBoxLayout()
        self.bot_label = BodyLabel("选择机器人:")
        self.bot_combo = ComboBox(self)
        self.bot_combo.setPlaceholderText("选择用于发送通知的机器人")
        self.bot_layout.addWidget(self.bot_label)
        self.bot_layout.addWidget(self.bot_combo)
        
        # 群组选择
        self.bot_group_layout = QHBoxLayout()
        self.bot_group_label = BodyLabel("选择群组:")
        self.bot_group_combo = ComboBox(self)
        self.bot_group_combo.setPlaceholderText("选择机器人所在的群组")
        self.bot_group_layout.addWidget(self.bot_group_label)
        self.bot_group_layout.addWidget(self.bot_group_combo)
        
        self.group_notification_layout.addLayout(self.bot_layout)
        self.group_notification_layout.addLayout(self.bot_group_layout)
        
        # 私聊通知设置
        self.private_notification_widget = QWidget()
        self.private_notification_layout = QVBoxLayout(self.private_notification_widget)
        
        # 发送账号选择
        self.sender_layout = QHBoxLayout()
        self.sender_label = BodyLabel("发送账号:")
        self.sender_combo = ComboBox(self)
        self.sender_combo.setPlaceholderText("选择用于发送私聊的账号")
        self.sender_layout.addWidget(self.sender_label)
        self.sender_layout.addWidget(self.sender_combo)
        
        # 接收用户设置
        self.receiver_layout = QHBoxLayout()
        self.receiver_label = BodyLabel("接收用户:")
        self.receiver_edit = LineEdit(self)
        self.receiver_edit.setPlaceholderText("输入接收用户的username (不含@)")
        self.receiver_layout.addWidget(self.receiver_label)
        self.receiver_layout.addWidget(self.receiver_edit)
        
        # 私聊提示
        self.private_notice = BodyLabel("请确保已经与对方互为好友，发送私聊消息更安全！")
        self.private_notice.setStyleSheet("color: #FF5722;")  # 设置橙色提示
        
        self.private_notification_layout.addLayout(self.sender_layout)
        self.private_notification_layout.addLayout(self.receiver_layout)
        self.private_notification_layout.addWidget(self.private_notice)
        
        # 添加两个小部件到堆叠窗口
        self.notification_stack.addWidget(self.group_notification_widget)
        self.notification_stack.addWidget(self.private_notification_widget)
        
        # 添加通知按钮
        self.add_notification_button_layout = QHBoxLayout()
        self.add_notification_button = PrimaryPushButton("添加到通知列表", self, FIF.ADD)
        self.add_notification_button_layout.addStretch()
        self.add_notification_button_layout.addWidget(self.add_notification_button)
        
        # 添加堆叠窗口和添加按钮到通知配置容器
        self.add_notification_layout.addWidget(self.notification_stack)
        self.add_notification_layout.addLayout(self.add_notification_button_layout)
        
        # 将添加通知区域和通知列表添加到水平布局中（左侧添加通知，右侧通知列表）
        self.notification_horizontal_layout.addWidget(self.add_notification_card, 1)  # 左侧添加通知区域，占比1
        self.notification_horizontal_layout.addLayout(self.notification_list_layout, 1)  # 右侧通知列表，占比1
        
        # 将水平布局添加到通知配置容器
        self.notification_config_layout.addLayout(self.notification_horizontal_layout)
        
        # 添加通知配置容器到通知布局
        self.notification_layout.addWidget(self.notification_config_container)
        
        # 底部按钮区域
        self.button_layout = QHBoxLayout()
        self.cancel_button = PushButton("取消", self, FIF.CLOSE)
        self.confirm_button = PrimaryPushButton("添加任务", self, FIF.ADD)
        
        self.button_layout.addStretch()
        self.button_layout.addWidget(self.cancel_button)
        self.button_layout.addWidget(self.confirm_button)
        
        # 添加各部分到主布局
        self.main_layout.addWidget(self.task_info_card)
        self.main_layout.addWidget(self.splitter)
        self.main_layout.addWidget(self.notification_card)
        self.main_layout.addLayout(self.button_layout)
        
        # 初始化UI状态
        self.init_ui_states()
        
    def init_ui_states(self):
        """初始化UI控件的默认状态"""
        # 设置一些样式和属性
        self.task_info_card.setMaximumHeight(300)  # 增加高度以适应新的关键词屏蔽设置
        
        # 默认选中群组通知
        self.group_radio.setChecked(True)
        self.notification_stack.setCurrentIndex(0)
        
        # 默认不启用通知
        self.notification_toggle.setChecked(False)
        # 隐藏通知配置区域
        self.notification_config_container.setVisible(False)
        
        # 默认隐藏已排除群组标签
        #self.excluded_groups_label.setVisible(False)
        
    def connect_signals(self):
        """连接信号槽"""
        # 通知开关切换时显示/隐藏通知配置
        self.notification_toggle.checkedChanged.connect(self._on_notification_toggle_changed)
        
        # 通知类型切换
        self.group_radio.toggled.connect(self._on_notification_type_changed)
        self.private_radio.toggled.connect(self._on_notification_type_changed)
        
        # 添加通知按钮
        self.add_notification_button.clicked.connect(self._on_add_notification_clicked)
        
        # 删除通知按钮
        self.remove_notification_button.clicked.connect(self._on_remove_notification_clicked)
        
        # 屏蔽开关
        self.block_toggle.checkedChanged.connect(self._on_block_toggle_changed)
        
    def _on_notification_toggle_changed(self, checked):
        """通知开关切换处理 - 占位方法，由子类重写"""
        self.notification_config_container.setVisible(checked)
        
    def _on_notification_type_changed(self, checked):
        """通知类型切换处理 - 占位方法，由子类重写"""
        if checked:
            if self.group_radio.isChecked():
                self.notification_stack.setCurrentIndex(0)
            else:
                self.notification_stack.setCurrentIndex(1)
                
    def _on_add_notification_clicked(self):
        """添加通知处理 - 占位方法，由子类重写"""
        pass
    
    def _on_remove_notification_clicked(self):
        """删除通知处理 - 占位方法，由子类重写"""
        pass
    
    def _on_block_toggle_changed(self, checked):
        """屏蔽开关切换处理 - 占位方法，由子类重写"""
        self.block_edit.setEnabled(checked)
        for widget in self.block_scope_widgets:
            widget.setEnabled(checked)


if __name__ == "__main__":
    from PySide6.QtWidgets import QApplication
    app = QApplication([])
    dialog = AddTaskDialogUI()
    dialog.show()
    app.exec()

