# -*- coding: utf-8 -*-

from PySide6.QtCore import (QCoreApplication, QDate, QDateTime, QLocale,
    QMetaObject, QObject, QPoint, QRect,
    QSize, QTime, QUrl, Qt)
from PySide6.QtGui import (QBrush, QColor, QConicalGradient, QCursor,
    QFont, QFontDatabase, QGradient, QIcon,
    QImage, QKeySequence, QLinearGradient, QPainter,
    QPalette, QPixmap, QRadialGradient, QTransform)
from PySide6.QtWidgets import (QApplication, QFrame, QHBoxLayout, QListWidgetItem,
    QSizePolicy, QSpacerItem, QVBoxLayout, QWidget)

from qfluentwidgets import (BodyLabel, CaptionLabel, ComboBox, LineEdit,
    ListWidget, PlainTextEdit, PrimaryPushButton, Push<PERSON>utton,
    RadioButton, SwitchButton, TextEdit, TimePicker, FluentIcon)

class Ui_AddSendMsgTask(object):
    def setupUi(self, AddSendMsgTask):
        if not AddSendMsgTask.objectName():
            AddSendMsgTask.setObjectName(u"AddSendMsgTask")
    
        self.verticalLayout_4 = QVBoxLayout(AddSendMsgTask)
        self.verticalLayout_4.setObjectName(u"verticalLayout_4")
        self.horizontalLayout_11 = QHBoxLayout()
        self.horizontalLayout_11.setObjectName(u"horizontalLayout_11")
        self.frame_left = QFrame(AddSendMsgTask)
        self.frame_left.setObjectName(u"frame_left")
        sizePolicy = QSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)
        sizePolicy.setHorizontalStretch(20)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.frame_left.sizePolicy().hasHeightForWidth())
        self.frame_left.setSizePolicy(sizePolicy)
        self.verticalLayout_2 = QVBoxLayout(self.frame_left)
        self.verticalLayout_2.setObjectName(u"verticalLayout_2")
        self.frame001 = QFrame(self.frame_left)
        self.frame001.setObjectName(u"frame001")
        self.frame001.setFrameShape(QFrame.StyledPanel)
        self.frame001.setFrameShadow(QFrame.Raised)
        self.horizontalLayout = QHBoxLayout(self.frame001)
        self.horizontalLayout.setObjectName(u"horizontalLayout")
        self.BodyLabel_15 = BodyLabel(self.frame001)
        self.BodyLabel_15.setObjectName(u"BodyLabel_15")
        sizePolicy1 = QSizePolicy(QSizePolicy.Policy.Fixed, QSizePolicy.Policy.Preferred)
        sizePolicy1.setHorizontalStretch(0)
        sizePolicy1.setVerticalStretch(0)
        sizePolicy1.setHeightForWidth(self.BodyLabel_15.sizePolicy().hasHeightForWidth())
        self.BodyLabel_15.setSizePolicy(sizePolicy1)

        self.horizontalLayout.addWidget(self.BodyLabel_15)

        self.JobName = LineEdit(self.frame001)
        self.JobName.setObjectName(u"JobName")

        self.horizontalLayout.addWidget(self.JobName)


        self.verticalLayout_2.addWidget(self.frame001)

        self.horizontalLayout_6 = QHBoxLayout()
        self.horizontalLayout_6.setObjectName(u"horizontalLayout_6")
        self.BodyLabel = BodyLabel(self.frame_left)
        self.BodyLabel.setObjectName(u"BodyLabel")
        sizePolicy1.setHeightForWidth(self.BodyLabel.sizePolicy().hasHeightForWidth())
        self.BodyLabel.setSizePolicy(sizePolicy1)

        self.horizontalLayout_6.addWidget(self.BodyLabel)

        self.GroupBtn = RadioButton(self.frame_left)
        self.GroupBtn.setObjectName(u"GroupBtn")

        self.horizontalLayout_6.addWidget(self.GroupBtn)

        self.UserBtn = RadioButton(self.frame_left)
        self.UserBtn.setObjectName(u"UserBtn")

        self.horizontalLayout_6.addWidget(self.UserBtn)


        self.verticalLayout_2.addLayout(self.horizontalLayout_6)

        self.horizontalLayout_5 = QHBoxLayout()
        self.horizontalLayout_5.setObjectName(u"horizontalLayout_5")
        self.BodyLabel_2 = BodyLabel(self.frame_left)
        self.BodyLabel_2.setObjectName(u"BodyLabel_2")
        sizePolicy1.setHeightForWidth(self.BodyLabel_2.sizePolicy().hasHeightForWidth())
        self.BodyLabel_2.setSizePolicy(sizePolicy1)

        self.horizontalLayout_5.addWidget(self.BodyLabel_2)

        self.ExecuteTypeBox = ComboBox(self.frame_left)
        self.ExecuteTypeBox.setObjectName(u"ExecuteTypeBox")
        sizePolicy2 = QSizePolicy(QSizePolicy.Policy.Fixed, QSizePolicy.Policy.Fixed)
        sizePolicy2.setHorizontalStretch(0)
        sizePolicy2.setVerticalStretch(0)
        sizePolicy2.setHeightForWidth(self.ExecuteTypeBox.sizePolicy().hasHeightForWidth())
        self.ExecuteTypeBox.setSizePolicy(sizePolicy2)

        self.horizontalLayout_5.addWidget(self.ExecuteTypeBox)

        self.frame = QFrame(self.frame_left)
        self.frame.setObjectName(u"frame")
        self.frame.setFrameShape(QFrame.StyledPanel)
        self.frame.setFrameShadow(QFrame.Raised)
        self.verticalLayout_5 = QVBoxLayout(self.frame)
        self.verticalLayout_5.setObjectName(u"verticalLayout_5")
        
        # 添加周期类型选择器
        self.horizontalLayout_period_type = QHBoxLayout()
        self.horizontalLayout_period_type.setObjectName(u"horizontalLayout_period_type")
        
        self.periodTypeLabel = BodyLabel(self.frame)
        self.periodTypeLabel.setObjectName(u"periodTypeLabel")
        
        self.periodTypeComboBox = ComboBox(self.frame)
        self.periodTypeComboBox.setObjectName(u"periodTypeComboBox")
        
        # 初始为隐藏状态，只有选择周期执行时才显示
        self.periodTypeLabel.setVisible(False)
        self.periodTypeComboBox.setVisible(False)
        
        self.horizontalLayout_period_type.addWidget(self.periodTypeLabel)
        self.horizontalLayout_period_type.addWidget(self.periodTypeComboBox)
        self.horizontalLayout_period_type.addStretch()
        
        self.verticalLayout_5.addLayout(self.horizontalLayout_period_type)
        
        # 创建用于所有时间组件的水平布局
        self.horizontalLayout_time_frames = QHBoxLayout()
        self.horizontalLayout_time_frames.setObjectName(u"horizontalLayout_time_frames")
        self.horizontalLayout_time_frames.setSpacing(10)  # 设置间距
        
        # 修改 frameSecond 为水平布局中的第一个组件
        self.frameSecond = QFrame(self.frame)
        self.frameSecond.setObjectName(u"frameSecond")
        sizePolicy3 = QSizePolicy(QSizePolicy.Policy.Preferred, QSizePolicy.Policy.Fixed)
        sizePolicy3.setHorizontalStretch(0)
        sizePolicy3.setVerticalStretch(0)
        sizePolicy3.setHeightForWidth(self.frameSecond.sizePolicy().hasHeightForWidth())
        self.frameSecond.setSizePolicy(sizePolicy3)
        self.frameSecond.setFrameShape(QFrame.StyledPanel)
        self.frameSecond.setFrameShadow(QFrame.Raised)
        self.verticalLayout_6 = QVBoxLayout(self.frameSecond)
        self.verticalLayout_6.setObjectName(u"verticalLayout_6")
        self.horizontalLayout_8 = QHBoxLayout()
        self.horizontalLayout_8.setObjectName(u"horizontalLayout_8")
        self.Second = LineEdit(self.frameSecond)
        self.Second.setObjectName(u"Second")
        sizePolicy4 = QSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Ignored)
        sizePolicy4.setHorizontalStretch(0)
        sizePolicy4.setVerticalStretch(0)
        sizePolicy4.setHeightForWidth(self.Second.sizePolicy().hasHeightForWidth())
        self.Second.setSizePolicy(sizePolicy4)

        self.horizontalLayout_8.addWidget(self.Second)

        self.BodyLabel_7 = BodyLabel(self.frameSecond)
        self.BodyLabel_7.setObjectName(u"BodyLabel_7")

        self.horizontalLayout_8.addWidget(self.BodyLabel_7)

        self.verticalLayout_6.addLayout(self.horizontalLayout_8)

        self.CaptionLabel_2 = CaptionLabel(self.frameSecond)
        self.CaptionLabel_2.setObjectName(u"CaptionLabel_2")
        self.CaptionLabel_2.setWordWrap(True)

        self.verticalLayout_6.addWidget(self.CaptionLabel_2)
        
        # 添加到水平布局
        self.horizontalLayout_time_frames.addWidget(self.frameSecond)

        # 添加一次性执行框架到水平布局
        self.frameOnce = QFrame(self.frame)
        self.frameOnce.setObjectName(u"frameOnce")
        sizePolicy3.setHeightForWidth(self.frameOnce.sizePolicy().hasHeightForWidth())
        self.frameOnce.setSizePolicy(sizePolicy3)
        self.frameOnce.setFrameShape(QFrame.StyledPanel)
        self.frameOnce.setFrameShadow(QFrame.Raised)
        self.verticalLayout_11 = QVBoxLayout(self.frameOnce)
        self.verticalLayout_11.setObjectName(u"verticalLayout_11")
        self.CaptionLabel_7 = CaptionLabel(self.frameOnce)
        self.CaptionLabel_7.setObjectName(u"CaptionLabel_7")
        self.CaptionLabel_7.setWordWrap(True)

        self.verticalLayout_11.addWidget(self.CaptionLabel_7)
        
        # 添加到水平布局
        self.horizontalLayout_time_frames.addWidget(self.frameOnce)

        # 添加每月执行框架到水平布局
        self.frameMonth = QFrame(self.frame)
        self.frameMonth.setObjectName(u"frameMonth")
        self.frameMonth.setFrameShape(QFrame.StyledPanel)
        self.frameMonth.setFrameShadow(QFrame.Raised)
        self.verticalLayout_7 = QVBoxLayout(self.frameMonth)
        self.verticalLayout_7.setObjectName(u"verticalLayout_7")
        self.horizontalLayout_12 = QHBoxLayout()
        self.horizontalLayout_12.setObjectName(u"horizontalLayout_12")
        self.LineEdit_5 = LineEdit(self.frameMonth)
        self.LineEdit_5.setObjectName(u"LineEdit_5")

        self.horizontalLayout_12.addWidget(self.LineEdit_5)

        self.BodyLabel_8 = BodyLabel(self.frameMonth)
        self.BodyLabel_8.setObjectName(u"BodyLabel_8")

        self.horizontalLayout_12.addWidget(self.BodyLabel_8)

        self.TimePickerMonth = TimePicker(self.frameMonth)
        self.TimePickerMonth.setObjectName(u"TimePickerMonth")

        self.horizontalLayout_12.addWidget(self.TimePickerMonth)

        self.verticalLayout_7.addLayout(self.horizontalLayout_12)

        self.CaptionLabel_3 = CaptionLabel(self.frameMonth)
        self.CaptionLabel_3.setObjectName(u"CaptionLabel_3")
        self.CaptionLabel_3.setWordWrap(True)

        self.verticalLayout_7.addWidget(self.CaptionLabel_3)
        
        # 添加到水平布局
        self.horizontalLayout_time_frames.addWidget(self.frameMonth)

        # 添加每日执行框架到水平布局
        self.frameDay = QFrame(self.frame)
        self.frameDay.setObjectName(u"frameDay")
        self.frameDay.setFrameShape(QFrame.StyledPanel)
        self.frameDay.setFrameShadow(QFrame.Raised)
        self.verticalLayout_8 = QVBoxLayout(self.frameDay)
        self.verticalLayout_8.setObjectName(u"verticalLayout_8")
        self.TimePickerDay = TimePicker(self.frameDay)
        self.TimePickerDay.setObjectName(u"TimePickerDay")

        self.verticalLayout_8.addWidget(self.TimePickerDay)

        self.CaptionLabel_4 = CaptionLabel(self.frameDay)
        self.CaptionLabel_4.setObjectName(u"CaptionLabel_4")
        self.CaptionLabel_4.setWordWrap(True)

        self.verticalLayout_8.addWidget(self.CaptionLabel_4)
        
        # 添加到水平布局
        self.horizontalLayout_time_frames.addWidget(self.frameDay)

        # 添加间隔小时框架到水平布局
        self.frameIntervalHour = QFrame(self.frame)
        self.frameIntervalHour.setObjectName(u"frameIntervalHour")
        self.frameIntervalHour.setFrameShape(QFrame.StyledPanel)
        self.frameIntervalHour.setFrameShadow(QFrame.Raised)
        self.verticalLayout_9 = QVBoxLayout(self.frameIntervalHour)
        self.verticalLayout_9.setObjectName(u"verticalLayout_9")
        self.horizontalLayout_14 = QHBoxLayout()
        self.horizontalLayout_14.setObjectName(u"horizontalLayout_14")
        self.LineEditInterHours = LineEdit(self.frameIntervalHour)
        self.LineEditInterHours.setObjectName(u"LineEditInterHours")

        self.horizontalLayout_14.addWidget(self.LineEditInterHours)

        self.BodyLabel_13 = BodyLabel(self.frameIntervalHour)
        self.BodyLabel_13.setObjectName(u"BodyLabel_13")

        self.horizontalLayout_14.addWidget(self.BodyLabel_13)

        self.verticalLayout_9.addLayout(self.horizontalLayout_14)

        self.CaptionLabel_5 = CaptionLabel(self.frameIntervalHour)
        self.CaptionLabel_5.setObjectName(u"CaptionLabel_5")
        self.CaptionLabel_5.setWordWrap(True)

        self.verticalLayout_9.addWidget(self.CaptionLabel_5)
        
        # 添加到水平布局
        self.horizontalLayout_time_frames.addWidget(self.frameIntervalHour)

        # 添加间隔分钟框架到水平布局
        self.frameMinute = QFrame(self.frame)
        self.frameMinute.setObjectName(u"frameMinute")
        self.frameMinute.setFrameShape(QFrame.StyledPanel)
        self.frameMinute.setFrameShadow(QFrame.Raised)
        self.verticalLayout_16 = QVBoxLayout(self.frameMinute)
        self.verticalLayout_16.setObjectName(u"verticalLayout_16")
        self.horizontalLayout_24 = QHBoxLayout()
        self.horizontalLayout_24.setObjectName(u"horizontalLayout_24")
        self.LineEdit_IntervalMin = LineEdit(self.frameMinute)
        self.LineEdit_IntervalMin.setObjectName(u"LineEdit_IntervalMin")

        self.horizontalLayout_24.addWidget(self.LineEdit_IntervalMin)

        self.BodyLabel_24 = BodyLabel(self.frameMinute)
        self.BodyLabel_24.setObjectName(u"BodyLabel_24")

        self.horizontalLayout_24.addWidget(self.BodyLabel_24)

        self.verticalLayout_16.addLayout(self.horizontalLayout_24)

        self.CaptionLabel_9 = CaptionLabel(self.frameMinute)
        self.CaptionLabel_9.setObjectName(u"CaptionLabel_9")
        self.CaptionLabel_9.setWordWrap(True)

        self.verticalLayout_16.addWidget(self.CaptionLabel_9)
        
        # 添加到水平布局
        self.horizontalLayout_time_frames.addWidget(self.frameMinute)

        # 添加每小时第几分钟框架到水平布局
        self.frameHour = QFrame(self.frame)
        self.frameHour.setObjectName(u"frameHour")
        self.frameHour.setFrameShape(QFrame.StyledPanel)
        self.frameHour.setFrameShadow(QFrame.Raised)
        self.verticalLayout_17 = QVBoxLayout(self.frameHour)
        self.verticalLayout_17.setObjectName(u"verticalLayout_17")
        self.horizontalLayout_25 = QHBoxLayout()
        self.horizontalLayout_25.setObjectName(u"horizontalLayout_25")
        self.LineEdit_Min = LineEdit(self.frameHour)
        self.LineEdit_Min.setObjectName(u"LineEdit_Min")

        self.horizontalLayout_25.addWidget(self.LineEdit_Min)

        self.BodyLabel_25 = BodyLabel(self.frameHour)
        self.BodyLabel_25.setObjectName(u"BodyLabel_25")

        self.horizontalLayout_25.addWidget(self.BodyLabel_25)

        self.verticalLayout_17.addLayout(self.horizontalLayout_25)

        self.CaptionLabel_10 = CaptionLabel(self.frameHour)
        self.CaptionLabel_10.setObjectName(u"CaptionLabel_10")
        self.CaptionLabel_10.setWordWrap(True)

        self.verticalLayout_17.addWidget(self.CaptionLabel_10)
        
        # 添加到水平布局
        self.horizontalLayout_time_frames.addWidget(self.frameHour)

        # 将整个水平布局添加到垂直布局中
        self.verticalLayout_5.addLayout(self.horizontalLayout_time_frames)

        self.horizontalLayout_5.addWidget(self.frame)


        self.verticalLayout_2.addLayout(self.horizontalLayout_5)

        self.horizontalLayout_10 = QHBoxLayout()
        self.horizontalLayout_10.setObjectName(u"horizontalLayout_10")
        self.BodyLabel_6 = BodyLabel(self.frame_left)
        self.BodyLabel_6.setObjectName(u"BodyLabel_6")
        sizePolicy1.setHeightForWidth(self.BodyLabel_6.sizePolicy().hasHeightForWidth())
        self.BodyLabel_6.setSizePolicy(sizePolicy1)

        self.horizontalLayout_10.addWidget(self.BodyLabel_6)

        self.SwitchButton = SwitchButton(self.frame_left)
        self.SwitchButton.setObjectName(u"SwitchButton")
        sizePolicy1.setHeightForWidth(self.SwitchButton.sizePolicy().hasHeightForWidth())
        self.SwitchButton.setSizePolicy(sizePolicy1)

        self.horizontalLayout_10.addWidget(self.SwitchButton)

        self.CaptionLabel = CaptionLabel(self.frame_left)
        self.CaptionLabel.setObjectName(u"CaptionLabel")

        self.horizontalLayout_10.addWidget(self.CaptionLabel)


        self.verticalLayout_2.addLayout(self.horizontalLayout_10)

        self.horizontalLayout_4 = QHBoxLayout()
        self.horizontalLayout_4.setObjectName(u"horizontalLayout_4")
        self.BodyLabel_3 = BodyLabel(self.frame_left)
        self.BodyLabel_3.setObjectName(u"BodyLabel_3")

        self.horizontalLayout_4.addWidget(self.BodyLabel_3)

        self.LineEdit = LineEdit(self.frame_left)
        self.LineEdit.setObjectName(u"LineEdit")

        self.horizontalLayout_4.addWidget(self.LineEdit)


        self.verticalLayout_2.addLayout(self.horizontalLayout_4)

        self.FrameAcc = QFrame(self.frame_left)
        self.FrameAcc.setObjectName(u"FrameAcc")
        self.horizontalLayout_15 = QHBoxLayout(self.FrameAcc)
        self.horizontalLayout_15.setObjectName(u"horizontalLayout_15")
        
        # 账号分组部分
        self.GroupLabel = BodyLabel(self.FrameAcc)
        self.GroupLabel.setObjectName(u"GroupLabel")
        sizePolicy1.setHeightForWidth(self.GroupLabel.sizePolicy().hasHeightForWidth())
        self.GroupLabel.setSizePolicy(sizePolicy1)
        
        self.AccountGroupComboBox = ComboBox(self.FrameAcc)
        self.AccountGroupComboBox.setObjectName(u"AccountGroupComboBox")
        
        self.RefreshGroupBtn = PushButton(self.FrameAcc)
        self.RefreshGroupBtn.setObjectName(u"RefreshGroupBtn")
        
        # 空间间隔
        self.accountSpacer = QSpacerItem(20, 20, QSizePolicy.Fixed, QSizePolicy.Minimum)
        
        # 添加账号部分
        self.BodyLabel_14 = BodyLabel(self.FrameAcc)
        self.BodyLabel_14.setObjectName(u"BodyLabel_14")
        sizePolicy1.setHeightForWidth(self.BodyLabel_14.sizePolicy().hasHeightForWidth())
        self.BodyLabel_14.setSizePolicy(sizePolicy1)

        self.UserComboBox = ComboBox(self.FrameAcc)
        self.UserComboBox.setObjectName(u"UserComboBox")

        self.PushButton_3 = PushButton(self.FrameAcc)
        self.PushButton_3.setObjectName(u"PushButton_3")
        sizePolicy2.setHeightForWidth(self.PushButton_3.sizePolicy().hasHeightForWidth())
        self.PushButton_3.setSizePolicy(sizePolicy2)
        
        # 添加所有控件到布局 - 先添加分组相关控件，再添加账号相关控件
        self.horizontalLayout_15.addWidget(self.GroupLabel)
        self.horizontalLayout_15.addWidget(self.AccountGroupComboBox)
        self.horizontalLayout_15.addWidget(self.RefreshGroupBtn)
        self.horizontalLayout_15.addItem(self.accountSpacer)
        self.horizontalLayout_15.addWidget(self.BodyLabel_14)
        self.horizontalLayout_15.addWidget(self.UserComboBox)
        self.horizontalLayout_15.addWidget(self.PushButton_3)

        self.verticalLayout_2.addWidget(self.FrameAcc)

        self.frame05 = QFrame(self.frame_left)
        self.frame05.setObjectName(u"frame05")
        self.verticalLayout_13 = QVBoxLayout(self.frame05)
        self.verticalLayout_13.setObjectName(u"verticalLayout_13")
        self.frame_group = QFrame(self.frame05)
        self.frame_group.setObjectName(u"frame_group")
        self.frame_group.setFrameShape(QFrame.StyledPanel)
        self.frame_group.setFrameShadow(QFrame.Raised)
        self.horizontalLayout_16 = QHBoxLayout(self.frame_group)
        self.horizontalLayout_16.setObjectName(u"horizontalLayout_16")
        self.RadioButtonAllGroup = RadioButton(self.frame_group)
        self.RadioButtonAllGroup.setObjectName(u"RadioButtonAllGroup")

        self.horizontalLayout_16.addWidget(self.RadioButtonAllGroup)

        self.CustomGroupBtn = RadioButton(self.frame_group)
        self.CustomGroupBtn.setObjectName(u"CustomGroupBtn")

        self.horizontalLayout_16.addWidget(self.CustomGroupBtn)


        self.verticalLayout_13.addWidget(self.frame_group)

        self.frame_user = QFrame(self.frame05)
        self.frame_user.setObjectName(u"frame_user")
        self.frame_user.setFrameShape(QFrame.StyledPanel)
        self.frame_user.setFrameShadow(QFrame.Raised)
        self.horizontalLayout_17 = QHBoxLayout(self.frame_user)
        self.horizontalLayout_17.setObjectName(u"horizontalLayout_17")
        self.RadioButtonAllUser = RadioButton(self.frame_user)
        self.RadioButtonAllUser.setObjectName(u"RadioButtonAllUser")

        self.horizontalLayout_17.addWidget(self.RadioButtonAllUser)

        self.ContactsBtn = RadioButton(self.frame_user)
        self.ContactsBtn.setObjectName(u"ContactsBtn")

        self.horizontalLayout_17.addWidget(self.ContactsBtn)

        self.RadioButtonCustomUser = RadioButton(self.frame_user)
        self.RadioButtonCustomUser.setObjectName(u"RadioButtonCustomUser")

        self.horizontalLayout_17.addWidget(self.RadioButtonCustomUser)


        self.verticalLayout_13.addWidget(self.frame_user)

        self.CaptionLabel_11 = CaptionLabel(self.frame05)
        self.CaptionLabel_11.setObjectName(u"CaptionLabel_11")

        self.verticalLayout_13.addWidget(self.CaptionLabel_11)


        self.verticalLayout_2.addWidget(self.frame05)

        self.PlainTextEdit = PlainTextEdit(self.frame_left)
        self.PlainTextEdit.setObjectName(u"PlainTextEdit")

        self.verticalLayout_2.addWidget(self.PlainTextEdit)

        self.frameconnact = QFrame(self.frame_left)
        self.frameconnact.setObjectName(u"frameconnact")
        self.verticalLayout_14 = QVBoxLayout(self.frameconnact)
        self.verticalLayout_14.setObjectName(u"verticalLayout_14")
        self.ListWidget = ListWidget(self.frameconnact)
        self.ListWidget.setObjectName(u"ListWidget")

        self.verticalLayout_14.addWidget(self.ListWidget)

        self.horizontalLayout_19 = QHBoxLayout()
        self.horizontalLayout_19.setObjectName(u"horizontalLayout_19")
        self.SelectAllBtn = PrimaryPushButton(self.frameconnact)
        self.SelectAllBtn.setObjectName(u"SelectAllBtn")

        self.horizontalLayout_19.addWidget(self.SelectAllBtn)

        self.FlushBtn = PrimaryPushButton(self.frameconnact)
        self.FlushBtn.setObjectName(u"FlushBtn")

        self.horizontalLayout_19.addWidget(self.FlushBtn)


        self.verticalLayout_14.addLayout(self.horizontalLayout_19)


        self.verticalLayout_2.addWidget(self.frameconnact)


        self.horizontalLayout_11.addWidget(self.frame_left)

        self.frameRight = QFrame(AddSendMsgTask)
        self.frameRight.setObjectName(u"frameRight")
        sizePolicy5 = QSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)
        sizePolicy5.setHorizontalStretch(30)
        sizePolicy5.setVerticalStretch(0)
        sizePolicy5.setHeightForWidth(self.frameRight.sizePolicy().hasHeightForWidth())
        self.frameRight.setSizePolicy(sizePolicy5)
        self.verticalLayout_3 = QVBoxLayout(self.frameRight)
        self.verticalLayout_3.setObjectName(u"verticalLayout_3")
        self.BodyLabel_4 = BodyLabel(self.frameRight)
        self.BodyLabel_4.setObjectName(u"BodyLabel_4")

        self.verticalLayout_3.addWidget(self.BodyLabel_4)

        self.frame_2 = QFrame(self.frameRight)
        self.frame_2.setObjectName(u"frame_2")
        self.frame_2.setFrameShape(QFrame.StyledPanel)
        self.frame_2.setFrameShadow(QFrame.Raised)
        self.horizontalLayout_13 = QHBoxLayout(self.frame_2)
        self.horizontalLayout_13.setObjectName(u"horizontalLayout_13")

        self.verticalLayout_3.addWidget(self.frame_2)

        self.MessageText = TextEdit(self.frameRight)
        self.MessageText.setObjectName(u"MessageText")

        self.verticalLayout_3.addWidget(self.MessageText)

        self.verticalLayout_12 = QVBoxLayout()
        self.verticalLayout_12.setObjectName(u"verticalLayout_12")
        self.framecustommsg = QFrame(self.frameRight)
        self.framecustommsg.setObjectName(u"framecustommsg")
        self.framecustommsg.setFrameShape(QFrame.StyledPanel)
        self.framecustommsg.setFrameShadow(QFrame.Raised)
        self.verticalLayout = QVBoxLayout(self.framecustommsg)
        self.verticalLayout.setObjectName(u"verticalLayout")
        self.frame_loc = QFrame(self.framecustommsg)
        self.frame_loc.setObjectName(u"frame_loc")
        self.horizontalLayout_2 = QHBoxLayout(self.frame_loc)
        self.horizontalLayout_2.setObjectName(u"horizontalLayout_2")
        self.BodyLabel_5 = BodyLabel(self.frame_loc)
        self.BodyLabel_5.setObjectName(u"BodyLabel_5")

        self.horizontalLayout_2.addWidget(self.BodyLabel_5)

        self.MsgHead = RadioButton(self.frame_loc)
        self.MsgHead.setObjectName(u"MsgHead")

        self.horizontalLayout_2.addWidget(self.MsgHead)

        self.MsgFoot = RadioButton(self.frame_loc)
        self.MsgFoot.setObjectName(u"MsgFoot")

        self.horizontalLayout_2.addWidget(self.MsgFoot)

        self.MsgRandom = RadioButton(self.frame_loc)
        self.MsgRandom.setObjectName(u"MsgRandom")

        self.horizontalLayout_2.addWidget(self.MsgRandom)


        self.verticalLayout.addWidget(self.frame_loc)

        self.frame_addmsg = QFrame(self.framecustommsg)
        self.frame_addmsg.setObjectName(u"frame_addmsg")
        self.horizontalLayout_3 = QHBoxLayout(self.frame_addmsg)
        self.horizontalLayout_3.setObjectName(u"horizontalLayout_3")
        self.BodyLabel_16 = BodyLabel(self.frame_addmsg)
        self.BodyLabel_16.setObjectName(u"BodyLabel_16")

        self.horizontalLayout_3.addWidget(self.BodyLabel_16)

        self.RandomString = RadioButton(self.frame_addmsg)
        self.RandomString.setObjectName(u"RandomString")

        self.horizontalLayout_3.addWidget(self.RandomString)

        self.RandomEm = RadioButton(self.frame_addmsg)
        self.RandomEm.setObjectName(u"RandomEm")

        self.horizontalLayout_3.addWidget(self.RandomEm)

        self.RandomAdd = RadioButton(self.frame_addmsg)
        self.RandomAdd.setObjectName(u"RandomAdd")

        self.horizontalLayout_3.addWidget(self.RandomAdd)


        self.verticalLayout.addWidget(self.frame_addmsg)

        self.horizontalLayout_7 = QHBoxLayout()
        self.horizontalLayout_7.setObjectName(u"horizontalLayout_7")
        self.BodyLabel_22 = BodyLabel(self.framecustommsg)
        self.BodyLabel_22.setObjectName(u"BodyLabel_22")

        self.horizontalLayout_7.addWidget(self.BodyLabel_22)

        self.LineEditNum = LineEdit(self.framecustommsg)
        self.LineEditNum.setObjectName(u"LineEditNum")

        self.horizontalLayout_7.addWidget(self.LineEditNum)

        self.BodyLabel_18 = BodyLabel(self.framecustommsg)
        self.BodyLabel_18.setObjectName(u"BodyLabel_18")

        self.horizontalLayout_7.addWidget(self.BodyLabel_18)

        self.LineEditStringLen = LineEdit(self.framecustommsg)
        self.LineEditStringLen.setObjectName(u"LineEditStringLen")

        self.horizontalLayout_7.addWidget(self.LineEditStringLen)

        self.BodyLabel_19 = BodyLabel(self.framecustommsg)
        self.BodyLabel_19.setObjectName(u"BodyLabel_19")

        self.horizontalLayout_7.addWidget(self.BodyLabel_19)

        self.LineEditEmLen = LineEdit(self.framecustommsg)
        self.LineEditEmLen.setObjectName(u"LineEditEmLen")

        self.horizontalLayout_7.addWidget(self.LineEditEmLen)


        self.verticalLayout.addLayout(self.horizontalLayout_7)

        self.horizontalLayout_18 = QHBoxLayout()
        self.horizontalLayout_18.setObjectName(u"horizontalLayout_18")
        self.BodyLabel_20 = BodyLabel(self.framecustommsg)
        self.BodyLabel_20.setObjectName(u"BodyLabel_20")

        self.horizontalLayout_18.addWidget(self.BodyLabel_20)

        self.LineEditEm = LineEdit(self.framecustommsg)
        self.LineEditEm.setObjectName(u"LineEditEm")

        self.horizontalLayout_18.addWidget(self.LineEditEm)


        self.verticalLayout.addLayout(self.horizontalLayout_18)


        self.verticalLayout_12.addWidget(self.framecustommsg)


        self.verticalLayout_3.addLayout(self.verticalLayout_12)


        self.horizontalLayout_11.addWidget(self.frameRight)


        self.verticalLayout_4.addLayout(self.horizontalLayout_11)

        self.horizontalLayout_9 = QHBoxLayout()
        self.horizontalLayout_9.setObjectName(u"horizontalLayout_9")
        self.horizontalSpacer = QSpacerItem(40, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.horizontalLayout_9.addItem(self.horizontalSpacer)

        self.Cancal = PushButton(AddSendMsgTask)
        self.Cancal.setObjectName(u"Cancal")
        sizePolicy6 = QSizePolicy(QSizePolicy.Policy.Minimum, QSizePolicy.Policy.Fixed)
        sizePolicy6.setHorizontalStretch(0)
        sizePolicy6.setVerticalStretch(0)
        sizePolicy6.setHeightForWidth(self.Cancal.sizePolicy().hasHeightForWidth())
        self.Cancal.setSizePolicy(sizePolicy6)
        self.Cancal.setMinimumSize(QSize(120, 50))
        self.Cancal.setIcon(FluentIcon.CANCEL)

        self.horizontalLayout_9.addWidget(self.Cancal)

        self.horizontalSpacer_2 = QSpacerItem(40, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.horizontalLayout_9.addItem(self.horizontalSpacer_2)

        self.SaveTask = PrimaryPushButton(AddSendMsgTask)
        self.SaveTask.setObjectName(u"SaveTask")
        sizePolicy6.setHeightForWidth(self.SaveTask.sizePolicy().hasHeightForWidth())
        self.SaveTask.setSizePolicy(sizePolicy6)
        self.SaveTask.setMinimumSize(QSize(120, 50))
        self.SaveTask.setIcon(FluentIcon.SAVE)

        self.horizontalLayout_9.addWidget(self.SaveTask)

        self.horizontalSpacer_3 = QSpacerItem(40, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.horizontalLayout_9.addItem(self.horizontalSpacer_3)


        self.verticalLayout_4.addLayout(self.horizontalLayout_9)


        self.retranslateUi(AddSendMsgTask)

        QMetaObject.connectSlotsByName(AddSendMsgTask)
    # setupUi

    def retranslateUi(self, AddSendMsgTask):
        AddSendMsgTask.setWindowTitle(QCoreApplication.translate("AddSendMsgTask", u"Form", None))
        self.BodyLabel_15.setText(QCoreApplication.translate("AddSendMsgTask", u"\u4efb\u52a1\u540d\uff1a", None))
        self.JobName.setPlaceholderText(QCoreApplication.translate("AddSendMsgTask", u"\u4e0d\u53ef\u4e3a\u7a7a", None))
        self.BodyLabel.setText(QCoreApplication.translate("AddSendMsgTask", u"\u4efb\u52a1\u7c7b\u578b", None))
        self.GroupBtn.setText(QCoreApplication.translate("AddSendMsgTask", u"\u7fa4\u6d88\u606f\u7fa4\u53d1", None))
        self.UserBtn.setText(QCoreApplication.translate("AddSendMsgTask", u"\u597d\u53cb\u7fa4\u53d1", None))
        self.BodyLabel_2.setText(QCoreApplication.translate("AddSendMsgTask", u"\u6267\u884c\u5468\u671f", None))
        
        # 周期类型选择器文本设置
        self.periodTypeLabel.setText(QCoreApplication.translate("AddSendMsgTask", u"\u5468\u671f\u7c7b\u578b:", None))
        self.periodTypeComboBox.clear()
        self.periodTypeComboBox.addItems([
            QCoreApplication.translate("AddSendMsgTask", u"\u79d2\u7ea7\u95f4\u9694", None),  # 秒级间隔
            QCoreApplication.translate("AddSendMsgTask", u"\u6bcf\u6708\u6307\u5b9a\u65e5\u671f", None),  # 每月指定日期
            QCoreApplication.translate("AddSendMsgTask", u"\u95f4\u9694\u5c0f\u65f6", None),  # 间隔小时
            QCoreApplication.translate("AddSendMsgTask", u"\u95f4\u9694\u5206\u949f", None),  # 间隔分钟
            QCoreApplication.translate("AddSendMsgTask", u"\u6bcf\u5c0f\u65f6\u6307\u5b9a\u5206\u949f", None)  # 每小时指定分钟
        ])
        
        self.Second.setText("")
        self.Second.setPlaceholderText(QCoreApplication.translate("AddSendMsgTask", u"60", None))
        self.BodyLabel_7.setText(QCoreApplication.translate("AddSendMsgTask", u"\u79d2", None))
        self.CaptionLabel_2.setText(QCoreApplication.translate("AddSendMsgTask", u"\u6bcf\u9694 5 \u79d2\u6267\u884c\u4e00\u6b21", None))
        self.CaptionLabel_7.setText(QCoreApplication.translate("AddSendMsgTask", u"\u53ea\u6267\u884c\u4e00\u6b21\u4efb\u52a1\u5c31\u4f1a\u505c\u6b62", None))
        self.LineEdit_5.setText("")
        self.LineEdit_5.setPlaceholderText(QCoreApplication.translate("AddSendMsgTask", u"1", None))
        self.BodyLabel_8.setText(QCoreApplication.translate("AddSendMsgTask", u"\u65e5", None))
        self.CaptionLabel_3.setText(QCoreApplication.translate("AddSendMsgTask", u"\u6bcf\u6708 1 \u53f7 01:30\u52060\u79d2\u6267\u884c\u4e00\u6b21", None))
        self.CaptionLabel_4.setText(QCoreApplication.translate("AddSendMsgTask", u"\u6bcf\u5929\u7684 01:30 \u6267\u884c\u4e00\u6b210-23\u5c0f\u65f6\u65b9\u5f0f", None))
        self.LineEditInterHours.setText("")
        self.LineEditInterHours.setPlaceholderText(QCoreApplication.translate("AddSendMsgTask", u"3", None))
        self.BodyLabel_13.setText(QCoreApplication.translate("AddSendMsgTask", u"\u65f6", None))
        self.CaptionLabel_5.setText(QCoreApplication.translate("AddSendMsgTask", u"\u6bcf\u95f4\u96943\u5c0f\u65f6\u6267\u884c\u4e00\u6b21\u8be5\u6d88\u606f\u53d1\u9001\u4efb\u52a1", None))
        self.LineEdit_IntervalMin.setText("")
        self.LineEdit_IntervalMin.setPlaceholderText(QCoreApplication.translate("AddSendMsgTask", u"30", None))
        self.BodyLabel_24.setText(QCoreApplication.translate("AddSendMsgTask", u"\u5206\u949f", None))
        self.CaptionLabel_9.setText(QCoreApplication.translate("AddSendMsgTask", u"\u6bcf\u95f4\u969430\u5206\u949f\u8fd0\u884c\u4e00\u6b21", None))
        self.LineEdit_Min.setText("")
        self.LineEdit_Min.setPlaceholderText(QCoreApplication.translate("AddSendMsgTask", u"25", None))
        self.BodyLabel_25.setText(QCoreApplication.translate("AddSendMsgTask", u"\u5206", None))
        self.CaptionLabel_10.setText(QCoreApplication.translate("AddSendMsgTask", u"\u6bcf\u5c0f\u65f6\u7b2c25\u5206\u949f\u53d1\u9001\u4e00\u6b21\u6d88\u606f", None))
        self.BodyLabel_6.setText(QCoreApplication.translate("AddSendMsgTask", u"\u7acb\u5373\u6267\u884c\u4e00\u6b21", None))
        self.SwitchButton.setText(QCoreApplication.translate("AddSendMsgTask", u"\u5173", None))
        self.SwitchButton.setOnText(QCoreApplication.translate("AddSendMsgTask", u"\u5f00", None))
        self.SwitchButton.setOffText(QCoreApplication.translate("AddSendMsgTask", u"\u5173", None))
        self.CaptionLabel.setText(QCoreApplication.translate("AddSendMsgTask", u"\u5982\u5f00\u542f\uff0c\u5219\u5148\u6267\u884c\u4e00\u6b21\u518d\u8fdb\u5165\u5b9a\u65f6\u5faa\u73af\uff0c\u5355\u6b21\u7fa4\u53d1\u9ed8\u8ba4\u7acb\u5373\u6267\u884c", None))
        self.BodyLabel_3.setText(QCoreApplication.translate("AddSendMsgTask", u"\u6d88\u606f\u5ef6\u65f6", None))
        self.LineEdit.setPlaceholderText(QCoreApplication.translate("AddSendMsgTask", u"\u6bcf\u53d1\u9001\u4e00\u6b21\u5ef6\u65f6N\u79d2\uff0c\u53ef\u4e3a\u968f\u673a\u503c\u5982\uff1a1,\u62161,5", None))
        self.BodyLabel_14.setText(QCoreApplication.translate("AddSendMsgTask", u"\u8d26\u53f7", None))
        self.PushButton_3.setText(QCoreApplication.translate("AddSendMsgTask", u"\u5237\u65b0", None))
        self.RadioButtonAllGroup.setText(QCoreApplication.translate("AddSendMsgTask", u"\u6240\u6709\u7fa4", None))
        self.CustomGroupBtn.setText(QCoreApplication.translate("AddSendMsgTask", u"\u81ea\u5b9a\u4e49\u7fa4", None))
        self.RadioButtonAllUser.setText(QCoreApplication.translate("AddSendMsgTask", u"\u6240\u6709\u4eba", None))
        self.ContactsBtn.setText(QCoreApplication.translate("AddSendMsgTask", u"\u8054\u7cfb\u4eba", None))
        self.RadioButtonCustomUser.setText(QCoreApplication.translate("AddSendMsgTask", u"\u81ea\u5b9a\u4e49\u4eba", None))
        self.CaptionLabel_11.setText(QCoreApplication.translate("AddSendMsgTask", u"Caption label", None))
        self.PlainTextEdit.setPlaceholderText(QCoreApplication.translate("AddSendMsgTask", u"\u4e00\u884c\u4e00\u4e2a\u6216\u7528\u9017\u53f7\u9694\u5f00", None))
        self.SelectAllBtn.setText(QCoreApplication.translate("AddSendMsgTask", u"\u5168\u9009", None))
        self.FlushBtn.setText(QCoreApplication.translate("AddSendMsgTask", u"\u5237\u65b0", None))
        self.BodyLabel_4.setText(QCoreApplication.translate("AddSendMsgTask", u"\u6d88\u606f\u5185\u5bb9\u3010HTML\u7f16\u8f91\u5668\u7c7b\u578b\u3011", None))
        self.MessageText.setPlaceholderText(QCoreApplication.translate("AddSendMsgTask", u"\u53ef\u4e0a\u4f20\u56fe\u7247\u4e00\u8d77\uff0c\u53ef\u5728\u5176\u4ed6\u7f16\u8f91\u5668\u7f16\u8f91\u597d\u5185\u5bb9\u590d\u5236\u8fdb\u6765\u3002\u5bf9\u65b9\u770b\u5230\u7684\u6548\u679c\u548c\u7f16\u8f91\u5668\u4e00\u81f4\uff0c\u5e26\u56fe\u7247\u5148\u53d1\u9001\u56fe\u7247\uff0c\u540e\u9762\u8ddf\u968f\u6587\u5b57\u3002\u82e5\u6587\u5b57\u8fc7\u591a\u5219\u62c6\u5206\u4e3a\u591a\u6761\u6d88\u606f\u53d1\u9001\u3002", None))
        self.BodyLabel_5.setText(QCoreApplication.translate("AddSendMsgTask", u"\u6dfb\u52a0\u4f4d\u7f6e", None))
        self.MsgHead.setText(QCoreApplication.translate("AddSendMsgTask", u"\u6d88\u606f\u5934", None))
        self.MsgFoot.setText(QCoreApplication.translate("AddSendMsgTask", u"\u6bb5\u843d\u5c3e", None))
        self.MsgRandom.setText(QCoreApplication.translate("AddSendMsgTask", u"\u968f\u673a\u9996\u5c3e", None))
        self.BodyLabel_16.setText(QCoreApplication.translate("AddSendMsgTask", u"\u6dfb\u52a0\u6d88\u606f", None))
        self.RandomString.setText(QCoreApplication.translate("AddSendMsgTask", u"\u968f\u673a\u5b57\u6bcd+\u6570\u5b57", None))
        self.RandomEm.setText(QCoreApplication.translate("AddSendMsgTask", u"\u968f\u673a\u8868\u60c5", None))
        self.RandomAdd.setText(QCoreApplication.translate("AddSendMsgTask", u"\u4e8c\u8005\u968f\u673a", None))
        self.BodyLabel_22.setText(QCoreApplication.translate("AddSendMsgTask", u"\u6dfb\u52a0\u6b21\u6570", None))
        self.LineEditNum.setText(QCoreApplication.translate("AddSendMsgTask", u"1", None))
        self.BodyLabel_18.setText(QCoreApplication.translate("AddSendMsgTask", u"\u5b57\u7b26\u957f\u5ea6\uff1a", None))
        self.LineEditStringLen.setText(QCoreApplication.translate("AddSendMsgTask", u"2,8", None))
        self.BodyLabel_19.setText(QCoreApplication.translate("AddSendMsgTask", u"\u8868\u60c5\u4e2a\u6570", None))
        self.LineEditEmLen.setText(QCoreApplication.translate("AddSendMsgTask", u"1,3", None))
        self.BodyLabel_20.setText(QCoreApplication.translate("AddSendMsgTask", u"\u8868\u60c5\uff1a", None))
        self.LineEditEm.setText("")
        self.Cancal.setText(QCoreApplication.translate("AddSendMsgTask", u"\u53d6\u6d88", None))
        self.SaveTask.setText(QCoreApplication.translate("AddSendMsgTask", u"\u6dfb\u52a0\u4efb\u52a1", None))
        self.RefreshGroupBtn.setText(QCoreApplication.translate("AddSendMsgTask", u"\u5237\u65b0", None))
        self.GroupLabel.setText(QCoreApplication.translate("AddSendMsgTask", u"\u8d26\u53f7\u5206\u7ec4", None))
    # retranslateUi

