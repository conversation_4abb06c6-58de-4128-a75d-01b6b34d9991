# -*- coding: utf-8 -*-

################################################################################
## Form generated from reading UI file 'add_msg_new.ui'
##
## Created by: Qt User Interface Compiler version 6.9.0
##
## WARNING! All changes made in this file will be lost when recompiling UI file!
################################################################################

from PySide6.QtCore import (QCoreApplication, QDate, QDateTime, QLocale,
    QMetaObject, QObject, QPoint, QRect,
    QSize, QTime, QUrl, Qt)
from PySide6.QtGui import (QB<PERSON>, QColor, QC<PERSON>alGradient, QCursor,
    QFont, QFontDatabase, QGradient, QIcon,
    QImage, QKeySequence, QLinearGradient, QPainter,
    QPalette, QPixmap, QRadialGradient, QTransform)
from PySide6.QtWidgets import (QApplication, QFrame, QHBoxLayout, QListWidgetItem,
    QSizePolicy, QSpacerItem, QVBoxLayout, QWidget)

from qfluentwidgets import (BodyLabel, CaptionLabel, CardWidget, CheckBox,
    ComboBox, HorizontalSeparator, LineEdit, ListWidget,
    PlainTextEdit, PrimaryPushButton, PushButton, RadioButton,
    TextEdit, ToggleButton)
from utils.editor import TextEditor
class Ui_AddMsg(object):
    def setupUi(self, AddMsg):
        if not AddMsg.objectName():
            AddMsg.setObjectName(u"AddMsg")
      
        self.verticalLayout_11 = QVBoxLayout(AddMsg)
        self.verticalLayout_11.setObjectName(u"verticalLayout_11")
        self.horizontalLayout = QHBoxLayout()
        self.horizontalLayout.setObjectName(u"horizontalLayout")
        self.BodyLabel = BodyLabel(AddMsg)
        self.BodyLabel.setObjectName(u"BodyLabel")
        sizePolicy = QSizePolicy(QSizePolicy.Policy.Fixed, QSizePolicy.Policy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.BodyLabel.sizePolicy().hasHeightForWidth())
        self.BodyLabel.setSizePolicy(sizePolicy)

        self.horizontalLayout.addWidget(self.BodyLabel)

        self.taskName = LineEdit(AddMsg)
        self.taskName.setObjectName(u"taskName")

        self.horizontalLayout.addWidget(self.taskName)


        self.verticalLayout_11.addLayout(self.horizontalLayout)

        self.horizontalLayout_18 = QHBoxLayout()
        self.horizontalLayout_18.setObjectName(u"horizontalLayout_18")
        self.verticalLayout_5 = QVBoxLayout()
        self.verticalLayout_5.setObjectName(u"verticalLayout_5")
        self.CardWidget_2 = CardWidget(AddMsg)
        self.CardWidget_2.setObjectName(u"CardWidget_2")
        self.verticalLayout_2 = QVBoxLayout(self.CardWidget_2)
        self.verticalLayout_2.setObjectName(u"verticalLayout_2")
        self.horizontalLayout_2 = QHBoxLayout()
        self.horizontalLayout_2.setObjectName(u"horizontalLayout_2")
        self.BodyLabel_2 = BodyLabel(self.CardWidget_2)
        self.BodyLabel_2.setObjectName(u"BodyLabel_2")
        sizePolicy.setHeightForWidth(self.BodyLabel_2.sizePolicy().hasHeightForWidth())
        self.BodyLabel_2.setSizePolicy(sizePolicy)

        self.horizontalLayout_2.addWidget(self.BodyLabel_2)

        self.CaptionLabel_2 = CaptionLabel(self.CardWidget_2)
        self.CaptionLabel_2.setObjectName(u"CaptionLabel_2")

        self.horizontalLayout_2.addWidget(self.CaptionLabel_2)


        self.verticalLayout_2.addLayout(self.horizontalLayout_2)

        self.msgList = ListWidget(self.CardWidget_2)
        self.msgList.setObjectName(u"msgList")

        self.verticalLayout_2.addWidget(self.msgList)


        self.verticalLayout_5.addWidget(self.CardWidget_2)

        self.CardWidget = CardWidget(AddMsg)
        self.CardWidget.setObjectName(u"CardWidget")
        self.verticalLayout = QVBoxLayout(self.CardWidget)
        self.verticalLayout.setObjectName(u"verticalLayout")
        self.horizontalLayout_5 = QHBoxLayout()
        self.horizontalLayout_5.setObjectName(u"horizontalLayout_5")
        self.BodyLabel_4 = BodyLabel(self.CardWidget)
        self.BodyLabel_4.setObjectName(u"BodyLabel_4")
        sizePolicy.setHeightForWidth(self.BodyLabel_4.sizePolicy().hasHeightForWidth())
        self.BodyLabel_4.setSizePolicy(sizePolicy)

        self.horizontalLayout_5.addWidget(self.BodyLabel_4)

        self.TextBtn = ToggleButton(self.CardWidget)
        self.TextBtn.setObjectName(u"TextBtn")

        self.horizontalLayout_5.addWidget(self.TextBtn)

        self.ImgBtn = ToggleButton(self.CardWidget)
        self.ImgBtn.setObjectName(u"ImgBtn")

        self.horizontalLayout_5.addWidget(self.ImgBtn)

        self.ImgTextBtn = ToggleButton(self.CardWidget)
        self.ImgTextBtn.setObjectName(u"ImgTextBtn")

        self.horizontalLayout_5.addWidget(self.ImgTextBtn)


        self.verticalLayout.addLayout(self.horizontalLayout_5)

        self.horizontalLayout_3 = QHBoxLayout()
        self.horizontalLayout_3.setObjectName(u"horizontalLayout_3")
        self.BodyLabel_3 = BodyLabel(self.CardWidget)
        self.BodyLabel_3.setObjectName(u"BodyLabel_3")
        sizePolicy.setHeightForWidth(self.BodyLabel_3.sizePolicy().hasHeightForWidth())
        self.BodyLabel_3.setSizePolicy(sizePolicy)

        self.horizontalLayout_3.addWidget(self.BodyLabel_3)

        self.CaptionLabel_3 = CaptionLabel(self.CardWidget)
        self.CaptionLabel_3.setObjectName(u"CaptionLabel_3")

        self.horizontalLayout_3.addWidget(self.CaptionLabel_3)


        self.verticalLayout.addLayout(self.horizontalLayout_3)

        self.MsgTextEdit = TextEditor()
        self.MsgTextEdit.setObjectName(u"MsgTextEdit")

        self.verticalLayout.addWidget(self.MsgTextEdit)

        self.horizontalLayout_4 = QHBoxLayout()
        self.horizontalLayout_4.setObjectName(u"horizontalLayout_4")
        self.HorizontalSeparator = HorizontalSeparator(self.CardWidget)
        self.HorizontalSeparator.setObjectName(u"HorizontalSeparator")

        self.horizontalLayout_4.addWidget(self.HorizontalSeparator)



        self.msgAddBtn = PushButton(self.CardWidget)
        self.msgAddBtn.setObjectName(u"msgAddBtn")

        self.horizontalLayout_4.addWidget(self.msgAddBtn)


        self.verticalLayout.addLayout(self.horizontalLayout_4)


        self.verticalLayout_5.addWidget(self.CardWidget)

        self.CardWidget_4 = CardWidget(AddMsg)
        self.CardWidget_4.setObjectName(u"CardWidget_4")
        self.verticalLayout_4 = QVBoxLayout(self.CardWidget_4)
        self.verticalLayout_4.setObjectName(u"verticalLayout_4")
        self.horizontalLayout_7 = QHBoxLayout()
        self.horizontalLayout_7.setObjectName(u"horizontalLayout_7")
        self.BodyLabel_5 = BodyLabel(self.CardWidget_4)
        self.BodyLabel_5.setObjectName(u"BodyLabel_5")
        sizePolicy.setHeightForWidth(self.BodyLabel_5.sizePolicy().hasHeightForWidth())
        self.BodyLabel_5.setSizePolicy(sizePolicy)

        self.horizontalLayout_7.addWidget(self.BodyLabel_5)

        self.Str = LineEdit(self.CardWidget_4)
        self.Str.setObjectName(u"Str")

        self.horizontalLayout_7.addWidget(self.Str)

        self.CaptionLabel_4 = CaptionLabel(self.CardWidget_4)
        self.CaptionLabel_4.setObjectName(u"CaptionLabel_4")
        sizePolicy.setHeightForWidth(self.CaptionLabel_4.sizePolicy().hasHeightForWidth())
        self.CaptionLabel_4.setSizePolicy(sizePolicy)

        self.horizontalLayout_7.addWidget(self.CaptionLabel_4)

        self.LineEdit_3 = LineEdit(self.CardWidget_4)
        self.LineEdit_3.setObjectName(u"LineEdit_3")

        self.horizontalLayout_7.addWidget(self.LineEdit_3)


        self.verticalLayout_4.addLayout(self.horizontalLayout_7)

        self.horizontalLayout_6 = QHBoxLayout()
        self.horizontalLayout_6.setObjectName(u"horizontalLayout_6")
        self.BodyLabel_6 = BodyLabel(self.CardWidget_4)
        self.BodyLabel_6.setObjectName(u"BodyLabel_6")
        sizePolicy.setHeightForWidth(self.BodyLabel_6.sizePolicy().hasHeightForWidth())
        self.BodyLabel_6.setSizePolicy(sizePolicy)

        self.horizontalLayout_6.addWidget(self.BodyLabel_6)

        self.LineEdit_4 = LineEdit(self.CardWidget_4)
        self.LineEdit_4.setObjectName(u"LineEdit_4")

        self.horizontalLayout_6.addWidget(self.LineEdit_4)

        self.CaptionLabel_5 = CaptionLabel(self.CardWidget_4)
        self.CaptionLabel_5.setObjectName(u"CaptionLabel_5")
        sizePolicy.setHeightForWidth(self.CaptionLabel_5.sizePolicy().hasHeightForWidth())
        self.CaptionLabel_5.setSizePolicy(sizePolicy)

        self.horizontalLayout_6.addWidget(self.CaptionLabel_5)

        self.LineEdit_5 = LineEdit(self.CardWidget_4)
        self.LineEdit_5.setObjectName(u"LineEdit_5")

        self.horizontalLayout_6.addWidget(self.LineEdit_5)


        self.verticalLayout_4.addLayout(self.horizontalLayout_6)

        self.horizontalLayout_10 = QHBoxLayout()
        self.horizontalLayout_10.setObjectName(u"horizontalLayout_10")
        self.BodyLabel_8 = BodyLabel(self.CardWidget_4)
        self.BodyLabel_8.setObjectName(u"BodyLabel_8")
        sizePolicy.setHeightForWidth(self.BodyLabel_8.sizePolicy().hasHeightForWidth())
        self.BodyLabel_8.setSizePolicy(sizePolicy)

        self.horizontalLayout_10.addWidget(self.BodyLabel_8)

        self.RandEMOJ = CheckBox(self.CardWidget_4)
        self.RandEMOJ.setObjectName(u"RandEMOJ")

        self.horizontalLayout_10.addWidget(self.RandEMOJ)


        self.verticalLayout_4.addLayout(self.horizontalLayout_10)


        self.verticalLayout_5.addWidget(self.CardWidget_4)


        self.horizontalLayout_18.addLayout(self.verticalLayout_5)

        self.verticalLayout_10 = QVBoxLayout()
        self.verticalLayout_10.setObjectName(u"verticalLayout_10")
        self.CardWidget_3 = CardWidget(AddMsg)
        self.CardWidget_3.setObjectName(u"CardWidget_3")
        self.verticalLayout_3 = QVBoxLayout(self.CardWidget_3)
        self.verticalLayout_3.setObjectName(u"verticalLayout_3")
        self.horizontalLayout_13 = QHBoxLayout()
        self.horizontalLayout_13.setObjectName(u"horizontalLayout_13")
        self.BodyLabel_11 = BodyLabel(self.CardWidget_3)
        self.BodyLabel_11.setObjectName(u"BodyLabel_11")
        sizePolicy.setHeightForWidth(self.BodyLabel_11.sizePolicy().hasHeightForWidth())
        self.BodyLabel_11.setSizePolicy(sizePolicy)

        self.horizontalLayout_13.addWidget(self.BodyLabel_11)

        self.CaptionLabel_6 = CaptionLabel(self.CardWidget_3)
        self.CaptionLabel_6.setObjectName(u"CaptionLabel_6")

        self.horizontalLayout_13.addWidget(self.CaptionLabel_6)


        self.verticalLayout_3.addLayout(self.horizontalLayout_13)

        self.horizontalLayout_8 = QHBoxLayout()
        self.horizontalLayout_8.setObjectName(u"horizontalLayout_8")
        self.CaptionLabel_7 = CaptionLabel(self.CardWidget_3)
        self.CaptionLabel_7.setObjectName(u"CaptionLabel_7")
        sizePolicy.setHeightForWidth(self.CaptionLabel_7.sizePolicy().hasHeightForWidth())
        self.CaptionLabel_7.setSizePolicy(sizePolicy)

        self.horizontalLayout_8.addWidget(self.CaptionLabel_7)

        self.GrouComboBox = ComboBox(self.CardWidget_3)
        self.GrouComboBox.setObjectName(u"GrouComboBox")

        self.horizontalLayout_8.addWidget(self.GrouComboBox)


        self.verticalLayout_3.addLayout(self.horizontalLayout_8)

        self.GroupList = ListWidget(self.CardWidget_3)
        QListWidgetItem(self.GroupList)
        QListWidgetItem(self.GroupList)
        QListWidgetItem(self.GroupList)
        QListWidgetItem(self.GroupList)
        self.GroupList.setObjectName(u"GroupList")

        self.verticalLayout_3.addWidget(self.GroupList)

        self.horizontalLayout_9 = QHBoxLayout()
        self.horizontalLayout_9.setObjectName(u"horizontalLayout_9")
        self.SelectAllGroup = CheckBox(self.CardWidget_3)
        self.SelectAllGroup.setObjectName(u"SelectAllGroup")

        self.horizontalLayout_9.addWidget(self.SelectAllGroup)

        self.horizontalSpacer = QSpacerItem(40, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.horizontalLayout_9.addItem(self.horizontalSpacer)


        self.verticalLayout_3.addLayout(self.horizontalLayout_9)


        self.verticalLayout_10.addWidget(self.CardWidget_3)

        self.CardWidget_5 = CardWidget(AddMsg)
        self.CardWidget_5.setObjectName(u"CardWidget_5")
        self.verticalLayout_9 = QVBoxLayout(self.CardWidget_5)
        self.verticalLayout_9.setObjectName(u"verticalLayout_9")
        self.verticalLayout_8 = QVBoxLayout()
        self.verticalLayout_8.setObjectName(u"verticalLayout_8")
        self.horizontalLayout_14 = QHBoxLayout()
        self.horizontalLayout_14.setObjectName(u"horizontalLayout_14")
        self.BodyLabel_12 = BodyLabel(self.CardWidget_5)
        self.BodyLabel_12.setObjectName(u"BodyLabel_12")
        sizePolicy.setHeightForWidth(self.BodyLabel_12.sizePolicy().hasHeightForWidth())
        self.BodyLabel_12.setSizePolicy(sizePolicy)

        self.horizontalLayout_14.addWidget(self.BodyLabel_12)

        self.CaptionLabel_8 = CaptionLabel(self.CardWidget_5)
        self.CaptionLabel_8.setObjectName(u"CaptionLabel_8")

        self.horizontalLayout_14.addWidget(self.CaptionLabel_8)


        self.verticalLayout_8.addLayout(self.horizontalLayout_14)

        self.frame_tag = QFrame(self.CardWidget_5)
        self.frame_tag.setObjectName(u"frame_tag")
        self.horizontalLayout_11 = QHBoxLayout(self.frame_tag)
        self.horizontalLayout_11.setObjectName(u"horizontalLayout_11")
        self.BodyLabel_9 = BodyLabel(self.frame_tag)
        self.BodyLabel_9.setObjectName(u"BodyLabel_9")
        sizePolicy.setHeightForWidth(self.BodyLabel_9.sizePolicy().hasHeightForWidth())
        self.BodyLabel_9.setSizePolicy(sizePolicy)

        self.horizontalLayout_11.addWidget(self.BodyLabel_9)

        self.UserRadioButton = RadioButton(self.frame_tag)
        self.UserRadioButton.setObjectName(u"UserRadioButton")
        sizePolicy1 = QSizePolicy(QSizePolicy.Policy.Fixed, QSizePolicy.Policy.Fixed)
        sizePolicy1.setHorizontalStretch(0)
        sizePolicy1.setVerticalStretch(0)
        sizePolicy1.setHeightForWidth(self.UserRadioButton.sizePolicy().hasHeightForWidth())
        self.UserRadioButton.setSizePolicy(sizePolicy1)
        self.UserRadioButton.setChecked(True)

        self.horizontalLayout_11.addWidget(self.UserRadioButton)

        self.RadioButton = RadioButton(self.frame_tag)
        self.RadioButton.setObjectName(u"RadioButton")
        self.RadioButton.setEnabled(False)
        sizePolicy2 = QSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Fixed)
        sizePolicy2.setHorizontalStretch(0)
        sizePolicy2.setVerticalStretch(0)
        sizePolicy2.setHeightForWidth(self.RadioButton.sizePolicy().hasHeightForWidth())
        self.RadioButton.setSizePolicy(sizePolicy2)

        self.horizontalLayout_11.addWidget(self.RadioButton)


        self.verticalLayout_8.addWidget(self.frame_tag)

        self.frameUser = QFrame(self.CardWidget_5)
        self.frameUser.setObjectName(u"frameUser")
        self.horizontalLayout_12 = QHBoxLayout(self.frameUser)
        self.horizontalLayout_12.setObjectName(u"horizontalLayout_12")
        self.CustomUser = RadioButton(self.frameUser)
        self.CustomUser.setObjectName(u"CustomUser")
        sizePolicy1.setHeightForWidth(self.CustomUser.sizePolicy().hasHeightForWidth())
        self.CustomUser.setSizePolicy(sizePolicy1)
        self.CustomUser.setChecked(True)

        self.horizontalLayout_12.addWidget(self.CustomUser)

        self.MonitorTask = RadioButton(self.frameUser)
        self.MonitorTask.setObjectName(u"MonitorTask")
        sizePolicy1.setHeightForWidth(self.MonitorTask.sizePolicy().hasHeightForWidth())
        self.MonitorTask.setSizePolicy(sizePolicy1)

        self.horizontalLayout_12.addWidget(self.MonitorTask)

        self.ImportUser = PushButton(self.frameUser)
        self.ImportUser.setObjectName(u"ImportUser")

        self.horizontalLayout_12.addWidget(self.ImportUser)

        self.horizontalSpacer_2 = QSpacerItem(40, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.horizontalLayout_12.addItem(self.horizontalSpacer_2)


        self.verticalLayout_8.addWidget(self.frameUser)

        self.frame = QFrame(self.CardWidget_5)
        self.frame.setObjectName(u"frame")
        self.frame.setFrameShape(QFrame.StyledPanel)
        self.frame.setFrameShadow(QFrame.Raised)
        self.verticalLayout_6 = QVBoxLayout(self.frame)
        self.verticalLayout_6.setObjectName(u"verticalLayout_6")
        self.horizontalLayout_16 = QHBoxLayout()
        self.horizontalLayout_16.setObjectName(u"horizontalLayout_16")
        self.BodyLabel_10 = BodyLabel(self.frame)
        self.BodyLabel_10.setObjectName(u"BodyLabel_10")
        sizePolicy.setHeightForWidth(self.BodyLabel_10.sizePolicy().hasHeightForWidth())
        self.BodyLabel_10.setSizePolicy(sizePolicy)

        self.horizontalLayout_16.addWidget(self.BodyLabel_10)

        self.horizontalSpacer_4 = QSpacerItem(40, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.horizontalLayout_16.addItem(self.horizontalSpacer_4)


        self.verticalLayout_6.addLayout(self.horizontalLayout_16)

        self.ListWidget_3 = ListWidget(self.frame)
        self.ListWidget_3.setObjectName(u"ListWidget_3")

        self.verticalLayout_6.addWidget(self.ListWidget_3)

        self.horizontalLayout_15 = QHBoxLayout()
        self.horizontalLayout_15.setObjectName(u"horizontalLayout_15")
        self.SelectAllTask = CheckBox(self.frame)
        self.SelectAllTask.setObjectName(u"SelectAllTask")

        self.horizontalLayout_15.addWidget(self.SelectAllTask)

        self.horizontalSpacer_3 = QSpacerItem(40, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.horizontalLayout_15.addItem(self.horizontalSpacer_3)


        self.verticalLayout_6.addLayout(self.horizontalLayout_15)


        self.verticalLayout_8.addWidget(self.frame)

        self.frame_2 = QFrame(self.CardWidget_5)
        self.frame_2.setObjectName(u"frame_2")
        self.frame_2.setFrameShape(QFrame.StyledPanel)
        self.frame_2.setFrameShadow(QFrame.Raised)
        self.verticalLayout_7 = QVBoxLayout(self.frame_2)
        self.verticalLayout_7.setObjectName(u"verticalLayout_7")
        self.CustomUserEdit = PlainTextEdit(self.frame_2)
        self.CustomUserEdit.setObjectName(u"CustomUserEdit")

        self.verticalLayout_7.addWidget(self.CustomUserEdit)


        self.verticalLayout_8.addWidget(self.frame_2)


        self.verticalLayout_9.addLayout(self.verticalLayout_8)


        self.verticalLayout_10.addWidget(self.CardWidget_5)


        self.horizontalLayout_18.addLayout(self.verticalLayout_10)


        self.verticalLayout_11.addLayout(self.horizontalLayout_18)

        self.horizontalLayout_17 = QHBoxLayout()
        self.horizontalLayout_17.setObjectName(u"horizontalLayout_17")
        self.horizontalSpacer_5 = QSpacerItem(249, 11, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.horizontalLayout_17.addItem(self.horizontalSpacer_5)

        self.CancelBtn = PushButton(AddMsg)
        self.CancelBtn.setObjectName(u"CancelBtn")

        self.horizontalLayout_17.addWidget(self.CancelBtn)

        self.SaveBtn = PrimaryPushButton(AddMsg)
        self.SaveBtn.setObjectName(u"SaveBtn")

        self.horizontalLayout_17.addWidget(self.SaveBtn)


        self.verticalLayout_11.addLayout(self.horizontalLayout_17)


        self.retranslateUi(AddMsg)

        QMetaObject.connectSlotsByName(AddMsg)
    # setupUi

    def retranslateUi(self, AddMsg):
        AddMsg.setWindowTitle(QCoreApplication.translate("AddMsg", u"Form", None))
        self.BodyLabel.setText(QCoreApplication.translate("AddMsg", u"\u4efb\u52a1\u540d\uff1a", None))
        self.BodyLabel_2.setText(QCoreApplication.translate("AddMsg", u"\u6d88\u606f\u5185\u5bb9\uff1a", None))
        self.CaptionLabel_2.setText(QCoreApplication.translate("AddMsg", u"[\u591a\u6761\u6d88\u606f\u968f\u673a\u53d1\u9001]", None))
        self.BodyLabel_4.setText(QCoreApplication.translate("AddMsg", u"\u4efb\u52a1\u7c7b\u578b\uff1a", None))
        self.TextBtn.setText(QCoreApplication.translate("AddMsg", u"\u6587\u672c", None))
        self.ImgBtn.setText(QCoreApplication.translate("AddMsg", u"\u56fe\u7247", None))
        self.ImgTextBtn.setText(QCoreApplication.translate("AddMsg", u"\u56fe\u6587", None))
        self.BodyLabel_3.setText(QCoreApplication.translate("AddMsg", u"\u7f16\u8f91\u6846\uff1a", None))
        self.CaptionLabel_3.setText(QCoreApplication.translate("AddMsg", u"\u53ef\u4ecetelegram\u7f16\u8f91\u597d\u7c98\u8d34\u8fc7\u6765", None))
        self.msgAddBtn.setText(QCoreApplication.translate("AddMsg", u"\u6dfb\u52a0", None))
        self.BodyLabel_5.setText(QCoreApplication.translate("AddMsg", u"\u53d1\u9001\u6d88\u606f\u95f4\u9694", None))
        self.CaptionLabel_4.setText(QCoreApplication.translate("AddMsg", u"~", None))
        self.BodyLabel_6.setText(QCoreApplication.translate("AddMsg", u"\u8d26\u6237\u95f4\u9694", None))
        self.CaptionLabel_5.setText(QCoreApplication.translate("AddMsg", u"~", None))
        self.BodyLabel_8.setText(QCoreApplication.translate("AddMsg", u"\u8f85\u52a9\u9009\u9879", None))
        self.RandEMOJ.setText(QCoreApplication.translate("AddMsg", u"\u5c3e\u90e8\u968f\u673a\u8868\u60c5", None))
        self.BodyLabel_11.setText(QCoreApplication.translate("AddMsg", u"\u8d26\u6237\u9009\u62e9", None))
        self.CaptionLabel_6.setText(QCoreApplication.translate("AddMsg", u"\u9009\u62e9\u7528\u6765\u53d1\u9001\u6d88\u606f\u7684\u8d26\u6237", None))
        self.CaptionLabel_7.setText(QCoreApplication.translate("AddMsg", u"\u8d26\u6237\u5206\u7ec4", None))

        __sortingEnabled = self.GroupList.isSortingEnabled()
        self.GroupList.setSortingEnabled(False)
        ___qlistwidgetitem = self.GroupList.item(0)
        ___qlistwidgetitem.setText(QCoreApplication.translate("AddMsg", u"\u65b0\u5efa\u9879\u76ee", None));
        ___qlistwidgetitem1 = self.GroupList.item(1)
        ___qlistwidgetitem1.setText(QCoreApplication.translate("AddMsg", u"\u65b0\u5efa\u9879\u76ee", None));
        ___qlistwidgetitem2 = self.GroupList.item(2)
        ___qlistwidgetitem2.setText(QCoreApplication.translate("AddMsg", u"\u65b0\u5efa\u9879\u76ee", None));
        ___qlistwidgetitem3 = self.GroupList.item(3)
        ___qlistwidgetitem3.setText(QCoreApplication.translate("AddMsg", u"\u65b0\u5efa\u9879\u76ee", None));
        self.GroupList.setSortingEnabled(__sortingEnabled)

        self.SelectAllGroup.setText(QCoreApplication.translate("AddMsg", u"\u5168\u9009/\u53d6\u6d88", None))
        self.BodyLabel_12.setText(QCoreApplication.translate("AddMsg", u"\u53d1\u9001\u76ee\u6807", None))
        self.CaptionLabel_8.setText(QCoreApplication.translate("AddMsg", u"\u8bbe\u7f6e\u6d88\u606f\u9700\u8981\u53d1\u9001\u5230\u7684\u5bf9\u8c61", None))
        self.BodyLabel_9.setText(QCoreApplication.translate("AddMsg", u"\u53d1\u9001\u5bf9\u8c61\uff1a", None))
        self.UserRadioButton.setText(QCoreApplication.translate("AddMsg", u"\u7528\u6237", None))
        self.RadioButton.setText(QCoreApplication.translate("AddMsg", u"\u7fa4\u7ec4", None))
        self.CustomUser.setText(QCoreApplication.translate("AddMsg", u"\u81ea\u5b9a\u4e49", None))
        self.MonitorTask.setText(QCoreApplication.translate("AddMsg", u"\u76d1\u542c\u4efb\u52a1", None))
        self.ImportUser.setText(QCoreApplication.translate("AddMsg", u"\u5bfc\u5165", None))
        self.BodyLabel_10.setText(QCoreApplication.translate("AddMsg", u"\u9009\u4e2d\u4efb\u52a1", None))
        self.SelectAllTask.setText(QCoreApplication.translate("AddMsg", u"\u5168\u9009/\u53d6\u6d88", None))
        self.CustomUserEdit.setPlaceholderText(QCoreApplication.translate("AddMsg", u"\u4e00\u884c\u4e00\u4e2a\uff0c\u6216\u4e2d\u82f1\u6587\u9017\u53f7\u9694\u5f00\u8d26\u6237\u3010\u4f8b\uff1aabc,wegingiw,engingign\u3011", None))
        self.CancelBtn.setText(QCoreApplication.translate("AddMsg", u"\u53d6\u6d88", None))
        self.SaveBtn.setText(QCoreApplication.translate("AddMsg", u"\u4fdd\u5b58", None))
    # retranslateUi

