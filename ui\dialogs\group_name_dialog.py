from PySide6.QtWidgets import QDialog, QVBoxLayout, QDialogButtonBox
from qfluentwidgets import TitleLabel, LineEdit, PushButton,PrimaryPushButton,Dialog,SubtitleLabel    
from PySide6.QtCore import Qt

class GroupNameDialog(QDialog):
    """分组名称输入对话框"""
    def __init__(self, parent=None, current_name=""):
        super().__init__(parent)
        self.setWindowTitle("分组名称") 
        self.setMinimumWidth(300)
        
        self.name_label = SubtitleLabel("请输入分组名称:", self)
        self.name_edit = LineEdit(self)
        self.name_edit.setText(current_name)
        self.name_edit.setPlaceholderText("例如：营销组, 测试组")
        
        # 使用 QDialogButtonBox 管理按钮
        self.button_box = QDialogButtonBox(self)
        self.save_button = PrimaryPushButton("保存", self)
        self.cancel_button = PushButton("取消", self)
        self.button_box.addButton(self.cancel_button, QDialogButtonBox.ButtonRole.RejectRole)
        self.button_box.addButton(self.save_button, QDialogButtonBox.ButtonRole.AcceptRole)
        
        self.layout = QVBoxLayout(self)
        self.layout.addWidget(self.name_label)
        self.layout.addWidget(self.name_edit)
        self.layout.addWidget(self.button_box)
        
        # 连接信号
        self.button_box.accepted.connect(self.accept)
        self.button_box.rejected.connect(self.reject)
        
        self.name_edit.setFocus()
    
    def get_name(self):
        """获取输入的分组名称"""
        return self.name_edit.text().strip() 