 #!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
分组选择对话框
用于从用户的Telegram账户中获取并选择分组和频道
"""

import asyncio
from typing import Dict, Any, List, Optional, Set

from PySide6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, 
    QTableWidget, QTableWidgetItem, QHeaderView, QComboBox,
    QDialogButtonBox, QCheckBox, QAbstractItemView, QProgressBar
)
from PySide6.QtCore import Qt, Signal, QSize
from PySide6.QtGui import QIcon, QPixmap

from telethon.tl.types import Dialog, Channel, Chat, User

from app.controllers.account_controller import account_controller
from app.controllers.monitor_controller import MonitorController
from core.telegram.client_manager import client_manager
from ui.common import show_info, show_error, show_loading, hide_loading
from qasync import asyncSlot


class GroupSelectDialog(QDialog):
    """分组选择对话框"""
    
    def __init__(self, monitor_controller: MonitorController, parent=None):
        """初始化
        
        Args:
            monitor_controller: 监控控制器
            parent: 父窗口
        """
        super().__init__(parent)
        self.monitor_controller = monitor_controller
        self.selected_groups = set()  # 存储选中的组ID
        self.dialogs_list = []  # 存储获取到的对话列表
        
        # 设置窗口属性
        self.setWindowTitle("选择分组或频道")
        self.setMinimumSize(700, 500)
        
        # 初始化UI
        self._init_ui()
        
        # 加载账户列表
        self._load_accounts()
    
    def _init_ui(self):
        """初始化UI"""
        main_layout = QVBoxLayout(self)
        
        # 账户选择区域
        account_layout = QHBoxLayout()
        account_layout.addWidget(QLabel("选择账户:"))
        
        self.account_combo = QComboBox()
        self.account_combo.currentIndexChanged.connect(self._on_account_changed)
        account_layout.addWidget(self.account_combo, 1)
        
        self.btn_refresh = QPushButton("刷新列表")
        self.btn_refresh.clicked.connect(self._on_refresh_clicked)
        account_layout.addWidget(self.btn_refresh)
        
        main_layout.addLayout(account_layout)
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        main_layout.addWidget(self.progress_bar)
        
        # 分组列表
        main_layout.addWidget(QLabel("选择要监控的分组或频道:"))
        
        self.group_table = QTableWidget()
        self.group_table.setColumnCount(4)
        self.group_table.setHorizontalHeaderLabels(["", "名称", "类型", "ID"])
        self.group_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.group_table.horizontalHeader().setSectionResizeMode(0, QHeaderView.ResizeToContents)
        self.group_table.horizontalHeader().setSectionResizeMode(2, QHeaderView.ResizeToContents)
        self.group_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.group_table.setEditTriggers(QAbstractItemView.NoEditTriggers)
        
        main_layout.addWidget(self.group_table, 1)
        
        # 全选按钮
        select_layout = QHBoxLayout()
        
        self.btn_select_all = QPushButton("全选")
        self.btn_select_all.clicked.connect(self._on_select_all_clicked)
        select_layout.addWidget(self.btn_select_all)
        
        self.btn_deselect_all = QPushButton("取消全选")
        self.btn_deselect_all.clicked.connect(self._on_deselect_all_clicked)
        select_layout.addWidget(self.btn_deselect_all)
        
        select_layout.addStretch()
        
        main_layout.addLayout(select_layout)
        
        # 按钮
        button_box = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        button_box.accepted.connect(self.accept)
        button_box.rejected.connect(self.reject)
        main_layout.addWidget(button_box)
    
    def _load_accounts(self):
        """加载账户列表"""
        try:
            # 显示加载指示器
            show_loading(self, "正在加载账户列表...")
            
            @asyncSlot()
            async def _load():
                try:
                    accounts = await account_controller.get_accounts()
                    hide_loading()
                    
                    if not accounts:
                        show_error(self, "没有可用的账户，请先添加账户")
                        self.reject()
                        return
                    
                    # 填充账户下拉框
                    self.account_combo.clear()
                    for account in accounts:
                        display_name = f"{account['phone']} ({account['first_name'] or ''} {account['last_name'] or ''})"
                        self.account_combo.addItem(display_name, account['id'])
                    
                except Exception as e:
                    hide_loading()
                    show_error(self, f"加载账户失败: {e}")
                    self.reject()
            
            # 执行异步加载
            _load()
            
        except Exception as e:
            hide_loading()
            show_error(self, f"加载账户失败: {e}")
            self.reject()
    
    def _on_account_changed(self, index):
        """账户选择变更
        
        Args:
            index: 下拉框索引
        """
        if index >= 0:
            account_id = self.account_combo.currentData()
            self._load_dialogs(account_id)
    
    def _on_refresh_clicked(self):
        """刷新按钮点击事件"""
        account_id = self.account_combo.currentData()
        if account_id:
            self._load_dialogs(account_id)
    
    def _load_dialogs(self, account_id):
        """加载对话列表
        
        Args:
            account_id: 账户ID
        """
        try:
            # 显示进度条
            self.progress_bar.setVisible(True)
            self.progress_bar.setValue(0)
            
            @asyncSlot()
            async def _load():
                try:
                    # 获取客户端
                    client = await client_manager.get_client(account_id)
                    
                    # 确保连接
                    if not client.is_connected():
                        await client.connect()
                    
                    if not await client.is_user_authorized():
                        self.progress_bar.setVisible(False)
                        show_error(self, "账户未授权，请先登录账户")
                        return
                    
                    # 更新进度
                    self.progress_bar.setValue(30)
                    
                    # 获取对话列表
                    self.dialogs_list = await client.get_dialogs()
                    
                    # 更新进度
                    self.progress_bar.setValue(70)
                    
                    # 显示对话列表
                    self._update_dialogs_table()
                    
                    # 完成
                    self.progress_bar.setValue(100)
                    self.progress_bar.setVisible(False)
                    
                except Exception as e:
                    self.progress_bar.setVisible(False)
                    show_error(self, f"加载对话列表失败: {e}")
                
            # 执行异步加载
            _load()
            
        except Exception as e:
            self.progress_bar.setVisible(False)
            show_error(self, f"加载对话列表失败: {e}")
    
    def _update_dialogs_table(self):
        """更新对话列表表格"""
        # 清空表格
        self.group_table.setRowCount(0)
        
        # 添加行
        for dialog in self.dialogs_list:
            entity = dialog.entity
            
            # 跳过私聊对话
            if isinstance(entity, User):
                continue
                
            # 获取对话类型
            if isinstance(entity, Channel):
                if entity.broadcast:
                    dialog_type = "频道"
                else:
                    dialog_type = "超级群组"
            elif isinstance(entity, Chat):
                dialog_type = "群组"
            else:
                dialog_type = "其他"
            
            # 添加到表格
            row = self.group_table.rowCount()
            self.group_table.insertRow(row)
            
            # 复选框
            checkbox = QCheckBox()
            checkbox.setChecked(dialog.id in self.selected_groups)
            checkbox.stateChanged.connect(lambda state, dialog_id=dialog.id: self._on_checkbox_changed(state, dialog_id))
            
            checkbox_cell = QTableWidgetItem()
            self.group_table.setItem(row, 0, checkbox_cell)
            self.group_table.setCellWidget(row, 0, checkbox)
            
            # 名称
            name_item = QTableWidgetItem(dialog.name)
            self.group_table.setItem(row, 1, name_item)
            
            # 类型
            type_item = QTableWidgetItem(dialog_type)
            self.group_table.setItem(row, 2, type_item)
            
            # ID
            id_item = QTableWidgetItem(str(dialog.id))
            id_item.setData(Qt.UserRole, dialog.id)
            self.group_table.setItem(row, 3, id_item)
    
    def _on_checkbox_changed(self, state, dialog_id):
        """复选框状态变更
        
        Args:
            state: 复选框状态
            dialog_id: 对话ID
        """
        if state == Qt.Checked:
            self.selected_groups.add(dialog_id)
        else:
            self.selected_groups.discard(dialog_id)
    
    def _on_select_all_clicked(self):
        """全选按钮点击事件"""
        for row in range(self.group_table.rowCount()):
            checkbox = self.group_table.cellWidget(row, 0)
            if isinstance(checkbox, QCheckBox):
                checkbox.setChecked(True)
    
    def _on_deselect_all_clicked(self):
        """取消全选按钮点击事件"""
        for row in range(self.group_table.rowCount()):
            checkbox = self.group_table.cellWidget(row, 0)
            if isinstance(checkbox, QCheckBox):
                checkbox.setChecked(False)
    
    def get_selected_groups(self) -> List[Dict[str, Any]]:
        """获取选中的分组
        
        Returns:
            选中的分组列表，每个元素包含id和name
        """
        selected = []
        
        for dialog in self.dialogs_list:
            if dialog.id in self.selected_groups:
                selected.append({
                    "id": dialog.id,
                    "name": dialog.name
                })
        
        return selected 