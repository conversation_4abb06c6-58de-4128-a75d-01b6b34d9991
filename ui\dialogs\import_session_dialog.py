from PySide6.QtWidgets import QWidget,QDialog, QVBoxLayout, QHBoxLayout, QTableWidget, QTableWidgetItem, QHeaderView,QComboBox
from PySide6.QtCore import Qt, QSize
from qfluentwidgets import (PushButton, CheckBox, BodyLabel, 
                           FluentIcon, InfoBar, InfoBarPosition, PrimaryPushButton,
                           StrongBodyLabel, SwitchButton,ComboBox, 
                           HorizontalSeparator, setTheme, Theme, CardWidget, StateToolTip,)
from PySide6.QtGui import QBrush, QColor


class BatchImportSessionUI(QDialog):
    def __init__(self,parent=None):
        super().__init__(parent=parent)
        # 设置Fluent主题
        self.setup_ui()
        
    def setup_ui(self):
        # 主布局
        self.main_layout = QVBoxLayout(self)
        self.main_layout.setContentsMargins(20, 20, 20, 20)  # 增加边距
        self.main_layout.setSpacing(10)  # 增加控件间距
        
        # 标题
        self.title_label = StrongBodyLabel("批量导入Session文件")
        self.title_label.setObjectName("titleLabel")
        self.main_layout.addWidget(self.title_label)
        
        # 分隔线
        self.separator = HorizontalSeparator()
        self.main_layout.addWidget(self.separator)
        self.main_layout.addSpacing(10)
        
        # 顶部按钮区 - 使用卡片包装
        top_card = CardWidget()
        top_card.setObjectName("topCard")
        
        self.top_button_layout = QHBoxLayout(top_card)
        self.top_button_layout.setContentsMargins(15, 10, 15, 10)
        self.top_button_layout.setSpacing(10)  # 按钮间距
        
        self.import_button = PrimaryPushButton("导入Session文件", self)
        self.import_button.setIcon(FluentIcon.FOLDER_ADD)
        self.import_button.setIconSize(QSize(16, 16))
        self.import_button.setObjectName("importButton")
        
        self.refresh_proxy_button = PrimaryPushButton("刷新代理池列表", self)
        self.refresh_proxy_button.setIcon(FluentIcon.SYNC)
        self.refresh_proxy_button.setIconSize(QSize(16, 16))
        self.refresh_proxy_button.setObjectName("refreshProxyButton")
        
        self.refresh_groups_button = PrimaryPushButton("刷新分组列表", self)
        self.refresh_groups_button.setIcon(FluentIcon.PEOPLE)
        self.refresh_groups_button.setIconSize(QSize(16, 16))
        self.refresh_groups_button.setObjectName("refreshGroupsButton")
        
        self.top_button_layout.addWidget(self.import_button)
        self.top_button_layout.addWidget(self.refresh_proxy_button)
        self.top_button_layout.addWidget(self.refresh_groups_button)
        self.top_button_layout.addStretch()
        
        self.main_layout.addWidget(top_card)
        self.main_layout.addSpacing(8)
        
        # 提示信息
        self.hint_label = BodyLabel("选择Session文件，并为每个文件指定代理和分组后批量导入")
        self.hint_label.setObjectName("hintLabel")
        self.main_layout.addWidget(self.hint_label)
        self.main_layout.addSpacing(5)
        
        # Session表格 - 使用卡片包装
        table_card = CardWidget()
        table_card.setObjectName("tableCard")
        table_layout = QVBoxLayout(table_card)
        table_layout.setContentsMargins(1, 1, 1, 1)
        
        self.session_table = QTableWidget()
        self.session_table.setColumnCount(5)  # 增加一列用于分组选择
        self.session_table.setHorizontalHeaderLabels(["选择", "Session名称", "代理设置", "用户分组", "状态"])
        self.session_table.horizontalHeader().setSectionResizeMode(0, QHeaderView.ResizeToContents)
        self.session_table.horizontalHeader().setSectionResizeMode(1, QHeaderView.Stretch)
        self.session_table.horizontalHeader().setSectionResizeMode(2, QHeaderView.Stretch)
        self.session_table.horizontalHeader().setSectionResizeMode(3, QHeaderView.Stretch)  # 用户分组列
        self.session_table.horizontalHeader().setSectionResizeMode(4, QHeaderView.ResizeToContents)
        self.session_table.setAlternatingRowColors(True)  # 交替行颜色
        self.session_table.verticalHeader().setVisible(False)  # 隐藏行号
        self.session_table.setSelectionBehavior(QTableWidget.SelectRows)  # 整行选择
        self.session_table.setObjectName("sessionTable")
        
        # 设置行高，确保自适应内部组件
        self.session_table.verticalHeader().setDefaultSectionSize(42)
        self.session_table.verticalHeader().setSectionResizeMode(QHeaderView.Fixed)
        
        table_layout.addWidget(self.session_table)
        self.main_layout.addWidget(table_card, 1)  # 1表示拉伸因子，让表格占据更多空间
        
        # 底部功能区 - 使用卡片包装
        bottom_card = CardWidget()
        bottom_card.setObjectName("bottomCard")
        
        self.bottom_section_layout = QHBoxLayout(bottom_card)
        self.bottom_section_layout.setContentsMargins(15, 10, 15, 10)
        self.bottom_section_layout.setSpacing(15)
        
        # 全选选项
        self.select_all_checkbox = CheckBox("全选", self)
        self.bottom_section_layout.addWidget(self.select_all_checkbox)
        
        # 并发设置区域
        self.concurrent_layout = QHBoxLayout()
        self.concurrent_layout.setSpacing(5)
        self.max_concurrent_label = BodyLabel("同时登录数:", self)
        self.max_concurrent_combo = ComboBox(self)
        self.max_concurrent_combo.addItems(["1","3", "5", "10", "15", "20"])
        self.max_concurrent_combo.setCurrentText("5")
        self.max_concurrent_combo.setMinimumWidth(80)
        
        self.concurrent_layout.addWidget(self.max_concurrent_label)
        self.concurrent_layout.addWidget(self.max_concurrent_combo)
        
        self.bottom_section_layout.addLayout(self.concurrent_layout)
        self.bottom_section_layout.addStretch()
        
        # 底部按钮区
        self.login_button = PrimaryPushButton("开始登录", self)
        self.login_button.setIcon(FluentIcon.PLAY)
        self.login_button.setMinimumWidth(120)
        self.login_button.setObjectName("loginButton")
        
        self.cancel_button = PushButton("取消", self)
        self.cancel_button.setIcon(FluentIcon.CLOSE)
        self.cancel_button.setMinimumWidth(100)
        self.cancel_button.setObjectName("cancelButton")
        
        self.bottom_section_layout.addWidget(self.login_button)
        self.bottom_section_layout.addWidget(self.cancel_button)
        
        # 添加底部卡片到主布局
        self.main_layout.addWidget(bottom_card)
    