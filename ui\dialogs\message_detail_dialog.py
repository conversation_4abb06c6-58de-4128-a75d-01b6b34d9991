#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
消息详情对话框
显示监控消息的详细信息
"""

from typing import Dict, Any

from PySide6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QLabel, QTextEdit, QPushButton,
    QWidget, QFormLayout, QDialogButtonBox, QTabWidget
)
from PySide6.QtGui import QFont, QIcon
from PySide6.QtCore import Qt


class MessageDetailDialog(QDialog):
    """消息详情对话框"""
    
    def __init__(self, message_data: Dict[str, Any], parent=None):
        """初始化
        
        Args:
            message_data: 消息数据
            parent: 父窗口
        """
        super().__init__(parent)
        self.message_data = message_data
        
        self._init_ui()
        self._load_message_data()
    
    def _init_ui(self):
        """初始化UI"""
        self.setWindowTitle("消息详情")
        self.setMinimumSize(600, 400)
        
        layout = QVBoxLayout(self)
        
        # 基本信息区域
        info_widget = QWidget()
        info_layout = QFormLayout(info_widget)
        
        # 时间
        self.time_label = QLabel()
        info_layout.addRow("时间:", self.time_label)
        
        # 发送者
        self.sender_label = QLabel()
        info_layout.addRow("发送者:", self.sender_label)
        
        # 群组
        self.group_label = QLabel()
        info_layout.addRow("群组:", self.group_label)
        
        # 匹配关键词
        self.keywords_label = QLabel()
        info_layout.addRow("匹配关键词:", self.keywords_label)
        
        layout.addWidget(info_widget)
        
        # 消息内容
        layout.addWidget(QLabel("消息内容:"))
        
        self.message_text = QTextEdit()
        self.message_text.setReadOnly(True)
        layout.addWidget(self.message_text)
        
        # 按钮
        button_box = QDialogButtonBox(QDialogButtonBox.StandardButton.Close)
        button_box.rejected.connect(self.reject)
        layout.addWidget(button_box)
    
    def _load_message_data(self):
        """加载消息数据"""
        # 时间
        self.time_label.setText(self.message_data.get("timestamp", "-"))
        
        # 发送者
        self.sender_label.setText(self.message_data.get("sender_name", "-"))
        
        # 群组
        self.group_label.setText(self.message_data.get("group_id", "-"))
        
        # 匹配关键词
        keywords = ", ".join(self.message_data.get("matched_keywords", []))
        self.keywords_label.setText(keywords)
        
        # 消息内容
        self.message_text.setPlainText(self.message_data.get("text", "")) 