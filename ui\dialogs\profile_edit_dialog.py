from PySide6.QtCore import Qt, Signal, QSize, QTimer
from PySide6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel,
                               QSplitter, QListWidget, QListWidgetItem, QDialog,
                               QFileDialog, QMessageBox, QScrollArea, QSpinBox)
from PySide6.QtGui import QPixmap, QImage, QPainter, QColor, QFont, QBrush, QPen

from qfluentwidgets import (PushButton, LineEdit, CheckBox, TextEdit, ComboBox, RadioButton,
                            FlowLayout, InfoBar, InfoBarPosition, CardWidget, SpinBox,
                            TitleLabel, SubtitleLabel, ImageLabel, TransparentPushButton, CaptionLabel,
                            ScrollArea, BodyLabel, FluentIcon as FIF, SearchLineEdit)
from qasync import asyncSlot
from app.controllers.account_controller import AccountController
import re
import random
import string


class UserProfileEditUI(QDialog):
    """用户资料修改页面"""
    
    # 定义信号
    select_all_clicked = Signal(bool)  # 是否全选
    cancel_clicked = Signal()  # 取消编辑
    proxy_toggled = Signal(bool)  # 代理选项切换信号
    group_filter_changed = Signal(str)  # 分组筛选变更信号
    
    def __init__(self, parent = None, account_controller: AccountController = None):
        super().__init__(parent)
        self.account_controller = account_controller  # 新增：控制器引用
        self.setObjectName('UserProfileEditUI')
        self.selected_users = []  # 存储选中的用户
        self.avatar_path = ""  # 存储选择的头像路径
        self.all_users = []  # 存储所有用户数据
        self.filtered_users = []  # 存储筛选后的用户数据
        self._original_user_data = None  # 单个/批量原始数据
        self._is_batch_mode = False  # 是否批量模式
        
        # 初始化UI
        self.init_ui()
        self.connect_signals()
    
    def init_ui(self):
        """初始化UI布局"""
        self.main_layout = QVBoxLayout(self)
        self.main_layout.setSpacing(20)
        self.main_layout.setContentsMargins(30, 30, 30, 30)
        
        # 页面标题
        title_label = TitleLabel("用户资料编辑", self)
        self.main_layout.addWidget(title_label)
        
        # 创建分割器
        self.content_splitter = QSplitter(Qt.Orientation.Horizontal)
        self.content_splitter.setHandleWidth(1)
        self.content_splitter.setChildrenCollapsible(False)
        
        # 左侧区域 (用户列表和选项)
        self.left_widget = CardWidget()
        self.left_layout = QVBoxLayout(self.left_widget)
        self.left_layout.setContentsMargins(15, 15, 15, 15)
        self.left_layout.setSpacing(15)
        
        # 用户列表标题和分组筛选
        list_header_layout = QHBoxLayout()
        list_header_layout.addWidget(SubtitleLabel("用户列表", self))
        list_header_layout.addStretch()
        
        self.left_layout.addLayout(list_header_layout)
        
        # 分组筛选下拉菜单
        group_filter_layout = QHBoxLayout()
        group_filter_layout.addWidget(QLabel("分组筛选:"))
        self.group_filter_combo = ComboBox(self)
        self.group_filter_combo.addItem("全部分组", "all")
        # 不再添加任何静态分组项
        group_filter_layout.addWidget(self.group_filter_combo, 1)
        self.left_layout.addLayout(group_filter_layout)
        
        # 用户列表
        self.user_list = QListWidget(self)
        self.user_list.setFrameShape(QListWidget.NoFrame)
        self.user_list.setSelectionMode(QListWidget.SelectionMode.SingleSelection)
        self.left_layout.addWidget(self.user_list)
        
        # 搜索框
        self.phone_filter = LineEdit(self)
        self.phone_filter.setPlaceholderText("输入手机号筛选")
        self.phone_filter.setClearButtonEnabled(True)
        self.left_layout.addWidget(self.phone_filter)
        
        # 底部按钮区域
        bottom_btn_layout = QHBoxLayout()
        self.select_all_btn = PushButton("全选", self)
        self.cancel_select_btn = PushButton("取消选择", self)
        bottom_btn_layout.addWidget(self.select_all_btn)
        bottom_btn_layout.addWidget(self.cancel_select_btn)
        self.left_layout.addLayout(bottom_btn_layout)
        
        # 右侧区域 (资料编辑)
        self.right_widget = CardWidget()
        self.right_scroll_area = ScrollArea()
        self.right_scroll_area.setWidget(self.right_widget)
        self.right_scroll_area.setWidgetResizable(True)
        self.right_scroll_area.setFrameShape(QScrollArea.NoFrame)
        
        self.right_layout = QVBoxLayout(self.right_widget)
        self.right_layout.setContentsMargins(20, 20, 20, 20)
        self.right_layout.setSpacing(15)
        
        # 右侧标题
        self.right_layout.addWidget(SubtitleLabel("用户资料", self))
        
        # 用户基本信息
        basic_info_layout = QHBoxLayout()
        
        # 左侧个人信息
        left_info_layout = QVBoxLayout()
        
        # 手机号显示（只读）
        phone_layout = QHBoxLayout()
        phone_layout.addWidget(QLabel("手机号:"))
        self.phone_label = BodyLabel()
        self.phone_label.setTextFormat(Qt.TextFormat.PlainText)
        phone_layout.addWidget(self.phone_label)
        left_info_layout.addLayout(phone_layout)
        
        # 姓名行
        name_layout = QHBoxLayout()
        
        # 姓
        last_name_layout = QVBoxLayout()
        last_name_layout.addWidget(QLabel("姓:"))
        self.last_name_edit = LineEdit()
        last_name_layout.addWidget(self.last_name_edit)
        
        # 名
        first_name_layout = QVBoxLayout()
        first_name_layout.addWidget(QLabel("名:"))
        self.first_name_edit = LineEdit()
        first_name_layout.addWidget(self.first_name_edit)
        
        name_layout.addLayout(last_name_layout)
        name_layout.addLayout(first_name_layout)
        
        left_info_layout.addLayout(name_layout)
        
        # 用户名
        username_layout = QVBoxLayout()
        username_label_layout = QHBoxLayout()
        username_label_layout.addWidget(QLabel("用户名:"))
        
        # 添加标签说明按钮
        self.username_help_btn = TransparentPushButton("?", self)
        self.username_help_btn.setFixedSize(20, 20)
        self.username_help_btn.setToolTip("点击查看用户名标签使用说明")
        self.username_help_btn.clicked.connect(self.show_username_help)
        username_label_layout.addWidget(self.username_help_btn)
        username_label_layout.addStretch()
        
        username_layout.addLayout(username_label_layout)
        self.username_edit = LineEdit()
        self.username_edit.setPlaceholderText("例如: user{数字1-3}_{字母1-2}")
        username_layout.addWidget(self.username_edit)
        
        # 添加标签提示区域
        tags_info_layout = QVBoxLayout()
        tags_info_layout.setSpacing(3)
        tags_info_layout.setContentsMargins(0, 5, 0, 0)
        
        # 标签说明标题
        tags_title = CaptionLabel("支持的标签:")
        tags_title.setStyleSheet("color: #606060; font-weight: bold;")
        tags_info_layout.addWidget(tags_title)
        
        # 创建标签按钮容器
        tags_container = QHBoxLayout()
        tags_container.setSpacing(8)
        tags_container.setContentsMargins(0, 0, 0, 0)
        
        # 数字标签按钮
        self.digit_tag_btn = PushButton("{数字1-3}")
        self.digit_tag_btn.setFixedHeight(24)
        self.digit_tag_btn.setStyleSheet("""
            PushButton {
                background-color: #E3F2FD;
                border: 1px solid #2196F3;
                border-radius: 4px;
                color: #1976D2;
                font-size: 11px;
                padding: 2px 8px;
            }
            PushButton:hover {
                background-color: #BBDEFB;
            }
            PushButton:pressed {
                background-color: #90CAF9;
            }
        """)
        self.digit_tag_btn.setToolTip("点击插入数字标签\n生成1-3位随机数字")
        self.digit_tag_btn.clicked.connect(lambda: self.insert_tag("{数字1-3}"))
        
        # 字母标签按钮
        self.letter_tag_btn = PushButton("{字母1-5}")
        self.letter_tag_btn.setFixedHeight(24)
        self.letter_tag_btn.setStyleSheet("""
            PushButton {
                background-color: #E8F5E8;
                border: 1px solid #4CAF50;
                border-radius: 4px;
                color: #388E3C;
                font-size: 11px;
                padding: 2px 8px;
            }
            PushButton:hover {
                background-color: #C8E6C9;
            }
            PushButton:pressed {
                background-color: #A5D6A7;
            }
        """)
        self.letter_tag_btn.setToolTip("点击插入字母标签\n生成1-5位随机字母")
        self.letter_tag_btn.clicked.connect(lambda: self.insert_tag("{字母1-5}"))
        
        # 数字字母标签按钮
        self.mixed_tag_btn = PushButton("{数字字母1-6}")
        self.mixed_tag_btn.setFixedHeight(24)
        self.mixed_tag_btn.setStyleSheet("""
            PushButton {
                background-color: #FFF3E0;
                border: 1px solid #FF9800;
                border-radius: 4px;
                color: #F57C00;
                font-size: 11px;
                padding: 2px 8px;
            }
            PushButton:hover {
                background-color: #FFE0B2;
            }
            PushButton:pressed {
                background-color: #FFCC02;
            }
        """)
        self.mixed_tag_btn.setToolTip("点击插入混合标签\n生成1-6位随机数字字母组合")
        self.mixed_tag_btn.clicked.connect(lambda: self.insert_tag("{数字字母1-6}"))
        
        # 添加标签按钮到容器
        tags_container.addWidget(self.digit_tag_btn)
        tags_container.addWidget(self.letter_tag_btn)
        tags_container.addWidget(self.mixed_tag_btn)
        tags_container.addStretch()
        
        tags_info_layout.addLayout(tags_container)
        
        # 添加使用说明
        usage_hint = CaptionLabel("💡 可组合使用多个标签，如: user{数字1-2}_{字母1-3}")
        usage_hint.setStyleSheet("color: #757575; font-size: 10px;")
        tags_info_layout.addWidget(usage_hint)
        
        username_layout.addLayout(tags_info_layout)
        left_info_layout.addLayout(username_layout)
        
        basic_info_layout.addLayout(left_info_layout, 1)
        
        # 右侧头像
        avatar_layout = QVBoxLayout()
        avatar_layout.addWidget(QLabel("头像:"))
        
        # 头像显示和选择按钮
        avatar_content = QHBoxLayout()
        self.avatar_label = ImageLabel(self)
        self.avatar_label.setFixedSize(128, 128)
        self.set_default_avatar()
        
        self.select_avatar_btn = PushButton("选择图片", self, icon = FIF.PHOTO)
        
        avatar_content.addWidget(self.avatar_label)
        avatar_content.addWidget(self.select_avatar_btn, 0, Qt.AlignmentFlag.AlignTop)
        
        avatar_layout.addLayout(avatar_content)
        basic_info_layout.addLayout(avatar_layout)
        
        self.right_layout.addLayout(basic_info_layout)
        
        # 用户简介
        self.right_layout.addWidget(QLabel("简介:"))
        self.bio_edit = TextEdit()
        self.bio_edit.setMaximumHeight(100)
        self.right_layout.addWidget(self.bio_edit)
        
        # 用户分组
        self.right_layout.addWidget(QLabel("用户分组:"))
        self.user_group_combo = ComboBox()
        self.user_group_combo.addItems(["未分组", "营销组", "测试组", "VIP用户"])
        self.right_layout.addWidget(self.user_group_combo)
        
        # 代理设置区域（修改为与add_account_ui.py一致）
        self.right_layout.addWidget(SubtitleLabel("代理设置"))
        
        # 代理选择按钮组
        self.default_proxy_radio = RadioButton('默认（不修改）')
        self.no_proxy_radio = RadioButton('禁用代理')
        self.system_proxy_radio = RadioButton('系统代理')
        self.proxy_ip_radio = RadioButton('代理IP')
        self.default_proxy_radio.setChecked(True)
        proxy_layout = FlowLayout()
        proxy_layout.addWidget(self.default_proxy_radio)
        proxy_layout.addWidget(self.no_proxy_radio)
        proxy_layout.addWidget(self.system_proxy_radio)
        proxy_layout.addWidget(self.proxy_ip_radio)
        self.right_layout.addLayout(proxy_layout)
        
        # 代理IP选择区域
        self.proxy_ip_widget = QWidget()
        proxy_ip_layout = QVBoxLayout(self.proxy_ip_widget)
        
        proxy_note = BodyLabel('从IP池中选择代理IP')
        proxy_ip_layout.addWidget(proxy_note)
        
        # 搜索框
        self.ip_search = SearchLineEdit(self)
        self.ip_search.setPlaceholderText('搜索IP地址')
        proxy_ip_layout.addWidget(self.ip_search)
        
        # IP列表
        self.ip_list = QListWidget()
        self.ip_list.setMinimumHeight(150)
        proxy_ip_layout.addWidget(self.ip_list)
        
        # 刷新IP池按钮
        self.refresh_ip_btn = PushButton('刷新IP列表')
        proxy_ip_layout.addWidget(self.refresh_ip_btn)
        
        self.proxy_ip_widget.hide()
        self.right_layout.addWidget(self.proxy_ip_widget)
        
        # 消息和邀请限制设置
        self.right_layout.addWidget(SubtitleLabel("消息和邀请限制"))
        
        # 每日消息限制
        daily_msg_layout = QHBoxLayout()
        daily_msg_layout.addWidget(QLabel("单账户每日消息限制(默认10):"))
        self.daily_msg_limit = SpinBox(self)
        self.daily_msg_limit.setRange(0, 1000)  # 0 表示不限制
        self.daily_msg_limit.setSpecialValueText("10")
        daily_msg_layout.addWidget(self.daily_msg_limit)
        self.right_layout.addLayout(daily_msg_layout)
        
        # 每日邀请限制
        daily_invite_layout = QHBoxLayout()
        daily_invite_layout.addWidget(QLabel("每日邀请限制(默认10):"))
        self.daily_invite_limit = SpinBox(self)
        self.daily_invite_limit.setRange(0, 500)  # 0 表示不限制
        self.daily_invite_limit.setSpecialValueText("10")
        daily_invite_layout.addWidget(self.daily_invite_limit)
        self.right_layout.addLayout(daily_invite_layout)
        
        # 将左右两部分添加到分割器
        self.content_splitter.addWidget(self.left_widget)
        self.content_splitter.addWidget(self.right_scroll_area)
        
        # 设置初始大小比例 (左:右 = 1:2)
        self.content_splitter.setSizes([300, 600])
        
        # 将分割器添加到主布局
        self.main_layout.addWidget(self.content_splitter, 1)
        
        # 在主布局中添加底部按钮区域
        bottom_layout = QHBoxLayout()
        bottom_layout.addStretch()
        self.cancel_btn = PushButton("取消", self)
        self.save_btn = PushButton("保存", self)
        self.save_btn.setIcon(FIF.SAVE)
        
        bottom_layout.addWidget(self.cancel_btn)
        bottom_layout.addWidget(self.save_btn)
        
        # 将底部按钮布局添加到主布局
        self.main_layout.addLayout(bottom_layout)
    
    def connect_signals(self):
        """连接信号与槽"""
        self.select_all_btn.clicked.connect(self.on_select_all)
        self.cancel_select_btn.clicked.connect(self.on_cancel_select)
        self.select_avatar_btn.clicked.connect(self.on_select_avatar)
        self.save_btn.clicked.connect(self.on_save)
        self.cancel_btn.clicked.connect(lambda: self.close())
        
        # 代理选项信号
        self.default_proxy_radio.toggled.connect(self.on_proxy_radio_toggled)
        self.no_proxy_radio.toggled.connect(self.on_proxy_radio_toggled)
        self.system_proxy_radio.toggled.connect(self.on_proxy_radio_toggled)
        self.proxy_ip_radio.toggled.connect(self.on_proxy_ip_radio_toggled)
        
        # IP搜索框信号
        self.ip_search.textChanged.connect(self.on_ip_search_changed)
        
        # 分组筛选信号
        self.group_filter_combo.currentIndexChanged.connect(self.on_group_filter_changed)
        
        # 手机号筛选信号
        self.phone_filter.textChanged.connect(self.filter_users)
        
        # 新增：点击左侧账户列表项时刷新右侧
        self.user_list.itemClicked.connect(self.on_user_item_clicked)
        # 新增：刷新IP池按钮
        self.refresh_ip_btn.clicked.connect(self.load_proxy_ips)
        
        # 用户名帮助按钮
        self.username_help_btn.clicked.connect(self.show_username_help)
        
        # 连接account_controller的信号（如果存在）
        if self.account_controller:
            self.account_controller.loading_started.connect(self.on_loading_started)
            self.account_controller.loading_finished.connect(self.on_loading_finished)
            self.account_controller.operation_failed.connect(self.on_operation_failed)
            self.account_controller.profile_updated.connect(self.on_profile_updated)
            self.account_controller.profiles_batch_updated.connect(self.on_batch_updated)
    
    def on_group_filter_changed(self, index):
        """分组筛选下拉菜单变更事件"""
        selected_group = self.group_filter_combo.currentData()
        self.group_filter_changed.emit(selected_group)
        self.apply_filters()
    
    def on_proxy_radio_toggled(self, checked):
        """代理单选按钮状态变化处理"""
        # 当选择禁用代理或系统代理时，确保IP池区域隐藏
        if self.no_proxy_radio.isChecked() or self.system_proxy_radio.isChecked():
            self.proxy_ip_widget.hide()
        # 发出信号
        self.proxy_toggled.emit(checked)
    
    def on_proxy_ip_radio_toggled(self, checked):
        """代理IP单选按钮状态变化处理"""
        if checked:
            self.proxy_ip_widget.show()
            # 新增：异步加载代理IP池
            self.load_proxy_ips()
            self.proxy_toggled.emit(True)
            print("代理IP单选按钮被选中，显示代理IP区域")
        else:
            self.proxy_ip_widget.hide()
            print("代理IP单选按钮被取消选中，隐藏代理IP区域")
    
    def on_ip_search_changed(self, text):
        """IP搜索框文本改变时过滤IP列表"""
        for i in range(self.ip_list.count()):
            item = self.ip_list.item(i)
            if text.lower() in item.text().lower():
                item.setHidden(False)
            else:
                item.setHidden(True)
    
    def update_ip_list(self, ip_list):
        """更新IP池列表

        Args:
            ip_list: 包含IP信息的列表，每个元素应该包含id、ip、port和account_count
        """
        self.ip_list.clear()
        for ip_info in ip_list:
            account_count = ip_info.get('account_count', 0)
            ip_text = f"{ip_info['ip']}:{ip_info['port']} (已绑定{account_count}个账户)"
            item = QListWidgetItem(ip_text)
            item.setData(Qt.UserRole, ip_info)  # 存储完整IP信息
            self.ip_list.addItem(item)
    
    def set_default_avatar(self):
        """设置默认头像"""
        # 创建一个128x128的空白图片
        pixmap = QPixmap(128, 128)
        pixmap.fill(QColor("#f0f0f0"))  # 浅灰色背景
        
        # 创建画笔
        painter = QPainter(pixmap)
        painter.setPen(QPen(QColor("#cccccc")))
        painter.setBrush(QBrush(QColor("#cccccc")))
        
        # 画圆形作为头像占位符
        painter.setRenderHint(QPainter.Antialiasing)
        painter.drawEllipse(24, 24, 80, 80)
        
        # 添加用户图标
        font = QFont("Segoe MDL2 Assets", 40)  # Windows 10 UI图标字体
        painter.setFont(font)
        painter.setPen(QPen(QColor("#ffffff")))
        painter.drawText(pixmap.rect(), Qt.AlignmentFlag.AlignCenter, "👤")
        
        painter.end()
        
        # 设置默认头像
        self.avatar_label.setPixmap(pixmap)
    
    def set_users(self, users):
        """设置用户列表

        Args:
            users: 用户数据列表，每个元素应包含phone和username字段
        """
        self._original_user_data = {}  # 批量时以“空”为基准
        self._is_batch_mode = True
        self.all_users = users.copy()  # 保存所有用户的原始数据
        # print(self.all_users)
        self.apply_filters()  # 应用当前的筛选条件
    
    def set_group_options(self, groups):
        """设置分组选项
        Args:
            groups: 分组对象列表，每个元素应包含'id'和'name'
        """
        self.group_filter_combo.clear()
        self.group_filter_combo.addItem("全部分组", userData = "all")
        for group in groups:
            self.group_filter_combo.addItem(group['name'], userData = group['id'])
        self.group_filter_combo.setCurrentIndex(0)
    
    def apply_filters(self):
        """应用筛选条件（分组和手机号）"""
        selected_group_id = self.group_filter_combo.currentData()
        phone_filter = self.phone_filter.text().lower()
        self.filtered_users = []
        for user in self.all_users:
            if selected_group_id != "all":
                if user.get('group_id') != selected_group_id:
                    continue
            if phone_filter and phone_filter not in user.get('phone', '').lower():
                continue
            self.filtered_users.append(user)
        self.update_user_list()
    
    def update_user_list(self):
        """更新用户列表显示"""
        self.user_list.clear()
        for user in self.filtered_users:
            item = QListWidgetItem(self.user_list)
            # 创建一个容器小部件
            widget = QWidget()
            layout = QHBoxLayout(widget)
            layout.setContentsMargins(5, 5, 5, 5)
            # 添加复选框
            checkbox = CheckBox()
            layout.addWidget(checkbox, 0)
            # 添加手机号|昵称标签
            phone = user.get('phone', '')
            username = user.get('username', '')
            label_text = f"{phone}|{username}" if username else phone
            phone_label = CaptionLabel(label_text)
            layout.addWidget(phone_label, 1)
            # 使小部件填满整个项
            item.setSizeHint(widget.sizeHint())
            self.user_list.setItemWidget(item, widget)
    
    def on_select_all(self):
        """全选按钮点击事件"""
        all_checked = True
        
        # 检查是否所有项都已选中
        for i in range(self.user_list.count()):
            item = self.user_list.item(i)
            widget = self.user_list.itemWidget(item)
            checkbox = widget.layout().itemAt(0).widget()
            if not checkbox.isChecked():
                all_checked = False
                break
        
        # 根据当前状态切换
        new_state = not all_checked
        
        # 设置所有复选框状态
        for i in range(self.user_list.count()):
            item = self.user_list.item(i)
            widget = self.user_list.itemWidget(item)
            checkbox = widget.layout().itemAt(0).widget()
            checkbox.setChecked(new_state)
        
        # 发出信号
        self.select_all_clicked.emit(new_state)
    
    def on_cancel_select(self):
        """取消选择按钮点击事件"""
        # 取消所有选择
        for i in range(self.user_list.count()):
            item = self.user_list.item(i)
            widget = self.user_list.itemWidget(item)
            checkbox = widget.layout().itemAt(0).widget()
            checkbox.setChecked(False)
    
    def on_select_avatar(self):
        """选择头像按钮点击事件"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择图片", "", "图片文件 (*.jpg *.jpeg *.png)"
        )
        
        if file_path:
            self.avatar_path = file_path
            pixmap = QPixmap(file_path)
            if not pixmap.isNull():
                self.avatar_label.setPixmap(pixmap.scaled(
                    128, 128, Qt.AspectRatioMode.KeepAspectRatio, Qt.TransformationMode.SmoothTransformation))
            else:
                QMessageBox.warning(self, "错误", "无法加载所选图片")
    
    def get_proxy_settings(self):
        """获取代理设置"""
        if self.default_proxy_radio.isChecked():
            return None  # 默认不修改
        elif self.no_proxy_radio.isChecked():
            return {"type": "none"}
        elif self.system_proxy_radio.isChecked():
            return {"type": "system"}
        elif self.proxy_ip_radio.isChecked():
            selected_items = self.ip_list.selectedItems()
            if selected_items:
                ip_info = selected_items[0].data(Qt.UserRole)
                return {
                    "id": ip_info['id'],
                    "type": "ip_pool",
                    "proxy_type": ip_info['proxy_type'],
                    "ip": ip_info['ip'],
                    "port": ip_info['port'],
                    "username": ip_info.get('username', ''),
                    "password": ip_info.get('password', '')
                }
            else:
                return {"type": "ip_pool", "error": "no_ip_selected"}
        return None
    
    def set_proxy_settings(self, proxy_settings):
        """设置代理选项 - 始终默认为"不修改" """
        # 始终默认选择"默认（不修改）"选项
        self.default_proxy_radio.setChecked(True)
        self.no_proxy_radio.setChecked(False)
        self.system_proxy_radio.setChecked(False)
        self.proxy_ip_radio.setChecked(False)
        
        # 注意：不再根据现有代理设置自动选择选项
        # 用户需要主动选择是否要更改代理设置
        
        # 但是如果用户选择了代理IP选项，仍需要在列表中预选对应的IP
        if proxy_settings and proxy_settings.get('type') == 'ip_pool':
            # 在IP列表中预选对应的IP项（但不自动选择代理IP选项）
            proxy_id = proxy_settings.get('proxy_id')
            if proxy_id:
                # 根据代理ID查找匹配的IP项
                for i in range(self.ip_list.count()):
                    item = self.ip_list.item(i)
                    ip_info = item.data(Qt.UserRole)
                    if ip_info.get('id') == proxy_id:
                        self.ip_list.setCurrentItem(item)
                        break
            else:
                # 兼容旧格式：根据IP和端口查找
                ip = proxy_settings.get('ip', '')
                port = proxy_settings.get('port', '')
                if ip and port:
                    for i in range(self.ip_list.count()):
                        item = self.ip_list.item(i)
                        ip_info = item.data(Qt.UserRole)
                        if ip_info['ip'] == ip and str(ip_info['port']) == str(port):
                            self.ip_list.setCurrentItem(item)
                            break
    
    def collect_changed_fields(self):
        """收集变更字段，根据模式使用不同逻辑"""
        if self._is_batch_mode:
            return self.collect_batch_fields()
        else:
            return self.collect_single_changed_fields()
    
    def collect_single_changed_fields(self):
        """收集单个编辑模式下的变更字段"""
        changed_data = {}
        orig = self._original_user_data or {}
        # 姓
        if self.last_name_edit.text() != orig.get('last_name', ''):
            changed_data['last_name'] = self.last_name_edit.text()
        # 名
        if self.first_name_edit.text() != orig.get('first_name', ''):
            changed_data['first_name'] = self.first_name_edit.text()
        # 用户名
        if self.username_edit.text() != orig.get('username', ''):
            changed_data['username'] = self.username_edit.text()
        # 简介
        if self.bio_edit.toPlainText() != orig.get('bio', ''):
            changed_data['bio'] = self.bio_edit.toPlainText()
        # 头像
        if self.avatar_path and self.avatar_path != orig.get('avatar_path', ''):
            changed_data['profile_photo'] = self.avatar_path  # 使用 profile_photo 字段
        # 分组
        group_id = self.user_group_combo.currentData()
        if group_id != orig.get('group_id'):
            changed_data['group_id'] = group_id
        # 代理
        proxy_settings = self.get_proxy_settings()
        if proxy_settings is not None:
            # 检查代理设置是否有变化
            orig_proxy_type = orig.get('proxy_type', '')
            orig_proxy_id = orig.get('proxy_id')
            
            new_proxy_type = proxy_settings.get('type', '')
            new_proxy_id = proxy_settings.get('id')
            
            # 如果代理类型或代理ID有变化，则记录变更
            if (new_proxy_type != orig_proxy_type or
                    new_proxy_id != orig_proxy_id):
                changed_data['proxy'] = proxy_settings
        # 消息限制 (0表示不限制，默认为10)
        daily_msg_limit = self.daily_msg_limit.value()
        if daily_msg_limit != orig.get('daily_msg_limit'):
            changed_data['daily_msg_limit'] = daily_msg_limit
        daily_invite_limit = self.daily_invite_limit.value()
        if daily_invite_limit != orig.get('daily_invite_limit'):
            changed_data['daily_invite_limit'] = daily_invite_limit
        # 手机号（只读，单个时可带上）
        changed_data['phone'] = orig.get('phone')
        return changed_data
    
    def collect_batch_fields(self):
        """收集批量编辑模式下的所有字段值（不做变更检测）"""
        batch_data = {}
        
        # 姓 - 直接获取当前值
        last_name = self.last_name_edit.text().strip()
        if last_name:  # 只有非空时才设置
            batch_data['last_name'] = last_name
        
        # 名 - 直接获取当前值
        first_name = self.first_name_edit.text().strip()
        if first_name:  # 只有非空时才设置
            batch_data['first_name'] = first_name
        
        # 用户名 - 直接获取当前值
        username = self.username_edit.text().strip()
        if username:  # 只有非空时才设置
            batch_data['username'] = username
        
        # 简介 - 直接获取当前值
        bio = self.bio_edit.toPlainText().strip()
        if bio:  # 只有非空时才设置
            batch_data['bio'] = bio
        
        # 头像 - 如果选择了头像文件
        if self.avatar_path:
            batch_data['profile_photo'] = self.avatar_path
        
        # 分组 - 直接获取当前选择
        group_id = self.user_group_combo.currentData()
        if group_id is not None:  # 允许设置为0（默认分组）
            batch_data['group_id'] = group_id
        
        # 代理设置 - 直接获取当前设置
        proxy_settings = self.get_proxy_settings()
        if proxy_settings is not None:
            batch_data['proxy'] = proxy_settings
        
        # 消息限制 - 直接获取当前值
        daily_msg_limit = self.daily_msg_limit.value()
        batch_data['daily_msg_limit'] = daily_msg_limit
        
        # 邀请限制 - 直接获取当前值
        daily_invite_limit = self.daily_invite_limit.value()
        batch_data['daily_invite_limit'] = daily_invite_limit
        
        # 添加选中的用户列表
        batch_data['selected_users'] = self.get_selected_users()
        
        return batch_data
    
    @asyncSlot()
    async def on_save(self):
        """保存按钮点击事件 - 直接调用controller进行保存"""
        if not self.account_controller:
            self.show_error("控制器未初始化，无法保存")
            return
        
        try:
            # 显示浮动日志并清空之前的日志
            self.show_floating_log()
            self.clear_log()
            self.log_step("开始保存用户资料")
            
            # 收集变更数据
            self.log_info("正在收集变更数据...")
            changed_data = self.collect_changed_fields()
            
            if not changed_data or (
                    len(changed_data) <= 1 and ('phone' in changed_data or 'selected_users' in changed_data)):
                self.log_warning("未检测到任何更改")
                self.show_warning("未检测到任何更改")
                return
            
            self.log_success(f"已收集到 {len(changed_data)} 个字段的变更")
            
            # 验证必要字段
            if self._is_batch_mode:
                selected_users = changed_data.get('selected_users', [])
                if not selected_users:
                    self.log_error("未选择要批量编辑的账户")
                    self.show_warning("请先选择要批量编辑的账户")
                    return
                if len(changed_data) <= 1:  # 只有 selected_users 字段
                    self.log_warning("未修改任何字段")
                    self.show_warning("请至少修改一个字段")
                    return
                self.log_info(f"已选择 {len(selected_users)} 个账户进行批量编辑")
            
            # 验证代理设置
            proxy_settings = changed_data.get('proxy')
            if proxy_settings:
                self.log_info("正在验证代理设置...")
                if proxy_settings.get('type') == 'ip_pool':
                    if proxy_settings.get('error') == 'no_ip_selected':
                        self.log_error("代理IP未选择")
                        self.show_warning("请选择一个代理IP")
                        return
                    self.log_success("代理设置验证通过")
            
            # 开始保存操作
            self.log_step("开始执行保存操作")
            if self._is_batch_mode:
                await self._save_batch_edit(changed_data)
            else:
                await self._save_single_edit(changed_data)
        
        except Exception as e:
            self.log_error(f"保存过程中发生异常: {str(e)}")
            self.show_error(f"保存时发生错误: {str(e)}")
    
    def show_warning(self, message: str):
        """显示警告信息"""
        InfoBar.warning(
            title = "警告",
            content = message,
            orient = Qt.Horizontal,
            isClosable = True,
            position = InfoBarPosition.TOP,
            duration = 3000,
            parent = self
        )
    
    def show_error(self, message: str):
        """显示错误信息"""
        InfoBar.error(
            title = "错误",
            content = message,
            orient = Qt.Horizontal,
            isClosable = True,
            position = InfoBarPosition.TOP,
            duration = 5000,
            parent = self
        )
    
    def get_selected_users(self):
        """获取所有已选择的用户手机号"""
        selected_phones = []
        
        for i in range(self.user_list.count()):
            item = self.user_list.item(i)
            widget = self.user_list.itemWidget(item)
            checkbox = widget.layout().itemAt(0).widget()
            phone_label = widget.layout().itemAt(1).widget()
            
            if checkbox.isChecked():
                # 解析手机号（格式：手机号|昵称）
                phone_text = phone_label.text()
                phone = phone_text.split('|')[0].strip()  # 提取手机号部分
                selected_phones.append(phone)
        
        return selected_phones
    
    def filter_users(self, text):
        """根据输入筛选用户列表 (废弃，使用apply_filters代替)"""
        self.apply_filters()
    
    def set_user_group_options(self, groups):
        """设置右侧分组下拉内容
        Args:
            groups: 分组对象列表，每个元素应包含'id'和'name'
        """
        self.user_group_combo.clear()
        for group in groups:
            self.user_group_combo.addItem(group['name'], userData = group['id'])
    
    def set_user_data(self, user_data):
        """设置用户数据到表单
        Args:
            user_data: 用户数据字典，包含个人资料信息
        """
        self._original_user_data = user_data.copy()  # 保存原始数据
        self._is_batch_mode = False
        # 设置手机号
        self.phone_label.setText(user_data.get('phone', ''))
        self.last_name_edit.setText(user_data.get('last_name', ''))
        self.first_name_edit.setText(user_data.get('first_name', ''))
        self.username_edit.setText(user_data.get('username', ''))
        self.bio_edit.setPlainText(user_data.get('bio', ''))
        
        # 设置头像
        avatar_path = user_data.get('avatar_path', '')
        if avatar_path:
            self.avatar_path = avatar_path
            pixmap = QPixmap(avatar_path)
            if not pixmap.isNull():
                self.avatar_label.setPixmap(pixmap.scaled(
                    128, 128, Qt.AspectRatioMode.KeepAspectRatio, Qt.TransformationMode.SmoothTransformation))
            else:
                self.set_default_avatar()
        else:
            self.set_default_avatar()
        
        # 设置分组 - 根据group_id选中对应分组
        group_id = user_data.get('group_id')
        if group_id is not None:
            index = self.user_group_combo.findData(group_id)
            if index >= 0:
                self.user_group_combo.setCurrentIndex(index)
        
        # 设置代理 - 始终默认为"不修改"
        proxy_type = user_data.get('proxy_type', '')
        proxy_id = user_data.get('proxy_id')
        
        # 始终默认选择"不修改"，但在IP列表中预选用户当前的代理IP（如果有的话）
        if proxy_type == 'ip_pool' and proxy_id:
            # 对于IP池代理，在IP列表中预选对应的项，但仍默认选择"不修改"
            self.set_proxy_settings({"type": "ip_pool", "proxy_id": proxy_id})
        else:
            # 其他情况都默认为"不修改"
            self.set_proxy_settings(None)
        
        # 设置消息和邀请限制
        daily_msg_limit = user_data.get('daily_msg_limit', 10)
        self.daily_msg_limit.setValue(daily_msg_limit if daily_msg_limit is not None else 10)
        
        daily_invite_limit = user_data.get('daily_invite_limit', 10)
        self.daily_invite_limit.setValue(daily_invite_limit if daily_invite_limit is not None else 10)
    
    def clear_form(self):
        """清空表单"""
        self.phone_label.clear()
        self.last_name_edit.clear()
        self.first_name_edit.clear()
        self.username_edit.clear()
        self.bio_edit.clear()
        self.avatar_path = ""
        self.set_default_avatar()
        self.user_group_combo.setCurrentIndex(0)
        
        # 重置代理设置为默认（不修改）
        self.default_proxy_radio.setChecked(True)
        self.no_proxy_radio.setChecked(False)
        self.system_proxy_radio.setChecked(False)
        self.proxy_ip_radio.setChecked(False)
        
        # 重置消息和邀请限制为默认值
        self.daily_msg_limit.setValue(10)
        self.daily_invite_limit.setValue(10)
        
        # 取消所有选择
        self.on_cancel_select()
    
    def on_user_item_clicked(self, item):
        """点击左侧账户列表项时，右侧刷新显示对应账户信息"""
        # 获取手机号|昵称文本
        widget = self.user_list.itemWidget(item)
        if widget:
            phone_label = widget.layout().itemAt(1).widget()
            phone_text = phone_label.text()
            # 解析手机号（手机号|昵称格式）
            phone = phone_text.split('|')[0].strip()
            # 查找用户数据
            user = next((u for u in self.all_users if u.get('phone') == phone), None)
            if user:
                self.set_user_data(user)
    
    async def _save_single_edit(self, changed_data):
        """处理单个账户编辑保存"""
        if not changed_data or (len(changed_data) == 1 and 'phone' in changed_data):
            self.show_info("未检测到任何更改", "info")
            return
        
        # 获取当前编辑的账户手机号
        phone = self._original_user_data.get('phone')
        if not phone:
            self.show_error("无法获取账户信息")
            return
        
        # 通过手机号查找账号ID
        account_id = self._find_account_id_by_phone(phone)
        if not account_id:
            self.show_error("未找到账号")
            return
        
        # 移除 phone 字段，仅用于查找
        changed_data.pop('phone', None)
        
        # 注意：用户名标签处理已移至服务层，UI层直接传递原始模板
        
        # 处理代理设置
        proxy_settings = changed_data.get('proxy')
        if proxy_settings:
            changed_data.update(self._process_proxy_settings(proxy_settings))
            changed_data.pop('proxy', None)
        
        # 调用控制器更新账户资料
        success = await self.account_controller.update_account_profile(account_id, changed_data)
        if success:
            self.log_success("账户资料更新成功")
            self.show_success("保存成功")
            # 更新原始数据，避免重复保存
            self._original_user_data.update(changed_data)
            # 延迟关闭日志框和对话框
            QTimer.singleShot(1500, self.close_floating_log)  # 1.5秒后关闭日志框
            QTimer.singleShot(2000, lambda: self.accept())  # 2秒后关闭对话框
        else:
            self.log_error("账户资料更新失败")
            self.show_error("保存失败")
    
    async def _save_batch_edit(self, changed_data):
        """处理批量编辑保存"""
        selected_phones = changed_data.get('selected_users', [])
        if not selected_phones:
            self.show_warning("请先选择要批量编辑的账户")
            return
        
        # 获取选中账户ID
        account_ids = []
        failed_phones = []
        
        for phone in selected_phones:
            account_id = self._find_account_id_by_phone(phone)
            if account_id:
                account_ids.append(account_id)
            else:
                failed_phones.append(phone)
        
        self.log_info(f"选中 {len(selected_phones)} 个账户，成功找到 {len(account_ids)} 个账户ID")
        if failed_phones:
            self.log_warning(f"无法找到以下账户的ID: {failed_phones}")
        
        if not account_ids:
            self.log_error("没有找到任何有效的账户ID")
            self.show_warning("没有找到有效的账户，请检查选择的账户")
            return
        
        # 移除 selected_users 字段
        changed_data.pop('selected_users', None)
        
        # 处理代理设置
        proxy_settings = changed_data.get('proxy')
        if proxy_settings:
            changed_data.update(self._process_proxy_settings(proxy_settings))
            changed_data.pop('proxy', None)
        
        # 注意：用户名标签处理已移至服务层，UI层直接传递原始模板
        
        if not changed_data:
            self.show_warning("没有需要更新的数据")
            return
        
        # 显示批量更新的详细信息
        self.log_step(f"准备批量更新 {len(account_ids)} 个账户")
        if 'username' in changed_data:
            username_template = changed_data['username']
            if '{' in username_template and '}' in username_template:
                self.log_info(f"检测到用户名模板: {username_template}")
                self.log_info("系统将为每个账户生成唯一的用户名")
        
        # 调用控制器批量更新（服务层会自动处理用户名标签）
        success_count, fail_count, message = await self.account_controller.batch_update_profiles(account_ids,
                                                                                                 changed_data)
        if success_count > 0:
            self.log_success(f"批量更新成功: {success_count}/{len(account_ids)} 个账户")
            self.show_success(f"已成功更新 {success_count} 个账户，失败 {fail_count} 个")
            # 延迟关闭日志框和对话框
            QTimer.singleShot(1500, self.close_floating_log)  # 1.5秒后关闭日志框
            QTimer.singleShot(2000, lambda: self.accept())  # 2秒后关闭对话框
        else:
            self.log_error(f"批量更新失败: {message}")
            self.show_error(f"批量更新失败: {message}")
    
    def _find_account_id_by_phone(self, phone):
        """通过手机号查找账户ID"""
        # 从all_users中查找
        for user in self.all_users:
            if user.get('phone') == phone:
                return user.get('id')
        # 如果是单个编辑模式，从原始数据中获取
        if not self._is_batch_mode and self._original_user_data:
            if self._original_user_data.get('phone') == phone:
                return self._original_user_data.get('id')
        return None
    
    def _process_proxy_settings(self, proxy_settings):
        """处理代理设置，转换为数据库格式"""
        result = {}
        proxy_type = proxy_settings.get('type')
        
        if proxy_type == 'none':
            result['proxy_type'] = 'none'
            result['proxy_id'] = None
        elif proxy_type == 'system':
            result['proxy_type'] = 'system'
            result['proxy_id'] = None
        elif proxy_type == 'ip_pool':
            result['proxy_type'] = 'ip_pool'
            result['proxy_id'] = proxy_settings.get('id')
            # 确保proxy_id不为空
            if not result['proxy_id']:
                raise ValueError("代理IP池设置缺少代理ID")
        else:
            result['proxy_type'] = 'none'
            result['proxy_id'] = None
        
        return result
    
    # 注意：用户名标签检查功能已移至服务层处理
    
    # 注意：批量用户名标签处理功能已移至服务层处理
    
    @asyncSlot()
    async def load_proxy_ips(self):
        """异步加载代理IP池数据并更新列表"""
        if self.account_controller:
            try:
                proxy_ips = await self.account_controller.get_proxy_ips()
                self.update_ip_list(proxy_ips)
            except Exception as e:
                print(f"加载代理IP失败: {e}")
        else:
            print("account_controller 未初始化")
    
    # ================ Controller信号处理方法 ================
    
    def on_loading_started(self, message):
        """处理加载开始信号"""
        self.save_btn.setEnabled(False)
        self.save_btn.setText("保存中...")
        self.show_info(message, "info")
    
    def on_loading_finished(self):
        """处理加载完成信号"""
        self.save_btn.setEnabled(True)
        self.save_btn.setText("保存")
    
    def on_operation_failed(self, message):
        """处理操作失败信号"""
        self.show_error(message)
    
    def on_profile_updated(self, account_id, success, message):
        """处理单个账户资料更新结果"""
        if success:
            self.show_success(message)
        else:
            self.show_error(message)
    
    def on_batch_updated(self, success_count, fail_count, message):
        """处理批量更新结果"""
        if success_count > 0:
            self.show_success(f"批量更新完成: 成功 {success_count} 个, 失败 {fail_count} 个")
        else:
            self.show_error(f"批量更新失败: {message}")
    
    # ================ 消息显示方法 ================
    
    def show_info(self, message, info_type = "info"):
        """显示信息提示"""
        InfoBar.info(
            title = "提示",
            content = message,
            orient = Qt.Orientation.Horizontal,
            isClosable = True,
            position = InfoBarPosition.TOP,
            duration = 3000,
            parent = self
        )
    
    def show_success(self, message):
        """显示成功提示"""
        InfoBar.success(
            title = "成功",
            content = message,
            orient = Qt.Orientation.Horizontal,
            isClosable = True,
            position = InfoBarPosition.TOP,
            duration = 3000,
            parent = self
        )
    
    def show_warning(self, message):
        """显示警告提示"""
        InfoBar.warning(
            title = "警告",
            content = message,
            orient = Qt.Orientation.Horizontal,
            isClosable = True,
            position = InfoBarPosition.TOP,
            duration = 3000,
            parent = self
        )
    
    def show_error(self, message):
        """显示错误提示"""
        InfoBar.error(
            title = "错误",
            content = message,
            orient = Qt.Orientation.Horizontal,
            isClosable = True,
            position = InfoBarPosition.TOP,
            duration = 5000,
            parent = self
        )
    
    # ================ 用户名标签功能 ================
    
    def show_username_help(self):
        """显示用户名标签使用说明"""
        help_text = """
用户名标签使用说明：

在设置用户名时，可以使用以下标签来生成唯一的用户名：

🔢 {数字1-3}
   生成1-3位随机数字
   示例：user{数字1-3} → user123, user45, user7

🔤 {字母1-5}
   生成1-5位随机字母（小写）
   示例：test{字母1-5} → testabcd, testxy, testqwert

🔀 {数字字母1-6}
   生成1-6位随机数字字母组合
   示例：name{数字字母1-6} → name1a2b3c, namexy9, namea1b2

💡 使用技巧：
• 可以组合多个标签：user{数字1-2}_{字母1-3}
• 建议在用户名前加固定前缀避免冲突
• 系统会自动处理用户名冲突并重新生成

⚠️ 注意事项：
• 用户名必须全局唯一
• 只能包含字母、数字和下划线
• 长度限制：5-32个字符
• 如果生成失败会自动重试
        """
        
        msg = QMessageBox(self)
        msg.setWindowTitle("用户名标签说明")
        msg.setText(help_text)
        msg.setIcon(QMessageBox.Icon.Information)
        msg.exec()
    
    # 注意：用户名标签解析功能已移至服务层处理
    # UI层只负责收集用户输入，服务层负责处理标签和冲突重试
    
    def validate_username(self, username):
        """验证用户名格式

        Args:
            username: 用户名字符串

        Returns:
            (是否有效, 错误消息)
        """
        if not username:
            return True, ""  # 空用户名是允许的
        
        # 检查长度
        if len(username) < 5:
            return False, "用户名长度不能少于5个字符"
        if len(username) > 32:
            return False, "用户名长度不能超过32个字符"
        
        # 检查字符
        if not re.match(r'^[a-zA-Z0-9_]+$', username):
            return False, "用户名只能包含字母、数字和下划线"
        
        # 检查开头
        if username[0].isdigit():
            return False, "用户名不能以数字开头"
        
        return True, ""
    
    # ================ 浮动日志管理功能 ================
    
    def show_floating_log(self):
        """显示浮动日志"""
        if hasattr(self, 'floating_log'):
            # 设置相对位置，传入self作为参考
            self.floating_log.set_position_relative_to_parent(self)
            self.floating_log.show_widget()
    
    def hide_floating_log(self):
        """隐藏浮动日志"""
        if hasattr(self, 'floating_log'):
            self.floating_log.minimize_widget()
    
    def close_floating_log(self):
        """关闭浮动日志"""
        if hasattr(self, 'floating_log'):
            self.floating_log.close_widget()
    
    def log_step(self, step_name: str):
        """记录操作步骤"""
        if hasattr(self, 'floating_log'):
            self.floating_log.log_step(step_name)
    
    def log_success(self, message: str):
        """记录成功消息"""
        if hasattr(self, 'floating_log'):
            self.floating_log.log_success(message)
    
    def log_error(self, message: str):
        """记录错误消息"""
        if hasattr(self, 'floating_log'):
            self.floating_log.log_error(message)
    
    def log_warning(self, message: str):
        """记录警告消息"""
        if hasattr(self, 'floating_log'):
            self.floating_log.log_warning(message)
    
    def log_info(self, message: str):
        """记录信息消息"""
        if hasattr(self, 'floating_log'):
            self.floating_log.log_info(message)
    
    def clear_log(self):
        """清空日志"""
        if hasattr(self, 'floating_log'):
            self.floating_log.clear_log()
    
    def insert_tag(self, tag):
        """插入标签到用户名输入框

        Args:
            tag: 要插入的标签字符串
        """
        current_text = self.username_edit.text()
        cursor_position = self.username_edit.cursorPosition()
        
        # 在光标位置插入标签
        new_text = current_text[:cursor_position] + tag + current_text[cursor_position:]
        self.username_edit.setText(new_text)
        
        # 将光标移动到标签后面
        self.username_edit.setCursorPosition(cursor_position + len(tag))
        
        # 聚焦到输入框
        self.username_edit.setFocus()


if __name__ == "__main__":
    import sys
    from PySide6.QtWidgets import QApplication
    
    app = QApplication(sys.argv)
    window = UserProfileEditUI()
    window.show()
    sys.exit(app.exec())


