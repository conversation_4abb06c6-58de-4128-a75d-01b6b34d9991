from PySide6.QtWidgets import <PERSON><PERSON>ox<PERSON>ayout, QHBoxLayout, QFrame, QWidget, QDialog, QSizePolicy, QFormLayout
from PySide6.QtCore import Qt, QMetaObject, QCoreApplication
from qfluentwidgets import ElevatedCardWidget, SubtitleLabel, CardWidget, HyperlinkLabel, PrimaryPushButton, BodyLabel, \
    CaptionLabel, TransparentPushButton, VerticalSeparator, HorizontalSeparator, AvatarWidget, PushButton


class UserInfoUI(QDialog):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle('个人信息')
        self.setFixedSize(400, 480)
        self.setWindowFlags(Qt.Dialog | Qt.WindowCloseButtonHint)
        self.setStyleSheet('background: #F8F8F8; border-radius: 12px;')
        self.setupUi(self)
    
    def setupUi(self, Form):
        if not Form.objectName():
            Form.setObjectName(u"Form")
        Form.resize(308, 430)
        self.verticalLayout_3 = QVBoxLayout(Form)
        self.verticalLayout_3.setObjectName(u"verticalLayout_3")
        self.ElevatedCardWidget = ElevatedCardWidget(Form)
        self.ElevatedCardWidget.setObjectName(u"ElevatedCardWidget")
        sizePolicy = QSizePolicy(QSizePolicy.Preferred, QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.ElevatedCardWidget.sizePolicy().hasHeightForWidth())
        self.ElevatedCardWidget.setSizePolicy(sizePolicy)
        self.horizontalLayout_4 = QHBoxLayout(self.ElevatedCardWidget)
        self.horizontalLayout_4.setObjectName(u"horizontalLayout_4")
        self.AvatarWidget = AvatarWidget(self.ElevatedCardWidget)
        self.AvatarWidget.setObjectName(u"AvatarWidget")

        self.horizontalLayout_4.addWidget(self.AvatarWidget)

        self.verticalLayout = QVBoxLayout()
        self.verticalLayout.setObjectName(u"verticalLayout")
        self.username = SubtitleLabel(self.ElevatedCardWidget)
        self.username.setObjectName(u"username")
        sizePolicy.setHeightForWidth(self.username.sizePolicy().hasHeightForWidth())
        self.username.setSizePolicy(sizePolicy)

        self.verticalLayout.addWidget(self.username)

        self.UserType = HyperlinkLabel(self.ElevatedCardWidget)
        self.UserType.setObjectName(u"UserType")

        self.verticalLayout.addWidget(self.UserType)


        self.horizontalLayout_4.addLayout(self.verticalLayout)


        self.verticalLayout_3.addWidget(self.ElevatedCardWidget)

        self.ElevatedCardWidget_2 = ElevatedCardWidget(Form)
        self.ElevatedCardWidget_2.setObjectName(u"ElevatedCardWidget_2")
        sizePolicy.setHeightForWidth(self.ElevatedCardWidget_2.sizePolicy().hasHeightForWidth())
        self.ElevatedCardWidget_2.setSizePolicy(sizePolicy)
        self.formLayout = QFormLayout(self.ElevatedCardWidget_2)
        self.formLayout.setObjectName(u"formLayout")
        self.CaptionLabel_4 = CaptionLabel(self.ElevatedCardWidget_2)
        self.CaptionLabel_4.setObjectName(u"CaptionLabel_4")
        sizePolicy1 = QSizePolicy(QSizePolicy.Fixed, QSizePolicy.Preferred)
        sizePolicy1.setHorizontalStretch(0)
        sizePolicy1.setVerticalStretch(0)
        sizePolicy1.setHeightForWidth(self.CaptionLabel_4.sizePolicy().hasHeightForWidth())
        self.CaptionLabel_4.setSizePolicy(sizePolicy1)

        self.formLayout.setWidget(0, QFormLayout.LabelRole, self.CaptionLabel_4)

        self.horizontalLayout = QHBoxLayout()
        self.horizontalLayout.setObjectName(u"horizontalLayout")
        self.VipGroup = HyperlinkLabel(self.ElevatedCardWidget_2)
        self.VipGroup.setObjectName(u"VipGroup")
        sizePolicy.setHeightForWidth(self.VipGroup.sizePolicy().hasHeightForWidth())
        self.VipGroup.setSizePolicy(sizePolicy)

        self.horizontalLayout.addWidget(self.VipGroup)

        self.ActivateCode = PrimaryPushButton(self.ElevatedCardWidget_2)
        self.ActivateCode.setObjectName(u"ActivateCode")

        self.horizontalLayout.addWidget(self.ActivateCode)


        self.formLayout.setLayout(0, QFormLayout.FieldRole, self.horizontalLayout)

        self.BodyLabel_4 = BodyLabel(self.ElevatedCardWidget_2)
        self.BodyLabel_4.setObjectName(u"BodyLabel_4")
        sizePolicy1.setHeightForWidth(self.BodyLabel_4.sizePolicy().hasHeightForWidth())
        self.BodyLabel_4.setSizePolicy(sizePolicy1)

        self.formLayout.setWidget(1, QFormLayout.LabelRole, self.BodyLabel_4)

        self.EndTime = CaptionLabel(self.ElevatedCardWidget_2)
        self.EndTime.setObjectName(u"EndTime")
        sizePolicy.setHeightForWidth(self.EndTime.sizePolicy().hasHeightForWidth())
        self.EndTime.setSizePolicy(sizePolicy)

        self.formLayout.setWidget(1, QFormLayout.FieldRole, self.EndTime)

        self.InviterLabel = BodyLabel(self.ElevatedCardWidget_2)
        self.InviterLabel.setObjectName(u"InviterLabel")
        self.InviterValue = CaptionLabel(self.ElevatedCardWidget_2)
        self.InviterValue.setObjectName(u"InviterValue")
        self.formLayout.setWidget(2, QFormLayout.FieldRole, self.InviterValue)

        self.formLayout.setWidget(2, QFormLayout.LabelRole, self.InviterLabel)

        self.formLayout.setWidget(2, QFormLayout.FieldRole, self.InviterValue)


        self.verticalLayout_3.addWidget(self.ElevatedCardWidget_2)

        self.CardWidget = CardWidget(Form)
        self.CardWidget.setObjectName(u"CardWidget")
        self.verticalLayout_2 = QVBoxLayout(self.CardWidget)
        self.verticalLayout_2.setObjectName(u"verticalLayout_2")
        self.formLayout_2 = QFormLayout()
        self.formLayout_2.setObjectName(u"formLayout_2")
        self.BodyLabel_7 = BodyLabel(self.CardWidget)
        self.BodyLabel_7.setObjectName(u"BodyLabel_7")
        sizePolicy1.setHeightForWidth(self.BodyLabel_7.sizePolicy().hasHeightForWidth())
        self.BodyLabel_7.setSizePolicy(sizePolicy1)

        self.formLayout_2.setWidget(0, QFormLayout.LabelRole, self.BodyLabel_7)

        self.horizontalLayout_2 = QHBoxLayout()
        self.horizontalLayout_2.setObjectName(u"horizontalLayout_2")
        self.InviteCode = HyperlinkLabel(self.CardWidget)
        self.InviteCode.setObjectName(u"InviteCode")

        self.horizontalLayout_2.addWidget(self.InviteCode)

        self.CopyInviteCode = PushButton(self.CardWidget)
        self.CopyInviteCode.setObjectName(u"CopyInviteCode")

        self.horizontalLayout_2.addWidget(self.CopyInviteCode)


        self.formLayout_2.setLayout(0, QFormLayout.FieldRole, self.horizontalLayout_2)

        self.BodyLabel_3 = BodyLabel(self.CardWidget)
        self.BodyLabel_3.setObjectName(u"BodyLabel_3")
        sizePolicy1.setHeightForWidth(self.BodyLabel_3.sizePolicy().hasHeightForWidth())
        self.BodyLabel_3.setSizePolicy(sizePolicy1)

        self.formLayout_2.setWidget(1, QFormLayout.LabelRole, self.BodyLabel_3)

        self.horizontalLayout_5 = QHBoxLayout()
        self.horizontalLayout_5.setObjectName(u"horizontalLayout_5")
        self.Phone = CaptionLabel(self.CardWidget)
        self.Phone.setObjectName(u"Phone")
        sizePolicy2 = QSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)
        sizePolicy2.setHorizontalStretch(0)
        sizePolicy2.setVerticalStretch(0)
        sizePolicy2.setHeightForWidth(self.Phone.sizePolicy().hasHeightForWidth())
        self.Phone.setSizePolicy(sizePolicy2)

        self.horizontalLayout_5.addWidget(self.Phone)

        self.ChanagePhone = HyperlinkLabel(self.CardWidget)
        self.ChanagePhone.setObjectName(u"ChanagePhone")

        self.horizontalLayout_5.addWidget(self.ChanagePhone)


        self.formLayout_2.setLayout(1, QFormLayout.FieldRole, self.horizontalLayout_5)

        self.BodyLabel_2 = BodyLabel(self.CardWidget)
        self.BodyLabel_2.setObjectName(u"BodyLabel_2")
        sizePolicy1.setHeightForWidth(self.BodyLabel_2.sizePolicy().hasHeightForWidth())
        self.BodyLabel_2.setSizePolicy(sizePolicy1)

        self.formLayout_2.setWidget(2, QFormLayout.LabelRole, self.BodyLabel_2)

        self.horizontalLayout_6 = QHBoxLayout()
        self.horizontalLayout_6.setObjectName(u"horizontalLayout_6")
        self.CaptionLabel_3 = CaptionLabel(self.CardWidget)
        self.CaptionLabel_3.setObjectName(u"CaptionLabel_3")
        sizePolicy2.setHeightForWidth(self.CaptionLabel_3.sizePolicy().hasHeightForWidth())
        self.CaptionLabel_3.setSizePolicy(sizePolicy2)

        self.horizontalLayout_6.addWidget(self.CaptionLabel_3)

        self.ChanageEmail = HyperlinkLabel(self.CardWidget)
        self.ChanageEmail.setObjectName(u"ChanageEmail")

        self.horizontalLayout_6.addWidget(self.ChanageEmail)


        self.formLayout_2.setLayout(2, QFormLayout.FieldRole, self.horizontalLayout_6)

        self.BodyLabel = BodyLabel(self.CardWidget)
        self.BodyLabel.setObjectName(u"BodyLabel")
        sizePolicy1.setHeightForWidth(self.BodyLabel.sizePolicy().hasHeightForWidth())
        self.BodyLabel.setSizePolicy(sizePolicy1)

        self.formLayout_2.setWidget(3, QFormLayout.LabelRole, self.BodyLabel)

        self.horizontalLayout_8 = QHBoxLayout()
        self.horizontalLayout_8.setObjectName(u"horizontalLayout_8")
        self.password = CaptionLabel(self.CardWidget)
        self.password.setObjectName(u"password")
        sizePolicy2.setHeightForWidth(self.password.sizePolicy().hasHeightForWidth())
        self.password.setSizePolicy(sizePolicy2)

        self.horizontalLayout_8.addWidget(self.password)

        self.ChangePassword = HyperlinkLabel(self.CardWidget)
        self.ChangePassword.setObjectName(u"ChangePassword")

        self.horizontalLayout_8.addWidget(self.ChangePassword)


        self.formLayout_2.setLayout(3, QFormLayout.FieldRole, self.horizontalLayout_8)

        self.BodyLabel_5 = BodyLabel(self.CardWidget)
        self.BodyLabel_5.setObjectName(u"BodyLabel_5")
        sizePolicy1.setHeightForWidth(self.BodyLabel_5.sizePolicy().hasHeightForWidth())
        self.BodyLabel_5.setSizePolicy(sizePolicy1)

        self.formLayout_2.setWidget(4, QFormLayout.LabelRole, self.BodyLabel_5)

        self.horizontalLayout_10 = QHBoxLayout()
        self.horizontalLayout_10.setObjectName(u"horizontalLayout_10")
        self.CaptionLabel_6 = CaptionLabel(self.CardWidget)
        self.CaptionLabel_6.setObjectName(u"CaptionLabel_6")
        sizePolicy2.setHeightForWidth(self.CaptionLabel_6.sizePolicy().hasHeightForWidth())
        self.CaptionLabel_6.setSizePolicy(sizePolicy2)

        self.horizontalLayout_10.addWidget(self.CaptionLabel_6)

        self.ViewDocs = HyperlinkLabel(self.CardWidget)
        self.ViewDocs.setObjectName(u"ViewDocs")

        self.horizontalLayout_10.addWidget(self.ViewDocs)


        self.formLayout_2.setLayout(4, QFormLayout.FieldRole, self.horizontalLayout_10)


        self.verticalLayout_2.addLayout(self.formLayout_2)

        self.HorizontalSeparator = HorizontalSeparator(self.CardWidget)
        self.HorizontalSeparator.setObjectName(u"HorizontalSeparator")

        self.verticalLayout_2.addWidget(self.HorizontalSeparator)

        self.horizontalLayout_7 = QHBoxLayout()
        self.horizontalLayout_7.setObjectName(u"horizontalLayout_7")
        self.OnlineShop = PushButton(self.CardWidget)
        self.OnlineShop.setObjectName(u"OnlineShop")

        self.horizontalLayout_7.addWidget(self.OnlineShop)

        self.VerticalSeparator = VerticalSeparator(self.CardWidget)
        self.VerticalSeparator.setObjectName(u"VerticalSeparator")

        self.horizontalLayout_7.addWidget(self.VerticalSeparator)

        self.Layout = PushButton(self.CardWidget)
        self.Layout.setObjectName(u"Layout")

        self.horizontalLayout_7.addWidget(self.Layout)


        self.verticalLayout_2.addLayout(self.horizontalLayout_7)


        self.verticalLayout_3.addWidget(self.CardWidget)


        self.retranslateUi(Form)

        QMetaObject.connectSlotsByName(Form)
    # setupUi

    def retranslateUi(self, Form):
        Form.setWindowTitle(QCoreApplication.translate("Form", u"Form", None))
        self.username.setText(QCoreApplication.translate("Form", u"Subtitle label", None))
        self.UserType.setText(QCoreApplication.translate("Form", u"Hyperlink label", None))
        self.CaptionLabel_4.setText(QCoreApplication.translate("Form", u"VIP\u7528\u6237", None))
        self.VipGroup.setText(QCoreApplication.translate("Form", u"Hyperlink label", None))
        self.ActivateCode.setText(QCoreApplication.translate("Form", u"\u6fc0\u6d3b", None))
        self.BodyLabel_4.setText(QCoreApplication.translate("Form", u"\u4f1a\u5458\u5230\u671f\uff1a", None))
        self.EndTime.setText(QCoreApplication.translate("Form", u"\u5230\u671f\u65f6\u95f4", None))
        self.BodyLabel_7.setText(QCoreApplication.translate("Form", u"\u9080\u8bf7\u7801", None))
        self.InviteCode.setText(QCoreApplication.translate("Form", u"\u4fee\u6539", None))
        self.CopyInviteCode.setText(QCoreApplication.translate("Form", u"\u590d\u5236", None))
        self.BodyLabel_3.setText(QCoreApplication.translate("Form", u"\u624b\u673a", None))
        self.Phone.setText(QCoreApplication.translate("Form", u"\u672a\u7ed1\u5b9a", None))
        self.ChanagePhone.setText(QCoreApplication.translate("Form", u"\u4fee\u6539", None))
        self.BodyLabel_2.setText(QCoreApplication.translate("Form", u"\u90ae\u7bb1", None))
        self.CaptionLabel_3.setText(QCoreApplication.translate("Form", u"\u672a\u7ed1\u5b9a", None))
        self.ChanageEmail.setText(QCoreApplication.translate("Form", u"\u4fee\u6539", None))
        self.BodyLabel.setText(QCoreApplication.translate("Form", u"\u5bc6\u7801", None))
        self.password.setText(QCoreApplication.translate("Form", u"******", None))
        self.ChangePassword.setText(QCoreApplication.translate("Form", u"\u4fee\u6539", None))
        self.BodyLabel_5.setText(QCoreApplication.translate("Form", u"\u6559\u7a0b", None))
        self.CaptionLabel_6.setText(QCoreApplication.translate("Form", u"\u5728\u7ebf\u624b\u518c\u7b80\u5355\u4e0a\u624b", None))
        self.ViewDocs.setText(QCoreApplication.translate("Form", u"\u67e5\u770b", None))
        self.OnlineShop.setText(QCoreApplication.translate("Form", u"\u5728\u7ebf\u8d2d\u4e70", None))
        self.Layout.setText(QCoreApplication.translate("Form", u"\u9000\u51fa\u767b\u5f55", None))
        self.InviterLabel.setText(QCoreApplication.translate("Form", u"邀请人", None))
        self.InviterValue.setText(QCoreApplication.translate("Form", u"无", None))
    # retranslateUi

