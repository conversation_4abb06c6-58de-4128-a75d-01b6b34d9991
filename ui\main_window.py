# coding: utf-8
import datetime
from PySide6.QtCore import QUrl, QSize, QCoreApplication, Qt
from PySide6.QtGui import QIcon, QColor, QCloseEvent, QDesktopServices
from PySide6.QtWidgets import QApplication, QWidget

from qfluentwidgets import NavigationItemPosition, MSFluentWindow, SplashScreen, FluentIcon, MessageBox, \
    InfoBarPosition, InfoBar, PushButton
from qfluentwidgets import FluentIcon as FIF
import re
from qasync import asyncSlot
# Service and Controller imports
from app.services.account_service import AccountService
from app.controllers.account_controller import AccountController
from app.services.monitor_service import MonitorTaskService  # Ensure this path is correct
from app.controllers.telegram_monitor_controller import TelegramMonitorController
from core.telegram.client_worker import TelegramClientWorker
from app.services.invite_service import InviteService  # 导入邀请服务
from app.controllers.invite_controller import InviteController  # 导入邀请控制器
from core.auth.models import software_config
from app.services.message_sending_service import MessageSendingService
from app.controllers.send_msg_controller import SendMessageController
from app.controllers.proxy_controller import ProxyController
from app.services.proxy_service import ProxyService
from frameConf.config import cfg
from frameConf.setting import DOC_URL, APP_NAME, VERSION
from frameConf.icon import Icon
from frameConf.signal_bus import signalBus
from frameConf import resource

from ui.views.convert_view import ConvertWindow
from ui.views.dashboard_view import DashboardView
from ui.views.account_view import AccountView
from ui.views.bot_view import BotView
from ui.views.invite_view import InviteView
from ui.views.monitor_view import MonitorView
from ui.views.send_msg_view import SendMsgView
from ui.views.proxy_view import ProxyView
from ui.views.setting_view import SettingView
from ui.views.doc_view import DocView
from ui.views.user_profile_view import UserInfoView
from app.controllers.auth_controller import AuthController
from utils.logger import get_logger
from utils.vip_checker import vip_checker
from utils.ntp_time import get_network_time
from ui.components import LogManager
from ui.components.log_manager import global_coordinator


class TestQwwidget(QWidget):
    def __init__(self, title):
        super().__init__()
        self.setWindowTitle('TestQwwidget')
        self.setObjectName(title)


class MainWindow(MSFluentWindow):
    
    def __init__(self,
                 auth_controller: AuthController):  # Removed account_controller and telegram_worker from parameters
        super().__init__()
        self.logger = get_logger("ui.main_window")
        # 1. 创建并启动 TelegramClientWorker
        self.initWindow()
        
        self.telegram_worker = TelegramClientWorker()
        self.telegram_worker.start()
        if not self.telegram_worker:
            self.logger.debug("self.telegram_worker没有初始化成功")
        # 先启动线程，防止出错
        self.auth_controller = auth_controller
        # 初始化VIP检查器，但不立即开始检查
        # 初始化并执行VIP检查
        
        vip_checker.initialize(self.auth_controller)
        vip_checker.vip_expired.connect(self.on_vip_expired)
        # 执行VIP检查
        
        # 初始化核心应用逻辑组件
        self._initialize_core_components()
        self.connectSignalToSlot()
        self.initNavigation()
        self.openUserProfile = None
        self.userProfileView = None
        
        self.on_login_succeeded()
        # 连接应用程序退出信号
        app = QApplication.instance()
        app.aboutToQuit.connect(self._cleanup_before_quit)
        
        # 登录成功后检查VIP状态
    
    def on_vip_expired(self, message):
        """当VIP过期时显示提示并禁用功能"""
        MessageBox(
            "VIP已过期",
            f"{message}\n请联系上级代理续费。",
            self
        ).exec()
        
        # 禁用所有功能按钮或切换到限制模式
        # self.disable_all_features()
    
    def disable_all_features(self):
        """禁用所有需要VIP的功能"""
        # 这里根据实际UI结构禁用按钮或功能区
        for widget in self.findChildren(PushButton):
            if widget.objectName() not in ["btn_contact_agent", "btn_logout"]:
                widget.setEnabled(False)
    
    def _initialize_core_components(self):
        """创建和初始化核心后台组件、服务和控制器"""
        self.logger.info("MainWindow: 初始化核心组件...")
        
        # 创建代理服务和控制器
        proxy_service = ProxyService()
        self.proxy_controller = ProxyController(proxy_service)
        self.start_proxy_service()  # 启动代理服务
        
        # 2. 创建 AccountService 和 AccountController
        self.account_service = AccountService(telegram_worker = self.telegram_worker)
        self.account_controller = AccountController(account_service = self.account_service)
        
        # 消息监控
        self.monitor_task_service = MonitorTaskService(telegram_worker = self.telegram_worker)
        self.monitor_controller = TelegramMonitorController(monitor_task_service = self.monitor_task_service)
        
        # +++ Initialize Message Sending Service and Controller +++
        self.message_sending_service = MessageSendingService(
            client_worker = self.telegram_worker
        )
        self.send_msg_controller = SendMessageController(
            message_service = self.message_sending_service
        )
        
        # +++ 初始化邀请服务和控制器 +++
        self.invite_service = InviteService(client_worker = self.telegram_worker)
        self.invite_controller = InviteController(invite_service = self.invite_service)
        # +++ 初始化邀请服务和控制器结束 +++
        
        # Start the background task processing loop for message sending
        # asyncio.create_task(self.message_sending_service.start_task_processing_loop())
        # It's better to start this after the event loop is running, perhaps in main.py or a dedicated startup phase.
        # For now, we'll assume it's started externally or after the window shows.
        # +++ End Initialize Message Sending Service and Controller +++
        
        # TODO: create sub interface
        self.dashboardInterface = DashboardView(self)
        self.accountInterface = AccountView(parent = self, account_controller = self.account_controller)
        self.botInterface = BotView(self)
        self.convertInterface = ConvertWindow(self, account_controller = self.account_controller)
        self.monitorInterface = MonitorView(parent = self, controller = self.monitor_controller,
                                            account_controller = self.account_controller)
        self.settingInterface = SettingView(self)
        
        # 创建代理视图 - 注意：即使controller为None也可创建，视图内部会处理
        self.proxyInterface = ProxyView(self.proxy_controller, self)
        self.messageTaskInterface = SendMsgView(self, account_controller = self.account_controller,
                                                controller = self.send_msg_controller)  # 新增：消息群发界面, pass controller
        self.inviteView = InviteView(parent = self, controller = self.invite_controller,
                                     accountController = self.account_controller)  # 传递邀请控制器
        
        self.docsInterface = DocView(self)
        
        # 初始化日志管理器字典
        self.log_managers = {}
        self._setup_log_managers()
    
    @asyncSlot()
    async def start_proxy_service(self):
        # 检查是否有本地代理，如果有则自动启动代理服务
        try:
            has_local = await self.proxy_controller.has_local_proxies()
            if has_local:
                self.logger.info("检测到本地代理，自动启动代理服务...")
                # 检查服务状态
                is_running = await self.proxy_controller.check_service_status()
                if not is_running:
                    # 启动服务
                    await self.proxy_controller.start_service()
        except Exception as e:
            self.logger.error(f"检查本地代理状态时出错: {e}")
    
    def logout(self):
        # 实现注销逻辑
        pass
    
    def connectSignalToSlot(self):
        signalBus.micaEnableChanged.connect(self.setMicaEffectEnabled)
    
    def initNavigation(self):
        # self.navigationInterface.setAcrylicEnabled(True)
        
        # TODO: add navigation items
        # self.addSubInterface(self.homeInterface, FIF.HOME, self.tr('Home'))
        
        # 使用FluentWindow的方法设置中心部件
        self.addSubInterface(self.dashboardInterface, ":/app/images/data_panel.png", "数据面板")
        self.addSubInterface(self.accountInterface, ":/app/images/icons/accounts.svg", "账号池")
        self.addSubInterface(self.convertInterface, FluentIcon.SYNC, "互转")
        self.addSubInterface(self.monitorInterface, ":/app/images/icons/monitor.svg", "消息监控")
        self.addSubInterface(self.messageTaskInterface, FluentIcon.SEND, "消息群发")  # 新增：消息群发导航项
        self.addSubInterface(self.inviteView, ":/app/images/icons/invite.svg", "拉群")  # 新增：拉群导航项
        self.addSubInterface(self.proxyInterface, FluentIcon.CONNECT, "IP池", FluentIcon.VPN)
        # add custom widget to bottom
        
        self.navigationInterface.addItem(
            routeKey = 'avatar',
            icon = ':/app/images/touxiang.svg',
            text = "用户",
            onClick = self.userProfile,
            position = NavigationItemPosition.BOTTOM
        )
        self.addSubInterface(self.docsInterface, FluentIcon.LINK, "文档", FluentIcon.LINK)
        # self.navigationInterface.addItem(
        #     routeKey = 'docs',
        #     icon =FluentIcon.LINK,
        #     text = "文档",
        #     onClick = lambda: QDesktopServices.openUrl(QUrl(DOC_URL)),
        #     position = NavigationItemPosition.BOTTOM
        # )
        self.addSubInterface(
            self.settingInterface, Icon.SETTINGS, self.tr('设置'), Icon.SETTINGS_FILLED, NavigationItemPosition.BOTTOM)
        
        self.splashScreen.finish()
        
        # 连接视图切换事件
        self._connect_view_change_events()
    
    def _connect_view_change_events(self):
        """连接视图切换事件"""
        # MSFluentWindow使用stackedWidget来管理视图
        if hasattr(self, 'stackedWidget'):
            self.stackedWidget.currentChanged.connect(self._on_view_changed)
    
    def _on_view_changed(self, index):
        """视图切换时的处理"""
        current_widget = self.stackedWidget.widget(index)
        if not current_widget:
            return
        
        # 获取当前视图对应的key
        view_key = self._get_view_key_by_widget(current_widget)
        if view_key:
            global_coordinator.switch_to_view(view_key)
    
    def _get_view_key_by_widget(self, widget):
        """根据widget获取视图key"""
        view_mapping = {
            self.dashboardInterface: "dashboard",
            self.accountInterface: "account",
            self.convertInterface: "convert",
            self.monitorInterface: "monitor",
            self.messageTaskInterface: "message_task",
            self.inviteView: "invite",
            self.proxyInterface: "proxy",
            self.settingInterface: "setting",
            self.docsInterface: "docs",
        }
        return view_mapping.get(widget)
    
    def userProfile(self):
        # 打开用户信息页，可以绑定卡密啥的
        
        if not self.openUserProfile:
            self.userProfileView = UserInfoView(auth_controller = self.auth_controller)
            self.userProfileView.closView.connect(self.on_user_profile_closed)
            # 获取主窗口位置和大小，传递给UserInfoView用于居中显示
            self.userProfileView.setMainWindowGeometry(self.geometry())
            self.userProfileView.show()
            self.openUserProfile = True
        else:
            if self.userProfileView:
                self.userProfileView.close()
                self.openUserProfile = False
    
    def on_user_profile_closed(self):
        self.openUserProfile = False
    
    def initWindow(self):
        self.resize(1200, 900)
        self.setMinimumWidth(760)
        self.setWindowIcon(QIcon(':/app/images/logo.png'))
        self.setWindowTitle(f"{APP_NAME} {VERSION}")
        self.setCustomBackgroundColor(QColor(240, 244, 249), QColor(32, 32, 32))
        self.setMicaEffectEnabled(cfg.get(cfg.micaEnabled))
        
        # 设置主题
        
        # create splash screen
        self.splashScreen = SplashScreen(self.windowIcon(), self)
        self.splashScreen.setIconSize(QSize(106, 106))
        self.splashScreen.raise_()
        
        desktop = QApplication.primaryScreen().availableGeometry()
        w, h = desktop.width(), desktop.height()
        self.move(w // 2 - self.width() // 2, h // 2 - self.height() // 2)
        # self.show() # show is called from main.py
        QApplication.processEvents()
    
    def resizeEvent(self, e):
        super().resizeEvent(e)
        if hasattr(self, 'splashScreen'):
            self.splashScreen.resize(self.size())
    
    def _cleanup_before_quit(self):
        """应用退出前的资源清理"""
        self.logger.info("MainWindow: 执行清理资源...")
        
        # 清理日志管理器
        self._cleanup_log_managers()
        
        # 执行同步清理
        if hasattr(self, 'telegram_worker') and self.telegram_worker:
            try:
                # 停止工作线程
                self.telegram_worker.stop()
                # 等待线程结束，最多等待5秒
                self.telegram_worker.wait(5000)  # 毫秒
                self.logger.info("TelegramClientWorker 已停止。")
            except Exception as e:
                self.logger.error(f"停止 TelegramClientWorker 出错: {e}")
        
        self.logger.info("MainWindow 清理完成")
    
    @asyncSlot()
    async def on_login_succeeded(self):
        """登录成功后的处理"""
        
        try:
            
            await vip_checker.start_checking()
            
            result = vip_checker.is_vip_valid()
            
            if result:
                pass
            else:
                self.show_info(f"VIP已到期，为了不影响您的正常使用，请及时续费！", "VIP到期", "warning", duration = 10000)
                # title = "会员已到期"
                # content = f"用户已到期，\n\n请前往用户中心激活新卡密!"
                # w = MessageBox(title, content, self)
                # w.yesButton.setText('确定')
                # w.cancelButton.setText('取消')
                # # close the message box when mask is clicked
                # w.setClosableOnMaskClicked(True)
                # w.open()
        except Exception as e:
            self.logger.error(f"检查VIP状态时出错: {e}")
    
    def is_expired(self, expire_str):
        # 获取网络时间、本地时间、vip时间，三者进行对比，网络时间优先
        if not expire_str or expire_str == "-":
            return True
        match = re.match(r"(\d{4}-\d{2}-\d{2})(?:[ T](\d{2}:\d{2}:\d{2}))?", expire_str)
        if not match:
            return False
        date_part = match.group(1)
        time_part = match.group(2) or "23:59:59"
        try:
            expire_dt = datetime.datetime.strptime(f"{date_part} {time_part}", "%Y-%m-%d %H:%M:%S")
            # 优先用网络时间
            net_dt = get_network_time()
            now = net_dt if net_dt else datetime.datetime.now(datetime.timezone.utc)
            # 兼容本地时间无时区
            if expire_dt.tzinfo is None:
                expire_dt = expire_dt.replace(tzinfo = datetime.timezone.utc)
            return (expire_dt - now).total_seconds() < 0
        except Exception:
            return False
    
    def show_info(self, msg, title = "提醒", type = "info", duration = 3000):
        if type == "info":
            InfoBar.info(
                title = title,
                content = msg,
                orient = Qt.Horizontal,
                isClosable = True,
                position = InfoBarPosition.TOP,
                duration = duration,
                parent = self)
        elif type == "success":
            InfoBar.success(
                title = title,
                content = msg,
                orient = Qt.Horizontal,
                isClosable = True,
                position = InfoBarPosition.TOP,
                duration = duration,
                parent = self
            )
        elif type == "warning":
            InfoBar.warning(
                title = title,
                content = msg,
                orient = Qt.Horizontal,
                isClosable = True,
                position = InfoBarPosition.TOP,
                duration = duration,
                parent = self
            )
        elif type == "error":
            InfoBar.error(
                title = title,
                content = msg,
                orient = Qt.Horizontal,
                isClosable = True,
                position = InfoBarPosition.TOP,
                duration = duration,
                parent = self
            )
        else:
            InfoBar.info(
                title = title,
                content = msg,
                orient = Qt.Horizontal,
                isClosable = True,
                position = InfoBarPosition.TOP,
                duration = duration,
                parent = self
            )
    
    def _setup_log_managers(self):
        """为每个视图设置日志管理器"""
        # 为每个主要视图创建日志管理器，并定义相应的模块过滤器
        view_configs = [
            ("dashboard", self.dashboardInterface, "数据面板", ["ui.views.dashboard"]),
            ("account", self.accountInterface, "账号池", [
                "ui.views.account", "app.controllers.account", "app.services.account"
            ]),
            ("convert", self.convertInterface, "互转", [
                "ui.views.convert", "core.telegram.client"
            ]),
            ("monitor", self.monitorInterface, "消息监控", [
                "ui.views.monitor", "app.controllers.telegram_monitor",
                "app.services.monitor", "core.monitor"
            ]),
            ("message_task", self.messageTaskInterface, "消息群发", [
                "ui.views.send_msg", "app.controllers.send_msg",
                "app.services.message_sending"
            ]),
            ("invite", self.inviteView, "拉群", [
                "ui.views.invite", "app.controllers.invite", "app.services.invite"
            ]),
            ("proxy", self.proxyInterface, "IP池", [
                "ui.views.proxy", "app.controllers.proxy", "app.services.proxy"
            ]),
            ("setting", self.settingInterface, "设置", ["ui.views.setting"]),
            ("docs", self.docsInterface, "文档", ["ui.views.doc"]),
        ]
        
        for key, view_widget, view_name, module_filters in view_configs:
            if view_widget:
                log_manager = LogManager(self, view_name, module_filters, key)
                self.log_managers[key] = log_manager
        
        self.logger.info(f"已为 {len(self.log_managers)} 个视图创建日志管理器")
    
    def _cleanup_log_managers(self):
        """清理日志管理器"""
        global_coordinator.cleanup_all()
        self.log_managers.clear()
    
    def closeEvent(self, event: QCloseEvent):
        """重写关闭事件处理"""
        self.logger.info("MainWindow: 接收到关闭事件")
        # 清理日志管理器
        self._cleanup_log_managers()
        # 调用父类的closeEvent，让窗口正常关闭
        super().closeEvent(event)
        # 资源清理由aboutToQuit信号处理，不在这里进行

