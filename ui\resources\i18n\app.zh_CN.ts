<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE TS>
<TS version="2.1" language="zh_CN" sourcelanguage="en_US">
<context>
    <name>RegisterWindow</name>
    <message>
        <location filename="../../view/register_window.py" line="38"/>
        <source>Email</source>
        <translation>邮箱</translation>
    </message>
    <message>
        <location filename="../../view/register_window.py" line="41"/>
        <source>Activation Code</source>
        <translation>激活码</translation>
    </message>
    <message>
        <location filename="../../view/register_window.py" line="44"/>
        <source>Remember me</source>
        <translation>记住我</translation>
    </message>
    <message>
        <location filename="../../view/register_window.py" line="46"/>
        <source>Login</source>
        <translation>登录</translation>
    </message>
    <message>
        <location filename="../../view/register_window.py" line="133"/>
        <source>Activate failed</source>
        <translation>激活失败</translation>
    </message>
    <message>
        <location filename="../../view/register_window.py" line="133"/>
        <source>Please check your activation code</source>
        <translation>请检查你的激活码是否正确</translation>
    </message>
    <message>
        <location filename="../../view/register_window.py" line="141"/>
        <source>Success</source>
        <translation>激活成功</translation>
    </message>
    <message>
        <location filename="../../view/register_window.py" line="141"/>
        <source>Activation successful</source>
        <translation>正在打开主界面</translation>
    </message>
</context>
<context>
    <name>SettingInterface</name>
    <message>
        <location filename="../../view/setting_interface.py" line="37"/>
        <source>Settings</source>
        <translation>设置</translation>
    </message>
    <message>
        <location filename="../../view/setting_interface.py" line="40"/>
        <source>Personalization</source>
        <translation>个性化</translation>
    </message>
    <message>
        <location filename="../../view/setting_interface.py" line="42"/>
        <source>Mica effect</source>
        <translation>云母效果</translation>
    </message>
    <message>
        <location filename="../../view/setting_interface.py" line="42"/>
        <source>Apply semi transparent to windows and surfaces</source>
        <translation>窗口和表面呈现半透明</translation>
    </message>
    <message>
        <location filename="../../view/setting_interface.py" line="49"/>
        <source>Application theme</source>
        <translation>应用主题</translation>
    </message>
    <message>
        <location filename="../../view/setting_interface.py" line="49"/>
        <source>Change the appearance of your application</source>
        <translation>调整应用的外观</translation>
    </message>
    <message>
        <location filename="../../view/setting_interface.py" line="49"/>
        <source>Light</source>
        <translation>浅色</translation>
    </message>
    <message>
        <location filename="../../view/setting_interface.py" line="49"/>
        <source>Dark</source>
        <translation>深色</translation>
    </message>
    <message>
        <location filename="../../view/setting_interface.py" line="71"/>
        <source>Use system setting</source>
        <translation>跟随系统设置</translation>
    </message>
    <message>
        <location filename="../../view/setting_interface.py" line="60"/>
        <source>Interface zoom</source>
        <translation>界面缩放</translation>
    </message>
    <message>
        <location filename="../../view/setting_interface.py" line="60"/>
        <source>Change the size of widgets and fonts</source>
        <translation>调整组件和字体的大小</translation>
    </message>
    <message>
        <location filename="../../view/setting_interface.py" line="71"/>
        <source>Language</source>
        <translation>语言</translation>
    </message>
    <message>
        <location filename="../../view/setting_interface.py" line="71"/>
        <source>Set your preferred language for UI</source>
        <translation>设置界面的首选语言</translation>
    </message>
    <message>
        <location filename="../../view/setting_interface.py" line="81"/>
        <source>Software update</source>
        <translation>软件更新</translation>
    </message>
    <message>
        <location filename="../../view/setting_interface.py" line="83"/>
        <source>Check for updates when the application starts</source>
        <translation>软件启动时检查更新</translation>
    </message>
    <message>
        <location filename="../../view/setting_interface.py" line="83"/>
        <source>The new version will be more stable and have more features</source>
        <translation>新版本更加稳定且拥有更多新特性</translation>
    </message>
    <message>
        <location filename="../../view/setting_interface.py" line="109"/>
        <source>About</source>
        <translation>关于</translation>
    </message>
    <message>
        <location filename="../../view/setting_interface.py" line="93"/>
        <source>Open help page</source>
        <translation>打开帮助页面</translation>
    </message>
    <message>
        <location filename="../../view/setting_interface.py" line="93"/>
        <source>Help</source>
        <translation>帮助</translation>
    </message>
    <message>
        <location filename="../../view/setting_interface.py" line="93"/>
        <source>Discover new features and learn useful tips about Fluent Client</source>
        <translation>发现并了解关于 Fluent Client 的新特性和最佳实践</translation>
    </message>
    <message>
        <location filename="../../view/setting_interface.py" line="102"/>
        <source>Provide feedback</source>
        <translation>提供反馈</translation>
    </message>
    <message>
        <location filename="../../view/setting_interface.py" line="102"/>
        <source>Help us improve Fluent Client by providing feedback</source>
        <translation>提供反馈以帮助我们改善 Fluent Client</translation>
    </message>
    <message>
        <location filename="../../view/setting_interface.py" line="109"/>
        <source>Check update</source>
        <translation>检查更新</translation>
    </message>
    <message>
        <location filename="../../view/setting_interface.py" line="109"/>
        <source>Copyright</source>
        <translation>版权所有</translation>
    </message>
    <message>
        <location filename="../../view/setting_interface.py" line="109"/>
        <source>Version</source>
        <translation>当前版本</translation>
    </message>
    <message>
        <location filename="../../view/setting_interface.py" line="164"/>
        <source>Updated successfully</source>
        <translation>更新成功</translation>
    </message>
    <message>
        <location filename="../../view/setting_interface.py" line="164"/>
        <source>Configuration takes effect after restart</source>
        <translation>配置在重启软件后生效</translation>
    </message>
</context>
</TS>
