#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
账户管理视图
用于管理Telegram账户的UI界面
"""

import os
from typing import Optional, Dict, List, Any
from datetime import datetime

from PySide6.QtCore import Qt, Slot, Signal, QSize, QTimer
from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, 
    QTableWidget, QTableWidgetItem, QHeaderView, QComboBox,
    QLineEdit, QCheckBox, QToolButton, QMenu, QDialog,
    QAbstractItemView, QSpacerItem, QSizePolicy, QFileDialog,
    QGroupBox, QMessageBox
)
from PySide6.QtGui import QIcon, QPixmap, QFont, QColor, QAction
from qfluentwidgets import InfoBar, InfoBarPosition
# 导入qasync装饰器
from qasync import asyncSlot

# 导入UI基类
from ui.designer.account_manage_ui import AccountManageUI

# 导入拆分后的组件
from ui.widgets.group_list_widget import GroupListWidget
from ui.widgets.account_table_widget import AccountTableWidget

# 导入其他视图
from ui.views.add_account_view import AddAccountView
from ui.views.import_sessions_view import ImportSessionsView
from ui.dialogs.profile_edit_dialog import UserProfileEditUI

from utils.logger import get_logger
from app.controllers.account_controller import AccountController
from config import config
from utils.decorators import require_vip, require_vip_async
from data.database import get_session

class AccountView(AccountManageUI):
    """Telegram账户管理视图"""
    
    def __init__(self, parent=None, account_controller: AccountController = None):
        """初始化账户管理视图"""
        super().__init__(parent)
        self.setObjectName("AccountView")
        if parent:
            self.setParent(parent)
            
        self._logger = get_logger("ui.views.account_view")
        self._account_controller = account_controller
        
        # 存储数据
        self._accounts = []  # 当前显示的账户列表
        self._groups = []    # 分组列表
        self._selected_group_id = -1  # 当前选中的分组ID，默认为"所有账号"
        self.auto_login = False  # 自动登录标志
        
        # 缓存数据
        self._accounts_cache = []  # 所有账户的缓存
        self._group_counts = {}  # 缓存分组账户数量 {group_id: count}
        self._cache_initialized = False  # 缓存是否已初始化
        
        # 初始化组件
        self._init_components()
        
        # 连接信号和槽
        self._connect_signals()
        
        # 加载初始数据
        self._load_initial_data()
    
    def _init_components(self):
        """初始化组件"""
        # 创建分组列表组件
        self._group_list = GroupListWidget(self)
        # 替换占位符
        group_layout = QVBoxLayout(self.group_container)
        group_layout.setContentsMargins(0, 0, 0, 0)
        group_layout.addWidget(self._group_list)
        
        # 创建账户表格组件
        self._account_table = AccountTableWidget(self)
        # 替换占位符
        table_layout = QVBoxLayout(self.table_container)
        table_layout.setContentsMargins(0, 0, 0, 0)
        table_layout.addWidget(self._account_table)
    
    def _connect_signals(self):
        """连接信号和槽"""
        # 账户操作信号
        self.add_account_clicked.connect(self._on_add_account)
        self.import_session_clicked.connect(self._on_import_session)
        self._account_table.batch_edit_btn.clicked.connect(self._on_batch_edit_accounts)  # 连接批量设置按钮
        
        # 分组相关信号
        self._group_list.group_selected.connect(self._on_group_selected)
        self._group_list.add_group_requested.connect(self._on_add_group)
        self._group_list.rename_group_requested.connect(self._on_rename_group)
        self._group_list.delete_group_requested.connect(self._on_delete_group)
        
        # 账户表格操作信号
        self._account_table.refresh_account_requested.connect(self._on_refresh_account)
        self._account_table.edit_account_requested.connect(self._on_edit_account)
        self._account_table.delete_account_requested.connect(self._on_delete_account)
        self._account_table.verify_account_requested.connect(self._on_verify_account)
        
        # 控制器信号连接
        if self._account_controller:
            # 分组相关
            self._account_controller.groups_loaded.connect(self._on_groups_loaded)
            self._account_controller.group_created.connect(self._on_group_created)
            self._account_controller.group_updated.connect(self._on_group_updated)
            self._account_controller.group_deleted.connect(self._on_group_deleted)
            
            # 账户相关
            self._account_controller.accounts_loaded.connect(self._on_accounts_loaded)
            self._account_controller.account_added.connect(self._on_account_added)
            self._account_controller.account_deleted.connect(self._on_account_deleted)
            self._account_controller.account_refreshed.connect(self._on_account_refreshed)

            # 批量操作相关
            self._account_controller.notify.connect(self._on_notify)
            
            # 新增：连接批量导入session成功后的信号
            self._account_controller.new_accounts_added_to_main_list.connect(self._on_new_accounts_added)
    
    @asyncSlot()
    async def _load_initial_data(self):
        """加载初始数据"""
        # 请求加载所有分组数据
        if self._account_controller:
            await self._account_controller.load_all_groups()
            # 加载默认的"所有账号"数据
            await self._account_controller.load_all_accounts()
            # 初始化缓存
            await self._init_cache()
            # 显示加载提示
            self.show_info("初始化", "正在自动登录所有账户...", "info")
    
    async def _init_cache(self):
        """初始化缓存数据"""
        if self._account_controller and not self._cache_initialized:
            # 获取所有账户数据
            self._accounts_cache = await self._account_controller.load_all_accounts()
            if self._accounts_cache is None:
                self._accounts_cache = []
            self._cache_initialized = True
            # 初始化分组计数
            self._update_group_counts(self._accounts_cache)
            self._logger.debug(f"账户缓存初始化完成，共 {len(self._accounts_cache)} 个账户")
    
    def _update_group_counts(self, accounts):
        """更新分组计数缓存和UI显示
        
        Args:
            accounts: 账户列表
        """
        if accounts is None:
            accounts = []
            
        # 清空之前的分组计数缓存
        self._group_counts = {}
        
        # 统计每个分组的账户数量
        for account in accounts:
            group_id = account.get('group_id')
            if group_id is not None and group_id != -1:  # 非默认分组
                self._group_counts[group_id] = self._group_counts.get(group_id, 0) + 1
        
        # 更新"所有账户"的计数
        if -1 in self._group_list.group_items:
            self._group_list.group_items[-1].text_label.setText(f"所有账户 ({len(accounts)})")
        
        # 更新其他分组项的计数显示
        for group in self._groups:
            group_id = group.get('id')
            if group_id != -1:  # 排除"所有账号"项
                group_name = group.get('name', '')
                count = self._group_counts.get(group_id, 0)
                if group_id in self._group_list.group_items:
                    self._group_list.group_items[group_id].text_label.setText(f"{group_name} ({count})")
        
        self._logger.debug(f"分组计数已更新: {self._group_counts}")
    
    @asyncSlot(int)
    async def _on_group_selected(self, group_id):
        """处理分组选中事件
        
        Args:
            group_id: 分组ID，-1表示所有账号
        """
        self._selected_group_id = group_id
        
        # 加载该分组的账户
        if self._account_controller:
            if group_id == -1:
                await self._account_controller.load_all_accounts()
            else:
                await self._account_controller.load_group_accounts(group_id)
    
    @asyncSlot(str)
    async def _on_add_group(self, name):
        """处理添加分组事件
        
        Args:
            name: 分组名称
        """
        self._logger.info(f"请求添加分组: {name}")
        await self._account_controller.create_group(name, "")
    
    @asyncSlot(int, str)
    async def _on_rename_group(self, group_id, new_name):
        """处理重命名分组事件
        
        Args:
            group_id: 分组ID
            new_name: 新分组名称
        """
        self._logger.info(f"请求重命名分组: ID={group_id}, 新名称={new_name}")
        if self._account_controller:
            await self._account_controller.update_group(group_id, new_name)
    
    @asyncSlot(int)
    async def _on_delete_group(self, group_id):
        """处理删除分组事件
        
        Args:
            group_id: 分组ID
        """
        if self._account_controller:
            await self._account_controller.delete_group(group_id)
    
    # ================ 账户相关处理方法 ================
    
    @require_vip
    def _on_add_account(self):
        """打开添加账户对话框"""
        self._add_account_dialog = AddAccountView(self, self._account_controller)
        result = self._add_account_dialog.exec()
        if result == QDialog.Accepted:
            # 显示加载提示
            self.show_info("刷新", "正在刷新账户列表...", "info")
            # 刷新账户列表
        self._reload_current_view()
            
    # 添加控制器信号处理方法，处理账户添加成功事件
    @asyncSlot(dict, str)
    async def _on_account_added(self, account_dict, message):
        """处理账户添加成功事件
        
        Args:
            account_dict: 账户信息字典
            message: 操作结果消息
        """
        if account_dict:  # 添加成功
            self._logger.info(f"账户添加成功: {account_dict.get('phone', '')}")
            # 显示成功消息
            self.show_info("成功", message, "success")
            # 刷新当前视图
            await self._account_controller.load_all_accounts()
        else:  # 添加失败
            self._logger.error(f"账户添加失败: {message}")
            # 显示错误消息
            self.show_info("错误", f"添加账户失败: {message}", "error")
    @require_vip
    def _on_import_session(self):
        """打开批量导入session对话框"""
        self._import_session_dialog = ImportSessionsView(self, self._account_controller)
        result = self._import_session_dialog.exec()
        if result == QDialog.Accepted:
            # 刷新账户列表
            self._reload_current_view()
    
    @asyncSlot(str)
    async def _on_refresh_account(self, phone):
        """刷新单个账号状态
        
        Args:
            phone: 账号手机号
        """
        if self._account_controller:
            # 通过手机号查找账号ID
            account_id = self._find_account_id_by_phone(phone)
            if account_id:
                # 显示加载提示
                self.show_info("刷新", f"正在刷新账号 {phone} 的信息...", "info")
                # 调用控制器刷新账号信息
                await self._account_controller.refresh_account_info(account_id)

    @asyncSlot(int, bool, str)
    async def _on_account_refreshed(self, account_id, success, message):
        """处理账户刷新完成事件

        Args:
            account_id: 账户ID
            success: 是否成功
            message: 结果消息
        """
        if success:
            self.show_info("刷新成功", message, "success")
            # 实时更新UI显示的账户信息，而不是重新加载所有数据
            await self._update_single_account_display(account_id)
        else:
            self.show_info("刷新失败", message, "error")

    async def _update_single_account_display(self, account_id):
        """实时更新单个账户的显示信息

        Args:
            account_id: 账户ID
        """
        try:
            # 从数据库获取更新后的账户信息
            async with get_session() as session:
                from data.repositories.account_repo import AccountRepository
                account_repo = AccountRepository(session=session)
                updated_account = await account_repo.get_account(account_id)

                if not updated_account:
                    self._logger.warning(f"刷新后未找到账户: {account_id}")
                    return

                # 转换为字典格式
                updated_account_dict = updated_account.to_dict()

                # 更新内存中的账户列表
                self._update_account_in_memory(updated_account_dict)

                # 更新表格显示
                self._account_table.update_single_account(updated_account_dict)

                self._logger.info(f"已实时更新账户显示: {updated_account_dict.get('phone', account_id)}")

        except Exception as e:
            self._logger.error(f"更新账户显示失败: {e}")
            # 如果实时更新失败，回退到重新加载整个视图
            await self._reload_current_view()

    def _update_account_in_memory(self, updated_account):
        """更新内存中的账户数据

        Args:
            updated_account: 更新后的账户字典
        """
        account_id = updated_account.get('id')
        if not account_id:
            return

        # 更新当前显示的账户列表
        for i, account in enumerate(self._accounts):
            if account.get('id') == account_id:
                self._accounts[i] = updated_account
                break

        # 更新缓存中的账户列表
        if hasattr(self, '_accounts_cache') and self._accounts_cache:
            for i, account in enumerate(self._accounts_cache):
                if account.get('id') == account_id:
                    self._accounts_cache[i] = updated_account
                    break

    def _edit_account(self, phone=None, is_batch=False):
        """编辑账号信息

        Args:
            phone: 账号手机号，如果为None则表示批量编辑
            is_batch: 是否为批量编辑模式
        """
        # 创建编辑对话框
        dialog = UserProfileEditUI(self, account_controller=self._account_controller)

        # 设置分组选项（分组对象列表）
        group_objs = [{'id': g['id'], 'name': g['name']} for g in self._groups]
        dialog.set_user_group_options(group_objs)  # 设置右侧分组下拉选项

        if not is_batch and phone:
            # 单个用户编辑模式
            # 隐藏左侧用户列表
            dialog.left_widget.hide()
            # 调整分割器大小
            dialog.content_splitter.setSizes([0, 900])
            dialog.setWindowTitle(f"编辑账号 - {phone}")

            # 通过手机号查找账号信息
            account = next((acc for acc in self._accounts if acc.get('phone') == phone), None)
            if not account:
                self._logger.error(f"未找到账号: {phone}")
                self.show_info("错误", f"未找到账号: {phone}", "error")
                return

            # 设置用户数据
            dialog.set_user_data(account)
        else:
            # 批量编辑模式
            dialog.setWindowTitle("批量编辑账号")
            dialog.left_widget.show()
            # 设置分组选项（分组对象列表）
            dialog.set_group_options(group_objs)  # 设置左侧分组筛选下拉选项
            # 设置用户列表（原始账户，含group_id）
            dialog.set_users(self._accounts)

        # 显示对话框
        result = dialog.exec()

        # 如果对话框被接受（保存成功），刷新视图
        if result == QDialog.Accepted:
            self._reload_current_view()

    @require_vip
    def _on_edit_account(self, phone):
        """编辑账号信息
        
        Args:
            phone: 账号手机号
        """
        self._edit_account(phone, is_batch=False)

    @require_vip
    def _on_batch_edit_accounts(self):
        """批量编辑账户设置"""
        self._edit_account(None, is_batch=True)






    
    @asyncSlot(str)
    @require_vip_async
    async def _on_delete_account(self, phone):
        """删除账号
        
        Args:
            phone: 账号手机号
        """
        self._logger.info(f"请求删除账号: {phone}")
        if self._account_controller:
            try:
                # 通过手机号查找账号ID
                self._logger.info(f"查找账号ID: {phone}")
                account_id = self._find_account_id_by_phone(phone)
                if account_id:
                    # 显示确认对话框
                    reply = QMessageBox.question(
                        self,
                        "确认删除",
                        f"确定要删除账号 {phone} 吗？\n此操作将断开连接并删除所有相关数据。",
                        QMessageBox.Yes | QMessageBox.No,
                        QMessageBox.No
                    )
                    
                    if reply == QMessageBox.Yes:
                        # 显示加载提示
                        self.show_info("删除", f"正在删除账号 {phone}...", "info")
                        # 调用控制器删除账号
                        await self._account_controller.delete_account(account_id, True)
                else:
                    self._logger.error(f"未找到账号: {phone}")
                    self.show_info("错误", f"未找到账号: {phone}", "error")
            except Exception as e:
                self._logger.error(f"删除账号时发生异常: {str(e)}")
                self.show_info("错误", f"删除账号失败: {str(e)}", "error")
    
    def _get_group_id_by_name(self, group_name):
        """根据分组名称获取分组ID
        
        Args:
            group_name: 分组名称
            
        Returns:
            分组ID，如果未找到则返回None
        """
        if group_name == "未分组":
            return None
            
        for group in self._groups:
            if group.get('name') == group_name:
                return group.get('id')
        return None
    
    def _on_verify_account(self, phone):
        """验证账号
        
        Args:
            phone: 账号手机号
        """
        # 实现验证账号的逻辑
        self._logger.info(f"请求验证账号: {phone}")
        # TODO: 实现账号验证功能
    
    def _find_account_id_by_phone(self, phone):
        """通过手机号查找账号ID
        
        Args:
            phone: 账号手机号
            
        Returns:
            账号ID，如果未找到则返回None
        """
        for account in self._accounts:
            if account.get('phone') == phone:
                return account.get('id')
        return None
    
    # ================ 控制器回调处理 ================
    
    def _on_groups_loaded(self, groups):
        """处理分组加载完成事件
        
        Args:
            groups: 分组列表
        """
        self._logger.debug(f"分组加载完成: {len(groups)}个分组")
        self._groups = groups
        
        # 更新分组UI
        self._group_list.update_group_list(groups)
    
    @asyncSlot(dict, str)
    async def _on_group_created(self, group_dict, message):
        """处理分组创建完成事件
        
        Args:
            group_dict: 分组信息字典
            message: 操作结果消息
        """
        if group_dict:  # 创建成功
            self._logger.info(f"分组创建成功: {group_dict['name']}")
            # 显示成功消息
            self.show_info("成功", f"创建分组 '{group_dict['name']}' 成功", "success")
            # 重新加载分组列表
            if self._account_controller:
                await self._account_controller.load_all_groups()
        else:  # 创建失败
            self._logger.error(f"分组创建失败: {message}") 
            # 显示错误消息
            self.show_info("错误", f"创建分组失败: {message}", "error")
    
    @asyncSlot(int, bool, str)
    async def _on_group_updated(self, group_id, success, message):
        """处理分组更新完成事件
        
        Args:
            group_id: 分组ID
            success: 是否成功
            message: 操作结果消息
        """
        if success:
            self._logger.info(f"分组更新成功: ID={group_id}")
            # 显示成功消息
            self.show_info("成功", "分组更新成功", "success")
            # 重新加载分组列表
            if self._account_controller:
                await self._account_controller.load_all_groups()
        else:
            self._logger.error(f"分组更新失败: ID={group_id}, 原因={message}")
            # 显示错误消息
            self.show_info("错误", f"更新分组失败: {message}", "error")
    @asyncSlot(int, bool, str)
    async def _on_group_deleted(self, group_id, success, message):
        """处理分组删除完成事件
        
        Args:
            group_id: 分组ID
            success: 是否成功
            message: 操作结果消息
        """
        if success:
            self._logger.info(f"分组删除成功: ID={group_id}")
            # 更新缓存中相关账户的分组信息
            for account in self._accounts_cache:
                if account.get('group_id') == group_id:
                    account['group_id'] = None
            # 更新分组计数
            self._update_group_counts(self._accounts_cache)
            # 显示成功消息
            self.show_info("成功", "分组删除成功", "success")
            
            # 如果当前正在显示该分组的账户，切换到所有账户视图
            if self._selected_group_id == group_id:
                # 选中"所有账号"项
                item = self._group_list.group_items.get(-1)
                if item:
                    item.clicked.emit(item)
            
            # 重新加载分组列表
            if self._account_controller:
                await self._account_controller.load_all_groups()
        else:
            self._logger.error(f"分组删除失败: ID={group_id}, 原因={message}")
            # 显示错误消息
            self.show_info("错误", f"删除分组失败: {message}", "error")
    
    @asyncSlot()
    async def _reload_current_view(self):
        """重新加载当前视图数据"""
        if self._account_controller:
            try:
                # 禁用相关按钮，防止重复操作
                self._disable_operation_buttons()
                
                # 根据当前选中的分组加载数据
                if self._selected_group_id == -1:
                    await self._account_controller.load_all_accounts()
                else:
                    await self._account_controller.load_group_accounts(self._selected_group_id)
                    
            except Exception as e:
                self._logger.error(f"刷新视图数据失败: {str(e)}")
                self.show_info("错误", f"刷新数据失败: {str(e)}", "error")
            finally:
                # 恢复按钮状态
                self._enable_operation_buttons()
    
    def _disable_operation_buttons(self):
        """禁用操作按钮"""
        # 禁用添加账户按钮
        if hasattr(self, 'add_account_btn'):
            self.add_account_btn.setEnabled(False)
        # 禁用导入会话按钮
        if hasattr(self, 'import_session_btn'):
            self.import_session_btn.setEnabled(False)
        # 禁用刷新按钮
        if hasattr(self, 'refresh_btn'):
            self.refresh_btn.setEnabled(False)

    def _enable_operation_buttons(self):
        """启用操作按钮"""
        # 启用添加账户按钮
        if hasattr(self, 'add_account_btn'):
            self.add_account_btn.setEnabled(True)
        # 启用导入会话按钮
        if hasattr(self, 'import_session_btn'):
            self.import_session_btn.setEnabled(True)
        # 启用刷新按钮
        if hasattr(self, 'refresh_btn'):
            self.refresh_btn.setEnabled(True)

    @asyncSlot()
    async def _on_accounts_loaded(self, accounts):
        """处理账户加载完成事件
        
        Args:
            accounts: 账户列表
        """
        self._logger.debug(f"账户加载完成: {len(accounts)}个账户")
        self._accounts = accounts
        
        # 更新账户表格
        self._account_table.update_table(accounts)
        
        # 如果是显示所有账户，更新分组计数
        if self._selected_group_id == -1:
            self._update_group_counts(accounts)
        
        # 自动登录所有账户
        if not self.auto_login:
            await self._auto_login_accounts(accounts)
        
        # 显示加载完成提示
        self.show_info("刷新完成", f"已加载 {len(accounts)} 个账户", "success")
    
    async def _auto_login_accounts(self, accounts):
        """自动登录账户"""
        if not self._account_controller or not accounts:
            self._logger.info("自动登录条件不满足：无控制器或无账户。")
            return

        # 显示总的加载提示
        self.show_info("初始化", f"正在准备自动登录 {len(accounts)} 个账户...", "info")

        for account in accounts:
            proxy_type = account.get('proxy_type')
            
            # 为了在日志和消息中识别账户，优先使用手机号，其次是用户名，最后是ID
            account_phone = account.get('phone')
            account_name = account.get('username')
            account_internal_id = account.get('id', '未知ID')
            identifier = account_phone if account_phone else (account_name if account_name else f"ID: {account_internal_id}")

            if proxy_type == 'ip_pool':
                proxy_id = account.get('proxy_id')
                if not proxy_id:  # 检查 proxy_id 是否为 None、空字符串等
                    warning_message = f"账户 {identifier} 设置为IP池代理，但代理ID缺失。登录时可能无法使用指定代理，请检查账户配置。"
                    self._logger.warning(warning_message)
                    self.show_info(
                        "代理配置警告",
                        warning_message,
                        "warning",
                        position=InfoBarPosition.TOP_RIGHT, # 尝试指定位置
                        # duration=7000 # 若 show_info 支持，可以指定显示时长
                    )
        
        # 设置自动登录标志，即使某些账户有配置警告，也尝试进行批量登录
        self.auto_login = True
        
        # 调用控制器的批量登录功能。
        # 控制器负责解释 proxy_type, proxy_id, 对 'ip_pool' 获取具体代理信息，
        # 并处理登录错误，包括 proxy_id 值无效或在代理系统中不存在的情况。
        # 控制器应使用其 'notify' 信号报告具体的登录失败信息。
        self._logger.info(f"开始调用控制器进行 {len(accounts)} 个账户的自动登录。")
        await self._account_controller.auto_login_accounts(accounts, max_concurrent=config.max_login_count)

    def _on_account_deleted(self, account_id, success, message):
        """处理账户删除完成事件
        Args:
            account_id: 账户ID
            success: 是否成功
            message: 操作结果消息
        """
        try:
            if success:
                self._logger.info(f"账户删除成功: ID={account_id}")
                # 从缓存中移除账户
                self._accounts_cache = [acc for acc in self._accounts_cache if acc.get('id') != account_id]
                # 更新分组计数
                self._update_group_counts(self._accounts_cache)
                # 显示成功消息
                self.show_info("成功", "账户删除成功", "success")
                # 刷新当前视图
                self._reload_current_view()
            else:
                self._logger.error(f"账户删除失败: ID={account_id}, 原因={message}")
                # 显示错误消息
                self.show_info("错误", f"删除账户失败: {message}", "error")
        except Exception as e:
            self._logger.error(f"处理账户删除完成事件时发生异常: {str(e)}")
            self.show_info("错误", f"处理删除结果时发生错误: {str(e)}", "error")
    
    def _on_notify(self, message, data, status):
        """处理通知消息"""
        self._logger.info(f"收到通知: {message}")
        # 根据消息类型更新UI
        self.show_info(message, str(data), status,position = InfoBarPosition.BOTTOM_RIGHT)

    @asyncSlot(list)
    async def _on_new_accounts_added(self, new_accounts: List[Dict[str, Any]]):
        """处理批量导入session成功后的账户数据更新
        
        Args:
            new_accounts: 新导入的账户数据列表
        """
        if not new_accounts:
            return
            
        self._logger.info(f"收到 {len(new_accounts)} 个新导入的账户数据")
        
        # 如果当前显示的是"所有账户"组，则直接更新表格
        if self._selected_group_id == -1:
            # 合并新账户数据到当前账户列表
            account_ids = {acc.get('id') for acc in self._accounts if acc.get('id')}
            for new_acc in new_accounts:
                if new_acc.get('id') not in account_ids:
                    self._accounts.append(new_acc)
                    account_ids.add(new_acc.get('id'))
            
            # 更新账户表格
            self._account_table.update_table(self._accounts)
            
            # 更新分组计数
            self._update_group_counts(self._accounts)
            
            # 显示提示信息
            self.show_info("账户导入", f"成功导入 {len(new_accounts)} 个新账户", "success")
        else:
            # 如果当前显示的是特定分组的账户，检查新账户是否属于当前分组
            group_accounts = []
            for new_acc in new_accounts:
                if new_acc.get('group_id') == self._selected_group_id:
                    group_accounts.append(new_acc)
            
            # 如果有属于当前分组的新账户，更新表格
            if group_accounts:
                # 合并新账户数据到当前账户列表
                account_ids = {acc.get('id') for acc in self._accounts if acc.get('id')}
                for new_acc in group_accounts:
                    if new_acc.get('id') not in account_ids:
                        self._accounts.append(new_acc)
                        account_ids.add(new_acc.get('id'))
                
                # 更新账户表格
                self._account_table.update_table(self._accounts)
                
                # 显示提示信息
                self.show_info("账户导入", f"成功导入 {len(group_accounts)} 个新账户到当前分组", "success")
            
        # 更新缓存
        if not self._cache_initialized:
            await self._init_cache()
        else:
            # 合并新账户数据到缓存
            cache_account_ids = {acc.get('id') for acc in self._accounts_cache if acc.get('id')}
            for new_acc in new_accounts:
                if new_acc.get('id') not in cache_account_ids:
                    self._accounts_cache.append(new_acc)
                    cache_account_ids.add(new_acc.get('id'))
            
            # 更新分组计数
            self._update_group_counts(self._accounts_cache)

