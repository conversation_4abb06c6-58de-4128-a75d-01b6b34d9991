from qasync import asyncSlot
from PySide6.QtCore import Qt
from utils.signal_bus import signal_bus
from qfluentwidgets import FluentIcon
from ui.common.message_box import createSuccessInfoBar, hide_loading, show_top_right_loading
from ui.dialogs.add_account_dialog import AddAccount<PERSON>
from utils.logger import get_logger
from app.controllers.account_controller import AccountController


class AddAccountView(AddAccountUI):
    
    def __init__(self, parent=None, account_controller:AccountController=None):
        super().__init__(parent)
        self.account_controller = account_controller
        self.is_code_sent = False
        self.client = None
        self.proxy_settings =None
        self._logger = get_logger("ui.views.add_account_view")
        # 初始化界面
        self.init_view()
        self.connect_signals()
    
    @asyncSlot()
    async def init_view(self):
        """初始化视图数据"""
        # 加载账户分组数据
        await self.load_account_groups()
        # 如果选择了代理IP池，则加载代理IP数据
        if self.proxy_ip_radio.isChecked():
            await self.load_proxy_ips()
    
    def connect_signals(self):
        """连接所有信号和槽"""
        # 代理相关信号
        self.proxy_ip_radio.toggled.connect(self.on_proxy_selection_changed)
        self.refresh_ip_btn.clicked.connect(lambda:self.on_proxy_selection_changed(True))
        
        # 表单操作信号
        self.send_code_btn.clicked.connect(self.send_verification_code)
        self.login_btn.clicked.connect(self.login)
        self.cancel_btn.clicked.connect(self.reject)
        # 分组单选框信号
        self.no_proxy_radio.toggled.connect(self.get_proxy_settings)
        self.system_proxy_radio.toggled.connect(self.get_proxy_settings)
        self.proxy_ip_radio.toggled.connect(self.get_proxy_settings)
        # 登录信号
        signal_bus.progress_updated.connect(self.show_info)
        signal_bus.operation_result.connect(self.show_info)
        signal_bus.notification.connect(self.show_info)
        
        # 连接账户控制器信号
        self.account_controller.login_code_sent.connect(self.on_login_code_sent)
        self.account_controller.login_code_verified.connect(self.on_login_code_verified)
        #self.account_controller.login_password_verified.connect(self.on_login_password_verified)
            
    @asyncSlot(bool)
    async def on_proxy_selection_changed(self, checked):
        """代理选择变更处理"""
        if checked:
            await self.load_proxy_ips()
    

    async def load_proxy_ips(self):
        """加载代理IP池数据"""
        if self.account_controller:
            # 获取代理IP列表
            proxy_ips = await self.account_controller.get_proxy_ips()
            # 更新UI中的IP列表
            self.update_ip_list(proxy_ips)
        else:
            self.show_info("错误", "账户控制器未初始化", "error")
    
    @asyncSlot()
    async def load_account_groups(self):
        """加载账户分组数据"""
        if self.account_controller:
            try:
                # 获取所有分组
                groups = await self.account_controller.get_account_groups()
                
                # 清空并重新填充下拉框
                self.group_combo.clear()
                
                # 添加默认选项
                self.group_combo.addItem(text="无分组",
                                          userData=-1)  # 默认选项，存储-1表示无分组
                
                # 添加所有分组到下拉框
                for group in groups:
                    self.group_combo.addItem(
                        text=group['name'], userData=group['id'])
                    
                self._logger.info(f"成功加载 {len(groups)} 个分组")
            except Exception as e:
                self.show_info("错误", f"加载分组失败: {str(e)}", "error")
                self._logger.error(f"加载分组失败: {e}")

    def get_proxy_settings(self):
        """获取代理设置"""
        if self.no_proxy_radio.isChecked():
            self._logger.info("禁用代理")
            return {
                "type": "none"
            }
        elif self.system_proxy_radio.isChecked():
            self._logger.info("使用系统代理")
            return {
                "type": "system"
            }
        elif self.proxy_ip_radio.isChecked():
            # 如果选中了代理IP，获取当前选中的IP
            selected_items = self.ip_list.selectedItems()
            self._logger.info(f"selected_items: {selected_items}")
            if selected_items:
                # 获取存储在UserRole中的数据
                ip_info = selected_items[0].data(Qt.UserRole)
                self._logger.info(f"使用代理IP: {ip_info}")
                return {
                    "id":ip_info['id'],
                    "type":"ip_pool",
                    "proxy_type": ip_info['proxy_type'],
                    "ip": ip_info['ip'],
                    "port": ip_info['port'],
                    "username": ip_info.get('username', ''),
                    "password": ip_info.get('password', '')
                }
        return None


    @asyncSlot()
    async def send_verification_code(self):
        """发送验证码"""

        phone_number = self.phone_input.text().strip()
        phone_number = phone_number.replace(" ", "")
        
        # 验证手机号格式
        if not phone_number:
            self.show_info("错误", "请输入手机号", "error")
            return
            
        # 检查是否只包含+号和数字
        if not all(c in '+0123456789' for c in phone_number):
            self.show_info("错误", "手机号只能包含+号和数字", "error")
            return
            
        # 检查并添加+号前缀
        if not phone_number.startswith('+'):
            phone_number = '+' + phone_number
        
        # 获取代理设置
        self.proxy_settings = self.get_proxy_settings()
        if self.proxy_settings is None:
            self.show_info("错误", "请选择一个代理IP", "error")
            return
        
        # 调用控制器发送验证码
        if self.account_controller:
            try:
                # 禁用发送按钮，防止重复点击
                self.send_code_btn.setEnabled(False)
                self.send_code_btn.setText("发送中...")
                #show_top_right_loading(self, "发送验证码中...")
                self._logger.info(f"发送验证码: {phone_number}, 代理设置: {self.proxy_settings}")
                
                # 调用控制器发送验证码 - 结果会通过信号返回
                await self.account_controller.start_login(
                    phone=phone_number,
                    proxy=self.proxy_settings
                )
                # 不在这里处理结果，由信号处理
            except Exception as e:
                self.send_code_btn.setEnabled(True)
                self.send_code_btn.setText("发送验证码")
                self.show_info("错误", f"发送失败: {str(e)}", "error")
                #hide_loading(self, "发送验证码失败")
        else:
            self.show_info("错误", "账户控制器未初始化", "error")
    
    @asyncSlot()
    async def login(self):
        """登录按钮点击处理"""
        # 获取输入数据
        phone_number = self.phone_input.text().strip()
        phone_number = phone_number.replace(" ", "")
        
        # 验证手机号格式
        if not phone_number:
            self.show_info("错误", "请输入手机号", "error")
            return
            
        # 检查是否只包含+号和数字
        if not all(c in '+0123456789' for c in phone_number):
            self.show_info("错误", "手机号只能包含+号和数字", "error")
            return
            
        # 检查并添加+号前缀
        if not phone_number.startswith('+'):
            phone_number = '+' + phone_number
        code = self.code_input.text().strip()
        password = self.password_input.text()
        

        
        # 验证输入
        if not phone_number:
            self.show_info("错误", "请输入手机号", "error")
            return
        
        if not code:
            self.show_info("错误", "请输入验证码", "error")
            return
        
        if not self.is_code_sent:
            self.show_info("错误", "请先发送验证码", "error")
            return
        
        # 选择的分组ID
        group_id = self.group_combo.currentData()
        # 登录按钮状态
        self.login_btn.setEnabled(False)
        self.login_btn.setText("登录中...")
        
        # 调用控制器进行登录
        if self.account_controller:
            try:
                # 提交验证码 - 结果会通过信号返回
                await self.account_controller.submit_code(phone_number, code, password)
                
                # 如果有输入密码，可能是预先知道需要两步验证
                
                # 注意：不在这里直接处理结果，由信号处理程序统一处理
                # 最终的完成登录操作会在验证成功后的信号处理中完成
            except Exception as e:
                self.login_btn.setEnabled(True)
                self.login_btn.setText("登录")
                self.show_info("错误", f"登录过程异常: {str(e)}", "error")
        else:
            self.login_btn.setEnabled(True)
            self.login_btn.setText("登录")
            self.show_info("错误", "账户控制器未初始化", "error")
        
    def on_login_code_sent(self, phone, success, message):
        """处理验证码发送结果
        
        Args:
            phone: 手机号
            success: 是否成功
            message: 消息内容
        """
        if success:
            self.is_code_sent = True
            self.send_code_btn.setText("已发送")
            createSuccessInfoBar(self, "发送验证码成功")
            self.show_info("验证码发送成功", message, "success")
        else:
            self.send_code_btn.setEnabled(True)
            self.send_code_btn.setText("发送验证码")
            #hide_loading(self, "发送验证码失败")
            self.show_info("验证码发送失败", message, "error")
    
    @asyncSlot()
    async def on_login_code_verified(self, phone, success, result):
        """处理验证码验证结果
        
        Args:
            phone: 手机号
            success: 是否成功
            result: 结果对象或错误消息
        """
        print(f"登录结果:{phone},{success},{result}")
        if success:
            # 如果不需要密码，可以直接完成登录
            # 获取分组ID
            group_id = self.group_combo.currentData()
            # 如果group_id为-1，表示无分组，将其设为None
            if group_id == -1:
                group_id = None
                
            # 获取代理设置
            proxy_id = None
            proxy_type = None
            if self.proxy_settings:
                proxy_type = self.proxy_settings.get('type')
                if proxy_type == 'ip_pool':
                    # 使用IP池时设置代理ID
                    proxy_id = self.proxy_settings.get('id')
            
            # 完成登录过程
            login_success = await self.account_controller.complete_login(
                phone=phone,
                proxy_id=proxy_id,
                proxy_type=proxy_type,
                group_id=group_id
            )
            
            if login_success:
                self._logger.info("保存成功")
                self.show_info("成功", "账户登录成功并保存", "success")
                self.close()
            else:
                self.login_btn.setEnabled(True)
                self.login_btn.setText("登录")
                self.show_info("错误", "账户信息保存失败", "error")

        else:
            self._logger.info("验证码验证失败")
            self.show_info("验证码验证失败", str(result), "error")
            self.login_btn.setEnabled(True)
            self.login_btn.setText("登录")
            
            # 如果需要密码，显示提示
            if result == "password_needed":
                self.show_info("需要两步验证", "请输入两步验证密码", "info")
                self.password_input.setFocus()
