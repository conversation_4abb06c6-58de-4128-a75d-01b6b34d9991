#!/usr/bin/env python
# -*- coding: utf-8 -*-

from PySide6.QtCore import Qt, Signal, Slot, QTimer
from PySide6.QtWidgets import QWidget, QFileDialog, QMessageBox, QDialog, QListWidgetItem
from qfluentwidgets import InfoBar, InfoBarPosition, Dialog
import json
import os
from qasync import asyncSlot
from utils.logger import get_logger
from app.controllers.account_controller import AccountController
from app.controllers.invite_controller import InviteController
from ui.dialogs.add_invite_task import Ui_Form
import re

class AddInviteTaskView(QDialog):
    """邀请任务视图类，实现UI交互逻辑"""
    
    # 信号定义
    taskSaved = Signal(dict)  # 任务保存信号
    
    def __init__(self, account_controller: AccountController = None,invite_controller:InviteController = None, parent=None):
        super().__init__(parent)
        self.setWindowTitle("添加邀请任务")
        
        # 初始化UI
        self.ui = Ui_Form()
        self.ui.setupUi(self)
        self.logger = get_logger("add_invite_task_view")
        
        # 初始化账户控制器
        self._account_controller = account_controller
        self.invite_controller = invite_controller
        # 设置窗口圆角
        self.setStyleSheet("""
            QDialog {
                background-color: white;
                border-radius: 16px;
            }
        """)
        # 设置无边框
        #self.setWindowFlags(Qt.Dialog | Qt.FramelessWindowHint)
        
        # 初始化数据
        self.task_data = {}
        self.users_list = []
        
        # 账户分组和用户相关变量
        self._account_groups = []  # 存储账户分组数据
        self._accounts = []  # 存储当前选中分组的账户
        self._is_loading_groups = False  # 是否正在加载分组
        self._is_loading_accounts = False  # 是否正在加载账户
        self._selected_accounts = []  # 存储选中的账户
        
        # 连接信号与槽
        self._connectSignalsSlots()
        
        # 设置默认值
        self._setDefaults()
        
        # 加载账户分组和账户数据
    
    def _load_initial_data(self):
        """加载初始数据：账户分组和账户"""
        self.logger.info("加载初始数据")
        try:
            self._load_account_groups()
        except Exception as e:
            self.logger.error(f"加载初始数据时发生错误: {e}", exc_info=True)
            self.show_info("加载失败", f"加载初始数据时发生错误: {e}", "error")
    
    @asyncSlot()
    async def _load_account_groups(self):
        """异步加载账户分组"""
        self.logger.info("加载账户分组")
        self._is_loading_groups = True
        
        try:
            account_groups = await self._account_controller.load_all_groups()
            if account_groups:
                self.ui.AccountBox.clear()
                self._account_groups = account_groups
                
                # 添加"所有账户"选项
                self.ui.AccountBox.addItem("所有账户", userData=-1)
                
                # 添加其他分组
                for account in self._account_groups:
                    self.ui.AccountBox.addItem(account['name'], userData=account['id'])
                
                self.logger.info(f"加载账户分组数据: {len(account_groups)}个分组")
                
                # 默认加载所有账户
                await self._load_accounts_by_group(-1)
            else:
                self.show_info("账户分组数据获取失败", "未能获取到账户分组数据。", "warning")
        except Exception as e:
            self.logger.error(f"加载账户分组数据时发生错误: {e}", exc_info=True)
            self.show_info("加载失败", f"加载账户分组数据时发生错误: {e}", "error")
        finally:
            self._is_loading_groups = False
    
    @asyncSlot()
    async def _load_accounts_by_group(self, group_id=-1):
        """根据分组ID异步加载账户"""
        self.logger.info(f"根据分组ID加载账户: {group_id}")
        self._is_loading_accounts = True
        
        try:
            # 清空当前账户列表
            self.ui.AccountsListWidget.clear()
            
            # 根据group_id获取账户列表
            if group_id >= 0:
                accounts = await self._account_controller.get_accounts_by_group(group_id)
            else:
                accounts = await self._account_controller.load_all_accounts(active_only=True)
                
            if accounts and isinstance(accounts, list):
                self._accounts = accounts
                
                if not accounts:
                    self.show_info("获取失败", "当前分组没有用户", "warning")
                    return
                
                # 将账户添加到列表控件中
                for account in accounts:
                    # 获取用户名和手机号
                    username = account.get('username', '未知用户') if isinstance(account, dict) else '未知用户'
                    phone = account.get('phone', '无手机号') if isinstance(account, dict) else '无手机号'
                    
                    # 创建显示文本，格式为: "用户名 | 手机号"
                    display_text = f"{username} | {phone}"
                    
                    # 创建列表项并添加
                    item = QListWidgetItem(display_text)
                    item.setFlags(item.flags() | Qt.ItemIsUserCheckable)
                    item.setCheckState(Qt.Unchecked)
                    item.setData(Qt.UserRole, account)  # 存储整个账户数据对象
                    self.ui.AccountsListWidget.addItem(item)
                    
                self.logger.info(f"已加载 {len(accounts)} 个账户到列表")
            else:
                self.logger.warning(f"未找到该分组下的账户: {group_id}")
                self.show_info("无账户", f"在此分组下未找到账户。", "info")
        except Exception as e:
            self.logger.error(f"加载分组账户时发生错误: {e}", exc_info=True)
            self.show_info("加载失败", f"加载分组账户时发生错误: {e}", "error")
        finally:
            self._is_loading_accounts = False
    
    def _connectSignalsSlots(self):
        """连接信号与槽"""
        # 按钮事件
        self.ui.SaveBtn.clicked.connect(self._onSaveClicked)
        self.ui.cancelBtn.clicked.connect(self._onCancelClicked)
        self.ui.ImportUser.clicked.connect(self._onImportClicked)
        
        # 单选按钮组
        self.ui.RadioButton_invite.toggled.connect(self._onInviteMethodChanged)
        self.ui.RadioButton_link.toggled.connect(self._onInviteMethodChanged)
        self.ui.CustomUser.toggled.connect(self._onUserSourceChanged)
        self.ui.Collectusers.toggled.connect(self._onUserSourceChanged)
        
        # 模板按钮 - 互斥逻辑
        self.ui.ToggleButton.clicked.connect(lambda: self._onTemplateToggled("formal"))
        self.ui.ToggleButton_friend.clicked.connect(lambda: self._onTemplateToggled("friend"))
        self.ui.ToggleButton_activity.clicked.connect(lambda: self._onTemplateToggled("activity"))
        
        # 全选/取消选择框
        self.ui.CheckBox.toggled.connect(self._onSelectAllToggled)
        
        # 账户分组下拉框的信号连接
        self.ui.AccountBox.currentIndexChanged.connect(self._on_group_index_changed)
        
        # 全选/取消所有账户
        self.ui.CheckBoxAccounts.toggled.connect(self._toggleSelectAllAccounts)
    
    def _setDefaults(self):
        """设置默认值"""
        # 默认选择直接邀请
        self.ui.RadioButton_invite.setChecked(True)
        
        # 默认选择自定义用户
        self.ui.CustomUser.setChecked(True)
        
        # 设置默认邀请间隔和数量
        self.ui.LineEdit_4.setText("30")
        self.ui.LineEdit_5.setText("60")
        self.ui.LineEdit_6.setText("10")
        self.ui.LineEdit_7.setText("20")
        
        # 初始状态下隐藏采集用户框架
        self.ui.frame.setVisible(False)
        
        # 设置群组/频道用户名placeholder为英文username
        self.ui.GroupName.setPlaceholderText("输入群组或频道英文username，如 mygroup123")
        
        # 设置邀请链接placeholder为例子url
        self.ui.InviteLink.setPlaceholderText("如：https://t.me/+zRT6ajcn9dc3NDM0")
    
    def _on_group_index_changed(self):
        """当账户分组选择发生变化时的处理"""
        # 如果正在加载，则忽略
        if self._is_loading_groups:
            return
            
        # 获取当前选中的分组ID
        group_id = self.ui.AccountBox.currentData()
        if group_id is not None:
            # 使用QTimer.singleShot确保异步方法在事件循环中被调用
            QTimer.singleShot(0, lambda: self._load_accounts_by_group(group_id))
            self.logger.info(f"切换到分组ID: {group_id}")
            return
            
        self.show_info("提醒", "该分组没有用户", "warning")
    
    def _toggleSelectAllAccounts(self, checked):
        """全选/取消所有账户"""
        for i in range(self.ui.AccountsListWidget.count()):
            item = self.ui.AccountsListWidget.item(i)
            item.setCheckState(Qt.Checked if checked else Qt.Unchecked)
            
        self.logger.info(f"{'全选' if checked else '取消全选'}账户")
    
    def _onInviteMethodChanged(self, checked):
        """邀请方式改变处理"""
        if checked:
            
            if self.ui.RadioButton_invite.isChecked():
                # 直接邀请模式
                self.ui.GroupName.setPlaceholderText("输入群组或频道的用户名")
                self.ui.CaptionLabel_2.setText("正式邀请邀请链接")
            else:
                QMessageBox.warning(self, "提示", "链接邀请本质上属于消息+邀请链接群发，没有设计的必要，请使用直接邀请。\n"
                                                    "如果需要使用链接邀请，请使用消息+邀请链接群发功能，账户控制更精细。\n"
                                                    "该功能将会在下个版本移除，请勿使用。")
           
                # 链接邀请模式
                self.ui.RadioButton_invite.setChecked(True)
                return
                self.ui.GroupName.setPlaceholderText("输入群组或频道的链接")
                self.ui.CaptionLabel_2.setText("邀请链接")
    
    def _onUserSourceChanged(self, checked):
        """用户来源改变处理"""
        if checked:
            # 根据选择显示/隐藏相应控件
            is_custom = self.ui.CustomUser.isChecked()
            
            # 控制PlainTextEdit的显示和启用状态
            self.ui.PlainTextEdit.setVisible(is_custom)
            self.ui.PlainTextEdit.setEnabled(is_custom)
            
            # 控制导入按钮的显示和启用状态
            self.ui.ImportUser.setVisible(is_custom)
            self.ui.ImportUser.setEnabled(is_custom)
            
            # 显示/隐藏采集用户框架
            self.ui.frame.setVisible(not is_custom)
            
            if self.ui.Collectusers.isChecked():
                QTimer.singleShot(0, lambda: self._load_monitor_tasks())
    
    def _onTemplateToggled(self, template_type):
        """处理模板按钮切换"""
        # 取消其他按钮选择状态
        if template_type == "formal":
            self.ui.ToggleButton.setChecked(True)
            self.ui.ToggleButton_friend.setChecked(False)
            self.ui.ToggleButton_activity.setChecked(False)
        elif template_type == "friend":
            self.ui.ToggleButton.setChecked(False)
            self.ui.ToggleButton_friend.setChecked(True)
            self.ui.ToggleButton_activity.setChecked(False)
        elif template_type == "activity":
            self.ui.ToggleButton.setChecked(False)
            self.ui.ToggleButton_friend.setChecked(False)
            self.ui.ToggleButton_activity.setChecked(True)
        
        # 应用模板
        self._applyTemplate(template_type)
    
    def _onSelectAllToggled(self, checked):
        """处理全选/取消全选"""
        # 遍历所有列表项并设置选中状态
        for i in range(self.ui.ListWidget.count()):
            item = self.ui.ListWidget.item(i)
            item.setCheckState(Qt.Checked if checked else Qt.Unchecked)
    
    def _applyTemplate(self, template_type):
        """应用邀请消息模板"""
        templates = {
            "formal": "您好，诚邀您加入我们的群组，一起交流学习。",
            "friend": "嘿，老朋友！我们有个新群，快来加入吧！",
            "activity": "我们正在举办活动，诚邀您参加，详情请加入群组了解。"
        }
        
        self.ui.InviteMsg.setText(templates.get(template_type, ""))
    
    def _onImportClicked(self):
        """导入用户列表"""
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "选择用户列表文件",
            "",
            "文本文件 (*.txt);;所有文件 (*)"
        )
        
        if file_path:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read().strip()
                    # 支持多种格式：每行一个或使用逗号分隔
                    if ',' in content:
                        users = [u.strip() for u in content.split(',')]
                    else:
                        users = [line.strip() for line in content.splitlines() if line.strip()]
                    
                    self.users_list = users
                    self.ui.PlainTextEdit.setPlainText(', '.join(users))
            except Exception as e:
                self.show_info("导入失败", f"无法导入用户列表: {str(e)}", "error")
    
    def _validateInputs(self):
        """验证输入数据"""
        # 检查任务名
        if not self.ui.TaskName.text().strip():
            self.show_info("验证失败", "请输入任务名称", "warning")
            return False
        
        # 检查群组/频道名
        group_name = self.ui.GroupName.text().strip()
        if not group_name:
            self.show_info("验证失败", "请输入群组或频道名称", "warning")
            return False
        
        # 校验群组/频道用户名只能为英文、数字、下划线
        if not re.fullmatch(r"[A-Za-z0-9_]+", group_name):
            self.show_info("验证失败", "群组/频道用户名只能包含英文、数字和下划线！", "warning")
            return False
        
        # 检查邀请链接
        if not self.ui.InviteLink.text().strip():
            self.show_info("验证失败", "请输入邀请链接，如：https://t.me/+zRT6ajcn9dc3NDM0", "warning")
            return False
        
        # 检查邀请间隔
        try:
            min_interval = int(self.ui.LineEdit_4.text())
            max_interval = int(self.ui.LineEdit_5.text())
            if min_interval <= 0 or max_interval <= 0 or min_interval > max_interval:
                self.show_info("验证失败", "请输入有效的邀请间隔范围", "warning")
                return False
        except ValueError:
            self.show_info("验证失败", "邀请间隔必须是数字", "warning")
            return False
        
        # 检查邀请数量
        try:
            min_count = int(self.ui.LineEdit_6.text())
            max_count = int(self.ui.LineEdit_7.text())
            if min_count <= 0 or max_count <= 0 or min_count > max_count:
                self.show_info("验证失败", "请输入有效的邀请数量范围", "warning")
                return False
        except ValueError:
            self.show_info("验证失败", "邀请数量必须是数字", "warning")
            return False
        
        # 检查用户列表
        if self.ui.CustomUser.isChecked():
            users_text = self.ui.PlainTextEdit.toPlainText().strip()
            if not users_text:
                self.show_info("验证失败", "请输入用户列表", "warning")
                return False
            
            # 解析用户列表
            if ',' in users_text:
                self.users_list = [u.strip() for u in users_text.split(',')]
            else:
                self.users_list = [line.strip() for line in users_text.splitlines() if line.strip()]
            
            if not self.users_list:
                self.show_info("验证失败", "用户列表为空", "warning")
                return False
        else:
            # 采集用户模式 - 检查是否有选中的用户
            has_selected = False
            for i in range(self.ui.ListWidget.count()):
                if self.ui.ListWidget.item(i).checkState() == Qt.Checked:
                    has_selected = True
                    break
            
            if not has_selected:
                self.show_info("验证失败", "请选择至少一个采集到的用户", "warning")
                return False
        
        # 检查是否选择了账户
        selected_accounts = []
        for i in range(self.ui.AccountsListWidget.count()):
            item = self.ui.AccountsListWidget.item(i)
            if item.checkState() == Qt.Checked:
                account_data = item.data(Qt.UserRole)
                if account_data:
                    selected_accounts.append(account_data)
        
        if not selected_accounts:
            self.show_info("验证失败", "请至少选择一个账户", "warning")
            return False
            
        self._selected_accounts = selected_accounts
        
        monitor_task_ids = []
        if self.ui.Collectusers.isChecked():
            # 支持多选
            for i in range(self.ui.ListWidget.count()):
                item = self.ui.ListWidget.item(i)
                if item.checkState() == Qt.Checked:
                    monitor_task_ids.append(item.data(Qt.UserRole))
            if not monitor_task_ids:
                self.show_info("验证失败", "请选择至少一个监控任务", "warning")
                return False
            self.task_data['monitor_task_id'] = monitor_task_ids
        
        return True
    
    def _collectTaskData(self):
        """收集任务数据，确保与InviteTask模型字段匹配"""
        users_list = []
        if self.ui.CustomUser.isChecked():
            users_text = self.ui.PlainTextEdit.toPlainText().strip()
            if users_text:
                users_text = users_text.replace('，', ',')
                users = [u.strip() for u in users_text.split(',') if u.strip()]
                final_users = []
                for u in users:
                    lines = [line.strip() for line in u.splitlines() if line.strip()]
                    final_users.extend(lines)
                users_list = list(set(final_users))
            else:
                users_list = []
        selected_accounts = []
        for i in range(self.ui.AccountsListWidget.count()):
            item = self.ui.AccountsListWidget.item(i)
            if item.checkState() == Qt.Checked:
                account_data = item.data(Qt.UserRole)
                if account_data:
                    selected_accounts.append(account_data)
        # 采集用户模式下收集所有选中的监控任务id
        monitor_task_ids = []
        if self.ui.Collectusers.isChecked():
            for i in range(self.ui.ListWidget.count()):
                item = self.ui.ListWidget.item(i)
                if item.checkState() == Qt.Checked:
                    monitor_task_ids.append(item.data(Qt.UserRole))
        self.task_data = {
            "task_name": self.ui.TaskName.text().strip(),
            "accounts": selected_accounts,
            "group_name": self.ui.GroupName.text().strip(),
            "group_invite_link": self.ui.InviteLink.text().strip(),
            "status": "pending",
            "message": self.ui.InviteMsg.toPlainText().strip(),
            "invite_interval_min": int(self.ui.LineEdit_4.text()),
            "invite_interval_max": int(self.ui.LineEdit_5.text()),
            "invite_type": "custom" if self.ui.CustomUser.isChecked() else "task",
            "batch_size_min": int(self.ui.LineEdit_6.text()),
            "batch_size_max": int(self.ui.LineEdit_7.text()),
            "users": users_list,
            "invite_method": "direct" if self.ui.RadioButton_invite.isChecked() else "link"
        }
        if self.ui.Collectusers.isChecked():
            self.task_data['monitor_task_id'] = monitor_task_ids
        return self.task_data
    
    @Slot()
    @asyncSlot()
    async def _onSaveClicked(self):
        """保存按钮点击处理"""
        if not self._validateInputs():
            return
        
        task_data = self._collectTaskData()
        try:
            success, result = await self.invite_controller.create_invite_task(task_data)
            
            if success:
                self.show_info("保存成功", f"任务已保存", "success")
                self.taskSaved.emit(success)
                self.close()
            else:
                self.show_info("保存失败", f"无法保存任务: {result}", "error")
        except Exception as e:
            self.logger.error(f"保存任务时发生错误: {str(e)}")
            self.show_info("保存失败", f"发生错误: {str(e)}", "error")
    
    @Slot()
    def _onCancelClicked(self):
        """取消按钮点击处理"""
        self.close()
    
    def loadTask(self, task_data):
        """加载现有任务数据"""
        if not task_data:
            return
        
        self.ui.TaskName.setText(task_data.get("task_name", ""))
        self.ui.GroupName.setText(task_data.get("group_name", ""))
        self.ui.InviteLink.setText(task_data.get("group_invite_link", ""))
        
        # 设置邀请间隔
        self.ui.LineEdit_4.setText(str(task_data.get("invite_interval_min", 30)))
        self.ui.LineEdit_5.setText(str(task_data.get("invite_interval_max", 60)))
        
        # 设置邀请数量
        self.ui.LineEdit_6.setText(str(task_data.get("batch_size_min", 10)))
        self.ui.LineEdit_7.setText(str(task_data.get("batch_size_max", 20)))
        
        # 设置邀请方式
        if task_data.get("invite_method") == "direct":
            self.ui.RadioButton_invite.setChecked(True)
        else:
            self.ui.RadioButton_link.setChecked(True)
        
        # 设置邀请消息
        self.ui.InviteMsg.setText(task_data.get("message", ""))
        
        # 设置用户来源
        if task_data.get("invite_type") == "custom":
            self.ui.CustomUser.setChecked(True)
        else:
            self.ui.Collectusers.setChecked(True)
        
        # 设置用户列表
        self.users_list = task_data.get("users", [])
        self.ui.PlainTextEdit.setPlainText(', '.join(self.users_list))
        
        # 等待账户加载完成，然后选中相应账户
        QTimer.singleShot(1000, lambda: self._apply_selected_accounts(task_data.get("accounts", [])))
        
    def _apply_selected_accounts(self, selected_accounts):
        """应用选中的账户到列表中"""
        if not selected_accounts:
            return
            
        try:
            # 遍历列表项，检查是否在选中的账户列表中
            for i in range(self.ui.AccountsListWidget.count()):
                item = self.ui.AccountsListWidget.item(i)
                account_data = item.data(Qt.UserRole)
                
                # 判断账户是否在选中列表中
                for selected in selected_accounts:
                    # 通过phone字段匹配
                    if (account_data and selected and 
                        account_data.get('phone') == selected.get('phone')):
                        item.setCheckState(Qt.Checked)
                        break
        except Exception as e:
            self.logger.error(f"应用已选账户时出错: {e}", exc_info=True)
    
    def showEvent(self, event):
        """显示事件，可用于加载最新数据"""
        super().showEvent(event)
        # 重新加载数据
        QTimer.singleShot(0, self._load_initial_data)

    def show_info(self, title, content, type='info', position = InfoBarPosition.TOP):
        """显示消息提示"""
        
        if type == 'success':
            InfoBar.success(
                title=title,
                content=content,
                orient=Qt.Horizontal,
                isClosable=True,
                position=position,
                duration=2000,
                parent=self
            )
        elif type == 'error':
            InfoBar.error(
                title=title,
                content=content,
                orient=Qt.Horizontal,
                isClosable=True,
                position=position,
                duration=3000,
                parent=self
            )
        elif type == 'warning':
            InfoBar.warning(
                title=title,
                content=content,
                orient=Qt.Horizontal,
                isClosable=True,
                position=position,
                duration=3000,
                parent=self
            )
        else:  # 默认为 info
            InfoBar.info(
                title=title,
                content=content,
                orient=Qt.Horizontal,
                isClosable=True,
                position=position,
                duration=2000,
                parent=self
            ) 

    @asyncSlot()
    async def _load_monitor_tasks(self):
        """异步加载监控任务列表到ListWidget"""
        self.ui.ListWidget.clear()
        try:
            tasks = await self.invite_controller.get_monitor_tasks_with_user_count()
            for task in tasks:
                display_text = f"{task['name']}（{task['user_count']}人）"
                item = QListWidgetItem(display_text)
                item.setData(Qt.UserRole, task['id'])  # 保存任务ID
                item.setFlags(item.flags() | Qt.ItemIsUserCheckable)
                item.setCheckState(Qt.Unchecked)
                self.ui.ListWidget.addItem(item)
        except Exception as e:
            self.logger.error(f"加载监控任务失败: {e}")
            self.show_info("加载失败", f"无法加载监控任务: {e}", "error")
