#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
添加监控任务视图
实现添加监控任务的UI界面
"""

from PySide6.QtWidgets import QDialog, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, QListWidgetItem, QMessageBox
from PySide6.QtCore import Qt, Signal, Slot, QTimer
from PySide6.QtGui import QIcon, QColor

from app.controllers.account_controller import AccountController
from app.controllers.telegram_monitor_controller import TelegramMonitorController
from ui.common.message_box import createErrorInfoBar, createInfoInfoBar, createSuccessInfoBar, createWarningInfoBar
from ui.dialogs.add_monitor_task_dialog import AddTaskDialogUI
from utils.logger import get_logger
import qasync
from typing import List, Dict, Any, Optional

from qasync import asyncSlot
from qfluentwidgets import (
    CardWidget, Push<PERSON>utton, ComboBox, LineEdit, 
    BodyLabel, TitleLabel, PrimaryPushButton, InfoBar, InfoBarPosition, 
    CheckBox, SearchLineEdit, MessageBox as FluentMessageBox, SubtitleLabel, 
    RadioButton, SwitchButton
)
from qfluentwidgets import FluentIcon as FIF

class AddMsgMonitorTask(AddTaskDialogUI):
    """任务添加对话框视图类，负责UI交互和事件转发"""
    taskAdded = Signal(dict)
    
    def __init__(self, 
                 account_controller: AccountController , 
                 telegram_monitor_controller: TelegramMonitorController, parent=None, 
              ):
        super().__init__(parent)
        self.setWindowTitle("添加/编辑监控任务")
        self.resize(900, 800)
        self.logger =  get_logger("AddMsgMonitorTaskView")
        
        self._account_controller = account_controller
        self._telegram_monitor_controller = telegram_monitor_controller
        
        # 初始化群组/频道缓存
        self._groups_cache = {}  # 格式: {phone: {'all': [...], 'groups': [...], 'channels': [], 'selections': {group_id: True/False}}}
        self._current_phone = None  # 当前选中的账户手机号
        self._current_account = None  # 当前选中的账户对象
        self._current_selected_task_id = None  # 当前选中的任务ID，用于编辑模式
        # 添加请求状态标志
        self._is_loading_groups = False  # 是否正在加载群组数据

        # self._load_initial_data()   #初始化加载用户分组和用户
        # 使用 QTimer.singleShot 来确保异步方法在事件循环中被调用
        QTimer.singleShot(0, self._load_initial_data)
        self.set_signal()
    def set_signal(self):
        self.cancel_button.clicked.connect(self.close)
        self.confirm_button.clicked.connect(self.save_task)
        # 连接账户分组下拉框的信号
        self.account_group_combo.currentIndexChanged.connect(self._on_group_index_changed)
        # 连接账户列表点击信号
        self.account_list.itemClicked.connect(self.on_account_item_clicked)
        
        # 连接群组类型过滤信号
        self.all_filter_radio.toggled.connect(lambda checked: checked and self._filter_by_type('all'))
        self.group_filter_radio.toggled.connect(lambda checked: checked and self._filter_by_type('group'))
        self.channel_filter_radio.toggled.connect(lambda checked: checked and self._filter_by_type('channel'))
        
        # 全选复选框信号
        self.select_all_checkbox.stateChanged.connect(self.on_select_all_changed)
        
        # 添加群组列表点击信号 - 使整行可点击
        self.group_list.itemClicked.connect(self.on_group_item_clicked)
        
        # 连接任务类型变化信号
        self.task_type_combo.currentTextChanged.connect(self._on_task_type_changed)
        
        # 连接通知开关信号
        self.notification_toggle.checkedChanged.connect(self._on_notification_toggle_changed)
        
        # 初始化UI状态
        self._on_task_type_changed(self.task_type_combo.currentText())

    def _filter_by_type(self, filter_type):
        """
        切换显示类型并清空选中状态
        
        Args:
            filter_type: 'all', 'group', 或 'channel'
        """
        self.logger.info(f"切换显示类型到: {filter_type}")
        
        # 保存当前的显示类型
        # if self._current_phone and self._current_phone in self._groups_cache: # Original code had this if, but caused issues if no phone selected
        current_cache = self._groups_cache.get(self._current_phone, {}) # Get cache for current phone, or empty dict
        
        current_cache['current_filter'] = filter_type
        current_cache['selections'] = {} # 清空选择状态
        
        # Ensure the cache for the current phone is updated, even if it was initially empty
        if self._current_phone:
            self._groups_cache[self._current_phone] = current_cache
        
        # 显示对应类型的群组
        self.show_groups(filter_type)

    def _load_initial_data(self):
        """加载初始数据"""
        self.logger.info("加载初始数据")
        try:
            self._load_account_groups()

            self._load_accounts_by_group()

        except Exception as e:
            self.logger.error(f"加载初始数据时发生错误: {e}", exc_info=True)
            self.show_info_message("加载失败", f"加载初始数据时发生错误: {e}", type="error")
        
        #分组和账户绑定


    def _load_task_data(self, task_data: Dict[str, Any]):
        """加载任务数据到界面控件中"""
        self.logger.info(f"加载任务数据到界面: {task_data.get('name', '未知任务')}")
        try:
            # 设置基本信息
            self.task_name_edit.setText(task_data.get("name", ""))
            if hasattr(self, 'task_description_edit'):
                self.task_description_edit.setText(task_data.get("description", ""))
            
            # 设置关键词
            keywords = task_data.get("keywords", [])
            if keywords and isinstance(keywords, list):
                self.keyword_edit.setText(", ".join(keywords))
            
            # 设置任务类型
            task_type = "关键词监控" if keywords else "群组监控"
            index = self.task_type_combo.findText(task_type)
            if index >= 0:
                self.task_type_combo.setCurrentIndex(index)
                self._on_task_type_changed(task_type)  # 触发界面更新
            
            # 设置屏蔽选项
            block_enabled = task_data.get("block_enabled", False)
            self.block_toggle.setChecked(block_enabled)
            
            if block_enabled:
                block_type = task_data.get("block_type", "all")
                if block_type == "all":
                    self.block_both_radio.setChecked(True)
                elif block_type == "user":
                    self.block_username_radio.setChecked(True)
                elif block_type == "msg":
                    self.block_message_radio.setChecked(True)
                
                block_keywords = task_data.get("block_keywords", [])
                if block_keywords and isinstance(block_keywords, list):
                    self.block_edit.setText(", ".join(block_keywords))
            
            # 加载通知设置
            notifications = task_data.get("notifications", [])
            if notifications and len(notifications) > 0:
                # 启用通知开关
                self.notification_toggle.setChecked(True)
                self.notification_config_container.setVisible(True)
                
                # 获取第一个通知配置
                notification = notifications[0]
                
                # 设置通知目标地址
                self.target_edit.setText(notification.get("target_address", ""))
                
                # 设置通知模板
                if notification.get("template"):
                    self.template_edit.setText(notification.get("template"))
            
            # 记录要加载的群组，在账户加载完成后处理
            self._pending_monitored_chats = task_data.get("monitored_chats", [])
            
            self.logger.info(f"任务数据加载成功: {task_data.get('name')}")
        except Exception as e:
            self.logger.error(f"加载任务数据失败: {e}", exc_info=True)
            self.show_info_message("加载失败", f"加载任务数据时出错: {e}", type="error")

    @asyncSlot()
    async def set_task_data(self, task_id: str):
        """设置任务数据"""
        self._current_selected_task_id = task_id
        self.logger.info(f"设置任务数据: {task_id}")
        
        try:
            # 异步获取任务数据
            task_data = await self._telegram_monitor_controller.get_task_data(task_id)
            
            if task_data:
                self._load_task_data(task_data)
            else:
                self.show_info_message("任务数据获取失败", f"任务数据获取失败: {task_id}", type="error")
        except Exception as e:
            self.logger.error(f"获取任务数据失败: {e}", exc_info=True)
            self.show_info_message("数据获取失败", f"获取任务数据时出错: {e}", type="error")

    def show_info_message(self, title: str, message: str, icon: FIF = FIF.INFO,type="info"):
        """显示信息消息"""
        if type == "info":
            createInfoInfoBar(self,title,message)
        elif type == "success":
            createSuccessInfoBar(self,title,message)
        elif type == "warning":
            createWarningInfoBar(self,title,message)
        elif type == "error":
            createErrorInfoBar(self,title,message)
        else:
            createInfoInfoBar(self,title,message)



    def _get_task_config(self):
        """获取任务配置"""
        self.logger.info("获取任务配置")
        task_config = self._telegram_monitor_controller.get_task_config()
        if task_config:
            self._task_config = task_config
            self.logger.info(f"获取任务配置: {task_config}")

    def on_add_task_button_clicked(self):
        """添加任务按钮点击事件"""
        self.logger.info("添加任务按钮点击事件")
        self._get_task_config()
        
        

    def _on_notification_toggle_changed(self, checked):
        """处理通知开关状态变化"""
        self.notification_config_container.setVisible(checked)

    def _collect_task_config(self) -> Dict[str, Any]:
        """
        收集界面上的所有任务配置信息
        
        Returns:
            任务配置信息字典
        """
        self.logger.info("收集任务配置信息")
        
        # 初始化配置字典
        task_config = {
            "name": self.task_name_edit.text().strip(),
            "type": self.task_type_combo.currentText(), # 任务类型，如关键词监控、群组监控
            "keywords": self.keyword_edit.text().strip(),
            "block_enabled": self.block_toggle.isChecked(),
            "block_type": "all",  # 默认屏蔽类型为all
            "block_keywords": [],  # 屏蔽关键词列表
            "monitored_chats": [], # 存储所有账户的群组选择
            "notifications": [],  # 添加通知配置列表
        }
        
        # 获取屏蔽设置
        if self.block_toggle.isChecked():
            # 获取屏蔽类型
            if self.block_both_radio.isChecked():
                task_config["block_type"] = "all"
            elif self.block_username_radio.isChecked():
                task_config["block_type"] = "user"
            elif self.block_message_radio.isChecked():
                task_config["block_type"] = "msg"
                
            # 获取屏蔽关键词列表
            block_keywords_text = self.block_edit.text().strip()
            if block_keywords_text:
                task_config["block_keywords"] = [k.strip() for k in block_keywords_text.split(",")]
        
        # 从缓存中收集所有账户的群组选择
        for phone, cache_data in self._groups_cache.items():
            selections = cache_data.get('selections', {})
            if not selections:  # 如果该账户没有选择任何群组，跳过
                continue
                
            # 获取该账户的所有群组数据
            all_groups = cache_data.get('all', [])
            
            # 收集该账户选中的群组
            for group in all_groups:
                group_id = str(group.get('id', ''))
                if selections.get(group_id, False):  # 如果该群组被选中
                    task_config["monitored_chats"].append({
                        "chat_id": group_id,
                        "chat_title": group.get('title', '未命名'),
                        "account_phone": phone  # 添加所属账户的手机号
                    })
        
        # 如果启用了通知功能，添加通知配置
        if self.notification_toggle.isChecked():
            target_address = self.target_edit.text().strip()
            
            # 获取通知模板
            notification_template = self.template_edit.toPlainText().strip()
            
            # 添加通知配置（不指定账户，由系统自动使用监听到关键词的账户）
            if target_address:
                notification_config = {
                    "target_type": "auto",  # 自动判断是群组还是用户
                    "target_address": target_address,
                    "is_active": True,
                    "template": notification_template,
                    "use_trigger_account": True  # 标记使用触发监听的账户
                }
                task_config["notifications"].append(notification_config)
        
        # 验证必要信息
        task_config["is_valid"] = (
            task_config["name"] and 
            task_config["monitored_chats"] and  # 确保至少有一个群组被选中
            (task_config["type"] == "群组监控" or task_config["keywords"])
        )
        
        if not task_config["is_valid"]:
            task_config["validation_errors"] = []
            if not task_config["name"]:
                task_config["validation_errors"].append("任务名称不能为空")
            if not task_config["monitored_chats"]:
                task_config["validation_errors"].append("请至少选择一个群组或频道")
            if task_config["type"] != "群组监控" and not task_config["keywords"]:
                task_config["validation_errors"].append("关键词监控任务需要设置关键词")
        
        self.logger.info(f"收集的任务配置: 名称={task_config['name']}, "
                         f"监控群组数={len(task_config['monitored_chats'])}, "
                         f"有效性={task_config['is_valid']}")
        
        return task_config

    @asyncSlot()
    async def save_task(self):
        """
        读取所有配置信息，保存任务
        """
        self.logger.info("准备保存任务")
        
        task_config_ui = self._collect_task_config() 
        
        if not task_config_ui.get("is_valid", False):
            error_msg = "\n".join(task_config_ui.get("validation_errors", ["未知错误"]))
            self.show_info_message("无法保存任务", f"请检查以下问题:\n{error_msg}", type="error")
            return
        
        # 构造提交给控制器的数据字典
        task_data_for_controller = {
            "name": task_config_ui["name"],
            "description": self.task_description_edit.text() if hasattr(self, 'task_description_edit') else "",
            "monitored_chats": task_config_ui["monitored_chats"],
            "keywords": [kw.strip() for kw in task_config_ui["keywords"].split(",") if kw.strip()],
            "block_enabled": task_config_ui.get("block_enabled", False),
            "block_type": task_config_ui.get("block_type", "all"),
            "block_keywords": task_config_ui.get("block_keywords", []),
            "is_active": True,
        }
        
        # 确保每个monitored_chat都有account_phone
        for chat in task_data_for_controller["monitored_chats"]:
            if not chat.get("account_phone"):
                self.show_info_message("数据错误", "有监控群组未绑定账户，请重新选择", type="error")
                return
        
        self.logger.info(f"AddMsgMonitorTask: 向控制器提交的任务数据: {task_data_for_controller}")

        try:
            success = False
            result_data_or_error = "操作未执行"

            if self._current_selected_task_id:
                self.logger.info(f"AddMsgMonitorTask: 请求更新任务 ID: {self._current_selected_task_id}")
                success, result_data_or_error = await self._telegram_monitor_controller.submit_updated_task(
                    self._current_selected_task_id, task_data_for_controller
                )
            else:
                self.logger.info("AddMsgMonitorTask: 请求创建新任务")
                success, result_data_or_error = await self._telegram_monitor_controller.submit_new_task(
                    task_data_for_controller
                )
            
            if success:
                action = "更新" if self._current_selected_task_id else "创建"
                self.show_info_message(f"任务{action}成功", f"任务 '{task_config_ui['name']}' 已成功{action}。", type="success")
                if isinstance(result_data_or_error, dict): # 成功时应返回任务字典
                     self.taskAdded.emit(result_data_or_error) 
                self.close()
            else:
                # result_data_or_error 是错误消息字符串
                self.show_info_message("任务操作失败", f"操作失败: {str(result_data_or_error)}", type="error")
                    
        except Exception as e:
            self.logger.error(f"AddMsgMonitorTask: 保存任务时发生前端错误: {e}", exc_info=True)
            self.show_info_message("保存失败", f"保存任务时发生意外错误: {e}", type="error")

    @asyncSlot()
    async def _load_account_groups(self):
        """加载账户分组"""
        self.logger.info("加载账户分组")
        account_groups = await self._account_controller.load_all_groups()
        if account_groups:
            self.account_group_combo.clear()
            self._account_groups = account_groups
            self.account_group_combo.addItem("所有账户",userData=-1)
            for account  in self._account_groups:
                self.account_group_combo.addItem(account['name'],userData=account['id'])

            self.logger.info(f"加载账户分组数据: {account_groups}")
        else:
            self.show_info_message("账户分组数据获取失败", "未能获取到账户分组数据。", type="warning")


    @asyncSlot()
    async def _load_accounts_by_group(self, group_id=-1):
        """根据分组ID加载账户"""
        self.logger.info(f"根据分组ID加载账户: {group_id}")
        try:
            # 使用 _account_controller 的 get_accounts_by_group 方法加载账户
            if group_id>=0:
                accounts = await self._account_controller.get_accounts_by_group(group_id)
            else:
                accounts = await self._account_controller.load_all_accounts(active_only=True)
            if accounts and isinstance(accounts, list):
                # 使用 self.account_list (QListWidget)
                if not accounts:
                    self.show_info_message("获取失败","当前分组没有用户",'warning')
                    
                self.account_list.clear()
                for account in accounts:
                    # 获取用户名和手机号
                    print(account)
                    username = account.get('username', '未知用户') if isinstance(account, dict) else '未知用户'
                    phone = account.get('phone', '无手机号') if isinstance(account, dict) else '无手机号'
                    # 创建显示文本，格式为: "用户名 | 手机号"
                    display_text = f"{username} | {phone}"
                    
                    # 创建列表项并设置数据
                    item = QListWidgetItem(display_text)
                    item.setData(Qt.UserRole, account) # 存储整个账户数据对象
                    self.account_list.addItem(item)
                self.logger.info(f"已加载 {len(accounts)} 个账户到列表")
                return accounts
            else:
                self.logger.warning(f"未找到该分组下的账户: {group_id}")
                self.show_info_message("无账户", f"在此分组下未找到账户。", type="info")
                return []
        except Exception as e:
            self.logger.error(f"加载分组账户时发生错误: {e}", exc_info=True)
            self.show_info_message("加载失败", f"加载分组账户时发生错误: {e}", type="error")
            return []
    
    @Slot()
    def _on_group_index_changed(self):
        """当账户分组选择发生变化时，获取userData并调用处理方法"""
        # 获取当前选中的分组ID (userData)
        group_id = self.account_group_combo.currentData()
        if group_id is not None:
            # 使用 QTimer.singleShot 来确保异步方法在事件循环中被调用
            self.logger.info(f"当前分组id:{group_id}")
            self._load_accounts_by_group(group_id)
            return
        self.show_info_message('提醒',"该分组没有用户",'warning')

    def on_account_item_clicked(self, item):
        """处理账户列表项点击事件"""
        try:
            # 如果当前正在加载群组数据，则忽略点击事件
            if self._is_loading_groups:
                self.show_info_message("请稍等", "正在加载数据，请勿重复点击", type="warning")
                return
                
            # 如果当前有选中的账户，保存其群组选择状态
            if self._current_phone and self._current_phone in self._groups_cache:
                self._save_selection_state()
                
            # 从item中获取存储的账户数据
            account_data = item.data(Qt.UserRole)
            if account_data:
                self.logger.info(f"选择了账户: {account_data.get('username', '未知')} ({account_data.get('phone', '未知')})")
                
                # 保存当前选中的账户信息
                self._current_account = account_data
                self._current_phone = account_data.get('phone', '')
                
                # 清空当前群组列表
                self.group_list.clear()
                
                # 获取并显示该账户的群组和频道
                self._get_groups(self._current_phone)
        except Exception as e:
            self.logger.error(f"处理账户选择时发生错误: {e}", exc_info=True)
    
    @asyncSlot()
    async def _get_groups(self, phone):
        '''
        获取对应手机号的所有群组，频道
        如果缓存中有，则从缓存读取，否则从控制器获取
        '''
        if not phone:
            self.logger.warning("手机号为空，无法获取群组和频道")
            return
            
        self.logger.info(f"获取账户 {phone} 的群组和频道")
        
        # 检查缓存
        if phone in self._groups_cache:
            self.logger.info(f"从缓存读取 {phone} 的群组和频道数据")
            self.show_groups()  # 使用缓存中的数据显示
            return
            
        try:
            # 设置加载状态为True，防止重复请求
            self._is_loading_groups = True
            
            # 显示加载中提示
            self.show_info_message("加载中", "正在获取群组和频道数据，请稍候...", type="info")
            
            # 使用账户控制器获取群组和频道数据
            groups_data = await self._account_controller.get_account_groups_by_type(phone)
            
            if not groups_data:
                self.logger.warning(f"未获取到 {phone} 的群组和频道数据")
                self.show_info_message("网络超时", f"{phone} 未能获取到群组和频道数据", type="error")
                return
                
            # 分类存储
            all_groups = groups_data
            groups_only = [g for g in groups_data if g.get('type', '').lower() in ('group', 'supergroup')]
            channels_only = [g for g in groups_data if g.get('type', '').lower() == 'channel']
            
            # 保存到缓存，初始化选择状态为空字典
            self._groups_cache[phone] = {
                'all': all_groups,
                'group': groups_only,
                'channel': channels_only,
                'selections': {}  # 初始化选择状态缓存
            }
            
            # 显示群组和频道
            self.show_groups()
            
            # 记录日志
            self.logger.info(f"成功获取并缓存 {phone} 的群组和频道: {len(all_groups)} 个 (群组 {len(groups_only)}, 频道 {len(channels_only)})")
            
        except Exception as e:
            self.logger.error(f"获取群组和频道时发生错误: {e}", exc_info=True)
            self.show_info_message("获取失败", f"获取群组和频道数据时发生错误: {e}", type="error")
        finally:
            # 无论成功或失败，都将加载状态设置回False
            self._is_loading_groups = False
            
    def show_groups(self, type='all'):
        '''
        显示频道，或群组，或全部显示
        type: 'all', 'group', 'channel'
        '''
        if not self._current_phone or self._current_phone not in self._groups_cache:
            self.logger.warning("没有可显示的群组和频道数据")
            return
            
        # 获取缓存的数据
        cache_data = self._groups_cache[self._current_phone]
        groups_to_show = cache_data.get(type, [])
        
        # 获取该账户的选择状态缓存
        selections = cache_data.get('selections', {})
        
        # 清空当前列表
        self.group_list.clear()
        
        if not groups_to_show:
            self.logger.info(f"没有{type}类型的群组/频道可显示")
            return
            
        # 添加到列表中，并恢复选中状态
        any_checked = False  # 跟踪是否有任何项目被选中
        
        for group in groups_to_show:
            # 获取群组名称和ID
            group_name = group.get('title', '未知名称')
            group_id = group.get('id', '')
            group_username = group.get('username', '')
            group_type = group.get('type', '')
            
            # 创建显示文本
            type_label = "📢" if group_type == 'channel' else "👥"
            if group_username:
                display_text = f"{type_label} {group_name} (@{group_username})"
            else:
                display_text = f"{type_label} {group_name} (ID: {group_id})"
                
            # 创建列表项
            item = QListWidgetItem(display_text)
            item.setData(Qt.UserRole, group)  # 存储整个群组数据
            
            # 添加复选框并设置选中状态
            item.setFlags(item.flags() | Qt.ItemIsUserCheckable)
            
            # 从缓存中恢复选中状态
            is_checked = selections.get(str(group_id), False)
            if is_checked:
                item.setCheckState(Qt.Checked)
                any_checked = True
            else:
                item.setCheckState(Qt.Unchecked)
            
            self.group_list.addItem(item)
            
        # 更新全选按钮状态
        self.select_all_checkbox.blockSignals(True)
        self.select_all_checkbox.setChecked(any_checked and len(groups_to_show) == sum(1 for gid, checked in selections.items() if checked))
        self.select_all_checkbox.blockSignals(False)
            
        self.logger.info(f"显示了 {len(groups_to_show)} 个{type}类型的群组/频道")
    
    def on_select_all_changed(self, state):
        """处理全选/取消全选复选框状态变化"""
        # 检查是否当前有任何项目被选中
        any_checked = False
        for i in range(self.group_list.count()):
            if self.group_list.item(i).checkState() == Qt.Checked:
                any_checked = True
                break
        
        # 根据当前选择状态决定动作
        if any_checked:
            # 如果有任何项目被选中，则取消全选
            for i in range(self.group_list.count()):
                self.group_list.item(i).setCheckState(Qt.Unchecked)
            # 设置全选按钮状态为未选中
            self.select_all_checkbox.blockSignals(True)
            self.select_all_checkbox.setChecked(False)
            self.select_all_checkbox.blockSignals(False)
            self.logger.info("已取消全选")
        else:
            # 如果没有项目被选中，则全选
            for i in range(self.group_list.count()):
                self.group_list.item(i).setCheckState(Qt.Checked)
            # 设置全选按钮状态为选中
            self.select_all_checkbox.blockSignals(True)
            self.select_all_checkbox.setChecked(True)
            self.select_all_checkbox.blockSignals(False)
            self.logger.info("已全选")
            
        # 保存选择状态到缓存
        self._save_selection_state()

    def on_group_item_clicked(self, item):
        """处理群组列表项点击事件 - 点击整行即可切换复选框状态"""
        # 切换复选框状态
        if item.checkState() == Qt.Checked:
            item.setCheckState(Qt.Unchecked)
        else:
            item.setCheckState(Qt.Checked)
        
        # 保存选择状态到缓存
        self._save_selection_state()
        
        # 更新全选按钮状态
        all_checked = True
        for i in range(self.group_list.count()):
            if self.group_list.item(i).checkState() != Qt.Checked:
                all_checked = False
                break
        
        self.select_all_checkbox.blockSignals(True)
        self.select_all_checkbox.setChecked(all_checked)
        self.select_all_checkbox.blockSignals(False)

    # 添加一个方法，用于保存选择状态
    def _save_selection_state(self):
        """保存当前群组的选择状态到缓存"""
        if not self._current_phone or self._current_phone not in self._groups_cache:
            return
            
        # 获取缓存中的选择状态字典
        selections = {}
        
        # 遍历当前列表中的所有项目，保存选择状态
        for i in range(self.group_list.count()):
            item = self.group_list.item(i)
            group_data = item.data(Qt.UserRole)
            if group_data:
                group_id = str(group_data.get('id', ''))
                is_checked = item.checkState() == Qt.Checked
                selections[group_id] = is_checked
                
        # 更新缓存
        self._groups_cache[self._current_phone]['selections'] = selections
        self.logger.debug(f"已保存 {self._current_phone} 的群组选择状态: {len(selections)} 项")

    def _on_task_type_changed(self, task_type: str):
        """处理任务类型变化的逻辑"""
        if task_type == "关键词监控":
            # 显示关键词相关控件
            self.keyword_label.setVisible(True)
            self.keyword_edit.setVisible(True)
        else:  # 群组监控
            # 隐藏关键词相关控件
            self.keyword_label.setVisible(False)
            self.keyword_edit.setVisible(False)
