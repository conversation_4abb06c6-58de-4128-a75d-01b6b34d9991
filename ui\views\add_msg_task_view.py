#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import json
import random
import base64
from PySide6.QtCore import Qt, Signal, QSize, QByteArray, QBuffer, QTimer
from PySide6.QtWidgets import QWidget, QFileDialog, QMessageBox, QHBoxLayout, QLabel, QPushButton, QToolBar, QToolButton, QDialog, QListWidgetItem
from PySide6.QtGui import QIcon, QPixmap, QTextCursor, QImage, QAction
from qfluentwidgets import TransparentToolButton, FluentIcon, InfoBar, InfoBarPosition, MessageBox,StateToolTip
from ui.dialogs.add_msg_ui import Ui_AddMsg
from utils.logger import get_logger
from app.controllers.account_controller import AccountController
from app.controllers.send_msg_controller import SendMessageController
import qasync
from qasync import asyncSlot


class MsgItemWidget(QWidget):
    """消息项组件，包含消息内容和删除按钮"""
    def __init__(self, msg_text, index, parent=None):
        super().__init__(parent)
        self.index = index
        
        # 创建水平布局
        layout = QHBoxLayout(self)
        layout.setContentsMargins(5, 2, 5, 2)
        
        # 添加消息文本标签
        self.label = QLabel(msg_text)
        self.label.setWordWrap(True)
        self.label.setStyleSheet("padding-right: 10px;")
        layout.addWidget(self.label, 1)  # 设置拉伸因子为1，占据大部分空间
        
        # 添加编辑按钮
        self.edit_btn = TransparentToolButton(FluentIcon.EDIT)
        self.edit_btn.setFixedWidth(36)
        self.edit_btn.setToolTip("编辑")
        self.edit_btn.setStyleSheet("padding: 2px;")
        self.edit_btn.setVisible(False)  # 默认隐藏
        layout.addWidget(self.edit_btn)
        
        # 添加删除按钮
        self.delete_btn = TransparentToolButton(FluentIcon.DELETE)
        self.delete_btn.setFixedWidth(36)
        self.delete_btn.setToolTip("删除")
        self.delete_btn.setStyleSheet("padding: 2px;")
        self.delete_btn.setVisible(False)  # 默认隐藏
        layout.addWidget(self.delete_btn)
        
        self.setLayout(layout)
        
        # 安装事件过滤器以处理鼠标悬停事件
        self.installEventFilter(self)
        
    def enterEvent(self, event):
        """鼠标进入事件"""
        self.edit_btn.setVisible(True)
        self.delete_btn.setVisible(True)
        return super().enterEvent(event)
        
    def leaveEvent(self, event):
        """鼠标离开事件"""
        self.edit_btn.setVisible(False)
        self.delete_btn.setVisible(False)
        return super().leaveEvent(event)


class AddMsgView(QDialog, Ui_AddMsg):
    """消息任务添加界面"""
    
    # 定义信号
    saveSignal = Signal(dict)
    
    def __init__(self,account_controller: AccountController = None, 
            msg_controller: SendMessageController = None,
            parent=None):
        super().__init__(parent)
        # 设置UI
        self.setupUi(self)
        self.setWindowTitle("添加消息任务")
        self.resize(700, 500)
        self.logger = get_logger("add_msg_task_view")
        # 初始化变量
        self._account_controller = account_controller
        self._msg_controller = msg_controller
        self.msg_list = []
        self.selected_groups = []
        self.selected_tasks = []
        self.current_msg_type = "text"  # 默认为文本类型
        self.editing_index = -1  # 当前正在编辑的消息索引，-1表示不在编辑模式
        
        # 账户分组和用户相关变量
        self._account_groups = []  # 存储账户分组数据
        self._accounts = []  # 存储当前选中分组的账户
        self._is_loading_groups = False  # 是否正在加载分组
        self._is_loading_accounts = False  # 是否正在加载账户
        self._is_loading_tasks = False   # 是否正在加载监听任务
        
        # 连接信号槽
        self._connectSignals()
        # 初始化界面
        self._initUI()
    
    def _initUI(self):
        """初始化界面"""
        # 禁用图片和图文按钮
        #self.ImgBtn.setEnabled(False)
        #self.ImgTextBtn.setEnabled(False)
        #self.MsgTextEdit.insert_image_action.setEnabled(False)
        # 默认选择文本类型
        self.TextBtn.setChecked(True)
        
        # 初始化发送间隔默认值
        self.Str.setText("60")
        self.LineEdit_3.setText("180")
        self.LineEdit_4.setText("10")
        self.LineEdit_5.setText("30")
        
        # 默认选择自定义用户发送模式，而不是用户发送模式
        self.CustomUser.setChecked(True)
        self.UserRadioButton.setChecked(False)
        
        # 隐藏任务列表框，显示自定义用户框
        self.frame.setVisible(False)
        self.frame_2.setVisible(True)
        
        # 清空消息列表
        self.msgList.clear()
        
        # 注意：初始化数据加载移至showEvent中处理，避免重复加载
    
    def _load_initial_data(self):
        """加载初始数据：账户分组和账户"""
        self.logger.info("加载初始数据")
        try:
            self._load_account_groups()
        except Exception as e:
            self.logger.error(f"加载初始数据时发生错误: {e}", exc_info=True)
            self.show_info("加载失败", f"加载初始数据时发生错误: {e}", "error")
    
    @asyncSlot()
    async def _load_account_groups(self):
        """异步加载账户分组"""
        self.logger.info("加载账户分组")
        self._is_loading_groups = True
        
        try:
            account_groups = await self._account_controller.load_all_groups()
            if account_groups:
                self.GrouComboBox.clear()
                self._account_groups = account_groups
                
                # 添加"所有账户"选项
                self.GrouComboBox.addItem("所有账户", userData=-1)
                
                # 添加其他分组
                for account in self._account_groups:
                    self.GrouComboBox.addItem(account['name'], userData=account['id'])
                
                self.logger.info(f"加载账户分组数据: {len(account_groups)}个分组")
                
                # 默认加载所有账户
                await self._load_accounts_by_group(-1)
            else:
                self.show_info("账户分组数据获取失败", "未能获取到账户分组数据。", "warning")
        except Exception as e:
            self.logger.error(f"加载账户分组数据时发生错误: {e}", exc_info=True)
            self.show_info("加载失败", f"加载账户分组数据时发生错误: {e}", "error")
        finally:
            self._is_loading_groups = False
    
    @asyncSlot()
    async def _load_accounts_by_group(self, group_id=-1):
        """根据分组ID异步加载账户"""
        self.logger.info(f"根据分组ID加载账户: {group_id}")
        self._is_loading_accounts = True
        
        try:
            # 清空当前账户列表
            self.GroupList.clear()
            
            # 根据group_id获取账户列表
            if group_id >= 0:
                accounts = await self._account_controller.get_accounts_by_group(group_id)
            else:
                accounts = await self._account_controller.load_all_accounts(active_only=True)
                
            if accounts and isinstance(accounts, list):
                self._accounts = accounts
                
                if not accounts:
                    self.show_info("获取失败", "当前分组没有用户", "warning")
                    return
                
                # 将账户添加到列表控件中
                for account in accounts:
                    # 获取用户名和手机号
                    username = account.get('username', '未知用户') if isinstance(account, dict) else '未知用户'
                    phone = account.get('phone', '无手机号') if isinstance(account, dict) else '无手机号'
                    
                    # 创建显示文本，格式为: "用户名 | 手机号"
                    display_text = f"{username} | {phone}"
                    
                    # 创建列表项并添加
                    item = QListWidgetItem(display_text)
                    item.setFlags(item.flags() | Qt.ItemIsUserCheckable)
                    item.setCheckState(Qt.Unchecked)
                    item.setData(Qt.UserRole, account)  # 存储整个账户数据对象
                    self.GroupList.addItem(item)
                    
                self.logger.info(f"已加载 {len(accounts)} 个账户到列表")
            else:
                self.logger.warning(f"未找到该分组下的账户: {group_id}")
                self.show_info("无账户", f"在此分组下未找到账户。", "info")
        except Exception as e:
            self.logger.error(f"加载分组账户时发生错误: {e}", exc_info=True)
            self.show_info("加载失败", f"加载分组账户时发生错误: {e}", "error")
        finally:
            self._is_loading_accounts = False
    
    def _connectSignals(self):
        """连接信号槽"""
        # 消息类型按钮
        self.TextBtn.clicked.connect(lambda: self._switchMsgType("text"))
        self.ImgBtn.clicked.connect(lambda: self._switchMsgType("image"))
        self.ImgTextBtn.clicked.connect(lambda: self._switchMsgType("image_text"))
        
        # 添加消息按钮
        self.msgAddBtn.clicked.connect(self._addOrUpdateMsg)
        
        # 全选/取消按钮
        self.SelectAllGroup.clicked.connect(self._toggleSelectAllGroups)
        self.SelectAllTask.clicked.connect(self._toggleSelectAllTasks)
        
        # 发送对象选择
        self.UserRadioButton.clicked.connect(lambda: self._switchTargetMode("user"))
        self.MonitorTask.clicked.connect(lambda: self._switchTargetMode("task"))
        self.CustomUser.clicked.connect(lambda: self._switchTargetMode("custom"))
        
        # 导入用户按钮
        self.ImportUser.clicked.connect(self._importUsers)
        
        # 保存和取消按钮
        self.SaveBtn.clicked.connect(self._saveTask)
        self.CancelBtn.clicked.connect(self.close)
        
        # 账户分组下拉框的信号连接
        self.GrouComboBox.currentIndexChanged.connect(self._on_group_index_changed)
    
    def _switchMsgType(self, msg_type):
        """切换消息类型"""
        self.current_msg_type = msg_type
        
        # 重置其他按钮状态
        if msg_type == "text":
            self.ImgBtn.setChecked(False)
            self.ImgTextBtn.setChecked(False)
            # 隐藏上传按钮
        elif msg_type == "image":
            self.TextBtn.setChecked(False)
            self.ImgTextBtn.setChecked(False)
            # 显示上传按钮
        elif msg_type == "image_text":
            self.TextBtn.setChecked(False)
            self.ImgBtn.setChecked(False)
            # 显示上传按钮
    


    
    def _createMsgItemWidget(self, text, index):
        """创建消息项组件"""
        widget = MsgItemWidget(text, index)
        widget.delete_btn.clicked.connect(lambda: self._removeMsg(index))
        widget.edit_btn.clicked.connect(lambda: self._editMsg(index))
        return widget
    
    def _editMsg(self, index):
        """编辑指定索引的消息"""
        if index < 0 or index >= len(self.msg_list):
            return
        
        # 保存当前编辑的消息索引
        self.editing_index = index
        
        # 获取消息数据
        msg_item = self.msg_list[index]
        msg_type = msg_item.get("type", "text")
        
        # 清空编辑框，避免残留内容干扰
        self.MsgTextEdit.clear()
        
        # 设置消息类型
        self._switchMsgType(msg_type)
        
        # 加载消息内容到编辑框（优先使用HTML内容以保留格式）
        if "html_content" in msg_item and "<" in msg_item["html_content"]:
            # 如果有HTML内容且包含标签，使用HTML显示
            self.MsgTextEdit.setHtml(msg_item["html_content"])
        else:
            # 否则使用纯文本显示
            self.MsgTextEdit.setPlainText(msg_item.get("content", ""))
        
        # 改变添加按钮为更新按钮
        self.msgAddBtn.setText("更新")
    
    def _removeMsg(self, index):
        """删除指定索引的消息"""
        if index < 0 or index >= len(self.msg_list):
            return
        
        # 如果正在编辑这条消息，退出编辑模式
        if self.editing_index == index:
            self.editing_index = -1
            self.msgAddBtn.setText("添加")
            self.MsgTextEdit.clear()
            
        reply = QMessageBox.question(self, "确认", "确定要删除此消息吗?", 
                                     QMessageBox.Yes | QMessageBox.No, QMessageBox.No)
        
        if reply == QMessageBox.Yes:
            # 移除列表中的项目
            self.msgList.takeItem(index)
            self.msg_list.pop(index)
            
            # 更新剩余项目的索引和显示文本
            self._updateMsgItemsAfterRemoval(index)
            
            # 如果在编辑一个已删除的消息之后的消息，更新编辑索引
            if self.editing_index > index:
                self.editing_index -= 1
    
    def _updateMsgItemsAfterRemoval(self, removed_index):
        """更新删除项目后的其他项目"""
        for i in range(removed_index, len(self.msg_list)):
            item = self.msgList.item(i)
            msg_content = self.msg_list[i]["content"]
            display_text = f"[{i+1}] {msg_content[:30]}..."
            
            # 移除旧的小部件并设置新的小部件
            self.msgList.setItemWidget(
                item,
                self._createMsgItemWidget(display_text, i)
            )
    

    
    def _toggleSelectAllGroups(self):
        """全选/取消所有账户"""
        is_checked = self.SelectAllGroup.isChecked()
        
        # 遍历所有账户项
        for i in range(self.GroupList.count()):
            item = self.GroupList.item(i)
            if is_checked:
                item.setCheckState(Qt.Checked)
            else:
                item.setCheckState(Qt.Unchecked)
                
        self.logger.info(f"{'全选' if is_checked else '取消全选'}账户")
    
    def _toggleSelectAllTasks(self):
        """全选/取消所有任务"""
        is_checked = self.SelectAllTask.isChecked()
        
        for i in range(self.ListWidget_3.count()):
            item = self.ListWidget_3.item(i)
            if is_checked:
                item.setCheckState(Qt.Checked)
            else:
                item.setCheckState(Qt.Unchecked)
    
    def _switchTargetMode(self, mode):
        """切换发送目标模式"""
        if mode == "user":
            self.frame.setVisible(False)
            self.frame_2.setVisible(False)
        elif mode == "task":
            self.frame.setVisible(True)
            self.frame_2.setVisible(False)
            # 使用QTimer.singleShot确保异步方法在事件循环中被调用
            QTimer.singleShot(0, lambda: self._loadTaskData())
        elif mode == "custom":
            self.frame.setVisible(False)
            self.frame_2.setVisible(True)
    
    @asyncSlot()
    async def _loadTaskData(self):
        """异步加载监听任务数据"""
        self.logger.info("加载监听任务数据")
        
        # 如果正在加载，则直接返回
        if self._is_loading_tasks:
            return
            
        self._is_loading_tasks = True
        try:
            # 清空当前任务列表
            self.ListWidget_3.clear()
            
            # 从控制器获取监听任务列表
            if self._msg_controller:
                tasks = await self._msg_controller.get_monitor_tasks()
                
                if tasks and isinstance(tasks, list) and len(tasks) > 0:
                    # 添加任务到列表中
                    for task in tasks:
                        # 获取任务名称和ID，并拼接用户数
                        task_name = task.get('name', '未命名任务')
                        user_count = task.get('user_count', 0)
                        task_id = task.get('id', '')
                        display_name = f"{task_name}（{user_count}人）"
                        # 创建列表项
                        item = QListWidgetItem(display_name)
                        item.setFlags(item.flags() | Qt.ItemIsUserCheckable)
                        item.setCheckState(Qt.Unchecked)
                        item.setData(Qt.UserRole, task)  # 存储整个任务数据
                        self.ListWidget_3.addItem(item)
                    
                    self.logger.info(f"已加载 {len(tasks)} 个监听任务")
                else:
                    self.show_info("提示", "暂无可用的监听任务", "info")
 
        except Exception as e:
            self.logger.error(f"加载监听任务时发生错误: {e}", exc_info=True)
            self.show_info("加载失败", f"加载监听任务数据时发生错误: {e}", "error")
        finally:
            self._is_loading_tasks = False
    
    def _importUsers(self):
        """导入用户列表"""
        file_path, _ = QFileDialog.getOpenFileName(self, "选择文件", "", "文本文件 (*.txt);;所有文件 (*)")
        
        if file_path:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                self.CustomUserEdit.setPlainText(content)
            except Exception as e:
                self.show_info("错误", f"导入失败: {str(e)}", "error")
    
    def _validateInputs(self):
        """验证输入是否有效"""
        # 检查任务名
        if not self.taskName.text().strip():
            self.show_info("警告", "请输入任务名称", "warning")
            return False
        
        # 检查消息列表
        if not self.msg_list:
            self.show_info("警告", "请至少添加一条消息", "warning")
            return False
        
        # 检查发送间隔
        try:
            min_interval = int(self.Str.text())
            max_interval = int(self.LineEdit_3.text())
            account_min_interval = int(self.LineEdit_4.text())
            account_max_interval = int(self.LineEdit_5.text())
            
            if min_interval <= 0 or max_interval <= 0 or account_min_interval <= 0 or account_max_interval <= 0:
                raise ValueError("间隔时间必须大于0")
            
            if min_interval > max_interval or account_min_interval > account_max_interval:
                raise ValueError("最大间隔必须大于或等于最小间隔")
        except ValueError as e:
            self.show_info("警告", f"时间间隔设置无效: {str(e)}", "warning")
            return False
        
        # 检查账户选择
        selected_accounts = []
        for i in range(self.GroupList.count()):
            item = self.GroupList.item(i)
            if item.checkState() == Qt.Checked:
                account_data = item.data(Qt.UserRole)
                if account_data:
                    selected_accounts.append(account_data)
        
        if not selected_accounts:
            self.show_info("警告", "请至少选择一个账户", "warning")
            return False
        
        # 检查发送目标
        if self.MonitorTask.isChecked():
            selected_tasks = []
            for i in range(self.ListWidget_3.count()):
                item = self.ListWidget_3.item(i)
                if item.checkState() == Qt.Checked:
                    selected_tasks.append(item.text())
            
            if not selected_tasks:
                self.show_info("警告", "请至少选择一个监听任务", "warning")
                return False
        
        if self.CustomUser.isChecked():
            if not self.CustomUserEdit.toPlainText().strip():
                self.show_info("警告", "请输入自定义用户", "warning")
                return False
        
        return True
    @asyncSlot()
    async def _saveTask(self):
        """保存任务（新增或编辑）"""
        if not self._validateInputs():
            return
        
        try:
            # 构造任务数据
            task_data = {}
            # 基本信息
            task_data["task_name"] = self.taskName.text().strip()
            
            # 收集消息内容
            task_data["messages"] = self.msg_list.copy()
            
            # 发送间隔设置
            task_data["message_interval_min"] = int(self.Str.text())
            task_data["message_interval_max"] = int(self.LineEdit_3.text())
            task_data["account_switch_min"] = int(self.LineEdit_4.text())
            task_data["account_switch_max"] = int(self.LineEdit_5.text())
            
            # 发送策略
            task_data["target_type"] = "custom"  # 默认策略，修改为正确的值
            
            # 计划发送
            task_data["schedule_type"] = "immediate"  # 默认立即发送
            
            # 收集选中账户
            selected_accounts = []
            for i in range(self.GroupList.count()):
                item = self.GroupList.item(i)
                if item.checkState() == Qt.Checked:
                    account_data = item.data(Qt.UserRole)
                    if account_data:
                        selected_accounts.append(account_data)
            task_data["accounts"] = selected_accounts
            
            # 发送目标类型和目标设置
            if self.MonitorTask.isChecked():
                # 监听任务模式
                task_data["target_type"] = "task"
                task_targets = []
                for i in range(self.ListWidget_3.count()):
                    item = self.ListWidget_3.item(i)
                    if item.checkState() == Qt.Checked:
                        task_info = item.data(Qt.UserRole)
                        if task_info and 'id' in task_info:
                            task_targets.append(task_info['id'])
                task_data["targets"] = task_targets
            elif self.CustomUser.isChecked():
                # 自定义用户模式
                task_data["target_type"] = "custom"
                # 处理自定义用户列表，支持换行符或逗号分隔
                custom_text = self.CustomUserEdit.toPlainText().strip()
                if ',' in custom_text:
                    targets = [t.strip() for t in custom_text.split(',') if t.strip()]
                else:
                    targets = [t.strip() for t in custom_text.split('\n') if t.strip()]
                task_data["targets"] = targets
            
            # 辅助选项
            task_data["random_emoji"] = self.RandEMOJ.isChecked()
            
            print(task_data)

            try:
                updated_task = await self._msg_controller.create_task(task_data)
                if updated_task:
                    self.show_info("成功", "任务添加成功", "success")
                    self.close()
                else:
                    self.show_info("错误", "任务添加失败", "error")
            except Exception as e:
                self.logger.error(f"更新任务失败: {str(e)}", exc_info=True)
                self.show_info("错误", f"更新任务失败: {str(e)}", "error")
            
        except Exception as e:
            self.logger.error(f"保存任务时发生错误: {str(e)}", exc_info=True)
            self.show_info("错误", f"保存任务失败: {str(e)}", "error")
    
    def _addOrUpdateMsg(self):
        """添加或更新消息"""
        if not self.MsgTextEdit.get_html_text().strip():
            self.show_info("警告", "消息内容不能为空", "warning")
            return
        
        try:
            # 获取消息内容
            plain_text = self.MsgTextEdit.get_html_text()
            
            # 创建消息数据
            msg_data = {
                "type": self.current_msg_type,
                "content": plain_text,
            }
            
            if self.editing_index >= 0 and self.editing_index < len(self.msg_list):
                # 更新现有消息
                self.msg_list[self.editing_index] = msg_data
                
                # 更新列表项显示
                item = self.msgList.item(self.editing_index)
                truncated_text = plain_text[:30] + "..." if len(plain_text) > 30 else plain_text
                display_text = f"[{self.editing_index + 1}] {truncated_text}"
                
                # 更新对应的小部件
                self.msgList.setItemWidget(
                    item,
                    self._createMsgItemWidget(display_text, self.editing_index)
                )
                
                # 重置编辑状态
                self.editing_index = -1
                self.msgAddBtn.setText("添加")
            else:
                # 添加新消息
                self.msg_list.append(msg_data)
                
                # 创建新的列表项
                index = len(self.msg_list) - 1
                truncated_text = plain_text[:30] + "..." if len(plain_text) > 30 else plain_text
                display_text = f"[{index + 1}] {truncated_text}"
                
                item = QListWidgetItem()
                self.msgList.addItem(item)
                self.msgList.setItemWidget(
                    item,
                    self._createMsgItemWidget(display_text, index)
                )
            
            # 清空编辑框
            self.MsgTextEdit.clear()
            
        except Exception as e:
            self.logger.error(f"添加/更新消息时发生错误: {str(e)}", exc_info=True)
            self.show_info("错误", f"添加/更新消息失败: {str(e)}", "error")

    def show_info(self, title, content, type='info', position = InfoBarPosition.TOP):
        """显示消息提示"""
        
        
        if type == 'success':
            InfoBar.success(
                title=title,
                content=content,
                orient=Qt.Horizontal,
                isClosable=True,
                position=position,
                duration=2000,
                parent=self
            )
        elif type == 'error':
            InfoBar.error(
                title=title,
                content=content,
                orient=Qt.Horizontal,
                isClosable=True,
                position=position,
                duration=3000,
                parent=self
            )
        elif type == 'warning':
            InfoBar.warning(
                title=title,
                content=content,
                orient=Qt.Horizontal,
                isClosable=True,
                position=position,
                duration=3000,
                parent=self
            )
        else:  # 默认为 info
            InfoBar.info(
                title=title,
                content=content,
                orient=Qt.Horizontal,
                isClosable=True,
                position=position,
                duration=2000,
                parent=self
            ) 


    def _on_group_index_changed(self):
        """当账户分组选择发生变化时的处理"""
        # 如果正在加载，则忽略
        if self._is_loading_groups:
            return
            
        # 获取当前选中的分组ID
        group_id = self.GrouComboBox.currentData()
        if group_id is not None:
            # 使用QTimer.singleShot确保异步方法在事件循环中被调用
            QTimer.singleShot(0, lambda: self._load_accounts_by_group(group_id))
            self.logger.info(f"切换到分组ID: {group_id}")
            return
            
        self.show_info("提醒", "该分组没有用户", "warning") 

    def showEvent(self, event):
        """窗口显示事件，用于加载初始数据"""
        super().showEvent(event)
        # 使用QTimer.singleShot确保异步方法在事件循环中被调用
        QTimer.singleShot(0, lambda: self._load_initial_data())
    
    def _on_task_saved(self, task_data):
        """任务保存成功后的回调"""
        self.logger.info(f"任务保存成功: {task_data.get('id')}")
        self.show_info("成功", "任务保存成功", "success")
        
        # 任务保存成功后，发送signal并关闭窗口
        self.saveSignal.emit(task_data)
        self.close()
