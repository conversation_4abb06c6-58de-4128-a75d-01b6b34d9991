from PySide6.QtWidgets import QWidget
from PySide6.QtGui import QIcon
from PySide6.QtCore import QTimer, Signal, Qt,QSize
from qfluentwidgets import MessageBox, InfoBar, InfoBarPosition
from PySide6.QtWidgets import QVBoxLayout,QApplication
from PySide6.QtGui import QDesktopServices,QColor
from qfluentwidgets import SplashScreen
from PySide6.QtCore import QUrl
from qasync import asyncSlot
from ui.widgets.auth_widget import AuthWidget
from app.controllers.auth_controller import AuthController
from utils.logger import get_logger
from core.auth.models import software_config
from utils.update_checker import update_checker
import datetime, re
from utils.ntp_time import get_network_time
from frameConf.config import cfg
from frameConf.setting import SERVER_URL, APP_ID, APP_KEY, VER_INDEX, VERSION
import sys

class AuthView(QWidget):
    # 添加信号以通知外部系统登录成功
    auth_completed = Signal(bool)  # True表示认证成功，False表示认证失败或取消
    
    def __init__(self,auth_controller: AuthController):
        super().__init__()
        self.setWindowTitle("营销助手")
        self.setMinimumSize(1000, 600)
        self.setWindowIcon(QIcon(":/icons/logo.png"))
        self._logger = get_logger("ui.views.auth_view")

  
        # 创建UI组件
        self.auth_widget = AuthWidget()
        # QWidget没有setCentralWidget，直接用布局
        #关闭验证码注册
        self.auth_widget.verification_code.hide()
        self.auth_widget.send_code_button.hide()
        # 添加登录状态标记，防止重复登录
        self._is_logging_in = False

        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.addWidget(self.auth_widget)
        self.setLayout(layout)
        # 创建控制器
     
        self.controller = auth_controller

        # 连接更新检查器信号
        update_checker.update_available.connect(self.on_update_available)

        # 连接UI信号到视图方法
        self.auth_widget.switch_to_register.connect(self.on_switch_to_register)
        self.auth_widget.switch_to_login.connect(self.on_switch_to_login)
        self.auth_widget.send_verification_code.connect(self.on_send_verification_code)
        self.auth_widget.register_requested.connect(self.on_register)
        self.auth_widget.login_requested.connect(self.on_login)
        self.auth_widget.forgot_password_requested.connect(self.on_forgot_password)
        # 延迟执行初始化，让UI先渲染
        QTimer.singleShot(100, self.on_init_software)
        

    async def auto_login(self,  email=None, password=None, remember_password=False, auto_login=False):
        # 加载本地保存的账号密码
        #self.show_info("正在自动登录...", "提示", "info",  duration=1000)
        # 自动登录
        if auto_login and email and password:
            await self._login(email, password, remember_password, auto_login)
        else:
            self.show_info("自动登录失败，请手动登录", "提示", "error")


    @asyncSlot()
    async def on_init_software(self):
        
        """初始化软件"""
        email, password, remember_password, auto_login = self.controller.load_credentials()
        self.auth_widget.login_email.setText(email)
        self.auth_widget.login_password.setText(password if remember_password else "")
        self.auth_widget.remember_password.setChecked(remember_password)
        self.auth_widget.auto_login.setChecked(auto_login)
        

        await self.controller.initialize_software(
            domain=SERVER_URL,
            app_id=APP_ID,
            key=APP_KEY,
            ver_index=VER_INDEX,
            version=VERSION
        )
        
        # 检查是否有新版本
        has_new_version = update_checker.check_for_updates()
        if has_new_version:
            return
        # 仍然保留原有的版本检查逻辑作为备份，但注释掉
        #if software_config.new_url:
        #     title = "发现新版本"
        #     content = f"检测到新版本，请前往下载：{software_config.new_url}\n\n{software_config.new_content}"
        #     w = MessageBox(title, content, self)
        #     w.yesButton.setText('立即更新')
        #     w.cancelButton.setText('暂不更新')
        #     # close the message box when mask is clicked
        #     w.setClosableOnMaskClicked(True)
    
        #     if w.exec():
        #         QDesktopServices.openUrl(QUrl(software_config.new_url))
        #         self.close()
        #     else:
        #         # 暂时不更新
        #         pass
                
        #自动登录
        await self.auto_login(email, password, remember_password, auto_login)
    
    def on_update_available(self, new_version, download_url, update_detail):
        """当有新版本可用时调用"""
        title = "发现新版本"
        content = f"检测到新版本 {new_version}，请前往下载\n\n{update_detail}"
        w = MessageBox(title, content, self)
        w.yesButton.setText('立即更新')
        w.cancelButton.setText('暂不更新')
        w.setClosableOnMaskClicked(True)
        
        if w.exec():
            # 如果用户选择更新，则执行更新操作
            if update_checker.perform_update():
                # 如果是强制更新，则关闭程序
                if update_checker.update_config.is_force_update():
                    self.close()
                    sys.exit(0)
        else:
            # 如果是强制更新但用户选择不更新，仍然关闭程序
            if update_checker.update_config.is_force_update():
                self.show_info("此版本已过期，必须更新才能继续使用", "强制更新", "warning", 6000)
                self.close()
                sys.exit(0)


    def on_credentials_loaded(self, email, password, remember_password, auto_login):
        """接收控制器加载的凭据并更新UI"""
        self.auth_widget.login_email.setText(email)
        self.auth_widget.login_password.setText(password)
        self.auth_widget.remember_password.setChecked(remember_password)
        self.auth_widget.auto_login.setChecked(auto_login)
    
    def on_auto_login(self, email, password):
        """执行自动登录"""
        self._logger.info(f"执行自动登录: email={email}, password={password}")
    
    def on_verification_code_sent(self, email):
        """验证码发送成功后的处理"""
        self.show_info(f"验证码已发送至: {email}\n(测试验证码: 123456)")
    
    def on_register_succeeded(self):
        """注册成功后的处理"""
        self.show_info("注册成功！", "成功", "success")
        user_name = self.auth_widget.user_name.text().strip()
        # 注册成功后切换到登录页面
        self.auth_widget.switch_to_login_page()
        # 自动填充邮箱
        self.auth_widget.login_email.setText(user_name)
    
    async def on_login_succeeded(self):
        """登录成功后的处理"""
        # 检查VIP状态
        result = await self.controller.verify_vip()
        #print(result)
        if  result.get('success'):
            data = result.get('data', {})
            is_vip = data.get('is_vip', True)  # 默认兼容老接口
            vip_expire = data.get('vip_expire', '')
            if not is_vip or (vip_expire and self.is_expired(vip_expire)):
                # 弹窗提醒续费
                url = "https://server.xile188.com"  # 可用software_config.new_url
                self.show_info(f"VIP已到期，请及时续费！\n充值地址：{url}", "VIP到期", "warning", duration=10000)
                return
        else:
            title = "登录错误"
            content = f"用户到期：\n\n{result.get('message')}，请联系代理续费!"
            w = MessageBox(title, content, self)
            w.yesButton.setText('立即更新')
            w.cancelButton.setText('暂不更新')
            # close the message box when mask is clicked
            w.setClosableOnMaskClicked(True)
            
            if w.exec():
                QDesktopServices.openUrl(QUrl(software_config.new_url))
                self.close()
            else:
                # 暂时不更新
                pass  

    def is_expired(self, expire_str):
        # 获取网络时间、本地时间、vip时间，三者进行对比，网络时间优先
        if not expire_str or expire_str == "-":
            return True
        match = re.match(r"(\d{4}-\d{2}-\d{2})(?:[ T](\d{2}:\d{2}:\d{2}))?", expire_str)
        if not match:
            return False
        date_part = match.group(1)
        time_part = match.group(2) or "23:59:59"
        try:
            expire_dt = datetime.datetime.strptime(f"{date_part} {time_part}", "%Y-%m-%d %H:%M:%S")
            # 优先用网络时间
            net_dt = get_network_time()
            now = net_dt if net_dt else datetime.datetime.now(datetime.timezone.utc)
            # 兼容本地时间无时区
            if expire_dt.tzinfo is None:
                expire_dt = expire_dt.replace(tzinfo=datetime.timezone.utc)
            return (expire_dt - now).total_seconds() < 0
        except Exception:
            return False
    
    def on_password_reset_sent(self, email):
        """密码重置链接发送后的处理"""
        self.show_info(f"密码重置链接已发送至: {email}")
    
    def on_switch_to_register(self):
        """当用户切换到注册页面时执行"""
        pass  # 可以添加其他UI逻辑
    
    def on_switch_to_login(self):
        """当用户切换到登录页面时执行"""
        pass  # 可以添加其他UI逻辑
    
    def on_send_verification_code(self, email):
        """处理发送验证码逻辑"""
        if not self.validate_email(email):
            self.show_info("邮箱格式不正确，请输入有效的邮箱地址", "错误", "error")
            # 停止倒计时，恢复按钮状态
            self.auth_widget.countdown_timer.stop()
            self.auth_widget.send_code_button.setText("发送验证码")
            self.auth_widget.send_code_button.setEnabled(True)
            return
            
        # 调用控制器处理发送验证码
    
    def on_register(self, account, password, code, invid, udid):
        """处理注册前的验证"""
        if not account:
            self.show_info("账号不能为空", "错误", "error")
            return False
        if len(password) < 6:
            self.show_info("密码长度不能少于6位", "错误", "error")
            return False
        
        # if not code:
        #     self.show_info("请输入验证码", "错误", "error")
        #     return False
        # invid 可选
        # udid 由controller自动补全
        return  self.do_register(account, password, code, invid, udid)
        
    @asyncSlot()
    async def do_register(self, account, password, code, invid, udid):
        result = await self.controller.register(account, password, code, invid, udid)
        if result['success'] == False:
            self.show_info(f"注册失败:{result['message']}", "错误", "error")
            return False
        else:
            self.show_info(f"{account}注册成功，请登录", "成功", "success")
            # 注册成功后切换到登录页面并自动填充账号
            self.auth_widget.switch_to_login_page()
            self.auth_widget.login_email.setText(account)
            return True
            
    @asyncSlot(str, str, bool, bool)
    async def on_login(self, account, password, remember_password=None, auto_login=None):
            return await self._login(account, password, remember_password, auto_login)
    
    async def _login(self, account, password, remember_password=None, auto_login=None):
        # 如果已经在登录过程中，直接返回，防止重复登录
        if self._is_logging_in:
            return False
            
        if not account:
            self.show_info("账号不能为空", "错误", "error")
            return False
        if not password:
            self.show_info("请输入密码", "错误", "error")
            return False
            
        try:
            # 设置登录状态为正在登录
            self._is_logging_in = True
            self.show_info("正在登录...", "提示", "info")
            result = await self.controller.login(account, password)
            if result.get("success"):
                # 登录成功后保存账号密码
                if remember_password:
                    self.controller.save_credentials(account, password, remember_password, auto_login)
                else:
                    self.controller.save_credentials(account, "", False, auto_login)
                #await self.on_login_succeeded()
                # 发出登录成功信号通知外部系统
                self.auth_completed.emit(True)
                # 隐藏登录窗口
                self.hide()
            else:
                self.show_info(f"登录失败：{result.get('message')}", "错误", "error")
        finally:
            # 无论登录成功还是失败，都重置登录状态
            self._is_logging_in = False
    
    def on_forgot_password(self):
        """处理忘记密码逻辑"""
        self.show_info("忘记密码功能暂未开放", "提示", "warning")
        return
        email = self.auth_widget.login_email.text().strip()
        if email and self.validate_email(email):
            self.controller.forgot_password(email)
        else:
            self.show_info("请先输入您的邮箱地址，然后再点击忘记密码", "提示", "warning")
    
    def validate_email(self, email):
        """简单的邮箱格式验证"""
        return "@" in email and "." in email

    def show_info(self, msg, title="提醒", type="info",duration=3000):
        if type == "info":
            InfoBar.info(
            title=title,
            content=msg,
            orient=Qt.Horizontal,
            isClosable=True,
            position=InfoBarPosition.TOP,
            duration=duration,
            parent=self)
        elif type=="success":
            InfoBar.success(
            title=title,
            content=msg,
            orient=Qt.Horizontal,
            isClosable=True,
            position=InfoBarPosition.TOP,
            duration=duration,
            parent=self
        )
        elif type=="warning":
            InfoBar.warning(
            title=title,
            content=msg,
            orient=Qt.Horizontal,
            isClosable=True,
            position=InfoBarPosition.TOP,
            duration=duration,
            parent=self
        )
        elif type=="error":
            InfoBar.error(
            title=title,
            content=msg,
            orient=Qt.Horizontal,
            isClosable=True,
            position=InfoBarPosition.TOP,
            duration=duration,
            parent=self
        )
        else:
            InfoBar.info(
            title=title,
            content=msg,
            orient=Qt.Horizontal,
            isClosable=True,
            position=InfoBarPosition.TOP,
            duration=duration,
            parent=self
        )



    def closeEvent(self, event):
        """重写关闭事件，在用户点击关闭按钮时处理"""
        # 如果是用户关闭窗口（而不是程序控制的hide()），则发出认证失败信号
        if self.isVisible():
            self.auth_completed.emit(False)
        # 调用父类方法，继续关闭操作
        super().closeEvent(event) 
