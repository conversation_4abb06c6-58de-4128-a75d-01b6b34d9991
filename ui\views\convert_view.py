#!/usr/bin/env python
# -*- coding: utf-8 -*-

import sys
import asyncio
import time
from pathlib import Path
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime
from PySide6.QtWidgets import (QWidget, QFileDialog, QTableWidgetItem, QMenu,
                               QApplication, QMessageBox, QHeaderView, QInputDialog)
from PySide6.QtCore import Qt, QThread, Signal, QMimeData, QEvent
from PySide6.QtGui import QDragEnterEvent, QDropEvent

from ui.designer.ui_convert import Ui_Form
from core.converter.converter import TelegramConverter
from utils.file_utils import FileUtils
from utils.logger import get_logger
from app.controllers.convert_controller import ConvertController
from app.controllers.account_controller import AccountController
from qfluentwidgets import RadioButton, CheckBox, ComboBox, PushButton, SearchLineEdit
from PySide6.QtWidgets import QListWidget, QListWidgetI<PERSON>, QGroupBox, Q<PERSON>oxLayout, QHBoxLayout
from qasync import asyncSlot
import asyncio


class WorkerThread(QThread):
    """后台工作线程，用于执行耗时操作"""
    
    # 定义信号
    update_progress = Signal(dict)
    conversion_done = Signal(list)
    log_message = Signal(str)
    
    def __init__(self, files: List[str], output_dir: str, convert_type: str,
                 proxy_config: Optional[Dict[str, Any]] = None, password_info: Optional[Dict[str, Any]] = None):
        super().__init__()
        self.files = files
        self.output_dir = output_dir
        self.convert_type = convert_type
        self.proxy_config = proxy_config or {"type": "none"}
        self.password_info = password_info or {}
        self.logger = get_logger(__name__)
    
    def run(self):
        """线程执行入口"""
        try:
            # 创建转换器
            converter = TelegramConverter()
            # 设置代理配置
            converter.set_proxy_config(self.proxy_config)
            
            # 使用事件循环执行异步操作
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            # 执行批量转换
            results = loop.run_until_complete(
                converter.batch_convert(self.files, self.output_dir, self.convert_type, self.password_info)
            )
            
            # 发送结果信号
            self.conversion_done.emit(results)
        
        except Exception as e:
            error_msg = f"转换过程发生错误: {str(e)}"
            self.logger.error(error_msg)
            self.log_message.emit(error_msg)


class ConvertWindow(QWidget):
    """Telegram会话格式转换窗口"""
    
    def __init__(self, parent = None, account_controller: AccountController = None):
        super().__init__(parent)
        
        # 设置UI
        self.ui = Ui_Form()
        self.ui.setupUi(self)
        self.setWindowTitle("Telegram Tdata和Session批量互转工具")
        
        # 控制器
        self.account_controller = account_controller
        self.convert_controller = ConvertController()
        if account_controller:
            self.convert_controller.set_account_controller(account_controller)
        
        self.logger = get_logger(__name__)
        # 存储文件列表
        self.file_list = []
        
        # 两步验证密码文件名
        self.password_file_name = "Password2FA.txt"
        
        # 代理设置
        self.proxy_settings = None
        
        # 初始化UI
        self.init_ui()
        self.setup_proxy_ui()
        self.connect_signals()
        
        # 配置文件拖放
        self.setAcceptDrops(True)
    
    def init_ui(self):
        """初始化UI组件"""
        # 设置转换方向选项
        self.ui.ComboBox.addItems(["Tdata转Session", "Session转Tdata"])
        
        # 设置密码类型选项
        self.ui.ComboBox_2.addItems(["无密码", "文件识别密码", "统一密码"])
        
        # 初始化密码提示（默认为无密码）
        self.ui.LineEdit.setPlaceholderText("无需密码")
        self.ui.LineEdit.setEnabled(False)  # 默认禁用密码输入框
        
        # 密码类型变更时更新提示
        self.ui.ComboBox_2.currentIndexChanged.connect(self.update_password_placeholder)
        
        # 监听密码输入框的变化
        self.ui.LineEdit.textChanged.connect(self.on_password_input_changed)
        
        # 设置表格属性
        self.ui.TableWidget.horizontalHeader().setSectionResizeMode(QHeaderView.ResizeMode.Stretch)
        self.ui.TableWidget.setContextMenuPolicy(Qt.ContextMenuPolicy.CustomContextMenu)
        self.ui.TableWidget.customContextMenuRequested.connect(self.show_context_menu)
        
        # 允许编辑密码列（第2列）
        self.ui.TableWidget.itemChanged.connect(self.on_table_item_changed)
        
        # 绑定按钮事件
        self.ui.PushButton.clicked.connect(self.import_files)
        self.ui.PushButton_2.clicked.connect(self.select_output_path)
        self.ui.PushButton_3.clicked.connect(self.clear_table)
        self.ui.PrimaryPushButton.clicked.connect(self.start_conversion)
        
        # 隐藏导出格式选项（转换方向已经确定了输出格式）
        self.ui.label_6.hide()  # 导出格式标签
        self.ui.CheckBox.hide()  # Telethon协议复选框
        self.ui.CheckBox_2.hide()  # Tdata直登号复选框
        
        # 设置默认导出路径为当前目录下的"导出"文件夹
        import os
        default_output_dir = os.path.join(os.getcwd(), "导出")
        self.ui.LineEdit_3.setText(default_output_dir)
        self.ui.LineEdit_3.setPlaceholderText("选择或输入导出路径")
        
        # 隐藏原来的代理设置（现在集成到自定义代理区域中）
        self.ui.label_4.hide()  # 代理ip标签
        self.ui.LineEdit_4.hide()  # IP地址输入框
        self.ui.LineEdit_5.hide()  # 端口输入框
        self.ui.LineEdit_6.hide()  # 用户名输入框
        self.ui.LineEdit_7.hide()  # 密码输入框
    
    def update_password_placeholder(self, index: int):
        """根据密码类型更新输入框提示"""
        if index == 0:  # 无密码
            self.ui.LineEdit.setPlaceholderText("无需密码")
            self.ui.LineEdit.setText("")
            self.ui.LineEdit.setEnabled(False)  # 禁用输入框
            # 清空表格中的密码列
            self.clear_table_passwords()
        elif index == 1:  # 文件识别密码
            self.ui.LineEdit.setPlaceholderText("密码文件名(默认Password2FA.txt)")
            self.ui.LineEdit.setText(self.password_file_name)
            self.ui.LineEdit.setEnabled(True)  # 启用输入框
            # 自动检测并填充文件密码到表格
            self.auto_fill_file_passwords()
        else:  # 统一密码
            self.ui.LineEdit.setPlaceholderText("请输入统一密码")
            self.ui.LineEdit.setText("")
            self.ui.LineEdit.setEnabled(True)  # 启用输入框
            # 清空表格中的密码列，等待用户输入统一密码
            self.clear_table_passwords()
    
    def on_password_input_changed(self, text: str):
        """密码输入框内容变化时的处理"""
        password_type_index = self.ui.ComboBox_2.currentIndex()
        
        if password_type_index == 1:  # 文件识别密码模式
            # 密码文件名变化时，重新检测并填充密码
            if text.strip():
                self.auto_fill_file_passwords()
        elif password_type_index == 2:  # 统一密码模式
            # 统一密码变化时，自动填充到表格
            if text.strip():
                self.auto_fill_unified_password()
            else:
                self.clear_table_passwords()
    
    def on_table_item_changed(self, item: QTableWidgetItem):
        """表格项内容变化时的处理"""
        # 只处理密码列（第2列）的变化
        if item.column() == 2:
            row = item.row()
            new_password = item.text().strip()
            
            if row < len(self.file_list):
                file_path = self.file_list[row]
                file_name = Path(file_path).name
                
                if new_password:
                    self.log_message(f"✓ 已为 {file_name} 设置密码")
                else:
                    self.log_message(f"⚠ 已清空 {file_name} 的密码")
    
    def setup_proxy_ui(self):
        """设置代理相关UI组件"""
        # 创建代理设置组
        proxy_group = QGroupBox("代理设置")
        proxy_layout = QVBoxLayout(proxy_group)
        
        # 代理选择单选按钮
        proxy_radio_layout = QHBoxLayout()
        self.no_proxy_radio = RadioButton('禁用代理')
        self.system_proxy_radio = RadioButton('系统代理')
        self.proxy_ip_radio = RadioButton('代理IP池')
        self.custom_proxy_radio = RadioButton('自定义代理')
        
        # 默认选中禁用代理
        self.no_proxy_radio.setChecked(True)
        
        proxy_radio_layout.addWidget(self.no_proxy_radio)
        proxy_radio_layout.addWidget(self.system_proxy_radio)
        proxy_radio_layout.addWidget(self.proxy_ip_radio)
        proxy_radio_layout.addWidget(self.custom_proxy_radio)
        proxy_radio_layout.addStretch()
        
        proxy_layout.addLayout(proxy_radio_layout)
        
        # 代理IP池选择区域
        self.proxy_ip_widget = QWidget()
        proxy_ip_layout = QVBoxLayout(self.proxy_ip_widget)
        
        # 搜索框
        self.ip_search = SearchLineEdit()
        self.ip_search.setPlaceholderText('搜索IP地址')
        proxy_ip_layout.addWidget(self.ip_search)
        
        # IP列表
        self.ip_list = QListWidget()
        self.ip_list.setMinimumHeight(150)
        proxy_ip_layout.addWidget(self.ip_list)
        
        # 刷新IP池按钮
        self.refresh_ip_btn = PushButton('刷新IP列表')
        proxy_ip_layout.addWidget(self.refresh_ip_btn)
        
        self.proxy_ip_widget.hide()  # 默认隐藏
        proxy_layout.addWidget(self.proxy_ip_widget)
        
        # 自定义代理设置区域
        self.custom_proxy_widget = QWidget()
        custom_proxy_layout = QVBoxLayout(self.custom_proxy_widget)
        
        # 代理设置输入框
        custom_proxy_inputs_layout = QHBoxLayout()
        
        # 创建标签和输入框
        from qfluentwidgets import BodyLabel, LineEdit
        ip_label = BodyLabel("IP地址:")
        custom_proxy_inputs_layout.addWidget(ip_label)
        
        # 创建新的输入框组件
        self.custom_ip_edit = LineEdit()
        self.custom_ip_edit.setPlaceholderText("127.0.0.1")
        custom_proxy_inputs_layout.addWidget(self.custom_ip_edit)
        
        port_label = BodyLabel("端口:")
        custom_proxy_inputs_layout.addWidget(port_label)
        self.custom_port_edit = LineEdit()
        self.custom_port_edit.setPlaceholderText("1080")
        self.custom_port_edit.setFixedWidth(80)
        custom_proxy_inputs_layout.addWidget(self.custom_port_edit)
        
        username_label = BodyLabel("用户名:")
        custom_proxy_inputs_layout.addWidget(username_label)
        self.custom_username_edit = LineEdit()
        self.custom_username_edit.setPlaceholderText("可选")
        custom_proxy_inputs_layout.addWidget(self.custom_username_edit)
        
        password_label = BodyLabel("密码:")
        custom_proxy_inputs_layout.addWidget(password_label)
        self.custom_password_edit = LineEdit()
        self.custom_password_edit.setPlaceholderText("可选")
        self.custom_password_edit.setEchoMode(LineEdit.Password)
        custom_proxy_inputs_layout.addWidget(self.custom_password_edit)
        
        custom_proxy_layout.addLayout(custom_proxy_inputs_layout)
        
        self.custom_proxy_widget.hide()  # 默认隐藏
        proxy_layout.addWidget(self.custom_proxy_widget)
        
        # 导入到账户管理选项（仅在Tdata转Session时显示）
        self.import_to_account_checkbox = CheckBox('转换完成后导入到账户管理')
        self.import_to_account_checkbox.hide()  # 默认隐藏
        proxy_layout.addWidget(self.import_to_account_checkbox)
        
        # 分组选择（仅在导入到账户管理时显示）
        self.group_combo = ComboBox()
        self.group_combo.hide()  # 默认隐藏
        proxy_layout.addWidget(self.group_combo)
        
        # 将代理设置组添加到主界面
        # 找到合适的位置插入代理设置
        main_layout = self.ui.CardWidget.layout()
        if main_layout:
            # 在密码设置后插入代理设置
            main_layout.insertWidget(1, proxy_group)
    
    def connect_signals(self):
        """连接信号和槽"""
        # 代理相关信号
        self.no_proxy_radio.toggled.connect(self.on_proxy_selection_changed)
        self.system_proxy_radio.toggled.connect(self.on_proxy_selection_changed)
        self.proxy_ip_radio.toggled.connect(self.on_proxy_selection_changed)
        self.custom_proxy_radio.toggled.connect(self.on_proxy_selection_changed)
        self.refresh_ip_btn.clicked.connect(self.load_proxy_ips)
        self.ip_search.textChanged.connect(self.on_ip_search_changed)
        
        # 转换类型变更信号
        self.ui.ComboBox.currentTextChanged.connect(self.on_convert_type_changed)
        
        # 导入到账户管理选项变更
        self.import_to_account_checkbox.toggled.connect(self.on_import_to_account_toggled)
        
        # 控制器信号
        if self.convert_controller:
            self.convert_controller.conversion_started.connect(self.on_conversion_started)
            self.convert_controller.conversion_progress.connect(self.on_conversion_progress)
            self.convert_controller.conversion_completed.connect(self.on_conversion_completed)
            self.convert_controller.conversion_failed.connect(self.on_conversion_failed)
            self.convert_controller.proxy_ips_loaded.connect(self.update_ip_list)
            self.convert_controller.accounts_imported.connect(self.on_accounts_imported)
    
    def dragEnterEvent(self, event: QDragEnterEvent):
        """处理拖拽进入事件"""
        if event.mimeData().hasUrls():
            event.acceptProposedAction()
    
    def dropEvent(self, event: QDropEvent):
        """处理拖拽释放事件"""
        files = []
        for url in event.mimeData().urls():
            file_path = url.toLocalFile()
            files.append(file_path)
        
        # 每次拖拽导入文件时清空原来的表格
        if files:
            self.clear_table()
            self.log_message("清空原有文件列表，开始导入拖拽文件")
        
        self.process_imported_files(files)
    
    def on_proxy_selection_changed(self, checked):
        """代理选择变更处理"""
        if checked:
            # 隐藏所有代理设置区域
            self.proxy_ip_widget.hide()
            self.custom_proxy_widget.hide()
            
            # 根据选择显示对应区域
            if self.proxy_ip_radio.isChecked():
                self.proxy_ip_widget.show()
                # 加载代理IP列表（使用QTimer.singleShot避免asyncio.create_task问题）
                from PySide6.QtCore import QTimer
                QTimer.singleShot(0, lambda: asyncio.ensure_future(self.load_proxy_ips()))
            elif self.custom_proxy_radio.isChecked():
                self.custom_proxy_widget.show()
            
            # 更新代理设置
            self.update_proxy_settings()
    
    def on_convert_type_changed(self, convert_type):
        """转换类型变更处理"""
        if "Tdata转Session" in convert_type:
            # 显示导入到账户管理选项
            self.import_to_account_checkbox.show()
            if self.import_to_account_checkbox.isChecked():
                self.group_combo.show()
                # 加载分组数据
                from PySide6.QtCore import QTimer
                QTimer.singleShot(0, lambda: asyncio.ensure_future(self.load_account_groups()))
        else:
            # 隐藏导入到账户管理选项
            self.import_to_account_checkbox.hide()
            self.group_combo.hide()
    
    def on_import_to_account_toggled(self, checked):
        """导入到账户管理选项变更"""
        if checked:
            self.group_combo.show()
            # 加载分组数据
            from PySide6.QtCore import QTimer
            QTimer.singleShot(0, lambda: asyncio.ensure_future(self.load_account_groups()))
        else:
            self.group_combo.hide()
    
    def on_ip_search_changed(self, text):
        """IP搜索框文本改变时过滤IP列表"""
        for i in range(self.ip_list.count()):
            item = self.ip_list.item(i)
            if text.lower() in item.text().lower():
                item.setHidden(False)
            else:
                item.setHidden(True)
    
    @asyncSlot()
    async def load_proxy_ips(self):
        """加载代理IP池数据"""
        if self.convert_controller:
            try:
                await self.convert_controller.get_proxy_ips()
            except Exception as e:
                self.log_message(f"加载代理IP失败: {str(e)}")
    
    @asyncSlot()
    async def load_account_groups(self):
        """加载账户分组数据"""
        if self.account_controller:
            try:
                groups = await self.account_controller.get_account_groups()
                
                # 清空并重新填充下拉框
                self.group_combo.clear()
                
                # 添加默认选项
                self.group_combo.addItem("无分组", userData = -1)
                
                # 添加所有分组到下拉框
                for group in groups:
                    self.group_combo.addItem(group['name'], userData = group['id'])
                
                self.logger.info(f"成功加载 {len(groups)} 个分组")
            except Exception as e:
                self.log_message(f"加载分组失败: {str(e)}")
    
    def update_ip_list(self, ip_list):
        """更新IP池列表"""
        # 清空现有列表
        self.ip_list.clear()
        
        if not ip_list:
            empty_item = QListWidgetItem("当前没有可用的代理IP，请前往代理管理页面添加")
            empty_item.setFlags(empty_item.flags() & ~Qt.ItemIsEnabled)
            self.ip_list.addItem(empty_item)
            return
        
        # 添加所有IP项
        for ip_info in ip_list:
            ip = ip_info.get('ip', '')
            port = ip_info.get('port', '')
            account_count = ip_info.get('account_count', 0)
            
            if account_count == 0:
                display_text = f"{ip}:{port} [未绑定]"
            else:
                display_text = f"{ip}:{port} [已绑定: {account_count}]"
            
            item = QListWidgetItem(display_text)
            item.setData(Qt.UserRole, ip_info)
            self.ip_list.addItem(item)
        
        self.log_message(f"已加载 {len(ip_list)} 个代理IP")
    
    def update_proxy_settings(self):
        """更新代理设置"""
        if self.no_proxy_radio.isChecked():
            self.proxy_settings = {"type": "none"}
        elif self.system_proxy_radio.isChecked():
            self.proxy_settings = {"type": "system"}
        elif self.proxy_ip_radio.isChecked():
            selected_items = self.ip_list.selectedItems()
            if selected_items:
                ip_info = selected_items[0].data(Qt.UserRole)
                self.proxy_settings = {
                    "id": ip_info['id'],
                    "type": "ip_pool",
                    "proxy_type": ip_info['proxy_type'],
                    "ip": ip_info['ip'],
                    "port": ip_info['port'],
                    "username": ip_info.get('username', ''),
                    "password": ip_info.get('password', '')
                }
            else:
                self.proxy_settings = {"type": "none"}
        elif self.custom_proxy_radio.isChecked():
            # 获取自定义代理设置
            ip = self.custom_ip_edit.text().strip()
            port_text = self.custom_port_edit.text().strip()
            username = self.custom_username_edit.text().strip()
            password = self.custom_password_edit.text().strip()
            
            if ip and port_text:
                try:
                    port = int(port_text)
                    self.proxy_settings = {
                        "type": "custom",
                        "proxy_type": "socks5",  # 默认使用socks5
                        "ip": ip,
                        "port": port,
                        "username": username if username else "",
                        "password": password if password else ""
                    }
                except ValueError:
                    self.log_message("自定义代理端口必须是数字")
                    self.proxy_settings = {"type": "none"}
            else:
                self.proxy_settings = {"type": "none"}
        else:
            self.proxy_settings = {"type": "none"}
        
        self.logger.info(f"代理设置已更新: {self.proxy_settings}")
    
    def get_import_config(self):
        """获取导入配置"""
        if not self.import_to_account_checkbox.isChecked():
            return None
        
        group_id = self.group_combo.currentData()
        if group_id == -1:
            group_id = None
        
        return {
            "proxy_config": self.proxy_settings,
            "group_id": group_id
        }
    
    def on_conversion_started(self, message):
        """转换开始处理"""
        self.log_message(message)
        self.ui.PrimaryPushButton.setEnabled(False)
        self.ui.PrimaryPushButton.setText("转换中...")
        
        # 重置所有行的状态为"转换中..."
        for row in range(self.ui.TableWidget.rowCount()):
            self.ui.TableWidget.setItem(row, 5, QTableWidgetItem("转换中..."))
    
    def on_conversion_progress(self, current: int, total: int, message: str):
        """转换进度处理"""
        progress_msg = f"进度: {current}/{total} - {message}"
        self.log_message(progress_msg)
        
        # 更新按钮文本显示进度
        self.ui.PrimaryPushButton.setText(f"转换中... ({current}/{total})")
    
    def on_conversion_completed(self, results):
        """转换完成处理"""
        self.handle_conversion_results(results)
    
    def on_conversion_failed(self, error_message):
        """转换失败处理"""
        self.log_message(f"转换失败: {error_message}")
        self.ui.PrimaryPushButton.setEnabled(True)
        self.ui.PrimaryPushButton.setText("开始转化")
        
        # 将所有未完成的行标记为失败
        for row in range(self.ui.TableWidget.rowCount()):
            status_item = self.ui.TableWidget.item(row, 5)
            if status_item and "转换中" in status_item.text():
                self.ui.TableWidget.setItem(row, 5, QTableWidgetItem(f"失败: {error_message}"))
    
    def on_accounts_imported(self, success_count, fail_count, message):
        """账户导入完成处理"""
        self.log_message(f"账户导入完成: 成功 {success_count}, 失败 {fail_count}")
        if message:
            self.log_message(message)
    
    @asyncSlot()
    async def import_converted_accounts(self, results, import_config):
        """导入转换后的账户"""
        if self.convert_controller:
            try:
                await self.convert_controller.import_converted_accounts(results, import_config)
            except Exception as e:
                self.log_message(f"导入账户失败: {str(e)}")
    
    def import_files(self):
        """导入文件按钮事件处理"""
        convert_type = self.ui.ComboBox.currentText()
        
        if "Session转Tdata" in convert_type:
            files, _ = QFileDialog.getOpenFileNames(
                self, "选择Session文件", "", "Session文件 (*.session);;所有文件 (*)"
            )
        else:
            folder = QFileDialog.getExistingDirectory(self, "选择Tdata文件夹")
            files = [folder] if folder else []
        
        # 每次新导入文件时清空原来的表格
        if files:
            self.clear_table()
            self.log_message("清空原有文件列表，开始导入新文件")
        
        self.process_imported_files(files)
    
    def is_direct_tdata_folder(self, folder_path: str) -> bool:
        """
        检查文件夹是否直接为tdata文件夹（包含tdata标识文件）

        Args:
            folder_path: 文件夹路径

        Returns:
            是否为tdata文件夹
        """
        path = Path(folder_path)
        
        # 检查常见的tdata标识文件
        tdata_indicators = [
            'map0', 'map1',  # 主要标识文件
            'key_datas',  # 密钥数据文件
            'settings0', 'settings1',  # 设置文件
            'usertag', 'binlog',  # 其他可能的标识文件
            'D877F783D5D3EF8C',  # 用户数据文件（十六进制格式）
            'working'  # 工作状态文件
        ]
        
        # 只要存在任何一个标识文件就认为是tdata目录
        for indicator in tdata_indicators:
            if (path / indicator).exists():
                self.log_message(f"在{folder_path}中发现tdata标识文件: {indicator}")
                return True
        
        # 检查是否有十六进制格式的用户数据文件（通常是16位十六进制）
        try:
            for item in path.iterdir():
                if item.is_file():
                    # 检查文件名是否为16位十六进制格式
                    filename = item.name
                    if len(filename) == 16 and all(c in '0123456789ABCDEFabcdef' for c in filename):
                        self.log_message(f"在{folder_path}中发现十六进制用户数据文件: {filename}")
                        return True
        except (PermissionError, OSError):
            pass
        
        return False
    
    def get_tdata_display_path(self, tdata_path: str) -> str:
        """
        获取tdata文件夹的显示路径（最后两个文件夹）

        Args:
            tdata_path: tdata文件夹的完整路径

        Returns:
            显示路径，格式为 "父文件夹/tdata文件夹"
        """
        try:
            path = Path(tdata_path)
            
            # 获取路径的各个部分
            parts = path.parts
            
            if len(parts) >= 2:
                # 返回最后两个文件夹，使用正斜杠分隔符
                display_path = f"{parts[-2]}/{parts[-1]}"
                self.logger.info(f"tdata显示路径: {tdata_path} -> {display_path}")
                return display_path
            elif len(parts) == 1:
                # 只有一个文件夹名
                return parts[-1]
            else:
                # 异常情况，返回完整路径
                return str(path)
        
        except Exception as e:
            self.logger.error(f"获取tdata显示路径失败: {e}")
            # 发生错误时返回文件夹名
            return Path(tdata_path).name
    
    def analyze_tdata_directory_structure(self, folder_path: str) -> List[str]:
        """
        分析tdata目录结构，支持三种情况：
        1. 当前目录为tdata目录，直接识别该目录
        2. 当前目录下有一个tdata目录，识别该子目录
        3. 当前目录下只有非tdata的目录，遍历这些目录的下一级目录寻找tdata目录

        Args:
            folder_path: 文件夹路径

        Returns:
            找到的tdata文件夹路径列表
        """
        path = Path(folder_path)
        tdata_folders = []
        
        self.log_message(f"开始分析目录结构: {folder_path}")
        
        # 情况1: 当前目录为tdata目录
        if self.is_direct_tdata_folder(folder_path):
            self.log_message(f"情况1: 当前目录为tdata目录")
            return [folder_path]
        
        # 情况2: 当前目录下有一个tdata目录
        tdata_subfolder = path / 'tdata'
        if tdata_subfolder.exists() and self.is_direct_tdata_folder(str(tdata_subfolder)):
            self.log_message(f"情况2: 发现子目录tdata")
            return [str(tdata_subfolder)]
        
        # 情况3: 当前目录下只有非tdata的目录，遍历这些目录的下一级目录
        self.log_message(f"情况3: 遍历子目录寻找tdata目录")
        
        # 获取所有一级子目录
        subdirs = [d for d in path.iterdir() if d.is_dir()]
        
        if not subdirs:
            self.log_message("未发现任何子目录")
            return []
        
        # 检查每个子目录
        for subdir in subdirs:
            # 检查子目录本身是否为tdata目录
            if self.is_direct_tdata_folder(str(subdir)):
                tdata_folders.append(str(subdir))
                self.log_message(f"发现tdata目录: {subdir.name}")
            else:
                # 检查子目录下是否有tdata子目录
                tdata_in_subdir = subdir / 'tdata'
                if tdata_in_subdir.exists() and self.is_direct_tdata_folder(str(tdata_in_subdir)):
                    tdata_folders.append(str(tdata_in_subdir))
                    self.log_message(f"在{subdir.name}下发现tdata目录")
        
        if tdata_folders:
            self.log_message(f"总共发现{len(tdata_folders)}个tdata目录")
        else:
            self.log_message("未发现任何tdata目录")
        
        return tdata_folders
    
    def process_imported_files(self, files: List[str]):
        """处理导入的文件"""
        convert_type = self.ui.ComboBox.currentText()
        imported_files = []
        skipped_files = []
        
        self.log_message(f"开始处理 {len(files)} 个导入项...")
        
        for file_path in files:
            if "Session转Tdata" in convert_type:
                path = Path(file_path)
                if path.is_dir():
                    # 在目录中查找session文件
                    session_files = FileUtils.find_session_files(file_path)
                    if session_files:
                        imported_files.extend(session_files)
                        self.log_message(f"在目录 {path.name} 中找到 {len(session_files)} 个session文件")
                    else:
                        skipped_files.append(f"目录 {path.name}: 未找到session文件")
                elif path.suffix == '.session':
                    if path.exists():
                        imported_files.append(file_path)
                        self.log_message(f"添加session文件: {path.name}")
                    else:
                        skipped_files.append(f"Session文件不存在: {path.name}")
                else:
                    skipped_files.append(f"不是session文件: {path.name}")
            else:  # Tdata转Session
                path = Path(file_path)
                if path.is_dir():
                    # 使用新的目录结构分析方法
                    self.log_message(f"分析目录结构: {path.name}")
                    tdata_folders = self.analyze_tdata_directory_structure(file_path)
                    if tdata_folders:
                        imported_files.extend(tdata_folders)
                        self.log_message(f"在 {path.name} 中找到 {len(tdata_folders)} 个tdata文件夹")
                    else:
                        # 如果新方法没有找到，尝试递归查找（兜底方案）
                        self.log_message(f"在 {path.name} 中未找到tdata文件夹，尝试递归查找...")
                        all_tdata_folders = FileUtils.find_tdata_folders(file_path)
                        if all_tdata_folders:
                            imported_files.extend(all_tdata_folders)
                            self.log_message(f"递归查找到 {len(all_tdata_folders)} 个tdata文件夹")
                        else:
                            skipped_files.append(f"目录 {path.name}: 未找到任何tdata文件夹")
                else:
                    skipped_files.append(f"不是目录: {path.name}")
        
        # 记录跳过的文件
        if skipped_files:
            self.log_message("以下文件被跳过:")
            for skipped in skipped_files:
                self.log_message(f"  - {skipped}")
        
        # 去重并添加到表格
        new_files = []
        duplicate_files = []
        for file_path in imported_files:
            if file_path not in self.file_list:
                self.file_list.append(file_path)
                self.add_file_to_table(file_path)
                new_files.append(file_path)
            else:
                duplicate_files.append(Path(file_path).name)
        
        # 记录重复文件
        if duplicate_files:
            self.log_message(f"跳过 {len(duplicate_files)} 个重复文件: {', '.join(duplicate_files[:5])}" +
                             ("..." if len(duplicate_files) > 5 else ""))
        
        # 更新日志
        if new_files:
            self.log_message(f"✓ 成功导入 {len(new_files)} 个新文件")
            
            # 为新导入的文件自动检测并填充密码
            self.detect_and_fill_passwords_for_new_files(new_files)
        else:
            self.log_message("⚠ 没有导入任何新文件")
        
        # 如果是Tdata转Session且选择了文件识别密码，提示用户
        if "Tdata转Session" in convert_type and self.ui.ComboBox_2.currentIndex() == 1 and new_files:
            password_file = self.ui.LineEdit.text().strip() or self.password_file_name
            
            # 检查有多少个tdata文件夹包含密码文件
            folders_with_password = 0
            folders_without_password = []
            
            for file_path in new_files:
                password_file_path = Path(file_path) / password_file
                if password_file_path.exists():
                    folders_with_password += 1
                else:
                    folders_without_password.append(Path(file_path).name)
            
            message = f"您选择了'文件识别密码'模式，系统将在每个tdata文件夹中查找名为'{password_file}'的文件。\n\n"
            message += f"检查结果:\n"
            message += f"• 包含密码文件: {folders_with_password} 个\n"
            message += f"• 缺少密码文件: {len(folders_without_password)} 个\n\n"
            
            if folders_without_password:
                message += f"缺少密码文件的文件夹:\n"
                for folder in folders_without_password[:5]:  # 只显示前5个
                    message += f"  - {folder}\n"
                if len(folders_without_password) > 5:
                    message += f"  ... 还有 {len(folders_without_password) - 5} 个\n"
                message += f"\n这些文件夹将使用无密码模式进行转换。"
            
            message += f"\n\n如需修改密码文件名，请在'默认密码'输入框中输入。"
            
            QMessageBox.information(self, "密码文件检查", message)
    
    def add_file_to_table(self, file_path: str):
        """将文件添加到表格"""
        convert_type = self.ui.ComboBox.currentText()
        row_position = self.ui.TableWidget.rowCount()
        self.ui.TableWidget.insertRow(row_position)
        
        # 提取电话号码和显示信息
        phone = "未知"
        file_type = ""
        password = ""
        display_info = ""  # 用于显示文件路径信息
        path = Path(file_path)
        
        if "Session转Tdata" in convert_type:
            file_type = "Session"
            phone = path.stem
            display_info = path.name  # Session文件显示文件名
        else:
            file_type = "Tdata"
            # 获取tdata文件夹的显示路径（最后两个文件夹）
            display_info = self.get_tdata_display_path(file_path)
            
            # 尝试从tdata文件夹名称中提取电话号码
            folder_name = path.name
            if folder_name.startswith("tdata_"):
                phone = folder_name[6:]
            elif folder_name.isdigit():
                phone = folder_name
            else:
                # 尝试从父文件夹名称提取
                parent_name = path.parent.name
                if parent_name.isdigit():
                    phone = parent_name
            
            # 根据密码类型处理密码
            password_index = self.ui.ComboBox_2.currentIndex()
            if password_index == 1:  # 文件识别密码模式
                password_file_name = self.ui.LineEdit.text().strip() or self.password_file_name
                password_file = path / password_file_name
                if password_file.exists() and password_file.is_file():
                    try:
                        password = password_file.read_text(encoding = 'utf-8').strip()
                        self.log_message(f"从 {password_file} 读取到密码")
                    except Exception as e:
                        self.log_message(f"读取密码文件 {password_file} 失败: {str(e)}")
        
        # 根据密码类型设置密码
        password_index = self.ui.ComboBox_2.currentIndex()
        if password_index == 0:  # 无密码
            password = ""
        elif password_index == 2:  # 统一密码模式
            password = self.ui.LineEdit.text().strip()
        
        # 设置表格项
        # 第0列：电话号码
        self.ui.TableWidget.setItem(row_position, 0, QTableWidgetItem(phone))
        # 第1列：文件类型 + 路径信息
        type_with_path = f"{file_type} ({display_info})"
        self.ui.TableWidget.setItem(row_position, 1, QTableWidgetItem(type_with_path))
        # 其他列保持不变
        self.ui.TableWidget.setItem(row_position, 2, QTableWidgetItem(password))
        self.ui.TableWidget.setItem(row_position, 3, QTableWidgetItem(datetime.now().strftime("%Y-%m-%d %H:%M:%S")))
        self.ui.TableWidget.setItem(row_position, 4, QTableWidgetItem("未知"))
        self.ui.TableWidget.setItem(row_position, 5, QTableWidgetItem("等待转换"))
        
        # 记录添加的文件信息
        self.log_message(f"添加文件到表格: {display_info} (电话: {phone})")
    
    def select_output_path(self):
        """选择输出路径"""
        folder = QFileDialog.getExistingDirectory(self, "选择输出目录")
        if folder:
            self.ui.LineEdit_3.setText(folder)
    
    def clear_table(self):
        """清空表格"""
        self.ui.TableWidget.setRowCount(0)
        self.file_list = []
        self.log_message("已清空列表")
    
    def show_context_menu(self, pos):
        """显示右键菜单"""
        context_menu = QMenu(self)
        
        # 添加菜单项
        import_action = context_menu.addAction("批量导入")
        remove_action = context_menu.addAction("移除选中项")
        
        # 密码相关菜单
        password_menu = context_menu.addMenu("密码操作")
        edit_password_action = password_menu.addAction("修改选中项密码")
        auto_fill_passwords_action = password_menu.addAction("自动填充文件密码")
        clear_passwords_action = password_menu.addAction("清空所有密码")
        set_password_file_action = password_menu.addAction("设置密码文件名")
        
        clear_action = context_menu.addAction("清空列表")
        
        # 显示菜单
        action = context_menu.exec(self.ui.TableWidget.mapToGlobal(pos))
        
        # 处理菜单事件
        if action == import_action:
            self.import_files()  # import_files方法内部已经处理了清空表格
        elif action == remove_action:
            self.remove_selected_items()
        elif action == edit_password_action:
            self.edit_selected_password()
        elif action == auto_fill_passwords_action:
            self.auto_fill_file_passwords()
        elif action == clear_passwords_action:
            self.clear_table_passwords()
        elif action == set_password_file_action:
            self.set_password_file_name()
        elif action == clear_action:
            self.clear_table()
    
    def edit_selected_password(self):
        """修改选中项的密码"""
        selected_items = self.ui.TableWidget.selectedItems()
        if not selected_items:
            return
        
        # 获取所有选中的行
        selected_rows = set()
        for item in selected_items:
            selected_rows.add(item.row())
        
        # 弹出输入对话框
        password, ok = QInputDialog.getText(
            self, "修改密码", "请输入新密码:",
            text = self.ui.LineEdit.text()
        )
        
        if ok and password:
            # 更新所有选中行的密码
            for row in selected_rows:
                self.ui.TableWidget.setItem(row, 2, QTableWidgetItem(password))
            
            self.log_message(f"已更新 {len(selected_rows)} 个项目的密码")
    
    def set_password_file_name(self):
        """设置密码文件名"""
        current_name = self.ui.LineEdit.text() or self.password_file_name
        file_name, ok = QInputDialog.getText(
            self, "设置密码文件名", "请输入密码文件名:",
            text = current_name
        )
        
        if ok and file_name:
            self.password_file_name = file_name
            if self.ui.ComboBox_2.currentIndex() == 0:  # 文件识别密码模式
                self.ui.LineEdit.setText(file_name)
            self.log_message(f"密码文件名已设置为: {file_name}")
    
    def remove_selected_items(self):
        """移除选中的项目"""
        selected_rows = set()
        for item in self.ui.TableWidget.selectedItems():
            selected_rows.add(item.row())
        
        # 从后往前删除，避免索引变化
        for row in sorted(selected_rows, reverse = True):
            if 0 <= row < len(self.file_list):
                del self.file_list[row]
            self.ui.TableWidget.removeRow(row)
        
        self.log_message(f"已移除 {len(selected_rows)} 个项目")
    
    def get_proxy_config(self) -> Dict[str, Any]:
        """获取代理配置"""
        # 更新代理设置
        self.update_proxy_settings()
        return self.proxy_settings or {"type": "none"}
    
    def get_password_info(self) -> Dict[str, Any]:
        """获取密码信息"""
        password_index = self.ui.ComboBox_2.currentIndex()
        
        if password_index == 0:  # 无密码
            password_info = {
                "type": "none",
                "password": "",
                "file_name": self.password_file_name
            }
        elif password_index == 1:  # 文件识别密码
            # 获取密码文件名，如果输入框为空则使用默认值
            file_name = self.ui.LineEdit.text().strip() or self.password_file_name
            password_info = {
                "type": "file",
                "password": "",  # 文件模式下这里不存储密码
                "file_name": file_name
            }
        else:  # 统一密码
            unified_password = self.ui.LineEdit.text().strip()
            password_info = {
                "type": "unified",
                "password": unified_password,
                "file_name": self.password_file_name
            }
        
        # 收集表格中的密码（优先级最高）
        password_info["table_passwords"] = {}
        for row in range(self.ui.TableWidget.rowCount()):
            phone_item = self.ui.TableWidget.item(row, 0)
            password_item = self.ui.TableWidget.item(row, 2)
            file_path = self.file_list[row] if row < len(self.file_list) else None
            
            if phone_item and password_item and file_path:
                phone = phone_item.text()
                password = password_item.text()
                # 只有当密码不为空时才添加到表格密码中
                if password and password.strip():
                    password_info["table_passwords"][file_path] = {
                        "phone": phone,
                        "password": password.strip()
                    }
        
        # 记录密码配置信息（不记录实际密码内容）
        password_count = len(password_info["table_passwords"])
        self.logger.info(f"密码配置: 类型={password_info['type']}, 表格密码数={password_count}")
        
        return password_info
    
    def start_conversion(self):
        """开始转换"""
        # 检查是否有文件
        if not self.file_list:
            self.log_message("❌ 请先导入文件")
            QMessageBox.warning(self, "提示", "请先导入需要转换的文件")
            return
        
        # 检查输出路径
        output_dir = self.ui.LineEdit_3.text().strip()
        if not output_dir:
            self.log_message("❌ 请选择输出路径")
            QMessageBox.warning(self, "提示", "请选择输出路径")
            return
        
        # 验证输出路径
        try:
            output_path = Path(output_dir)
            if output_path.exists() and not output_path.is_dir():
                self.log_message("❌ 输出路径不是有效的目录")
                QMessageBox.warning(self, "错误", "输出路径不是有效的目录")
                return
        except Exception as e:
            self.log_message(f"❌ 输出路径无效: {str(e)}")
            QMessageBox.warning(self, "错误", f"输出路径无效: {str(e)}")
            return
        
        # 确保输出目录存在
        if not FileUtils.ensure_dir(output_dir):
            self.log_message("❌ 创建输出目录失败")
            QMessageBox.critical(self, "错误", "创建输出目录失败，请检查权限")
            return
        
        # 获取转换类型
        convert_type = self.ui.ComboBox.currentText()
        conversion_type = "session_to_tdata" if "Session转Tdata" in convert_type else "tdata_to_session"
        
        # 获取代理配置
        proxy_config = self.get_proxy_config()
        
        # 获取密码信息
        password_info = self.get_password_info()
        
        # 转换前的最终检查和确认
        self.log_message("=== 开始转换前检查 ===")
        self.log_message(f"转换类型: {convert_type}")
        self.log_message(f"文件数量: {len(self.file_list)}")
        self.log_message(f"输出目录: {output_dir}")
        self.log_message(f"代理类型: {proxy_config.get('type', '未知')}")
        self.log_message(f"密码类型: {password_info.get('type', '未知')}")
        
        # 检查密码配置
        if conversion_type == "tdata_to_session":
            password_type = password_info.get('type', 'none')
            if password_type == 'unified' and not password_info.get('password', '').strip():
                reply = QMessageBox.question(
                    self, "密码确认",
                    "您选择了'统一密码'模式但未输入密码。\n\n"
                    "如果您的tdata文件需要两步验证密码，转换可能会失败。\n\n"
                    "是否继续？",
                    QMessageBox.Yes | QMessageBox.No,
                    QMessageBox.No
                )
                if reply == QMessageBox.No:
                    return
            
            # 检查表格中的密码设置
            table_passwords = password_info.get('table_passwords', {})
            if table_passwords:
                self.log_message(f"表格中设置了 {len(table_passwords)} 个文件的密码")
        
        # 最终确认
        reply = QMessageBox.question(
            self, "开始转换",
            f"准备开始转换 {len(self.file_list)} 个文件。\n\n"
            f"转换类型: {convert_type}\n"
            f"输出目录: {output_dir}\n\n"
            f"确定开始转换吗？",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.Yes
        )
        
        if reply == QMessageBox.No:
            self.log_message("用户取消了转换操作")
            return
        
        # 更新状态
        for row in range(self.ui.TableWidget.rowCount()):
            status_item = QTableWidgetItem("等待转换...")
            status_item.setBackground(Qt.yellow)
            self.ui.TableWidget.setItem(row, 5, status_item)
        
        # 使用控制器进行转换
        if self.convert_controller:
            from PySide6.QtCore import QTimer
            QTimer.singleShot(0, lambda: asyncio.ensure_future(self.convert_controller.batch_convert(
                self.file_list,
                output_dir,
                conversion_type,
                proxy_config,
                password_info
            )))
        else:
            # 回退到原有的线程方式
            self.ui.PrimaryPushButton.setEnabled(False)
            self.worker = WorkerThread(
                self.file_list,
                output_dir,
                conversion_type,
                proxy_config,
                password_info
            )
            self.worker.log_message.connect(self.log_message)
            self.worker.conversion_done.connect(self.handle_conversion_results)
            self.worker.start()
        
        self.log_message(f"🚀 开始{convert_type}，共 {len(self.file_list)} 个文件")
        self.log_message("=== 转换过程开始 ===")
    
    def handle_conversion_results(self, results: List[Dict[str, Any]]):
        """处理转换结果"""
        try:
            # 更新表格
            for i, result in enumerate(results):
                if i < self.ui.TableWidget.rowCount():
                    # 更新电话号码
                    if "phone" in result and result["phone"] != "未知":
                        self.ui.TableWidget.setItem(i, 0, QTableWidgetItem(result["phone"]))
                    
                    # 更新会员状态
                    if "premium" in result:
                        premium_text = "是" if result["premium"] else "否"
                        self.ui.TableWidget.setItem(i, 4, QTableWidgetItem(premium_text))
                    
                    # 更新转换结果
                    status_text = result.get("status", "未知")
                    status_item = QTableWidgetItem(status_text)
                    
                    # 根据状态设置不同的颜色
                    if "成功" in status_text:
                        status_item.setBackground(Qt.green)
                        status_item.setForeground(Qt.white)
                    elif "失败" in status_text:
                        status_item.setBackground(Qt.red)
                        status_item.setForeground(Qt.white)
                    
                    self.ui.TableWidget.setItem(i, 5, status_item)
                    
                    # 记录详细结果
                    if "成功" in status_text:
                        output_path = result.get("output_path", "")
                        self.log_message(f"✓ 文件 {i + 1} 转换成功: {output_path}")
                        
                        # 记录额外的成功信息
                        if "password_source" in result:
                            self.log_message(f"  密码来源: {result['password_source']}")
                        if "file_size" in result:
                            self.log_message(f"  文件大小: {result['file_size']} 字节")
                    else:
                        self.log_message(f"✗ 文件 {i + 1} 转换失败: {status_text}")
                        
                        # 记录详细的错误信息
                        if "error_details" in result:
                            error_details = result["error_details"]
                            if "error_type" in error_details:
                                self.log_message(f"  错误类型: {error_details['error_type']}")
                            if "password_attempts" in error_details and error_details["password_attempts"]:
                                self.log_message(f"  密码尝试: {'; '.join(error_details['password_attempts'])}")
            
            # 恢复按钮状态
            self.ui.PrimaryPushButton.setEnabled(True)
            self.ui.PrimaryPushButton.setText("开始转化")
            
            # 计算成功数量
            success_count = sum(1 for r in results if "成功" in r.get("status", ""))
            fail_count = len(results) - success_count
            
            # 记录详细统计
            self.log_message(f"=== 转换完成统计 ===")
            self.log_message(f"成功: {success_count} 个")
            self.log_message(f"失败: {fail_count} 个")
            self.log_message(f"总计: {len(results)} 个")
            
            # 统计失败原因
            if fail_count > 0:
                failure_reasons = {}
                for result in results:
                    if "失败" in result.get("status", ""):
                        status = result.get("status", "")
                        # 提取失败原因的关键词
                        if "密码" in status:
                            failure_reasons["密码相关"] = failure_reasons.get("密码相关", 0) + 1
                        elif "文件" in status and ("不存在" in status or "损坏" in status):
                            failure_reasons["文件问题"] = failure_reasons.get("文件问题", 0) + 1
                        elif "网络" in status or "连接" in status:
                            failure_reasons["网络问题"] = failure_reasons.get("网络问题", 0) + 1
                        else:
                            failure_reasons["其他原因"] = failure_reasons.get("其他原因", 0) + 1
                
                self.log_message("失败原因统计:")
                for reason, count in failure_reasons.items():
                    self.log_message(f"  {reason}: {count} 个")
            
            # 显示完成消息
            if success_count == len(results):
                message_type = "全部成功"
                message_icon = QMessageBox.Information
                detailed_text = f"恭喜！所有 {len(results)} 个文件都转换成功！"
            elif success_count > 0:
                message_type = "部分成功"
                message_icon = QMessageBox.Warning
                detailed_text = f"成功: {success_count} 个\n失败: {fail_count} 个\n总计: {len(results)} 个\n\n请检查日志了解失败原因。"
            else:
                message_type = "全部失败"
                message_icon = QMessageBox.Critical
                detailed_text = f"很遗憾，所有 {len(results)} 个文件都转换失败。\n\n请检查:\n1. 文件是否完整\n2. 密码是否正确\n3. 网络连接是否正常\n4. 查看详细日志了解具体原因"
            
            msg_box = QMessageBox(self)
            msg_box.setIcon(message_icon)
            msg_box.setWindowTitle("转换完成")
            msg_box.setText(f"转换完成 - {message_type}")
            msg_box.setDetailedText(detailed_text)
            msg_box.exec()
            
            # 如果需要导入到账户管理
            import_config = self.get_import_config()
            if import_config and success_count > 0:
                self.log_message(f"准备导入 {success_count} 个成功转换的账户到账户管理...")
                from PySide6.QtCore import QTimer
                QTimer.singleShot(0,
                                  lambda: asyncio.ensure_future(self.import_converted_accounts(results, import_config)))
        
        except Exception as e:
            error_msg = f"处理转换结果时发生错误: {str(e)}"
            self.logger.error(error_msg)
            self.log_message(error_msg)
            
            # 确保按钮状态恢复
            self.ui.PrimaryPushButton.setEnabled(True)
            self.ui.PrimaryPushButton.setText("开始转化")
    
    def clear_table_passwords(self):
        """清空表格中的密码列"""
        for row in range(self.ui.TableWidget.rowCount()):
            self.ui.TableWidget.setItem(row, 2, QTableWidgetItem(""))
        self.log_message("已清空表格中的密码")
    
    def auto_fill_file_passwords(self):
        """自动检测并填充文件密码到表格"""
        if not self.file_list:
            return
        
        password_file_name = self.ui.LineEdit.text().strip() or self.password_file_name
        filled_count = 0
        not_found_count = 0
        
        self.log_message(f"开始自动检测密码文件: {password_file_name}")
        
        for row in range(self.ui.TableWidget.rowCount()):
            if row < len(self.file_list):
                file_path = self.file_list[row]
                
                # 只处理tdata文件夹
                if Path(file_path).is_dir():
                    password_file = Path(file_path) / password_file_name
                    
                    if password_file.exists() and password_file.is_file():
                        try:
                            password = password_file.read_text(encoding = 'utf-8').strip()
                            if password:
                                self.ui.TableWidget.setItem(row, 2, QTableWidgetItem(password))
                                filled_count += 1
                                self.log_message(f"  ✓ {Path(file_path).name}: 找到密码文件")
                            else:
                                self.ui.TableWidget.setItem(row, 2, QTableWidgetItem(""))
                                self.log_message(f"  ⚠ {Path(file_path).name}: 密码文件为空")
                        except Exception as e:
                            self.ui.TableWidget.setItem(row, 2, QTableWidgetItem(""))
                            self.log_message(f"  ✗ {Path(file_path).name}: 读取密码文件失败 - {str(e)}")
                    else:
                        self.ui.TableWidget.setItem(row, 2, QTableWidgetItem(""))
                        not_found_count += 1
        
        # 统计结果
        if filled_count > 0:
            self.log_message(f"✓ 自动填充完成: 成功 {filled_count} 个, 未找到 {not_found_count} 个")
        else:
            self.log_message(f"⚠ 未找到任何密码文件")
    
    def auto_fill_unified_password(self):
        """自动填充统一密码到表格"""
        unified_password = self.ui.LineEdit.text().strip()
        
        if not unified_password:
            self.log_message("统一密码为空，无法填充")
            return
        
        filled_count = 0
        for row in range(self.ui.TableWidget.rowCount()):
            self.ui.TableWidget.setItem(row, 2, QTableWidgetItem(unified_password))
            filled_count += 1
        
        if filled_count > 0:
            self.log_message(f"✓ 已将统一密码填充到 {filled_count} 个条目")
    
    def detect_and_fill_passwords_for_new_files(self, new_file_paths: List[str]):
        """为新导入的文件检测并填充密码"""
        password_type_index = self.ui.ComboBox_2.currentIndex()
        
        if password_type_index == 1:  # 文件识别密码模式
            password_file_name = self.ui.LineEdit.text().strip() or self.password_file_name
            
            for file_path in new_file_paths:
                if Path(file_path).is_dir():
                    password_file = Path(file_path) / password_file_name
                    
                    if password_file.exists() and password_file.is_file():
                        try:
                            password = password_file.read_text(encoding = 'utf-8').strip()
                            if password:
                                # 找到对应的表格行并填充密码
                                for row in range(self.ui.TableWidget.rowCount()):
                                    if row < len(self.file_list) and self.file_list[row] == file_path:
                                        self.ui.TableWidget.setItem(row, 2, QTableWidgetItem(password))
                                        break
                        except Exception as e:
                            self.log_message(f"读取密码文件失败 {file_path}: {str(e)}")
        
        elif password_type_index == 2:  # 统一密码模式
            unified_password = self.ui.LineEdit.text().strip()
            if unified_password:
                # 为新文件填充统一密码
                for file_path in new_file_paths:
                    for row in range(self.ui.TableWidget.rowCount()):
                        if row < len(self.file_list) and self.file_list[row] == file_path:
                            self.ui.TableWidget.setItem(row, 2, QTableWidgetItem(unified_password))
                            break
    
    def log_message(self, message: str):
        """显示日志消息"""
        timestamp = time.strftime("%Y-%m-%d %H:%M:%S")
        log_text = f"[{timestamp}] {message}"
        
        # 添加到日志框
        self.ui.textBrowser.append(log_text)
        # 滚动到底部
        self.ui.textBrowser.verticalScrollBar().setValue(
            self.ui.textBrowser.verticalScrollBar().maximum()
        )
        
        # 记录到日志文件
        self.logger.info(message)
