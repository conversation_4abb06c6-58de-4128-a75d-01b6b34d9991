import sys
import os
from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QApplication, 
    QGridLayout, QMenu, QFrame
)
from PySide6.QtCore import Qt, QUrl, Signal
from PySide6.QtGui import QDesktopServices, QPixmap, QIcon, QAction, QCursor

from qfluentwidgets import (
    FluentIcon as FIF, 
    ScrollArea, 
    PushButton, 
    CardWidget, 
    TransparentToolButton, 
    ToolTipFilter, 
    TitleLabel, 
    BodyLabel, 
    IconWidget,
    FluentStyleSheet,
    Theme,
    isDarkTheme,
    StrongBodyLabel,
    SmoothScrollArea,
    InfoBar,
    InfoBarPosition
)

class DocLinkCard(QFrame):
    """文档链接卡片组件"""
    
    linkClicked = Signal(str)
    linkCopied = Signal(str)
    
    def __init__(self, icon, title, url, parent=None):
        super().__init__(parent)
        self.url = url
        
        # 设置样式
        self.setObjectName("DocLinkCard")
        self.setStyleSheet("""
            #DocLinkCard {
                background-color: rgb(245, 245, 245);
                border-radius: 8px;
            }
            #DocLinkCard:hover {
                background-color: rgb(235, 235, 235);
            }
        """)
        
        # 设置布局
        layout = QHBoxLayout(self)
        layout.setContentsMargins(15, 10, 15, 10)
        layout.setSpacing(10)
        
        # 图标
        self.iconWidget = IconWidget(icon, self)
        self.iconWidget.setFixedSize(24, 24)
        
        # 标题
        self.titleLabel = StrongBodyLabel(title, self)
        
        # 布局添加组件
        layout.addWidget(self.iconWidget)
        layout.addWidget(self.titleLabel, 1)
        
        # 样式设置
        self.setCursor(Qt.PointingHandCursor)
        self.setFixedHeight(50)
        
    def mousePressEvent(self, event):
        """鼠标按下事件"""
        super().mousePressEvent(event)
        
        if event.button() == Qt.LeftButton:
            self.linkClicked.emit(self.url)
        elif event.button() == Qt.RightButton:
            self._showContextMenu(event.pos())
    
    def _showContextMenu(self, pos):
        """显示右键菜单"""
        menu = QMenu(self)
        copyAction = QAction("复制链接", self)
        copyAction.triggered.connect(lambda: self.linkCopied.emit(self.url))
        menu.addAction(copyAction)
        menu.exec(self.mapToGlobal(pos))


class DocView(ScrollArea):
    """文档视图组件"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setObjectName("DocView")
        
        # 设置主要容器和布局
        self.view = QWidget(self)
        self.vBoxLayout = QVBoxLayout(self.view)
        self.vBoxLayout.setContentsMargins(20, 20, 20, 20)
        self.vBoxLayout.setSpacing(20)
        
        # 文档数据
        self.docs_data = [
            {"icon": FIF.DOCUMENT, "title": "官方文档", "url": "https://docs.xile188.com/#/README"},
            {"icon": FIF.HEART, "title": "登录注册", "url": "https://docs.xile188.com/#/zh-cn/user/login"},
            {"icon": FIF.HISTORY, "title": "更新日志", "url": "https://docs.xile188.com/#/zh-cn/tg/update"},
            {"icon": FIF.ACCEPT, "title": "账户管理", "url": "https://docs.xile188.com/#/zh-cn/account/account_group"},
            {"icon": FIF.HELP, "title": "帮助、问题文档", "url": "https://docs.xile188.com/#/zh-cn/faq/README"},
            {"icon": FIF.HELP, "title": "更多文档", "url": "https://docs.xile188.com/"},
        ]
        
        # 标题区域
        self.setupHeader()
        
        # 文档链接区域
        self.setupDocLinks()
        
        # 设置滚动区域
        self.setWidget(self.view)
        self.setWidgetResizable(True)
        
    def setupHeader(self):
        """设置标题区域"""
        headerLayout = QHBoxLayout()
        
        # 标题图标
        iconWidget = IconWidget(FIF.BOOK_SHELF, self)
        iconWidget.setFixedSize(32, 32)
        
        # 标题文字
        titleLabel = TitleLabel("相关文档")
        titleLabel.setObjectName("DocTitle")
        
        # 添加到布局
        headerLayout.addWidget(iconWidget)
        headerLayout.addWidget(titleLabel)
        headerLayout.addStretch(1)
        headerLayout.setContentsMargins(10, 10, 10, 20)
        
        self.vBoxLayout.addLayout(headerLayout)
        
    def setupDocLinks(self):
        """设置文档链接区域"""
        # 创建一个大的卡片容器
        containerCard = CardWidget(self)
        containerLayout = QVBoxLayout(containerCard)
        containerLayout.setContentsMargins(20, 20, 20, 20)
        containerLayout.setSpacing(15)
        
        # 添加说明文本
        descLabel = BodyLabel("以下是可供参考的相关文档链接，左键点击打开链接，右键点击复制链接")
        containerLayout.addWidget(descLabel)
        
        # 创建链接卡片并横向排列
        for i in range(0, len(self.docs_data), 2):  # 每行最多2个卡片
            rowLayout = QHBoxLayout()
            rowLayout.setSpacing(15)
            
            # 添加第一个卡片
            card1 = self._createDocLinkCard(self.docs_data[i], containerCard)
            rowLayout.addWidget(card1)
            
            # 如果有第二个卡片，添加第二个卡片
            if i + 1 < len(self.docs_data):
                card2 = self._createDocLinkCard(self.docs_data[i + 1], containerCard)
                rowLayout.addWidget(card2)
            else:
                # 添加一个空的占位widget保持布局对称
                spacer = QWidget()
                spacer.setFixedHeight(50)
                rowLayout.addWidget(spacer)
            
            containerLayout.addLayout(rowLayout)
        
        containerLayout.addStretch(1)
        
        # 添加到主布局
        self.vBoxLayout.addWidget(containerCard)
        self.vBoxLayout.addStretch(1)
    
    def _createDocLinkCard(self, doc_data, parent):
        """创建文档链接卡片"""
        card = DocLinkCard(doc_data["icon"], doc_data["title"], doc_data["url"], parent)
        card.linkClicked.connect(self.openUrl)
        card.linkCopied.connect(self.copyUrl)
        return card
        
    def openUrl(self, url):
        """打开URL"""
        QDesktopServices.openUrl(QUrl(url))
    
    def copyUrl(self, url):
        """复制URL到剪贴板"""
        clipboard = QApplication.clipboard()
        clipboard.setText(url)
        
        # 显示复制成功提示
        InfoBar.success(
            title="复制成功",
            content=f"链接已复制到剪贴板",
            parent=self,
            position=InfoBarPosition.TOP,
            duration=2000
        )


if __name__ == "__main__":
    app = QApplication(sys.argv)
    
    # 设置主题
    from qfluentwidgets import setTheme, Theme
    setTheme(Theme.AUTO)
    
    window = DocView()
    window.resize(600, 500)
    window.show()
    
    sys.exit(app.exec())