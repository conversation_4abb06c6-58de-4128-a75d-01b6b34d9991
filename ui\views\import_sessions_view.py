"""
批量导入session视图
"""

from PySide6.QtWidgets import QFileDialog, QComboBox, QTableWidgetItem, QWidget, QHBoxLayout
from PySide6.QtCore import Qt, QCoreApplication, Signal, QTimer
from qasync import asyncSlot
from qfluentwidgets import Dialog, InfoBar, InfoBarPosition, CheckBox, ComboBox, MessageBox, InfoBarIcon, IndeterminateProgressRing
from app.controllers.account_controller import AccountController
from ui.dialogs.import_session_dialog import BatchImportSessionUI
from ui.common.message_box import createErrorInfoBar, createSuccessInfoBar, createWarningInfoBar
from pathlib import Path
from PySide6.QtGui import QBrush, QColor
from utils.logger import get_logger

class ImportSessionsView(BatchImportSessionUI):
    # 信号定义 (如果需要，可以用于通知外部，但这里我们将主要通过controller交互)
    # import_sessions_signal = Signal(list, int) 

    def __init__(self, parent=None, account_controller: AccountController = None):
        super().__init__(parent=parent)
        self.account_controller = account_controller
        self._logger = get_logger("ui.views.import_sessions_view")

        self.setWindowTitle("批量导入Session")
        self.resize(1000, 700) # 设置默认大小

        # 数据存储
        self.session_files = []  # 选中的session文件列表
        self.proxy_list = []     # 可用代理列表 [{'ip': '', 'port': '', 'username':'', 'password':'', 'account_count':0}]
        self.group_list = []     # 可用分组列表 [{'id': 1, 'name': 'Group A'}]
        
        # 用于管理IP绑定统计和代理映射
        self.ip_binding_count = {}  # 格式: {ip_port: count}
        self.session_proxy_map = {}  # 格式: {session_file_path: proxy_config}
        
        # 存储UI组件引用
        self.row_checkboxes = []    # 每行的复选框
        self.row_proxy_combos = []  # 每行的主代理类型下拉框
        self.row_ip_combos = []     # 每行的IP池选择下拉框
        self.row_group_combos = []  # 每行的分组下拉框
        
        # 默认选中全选按钮
        self.select_all_checkbox.setChecked(True)
        
        # 连接信号
        self.connect_signals()

        # 初始加载数据
        QTimer.singleShot(0, self.initial_load_data)

    @asyncSlot()
    async def initial_load_data(self):
        """初始加载代理和分组列表"""
        await self.refresh_proxy_list_async()
        await self.refresh_groups_list_async()

    def connect_signals(self):
        """连接所有信号"""
        self.import_button.clicked.connect(self.select_session_files)
        self.refresh_proxy_button.clicked.connect(self.refresh_proxy_list_async)
        self.refresh_groups_button.clicked.connect(self.refresh_groups_list_async)
        self.select_all_checkbox.stateChanged.connect(self.toggle_select_all)
        self.login_button.clicked.connect(self.login_selected_sessions_async)
        self.cancel_button.clicked.connect(self.close)

        # 为控制器进度信号连接回调
        if self.account_controller:
            # 连接到 notify 信号进行行级更新
            self.account_controller.notify.connect(self._handle_controller_notification)
            # sessions_batch_imported 用于显示最终的摘要信息，可以连接到一个不同的槽或在这里处理
            self.account_controller.sessions_batch_imported.connect(self._handle_batch_import_summary)

    def show_success_message(self, title, content):
        """显示成功信息"""
        createSuccessInfoBar(parten=self,title=title, content=content)

    def show_warning_message(self, title, content):
        """显示警告信息"""
        createWarningInfoBar(parten=self,title=title, content=content)

    def show_error_message(self, title, content):
        """显示错误信息"""
        createErrorInfoBar(parten=self,title=title, content=content)

    # ================ 文件选择相关 ================
    def select_session_files(self):
        """选择session文件"""
        files, _ = QFileDialog.getOpenFileNames(self, "选择Session文件", "", "Session Files (*.session)")
        self.session_files =[]  #清空之前导入记录
        if files:
            processed_files = []
            for file_path in files:
                # 统一路径表示，主要为了字典key的稳定性
                processed_file = str(Path(file_path)) 
                processed_files.append(processed_file)
                
            # 将新选择的文件添加到现有列表中，而不是替换
            # 去重，确保每个文件只出现一次，新文件排在后面
            existing_files_set = set(self.session_files)
            for pf in processed_files:
                if pf not in existing_files_set:
                    self.session_files.append(pf)
                    existing_files_set.add(pf)
            
            # 清理旧的选中文件的代理映射，保留未被移除的文件的映射
            current_files_set_in_table = set(self.session_files)
            keys_to_remove_from_map = [k for k in self.session_proxy_map if k not in current_files_set_in_table]
            for k in keys_to_remove_from_map:
                old_proxy_cfg = self.session_proxy_map.pop(k, None)
                if old_proxy_cfg and old_proxy_cfg.get('type') == 'ip_pool':
                    self.unbind_ip(old_proxy_cfg.get('ip'), old_proxy_cfg.get('port'))

            # 确保新添加的文件被选中：强制设置全选状态为True
            # 这将使得 update_session_table 在重建时勾选所有行
            if processed_files: # 只有当确实有新文件被选择时才触发全选
                self.select_all_checkbox.setChecked(True)

            self.update_session_table()
            self._update_ip_labels_in_combos() 
            self.show_success_message("文件选择", f"已添加 {len(processed_files)} 个Session文件，当前列表共 {len(self.session_files)} 个")

    # ================ 表格相关 ================
    def update_session_table(self):
        """更新会话表格"""
        # 先清空表格内容
        self.session_table.setRowCount(0)
        
        self.select_all_checkbox.blockSignals(True) # 暂停全选按钮信号
        
        self.row_checkboxes.clear()
        self.row_proxy_combos.clear()
        self.row_ip_combos.clear()
        self.row_group_combos.clear()
        
        all_checked = self.select_all_checkbox.isChecked()
        
        for file_path in self.session_files:
            self._add_session_row(file_path, all_checked)
        
        # self.adjust_table_column_widths() # Base class sets resize modes
        self._update_select_all_checkbox_state() # 更新全选按钮状态
        self.select_all_checkbox.blockSignals(False) # 恢复全选按钮信号

    def _add_session_row(self, file_path, checked=True):
        """添加一行会话数据"""
        row = self.session_table.rowCount()
        self.session_table.insertRow(row)
        
        filename = Path(file_path).name
        
        # 0. 选择复选框
        checkbox_container = QWidget()
        chk_layout = QHBoxLayout(checkbox_container)
        chk_layout.setContentsMargins(0,0,0,0)
        chk_layout.setAlignment(Qt.AlignCenter)
        checkbox = CheckBox()
        checkbox.setChecked(checked)
        checkbox.stateChanged.connect(self._update_select_all_checkbox_state)
        chk_layout.addWidget(checkbox)
        self.row_checkboxes.append(checkbox)
        self.session_table.setCellWidget(row, 0, checkbox_container)
        
        # 1. Session名称
        item_filename = QTableWidgetItem(filename)
        item_filename.setData(Qt.UserRole, file_path) # 存储完整路径
        item_filename.setToolTip(file_path)
        self.session_table.setItem(row, 1, item_filename)
        
        # 2. 代理设置
        proxy_container = QWidget()
        proxy_layout = QHBoxLayout(proxy_container)
        proxy_layout.setContentsMargins(2,2,2,2) 
        
        proxy_combo = ComboBox()
        proxy_combo.addItems(["不使用代理", "系统代理", "IP池"])
        
        ip_combo = ComboBox()
        ip_combo.setVisible(False) # 初始隐藏
        
        proxy_layout.addWidget(proxy_combo, 1) # 权重1
        proxy_layout.addWidget(ip_combo, 2)    # 权重2, 更宽

        self.row_proxy_combos.append(proxy_combo)
        self.row_ip_combos.append(ip_combo)
        
        # 恢复或设置代理状态
        current_proxy_config = self.session_proxy_map.get(file_path)
        if current_proxy_config:
            proxy_type = current_proxy_config.get('type')
            if proxy_type == 'system':
                proxy_combo.setCurrentIndex(1)
            elif proxy_type == 'ip_pool':
                proxy_combo.setCurrentIndex(2)
                ip_combo.setVisible(True)
                self._populate_ip_combo_options(ip_combo)
                
                # Try to set current IP
                if current_proxy_config.get('ip') and current_proxy_config.get('port'):
                    target_ip = current_proxy_config.get('ip')
                    target_port = current_proxy_config.get('port')
                    found_idx = 0 # Default to "选择代理IP"
                    for i, p_item in enumerate(self.proxy_list):
                        if p_item.get('ip') == target_ip and p_item.get('port') == target_port:
                            found_idx = i + 1 # +1 because of placeholder "选择代理IP"
                            break
                    ip_combo.setCurrentIndex(found_idx)
                else:
                    ip_combo.setCurrentIndex(0) # Default to "选择代理IP"
            else: # no_proxy or unknown
                proxy_combo.setCurrentIndex(0)
        else: # No config for this file yet
            proxy_combo.setCurrentIndex(0) # 默认不使用代理
            self._populate_ip_combo_options(ip_combo) # Populate options even if none selected yet

        proxy_combo.currentIndexChanged.connect(
            lambda idx, r=row, f=file_path: self.on_proxy_type_changed(r, idx, f)
        )
        ip_combo.currentIndexChanged.connect(
            lambda idx, r=row, f=file_path: self.on_ip_selection_changed(r, idx, f)
        )
        self.session_table.setCellWidget(row, 2, proxy_container)
        
        # 3. 用户分组
        group_container = QWidget()
        grp_layout = QHBoxLayout(group_container)
        grp_layout.setContentsMargins(2,2,2,2)
        group_combo = ComboBox()
        group_combo.addItem(text="无分组", userData=-1, icon=None) # 默认项，值为-1
        for group_data in self.group_list: # group_list是 [{'id': ..., 'name': ...}, ...]
             group_combo.addItem(text=group_data.get('name', '未知分组'), userData=group_data.get('id'), icon=None)
        grp_layout.addWidget(group_combo)
        self.row_group_combos.append(group_combo)
        self.session_table.setCellWidget(row, 3, group_container)
        
        # 4. 状态
        status_item = QTableWidgetItem("待登录")
        status_item.setForeground(QBrush(QColor("#3296fa"))) # 蓝色
        status_item.setTextAlignment(Qt.AlignCenter)
        self.session_table.setItem(row, 4, status_item)

        self.session_table.resizeRowToContents(row)


    def _update_select_all_checkbox_state(self):
        """当单个复选框状态改变时，检查是否需要更新全选按钮"""
        if not self.row_checkboxes:
            self.select_all_checkbox.setChecked(False)
            return
            
        all_checked = all(cb.isChecked() for cb in self.row_checkboxes if cb)
        some_checked = any(cb.isChecked() for cb in self.row_checkboxes if cb)

        self.select_all_checkbox.blockSignals(True)
        if all_checked:
            self.select_all_checkbox.setCheckState(Qt.Checked)
        elif some_checked:
            self.select_all_checkbox.setCheckState(Qt.PartiallyChecked)
        else:
            self.select_all_checkbox.setCheckState(Qt.Unchecked)
        self.select_all_checkbox.blockSignals(False)

    def resizeEvent(self, event):
        """当对话框大小改变时，调整表格列宽"""
        super().resizeEvent(event)
        # self.adjust_table_column_widths() # Base class handles resize modes

    # def adjust_table_column_widths(self):
    #     """调整表格列宽 (如果需要手动调整)"""
    #     # self.session_table.horizontalHeader().setSectionResizeMode(0, QHeaderView.ResizeToContents)
    #     # self.session_table.horizontalHeader().setSectionResizeMode(1, QHeaderView.Stretch)
    #     # ... (其他列)
    #     pass


    # ================ 代理相关 ================
    def _populate_ip_combo_options(self, ip_combo: ComboBox):
        """填充指定IP池下拉框的选项"""
        ip_combo.clear()
        ip_combo.addItem("选择代理IP") # No user data, placeholder at index 0

        for proxy in self.proxy_list: # proxy_list from controller
            proxy_key = f"{proxy['ip']}:{proxy['port']}"
            binding_count = self.ip_binding_count.get(proxy_key, 0)
            # Display text includes IP, port, and binding count
            display_text = f"{proxy['ip']}:{proxy['port']} (已绑定:{binding_count})"
            ip_combo.addItem(display_text, userData=proxy) # Store the whole proxy dict

    def on_proxy_type_changed(self, row: int, combo_idx: int, session_file_path: str):
        """处理代理类型更改事件"""
        if row >= len(self.row_ip_combos) or row >= len(self.row_proxy_combos):
            return
        
        ip_combo = self.row_ip_combos[row]
        
        ip_combo.blockSignals(True) # 阻断IP下拉框信号
        
        old_proxy_config = self.session_proxy_map.get(session_file_path)
        if old_proxy_config and old_proxy_config.get('type') == 'ip_pool':
            self.unbind_ip(old_proxy_config.get('ip'), old_proxy_config.get('port'))

        if combo_idx == 2:  # IP池
            ip_combo.setVisible(True)
            self._populate_ip_combo_options(ip_combo) # Pass only ip_combo
            # 默认不自动选择IP，让用户选择
            ip_combo.setCurrentIndex(0) # "选择代理IP"
            if session_file_path in self.session_proxy_map: # 如果之前有绑定，先清除
                del self.session_proxy_map[session_file_path]
        else:
            ip_combo.setVisible(False)
            ip_combo.setCurrentIndex(-1) # 清除选择
            if combo_idx == 1:  # 系统代理
                self.session_proxy_map[session_file_path] = {'type': 'system'}
            else:  # 无代理 (combo_idx == 0)
                if session_file_path in self.session_proxy_map:
                    del self.session_proxy_map[session_file_path]
        
        self._update_ip_labels_in_combos() # 更新所有IP池标签，因为一个IP的绑定状态可能改变
        ip_combo.blockSignals(False)

    def on_ip_selection_changed(self, row: int, combo_idx: int, session_file_path: str):
        """处理IP选择更改事件"""
        if row >= len(self.row_ip_combos) or row >= len(self.row_proxy_combos):
            return

        ip_combo = self.row_ip_combos[row]
        proxy_combo = self.row_proxy_combos[row]

        # 确保代理类型是IP池
        if proxy_combo.currentIndex() != 2:
            return
        
        old_proxy_config = self.session_proxy_map.get(session_file_path)
        if old_proxy_config and old_proxy_config.get('type') == 'ip_pool':
            self.unbind_ip(old_proxy_config.get('ip'), old_proxy_config.get('port'))
        
        # 获取新选择的IP数据 from userData
        selected_proxy_data = ip_combo.currentData() # This will be the full proxy dict or None

        if selected_proxy_data and selected_proxy_data.get('ip'): # valid IP selected (currentData is not None)
            # 检查 selected_proxy_data 是否是我们期望的字典格式
            if not isinstance(selected_proxy_data, dict):
                self._logger.warning(f"IP Combo userData for {session_file_path} is not a dict: {selected_proxy_data}")
                if session_file_path in self.session_proxy_map:
                    if self.session_proxy_map[session_file_path].get('type') == 'ip_pool':
                        del self.session_proxy_map[session_file_path]
                self._update_ip_labels_in_combos()
                return

            new_config = {
                'id': selected_proxy_data.get('id'), # Add database ID of the proxy
                'type': 'ip_pool', # Meta-type indicating it's from the pool
                'proxy_type': selected_proxy_data.get('proxy_type'), # Actual proxy protocol
                'ip': selected_proxy_data.get('ip'),
                'port': selected_proxy_data.get('port'),
                'username': selected_proxy_data.get('username', ''),
                'password': selected_proxy_data.get('password', '')
            }
            self.session_proxy_map[session_file_path] = new_config
            self.bind_ip(new_config.get('ip'), new_config.get('port'))
        else: # 选择了 "选择代理IP" (currentData is None for the placeholder) or invalid data
            if session_file_path in self.session_proxy_map:
                if self.session_proxy_map[session_file_path].get('type') == 'ip_pool':
                    del self.session_proxy_map[session_file_path]

        self._update_ip_labels_in_combos()

    def bind_ip(self, ip, port):
        """增加IP绑定计数"""
        if not ip or not port: return
        ip_key = f"{ip}:{port}"
        self.ip_binding_count[ip_key] = self.ip_binding_count.get(ip_key, 0) + 1
    
    def unbind_ip(self, ip, port):
        """减少IP绑定计数"""
        if not ip or not port: return
        ip_key = f"{ip}:{port}"
        if ip_key in self.ip_binding_count:
            self.ip_binding_count[ip_key] = max(0, self.ip_binding_count.get(ip_key, 0) - 1)

    def _update_ip_labels_in_combos(self):
        """更新所有IP下拉框的显示文本（绑定数）"""
        for r_idx, ip_combo in enumerate(self.row_ip_combos):
            if r_idx >= len(self.session_files): continue
            
            current_selected_index = ip_combo.currentIndex() # Preserve current selection by index

            ip_combo.blockSignals(True)
            
            # Update texts for items starting from index 1
            # Assumes ip_combo items (after placeholder) correspond to self.proxy_list
            for i in range(1, ip_combo.count()): 
                if (i - 1) < len(self.proxy_list):
                    proxy_item = self.proxy_list[i-1] 
                    proxy_key = f"{proxy_item['ip']}:{proxy_item['port']}"
                    binding_count = self.ip_binding_count.get(proxy_key, 0)
                    base_text = f"{proxy_item['ip']}:{proxy_item['port']}"
                    ip_combo.setItemText(i, f"{base_text} (已绑定:{binding_count})")

            # Restore selection by index
            if 0 <= current_selected_index < ip_combo.count():
                 ip_combo.setCurrentIndex(current_selected_index)
            else: 
                 # If old selection is invalid (e.g. proxy list changed), try to restore from map or default to 0
                 restored_idx = 0 # Default to "选择代理IP"
                 session_file_path_for_row = self.session_files[r_idx]
                 mapped_proxy = self.session_proxy_map.get(session_file_path_for_row)
                 if mapped_proxy and mapped_proxy.get('type') == 'ip_pool':
                     target_ip = mapped_proxy.get('ip')
                     target_port = mapped_proxy.get('port')
                     for i_proxy, p_item in enumerate(self.proxy_list):
                         if p_item.get('ip') == target_ip and p_item.get('port') == target_port:
                             restored_idx = i_proxy + 1 # +1 for placeholder
                             break
                 ip_combo.setCurrentIndex(restored_idx)
                 
            ip_combo.blockSignals(False)

    @asyncSlot()
    async def refresh_proxy_list_async(self):
        """异步刷新代理列表"""
        self._logger.info("开始刷新代理列表...")
        if not self.account_controller:
            self.show_error_message("错误", "账号控制器未初始化")
            return
        
        # 可选：显示加载指示
        # loading_indicator = IndeterminateProgressRing(self)
        # loading_indicator.show()

        try:
            proxies = await self.account_controller.get_proxy_ips() # 假设方法
            if proxies is not None: # Controller should return [] on success no proxies, None on error
                self.proxy_list = proxies
                # 重置绑定计数，基于controller返回的account_count（如果提供）
                self.ip_binding_count.clear()
                for p in self.proxy_list:
                    key = f"{p['ip']}:{p['port']}"
                    # 假设 controller 返回的代理信息中包含 'account_count'
                    self.ip_binding_count[key] = p.get('account_count', 0) 
                
                # 更新已绑定到session的代理计数，如果它们仍在列表中
                # 清理 session_proxy_map 中无效的代理
                valid_proxy_keys = {f"{p['ip']}:{p['port']}" for p in self.proxy_list}
                for file_path, config in list(self.session_proxy_map.items()): # Iterate over a copy
                    if config.get('type') == 'ip_pool':
                        proxy_key = f"{config['ip']}:{config['port']}"
                        if proxy_key not in valid_proxy_keys:
                            # 该代理已不存在，从映射中移除
                            del self.session_proxy_map[file_path]
                        # else: # 代理仍然有效，其绑定计数已由上面初始化
                        #    pass
                
                self.update_session_table() # 重建表格会重新填充和设置IP下拉框
                self._update_ip_labels_in_combos() # 确保标签是最新的
                self.show_success_message("代理更新", f"已更新 {len(self.proxy_list)} 个代理")
            else:
                self.show_error_message("代理更新失败", "未能获取到代理数据")
        except Exception as e:
            self._logger.error(f"刷新代理列表失败: {e}", exc_info=True)
            self.show_error_message("代理更新异常", str(e))
        # finally:
            # loading_indicator.close()
            # loading_indicator.deleteLater()

    @asyncSlot()
    async def refresh_groups_list_async(self):
        """异步刷新分组列表"""
        self._logger.info("开始刷新分组列表...")
        if not self.account_controller:
            self.show_error_message("错误", "账号控制器未初始化")
            return
        try:
            groups = await self.account_controller.get_all_groups() # 假设方法
            if groups is not None:
                self.group_list = groups
                self.update_session_table() # 重建表格以更新分组下拉框
                self.show_success_message("分组更新", f"已更新 {len(self.group_list)} 个分组")
            else:
                self.show_error_message("分组更新失败", "未能获取到分组数据")
        except Exception as e:
            self._logger.error(f"刷新分组列表失败: {e}", exc_info=True)
            self.show_error_message("分组更新异常", str(e))

    def toggle_select_all(self, state_or_int):
        """处理全选/取消全选复选框的状态改变"""
        # Qt.CheckState enum or int from stateChanged
        if isinstance(state_or_int, Qt.CheckState):
            is_checked = (state_or_int == Qt.Checked)
        else: # int
            is_checked = (state_or_int == Qt.Checked.value)

        for checkbox in self.row_checkboxes:
            if checkbox:
                checkbox.blockSignals(True)
                checkbox.setChecked(is_checked)
                checkbox.blockSignals(False)
        
        # 如果是用户点击全选框导致取消全选（变为Unchecked），确保所有子项都取消
        if not is_checked and self.sender() == self.select_all_checkbox:
             self.select_all_checkbox.setCheckState(Qt.Unchecked)


    @asyncSlot()
    async def login_selected_sessions_async(self):
        """收集选中的会话信息并开始登录"""
        selected_sessions_data = []
        
        for i, checkbox in enumerate(self.row_checkboxes):
            if checkbox and checkbox.isChecked() and i < len(self.session_files):
                session_file_path = self.session_files[i]
                group_combo = self.row_group_combos[i]
                
                proxy_config = self.session_proxy_map.get(session_file_path)
                
                group_id = group_combo.currentData() if group_combo else -1
                try: # Ensure group_id is int
                    group_id = int(group_id)
                except (ValueError, TypeError):
                    group_id = -1 
                
                # 获取原始行号，用于后续状态更新
                original_row_index = i

                selected_sessions_data.append({
                    'session_file': session_file_path,
                    'proxy_config': proxy_config, # Can be None
                    'group_id': group_id,
                    'row_index': original_row_index # Store row index for status updates
                })
        
        max_concurrent = int(self.max_concurrent_combo.currentText())
        
        if not selected_sessions_data:
            self.show_warning_message("无选择", "请至少选择一个Session文件进行登录")
            return
            
        confirm_dialog = MessageBox(
            "确认导入并登录",
            f"将尝试登录 {len(selected_sessions_data)} 个Session，最大并发数 {max_concurrent}。\n确定继续吗？",
            self
        )
        
        if confirm_dialog.exec():
            self._logger.info(f"开始导入并登录 {len(selected_sessions_data)} 个会话，并发数: {max_concurrent}，文件: {selected_sessions_data}")
            # 禁用部分UI
            self.login_button.setEnabled(False)
            self.import_button.setEnabled(False)
      

            if self.account_controller:
                try:
                    # 假设controller的方法会处理这些session
                    # 并通过回调更新状态
                    await self.account_controller.batch_import_sessions(
                        selected_sessions_data, 
                        max_concurrent
                    )
                    self.show_success_message("操作完成", "所有选中的Session已处理完毕。")
                except Exception as e:
                    self._logger.error(f"批量登录会话时发生错误: {e}", exc_info=True)
                    self.show_error_message("登录失败", f"处理过程中发生错误: {str(e)}")
                finally:
                    # 重新启用UI
                    self.login_button.setEnabled(True)
                    self.import_button.setEnabled(True)
            else:
                self.show_error_message("错误", "账号控制器未初始化")
                self.login_button.setEnabled(True)
                self.import_button.setEnabled(True)
            # self.accept() # 通常在任务完成后关闭，但这里可能是长任务，先不关闭

    def _handle_batch_import_summary(self, success_count: int, fail_count: int, message: str):
        """处理批量导入的最终摘要信息"""
        self._logger.info(f"批量导入摘要: {message}")
        # 此处可以显示一个总结性的 InfoBar
        # 例如，如果所有都成功了
        if fail_count == 0 and success_count > 0:
             self.show_success_message("批量导入完成", message)
        elif success_count > 0 and fail_count > 0:
             self.show_warning_message("批量导入部分完成", message)
        elif success_count == 0 and fail_count > 0:
             self.show_error_message("批量导入失败", message)
        # else (no items processed) - 可能是 "没有有效的session文件进行导入"

    def _handle_controller_notification(self, event: str, data: object, info_type: str):
        """处理来自账户控制器的通知信号"""
        self._logger.debug(f"ImportSessionsView 收到通知: event='{event}', type='{info_type}', data='{data}'")
        
        prefix = "导入通知_"
        if event.startswith(prefix):
            try:
                row_index_str = event[len(prefix):]
                row_index = int(row_index_str)

                if 0 <= row_index < self.session_table.rowCount():
                    status_message = ""
                    color_hex = "#000000" # Default black
                    
                    is_successful_import = False
                    imported_account_data = None

                    if isinstance(data, dict):
                        status_message = data.get("message", "未知状态")
                        is_successful_import = data.get("success", False)
                        if is_successful_import and "account_data" in data:
                            imported_account_data = data["account_data"]

                    if info_type == "success":
                        color_hex = "#28a745" # Green
                    elif info_type == "error":
                        color_hex = "#dc3545" # Red
                    elif info_type == "warning":
                        color_hex = "#ffc107" # Yellow
                    elif info_type == "info":
                        color_hex = "#17a2b8" # Blue
                    
                    # 更新状态列
                    self.update_session_status_callback(row_index, status_message, color_hex)

                    # 如果成功并且有账户数据，更新其他列
                    if is_successful_import and imported_account_data:
                        # 更新 Session 名称列 (第1列) - 例如，使用手机号或用户名
                        session_name_item = self.session_table.item(row_index, 1)
                        if session_name_item:
                            display_name = imported_account_data.get('phone') or imported_account_data.get('username') or Path(imported_account_data.get('session_file', '')).name
                            session_name_item.setText(display_name)
                            session_name_item.setToolTip(f"ID: {imported_account_data.get('id')}\nPhone: {imported_account_data.get('phone', 'N/A')}\nUsername: {imported_account_data.get('username', 'N/A')}")
                        
                        # 你可以在这里更新其他相关列，如果你的表格设计包含它们的话
                        # 例如，如果有一个"用户名"列，你可以这样填充：
                        # username_item = QTableWidgetItem(imported_account_data.get('username', 'N/A'))
                        # self.session_table.setItem(row_index, YOUR_USERNAME_COLUMN_INDEX, username_item)

                else:
                    self._logger.warning(f"行索引 {row_index} 超出范围。")

            except ValueError:
                self._logger.warning(f"无法从事件 '{event}' 中解析行索引。")
            except Exception as e:
                self._logger.error(f"处理通知时发生错误: {e}", exc_info=True)
        # else:
            # self._logger.debug(f"接收到非导入通知事件: {event}")


    def update_session_status_callback(self, row_index: int, status_message: str, color_hex: str = "#000000", is_processing: bool = False):
        """
        回调函数，用于更新表格中特定行的状态。
        row_index: 在 self.session_files 中的索引，也对应表格的行号。
        status_message: 要显示的状态文本。
        color_hex: 状态文本的颜色。
        is_processing: 是否正在处理中 (例如，显示一个加载动画)。
        """
        if 0 <= row_index < self.session_table.rowCount():
            status_item = self.session_table.item(row_index, 4) # 第4列是状态
            if not status_item:
                status_item = QTableWidgetItem()
                status_item.setTextAlignment(Qt.AlignCenter)
                self.session_table.setItem(row_index, 4, status_item)
            
            status_item.setText(str(status_message))
            status_item.setForeground(QBrush(QColor(color_hex)))

            # 如果有更复杂的加载指示器，可以在这里处理
            # 例如，在状态单元格中放置一个 IndeterminateProgressRing
            # if is_processing:
            #     ring = IndeterminateProgressRing()
            #     # ... set size, etc.
            #     self.session_table.setCellWidget(row_index, 4, ring) # This replaces the item
            # else:
            #     self.session_table.removeCellWidget(row_index, 4) # Remove widget to show item again

            QCoreApplication.processEvents() # 确保UI更新


