from PySide6.QtCore import Slot, Signal, QDateTime
from PySide6.QtWidgets import QFileDialog, QMessageBox
from qfluentwidgets import InfoBar, InfoBarPosition,FluentIcon
from PySide6.QtCore import Qt
from qasync import asyncSlot
from ui.designer.send_message_ui import TaskCardUI, SendMessageWidgetUI
from ui.views.add_invite_task_view import AddInviteTaskView
from app.controllers.invite_controller import InviteController
from app.controllers.account_controller import AccountController
from utils.logger import get_logger

class TaskCard(TaskCardUI):
    """任务卡片组件"""
    
    # 自定义信号
    view_task_signal = Signal(str)  # 查看任务信号，参数为任务ID
    pause_task_signal = Signal(str, bool)  # 暂停/开始任务信号，参数为任务ID和是否暂停
    delete_task_signal = Signal(str)  # 删除任务信号，参数为任务ID
    
    def __init__(self, task_id, task_name, recipients, status, progress, parent=None):
        super().__init__(parent)
        self.task_id = task_id
        
        # 初始化UI组件数据
        self.update_ui_data(task_name, recipients, status, progress)
        
        # 设置状态数量的默认值
        self.update_status_counts(5, 2, 10)  # 默认：5成功，2失败，10待邀
        
        # 连接信号槽
        self.view_button.setIcon(FluentIcon.VIEW)
        self.pause_button.setIcon(FluentIcon.PAUSE)
        self.delete_button.setIcon(FluentIcon.DELETE)
        self.view_button.clicked.connect(self._on_view_clicked)
        self.pause_button.clicked.connect(self._on_pause_clicked)
        self.delete_button.clicked.connect(self._on_delete_clicked)
    
    def update_status_counts(self, success_count, failed_count, pending_count):
        """更新状态数量"""
        self.success_count.setText(f"成功 {success_count}")
        self.failed_count.setText(f"失败 {failed_count}")
        self.pending_count.setText(f"待邀 {pending_count}")
    
    def update_ui_data(self, task_name, recipients, status, progress):
        """更新UI组件数据"""
        # 设置数据
        self.name_label.setText(task_name)
        self.id_label.setText(f"#{self.task_id}")
        self.status_label.setText(status)
        self.recipients_label.setText(f"收件人: {recipients}")
        self.progress_bar.setValue(progress)
        self.progress_label.setText(f"{progress}%")
        
        # 设置状态样式
        if status == "运行中":
            self.status_label.setProperty("status", "running")
            self.pause_button.setText("暂停")
        elif status == "已完成":
            self.status_label.setProperty("status", "completed")
            self.pause_button.setText("开始")
        elif status == "失败":
            self.status_label.setProperty("status", "failed")
            self.pause_button.setText("重试")
        elif status == "等待中":
            self.status_label.setProperty("status", "pending")
            self.pause_button.setText("开始")
        elif status == "已暂停":
            self.status_label.setProperty("status", "paused")
            self.pause_button.setText("开始")
        
        # 重新应用样式表以更新状态样式
        self.status_label.style().unpolish(self.status_label)
        self.status_label.style().polish(self.status_label)
    
    def update_progress(self, progress):
        """更新任务进度"""
        self.progress_bar.setValue(progress)
        self.progress_label.setText(f"{progress}%")
    
    def update_status(self, status):
        """更新任务状态"""
        self.status_label.setText(status)
        
        # 更新按钮文本
        if status == "运行中":
            self.status_label.setProperty("status", "running")
            self.pause_button.setText("暂停")
        elif status == "已完成":
            self.status_label.setProperty("status", "completed")
            self.pause_button.setText("开始")
        elif status == "失败":
            self.status_label.setProperty("status", "failed")
            self.pause_button.setText("重试")
        elif status == "等待中":
            self.status_label.setProperty("status", "pending")
            self.pause_button.setText("开始")
        elif status == "已暂停":
            self.status_label.setProperty("status", "paused")
            self.pause_button.setText("开始")
        
        # 重新应用样式表以更新状态样式
        self.status_label.style().unpolish(self.status_label)
        self.status_label.style().polish(self.status_label)
    
    @Slot()
    def _on_view_clicked(self):
        """查看按钮点击事件"""
        self.view_task_signal.emit(self.task_id)
    
    @Slot()
    def _on_pause_clicked(self):
        """暂停/开始按钮点击事件"""
        status = self.status_label.text()
        if status == "运行中":
            # 运行中点击暂停
            self.pause_task_signal.emit(self.task_id, True)
        else:
            # 其他状态点击开始/重试
            self.pause_task_signal.emit(self.task_id, False)
    
    @Slot()
    def _on_delete_clicked(self):
        """删除按钮点击事件"""
        self.delete_task_signal.emit(self.task_id)


class InviteView(SendMessageWidgetUI):
    """消息群发任务管理页"""
    
    # 添加创建任务信号
    create_task_signal = Signal()
    
    def __init__(self, parent=None, controller:InviteController =None,accountController:AccountController =None):
        super().__init__(parent)
        self.logger = get_logger("ui.views.invite_view")
        self.invite_controller = controller
        self.accountController = accountController
        
        # 连接信号槽
        self.new_task_button.clicked.connect(self._on_create_task_clicked)
        self.refresh_button.clicked.connect(self._on_refresh_tasks)
        self.search_input.textChanged.connect(self._on_search_changed)
        self.status_combo.currentIndexChanged.connect(self._on_status_filter_changed)
        
        # 创建添加任务视图实例但不显示
        self.add_invite_task_view = None
        
        # 初始化任务列表
        self._refresh_tasks()
        
        if self.invite_controller:
            self.invite_controller.task_stats_updated.connect(self._on_task_stats_updated)
            self.invite_controller.task_progress_updated.connect(self._on_task_progress_updated)
            self._on_task_initialize()
            
    @asyncSlot()
    async def _on_task_initialize(self):
        """任务初始化"""
        await self.invite_controller.task_initialize()
        
    @Slot()
    def _on_create_task_clicked(self):
        """创建新任务按钮点击事件，打开添加任务页面"""
        self.logger.info("点击创建新任务按钮")
        # 创建添加任务视图实例（避免多次点击创建多个实例）
        if not self.add_invite_task_view or not self.add_invite_task_view.isVisible():
            self.add_invite_task_view = AddInviteTaskView(parent=self,account_controller=self.accountController,
                                                          invite_controller=self.invite_controller)
            # 连接任务保存信号
            self.add_invite_task_view.taskSaved.connect(self._on_task_saved)
        
        # 显示添加任务页面
        self.add_invite_task_view.exec()
        
        # 发出创建任务信号（保持原有逻辑）
        self.create_task_signal.emit()
    
    @Slot(dict)
    @asyncSlot()
    async def _on_task_saved(self, result):
        """处理任务保存事件"""
        self.logger.info(f"任务已保存: {result}")
        try:
            # 调用控制器创建任务
            if self.invite_controller:
                self.show_info("创建成功", "任务创建成功！", "success")
                # 刷新任务列表
                await self._refresh_tasks()
            else:
                self.logger.warning("控制器未初始化，无法创建任务")
                self.show_info("警告", "系统内部错误，请稍后重试。", "warning")
        except Exception as e:
            self.logger.error(f"创建任务失败: {str(e)}")
            self.show_info("错误", f"创建任务失败: {str(e)}", "error")
    
    @asyncSlot()
    async def _refresh_tasks(self):
        """刷新任务列表"""
        # 清空任务容器
        for i in range(self.task_container_layout.count()):
            item = self.task_container_layout.itemAt(0)
            if item and item.widget():
                widget = item.widget()
                self.task_container_layout.removeWidget(widget)
                widget.deleteLater()
        
        try:
            # 获取任务列表
            status_filter = None
            if self.status_combo.currentIndex() > 0:
                status_map = {
                    1: "pending",
                    2: "running",
                    3: "completed",
                    4: "failed",
                    5: "paused"
                }
                status_filter = status_map.get(self.status_combo.currentIndex())
            
            tasks = await self.invite_controller.get_invite_tasks(status_filter)
            
            if not tasks:
                self.logger.info("没有找到任务")
                return
            
            for task in tasks:
                # 转换状态文本
                status_map = {
                    "pending": "等待中",
                    "running": "运行中",
                    "completed": "已完成",
                    "failed": "失败",
                    "paused": "已暂停"
                }
                status = status_map.get(task.get("status", "pending"), "等待中")
                
                # 获取统计数据
                stats = task.get("stats", {})
                success_count = stats.get("success", 0)
                failed_count = stats.get("failed", 0)
                pending_count = stats.get("pending", 0)
                total_count = success_count + failed_count + pending_count
                
                # 重新计算任务进度，基于已处理的用户比例
                progress = 0
                if total_count > 0:
                    progress = int((success_count + failed_count) / total_count * 100)
                
                # 创建任务卡片
                task_card = TaskCard(
                    str(task.get("id", "")),
                    task.get("task_name", "未命名任务"),
                    f"{total_count} 人",
                    status,
                    progress
                )
                
                # 设置状态数量
                task_card.update_status_counts(success_count, failed_count, pending_count)
                
                # 确保进度条和百分比正确显示
                task_card.update_progress(progress)
                
                # 连接自定义信号
                task_card.view_task_signal.connect(self._on_view_task)
                task_card.pause_task_signal.connect(self._on_pause_task)
                task_card.delete_task_signal.connect(self._on_delete_task)
                
                self.task_container_layout.insertWidget(self.task_container_layout.count() - 1, task_card)
                
        except Exception as e:
            self.logger.error(f"获取任务列表失败: {str(e)}")
            self.show_info("错误", f"获取任务列表失败: {str(e)}", "error")
            # 使用示例数据
            #self._init_sample_tasks()
    
    def _init_sample_tasks(self):
        """初始化示例任务数据"""
        # 清空任务容器
        for i in range(self.task_container_layout.count()):
            item = self.task_container_layout.itemAt(0)
            if item and item.widget():
                widget = item.widget()
                self.task_container_layout.removeWidget(widget)
                widget.deleteLater()
        
        # 添加示例任务卡片
        for i in range(5):
            status = ["运行中", "等待中", "已完成", "失败", "已暂停"][i % 5]
            
            # 设置不同的状态数量
            success = i * 5 + 2
            failed = i + 1
            pending = 50 + i * 20 - success - failed
            total = success + failed + pending
            
            # 计算进度
            progress = int((success + failed) / total * 100) if total > 0 else 0
            
            task_card = TaskCard(
                f"TK{1000+i}",
                f"营销活动 #{i+1}",
                f"{total} 人",
                status,
                progress
            )
            
            # 设置状态数量
            task_card.update_status_counts(success, failed, pending)
            
            # 确保进度条和百分比正确显示
            task_card.update_progress(progress)
            
            # 连接自定义信号
            task_card.view_task_signal.connect(self._on_view_task)
            task_card.pause_task_signal.connect(self._on_pause_task)
            task_card.delete_task_signal.connect(self._on_delete_task)
            
            self.task_container_layout.insertWidget(self.task_container_layout.count() - 1, task_card)
    
    @Slot()
    @asyncSlot()
    async def _on_refresh_tasks(self):
        """刷新任务列表按钮点击事件"""
        await self._refresh_tasks()
    
    @Slot(str)
    @asyncSlot()
    async def _on_search_changed(self, text):
        """搜索框文本变更事件"""
        # 这里可以添加搜索任务逻辑
        await self._refresh_tasks()
    
    @Slot(int)
    @asyncSlot()
    async def _on_status_filter_changed(self, index):
        """状态筛选下拉框变更事件"""
        await self._refresh_tasks()
    
    @Slot(str)
    @asyncSlot()
    async def _on_view_task(self, task_id):
        """查看任务事件"""
        self.logger.info(f"查看任务: {task_id}")
        try:
            # 获取任务详情
            task = await self.invite_controller.get_invite_task(int(task_id))
            if not task:
                self.show_info("查看失败", "找不到指定任务", "warning")
                return
            # 创建并显示任务详情视图
            if not self.add_invite_task_view or not self.add_invite_task_view.isVisible():
                self.add_invite_task_view = AddInviteTaskView(parent=self, account_controller=self.accountController, 
                                                             invite_controller=self.invite_controller)
                self.add_invite_task_view.taskSaved.connect(self._on_task_saved)
            
            # 转换数据格式，确保与InviteTask模型字段匹配
            view_data = {
                "task_name": task.get("task_name"),
                "group_name": task.get("group_name"),
                "group_invite_link": task.get("group_invite_link"),
                "invite_method": task.get("invite_method", "direct"),
                "message": task.get("message"),
                "invite_type": task.get("invite_type", "custom"),
                "invite_interval_min": task.get("invite_interval_min", 60),
                "invite_interval_max": task.get("invite_interval_max", 300),
                "batch_size_min": task.get("batch_size_min", 1),
                "batch_size_max": task.get("batch_size_max", 5),
                "accounts": task.get("accounts", []),
                "users": task.get("users", [])
            }
            
            # 加载任务数据
            self.add_invite_task_view.loadTask(view_data)
            self.add_invite_task_view.show()
        except Exception as e:
            self.logger.error(f"查看任务详情失败: {str(e)}")
            self.show_info("错误", f"查看任务详情失败: {str(e)}", "error")
    
    @Slot(str, bool)
    @asyncSlot()
    async def _on_pause_task(self, task_id, pause):
        """暂停/开始任务事件"""
        try:
            if pause:
                self.logger.info(f"暂停任务: {task_id}")
                success = await self.invite_controller.stop_invite_task(int(task_id))
                if success:
                    # 更新任务卡片状态
                    for i in range(self.task_container_layout.count()):
                        item = self.task_container_layout.itemAt(i)
                        if item and item.widget():
                            widget = item.widget()
                            if isinstance(widget, TaskCard) and widget.task_id == task_id:
                                widget.update_status("已暂停")
                                break
                    self.show_info("成功", "任务已暂停", "success")
                else:
                    self.show_info("警告", "暂停任务失败，可能任务已经停止或不存在", "warning")
            else:
                self.logger.info(f"开始任务: {task_id}")
                success = await  self.invite_controller.start_invite_task(int(task_id))
                if success:
                    # 更新任务卡片状态
                    for i in range(self.task_container_layout.count()):
                        item = self.task_container_layout.itemAt(i)
                        if item and item.widget():
                            widget = item.widget()
                            if isinstance(widget, TaskCard) and widget.task_id == task_id:
                                widget.update_status("运行中")
                                break
                    self.show_info("成功", "任务已开始", "success")
                else:
                    self.show_info("警告", "开始任务失败，可能任务已经在运行或已完成", "warning")
        except Exception as e:
            self.logger.error(f"暂停/开始任务失败: {str(e)}")
            self.show_info("错误", f"操作失败: {str(e)}", "error")
    
    @Slot(str)
    @asyncSlot()
    async def _on_delete_task(self, task_id):
        """删除任务事件"""
        self.logger.info(f"删除任务: {task_id}")
        # 确认删除
        reply = QMessageBox.question(
            self, 
            "确认删除", 
            f"确定要删除任务 #{task_id} 吗？\n此操作无法撤销。",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        if reply != QMessageBox.Yes:
            return
        try:
            success, msg = await self.invite_controller.delete_invite_task(int(task_id))
            if success:
                # 从界面中移除任务卡片
                for i in range(self.task_container_layout.count()):
                    item = self.task_container_layout.itemAt(i)
                    if item and item.widget():
                        widget = item.widget()
                        if isinstance(widget, TaskCard) and widget.task_id == task_id:
                            self.task_container_layout.removeWidget(widget)
                            widget.deleteLater()
                            break
                self.show_info("成功", msg, "success")
            else:
                self.show_info("警告", msg, "warning")
        except Exception as e:
            self.logger.error(f"删除任务失败: {str(e)}")
            self.show_info("错误", f"删除任务失败: {str(e)}", "error")
    
    def _on_task_stats_updated(self, task_id, success, failed, pending):
        """实时更新任务卡片的状态数量"""
        for i in range(self.task_container_layout.count()):
            item = self.task_container_layout.itemAt(i)
            if item and item.widget():
                widget = item.widget()
                if hasattr(widget, 'task_id') and str(widget.task_id) == str(task_id):
                    widget.update_status_counts(success, failed, pending)
                    break
    
    def _on_task_progress_updated(self, task_id, progress, status):
        """实时更新任务进度"""
        for i in range(self.task_container_layout.count()):
            item = self.task_container_layout.itemAt(i)
            if item and item.widget():
                widget = item.widget()
                if hasattr(widget, 'task_id') and str(widget.task_id) == str(task_id):
                    widget.update_progress(progress)
                    # 如果状态有变化，也更新状态
                    if status == "running":
                        widget.update_status("运行中")
                    elif status == "completed":
                        widget.update_status("已完成")
                    break
    
    def show_info(self, title, content, type='info', position = InfoBarPosition.TOP):
        """显示消息提示"""
        
        
        if type == 'success':
            InfoBar.success(
                title=title,
                content=content,
                orient=Qt.Horizontal,
                isClosable=True,
                position=position,
                duration=2000,
                parent=self
            )
        elif type == 'error':
            InfoBar.error(
                title=title,
                content=content,
                orient=Qt.Horizontal,
                isClosable=True,
                position=position,
                duration=3000,
                parent=self
            )
        elif type == 'warning':
            InfoBar.warning(
                title=title,
                content=content,
                orient=Qt.Horizontal,
                isClosable=True,
                position=position,
                duration=3000,
                parent=self
            )
        else:  # 默认为 info
            InfoBar.info(
                title=title,
                content=content,
                orient=Qt.Horizontal,
                isClosable=True,
                position=position,
                duration=2000,
                parent=self
            ) 
