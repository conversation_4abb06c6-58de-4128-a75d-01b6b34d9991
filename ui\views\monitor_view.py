#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
监控视图
实现监控任务的UI界面
"""

import datetime
from typing import Dict, Any, List, Optional, Callable, Union

from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, QTableWidget, 
    QTableWidgetItem, QHeaderView, QComboBox, QLineEdit, QTextEdit, 
    QCheckBox, QTabWidget, QSplitter, QDialog, QDialogButtonBox, 
    QFormLayout, QSpinBox, QMessageBox, QMenu, QFrame, QFileDialog
)
from PySide6.QtGui import QIcon, QColor, QBrush, QFont, QAction
from PySide6.QtCore import Qt, QSize, QTimer, Signal, Slot
from qfluentwidgets import InfoBar, InfoBarPosition, Tool<PERSON>utton, StrongBody<PERSON>abe<PERSON>, FluentIcon as FIF

from app.controllers.account_controller import Account<PERSON>ontroller
from ui.designer.msg_monitor_ui import TelegramMonitorPage, TaskItemWidget
from ui.views.add_monitor_view import AddMsgMonitorTask
from app.controllers.telegram_monitor_controller import TelegramMonitorController

from utils.logger import get_logger
from qasync import asyncSlot


class MonitorView(TelegramMonitorPage):
    """
    监控任务管理主视图
    负责展示监控任务列表、任务详情、用户统计数据等
    """
    
    def __init__(self,controller:TelegramMonitorController ,
                 account_controller:AccountController , parent=None):
        """
        初始化监控视图
        
        Args:
            parent: 父窗口
        """
        super().__init__(parent)
        self.setObjectName("MonitorView")
        self._logger = get_logger("ui.views.monitor_view")
        self._logger.info("初始化监控视图")
        
        # 创建控制器
        self._controller = controller
        self._account_controller = account_controller
        # 初始化UI
       
        
        # 连接信号和槽
        self._connect_signals()
        
        # 初始加载任务
        
        self._controller.load_tasks()
        
    def _setup_ui(self):
        """设置UI组件"""
        self._logger.debug("设置UI组件")
        
        # 禁用依赖于任务选择的按钮
        self.deleteTaskButton.setEnabled(False)
        
        # 设置分页控件的初始状态
        self.prevPageButton.setEnabled(False)
        self.nextPageButton.setEnabled(False)
        self.pageComboBox.setEnabled(False)
        
        # 初始化日志区域
        self._append_log("欢迎使用 Telegram 消息监控系统", "info")
        
    def _connect_signals(self):
        """连接控制器信号和视图槽函数"""
        self._logger.debug("连接控制器信号")
        
        if self._controller:
            self._controller.loading_started.connect(self._on_loading_started)
            self._controller.loading_finished.connect(self._on_loading_finished)
            self._controller.operation_success.connect(self._show_success_message_from_controller)
            self._controller.operation_failed.connect(self._show_error_message_from_controller)
            
            # 任务列表相关信号
            self._controller.tasks_loaded.connect(self._refresh_task_list)
            self._controller.task_added.connect(self._on_task_operation_completed)
            self._controller.task_updated.connect(self._on_task_operation_completed)
            self._controller.task_deleted.connect(self._on_task_deleted)
            
            # 详情页面相关信号
            self._controller.task_details_loaded.connect(self._on_task_details_loaded)
            self._controller.task_users_loaded.connect(self._update_users_table)
            
            # 全部用户数据信号 - 确保正确连接
            self._controller.all_users_loaded.connect(self._update_users_table)
            
            self._controller.task_selection_changed.connect(self._on_task_selection_changed)
            
            # 总用户数量统计信号
            self._controller.total_users_count_loaded.connect(self._update_stats_cards)
            
            # 监听日志信号
            self._controller.log_received.connect(self._append_log)
        
        # 日志信号
   
        # 视图 -> 控制器信号连接
        # 任务管理按钮
        self.addTaskButton.clicked.connect(self.open_add_task_dialog)
        self.deleteTaskButton.clicked.connect(self._on_delete_current_task_clicked)
        self.searchBox.searchButton.clicked.connect(self._on_search_tasks)
        
        # 表格相关信号
        self.tableSearchBox.searchButton.clicked.connect(self._on_search_user)
        self.exportButton.clicked.connect(self._export_data)
        self.prevPageButton.clicked.connect(self._controller.go_to_previous_page)
        self.nextPageButton.clicked.connect(self._controller.go_to_next_page)
        self.pageComboBox.currentTextChanged.connect(self._on_page_changed)
        self.pageSizeComboBox.currentTextChanged.connect(self._on_page_size_changed)
        
        # 全部用户按钮信号
        self.allUsersButton.clicked.connect(self._on_all_users_clicked)

    def open_add_task_dialog(self, edit_mode:bool = False, task_id: str = None):
        """打开添加任务对话框
        
        Args:
            edit_mode: 是否为编辑模式
            task_id: 要编辑的任务ID，编辑模式下必须提供
        """
        self._logger.info(f"打开{'编辑' if edit_mode else '添加'}任务对话框 {task_id if task_id else ''}")
        
        if not self._controller or not self._account_controller:
            self._show_error_message("控制器未初始化，无法添加任务。")
            return

        # 创建对话框
        add_view = AddMsgMonitorTask(account_controller=self._account_controller, 
                                    telegram_monitor_controller=self._controller, 
                                    parent=self)
        
        # 设置对话框模式
        if edit_mode:
            # 编辑模式下，需要有效的任务ID
            if not task_id:
                self._logger.warning("编辑模式但未提供任务ID")
                task_id = self._controller._current_selected_task_id  # 尝试使用当前选中的任务ID
                
                if not task_id:
                    self._show_error_message("未选择任务", "请先选择一个任务再编辑。")
                    return
            
            add_view.setWindowTitle("编辑监控任务")
            self._logger.info(f"编辑监控任务 ID: {task_id}")
            add_view.set_task_data(task_id)
        else:
            add_view.setWindowTitle("添加监控任务")

        # 显示对话框
        add_view.exec()

    # ========= 界面更新方法 =========
    
    def _refresh_task_list(self, tasks: List[Dict[str, Any]]):
        """
        刷新任务列表显示
        
        Args:
            tasks: 任务列表数据
        """
        self._logger.debug(f"刷新任务列表，共{len(tasks)}个任务")
        
        # 清除旧的列表项
        for i in reversed(range(self.flowLayout.count())):
            item = self.flowLayout.itemAt(i)
            if item and item.widget():
                widget = item.widget()
                widget.deleteLater()
        
        # 获取并显示任务
        if not tasks:
            no_task_label = QLabel("暂无监控任务", self.scrollWidget)
            no_task_label.setAlignment(Qt.AlignCenter)
            self.flowLayout.addWidget(no_task_label)
            self._logger.info("没有找到监控任务")
            return
            
        # 添加任务项到列表
        for task in tasks:
            self._add_task_item(task)
            
    def _add_task_item(self, task_data: Dict[str, Any]):
        """
        添加任务项到视图
        
        Args:
            task_data: 任务数据字典 (包含 id, name, is_running, status_text等)
        """
        try:
            task_id = task_data.get("id")
            task_name = task_data.get("name", "未知任务")
            is_running = task_data.get("is_running", False)
            status_text = task_data.get("status_text", "已停止")
            
            # 使用TaskItemWidget类创建任务项
            task_widget = TaskItemWidget(
                task_id=task_id,
                task_name=task_name,
                is_running=is_running,
                status_text=status_text,
                parent=self.scrollWidget
            )
            
            # 连接按钮信号
            task_widget.edit_button.clicked.connect(
                lambda checked=False, tid=task_id: self.open_add_task_dialog(edit_mode=True, task_id=tid)
            )
            
            if is_running:
                task_widget.start_stop_button.clicked.connect(
                    lambda checked=False, tid=task_id: self._on_stop_task_clicked(tid)
                )
            else:
                task_widget.start_stop_button.clicked.connect(
                    lambda checked=False, tid=task_id: self._on_start_task_clicked(tid)
                )
            
            # 设置任务项点击事件
            task_widget.mousePressEvent = lambda event, tid=task_id: self._handle_task_click(tid, event)
            
            # 设置选中状态
            if task_id == self._controller._current_selected_task_id:
                task_widget.set_selected(True)
            
            # 添加到流布局
            self.flowLayout.addWidget(task_widget)
            
        except Exception as e:
            self._logger.error(f"添加任务项到视图失败: {str(e)}", exc_info=True)
    
    def _handle_task_click(self, task_id: str, event):
        """处理任务项点击的中间处理函数，用于启动异步操作"""
        QTimer.singleShot(0, lambda: self._on_task_item_clicked(task_id, event))
    
    @asyncSlot()
    async def _on_task_item_clicked(self, task_id: str, event=None):
        """
        处理任务项点击事件
        
        Args:
            task_id: 任务ID
            event: 鼠标事件
        """
        self._logger.debug(f"任务项点击: {task_id}")
        
        # 将所有任务项恢复默认样式，并将当前点击项设为选中样式
        for i in range(self.flowLayout.count()):
            item = self.flowLayout.itemAt(i)
            if item and item.widget():
                widget = item.widget()
                if isinstance(widget, TaskItemWidget):
                    widget.set_selected(widget.task_id == task_id)
        
        # 启用删除按钮
        self.deleteTaskButton.setEnabled(True)
        
        # 通知控制器选择变更
        await self._controller.select_task(task_id)
    
    def _on_task_selection_changed(self, task_id: str):
        """
        处理任务选择变更
        
        Args:
            task_id: 任务ID
        """
        self._logger.debug(f"任务选择变更: {task_id}")
        
        if not task_id:
            # 清空详情区域
            self._clear_task_details()
            self.deleteTaskButton.setEnabled(False)
    
    def _clear_task_details(self):
        """清空任务详情区域"""
        self._logger.debug("清空任务详情区域")
        
        # 清空统计卡片
        self._update_stats_cards({})
        
        # 清空用户表格
        self.tableWidget.setRowCount(0)
        
        # 重置分页控件
        self._update_pagination_controls(1, 1)
    
    def _on_task_details_loaded(self, task_id: str, details: Dict[str, Any]):
        """
        处理任务详情加载完成
        
        Args:
            task_id: 任务ID
            details: 任务详情数据
        """
        self._logger.debug(f"任务详情加载完成: {task_id}")
        
        # 记录详情内容，帮助诊断
        self._logger.debug(f"任务详情数据: {details}")
        
        # 检查统计数据
        stats = details.get("stats", {})
        self._logger.debug(f"统计数据: {stats}")
        
        # 更新统计卡片
        self._update_stats_cards(stats)
    
    def _update_stats_cards(self, stats: Dict[str, Any]):
        """
        更新统计卡片
        
        Args:
            stats: 统计数据
        """
        # 设置统计卡片数据
        self.totalUsersCard.setValue(str(stats.get("total_users", "0")), stats.get("total_users", 0) > 0, "#409eff")
        self.todayUsersCard.setValue(str(stats.get("today_users", "0")), stats.get("today_users", 0) > 0,"#00CD00")
        self.avgDailyCard.setValue(str(stats.get("avg_daily", "0")), stats.get("avg_daily", 0) > 0,"#FF7F24")
        self.runningDaysCard.setValue(str(stats.get("running_days", "0")), stats.get("running_days", 0) > 0,"#6959CD")
    
    def _update_users_table(self, users_data: List[Dict[str, Any]], pagination_info: Dict[str, Any]):
        """
        更新用户表格数据
        
        Args:
            users_data: 用户数据列表
            pagination_info: 分页信息
        """
        self._logger.info(f"开始更新用户表格，收到{len(users_data)}条数据")
        self._logger.debug(f"分页信息：{pagination_info}")
        
        try:
            if not users_data:
                self._logger.warning("收到空的用户数据列表")
                self.tableWidget.setRowCount(0)
                self._update_pagination_controls(
                    pagination_info.get("current_page", 1),
                    pagination_info.get("total_pages", 1)
                )
                # 检查表格标题并设置
                if self.tableTitle.text() == "全部用户数据":
                    self._logger.info("当前是全部用户数据模式，但数据为空")
                return
            
            # 记录收到的前几条数据用于调试
            for i, user in enumerate(users_data[:3]):
                self._logger.info(f"示例数据[{i}]: {user}")
            
            # 设置表格行数
            self.tableWidget.setRowCount(len(users_data))
            self._logger.info(f"设置表格行数: {len(users_data)}")
            
            # 填充表格数据
            for row, user in enumerate(users_data):
                try:
                    # 逐个单元格设置数据
                    self.tableWidget.setItem(row, 0, QTableWidgetItem(str(user.get("uid", ""))))
                    self.tableWidget.setItem(row, 1, QTableWidgetItem(str(user.get("username", ""))))
                    self.tableWidget.setItem(row, 2, QTableWidgetItem(str(user.get("nickname", ""))))
                    self.tableWidget.setItem(row, 3, QTableWidgetItem(str(user.get("keyword", ""))))
                    self.tableWidget.setItem(row, 4, QTableWidgetItem(str(user.get("task_type", ""))))
                    self.tableWidget.setItem(row, 5, QTableWidgetItem(str(user.get("join_date", ""))))
                except Exception as e:
                    self._logger.error(f"设置表格行 {row} 数据时出错: {str(e)}")
            
            # 调整列宽
            self.tableWidget.resizeColumnsToContents()
            
            # 更新分页控件
            self._update_pagination_controls(
                pagination_info.get("current_page", 1),
                pagination_info.get("total_pages", 1)
            )
            
            self._logger.info("用户表格更新完成")
        except Exception as e:
            self._logger.error(f"更新用户表格时发生错误: {str(e)}", exc_info=True)
    
    def _update_pagination_controls(self, current_page: int, total_pages: int):
        """
        更新分页控件
        
        Args:
            current_page: 当前页码
            total_pages: 总页数
        """
        # 设置页码信息标签
        self.pageInfoLabel.setText(f"第 {current_page} 页 / 共 {total_pages} 页")
        
        # 设置页码下拉框
        self.pageComboBox.blockSignals(True)
        self.pageComboBox.clear()
        if total_pages > 0:
            self.pageComboBox.addItems([str(i) for i in range(1, total_pages + 1)])
            self.pageComboBox.setCurrentText(str(current_page))
        self.pageComboBox.blockSignals(False)
        
        # 设置按钮状态
        self.prevPageButton.setEnabled(current_page > 1)
        self.nextPageButton.setEnabled(current_page < total_pages)
        self.pageComboBox.setEnabled(total_pages > 1)
    
    # ========= 事件处理方法 =========
    
    def _on_search_tasks(self):
        """处理任务搜索"""
        search_term = self.searchBox.text().strip()
        self._logger.debug(f"搜索任务: {search_term}")
        self._controller.load_tasks(search_term)
    
    def _on_task_operation_completed(self, task_data_or_id: Union[Dict[str, Any], str], 
                                   success_or_message: Union[bool, str], 
                                   optional_message: Optional[str] = None):
        """
        处理任务添加或更新完成的通用槽。
        - task_added signal: (task_dict, message_str)
        - task_updated signal: (task_id_str, success_bool, message_str)
        """
        is_update_signal = isinstance(success_or_message, bool)

        if is_update_signal: # From task_updated
            task_id = task_data_or_id
            success = success_or_message
            message = optional_message
            self._logger.info(f"MonitorView: 收到任务更新信号 - ID: {task_id}, 成功: {success}, 消息: '{message}'")
            if success:
                # InfoBar for success is usually handled by the controller or AddMonitorView
                # self._show_success_message("任务状态", message or f"任务 {task_id} 已更新。")
                self._controller.load_tasks()
            # else: error InfoBar handled by controller
        else: # From task_added
            task_dict = task_data_or_id
            message = success_or_message
            self._logger.info(f"MonitorView: 收到任务添加信号 - 名称: {task_dict.get('name')}, 消息: '{message}'")
            # InfoBar for success is usually handled by the controller or AddMonitorView
            self._controller.load_tasks()

    def _on_task_deleted(self, task_id: str, success: bool, message: str):
        """
        处理任务删除完成
        
        Args:
            task_id: 任务ID
            success: 是否成功
            message: 消息
        """
        self._logger.debug(f"任务删除: {task_id}, 成功: {success}")
        
        if success:
            # 禁用删除按钮
            self.deleteTaskButton.setEnabled(False)
            # 清空详情区域
            self._clear_task_details()
            self._controller.load_tasks() # Also reload tasks after successful deletion
    
    @asyncSlot()
    async def _on_search_user(self):
        """处理用户搜索"""
        search_term = self.tableSearchBox.text().strip()
        self._logger.debug(f"搜索用户: '{search_term}'")
        
        if not self._controller:
            return
        
        # 检查当前是否为全部用户模式
        if self.tableTitle.text() == "全部用户数据":
            page_size = int(self.pageSizeComboBox.currentText())
            # 搜索全部用户数据
            await self._controller.load_all_users(1, page_size, search_term)
        else:
            # 搜索当前任务用户数据
            await self._controller.search_task_users(search_term)
    
    @asyncSlot(str)
    async def _on_page_changed(self, page_str: str):
        """
        处理页码变更
        
        Args:
            page_str: 页码字符串
        """
        if not page_str or not self._controller:
            return
        
        try:
            page = int(page_str)
            # 检查当前是否为全部用户模式
            if self.tableTitle.text() == "全部用户数据":
                page_size = int(self.pageSizeComboBox.currentText())
                search_term = self.tableSearchBox.text().strip()
                await self._controller.load_all_users(page, page_size, search_term)
            else:
                await self._controller.go_to_page(page)
        except ValueError:
            self._logger.warning(f"无效的页码: {page_str}")
    
    @asyncSlot(str)
    async def _on_page_size_changed(self, page_size_str: str):
        """
        处理每页显示数量变更
        
        Args:
            page_size_str: 每页显示数量字符串
        """
        if not page_size_str or not self._controller:
            return
        
        try:
            page_size = int(page_size_str)
            # 检查当前是否为全部用户模式
            if self.tableTitle.text() == "全部用户数据":
                search_term = self.tableSearchBox.text().strip()
                await self._controller.load_all_users(1, page_size, search_term)
            else:
                await self._controller.change_page_size(page_size)
        except ValueError:
            self._logger.warning(f"无效的每页显示数量: {page_size_str}")
    
    @asyncSlot()
    async def _on_delete_current_task_clicked(self):
        if self._controller and self._controller._current_selected_task_id:
            self._confirm_delete_task(self._controller._current_selected_task_id)
        else:
            self._show_info_message("提示", "请先选择一个任务再删除。")

    @asyncSlot(str)
    async def _confirm_delete_task(self, task_id: str):
        """
        确认删除任务
        
        Args:
            task_id: 任务ID
        """
        self._logger.info(f"确认删除任务: {task_id}")
        
        # 找到任务名称
        task_name = ""
        for i in range(self.flowLayout.count()):
            item = self.flowLayout.itemAt(i)
            if item and item.widget():
                widget = item.widget()
                if hasattr(widget, "property") and widget.property("task_id") == task_id:
                    task_name = widget.property("task_name")
                    break
        
        # 弹出确认对话框
        confirm = QMessageBox.question(
            self,
            "确认删除",
            f"确定要删除任务 '{task_name}' 吗？\n此操作不可恢复。",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if confirm == QMessageBox.Yes:
            if self._controller:
                await self._controller.delete_task(task_id)
    
    @asyncSlot()
    async def _export_data(self):
        """导出当前任务的数据"""
        self._logger.info("导出数据")
        
        # 检查是否有选中的任务
        if not self._controller or not self._controller._current_selected_task_id:
            self._show_info_message("提示", "请先选择一个任务以导出数据")
            return
        
        # 获取保存路径
        file_path, _ = QFileDialog.getSaveFileName(
            self,
            "导出数据",
            "",
            "CSV Files (*.csv);;Excel Files (*.xlsx)"
        )
        
        if not file_path:
            self._logger.debug("用户取消了导出操作")
            return
        
        # 调用控制器导出数据
        if self._controller:
            await self._controller.export_task_data(self._controller._current_selected_task_id, file_path)
    
    # ========= 辅助方法 =========
    
    @asyncSlot(str)
    async def _on_start_task_clicked(self, task_id: str):
        self._logger.info(f"View: 请求启动任务 {task_id}")
        if self._controller:
            await self._controller.start_task_by_id(task_id)

    @asyncSlot(str)
    async def _on_stop_task_clicked(self, task_id: str):
        self._logger.info(f"View: 请求停止任务 {task_id}")
        if self._controller:
            await self._controller.stop_task_by_id(task_id)

    def _on_loading_started(self, message: str):
        """
        处理开始加载事件
        
        Args:
            message: 加载消息
        """
        # 这里可以显示加载状态，如进度条或提示框
        self._logger.debug(f"加载中: {message}")
    
    def _on_loading_finished(self):
        """处理加载完成事件"""
        # 清除加载状态
        self._logger.debug("加载完成")
    
    def _append_log(self, message: str, level: str = "info"):
        """
        添加日志到日志区域
        
        Args:
            message: 日志消息
            level: 日志级别 (info/warning/error/debug)
        """
        # 获取当前时间
        now = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        
        # 根据日志级别设置颜色
        color_map = {
            "info": "black",
            "warning": "orange",
            "error": "red",
            "debug": "gray"
        }
        color = color_map.get(level.lower(), "black")
        
        # 构建HTML格式的日志条目
        log_entry = f"<span style='color:{color}'>[{now}] [{level.upper()}] {message}</span>"
        
        # 添加到日志区域
        self.logEdit.append(log_entry)
    
    def _show_info_message(self, title: str, message: str):
        """
        显示信息消息
        
        Args:
            title: 标题
            message: 消息内容
        """
        InfoBar.info(
            title=title,
            content=message,
            orient=Qt.Horizontal,
            isClosable=True,
            position=InfoBarPosition.TOP_RIGHT,
            duration=2000,
            parent=self
        )
    
    def _show_success_message_from_controller(self, title: str, message: str):
        # This is connected to controller.operation_success
        InfoBar.success(title, message, orient=Qt.Horizontal, isClosable=True, position=InfoBarPosition.TOP_RIGHT, duration=2000, parent=self)

    def _show_error_message_from_controller(self, message: str, title: str = "操作失败"):
        # This is connected to controller.operation_failed
        InfoBar.error(title, message, orient=Qt.Horizontal, isClosable=True, position=InfoBarPosition.TOP_RIGHT, duration=3000, parent=self)

    @asyncSlot()
    async def _on_all_users_clicked(self):
        """处理显示全部用户按钮点击事件"""
        self._logger.info("显示全部用户按钮被点击")
        
        # 清除当前选中任务
        self._clear_task_selection()
        
        # 更新表格标题
        self.tableTitle.setText("全部用户数据")
        self._logger.info("表格标题已设置为：全部用户数据")
        
        # 先清空表格，等待新数据
        self.tableWidget.setRowCount(0)
        
        # 显示加载状态
        self._on_loading_started("正在加载全部用户数据...")
        
        # 调用控制器获取所有用户数据
        if self._controller:
            try:
                page_size = int(self.pageSizeComboBox.currentText())
                search_term = self.tableSearchBox.text().strip()
                self._logger.info(f"请求加载全部用户数据: 页码 1, 每页 {page_size} 条, 搜索词: '{search_term}'")
                await self._controller.load_all_users(1, page_size, search_term)
            except Exception as e:
                self._logger.error(f"请求加载全部用户数据时出错: {str(e)}")
                self._show_error_message_from_controller(f"加载全部用户数据失败: {str(e)}")
                self._on_loading_finished()
        else:
            self._logger.error("控制器未初始化，无法加载全部用户数据")
            self._show_error_message_from_controller("控制器未初始化，无法加载全部用户数据")
    
    def _clear_task_selection(self):
        """清除任务选择状态"""
        self._logger.debug("清除任务选择状态")
        
        # 清除所有任务项的选中状态
        for i in range(self.flowLayout.count()):
            item = self.flowLayout.itemAt(i)
            if item and item.widget():
                widget = item.widget()
                if hasattr(widget, "set_selected"):
                    widget.set_selected(False)
        
        # 禁用删除按钮
        self.deleteTaskButton.setEnabled(False)

