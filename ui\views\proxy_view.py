#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
代理视图模块
负责代理管理界面的展示和交互

主要功能流程:
1. 代理添加流程:
   - 用户输入代理IP（单个IP或IP范围）和端口信息（固定端口或端口范围）
   - 点击添加按钮，验证输入数据的有效性
   - 调用控制器方法将代理添加到数据库
   - 刷新表格显示新添加的代理
   - 自动验证新添加代理的有效性
   - 实时更新表格中的代理状态

2. 代理验证流程:
   - 验证单个代理: 从表格中选择代理，点击验证按钮
   - 验证所有代理: 点击验证全部按钮
   - 控制器调用核心服务验证代理连接
   - 通过信号机制实时更新验证进度和结果
   - 验证完成后更新表格显示，并高亮显示验证结果
"""

import os
import sys
import asyncio
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime
from qasync import asyncSlot
from PySide6.QtCore import Qt, QSize, Signal, Slot, QDateTime, QTimer
from PySide6.QtGui import QColor, QIcon
from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QTableWidgetItem, QHeaderView,QToolTip, 
    QAbstractItemView, QMenu, QMessageBox, QFileDialog
)
from utils.logger import get_logger
from qfluentwidgets import InfoBar, InfoBarPosition, TableWidget,StateToolTip

from ui.designer.proxy_manager_ui import ProxyManagerUI
from ui.common.message_box import (
    createInfoInfoBar, show_dialog, set_window_center, show_toast,
    createSuccessInfoBar, createWarningInfoBar, createErrorInfoBar
)

from app.controllers.proxy_controller import ProxyController
from data.models.proxy import ProxyModel


class ProxyView(QWidget):
    """代理视图类
    
    负责以下功能:
    - 展示代理管理界面
    - 处理用户交互
    - 更新UI状态
    """
    
    def __init__(self, controller: ProxyController, parent=None):
        """初始化视图
        Args:
            controller: 代理控制器
            parent: 父窗口
        """
        super().__init__(parent)
        self.controller = controller
        self._logger = get_logger("proxy_view")
        self.setObjectName("proxyView")
        # 创建UI组件
        self.ui = ProxyManagerUI()
        self.stateTooltip = None
        # 设置布局
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.addWidget(self.ui)
        
        # 分页参数初始化
        self._current_page = 1
        self._total_pages = 1
        self._page_size = 20
        self._total_count = 0
        
        # 初始化UI
        self._init_ui()
        
        # 如果控制器为None，禁用操作按钮
        if self.controller is None:
            self._logger.warning("代理控制器未初始化，禁用操作按钮")
            self._disable_ui_controls()
            self.show_info(tilte="警告",msg="代理服务未初始化，部分功能不可用",type="warning")
        else:
            # 设置信号连接
            self._setup_signals()
            
            # 加载第一页数据
            self._logger.info("初始化视图，加载第一页代理数据")
            self._load_initial_data()
    
    def _init_ui(self):
        """初始化UI组件"""
        # 设置初始状态
        self.ui.update_port_widgets_visibility(True)
        self.ui.random_port_container.setVisible(False)
        self.ui.credentials_group.setVisible(False)
        
        # 设置表格属性
        self._setup_table()
        
        # 设置上下文菜单
        self._setup_context_menu()
        
        # 如果控制器存在，检查当前服务状态并设置按钮状态
        # if self.controller is not None:
        #     is_running = self.controller.get_service_status()
        #     self.ui.restart_btn.setEnabled(is_running)
        #     self.ui.update_status_label(is_running)
        #
    def _setup_signals(self):
        """设置信号连接"""
        # 检查controller是否存在
        if self.controller is None:
            self._logger.warning("代理控制器为None，无法连接信号")
            return
        
        # UI信号连接
        self.ui.toggle_proxy_btn.clicked.connect(self._on_toggle_service)
        self.ui.restart_btn.clicked.connect(self._on_restart_service)
        
        # IP类型切换
        self.ui.ip_type_combo.currentIndexChanged.connect(self._on_ip_type_changed)
        
        # 端口类型切换
        self.ui.fixed_port_radio.toggled.connect(self.ui.update_port_widgets_visibility)
        self.ui.random_port_radio.toggled.connect(
            lambda: self.ui.update_port_widgets_visibility(not self.ui.random_port_radio.isChecked())
        )
        
        # 添加代理按钮
        self.ui.add_proxy_btn.clicked.connect(self._on_add_proxy_clicked)
        self.ui.validate_btn.clicked.connect(self._on_validate_selected_clicked)
        self.ui.validate_all_btn.clicked.connect(self._on_validate_all_clicked)
        self.ui.export_btn.clicked.connect(self._on_export_proxies_clicked)
        self.ui.delete_btn.clicked.connect(self._on_delete_selected_clicked)
        self.ui.invalid_btn.clicked.connect(self._on_delete_invalid_clicked)
        self.ui.delete_all_btn.clicked.connect(self._on_delete_all_clicked)
        
        # 分页控制信号连接
        self.ui.prev_page_btn.clicked.connect(self._on_prev_page)
        self.ui.next_page_btn.clicked.connect(self._on_next_page)
        self.ui.page_size_combo.currentTextChanged.connect(self._on_page_size_changed)
        
        # 控制器信号连接
        self.controller.signals.proxies_updated.connect(self._on_proxies_updated)
        self.controller.signals.operation_result.connect(self._on_operation_result)
        self.controller.signals.proxy_validated.connect(self._on_proxy_validated)
        self.controller.signals.validation_progress.connect(self._on_validation_progress)
        self.controller.signals.validation_completed.connect(self._on_validation_completed)
        self.controller.signals.service_status_changed.connect(self._update_service_buttons)
    
    def _setup_table(self):
        """设置代理表格"""
        # 设置表头
        table = self.ui.proxy_table
        table.setColumnCount(10)
        headers = ["ID", "IP地址", "端口", "用户名", "密码", "类型", "协议", "状态", "响应时间", "最后验证"]
        table.setHorizontalHeaderLabels(headers)
        
        # 设置表头样式
        header = table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.Stretch)
        header.setSectionResizeMode(0, QHeaderView.Fixed)  # ID列固定宽度
        header.setSectionResizeMode(2, QHeaderView.Fixed)  # 端口列固定宽度
        header.setSectionResizeMode(3, QHeaderView.Fixed)  # 用户名列固定宽度
        header.setSectionResizeMode(4, QHeaderView.Fixed)  # 密码列固定宽度
        header.setSectionResizeMode(5, QHeaderView.Fixed)  # 类型列固定宽度
        header.setSectionResizeMode(6, QHeaderView.Fixed)  # 协议列固定宽度
        header.setSectionResizeMode(8, QHeaderView.Fixed)  # 响应时间列固定宽度
        
        # 设置列宽
        table.setColumnWidth(0, 50)   # ID
        table.setColumnWidth(2, 80)   # 端口
        table.setColumnWidth(3, 100)  # 用户名
        table.setColumnWidth(4, 100)  # 密码
        table.setColumnWidth(5, 80)   # 类型
        table.setColumnWidth(6, 80)   # 协议
        table.setColumnWidth(8, 120)  # 响应时间
        
        # 设置选择模式
        table.setSelectionBehavior(QAbstractItemView.SelectRows)
        table.setSelectionMode(QAbstractItemView.ExtendedSelection)
        
        # 设置上下文菜单策略
        table.setContextMenuPolicy(Qt.CustomContextMenu)
        table.customContextMenuRequested.connect(self._show_context_menu)
        
        # 连接双击信号
        table.cellDoubleClicked.connect(self._on_table_double_clicked)
        
        # 设置鼠标悬停事件
        table.setMouseTracking(True)
        table.cellEntered.connect(self._on_cell_entered)
    
    def _setup_context_menu(self):
        """设置上下文菜单"""
        self.context_menu = QMenu(self)
        
        # 添加菜单项
        self.validate_action = self.context_menu.addAction("验证")
        self.activate_action = self.context_menu.addAction("激活")
        self.deactivate_action = self.context_menu.addAction("禁用")
        self.context_menu.addSeparator()
        self.delete_action = self.context_menu.addAction("删除")
        
        # 连接信号
        self.validate_action.triggered.connect(self._on_validate_selected_clicked)
        self.activate_action.triggered.connect(lambda: self._on_set_active_selected(True))
        self.deactivate_action.triggered.connect(lambda: self._on_set_active_selected(False))
        self.delete_action.triggered.connect(self._on_delete_selected_clicked)
    
    def _show_context_menu(self, pos):
        """显示上下文菜单
        
        Args:
            pos: 菜单显示位置
        """
        # 获取选中的行
        selected_rows = self.ui.proxy_table.selectionModel().selectedRows()
        if not selected_rows:
            return
        
        # 显示上下文菜单
        self.context_menu.exec_(self.ui.proxy_table.mapToGlobal(pos))
    
    @Slot(int)
    def _on_ip_type_changed(self, index: int):
        """IP类型切换处理
        
        Args:
            index: 组合框索引
        """
        # 本机公网IP
        if index == 0:
            self.ui.ip_edit.setPlaceholderText("留空将自动获取本机公网IP，或输入指定IP")
            self.ui.ip_edit.setEnabled(True)
            self.ui.credentials_group.setVisible(False)
        # 外部SOCKS5
        elif index == 1:
            self.ui.ip_edit.setPlaceholderText("请输入IP地址或IP范围\n例如: *********** 或 ***********-************")
            self.ui.ip_edit.setEnabled(True)
            self.ui.credentials_group.setVisible(True)
    
    @asyncSlot()
    async def _on_toggle_service(self):
        """切换代理服务状态"""
        self._logger.info("用户点击切换代理服务状态")
        
        # 异步获取当前服务状态
        current_status = await self.controller.check_service_status()
        action = "停止" if current_status else "启动"
        
        # 显示加载状态
        self.loading_ui(f"{action}服务中", f"正在{action}代理服务...", True)
        
        try:
            # 根据当前状态调用相应的控制器方法
            if current_status:
                # 停止服务
                result = await self.controller.stop_service()
            else:
                # 启动服务
                result = await self.controller.start_service()
                
            # 处理结果
            if result:
                self.loading_ui(f"{action}成功", f"代理服务已{action}", False)
            else:
                self.loading_ui(f"{action}失败", f"代理服务{action}失败", False)
                self.show_info(tilte="操作失败", msg=f"代理服务{action}失败", type="error")
        except Exception as e:
            self._logger.exception(f"切换代理服务状态时出错: {e}")
            self.loading_ui(f"{action}失败", f"代理服务{action}失败: {str(e)}", False)
            self.show_info(tilte="操作失败", msg=f"代理服务{action}失败: {str(e)}", type="error")
    
    @asyncSlot()
    async def _on_restart_service(self):
        """重启代理服务"""
        self._logger.info("用户点击重启代理服务")
        
        # 显示加载状态
        self.loading_ui("重启服务中", "正在重启代理服务...", True)
        
        try:
            # 调用控制器重启服务
            result = await self.controller.restart_service()
            
            # 处理结果
            if result:
                self.loading_ui("重启成功", "代理服务已重启", False)
            else:
                self.loading_ui("重启失败", "代理服务重启失败", False)
                self.show_info(tilte="操作失败", msg="代理服务重启失败", type="error")
        except Exception as e:
            self._logger.exception(f"重启代理服务时出错: {e}")
            self.loading_ui("重启失败", f"代理服务重启失败: {str(e)}", False)
            self.show_info(tilte="操作失败", msg=f"代理服务重启失败: {str(e)}", type="error")
    
    @asyncSlot()
    async def _on_add_proxy_clicked(self):
        """添加代理按钮点击处理"""
        # 获取用户输入
        ip_type_index = self.ui.ip_type_combo.currentIndex()
        is_local = (ip_type_index == 0)  # 0表示本地公网IP
        proxy_type = "socks5"
        
        # 获取IP文本并去除所有空格（包括行内空格）
        raw_host = self.ui.ip_edit.toPlainText().strip()
        host = ''.join(raw_host.split())  # 去除所有空格
        
        # 如果去除空格后与原始输入不同，显示提示
        if raw_host != host and host:
            self.show_info(
                tilte="输入处理",
                msg=f"已自动移除IP中的所有空格，处理后的IP: {host}",
                type='info',
                duration=2000
            )
        
        # 固定端口或随机端口
        if self.ui.fixed_port_radio.isChecked():
            port = self.ui.port_spin.value()
            port_end = None
        else:
            port = self.ui.min_port_spin.value()
            port_end = self.ui.max_port_spin.value()
        
        # 获取凭据
        username = self.ui.username_edit.text().strip() if self.ui.credentials_group.isVisible() else ""
        password = self.ui.password_edit.text().strip() if self.ui.credentials_group.isVisible() else ""
        
        # 如果是本地代理，提示端口不能重复
        if is_local:
            self.show_info(
                tilte="添加本地代理",
                msg="添加本地代理时，如果端口已被使用，系统会自动分配一个新的可用端口",
                type='info',
                duration=3000
            )
        
        # 调用代理添加方法
        await self._on_add_proxy(host, port, username, password, proxy_type, is_local)
    
    @asyncSlot()
    async def _on_validate_selected_clicked(self):
        """验证选中代理按钮点击处理"""
        # 获取选中的行
        selected_rows = self.ui.proxy_table.selectionModel().selectedRows()
        if not selected_rows:
            self.show_info(tilte='无代理', msg='请先选中验证需要的代理', type='warning')
            return
        
        # 显示验证进度
        self.ui.show_validation_progress(True)
        
        # 遍历选中行，调用验证方法
        for row in selected_rows:
            proxy_id = int(self.ui.proxy_table.item(row.row(), 0).text())
            await self.controller.validate_proxy(proxy_id)
    
    @asyncSlot()
    async def _on_validate_all_clicked(self):
        """验证所有代理按钮点击处理"""
        await self.controller.validate_all_proxies()
    
    @asyncSlot()
    async def _on_delete_selected_clicked(self):
        """删除选中代理按钮点击处理"""
        # 获取选中的行
        selected_rows = self.ui.proxy_table.selectionModel().selectedRows()
        if not selected_rows:
            self.show_info(tilte='无代理', msg='请先选中要删除的代理', type='warning')
            return
        
        # 提示用户确认
        reply = show_dialog(
            title="删除确认",
            content=f"确定要删除选中的 {len(selected_rows)} 个代理吗？",
            parent=self
        )
        
        if reply:
            # 显示删除进度
            self.loading_ui("删除中", "正在删除选中的代理...", False)
            
            # 遍历选中行，调用删除方法
            for row in selected_rows:
                proxy_id = int(self.ui.proxy_table.item(row.row(), 0).text())
                await self.controller.delete_proxy(proxy_id)
            
            # 刷新当前页
            await self.load_proxies_by_page(self._current_page)
            
            # 显示删除完成
            self.loading_ui("删除完成", "选中的代理已删除", False)
    
    @asyncSlot()
    async def _on_delete_invalid_clicked(self):
        """删除失效代理按钮点击处理"""
        # 查找失效的代理行
        invalid_rows = []
        for row in range(self.ui.proxy_table.rowCount()):
            # 获取状态列的文本
            status_item = self.ui.proxy_table.item(row, 7)
            if status_item and "失败" in status_item.text():
                invalid_rows.append(row)
        
        if not invalid_rows:
            self.show_info(tilte='无失效代理', msg='未找到失效的代理', type='warning')
            return
        
        # 提示用户确认
        reply = show_dialog(
            title="删除确认",
            content=f"确定要删除 {len(invalid_rows)} 个失效的代理吗？",
            parent=self
        )
        
        if reply:
            # 显示删除进度
            self.loading_ui("删除中", "正在删除失效的代理...", True)
            
            # 删除失效代理ip
            await self.controller.delete_all_invalid_proxies()
            # 刷新当前页
            await self.load_proxies_by_page(self._current_page)
            
            # 显示删除完成
            self.loading_ui("删除完成", "失效的代理已删除", False)
    
    @asyncSlot()
    async def _on_delete_all_clicked(self):
        """删除所有代理按钮点击处理"""
        await self.controller.delete_all_proxies()
    
    @asyncSlot()
    async def _on_add_proxy(self, host, port, username, password, proxy_type, is_local):
        """添加代理处理"""
        try:
            # 获取原始IP文本，只去除首尾空白，保留换行符
            raw_host = self.ui.ip_edit.toPlainText().strip()
            print(raw_host)
            self._logger.info(f"用户开始添加代理: IP类型={proxy_type}({['远程', '本地'][int(is_local)]}), IP文本='{raw_host}'")
            
            # 判断是固定端口还是随机端口
            is_fixed_port = self.ui.fixed_port_radio.isChecked()
            
            if is_fixed_port:
                port_start = port
                port_end = port
                self._logger.debug(f"固定端口: {port_start}")
            else:
                port_start = self.ui.min_port_spin.value()
                port_end = self.ui.max_port_spin.value()
                self._logger.debug(f"端口范围: {port_start}-{port_end}")
                
                # 验证端口范围
                if port_start > port_end:
                    self.show_info(tilte="错误",msg="起始端口不能大于结束端口",type='error')
                    return
            
            # 获取用户名密码
            username = username.strip()
            password = password.strip()
            self._logger.debug(f"用户凭据: username={username}, password={'*****' if password else None}")
            
            # 如果用户名不为空，密码必须有值
            if username and not password:
                self.show_info(tilte="凭据错误",msg="设置用户名时密码不能为空",type='error')
                return
            
            # 调用控制器添加代理
            success, proxy_ids = await self.controller.add_proxies_from_text(
                ip_text=raw_host,  # 使用保留换行符的原始文本
                port_start=port_start,
                port_end=port_end,
                username=username,
                password=password,
                proxy_type=proxy_type,
                is_local=is_local
            )
            
            if success and proxy_ids:
                # 显示加载状态
                self.loading_ui("验证中", "正在验证新添加的代理...", True)
                
                # 验证新添加的代理
                await self.controller.validate_proxies(proxy_ids)
                
                # 刷新表格显示新添加的代理
                await self.load_proxies_by_page(self._current_page)
                
                # 清空IP输入框
                self.ui.ip_edit.setPlainText("")
                
                # 如果有用户名和密码，也清空
                if self.ui.credentials_group.isVisible():
                    self.ui.username_edit.clear()
                    self.ui.password_edit.clear()
                
                self._logger.info("已清空输入框，代理添加和验证流程完成")
            
        except Exception as e:
            self._logger.exception(f"添加代理时发生异常: {str(e)}")
            self.loading_ui("添加代理失败", f"添加代理失败: {str(e)}",False)
            self.show_info("添加代理失败", f"添加代理失败: {str(e)}",'error')
        finally:
            self.show_info("代理添加和验证完成", "代理已成功添加并验证！",'info')

    @asyncSlot()
    async def load_proxies_by_page(self, page: int = 1):
        """加载指定页码的代理列表
        
        Args:
            page: 页码，从1开始
        """
        self._logger.info(f"加载代理页面: {page}")
        self._current_page = page
        
        try:
            # 显示加载状态
    
            # 计算偏移量
            offset = (page - 1) * self._page_size
            
            # 调用控制器加载代理数据
            proxies, total = await self.controller.refresh_proxies(offset=offset, limit=self._page_size)
            
            
            self._logger.info(f"页面 {page} 加载完成，显示 {len(proxies)} 条记录，共 {total} 条")
            
            # 如果没有代理数据，显示提示
            if total == 0 and page == 1:
                # 确保表格中显示为空列表的提示信息
                self.ui.proxy_table.clearContents()
                self.ui.proxy_table.setRowCount(1)
                empty_item = QTableWidgetItem("暂无代理数据，请点击添加按钮添加代理")
                empty_item.setTextAlignment(Qt.AlignCenter)
                # 合并所有列显示提示信息
                self.ui.proxy_table.setSpan(0, 0, 1, 10)
                self.ui.proxy_table.setItem(0, 0, empty_item)
                self.ui.proxy_count_label.setText("总代理：0")
                
        except Exception as e:
            self._logger.exception(f"加载代理页面失败: {e}")
            self.show_info(tilte="错误", msg=f"加载代理数据失败: {str(e)}", type="error")
            
            # 显示错误提示到表格
            self.ui.proxy_table.clearContents()
            self.ui.proxy_table.setRowCount(1)
            error_item = QTableWidgetItem(f"加载失败: {str(e)}")
            error_item.setTextAlignment(Qt.AlignCenter)
            error_item.setForeground(QColor("#dc3545"))  # 红色
            self.ui.proxy_table.setSpan(0, 0, 1, 10)
            self.ui.proxy_table.setItem(0, 0, error_item)

    @Slot(list, int)
    def _on_proxies_updated(self, proxies: List[ProxyModel], total: int):
        """代理列表更新处理
        
        Args:
            proxies: 当前页的代理列表
            total: 总代理数量
        """
        # 保存总数
        self._total_count = total
        
        # 计算总页数
        self._page_size = int(self.ui.page_size_combo.currentText())
        self._total_pages = max(1, (total + self._page_size - 1) // self._page_size)
        
        # 确保当前页在有效范围内
        if self._current_page > self._total_pages:
            self._current_page = self._total_pages
            # 重新加载正确的页码
            self._load_page_by_timer(self._current_page)
            return
        
        # 更新分页控件状态
        self._update_pagination_ui()
        
        # 清空表格
        self.ui.proxy_table.clearContents()
        self.ui.proxy_table.setRowCount(len(proxies))
        
        # 填充表格
        for row, proxy in enumerate(proxies):
            # ID列
            id_item = QTableWidgetItem(str(proxy.id))
            id_item.setTextAlignment(Qt.AlignCenter)
            self.ui.proxy_table.setItem(row, 0, id_item)
            
            # IP地址列
            host_item = QTableWidgetItem(proxy.host)
            self.ui.proxy_table.setItem(row, 1, host_item)
            
            # 端口列
            port_item = QTableWidgetItem(str(proxy.port))
            port_item.setTextAlignment(Qt.AlignCenter)
            self.ui.proxy_table.setItem(row, 2, port_item)
            
            # 用户名列
            username_item = QTableWidgetItem(proxy.username if proxy.username else "-")
            username_item.setTextAlignment(Qt.AlignCenter)
            self.ui.proxy_table.setItem(row, 3, username_item)
            
            # 密码列
            password_item = QTableWidgetItem(proxy.password if proxy.password else "-")
            password_item.setTextAlignment(Qt.AlignCenter)
            self.ui.proxy_table.setItem(row, 4, password_item)
            
            # 类型列
            type_item = QTableWidgetItem("本地" if proxy.is_local else "远程")
            type_item.setTextAlignment(Qt.AlignCenter)
            self.ui.proxy_table.setItem(row, 5, type_item)
            
            # 协议列
            protocol_item = QTableWidgetItem(proxy.proxy_type.upper())
            protocol_item.setTextAlignment(Qt.AlignCenter)
            self.ui.proxy_table.setItem(row, 6, protocol_item)
            
            # 状态列
            status_text = "正常" if proxy.is_valid else "未验证" if proxy.last_checked is None else "失败"
            status_item = QTableWidgetItem(status_text)
            status_item.setTextAlignment(Qt.AlignCenter)
            
            # 设置状态列颜色
            if proxy.is_valid:
                status_item.setForeground(QColor("#28a745"))  # 绿色
            elif proxy.last_checked is None:
                status_item.setForeground(QColor("#6c757d"))  # 灰色
            else:
                status_item.setForeground(QColor("#dc3545"))  # 红色
                
            # 如果代理未激活，使用删除线
            if not proxy.is_active:
                # 设置字体
                font = status_item.font()
                font.setStrikeOut(True)
                status_item.setFont(font)
                status_item.setText(status_text + "(禁用)")
                
            self.ui.proxy_table.setItem(row, 7, status_item)
            
            # 响应时间列
            if proxy.response_time is not None:
                response_time = f"{proxy.response_time:.2f}ms"
            else:
                response_time = "-"
            response_item = QTableWidgetItem(response_time)
            response_item.setTextAlignment(Qt.AlignCenter)
            self.ui.proxy_table.setItem(row, 8, response_item)
            
            # 最后验证列
            if proxy.last_checked:
                last_check = proxy.last_checked.strftime("%Y-%m-%d %H:%M:%S")
            else:
                last_check = "-"
            last_check_item = QTableWidgetItem(last_check)
            self.ui.proxy_table.setItem(row, 9, last_check_item)
        
        # 如果列表为空，添加提示信息
        if len(proxies) == 0:
            self.ui.proxy_table.setRowCount(1)
            empty_item = QTableWidgetItem("暂无代理数据，请点击添加按钮添加代理")
            empty_item.setTextAlignment(Qt.AlignCenter)
            # 合并所有列显示提示信息
            self.ui.proxy_table.setSpan(0, 0, 1, 10)
            self.ui.proxy_table.setItem(0, 0, empty_item)
            
        # 更新总数标签
        self.ui.proxy_count_label.setText(f"总代理：{total}")
        
        # 设置服务状态
        self.ui.update_status_label(self.controller.get_service_status())
    
    @asyncSlot()
    async def _load_page_by_timer(self, page: int):
        """通过计时器调用的加载页面方法"""
        await self.load_proxies_by_page(page)
    
    def _update_pagination_ui(self):
        """更新分页UI状态"""
        # 更新页码信息
        self.ui.page_info_label.setText(f"第 {self._current_page}/{self._total_pages} 页")
        
        # 更新按钮状态
        self.ui.prev_page_btn.setEnabled(self._current_page > 1)
        self.ui.next_page_btn.setEnabled(self._current_page < self._total_pages)
    
    @asyncSlot()
    async def _on_prev_page(self):
        """上一页按钮点击处理"""
        if self._current_page > 1:
            self._current_page -= 1
            await self.load_proxies_by_page(self._current_page)
    
    @asyncSlot()
    async def _on_next_page(self):
        """下一页按钮点击处理"""
        if self._current_page < self._total_pages:
            self._current_page += 1
            await self.load_proxies_by_page(self._current_page)
    
    @asyncSlot()
    async def _on_page_size_changed(self, size_text: str):
        """页面大小改变处理
        
        Args:
            size_text: 页面大小文本
        """
        try:
            self._page_size = int(size_text)
            # 重置页码并重新加载
            self._current_page = 1
            await self.load_proxies_by_page(self._current_page)
        except ValueError:
            self._logger.error(f"无效的页面大小：{size_text}")
    
    @asyncSlot()
    async def _on_operation_result(self, success: bool, message: str):
        """操作结果处理
        
        Args:
            success: 是否成功
            message: 消息内容
        """
        # 显示结果消息
        if success:
            createSuccessInfoBar(
                self,
                message,
                title="操作成功",
                position=InfoBarPosition.TOP,
                duration=3000
            )
            # 如果是服务状态变化，更新UI
            if "服务" in message:
                is_running = "启动" in message or "重启" in message
                is_stopped = "停止" in message
                
                # 更新重启按钮状态
                self.ui.restart_btn.setEnabled(is_running)
                
     
                
                # 更新状态标签
                self.ui.update_status_label(is_running)
                
        else:
            createErrorInfoBar(
                self,
                message,
                title="操作失败",
                position=InfoBarPosition.TOP,
                duration=3000
            )
    @asyncSlot()
    async def _on_validation_progress(self, current: int, total: int):
        """验证进度处理
        
        Args:
            current: 当前进度
            total: 总进度
        """
        # 显示进度条并更新进度
        self.ui.update_validation_progress(current, total)
        
        # 如果验证完成，隐藏进度条和加载对话框
        if current >= total:
            self.ui.show_validation_progress(False)
            
            # 刷新当前页代理列表，确保显示最新的验证结果
            await self.load_proxies_by_page(self._current_page)
            
            # 统计有效和无效代理数量
            valid_count = 0
            invalid_count = 0
            
            for row in range(self.ui.proxy_table.rowCount()):
                status_item = self.ui.proxy_table.item(row, 7)
                if status_item:
                    if "正常" in status_item.text():
                        valid_count += 1
                    elif "失败" in status_item.text():
                        invalid_count += 1
            self.show_info(tilte="验证结果",msg=f"验证完成：有效代理 {valid_count} 个，无效代理 {invalid_count} 个",type='success')
            self.loading_ui("验证完成", "代理ip已验证完成",False)
            
    @asyncSlot()
    async def _on_proxy_status_changed(self, proxy_id: int, is_valid: bool):
        """代理状态变化处理
        
        Args:
            proxy_id: 代理ID
            is_valid: 是否有效
        """
        # 查找代理ID所在行
        for row in range(self.ui.proxy_table.rowCount()):
            if int(self.ui.proxy_table.item(row, 0).text()) == proxy_id:
                # 更新状态列
                status_item = self.ui.proxy_table.item(row, 7)
                if status_item:
                    status_text = "正常" if is_valid else "失败"
                    status_item.setText(status_text)
                    
                    # 更新颜色
                    if is_valid:
                        status_item.setForeground(QColor("#28a745"))  # 绿色
                    else:
                        status_item.setForeground(QColor("#dc3545"))  # 红色
                
                # 刷新当前页数据
                await self.load_proxies_by_page(self._current_page)
                break

    @asyncSlot()
    async def _on_delete_invalid(self):
        """删除失效的代理"""
        # 获取表格中的代理数量
        row_count = self.ui.proxy_table.rowCount()
        if row_count == 0:
            createWarningInfoBar(
                self,
                "没有代理可删除",
                title="消息提醒",
                position=InfoBarPosition.TOP,
                duration=3000
            )
            return
        
        # 查找失效的代理行
        invalid_rows = []
        for row in range(row_count):
            # 获取状态列的文本
            status_item = self.ui.proxy_table.item(row, 7)
            if status_item and "失败" in status_item.text():
                invalid_rows.append(row)
        
        if not invalid_rows:
            createWarningInfoBar(
                parent=self,
                content="没有发现失效的代理",
                title="消息提醒",
                position=InfoBarPosition.TOP,
                duration=3000
            )
            return
        
        # 提示用户
        reply = show_dialog(
            title="删除确认",
            content=f"确定要删除{len(invalid_rows)}个失效的代理吗？",
            parent=self
        )
        
        if reply:  # 如果用户点击了确定按钮
            # 从后向前删除，避免索引变化
            for row in sorted(invalid_rows, reverse=True):
                proxy_id = int(self.ui.proxy_table.item(row, 0).text())
                await self.controller.delete_proxy(proxy_id)
            
            # 删除后刷新当前页
            await self.load_proxies_by_page(self._current_page)

    @asyncSlot()
    async def _on_delete_all(self):
        """删除全部代理"""
        # 获取表格中的代理数量
        row_count = self.ui.proxy_table.rowCount()
        if row_count == 0:
            createWarningInfoBar(
                self,
                "没有代理可删除",
                title="消息提醒",
                position=InfoBarPosition.TOP,
                duration=3000
            )
            return
        
        # 提示用户确认
        reply = show_dialog(
            title="删除确认",
            content=f"确定要删除全部代理吗？此操作不可恢复！",
            parent=self
        )
        
        if reply:  # 如果用户点击了确定按钮
            # 显示加载对话框
            self.loading_ui("删除中", "正在删除全部代理...",True)
            
            # 调用控制器的删除所有代理方法
            await self.controller.delete_all_proxies()
            
            # 删除后重置为第一页
            self._current_page = 1
            
            # 刷新代理列表
            await self.load_proxies_by_page(self._current_page)
            
            # 更新加载对话框
            self.loading_ui("完成", "代理已删除",False)

    def _disable_ui_controls(self):
        """禁用UI控件"""
        # 禁用服务控制按钮
        self.ui.toggle_proxy_btn.setEnabled(False)
        self.ui.restart_btn.setEnabled(False)
        
        # 禁用代理操作按钮
        self.ui.add_proxy_btn.setEnabled(False)
        self.ui.validate_btn.setEnabled(False)
        self.ui.validate_all_btn.setEnabled(False)
        self.ui.delete_btn.setEnabled(False)
        self.ui.invalid_btn.setEnabled(False)

    def loading_ui(self,title,content,start=True):
        if not start:
            if self.stateTooltip:
                self.stateTooltip.setTitle(title)
                self.stateTooltip.setContent(content)
                self.stateTooltip.setState(True)
                self.stateTooltip = None
        else:
            self.stateTooltip = StateToolTip(title, content, self)
            self.stateTooltip.move(510, 30)
            self.stateTooltip.show()

    def show_info(self,tilte,msg,type='info',duration=3000):
        if type=="error":
            createErrorInfoBar(
                self,
                content=str(msg),
                title=tilte,
                position=InfoBarPosition.TOP,
                duration=3000
            )
        elif type=="success":
            createSuccessInfoBar(
                self,
                content=str(msg),
                title=tilte,
                position=InfoBarPosition.TOP,
                duration=duration)
        elif type=="warning":
            createWarningInfoBar(
                self,
                content=str(msg),
                title=tilte,
                position=InfoBarPosition.TOP,
                duration=3000)
        elif type=="info":
            createInfoInfoBar(
                self,
                content=str(msg),
                title=tilte,
                position=InfoBarPosition.TOP,
                duration=6000)
        else:
            createInfoInfoBar(
                self,
                content=str(msg),
                title=tilte,
                position=InfoBarPosition.TOP,
                duration=3000)

    def _update_service_buttons(self, is_running: bool):
        """根据服务状态更新按钮可用性
        
        Args:
            is_running: 服务是否运行中
        """
        self._logger.debug(f"更新服务按钮状态: 服务运行状态={is_running}")
        
        # 只有在服务运行时才启用重启按钮
        self.ui.restart_btn.setEnabled(is_running)
        
        # 更新启动/停止按钮文本
        if is_running:
            self.ui.toggle_proxy_btn.setText("停止服务")
        else:
            self.ui.toggle_proxy_btn.setText("启动服务")
            
        # 更新状态标签
        self.ui.update_status_label(is_running)

    @asyncSlot()
    async def _on_set_active_selected(self, is_active: bool):
        """设置选中代理激活状态
        
        Args:
            is_active: 是否激活
        """
        # 获取选中的行
        selected_rows = self.ui.proxy_table.selectionModel().selectedRows()
        if not selected_rows:
            self.show_info(
                tilte="提醒",
                msg=f"请选择要{'激活' if is_active else '禁用'}的代理",
                type="warning"
            )
            return
        
        # 显示操作进度
        action = "激活" if is_active else "禁用"
        self.loading_ui(f"{action}中", f"正在{action}选中的代理...", True)
        
        # 设置选中行的代理状态
        for row in selected_rows:
            proxy_id = int(self.ui.proxy_table.item(row.row(), 0).text())
            await self.controller.set_proxy_active(proxy_id, is_active)
        
        # 刷新当前页
        await self.load_proxies_by_page(self._current_page)
        
        # 更新UI状态
        self.loading_ui(f"{action}完成", f"代理已{action}", False)

    @asyncSlot()
    async def _load_initial_data(self):
        """初始化加载数据"""
        try:
            # 显示加载状态
            self.loading_ui("加载中", "正在加载代理数据...", True)
            
            # 通过控制器加载代理数据
            proxies, total = await self.controller.refresh_proxies(offset=0, limit=self._page_size)
            print(proxies, total)
            # 更新UI状态
            self.loading_ui("加载完成", "代理数据已加载", False)
            
            # 异步检查服务状态并更新UI
            is_running = await self.controller.check_service_status()
            self._update_service_buttons(is_running)
            
            self._logger.info(f"初始数据加载完成，共 {total} 个代理")
            
            # 如果没有代理数据，显示提示
            if total == 0:
                self.show_info(tilte="提示", msg="当前没有代理数据，请添加代理", type="info")
                
        except Exception as e:
            self._logger.exception(f"初始化加载数据失败: {e}")
            self.loading_ui("加载失败", f"加载代理数据失败: {str(e)}", False)
            self.show_info(tilte="错误", msg=f"加载代理数据失败: {str(e)}", type="error")

    @asyncSlot()
    async def _on_proxy_validated(self, proxy_id: int, is_valid: bool, response_time: float):
        """代理验证结果处理
        
        Args:
            proxy_id: 代理ID
            is_valid: 是否有效
            response_time: 响应时间
        """
        # 查找代理ID所在行
        for row in range(self.ui.proxy_table.rowCount()):
            if int(self.ui.proxy_table.item(row, 0).text()) == proxy_id:
                # 更新状态列
                status_item = self.ui.proxy_table.item(row, 7)
                if status_item:
                    status_text = "正常" if is_valid else "失败"
                    status_item.setText(status_text)
                    
                    # 更新颜色
                    if is_valid:
                        status_item.setForeground(QColor("#28a745"))  # 绿色
                    else:
                        status_item.setForeground(QColor("#dc3545"))  # 红色
                
                # 更新响应时间列
                response_item = self.ui.proxy_table.item(row, 8)
                if response_item:
                    response_text = f"{response_time:.2f}ms" if response_time > 0 else "-"
                    response_item.setText(response_text)
                
                # 更新最后验证时间列
                last_check_item = self.ui.proxy_table.item(row, 9)
                if last_check_item:
                    now = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                    last_check_item.setText(now)
                
                break
    
    @asyncSlot()
    async def _on_validation_completed(self, valid_count: int, total_count: int):
        """验证完成处理
        
        Args:
            valid_count: 有效代理数量
            total_count: 总代理数量
        """
        # 隐藏进度条
        self.ui.show_validation_progress(False)
        
        # 刷新当前页显示最新的验证结果
        await self.load_proxies_by_page(self._current_page)
        
        # 显示验证结果
        self.show_info(
            tilte="验证结果",
            msg=f"验证完成：有效代理 {valid_count} 个，无效代理 {total_count - valid_count} 个",
            type='success',
            duration=5000
        )
        
        # 更新加载状态
        self.loading_ui("验证完成", "代理验证已完成", False)
        
        # 高亮显示有效/无效代理
        for row in range(self.ui.proxy_table.rowCount()):
            status_item = self.ui.proxy_table.item(row, 7)
            if status_item:
                # 根据状态设置行背景色
                if "正常" in status_item.text():
                    # 可以设置行背景色为淡绿色
                    for col in range(self.ui.proxy_table.columnCount()):
                        item = self.ui.proxy_table.item(row, col)
                        if item:
                            item.setBackground(QColor(240, 255, 240))  # 淡绿色
                elif "失败" in status_item.text():
                    # 可以设置行背景色为淡红色
                    for col in range(self.ui.proxy_table.columnCount()):
                        item = self.ui.proxy_table.item(row, col)
                        if item:
                            item.setBackground(QColor(255, 240, 240))  # 淡红色

    def _on_table_double_clicked(self, row, column):
        """处理表格行双击事件，复制代理配置
        
        Args:
            row: 行索引
            column: 列索引
        """
        try:
            # 获取代理信息
            ip = self.ui.proxy_table.item(row, 1).text()
            port = self.ui.proxy_table.item(row, 2).text()
            username = self.ui.proxy_table.item(row, 3).text()
            password = self.ui.proxy_table.item(row, 4).text()
            protocol = self.ui.proxy_table.item(row, 6).text().lower()
            
            # 生成代理配置字符串
            if username != "-" and password != "-":
                # 有用户名和密码的格式
                proxy_config = f"{protocol}://{username}:{password}@{ip}:{port}"
            else:
                # 无用户名和密码的格式
                proxy_config = f"{protocol}://{ip}:{port}"
            
            # 复制到剪贴板
            from PySide6.QtGui import QGuiApplication
            clipboard = QGuiApplication.clipboard()
            clipboard.setText(proxy_config)
            
            # 显示提示
            self.show_info(
                tilte="复制成功",
                msg=f"代理配置已复制到剪贴板: {proxy_config}",
                type="success",
                duration=2000
            )
            
            self._logger.info(f"已复制代理配置: {proxy_config}")
            
        except Exception as e:
            self._logger.exception(f"复制代理配置时出错: {e}")
            self.show_info(
                tilte="复制失败",
                msg=f"复制代理配置失败: {str(e)}",
                type="error"
            )

    def _on_cell_entered(self, row, column):
        """处理鼠标进入单元格事件，显示提示信息
        
        Args:
            row: 行索引
            column: 列索引
        """
        try:
            # 确保行有效
            if row >= 0 and row < self.ui.proxy_table.rowCount():
                # 获取代理信息
                ip_item = self.ui.proxy_table.item(row, 1)
                port_item = self.ui.proxy_table.item(row, 2)
                protocol_item = self.ui.proxy_table.item(row, 6)
                
                if ip_item and port_item and protocol_item:
                    ip = ip_item.text()
                    port = port_item.text()
                    protocol = protocol_item.text().lower()
                    
                    # 构建提示信息
                    tooltip_text = f"双击此行可复制代理配置: {protocol}://{ip}:{port}"
                    
                    # 显示工具提示
                    QToolTip.showText(
                        self.ui.proxy_table.mapToGlobal(self.ui.proxy_table.visualItemRect(ip_item).topLeft()),
                        tooltip_text
                    )
        except Exception as e:
            self._logger.debug(f"显示提示信息时出错: {e}")
            # 这里不显示错误提示，因为这是非关键功能

    @asyncSlot()
    async def _on_export_proxies_clicked(self):
        """导出代理按钮点击处理"""
        self._logger.info("用户点击导出代理按钮")
        self.show_info(
                tilte="导出代理",
                msg=f"导出全部代理功能正在开发中,当前仅支持双击复制指定代理",
                type="error",
                duration=5000
            )
        # try:
        #     # 获取所有代理
        #     proxies, total = await self.controller.get_all_proxies()
            
        #     if not proxies:
        #         self.show_info(tilte="导出失败", msg="没有可导出的代理", type="warning")
        #         return
            
        #     # 打开文件保存对话框
        #     file_path, _ = QFileDialog.getSaveFileName(
        #         self,
        #         "导出代理",
        #         os.path.expanduser("~/proxies.txt"),
        #         "文本文件 (*.txt)"
        #     )
            
        #     if not file_path:
        #         self._logger.info("用户取消了导出操作")
        #         return
            
        #     # 显示加载状态
        #     self.loading_ui("导出中", "正在导出代理配置...", True)
            
        #     # 生成代理配置文本
        #     proxy_configs = []
        #     for proxy in proxies:
        #         if proxy.username and proxy.password:
        #             # 有用户名和密码
        #             config = f"{proxy.proxy_type}://{proxy.username}:{proxy.password}@{proxy.host}:{proxy.port}"
        #         else:
        #             # 无用户名和密码
        #             config = f"{proxy.proxy_type}://{proxy.host}:{proxy.port}"
        #         proxy_configs.append(config)
            
        #     # 写入文件
        #     with open(file_path, "w", encoding="utf-8") as f:
        #         f.write("\n".join(proxy_configs))
            
        #     # 更新加载状态
        #     self.loading_ui("导出完成", f"已成功导出 {len(proxy_configs)} 个代理配置", False)
            
        #     self.show_info(
        #         tilte="导出成功",
        #         msg=f"已成功导出 {len(proxy_configs)} 个代理配置到: {file_path}",
        #         type="success",
        #         duration=5000
        #     )
            
        # except Exception as e:
        #     self._logger.exception(f"导出代理时出错: {e}")
        #     self.loading_ui("导出失败", f"导出代理失败: {str(e)}", False)
        #     self.show_info(tilte="导出失败", msg=f"导出代理失败: {str(e)}", type="error")
