from typing import Optional, List, Dict
from ui.designer.send_message_manager_ui import SendMsg<PERSON>, TaskDetailDialog
from ui.views.add_msg_task_view import AddMsgView
from utils.logger import get_logger
from app.controllers.account_controller import AccountController
from PySide6.QtWidgets import Q<PERSON>idget, QDialog, QTableWidgetItem, QHeaderView, QMessageBox, QPushButton, QHBoxLayout, QListWidgetItem, QProgressBar, QLabel
from PySide6.QtCore import Qt, Signal, Slot, QDateTime, QTimer
from PySide6.QtGui import QColor
from qfluentwidgets import InfoBar, InfoBarPosition, StateToolTip, FluentIcon,TableWidget, PrimaryPushButton, Flyout,ProgressBar,PushButton
from qasync import asyncSlot
from app.controllers.send_msg_controller import SendMessageController
class SendMsgView(SendMsgUI):
    """Telegram消息发送管理视图"""
    
    def __init__(self, parent: Optional[QWidget] = None, account_controller: AccountController = None,
                 controller: Optional[SendMessageController] = None):
        """初始化消息发送视图"""
        super().__init__(parent)
        self.setObjectName("SendMsgView")
        if parent:
            self.setParent(parent)
            
        self._logger = get_logger("ui.views.send_msg")
        self._controller = controller
        self._account_controller = account_controller
        self._current_tasks: List[dict] = []  # 存储当前显示的任务列表（dict）
        self._progress_bars = {}  # 存储任务ID与进度条的映射

        if not self._controller:
            self._logger.warning("SendMessageController未提供给SendMsgView，UI将无法工作。")
        else:
            # 连接控制器信号
            self._connect_controller_signals()
            
        # 初始化UI组件连接
        self._init_connections()
        self.load_tasks()
        # 加载初始数据
    @asyncSlot()
    async def load_tasks(self):
        # 使用带统计的接口加载任务
        if not self._controller:
            return
        try:
            tasks = await self._controller.get_all_tasks_with_stats()
            self._current_tasks = tasks
            self._update_task_table(tasks)
            await self.load_global_stats()
        except Exception as e:
            self._logger.error(f"加载任务列表失败: {str(e)}", exc_info=True)
            InfoBar.error(
                title="加载失败",
                content=f"无法加载任务列表: {str(e)}",
                parent=self,
                position=InfoBarPosition.TOP,
                duration=3000
            )
        
    def _connect_controller_signals(self):
        """连接控制器的信号"""
        if not self._controller:
            return
        
        # 连接任务创建信号
        self._controller.task_created.connect(self._on_task_created)
        # 连接任务更新信号
        self._controller.task_updated.connect(self._on_task_updated)
        # 连接任务删除信号
        self._controller.task_deleted.connect(self._on_task_deleted)
        # 连接任务状态变更信号
        self._controller.task_status_changed.connect(self._on_task_status_changed)
        # 连接任务进度更新信号
        self._controller.task_progress_updated.connect(self._on_task_progress_updated)
        
    def _init_connections(self):
        """初始化按钮和事件连接"""
        # 添加任务按钮
        self.add_task_btn.clicked.connect(self._open_add_task_dialog)
        
        # 过滤器控件
        self.status_filter.currentIndexChanged.connect(self._apply_filters)
        self.time_filter.currentIndexChanged.connect(self._apply_filters)
        self.type_filter.currentIndexChanged.connect(self._apply_filters)
        self.search_box.textChanged.connect(self._apply_filters)
    
    def _open_add_task_dialog(self):
        """打开添加消息任务对话框"""
        if not self._controller or not self._account_controller:
            InfoBar.error(
                title="错误",
                content="系统组件未正确初始化，无法添加任务",
                parent=self,
                position=InfoBarPosition.TOP,
                duration=3000
            )
            return
            
        dialog = AddMsgView(
            account_controller=self._account_controller, 
            msg_controller=self._controller,
            parent=self
        )
        
        # 连接保存信号
        dialog.saveSignal.connect(self._on_task_saved)
        
        # 显示对话框
        dialog.exec()
    
   
    async def _load_tasks(self):
        """加载任务列表"""
        if not self._controller:
            return
            
        try:
            # 显示加载状态
            state_tooltip = StateToolTip("正在加载", "正在获取任务列表...", self)
            state_tooltip.move(self.width() // 2 - state_tooltip.width() // 2,
                              self.height() // 2 - state_tooltip.height() // 2)
            state_tooltip.show()
            
            # 获取任务列表（dict）
            tasks = await self._controller.get_all_tasks()
            self._current_tasks = tasks
            
            # 更新表格
            self._update_task_table(tasks)
            
            # 更新统计信息
            self._update_stats()
            
            # 关闭加载状态
            state_tooltip.setContent("加载完成")
            state_tooltip.setState(True)
            state_tooltip.hide()
            
        except Exception as e:
            self._logger.error(f"加载任务列表失败: {str(e)}", exc_info=True)
            InfoBar.error(
                title="加载失败",
                content=f"无法加载任务列表: {str(e)}",
                parent=self,
                position=InfoBarPosition.TOP,
                duration=3000
            )
    
    def _update_task_table(self, tasks: List[dict]):
        """更新任务表格"""
        self.task_table.setRowCount(0)  # 清空表格
        self._progress_bars = {}  # 清空进度条映射
        
        for i, task in enumerate(tasks):
            row_position = self.task_table.rowCount()
            self.task_table.insertRow(row_position)
            
            # 任务ID
            id_item = QTableWidgetItem(str(task.get("id", "")))
            self.task_table.setItem(row_position, 0, id_item)
            
            # 任务名称
            name_item = QTableWidgetItem(task.get("task_name", ""))
            self.task_table.setItem(row_position, 1, name_item)
            
            # 状态
            status_text = self._get_status_text(task.get("status", ""))
            status_color = self._get_status_color(task.get("status", ""))
            status_item = QTableWidgetItem(status_text)
            status_item.setForeground(QColor(status_color))
            self.task_table.setItem(row_position, 2, status_item)
            
            # 类型
            content_type = task.get("content_type", "未知")
            type_item = QTableWidgetItem(content_type)
            self.task_table.setItem(row_position, 3, type_item)
            
            # 目标数 - 显示 成功/失败/总数
            total_count = task.get('total_count', 0)
            success_count = task.get('success_count', 0)
            failed_count = task.get('failed_count', 0)
            colored_text = f"<font color='#19be6b'>{success_count}</font>/<font color='#ff9900'>{failed_count}</font>/<font color='black'>{total_count}</font>"
            stats_label = QLabel(colored_text)
            stats_label.setAlignment(Qt.AlignCenter)
            self.task_table.setCellWidget(row_position, 4, stats_label)
            
            # 进度栏 - (成功+失败)/总数
            progress_widget = QWidget()
            progress_layout = QHBoxLayout(progress_widget)
            progress_layout.setContentsMargins(4, 2, 4, 2)
            progress_bar = ProgressBar()
            progress_bar.setRange(0, total_count if total_count > 0 else 100)
            finished_count = success_count + failed_count
            if total_count > 0:
                progress_bar.setValue(finished_count)
                progress_percent = min(100, int((finished_count / total_count) * 100))
                progress_bar.setFormat(f"{progress_percent}%")
            else:
                progress_bar.setValue(0)
                progress_bar.setFormat("0%")
            # 根据任务状态设置进度条颜色
            status_value = task.get("status", "")
            if status_value == "completed":
                progress_bar.setStyleSheet("QProgressBar::chunk { background-color: #19be6b; }")
            elif status_value == "failed":
                progress_bar.setStyleSheet("QProgressBar::chunk { background-color: #ed4014; }")
            elif status_value == "running":
                progress_bar.setStyleSheet("QProgressBar::chunk { background-color: #2d8cf0; }")
            elif status_value == "paused":
                progress_bar.setStyleSheet("QProgressBar::chunk { background-color: #ff9900; }")
            progress_layout.addWidget(progress_bar)
            # 增加百分比标签
            percent_label = QLabel(f"{progress_percent if total_count > 0 else 0}%")
            percent_label.setAlignment(Qt.AlignVCenter | Qt.AlignLeft)
            percent_label.setMinimumWidth(40)
            progress_layout.addWidget(percent_label)
            self.task_table.setCellWidget(row_position, 5, progress_widget)
            self._progress_bars[str(task.get("id", ""))] = progress_bar
            # 创建时间
            create_time = task.get("created_at", "未知")
            time_item = QTableWidgetItem(create_time)
            self.task_table.setItem(row_position, 6, time_item)
            # 操作按钮
            control_widget = self._create_control_buttons(task, row_position)
            self.task_table.setCellWidget(row_position, 7, control_widget)
    
    def _create_control_buttons(self, task: dict, row_index):
        """创建操作按钮"""
        widget = QWidget()
        layout = QHBoxLayout(widget)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(5)
        
        # 获取任务ID
        task_id = task.get("id")
        
        # 创建一个闭包函数来正确捕获task_id
        def create_button_handler(tid, action):
            if action == "view":
                return lambda: self._view_task_detail(tid)
            elif action == "pause":
                return lambda: self._pause_task(tid)
            elif action == "resume":
                return lambda: self._resume_task(tid)
            elif action == "start":
                return lambda: self._start_task(tid)
            elif action == "delete":
                return lambda: self._delete_task(tid)
        
        # 查看按钮
        view_btn = PushButton("查看")
        view_btn.setEnabled(False)
        view_btn.setIcon(FluentIcon.VIEW)
        view_btn.setProperty("row", row_index)
        view_btn.setProperty("task_id", task_id)
        view_btn.clicked.connect(create_button_handler(task_id, "view"))
        
        # 启停按钮 - 根据状态动态设置
        toggle_btn = PushButton()
        toggle_btn.setProperty("row", row_index)
        toggle_btn.setProperty("task_id", task_id)
        
        status_value = task.get("status", "")
        if status_value == "running":
            toggle_btn.setText("暂停")
            toggle_btn.setIcon(FluentIcon.PAUSE)
            toggle_btn.clicked.connect(create_button_handler(task_id, "pause"))
        elif status_value == "paused":
            toggle_btn.setText("恢复")
            toggle_btn.setIcon(FluentIcon.PLAY)
            toggle_btn.clicked.connect(create_button_handler(task_id, "resume"))
        elif status_value == "pending":
            toggle_btn.setText("开始")
            toggle_btn.setIcon(FluentIcon.PLAY)
            toggle_btn.clicked.connect(create_button_handler(task_id, "start"))
        elif status_value == "completed" or status_value == "failed":
            toggle_btn.setText("已完成")
            toggle_btn.setIcon(FluentIcon.SYNC)
            toggle_btn.clicked.connect(lambda: self.show_info("提示", "任务已完成，无法重新开始", "warning"))
        
        # 删除按钮
        delete_btn = PushButton("删除")
        delete_btn.setIcon(FluentIcon.DELETE)
        delete_btn.setProperty("row", row_index)
        delete_btn.setProperty("task_id", task_id)
        delete_btn.clicked.connect(create_button_handler(task_id, "delete"))
        
        if status_value == "running":
            delete_btn.setEnabled(False)
        
        layout.addWidget(view_btn)
        layout.addWidget(toggle_btn)
        layout.addWidget(delete_btn)
        
        return widget
    
    def _get_status_text(self, status_value):
        status_map = {
            "pending": "待处理",
            "running": "运行中",
            "paused": "已暂停",
            "completed": "已完成",
            "failed": "已失败",
            "cancelled": "已取消"
        }
        return status_map.get(status_value, "未知")
    
    def _get_status_color(self, status_value):
        status_color_map = {
            "pending": "#909399",
            "running": "#2d8cf0",
            "paused": "#ff9900",
            "completed": "#19be6b",
            "failed": "#ed4014",
            "cancelled": "#909399"
        }
        return status_color_map.get(status_value, "#909399")
    
    def _update_stats(self):
        if not self._current_tasks:
            return
        total = len(self._current_tasks)
        running = sum(1 for t in self._current_tasks if t.get("status") == "running")
        completed = sum(1 for t in self._current_tasks if t.get("status") == "completed")
        failed = sum(1 for t in self._current_tasks if t.get("status") == "failed")
        paused = sum(1 for t in self._current_tasks if t.get("status") == "paused")
        today_sent = 0
        today = QDateTime.currentDateTime().date()
        for task in self._current_tasks:
            # 假设created_at为字符串"YYYY-MM-DD HH:MM:SS"
            try:
                if "created_at" in task and task["created_at"] != "未知":
                    task_date = QDateTime.fromString(task["created_at"], "yyyy-MM-dd HH:mm:ss").date()
                    if task_date == today:
                        today_sent += task.get("successful_sends", 0)
            except Exception:
                continue
        # 统计卡片更新略
    
    def _apply_filters(self):
        status_filter = self.status_filter.currentText()
        time_filter = self.time_filter.currentText()
        type_filter = self.type_filter.currentText()
        search_text = self.search_box.text().lower()
        filtered_tasks = []
        for task in self._current_tasks:
            if status_filter != "全部":
                task_status = self._get_status_text(task.get("status", ""))
                if status_filter != task_status:
                    continue
            if time_filter != "全部时间" and "created_at" in task:
                today = QDateTime.currentDateTime().date()
                try:
                    task_date = QDateTime.fromString(task["created_at"], "yyyy-MM-dd HH:mm:ss").date()
                except Exception:
                    continue
                if time_filter == "今天" and task_date != today:
                    continue
                elif time_filter == "昨天" and task_date != today.addDays(-1):
                    continue
                elif time_filter == "本周":
                    if (today.dayOfWeek() - task_date.dayOfWeek()) > 7 or task_date > today:
                        continue
                elif time_filter == "本月":
                    if task_date.month() != today.month() or task_date.year() != today.year():
                        continue
            if type_filter != "全部类型":
                content_type = task.get("content_type", "未知")
                if type_filter.replace("消息", "") != content_type:
                    continue
            if search_text:
                task_name = task.get("task_name", "").lower()
                task_id = str(task.get("id", "")).lower()
                if search_text not in task_name and search_text not in task_id:
                    continue
            filtered_tasks.append(task)
        self._update_task_table(filtered_tasks)
  
    @asyncSlot()
    async def _pause_task(self, task_id):
        if not self._controller:
            return
        try:
            success = await self._controller.update_task_status(str(task_id), "paused")
            if success:
                self.show_info("成功", f"任务 {task_id} 已暂停", "success")
                # 不再重新加载全部任务，状态变更事件会自动更新UI
            else:
                self.show_info("失败", f"无法暂停任务 {task_id}", "error")
        except Exception as e:
            self._logger.error(f"暂停任务 {task_id} 失败: {str(e)}", exc_info=True)
            self.show_info("错误", f"暂停任务时发生错误: {str(e)}", "error")
    
    @asyncSlot()
    async def _resume_task(self, task_id):
        if not self._controller:
            return
        try:
            success = await self._controller.update_task_status(str(task_id), "pending")
            if success:
                self.show_info("成功", f"任务 {task_id} 已恢复", "success")
                # 不再重新加载全部任务，状态变更事件会自动更新UI
            else:
                self.show_info("失败", f"无法恢复任务 {task_id}", "error")
        except Exception as e:
            self._logger.error(f"恢复任务 {task_id} 失败: {str(e)}", exc_info=True)
            self.show_info("错误", f"恢复任务时发生错误: {str(e)}", "error")
    
    @asyncSlot()
    async def _start_task(self, task_id):
        if not self._controller:
            return
        try:
            success, error_msg = await self._controller.start_task(str(task_id))
            if success:
                self.show_info("成功", f"任务 {task_id} 已启动", "success")
                # 不再重新加载全部任务，状态变更事件会自动更新UI
            else:
                self.show_info("启动失败", f"无法启动任务 {task_id}：{error_msg}", "error")
        except Exception as e:
            self.show_info("错误", f"启动任务时发生异常：{str(e)}", "error")
    
    @asyncSlot()
    async def _restart_task(self, task_id):
        """重新启动已完成或失败的任务"""
        if not self._controller:
            return
        
        try:
            # 先将任务状态改为待处理
            success = await self._controller.update_task_status(str(task_id), "pending")
            if success:
                # 然后启动任务
                success = await self._controller.start_task(str(task_id))
                if success:
                    self.show_info("成功", f"任务 {task_id} 已重新启动", "success")
                    await self._load_tasks()
                else:
                    self.show_info("失败", f"无法启动任务 {task_id}", "error")
            else:
                self.show_info("失败", f"无法将任务 {task_id} 状态重置为待处理", "error")
        except Exception as e:
            self._logger.error(f"重启任务 {task_id} 失败: {str(e)}", exc_info=True)
            self.show_info("错误", f"重启任务时发生错误: {str(e)}", "error")

    @asyncSlot()
    async def _delete_task(self, task_id):
        if not self._controller:
            return
        reply = QMessageBox.question(
            self, 
            "确认删除", 
            f"确定要删除任务 {task_id} 吗？此操作不可撤销。",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        if reply != QMessageBox.Yes:
            return
        try:
            success = await self._controller.delete_task(str(task_id))
            if success:
                self.show_info("成功", f"任务 {task_id} 已删除", "success")
                await self._load_tasks()
            else:
                self.show_info("失败", f"无法删除任务 {task_id}", "error")
        except Exception as e:
            self._logger.error(f"删除任务 {task_id} 失败: {str(e)}", exc_info=True)
            self.show_info("错误", f"删除任务时发生错误: {str(e)}", "error")
    

    def show_info(self, title, content, type='info', position = InfoBarPosition.TOP):
        """显示消息提示"""
        
        
        if type == 'success':
            InfoBar.success(
                title=title,
                content=content,
                orient=Qt.Horizontal,
                isClosable=True,
                position=position,
                duration=2000,
                parent=self
            )
        elif type == 'error':
            InfoBar.error(
                title=title,
                content=content,
                orient=Qt.Horizontal,
                isClosable=True,
                position=position,
                duration=3000,
                parent=self
            )
        elif type == 'warning':
            InfoBar.warning(
                title=title,
                content=content,
                orient=Qt.Horizontal,
                isClosable=True,
                position=position,
                duration=3000,
                parent=self
            )
        else:  # 默认为 info
            InfoBar.info(
                title=title,
                content=content,
                orient=Qt.Horizontal,
                isClosable=True,
                position=position,
                duration=2000,
                parent=self
            ) 

    @asyncSlot()
    async def _view_task_detail(self, task_id):
        """查看任务详情"""
        if not self._controller:
            return
        
        try:
            # 获取任务详情
            task = await self._controller.get_task_by_id(str(task_id))
            if not task:
                self.show_info("错误", f"无法获取任务 {task_id} 的详情", "error")
                return
            
            # 显示任务详情对话框
            detail_dialog = TaskDetailDialog(self)
            detail_dialog.setWindowTitle(f"任务详情 - {task.get('task_name', '')}")
            
            # 设置基本信息
            detail_dialog.task_name_label.setText(task.get("task_name", ""))
            detail_dialog.task_id_label.setText(str(task.get("id", "")))
            detail_dialog.status_label.setText(self._get_status_text(task.get("status", "")))
            #detail_dialog.created_time_label.setText(task.get("created_at", ""))
            
            # 设置统计信息
            total_targets = task.get("total_iterations", 0)
            if total_targets == 0:
                total_targets = len(task.get("target_ids", []))
           # detail_dialog.total_targets_label.setText(str(total_targets))
            
            processed = task.get("processed_iterations", 0)
            #detail_dialog.processed_label.setText(str(processed))
            
            success = task.get("successful_sends", 0)
            #detail_dialog.success_label.setText(str(success))
            
            failed = task.get("failed_sends", 0)
            #detail_dialog.failed_label.setText(str(failed))
            
            # 添加进度条到详情对话框
            # if not hasattr(detail_dialog, "progress_bar"):
            #     progress_bar = QProgressBar(detail_dialog)
            #     # 将进度条添加到布局中（尝试不同的布局）
            #     try:
            #         if hasattr(detail_dialog, "stats_layout"):
            #             detail_dialog.stats_layout.addWidget(progress_bar)
            #         elif hasattr(detail_dialog, "info_layout"):
            #             detail_dialog.info_layout.addWidget(progress_bar)
            #         else:
            #             # 根据对话框结构，找一个合适的布局添加
            #             detail_dialog.layout().addWidget(progress_bar)
            #     except Exception as e:
            #         self._logger.warning(f"无法添加进度条到详情对话框: {str(e)}")
            #     detail_dialog.progress_bar = progress_bar
            
            # if total_targets > 0:
            #     detail_dialog.progress_bar.setRange(0, total_targets)
            #     detail_dialog.progress_bar.setValue(processed)
            #     progress_percent = min(100, int((processed / total_targets) * 100))
            #     detail_dialog.progress_bar.setFormat(f"{progress_percent}%")
            # else:
            #     detail_dialog.progress_bar.setRange(0, 100)
            #     detail_dialog.progress_bar.setValue(0)
            #     detail_dialog.progress_bar.setFormat("0%")
            
            # 根据任务状态设置进度条颜色
            status = task.get("status", "")
            # if status == "completed":
            #     detail_dialog.progress_bar.setStyleSheet("QProgressBar::chunk { background-color: #19be6b; }")
            # elif status == "failed":
            #     detail_dialog.progress_bar.setStyleSheet("QProgressBar::chunk { background-color: #ed4014; }")
            # elif status == "running":
            #     detail_dialog.progress_bar.setStyleSheet("QProgressBar::chunk { background-color: #2d8cf0; }")
            # elif status == "paused":
            #     detail_dialog.progress_bar.setStyleSheet("QProgressBar::chunk { background-color: #ff9900; }")
            
            # 设置账户信息
            accounts_text = ""
            for account in task.get("accounts_info", []):
                accounts_text += f"{account.get('account_phone', '')}: {account.get('success_count', 0)}成功/{account.get('fail_count', 0)}失败\n"
           # detail_dialog.accounts_text.setPlainText(accounts_text)
            
            # 设置目标信息
            targets_text = ""
            for target in task.get("targets_info", []):
                status = "成功" if target.get("status") == "success" else "失败" if target.get("status") == "failed" else "待处理"
                targets_text += f"{target.get('target_id', '')}: {status}\n"
            #detail_dialog.targets_text.setPlainText(targets_text)
            
            # 设置消息内容
            content_text = ""
            for content in task.get("contents", []):
                if hasattr(content, "content") and content.content:
                    content_text += f"{content.content.text_content}\n\n"
            #detail_dialog.message_text.setPlainText(content_text)
            
            # 添加编辑按钮
            edit_btn = PushButton("编辑任务")
            edit_btn.setIcon(FluentIcon.EDIT)
            edit_btn.clicked.connect(lambda: self._edit_task(task))
           # detail_dialog.button_layout.addWidget(edit_btn)
            
            # 显示对话框
            detail_dialog.exec()
            
        except Exception as e:
            self._logger.error(f"查看任务详情时出错: {str(e)}", exc_info=True)
            self.show_info("错误", f"查看任务详情时出错: {str(e)}", "error")
    
    def _edit_task(self, task):
        """编辑任务"""
        if not self._controller or not self._account_controller:
            self.show_info("错误", "系统组件未正确初始化", "error")
            return
        
        try:
            # 创建编辑对话框
            dialog = AddMsgView(
                account_controller=self._account_controller,
                msg_controller=self._controller,
                parent=self
            )
            
            # 设置对话框为编辑模式
            dialog.setWindowTitle(f"编辑任务 - {task.get('task_name', '')}")
            
            # 填充任务数据
            dialog.taskName.setText(task.get("task_name", ""))
            
            # 设置消息内容
            if task.get("contents"):
                for content in task.get("contents"):
                    if hasattr(content, "content") and content.content:
                        msg_data = {
                            "type": "text",  # 默认为文本
                            "content": content.content.text_content,
                            "html_content": content.content.html_content,
                            "task_id": task.get("id")  # 添加任务ID以便编辑时使用
                        }
                        
                        # 根据media_type设置消息类型
                        if content.content.media_type == "photo":
                            if content.content.media_path:
                                msg_data["type"] = "image"
                            else:
                                msg_data["type"] = "image_text"
                        
                        dialog.msg_list.append(msg_data)
                        
                        # 添加到UI列表
                        truncated_text = (msg_data["content"][:30] + "...") if len(msg_data["content"]) > 30 else msg_data["content"]
                        display_text = f"[{len(dialog.msg_list)}] {truncated_text}"
                        
                        item = QListWidgetItem()
                        dialog.msgList.addItem(item)
                        dialog.msgList.setItemWidget(
                            item,
                            dialog._createMsgItemWidget(display_text, len(dialog.msg_list) - 1)
                        )
            
            # 设置发送间隔
            dialog.Str.setText(str(task.get("interval_seconds_min", 60)))
            dialog.LineEdit_3.setText(str(task.get("interval_seconds_max", 180)))
            dialog.LineEdit_4.setText(str(task.get("account_interval_min", 10)))
            dialog.LineEdit_5.setText(str(task.get("account_interval_max", 30)))
            
            # 设置目标类型
            target_type = task.get("target_type")
            if target_type == "users":
                if task.get("target_ids") and len(task.get("target_ids")) > 0:
                    dialog.CustomUser.setChecked(True)
                    dialog.UserRadioButton.setChecked(False)
                    dialog.MonitorTask.setChecked(False)
                    dialog._switchTargetMode("custom")
                    
                    # 设置自定义用户列表
                    dialog.CustomUserEdit.setPlainText("\n".join(task.get("target_ids", [])))
                else:
                    dialog.UserRadioButton.setChecked(True)
                    dialog.CustomUser.setChecked(False)
                    dialog.MonitorTask.setChecked(False)
                    dialog._switchTargetMode("user")
            elif target_type == "groups" or target_type == "channels":
                dialog.MonitorTask.setChecked(True)
                dialog.UserRadioButton.setChecked(False)
                dialog.CustomUser.setChecked(False)
                dialog._switchTargetMode("task")
                
                # 设置选中的监听任务
                # 这里需要在对话框显示后加载完监听任务列表后再设置选中状态
            
            # 账户选择将在对话框打开后通过 dialog 的 _on_group_index_changed 加载
            
            # 连接保存信号
            dialog.saveSignal.connect(self._on_task_saved)
            
            # 显示对话框
            dialog.exec()
            
        except Exception as e:
            self._logger.error(f"编辑任务时出错: {str(e)}", exc_info=True)
            self.show_info("错误", f"编辑任务时出错: {str(e)}", "error")
    @asyncSlot()
    async def _on_task_saved(self, task_data):
        """处理任务保存成功后的操作"""
        self._logger.info(f"任务已保存: {task_data.get('id')}")
        self.show_info("成功", "任务已保存", "success")
        
        # 刷新任务列表
        await self._load_tasks()

    @asyncSlot()
    async def _on_task_created(self, task_data):
        """处理任务创建事件"""
        self._logger.info(f"收到任务创建事件: {task_data.get('id')}")
        await self.load_tasks()
        await self.load_global_stats()
        self.show_info("任务创建成功", f"任务 '{task_data.get('task_name', '')}' 已创建", "success")
    
    @asyncSlot()
    async def _on_task_updated(self, task_data):
        """处理任务更新事件"""
        self._logger.info(f"收到任务更新事件: {task_data.get('id')}")
        await self.load_tasks()
        await self.load_global_stats()
    
    @asyncSlot()
    async def _on_task_deleted(self, task_id):
        """处理任务删除事件"""
        self._logger.info(f"收到任务删除事件: {task_id}")
        await self.load_tasks()
        await self.load_global_stats()
        self.show_info("任务已删除", f"任务 {task_id} 已被删除", "info")
    
    @asyncSlot()
    async def _on_task_status_changed(self, task_id, status):
        """处理任务状态变更事件"""
        status_text = self._get_status_text(status)
        self._logger.info(f"收到任务状态变更事件: {task_id} -> {status}")
        
        # 找到当前任务的统计数据，确保在状态变更时不丢失
        current_task = None
        for task in self._current_tasks:
            if str(task.get("id", "")) == str(task_id):
                current_task = task
                # 更新任务状态，但保留其他数据
                task["status"] = status
                break
                
        if not current_task:
            self._logger.warning(f"找不到任务ID为 {task_id} 的任务数据")
            return
            
        # 获取当前的统计数据
        total_count = current_task.get('total_count', 0)
        success_count = current_task.get('success_count', 0)
        failed_count = current_task.get('failed_count', 0)
        
        # 找到对应的任务项并更新状态
        for i in range(self.task_table.rowCount()):
            id_item = self.task_table.item(i, 0)
            if id_item and id_item.text() == str(task_id):
                # 更新状态文本和颜色
                status_item = self.task_table.item(i, 2)
                if status_item:
                    status_item.setText(status_text)
                    status_item.setForeground(QColor(self._get_status_color(status)))
                
                # 更新进度条颜色，但保持进度值不变
                if str(task_id) in self._progress_bars:
                    progress_bar = self._progress_bars[str(task_id)]
                    
                    # 确保进度条范围正确设置
                    if total_count > 0:
                        progress_bar.setRange(0, total_count)
                        finished_count = success_count + failed_count
                        progress_bar.setValue(finished_count)
                        progress_percent = min(100, int((finished_count / total_count) * 100))
                    else:
                        progress_bar.setRange(0, 100)
                        progress_bar.setValue(0)
                        progress_percent = 0
                        
                    # 根据状态设置颜色
                    if status == "completed":
                        progress_bar.setStyleSheet("QProgressBar::chunk { background-color: #19be6b; }")
                    elif status == "failed":
                        progress_bar.setStyleSheet("QProgressBar::chunk { background-color: #ed4014; }")
                    elif status == "running":
                        progress_bar.setStyleSheet("QProgressBar::chunk { background-color: #2d8cf0; }")
                    elif status == "paused":
                        progress_bar.setStyleSheet("QProgressBar::chunk { background-color: #ff9900; }")
                
                # 更新统计文本，保持原有数据
                stats_widget = self.task_table.cellWidget(i, 4)
                if stats_widget and isinstance(stats_widget, QLabel):
                    colored_text = f"<font color='#19be6b'>{success_count}</font>/<font color='#ff9900'>{failed_count}</font>/<font color='black'>{total_count}</font>"
                    stats_widget.setText(colored_text)
                    
                # 更新进度百分比标签
                progress_widget = self.task_table.cellWidget(i, 5)
                if progress_widget:
                    layout = progress_widget.layout()
                    if layout and layout.count() > 1:
                        percent_label = layout.itemAt(1).widget()
                        if percent_label and isinstance(percent_label, QLabel):
                            if total_count > 0:
                                finished_count = success_count + failed_count
                                progress_percent = min(100, int((finished_count / total_count) * 100))
                                percent_label.setText(f"{progress_percent}%")
                            else:
                                percent_label.setText("0%")
                
                # 更新操作按钮
                control_widget = self.task_table.cellWidget(i, 7)
                if control_widget:
                    layout = control_widget.layout()
                    # 索引1是开始/暂停/恢复按钮 (第二个按钮)
                    if layout and layout.count() > 1:
                        toggle_btn = layout.itemAt(1).widget()
                        if toggle_btn:
                            # 创建一个闭包函数来正确捕获task_id
                            def create_button_handler(tid, action):
                                if action == "pause":
                                    return lambda: self._pause_task(tid)
                                elif action == "resume":
                                    return lambda: self._resume_task(tid)
                                else:  # start
                                    return lambda: self._start_task(tid)
                            
                            # 断开之前的连接
                            try:
                                toggle_btn.clicked.disconnect()
                            except:
                                pass
                            
                            if status == "running":
                                toggle_btn.setText("暂停")
                                toggle_btn.setIcon(FluentIcon.PAUSE)
                                toggle_btn.clicked.connect(create_button_handler(task_id, "pause"))
                            elif status == "paused":
                                toggle_btn.setText("恢复")
                                toggle_btn.setIcon(FluentIcon.PLAY)
                                toggle_btn.clicked.connect(create_button_handler(task_id, "resume"))
                            elif status == "pending":
                                toggle_btn.setText("开始")
                                toggle_btn.setIcon(FluentIcon.PLAY)
                                toggle_btn.clicked.connect(create_button_handler(task_id, "start"))
                            elif status == "completed" or status == "failed":
                                toggle_btn.setText("已完成")
                                toggle_btn.setIcon(FluentIcon.SYNC)
                                toggle_btn.clicked.connect(lambda: self.show_info("提示", "任务已完成，无法重新开始", "warning"))
                    
                    # 索引2是删除按钮 (第三个按钮)
                    if layout and layout.count() > 2:
                        delete_btn = layout.itemAt(2).widget()
                        if delete_btn:
                            if status == "running":
                                delete_btn.setEnabled(False)
                            else:
                                delete_btn.setEnabled(True)
                
                break
        
        # 更新统计信息
        self._update_stats()
        await self.load_global_stats()
        # 事件末尾刷新统计卡片
        

    @asyncSlot()
    async def _on_task_progress_updated(self, task_id, processed, success, failed):
        """处理任务进度更新事件"""
        self._logger.debug(f"收到任务进度更新: {task_id}, 已处理: {processed}, 成功: {success}, 失败: {failed}")
        
        # 更新内存中的任务数据
        current_task = None
        for task in self._current_tasks:
            if str(task.get("id", "")) == str(task_id):
                current_task = task
                # 更新内存中的数据
                task['success_count'] = success
                task['failed_count'] = failed
                # 处理总数保持不变，除非它小于当前已处理的总数
                if task.get('total_count', 0) < (success + failed):
                    task['total_count'] = success + failed
                break
        
        # 如果找不到任务或目标总数为0，保持原有显示
        if not current_task:
            self._logger.warning(f"找不到任务ID为 {task_id} 的任务数据，无法更新进度")
            return
            
        # 获取当前任务的目标总数
        target_count = current_task.get('total_count', 0)
        if target_count == 0:
            # 尝试从其他字段获取
            target_count = len(current_task.get('target_ids', []))
            if target_count > 0:
                # 更新内存中的数据
                current_task['total_count'] = target_count
        
        # 找到对应的任务行并更新信息
        for i in range(self.task_table.rowCount()):
            id_item = self.task_table.item(i, 0)
            if id_item and id_item.text() == str(task_id):
                # 更新统计信息
                stats_label = self.task_table.cellWidget(i, 4)
                if stats_label:
                    colored_text = f"<font color='#19be6b'>{success}</font>/<font color='#ff9900'>{failed}</font>/<font color='black'>{target_count}</font>"
                    stats_label.setText(colored_text)
                
                # 更新进度条
                if str(task_id) in self._progress_bars:
                    progress_bar = self._progress_bars[str(task_id)]
                    progress_bar.setRange(0, target_count if target_count > 0 else 100)
                    progress_bar.setValue(processed)
                    progress_percent = min(100, int((processed / target_count) * 100) if target_count > 0 else 0)
                    progress_bar.setFormat(f"{progress_percent}%")
                    
                    # 获取进度条所在的单元格小部件
                    progress_widget = self.task_table.cellWidget(i, 5)
                    if progress_widget:
                        # 更新百分比标签
                        layout = progress_widget.layout()
                        if layout.count() > 1:  # 确保有百分比标签
                            percent_label = layout.itemAt(1).widget()
                            if percent_label:
                                percent_label.setText(f"{progress_percent}%")
                    
                    # 根据任务状态设置进度条颜色
                    status = current_task.get("status", "")
                    if status == "completed":
                        progress_bar.setStyleSheet("QProgressBar::chunk { background-color: #19be6b; }")
                    elif status == "failed":
                        progress_bar.setStyleSheet("QProgressBar::chunk { background-color: #ed4014; }")
                    elif status == "running":
                        progress_bar.setStyleSheet("QProgressBar::chunk { background-color: #2d8cf0; }")
                    elif status == "paused":
                        progress_bar.setStyleSheet("QProgressBar::chunk { background-color: #ff9900; }")
                break
        
        # 更新任务详情对话框（如果打开）
        for widget in self.findChildren(TaskDetailDialog):
            if hasattr(widget, "task_id_label") and widget.task_id_label.text() == str(task_id):
                if hasattr(widget, "processed_label"):
                    widget.processed_label.setText(str(processed))
                if hasattr(widget, "success_label"):
                    widget.success_label.setText(str(success))
                if hasattr(widget, "failed_label"):
                    widget.failed_label.setText(str(failed))
                
                if hasattr(widget, "progress_bar"):
                    widget.progress_bar.setRange(0, target_count)
                    widget.progress_bar.setValue(processed)
                    progress_percent = min(100, int((processed / target_count) * 100) if target_count > 0 else 0)
                    widget.progress_bar.setFormat(f"{progress_percent}%")
                break
        
        # 刷新统计信息
        self._update_stats()
        # 事件末尾刷新统计卡片
        await self.load_global_stats()


    async def load_global_stats(self):
        """异步加载全局统计数据并刷新统计卡片"""
        if not self._controller:
            return
        try:
            stats = await self._controller.get_global_stats()
            super().update_stats(stats)
        except Exception as e:
            self._logger.error(f"加载全局统计数据失败: {str(e)}", exc_info=True)
