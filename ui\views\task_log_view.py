#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
任务日志视图组件

实时显示任务执行状态和日志信息
"""

from typing import Dict, Any, List
from datetime import datetime

from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QTableWidget, QTableWidgetItem,
    QTextEdit, QSplitter, QLabel, QPushButton, QComboBox, QLineEdit,
    QHeaderView, QTabWidget, QProgressBar, QFrame
)
from PySide6.QtCore import Qt, QTimer, Signal, Slot
from PySide6.QtGui import QFont, QColor

from utils.logger import get_logger
from core.task_manager.task_service import task_service

logger = get_logger(__name__)


class TaskStatusWidget(QFrame):
    """任务状态显示组件"""
    
    def __init__(self):
        super().__init__()
        self.setFrameStyle(QFrame.StyledPanel)
        self.setup_ui()
        
    def setup_ui(self):
        layout = QVBoxLayout(self)
        
        # 标题
        title_label = QLabel("任务统计")
        title_label.setFont(QFont("Arial", 12, QFont.Bold))
        layout.addWidget(title_label)
        
        # 统计信息
        stats_layout = QHBoxLayout()
        
        self.total_label = QLabel("总计: 0")
        self.pending_label = QLabel("等待: 0")
        self.running_label = QLabel("运行: 0")
        self.completed_label = QLabel("完成: 0")
        self.failed_label = QLabel("失败: 0")
        self.cancelled_label = QLabel("取消: 0")
        
        # 设置样式
        self.pending_label.setStyleSheet("color: orange;")
        self.running_label.setStyleSheet("color: blue;")
        self.completed_label.setStyleSheet("color: green;")
        self.failed_label.setStyleSheet("color: red;")
        self.cancelled_label.setStyleSheet("color: gray;")
        
        stats_layout.addWidget(self.total_label)
        stats_layout.addWidget(self.pending_label)
        stats_layout.addWidget(self.running_label)
        stats_layout.addWidget(self.completed_label)
        stats_layout.addWidget(self.failed_label)
        stats_layout.addWidget(self.cancelled_label)
        stats_layout.addStretch()
        
        layout.addLayout(stats_layout)
        
    def update_stats(self, stats: Dict[str, int]):
        """更新统计信息"""
        self.total_label.setText(f"总计: {stats.get('total', 0)}")
        self.pending_label.setText(f"等待: {stats.get('pending', 0)}")
        self.running_label.setText(f"运行: {stats.get('running', 0)}")
        self.completed_label.setText(f"完成: {stats.get('completed', 0)}")
        self.failed_label.setText(f"失败: {stats.get('failed', 0)}")
        self.cancelled_label.setText(f"取消: {stats.get('cancelled', 0)}")


class TaskListWidget(QTableWidget):
    """任务列表组件"""
    
    task_selected = Signal(str)  # task_id
    
    def __init__(self):
        super().__init__()
        self.setup_ui()
        self.tasks_data = {}
        
    def setup_ui(self):
        # 设置列
        headers = ["任务ID", "名称", "分类", "状态", "优先级", "进度", "创建时间", "开始时间", "完成时间"]
        self.setColumnCount(len(headers))
        self.setHorizontalHeaderLabels(headers)
        
        # 设置表格属性
        self.setSelectionBehavior(QTableWidget.SelectRows)
        self.setAlternatingRowColors(True)
        self.setSortingEnabled(True)
        
        # 调整列宽
        header = self.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeToContents)  # 任务ID
        header.setSectionResizeMode(1, QHeaderView.Stretch)  # 名称
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)  # 分类
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)  # 状态
        header.setSectionResizeMode(4, QHeaderView.ResizeToContents)  # 优先级
        header.setSectionResizeMode(5, QHeaderView.ResizeToContents)  # 进度
        
        # 连接信号
        self.itemSelectionChanged.connect(self.on_selection_changed)
        
    def update_tasks(self, tasks: Dict[str, Dict]):
        """更新任务列表"""
        self.tasks_data = tasks
        
        # 清空表格
        self.setRowCount(0)
        
        # 添加任务
        for task_id, task_info in tasks.items():
            self.add_task_row(task_id, task_info)
            
    def add_task_row(self, task_id: str, task_info: Dict):
        """添加任务行"""
        row = self.rowCount()
        self.insertRow(row)
        
        # 任务ID
        self.setItem(row, 0, QTableWidgetItem(task_id[:8] + "..."))
        
        # 名称
        self.setItem(row, 1, QTableWidgetItem(task_info.get("name", "")))
        
        # 分类
        self.setItem(row, 2, QTableWidgetItem(task_info.get("category", "")))
        
        # 状态
        status_item = QTableWidgetItem(task_info.get("status", ""))
        status = task_info.get("status", "")
        if status == "running":
            status_item.setBackground(QColor(173, 216, 230))  # 浅蓝色
        elif status == "completed":
            status_item.setBackground(QColor(144, 238, 144))  # 浅绿色
        elif status == "failed":
            status_item.setBackground(QColor(255, 182, 193))  # 浅红色
        elif status == "cancelled":
            status_item.setBackground(QColor(211, 211, 211))  # 浅灰色
        self.setItem(row, 3, status_item)
        
        # 优先级
        self.setItem(row, 4, QTableWidgetItem(str(task_info.get("priority", ""))))
        
        # 进度
        progress = f"{task_info.get('progress', 0)}/{task_info.get('total', 100)}"
        self.setItem(row, 5, QTableWidgetItem(progress))
        
        # 时间信息
        created_at = task_info.get("created_at", "")
        if created_at:
            created_at = datetime.fromisoformat(created_at.replace('Z', '+00:00')).strftime("%H:%M:%S")
        self.setItem(row, 6, QTableWidgetItem(created_at))
        
        started_at = task_info.get("started_at", "")
        if started_at:
            started_at = datetime.fromisoformat(started_at.replace('Z', '+00:00')).strftime("%H:%M:%S")
        self.setItem(row, 7, QTableWidgetItem(started_at))
        
        completed_at = task_info.get("completed_at", "")
        if completed_at:
            completed_at = datetime.fromisoformat(completed_at.replace('Z', '+00:00')).strftime("%H:%M:%S")
        self.setItem(row, 8, QTableWidgetItem(completed_at))
        
        # 存储完整的task_id
        self.item(row, 0).setData(Qt.UserRole, task_id)
        
    def on_selection_changed(self):
        """选择变化时触发"""
        current_row = self.currentRow()
        if current_row >= 0:
            task_id_item = self.item(current_row, 0)
            if task_id_item:
                task_id = task_id_item.data(Qt.UserRole)
                if task_id:
                    self.task_selected.emit(task_id)


class TaskLogWidget(QTextEdit):
    """任务日志组件"""
    
    def __init__(self):
        super().__init__()
        self.setup_ui()
        self.current_task_id = None
        
    def setup_ui(self):
        self.setReadOnly(True)
        self.setFont(QFont("Consolas", 9))
        
    def show_task_logs(self, task_id: str, task_info: Dict):
        """显示任务日志"""
        self.current_task_id = task_id
        self.clear()
        
        # 显示任务基本信息
        self.append(f"=== 任务信息 ===")
        self.append(f"任务ID: {task_id}")
        self.append(f"名称: {task_info.get('name', '')}")
        self.append(f"分类: {task_info.get('category', '')}")
        self.append(f"状态: {task_info.get('status', '')}")
        self.append(f"描述: {task_info.get('description', '')}")
        self.append("")
        
        # 显示日志
        logs = task_info.get("logs", [])
        if logs:
            self.append("=== 执行日志 ===")
            for log in logs:
                timestamp = log.get("timestamp", "")
                level = log.get("level", "info")
                message = log.get("message", "")
                
                # 格式化时间
                if timestamp:
                    try:
                        dt = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
                        timestamp = dt.strftime("%H:%M:%S.%f")[:-3]
                    except:
                        pass
                
                # 根据日志级别设置颜色
                if level == "error":
                    self.append(f'<span style="color: red;">[{timestamp}] [{level.upper()}] {message}</span>')
                elif level == "warning":
                    self.append(f'<span style="color: orange;">[{timestamp}] [{level.upper()}] {message}</span>')
                elif level == "info":
                    self.append(f'<span style="color: blue;">[{timestamp}] [{level.upper()}] {message}</span>')
                else:
                    self.append(f"[{timestamp}] [{level.upper()}] {message}")
        else:
            self.append("暂无日志")
            
        # 滚动到底部
        self.moveCursor(self.textCursor().End)


class TaskLogView(QWidget):
    """任务日志视图主组件"""
    
    def __init__(self):
        super().__init__()
        self.setup_ui()
        self.setup_timer()
        self.connect_signals()
        
    def setup_ui(self):
        layout = QVBoxLayout(self)
        
        # 顶部控制栏
        control_layout = QHBoxLayout()
        
        # 刷新按钮
        self.refresh_btn = QPushButton("刷新")
        self.refresh_btn.clicked.connect(self.refresh_tasks)
        control_layout.addWidget(self.refresh_btn)
        
        # 清理按钮
        self.clear_btn = QPushButton("清理已完成")
        self.clear_btn.clicked.connect(self.clear_completed_tasks)
        control_layout.addWidget(self.clear_btn)
        
        # 状态过滤
        self.status_filter = QComboBox()
        self.status_filter.addItems(["全部", "等待", "运行", "完成", "失败", "取消"])
        self.status_filter.currentTextChanged.connect(self.filter_tasks)
        control_layout.addWidget(QLabel("状态:"))
        control_layout.addWidget(self.status_filter)
        
        # 分类过滤
        self.category_filter = QComboBox()
        self.category_filter.addItems(["全部", "账户", "消息", "监控", "邀请", "系统"])
        self.category_filter.currentTextChanged.connect(self.filter_tasks)
        control_layout.addWidget(QLabel("分类:"))
        control_layout.addWidget(self.category_filter)
        
        control_layout.addStretch()
        layout.addLayout(control_layout)
        
        # 任务统计
        self.status_widget = TaskStatusWidget()
        layout.addWidget(self.status_widget)
        
        # 主分割器
        splitter = QSplitter(Qt.Horizontal)
        
        # 任务列表
        self.task_list = TaskListWidget()
        splitter.addWidget(self.task_list)
        
        # 任务日志
        self.task_log = TaskLogWidget()
        splitter.addWidget(self.task_log)
        
        # 设置分割比例
        splitter.setSizes([400, 600])
        
        layout.addWidget(splitter)
        
    def setup_timer(self):
        """设置定时器"""
        self.timer = QTimer()
        self.timer.timeout.connect(self.refresh_tasks)
        self.timer.start(2000)  # 每2秒刷新一次
        
    def connect_signals(self):
        """连接信号"""
        self.task_list.task_selected.connect(self.on_task_selected)
        
        # 连接任务管理器信号
        task_service.task_submitted.connect(self.on_task_submitted)
        task_service.task_started.connect(self.on_task_started)
        task_service.task_completed.connect(self.on_task_completed)
        task_service.task_failed.connect(self.on_task_failed)
        task_service.task_cancelled.connect(self.on_task_cancelled)
        task_service.task_log.connect(self.on_task_log)
        
    @Slot()
    def refresh_tasks(self):
        """刷新任务列表"""
        try:
            # 获取所有任务
            all_tasks = task_service.get_all_tasks()
            
            # 应用过滤器
            filtered_tasks = self.apply_filters(all_tasks)
            
            # 更新任务列表
            self.task_list.update_tasks(filtered_tasks)
            
            # 更新统计信息
            stats = task_service.get_task_stats()
            self.status_widget.update_stats(stats)
            
        except Exception as e:
            logger.error(f"刷新任务列表失败: {e}")
            
    def apply_filters(self, tasks: Dict[str, Dict]) -> Dict[str, Dict]:
        """应用过滤器"""
        filtered = {}
        
        status_filter = self.status_filter.currentText()
        category_filter = self.category_filter.currentText()
        
        for task_id, task_info in tasks.items():
            # 状态过滤
            if status_filter != "全部":
                status_map = {
                    "等待": "pending",
                    "运行": "running", 
                    "完成": "completed",
                    "失败": "failed",
                    "取消": "cancelled"
                }
                if task_info.get("status") != status_map.get(status_filter):
                    continue
                    
            # 分类过滤
            if category_filter != "全部":
                category_map = {
                    "账户": "account",
                    "消息": "message",
                    "监控": "monitor",
                    "邀请": "invite",
                    "系统": "system"
                }
                if task_info.get("category") != category_map.get(category_filter):
                    continue
                    
            filtered[task_id] = task_info
            
        return filtered
        
    @Slot()
    def filter_tasks(self):
        """过滤任务"""
        self.refresh_tasks()
        
    @Slot()
    def clear_completed_tasks(self):
        """清理已完成的任务"""
        try:
            count = task_service.clear_completed_tasks()
            logger.info(f"已清理 {count} 个已完成的任务")
            self.refresh_tasks()
        except Exception as e:
            logger.error(f"清理任务失败: {e}")
            
    @Slot(str)
    def on_task_selected(self, task_id: str):
        """任务选择事件"""
        task_info = task_service.get_task_status(task_id)
        if task_info:
            self.task_log.show_task_logs(task_id, task_info)
            
    # 任务事件处理
    @Slot(str, dict)
    def on_task_submitted(self, task_id: str, task_info: dict):
        """任务提交事件"""
        pass  # 定时刷新会更新
        
    @Slot(str, dict)
    def on_task_started(self, task_id: str, task_info: dict):
        """任务开始事件"""
        pass  # 定时刷新会更新
        
    @Slot(str, object)
    def on_task_completed(self, task_id: str, result: object):
        """任务完成事件"""
        pass  # 定时刷新会更新
        
    @Slot(str, str)
    def on_task_failed(self, task_id: str, error: str):
        """任务失败事件"""
        pass  # 定时刷新会更新
        
    @Slot(str)
    def on_task_cancelled(self, task_id: str):
        """任务取消事件"""
        pass  # 定时刷新会更新
        
    @Slot(str, str, str, str)
    def on_task_log(self, task_id: str, timestamp: str, level: str, message: str):
        """任务日志事件"""
        # 如果当前显示的是这个任务的日志，实时更新
        if self.task_log.current_task_id == task_id:
            self.on_task_selected(task_id)
