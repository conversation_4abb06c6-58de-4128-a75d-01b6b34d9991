#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
任务管理视图模块
提供任务管理界面，显示和管理任务
"""

from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QTableWidget, QTableWidgetItem,
    QPushButton, QLabel, QHeaderView, QTabWidget, QTextEdit, QProgressBar,
    QComboBox, QMessageBox, QSplitter
)
from PySide6.QtCore import Qt, Slot, Signal, QTimer
from PySide6.QtGui import QColor, QBrush

from app.controllers.task_controller import task_controller
from core.task_manager import TaskStatus
from utils.logger import get_logger


class TaskManagerView(QWidget):
    """任务管理视图，显示和管理任务"""
    
    def __init__(self, parent=None):
        """初始化任务管理视图"""
        super().__init__(parent)
        self._logger = get_logger("ui.views.task_manager")
        self._init_ui()
        self._connect_signals()
        self._refresh_timer = QTimer(self)
        self._refresh_timer.timeout.connect(self._refresh_task_list)
        self._refresh_timer.start(5000)  # 每5秒刷新一次
        self._logger.info("任务管理视图已初始化")
    
    def _init_ui(self):
        """初始化UI"""
        self.setWindowTitle("任务管理")
        
        # 主布局
        main_layout = QVBoxLayout(self)
        
        # 创建分割器
        splitter = QSplitter(Qt.Vertical)
        main_layout.addWidget(splitter)
        
        # 上半部分 - 任务列表
        task_list_widget = QWidget()
        task_list_layout = QVBoxLayout(task_list_widget)
        
        # 任务列表标题和按钮
        header_layout = QHBoxLayout()
        header_label = QLabel("任务列表")
        header_label.setStyleSheet("font-weight: bold; font-size: 14px;")
        header_layout.addWidget(header_label)
        
        header_layout.addStretch()
        
        self.refresh_btn = QPushButton("刷新")
        self.stop_all_btn = QPushButton("停止所有")
        header_layout.addWidget(self.refresh_btn)
        header_layout.addWidget(self.stop_all_btn)
        
        task_list_layout.addLayout(header_layout)
        
        # 任务表格
        self.task_table = QTableWidget()
        self.task_table.setColumnCount(8)
        self.task_table.setHorizontalHeaderLabels([
            "任务ID", "名称", "类型", "状态", "进度", "成功", "失败", "操作"
        ])
        self.task_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.task_table.verticalHeader().setVisible(False)
        self.task_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.task_table.setEditTriggers(QTableWidget.NoEditTriggers)
        
        task_list_layout.addWidget(self.task_table)
        
        # 下半部分 - 任务详情
        task_detail_widget = QWidget()
        task_detail_layout = QVBoxLayout(task_detail_widget)
        
        # 任务详情标签页
        self.detail_tabs = QTabWidget()
        
        # 日志标签页
        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)
        self.detail_tabs.addTab(self.log_text, "日志")
        
        # 进度标签页
        progress_widget = QWidget()
        progress_layout = QVBoxLayout(progress_widget)
        
        # 进度信息
        progress_info_layout = QHBoxLayout()
        self.progress_label = QLabel("进度: 0/0")
        self.success_label = QLabel("成功: 0")
        self.failed_label = QLabel("失败: 0")
        progress_info_layout.addWidget(self.progress_label)
        progress_info_layout.addWidget(self.success_label)
        progress_info_layout.addWidget(self.failed_label)
        progress_info_layout.addStretch()
        
        progress_layout.addLayout(progress_info_layout)
        
        # 进度条
        self.progress_bar = QProgressBar()
        progress_layout.addWidget(self.progress_bar)
        
        # 最近处理信息
        recent_info_layout = QHBoxLayout()
        self.last_target_label = QLabel("最近目标: -")
        self.last_account_label = QLabel("使用账户: -")
        self.last_status_label = QLabel("状态: -")
        recent_info_layout.addWidget(self.last_target_label)
        recent_info_layout.addWidget(self.last_account_label)
        recent_info_layout.addWidget(self.last_status_label)
        recent_info_layout.addStretch()
        
        progress_layout.addLayout(recent_info_layout)
        progress_layout.addStretch()
        
        self.detail_tabs.addTab(progress_widget, "进度")
        
        task_detail_layout.addWidget(self.detail_tabs)
        
        # 添加到分割器
        splitter.addWidget(task_list_widget)
        splitter.addWidget(task_detail_widget)
        splitter.setSizes([300, 200])  # 设置初始大小
        
        # 初始刷新任务列表
        self._refresh_task_list()
    
    def _connect_signals(self):
        """连接信号和槽"""
        # 按钮点击事件
        self.refresh_btn.clicked.connect(self._refresh_task_list)
        self.stop_all_btn.clicked.connect(self._stop_all_tasks)
        
        # 任务控制器信号
        task_controller.task_added.connect(self._on_task_added)
        task_controller.task_removed.connect(self._on_task_removed)
        task_controller.task_status_changed.connect(self._on_task_status_changed)
        task_controller.task_progress_updated.connect(self._on_task_progress_updated)
        task_controller.task_log.connect(self._on_task_log)
        
        # 表格选择变化
        self.task_table.itemSelectionChanged.connect(self._on_task_selection_changed)
    
    @Slot()
    def _refresh_task_list(self):
        """刷新任务列表"""
        tasks = task_controller.get_all_tasks()
        self.task_table.setRowCount(len(tasks))
        
        for row, task in enumerate(tasks):
            # 任务ID
            task_id = task.get("task_id", "")
            id_item = QTableWidgetItem(task_id[:8] + "...")
            id_item.setData(Qt.UserRole, task_id)
            self.task_table.setItem(row, 0, id_item)
            
            # 名称
            name_item = QTableWidgetItem(task.get("name", ""))
            self.task_table.setItem(row, 1, name_item)
            
            # 类型
            type_item = QTableWidgetItem(task.get("task_type", "").replace("Task", ""))
            self.task_table.setItem(row, 2, type_item)
            
            # 状态
            status = task.get("status", "")
            status_item = QTableWidgetItem(status)
            
            # 根据状态设置颜色
            if status == "RUNNING":
                status_item.setBackground(QBrush(QColor(200, 230, 200)))
            elif status == "COMPLETED":
                status_item.setBackground(QBrush(QColor(200, 200, 255)))
            elif status == "FAILED":
                status_item.setBackground(QBrush(QColor(255, 200, 200)))
            elif status == "PAUSED":
                status_item.setBackground(QBrush(QColor(255, 255, 200)))
            
            self.task_table.setItem(row, 3, status_item)
            
            # 进度
            progress = task.get("progress", {})
            current = progress.get("current", 0)
            total = progress.get("total", 0)
            if total > 0:
                progress_text = f"{current}/{total} ({int(current/total*100)}%)"
            else:
                progress_text = f"{current}/{total}"
            progress_item = QTableWidgetItem(progress_text)
            self.task_table.setItem(row, 4, progress_item)
            
            # 成功
            success = progress.get("success", 0)
            success_item = QTableWidgetItem(str(success))
            self.task_table.setItem(row, 5, success_item)
            
            # 失败
            failed = progress.get("failed", 0)
            failed_item = QTableWidgetItem(str(failed))
            self.task_table.setItem(row, 6, failed_item)
            
            # 操作按钮
            action_widget = QWidget()
            action_layout = QHBoxLayout(action_widget)
            action_layout.setContentsMargins(0, 0, 0, 0)
            
            # 根据状态显示不同的按钮
            if status == "RUNNING":
                pause_btn = QPushButton("暂停")
                pause_btn.clicked.connect(lambda checked=False, tid=task_id: self._pause_task(tid))
                stop_btn = QPushButton("停止")
                stop_btn.clicked.connect(lambda checked=False, tid=task_id: self._stop_task(tid))
                action_layout.addWidget(pause_btn)
                action_layout.addWidget(stop_btn)
            elif status == "PAUSED":
                resume_btn = QPushButton("恢复")
                resume_btn.clicked.connect(lambda checked=False, tid=task_id: self._resume_task(tid))
                stop_btn = QPushButton("停止")
                stop_btn.clicked.connect(lambda checked=False, tid=task_id: self._stop_task(tid))
                action_layout.addWidget(resume_btn)
                action_layout.addWidget(stop_btn)
            elif status == "PENDING":
                start_btn = QPushButton("启动")
                start_btn.clicked.connect(lambda checked=False, tid=task_id: self._start_task(tid))
                action_layout.addWidget(start_btn)
            else:  # 完成、失败或取消状态
                remove_btn = QPushButton("移除")
                remove_btn.clicked.connect(lambda checked=False, tid=task_id: self._remove_task(tid))
                action_layout.addWidget(remove_btn)
            
            self.task_table.setCellWidget(row, 7, action_widget)
    
    def _on_task_selection_changed(self):
        """处理任务选择变化"""
        selected_items = self.task_table.selectedItems()
        if not selected_items:
            return
        
        row = selected_items[0].row()
        task_id_item = self.task_table.item(row, 0)
        if not task_id_item:
            return
        
        task_id = task_id_item.data(Qt.UserRole)
        task_info = task_controller.get_task_info(task_id)
        if not task_info:
            return
        
        # 更新进度信息
        self._update_task_detail(task_info)
    
    def _update_task_detail(self, task_info):
        """更新任务详情"""
        progress = task_info.get("progress", {})
        current = progress.get("current", 0)
        total = progress.get("total", 0)
        success = progress.get("success", 0)
        failed = progress.get("failed", 0)
        
        # 更新进度标签
        self.progress_label.setText(f"进度: {current}/{total}")
        self.success_label.setText(f"成功: {success}")
        self.failed_label.setText(f"失败: {failed}")
        
        # 更新进度条
        if total > 0:
            self.progress_bar.setMaximum(total)
            self.progress_bar.setValue(current)
        else:
            self.progress_bar.setMaximum(1)
            self.progress_bar.setValue(0)
        
        # 更新最近处理信息
        extra = progress.get("extra", {})
        self.last_target_label.setText(f"最近目标: {extra.get('last_target', '-')}")
        self.last_account_label.setText(f"使用账户: {extra.get('last_account', '-')}")
        self.last_status_label.setText(f"状态: {extra.get('last_status', '-')}")
    
    @Slot(str, dict)
    def _on_task_added(self, task_id, task_info):
        """处理任务添加事件"""
        self._refresh_task_list()
    
    @Slot(str)
    def _on_task_removed(self, task_id):
        """处理任务移除事件"""
        self._refresh_task_list()
    
    @Slot(str, str)
    def _on_task_status_changed(self, task_id, status):
        """处理任务状态变化事件"""
        self._refresh_task_list()
    
    @Slot(str, int, int, dict)
    def _on_task_progress_updated(self, task_id, current, total, extra_info):
        """处理任务进度更新事件"""
        # 查找对应的行
        for row in range(self.task_table.rowCount()):
            task_id_item = self.task_table.item(row, 0)
            if task_id_item and task_id_item.data(Qt.UserRole) == task_id:
                # 更新进度
                if total > 0:
                    progress_text = f"{current}/{total} ({int(current/total*100)}%)"
                else:
                    progress_text = f"{current}/{total}"
                self.task_table.item(row, 4).setText(progress_text)
                
                # 更新成功/失败数
                success = extra_info.get("success", 0)
                failed = extra_info.get("failed", 0)
                self.task_table.item(row, 5).setText(str(success))
                self.task_table.item(row, 6).setText(str(failed))
                
                # 如果当前选中的是这个任务，更新详情
                selected_items = self.task_table.selectedItems()
                if selected_items and selected_items[0].row() == row:
                    task_info = task_controller.get_task_info(task_id)
                    if task_info:
                        self._update_task_detail(task_info)
                
                break
    
    @Slot(str, str, str)
    def _on_task_log(self, task_id, message, level):
        """处理任务日志事件"""
        # 查找对应的行
        selected_items = self.task_table.selectedItems()
        if not selected_items:
            return
        
        row = selected_items[0].row()
        task_id_item = self.task_table.item(row, 0)
        if not task_id_item or task_id_item.data(Qt.UserRole) != task_id:
            return
        
        # 根据级别设置颜色
        color = "black"
        if level == "error":
            color = "red"
        elif level == "warning":
            color = "orange"
        elif level == "info":
            color = "blue"
        elif level == "debug":
            color = "gray"
        
        # 添加日志
        self.log_text.append(f'<span style="color:{color}">[{level.upper()}] {message}</span>')
    
    def _start_task(self, task_id):
        """启动任务"""
        success = task_controller.start_task(task_id)
        if not success:
            QMessageBox.warning(self, "启动失败", f"任务 {task_id} 启动失败")
    
    def _pause_task(self, task_id):
        """暂停任务"""
        success = task_controller.pause_task(task_id)
        if not success:
            QMessageBox.warning(self, "暂停失败", f"任务 {task_id} 暂停失败")
    
    def _resume_task(self, task_id):
        """恢复任务"""
        success = task_controller.resume_task(task_id)
        if not success:
            QMessageBox.warning(self, "恢复失败", f"任务 {task_id} 恢复失败")
    
    def _stop_task(self, task_id):
        """停止任务"""
        reply = QMessageBox.question(
            self, "确认停止", f"确定要停止任务 {task_id} 吗？",
            QMessageBox.Yes | QMessageBox.No, QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            success = task_controller.stop_task(task_id)
            if not success:
                QMessageBox.warning(self, "停止失败", f"任务 {task_id} 停止失败")
    
    def _remove_task(self, task_id):
        """移除任务"""
        reply = QMessageBox.question(
            self, "确认移除", f"确定要移除任务 {task_id} 吗？",
            QMessageBox.Yes | QMessageBox.No, QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            success = task_controller.remove_task(task_id)
            if not success:
                QMessageBox.warning(self, "移除失败", f"任务 {task_id} 移除失败")
    
    def _stop_all_tasks(self):
        """停止所有任务"""
        reply = QMessageBox.question(
            self, "确认停止所有", "确定要停止所有任务吗？",
            QMessageBox.Yes | QMessageBox.No, QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            task_controller.stop_all_tasks()
            self._refresh_task_list()
    
    def closeEvent(self, event):
        """关闭事件处理"""
        self._refresh_timer.stop()
        super().closeEvent(event) 