"""
账户管理页面示例
展示如何集成进度监控器
"""
from PySide6.QtWidgets import QWidget, QVBoxLayout
from ui.common.progress_monitor import progress_monitor

class AccountPage(QWidget):
    """账户管理页面"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.init_ui()
        
        # 设置进度监控器的父窗口
        progress_monitor.set_parent(self)
    
    def init_ui(self):
        """初始化UI"""
        layout = QVBoxLayout(self)
        # 添加页面组件
        
        self.setLayout(layout)
    
    def showEvent(self, event):
        """窗口显示事件"""
        super().showEvent(event)
        # 当页面显示时，更新进度监控器的父窗口
        progress_monitor.set_parent(self)
    
    def hideEvent(self, event):
        """窗口隐藏事件"""
        super().hideEvent(event)
        # 当页面隐藏时，可以选择取消设置进度监控器的父窗口
        # progress_monitor.set_parent(None)

# 注意：这只是一个示例，实际应用中需要根据具体情况进行调整 