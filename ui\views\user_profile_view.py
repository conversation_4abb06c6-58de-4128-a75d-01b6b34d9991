from PySide6.QtWidgets import QMessageBox, QInputDialog
from qfluentwidgets import InfoBarPosition,InfoBar,MessageBoxBase,SubtitleLabel,LineEdit,CaptionLabel
from PySide6.QtGui import QCloseEvent, QIcon,QColor
from PySide6.QtCore import Signal,Qt, QRect
from PySide6.QtWidgets import QApplication
import datetime
from PySide6.QtGui import QColor
import re
from qasync import asyncSlot
from ui.dialogs.user_profile_dialog import UserInfoUI
from app.controllers.auth_controller import AuthController
from core.auth.models import user_info


class AcviteCodeMessageBox(MessageBoxBase):
    """ Custom message box """

    def __init__(self, parent=None):
        super().__init__(parent)
        self.titleLabel = SubtitleLabel('输入激活码', self)
        self.acviteCode = LineEdit(self)

        self.acviteCode.setPlaceholderText('输入您的上级代码提供的激活码')
        self.acviteCode.setClearButtonEnabled(True)

        self.warningLabel = CaptionLabel("输入您的激活码进行激活")
        self.warningLabel.setTextColor("#cf1010", QColor(255, 28, 32))

        # add widget to view layout
        self.viewLayout.addWidget(self.titleLabel)
        self.viewLayout.addWidget(self.acviteCode)
        self.viewLayout.addWidget(self.warningLabel)
        self.warningLabel.hide()

        # change the text of button
        self.yesButton.setText('激活')
        self.cancelButton.setText('取消')

        self.widget.setMinimumWidth(350)

        # self.hideYesButton()

    def validate(self):
        """ Rewrite the virtual method """
        isValid = self.acviteCode.text().strip()
        # self.warningLabel.setHidden(isValid)
        # self.acviteCode.setError(not isValid)
        return isValid

class UserInfoView(UserInfoUI):
    closView = Signal(bool)
    def __init__(self,auth_controller: AuthController,parent=None):
        super().__init__(parent)
        self.setWindowTitle("用户信息")
        self.controller = auth_controller
        self.main_window_geometry = None
        self.init_ui()
        self.manage_signals()
        
    def setMainWindowGeometry(self, geometry):
        """设置主窗口的几何信息用于居中显示"""
        self.main_window_geometry = geometry
        self.center_window()
        
    def init_ui(self):
        style = """
        TransparentPushButton {
            color: #222;
        }
        TransparentPushButton:hover {
            color: red;
        }
    """
        # 设置退出按钮悬停变红色字体
        self.Layout.setStyleSheet(style)
        self.CopyInviteCode.setStyleSheet(style)
        self.OnlineShop.setStyleSheet(style)
        self.AvatarWidget.setImage(":/app/images/touxiang.svg")
        # 设置头像大小和缩放，防止溢出
        self.AvatarWidget.setFixedSize(100, 100)  # 你可以根据UI实际需求调整尺寸        
        self.AvatarWidget.setScaledContents(True)
        
        # 设置窗口左上角图标
        self.setWindowIcon(QIcon(":/app/images/logo.png"))
        self.init_user_info()
        
        # 显示用户信息
    def init_user_info(self):
        self.username.setText(user_info.name or user_info.account or "未登录")
        self.UserType.setText("UID: " + (user_info.uid or "-"))
        self.VipGroup.setText(user_info.invID or "-")
        expire_str = user_info.vipExpDate or user_info.vipExpTime or "-"
        self.EndTime.setText(expire_str)
        self.set_expire_time_color(expire_str)  #  设置到期时间颜色
        self.InviteCode.setText(user_info.uid or "-")
        self.Phone.setText(user_info.phone or "未绑定")
        self.CaptionLabel_3.setText(user_info.email or "未绑定")
        self.password.setText("******")
        # 设置邀请人
        self.InviterLabel.setText("邀请人")
        inviter = user_info.invID or "无"
        self.InviterValue.setText(inviter)

    def manage_signals(self):
        self.OnlineShop.clicked.connect(self.on_online_shop)
        self.Layout.clicked.connect(self.on_logout)
        self.ActivateCode.clicked.connect(self.on_activate_code)
        self.ChanagePhone.clicked.connect(self.on_change_phone)
        self.ChanageEmail.clicked.connect(self.on_change_email)
        self.ChangePassword.clicked.connect(self.on_change_password)
        self.ViewDocs.clicked.connect(self.on_view_docs)
        self.CopyInviteCode.clicked.connect(self.on_copy_invite_code)
        self.InviteCode.clicked.connect(self.on_copy_invite_code)

    def center_window(self):
        """将窗口居中显示在主窗口上"""
        if self.main_window_geometry:
            main_geometry = self.main_window_geometry
            self_size = self.size()
            
            # 计算居中位置
            x = main_geometry.x() + (main_geometry.width() - self_size.width()) // 2
            y = main_geometry.y() + (main_geometry.height() - self_size.height()) // 2
            
            self.move(x, y)

    def showEvent(self, event):
        """窗口显示事件，确保每次显示时都居中"""
        super().showEvent(event)
        self.center_window()

    def on_copy_invite_code(self):
        text = self.InviteCode.text().replace(' ', '')
        
        clipboard = QApplication.clipboard()
        clipboard.setText(text)
        self.show_info(f"邀请码{text}已复制", "已复制", 'success')

    def on_online_shop(self):
        self.show_info('在线购买功能未实现', '在线购买')

    def on_logout(self):
        self.logout()
    @asyncSlot()
    async def on_activate_code(self):
        w = AcviteCodeMessageBox(self)
        if w.exec():
            code = w.validate()
            if code and code.strip():
                self.show_info(f'激活码 {code} 提交成功，正在充值…', '激活', 'info')
                # 调用controller.kami_topup
               
                result = await self.controller.kami_topup(code)
                if result.get('success'):
                    self.show_info('充值成功！', '激活', 'success')
                    # 充值成功后刷新用户信息                    
                    await self.controller.get_user_info()
                    self.init_user_info()
                else:
                    self.show_info(f"充值失败：{result.get('message')}", '激活', 'error')
            else:
                self.show_info('激活码不能为空', '激活', 'warning')
        # 用户取消则不提示

    def on_change_phone(self):
        self.show_info('修改手机功能未实现', '修改手机')

    def on_change_email(self):
        self.show_info('修改邮箱功能未实现', '修改邮箱')

    def on_change_password(self):
        self.show_info('修改密码功能未实现', '修改密码')

    def on_view_docs(self):
        self.show_info('查看教程功能未实现', '教程')

    def goto_center(self):
        self.show_info('进入个人中心功能未实现', '个人中心')

    def logout(self):
        reply = QMessageBox.question(self, '退出登录', '确定要退出登录吗？', QMessageBox.Yes | QMessageBox.No)
        if reply == QMessageBox.Yes:
            self.close() 

    def show_info(self, msg, title="提醒", type="info",duration=3000):
        if type == "info":
            InfoBar.info(
            title=title,
            content=msg,
            orient=Qt.Horizontal,
            isClosable=True,
            position=InfoBarPosition.TOP,
            duration=duration,
            parent=self)
        elif type=="success":
            InfoBar.success(
            title=title,
            content=msg,
            orient=Qt.Horizontal,
            isClosable=True,
            position=InfoBarPosition.TOP,
            duration=duration,
            parent=self
        )
        elif type=="warning":
            InfoBar.warning(
            title=title,
            content=msg,
            orient=Qt.Horizontal,
            isClosable=True,
            position=InfoBarPosition.TOP,
            duration=duration,
            parent=self
        )
        elif type=="error":
            InfoBar.error(
            title=title,
            content=msg,
            orient=Qt.Horizontal,
            isClosable=True,
            position=InfoBarPosition.TOP,
            duration=duration,
            parent=self
        )
        else:
            InfoBar.info(
            title=title,
            content=msg,
            orient=Qt.Horizontal,
            isClosable=True,
            position=InfoBarPosition.TOP,
            duration=duration,
            parent=self
        )
            

    def closeEvent(self, event: QCloseEvent):
        parent = self.parent()
        # 关闭时通知主窗口可再次打开用户信息页
        self.closView.emit(True)
        super().closeEvent(event)

    def set_expire_time_color(self, expire_str):
        """
        根据到期时间设置EndTime控件颜色：
        - 已过期：灰色
        - 7天内：橙色
        - 大于7天：红色
        """

        if not expire_str or expire_str == "-":
            self.EndTime.setStyleSheet("color: #cf1010;")
            return
        # 尝试解析日期
        # 支持格式如 2024-07-01 23:59:59 或 2024-07-01
        match = re.match(r"(\d{4}-\d{2}-\d{2})(?:[ T](\d{2}:\d{2}:\d{2}))?", expire_str)
        if not match:
            self.EndTime.setStyleSheet("color: #cf1010;")
            return
        date_part = match.group(1)
        time_part = match.group(2) or "23:59:59"
        try:
            expire_dt = datetime.datetime.strptime(f"{date_part} {time_part}", "%Y-%m-%d %H:%M:%S")
            now = datetime.datetime.now()
            delta = (expire_dt - now).total_seconds()
            if delta <= 0:
                # 已过期
                self.EndTime.setStyleSheet("color: #888888;")
            elif delta <= 7*24*3600:
                # 7天内
                self.EndTime.setStyleSheet("color: orange;")
            else:
                # 大于7天
                self.EndTime.setStyleSheet("color: red;")
        except Exception:
            self.EndTime.setStyleSheet("color: #cf1010;")

