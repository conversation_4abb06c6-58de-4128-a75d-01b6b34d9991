from PySide6.QtCore import Qt, Signal
from PySide6.QtWidgets import QWidget, QVBoxLayout, QHBoxLayout, QTableWidgetItem, QWidget,QLabel
from PySide6.QtGui import QColor, QCursor
from qfluentwidgets import TableWidget, PushButton, FluentIcon, ToolButton
from datetime import datetime
from PySide6.QtWidgets import QHeaderView, QMenu

class AccountTableWidget(QWidget):
    """账户表格组件"""
    # 操作信号
    refresh_account_requested = Signal(str)  # 刷新单个账号状态，传递手机号
    edit_account_requested = Signal(str)     # 编辑账号信息，传递手机号
    delete_account_requested = Signal(str)   # 删除账号，传递手机号
    verify_account_requested = Signal(str)   # 验证账号，传递手机号
    select_all_toggled = Signal(bool)        # 全选/取消全选，传递选中状态
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.selected_rows = set()  # 存储被选中的行索引
        
        self.init_ui()
    
    def init_ui(self):
        """初始化UI"""
        self.layout = QVBoxLayout(self)
        self.layout.setContentsMargins(0, 0, 0, 0)
        
        # 创建表格
        self.table = TableWidget(self)
        self.init_table()
        
        # 底部控制区域
        bottom_layout = QHBoxLayout()
        
        # 表格底部按钮组（居中对齐）
        bottom_btn_layout = QHBoxLayout()
        self.select_all_btn = PushButton('全选/取消', self, icon=FluentIcon.CHECKBOX)
        self.refresh_all_btn = PushButton('全部刷新', self, icon=FluentIcon.SYNC)
        self.batch_edit_btn = PushButton('全部设置', self, icon=FluentIcon.EDIT)
        
        bottom_btn_layout.addStretch()  # 添加弹性空间使按钮居中
        bottom_btn_layout.addWidget(self.select_all_btn)
        bottom_btn_layout.addWidget(self.refresh_all_btn)
        bottom_btn_layout.addWidget(self.batch_edit_btn)
        bottom_btn_layout.addStretch()  # 添加弹性空间使按钮居中
        
        # 右侧分页控制
        page_layout = QHBoxLayout()
        self.total_label = QLabel('共 0 页')
        self.prev_page_btn = PushButton('上一页')
        self.next_page_btn = PushButton('下一页')
        
        page_layout.addWidget(self.total_label)
        page_layout.addWidget(self.prev_page_btn)
        page_layout.addWidget(self.next_page_btn)
        
        # 将两个布局添加到底部总布局
        bottom_layout.addLayout(bottom_btn_layout, 1)  # 使用权重1
        bottom_layout.addLayout(page_layout)
        
        self.layout.addWidget(self.table)
        self.layout.addLayout(bottom_layout)
        
        # 连接信号
        self.table.setContextMenuPolicy(Qt.ContextMenuPolicy.CustomContextMenu)
        #self.table.customContextMenuRequested.connect(self.show_context_menu)
        self.select_all_btn.clicked.connect(self.toggle_select_all)
    
    def init_table(self):
        """初始化表格结构"""
        self.table.setColumnCount(6)
        self.table.setHorizontalHeaderLabels([
            '手机号', 'Username', '昵称', '在线状态', '创建时间', '操作'
        ])
        
        # 设置列宽
        self.table.horizontalHeader().setSectionResizeMode(0, QHeaderView.ResizeMode.Interactive) # 手机号
        self.table.horizontalHeader().setSectionResizeMode(1, QHeaderView.ResizeMode.Interactive) # Username
        self.table.horizontalHeader().setSectionResizeMode(2, QHeaderView.ResizeMode.Interactive) # 昵称
        self.table.horizontalHeader().setSectionResizeMode(3, QHeaderView.ResizeMode.Interactive) # 在线状态
        self.table.horizontalHeader().setSectionResizeMode(4, QHeaderView.ResizeMode.Interactive) # 创建时间
        self.table.horizontalHeader().setSectionResizeMode(5, QHeaderView.ResizeMode.Stretch)   # 操作列拉伸

        self.table.setColumnWidth(0, 130)  # 手机号
        self.table.setColumnWidth(1, 150)  # Username
        self.table.setColumnWidth(2, 150)  # 昵称
        self.table.setColumnWidth(3, 80)   # 在线状态
        self.table.setColumnWidth(4, 140)  # 创建时间
        # 操作列将自动拉伸填充剩余空间
    
    def update_table(self, accounts):
        """更新表格数据
        
        Args:
            accounts: 账户数据列表
        """
        # 保存当前选中状态
        selected_phones = {
            self.table.item(row, 0).text() 
            for row in self.selected_rows 
            if row < self.table.rowCount() and self.table.item(row, 0)
        }
        
        # 清空表格
        self.table.setRowCount(0)
        self.selected_rows.clear()
        
        # 添加账户行
        for row, account in enumerate(accounts):
            self.table.insertRow(row)
            
            # 手机号 (Column 0)
            phone = account.get('phone', '')
            phone_item = QTableWidgetItem(phone)
            self.table.setItem(row, 0, phone_item)
            
            # Username (Column 1)
            username = account.get('username', '')
            username_item = QTableWidgetItem(username)
            self.table.setItem(row, 1, username_item)

            # 昵称 (Column 2)
            first_name = account.get('first_name', '')
            last_name = account.get('last_name', '')
            nickname_str = f"{first_name} {last_name}".strip()
            if not nickname_str: # Fallback if no first/last name
                nickname_str = username # Or keep it empty: ''
            nickname_item = QTableWidgetItem(nickname_str)
            self.table.setItem(row, 2, nickname_item)
            
            # 在线状态 (Column 3)
            is_connected = account.get('is_connected', False)
            status_text = "在线" if is_connected else "离线"
            status_item = QTableWidgetItem(status_text)
            if is_connected:
                status_item.setForeground(QColor('#28a745'))  # 绿色 - 在线
            else:
                status_item.setForeground(QColor('#6c757d'))  # 灰色 - 离线
            self.table.setItem(row, 3, status_item)
            
            # 创建时间 (Column 4)
            created_at_iso = account.get('created_at', '')
            formatted_created_at = ""
            if created_at_iso:
                try:
                    # Assuming created_at_iso is a string like "2023-10-26T10:00:00" or "2023-10-26T10:00:00Z"
                    if 'Z' in created_at_iso:
                         dt_obj = datetime.fromisoformat(created_at_iso.replace("Z", "+00:00"))
                    else:
                         dt_obj = datetime.fromisoformat(created_at_iso)
                    formatted_created_at = dt_obj.strftime('%Y-%m-%d %H:%M')
                except ValueError:
                    formatted_created_at = created_at_iso # Fallback if parsing fails
            else:
                formatted_created_at = '未知'
            created_at_item = QTableWidgetItem(formatted_created_at)
            self.table.setItem(row, 4, created_at_item)
            
            # 操作按钮单元格 (Column 5)
            operation_widget = QWidget()
            operation_layout = QHBoxLayout(operation_widget)
            operation_layout.setContentsMargins(8, 4, 8, 4)  # 增加左右边距
            operation_layout.setSpacing(8)  # 增加按钮之间的间距
            
            # 刷新按钮
            refresh_btn = ToolButton()
            refresh_btn.setIcon(FluentIcon.SYNC)
            #refresh_btn.setFixedWidth(20)  # 设置固定宽度
            refresh_btn.setToolTip('刷新状态')
            refresh_btn.clicked.connect(lambda checked, p=phone: self.refresh_account_requested.emit(p))
            
            # 编辑按钮
            edit_btn = ToolButton()
            edit_btn.setIcon(FluentIcon.EDIT)
            #edit_btn.setFixedWidth(20)  # 设置固定宽度
            edit_btn.setToolTip('编辑信息')
            edit_btn.clicked.connect(lambda checked, p=phone: self.edit_account_requested.emit(p))
            # 删除按钮
            delete_btn = ToolButton()
            delete_btn.setIcon(FluentIcon.DELETE)
            #delete_btn.setFixedWidth(20)  # 设置固定宽度
            delete_btn.setToolTip('删除账号')
            delete_btn.clicked.connect(lambda checked, p=phone: self.delete_account_requested.emit(p))
            
            # 添加按钮到布局
            operation_layout.addWidget(refresh_btn)
            operation_layout.addWidget(edit_btn)
            operation_layout.addWidget(delete_btn)
            operation_layout.addStretch()
            
            # 设置单元格的widget
            self.table.setCellWidget(row, 5, operation_widget)
            
            # 恢复之前的选中状态
            if phone in selected_phones:
                self.selected_rows.add(row)
                for col in range(self.table.columnCount()):
                    if self.table.item(row, col):
                        self.table.item(row, col).setBackground(QColor('#E6F7FF'))  # 浅蓝色背景

    def update_single_account(self, updated_account):
        """更新单个账户的显示信息

        Args:
            updated_account: 更新后的账户字典
        """
        phone = updated_account.get('phone', '')
        if not phone:
            return

        # 查找对应的行
        target_row = -1
        for row in range(self.table.rowCount()):
            if self.table.item(row, 0) and self.table.item(row, 0).text() == phone:
                target_row = row
                break

        if target_row == -1:
            return  # 未找到对应行

        # 保存选中状态
        was_selected = target_row in self.selected_rows

        # 更新各列数据
        # Username (Column 1)
        username = updated_account.get('username', '')
        if self.table.item(target_row, 1):
            self.table.item(target_row, 1).setText(username)

        # 昵称 (Column 2)
        first_name = updated_account.get('first_name', '')
        last_name = updated_account.get('last_name', '')
        nickname_str = f"{first_name} {last_name}".strip()
        if not nickname_str:
            nickname_str = username
        if self.table.item(target_row, 2):
            self.table.item(target_row, 2).setText(nickname_str)

        # 在线状态 (Column 3)
        is_connected = updated_account.get('is_connected', False)
        status_text = "在线" if is_connected else "离线"
        if self.table.item(target_row, 3):
            status_item = self.table.item(target_row, 3)
            status_item.setText(status_text)
            if is_connected:
                status_item.setForeground(QColor('#28a745'))  # 绿色 - 在线
            else:
                status_item.setForeground(QColor('#6c757d'))  # 灰色 - 离线

        # 恢复选中状态
        if was_selected:
            for col in range(self.table.columnCount()):
                if self.table.item(target_row, col):
                    self.table.item(target_row, col).setBackground(QColor('#E6F7FF'))  # 浅蓝色背景

    def toggle_select_all(self):
        """切换全选/取消全选状态"""
        if len(self.selected_rows) == self.table.rowCount():
            # 已经全选，执行取消全选
            self.selected_rows.clear()
            for row in range(self.table.rowCount()):
                for col in range(self.table.columnCount()):
                    if self.table.item(row, col):
                        self.table.item(row, col).setBackground(QColor(Qt.white))  # 重置为白色背景
            self.select_all_toggled.emit(False)
        else:
            # 执行全选
            self.selected_rows = set(range(self.table.rowCount()))
            for row in range(self.table.rowCount()):
                for col in range(self.table.columnCount()):
                    if self.table.item(row, col):
                        self.table.item(row, col).setBackground(QColor('#E6F7FF'))  # 浅蓝色背景
            self.select_all_toggled.emit(True)
    
    def get_selected_phones(self):
        """获取选中的账户手机号列表
        
        Returns:
            选中的账户手机号列表
        """
        return [
            self.table.item(row, 0).text()
            for row in self.selected_rows
            if row < self.table.rowCount() and self.table.item(row, 0)
        ]

        
    def show_context_menu(self, pos):
        """显示上下文菜单"""
        menu = QMenu(self)
        menu.addAction('刷新状态', lambda: self.refresh_account_requested.emit(self.get_selected_phones()[0]))
        menu.addAction('编辑信息', lambda: self.edit_account_requested.emit(self.get_selected_phones()[0]))
        menu.addAction('删除账号', lambda: self.confirm_delete_account(self.get_selected_phones()[0]))
        menu.exec(self.table.mapToGlobal(pos))
    
