from PySide6.QtWidgets import (QWidget, QStackedWidget, QLabel, QLineEdit, 
                             QPushButton, QCheckBox, QHBoxLayout, QVBoxLayout,
                             QGridLayout, QFrame, QSizePolicy)
from PySide6.QtCore import Qt, Signal, QSize, QTimer
from PySide6.QtGui import QIcon, QPixmap, QColor, QPainter, QFont, QCursor
from qfluentwidgets import (CaptionLabel, ElevatedCardWidget, FluentIcon, 
                           IconWidget, TransparentToolButton)

class FeatureCard(ElevatedCardWidget):
    """自定义功能卡片组件"""
    
    def __init__(self, icon, title, description, parent=None):
        super().__init__(parent)
        
        # 创建图标组件
        self.iconWidget = IconWidget(icon, self)
        self.iconWidget.setFixedSize(48, 48)
        
        # 创建标题和描述标签
        self.titleLabel = CaptionLabel(title, self)
        self.titleLabel.setObjectName("featureLabel")
        
        self.descLabel = QLabel(description, self)
        self.descLabel.setObjectName("featureDesc")
        self.descLabel.setWordWrap(True)
        
        # 设置布局
        self.vBoxLayout = QVBoxLayout(self)
        self.vBoxLayout.setContentsMargins(16, 16, 16, 16)
        self.vBoxLayout.setSpacing(8)
        
        # 创建标题行水平布局
        self.headerLayout = QHBoxLayout()
        self.headerLayout.setSpacing(12)
        self.headerLayout.addWidget(self.iconWidget)
        self.headerLayout.addWidget(self.titleLabel)
        self.headerLayout.addStretch(1)
        
        # 添加到主布局
        self.vBoxLayout.addLayout(self.headerLayout)
        self.vBoxLayout.addWidget(self.descLabel)
        
        # 设置卡片大小
        self.setFixedHeight(130)
        self.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)


class AuthWidget(QWidget):
    # 定义信号
    register_requested = Signal(str, str, str, str, str)  # account, password, code, invid, udid
    login_requested = Signal(str, str, bool, bool)  # 邮箱, 密码, 记住密码, 自动登录
    forgot_password_requested = Signal()
    switch_to_register = Signal()
    switch_to_login = Signal()
    send_verification_code = Signal(str)  # 发送验证码信号
   
    def __init__(self):
        super().__init__()
        
        # 倒计时相关
        self.countdown_timer = QTimer(self)
        self.countdown_timer.timeout.connect(self.update_countdown)
        self.countdown_seconds = 60
        
        # 设置整体布局
        self.main_layout = QHBoxLayout(self)
        self.main_layout.setContentsMargins(0, 0, 0, 0)
        self.main_layout.setSpacing(0)
        
        # 创建左右两个部分
        self.left_widget = QWidget()
        self.right_widget = QWidget()
        
        # 设置左右两边的样式
        self.left_widget.setObjectName("leftPanel")
        self.right_widget.setObjectName("rightPanel")
        
        # 左边部分占据2/3空间
        self.main_layout.addWidget(self.left_widget, 2)
        # 右边部分占据1/3空间
        self.main_layout.addWidget(self.right_widget, 1)
        
        # 初始化左边面板
        self.setup_left_panel()
        # 初始化右边面板
        self.setup_right_panel()
        
        # 应用样式表
        self.setStyleSheet("""
            QWidget#leftPanel {
                background-color: #FFFFFF;
                border-right: 1px solid #E0E0E0;
            }
            QWidget#rightPanel {
                background-color: #F8F9FA;
            }
            QPushButton#primaryButton {
                background-color: #6C5CE7;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 12px;
                font-weight: bold;
                font-size: 14px;
            }
            QPushButton#primaryButton:hover {
                background-color: #5D4ED6;
            }
            QPushButton#primaryButton:pressed {
                background-color: #4E3DD5;
            }
            QPushButton#primaryButton:disabled {
                background-color: #A5A5A5;
                color: #E0E0E0;
            }
            QPushButton#sendCodeButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 12px;
                font-weight: bold;
                font-size: 14px;
            }
            QPushButton#sendCodeButton:hover {
                background-color: #45a049;
            }
            QPushButton#sendCodeButton:pressed {
                background-color: #3d8b40;
            }
            QPushButton#sendCodeButton:disabled {
                background-color: #A5A5A5;
                color: #E0E0E0;
            }
            QPushButton#linkButton {
                background-color: transparent;
                color: #6C5CE7;
                border: none;
                font-size: 12px;
                text-align: right;
            }
            QPushButton#linkButton:hover {
                color: #5D4ED6;
                text-decoration: underline;
            }
            QLineEdit {
                border: 1px solid #E0E0E0;
                border-radius: 4px;
                padding: 12px;
                background-color: white;
                font-size: 14px;
            }
            QLineEdit:focus {
                border: 1px solid #6C5CE7;
            }
            QLabel#titleLabel {
                font-size: 24px;
                font-weight: bold;
                color: #333333;
            }
            QLabel#descriptionLabel {
                font-size: 14px;
                color: #666666;
            }
            QLabel#featureLabel {
                font-size: 14px;
                font-weight: bold;
                color: #333333;
            }
            QLabel#featureDesc {
                font-size: 12px;
                color: #666666;
            }
            QLabel#inviteText {
                font-size: 14px;
                color: #333333;
            }
            QFrame#featureBox {
                background-color: white;
                border-radius: 8px;
                padding: 10px;
            }
            QFrame#inviteBox {
                background-color: #F0F0FF;
                border-radius: 8px;
                padding: 16px;
            }
            QCheckBox {
                font-size: 12px;
                color: #666666;
            }
            QCheckBox::indicator {
                width: 18px;
                height: 18px;
            }
        """)
        
    def setup_left_panel(self):
        layout = QVBoxLayout(self.left_widget)
        layout.setContentsMargins(40, 40, 40, 40)
        layout.setSpacing(20)
        
        # 标题和描述
        title_layout = QVBoxLayout()
        title = QLabel("Telegram营销好帮手")
        title.setObjectName("titleLabel")
        layout.addWidget(title)
        
        # 功能区布局 (2x2网格)
        features_grid = QGridLayout()
        features_grid.setSpacing(20)
        
        # 创建四个功能模块配置
        features = [
            {
                "icon": FluentIcon.PEOPLE, 
                "title": "账号池管理",
                "desc": "多账号管理，智能调度账号，降低风险"
            },
            {
                "icon": FluentIcon.SYNC, 
                "title": "用户监听服务",
                "desc": "监听用户，自动回复，提高转化率"
            },
            {
                "icon": FluentIcon.SETTING, 
                "title": "消息群发",
                "desc": "群发消息，定时发送，提高效率"
            },
            {
                "icon": FluentIcon.PHONE, 
                "title": "智能拉群",
                "desc": "自动选择最优账号，智能负载均衡，用量预警"
            }
        ]
        
        # 使用自定义卡片组件创建功能卡片
        row, col = 0, 0
        for feature in features:
            # 创建自定义卡片
            card = FeatureCard(feature["icon"], feature["title"], feature["desc"])
            
            # 添加到网格布局
            features_grid.addWidget(card, row, col)
            col += 1
            if col > 1:
                col = 0
                row += 1
                
        layout.addLayout(features_grid)
        
        # 添加邀请区域
        invite_box = QFrame()
        #invite_box = ElevatedCardWidget()
        invite_box.setObjectName("inviteBox")
        invite_layout = QVBoxLayout(invite_box)
        
        # 邀请文本
        invite_header = QHBoxLayout()
        fuli = QLabel("邀请福利")
        fuli.setFont(QFont("Arial", 18, QFont.Bold))
        fuli.setObjectName("fuli")
        invite_header.addWidget(fuli)
        invite_header.addStretch()
        invite_layout.addLayout(invite_header)
        
        invite_text = QLabel("邀请1位好友 = 3天PRO会员")
        invite_text.setObjectName("inviteText")
        invite_text.setFont(QFont("Arial", 14, QFont.Bold))
        invite_layout.addWidget(invite_text)
        
        invite_desc = QLabel("无限邀请，无限累积")
        invite_desc.setObjectName("descriptionLabel")
        invite_layout.addWidget(invite_desc)
        
        # 用户头像区域
        user_layout = QHBoxLayout()
        for i in range(4):  # 显示4个用户头像
            user_avatar = QLabel("👤")
            user_avatar.setFont(QFont("Arial", 16))
            user_layout.addWidget(user_avatar)
            
        user_layout.addStretch()
        new_users = QLabel("今日新增35用户")
        new_users.setObjectName("descriptionLabel")
        user_layout.addWidget(new_users)
        
        invite_layout.addLayout(user_layout)
        layout.addWidget(invite_box)
        
        # 添加底部版权信息
        layout.addStretch()
        copyright = QLabel("© 2025 天壹 财团. 保留所有权利")
        copyright.setAlignment(Qt.AlignCenter)
        copyright.setObjectName("descriptionLabel")
        layout.addWidget(copyright)
        
    def setup_right_panel(self):
        # 创建堆叠窗口部件以支持切换登录和注册页面
        self.stacked_widget = QStackedWidget()
        
        # 创建登录页面
        self.login_page = QWidget()
        self.setup_login_page()
        
        # 创建注册页面
        self.register_page = QWidget()
        self.setup_register_page()
        
        # 将页面添加到堆叠窗口部件
        self.stacked_widget.addWidget(self.login_page)  # 索引0
        self.stacked_widget.addWidget(self.register_page)  # 索引1
        
        # 默认显示登录页面
        self.stacked_widget.setCurrentIndex(0)
        
        # 右侧面板整体布局
        right_layout = QVBoxLayout(self.right_widget)
        right_layout.setContentsMargins(40, 40, 40, 40)
        right_layout.addWidget(self.stacked_widget)
    
    def setup_login_page(self):
        layout = QVBoxLayout(self.login_page)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(20)
        
        # 标题
        title = QLabel("账号登录")
        title.setObjectName("titleLabel")
        layout.addWidget(title)
        
        # 描述
        description = QLabel("登录您的 营销助手 账号，享受更多功能")
        description.setObjectName("descriptionLabel")
        layout.addWidget(description)
        
        layout.addSpacing(20)
        
        # 邮箱输入
        self.login_email = QLineEdit()
        self.login_email.setPlaceholderText("请输入邮箱")
        self.login_email.setToolTip("邮箱地址")
        layout.addWidget(self.login_email)
        
        # 密码输入
        self.login_password = QLineEdit()
        self.login_password.setPlaceholderText("• • • • • • • • • •")
        self.login_password.setEchoMode(QLineEdit.Password)
        self.login_password.setToolTip("密码")
        layout.addWidget(self.login_password)
        
        # 记住密码、自动登录和忘记密码
        options_layout = QHBoxLayout()
        
        # 复选框布局
        checkboxes_layout = QHBoxLayout()
        self.remember_password = QCheckBox("记住密码?")
        checkboxes_layout.addWidget(self.remember_password)
        
        self.auto_login = QCheckBox("自动登录")
        checkboxes_layout.addWidget(self.auto_login)
        
        # 添加复选框布局到选项布局
        options_layout.addLayout(checkboxes_layout)
        
        # 关联记住密码和自动登录选择逻辑
        self.auto_login.stateChanged.connect(self.on_auto_login_changed)
        
        options_layout.addStretch()
        
        forgot_password = QPushButton("忘记密码?")
        forgot_password.setObjectName("linkButton")
        forgot_password.setCursor(QCursor(Qt.PointingHandCursor))
        forgot_password.clicked.connect(self.forgot_password_requested.emit)
        options_layout.addWidget(forgot_password)
        
        layout.addLayout(options_layout)
        
        layout.addSpacing(20)
        
        # 登录按钮
        login_button = QPushButton("登录")
        login_button.setObjectName("primaryButton")
        login_button.setCursor(QCursor(Qt.PointingHandCursor))
        login_button.clicked.connect(self.handle_login)
        layout.addWidget(login_button)
        
        layout.addSpacing(20)
        
        # 没有账号区域
        no_account_layout = QHBoxLayout()
        no_account_layout.addStretch()
        no_account_text = QLabel("没有账号?")
        no_account_text.setObjectName("descriptionLabel")
        no_account_layout.addWidget(no_account_text)
        
        register_link = QPushButton("注册")
        register_link.setObjectName("linkButton")
        register_link.setCursor(QCursor(Qt.PointingHandCursor))
        register_link.clicked.connect(self.switch_to_register_page)
        no_account_layout.addWidget(register_link)
        
        layout.addLayout(no_account_layout)
        
        # 底部信息
        layout.addStretch()
        
        terms_layout = QHBoxLayout()
        terms_layout.addStretch()
        
        login_terms = QLabel("登录即表示您已阅读并同意")
        login_terms.setObjectName("descriptionLabel")
        terms_layout.addWidget(login_terms)
        
        terms_link = QPushButton("服务条款")
        terms_link.setObjectName("linkButton")
        terms_link.setCursor(QCursor(Qt.PointingHandCursor))
        terms_layout.addWidget(terms_link)
        
        and_label = QLabel("和")
        and_label.setObjectName("descriptionLabel")
        terms_layout.addWidget(and_label)
        
        privacy_link = QPushButton("隐私政策")
        privacy_link.setObjectName("linkButton")
        privacy_link.setCursor(QCursor(Qt.PointingHandCursor))
        terms_layout.addWidget(privacy_link)
        
        layout.addLayout(terms_layout)
        
    def on_auto_login_changed(self, state):
        """当自动登录状态改变时，同步记住密码状态"""
        if state == Qt.Checked:
            # 如果选中了自动登录，则必须记住密码
            self.remember_password.setChecked(True)
            
    def handle_login(self):
        email = self.login_email.text().strip()
        password = self.login_password.text()
        remember_password = self.remember_password.isChecked()
        auto_login = self.auto_login.isChecked()
        self.login_requested.emit(email, password, remember_password, auto_login)
    
    def setup_register_page(self):
        layout = QVBoxLayout(self.register_page)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(20)
        
        # 标题
        title = QLabel("创建账号")
        title.setObjectName("titleLabel")
        layout.addWidget(title)
        
        # 描述
        description = QLabel("注册 营销助手 账号，解锁全部功能")
        description.setObjectName("descriptionLabel")
        layout.addWidget(description)
        
        layout.addSpacing(20)
        
        # 邮箱输入
        self.user_name = QLineEdit()
        self.user_name.setPlaceholderText("请输入您的用户名")
        self.user_name.setToolTip("用户名")
        layout.addWidget(self.user_name)
        
        # 添加邮箱验证码输入框和发送验证码按钮
        verification_layout = QHBoxLayout()
        
        # 验证码输入框
        self.verification_code = QLineEdit()
        self.verification_code.setPlaceholderText("请输入验证码")
        self.verification_code.setToolTip("邮箱验证码")
        verification_layout.addWidget(self.verification_code)
        
        # 发送验证码按钮
        self.send_code_button = QPushButton("发送验证码")
        self.send_code_button.setObjectName("sendCodeButton")
        self.send_code_button.setCursor(QCursor(Qt.PointingHandCursor))
        self.send_code_button.clicked.connect(self.handle_send_verification_code)
        verification_layout.addWidget(self.send_code_button)
        
        layout.addLayout(verification_layout)
        
        # 密码输入
        self.register_password = QLineEdit()
        self.register_password.setPlaceholderText("请设置密码 (至少8位)")
        self.register_password.setEchoMode(QLineEdit.Password)
        self.register_password.setToolTip("密码")
        layout.addWidget(self.register_password)
        
        # 用户名输入
        self.register_password_confirm = QLineEdit()
        self.register_password_confirm.setPlaceholderText("请再次输入密码")
        self.register_password_confirm.setEchoMode(QLineEdit.Password)
        self.register_password_confirm.setToolTip("确认密码")
        layout.addWidget(self.register_password_confirm)
        
        # 邀请码输入
        invite_layout = QHBoxLayout()
        self.invite_code = QLineEdit()
        self.invite_code.setPlaceholderText("邀请码 (选填)")
        self.invite_code.setToolTip("邀请码")
        invite_layout.addWidget(self.invite_code)
        
        get_invite_button = QPushButton("可获得1天PRO权限")
        get_invite_button.setObjectName("primaryButton")
        get_invite_button.setStyleSheet("background-color: #4CAF50;")
        get_invite_button.setCursor(QCursor(Qt.PointingHandCursor))
        invite_layout.addWidget(get_invite_button)
        
        layout.addLayout(invite_layout)
        
        layout.addSpacing(20)
        
        # 注册按钮
        register_button = QPushButton("注册")
        register_button.setObjectName("primaryButton")
        register_button.setCursor(QCursor(Qt.PointingHandCursor))
        register_button.clicked.connect(self.handle_register)
        layout.addWidget(register_button)
        
        layout.addSpacing(20)
        
        # 已有账号区域
        has_account_layout = QHBoxLayout()
        has_account_layout.addStretch()
        has_account_text = QLabel("已有账号?")
        has_account_text.setObjectName("descriptionLabel")
        has_account_layout.addWidget(has_account_text)
        
        login_link = QPushButton("登录")
        login_link.setObjectName("linkButton")
        login_link.setCursor(QCursor(Qt.PointingHandCursor))
        login_link.clicked.connect(self.switch_to_login_page)
        has_account_layout.addWidget(login_link)
        
        layout.addLayout(has_account_layout)
        
        # 底部信息
        layout.addStretch()
        
        terms_layout = QHBoxLayout()
        terms_layout.addStretch()
        
        register_terms = QLabel("注册即表示您已阅读并同意")
        register_terms.setObjectName("descriptionLabel")
        terms_layout.addWidget(register_terms)
        
        terms_link = QPushButton("服务条款")
        terms_link.setObjectName("linkButton")
        terms_link.setCursor(QCursor(Qt.PointingHandCursor))
        terms_layout.addWidget(terms_link)
        
        and_label = QLabel("和")
        and_label.setObjectName("descriptionLabel")
        terms_layout.addWidget(and_label)
        
        privacy_link = QPushButton("隐私政策")
        privacy_link.setObjectName("linkButton")
        privacy_link.setCursor(QCursor(Qt.PointingHandCursor))
        terms_layout.addWidget(privacy_link)
        
        layout.addLayout(terms_layout)
    
    def switch_to_register_page(self):
        self.stacked_widget.setCurrentIndex(1)
        self.switch_to_register.emit()
    
    def switch_to_login_page(self):
        self.stacked_widget.setCurrentIndex(0)
        self.switch_to_login.emit()
    
    def handle_register(self):
        account = self.user_name.text().strip()
        password = self.register_password.text()
        code = self.verification_code.text().strip()
        invid = self.invite_code.text().strip()
        # udid 由controller自动补全，这里传空字符串
        udid = ''
        self.register_requested.emit(account, password, code, invid, udid)
        
    def handle_send_verification_code(self):
        """处理发送验证码事件"""
        email = self.user_name.text().strip()
        if email:
            # 发送验证码信号
            self.send_verification_code.emit(email)
            # 开始倒计时
            self.send_code_button.setEnabled(False)
            self.countdown_seconds = 60
            self.send_code_button.setText(f"重新发送({self.countdown_seconds})")
            self.countdown_timer.start(1000)  # 每秒触发一次
    
    def update_countdown(self):
        """更新倒计时显示"""
        self.countdown_seconds -= 1
        if self.countdown_seconds <= 0:
            self.countdown_timer.stop()
            self.send_code_button.setText("发送验证码")
            self.send_code_button.setEnabled(True)
        else:
            self.send_code_button.setText(f"重新发送({self.countdown_seconds})") 