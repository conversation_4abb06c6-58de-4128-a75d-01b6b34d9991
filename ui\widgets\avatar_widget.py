#!/usr/bin/python3
# -*- coding: utf-8 -*-

from PySide6.QtWidgets import QWidget
from PySide6.QtGui import QPainter, QPixmap, QImage, QColor, QFont,QPainterPath
from PySide6.QtCore import Qt, QRect, QSize
from qfluentwidgets import  NavigationWidget
class AvatarWidget(NavigationWidget):
    """
    用户头像组件：上方显示圆形头像，下方显示用户名
    支持传入头像(QPixmap/QImage/路径)和用户名
    """
    def __init__(self, name: str = '', avatar=None, parent=None, avatar_size: int = 64, font_size: int = 14):
        super().__init__(parent)
        self._name = name
        self._avatar_size = avatar_size
        self._font_size = font_size
        self._avatar = None
        self.setAvatar(avatar)
        self.setMinimumSize(QSize(avatar_size + 16, avatar_size + font_size + 32))

    def setName(self, name: str):
        self._name = name
        self.update()

    def setAvatar(self, avatar):
        if isinstance(avatar, QPixmap):
            self._avatar = avatar
        elif isinstance(avatar, QImage):
            self._avatar = QPixmap.fromImage(avatar)
        elif isinstance(avatar, str) and avatar:
            self._avatar = QPixmap(avatar)
        else:
            # 默认头像（灰色）
            self._avatar = QPixmap(self._avatar_size, self._avatar_size)
            self._avatar.fill(QColor('#cccccc'))
        
        self.update()

    def paintEvent(self, event):
        painter = QPainter(self)
        painter.setRenderHints(QPainter.Antialiasing | QPainter.SmoothPixmapTransform)
        w = self.width()
        h = self.height()
        avatar_d = self._avatar_size
        # 头像区域
        avatar_rect = QRect((w - avatar_d) // 2, 8, avatar_d, avatar_d)
        # 绘制圆形头像
        painter.save()
        path = QPainterPath()
        path.addEllipse(avatar_rect)
        painter.setClipPath(path)
        if self._avatar and not self._avatar.isNull():
            painter.drawPixmap(avatar_rect, self._avatar)
        else:
            painter.fillRect(avatar_rect, QColor('#cccccc'))
        painter.restore()
        # 绘制用户名
        if self._name:
            font = QFont()
            font.setPointSize(self._font_size)
            font.setBold(True)
            painter.setFont(font)
            painter.setPen(QColor('#222222'))
            text_rect = QRect(0, avatar_rect.bottom() + 8, w, self._font_size + 12)
            painter.drawText(text_rect, Qt.AlignHCenter | Qt.AlignTop, self._name)

    def sizeHint(self):
        return QSize(self._avatar_size + 16, self._avatar_size + self._font_size + 32)


