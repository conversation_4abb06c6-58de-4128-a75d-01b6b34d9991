from PySide6.QtCore import Qt, Signal, QSize
from PySide6.QtWidgets import QWidget, QHBoxLayout, QLabel
from PySide6.QtGui import QCursor
from qfluentwidgets import ToolButton, FluentIcon

class GroupItem(QWidget):
    """分组项组件"""
    clicked = Signal(object)  # 点击信号，传递自身引用
    delete_clicked = Signal(object)  # 删除按钮点击信号，传递自身引用
    edit_clicked = Signal(object) # 编辑按钮点击信号，传递自身引用
    
    def __init__(self, text, group_id, is_all=False, parent=None):
        super().__init__(parent)
        self.group_id = group_id  # 分组ID，-1表示"所有账号"项
        self.is_all = is_all  # 是否为"所有账号"项
        self.is_selected = False  # 是否被选中
        
        self.init_ui(text)
    
    def init_ui(self, text):
        """初始化UI"""
        self.setAttribute(Qt.WidgetAttribute.WA_StyledBackground, True)
        self.setFixedHeight(36)
        
        # 整体布局
        self.layout = QHBoxLayout(self)
        self.layout.setContentsMargins(8, 4, 8, 4)
        self.layout.setSpacing(6)
        
        # 添加文本标签
        self.text_label = QLabel(text, self)
        self.text_label.setObjectName("groupItemLabel")
        
        self.layout.addWidget(self.text_label)
        self.layout.addStretch(1)
        
        # 如果不是"所有账号"项，添加删除按钮（默认隐藏）
        if not self.is_all:
            self.delete_btn = ToolButton(FluentIcon.DELETE, self)
            self.delete_btn.setFixedSize(QSize(24, 24))
            self.delete_btn.setToolTip("删除分组")
            self.delete_btn.clicked.connect(self._on_delete_clicked)
            self.delete_btn.setVisible(False)  # 默认隐藏
            

            # 添加编辑按钮 (默认隐藏)
            self.edit_btn = ToolButton(FluentIcon.EDIT, self)
            self.edit_btn.setFixedSize(QSize(24, 24))
            self.edit_btn.setToolTip("编辑分组名称")
            self.edit_btn.clicked.connect(self._on_edit_clicked)
            self.edit_btn.setVisible(False) # 默认隐藏
            self.layout.addWidget(self.edit_btn)
            self.layout.addWidget(self.delete_btn)
        # 更新样式
        self.update_style()
        
        # 鼠标点击事件
        self.setCursor(Qt.CursorShape.PointingHandCursor)
    
    def update_style(self):
        """更新样式"""
        if self.is_selected:
            self.setStyleSheet("""
                QWidget {
                    background-color: #ECF2FC;
                    border-radius: 4px;
                }
                #groupItemLabel {
                    color: #0070F5;
                    font-weight: bold;
                }
            """)
        else:
            self.setStyleSheet("""
                QWidget {
                    background-color: transparent;
                    border-radius: 4px;
                }
                QWidget:hover {
                    background-color: #F5F5F5;
                }
            """)
    
    def set_selected(self, selected):
        """设置是否选中"""
        self.is_selected = selected
        self.update_style()
    
    def _on_delete_clicked(self):
        """删除按钮点击事件"""
        self.delete_clicked.emit(self)
    
    def _on_edit_clicked(self):
        """编辑按钮点击事件"""
        self.edit_clicked.emit(self)
    
    def mousePressEvent(self, event):
        """鼠标点击事件"""
        if event.button() == Qt.MouseButton.LeftButton:
            self.clicked.emit(self)
        super().mousePressEvent(event)
    
    def enterEvent(self, event):
        """鼠标进入事件"""
        if not self.is_all and hasattr(self, 'delete_btn'):
            self.delete_btn.setVisible(True)
        if not self.is_all and hasattr(self, 'edit_btn'):
            self.edit_btn.setVisible(True)
        super().enterEvent(event)
    
    def leaveEvent(self, event):
        """鼠标离开事件"""
        if not self.is_all and hasattr(self, 'delete_btn'):
            self.delete_btn.setVisible(False)
        if not self.is_all and hasattr(self, 'edit_btn'):
            self.edit_btn.setVisible(False)
        super().leaveEvent(event) 