from PySide6.QtCore import Qt, Signal
from PySide6.QtWidgets import QWidget, QVBoxLayout, QHBoxLayout, QListWidget, QListWidgetItem
from PySide6.QtGui import QCursor
from qfluentwidgets import <PERSON>ushButton, FluentIcon, CardWidget, InfoBar, InfoBarPosition, ListWidget,StrongBodyLabel

from ui.widgets.group_item_widget import GroupItem
from ui.dialogs.group_name_dialog import GroupNameDialog
from PySide6.QtWidgets import QMenu, QMessageBox

class GroupListWidget(CardWidget):
    """账户分组列表组件"""
    # 分组相关信号
    group_selected = Signal(int)  # 分组被选中，传递分组ID
    add_group_requested = Signal(str)  # 请求添加新分组，传递分组名称
    rename_group_requested = Signal(int, str)  # 请求重命名分组，传递分组ID和新名称
    delete_group_requested = Signal(int)  # 请求删除分组，传递分组ID
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.current_selected_item = None
        self.group_items = {}  # 存储分组项，key为分组ID
        
        self.init_ui()
    
    def init_ui(self):
        """初始化UI"""
        self.layout = QVBoxLayout(self)
        self.layout.setContentsMargins(12, 12, 12, 12)
        self.layout.setSpacing(10)
        
        # 分组标题和添加按钮
        title_layout = QHBoxLayout()
        title = StrongBodyLabel('账户分组', self)
        self.add_group_btn = PushButton('添加分组', self, icon=FluentIcon.ADD)
        self.add_group_btn.setFixedHeight(30)
        
        title_layout.addWidget(title)
        title_layout.addStretch()
        title_layout.addWidget(self.add_group_btn)
        self.layout.addLayout(title_layout)
        
        # 分组列表
        self.group_list = ListWidget(self)
        self.group_list.setFrameShape(QListWidget.NoFrame)
        self.group_list.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)
        self.group_list.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)
        self.group_list.setSpacing(4)
        
        self.layout.addWidget(self.group_list)
        
        # 初始化"所有账号"项
        self.init_all_accounts_item()
        
        # 连接信号
        self.add_group_btn.clicked.connect(self.prompt_add_group)
    
    def init_all_accounts_item(self):
        """添加"所有账号"选项到分组列表"""
        item = QListWidgetItem(self.group_list)
        all_accounts_widget = GroupItem("所有账户 (0)", -1, True)
        
        # 设置点击事件处理
        all_accounts_widget.clicked.connect(self.on_group_item_clicked)
        
        item.setSizeHint(all_accounts_widget.sizeHint())
        self.group_list.setItemWidget(item, all_accounts_widget)
        
        # 默认选中"所有账号"
        all_accounts_widget.set_selected(True)
        self.current_selected_item = all_accounts_widget
        
        # 存储引用
        self.group_items[-1] = all_accounts_widget
    
    def update_group_list(self, groups):
        """根据提供的分组列表更新分组列表项
        
        Args:
            groups: 分组列表，每个分组应包含id, name, account_count等字段
        """
        # 清理旧的分组项（保留"所有账号"项）
        existing_group_ids = set(self.group_items.keys()) - {-1}  # 排除特殊ID
        new_group_ids = {group.get('id') for group in groups}
        
        # 删除不再存在的分组项
        ids_to_remove = existing_group_ids - new_group_ids
        for group_id in ids_to_remove:
            if group_id in self.group_items:
                # 从列表中删除项
                for i in range(self.group_list.count()):
                    item = self.group_list.item(i)
                    if item and self.group_list.itemWidget(item) == self.group_items[group_id]:
                        self.group_list.takeItem(i)
                        break
                # 从字典中删除引用
                del self.group_items[group_id]
        
        # 添加或更新分组项
        for group in groups:
            group_id = group.get('id')
            group_name = group.get('name', '')
            account_count = group.get('account_count', 0)
            display_text = f"{group_name} ({account_count})"
            
            if group_id in self.group_items:
                # 更新现有分组
                self.group_items[group_id].text_label.setText(display_text)
            else:
                # 添加新分组
                self.add_group_item(display_text, group_id)
    
    def add_group_item(self, text, group_id):
        """添加分组项到列表
        
        Args:
            text: 显示文本
            group_id: 分组ID
            
        Returns:
            创建的GroupItem实例
        """
        item = QListWidgetItem(self.group_list)
        group_item = GroupItem(text, group_id)
        
        # 连接信号
        group_item.clicked.connect(self.on_group_item_clicked)
        group_item.delete_clicked.connect(lambda item: self.confirm_delete_group(item.group_id))
        # 连接编辑按钮信号
        group_item.edit_clicked.connect(lambda item: self._handle_item_edit_clicked(item))
        
        # 设置右键菜单
        group_item.setContextMenuPolicy(Qt.ContextMenuPolicy.CustomContextMenu)
        group_item.customContextMenuRequested.connect(
            lambda pos, gid=group_id: self.show_context_menu(gid, pos)
        )
        
        item.setSizeHint(group_item.sizeHint())
        self.group_list.setItemWidget(item, group_item)
        
        # 存储引用
        self.group_items[group_id] = group_item
        
        return group_item
    
    def on_group_item_clicked(self, item):
        """处理分组项点击事件"""
        if self.current_selected_item:
            self.current_selected_item.set_selected(False)
        
        item.set_selected(True)
        self.current_selected_item = item
        
        # 发出分组选中信号
        self.group_selected.emit(item.group_id)
    
    def prompt_add_group(self):
        """弹出对话框让用户输入新分组名称"""
        dialog = GroupNameDialog(self)
        if dialog.exec():  # 对话框被接受
            name = dialog.get_name()
            if name:
                # 发出添加分组请求信号
                self.add_group_requested.emit(name)
            else:
                InfoBar.warning(
                    title='提示',
                    content="分组名称不能为空！",
                    orient=Qt.Horizontal,
                    isClosable=True,
                    position=InfoBarPosition.TOP,
                    duration=2000,
                    parent=self
                )
    
    def confirm_delete_group(self, group_id):
        """确认删除分组
        
        Args:
            group_id: 要删除的分组ID
        """
        # 获取分组名称
        group_name = ''
        if group_id in self.group_items:
            text = self.group_items[group_id].text_label.text()
            if "(" in text:
                group_name = text.split(" (")[0]
            else:
                group_name = text
        
        reply = QMessageBox.question(
            self,
            '确认删除',
            f"确定要删除分组 '{group_name}' 吗？\n分组下的账号不会被删除，只是解除关联。",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )
        
        if reply == QMessageBox.StandardButton.Yes:
            # 发出删除分组请求信号
            self.delete_group_requested.emit(group_id)
    
    def _handle_item_edit_clicked(self, item_widget: GroupItem):
        """处理分组项的编辑按钮点击事件"""
        group_id = item_widget.group_id
        current_name = ''
        # 从 item_widget 的 text_label 获取当前名称 (去除数量统计)
        text = item_widget.text_label.text()
        if " (" in text:
            current_name = text.split(" (")[0]
        else:
            current_name = text
            
        if group_id != -1: # 确保不是 "所有账号" 项
            self.prompt_rename_group(group_id, current_name)
    
    def show_context_menu(self, group_id, pos):
        """显示分组项的右键菜单
        
        Args:
            group_id: 分组ID
            pos: 鼠标位置
        """
        # 不为"所有账号"项显示右键菜单
        if group_id == -1:
            return
        
        # 获取当前分组名称
        group_name = ''
        if group_id in self.group_items:
            text = self.group_items[group_id].text_label.text()
            if "(" in text:
                group_name = text.split(" (")[0]
            else:
                group_name = text
        
        menu = QMenu(self)
        rename_action = menu.addAction('重命名')
        delete_action = menu.addAction('删除')
        
        # 获取选中的操作
        action = menu.exec(QCursor.pos())
        
        if action == rename_action:
            self.prompt_rename_group(group_id, group_name)
        elif action == delete_action:
            self.confirm_delete_group(group_id)
    
    def prompt_rename_group(self, group_id, current_name):
        """弹出对话框让用户输入新的分组名称
        
        Args:
            group_id: 分组ID
            current_name: 当前分组名称
        """
        dialog = GroupNameDialog(self, current_name=current_name)
        if dialog.exec():
            new_name = dialog.get_name()
            if new_name and new_name != current_name:
                # 发出重命名分组请求信号
                self.rename_group_requested.emit(group_id, new_name)
            elif not new_name:
                InfoBar.warning(
                    title='提示',
                    content="分组名称不能为空！",
                    orient=Qt.Horizontal,
                    isClosable=True,
                    position=InfoBarPosition.TOP,
                    duration=2000,
                    parent=self
                ) 