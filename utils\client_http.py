#!/usr/bin/python3
# -*- coding: utf-8 -*-
"""
认证模块客户端 - 提供与认证服务器的通信功能
"""

import json
import ssl
from typing import Dict, Any, Optional
import aiohttp

from utils.logger import get_logger




class AuthClient:
    """认证客户端类，提供与认证服务器的通信功能"""
    
    def __init__(self, headers: Dict[str, str] = None):
        """
        初始化认证客户端
        
        Args:
            headers: 请求头
        """
        self.headers = headers or {'Content-Type': 'application/json'}
        # 获取模块日志记录器
        self._logger = get_logger("core.auth.client")
        self._connector: Optional[aiohttp.TCPConnector] = None
    
    def _get_connector(self) -> aiohttp.TCPConnector:
        """
        获取或创建连接器
        
        Returns:
            aiohttp.TCPConnector 实例
        """
        if self._connector is None or self._connector.closed:
            self._connector = aiohttp.TCPConnector(verify_ssl=False)
        return self._connector
    
    async def get(self, url: str, params: Dict[str, Any]) -> Dict[str, Any]:
        """
        异步HTTP GET请求
        
        Args:
            url: 请求URL
            params: URL参数
            
        Returns:
            响应JSON或错误信息
        """
        #self._logger.debug(f"发送GET请求: {url}")
        async with aiohttp.ClientSession(connector=self._get_connector()) as session:
            try:
                async with session.get(url, params=params, headers=self.headers) as response:
                    if response.status != 200:
                        error_text = await response.text()
                        #self._logger.error(f"HTTP请求失败: {response.status} - {error_text}")
                        return {"error": f"HTTP Error {response.status}: {error_text}"}
                    
                    result = await response.text()
                    #self._logger.debug(f"请求成功: {url}")
                    return json.loads(result)
            except aiohttp.ClientError as e:
                #self._logger.error(f"网络请求异常: {e}")
                return {"error": f"Network Error: {e}"}
    
    async def post(self, url: str, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        异步HTTP POST请求
        
        Args:
            url: 请求URL
            data: POST数据
            
        Returns:
            响应JSON或错误信息
        """
        #self._logger.debug(f"发送POST请求: {url}")
        async with aiohttp.ClientSession(connector=self._get_connector()) as session:
            try:
                async with session.post(url, json=data, headers=self.headers) as response:
                    if response.status != 200:
                        error_text = await response.text()
                        #self._logger.error(f"HTTP请求失败: {response.status} - {error_text}")
                        return {"error": f"HTTP Error {response.status}: {error_text}"}
                    
                    result = await response.text()
                    #self._logger.debug(f"请求成功: {url}")
                    return json.loads(result)
            except aiohttp.ClientError as e:
                self._logger.error(f"网络请求异常: {e}")
                return {"error": f"Network Error: {e}"}
    
    def update_headers(self, headers: Dict[str, str]) -> None:
        """
        更新请求头
        
        Args:
            headers: 新的请求头
        """
        self.headers = headers


# 创建全局实例
auth_client = AuthClient() 
