#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
配置管理模块
负责加载和管理应用程序配置
"""

import os
from pathlib import Path
from typing import Optional, Dict, Any
from dotenv import load_dotenv


class Config:
    """配置管理类，用于加载和访问应用程序配置"""
    
    def __init__(self, env_file: Optional[str] = None):
        """
        初始化配置
        
        Args:
            env_file: .env文件路径，如果为None则使用默认路径
        """
        # 加载环境变量
        if env_file:
            load_dotenv(env_file)
        else:
            load_dotenv()
        
        # API配置
        self.api_id = self._get_env_var('API_ID')
        self.api_hash = self._get_env_var('API_HASH')
        
        # 数据存储路径
        self.session_dir = self._resolve_path(self._get_env_var('SESSION_DIR', '%APPDATA%\\TelegramTool\\sessions'))
        self.database_url = self._get_env_var('DATABASE_URL', 'sqlite:///%APPDATA%\\TelegramTool\\database.db')
        self.database_url = self._resolve_path(self.database_url)
        
        # 日志配置
        self.log_level = self._get_env_var('LOG_LEVEL', 'INFO')
        self.log_dir = self._resolve_path(self._get_env_var('LOG_DIR', '%APPDATA%\\TelegramTool\\logs'))
        
        # 确保目录存在
        self._ensure_dirs_exist()
    
    def _get_env_var(self, key: str, default: Any = None) -> Any:
        """获取环境变量，如果不存在则返回默认值"""
        value = os.getenv(key, default)
        if key == 'API_ID' and value is not None:
            try:
                value = int(value)
            except ValueError:
                # 如果无法转换为整数，则保持字符串格式
                pass
        return value
    
    def _resolve_path(self, path: str) -> str:
        """解析路径中的环境变量"""
        # 替换Windows环境变量
        if '%APPDATA%' in path:
            appdata = os.getenv('APPDATA', '')
            path = path.replace('%APPDATA%', appdata)
        
        # 替换SQLite数据库URL中的路径部分
        if path.startswith('sqlite:///'):
            db_path = path[10:]
            if '%APPDATA%' in db_path:
                appdata = os.getenv('APPDATA', '')
                db_path = db_path.replace('%APPDATA%', appdata)
            path = f'sqlite:///{db_path}'
        
        return path
    
    def _ensure_dirs_exist(self):
        """确保所有需要的目录都存在"""
        Path(self.session_dir).mkdir(parents=True, exist_ok=True)
        
        # 提取SQLite数据库路径并创建目录
        if self.database_url.startswith('sqlite:///'):
            db_path = self.database_url[10:]
            Path(db_path).parent.mkdir(parents=True, exist_ok=True)
        
        Path(self.log_dir).mkdir(parents=True, exist_ok=True)
    
    def to_dict(self) -> Dict[str, Any]:
        """将配置转换为字典"""
        return {
            'api_id': self.api_id,
            'api_hash': self.api_hash,
            'session_dir': self.session_dir,
            'database_url': self.database_url,
            'log_level': self.log_level,
            'log_dir': self.log_dir
        } 