#!/usr/bin/python3
# -*- coding: utf-8 -*-
"""
认证模块加密工具 - 提供加密解密和签名验证功能
"""

import hashlib
from binascii import hexlify
from typing import Dict, Any

from Crypto.Cipher import AES
from Crypto.Util.Padding import pad, unpad

from utils.logger import get_logger

# 获取模块日志记录器
_logger = get_logger("core.auth.crypto")


class AESEncryption:
    """AES加密工具类，支持AES-256-CBC加密模式"""
    
    def __init__(self, key: str, iv: str):
        """
        初始化AES加密器

        Args:
            key: 密钥(32字节)
            iv: 初始化向量(16字节)
        """
        self.key = key.encode('utf-8')
        self.iv = iv.encode('utf-8')
        #_logger.debug(f"初始化AES加密器: key长度={len(key)}, iv长度={len(iv)}")
    
    def encode(self, data: str) -> str:
        """
        AES加密数据

        Args:
            data: 待加密明文

        Returns:
            加密后的十六进制字符串
        """
        try:
            # 创建AES对象，使用CBC模式
            cipher = AES.new(self.key, AES.MODE_CBC, self.iv)
            # 对数据进行填充，然后加密
            padded_data = pad(data.encode('utf-8'), AES.block_size)
            encrypted = cipher.encrypt(padded_data)
            # 将加密后的数据转换为十六进制字符串
            return hexlify(encrypted).decode('utf-8')
        except Exception as e:
            _logger.error(f"AES加密失败: {e}")
            raise
    
    def decode(self, hex_data: str) -> str:
        """
        AES解密数据

        Args:
            hex_data: 十六进制格式的加密数据

        Returns:
            解密后的明文
        """
        try:
            # 将十六进制字符串转换为字节类型
            encrypted_data = bytes.fromhex(hex_data)
            # 创建AES对象，使用CBC模式
            cipher = AES.new(self.key, AES.MODE_CBC, self.iv)
            # 解密数据，并去除填充
            decrypted = cipher.decrypt(encrypted_data)
            unpadded_data = unpad(decrypted, AES.block_size)
            return unpadded_data.decode('utf-8')
        except Exception as e:
            _logger.error(f"AES解密失败: {e}")
            raise


class SignatureUtil:
    """签名工具类，提供签名生成和验证功能"""
    
    @staticmethod
    def dict_to_query_string(data: Dict[str, Any]) -> str:
        """
        将字典转换为查询字符串

        Args:
            data: 要转换的字典

        Returns:
            查询字符串，如 'key1=value1&key2=value2'
        """
        query_params = []
        for key, value in data.items():
            query_params.append(f"{key}={value}")
        return '&'.join(query_params)
    
    @staticmethod
    def generate_sign(data: str, key: str) -> str:
        """
        生成MD5签名

        Args:
            data: 待签名数据
            key: 密钥

        Returns:
            MD5签名
        """
        return hashlib.md5((data + key).encode()).hexdigest()
    
    @staticmethod
    def verify_sign(code: str, timestamp: str, key: str, sign: str) -> bool:
        """
        验证签名

        Args:
            code: 状态码
            timestamp: 时间戳
            key: 密钥
            sign: 待验证的签名

        Returns:
            签名是否有效
        """
        calc_sign = hashlib.md5((str(code) + str(timestamp) + key).encode()).hexdigest()
        return calc_sign == sign


# 创建默认的AES加密实例
default_aes = AESEncryption(key='ONxYDyNaCoyTzsp83JoQ3YYuMPHxk3j7', iv='yNaCoyTzsp83JoQ3')
signature_util = SignatureUtil() 
