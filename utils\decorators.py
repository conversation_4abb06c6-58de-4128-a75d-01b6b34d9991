from functools import wraps
from PySide6.QtWidgets import QMessageBox
from qfluentwidgets import MessageBox
from utils.vip_checker import vip_checker

def require_vip(func):
    """装饰器：要求VIP有效才能执行函数"""
    @wraps(func)
    def wrapper(self, *args, **kwargs):
        if not vip_checker.is_vip_valid():
            MessageBox(
                
                "VIP已过期", 
                f"{vip_checker.get_expire_message()}\n请联系上级代理续费。",
                self, 
            ).exec()
            return None
        return func(self, *args, **kwargs)
    return wrapper

def require_vip_async(func):
    """装饰器：要求VIP有效才能执行异步函数"""
    @wraps(func)
    async def wrapper(self, *args, **kwargs):
        if not vip_checker.is_vip_valid():
            MessageBox(
                
                "VIP已过期", 
                f"{vip_checker.get_expire_message()}\n请联系上级代理续费。",
                self, 

            ).exec()
            return None
        return await func(self, *args, **kwargs)
    return wrapper
