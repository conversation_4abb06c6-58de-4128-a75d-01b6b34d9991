from PySide6.QtWidgets import (QApplication, QTextEdit, QMainWindow, QFileDialog, QVBoxLayout, QWidget, QPushButton, QInputDialog, QHBoxLayout)
from PySide6.QtGui import QTextCursor, QTextCharFormat, QFont, QKeySequence, QImage
from PySide6.QtCore import Qt, QTimer
from qfluentwidgets import TextEdit,Action
import sys
import re

class TextEditor(QWidget):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle('Message Editor')
        self.setGeometry(100, 100, 800, 600)

        self.text_edit = TextEdit(self)
        # 确保文本编辑器能接收焦点
        self.text_edit.setFocusPolicy(Qt.StrongFocus)

        layout = QVBoxLayout()

        self.create_actions()  # 确保在创建工具栏之前调用
        
        # 添加快捷键到窗口
        self.addActions([
            self.bold_action,
            self.italic_action,
            self.underline_action,
            self.strikethrough_action,
            self.insert_link_action,
            self.insert_image_action
        ])

        # 创建工具栏布局
        toolbar_layout = QHBoxLayout()
        toolbar_layout.addWidget(self.create_tool_button(self.bold_action))
        toolbar_layout.addWidget(self.create_tool_button(self.italic_action))
        toolbar_layout.addWidget(self.create_tool_button(self.underline_action))
        toolbar_layout.addWidget(self.create_tool_button(self.strikethrough_action))
        toolbar_layout.addWidget(self.create_tool_button(self.insert_link_action))
        toolbar_layout.addWidget(self.create_tool_button(self.insert_image_action))

        layout.addLayout(toolbar_layout)
        layout.addWidget(self.text_edit)

        self.setLayout(layout)

        self.image_paths = []  # 用于存储图片路径

    def create_tool_button(self, action):
        button = QPushButton(action.text(), self)
        button.setToolTip(action.toolTip())
        button.setCheckable(True)  # 设置按钮为可切换状态
        button.clicked.connect(lambda checked, act=action: self._handle_button_click(button, act))
        return button

    def _handle_button_click(self, button, action):
        # 触发动作
        action.trigger()
        # 重置按钮状态，确保按钮不会保持在按下状态
        QTimer.singleShot(100, lambda: button.setChecked(False))

    def create_actions(self):
        self.bold_action = Action('加粗', self)
        self.bold_action.setShortcut(QKeySequence.Bold)
        self.bold_action.setToolTip('加粗 (Ctrl+B)')
        self.bold_action.triggered.connect(self.toggle_bold)

        self.italic_action = Action('斜体', self)
        self.italic_action.setShortcut(QKeySequence.Italic)
        self.italic_action.setToolTip('斜体 (Ctrl+I)')
        self.italic_action.triggered.connect(self.toggle_italic)

        self.underline_action = Action('下划线', self)
        self.underline_action.setShortcut(QKeySequence.Underline)
        self.underline_action.setToolTip('下划线 (Ctrl+U)')
        self.underline_action.triggered.connect(self.toggle_underline)

        self.strikethrough_action = Action('删除线', self)
        self.strikethrough_action.setShortcut('Ctrl+T')
        self.strikethrough_action.setToolTip('删除线 (Ctrl+T)')
        self.strikethrough_action.triggered.connect(self.toggle_strikethrough)

        self.insert_image_action = Action('插入图片', self)
        self.insert_image_action.setShortcut('Ctrl+P')
        self.insert_image_action.setToolTip('插入图片 (Ctrl+P)')
        self.insert_image_action.triggered.connect(self.insert_image)

        self.insert_link_action = Action('插入链接', self)
        self.insert_link_action.setShortcut('Ctrl+K')
        self.insert_link_action.setToolTip('插入链接 (Ctrl+K)')
        self.insert_link_action.triggered.connect(self.insert_link)

    def toggle_bold(self):
        fmt = QTextCharFormat()
        fmt.setFontWeight(QFont.Bold if not self.text_edit.fontWeight() == QFont.Bold else QFont.Normal)
        self.merge_format_on_word_or_selection(fmt)
        self.text_edit.setFocus()  # 将焦点重新设置回文本编辑器

    def toggle_italic(self):
        fmt = QTextCharFormat()
        fmt.setFontItalic(not self.text_edit.fontItalic())
        self.merge_format_on_word_or_selection(fmt)
        self.text_edit.setFocus()  # 将焦点重新设置回文本编辑器

    def toggle_underline(self):
        fmt = QTextCharFormat()
        fmt.setFontUnderline(not self.text_edit.fontUnderline())
        self.merge_format_on_word_or_selection(fmt)
        self.text_edit.setFocus()  # 将焦点重新设置回文本编辑器

    def toggle_strikethrough(self):
        cursor = self.text_edit.textCursor()
        if cursor.hasSelection():
            fmt = cursor.charFormat()
            fmt.setFontStrikeOut(not fmt.fontStrikeOut())
            cursor.mergeCharFormat(fmt)
        else:
            fmt = QTextCharFormat()
            fmt.setFontStrikeOut(True)
            cursor.insertText(' ', fmt)
            cursor.deletePreviousChar()
        self.text_edit.setFocus()  # 将焦点重新设置回文本编辑器

    def insert_image(self):
        file_names, _ = QFileDialog.getOpenFileNames(self, '插入图片', '', '图片 (*.png *.xpm *.jpg *.jpeg *.webp)')
        cursor = self.text_edit.textCursor()
        cursor.movePosition(QTextCursor.Start)
        self.text_edit.setTextCursor(cursor)
        for file_name in file_names:
            if file_name:
                # 插入图片时调整大小为原图的20%
                image = QImage(file_name)
                new_width = int(image.width() * 0.2)
                new_height = int(image.height() * 0.2)
                
                # 使用HTML的width和height属性来控制图片尺寸
                cursor.insertHtml(f'<img src="{file_name}" width="{new_width}" height="{new_height}" />\n')
        self.text_edit.setTextCursor(cursor)
        self.text_edit.setFocus()  # 将焦点重新设置回文本编辑器

    def insert_link(self):
        cursor = self.text_edit.textCursor()
        if cursor.hasSelection():
            selected_text = cursor.selectedText()
            url, ok = QInputDialog.getText(self, '插入链接', '输入URL:')
            if ok and url:
                cursor.insertHtml(f'<a href="{url}">{selected_text}</a>')
        self.text_edit.setFocus()  # 将焦点重新设置回文本编辑器

    def merge_format_on_word_or_selection(self, format):
        cursor = self.text_edit.textCursor()
        if not cursor.hasSelection():
            cursor.select(QTextCursor.WordUnderCursor)
        cursor.mergeCharFormat(format)
        self.text_edit.mergeCurrentCharFormat(format)
        self.text_edit.setFocus()  # 确保文本编辑器保持焦点

    def clear(self):
        self.text_edit.clear()
    def setHtml(self,html):
        self.text_edit.setHtml(html)
    def setPlainText(self,text):
        self.text_edit.setPlainText(text)
        
    def print_html(self):
        html_text = self.get_html_text()
        print(html_text)

    def showEvent(self, event):
        # 窗口显示时自动聚焦到文本编辑器
        super().showEvent(event)
        self.text_edit.setFocus()

    def get_html_text(self):
        html = self.text_edit.toHtml()
        # 使用正则表达式替换不被允许的标签，处理font-weight前的空格
        html = re.sub(r'<span style=".*?font-weight:(600|700);.*?">(.*?)</span>', r'<b>\2</b>', html)
        html = re.sub(r'<span style=".*?font-style:italic;.*?">(.*?)</span>', r'<i>\1</i>', html)
        html = re.sub(r'<span style=".*?text-decoration: underline;.*?">(.*?)</span>', r'<u>\1</u>', html)
        html = re.sub(r'<span style=".*?text-decoration: line-through;.*?">(.*?)</span>', r'<s>\1</s>', html)
        
        # 移除不必要的HTML头部和样式
        html = re.sub(r'<!DOCTYPE[^>]*>', '', html)
        html = re.sub(r'<html[^>]*>', '', html)
        html = re.sub(r'<head>.*?</head>', '', html, flags=re.DOTALL)
        html = re.sub(r'<body[^>]*>', '', html)
        html = re.sub(r'</body>', '', html)
        html = re.sub(r'</html>', '', html)
        # 移除<p>标签
        html = re.sub(r'<p[^>]*>', '', html)
        html = re.sub(r'</p>', '', html)
        
        # 移除<a>标签内的<span>标签
        html = re.sub(r'<a href="([^"]*)"><span[^>]*>(.*?)</span></a>', r'<a href="\1">\2</a>', html)
        
        # 移除多余的空格（但保留换行）
        #html = re.sub(r'[ \t]+', ' ', html)
        
        # 替换<br />和<br/>为\n
        html = re.sub(r'<br\s*/>', r'\n', html)
        
        html = html.strip()
        return html
    def closeEvent(self, event):
        pass
        #event.accept()
        # text = self.get_html_text()
        # print(text)
if __name__ == '__main__':
    app = QApplication(sys.argv)
    editor = TextEditor()

    editor.show()
    sys.exit(app.exec())
