#!/usr/bin/env python
# -*- coding: utf-8 -*-

import shutil
from pathlib import Path
from datetime import datetime
from typing import List, Tuple, Optional
from loguru import logger

class FileUtils:
    """文件操作工具类"""
    
    @staticmethod
    def validate_session_file(file_path: str) -> bool:
        """
        验证是否为有效的session文件
        
        Args:
            file_path: 文件路径
            
        Returns:
            是否为有效的session文件
        """
        path = Path(file_path)
        if not path.is_file():
            return False
        
        return path.suffix == '.session'
    
    @staticmethod
    def validate_tdata_folder(folder_path: str) -> bool:
        """
        验证是否为有效的tdata文件夹
        
        Args:
            folder_path: 文件夹路径
            
        Returns:
            是否为有效的tdata文件夹
        """
        path = Path(folder_path)
        if not path.is_dir():
            return False
        
        # tdata文件夹通常包含map0或map1文件
        return (path / 'map0').exists() or (path / 'map1').exists()
    
    @staticmethod
    def find_session_files(path_str: str) -> List[str]:
        """
        查找目录中的所有session文件
        
        Args:
            path_str: 目录路径
            
        Returns:
            session文件路径列表
        """
        session_files = []
        path = Path(path_str)
        
        if path.is_file() and path.suffix == '.session':
            return [str(path)]
        
        if path.is_dir():
            for file_path in path.glob('*.session'):
                session_files.append(str(file_path))
        
        return session_files
    
    @staticmethod
    def find_tdata_folders(path_str: str) -> List[str]:
        """
        查找目录中的所有tdata文件夹
        
        Args:
            path_str: 目录路径
            
        Returns:
            tdata文件夹路径列表
        """
        tdata_folders = []
        path = Path(path_str)
        
        if path.is_dir():
            # 检查当前目录是否为tdata文件夹
            if FileUtils.validate_tdata_folder(str(path)):
                return [str(path)]
            
            # 检查子目录
            for item_path in path.iterdir():
                if item_path.is_dir() and FileUtils.validate_tdata_folder(str(item_path)):
                    tdata_folders.append(str(item_path))
        
        return tdata_folders
    
    @staticmethod
    def create_backup(file_path: str) -> Optional[str]:
        """
        创建文件备份
        
        Args:
            file_path: 文件路径
            
        Returns:
            备份文件路径，如果备份失败则返回None
        """
        try:
            path = Path(file_path)
            if not path.exists():
                return None
            
            timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
            backup_path = Path(f"{file_path}.{timestamp}.bak")
            
            if path.is_file():
                shutil.copy2(path, backup_path)
            elif path.is_dir():
                shutil.copytree(path, backup_path)
            
            logger.info(f"已创建备份: {backup_path}")
            return str(backup_path)
        
        except Exception as e:
            logger.error(f"创建备份失败: {str(e)}")
            return None
    
    @staticmethod
    def ensure_dir(dir_path: str) -> bool:
        """
        确保目录存在，不存在则创建
        
        Args:
            dir_path: 目录路径
            
        Returns:
            操作是否成功
        """
        try:
            path = Path(dir_path)
            if not path.exists():
                path.mkdir(parents=True, exist_ok=True)
                logger.info(f"创建目录: {path}")
            return True
        
        except Exception as e:
            logger.error(f"创建目录失败: {dir_path}, 错误: {str(e)}")
            return False 