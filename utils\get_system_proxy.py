#!/usr/bin/python3
# -*- coding: utf-8 -*-
"""
系统代理获取模块，支持从Windows系统获取代理设置
"""
import python_socks
import winreg
import sys
import re
import urllib.parse


def get_windows_proxy_settings():
    """
    读取 Windows 注册表中的代理设置。
    返回一个包含代理信息的字典，如果没有启用代理则返回 None。
    """
    proxy_info = {}
    registry_key_path = r"Software\Microsoft\Windows\CurrentVersion\Internet Settings"
    
    try:
        # 打开注册表项
        with winreg.OpenKey(winreg.HKEY_CURRENT_USER, registry_key_path, 0, winreg.KEY_READ) as internet_settings_key:
            # 检查手动代理设置
            _check_manual_proxy(internet_settings_key, proxy_info)
            
            # 检查自动脚本代理设置
            _check_auto_script_proxy(internet_settings_key, proxy_info)
            
            # 检查自动检测代理设置
            _check_auto_detect_proxy(internet_settings_key, proxy_info)
                
    except FileNotFoundError:
        print(f"错误：无法找到注册表项 HKEY_CURRENT_USER\\{registry_key_path}")
        return None
    except Exception as e:
        print(f"访问注册表时发生错误: {e}")
        return None
        
    # 如果 proxy_info 字典不为空，说明至少有一个代理设置被启用了
    return proxy_info if proxy_info else None


def _check_manual_proxy(registry_key, proxy_info):
    """检查手动代理设置"""
    try:
        proxy_enable_value, _ = winreg.QueryValueEx(registry_key, "ProxyEnable")
        if proxy_enable_value == 1:
            proxy_server_value, _ = winreg.QueryValueEx(registry_key, "ProxyServer")
            proxy_info['type'] = 'manual'
            
            # ProxyServer 值通常是 "address:port" 格式
            parts = proxy_server_value.split(':')
            if len(parts) == 2:
                proxy_info['address'] = parts[0]
                try:
                    # 将端口转换为整数
                    proxy_info['port'] = int(parts[1])
                except ValueError:
                    # 如果转换失败，设置一个默认端口
                    print(f"警告：无法将端口 '{parts[1]}' 转换为整数，使用默认值")
                    proxy_info['port'] = 80
            else:
                # 处理可能只有地址没有端口或格式不规范的情况
                proxy_info['address'] = proxy_server_value
                proxy_info['port'] = 80  # 设置一个默认端口
    except FileNotFoundError:
        # ProxyEnable 或 ProxyServer 键不存在
        pass 
    except Exception as e:
        print(f"读取手动代理设置时出错: {e}")


def _check_auto_script_proxy(registry_key, proxy_info):
    """检查自动代理脚本设置"""
    try:
        autoconfig_url_value, _ = winreg.QueryValueEx(registry_key, "AutoConfigURL")
        if autoconfig_url_value:  # 如果值不为空
            # 如果之前没检测到手动代理，或者我们就是要检查所有开启的代理
            if 'type' not in proxy_info: 
                proxy_info['type'] = 'automatic_script'
            else:
                # 如果手动和自动都填了，可以标记出来
                proxy_info['also_has_type'] = 'automatic_script'
            
            proxy_info['script_url'] = autoconfig_url_value
    except FileNotFoundError:
        pass
    except Exception as e:
        print(f"读取自动代理脚本设置时出错: {e}")


def _check_auto_detect_proxy(registry_key, proxy_info):
    """检查自动检测代理设置"""
    try:
        proxy_autodetect_value, _ = winreg.QueryValueEx(registry_key, "ProxyAutoDetect")
        if proxy_autodetect_value == 1:
            if 'type' not in proxy_info:
                proxy_info['type'] = 'automatic_detect'
            else:
                proxy_info['also_has_type'] = 'automatic_detect'
            # 自动检测的具体代理服务器无法直接从注册表简单读取，依赖 WPAD 协议
            proxy_info['details'] = 'Enabled (WPAD)'
    except FileNotFoundError:
        pass
    except Exception as e:
        print(f"读取自动检测设置时出错: {e}")


def get_pac_proxy(pac_url):
    """
    从PAC文件中获取代理配置
    
    :param pac_url: PAC文件URL
    :return: 代理配置字典，包含ip和port
    """
    try:
        # 解析URL
        parsed = urllib.parse.urlparse(pac_url)
        
        # 处理认证信息
        username, password, netloc = _extract_auth_info(parsed)
            
        # 提取主机和端口
        match = re.match(r'(.+):(\d+)', netloc)
        if match:
            ip = match.group(1)
            port = int(match.group(2))
            
            proxy_config = {
                'ip': ip,
                'port': port
            }
            
            # 如果存在认证信息则添加
            if username and password:
                proxy_config['username'] = username
                proxy_config['password'] = password
            
            print(f"从PAC URL解析到代理配置:")
            print(f"  IP: {proxy_config['ip']}")
            print(f"  端口: {proxy_config['port']}")
            print(f"读取代理{proxy_config}")
            return proxy_config
            
    except Exception as e:
        print(f"解析PAC URL时出错: {e}")
        return None


def _extract_auth_info(parsed_url):
    """从解析的URL中提取认证信息"""
    username = None
    password = None
    netloc = parsed_url.netloc
    
    # 检查是否包含认证信息
    if '@' in netloc:
        auth, netloc = netloc.split('@')
        if ':' in auth:
            username, password = auth.split(':')
    
    return username, password, netloc


def get_win_proxy_info():
    """
    获取Windows系统代理信息
    
    :return: 包含代理配置的字典，如果没有启用代理则返回None
    """
    if sys.platform != 'win32':
        print("此脚本只能在 Windows 操作系统上运行以读取注册表。")
        return None
    
    print("正在获取")
    settings = get_windows_proxy_settings()

    if not settings:
        print("系统当前未启用代理设置 (或无法读取)。")
        return None

    # 处理手动代理设置
    if _has_manual_proxy(settings):
        return _extract_manual_proxy(settings)

    # 处理自动代理脚本
    if _has_pac_script(settings):
        return _get_proxy_from_pac(settings)

    # 处理自动检测代理
    if _has_auto_detect(settings):
        return None

    # 处理多种代理设置的情况
    if _has_multiple_proxy_types(settings):
        return _handle_multiple_proxy_types(settings)

    # 如果走到这里，说明没有找到可用的代理
    return None


def _has_manual_proxy(settings):
    """判断是否有手动代理设置"""
    return 'address' in settings and 'port' in settings


def _extract_manual_proxy(settings):
    """提取手动代理设置"""
    print(f"  手动代理地址: {settings['address']}")
    print(f"  手动代理端口: {settings['port']}")
    # 端口已经在_check_manual_proxy中转换为整数，此处不需要再次转换
    return {
        'ip': settings['address'],
        'port': settings['port']
    }


def _has_pac_script(settings):
    """判断是否有PAC脚本设置"""
    return 'script_url' in settings


def _get_proxy_from_pac(settings):
    """从PAC脚本获取代理"""
    pac_url = settings['script_url']
    print(f"  自动配置脚本 (PAC URL): {settings['script_url']}")
    return get_pac_proxy(pac_url)


def _has_auto_detect(settings):
    """判断是否有自动检测代理设置"""
    return 'details' in settings and settings['type'] == 'automatic_detect'


def _has_multiple_proxy_types(settings):
    """判断是否有多种代理类型"""
    return 'also_has_type' in settings


def _handle_multiple_proxy_types(settings):
    """处理多种代理类型的情况"""
    print(f"  同时检测到其他类型: {settings['also_has_type']}")
    
    # 尝试使用自动脚本代理
    if settings['also_has_type'] == 'automatic_script' and 'script_url' in settings:
        print(f"    - 自动配置脚本 (PAC URL): {settings['script_url']}")
        return get_pac_proxy(settings['script_url'])
    
    # 尝试使用自动检测代理
    elif settings['also_has_type'] == 'automatic_detect' and 'details' in settings:
        print(f"    - 自动检测详情: {settings['details']}")
        return None
    
    return None


class GetProxy:
    """代理获取类，支持多种代理类型"""
    
    def __init__(self, type, ip=None, port=None, username=None, password=None):
        """
        初始化代理对象
        
        :param type: 代理类型，可选值: "disable", "system", "http", "socks5", "ip_pool"
        :param ip: 代理服务器IP
        :param port: 代理服务器端口
        :param username: 代理认证用户名
        :param password: 代理认证密码
        """
        self.type = type
        self.ip = ip
        # 确保端口是整数类型
        self.port = int(port) if port is not None and isinstance(port, str) else port
        self.user = username
        self.password = password
    
    @classmethod
    def from_dict(cls, proxy_dict):
        """
        从字典创建代理对象，支持username和user两种参数名
        
        :param proxy_dict: 包含代理配置的字典
        :return: GetProxy实例
        """
        if not proxy_dict or not isinstance(proxy_dict, dict):
            return cls(type="disable")
            
        proxy_type = proxy_dict.get('type', 'disable')
        # 优先使用user参数，如果不存在则尝试使用username参数
        user = proxy_dict.get('user', proxy_dict.get('username', None))
        
        # 获取端口并确保是整数
        port = proxy_dict.get('port')
        if port is not None and isinstance(port, str):
            try:
                port = int(port)
            except ValueError:
                print(f"警告：无法将端口 '{port}' 转换为整数，使用原值")
        
        return cls(
            type=proxy_type,
            ip=proxy_dict.get('ip'),
            port=port,
            username=user,
            password=proxy_dict.get('password')
        )
        
    def get_proxy(self):
        """
        根据代理类型返回代理配置
        
        :return: 元组 (ProxyType, ip, port, user, password) 或 None
        """
        if self.type == "disable":
            return None
        elif self.type == "system":
            return self.get_system_proxy()
        elif self.type == "http":
            return self._get_http_proxy()
        elif self.type == "socks5" or self.type == "ip_pool":  # 增加对ip_pool类型的处理，将其视为SOCKS5类型
            return self._get_socks5_proxy()
        else:
            return None
    
    def _get_http_proxy(self):
        """获取HTTP代理配置"""
        if self.user and self.password:
            return (
                python_socks.ProxyType.HTTP,
                self.ip,
                self.port,
                self.user,
                self.password
            )
        return (
            python_socks.ProxyType.HTTP,
            self.ip,
            self.port,
        )
    
    def _get_socks5_proxy(self):
        """获取SOCKS5代理配置"""
        if self.user and self.password:
            return (
                python_socks.ProxyType.SOCKS5,
                self.ip,
                self.port,
                self.user,
                self.password
            )
        return (
                python_socks.ProxyType.SOCKS5,
                self.ip,
                self.port
            )
    def get_system_proxy(self):
        """
        获取系统代理配置
        
        :return: 元组 (ProxyType, ip, port, None, None) 或 None
        """
        result = get_win_proxy_info()
        print(result)
        if result:
            # 确保端口是整数类型
            port = result.get('port')
            if isinstance(port, str):
                try:
                    port = int(port)
                except ValueError:
                    print(f"警告：无法将端口 '{port}' 转换为整数")
                    return None
            
            return (
                python_socks.ProxyType.SOCKS5,
                result.get('ip'),
                port
            )
        return None


if __name__ == "__main__":
    proxy = GetProxy(type="system")
    #proxy = GetProxy(type="socks5",ip="*************",port=3129,user="",password="")
    print(proxy.get_proxy())
