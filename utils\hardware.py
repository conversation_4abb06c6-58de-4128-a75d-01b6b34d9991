#!/usr/bin/python3
# -*- coding: utf-8 -*-
"""
认证模块硬件信息工具 - 提供系统硬件信息收集功能
"""

import hashlib
from typing import Optional, Tuple
import threading

import wmi


class HardwareInfoCollector:
    """硬件信息收集器，提供系统硬件信息收集功能"""
    
    _machine_code: Optional[str] = None
    _lock = threading.Lock()

    @staticmethod
    def get_physical_mac_address() -> Optional[str]:
        """
        获取物理网卡MAC地址
        
        Returns:
            物理网卡MAC地址，如果获取失败则返回None
        """
        try:
            wmi_obj = wmi.WMI()
            for adapter in wmi_obj.Win32_NetworkAdapter(PhysicalAdapter=True):
                return adapter.MACAddress
            return None
        except Exception as e:
            print(f"获取MAC地址失败: {e}")
            return None
    
    @staticmethod
    def get_system_info() -> Tuple[str, str, str, str]:
        """
        获取系统硬件信息
        
        Returns:
            包含CPU信息、磁盘信息、GPU信息和MAC地址的元组
        """
        try:
            cpu_info = ','.join(cpu.Name.strip() for cpu in wmi.WMI().Win32_Processor())
            disk_info = ','.join(disk.Model for disk in wmi.WMI().Win32_DiskDrive())
            gpu_info = ','.join(gpu.Name for gpu in wmi.WMI().Win32_VideoController())
            mac_info = HardwareInfoCollector.get_physical_mac_address()
            return cpu_info, disk_info, gpu_info, mac_info or ""
        except Exception as e:
            print(f"获取系统信息失败: {e}")
            return "", "", "", ""
    
    @staticmethod
    def _generate_machine_code() -> str:
        """
        生成机器码（私有方法）
        Returns:
            基于硬件信息的机器码
        """
        try:
            cpu_info, disk_info, gpu_info, mac_info = HardwareInfoCollector.get_system_info()
            hash_source = ''.join(cpu_info) + ''.join(disk_info) + ''.join(gpu_info) + ''.join(mac_info)
      
            return hashlib.md5(hash_source.encode()).hexdigest()
        except Exception as e:
            print(f"生成机器码失败: {e}")
            return ""

    @classmethod
    def get_machine_code(cls) -> str:
        """
        获取全局唯一机器码，首次调用时生成并缓存，后续直接返回
        Returns:
            机器码字符串
        """
        if cls._machine_code is None:
            with cls._lock:
                if cls._machine_code is None:  # 双重检查，防止并发生成
                    cls._machine_code = cls._generate_machine_code()
            cls._machine_code = cls._generate_machine_code()
        return cls._machine_code


# 创建全局实例
hardware_collector = HardwareInfoCollector() 
