# utils/html_processor.py
import re
import base64
import os
import uuid
import sys
from pathlib import Path
from typing import Tuple, Optional, List
from utils.logger import get_logger
from config import config
# 添加项目根目录到 Python 路径，以便能够导入 utils 模块
# current_dir = os.path.dirname(os.path.abspath(__file__))
# project_root = os.path.dirname(current_dir)  # 获取父目录作为项目根目录
# if project_root not in sys.path:
#     sys.path.append(project_root)
#
# try:
#     from utils.logger import get_logger
# except ImportError:
#     # 如果导入失败，使用基本的日志配置
#     def get_logger(name):
#         logger = logging.getLogger(name)
#         if not logger.handlers:
#             handler = logging.StreamHandler()
#             formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
#             handler.setFormatter(formatter)
#             logger.addHandler(handler)
#             logger.setLevel(logging.INFO)
#         return logger

class HtmlProcessor:
    """HTML处理工具类，用于处理富文本内容，提取图片和文本"""
    
    def __init__(self):
        self._logger = get_logger("utils.html_processor")
        
        # 使用配置中的项目根目录来确定临时目录位置
        self._temp_dir = os.path.join(str(config.project_root), "temp")
        
        # 确保临时目录存在
        if not os.path.exists(self._temp_dir):
            os.makedirs(self._temp_dir)
    
    def extract_image_and_text(self, html_content: str) -> Tuple[str, Optional[str]]:
        """从HTML内容中提取第一张图片和文本
        
        Args:
            html_content: HTML格式的内容
            
        Returns:
            Tuple[str, Optional[str]]: (HTML内容(不含img标签), 图片路径或None)
        """
        if not html_content:
            return "", None
            
        try:
            # 使用正则表达式查找第一个img标签
            img_match = re.search(r'<img[^>]+src=["\']([^"\']+)["\'][^>]*>', html_content)
            img_path = None
            
            if img_match:
                # 提取图片src
                src = img_match.group(1)
                
                if src.startswith('data:image'):
                    # 处理Base64编码的图片
                    img_path = self._save_base64_image(src)
                elif src.startswith(('http://', 'https://')):
                    # 处理网络图片URL
                    img_path = src
                elif os.path.exists(src):
                    # 本地文件路径
                    img_path = src
                
                # 从HTML中移除img标签，但保留其他HTML标签
                html_content = re.sub(r'<img[^>]+>', '', html_content)
            
            # 返回处理后的HTML内容（不含img标签）和图片路径
            return html_content, img_path
            
        except Exception as e:
            self._logger.error(f"提取图片和文本时出错: {str(e)}", exc_info=True)
            # 出错时返回原始HTML内容，不处理图片
            return html_content, None
    
    def extract_all_images_and_text(self, html_content: str) -> Tuple[str, List[str]]:
        """从HTML内容中提取所有图片和文本
        
        Args:
            html_content: HTML格式的内容
            
        Returns:
            Tuple[str, List[str]]: (HTML内容(不含img标签), 图片路径列表)
        """
        if not html_content:
            return "", []
            
        try:
            # 使用正则表达式查找所有img标签
            img_matches = re.findall(r'<img[^>]+src=["\']([^"\']+)["\'][^>]*>', html_content)
            img_paths = []
            
            for src in img_matches:
                if src.startswith('data:image'):
                    # 处理Base64编码的图片
                    img_path = self._save_base64_image(src)
                    if img_path:
                        img_paths.append(img_path)
                elif src.startswith(('http://', 'https://')):
                    # 处理网络图片URL
                    img_paths.append(src)
                elif os.path.exists(src):
                    # 本地文件路径
                    img_paths.append(src)
            
            # 从HTML中移除所有img标签，但保留其他HTML标签
            html_without_img = re.sub(r'<img[^>]+>', '', html_content)
            
            # 返回处理后的HTML内容（不含img标签）和图片路径列表
            return html_without_img, img_paths
            
        except Exception as e:
            self._logger.error(f"提取所有图片和文本时出错: {str(e)}", exc_info=True)
            # 出错时返回原始HTML内容，不处理图片
            return html_content, []
    
    def _save_base64_image(self, base64_str: str) -> str:
        """保存Base64编码的图片到临时文件
        
        Args:
            base64_str: Base64编码的图片数据
            
        Returns:
            str: 保存的图片路径
        """
        try:
            # 解析Base64数据
            if "," in base64_str:
                header, encoded = base64_str.split(",", 1)
            else:
                header = "image/png"  # 默认格式
                encoded = base64_str
                
            img_format = "png"  # 默认格式
            
            # 尝试从header中获取图片格式
            if "image/jpeg" in header:
                img_format = "jpg"
            elif "image/png" in header:
                img_format = "png"
            elif "image/gif" in header:
                img_format = "gif"
                
            # 生成唯一文件名
            file_name = f"img_{uuid.uuid4().hex}.{img_format}"
            file_path = os.path.join(self._temp_dir, file_name)
            
            # 解码并保存图片
            img_data = base64.b64decode(encoded)
            with open(file_path, "wb") as f:
                f.write(img_data)
                
            return file_path
            
        except Exception as e:
            self._logger.error(f"保存Base64图片时出错: {str(e)}", exc_info=True)
            return None
    
    def has_image(self, html_content: str) -> bool:
        """检查HTML内容是否包含图片
        
        Args:
            html_content: HTML格式的内容
            
        Returns:
            bool: 是否包含图片
        """
        if not html_content:
            return False
        
        return bool(re.search(r'<img[^>]+src=["\']([^"\']+)["\'][^>]*>', html_content))
    
    
if __name__ == "__main__":
    html_processor = HtmlProcessor()
    
    # 创建测试目录
    test_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "test_images")
    if not os.path.exists(test_dir):
        os.makedirs(test_dir)
    
    # 创建测试图片文件
    test_images = ["ninng.png", "ninng12.png", "ninng123.jpg"]
    for img_name in test_images:
        with open(os.path.join(test_dir, img_name), "wb") as f:
            f.write(b"test image data")
    
    # 构建测试HTML内容，使用绝对路径
    img_paths = [os.path.join(test_dir, img) for img in test_images]
    html_content = f"""
    <img src="{img_paths[0]}">
    <img src="{img_paths[1]}">
    <img src="{img_paths[2]}">
    测试消<b>息3</b>
    鹅嘎我<u>国你往23</u>13
    测试消息3
    """
    
    # 测试提取单张图片
    html_without_img, img = html_processor.extract_image_and_text(html_content)
    print("提取单张图片结果:")
    print(f"HTML内容(不含img标签): {html_without_img}")
    print(f"图片: {img}")
    print()
    
    # 测试提取所有图片
    html_without_img, imgs = html_processor.extract_all_images_and_text(html_content)
    print("提取所有图片结果:")
    print(f"HTML内容(不含img标签): {html_without_img}")
    print(f"图片列表: {imgs}")
    print(f"图片数量: {len(imgs)}")
    
    # 测试提取纯文本（如果需要）
    # text_content = re.sub(r'<[^>]+>', '', html_without_img).strip()
    # print("\n提取纯文本结果:")
    # print(f"纯文本内容: {text_content}")

