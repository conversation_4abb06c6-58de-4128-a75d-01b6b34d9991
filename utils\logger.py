#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
日志模块 - 提供全局日志功能
使用loguru库实现日志管理，支持日志轮转、级别过滤和格式化
"""

import os
import sys
from datetime import datetime
from pathlib import Path
from typing import Dict, Optional, Union
from config import config
from loguru import logger

class LoggerManager:
    """日志管理器类，用于配置和管理应用日志"""
    
    def __init__(self):
        """初始化日志管理器"""
        self._initialized = False
        # 默认日志级别
        self._default_level = "DEBUG"
        # 模块日志级别
        self._module_levels: Dict[str, str] = {}
    
    def init_logger(self, 
                   log_dir: Optional[Union[str, Path]] = None, 
                   default_level: str = "DEBUG",
                   rotation: str = "10 MB",
                   retention: str = "1 month"):
        """
        初始化日志系统
        
        Args:
            log_dir: 日志文件存储目录，默认为 {APPDATA}/logs
            default_level: 默认日志级别
            rotation: 日志轮转策略 (如: "10 MB", "1 day")
            retention: 日志保留策略 (如: "1 month", "10 days")
        """
        if self._initialized:
            return
        
        # 设置默认日志级别
        self._default_level = default_level
        
        # 移除默认处理器
        logger.remove()
        
        # 添加控制台处理器
        logger.add(
            sys.stderr,
            level=default_level,
            format="<green>{time:YYYY-MM-DD HH:mm:ss.SSS}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>"
        )
        
        # 如果提供了日志目录，配置文件日志
        if log_dir:
            log_path = Path(log_dir)
            print(log_path)
        else:
            # 默认使用 APPDATA 下的 logs 目录
            log_path =config.get("logs_path")
           # app_data = os.getenv('APPDATA') or os.path.expanduser('~')
            #log_path = Path(app_data) / "TeleTest" / "logs"
            log_path = Path(log_path)  # 转为Path对象
            print(log_path)
        
        # 确保日志目录存在
        log_path.mkdir(parents=True, exist_ok=True)
        
        # 添加文件处理器 - 所有日志
        log_file = log_path / f"teletest_{datetime.now().strftime('%Y%m%d')}.log"
        logger.add(
            str(log_file),
            level="DEBUG",
            format="{time:YYYY-MM-DD HH:mm:ss.SSS} | {level: <8} | {name}:{function}:{line} - {message}",
            rotation=rotation,
            retention=retention,
            encoding="utf-8"
        )
        
        # 添加文件处理器 - 仅错误日志
        error_log_file = log_path / f"teletest_error_{datetime.now().strftime('%Y%m%d')}.log"
        logger.add(
            str(error_log_file),
            level="ERROR",
            format="{time:YYYY-MM-DD HH:mm:ss.SSS} | {level: <8} | {name}:{function}:{line} - {message}",
            rotation=rotation,
            retention=retention,
            encoding="utf-8"
        )
        
        self._initialized = True
        
        # 记录初始化完成日志
        logger.info(f"日志系统初始化完成，默认级别: {default_level}，日志目录: {log_path}")
    
    def set_module_level(self, module_name: str, level: str) -> None:
        """
        为特定模块设置日志级别
        
        Args:
            module_name: 模块名称
            level: 日志级别
        """
        self._module_levels[module_name] = level
        logger.info(f"设置模块 {module_name} 的日志级别为 {level}")
    
    def get_logger(self, module_name: str):
        """
        获取指定模块的日志记录器
        
        Args:
            module_name: 模块名称
        
        Returns:
            配置好的logger实例
        """
        if not self._initialized:
            self.init_logger()
        
        # 获取模块特定级别，如果没有设置则使用默认级别
        level = self._module_levels.get(module_name, self._default_level)
        
        # 使用模块名称作为上下文信息
        module_logger = logger.bind(name=module_name)
        
        return module_logger
    
    def exception_handler(self, func):
        """
        异常处理装饰器，用于捕获并记录函数执行过程中的异常
        
        Args:
            func: 要装饰的函数
            
        Returns:
            装饰后的函数
        """
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except Exception as e:
                module_name = func.__module__
                func_name = func.__name__
                module_logger = self.get_logger(module_name)
                module_logger.exception(f"函数 {func_name} 执行异常: {str(e)}")
                raise
        return wrapper


# 创建全局日志管理器实例
log_manager = LoggerManager()

# 提供便捷访问方法
def get_logger(module_name: str):
    """获取指定模块的日志记录器"""
    return log_manager.get_logger(module_name)

def init_logger(**kwargs):
    """初始化日志系统"""
    log_manager.init_logger(**kwargs)

def set_module_level(module_name: str, level: str):
    """为特定模块设置日志级别"""
    log_manager.set_module_level(module_name, level)

def exception_handler(func):
    """异常处理装饰器"""
    return log_manager.exception_handler(func) 
