"""
日志使用示例 - 展示如何在不同模块中使用日志系统
"""
from utils.logger import get_logger, exception_handler

# 1. 在应用层中使用日志
class AccountController:
    def __init__(self):
        # 使用模块路径作为日志名称
        self.logger = get_logger("app.controllers.account_controller")
    
    def add_account(self, phone: str):
        self.logger.info(f"开始添加账户: {phone}")
        try:
            # 业务逻辑
            result = self._process_account(phone)
            self.logger.info(f"账户添加成功: {phone}")
            return result
        except Exception as e:
            self.logger.error(f"账户添加失败: {phone}, 错误: {str(e)}")
            raise
    
    def _process_account(self, phone: str):
        self.logger.debug(f"处理账户: {phone}")
        # 处理逻辑
        return {"status": "success", "phone": phone}

# 2. 在核心层中使用日志
class TelegramClient:
    def __init__(self, session_name):
        self.session_name = session_name
        self.logger = get_logger("core.telegram.client")
    
    def connect(self):
        self.logger.info(f"连接Telegram客户端: {self.session_name}")
        # 连接逻辑
        return True
    
    @exception_handler
    def send_message(self, chat_id, message):
        self.logger.debug(f"准备发送消息到 {chat_id}")
        # 如果出现异常，exception_handler装饰器会自动记录错误
        if not isinstance(chat_id, str):
            raise ValueError("chat_id必须是字符串")
        
        self.logger.info(f"消息已发送到 {chat_id}")
        return True

# 3. 在数据访问层中使用日志
class AccountRepository:
    def __init__(self):
        self.logger = get_logger("data.repositories.account_repo")
    
    async def save(self, account_data):
        self.logger.info(f"保存账户数据: {account_data.get('phone')}")
        # 保存逻辑
        self.logger.debug(f"账户数据保存细节: {account_data}")
        return True
    
    async def find_by_phone(self, phone):
        self.logger.info(f"查询账户: {phone}")
        # 查询逻辑
        return {"phone": phone, "status": "active"}

# 4. 在工具类中使用日志
class ConfigManager:
    def __init__(self):
        self.logger = get_logger("utils.config_manager")
    
    def load_config(self, config_path):
        self.logger.info(f"加载配置文件: {config_path}")
        try:
            # 配置加载逻辑
            self.logger.debug("配置加载成功")
            return {"api_id": "123456", "api_hash": "abcdef"}
        except Exception as e:
            self.logger.error(f"配置加载失败: {str(e)}")
            raise 