import ntplib
from datetime import datetime, timezone
from typing import List, Optional

NTP_SERVERS = [
    'pool.ntp.org',
    'time.google.com',
    'time.windows.com',
    'time1.aliyun.com',
    'ntp.ntsc.ac.cn',
]

def get_network_time(ntp_servers: Optional[List[str]] = None) -> Optional[datetime]:
    """
    依次从多个NTP服务器获取当前UTC时间，全部失败返回None。
    :param ntp_servers: NTP服务器地址列表，默认使用内置服务器
    :return: datetime对象 (UTC时间)，如果全部失败则返回 None
    """
    servers = ntp_servers or NTP_SERVERS
    client = ntplib.NTPClient()
    for host in servers:
        try:
            response = client.request(host, version=3, timeout=3)
            utc_dt = datetime.fromtimestamp(response.tx_time, timezone.utc)
            return utc_dt
        except Exception as e:
            continue
    return None 