from PySide6.QtCore import QObject, Signal

class SignalBus(QObject):
    """
    信号总线，用于不同层之间的通信
    避免核心层直接与UI层耦合
    """
    # 全局通知信号
    notification = Signal(str, str)  # (通知类型, 通知内容)
    
    # 进度通知信号
    progress_updated = Signal(str, str, str)  # (操作类型, 进度内容, 标题)
    
    # 操作结果信号
    operation_result = Signal(str, bool, object)  # (操作类型, 是否成功, 结果数据)
    
    # 单例模式
    _instance = None
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(SignalBus, cls).__new__(cls)
            # 初始化
            cls._instance.__initialized = False
        return cls._instance
    
    def __init__(self):
        if self.__initialized:
            return
        super().__init__()
        self.__initialized = True
    
    def send_notification(self, notice_type: str, content: str):
        """发送通知"""
        self.notification.emit(notice_type, content)
    
    def send_progress(self, operation: str, content: str, title: str = "进行中"):
        """发送进度通知"""
        self.progress_updated.emit(operation, content, title)
    
    def send_result(self, operation: str, success: bool, data=None):
        """发送操作结果"""
        self.operation_result.emit(operation, success, data)

# 全局单例
signal_bus = SignalBus() 