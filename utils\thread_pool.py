#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
线程池管理模块
负责管理应用程序的线程池，处理异步任务
"""

import asyncio
import concurrent.futures
from typing import Callable, Any, List, Dict, Optional
from functools import partial

from .logger import get_logger

# 获取模块日志记录器
logger = get_logger(__name__)


class ThreadPoolManager:
    """线程池管理器，用于处理后台任务"""
    
    def __init__(self, max_workers: Optional[int] = None):
        """
        初始化线程池管理器
        
        Args:
            max_workers: 最大工作线程数，默认为None(系统自动决定)
        """
        self._executor = concurrent.futures.ThreadPoolExecutor(max_workers=max_workers)
        self._tasks: Dict[str, concurrent.futures.Future] = {}
    
    def submit_task(self, task_id: str, func: Callable, *args, **kwargs) -> concurrent.futures.Future:
        """
        提交任务到线程池
        
        Args:
            task_id: 任务ID，用于标识和跟踪任务
            func: 要执行的函数
            *args: 传递给函数的位置参数
            **kwargs: 传递给函数的关键字参数
            
        Returns:
            Future对象，可用于获取任务结果
        """
        logger.debug(f"提交任务 {task_id} 到线程池")
        future = self._executor.submit(func, *args, **kwargs)
        self._tasks[task_id] = future
        return future
    
    def cancel_task(self, task_id: str) -> bool:
        """
        取消任务
        
        Args:
            task_id: 要取消的任务ID
            
        Returns:
            bool: 是否成功取消
        """
        if task_id in self._tasks:
            future = self._tasks[task_id]
            result = future.cancel()
            if result:
                logger.debug(f"已取消任务 {task_id}")
                del self._tasks[task_id]
            else:
                logger.debug(f"无法取消任务 {task_id}，可能已完成或正在运行")
            return result
        
        logger.warning(f"尝试取消不存在的任务 {task_id}")
        return False
    
    def get_task_result(self, task_id: str, timeout: Optional[float] = None) -> Any:
        """
        获取任务结果
        
        Args:
            task_id: 任务ID
            timeout: 等待结果的超时时间(秒)
            
        Returns:
            任务的返回值
            
        Raises:
            KeyError: 如果任务ID不存在
            TimeoutError: 如果等待超时
            Exception: 任务执行过程中的异常
        """
        if task_id not in self._tasks:
            raise KeyError(f"任务 {task_id} 不存在")
        
        future = self._tasks[task_id]
        try:
            result = future.result(timeout=timeout)
            logger.debug(f"获取任务 {task_id} 的结果")
            return result
        except concurrent.futures.TimeoutError:
            logger.warning(f"获取任务 {task_id} 结果超时")
            raise TimeoutError(f"获取任务 {task_id} 结果超时")
        except Exception as e:
            logger.error(f"任务 {task_id} 执行出错: {str(e)}")
            raise
    
    def is_task_done(self, task_id: str) -> bool:
        """
        检查任务是否完成
        
        Args:
            task_id: 任务ID
            
        Returns:
            bool: 任务是否完成
        """
        if task_id in self._tasks:
            return self._tasks[task_id].done()
        return False
    
    def shutdown(self, wait: bool = True):
        """
        关闭线程池
        
        Args:
            wait: 是否等待所有任务完成
        """
        logger.info(f"关闭线程池，等待所有任务完成: {wait}")
        self._executor.shutdown(wait=wait)
        self._tasks.clear()
    
    async def run_in_thread(self, func: Callable, *args, **kwargs) -> Any:
        """
        在线程中运行函数，适用于异步上下文
        
        Args:
            func: 要执行的函数
            *args: 传递给函数的位置参数
            **kwargs: 传递给函数的关键字参数
            
        Returns:
            函数的返回值
        """
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(
            self._executor, 
            partial(func, *args, **kwargs)
        )


# 创建全局线程池实例
thread_pool = ThreadPoolManager() 