#!/usr/bin/python3
# -*- coding: utf-8 -*-
"""
版本更新检查模块 - 提供软件版本检查和更新功能
"""

import os
import json
import subprocess
from pathlib import Path
from typing import Dict, Any, Optional, Tuple

from PySide6.QtCore import QObject, Signal
from utils.logger import get_logger
from frameConf.setting import VERSION, APP_ID, APP_NAME
from core.auth.models import software_config
from config import config


class UpdateConfig:
    """更新配置类，存储版本更新相关信息"""
    
    def __init__(self):
        self.old_ver = VERSION  # 当前版本
        self.new_ver = ""  # 新版本
        self.complete_url = ""  # 完整安装包下载地址
        self.update_url = ""  # 更新包下载地址
        self.force_update = "no"  # 是否强制更新
        self.visible = "yes"  # 是否显示更新提示
        self.command = ""  # 更新命令
        self.update_detail = ""  # 更新内容

    def load_from_file(self, file_path: str) -> bool:
        """从文件加载更新配置"""
        try:
            if not os.path.exists(file_path):
                return False
                
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
                
            self.old_ver = data.get('oldVer', VERSION)
            self.new_ver = data.get('newVer', '')
            self.complete_url = data.get('completeUrl', '')
            self.update_url = data.get('updateUrl', '')
            self.force_update = data.get('forceUpdate', 'no')
            self.visible = data.get('visible', 'yes')
            self.command = data.get('command', '')
            self.update_detail = data.get('updateDetail', '')
            
            return True
        except Exception as e:
            return False
    
    def save_to_file(self, file_path: str) -> bool:
        """保存更新配置到文件"""
        try:
            data = {
                'oldVer': self.old_ver,
                'newVer': self.new_ver,
                'completeUrl': self.complete_url,
                'updateUrl': self.update_url,
                'forceUpdate': self.force_update,
                'visible': self.visible,
                'command': self.command,
                'updateDetail': self.update_detail
            }
            
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=4)
                
            return True
        except Exception as e:
            return False
    
    def has_new_version(self) -> bool:
        """检查是否有新版本"""
        if not self.new_ver:
            return False
        
        # 简单的版本比较，可以根据需要扩展为更复杂的版本比较逻辑
        return self.new_ver != self.old_ver and self.visible.lower() == 'yes'
    
    def is_force_update(self) -> bool:
        """检查是否强制更新"""
        return self.force_update.lower() == 'yes'


class UpdateChecker(QObject):
    """版本更新检查器"""
    
    # 定义信号
    update_available = Signal(str, str, str)  # 新版本号, 下载链接, 更新内容
    
    def __init__(self):
        super().__init__()
        self._logger = get_logger("utils.update_checker")
        self.update_config = UpdateConfig()
        # 使用项目根目录的 update.tmp 文件
        self.update_file_path = str(Path(config.project_root) / "update.tmp")
    
    def check_for_updates(self) -> bool:
        """检查是否有更新可用"""
        try:
            # 检查 software_config.new_url 是否有值
            if software_config.new_url:
                self._logger.info(f"检测到新版本链接: {software_config.new_url}")
                
                # 创建更新配置
                self.update_config.old_ver = VERSION
                self.update_config.new_ver = VERSION.split('.')[0] + "." + str(int(VERSION.split('.')[1]) + 1)  # 版本号+0.1
                self.update_config.complete_url = software_config.new_url
                self.update_config.update_url = software_config.new_url
                self.update_config.force_update = "yes" if software_config.new_url else "no"
                self.update_config.visible = "yes"
                self.update_config.command = ""
                self.update_config.update_detail = software_config.new_content or f"{APP_NAME} 有新版本可用，请更新！"
                
                # 保存更新配置到 update.tmp 文件
                self.update_config.save_to_file(self.update_file_path)
                
                # 发射信号通知有新版本
                self.update_available.emit(
                    self.update_config.new_ver,
                    self.update_config.complete_url,
                    self.update_config.update_detail
                )
                return True
            else:
                # 尝试从文件加载更新配置
                if self.update_config.load_from_file(self.update_file_path):
                    if self.update_config.has_new_version():
                        self._logger.info(f"从文件检测到新版本: {self.update_config.new_ver}")
                        # 发射信号通知有新版本
                        self.update_available.emit(
                            self.update_config.new_ver,
                            self.update_config.complete_url,
                            self.update_config.update_detail
                        )
                        return True
 
            return False
        except Exception as e:
            self._logger.error(f"检查更新时发生错误: {str(e)}")
            return False
    
    def perform_update(self) -> bool:
        """执行更新操作"""
        try:
            if not self.update_config.complete_url and not software_config.new_url:
                self._logger.error("没有可用的下载链接")
                return False
            
            # 使用 software_config.new_url 或 update_config.complete_url
            download_url = software_config.new_url or self.update_config.complete_url
            
            # 打开浏览器下载新版本
            #QDesktopServices.openUrl(QUrl(download_url))
            
            # 执行更新命令
            subprocess.Popen("updater.exe -update", shell=True)
            return True
        except Exception as e:
            self._logger.error(f"执行更新时发生错误: {str(e)}")
            return False


# 创建全局更新检查器实例
update_checker = UpdateChecker() 
