from PySide6.QtCore import QObject, Signal, QTimer
from app.controllers.auth_controller import AuthController
from utils.logger import get_logger
from  qasync import asyncSlot

class VipChecker(QObject):
    vip_expired = Signal(str)  # 发送过期信息的信号
    
    def __init__(self):
        super().__init__()
        self._logger = get_logger("utils.vip_checker")
        self._controller = None
        self._timer = QTimer(self)
        self._timer.setInterval(30 * 60 * 1000)  # 30分钟检查一次
        self._timer.timeout.connect(self._on_timer_timeout)
        self._is_vip_valid = False
        self._expire_message = ""
        self._initialized = False
        self._checking_started = False
        
    def initialize(self, auth_controller: AuthController):
        """仅初始化控制器，但不立即检查"""
        self._controller = auth_controller
        self._initialized = True
        self._logger.info("VIP检查器已初始化，等待登录后开始检查")
        
    async def start_checking(self):
        """登录成功后调用此方法开始检查VIP状态"""
        if not self._initialized:
            self._logger.error("VIP检查器尚未初始化，无法开始检查")
            return
            
        if self._checking_started:
            self._logger.info("VIP检查已经启动，不重复启动")
            return
            
        self._logger.info("开始VIP状态检查")
        self._checking_started = True
        self._timer.start()
        # 立即执行一次检查
        await self._check_vip_status()
        
    def stop(self):
        """停止检查"""
        if self._checking_started:
            self._timer.stop()
            self._checking_started = False
            self._logger.info("VIP状态检查已停止")
    
    @asyncSlot()
    async def _on_timer_timeout(self):
        """定时器触发时的处理函数"""
        await self._check_vip_status()
        
    async def _check_vip_status(self):
        if not self._controller:
            self._logger.warning("无法检查VIP状态：控制器未初始化")
            self._is_vip_valid = False
            return
            
        self._logger.debug("正在检查VIP状态...")
        try:
            result = await self._controller.verify_vip()
            # 检查结果是否包含必要的字段
            if not isinstance(result, dict):
                self._logger.error(f"VIP验证返回了非字典结果: {result}")
                self._is_vip_valid = False
                self._expire_message = "VIP验证失败，返回格式错误"
                self.vip_expired.emit(self._expire_message)
                return
            
            # 兼容旧格式的响应
            if result.get('success'):
                msg = result.get('message', '')
                self._is_vip_valid = True
                self._logger.info("VIP状态有效")
                return
            else:
                self._is_vip_valid = False
                self._expire_message = result.get('message', '验证VIP失败，请联系代理')
                self._logger.error(f"VIP验证失败: {self._expire_message}")
                self.vip_expired.emit(self._expire_message)
                return
        except Exception as e:
            self._logger.exception(f"VIP状态检查出现异常: {e}")
            self._is_vip_valid = False
            self._expire_message = f"VIP验证出错: {str(e)}"
            self.vip_expired.emit(self._expire_message)
            
    def is_vip_valid(self):
        return self._is_vip_valid
        
    def get_expire_message(self):
        return self._expire_message

# 创建单例
vip_checker = VipChecker()
