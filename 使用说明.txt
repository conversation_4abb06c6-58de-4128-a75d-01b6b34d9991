运行环境windows 10+ 64位系统
如果使用多ip服务器或电脑，请先再ip池配置你的ip。
请把软件目录下的3proxy文件夹，再win的防火墙里添加信任。【代理软件会有大量网络转发，导致win误判。】
3proxy属于github高星开源应用，可下载win64版本任意替换到目录里的3proxy文件

启动软件会自检代理ip池是否启动，会自动启动，用户可自检是否启动成功。



每个账号每日采集建议不超过 20 个群组，按线程配置适量并发    【监听采集使用高质量账户】
单账号日发送条数控制在10条以上、45条以下（仅供参考，实际根据账号质量测试微调）。【群发可以使用低质量账户】
每账号每天邀请 20~40 人，设置延迟 2~4 秒，开启失败切号和加为联系人功能  (仅供参考 自行测试)







2.登录接口已经完善，明天优先完成登录功能    【已完善99%】
    登录成功检测用户是否到期：如果到期弹窗提醒vip到期，请续费，附带链接。【已完成】
    还差vip验证，vip到期验证，到期是否允许登录等   检查VIP到期功能，使用网络时间【已完成】
    vip到期弹窗提醒     【到期然后软件就不允许使用了】         【已完成】
    心跳失败弹窗提醒账户异常，因为可能是账户被限制了。      


3.完成单个账户登录不入库和不更新显示的问题      【已修复】


1.邀请用户时发生异常        [已修复]
应该需要先添加用户，再邀请入群，不然没法入群，多尝试几种方案解决这个问题。




6.邀请用户的状态显示问题及邀请失败问题  【已完成】

4.关键词监听通知发送到群功能           【已完成】


5.消息群发的状态显示问题             【已完成】

账户间隔时间功能，应该是基于账户上一次发送消息时间来判断，只要大于设定间隔，就可以引用该账户，而不是必须延时间隔。



7.代理ip池服务端测试                【】

1.添加的代理ip为本地代理时，当添加完成写入配置文件后，需要判断服务是否启动，如果已启动，需要重启，未启动，则启动服务再执行验证功能。

2.批量添加代理ip完成后，开始执行验证功能应只验证当前添加的批次，而不是验证所有代理IP。


3.打开软件时，需要判断代理ip里是否有本地代理，如果有本地代理则启动代理服务。【已修复】
3.删除的ip为本地代理ip时候，配置文件里代理ip行也要删除，然后重启服务。现在删除多个可以删除，删除单个ip不会删除，检查这个问题并修复 【已修复】


添加多行范围ip时出错，检查修复这个错误。ERROR    | app.services.proxy_service:add_ip_range_task:315 - 添加IP范围失败: Expected 4 octets in '***********-5192.168.2.1-3'




线程销毁问题  【使用了适配方案完成，后期可能考虑下面的方案更强大】

无解：得重构线程管理文件改为线程池+异步优先级管理方案，这样才能确保耗时任务和普通任务都不会销毁
但此方案得重新服务端调用线程代码，后期在重构
 

批量导入session     
现在的方案是在client里使用asyncio批量导入，可用，但是不够优雅，应该服务层调用线程管理，批量登陆，考虑优化


8.上线




需要解决的问题
1.数据库保存session路径改为自动识别项目路径【完成】
2.增加图文发送功能【完成】
3.用户状态管理功能改为实时变动
4.在线更新功能完善  【已完成】
5.每个页面vip检测功能完成【完成】
6.修复删除账户或任务时删除对应任务相关数据【完成】
7.登录未成功不保留临时session，调用telethon的删除命令实现


8.用户设置功能
要支持以用户组批量设置用户的相关资料，或个人资料，或设置
增加设置字段：单账户每日可发送消息数量，单账户每日可邀请人入群数量。支持批量设置所有账户或以分组设置



9.设计一个工具箱，里面各种筛选处理工具，tdata转session

10.解决session保存数据库路径问题，路径应该程序识别组合，而不是存起来【完成】

11.虚拟设备功能，每个账户可设置单独的登录设备，提高用户质量，设计一个设备表，用户设备表绑定ID即可

12.监听功能删除时显示任务名错误，点击全部错误，默认显示用户数量错误【完成】








1.2.7版本更新

1.增加在线更新[完成]
2.增加图文消息【完成】
3.增加使用文档【完成】
4.修复消息尾随机表情【完成】
5.修复路径bug【完成】
6.修复删除监听任务时不会删除数据库队医那个任务采集数据的bug【完成】
7.修复软件设置显示异常【完成】
8.上线直登号和协议号互转功能【待调整】
9.完善账户设置【待调整】
8.采用单文件模式使用更简洁【完成】

9.vip检查功能，每个view页面都需要检测，如果vip到期则所有功能不能使用，提醒用户联系上级代理续费。【完成】
    检查参数都设置好了，但是装饰器运行检测时会返回false导致拦截，使用pycharm调试装饰器找到问题并修复。
 