1.设置里配置好新版号和相关信息。

2.服务端配置好新版，并在老版本的更新和下载链接提交为新版

3.本地检测新版，生成update.tmp文件，运行updater.exe -update 进行更新



vip到期弹窗提醒 功能使用方法
主窗口中集成VIP检查
在主窗口初始化时添加VIP检查：
# 主窗口文件中添加
from utils.vip_checker import vip_checker
from qfluentwidgets import MessageBox

class MainWindow(QMainWindow):
    def __init__(self, auth_controller):
        super().__init__()
        # 初始化VIP检查器
        vip_checker.initialize(auth_controller)
        
        # 连接VIP过期信号
        vip_checker.vip_expired.connect(self.on_vip_expired)
        
    def on_vip_expired(self, message):
        """当VIP过期时显示提示并禁用功能"""
        MessageBox(
            "VIP已过期",
            f"{message}\n请联系上级代理续费。",
            self
        ).exec()
        
        # 禁用所有功能按钮或切换到限制模式
        self.disable_all_features()
        
    def disable_all_features(self):
        """禁用所有需要VIP的功能"""
        # 这里根据实际UI结构禁用按钮或功能区
        for widget in self.findChildren(QPushButton):
            if widget.objectName() not in ["btn_contact_agent", "btn_logout"]:
                widget.setEnabled(False)


在各个视图中使用装饰器
在各个功能视图中，对需要VIP权限的方法使用装饰器：

# 在各个view文件中
from utils.decorators import require_vip, require_vip_async

class SomeFeatureView(QWidget):
    
    @require_vip
    def on_feature_button_clicked(self):
        # 功能实现...
        pass
        
    @require_vip_async
    async def some_async_feature(self):
        # 异步功能实现...
        pass