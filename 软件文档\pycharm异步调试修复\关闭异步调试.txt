This also occurs in version 2023.3.4 (#PY-233.14475.56). For those who do not know how to disable this flag, which appears to be on by default, perform the following, from PY-57667. Note this worked for me without restarting PyCharm.

To revert changes: 
Open PyCharm
Use Shift + Shift (Search Everywhere)       点两次shift
In the popup type: Registry and press Enter
Find "Registry" in the list of results and click on it.
In the new popup find python.debug.asyncio.repl line and uncheck the respective checkbox
Press Close.
Restart the IDE.
The asyncio support will be disabled in the debugger.