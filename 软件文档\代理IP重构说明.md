# 代理IP模块重构说明

## 重构目标

1. 分离本地代理和远程代理的职责
2. 遵循分层架构原则，确保层级职责清晰
3. 提高代码可维护性和可测试性
4. 使用领域驱动设计思想，引入领域模型

## 架构变更

### 原有架构问题

1. 核心服务层与应用层职责不明确
2. 数据访问混杂在业务逻辑中
3. 本地代理和远程代理逻辑混合
4. 任务管理器包含业务逻辑

### 新架构设计

采用分层架构 + 领域驱动设计：

1. **领域层**：定义代理领域模型
   - `BaseProxy`：代理基类
   - `LocalProxy`：本地代理
   - `RemoteProxy`：远程代理

2. **核心服务层**：实现核心业务逻辑
   - `ProxyCoreService`：整合各子服务的核心服务
   - `ProxyConfigService`：配置文件管理服务
   - `ProxyValidationService`：代理验证服务
   - `Proxy3ProxyService`：3proxy服务管理

3. **仓库适配层**：连接领域模型和数据访问层
   - `ProxyRepositoryAdapter`：适配数据仓库和领域模型

4. **任务管理**：
   - `ProxyTaskManager`：纯粹的任务调度，不包含业务逻辑

5. **应用服务层**：
   - `ProxyService`：面向应用的服务接口，协调核心服务和仓库

6. **控制层**：
   - `ProxyController`：处理UI事件，调用应用服务

## 主要改进

1. **职责分离**：
   - 本地代理和远程代理通过类型区分，各自处理不同逻辑
   - 配置服务专注于配置文件生成和管理
   - 验证服务专注于代理验证
   - 3proxy服务专注于服务管理

2. **分层清晰**：
   - 数据访问层不再直接被控制层调用
   - 应用层不再直接操作数据层
   - 核心层通过仓库适配器访问数据

3. **信号机制改进**：
   - 统一信号定义和传递路径
   - 避免跨层级信号连接

4. **错误处理**：
   - 统一的错误处理和日志记录
   - 异常不会跨多层传递

5. **任务管理**：
   - 任务管理器专注于任务调度，不包含业务逻辑
   - 业务逻辑移至服务层

## 文件结构变更

```
core/proxy/
├── __init__.py                      # 模块初始化和全局实例
├── proxy_task_manager.py            # 任务管理器
├── domain/                          # 领域模型
│   ├── __init__.py
│   └── proxy.py                     # 代理领域模型
├── services/                        # 核心服务
│   ├── __init__.py
│   ├── proxy_core_service.py        # 核心代理服务
│   ├── proxy_config_service.py      # 配置服务
│   ├── proxy_validation_service.py  # 验证服务
│   └── proxy_3proxy_service.py      # 3proxy服务管理
└── repositories/                    # 仓库适配器
    ├── __init__.py
    └── proxy_repository_adapter.py  # 代理仓库适配器
```

## 使用方式

### 初始化

```python
# 在应用启动时初始化代理模块
from core.proxy import init_proxy_module

# 初始化并获取控制器
proxy_controller = await init_proxy_module()
```

### 获取服务

```python
# 获取代理服务
from core.proxy import get_proxy_service
proxy_service = get_proxy_service()

# 获取核心服务
from core.proxy import get_core_proxy_service
core_service = get_core_proxy_service()

# 获取任务管理器
from core.proxy import get_proxy_task_manager
task_manager = get_proxy_task_manager()
```

### 清理资源

```python
# 在应用退出时清理资源
from core.proxy import cleanup_proxy_module
await cleanup_proxy_module()
```

## 注意事项

1. 代理模块初始化必须在应用启动时进行
2. 使用前应确保模块已初始化
3. 应用退出时应调用清理函数以释放资源 