生成代理ip池的开发文档，参考代理ip管理@proxy_manager_ui.py，配合3proxy，验证，启动代理ip等，放子线程进行，代理ip分为本地代理和远程代理，然后是socks5,http 
参考文件@proxy_service.py
## 1.1 数据模型
### ProxyItem
- id: 唯一标识符，自增
- ip: 代理IP地址
- port: 代理端口
- username: 用户名(可选)
- password: 密码(可选)
- proxy_type: 代理类型(LOCAL/REMOTE)
- protocol: 协议类型(SOCKS5/HTTP)
- status: 状态(待验证/正常/验证失败)
- last_check: 最后验证时间

## 1.2 数据操作类
ProxyModel类负责:
代理数据的CRUD操作
IP范围解析
批量处理代理数据
按条件筛选代理

## 2.1 代理服务(ProxyService)
负责代理服务的核心操作:
3proxy服务管理(启动/停止/重启)
配置文件生成
代理验证
公网IP获取

## 2.2 异步任务管理(ProxyTaskManager)
处理耗时操作的异步执行:
创建和管理子线程
信号连接机制
异步任务状态监控
结果回调处理

## 3.1 代理控制器(ProxyController)
连接UI和服务:
处理视图信号
调用服务方法
更新UI状态
错误处理和日志记录

## 3.2 信号类(ProxySignals)
定义系统信号:
服务状态变化信号
代理列表更新信号
验证结果信号
操作结果信号

## 4.1 代理视图(ProxyView)
ui文件: @proxy_manager_ui.py 从ui文件识别组件
view文件：@proxy_view.py
处理UI交互:
信号发送
UI更新
用户输入验证
状态反馈

## 4.2 消息弹窗进度提醒
from ui.common.message_box import (show_dialog,set_window_center,show_toast,
                                   show_loading,hide_loading,move_loading,
                                   createSuccessInfoBar,createWarningInfoBar,createErrorInfoBar)
from ui.common.notification import show_notification
from ui.common.loading import show_loading, hide_loading, LoadingDialog 
识别使用已经有的组件，切勿修改。

## 5.1  技术要点
使用qasync处理Qt和异步代码的交互
采用异步IO操作验证代理
适当设置超时机制避免长时间阻塞
使用信号槽机制进行组件间通信

## 5.2  性能优化
批量验证时分批处理
缓存验证结果
避免频繁IO操作
定时检查代替轮询

## 6.1 接口设计
为其他模块提供的接口:
get_valid_proxy(): 获取有效代理
validate_proxy(): 验证指定代理
get_proxy_by_type(): 按类型获取代理


## 6.2 记录每个代理ip被绑定的次数，代理ip绑定telegram账户进行登录
为其他模块提供的接口:

## 7.1验证接口
IP_CHECK_SERVICES = [
    "http://httpbin.org/ip",
    "http://ip.3322.net", 
    "http://myip.ipip.net",   
    "http://icanhazip.com"
]
访问并返回代理ip访问耗时，通过即算验证成功，
## 7.1 技术要求
验证代理ip使用Pyside6的子线程+ aiohttp+aiohttp_socks