@proxy_view.py  @proxy_service.py @proxy_task_manager.py  @proxy_service.py @proxy_controller.py      1.当用户输入的是范围ip时，应该在控制器里调用utils方法，生成对应的ip，端口，用户名，密码。注意每组ip的端口不可以相同，用户名和密码不可以相同。生成后就刷新view显示，然后发送给服务层到核心层处理，验证和保存ip。2.只有本机的ip才需要写入配置文件，外部socks5不需要写入配置文件。

## 控制层
控制层不应该直接访问仓储层(Repository)

## 仓储层(Repository)的问题
当前AccountRepository中存在不合理设计：
每个方法都自行处理事务（调用commit和rollback）
仓储层应该只负责数据访问操作，不应该控制事务


## 服务层(Service)的问题
AccountService使用了上下文管理器来管理会话，但未完全发挥作用：


## 正确的实现方式
仓储层不应提交事务，只执行数据访问
服务层负责事务边界控制
服务层的异步上下文管理器(async with)应负责提交或回滚事务
## 建议修复方案
修改AccountRepository方法，移除所有commit和rollback调用
让服务层的上下文管理器处理事务提交/回滚
确保异常正确传播到服务层