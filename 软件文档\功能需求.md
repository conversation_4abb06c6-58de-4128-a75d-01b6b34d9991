技术栈：pyside6 + 异步 +子线程+ telethon +SQLAlchemy(orm异步数据库)+loguru日志模块+python-dotenv
架构：分层架构

# 功能需求

## 1. 系统配置
- **.env配置**：放项目根目录，包含appid、apphash、session文件夹路径、数据库路径等核心配置
- **持久化存储**：配置文件、sessions文件和数据库放APPDATA目录下
- **数据存储**：代理IP、用户信息等数据存储于数据库中
- **统一配置接口**：通过config.py提供全局配置访问

## 2. 日志管理
- 使用loguru模块进行日志记录
- 每个模块都需要记录日志，包含不同级别（INFO、WARNING、ERROR等）
- 日志轮转和归档功能，便于长期运行时的日志管理
- 异常捕获与记录，方便调试错误

## 3. Telegram多账户管理
- **账户操作**：
  - 增加新账户（手动输入/扫码）
  - 导入session账户
  - 账户绑定代理IP
  - 设置/修改两步验证
  - 刷新用户信息
  - 删除用户
  - 修改用户资料（头像、名称、简介等）
  - 重新验证用户
- **连接管理**：
  - 长期不活跃账户自动断开连接
  - 需要使用时再进行连接
  - 连接状态监控与显示
- **多线程处理**：使用qt子线程+异步方式管理多账户客户端，防止UI卡顿
- **修改用户资料** 可以修改用户的用户名，昵称，头像，简介等
  
## 4. 代理IP池管理
- **IP操作**：
  - 添加socks5代理IP
  - 批量导入代理IP
  - 账户绑定代理IP功能
  - 失效IP自动/手动删除
- **IP验证**：
  - 使用子线程+异步方式批量验证IP有效性
  - 定期检测代理IP状态
  - IP质量评分（速度、稳定性）
  - ip测速（验证的同时检测链接telegram api需要的时间）
  - 验证进度实时显示，支持中断验证操作
  - 验证任务在独立子线程中进行，不阻塞UI界面

## 5. 群组消息监控
- 监控指定群组的消息
- 多账户监控，提高效率和降低风险
- 消息过滤与关键词提取
- 数据写入异步数据库保存
- 消息提醒与通知机制

## 6. 数据库设计
- 使用SQLAlchemy ORM进行异步数据库操作
- 建立用户、代理IP、监控任务等数据模型
- 数据查询优化与索引设计
- 数据备份与恢复机制

## 7. 测试规范
- 每个数据库操作单独编写测试文件
- 接口单元测试
- 功能集成测试
- 性能压力测试

## 8. 技术要求
- 子线程使用Qt的子线程，数据库使用异步操作
- 所有代码使用typing进行类型注解，提高可维护性
- 预留扩展接口，便于后续功能增加
- 采用分层架构设计，提高代码复用性和可维护性
- 尽可能复用项目中已有的代码和函数

## 9. 用户界面
- 简洁直观的多账户管理界面
- 代理IP管理与状态显示
- 任务监控与数据可视化
- 操作日志实时显示
- 暗黑/明亮主题切换

# 项目分层架构设计

## 目录结构

```
/
├── .env                    # 环境变量配置文件
├── .env.example            # 环境变量示例文件
├── config.py               # 配置管理模块
├── main.py                 # 主程序入口
├── requirements.txt        # 依赖包列表
├── app/                    # 应用层
│   ├── __init__.py
│   ├── controllers/        # 控制器
│   │   ├── __init__.py
│   │   ├── account_controller.py    # 账户管理控制器
│   │   ├── proxy_controller.py      # 代理IP控制器
│   │   └── monitor_controller.py    # 消息监控控制器
│   └── services/           # 服务层
│       ├── __init__.py
│       ├── account_service.py       # 账户服务
│       ├── proxy_service.py         # 代理IP服务
│       └── monitor_service.py       # 监控服务
├── core/                   # 核心业务层
│   ├── __init__.py
│   ├── telegram/           # Telegram功能核心
│   │   ├── __init__.py
│   │   ├── client_manager.py        # 客户端管理器
│   │   ├── session_handler.py       # 会话处理
│   │   └── message_handler.py       # 消息处理
│   ├── proxy/              # 代理IP核心功能
│   │   ├── __init__.py
│   │   ├── validator.py             # IP验证器
│   │   └── connector.py             # 连接管理
│   └── monitor/            # 监控核心功能
│       ├── __init__.py
│       ├── filter.py                # 消息过滤器
│       └── extractor.py             # 关键词提取
├── data/                   # 数据访问层
│   ├── __init__.py
│   ├── database.py                  # 数据库连接
│   ├── repositories/       # 仓库模式
│   │   ├── __init__.py
│   │   ├── account_repo.py          # 账户数据仓库
│   │   ├── proxy_repo.py            # 代理IP数据仓库
│   │   └── monitor_repo.py          # 监控数据仓库
│   ├── models/             # 数据模型
│   │   ├── __init__.py
│   │   ├── account.py               # 账户模型
│   │   ├── proxy.py                 # 代理IP模型
│   │   └── monitor.py               # 监控任务模型
│   └── migrations/         # 数据库迁移脚本
├── ui/                     # 表示层
│   ├── __init__.py
│   ├── main_window.py               # 主窗口
│   ├── common/             # 公共UI组件
│   │   ├── __init__.py              # 统一导出组件
│   │   ├── message_box.py           # 消息框组件
│   │   ├── toast.py                 # Toast消息组件
│   │   ├── status_bar.py            # 状态栏管理器
│   │   ├── notification.py          # 通知角标组件
│   │   └── loading.py               # 加载指示器组件
│   ├── dialogs/            # 子窗口
│   ├── resources/          # UI资源文件
│   ├── designer/           # 存放Qt Designer自动生成的py文件
│   │   ├── __init__.py
│   │   ├── account_ui.py  # 账户界面基类
│   │   ├── proxy_ui.py    # 代理界面基类
│   │   └── monitor_ui.py  # 监控界面基类
│   └── views/              # 视图组件
│       ├── __init__.py
│       ├── account_view.py          # 账户管理视图
│       ├── proxy_view.py            # 代理管理视图
│       └── monitor_view.py          # 监控视图
├── utils/                  # 工具类
│   ├── __init__.py
│   ├── logger.py                    # 日志管理
│   ├── logger_examples.py           # 日志使用示例
│   └── thread_pool.py               # 线程池管理
└── tests/                  # 测试目录
    ├── __init__.py
    ├── test_logger.py                # 日志模块测试
    ├── unit/               # 单元测试
    │   ├── __init__.py
    │   ├── test_account.py
    │   ├── test_proxy.py
    │   └── test_monitor.py
    └── integration/        # 集成测试
        ├── __init__.py
        └── test_workflow.py
```

## 各层职责与接口定义

### 1. 表示层 (UI)
- 负责用户界面展示和用户交互
- 使用PySide6实现界面
- 通过控制器与应用层通信

### 2. 应用层 (Controllers/Services)
- 协调UI和业务逻辑
- 实现用户操作的处理流程
- 提供API接口供UI层调用

### 3. 核心业务层 (Core)
- 实现业务核心逻辑
- Telegram客户端管理、代理验证等
- 通过接口方式提供功能，便于扩展

### 4. 数据访问层 (Data)
- 使用SQLAlchemy ORM进行数据库操作
- 实现数据的CRUD操作
- 通过仓库模式封装数据访问逻辑

## 扩展接口设计

### 接口示例

```python
# 账户管理接口
class IAccountManager(Protocol):
    async def add_account(self, phone: str, proxy_id: Optional[int] = None) -> int: ...
    async def import_session(self, session_path: str) -> int: ...
    async def delete_account(self, account_id: int) -> bool: ...
    async def verify_account(self, account_id: int) -> bool: ...
    async def update_profile(self, account_id: int, **profile_data) -> bool: ...

# 代理管理接口
class IProxyManager(Protocol):
    async def add_proxy(self, host: str, port: int, username: str = None, password: str = None) -> int: ...
    async def bulk_import(self, proxy_list: List[Dict]) -> List[int]: ...
    async def validate_proxy(self, proxy_id: int) -> bool: ...
    async def delete_proxy(self, proxy_id: int) -> bool: ...
    async def assign_proxy(self, account_id: int, proxy_id: int) -> bool: ...

# 监控管理接口
class IMonitorManager(Protocol):
    async def add_monitor_task(self, group_id: str, keywords: List[str], account_ids: List[int]) -> int: ...
    async def start_monitoring(self, task_id: int) -> bool: ...
    async def stop_monitoring(self, task_id: int) -> bool: ...
    async def get_messages(self, task_id: int, start_time: datetime, end_time: datetime) -> List[Dict]: ...
```

## 扩展性设计

1. **插件系统**：
   - 预留plugins目录，实现插件接口，支持动态加载新功能

2. **事件系统**：
   - 实现事件发布/订阅机制，便于模块间松耦合通信

3. **API扩展**：
   - 所有服务层方法设计为异步API
   - 使用Protocol类型定义接口规范
   - 依赖注入使扩展更灵活

4. **配置模块**：
   - 灵活的配置系统支持新功能的参数设定

此架构设计符合开闭原则，在保持现有功能稳定的同时，方便未来功能扩展。

## 已实现的功能组件

### 1. 公共UI组件

#### 表示层
- **公共UI组件 (ui/common/)**：
  - **消息框组件 (message_box.py)**：统一提示框、确认框和错误提示框的样式和调用方式
  - **Toast消息组件 (toast.py)**：显示临时提示信息，自动消失
  - **状态栏管理器 (status_bar.py)**：管理主窗口状态栏的显示
  - **通知角标组件 (notification.py)**：显示未读消息数等信息
  - **加载指示器组件 (loading.py)**：显示操作进行中的加载动画
  - **统一导出 (__init__.py)**：提供简化的导入方式，使组件使用更便捷

### 2. 代理IP管理功能

#### 数据访问层
- **代理模型 (data/models/proxy.py)**：定义代理IP的数据结构
- **代理数据仓库 (data/repositories/proxy_repo.py)**：实现代理IP数据的增删改查操作

#### 核心业务层
- **代理验证器 (core/proxy/validator.py)**：实现代理IP有效性验证
- **代理连接管理 (core/proxy/connector.py)**：管理代理IP的连接和使用

#### 应用层
- **代理服务 (app/services/proxy_service.py)**：协调数据层和核心层，提供代理管理服务
- **代理控制器 (app/controllers/proxy_controller.py)**：处理UI和服务层之间的交互

#### 表示层
- **代理管理视图 (ui/views/proxy_view.py)**：代理IP管理界面实现
- **代理相关对话框 (ui/dialogs/proxy_dialogs.py)**：添加、编辑代理IP等对话框

### 3. 账户管理功能

#### 数据访问层
- **账户模型 (data/models/account.py)**：定义Telegram账户的数据结构
- **账户数据仓库 (data/repositories/account_repo.py)**：实现账户数据的增删改查操作

#### 核心业务层
- **客户端管理器 (core/telegram/client_manager.py)**：管理多个Telegram客户端实例
- **会话处理器 (core/telegram/session_handler.py)**：处理Telegram会话的创建、导入和验证
- **账户管理器 (core/telegram/account_manager.py)**：提供账户管理的核心功能

#### 应用层
- **账户服务 (app/services/account_service.py)**：协调数据层和核心层，提供账户管理服务
- **账户控制器 (app/controllers/account_controller.py)**：处理UI和服务层之间的交互

#### 表示层
- **账户管理视图 (ui/views/account_view.py)**：账户管理界面实现
- **账户相关对话框 (ui/dialogs/account_dialogs.py)**：添加账户、导入会话、编辑资料等对话框
