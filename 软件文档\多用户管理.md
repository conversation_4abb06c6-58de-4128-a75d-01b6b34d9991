### 技术要求：
-pyside6 QThread telethon 使用pyside6的一个子线程和异步实现管理几百个telegram账户。
### 功能：
- 用户分组：添加用户可选择用户输入哪一个分组。账户管理页可修改账户分组，账户管理页可添加新的分组。
- 添加用户页：可以给每个登录的用户设定指定代理ip进行绑定（每个代理ip后面显示该ip已绑定的次数，优先显示绑定0次的ip），用户手动发送验证码，输入两步验证密码（可为空）进行登录，成功保存用户信息到数据。
- 批量导入session页：可以给每个session设置指定的代理ip击行绑定（每个代理ip后面显示该ip已绑定的次数，优先显示绑定0次的ip）。导入后直接创建客户端，然后自动登录，登录成功提醒用户成功，失败提醒用户失败，成功保存用户信息到数据库。
- 刷新用户信息：从telegram刷新用户的最新资料，更新表格显示
- 删除用户：断开客户端链接，删除数据库用户信息，删除session文件。
- 更改用户资料或分组，更改用户的姓，名，useraname，头像，个人简介。
- 批量更改用户资料或分组：管理页面可选中用户，全部设置可批量设置用户的姓，名，useraname（多个账户可使用随机字符标签为每个账户生成不同的username,如：{随机字符3}，{随机数字3-5}，{随机字母2-3}），头像，个人简介。
- 用户验证：当用户失效后，点击该按钮可快速进入登录页，进行这个账户的重新登录。
- 自动登录：每次打开软件自动读取数据库里保存的用户信息，进行登录
## 设计好的ui界面
ui文件：@account_manage_ui.py @add_account_dialog.py @send_message_manager_ui.py
@account_view.py 


后台工作层 (Worker - QThread + asyncio):
一个或多个 QThread 子类 (TelegramWorker)。
每个 TelegramWorker 内部运行一个独立的 asyncio 事件循环。
所有 Telethon 操作（登录、API 调用、连接管理）都在此线程的 asyncio 循环中执行。
管理 TelegramClient 实例池（每个账户一个）。
通过 信号 将结果（成功、失败、数据、进度）发送回主线程的槽函数。
接收来自主线程的 任务请求（例如，通过 QMetaObject.invokeMethod 或信号触发的槽）。
数据持久化层 (Data Persistence):
负责存储和检索应用程序数据（账户信息、分组、代理设置、session 文件路径等）。
推荐使用 SQLite 数据库，通过 sqlite3 模块或 ORM (如 SQLAlchemy) 访问。
Session 文件 (.session) 由 Telethon 管理，但其路径和关联账户信息需存入数据库。
数据库操作应尽可能在 后台工作线程 中执行，或通过信号/槽机制安全地进行，避免阻塞主线程。
Telegram 交互层 (Telethon Wrapper):
封装 Telethon 的常用操作，提供更简洁的接口给 TelegramWorker。
例如：connect_client, login, get_user_info, update_profile, logout 等异步函数。
所有函数都是 async def 并在 TelegramWorker 的 asyncio 循环中运行。
4. 核心组件设计
MainWindow (基于 account_manage_ui.py)
显示账户列表（使用 QTableView 和自定义模型 AccountTableModel）。
提供按钮：添加账户、批量导入、刷新选中、删除选中、批量操作、管理分组。
包含状态栏显示操作信息。
连接信号和槽，将用户操作分派给逻辑层或直接触发 TelegramWorker 的任务。
AccountTableModel (QAbstractTableModel)
为 QTableView 提供数据。
存储账户数据列表（从数据库加载）。
实现 rowCount, columnCount, data, headerData 等方法。
提供更新数据的方法（如 update_account_data, add_account_row, remove_account_row），这些方法应在主线程中调用（通常由 TelegramWorker 的信号触发）。
包含列：选择框、手机号、姓名、用户名、分组、代理、状态（在线/离线/错误/需要验证）、上次活动时间等。
AddAccountDialog (基于 add_account_dialog.py)
输入字段：API ID, API Hash, 手机号, 验证码, 两步验证密码 (可选)。
下拉框：选择分组（从数据库加载），选择代理 IP（显示已绑定次数，优先 0 次）。
按钮：发送验证码，登录。
状态标签：显示登录过程信息（等待验证码、等待密码、登录中、成功、失败信息）。
交互流程：
用户输入 API/Hash/Phone，选择代理/分组。
点击“发送验证码” -> 主线程通知 TelegramWorker。
TelegramWorker 异步调用 client.send_code_request() -> 成功后发信号回主线程，激活验证码输入框。
用户输入验证码（和密码，如果需要） -> 点击“登录”。
主线程通知 TelegramWorker -> TelegramWorker 异步调用 client.sign_in() 或 client.check_password()。
TelegramWorker 根据结果（成功/失败/需要密码）发信号回主线程更新UI和数据库。
ImportSessionDialog (假设 UI 文件 import_session_dialog.ui)
按钮：选择 Session 文件（多选）。
列表/表格：显示选中的 Session 文件名，并提供下拉框为每个 Session 选择代理 IP（显示绑定次数）。
按钮：开始导入。
进度条/状态显示。
交互流程：
用户选择文件，分配代理。
点击“开始导入” -> 主线程通知 TelegramWorker。
TelegramWorker 异步处理每个 Session：
创建 TelegramClient 实例，传入 Session 文件路径和代理。
调用 client.connect() 和 client.is_user_authorized() 或 client.get_me()。
成功 -> 保存账户信息（尽可能提取手机号或用户ID）、分组（默认或指定）、代理到数据库。发信号更新UI（成功计数）。
失败 -> 记录失败原因。发信号更新UI（失败计数）。
TelegramWorker 发送进度信号和完成信号。
TelegramWorker (QThread)
run() 方法:
创建并设置 asyncio 事件循环 (self.loop = asyncio.new_event_loop(); asyncio.set_event_loop(self.loop)).
运行事件循环直到停止 (self.loop.run_forever()).
在退出前清理资源 (self.loop.run_until_complete(self.shutdown()), self.loop.close()).
asyncio 任务管理:
维护一个 dict 存储活跃的 TelegramClient 实例，键可以是手机号或数据库账户 ID (self.clients = {})。
提供启动/停止 asyncio 循环的方法，由主线程控制。
使用 asyncio.run_coroutine_threadsafe(coro, self.loop) 从主线程或其他线程向此 QThread 的 asyncio 循环提交协程任务。
信号 (Signals): 定义各种信号将结果传回主线程。
account_login_success(account_id, user_data)
account_login_failed(phone_or_id, error_message)
code_required(phone_or_id)
password_required(phone_or_id)
account_info_updated(account_id, updated_data)
account_update_failed(account_id, error_message)
account_deleted(account_id)
proxy_usage_updated(proxy_ip, count)
operation_progress(current, total, message)
operation_finished(summary)
account_status_changed(account_id, status) # e.g., 'Online', 'Offline', 'Need Auth'
槽 (Slots) 或方法: 接收主线程的任务请求。这些方法内部使用 asyncio.run_coroutine_threadsafe 来调度实际的异步工作。
start_login(account_details)
submit_code(account_id, code)
submit_password(account_id, password)
import_sessions(session_proxy_list)
refresh_accounts(account_ids)
delete_accounts(account_ids)
update_accounts_profile(account_ids, profile_data)
update_accounts_group(account_ids, group_id)
initialize_clients(all_account_data) # 用于程序启动时自动登录
DatabaseManager
提供 CRUD 接口：
add_account(details)
get_account(account_id or phone)
get_all_accounts()
update_account(account_id, data)
delete_account(account_id)
add_group(name)
get_group(group_id or name)
get_all_groups()
update_group(group_id, name)
delete_group(group_id)
get_proxies() # 返回代理列表及绑定计数
update_proxy_binding(proxy_ip, increment) # 增加或减少绑定计数
get_accounts_by_group(group_id)
get_active_accounts() # 返回状态为 'Online' 或已成功登录的账户
确保线程安全：如果 TelegramWorker 和主线程都可能访问数据库，需要使用线程安全的连接池或将所有数据库操作委托给单一线程（例如 TelegramWorker 或一个专门的数据库线程）。简单的做法是在 TelegramWorker 中执行所有数据库写操作，主线程只读或通过信号请求写。
5. 功能实现细节
用户分组:
数据库 groups 表 (id, name)。accounts 表有 group_id 外键。
添加/编辑账户对话框：下拉列表加载 get_all_groups() 数据。
账户管理页：允许直接修改表格中的分组（使用 QComboBoxDelegate），或提供专门的“管理分组”对话框（增删改查分组）。修改后更新数据库。
添加用户:
代理选择：DatabaseManager.get_proxies() 获取代理及其绑定计数。UI 对列表排序，优先显示计数为 0 的。
登录流程：如 AddAccountDialog 交互流程所述，涉及主线程与 TelegramWorker 的多次信号/槽通信。
成功后：TelegramWorker 保存 Telethon session，更新数据库账户信息（包括绑定的代理 IP），并调用 DatabaseManager.update_proxy_binding(proxy_ip, 1) 增加代理计数。
批量导入 Session:
代理选择：同上。
TelegramWorker 循环处理：对每个 session 创建 client -> connect -> get_me() -> 成功则保存信息（用户 ID，尝试获取 phone，绑定的代理）到数据库，更新代理计数。
失败处理：记录失败原因，可能标记账户状态为“导入失败”。
刷新用户信息:
用户选中行，点击“刷新”。
主线程将选中的 account_ids 发送给 TelegramWorker。
TelegramWorker 异步调用对应 client 的 get_me() 或相关方法，获取最新信息。
成功后，发送 account_info_updated 信号，携带 account_id 和新数据。
主线程的槽函数接收信号，更新 AccountTableModel，从而刷新 QTableView。
删除用户:
用户选中，点击“删除”。确认对话框。
主线程将 account_ids 发送给 TelegramWorker。
TelegramWorker 异步执行：
调用 client.log_out() (可选，会使 session 失效) 或仅 client.disconnect().
从 self.clients 字典移除 client 实例。
删除对应的 .session 文件。
从数据库删除账户记录。
如果账户绑定了代理，调用 DatabaseManager.update_proxy_binding(proxy_ip, -1) 减少计数。
发送 account_deleted 信号。
主线程接收信号，从 AccountTableModel 移除对应行。
更改用户资料/分组 (单个):
通过编辑对话框或表格内编辑实现。
主线程将 account_id 和更改的数据（姓、名、用户名、头像路径、简介、新 group_id）发送给 TelegramWorker。
TelegramWorker 异步调用 Telethon 的 UpdateProfileRequest, UploadProfilePhotoRequest 等。
成功后，更新数据库，发送 account_info_updated 信号。
主线程更新模型和视图。
批量更改用户资料/分组:
提供批量操作对话框，允许用户输入模板。
随机字符标签解析：在 主线程 或 逻辑层 解析 {随机字符3}, {随机数字3-5}, {随机字母2-3} 等标签。为 每个 选中的账户生成 不同 的随机值。
主线程将 account_ids 和一个包含每个账户对应生成数据的列表/字典发送给 TelegramWorker。
TelegramWorker 异步循环处理每个账户的更新请求。
报告进度和最终结果。
用户验证 (Re-authentication):
当 AccountTableModel 中的账户状态为“需要验证”或“登录失败”时，启用“验证”按钮。
点击按钮 -> 打开 AddAccountDialog，预填充该账户的 API ID/Hash, Phone, 代理（如果之前有）。
后续流程同“添加用户”的登录部分。成功后更新数据库记录和状态。
自动登录:
程序启动时，MainWindow 从 DatabaseManager.get_all_accounts() 获取所有账户信息。
将账户数据列表传递给 TelegramWorker 的 initialize_clients 方法。
TelegramWorker 异步为每个账户创建 TelegramClient，使用存储的 Session 和代理进行连接。
调用 client.connect() 和 client.is_user_authorized() 或 client.get_me() 验证登录状态。
根据结果，发送 account_status_changed 信号更新每个账户在 AccountTableModel 中的状态。

. API / 接口 (供其他模块或未来扩展使用)
为了方便未来扩展（如集成发送消息模块、统计模块等），可以提供以下接口（通常通过主应用实例或专门的服务类访问）：
get_all_groups(): 返回所有分组列表 [(id, name), ...].
get_accounts(filter_criteria):
get_accounts(group_id=X): 返回指定分组的所有账户对象或 ID 列表。
get_accounts(status='Online'): 返回所有当前状态为“在线”的账户。
get_accounts(is_valid=True): 返回所有已成功登录且未标记为失效的账户。
返回账户数据列表（包含 ID, phone, user_info 等）。
get_logged_in_clients(): (可能由 TelegramWorker 提供，需线程安全访问) 返回当前已成功连接的 TelegramClient 实例字典 {account_id: client_instance}。 注意： 直接暴露 client 实例需要非常小心，最好提供封装后的方法。
request_telegram_action(account_id, action_type, **params): 一个通用的请求接口，将任务提交给 TelegramWorker。例如 request_telegram_action(123, 'send_message', target='@username', text='Hello')。 Worker 需要解析 action_type 并调用相应的 Telethon 方法。
get_proxy_info(proxy_id): 获取指定代理的详细信息。
get_all_proxies_with_usage(): 返回所有代理及其当前绑定数量。
这些接口应设计为从主线程调用，内部可能会与 TelegramWorker 或 DatabaseManager 交互。

. 错误处理与日志记录
Telethon 异常: 在 TelegramWorker 的异步任务中捕获 Telethon 可能抛出的各种异常（AuthKeyError, UserDeactivatedError, RpcError, ConnectionError 等）。
UI 反馈: 将捕获到的错误信息通过信号发送回主线程，在 UI 上清晰地展示给用户（例如，在状态栏、账户状态列或弹出消息框）。
日志记录: 使用 Python 的 logging 模块记录详细的操作日志，包括成功、失败、错误堆栈信息，方便调试。可以配置日志记录到文件。
状态管理: 账户状态 (status 列) 需要准确反映账户的真实情况（在线、离线、连接中、授权失败、需要密码等）。