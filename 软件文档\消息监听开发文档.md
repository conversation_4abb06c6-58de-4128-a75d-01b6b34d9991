使用telethon + pyside6 +异步+已经开发好的多账户管理，完成消息监听功能。

# 1. 功能需求

## 1.1 关键词监控
- 用户发送的消息里包含设定的关键词时，记录该用户信息
- 支持多关键词同时监控，可以管理关键词列表
- 关键词匹配支持精确匹配和模糊匹配
- 记录关键词匹配的上下文信息（消息内容、发送时间、群组信息）

## 1.2 用户监听
- 监听群组中新加入的用户，记录用户信息
- 监听群组中发送消息的用户，记录用户信息和消息内容
- 对监听到的用户进行分类和标记，便于后续管理

## 1.3 通用筛选功能
- 关键词筛选：当消息中包含设定的忽略关键词时，忽略该用户
- 昵称筛选：当用户昵称中包含设定的忽略昵称关键词时，忽略该用户
- 支持添加、编辑和删除筛选规则
- 筛选规则可应用于特定监控任务或全局配置

## 1.4 监听群组选择
- 可选中用户分组，根据不同分组下的用户选择监听的群组
- 支持同时监听多个用户的群组
- 可根据群组类型、活跃度等条件进行筛选
- 提供群组预览功能，展示群组的基本信息

## 1.5 消息通知
- 将监听到的用户信息通过多种渠道进行通知：
  - 私聊通知：将信息发送给指定Telegram用户
  - 群组通知：将信息发送到指定Telegram群组
  - 预留邮件通知接口
  - 预留webhook接口，支持自定义通知方式
- 支持通知模板自定义，可配置通知内容格式
- 支持多种通知触发条件设置

## 1.6 消息群发引用
- 消息群发功能可以选择采集任务名，直接向任务名内采集到的用户发送消息
- 支持批量导入采集到的用户列表
- 支持定时群发和立即群发
- 提供群发任务管理，包括历史记录查看和统计分析

# 2. 系统架构设计

## 2.1 分层架构
按照项目现有的分层架构，完成消息监听功能的各层次实现：

### 2.1.1 表示层（UI）
- **监控视图** (ui/views/monitor_view.py)
  - 监控任务管理界面
  - 用户数据展示界面
  - 通知配置界面
  - 监控统计与分析界面

- **对话框组件** (ui/dialogs/)
  - 添加监控任务对话框
  - 群组选择对话框
  - 用户详情对话框
  - 通知配置对话框

### 2.1.2 应用层（Controllers/Services）
- **监控控制器** (app/controllers/monitor_controller.py)
  - 处理UI事件与业务逻辑的桥梁
  - 任务管理相关方法
  - 消息过滤逻辑
  - 通知分发控制

- **监控服务** (app/services/monitor_service.py)
  - 核心业务逻辑实现
  - 监控任务启动、停止、管理
  - 消息处理与过滤
  - 数据统计与分析

### 2.1.3 核心业务层（Core）
- **消息过滤器** (core/monitor/filter.py)
  - 关键词过滤实现
  - 用户昵称过滤实现
  - 复合过滤条件处理

- **关键词提取器** (core/monitor/extractor.py)
  - 文本关键词提取算法
  - 关键词匹配优化
  - 上下文关联分析

- **通知发送器** (core/monitor/notifier.py)
  - 支持多种通知渠道
  - 通知模板处理
  - 通知失败重试机制

### 2.1.4 数据访问层（Data）
- **监控数据仓库** (data/repositories/monitor_repo.py)
  - 任务数据CRUD操作
  - 监控消息数据存取
  - 用户数据管理
  - 查询优化与缓存

- **数据模型** (data/models/monitor.py)
  - 监控任务模型
  - 监控消息模型
  - 用户数据模型
  - 通知配置模型

## 2.2 关键流程设计

### 2.2.1 消息监听流程
1. 用户在UI中创建监控任务，选择目标群组和监控规则
2. Controller接收请求，调用Service创建任务并保存到数据库
3. 启动监控时，Service通过ClientManager获取Telegram客户端
4. 为每个客户端注册消息事件处理器，监听选定群组的消息
5. 收到消息时，应用过滤规则和关键词匹配
6. 匹配成功的消息保存到数据库，并触发通知发送

### 2.2.2 用户入群监听流程
1. 为客户端注册群组事件处理器，监听用户加入事件
2. 捕获新用户加入事件，获取用户信息
3. 应用昵称过滤规则，决定是否记录该用户
4. 将符合条件的用户保存到数据库，并触发通知

### 2.2.3 通知发送流程
1. 收到匹配的消息或用户加入事件后，创建通知任务
2. 从数据库加载通知配置和通知模板
3. 根据通知类型选择对应的发送方式
4. 格式化通知内容，使用选定的Telegram账户发送通知
5. 记录通知发送结果，失败时尝试重试

# 3. 数据库设计

## 3.1 监控任务表 (monitor_tasks)
- id: 主键
- name: 任务名称
- description: 任务描述
- group_id: 群组ID
- group_title: 群组标题
- keywords: 关键词列表（JSON格式）
- ignore_keywords: 忽略关键词列表（JSON格式）
- ignore_nicknames: 忽略昵称列表（JSON格式）
- monitor_new_users: 是否监控新用户
- monitor_messages: 是否监控消息
- is_active: 是否启用
- is_running: 是否正在运行
- created_at: 创建时间
- updated_at: 更新时间

## 3.2 监控消息表 (monitor_messages)
- id: 主键
- task_id: 所属任务ID（外键）
- message_id: Telegram消息ID
- chat_id: 群组ID
- user_id: 用户ID
- username: 用户名
- first_name: 用户名字
- last_name: 用户姓
- text: 消息内容
- matched_keywords: 匹配的关键词（JSON格式）
- message_type: 消息类型（文本、媒体等）
- timestamp: 消息时间戳
- collected_at: 采集时间

## 3.3 通知配置表 (notification_configs)
- id: 主键
- task_id: 所属任务ID（外键）
- target_type: 通知目标类型（private_chat/group_chat/email/webhook）
- target_address: 通知目标地址
- account_id: 发送通知使用的账户ID
- template: 通知模板
- is_active: 是否启用
- created_at: 创建时间
- updated_at: 更新时间

# 4. 技术实现方案

## 4.1 消息监听实现
- 使用Telethon的事件处理机制监听消息
- 对每个监控任务，为相应的客户端动态添加事件处理器
- 使用asyncio确保事件处理非阻塞

```python
# 添加消息事件处理器示例
@client.on(events.NewMessage(chats=int(task_data["group_id"])))
async def message_handler(event):
    # 处理消息
    message = event.message
    sender = await event.get_sender()
    
    # 应用筛选规则和关键词匹配
    if await message_filter.should_ignore(sender, message.text):
        return
        
    matched_keywords = await keyword_extractor.extract(message.text, task_data["keywords"])
    if matched_keywords:
        # 记录消息并发送通知
        await record_and_notify(task_id, event, sender, matched_keywords)
```

## 4.2 用户入群监听实现
- 使用Telethon的ChatAction事件监听用户加入
- 为每个监控任务注册独立的事件处理器

```python
# 添加用户加入事件处理器示例
@client.on(events.ChatAction(chats=int(task_data["group_id"])))
async def user_joined_handler(event):
    if event.user_joined or event.user_added:
        # 获取用户信息
        user = await event.get_user()
        
        # 应用昵称筛选规则
        if await message_filter.should_ignore_user(user):
            return
            
        # 记录用户并发送通知
        await record_and_notify_user_joined(task_id, event, user)
```

## 4.3 优化考虑
- 使用缓存减少数据库查询压力
- 实现消息队列处理通知，避免通知发送阻塞消息处理
- 批量处理数据库操作，提高性能
- 实现优雅的异常处理和自动重连机制
- 添加监控任务的限流和资源控制，防止过载

# 5. UI设计
根据现有UI框架，设计监控模块的界面，包括：

## 5.1 监控任务管理界面
- 任务列表展示，支持搜索、排序和筛选
- 任务详情查看，包括监控状态、统计信息
- 任务操作按钮：添加、编辑、删除、启动、停止
- 任务状态实时更新指示器

## 5.2 监控数据展示界面
- 监控数据表格，支持分页和搜索
- 用户信息详细展示
- 消息内容查看，包含关键词高亮
- 数据导出功能

## 5.3 通知配置界面
- 通知目标管理
- 通知模板编辑
- 通知测试功能
- 通知历史记录查看

# 6. 测试计划
- 单元测试：测试各组件的独立功能
- 集成测试：测试组件间的交互
- 性能测试：测试系统在高负载下的表现
- 可靠性测试：测试长时间运行的稳定性
- 用户界面测试：测试UI的易用性和响应性

# 7. 部署方案
- 支持配置文件的灵活配置
- 提供详细的安装和配置说明
- 实现日志记录和错误报告
- 支持数据备份和恢复机制

# 8. 后续扩展
- 支持更复杂的过滤规则，如正则表达式匹配
- 实现机器学习模型进行智能关键词提取和用户分析
- 提供API接口供外部系统集成
- 增加更多通知渠道，如短信、微信等