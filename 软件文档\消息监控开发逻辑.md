使用telethon + pyside6 +异步+已经开发好的多账户管理，完成消息监听功能。
# 关键词监控
    用户发送的消息里包含设定的关键词，就记录该用户

# 用户监听
    群组入群用户，发送消息的用户，都记录。

## 通用筛选功能：
    关键词筛选：当关键词里包含了设定的关键词，忽略用户
    昵称筛选：当昵称里包含了设定的昵称，忽略用户

# 3监听群组选择
    可选中用户分组，根据不同分组下的用户选择监听的群组
    同时支持监听多个用户的群组
# 4 消息通知 
    把监听到的用户，通过私聊或发送到群里的方式，发送过去。
    该功能预留下接口，后续可能会使用邮件通知，或者机器人通知。
    需要支持多个通知类型，同意消息发送到多个对象。
# 5消息群发引用
    消息群发功能可以选择采集任务名，直接向任务名内采集到的用户发送消息。
采用分层架构，帮我完善好这些功能的设计方案，服务层文件和功能和已有的文件相同，

现有架构基础上，增加强大的消息监听能力，包括关键词监控、用户行为监控（入群、发言），并结合灵活的筛选和可扩展的通知机制。
核心功能分解:
事件捕获: 利用 Telethon 的事件处理器捕获指定群组的 NewMessage (新消息) 和 ChatAction (成员变动，如入群) 事件。
规则匹配: 对捕获的事件应用监控规则（关键词、用户发言、用户入群）。
过滤处理: 对匹配规则的用户/消息应用忽略规则（忽略关键词、忽略昵称）。
数据记录: 将通过过滤的、被监控到的用户/事件信息持久化到数据库。
通知发送: 将监控到的信息通过配置的通知渠道（私聊、群组，预留邮件/Webhook等）发送给指定目标。
配置管理: 通过 UI 提供界面，让用户配置监控关键词、忽略规则、选择监听的账户和群组、配置通知目标等。
架构设计整合:
监听逻辑主要在 TelethonWorker (运行于 QThread 的异步环境) 中实现，UI 负责配置和展示，数据库负责存储配置和结果。
1. 新增/修改的组件:
MonitoringManager (在 TelethonWorker 内部或作为协作类):
负责根据配置启动/停止对特定账户下特定群组的监听。
动态地向 TelegramClient 实例添加/移除针对特定 chat_id 的事件处理器 (events.NewMessage, events.ChatAction)。 这是关键，避免全局监听所有消息。
接收来自事件处理器的原始事件，并传递给规则匹配和过滤引擎。
协调数据记录和通知发送。
FilteringEngine (可作为 MonitoringManager 的一部分或单独模块):
包含应用“忽略关键词”和“忽略昵称”规则的逻辑。
接收用户昵称、消息文本等信息，返回是否应被忽略。
NotificationService (可作为 MonitoringManager 的一部分或单独模块):
负责将格式化后的监控结果发送到所有配置的通知目标。
包含不同通知渠道（Telegram 消息、邮件、Webhook 等）的发送逻辑。
设计必须是可扩展的，方便未来添加新的通知类型。
UI (PySide6 - MainWindow 或专用配置窗口/标签页):
配置区:
输入监控关键词（列表）。
输入忽略关键词（列表）。
输入忽略昵称（列表，支持部分匹配）。
账户和群组选择:
列表显示所有已添加的 Telethon 账户。
允许多选账户。
当选择账户后，动态加载这些账户加入的群组列表（需要Worker通过Telethon查询）。
提供树状或列表视图，让用户勾选要监听的具体群组（按账户分组）。
通知目标配置:
添加/删除通知目标。
选择通知类型（Telegram 私聊/群聊 ID，未来可扩展 Email 地址、Webhook URL 等）。
输入目标 ID 或地址。
控制区:
“开始监听” / “停止监听” 按钮。
显示区 (可选):
实时显示捕获到的、未被过滤的用户/事件日志。
数据库模型 (SQLAlchemy - 在 database/models.py 中添加):
MonitoringConfig: (可能是一张或多张表) 存储监控关键词、忽略关键词、忽略昵称。
MonitoredGroup: 存储被选择监听的群组信息。
id (PK)
managed_account_id (FK, 关联到现有的 Account 表的 id)
group_id (BigInteger, Telegram 群组的数字 ID)
group_name (String, 群组名称，方便显示)
is_active (Boolean, 是否启用对此群组的监听)
NotificationTarget: 存储通知目标配置。
id (PK)
target_type (String, e.g., 'telegram_chat', 'telegram_group', 'email', 'webhook')
target_address (String, 对应类型的 ID、地址或 URL)
description (String, 可选描述)
is_active (Boolean)
MonitoredEventLog: 记录被捕获并触发通知的用户事件。
id (PK)
timestamp (DateTime, 事件发生时间)
managed_account_id (FK, 哪个管理账户监听到的)
group_id (BigInteger, 哪个群组发生的)
group_name (String)
captured_user_id (BigInteger, 被捕获的用户 ID)
captured_user_info (String, 用户当时的昵称/用户名)
triggering_reason (String, e.g., 'keyword_match', 'user_join', 'user_message')
triggering_content (Text, 可选，如匹配的关键词或消息内容摘要)
message_id (BigInteger, 可选，关联的消息 ID)
notified (Boolean, 标记是否已发送通知，防止重复)
详细流程设计:
1. 配置流程 (UI -> Worker -> DB):
用户在 UI 配置关键词、忽略规则、通知目标。
UI 发送信号到 TelethonWorker 的槽函数 (例如 update_monitoring_config, add_notification_target)。
Worker 槽函数将配置写入数据库 (MonitoringConfig, NotificationTarget)。
用户在 UI 选择一个或多个“管理账户”。
UI 发送信号请求这些账户的群组列表 (request_groups_for_accounts)。
Worker 接收信号，对于每个选中的、已登录的账户，调用 client.get_dialogs() 过滤出群组，并将结果（group_id, group_name）通过信号发送回 UI。
UI 显示群组列表，用户勾选要监听的群组。
UI 发送信号 (update_monitored_groups)，包含选中的 (managed_account_id, group_id, group_name) 列表。
Worker 接收信号，更新数据库中的 MonitoredGroup 表（标记 is_active）。
2. 启动监听流程 (UI -> Worker):
用户点击“开始监听”。
UI 发送信号 (start_monitoring) 到 TelethonWorker。
Worker (MonitoringManager):
从数据库加载所有 is_active=True 的 MonitoredGroup 记录。
从数据库加载监控关键词、忽略规则。
按 managed_account_id 分组。
对于每个需要监听的管理账户 (managed_account_id):
获取对应的 TelegramClient 实例 (确保已登录并运行)。
获取该账户下所有需要监听的 group_id 列表。
关键: 动态添加事件处理器：
更新内部状态为“监听中”。
通过信号更新 UI 状态。
3. 事件处理流程 (Telethon -> Worker -> Filtering -> Recording -> Notification):
_process_new_message(account_id, event): (在 Worker 的 asyncio 循环中运行)
获取消息发送者信息 (user_id, first_name, last_name, username)。
获取消息文本 (event.message.text)。
获取群组 ID (event.chat_id)。
调用 FilteringEngine:
检查用户昵称是否匹配“忽略昵称”列表。如果匹配，return (忽略)。
检查消息文本是否包含“忽略关键词”列表中的任何词。如果匹配，return (忽略)。
应用监控规则:
关键词监控: 检查消息文本是否包含“监控关键词”列表中的任何词。如果匹配：
调用 _record_and_notify(account_id, event.chat_id, user_id, user_info, 'keyword_match', matched_keyword)
用户发言监控: (如果配置了此规则)
调用 _record_and_notify(account_id, event.chat_id, user_id, user_info, 'user_message', event.message.text[:50]) # 记录消息摘要
_process_chat_action(account_id, event): (在 Worker 的 asyncio 循环中运行)
检查事件是否是用户加入群组 (event.user_joined 或 event.user_added)。
如果是，获取加入用户的用户信息 (event.user_id, user_info)。
获取群组 ID (event.chat_id)。
调用 FilteringEngine:
检查用户昵称是否匹配“忽略昵称”列表。如果匹配，return。
应用监控规则 (用户入群监控):
调用 _record_and_notify(account_id, event.chat_id, user_id, user_info, 'user_join', None)
_record_and_notify(account_id, group_id, user_id, user_info, reason, content):
(可选) 防抖/去重: 检查 MonitoredEventLog 数据库，看短时间内（例如过去 5 分钟）是否已记录过同一个用户在同一个群组因为相同原因的事件。如果找到，可以选择不记录或不通知。
记录数据库: 创建 MonitoredEventLog 记录并保存。标记 notified=False。
触发通知: 调用 NotificationService.send_notification(log_entry)，传递刚创建的日志条目。
(通知发送成功后) 更新记录: 将 MonitoredEventLog 中的 notified 标记为 True。
4. 通知发送流程 (NotificationService):
send_notification(log_entry):
从数据库加载所有 is_active=True 的 NotificationTarget。
格式化消息: 根据 log_entry 的内容（哪个群、哪个用户、什么原因、内容摘要）生成用户友好的通知文本。
遍历通知目标:
对于每个 target:
根据 target.target_type 选择发送方式：
'telegram_chat' / 'telegram_group':
选择发送账户: 需要策略决定用哪个已登录的管理账户发送通知（例如，轮询、固定账户、或与监听到事件的账户相同？建议配置一个专门用于发送通知的账户）。
获取发送账户的 TelegramClient。
调用 client.send_message(target.target_address, formatted_message)。使用 try...except 处理发送错误。
'email': (未来实现)
使用 smtplib 或相关库发送邮件。
'webhook': (未来实现)
使用 aiohttp 或 requests 发送 POST 请求到 target.target_address。
调度异步发送任务 (asyncio.create_task)。
5. 停止监听流程 (UI -> Worker):
用户点击“停止监听”。
UI 发送信号 (stop_monitoring) 到 TelethonWorker。
Worker (MonitoringManager):
对于之前添加过处理器的所有 TelegramClient 实例：
调用 client.remove_event_handler(self._message_handler_callback) 和 client.remove_event_handler(self._action_handler_callback) 移除之前添加的回调。
注意: 如果是使用 lambda 直接定义的，需要保存 lambda 的引用才能移除，或者移除所有该类型的处理器（可能影响其他功能）。使用成员变量保存回调函数引用是更好的做法。
更新内部状态为“已停止”。
通过信号更新 UI 状态。
技术实现要点:
动态事件处理器: 核心在于根据用户选择动态添加和移除针对特定 chat_id 的处理器，而不是接收所有消息再过滤。这极大提高了效率。
线程安全: UI 和 Worker 之间的通信严格使用信号和槽。Worker 内部的操作（访问共享数据如配置、调用 Telethon 方法）都在其自身的 asyncio 事件循环中进行，避免了多线程直接访问的问题。数据库访问可以通过 SQLAlchemy 的会话管理来处理线程问题（如为每个操作创建新会话或使用线程安全的会话工厂）。
并发: Worker 内部的 asyncio 事件循环天然支持并发处理多个账户的事件和通知发送。
错误处理: 在 Telethon 调用（连接、获取信息、发送消息）、数据库操作、通知发送等环节都需要健壮的错误处理和日志记录。
资源管理: 确保在停止监听或程序退出时，所有事件处理器被移除，客户端正确断开连接。
扩展性考虑:
新通知类型: 在 NotificationService 中添加新的 elif target.target_type == 'new_type': 分支和对应的发送逻辑即可。UI 中添加新的类型选项。
新过滤规则: 修改 FilteringEngine 的逻辑。
新监控规则: 在 _process_new_message 或 _process_chat_action 中添加新的检查逻辑。
更复杂的配置: 如果配置项变得非常多，可以考虑将配置存储拆分成更多表，并在 UI 中使用更复杂的控件（如表格、树形视图）来管理。
待办/注意事项:
性能: 如果监听大量群组或消息量极大的群组，需要关注 Worker 线程的 CPU 和内存占用，以及数据库写入性能。对 MonitoredEventLog 的查询（用于去重）需要索引优化。
Telegram API 限制: 大量并发操作（尤其是频繁的 get_dialogs 或添加/移除处理器）可能触及 API 限制，需要加入适当的延迟或重试逻辑。
获取用户信息: event.get_sender() / event.get_user() 是异步操作，如果在事件处理器中频繁调用，可能会增加延迟。可以考虑缓存用户信息。
群组 ID vs Username: Telethon 事件通常使用数字 ID。如果用户输入的是群组用户名，需要在添加监控时将其解析为数字 ID (client.get_entity(username))。
掉线与重连: 需要确保 TelethonWorker 中的账户管理器能处理客户端意外掉线后的自动重连，并在重连成功后重新附加事件处理器。