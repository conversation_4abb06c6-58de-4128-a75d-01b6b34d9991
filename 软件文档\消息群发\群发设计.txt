群发任务管理页：
任务名
消息内容：每个任务支持多条消息随机发送
	消息可能包含telegram支持的html标签，而不是单纯的纯文本。
	消息内容如果是图片或图文，从img标签里读取图片进行上传。
账户：
	用户可以选中多个用来进行消息发送的账户
	可以考虑公用已经有的账户模型，要  @account.py  要记录每个用户每日运行发送的消息数量，最后一次发送的消息数量，当日已发送消息数量
	达到数量后，该账户当日就不可再发送消息
	这个限制在后面消息邀请人入群也有用到，所以要公用一个模型

发送对象：分为用户和群组
用户：
	自定义用户：
		用户输入多个用户名
	监听任务读取用户：
		把任务的相关用户导入到该群发任务里
数据设计，因为要统计每个用户是否已发送，发送用户可能几千上万人，这里考虑设计为一个单独的表，
每次保存任务把该任务需要发送的用户放到表里，方便处理


发送消息间隔：每次发送消息的间隔时间，不区分账户
账户间隔：账户上一次发送时间和下一次发送之间的间隔时间。
账户总数：每日每个账户允许发送的最大次数，达到次数后不能再发送。对所有任务生效

	
消息群发任务管理：
显示人任务的发送状态，名称，类型，目标数量，已发送数量，创建日期，增删改操作。

显示所有需要发送消息的任务数量，运行中的数量，已完成的，失败的，暂停的，今日发送了多少条消息



限制群发次数和时间方案
数据库记录最后消息发送时间，记录发送次数，记录允许的总次数

发送消息时判断，如果记录时间是昨天，把时间改为今天且发送次数改为1
如果记录时间是今天，更新发信时间且发信时间加1，且发送次数不小于允许的总次数
如果>=允许的总次数，该账户今天就不允许发送消息。


ui文件参考： @add_msg_task_view.py  @send_msg_view.py 
根据功能需求，设计模型，注意要分层架构。