## 目录结构

```
/
├── .env                    # 环境变量配置文件
├── .env.example            # 环境变量示例文件
├── config.py               # 配置管理模块
├── main.py                 # 主程序入口
├── requirements.txt        # 依赖包列表
├── update.tmp              # 版本更新配置文件
├── app/                    # 应用层
│   ├── __init__.py
│   ├── controllers/        # 控制器
│   │   ├── __init__.py
│   │   ├── account_controller.py    # 账户管理控制器
│   │   ├── proxy_controller.py      # 代理IP控制器
│   │   ├── add_task_controller.py   # 添加监控任务控制器
│   │   ├── base_controller.py      # 控制器基类
│   │   ├── import_controller.py    # 导入功能控制器
│   │   ├── main_controller.py      # 主界面控制器
│   │   ├── mass_mailing_controller.py  # 群发功能控制器
│   │   ├── task_controller.py      # 通用任务管理控制器
│   │   └── telegram_monitor_controller.py  # 监控任务管理控制器
│   └── services/           # 服务层
│       ├── __init__.py
│       ├── account_service.py       # 账户服务
│       ├── proxy_service.py         # 代理IP服务
│       ├── import_service.py        # 导入服务
│       ├── mass_mailing_service.py  # 群发服务
│       ├── task_service.py          # 通用任务管理服务
│       └── monitor_service.py       # 监控服务
├── core/                   # 核心业务层
│   ├── __init__.py
│   ├── telegram/           # Telegram功能核心
│   │   ├── __init__.py
│   │   ├── client_manager.py        # 客户端管理器
│   │   ├── session_handler.py       # 会话处理
│   │   └── message_handler.py       # 消息处理
│   ├── proxy/              # 代理IP核心功能
│   │   ├── __init__.py
│   │   ├── proxy_task_manager.py    # 代理任务管理器
│   │   ├── proxy_config_manager.py  # 3proxy配置文件管理器，负责配置文件生成、增量更新、删除等
│   │   ├── proxy_3proxy_manager.py  # 3proxy服务管理器，负责服务的启动、停止、重启、状态检测等
│   │   └── proxy_validator.py       # 代理IP验证器，负责单个和批量代理IP的验证
│   ├── task_manager/       # 通用任务管理框架
│   │   ├── __init__.py              # 任务管理器包初始化
│   │   ├── task_base.py             # 任务基类
│   │   ├── task_manager.py          # 任务管理器
│   │   └── tasks/                   # 具体任务实现
│   │       ├── __init__.py          # 任务包初始化
│   │       └── message_sending_task.py  # 消息群发任务
│   └── monitor/            # 监控核心功能
│       ├── __init__.py
│       ├── filter.py                # 消息过滤器
│       ├── extractor.py             # 关键词提取
│       └── notifier.py              # 通知管理器 (包含Notifier和NotificationManager类)
├── data/                   # 数据访问层
│   ├── __init__.py
│   ├── database.py                  # 数据库连接
│   ├── repositories/       # 仓库模式
│   │   ├── __init__.py
│   │   ├── account_repo.py          # 账户数据仓库
│   │   ├── proxy_repo.py            # 代理IP数据仓库
│   │   ├── monitor_repo.py          # 监控数据仓库
│   │   ├── base_repo.py             # 仓库基类
│   │   └── mass_mailing_repo.py     # 群发仓库
│   ├── models/             # 数据模型
│   │   ├── __init__.py
│   │   ├── account.py               # 账户模型
│   │   ├── proxy.py                 # 代理IP模型
│   │   ├── monitor.py               # 监控任务模型
│   │   ├── base.py                 # 模型基类
│   │   └── mass_mailing.py         # 群发模型
│   └── migrations/         # 数据库迁移脚本
├── ui/                     # 表示层
│   ├── __init__.py
│   ├── main_window.py               # 主窗口
│   ├── common/             # 公共UI组件
│   │   ├── __init__.py              # 统一导出组件
│   │   ├── message_box.py           # 消息框组件
│   │   ├── toast.py                 # Toast消息组件
│   │   ├── status_bar.py            # 状态栏管理器
│   │   ├── notification.py          # 通知角标组件
│   │   └── loading.py               # 加载指示器组件
│   ├── designer/           # UI设计文件
│   │   ├── __init__.py
│   │   ├── proxy_manager_ui.py      # 代理管理器UI
│   │   ├── msg_monitor_ui.py        # 监控页面设计
│   │   └── add_monitor_task_dialog.py  # 添加监控任务对话框设计
│   ├── dialogs/            # 子窗口
│   │   ├── __init__.py
│   │   ├── message_detail_dialog.py # 消息详情对话框
│   │   ├── group_select_dialog.py   # 分组选择对话框
│   │   └── add_monitor_task_dialog.py  # 添加监控任务对话框
│   ├── resources/          # UI资源文件
│   └── views/              # 视图组件
│       ├── __init__.py
│       ├── account_view.py          # 账户管理视图
│       ├── proxy_view.py            # 代理管理视图
│       ├── monitor_view.py          # 监控视图
│       ├── add_monitor_view.py      # 添加监控任务视图
│       ├── base_view.py            # 视图基类
│       ├── import_sessions_view.py # 导入会话视图
│       ├── main_view.py           # 主视图
│       ├── mass_mailing_view.py    # 群发视图
│       └── task_manager_view.py    # 任务管理视图
├── utils/                  # 工具类
│   ├── __init__.py
│   ├── logger.py                    # 日志管理
│   ├── logger_examples.py           # 日志使用示例
│   ├── thread_pool.py               # 线程池管理
│   ├── update_checker.py            # 版本更新检查工具
│   └── ntp_time.py                  # 网络时间获取工具
├── 3proxy/                 # 3proxy代理服务器
│   ├── bin/                # 3proxy二进制文件
│   │   └── 3proxy.exe               # 3proxy可执行文件
│   └── cfg/                # 3proxy配置文件
└── tests/                  # 测试目录
    ├── __init__.py
    ├── test_logger.py                # 日志模块测试
    ├── test_monitor.py               # 消息监听功能测试
    ├── unit/               # 单元测试
    │   ├── __init__.py
    │   ├── test_account.py
    │   ├── test_proxy.py
    │   └── test_monitor.py
    └── integration/        # 集成测试
        ├── __init__.py
        └── test_workflow.py
```